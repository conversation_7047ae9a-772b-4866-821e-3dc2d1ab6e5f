{"version": 3, "sources": ["../../../locales/kaa.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Qoyıw\",\n    \"pasteAsPlaintext\": \"Ápiwayı tekst retinde qoyıw\",\n    \"pasteCharts\": \"Diagrammalardı qoyıw\",\n    \"selectAll\": \"Barlıǵın tańlaw\",\n    \"multiSelect\": \"\",\n    \"moveCanvas\": \"\",\n    \"cut\": \"Qıyıw\",\n    \"copy\": \"Kóshirip alıw\",\n    \"copyAsPng\": \"Almasıw buferine PNG retinde kóshirip alıw\",\n    \"copyAsSvg\": \"Almasıw buferine SVG retinde kóshirip alıw\",\n    \"copyText\": \"Almasıw buferine tekst retinde kóshirip alıw\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"\",\n    \"sendToBack\": \"\",\n    \"bringToFront\": \"\",\n    \"sendBackward\": \"\",\n    \"delete\": \"Óshiriw\",\n    \"copyStyles\": \"\",\n    \"pasteStyles\": \"\",\n    \"stroke\": \"Jiyek\",\n    \"background\": \"Fon\",\n    \"fill\": \"\",\n    \"strokeWidth\": \"\",\n    \"strokeStyle\": \"\",\n    \"strokeStyle_solid\": \"\",\n    \"strokeStyle_dashed\": \"\",\n    \"strokeStyle_dotted\": \"\",\n    \"sloppiness\": \"\",\n    \"opacity\": \"\",\n    \"textAlign\": \"\",\n    \"edges\": \"Qırlar\",\n    \"sharp\": \"\",\n    \"round\": \"\",\n    \"arrowheads\": \"\",\n    \"arrowhead_none\": \"Joq\",\n    \"arrowhead_arrow\": \"Jebe\",\n    \"arrowhead_bar\": \"\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Shrift ólshemi\",\n    \"fontFamily\": \"Shrift toplamı\",\n    \"addWatermark\": \"\",\n    \"handDrawn\": \"\",\n    \"normal\": \"\",\n    \"code\": \"Kod\",\n    \"small\": \"\",\n    \"medium\": \"Ortasha\",\n    \"large\": \"Úlken\",\n    \"veryLarge\": \"Júdá úlken\",\n    \"solid\": \"\",\n    \"hachure\": \"\",\n    \"zigzag\": \"Zigzag\",\n    \"crossHatch\": \"\",\n    \"thin\": \"Jińishke\",\n    \"bold\": \"Qalıń\",\n    \"left\": \"\",\n    \"center\": \"\",\n    \"right\": \"\",\n    \"extraBold\": \"\",\n    \"architect\": \"\",\n    \"artist\": \"Súwretshi\",\n    \"cartoonist\": \"\",\n    \"fileTitle\": \"Fayl ataması\",\n    \"colorPicker\": \"Reńdi tańlaw\",\n    \"canvasColors\": \"\",\n    \"canvasBackground\": \"\",\n    \"drawingCanvas\": \"\",\n    \"layers\": \"Qatlamlar\",\n    \"actions\": \"Háreketler\",\n    \"language\": \"Til\",\n    \"liveCollaboration\": \"\",\n    \"duplicateSelection\": \"Nusqa\",\n    \"untitled\": \"Atamasız\",\n    \"name\": \"Ataması\",\n    \"yourName\": \"Atıńız\",\n    \"madeWithExcalidraw\": \"Excalidraw járdeminde islengen\",\n    \"group\": \"\",\n    \"ungroup\": \"\",\n    \"collaborators\": \"Qatnasıwshılar\",\n    \"showGrid\": \"\",\n    \"addToLibrary\": \"Kitapxanaǵa qosıw\",\n    \"removeFromLibrary\": \"Kitapxanadan alıp taslaw\",\n    \"libraryLoadingMessage\": \"Kitapxana júklenbekte…\",\n    \"libraries\": \"Kitapxanalardı kóriw\",\n    \"loadingScene\": \"Saxna júklenbekte…\",\n    \"align\": \"\",\n    \"alignTop\": \"\",\n    \"alignBottom\": \"\",\n    \"alignLeft\": \"\",\n    \"alignRight\": \"\",\n    \"centerVertically\": \"\",\n    \"centerHorizontally\": \"\",\n    \"distributeHorizontally\": \"\",\n    \"distributeVertically\": \"\",\n    \"flipHorizontal\": \"\",\n    \"flipVertical\": \"\",\n    \"viewMode\": \"Kóriw rejimi\",\n    \"share\": \"Bólisiw\",\n    \"showStroke\": \"\",\n    \"showBackground\": \"\",\n    \"toggleTheme\": \"Temanı ózgertiw\",\n    \"personalLib\": \"Jeke kitapxana\",\n    \"excalidrawLib\": \"Excalidraw kitapxanası\",\n    \"decreaseFontSize\": \"Shrift ólshemin kishireytiw\",\n    \"increaseFontSize\": \"Shrift ólshemin úlkeytiw\",\n    \"unbindText\": \"\",\n    \"bindText\": \"\",\n    \"createContainerFromText\": \"\",\n    \"link\": {\n      \"edit\": \"Siltemeni ózgertiw\",\n      \"editEmbed\": \"\",\n      \"create\": \"Siltemeni jaratıw\",\n      \"createEmbed\": \"\",\n      \"label\": \"Silteme\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Qatardı ózgertiw\",\n      \"exit\": \"Qatardı ózgertiw redaktorınan shıǵıw\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Qulıplaw\",\n      \"unlock\": \"Qulıptan shıǵarıw\",\n      \"lockAll\": \"Barlıǵın qulıplaw\",\n      \"unlockAll\": \"Barlıǵın qulıptan shıǵarıw\"\n    },\n    \"statusPublished\": \"\",\n    \"sidebarLock\": \"\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"\",\n    \"hint_emptyLibrary\": \"\",\n    \"hint_emptyPrivateLibrary\": \"\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"\",\n    \"exportJSON\": \"\",\n    \"exportImage\": \"Súwretti eksportlaw...\",\n    \"export\": \"Retinde saqlaw...\",\n    \"copyToClipboard\": \"Almasıw buferine kóshirip alındı\",\n    \"save\": \"Ámeldegi faylǵa saqlaw\",\n    \"saveAs\": \"Retinde saqlaw\",\n    \"load\": \"Ashıw\",\n    \"getShareableLink\": \"\",\n    \"close\": \"Jabıw\",\n    \"selectLanguage\": \"Tildi tańlaw\",\n    \"scrollBackToContent\": \"\",\n    \"zoomIn\": \"\",\n    \"zoomOut\": \"\",\n    \"resetZoom\": \"\",\n    \"menu\": \"Menyu\",\n    \"done\": \"Tayın\",\n    \"edit\": \"Ózgertiw\",\n    \"undo\": \"\",\n    \"redo\": \"\",\n    \"resetLibrary\": \"\",\n    \"createNewRoom\": \"\",\n    \"fullScreen\": \"Tolıq ekran\",\n    \"darkMode\": \"Qarańǵı tema\",\n    \"lightMode\": \"Jaqtı tema\",\n    \"zenMode\": \"\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"\",\n    \"cancel\": \"Biykarlaw\",\n    \"clear\": \"Tazalaw\",\n    \"remove\": \"Óshiriw\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Jariyalaw\",\n    \"submit\": \"Jiberiw\",\n    \"confirm\": \"Tastıyıqlaw\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"\",\n    \"couldNotCreateShareableLink\": \"\",\n    \"couldNotCreateShareableLinkTooBig\": \"\",\n    \"couldNotLoadInvalidFile\": \"\",\n    \"importBackendFailed\": \"\",\n    \"cannotExportEmptyCanvas\": \"\",\n    \"couldNotCopyToClipboard\": \"Almasıw buferine kóshirip alıw ámelge aspadı.\",\n    \"decryptFailed\": \"\",\n    \"uploadedSecurly\": \"\",\n    \"loadSceneOverridePrompt\": \"\",\n    \"collabStopOverridePrompt\": \"\",\n    \"errorAddingToLibrary\": \"\",\n    \"errorRemovingFromLibrary\": \"\",\n    \"confirmAddLibrary\": \"\",\n    \"imageDoesNotContainScene\": \"\",\n    \"cannotRestoreFromImage\": \"\",\n    \"invalidSceneUrl\": \"\",\n    \"resetLibrary\": \"\",\n    \"removeItemsFromsLibrary\": \"\",\n    \"invalidEncryptionKey\": \"\",\n    \"collabOfflineWarning\": \"\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"\",\n    \"imageInsertError\": \"\",\n    \"fileTooBig\": \"\",\n    \"svgImageInsertError\": \"\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"Jaramsız SVG.\",\n    \"cannotResolveCollabServer\": \"\",\n    \"importLibraryError\": \"Kitapxananı júklew ámelge aspadı\",\n    \"collabSaveFailed\": \"\",\n    \"collabSaveFailed_sizeExceeded\": \"\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"\",\n    \"image\": \"Súwret qoyıw\",\n    \"rectangle\": \"Tórt múyeshlik\",\n    \"diamond\": \"\",\n    \"ellipse\": \"\",\n    \"arrow\": \"\",\n    \"line\": \"Sızıq\",\n    \"freedraw\": \"Sızıw\",\n    \"text\": \"Tekst\",\n    \"library\": \"Kitapxana\",\n    \"lock\": \"\",\n    \"penMode\": \"\",\n    \"link\": \"\",\n    \"eraser\": \"Óshirgish\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"\",\n    \"selectedShapeActions\": \"\",\n    \"shapes\": \"Figuralar\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"\",\n    \"linearElement\": \"\",\n    \"freeDraw\": \"\",\n    \"text\": \"\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"\",\n    \"text_editing\": \"\",\n    \"linearElementMulti\": \"\",\n    \"lockAngle\": \"\",\n    \"resize\": \"\",\n    \"resizeImage\": \"\",\n    \"rotate\": \"\",\n    \"lineEditor_info\": \"\",\n    \"lineEditor_pointSelected\": \"\",\n    \"lineEditor_nothingSelected\": \"\",\n    \"placeImage\": \"\",\n    \"publishLibrary\": \"\",\n    \"bindTextToElement\": \"Tekst qosıw ushın Enter túymesin basıń\",\n    \"deepBoxSelect\": \"\",\n    \"eraserRevert\": \"\",\n    \"firefox_clipboard_write\": \"\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Aldınnan kóriwdi kórsetiw múmkin emes\",\n    \"canvasTooBig\": \"\",\n    \"canvasTooBigTip\": \"\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"\",\n    \"clearCanvasMessage\": \"\",\n    \"clearCanvasCaveat\": \"\",\n    \"trackedToSentry\": \"\",\n    \"openIssueMessage\": \"\",\n    \"sceneContent\": \"\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"\",\n    \"desc_privacy\": \"\",\n    \"button_startSession\": \"\",\n    \"button_stopSession\": \"\",\n    \"desc_inProgressIntro\": \"\",\n    \"desc_shareLink\": \"\",\n    \"desc_exitSession\": \"\",\n    \"shareTitle\": \"\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Qátelik\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Diskke saqlaw\",\n    \"disk_details\": \"\",\n    \"disk_button\": \"Faylǵa saqlaw\",\n    \"link_title\": \"\",\n    \"link_details\": \"\",\n    \"link_button\": \"Siltemege eksportlaw\",\n    \"excalidrawplus_description\": \"\",\n    \"excalidrawplus_button\": \"Eksportlaw\",\n    \"excalidrawplus_exportError\": \"\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Biziń blogtı oqıń\",\n    \"click\": \"basıw\",\n    \"deepSelect\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"curvedArrow\": \"\",\n    \"curvedLine\": \"\",\n    \"documentation\": \"Hújjetshilik\",\n    \"doubleClick\": \"\",\n    \"drag\": \"\",\n    \"editor\": \"Redaktor\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"\",\n    \"howto\": \"\",\n    \"or\": \"yamasa\",\n    \"preventBinding\": \"\",\n    \"tools\": \"Ásbaplar\",\n    \"shortcuts\": \"\",\n    \"textFinish\": \"\",\n    \"textNewLine\": \"\",\n    \"title\": \"Járdem\",\n    \"view\": \"Kóriw\",\n    \"zoomToFit\": \"\",\n    \"zoomToSelection\": \"\",\n    \"toggleElementLock\": \"\",\n    \"movePageUpDown\": \"\",\n    \"movePageLeftRight\": \"\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"\"\n  },\n  \"publishDialog\": {\n    \"title\": \"\",\n    \"itemName\": \"\",\n    \"authorName\": \"Avtor atı\",\n    \"githubUsername\": \"GitHub paydalanıwshı atı\",\n    \"twitterUsername\": \"Twitter paydalanıwshı atı\",\n    \"libraryName\": \"Kitapxana ataması\",\n    \"libraryDesc\": \"\",\n    \"website\": \"Veb-sayt\",\n    \"placeholder\": {\n      \"authorName\": \"Atıńız yamasa paydalanıwshı atı\",\n      \"libraryName\": \"Kitapxanańız ataması\",\n      \"libraryDesc\": \"\",\n      \"githubHandle\": \"\",\n      \"twitterHandle\": \"\",\n      \"website\": \"Jeke veb-saytıńız yamasa basqa saytqa silteme (májbúriy emes)\"\n    },\n    \"errors\": {\n      \"required\": \"Májbúriy\",\n      \"website\": \"Jaramlı URL mánzil kirgiziń\"\n    },\n    \"noteDescription\": \"\",\n    \"noteGuidelines\": \"\",\n    \"noteLicense\": \"\",\n    \"noteItems\": \"\",\n    \"atleastOneLibItem\": \"\",\n    \"republishWarning\": \"\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Kitapxana jiberildi\",\n    \"content\": \"\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Kitapxananı qayta ornatıw\",\n    \"removeItemsFromLib\": \"\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Súwretti eksportlaw\",\n    \"label\": {\n      \"withBackground\": \"Fon\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"Qarańǵı tema\",\n      \"embedScene\": \"\",\n      \"scale\": \"Kólem\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Almasıw buferine kóshirip alıw\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"\",\n    \"link\": \"\"\n  },\n  \"stats\": {\n    \"angle\": \"\",\n    \"element\": \"Element\",\n    \"elements\": \"Elementler\",\n    \"height\": \"\",\n    \"scene\": \"Saxna\",\n    \"selected\": \"Tańlandı\",\n    \"storage\": \"\",\n    \"title\": \"\",\n    \"total\": \"\",\n    \"version\": \"Versiya\",\n    \"versionCopy\": \"Kóshirip alıw ushın basıń\",\n    \"versionNotAvailable\": \"\",\n    \"width\": \"Eni\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Kitapxanaǵa qosıldı\",\n    \"copyStyles\": \"\",\n    \"copyToClipboard\": \"Almasıw buferine kóshirip alındı.\",\n    \"copyToClipboardAsPng\": \"\",\n    \"fileSaved\": \"Fayl saqlandı.\",\n    \"fileSavedToFilename\": \"{filename} saqlandı\",\n    \"canvas\": \"\",\n    \"selection\": \"\",\n    \"pasteAsSingleElement\": \"\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"\",\n    \"black\": \"Qara\",\n    \"white\": \"Aq\",\n    \"red\": \"Qızıl\",\n    \"pink\": \"Qızǵılt\",\n    \"grape\": \"\",\n    \"violet\": \"Qızǵılt kók\",\n    \"gray\": \"\",\n    \"blue\": \"Kók\",\n    \"cyan\": \"Kók aspan\",\n    \"teal\": \"Piruza\",\n    \"green\": \"Jasıl\",\n    \"yellow\": \"Sarı\",\n    \"orange\": \"Qızǵılt sarı\",\n    \"bronze\": \"\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"\",\n      \"center_heading_plus\": \"Excalidraw+ ge ótiwdi qáleysiz be?\",\n      \"menuHint\": \"Eksportlaw, sazlawlar, tiller, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Eksportlaw, sazlawlar hám basqa...\",\n      \"center_heading\": \"Diagrammalar. Ápiwayı.\",\n      \"toolbarHint\": \"\",\n      \"helpHint\": \"\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Kóp qollanılatuǵın arnawlı reńler\",\n    \"colors\": \"Reńler\",\n    \"shades\": \"\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Súwret retinde eksportlaw\",\n        \"button\": \"Súwret retinde eksportlaw\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Diskke saqlaw\",\n        \"button\": \"Diskke saqlaw\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Fayldan júklew\",\n        \"button\": \"Fayldan júklew\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Siltemeden júklew\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}