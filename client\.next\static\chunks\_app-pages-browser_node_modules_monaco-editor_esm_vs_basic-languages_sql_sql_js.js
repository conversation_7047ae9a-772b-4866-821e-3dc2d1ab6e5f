"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_sql_sql_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/sql/sql.js":
/*!***********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/sql/sql.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/sql/sql.ts\nvar conf = {\n  comments: {\n    lineComment: \"--\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sql\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    // This list is generated using `keywords.js`\n    \"ABORT\",\n    \"ABSOLUTE\",\n    \"ACTION\",\n    \"ADA\",\n    \"ADD\",\n    \"AFTER\",\n    \"ALL\",\n    \"ALLOCATE\",\n    \"ALTER\",\n    \"ALWAYS\",\n    \"ANALYZE\",\n    \"AND\",\n    \"ANY\",\n    \"ARE\",\n    \"AS\",\n    \"ASC\",\n    \"ASSERTION\",\n    \"AT\",\n    \"ATTACH\",\n    \"AUTHORIZATION\",\n    \"AUTOINCREMENT\",\n    \"AVG\",\n    \"BACKUP\",\n    \"BEFORE\",\n    \"BEGIN\",\n    \"BETWEEN\",\n    \"BIT\",\n    \"BIT_LENGTH\",\n    \"BOTH\",\n    \"BREAK\",\n    \"BROWSE\",\n    \"BULK\",\n    \"BY\",\n    \"CASCADE\",\n    \"CASCADED\",\n    \"CASE\",\n    \"CAST\",\n    \"CATALOG\",\n    \"CHAR\",\n    \"CHARACTER\",\n    \"CHARACTER_LENGTH\",\n    \"CHAR_LENGTH\",\n    \"CHECK\",\n    \"CHECKPOINT\",\n    \"CLOSE\",\n    \"CLUSTERED\",\n    \"COALESCE\",\n    \"COLLATE\",\n    \"COLLATION\",\n    \"COLUMN\",\n    \"COMMIT\",\n    \"COMPUTE\",\n    \"CONFLICT\",\n    \"CONNECT\",\n    \"CONNECTION\",\n    \"CONSTRAINT\",\n    \"CONSTRAINTS\",\n    \"CONTAINS\",\n    \"CONTAINSTABLE\",\n    \"CONTINUE\",\n    \"CONVERT\",\n    \"CORRESPONDING\",\n    \"COUNT\",\n    \"CREATE\",\n    \"CROSS\",\n    \"CURRENT\",\n    \"CURRENT_DATE\",\n    \"CURRENT_TIME\",\n    \"CURRENT_TIMESTAMP\",\n    \"CURRENT_USER\",\n    \"CURSOR\",\n    \"DATABASE\",\n    \"DATE\",\n    \"DAY\",\n    \"DBCC\",\n    \"DEALLOCATE\",\n    \"DEC\",\n    \"DECIMAL\",\n    \"DECLARE\",\n    \"DEFAULT\",\n    \"DEFERRABLE\",\n    \"DEFERRED\",\n    \"DELETE\",\n    \"DENY\",\n    \"DESC\",\n    \"DESCRIBE\",\n    \"DESCRIPTOR\",\n    \"DETACH\",\n    \"DIAGNOSTICS\",\n    \"DISCONNECT\",\n    \"DISK\",\n    \"DISTINCT\",\n    \"DISTRIBUTED\",\n    \"DO\",\n    \"DOMAIN\",\n    \"DOUBLE\",\n    \"DROP\",\n    \"DUMP\",\n    \"EACH\",\n    \"ELSE\",\n    \"END\",\n    \"END-EXEC\",\n    \"ERRLVL\",\n    \"ESCAPE\",\n    \"EXCEPT\",\n    \"EXCEPTION\",\n    \"EXCLUDE\",\n    \"EXCLUSIVE\",\n    \"EXEC\",\n    \"EXECUTE\",\n    \"EXISTS\",\n    \"EXIT\",\n    \"EXPLAIN\",\n    \"EXTERNAL\",\n    \"EXTRACT\",\n    \"FAIL\",\n    \"FALSE\",\n    \"FETCH\",\n    \"FILE\",\n    \"FILLFACTOR\",\n    \"FILTER\",\n    \"FIRST\",\n    \"FLOAT\",\n    \"FOLLOWING\",\n    \"FOR\",\n    \"FOREIGN\",\n    \"FORTRAN\",\n    \"FOUND\",\n    \"FREETEXT\",\n    \"FREETEXTTABLE\",\n    \"FROM\",\n    \"FULL\",\n    \"FUNCTION\",\n    \"GENERATED\",\n    \"GET\",\n    \"GLOB\",\n    \"GLOBAL\",\n    \"GO\",\n    \"GOTO\",\n    \"GRANT\",\n    \"GROUP\",\n    \"GROUPS\",\n    \"HAVING\",\n    \"HOLDLOCK\",\n    \"HOUR\",\n    \"IDENTITY\",\n    \"IDENTITYCOL\",\n    \"IDENTITY_INSERT\",\n    \"IF\",\n    \"IGNORE\",\n    \"IMMEDIATE\",\n    \"IN\",\n    \"INCLUDE\",\n    \"INDEX\",\n    \"INDEXED\",\n    \"INDICATOR\",\n    \"INITIALLY\",\n    \"INNER\",\n    \"INPUT\",\n    \"INSENSITIVE\",\n    \"INSERT\",\n    \"INSTEAD\",\n    \"INT\",\n    \"INTEGER\",\n    \"INTERSECT\",\n    \"INTERVAL\",\n    \"INTO\",\n    \"IS\",\n    \"ISNULL\",\n    \"ISOLATION\",\n    \"JOIN\",\n    \"KEY\",\n    \"KILL\",\n    \"LANGUAGE\",\n    \"LAST\",\n    \"LEADING\",\n    \"LEFT\",\n    \"LEVEL\",\n    \"LIKE\",\n    \"LIMIT\",\n    \"LINENO\",\n    \"LOAD\",\n    \"LOCAL\",\n    \"LOWER\",\n    \"MATCH\",\n    \"MATERIALIZED\",\n    \"MAX\",\n    \"MERGE\",\n    \"MIN\",\n    \"MINUTE\",\n    \"MODULE\",\n    \"MONTH\",\n    \"NAMES\",\n    \"NATIONAL\",\n    \"NATURAL\",\n    \"NCHAR\",\n    \"NEXT\",\n    \"NO\",\n    \"NOCHECK\",\n    \"NONCLUSTERED\",\n    \"NONE\",\n    \"NOT\",\n    \"NOTHING\",\n    \"NOTNULL\",\n    \"NULL\",\n    \"NULLIF\",\n    \"NULLS\",\n    \"NUMERIC\",\n    \"OCTET_LENGTH\",\n    \"OF\",\n    \"OFF\",\n    \"OFFSET\",\n    \"OFFSETS\",\n    \"ON\",\n    \"ONLY\",\n    \"OPEN\",\n    \"OPENDATASOURCE\",\n    \"OPENQUERY\",\n    \"OPENROWSET\",\n    \"OPENXML\",\n    \"OPTION\",\n    \"OR\",\n    \"ORDER\",\n    \"OTHERS\",\n    \"OUTER\",\n    \"OUTPUT\",\n    \"OVER\",\n    \"OVERLAPS\",\n    \"PAD\",\n    \"PARTIAL\",\n    \"PARTITION\",\n    \"PASCAL\",\n    \"PERCENT\",\n    \"PIVOT\",\n    \"PLAN\",\n    \"POSITION\",\n    \"PRAGMA\",\n    \"PRECEDING\",\n    \"PRECISION\",\n    \"PREPARE\",\n    \"PRESERVE\",\n    \"PRIMARY\",\n    \"PRINT\",\n    \"PRIOR\",\n    \"PRIVILEGES\",\n    \"PROC\",\n    \"PROCEDURE\",\n    \"PUBLIC\",\n    \"QUERY\",\n    \"RAISE\",\n    \"RAISERROR\",\n    \"RANGE\",\n    \"READ\",\n    \"READTEXT\",\n    \"REAL\",\n    \"RECONFIGURE\",\n    \"RECURSIVE\",\n    \"REFERENCES\",\n    \"REGEXP\",\n    \"REINDEX\",\n    \"RELATIVE\",\n    \"RELEASE\",\n    \"RENAME\",\n    \"REPLACE\",\n    \"REPLICATION\",\n    \"RESTORE\",\n    \"RESTRICT\",\n    \"RETURN\",\n    \"RETURNING\",\n    \"REVERT\",\n    \"REVOKE\",\n    \"RIGHT\",\n    \"ROLLBACK\",\n    \"ROW\",\n    \"ROWCOUNT\",\n    \"ROWGUIDCOL\",\n    \"ROWS\",\n    \"RULE\",\n    \"SAVE\",\n    \"SAVEPOINT\",\n    \"SCHEMA\",\n    \"SCROLL\",\n    \"SECOND\",\n    \"SECTION\",\n    \"SECURITYAUDIT\",\n    \"SELECT\",\n    \"SEMANTICKEYPHRASETABLE\",\n    \"SEMANTICSIMILARITYDETAILSTABLE\",\n    \"SEMANTICSIMILARITYTABLE\",\n    \"SESSION\",\n    \"SESSION_USER\",\n    \"SET\",\n    \"SETUSER\",\n    \"SHUTDOWN\",\n    \"SIZE\",\n    \"SMALLINT\",\n    \"SOME\",\n    \"SPACE\",\n    \"SQL\",\n    \"SQLCA\",\n    \"SQLCODE\",\n    \"SQLERROR\",\n    \"SQLSTATE\",\n    \"SQLWARNING\",\n    \"STATISTICS\",\n    \"SUBSTRING\",\n    \"SUM\",\n    \"SYSTEM_USER\",\n    \"TABLE\",\n    \"TABLESAMPLE\",\n    \"TEMP\",\n    \"TEMPORARY\",\n    \"TEXTSIZE\",\n    \"THEN\",\n    \"TIES\",\n    \"TIME\",\n    \"TIMESTAMP\",\n    \"TIMEZONE_HOUR\",\n    \"TIMEZONE_MINUTE\",\n    \"TO\",\n    \"TOP\",\n    \"TRAILING\",\n    \"TRAN\",\n    \"TRANSACTION\",\n    \"TRANSLATE\",\n    \"TRANSLATION\",\n    \"TRIGGER\",\n    \"TRIM\",\n    \"TRUE\",\n    \"TRUNCATE\",\n    \"TRY_CONVERT\",\n    \"TSEQUAL\",\n    \"UNBOUNDED\",\n    \"UNION\",\n    \"UNIQUE\",\n    \"UNKNOWN\",\n    \"UNPIVOT\",\n    \"UPDATE\",\n    \"UPDATETEXT\",\n    \"UPPER\",\n    \"USAGE\",\n    \"USE\",\n    \"USER\",\n    \"USING\",\n    \"VACUUM\",\n    \"VALUE\",\n    \"VALUES\",\n    \"VARCHAR\",\n    \"VARYING\",\n    \"VIEW\",\n    \"VIRTUAL\",\n    \"WAITFOR\",\n    \"WHEN\",\n    \"WHENEVER\",\n    \"WHERE\",\n    \"WHILE\",\n    \"WINDOW\",\n    \"WITH\",\n    \"WITHIN GROUP\",\n    \"WITHOUT\",\n    \"WORK\",\n    \"WRITE\",\n    \"WRITETEXT\",\n    \"YEAR\",\n    \"ZONE\"\n  ],\n  operators: [\n    // Logical\n    \"ALL\",\n    \"AND\",\n    \"ANY\",\n    \"BETWEEN\",\n    \"EXISTS\",\n    \"IN\",\n    \"LIKE\",\n    \"NOT\",\n    \"OR\",\n    \"SOME\",\n    // Set\n    \"EXCEPT\",\n    \"INTERSECT\",\n    \"UNION\",\n    // Join\n    \"APPLY\",\n    \"CROSS\",\n    \"FULL\",\n    \"INNER\",\n    \"JOIN\",\n    \"LEFT\",\n    \"OUTER\",\n    \"RIGHT\",\n    // Predicates\n    \"CONTAINS\",\n    \"FREETEXT\",\n    \"IS\",\n    \"NULL\",\n    // Pivoting\n    \"PIVOT\",\n    \"UNPIVOT\",\n    // Merging\n    \"MATCHED\"\n  ],\n  builtinFunctions: [\n    // Aggregate\n    \"AVG\",\n    \"CHECKSUM_AGG\",\n    \"COUNT\",\n    \"COUNT_BIG\",\n    \"GROUPING\",\n    \"GROUPING_ID\",\n    \"MAX\",\n    \"MIN\",\n    \"SUM\",\n    \"STDEV\",\n    \"STDEVP\",\n    \"VAR\",\n    \"VARP\",\n    // Analytic\n    \"CUME_DIST\",\n    \"FIRST_VALUE\",\n    \"LAG\",\n    \"LAST_VALUE\",\n    \"LEAD\",\n    \"PERCENTILE_CONT\",\n    \"PERCENTILE_DISC\",\n    \"PERCENT_RANK\",\n    // Collation\n    \"COLLATE\",\n    \"COLLATIONPROPERTY\",\n    \"TERTIARY_WEIGHTS\",\n    // Azure\n    \"FEDERATION_FILTERING_VALUE\",\n    // Conversion\n    \"CAST\",\n    \"CONVERT\",\n    \"PARSE\",\n    \"TRY_CAST\",\n    \"TRY_CONVERT\",\n    \"TRY_PARSE\",\n    // Cryptographic\n    \"ASYMKEY_ID\",\n    \"ASYMKEYPROPERTY\",\n    \"CERTPROPERTY\",\n    \"CERT_ID\",\n    \"CRYPT_GEN_RANDOM\",\n    \"DECRYPTBYASYMKEY\",\n    \"DECRYPTBYCERT\",\n    \"DECRYPTBYKEY\",\n    \"DECRYPTBYKEYAUTOASYMKEY\",\n    \"DECRYPTBYKEYAUTOCERT\",\n    \"DECRYPTBYPASSPHRASE\",\n    \"ENCRYPTBYASYMKEY\",\n    \"ENCRYPTBYCERT\",\n    \"ENCRYPTBYKEY\",\n    \"ENCRYPTBYPASSPHRASE\",\n    \"HASHBYTES\",\n    \"IS_OBJECTSIGNED\",\n    \"KEY_GUID\",\n    \"KEY_ID\",\n    \"KEY_NAME\",\n    \"SIGNBYASYMKEY\",\n    \"SIGNBYCERT\",\n    \"SYMKEYPROPERTY\",\n    \"VERIFYSIGNEDBYCERT\",\n    \"VERIFYSIGNEDBYASYMKEY\",\n    // Cursor\n    \"CURSOR_STATUS\",\n    // Datatype\n    \"DATALENGTH\",\n    \"IDENT_CURRENT\",\n    \"IDENT_INCR\",\n    \"IDENT_SEED\",\n    \"IDENTITY\",\n    \"SQL_VARIANT_PROPERTY\",\n    // Datetime\n    \"CURRENT_TIMESTAMP\",\n    \"DATEADD\",\n    \"DATEDIFF\",\n    \"DATEFROMPARTS\",\n    \"DATENAME\",\n    \"DATEPART\",\n    \"DATETIME2FROMPARTS\",\n    \"DATETIMEFROMPARTS\",\n    \"DATETIMEOFFSETFROMPARTS\",\n    \"DAY\",\n    \"EOMONTH\",\n    \"GETDATE\",\n    \"GETUTCDATE\",\n    \"ISDATE\",\n    \"MONTH\",\n    \"SMALLDATETIMEFROMPARTS\",\n    \"SWITCHOFFSET\",\n    \"SYSDATETIME\",\n    \"SYSDATETIMEOFFSET\",\n    \"SYSUTCDATETIME\",\n    \"TIMEFROMPARTS\",\n    \"TODATETIMEOFFSET\",\n    \"YEAR\",\n    // Logical\n    \"CHOOSE\",\n    \"COALESCE\",\n    \"IIF\",\n    \"NULLIF\",\n    // Mathematical\n    \"ABS\",\n    \"ACOS\",\n    \"ASIN\",\n    \"ATAN\",\n    \"ATN2\",\n    \"CEILING\",\n    \"COS\",\n    \"COT\",\n    \"DEGREES\",\n    \"EXP\",\n    \"FLOOR\",\n    \"LOG\",\n    \"LOG10\",\n    \"PI\",\n    \"POWER\",\n    \"RADIANS\",\n    \"RAND\",\n    \"ROUND\",\n    \"SIGN\",\n    \"SIN\",\n    \"SQRT\",\n    \"SQUARE\",\n    \"TAN\",\n    // Metadata\n    \"APP_NAME\",\n    \"APPLOCK_MODE\",\n    \"APPLOCK_TEST\",\n    \"ASSEMBLYPROPERTY\",\n    \"COL_LENGTH\",\n    \"COL_NAME\",\n    \"COLUMNPROPERTY\",\n    \"DATABASE_PRINCIPAL_ID\",\n    \"DATABASEPROPERTYEX\",\n    \"DB_ID\",\n    \"DB_NAME\",\n    \"FILE_ID\",\n    \"FILE_IDEX\",\n    \"FILE_NAME\",\n    \"FILEGROUP_ID\",\n    \"FILEGROUP_NAME\",\n    \"FILEGROUPPROPERTY\",\n    \"FILEPROPERTY\",\n    \"FULLTEXTCATALOGPROPERTY\",\n    \"FULLTEXTSERVICEPROPERTY\",\n    \"INDEX_COL\",\n    \"INDEXKEY_PROPERTY\",\n    \"INDEXPROPERTY\",\n    \"OBJECT_DEFINITION\",\n    \"OBJECT_ID\",\n    \"OBJECT_NAME\",\n    \"OBJECT_SCHEMA_NAME\",\n    \"OBJECTPROPERTY\",\n    \"OBJECTPROPERTYEX\",\n    \"ORIGINAL_DB_NAME\",\n    \"PARSENAME\",\n    \"SCHEMA_ID\",\n    \"SCHEMA_NAME\",\n    \"SCOPE_IDENTITY\",\n    \"SERVERPROPERTY\",\n    \"STATS_DATE\",\n    \"TYPE_ID\",\n    \"TYPE_NAME\",\n    \"TYPEPROPERTY\",\n    // Ranking\n    \"DENSE_RANK\",\n    \"NTILE\",\n    \"RANK\",\n    \"ROW_NUMBER\",\n    // Replication\n    \"PUBLISHINGSERVERNAME\",\n    // Rowset\n    \"OPENDATASOURCE\",\n    \"OPENQUERY\",\n    \"OPENROWSET\",\n    \"OPENXML\",\n    // Security\n    \"CERTENCODED\",\n    \"CERTPRIVATEKEY\",\n    \"CURRENT_USER\",\n    \"HAS_DBACCESS\",\n    \"HAS_PERMS_BY_NAME\",\n    \"IS_MEMBER\",\n    \"IS_ROLEMEMBER\",\n    \"IS_SRVROLEMEMBER\",\n    \"LOGINPROPERTY\",\n    \"ORIGINAL_LOGIN\",\n    \"PERMISSIONS\",\n    \"PWDENCRYPT\",\n    \"PWDCOMPARE\",\n    \"SESSION_USER\",\n    \"SESSIONPROPERTY\",\n    \"SUSER_ID\",\n    \"SUSER_NAME\",\n    \"SUSER_SID\",\n    \"SUSER_SNAME\",\n    \"SYSTEM_USER\",\n    \"USER\",\n    \"USER_ID\",\n    \"USER_NAME\",\n    // String\n    \"ASCII\",\n    \"CHAR\",\n    \"CHARINDEX\",\n    \"CONCAT\",\n    \"DIFFERENCE\",\n    \"FORMAT\",\n    \"LEFT\",\n    \"LEN\",\n    \"LOWER\",\n    \"LTRIM\",\n    \"NCHAR\",\n    \"PATINDEX\",\n    \"QUOTENAME\",\n    \"REPLACE\",\n    \"REPLICATE\",\n    \"REVERSE\",\n    \"RIGHT\",\n    \"RTRIM\",\n    \"SOUNDEX\",\n    \"SPACE\",\n    \"STR\",\n    \"STUFF\",\n    \"SUBSTRING\",\n    \"UNICODE\",\n    \"UPPER\",\n    // System\n    \"BINARY_CHECKSUM\",\n    \"CHECKSUM\",\n    \"CONNECTIONPROPERTY\",\n    \"CONTEXT_INFO\",\n    \"CURRENT_REQUEST_ID\",\n    \"ERROR_LINE\",\n    \"ERROR_NUMBER\",\n    \"ERROR_MESSAGE\",\n    \"ERROR_PROCEDURE\",\n    \"ERROR_SEVERITY\",\n    \"ERROR_STATE\",\n    \"FORMATMESSAGE\",\n    \"GETANSINULL\",\n    \"GET_FILESTREAM_TRANSACTION_CONTEXT\",\n    \"HOST_ID\",\n    \"HOST_NAME\",\n    \"ISNULL\",\n    \"ISNUMERIC\",\n    \"MIN_ACTIVE_ROWVERSION\",\n    \"NEWID\",\n    \"NEWSEQUENTIALID\",\n    \"ROWCOUNT_BIG\",\n    \"XACT_STATE\",\n    // TextImage\n    \"TEXTPTR\",\n    \"TEXTVALID\",\n    // Trigger\n    \"COLUMNS_UPDATED\",\n    \"EVENTDATA\",\n    \"TRIGGER_NESTLEVEL\",\n    \"UPDATE\",\n    // ChangeTracking\n    \"CHANGETABLE\",\n    \"CHANGE_TRACKING_CONTEXT\",\n    \"CHANGE_TRACKING_CURRENT_VERSION\",\n    \"CHANGE_TRACKING_IS_COLUMN_IN_MASK\",\n    \"CHANGE_TRACKING_MIN_VALID_VERSION\",\n    // FullTextSearch\n    \"CONTAINSTABLE\",\n    \"FREETEXTTABLE\",\n    // SemanticTextSearch\n    \"SEMANTICKEYPHRASETABLE\",\n    \"SEMANTICSIMILARITYDETAILSTABLE\",\n    \"SEMANTICSIMILARITYTABLE\",\n    // FileStream\n    \"FILETABLEROOTPATH\",\n    \"GETFILENAMESPACEPATH\",\n    \"GETPATHLOCATOR\",\n    \"PATHNAME\",\n    // ServiceBroker\n    \"GET_TRANSMISSION_STATUS\"\n  ],\n  builtinVariables: [\n    // Configuration\n    \"@@DATEFIRST\",\n    \"@@DBTS\",\n    \"@@LANGID\",\n    \"@@LANGUAGE\",\n    \"@@LOCK_TIMEOUT\",\n    \"@@MAX_CONNECTIONS\",\n    \"@@MAX_PRECISION\",\n    \"@@NESTLEVEL\",\n    \"@@OPTIONS\",\n    \"@@REMSERVER\",\n    \"@@SERVERNAME\",\n    \"@@SERVICENAME\",\n    \"@@SPID\",\n    \"@@TEXTSIZE\",\n    \"@@VERSION\",\n    // Cursor\n    \"@@CURSOR_ROWS\",\n    \"@@FETCH_STATUS\",\n    // Datetime\n    \"@@DATEFIRST\",\n    // Metadata\n    \"@@PROCID\",\n    // System\n    \"@@ERROR\",\n    \"@@IDENTITY\",\n    \"@@ROWCOUNT\",\n    \"@@TRANCOUNT\",\n    // Stats\n    \"@@CONNECTIONS\",\n    \"@@CPU_BUSY\",\n    \"@@IDLE\",\n    \"@@IO_BUSY\",\n    \"@@PACKET_ERRORS\",\n    \"@@PACK_RECEIVED\",\n    \"@@PACK_SENT\",\n    \"@@TIMETICKS\",\n    \"@@TOTAL_ERRORS\",\n    \"@@TOTAL_READ\",\n    \"@@TOTAL_WRITE\"\n  ],\n  pseudoColumns: [\"$ACTION\", \"$IDENTITY\", \"$ROWGUID\", \"$PARTITION\"],\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@pseudoColumns\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@complexIdentifiers\" },\n      { include: \"@scopes\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [\n        /[\\w@#$]+/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [/--+.*/, \"comment\"],\n      [/\\/\\*/, { token: \"comment.quote\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^*/]+/, \"comment\"],\n      // Not supporting nested comments, as nested comments seem to not be standard?\n      // i.e. http://stackoverflow.com/questions/728172/are-there-multiline-comment-delimiters-in-sql-that-are-vendor-agnostic\n      // [/\\/\\*/, { token: 'comment.quote', next: '@push' }],    // nested comment not allowed :-(\n      [/\\*\\//, { token: \"comment.quote\", next: \"@pop\" }],\n      [/./, \"comment\"]\n    ],\n    pseudoColumns: [\n      [\n        /[$][A-Za-z_][\\w@#$]*/,\n        {\n          cases: {\n            \"@pseudoColumns\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [\n      [/N'/, { token: \"string\", next: \"@string\" }],\n      [/'/, { token: \"string\", next: \"@string\" }]\n    ],\n    string: [\n      [/[^']+/, \"string\"],\n      [/''/, \"string\"],\n      [/'/, { token: \"string\", next: \"@pop\" }]\n    ],\n    complexIdentifiers: [\n      [/\\[/, { token: \"identifier.quote\", next: \"@bracketedIdentifier\" }],\n      [/\"/, { token: \"identifier.quote\", next: \"@quotedIdentifier\" }]\n    ],\n    bracketedIdentifier: [\n      [/[^\\]]+/, \"identifier\"],\n      [/]]/, \"identifier\"],\n      [/]/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    quotedIdentifier: [\n      [/[^\"]+/, \"identifier\"],\n      [/\"\"/, \"identifier\"],\n      [/\"/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    scopes: [\n      [/BEGIN\\s+(DISTRIBUTED\\s+)?TRAN(SACTION)?\\b/i, \"keyword\"],\n      [/BEGIN\\s+TRY\\b/i, { token: \"keyword.try\" }],\n      [/END\\s+TRY\\b/i, { token: \"keyword.try\" }],\n      [/BEGIN\\s+CATCH\\b/i, { token: \"keyword.catch\" }],\n      [/END\\s+CATCH\\b/i, { token: \"keyword.catch\" }],\n      [/(BEGIN|CASE)\\b/i, { token: \"keyword.block\" }],\n      [/END\\b/i, { token: \"keyword.block\" }],\n      [/WHEN\\b/i, { token: \"keyword.choice\" }],\n      [/THEN\\b/i, { token: \"keyword.choice\" }]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/sql/sql.js\n"));

/***/ })

}]);