{"version": 3, "sources": ["../../../locales/ko-KR.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"붙여넣기\",\n    \"pasteAsPlaintext\": \"일반 텍스트로 붙여넣기\",\n    \"pasteCharts\": \"차트 붙여넣기\",\n    \"selectAll\": \"전체 선택\",\n    \"multiSelect\": \"선택 영역에 추가하기\",\n    \"moveCanvas\": \"캔버스 이동\",\n    \"cut\": \"잘라내기\",\n    \"copy\": \"복사\",\n    \"copyAsPng\": \"클립보드로 PNG 이미지 복사\",\n    \"copyAsSvg\": \"클립보드로 SVG 이미지 복사\",\n    \"copyText\": \"클립보드로 텍스트 복사\",\n    \"copySource\": \"소스코드를 클립보드로 복사\",\n    \"convertToCode\": \"코드로 변환\",\n    \"bringForward\": \"앞으로 가져오기\",\n    \"sendToBack\": \"맨 뒤로 보내기\",\n    \"bringToFront\": \"맨 앞으로 가져오기\",\n    \"sendBackward\": \"뒤로 보내기\",\n    \"delete\": \"삭제\",\n    \"copyStyles\": \"스타일 복사하기\",\n    \"pasteStyles\": \"스타일 붙여넣기\",\n    \"stroke\": \"선 색상\",\n    \"background\": \"배경색\",\n    \"fill\": \"채우기\",\n    \"strokeWidth\": \"선 굵기\",\n    \"strokeStyle\": \"선\",\n    \"strokeStyle_solid\": \"실선\",\n    \"strokeStyle_dashed\": \"파선\",\n    \"strokeStyle_dotted\": \"점선\",\n    \"sloppiness\": \"대충 긋기\",\n    \"opacity\": \"불투명도\",\n    \"textAlign\": \"텍스트 정렬\",\n    \"edges\": \"가장자리\",\n    \"sharp\": \"뾰족하게\",\n    \"round\": \"둥글게\",\n    \"arrowheads\": \"화살촉\",\n    \"arrowhead_none\": \"없음\",\n    \"arrowhead_arrow\": \"화살표\",\n    \"arrowhead_bar\": \"막대\",\n    \"arrowhead_circle\": \"원\",\n    \"arrowhead_circle_outline\": \"원 (외곽선)\",\n    \"arrowhead_triangle\": \"삼각형\",\n    \"arrowhead_triangle_outline\": \"삼각형 (외곽선)\",\n    \"arrowhead_diamond\": \"마름모\",\n    \"arrowhead_diamond_outline\": \"마름모 (외곽선)\",\n    \"fontSize\": \"글자 크기\",\n    \"fontFamily\": \"글꼴\",\n    \"addWatermark\": \"\\\"Made with Excalidraw\\\" 추가\",\n    \"handDrawn\": \"손글씨\",\n    \"normal\": \"일반\",\n    \"code\": \"코드\",\n    \"small\": \"작게\",\n    \"medium\": \"보통\",\n    \"large\": \"크게\",\n    \"veryLarge\": \"매우 크게\",\n    \"solid\": \"단색\",\n    \"hachure\": \"평행선\",\n    \"zigzag\": \"지그재그\",\n    \"crossHatch\": \"교차선\",\n    \"thin\": \"얇게\",\n    \"bold\": \"굵게\",\n    \"left\": \"왼쪽\",\n    \"center\": \"가운데\",\n    \"right\": \"오른쪽\",\n    \"extraBold\": \"매우 굵게\",\n    \"architect\": \"건축가\",\n    \"artist\": \"예술가\",\n    \"cartoonist\": \"만화가\",\n    \"fileTitle\": \"파일 이름\",\n    \"colorPicker\": \"색상 선택기\",\n    \"canvasColors\": \"캔버스에서 사용되었음\",\n    \"canvasBackground\": \"캔버스 배경\",\n    \"drawingCanvas\": \"캔버스 그리기\",\n    \"layers\": \"레이어\",\n    \"actions\": \"동작\",\n    \"language\": \"언어\",\n    \"liveCollaboration\": \"실시간 협업...\",\n    \"duplicateSelection\": \"복제\",\n    \"untitled\": \"제목 없음\",\n    \"name\": \"이름\",\n    \"yourName\": \"이름 입력\",\n    \"madeWithExcalidraw\": \"Made with Excalidraw\",\n    \"group\": \"그룹 생성\",\n    \"ungroup\": \"그룹 해제\",\n    \"collaborators\": \"공동 작업자\",\n    \"showGrid\": \"그리드 보기\",\n    \"addToLibrary\": \"라이브러리에 추가\",\n    \"removeFromLibrary\": \"라이브러리에서 제거\",\n    \"libraryLoadingMessage\": \"라이브러리 불러오는 중…\",\n    \"libraries\": \"라이브러리 찾기\",\n    \"loadingScene\": \"화면 불러오는 중…\",\n    \"align\": \"정렬\",\n    \"alignTop\": \"상단 정렬\",\n    \"alignBottom\": \"하단 정렬\",\n    \"alignLeft\": \"왼쪽 정렬\",\n    \"alignRight\": \"오른쪽 정렬\",\n    \"centerVertically\": \"수직으로 중앙 정렬\",\n    \"centerHorizontally\": \"수평으로 중앙 정렬\",\n    \"distributeHorizontally\": \"수평으로 분배\",\n    \"distributeVertically\": \"수직으로 분배\",\n    \"flipHorizontal\": \"좌우반전\",\n    \"flipVertical\": \"상하반전\",\n    \"viewMode\": \"보기 모드\",\n    \"share\": \"공유\",\n    \"showStroke\": \"윤곽선 색상 선택기 열기\",\n    \"showBackground\": \"배경 색상 선택기 열기\",\n    \"toggleTheme\": \"테마 전환\",\n    \"personalLib\": \"개인 라이브러리\",\n    \"excalidrawLib\": \"Excalidraw 라이브러리\",\n    \"decreaseFontSize\": \"폰트 사이즈 줄이기\",\n    \"increaseFontSize\": \"폰트 사이즈 키우기\",\n    \"unbindText\": \"텍스트 분리\",\n    \"bindText\": \"텍스트를 컨테이너에 결합\",\n    \"createContainerFromText\": \"텍스트를 컨테이너에 담기\",\n    \"link\": {\n      \"edit\": \"링크 수정하기\",\n      \"editEmbed\": \"링크 & 임베드 수정하기\",\n      \"create\": \"링크 만들기\",\n      \"createEmbed\": \"링크 & 임베드 만들기\",\n      \"label\": \"링크\",\n      \"labelEmbed\": \"링크 & 임베드\",\n      \"empty\": \"링크를 지정하지 않았습니다\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"선 수정하기\",\n      \"exit\": \"선 편집기 종료\"\n    },\n    \"elementLock\": {\n      \"lock\": \"잠금\",\n      \"unlock\": \"잠금 해제\",\n      \"lockAll\": \"모두 잠금\",\n      \"unlockAll\": \"모두 잠금 해제\"\n    },\n    \"statusPublished\": \"게시됨\",\n    \"sidebarLock\": \"사이드바 유지\",\n    \"selectAllElementsInFrame\": \"프레임의 모든 요소 선택\",\n    \"removeAllElementsFromFrame\": \"프레임의 모든 요소 삭제\",\n    \"eyeDropper\": \"캔버스에서 색상 고르기\",\n    \"textToDiagram\": \"텍스트를 다이어그램으로\",\n    \"prompt\": \"프롬프트\"\n  },\n  \"library\": {\n    \"noItems\": \"추가된 아이템 없음\",\n    \"hint_emptyLibrary\": \"캔버스 위에서 아이템을 선택하여 여기에 추가를 하거나, 아래의 공용 저장소에서 라이브러리를 설치하세요.\",\n    \"hint_emptyPrivateLibrary\": \"캔버스 위에서 아이템을 선택하여 여기 추가하세요.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"캔버스 초기화\",\n    \"exportJSON\": \"파일로 내보내기\",\n    \"exportImage\": \"이미지 내보내기\",\n    \"export\": \"다른 이름으로 저장...\",\n    \"copyToClipboard\": \"클립보드로 복사\",\n    \"save\": \"현재 파일에 저장\",\n    \"saveAs\": \"다른 이름으로 저장\",\n    \"load\": \"열기\",\n    \"getShareableLink\": \"공유 가능한 링크 생성\",\n    \"close\": \"닫기\",\n    \"selectLanguage\": \"언어 선택\",\n    \"scrollBackToContent\": \"콘텐츠 영역으로 스크롤하기\",\n    \"zoomIn\": \"확대\",\n    \"zoomOut\": \"축소\",\n    \"resetZoom\": \"확대/축소 초기화\",\n    \"menu\": \"메뉴\",\n    \"done\": \"완료\",\n    \"edit\": \"수정\",\n    \"undo\": \"실행 취소\",\n    \"redo\": \"다시 실행\",\n    \"resetLibrary\": \"라이브러리 리셋\",\n    \"createNewRoom\": \"방 만들기\",\n    \"fullScreen\": \"전체화면\",\n    \"darkMode\": \"다크 모드\",\n    \"lightMode\": \"밝은 모드\",\n    \"zenMode\": \"젠 모드\",\n    \"objectsSnapMode\": \"다른 요소들에 정렬시키기\",\n    \"exitZenMode\": \"젠 모드 종료하기\",\n    \"cancel\": \"취소\",\n    \"clear\": \"지우기\",\n    \"remove\": \"삭제\",\n    \"embed\": \"임베딩 토글\",\n    \"publishLibrary\": \"게시하기\",\n    \"submit\": \"제출\",\n    \"confirm\": \"확인\",\n    \"embeddableInteractionButton\": \"클릭하여 상호작용\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"모든 작업 내용이 초기화됩니다. 계속하시겠습니까?\",\n    \"couldNotCreateShareableLink\": \"공유 가능한 링크를 생성할 수 없습니다.\",\n    \"couldNotCreateShareableLinkTooBig\": \"공유 가능한 링크를 생성할 수 없습니다: 화면이 너무 큽니다.\",\n    \"couldNotLoadInvalidFile\": \"유효하지 않은 파일입니다.\",\n    \"importBackendFailed\": \"서버로부터 불러 오지 못했습니다.\",\n    \"cannotExportEmptyCanvas\": \"빈 캔버스를 내보낼 수 없습니다.\",\n    \"couldNotCopyToClipboard\": \"클립보드로 복사하지 못했습니다.\",\n    \"decryptFailed\": \"데이터를 복호화하지 못했습니다.\",\n    \"uploadedSecurly\": \"업로드는 종단 간 암호화로 보호되므로 Excalidraw 서버 및 타사가 콘텐츠를 읽을 수 없습니다.\",\n    \"loadSceneOverridePrompt\": \"외부 파일을 불러 오면 기존 콘텐츠가 대체됩니다. 계속 진행할까요?\",\n    \"collabStopOverridePrompt\": \"협업 세션을 종료하면 로컬 저장소에 있는 그림이 협업 세션의 그림으로 대체됩니다. 진행하겠습니까?\\n\\n(로컬 저장소에 있는 그림을 유지하려면 현재 브라우저 탭을 닫아주세요.)\",\n    \"errorAddingToLibrary\": \"아이템을 라이브러리에 추가 할수 없습니다\",\n    \"errorRemovingFromLibrary\": \"라이브러리에서 아이템을 삭제할수 없습니다\",\n    \"confirmAddLibrary\": \"{{numShapes}}개의 모양이 라이브러리에 추가됩니다. 계속하시겠어요?\",\n    \"imageDoesNotContainScene\": \"이 이미지는 화면 데이터를 포함하고 있지 않은 것 같습니다. 내보낼 때 화면을 첨부하도록 설정하셨나요?\",\n    \"cannotRestoreFromImage\": \"이미지 파일에서 화면을 복구할 수 없었습니다\",\n    \"invalidSceneUrl\": \"제공된 URL에서 화면을 가져오는데 실패했습니다. 주소가 잘못되거나, 유효한 Excalidraw JSON 데이터를 포함하고 있지 않은 것일 수 있습니다.\",\n    \"resetLibrary\": \"당신의 라이브러리를 초기화 합니다. 계속하시겠습니까?\",\n    \"removeItemsFromsLibrary\": \"{{count}}개의 아이템을 라이브러리에서 삭제하시겠습니까?\",\n    \"invalidEncryptionKey\": \"암호화 키는 반드시 22글자여야 합니다. 실시간 협업이 비활성화됩니다.\",\n    \"collabOfflineWarning\": \"인터넷에 연결되어 있지 않습니다.\\n변경 사항들이 저장되지 않습니다!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"지원하지 않는 파일 형식 입니다.\",\n    \"imageInsertError\": \"이미지를 삽입할 수 없습니다. 나중에 다시 시도 하십시오\",\n    \"fileTooBig\": \"파일이 너무 큽니다. 최대 크기는 {{maxSize}} 입니다.\",\n    \"svgImageInsertError\": \"SVG 이미지를 삽입하지 못했습니다. SVG 문법이 유효하지 않은 것 같습니다.\",\n    \"failedToFetchImage\": \"이미지를 가져오는데 실패했습니다.\",\n    \"invalidSVGString\": \"유효하지 않은 SVG입니다.\",\n    \"cannotResolveCollabServer\": \"협업 서버에 접속하는데 실패했습니다. 페이지를 새로고침하고 다시 시도해보세요.\",\n    \"importLibraryError\": \"라이브러리를 불러오지 못했습니다.\",\n    \"collabSaveFailed\": \"데이터베이스에 저장하지 못했습니다. 문제가 계속 된다면, 작업 내용을 잃지 않도록 로컬 저장소에 저장해 주세요.\",\n    \"collabSaveFailed_sizeExceeded\": \"데이터베이스에 저장하지 못했습니다. 캔버스가 너무 큰 거 같습니다. 문제가 계속 된다면, 작업 내용을 잃지 않도록 로컬 저장소에 저장해 주세요.\",\n    \"imageToolNotSupported\": \"이미지가 비활성화 되었습니다.\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"귀하께서는 <bold>강력한 지문 차단 설정</bold>이 활성화된 Brave browser를 사용하고 계신 것 같습니다.\",\n      \"line2\": \"이 기능으로 인해 화이트보드의 <bold>텍스트 요소들</bold>이 손상될 수 있습니다.\",\n      \"line3\": \"저희는 해당 기능을 비활성화하는 것을 강력히 권장 드립니다. 비활성화 방법에 대해서는 <link>이 게시글</link>을 참고해주세요.\",\n      \"line4\": \"만약 이 설정을 껐음에도 텍스트 요소들이 올바르게 표시되지 않는다면, 저희 Github에 <issueLink>이슈</issueLink>를 올려주시거나 <discordLink>Discord</discordLink>로 알려주세요.\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"임베드 요소들은 라이브러리에 추가할 수 없습니다.\",\n      \"iframe\": \"IFrame 요소들은 라이브러리에 추가할 수 없습니다.\",\n      \"image\": \"라이브러리에 이미지 삽입 기능은 곧 지원될 예정입니다!\"\n    },\n    \"asyncPasteFailedOnRead\": \"붙여넣는데 실패했습니다. (시스템 클립보드를 읽는데 실패했습니다)\",\n    \"asyncPasteFailedOnParse\": \"붙여넣는데 실패했습니다.\",\n    \"copyToSystemClipboardFailed\": \"클립보드로 복사하는데 실패했습니다.\"\n  },\n  \"toolBar\": {\n    \"selection\": \"선택\",\n    \"image\": \"이미지 삽입\",\n    \"rectangle\": \"사각형\",\n    \"diamond\": \"다이아몬드\",\n    \"ellipse\": \"타원\",\n    \"arrow\": \"화살표\",\n    \"line\": \"선\",\n    \"freedraw\": \"그리기\",\n    \"text\": \"텍스트\",\n    \"library\": \"라이브러리\",\n    \"lock\": \"선택된 도구 유지하기\",\n    \"penMode\": \"펜 모드 - 터치 방지\",\n    \"link\": \"선택한 도형에 대해서 링크를 추가/업데이트\",\n    \"eraser\": \"지우개\",\n    \"frame\": \"프레임 도구\",\n    \"magicframe\": \"와이어프레임을 코드로\",\n    \"embeddable\": \"웹 임베드\",\n    \"laser\": \"레이저 포인터\",\n    \"hand\": \"손 (패닝 도구)\",\n    \"extraTools\": \"다른 도구\",\n    \"mermaidToExcalidraw\": \"Mermaid에서 불러오기\",\n    \"magicSettings\": \"AI 설정\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"캔버스 동작\",\n    \"selectedShapeActions\": \"선택된 모양 동작\",\n    \"shapes\": \"모양\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"캔버스를 옮기려면 마우스 휠이나 스페이스바를 누르고 드래그하거나, 손 도구를 사용하기\",\n    \"linearElement\": \"여러 점을 연결하려면 클릭하고, 직선을 그리려면 바로 드래그하세요.\",\n    \"freeDraw\": \"클릭 후 드래그하세요. 완료되면 놓으세요.\",\n    \"text\": \"팁: 선택 툴로 아무 곳이나 더블 클릭해 텍스트를 추가할 수도 있습니다.\",\n    \"embeddable\": \"클릭 및 드래그하여 웹사이트 임베드 만들기\",\n    \"text_selected\": \"더블 클릭 또는 ENTER를 눌러서 텍스트 수정\",\n    \"text_editing\": \"ESC나 CtrlOrCmd+ENTER를 눌러서 수정을 종료하기\",\n    \"linearElementMulti\": \"마지막 지점을 클릭하거나 Esc 또는 Enter 키를 눌러 완료하세요.\",\n    \"lockAngle\": \"SHIFT 키를 누르면서 회전하면 각도를 제한할 수 있습니다.\",\n    \"resize\": \"SHIFT 키를 누르면서 조정하면 크기의 비율이 제한됩니다.\\nALT를 누르면서 조정하면 중앙을 기준으로 크기를 조정합니다.\",\n    \"resizeImage\": \"SHIFT를 눌러서 자유롭게 크기를 변경하거나,\\nALT를 눌러서 중앙을 고정하고 크기를 변경하기\",\n    \"rotate\": \"SHIFT 키를 누르면서 회전하면 각도를 제한할 수 있습니다.\",\n    \"lineEditor_info\": \"꼭짓점을 수정하려면 CtrlOrCmd 키를 누르고 더블 클릭을 하거나 CtrlOrCmd + Enter를 누르세요.\",\n    \"lineEditor_pointSelected\": \"Delete 키로 꼭짓점을 제거하거나,\\nCtrlOrCmd+D 로 복제하거나, 드래그 해서 이동시키기\",\n    \"lineEditor_nothingSelected\": \"꼭짓점을 선택해서 수정하거나 (SHIFT를 눌러서 여러개 선택),\\nAlt를 누르고 클릭해서 새로운 꼭짓점 추가하기\",\n    \"placeImage\": \"클릭해서 이미지를 배치하거나, 클릭하고 드래그해서 사이즈를 조정하기\",\n    \"publishLibrary\": \"당신만의 라이브러리를 게시하기\",\n    \"bindTextToElement\": \"Enter 키를 눌러서 텍스트 추가하기\",\n    \"deepBoxSelect\": \"CtrlOrCmd 키를 눌러서 깊게 선택하고, 드래그하지 않도록 하기\",\n    \"eraserRevert\": \"Alt를 눌러서 삭제하도록 지정된 요소를 되돌리기\",\n    \"firefox_clipboard_write\": \"이 기능은 설정에서 \\\"dom.events.asyncClipboard.clipboardItem\\\" 플래그를 \\\"true\\\"로 설정하여 활성화할 수 있습니다. Firefox에서 브라우저 플래그를 수정하려면, \\\"about:config\\\" 페이지에 접속하세요.\",\n    \"disableSnapping\": \"CtrlOrCmd 키를 눌러서 다른 요소와의 정렬 무시하기\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"미리보기를 볼 수 없습니다\",\n    \"canvasTooBig\": \"캔버스가 너무 큽니다.\",\n    \"canvasTooBigTip\": \"팁: 멀리 있는 요소들을 좀 더 가까이로 붙여 보세요.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"오류가 발생했습니다. <button>페이지 새로고침</button>\",\n    \"clearCanvasMessage\": \"새로고침으로 해결되지 않을 경우, <button>캔버스 비우기</button>\",\n    \"clearCanvasCaveat\": \" 작업 내용을 잃게 됩니다 \",\n    \"trackedToSentry\": \"오류 {{eventId}} 가 시스템에서 발견되었습니다.\",\n    \"openIssueMessage\": \"저희는 화면 정보를 오류에 포함하지 않도록 매우 주의하고 있습니다. 혹시 화면에 민감한 내용이 없다면 이곳에 업로드를 고려해주세요.<button>버그 트래커</button> 아래 정보를 GitHub 이슈에 복사 및 붙여넣기해 주세요.\",\n    \"sceneContent\": \"화면 내용:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"현재 화면에 공동 작업자를 초대해 협업할 수 있습니다.\",\n    \"desc_privacy\": \"안심하세요, 세션은 종단 간 암호화를 사용하므로 당신의 작업은 비공개로 유지되며 서버조차도 작업 내용을 알 수 없습니다.\",\n    \"button_startSession\": \"세션 시작\",\n    \"button_stopSession\": \"세션 중단\",\n    \"desc_inProgressIntro\": \"실시간 협업 세션이 진행 중입니다.\",\n    \"desc_shareLink\": \"공동 작업자에게 이 링크를 공유하세요.\",\n    \"desc_exitSession\": \"세션을 중단하면 연결은 끊어지나 작업을 이어갈 수 있습니다. 이 작업은 다른 작업자에게 영향을 미치지 않으며 각자의 공동 작업은 계속 유지됩니다.\",\n    \"shareTitle\": \"Excalidraw의 실시간 협업 세션에 참가하기\"\n  },\n  \"errorDialog\": {\n    \"title\": \"오류\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"디스크에 저장\",\n    \"disk_details\": \"나중에 다시 불러올 수 있도록 화면 데이터를 내보냅니다.\",\n    \"disk_button\": \"파일로 저장\",\n    \"link_title\": \"공유 가능한 링크 생성\",\n    \"link_details\": \"읽기 전용 링크로 내보냅니다.\",\n    \"link_button\": \"링크로 내보내기\",\n    \"excalidrawplus_description\": \"화면을 당신의 Excalidraw+ 작업 공간으로 저장합니다.\",\n    \"excalidrawplus_button\": \"내보내기\",\n    \"excalidrawplus_exportError\": \"지금은 Excalidraw+로 내보낼 수 없습니다...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"블로그 읽어보기\",\n    \"click\": \"클릭\",\n    \"deepSelect\": \"깊게 선택\",\n    \"deepBoxSelect\": \"영역을 깊게 선택하고, 드래그하지 않도록 하기\",\n    \"curvedArrow\": \"곡선 화살표\",\n    \"curvedLine\": \"곡선\",\n    \"documentation\": \"설명서\",\n    \"doubleClick\": \"더블 클릭\",\n    \"drag\": \"드래그\",\n    \"editor\": \"에디터\",\n    \"editLineArrowPoints\": \"직선 / 화살표 꼭짓점 수정\",\n    \"editText\": \"텍스트 수정 / 라벨 추가\",\n    \"github\": \"문제 제보하기\",\n    \"howto\": \"가이드 참고하기\",\n    \"or\": \"또는\",\n    \"preventBinding\": \"화살표가 붙지 않게 하기\",\n    \"tools\": \"도구\",\n    \"shortcuts\": \"키보드 단축키\",\n    \"textFinish\": \"편집 완료 (텍스트 에디터)\",\n    \"textNewLine\": \"줄바꿈(텍스트 에디터)\",\n    \"title\": \"도움말\",\n    \"view\": \"보기\",\n    \"zoomToFit\": \"모든 요소가 보이도록 확대/축소\",\n    \"zoomToSelection\": \"선택 영역으로 확대/축소\",\n    \"toggleElementLock\": \"선택한 항목을 잠금/잠금 해제\",\n    \"movePageUpDown\": \"페이지 움직이기 위/아래\",\n    \"movePageLeftRight\": \"페이지 움직이기 좌/우\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"캔버스 지우기\"\n  },\n  \"publishDialog\": {\n    \"title\": \"라이브러리 게시하기\",\n    \"itemName\": \"아이템 이름\",\n    \"authorName\": \"저자명\",\n    \"githubUsername\": \"깃허브 사용자이름\",\n    \"twitterUsername\": \"트위터 사용자이름\",\n    \"libraryName\": \"라이브러리 이름\",\n    \"libraryDesc\": \"라이브러리 설명\",\n    \"website\": \"웹사이트\",\n    \"placeholder\": {\n      \"authorName\": \"이름 또는 사용자명\",\n      \"libraryName\": \"당신의 라이브러리 이름\",\n      \"libraryDesc\": \"사람들에게 라이브러리의 용도를 알기 쉽게 설명해주세요\",\n      \"githubHandle\": \"GitHub 사용자명 (선택), 제출한 뒤에도 심사를 위해서 라이브러리를 수정할 때 사용됩니다\",\n      \"twitterHandle\": \"Twitter 사용자명 (선택), Twitter를 통해서 홍보할 때 제작자를 밝히기 위해 사용됩니다\",\n      \"website\": \"개인 웹사이트나 다른 어딘가의 링크 (선택)\"\n    },\n    \"errors\": {\n      \"required\": \"필수사항\",\n      \"website\": \"유효한 URL을 입력하세요\"\n    },\n    \"noteDescription\": \"당신의 라이브러리를 제출하여 <link>공개 라이브러리 저장소</link>에서 다른 사람들의 그림에 사용할 수 있도록 하세요.\",\n    \"noteGuidelines\": \"라이브러리는 먼저 수동으로 승인되어야 합니다. 제출하기 전에 <link>가이드라인</link>을 먼저 읽어보세요. 의견을 공유하거나 변경사항을 만들기 위해선 GitHub 계정이 필요하지만, 반드시 필요하진 않습니다.\",\n    \"noteLicense\": \"제출함으로써, 당신은 라이브러리가 <link>MIT 라이선스 </link>하에 배포됨을, 즉 아무나 제약 없이 사용할 수 있음에 동의합니다.\",\n    \"noteItems\": \"각각의 라이브러리는 분류할 수 있도록 고유한 이름을 가져야 합니다. 다음의 라이브러리 항목이 포함됩니다:\",\n    \"atleastOneLibItem\": \"최소한 하나의 라이브러리를 선택해주세요\",\n    \"republishWarning\": \"참고: 선택된 항목의 일부는 이미 제출/게시되었습니다. 기존의 라이브러리나 제출물을 업데이트하는 경우에만 제출하세요.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"라이브러리 제출됨\",\n    \"content\": \"{{authorName}}님 감사합니다. 당신의 라이브러리가 심사를 위해 제출되었습니다. 진행 상황을<link>여기에서 확인하실 수 있습니다.</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"라이브러리 리셋\",\n    \"removeItemsFromLib\": \"선택한 항목을 라이브러리에서 제거\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"이미지 내보내기\",\n    \"label\": {\n      \"withBackground\": \"배경\",\n      \"onlySelected\": \"선택한 항목만\",\n      \"darkMode\": \"다크 모드\",\n      \"embedScene\": \"화면을 담기\",\n      \"scale\": \"크기\",\n      \"padding\": \"여백\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"화면 정보가 내보내는 PNG/SVG 파일에 저장되어 이후에 파일에서 화면을 복구할 수 있습니다. 파일 크기가 증가합니다.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"PNG로 내보내기\",\n      \"exportToSvg\": \"SVG로 내보내기\",\n      \"copyPngToClipboard\": \"클립보드로 PNG 복사\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"클립보드로 복사\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"그림은 종단 간 암호화되므로 Excalidraw의 서버는 절대로 내용을 알 수 없습니다.\",\n    \"link\": \"Excalidraw의 종단 간 암호화에 대한 블로그 포스트\"\n  },\n  \"stats\": {\n    \"angle\": \"각도\",\n    \"element\": \"요소\",\n    \"elements\": \"요소\",\n    \"height\": \"높이\",\n    \"scene\": \"화면\",\n    \"selected\": \"선택됨\",\n    \"storage\": \"저장공간\",\n    \"title\": \"덕후들을 위한 통계\",\n    \"total\": \"합계\",\n    \"version\": \"버전\",\n    \"versionCopy\": \"복사하려면 클릭\",\n    \"versionNotAvailable\": \"해당 버전 사용 불가능\",\n    \"width\": \"너비\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"라이브러리에 추가되었습니다\",\n    \"copyStyles\": \"스타일 복사.\",\n    \"copyToClipboard\": \"클립보드로 복사.\",\n    \"copyToClipboardAsPng\": \"{{exportSelection}}를 클립보드에 PNG로 복사했습니다\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"파일이 저장되었습니다.\",\n    \"fileSavedToFilename\": \"{filename} 로 저장되었습니다\",\n    \"canvas\": \"캔버스\",\n    \"selection\": \"선택한 요소\",\n    \"pasteAsSingleElement\": \"단일 요소로 붙여넣거나, 기존 텍스트 에디터에 붙여넣으려면 {{shortcut}} 을 사용하세요.\",\n    \"unableToEmbed\": \"이 URL의 임베딩이 허용되지 않았습니다. GitHub에 이슈를 남겨서 이 URL이 화이트리스트에 등재될 수 있도록 요청하세요\",\n    \"unrecognizedLinkFormat\": \"임베딩하려는 링크의 형식이 잘못된 것 같습니다. 원본 사이트에서 제공하는 \\\"임베딩\\\" 텍스트를 그대로 붙여 넣어 주세요\"\n  },\n  \"colors\": {\n    \"transparent\": \"투명\",\n    \"black\": \"블랙\",\n    \"white\": \"화이트\",\n    \"red\": \"레드\",\n    \"pink\": \"핑크\",\n    \"grape\": \"그레이프\",\n    \"violet\": \"바이올렛\",\n    \"gray\": \"그레이\",\n    \"blue\": \"블루\",\n    \"cyan\": \"시안\",\n    \"teal\": \"틸\",\n    \"green\": \"그린\",\n    \"yellow\": \"옐로우\",\n    \"orange\": \"오렌지\",\n    \"bronze\": \"브론즈\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"모든 데이터는 브라우저에 안전하게 저장됩니다.\",\n      \"center_heading_plus\": \"대신 Excalidraw+로 이동하시겠습니까?\",\n      \"menuHint\": \"내보내기, 설정, 언어, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"내보내기, 설정, 등등...\",\n      \"center_heading\": \"간단하게 만드는 다이어그램.\",\n      \"toolbarHint\": \"도구를 선택하고, 그리세요!\",\n      \"helpHint\": \"단축키 & 도움말\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"가장 많이 사용된 색상들\",\n    \"colors\": \"색상\",\n    \"shades\": \"색조\",\n    \"hexCode\": \"Hex 코드\",\n    \"noShades\": \"사용할 수 있는 색조가 없음\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"이미지로 내보내기\",\n        \"button\": \"이미지로 내보내기\",\n        \"description\": \"나중에 다시 불러올 수 있도록 화면 데이터를 이미지로 내보냅니다.\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"디스크에 저장\",\n        \"button\": \"디스크에 저장\",\n        \"description\": \"나중에 다시 불러올 수 있도록 화면 데이터를 내보냅니다.\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Excalidraw+로 내보내기\",\n        \"description\": \"화면을 당신의 Excalidraw+ 작업 공간으로 저장합니다.\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"파일에서 불러오기\",\n        \"button\": \"파일에서 불러오기\",\n        \"description\": \"파일을 불러오면 <bold>현재 작성된 데이터를 덮어쓰게 됩니다</bold>.<br></br>다음 옵션 중 하나를 선택하여 작업물을 백업해 둘 수 있습니다.\"\n      },\n      \"shareableLink\": {\n        \"title\": \"주소에서 불러오기\",\n        \"button\": \"컨텐츠를 덮어쓰기\",\n        \"description\": \"외부 작업물을 불러오면 <bold>현재 작성된 데이터를 덮어쓰게 됩니다</bold>.<br></br>다음 옵션 중 하나를 선택하여 작업물을 백업해 둘 수 있습니다.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"Mermaid에서 불러오기\",\n    \"button\": \"삽입하기\",\n    \"description\": \"지금은 <flowchartLink>순서도</flowchartLink>,<sequenceLink> 시퀀스</sequenceLink>, <classLink>클래스 </classLink>다이어그램만 지원합니다. 다른 형식들은 Excalidraw에서는 이미지로 표시됩니다.\",\n    \"syntax\": \"Mermaid 구문\",\n    \"preview\": \"미리보기\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}