import "../chunk-XDFCUUT6.js";

// locales/es-ES.json
var labels = {
  paste: "Pegar",
  pasteAsPlaintext: "Pegar como texto sin formato",
  pasteCharts: "Pegar gr\xE1ficos",
  selectAll: "Seleccionar todo",
  multiSelect: "A\xF1adir elemento a la selecci\xF3n",
  moveCanvas: "Mover el lienzo",
  cut: "Cortar",
  copy: "Copiar",
  copyAsPng: "Copiar al portapapeles como PNG",
  copyAsSvg: "Copiar al portapapeles como SVG",
  copyText: "Copiar al portapapeles como texto",
  copySource: "Copiar fuente al portapapeles",
  convertToCode: "Convertir a c\xF3digo",
  bringForward: "Traer hacia delante",
  sendToBack: "Enviar al fondo",
  bringToFront: "Traer al frente",
  sendBackward: "Enviar atr\xE1s",
  delete: "Bo<PERSON><PERSON>",
  copyStyles: "Copiar estilos",
  pasteStyles: "Pegar estilos",
  stroke: "Trazo",
  background: "Fondo",
  fill: "<PERSON>llen<PERSON>",
  strokeWidth: "<PERSON><PERSON>or del trazo",
  strokeStyle: "Estilo del trazo",
  strokeStyle_solid: "S\xF3lido",
  strokeStyle_dashed: "Discontinua",
  strokeStyle_dotted: "Punteado",
  sloppiness: "Estilo de trazo",
  opacity: "Opacidad",
  textAlign: "Alineado de texto",
  edges: "Bordes",
  sharp: "Afilado",
  round: "Redondo",
  arrowheads: "Puntas de flecha",
  arrowhead_none: "Ninguna",
  arrowhead_arrow: "Flecha",
  arrowhead_bar: "Barra",
  arrowhead_circle: "C\xEDrculo",
  arrowhead_circle_outline: "C\xEDrculo (contorno)",
  arrowhead_triangle: "Tri\xE1ngulo",
  arrowhead_triangle_outline: "Tri\xE1ngulo (contorno)",
  arrowhead_diamond: "Diamante",
  arrowhead_diamond_outline: "Diamante (contorno)",
  fontSize: "Tama\xF1o de la fuente",
  fontFamily: "Tipo de fuente",
  addWatermark: 'Agregar "Hecho con Excalidraw"',
  handDrawn: "Dibujado a mano",
  normal: "Normal",
  code: "C\xF3digo",
  small: "Peque\xF1a",
  medium: "Mediana",
  large: "Grande",
  veryLarge: "Muy grande",
  solid: "S\xF3lido",
  hachure: "Folleto",
  zigzag: "Zigzag",
  crossHatch: "Rayado transversal",
  thin: "Fino",
  bold: "Grueso",
  left: "Izquierda",
  center: "Centrado",
  right: "Derecha",
  extraBold: "Extra negrita",
  architect: "Arquitecto",
  artist: "Artista",
  cartoonist: "Caricatura",
  fileTitle: "Nombre del archivo",
  colorPicker: "Selector de color",
  canvasColors: "Usado en lienzo",
  canvasBackground: "Fondo del lienzo",
  drawingCanvas: "Lienzo de dibujo",
  layers: "Capas",
  actions: "Acciones",
  language: "Idioma",
  liveCollaboration: "Colaboraci\xF3n en directo...",
  duplicateSelection: "Duplicar",
  untitled: "Sin t\xEDtulo",
  name: "Nombre",
  yourName: "Tu nombre",
  madeWithExcalidraw: "Hecho con Excalidraw",
  group: "Agrupar selecci\xF3n",
  ungroup: "Desagrupar selecci\xF3n",
  collaborators: "Colaboradores",
  showGrid: "Mostrar cuadr\xEDcula",
  addToLibrary: "A\xF1adir a la biblioteca",
  removeFromLibrary: "Eliminar de la biblioteca",
  libraryLoadingMessage: "Cargando biblioteca\u2026",
  libraries: "Explorar bibliotecas",
  loadingScene: "Cargando escena\u2026",
  align: "Alinear",
  alignTop: "Alineaci\xF3n superior",
  alignBottom: "Alineaci\xF3n inferior",
  alignLeft: "Alinear a la izquierda",
  alignRight: "Alinear a la derecha",
  centerVertically: "Centrar verticalmente",
  centerHorizontally: "Centrar horizontalmente",
  distributeHorizontally: "Distribuir horizontalmente",
  distributeVertically: "Distribuir verticalmente",
  flipHorizontal: "Girar horizontalmente",
  flipVertical: "Girar verticalmente",
  viewMode: "Modo presentaci\xF3n",
  share: "Compartir",
  showStroke: "Mostrar selector de color de trazo",
  showBackground: "Mostrar el selector de color de fondo",
  toggleTheme: "Cambiar tema",
  personalLib: "Biblioteca personal",
  excalidrawLib: "Biblioteca Excalidraw",
  decreaseFontSize: "Disminuir tama\xF1o de letra",
  increaseFontSize: "Aumentar el tama\xF1o de letra",
  unbindText: "Desvincular texto",
  bindText: "Vincular texto al contenedor",
  createContainerFromText: "Envolver el texto en un contenedor",
  link: {
    edit: "Editar enlace",
    editEmbed: "Editar enlace e incrustar",
    create: "Crear enlace",
    createEmbed: "Crear enlace e incrustar",
    label: "Enlace",
    labelEmbed: "Enlazar e incrustar",
    empty: "No se ha establecido un enlace"
  },
  lineEditor: {
    edit: "Editar l\xEDnea",
    exit: "Salir del editor en l\xEDnea"
  },
  elementLock: {
    lock: "Bloquear",
    unlock: "Desbloquear",
    lockAll: "Bloquear todo",
    unlockAll: "Desbloquear todo"
  },
  statusPublished: "Publicado",
  sidebarLock: "Mantener barra lateral abierta",
  selectAllElementsInFrame: "Seleccionar todos los elementos en el marco",
  removeAllElementsFromFrame: "Eliminar todos los elementos del marco",
  eyeDropper: "Seleccionar un color del lienzo",
  textToDiagram: "Texto a diagrama",
  prompt: "Sugerencia"
};
var library = {
  noItems: "No hay elementos a\xF1adidos todav\xEDa...",
  hint_emptyLibrary: "Seleccione un elemento en el lienzo para a\xF1adirlo aqu\xED, o instale una biblioteca del repositorio p\xFAblico, a continuaci\xF3n.",
  hint_emptyPrivateLibrary: "Seleccione un elemento del lienzo para a\xF1adirlo aqu\xED."
};
var buttons = {
  clearReset: "Limpiar lienzo y reiniciar el color de fondo",
  exportJSON: "Exportar a archivo",
  exportImage: "Exportar imagen...",
  export: "Guardar en...",
  copyToClipboard: "Copiar al portapapeles",
  save: "Guardar en archivo actual",
  saveAs: "Guardar como",
  load: "Abrir",
  getShareableLink: "Obtener enlace para compartir",
  close: "Cerrar",
  selectLanguage: "Elegir idioma",
  scrollBackToContent: "Volver al contenido",
  zoomIn: "Acercarse",
  zoomOut: "Alejarse",
  resetZoom: "Restablecer zoom",
  menu: "Men\xFA",
  done: "Hecho",
  edit: "Editar",
  undo: "Deshacer",
  redo: "Rehacer",
  resetLibrary: "Reiniciar biblioteca",
  createNewRoom: "Crear nueva sala",
  fullScreen: "Pantalla completa",
  darkMode: "Modo oscuro",
  lightMode: "Modo claro",
  zenMode: "Modo Zen",
  objectsSnapMode: "Ajustar a los objetos",
  exitZenMode: "Salir del modo Zen",
  cancel: "Cancelar",
  clear: "Borrar",
  remove: "Eliminar",
  embed: "",
  publishLibrary: "Publicar",
  submit: "Enviar",
  confirm: "Confirmar",
  embeddableInteractionButton: "Pulsa para interactuar"
};
var alerts = {
  clearReset: "Esto limpiar\xE1 todo el lienzo. Est\xE1s seguro?",
  couldNotCreateShareableLink: "No se pudo crear un enlace para compartir.",
  couldNotCreateShareableLinkTooBig: "No se pudo crear el enlace para compartir: la escena es demasiado grande",
  couldNotLoadInvalidFile: "No se pudo cargar el archivo no v\xE1lido",
  importBackendFailed: "La importaci\xF3n fall\xF3.",
  cannotExportEmptyCanvas: "No se puede exportar un lienzo vaci\xF3",
  couldNotCopyToClipboard: "No se pudo copiar al portapapeles.",
  decryptFailed: "No se pudieron descifrar los datos.",
  uploadedSecurly: "La carga ha sido asegurada con cifrado de principio a fin, lo que significa que el servidor de Excalidraw y terceros no pueden leer el contenido.",
  loadSceneOverridePrompt: "Si carga este dibujo externo, reemplazar\xE1 el que tiene. \xBFDesea continuar?",
  collabStopOverridePrompt: "Detener la sesi\xF3n sobrescribir\xE1 su dibujo anterior almacenado localmente. \xBFEst\xE1 seguro?\n\n(Si desea mantener su dibujo local, simplemente cierre la pesta\xF1a del navegador.)",
  errorAddingToLibrary: "No se pudo agregar el elemento a la biblioteca",
  errorRemovingFromLibrary: "No se pudo quitar el elemento de la biblioteca",
  confirmAddLibrary: "Esto a\xF1adir\xE1 {{numShapes}} forma(s) a tu biblioteca. \xBFEst\xE1s seguro?",
  imageDoesNotContainScene: "Esta imagen no parece contener datos de escena. \xBFHa habilitado la inserci\xF3n de la escena durante la exportaci\xF3n?",
  cannotRestoreFromImage: "No se pudo restaurar la escena desde este archivo de imagen",
  invalidSceneUrl: "No se ha podido importar la escena desde la URL proporcionada. Est\xE1 mal formada, o no contiene datos de Excalidraw JSON v\xE1lidos.",
  resetLibrary: "Esto borrar\xE1 tu biblioteca. \xBFEst\xE1s seguro?",
  removeItemsFromsLibrary: "\xBFEliminar {{count}} elemento(s) de la biblioteca?",
  invalidEncryptionKey: "La clave de cifrado debe tener 22 caracteres. La colaboraci\xF3n en vivo est\xE1 deshabilitada.",
  collabOfflineWarning: "No hay conexi\xF3n a internet disponible.\n\xA1No se guardar\xE1n los cambios!"
};
var errors = {
  unsupportedFileType: "Tipo de archivo no admitido.",
  imageInsertError: "No se pudo insertar la imagen. Int\xE9ntelo de nuevo m\xE1s tarde...",
  fileTooBig: "Archivo demasiado grande. El tama\xF1o m\xE1ximo permitido es {{maxSize}}.",
  svgImageInsertError: "No se pudo insertar la imagen SVG. El c\xF3digo SVG parece inv\xE1lido.",
  failedToFetchImage: "Error al obtener la imagen.",
  invalidSVGString: "SVG no v\xE1lido.",
  cannotResolveCollabServer: "No se pudo conectar al servidor colaborador. Por favor, vuelva a cargar la p\xE1gina y vuelva a intentarlo.",
  importLibraryError: "No se pudo cargar la librer\xEDa",
  collabSaveFailed: "No se pudo guardar en la base de datos del backend. Si los problemas persisten, deber\xEDa guardar su archivo localmente para asegurarse de que no pierde su trabajo.",
  collabSaveFailed_sizeExceeded: "No se pudo guardar en la base de datos del backend, el lienzo parece ser demasiado grande. Deber\xEDa guardar el archivo localmente para asegurarse de que no pierde su trabajo.",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "Parece que est\xE1s usando el navegador Brave con el ajuste <bold>Forzar el bloqueo de huellas digitales</bold> habilitado.",
    line2: "Esto podr\xEDa resultar en errores en los <bold>Elementos de Texto</bold> en tus dibujos.",
    line3: "Recomendamos fuertemente deshabilitar esta configuraci\xF3n. Puedes seguir <link>estos pasos</link> sobre c\xF3mo hacerlo.",
    line4: ""
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "Los elementos IFrame no se pueden agregar a la biblioteca.",
    image: ""
  },
  asyncPasteFailedOnRead: "No se pudo pegar (no se pudo leer desde el portapapeles del sistema).",
  asyncPasteFailedOnParse: "No se pudo pegar.",
  copyToSystemClipboardFailed: "No se pudo copiar al portapapeles."
};
var toolBar = {
  selection: "Selecci\xF3n",
  image: "Insertar imagen",
  rectangle: "Rect\xE1ngulo",
  diamond: "Diamante",
  ellipse: "Elipse",
  arrow: "Flecha",
  line: "L\xEDnea",
  freedraw: "Dibujar",
  text: "Texto",
  library: "Biblioteca",
  lock: "Mantener la herramienta seleccionada activa despu\xE9s de dibujar",
  penMode: "Modo L\xE1piz - previene toque",
  link: "A\xF1adir/Actualizar enlace para una forma seleccionada",
  eraser: "Borrar",
  frame: "",
  magicframe: "Esquema a c\xF3digo",
  embeddable: "Incrustar Web",
  laser: "Puntero l\xE1ser",
  hand: "Mano (herramienta de panoramizaci\xF3n)",
  extraTools: "M\xE1s herramientas",
  mermaidToExcalidraw: "Mermaid a Excalidraw",
  magicSettings: "Ajustes AI"
};
var headings = {
  canvasActions: "Acciones del lienzo",
  selectedShapeActions: "Acciones de la forma seleccionada",
  shapes: "Formas"
};
var hints = {
  canvasPanning: "Para mover el lienzo, mantenga la rueda del rat\xF3n o la barra espaciadora mientras arrastra o utilice la herramienta de mano",
  linearElement: "Haz clic para dibujar m\xFAltiples puntos, arrastrar para solo una l\xEDnea",
  freeDraw: "Haz clic y arrastra, suelta al terminar",
  text: "Consejo: tambi\xE9n puedes a\xF1adir texto haciendo doble clic en cualquier lugar con la herramienta de selecci\xF3n",
  embeddable: "Haga clic y arrastre para crear un sitio web incrustado",
  text_selected: "Doble clic o pulse ENTER para editar el texto",
  text_editing: "Pulse Escape o Ctrl/Cmd + ENTER para terminar de editar",
  linearElementMulti: "Haz clic en el \xFAltimo punto o presiona Escape o Enter para finalizar",
  lockAngle: "Puedes restringir el \xE1ngulo manteniendo presionado el bot\xF3n SHIFT",
  resize: "Para mantener las proporciones mant\xE9n SHIFT presionado mientras modificas el tama\xF1o, \nmant\xE9n presionado ALT para modificar el tama\xF1o desde el centro",
  resizeImage: "Puede redimensionar libremente pulsando SHIFT,\npulse ALT para redimensionar desde el centro",
  rotate: "Puedes restringir los \xE1ngulos manteniendo presionado SHIFT mientras giras",
  lineEditor_info: "Mantenga pulsado CtrlOrCmd y haga doble click o presione CtrlOrCmd + Enter para editar puntos",
  lineEditor_pointSelected: "Presione Suprimir para eliminar el/los punto(s), CtrlOrCmd+D para duplicarlo, o arr\xE1strelo para moverlo",
  lineEditor_nothingSelected: "Seleccione un punto a editar (mantenga MAY\xDASCULAS para seleccionar m\xFAltiples),\no mantenga pulsado Alt y haga click para a\xF1adir nuevos puntos",
  placeImage: "Haga clic para colocar la imagen o haga click y arrastre para establecer su tama\xF1o manualmente",
  publishLibrary: "Publica tu propia biblioteca",
  bindTextToElement: "Presione Entrar para agregar",
  deepBoxSelect: "Mant\xE9n CtrlOrCmd para seleccionar en profundidad, y para evitar arrastrar",
  eraserRevert: "Mantenga pulsado Alt para revertir los elementos marcados para su eliminaci\xF3n",
  firefox_clipboard_write: 'Esta caracter\xEDstica puede ser habilitada estableciendo la bandera "dom.events.asyncClipboard.clipboardItem" a "true". Para cambiar las banderas del navegador en Firefox, visite la p\xE1gina "about:config".',
  disableSnapping: "Mant\xE9n pulsado CtrlOrCmd para desactivar el ajuste"
};
var canvasError = {
  cannotShowPreview: "No se puede mostrar la vista previa",
  canvasTooBig: "El lienzo podr\xEDa ser demasiado grande.",
  canvasTooBigTip: "Sugerencia: intenta acercar un poco m\xE1s los elementos m\xE1s lejanos."
};
var errorSplash = {
  headingMain: "Se encontr\xF3 un error. Intente <button>recargando la p\xE1gina.</button>",
  clearCanvasMessage: "Si la recarga no funciona, intente <button>limpiando el lienzo.</button>",
  clearCanvasCaveat: " Esto provocar\xE1 la p\xE9rdida de su trabajo ",
  trackedToSentry: "El error con el identificador {{eventId}} fue rastreado en nuestro sistema.",
  openIssueMessage: "Fuimos muy cautelosos de no incluir la informaci\xF3n de tu escena en el error. Si tu escena no es privada, por favor considera seguir nuestro <button>rastreador de errores.</button> Por favor, incluya la siguiente informaci\xF3n copi\xE1ndola y peg\xE1ndola en el issue de GitHub.",
  sceneContent: "Contenido de la escena:"
};
var roomDialog = {
  desc_intro: "Puede invitar a otras personas a tu actual escena para que colaboren contigo.",
  desc_privacy: "No te preocupes, la sesi\xF3n usa encriptaci\xF3n de punta a punta, por lo que todo lo que se dibuje se mantendr\xE1 privadamente. Ni siquiera nuestro servidor podr\xE1 ver lo que haces.",
  button_startSession: "Iniciar sesi\xF3n",
  button_stopSession: "Detener sesi\xF3n",
  desc_inProgressIntro: "La sesi\xF3n de colaboraci\xF3n en vivo est\xE1 ahora en progreso.",
  desc_shareLink: "Comparte este enlace con cualquier persona con quien quieras colaborar:",
  desc_exitSession: "Detener la sesi\xF3n te desconectar\xE1 de la sala, pero podr\xE1s seguir trabajando con la escena en su computadora, esto es de modo local. Ten en cuenta que esto no afectar\xE1 a otras personas, y que las mismas seguir\xE1n siendo capaces de colaborar en tu escena.",
  shareTitle: "\xDAnase a una sesi\xF3n colaborativa en vivo en Excalidraw"
};
var errorDialog = {
  title: "Error"
};
var exportDialog = {
  disk_title: "Guardar en disco",
  disk_details: "Exportar los datos de la escena a un archivo desde el cual pueda importar m\xE1s tarde.",
  disk_button: "Guardar en archivo",
  link_title: "Enlace para compartir",
  link_details: "Exportar como enlace de s\xF3lo lectura.",
  link_button: "Exportar a Link",
  excalidrawplus_description: "Guarde la escena en su espacio de trabajo de Excalidraw+.",
  excalidrawplus_button: "Exportar",
  excalidrawplus_exportError: "No se pudo exportar a Excalidraw+ en este momento..."
};
var helpDialog = {
  blog: "Lea nuestro blog",
  click: "click",
  deepSelect: "Selecci\xF3n profunda",
  deepBoxSelect: "Seleccione en profundidad dentro de la caja, y evite arrastrar",
  curvedArrow: "Flecha curva",
  curvedLine: "L\xEDnea curva",
  documentation: "Documentaci\xF3n",
  doubleClick: "doble clic",
  drag: "arrastrar",
  editor: "Editor",
  editLineArrowPoints: "Editar puntos de l\xEDnea/flecha",
  editText: "Editar texto / a\xF1adir etiqueta",
  github: "\xBFHa encontrado un problema? Env\xEDelo",
  howto: "Siga nuestras gu\xEDas",
  or: "o",
  preventBinding: "Evitar enlace de flechas",
  tools: "Herramientas",
  shortcuts: "Atajos del teclado",
  textFinish: "Finalizar edici\xF3n (editor de texto)",
  textNewLine: "A\xF1adir nueva linea (editor de texto)",
  title: "Ayuda",
  view: "Vista",
  zoomToFit: "Ajustar la vista para mostrar todos los elementos",
  zoomToSelection: "Ampliar selecci\xF3n",
  toggleElementLock: "Bloquear/desbloquear selecci\xF3n",
  movePageUpDown: "Mover p\xE1gina hacia arriba/abajo",
  movePageLeftRight: "Mover p\xE1gina hacia la izquierda/derecha"
};
var clearCanvasDialog = {
  title: "Borrar lienzo"
};
var publishDialog = {
  title: "Publicar biblioteca",
  itemName: "Nombre del art\xEDculo",
  authorName: "Nombre del autor",
  githubUsername: "Nombre de usuario de GitHub",
  twitterUsername: "Nombre de usuario de Twitter",
  libraryName: "Nombre de la biblioteca",
  libraryDesc: "Descripci\xF3n de la biblioteca",
  website: "Sitio Web",
  placeholder: {
    authorName: "Nombre o nombre de usuario",
    libraryName: "Nombre de tu biblioteca",
    libraryDesc: "Descripci\xF3n de su biblioteca para ayudar a la gente a entender su uso",
    githubHandle: "Nombre de usuario de GitHub (opcional), as\xED podr\xE1 editar la biblioteca una vez enviada para su revisi\xF3n",
    twitterHandle: "Nombre de usuario de Twitter (opcional), as\xED sabemos a qui\xE9n acreditar cuando se promociona en Twitter",
    website: "Enlace a su sitio web personal o en cualquier otro lugar (opcional)"
  },
  errors: {
    required: "Requerido",
    website: "Introduce una URL v\xE1lida"
  },
  noteDescription: "Env\xEDa tu biblioteca para ser incluida en el <link>repositorio de librer\xEDa p\xFAblica</link>para que otras personas utilicen en sus dibujos.",
  noteGuidelines: "La biblioteca debe ser aprobada manualmente primero. Por favor, lea la <link>pautas</link> antes de enviar. Necesitar\xE1 una cuenta de GitHub para comunicarse y hacer cambios si se solicita, pero no es estrictamente necesario.",
  noteLicense: "Al enviar, usted acepta que la biblioteca se publicar\xE1 bajo el <link>Licencia MIT </link>que en breve significa que cualquiera puede utilizarlos sin restricciones.",
  noteItems: "Cada elemento de la biblioteca debe tener su propio nombre para que sea filtrable. Los siguientes elementos de la biblioteca ser\xE1n incluidos:",
  atleastOneLibItem: "Por favor, seleccione al menos un elemento de la biblioteca para empezar",
  republishWarning: "Nota: algunos de los elementos seleccionados est\xE1n marcados como ya publicados/enviados. S\xF3lo deber\xEDa volver a enviar elementos cuando se actualice una biblioteca o env\xEDo."
};
var publishSuccessDialog = {
  title: "Biblioteca enviada",
  content: "Gracias {{authorName}}. Su biblioteca ha sido enviada para ser revisada. Puede seguir el estado<link>aqu\xED</link>"
};
var confirmDialog = {
  resetLibrary: "Reiniciar biblioteca",
  removeItemsFromLib: "Eliminar elementos seleccionados de la biblioteca"
};
var imageExportDialog = {
  header: "Exportar imagen",
  label: {
    withBackground: "Fondo",
    onlySelected: "S\xF3lo seleccionados",
    darkMode: "Modo oscuro",
    embedScene: "Incrustar escena",
    scale: "Escalar",
    padding: "Espaciado"
  },
  tooltip: {
    embedScene: ""
  },
  title: {
    exportToPng: "Exportar a PNG",
    exportToSvg: "Exportar a SVG",
    copyPngToClipboard: "Copiar PNG al portapapeles"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "Copiar al portapapeles"
  }
};
var encrypted = {
  tooltip: "Tus dibujos est\xE1n cifrados de punto a punto, por lo que los servidores de Excalidraw nunca los ver\xE1n.",
  link: "Entrada en el blog sobre cifrado de extremo a extremo"
};
var stats = {
  angle: "\xC1ngulo",
  element: "Elemento",
  elements: "Elementos",
  height: "Alto",
  scene: "Escena",
  selected: "Seleccionado",
  storage: "Almacenamiento",
  title: "Estad\xEDsticas para nerds",
  total: "Total",
  version: "Versi\xF3n",
  versionCopy: "Click para copiar",
  versionNotAvailable: "Versi\xF3n no disponible",
  width: "Ancho"
};
var toast = {
  addedToLibrary: "A\xF1adido a la biblioteca",
  copyStyles: "Estilos copiados.",
  copyToClipboard: "Copiado en el portapapeles.",
  copyToClipboardAsPng: "Copiado {{exportSelection}} al portapapeles como PNG\n({{exportColorScheme}})",
  fileSaved: "Archivo guardado.",
  fileSavedToFilename: "Guardado en {filename}",
  canvas: "lienzo",
  selection: "selecci\xF3n",
  pasteAsSingleElement: "Usa {{shortcut}} para pegar como un solo elemento,\no pegar en un editor de texto existente",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "Transparente",
  black: "Negro",
  white: "Blanco",
  red: "Rojo",
  pink: "Rosa",
  grape: "Uva",
  violet: "Violeta",
  gray: "Gris",
  blue: "Azul",
  cyan: "Cian",
  teal: "Turquesa",
  green: "Verde",
  yellow: "Amarillo",
  orange: "Naranja",
  bronze: "Bronce"
};
var welcomeScreen = {
  app: {
    center_heading: "Toda su informaci\xF3n es guardada localmente en su navegador.",
    center_heading_plus: "\xBFQuieres ir a Excalidraw+?",
    menuHint: "Exportar, preferencias, idiomas, ..."
  },
  defaults: {
    menuHint: "Exportar, preferencias y m\xE1s...",
    center_heading: "Diagramas. Hecho. Simplemente.",
    toolbarHint: "\xA1Elige una herramienta y empieza a dibujar!",
    helpHint: "Atajos y ayuda"
  }
};
var colorPicker = {
  mostUsedCustomColors: "Colores personalizados m\xE1s utilizados",
  colors: "Colores",
  shades: "",
  hexCode: "C\xF3digo Hexadecimal",
  noShades: ""
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "Exportar como imagen",
      button: "Exportar como imagen",
      description: ""
    },
    saveToDisk: {
      title: "Guardar en el disco",
      button: "Guardar en el disco",
      description: "Exporta los datos de la escena a un archivo desde el cual podr\xE1s importar m\xE1s tarde."
    },
    excalidrawPlus: {
      title: "",
      button: "Exportar a Excalidraw+",
      description: ""
    }
  },
  modal: {
    loadFromFile: {
      title: "Cargar desde un archivo",
      button: "Cargar desde un archivo",
      description: ""
    },
    shareableLink: {
      title: "Cargar desde un enlace",
      button: "Reemplazar mi contenido",
      description: "Cargar un dibujo externo <bold>reemplazar\xE1 tu contenido existente</bold>.<br></br>Puedes primero hacer una copia de seguridad de tu dibujo usando una de las opciones de abajo."
    }
  }
};
var mermaid = {
  title: "Mermaid a Excalidraw",
  button: "Insertar",
  description: "Actualmente s\xF3lo <flowchartLink>Flowchart</flowchartLink>,<sequenceLink> Secuencia, </sequenceLink> y <classLink>Class </classLink>Diagramas son soportados. Los otros tipos se renderizar\xE1n como imagen en Excalidraw.",
  syntax: "Sintaxis Mermaid",
  preview: "Vista previa"
};
var es_ES_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  es_ES_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=es-ES-AQYVXC32.js.map
