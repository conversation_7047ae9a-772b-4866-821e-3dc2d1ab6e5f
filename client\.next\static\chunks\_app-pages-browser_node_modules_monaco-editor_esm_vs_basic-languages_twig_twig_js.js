"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_twig_twig_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/twig/twig.js":
/*!*************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/twig/twig.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/twig/twig.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  comments: {\n    blockComment: [\"{#\", \"#}\"]\n  },\n  brackets: [\n    [\"{#\", \"#}\"],\n    [\"{%\", \"%}\"],\n    [\"{{\", \"}}\"],\n    [\"(\", \")\"],\n    [\"[\", \"]\"],\n    // HTML\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{# \", close: \" #}\" },\n    { open: \"{% \", close: \" %}\" },\n    { open: \"{{ \", close: \" }}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    // HTML\n    { open: \"<\", close: \">\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  ignoreCase: true,\n  keywords: [\n    // (opening) tags\n    \"apply\",\n    \"autoescape\",\n    \"block\",\n    \"deprecated\",\n    \"do\",\n    \"embed\",\n    \"extends\",\n    \"flush\",\n    \"for\",\n    \"from\",\n    \"if\",\n    \"import\",\n    \"include\",\n    \"macro\",\n    \"sandbox\",\n    \"set\",\n    \"use\",\n    \"verbatim\",\n    \"with\",\n    // closing tags\n    \"endapply\",\n    \"endautoescape\",\n    \"endblock\",\n    \"endembed\",\n    \"endfor\",\n    \"endif\",\n    \"endmacro\",\n    \"endsandbox\",\n    \"endset\",\n    \"endwith\",\n    // literals\n    \"true\",\n    \"false\"\n  ],\n  tokenizer: {\n    root: [\n      // whitespace\n      [/\\s+/],\n      // Twig Tag Delimiters\n      [/{#/, \"comment.twig\", \"@commentState\"],\n      [/{%[-~]?/, \"delimiter.twig\", \"@blockState\"],\n      [/{{[-~]?/, \"delimiter.twig\", \"@variableState\"],\n      // HTML\n      [/<!DOCTYPE/, \"metatag.html\", \"@doctype\"],\n      [/<!--/, \"comment.html\", \"@comment\"],\n      [/(<)((?:[\\w\\-]+:)?[\\w\\-]+)(\\s*)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"\", \"delimiter.html\"]],\n      [/(<)(script)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@style\" }]],\n      [/(<)((?:[\\w\\-]+:)?[\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)((?:[\\w\\-]+:)?[\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/[^<{]+/]\n      // text\n    ],\n    /**\n     * Comment Tag Handling\n     */\n    commentState: [\n      [/#}/, \"comment.twig\", \"@pop\"],\n      [/./, \"comment.twig\"]\n    ],\n    /**\n     * Block Tag Handling\n     */\n    blockState: [\n      [/[-~]?%}/, \"delimiter.twig\", \"@pop\"],\n      // whitespace\n      [/\\s+/],\n      // verbatim\n      // Unlike other blocks, verbatim ehas its own state\n      // transition to ensure we mark its contents as strings.\n      [\n        /(verbatim)(\\s*)([-~]?%})/,\n        [\"keyword.twig\", \"\", { token: \"delimiter.twig\", next: \"@rawDataState\" }]\n      ],\n      { include: \"expression\" }\n    ],\n    rawDataState: [\n      // endverbatim\n      [\n        /({%[-~]?)(\\s*)(endverbatim)(\\s*)([-~]?%})/,\n        [\"delimiter.twig\", \"\", \"keyword.twig\", \"\", { token: \"delimiter.twig\", next: \"@popall\" }]\n      ],\n      [/./, \"string.twig\"]\n    ],\n    /**\n     * Variable Tag Handling\n     */\n    variableState: [[/[-~]?}}/, \"delimiter.twig\", \"@pop\"], { include: \"expression\" }],\n    stringState: [\n      // closing double quoted string\n      [/\"/, \"string.twig\", \"@pop\"],\n      // interpolation start\n      [/#{\\s*/, \"string.twig\", \"@interpolationState\"],\n      // string part\n      [/[^#\"\\\\]*(?:(?:\\\\.|#(?!\\{))[^#\"\\\\]*)*/, \"string.twig\"]\n    ],\n    interpolationState: [\n      // interpolation end\n      [/}/, \"string.twig\", \"@pop\"],\n      { include: \"expression\" }\n    ],\n    /**\n     * Expression Handling\n     */\n    expression: [\n      // whitespace\n      [/\\s+/],\n      // operators - math\n      [/\\+|-|\\/{1,2}|%|\\*{1,2}/, \"operators.twig\"],\n      // operators - logic\n      [/(and|or|not|b-and|b-xor|b-or)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - comparison (symbols)\n      [/==|!=|<|>|>=|<=/, \"operators.twig\"],\n      // operators - comparison (words)\n      [/(starts with|ends with|matches)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - containment\n      [/(in)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - test\n      [/(is)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - misc\n      [/\\||~|:|\\.{1,2}|\\?{1,2}/, \"operators.twig\"],\n      // names\n      [\n        /[^\\W\\d][\\w]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword.twig\",\n            \"@default\": \"variable.twig\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d+(\\.\\d+)?/, \"number.twig\"],\n      // punctuation\n      [/\\(|\\)|\\[|\\]|{|}|,/, \"delimiter.twig\"],\n      // strings\n      [/\"([^#\"\\\\]*(?:\\\\.[^#\"\\\\]*)*)\"|\\'([^\\'\\\\]*(?:\\\\.[^\\'\\\\]*)*)\\'/, \"string.twig\"],\n      // opening double quoted string\n      [/\"/, \"string.twig\", \"@stringState\"],\n      // misc syntactic constructs\n      // These are not operators per se, but for the purposes of lexical analysis we\n      // can treat them as such.\n      // arrow functions\n      [/=>/, \"operators.twig\"],\n      // assignment\n      [/=/, \"operators.twig\"]\n    ],\n    /**\n     * HTML\n     */\n    doctype: [\n      [/[^>]+/, \"metatag.content.html\"],\n      [/>/, \"metatag.html\", \"@pop\"]\n    ],\n    comment: [\n      [/-->/, \"comment.html\", \"@pop\"],\n      [/[^-]+/, \"comment.content.html\"],\n      [/./, \"comment.content.html\"]\n    ],\n    otherTag: [\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    // -- BEGIN <script> tags handling\n    // After <script\n    script: [\n      [/type/, \"attribute.name.html\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(script\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <script ... type\n    scriptAfterType: [\n      [/=/, \"delimiter.html\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type =\n    scriptAfterTypeEquals: [\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type = $S2\n    scriptWithCustomType: [\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^<]+/, \"\"]\n    ],\n    // -- END <script> tags handling\n    // -- BEGIN <style> tags handling\n    // After <style\n    style: [\n      [/type/, \"attribute.name.html\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(style\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <style ... type\n    styleAfterType: [\n      [/=/, \"delimiter.html\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type =\n    styleAfterTypeEquals: [\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type = $S2\n    styleWithCustomType: [\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^<]+/, \"\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/twig/twig.js\n"));

/***/ })

}]);