{"version": 3, "sources": ["../../../locales/he-IL.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"הדבק\",\n    \"pasteAsPlaintext\": \"הדבק ללא עיצוב\",\n    \"pasteCharts\": \"הדבק גרפים\",\n    \"selectAll\": \"בחר הכל\",\n    \"multiSelect\": \"הוסף רכיב לבחירה\",\n    \"moveCanvas\": \"הזז את הקנבס\",\n    \"cut\": \"גזור\",\n    \"copy\": \"העתק\",\n    \"copyAsPng\": \"העתק ללוח כ PNG\",\n    \"copyAsSvg\": \"העתק ללוח כ SVG\",\n    \"copyText\": \"העתק ללוח כטקסט\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"הבא שכבה קדימה\",\n    \"sendToBack\": \"שלח אחורה\",\n    \"bringToFront\": \"העבר לחזית\",\n    \"sendBackward\": \"העבר שכבה אחורה\",\n    \"delete\": \"מחק\",\n    \"copyStyles\": \"העתק סגנון\",\n    \"pasteStyles\": \"הדב<PERSON> סגנון\",\n    \"stroke\": \"קו מתאר\",\n    \"background\": \"רקע\",\n    \"fill\": \"מילוי\",\n    \"strokeWidth\": \"עובי קו מתאר\",\n    \"strokeStyle\": \"סגנון קו המתאר\",\n    \"strokeStyle_solid\": \"מלא\",\n    \"strokeStyle_dashed\": \"מקווקו\",\n    \"strokeStyle_dotted\": \"מנוקד\",\n    \"sloppiness\": \"רישול\",\n    \"opacity\": \"אטימות\",\n    \"textAlign\": \"יישור טקסט\",\n    \"edges\": \"קצוות\",\n    \"sharp\": \"חד\",\n    \"round\": \"עגול\",\n    \"arrowheads\": \"ראשי חצים\",\n    \"arrowhead_none\": \"ללא\",\n    \"arrowhead_arrow\": \"חץ\",\n    \"arrowhead_bar\": \"קצה אנכי\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"משולש\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"גודל גופן\",\n    \"fontFamily\": \"גופן\",\n    \"addWatermark\": \"הוסף \\\"נוצר באמצעות Excalidraw\\\"\",\n    \"handDrawn\": \"ציור יד\",\n    \"normal\": \"רגיל\",\n    \"code\": \"קוד\",\n    \"small\": \"קטן\",\n    \"medium\": \"בינוני\",\n    \"large\": \"גדול\",\n    \"veryLarge\": \"גדול מאוד\",\n    \"solid\": \"מוצק\",\n    \"hachure\": \"קווים מקבילים קצרים להצגת כיוון וחדות שיפוע במפה\",\n    \"zigzag\": \"זיגזג\",\n    \"crossHatch\": \"קווים מוצלבים שתי וערב\",\n    \"thin\": \"דק\",\n    \"bold\": \"מודגש\",\n    \"left\": \"שמאל\",\n    \"center\": \"מרכז\",\n    \"right\": \"ימין\",\n    \"extraBold\": \"מודגש במיוחד\",\n    \"architect\": \"ארכיטקט\",\n    \"artist\": \"אמן\",\n    \"cartoonist\": \"קריקטוריסט\",\n    \"fileTitle\": \"שם קובץ\",\n    \"colorPicker\": \"בוחר צבעים\",\n    \"canvasColors\": \"בשימוש בקנבס\",\n    \"canvasBackground\": \"רקע קנבס\",\n    \"drawingCanvas\": \"קנבס ציור\",\n    \"layers\": \"שכבות\",\n    \"actions\": \"פעולות\",\n    \"language\": \"שפה\",\n    \"liveCollaboration\": \"התחל שיתוף חי...\",\n    \"duplicateSelection\": \"שכפל\",\n    \"untitled\": \"ללא כותרת\",\n    \"name\": \"שם\",\n    \"yourName\": \"שמך\",\n    \"madeWithExcalidraw\": \"נוצר באמצעות Excalidraw\",\n    \"group\": \"קבץ\",\n    \"ungroup\": \"פרק קבוצה\",\n    \"collaborators\": \"שותפים\",\n    \"showGrid\": \"הצג רשת\",\n    \"addToLibrary\": \"הוסף לספריה\",\n    \"removeFromLibrary\": \"הסר מספריה\",\n    \"libraryLoadingMessage\": \"טוען ספריה…\",\n    \"libraries\": \"עיין בספריות\",\n    \"loadingScene\": \"טוען תצוגה…\",\n    \"align\": \"יישר\",\n    \"alignTop\": \"יישר למעלה\",\n    \"alignBottom\": \"יישר למטה\",\n    \"alignLeft\": \"יישר לשמאל\",\n    \"alignRight\": \"יישר לימין\",\n    \"centerVertically\": \"מרכז אנכית\",\n    \"centerHorizontally\": \"מרכז אופקית\",\n    \"distributeHorizontally\": \"חלוקה אופקית\",\n    \"distributeVertically\": \"חלוקה אנכית\",\n    \"flipHorizontal\": \"הפוך אופקית\",\n    \"flipVertical\": \"הפוך אנכית\",\n    \"viewMode\": \"מצב תצוגה\",\n    \"share\": \"שתף\",\n    \"showStroke\": \"הצג בוחר צבע מברשת\",\n    \"showBackground\": \"הצג בוחר צבע רקע\",\n    \"toggleTheme\": \"שינוי ערכת העיצוב\",\n    \"personalLib\": \"ספריה פרטית\",\n    \"excalidrawLib\": \"הספריה של Excalidraw\",\n    \"decreaseFontSize\": \"הקטן את גודל הגופן\",\n    \"increaseFontSize\": \"הגדל את גודל הגופן\",\n    \"unbindText\": \"ביטול קיבוע הטקסט\",\n    \"bindText\": \"קיבוע הטקסט למיכל\",\n    \"createContainerFromText\": \"ארוז טקסט במיכל\",\n    \"link\": {\n      \"edit\": \"עריכת קישור\",\n      \"editEmbed\": \"ערוך קישור ושבץ\",\n      \"create\": \"יצירת קישור\",\n      \"createEmbed\": \"צור קישור ושבץ\",\n      \"label\": \"קישור\",\n      \"labelEmbed\": \"קשר ושבץ\",\n      \"empty\": \"לא נקבע קישור\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"ערוך קו\",\n      \"exit\": \"צא מעורך הקו\"\n    },\n    \"elementLock\": {\n      \"lock\": \"נעילה\",\n      \"unlock\": \"ביטול נעילה\",\n      \"lockAll\": \"לנעול הכל\",\n      \"unlockAll\": \"שחרור הכול\"\n    },\n    \"statusPublished\": \"פורסם\",\n    \"sidebarLock\": \"שמור את סרגל הצד פתוח\",\n    \"selectAllElementsInFrame\": \"בחר את כל האלמנטים במסגרת\",\n    \"removeAllElementsFromFrame\": \"הסר את כל האלמנטים שבמסגרת\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"עוד לא הוספת דברים...\",\n    \"hint_emptyLibrary\": \"בחר משהו בקנבס כדי להוסיף אותו לכאן, או שתתקין ספריה מהספריה הציבורית מטה.\",\n    \"hint_emptyPrivateLibrary\": \"בחר משהו בקנבס כדי להוסיף אותו לכאן.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"אפס את הקנבאס\",\n    \"exportJSON\": \"ייצא לקובץ\",\n    \"exportImage\": \"ייצוא התמונה...\",\n    \"export\": \"שמור ל...\",\n    \"copyToClipboard\": \"העתק ללוח\",\n    \"save\": \"שמור לקובץ נוכחי\",\n    \"saveAs\": \"שמירה בשם\",\n    \"load\": \"פתח\",\n    \"getShareableLink\": \"קבל קישור לשיתוף\",\n    \"close\": \"סגור\",\n    \"selectLanguage\": \"בחר שפה\",\n    \"scrollBackToContent\": \"גלול בחזרה לתוכן\",\n    \"zoomIn\": \"הגדל\",\n    \"zoomOut\": \"הקטן\",\n    \"resetZoom\": \"איפוס זום\",\n    \"menu\": \"תפריט\",\n    \"done\": \"בוצע\",\n    \"edit\": \"ערוך\",\n    \"undo\": \"בטל\",\n    \"redo\": \"בצע מחדש\",\n    \"resetLibrary\": \"איפוס ספריה\",\n    \"createNewRoom\": \"צור חדר חדש\",\n    \"fullScreen\": \"מסך מלא\",\n    \"darkMode\": \"מצב כהה\",\n    \"lightMode\": \"מצב בהיר\",\n    \"zenMode\": \"מצב זן\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"צא ממצב זן\",\n    \"cancel\": \"ביטול\",\n    \"clear\": \"ניקוי\",\n    \"remove\": \"הסר\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"פרסום\",\n    \"submit\": \"שליחה\",\n    \"confirm\": \"אשר\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"פעולה זו תנקה את כל הקנבס. אתה בטוח?\",\n    \"couldNotCreateShareableLink\": \"יצירת קישור לשיתוף נכשל.\",\n    \"couldNotCreateShareableLinkTooBig\": \"יצירת קישור לשיתוף נכשל: התצוגה גדולה מדי\",\n    \"couldNotLoadInvalidFile\": \"טעינת קובץ לא תקין נכשלה\",\n    \"importBackendFailed\": \"ייבוא מהשרת נכשל.\",\n    \"cannotExportEmptyCanvas\": \"לא ניתן לייצא קנבאס ריק.\",\n    \"couldNotCopyToClipboard\": \"לא ניתן היה להעתיק ללוח.\",\n    \"decryptFailed\": \"פיענוח ההצפנה של המידע נכשל.\",\n    \"uploadedSecurly\": \"ההעלאה אובטחה באמצעות הצפנה מקצה לקצה, פירוש הדבר שהשרת של Excalidraw וגורמי צד ג׳ לא יכולים לקרוא את התוכן.\",\n    \"loadSceneOverridePrompt\": \"טעינה של ציור חיצוני תחליף את התוכן הקיים שלך. האם תרצה להמשיך?\",\n    \"collabStopOverridePrompt\": \"עצירת השיתוף תוביל למחיקת הציור הקודם ששמור מקומית בדפדפן. האם אתה בטוח?\\n\\n(אם תרצה לשמור את הציור המקומי, סגור את הטאב של הדפדפן במקום.)\",\n    \"errorAddingToLibrary\": \"לא ניתן להוסיף פריט לספרייה\",\n    \"errorRemovingFromLibrary\": \"לא ניתן להסיר פריט מהספריה\",\n    \"confirmAddLibrary\": \"זה יוסיף {{numShapes}} צורה(ות) לספריה שלך. האם אתה בטוח?\",\n    \"imageDoesNotContainScene\": \"נראה שהתמונה לא מכילה מידע על הסצינה. האם אפשרת הטמעת מידע הסצינה בעת השמירה?\",\n    \"cannotRestoreFromImage\": \"לא הצלחנו לשחזר את הסצנה מקובץ התמונה\",\n    \"invalidSceneUrl\": \"ייבוא מידע סצנה מהקישור שסופק כשל. או שהוא משובש, או שאינו מכיל מידע של Excalidraw בפורמט JSON.\",\n    \"resetLibrary\": \"פעולה זו תנקה את כל הספריה שלך. אתה בטוח?\",\n    \"removeItemsFromsLibrary\": \"מחק {{count}} פריט(ים) מהספריה?\",\n    \"invalidEncryptionKey\": \"מפתח ההצפנה חייב להיות בן 22 תוים. השיתוף החי מנוטרל.\",\n    \"collabOfflineWarning\": \"אין חיבור זמין לאינטרנט.\\nהשינויים שלך לא ישמרו!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"סוג הקובץ אינו נתמך.\",\n    \"imageInsertError\": \"לא ניתן היה להוסיף את התמונה. אנא נסה שוב מאוחר יותר...\",\n    \"fileTooBig\": \"הקובץ גדול מדי. הגודל המירבי המותר הינו {{maxSize}}.\",\n    \"svgImageInsertError\": \"לא ניתן היה להוסיף את תמונת ה-SVG. הסימונים בתוך קובץ ה-SVG עשויים להיות שגויים.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"SVG שגוי.\",\n    \"cannotResolveCollabServer\": \"לא הצלחתי להתחבר לשרת השיתוף. אנא רענן את הדף ונסה שוב.\",\n    \"importLibraryError\": \"לא ניתן היה לטעון את הספריה\",\n    \"collabSaveFailed\": \"לא הצלחתי להתחבר למסד הנתונים האחורי. אם הבעיה ממשיכה, כדאי שתשמור את הקובץ מקומית כדי לוודא שלא תאבד את העבודה שלך.\",\n    \"collabSaveFailed_sizeExceeded\": \"לא הצלחתי לשמור למסד הנתונים האחורי, נראה שהקנבס שלך גדול מדי. כדאי שתשמור את הקובץ מקומית כדי לוודא שלא תאבד את העבודה שלך.\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"בחירה\",\n    \"image\": \"הוספת תמונה\",\n    \"rectangle\": \"מלבן\",\n    \"diamond\": \"יהלום\",\n    \"ellipse\": \"אליפסה\",\n    \"arrow\": \"חץ\",\n    \"line\": \"קו\",\n    \"freedraw\": \"צייר\",\n    \"text\": \"טקסט\",\n    \"library\": \"ספריה\",\n    \"lock\": \"השאר את הכלי הנבחר פעיל גם לאחר סיום הציור\",\n    \"penMode\": \"מצב עט - מנע נגיעה\",\n    \"link\": \"הוספה/עדכון קישור של הצורה שנבחרה\",\n    \"eraser\": \"מחק\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"יד (כלי הזזה)\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"פעולות קנבאס\",\n    \"selectedShapeActions\": \"פעולות על הצורות שנבחרו\",\n    \"shapes\": \"צורות\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"כדי להזיז את הקנבס, החזק את גלגל העכבר לחוץ או את מקש הרווח לחוץ תוך כדי גרירה, או השתמש בכלי היד\",\n    \"linearElement\": \"לחץ להתחלת מספר נקודות, גרור לקו יחיד\",\n    \"freeDraw\": \"לחץ וגרור, שחרר כשסיימת\",\n    \"text\": \"טיפ: אפשר להוסיף טקסט על ידי לחיצה כפולה בכל מקום עם כלי הבחירה\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"לחץ לחיצה כפולה או הקש על אנטר לעריכת הטקסט\",\n    \"text_editing\": \"כדי לסיים את העריכה לחץ על מקש Escape או על Ctrl (Cmd במחשבי אפל) ומקש Enter\",\n    \"linearElementMulti\": \"הקלק על הנקודה האחרונה או הקש Escape או Enter לסיום\",\n    \"lockAngle\": \"ניתן להגביל את הזוויות על ידי החזקה של מקש ה- SHIFT\",\n    \"resize\": \"ניתן להגביל פרופורציות על ידי לחיצה על SHIFT תוך כדי שינוי גודל,\\nהחזק ALT בשביל לשנות גודל ביחס למרכז\",\n    \"resizeImage\": \"אתה יכול לשנות את הגודל בחופשיות על ידי החזקת מקש SHIFT,\\nהחזק את מקש ALT כדי לבצע שינוי גודל מהמרכז\",\n    \"rotate\": \"ניתן להגביל זוויות על ידי לחיצה על SHIFT תוך כדי סיבוב\",\n    \"lineEditor_info\": \"החזק Ctrl / Cmd ובצע לחיצה כפולה או לחץ Ctrl / Cmd + Enter לעריכת נקודות\",\n    \"lineEditor_pointSelected\": \"לחץ Delete למחיקת נקודה/ות,\\nCtrl / Cmd + D לשכפול, או גרור להזזה\",\n    \"lineEditor_nothingSelected\": \"בחר נקודה כדי לערוך (החזק SHIFT לבחירת כמה),\\nאו החזק Alt והקלק להוספת נקודות חדשות\",\n    \"placeImage\": \"הקלק להנחת התמונה, או הקלק וגרור להגדרת הגודל שלו ידנית\",\n    \"publishLibrary\": \"פרסם ספריה משלך\",\n    \"bindTextToElement\": \"הקש Enter כדי להוספת טקסט\",\n    \"deepBoxSelect\": \"החזק Ctrl / Cmd לבחירה עמוקה ולמניעת גרירה\",\n    \"eraserRevert\": \"החזק Alt להחזרת רכיבים מסומנים למחיקה\",\n    \"firefox_clipboard_write\": \"יכולות זה ניתנת להפעלה על ידי שינוי הדגל של \\\"dom.events.asyncClipboard.clipboardItem\\\" למצב \\\"true\\\". כדי לשנות את הדגל בדפדפן Firefox, בקר בעמוד ״about:config״.\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"לא ניתן להראות תצוגה מקדימה\",\n    \"canvasTooBig\": \"הקנבס עלול להיות גדול מדי.\",\n    \"canvasTooBigTip\": \"טיפ: נסה להזיז את הרכיבים הרחוקים ביותר מעט קרוב יותר האחד לשני.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"\",\n    \"clearCanvasMessage\": \"אם טעינה מחדש לא עובדת, נסה \",\n    \"clearCanvasCaveat\": \" זה יגרום לאובדן העבודה \",\n    \"trackedToSentry\": \"\",\n    \"openIssueMessage\": \"\",\n    \"sceneContent\": \"תוכן הקנבאס:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"אתה יכול להזמין אנשים לקנבאס הנוכחי שלך לעבודה משותפת.\",\n    \"desc_privacy\": \"אל דאגה, השיתוף מוצפן מקצה לקצה, כך שכל מה שתצייר ישאר פרטי. אפילו השרתים שלנו לא יוכלו לראות את מה שאתה ממציא.\",\n    \"button_startSession\": \"התחל שיתוף\",\n    \"button_stopSession\": \"הפסק שיתוף\",\n    \"desc_inProgressIntro\": \"שיתוף חי פעיל כרגע.\",\n    \"desc_shareLink\": \"שתף את הקישור עם כל מי שאתה מעוניין לעבוד אתו:\",\n    \"desc_exitSession\": \"עצירת השיתוף תנתק אותך מהחדר, אבל עדיין תוכל להמשיך לעבוד על הקנבאס, מקומית. שים לב שזה לא ישפיע על אנשים אחרים, והם עדיין יוכלו לבצע שיתוף עם הגרסה שלהם.\",\n    \"shareTitle\": \"הצטרף לשיתוף לעבודה משותפת חיה, בזמן אמת, על גבי Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"שגיאה\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"שמור לכונן\",\n    \"disk_details\": \"ייצא מידע של הקנבאס לקובץ שתוכל לייבא אחר כך.\",\n    \"disk_button\": \"שמירה לקובץ\",\n    \"link_title\": \"קבל קישור לשיתוף\",\n    \"link_details\": \"ייצוא כקישור לקריאה בלבד.\",\n    \"link_button\": \"ייצוא לקישור\",\n    \"excalidrawplus_description\": \"שמור את הקנבאס לסביבת העבודה שלך ב- +Excalidraw.\",\n    \"excalidrawplus_button\": \"ייצוא\",\n    \"excalidrawplus_exportError\": \"לא הצלחתי לייצא ל- +Excalidraw כרגע...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"קרא את הבלוג שלנו\",\n    \"click\": \"קליק\",\n    \"deepSelect\": \"בחירה עמוקה\",\n    \"deepBoxSelect\": \"בחירה עמוקה בתוך קופסה ומניעת גרירה\",\n    \"curvedArrow\": \"חץ מעוגל\",\n    \"curvedLine\": \"קו מעוגל\",\n    \"documentation\": \"תיעוד\",\n    \"doubleClick\": \"לחיצה כפולה\",\n    \"drag\": \"גרור\",\n    \"editor\": \"עורך\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"מצאת בעיה? דווח\",\n    \"howto\": \"עקוב אחר המדריכים שלנו\",\n    \"or\": \"או\",\n    \"preventBinding\": \"למנוע נעיצת חיצים\",\n    \"tools\": \"כלים\",\n    \"shortcuts\": \"קיצורי מקלדת\",\n    \"textFinish\": \"סיים עריכה (עורך טקסט)\",\n    \"textNewLine\": \"הוסף שורה חדשה (עורך טקסט)\",\n    \"title\": \"עזרה\",\n    \"view\": \"תצוגה\",\n    \"zoomToFit\": \"זום להתאמת כל האלמנטים למסך\",\n    \"zoomToSelection\": \"התמקד בבחירה\",\n    \"toggleElementLock\": \"נעילה/ביטול הנעילה של הרכיבים הנבחרים\",\n    \"movePageUpDown\": \"זוז עמוד למעלה/למטה\",\n    \"movePageLeftRight\": \"זוז עמוד שמאלה/ימינה\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"ניקוי הקנבס\"\n  },\n  \"publishDialog\": {\n    \"title\": \"פרסם ספריה\",\n    \"itemName\": \"שם הפריט\",\n    \"authorName\": \"שם היוצר\",\n    \"githubUsername\": \"שם המשתמש שלך ב-GitHub\",\n    \"twitterUsername\": \"שם המשתמש שלך ב-Twitter\",\n    \"libraryName\": \"שם הספריה\",\n    \"libraryDesc\": \"תיאור הספריה\",\n    \"website\": \"אתר\",\n    \"placeholder\": {\n      \"authorName\": \"שמך או שם המשתמש שלך\",\n      \"libraryName\": \"תנו שם לספריה\",\n      \"libraryDesc\": \"תיאור של הספריה שלך כדי לסייע למשתמשים להבין את השימוש בה\",\n      \"githubHandle\": \"כינוי GitHub (לא חובה), כדי שתוכל לערוך את הספרית לאחר שנשלחה לבדיקה\",\n      \"twitterHandle\": \"שם משתמש טוויטר (לא חובה), כדי שנדע למי לתת קרדיט כשאנחנו מפרסמים בטוויטר\",\n      \"website\": \"קישור לאתר הפרטי שלך או לכל מקום אחר (אופציונאלי)\"\n    },\n    \"errors\": {\n      \"required\": \"נדרש\",\n      \"website\": \"הזינו כתובת URL תקינה\"\n    },\n    \"noteDescription\": \"\",\n    \"noteGuidelines\": \"\",\n    \"noteLicense\": \"\",\n    \"noteItems\": \"לכל פריט בסיפריה חייב להיות שם כדי שאפשר יהיה לסנן. הפריטי סיפריה הבאים יהיו כלולים:\",\n    \"atleastOneLibItem\": \"אנא בחר לפחות פריט אחד מספריה כדי להתחיל\",\n    \"republishWarning\": \"הערה: חלק מהפריטים שבחרת מסומנים ככאלו שכבר פורסמו/נשלחו. אתה צריך לשלוח פריטים מחדש כאשר אתה מעדכן ספריה או הגשה קיימים.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"הספריה הוגשה\",\n    \"content\": \"תודה {{authorName}}. הספריה שלך נשלחה לבחינה. תוכל לעקוב אחרי סטטוס הפרסום\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"איפוס ספריה\",\n    \"removeItemsFromLib\": \"הסר את הפריטים הנבחרים מהספריה\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"הציורים שלך מוצפנים מקצה לקצה כך שהשרתים של Excalidraw לא יראו אותם לעולם.\",\n    \"link\": \"פוסט בבלוג על הצפנה מקצה לקצב ב-Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"זווית\",\n    \"element\": \"רכיב\",\n    \"elements\": \"רכיבים\",\n    \"height\": \"גובה\",\n    \"scene\": \"תצוגה\",\n    \"selected\": \"נבחר\",\n    \"storage\": \"אחסון\",\n    \"title\": \"סטטיסטיקות לחנונים\",\n    \"total\": \"סה״כ\",\n    \"version\": \"גרסה\",\n    \"versionCopy\": \"לחץ להעתקה\",\n    \"versionNotAvailable\": \"הגרסה אינה זמינה\",\n    \"width\": \"רוחב\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"נוסף לספריה\",\n    \"copyStyles\": \"סגנונות הועתקו.\",\n    \"copyToClipboard\": \"הועתק ללוח.\",\n    \"copyToClipboardAsPng\": \"{{exportSelection}} הועתקה ללוח כ-PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"קובץ נשמר.\",\n    \"fileSavedToFilename\": \"נשמר לקובץ {filename}\",\n    \"canvas\": \"קנבאס\",\n    \"selection\": \"בחירה\",\n    \"pasteAsSingleElement\": \"השתמש ב- {{shortcut}} כדי להדביק כפריט יחיד,\\nאו הדבק לתוך עורך טקסט קיים\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"שקוף\",\n    \"black\": \"\",\n    \"white\": \"\",\n    \"red\": \"\",\n    \"pink\": \"\",\n    \"grape\": \"\",\n    \"violet\": \"\",\n    \"gray\": \"\",\n    \"blue\": \"\",\n    \"cyan\": \"\",\n    \"teal\": \"\",\n    \"green\": \"\",\n    \"yellow\": \"\",\n    \"orange\": \"\",\n    \"bronze\": \"\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"כל המידע שלח נשמר מקומית בדפדפן.\",\n      \"center_heading_plus\": \"אתה רוצה ללכת אל Excalidraw+ במקום?\",\n      \"menuHint\": \"ייצוא, העדפות, שפות, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"ייצוא, העדפות, ועוד...\",\n      \"center_heading\": \"איורים. נעשים. פשוטים.\",\n      \"toolbarHint\": \"בחר כלי & והתחל לצייר!\",\n      \"helpHint\": \"קיצורים & עזרה\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"\",\n    \"shades\": \"\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}