/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xmlhttprequest-ssl";
exports.ids = ["vendor-chunks/xmlhttprequest-ssl"];
exports.modules = {

/***/ "(ssr)/./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js":
/*!***************************************************************!*\
  !*** ./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Wrapper for built-in http.js to emulate the browser XMLHttpRequest object.\n *\n * This can be used with JS designed for browsers to improve reuse of code and\n * allow the use of existing libraries.\n *\n * Usage: include(\"XMLHttpRequest.js\") and use XMLHttpRequest per W3C specs.\n *\n * <AUTHOR> DeFelippi <<EMAIL>>\n * @contributor David Ellis <<EMAIL>>\n * @license MIT\n */\n\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar Url = __webpack_require__(/*! url */ \"url\");\nvar spawn = (__webpack_require__(/*! child_process */ \"child_process\").spawn);\n\n/**\n * Module exports.\n */\n\nmodule.exports = XMLHttpRequest;\n\n// backwards-compat\nXMLHttpRequest.XMLHttpRequest = XMLHttpRequest;\n\n/**\n * `XMLHttpRequest` constructor.\n *\n * Supported options for the `opts` object are:\n *\n *  - `agent`: An http.Agent instance; http.globalAgent may be used; if 'undefined', agent usage is disabled\n *\n * @param {Object} opts optional \"options\" object\n */\n\nfunction XMLHttpRequest(opts) {\n  \"use strict\";\n\n  opts = opts || {};\n\n  /**\n   * Private variables\n   */\n  var self = this;\n  var http = __webpack_require__(/*! http */ \"http\");\n  var https = __webpack_require__(/*! https */ \"https\");\n\n  // Holds http.js objects\n  var request;\n  var response;\n\n  // Request settings\n  var settings = {};\n\n  // Disable header blacklist.\n  // Not part of XHR specs.\n  var disableHeaderCheck = false;\n\n  // Set some default headers\n  var defaultHeaders = {\n    \"User-Agent\": \"node-XMLHttpRequest\",\n    \"Accept\": \"*/*\"\n  };\n\n  var headers = Object.assign({}, defaultHeaders);\n\n  // These headers are not user setable.\n  // The following are allowed but banned in the spec:\n  // * user-agent\n  var forbiddenRequestHeaders = [\n    \"accept-charset\",\n    \"accept-encoding\",\n    \"access-control-request-headers\",\n    \"access-control-request-method\",\n    \"connection\",\n    \"content-length\",\n    \"content-transfer-encoding\",\n    \"cookie\",\n    \"cookie2\",\n    \"date\",\n    \"expect\",\n    \"host\",\n    \"keep-alive\",\n    \"origin\",\n    \"referer\",\n    \"te\",\n    \"trailer\",\n    \"transfer-encoding\",\n    \"upgrade\",\n    \"via\"\n  ];\n\n  // These request methods are not allowed\n  var forbiddenRequestMethods = [\n    \"TRACE\",\n    \"TRACK\",\n    \"CONNECT\"\n  ];\n\n  // Send flag\n  var sendFlag = false;\n  // Error flag, used when errors occur or abort is called\n  var errorFlag = false;\n  var abortedFlag = false;\n\n  // Event listeners\n  var listeners = {};\n\n  /**\n   * Constants\n   */\n\n  this.UNSENT = 0;\n  this.OPENED = 1;\n  this.HEADERS_RECEIVED = 2;\n  this.LOADING = 3;\n  this.DONE = 4;\n\n  /**\n   * Public vars\n   */\n\n  // Current state\n  this.readyState = this.UNSENT;\n\n  // default ready state change handler in case one is not set or is set late\n  this.onreadystatechange = null;\n\n  // Result & response\n  this.responseText = \"\";\n  this.responseXML = \"\";\n  this.response = Buffer.alloc(0);\n  this.status = null;\n  this.statusText = null;\n\n  /**\n   * Private methods\n   */\n\n  /**\n   * Check if the specified header is allowed.\n   *\n   * @param string header Header to validate\n   * @return boolean False if not allowed, otherwise true\n   */\n  var isAllowedHttpHeader = function(header) {\n    return disableHeaderCheck || (header && forbiddenRequestHeaders.indexOf(header.toLowerCase()) === -1);\n  };\n\n  /**\n   * Check if the specified method is allowed.\n   *\n   * @param string method Request method to validate\n   * @return boolean False if not allowed, otherwise true\n   */\n  var isAllowedHttpMethod = function(method) {\n    return (method && forbiddenRequestMethods.indexOf(method) === -1);\n  };\n\n  /**\n   * Public methods\n   */\n\n  /**\n   * Open the connection. Currently supports local server requests.\n   *\n   * @param string method Connection method (eg GET, POST)\n   * @param string url URL for the connection.\n   * @param boolean async Asynchronous connection. Default is true.\n   * @param string user Username for basic authentication (optional)\n   * @param string password Password for basic authentication (optional)\n   */\n  this.open = function(method, url, async, user, password) {\n    this.abort();\n    errorFlag = false;\n    abortedFlag = false;\n\n    // Check for valid request method\n    if (!isAllowedHttpMethod(method)) {\n      throw new Error(\"SecurityError: Request method not allowed\");\n    }\n\n    settings = {\n      \"method\": method,\n      \"url\": url.toString(),\n      \"async\": (typeof async !== \"boolean\" ? true : async),\n      \"user\": user || null,\n      \"password\": password || null\n    };\n\n    setState(this.OPENED);\n  };\n\n  /**\n   * Disables or enables isAllowedHttpHeader() check the request. Enabled by default.\n   * This does not conform to the W3C spec.\n   *\n   * @param boolean state Enable or disable header checking.\n   */\n  this.setDisableHeaderCheck = function(state) {\n    disableHeaderCheck = state;\n  };\n\n  /**\n   * Sets a header for the request.\n   *\n   * @param string header Header name\n   * @param string value Header value\n   * @return boolean Header added\n   */\n  this.setRequestHeader = function(header, value) {\n    if (this.readyState != this.OPENED) {\n      throw new Error(\"INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN\");\n    }\n    if (!isAllowedHttpHeader(header)) {\n      console.warn('Refused to set unsafe header \"' + header + '\"');\n      return false;\n    }\n    if (sendFlag) {\n      throw new Error(\"INVALID_STATE_ERR: send flag is true\");\n    }\n    headers[header] = value;\n    return true;\n  };\n\n  /**\n   * Gets a header from the server response.\n   *\n   * @param string header Name of header to get.\n   * @return string Text of the header or null if it doesn't exist.\n   */\n  this.getResponseHeader = function(header) {\n    if (typeof header === \"string\"\n      && this.readyState > this.OPENED\n      && response.headers[header.toLowerCase()]\n      && !errorFlag\n    ) {\n      return response.headers[header.toLowerCase()];\n    }\n\n    return null;\n  };\n\n  /**\n   * Gets all the response headers.\n   *\n   * @return string A string with all response headers separated by CR+LF\n   */\n  this.getAllResponseHeaders = function() {\n    if (this.readyState < this.HEADERS_RECEIVED || errorFlag) {\n      return \"\";\n    }\n    var result = \"\";\n\n    for (var i in response.headers) {\n      // Cookie headers are excluded\n      if (i !== \"set-cookie\" && i !== \"set-cookie2\") {\n        result += i + \": \" + response.headers[i] + \"\\r\\n\";\n      }\n    }\n    return result.substr(0, result.length - 2);\n  };\n\n  /**\n   * Gets a request header\n   *\n   * @param string name Name of header to get\n   * @return string Returns the request header or empty string if not set\n   */\n  this.getRequestHeader = function(name) {\n    // @TODO Make this case insensitive\n    if (typeof name === \"string\" && headers[name]) {\n      return headers[name];\n    }\n\n    return \"\";\n  };\n\n  /**\n   * Sends the request to the server.\n   *\n   * @param string data Optional data to send as request body.\n   */\n  this.send = function(data) {\n    if (this.readyState != this.OPENED) {\n      throw new Error(\"INVALID_STATE_ERR: connection must be opened before send() is called\");\n    }\n\n    if (sendFlag) {\n      throw new Error(\"INVALID_STATE_ERR: send has already been called\");\n    }\n\n    var ssl = false, local = false;\n    var url = Url.parse(settings.url);\n    var host;\n    // Determine the server\n    switch (url.protocol) {\n      case 'https:':\n        ssl = true;\n        // SSL & non-SSL both need host, no break here.\n      case 'http:':\n        host = url.hostname;\n        break;\n\n      case 'file:':\n        local = true;\n        break;\n\n      case undefined:\n      case '':\n        host = \"localhost\";\n        break;\n\n      default:\n        throw new Error(\"Protocol not supported.\");\n    }\n\n    // Load files off the local filesystem (file://)\n    if (local) {\n      if (settings.method !== \"GET\") {\n        throw new Error(\"XMLHttpRequest: Only GET method is supported\");\n      }\n\n      if (settings.async) {\n        fs.readFile(unescape(url.pathname), function(error, data) {\n          if (error) {\n            self.handleError(error, error.errno || -1);\n          } else {\n            self.status = 200;\n            self.responseText = data.toString('utf8');\n            self.response = data;\n            setState(self.DONE);\n          }\n        });\n      } else {\n        try {\n          this.response = fs.readFileSync(unescape(url.pathname));\n          this.responseText = this.response.toString('utf8');\n          this.status = 200;\n          setState(self.DONE);\n        } catch(e) {\n          this.handleError(e, e.errno || -1);\n        }\n      }\n\n      return;\n    }\n\n    // Default to port 80. If accessing localhost on another port be sure\n    // to use http://localhost:port/path\n    var port = url.port || (ssl ? 443 : 80);\n    // Add query string if one is used\n    var uri = url.pathname + (url.search ? url.search : '');\n\n    // Set the Host header or the server may reject the request\n    headers[\"Host\"] = host;\n    if (!((ssl && port === 443) || port === 80)) {\n      headers[\"Host\"] += ':' + url.port;\n    }\n\n    // Set Basic Auth if necessary\n    if (settings.user) {\n      if (typeof settings.password == \"undefined\") {\n        settings.password = \"\";\n      }\n      var authBuf = new Buffer(settings.user + \":\" + settings.password);\n      headers[\"Authorization\"] = \"Basic \" + authBuf.toString(\"base64\");\n    }\n\n    // Set content length header\n    if (settings.method === \"GET\" || settings.method === \"HEAD\") {\n      data = null;\n    } else if (data) {\n      headers[\"Content-Length\"] = Buffer.isBuffer(data) ? data.length : Buffer.byteLength(data);\n\n      var headersKeys = Object.keys(headers);\n      if (!headersKeys.some(function (h) { return h.toLowerCase() === 'content-type' })) {\n        headers[\"Content-Type\"] = \"text/plain;charset=UTF-8\";\n      }\n    } else if (settings.method === \"POST\") {\n      // For a post with no data set Content-Length: 0.\n      // This is required by buggy servers that don't meet the specs.\n      headers[\"Content-Length\"] = 0;\n    }\n\n    var agent = opts.agent || false;\n    var options = {\n      host: host,\n      port: port,\n      path: uri,\n      method: settings.method,\n      headers: headers,\n      agent: agent\n    };\n\n    if (ssl) {\n      options.pfx = opts.pfx;\n      options.key = opts.key;\n      options.passphrase = opts.passphrase;\n      options.cert = opts.cert;\n      options.ca = opts.ca;\n      options.ciphers = opts.ciphers;\n      options.rejectUnauthorized = opts.rejectUnauthorized === false ? false : true;\n    }\n\n    // Reset error flag\n    errorFlag = false;\n    // Handle async requests\n    if (settings.async) {\n      // Use the proper protocol\n      var doRequest = ssl ? https.request : http.request;\n\n      // Request is being sent, set send flag\n      sendFlag = true;\n\n      // As per spec, this is called here for historical reasons.\n      self.dispatchEvent(\"readystatechange\");\n\n      // Handler for the response\n      var responseHandler = function(resp) {\n        // Set response var to the response we got back\n        // This is so it remains accessable outside this scope\n        response = resp;\n        // Check for redirect\n        // @TODO Prevent looped redirects\n        if (response.statusCode === 302 || response.statusCode === 303 || response.statusCode === 307) {\n          // Change URL to the redirect location\n          settings.url = response.headers.location;\n          var url = Url.parse(settings.url);\n          // Set host var in case it's used later\n          host = url.hostname;\n          // Options for the new request\n          var newOptions = {\n            hostname: url.hostname,\n            port: url.port,\n            path: url.path,\n            method: response.statusCode === 303 ? 'GET' : settings.method,\n            headers: headers\n          };\n\n          if (ssl) {\n            newOptions.pfx = opts.pfx;\n            newOptions.key = opts.key;\n            newOptions.passphrase = opts.passphrase;\n            newOptions.cert = opts.cert;\n            newOptions.ca = opts.ca;\n            newOptions.ciphers = opts.ciphers;\n            newOptions.rejectUnauthorized = opts.rejectUnauthorized === false ? false : true;\n          }\n\n          // Issue the new request\n          request = doRequest(newOptions, responseHandler).on('error', errorHandler);\n          request.end();\n          // @TODO Check if an XHR event needs to be fired here\n          return;\n        }\n\n        setState(self.HEADERS_RECEIVED);\n        self.status = response.statusCode;\n\n        response.on('data', function(chunk) {\n          // Make sure there's some data\n          if (chunk) {\n            var data = Buffer.from(chunk);\n            self.response = Buffer.concat([self.response, data]);\n          }\n          // Don't emit state changes if the connection has been aborted.\n          if (sendFlag) {\n            setState(self.LOADING);\n          }\n        });\n\n        response.on('end', function() {\n          if (sendFlag) {\n            // The sendFlag needs to be set before setState is called.  Otherwise if we are chaining callbacks\n            // there can be a timing issue (the callback is called and a new call is made before the flag is reset).\n            sendFlag = false;\n            // Discard the 'end' event if the connection has been aborted\n            setState(self.DONE);\n            // Construct responseText from response\n            self.responseText = self.response.toString('utf8');\n          }\n        });\n\n        response.on('error', function(error) {\n          self.handleError(error);\n        });\n      }\n\n      // Error handler for the request\n      var errorHandler = function(error) {\n        // In the case of https://nodejs.org/api/http.html#requestreusedsocket triggering an ECONNRESET,\n        // don't fail the xhr request, attempt again.\n        if (request.reusedSocket && error.code === 'ECONNRESET')\n          return doRequest(options, responseHandler).on('error', errorHandler);\n        self.handleError(error);\n      }\n\n      // Create the request\n      request = doRequest(options, responseHandler).on('error', errorHandler);\n\n      if (opts.autoUnref) {\n        request.on('socket', (socket) => {\n          socket.unref();\n        });\n      }\n\n      // Node 0.4 and later won't accept empty data. Make sure it's needed.\n      if (data) {\n        request.write(data);\n      }\n\n      request.end();\n\n      self.dispatchEvent(\"loadstart\");\n    } else { // Synchronous\n      // Create a temporary file for communication with the other Node process\n      var contentFile = \".node-xmlhttprequest-content-\" + process.pid;\n      var syncFile = \".node-xmlhttprequest-sync-\" + process.pid;\n      fs.writeFileSync(syncFile, \"\", \"utf8\");\n      // The async request the other Node process executes\n      var execString = \"var http = require('http'), https = require('https'), fs = require('fs');\"\n        + \"var doRequest = http\" + (ssl ? \"s\" : \"\") + \".request;\"\n        + \"var options = \" + JSON.stringify(options) + \";\"\n        + \"var responseText = '';\"\n        + \"var responseData = Buffer.alloc(0);\"\n        + \"var req = doRequest(options, function(response) {\"\n        + \"response.on('data', function(chunk) {\"\n        + \"  var data = Buffer.from(chunk);\"\n        + \"  responseText += data.toString('utf8');\"\n        + \"  responseData = Buffer.concat([responseData, data]);\"\n        + \"});\"\n        + \"response.on('end', function() {\"\n        + \"fs.writeFileSync('\" + contentFile + \"', JSON.stringify({err: null, data: {statusCode: response.statusCode, headers: response.headers, text: responseText, data: responseData.toString('base64')}}), 'utf8');\"\n        + \"fs.unlinkSync('\" + syncFile + \"');\"\n        + \"});\"\n        + \"response.on('error', function(error) {\"\n        + \"fs.writeFileSync('\" + contentFile + \"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');\"\n        + \"fs.unlinkSync('\" + syncFile + \"');\"\n        + \"});\"\n        + \"}).on('error', function(error) {\"\n        + \"fs.writeFileSync('\" + contentFile + \"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');\"\n        + \"fs.unlinkSync('\" + syncFile + \"');\"\n        + \"});\"\n        + (data ? \"req.write('\" + JSON.stringify(data).slice(1,-1).replace(/'/g, \"\\\\'\") + \"');\":\"\")\n        + \"req.end();\";\n      // Start the other Node Process, executing this string\n      var syncProc = spawn(process.argv[0], [\"-e\", execString]);\n      var statusText;\n      while(fs.existsSync(syncFile)) {\n        // Wait while the sync file is empty\n      }\n      self.responseText = fs.readFileSync(contentFile, 'utf8');\n      // Kill the child process once the file has data\n      syncProc.stdin.end();\n      // Remove the temporary file\n      fs.unlinkSync(contentFile);\n      if (self.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)) {\n        // If the file returned an error, handle it\n        var errorObj = JSON.parse(self.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/, \"\"));\n        self.handleError(errorObj, 503);\n      } else {\n        // If the file returned okay, parse its data and move to the DONE state\n        self.status = self.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/, \"$1\");\n        var resp = JSON.parse(self.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/, \"$1\"));\n        response = {\n          statusCode: self.status,\n          headers: resp.data.headers\n        };\n        self.responseText = resp.data.text;\n        self.response = Buffer.from(resp.data.data, 'base64');\n        setState(self.DONE, true);\n      }\n    }\n  };\n\n  /**\n   * Called when an error is encountered to deal with it.\n   * @param  status  {number}    HTTP status code to use rather than the default (0) for XHR errors.\n   */\n  this.handleError = function(error, status) {\n    this.status = status || 0;\n    this.statusText = error;\n    this.responseText = error.stack;\n    errorFlag = true;\n    setState(this.DONE);\n  };\n\n  /**\n   * Aborts a request.\n   */\n  this.abort = function() {\n    if (request) {\n      request.abort();\n      request = null;\n    }\n\n    headers = Object.assign({}, defaultHeaders);\n    this.responseText = \"\";\n    this.responseXML = \"\";\n    this.response = Buffer.alloc(0);\n\n    errorFlag = abortedFlag = true\n    if (this.readyState !== this.UNSENT\n        && (this.readyState !== this.OPENED || sendFlag)\n        && this.readyState !== this.DONE) {\n      sendFlag = false;\n      setState(this.DONE);\n    }\n    this.readyState = this.UNSENT;\n  };\n\n  /**\n   * Adds an event listener. Preferred method of binding to events.\n   */\n  this.addEventListener = function(event, callback) {\n    if (!(event in listeners)) {\n      listeners[event] = [];\n    }\n    // Currently allows duplicate callbacks. Should it?\n    listeners[event].push(callback);\n  };\n\n  /**\n   * Remove an event callback that has already been bound.\n   * Only works on the matching funciton, cannot be a copy.\n   */\n  this.removeEventListener = function(event, callback) {\n    if (event in listeners) {\n      // Filter will return a new array with the callback removed\n      listeners[event] = listeners[event].filter(function(ev) {\n        return ev !== callback;\n      });\n    }\n  };\n\n  /**\n   * Dispatch any events, including both \"on\" methods and events attached using addEventListener.\n   */\n  this.dispatchEvent = function (event) {\n    if (typeof self[\"on\" + event] === \"function\") {\n      if (this.readyState === this.DONE && settings.async)\n        setTimeout(function() { self[\"on\" + event]() }, 0)\n      else\n        self[\"on\" + event]()\n    }\n    if (event in listeners) {\n      for (let i = 0, len = listeners[event].length; i < len; i++) {\n        if (this.readyState === this.DONE)\n          setTimeout(function() { listeners[event][i].call(self) }, 0)\n        else\n          listeners[event][i].call(self)\n      }\n    }\n  };\n\n  /**\n   * Changes readyState and calls onreadystatechange.\n   *\n   * @param int state New state\n   */\n  var setState = function(state) {\n    if ((self.readyState === state) || (self.readyState === self.UNSENT && abortedFlag))\n      return\n\n    self.readyState = state;\n\n    if (settings.async || self.readyState < self.OPENED || self.readyState === self.DONE) {\n      self.dispatchEvent(\"readystatechange\");\n    }\n\n    if (self.readyState === self.DONE) {\n      let fire\n\n      if (abortedFlag)\n        fire = \"abort\"\n      else if (errorFlag)\n        fire = \"error\"\n      else\n        fire = \"load\"\n\n      self.dispatchEvent(fire)\n\n      // @TODO figure out InspectorInstrumentation::didLoadXHR(cookie)\n      self.dispatchEvent(\"loadend\");\n    }\n  };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js\n");

/***/ })

};
;