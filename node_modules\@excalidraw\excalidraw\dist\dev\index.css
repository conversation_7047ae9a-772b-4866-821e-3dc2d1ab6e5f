@charset "UTF-8";

/* components/Spinner.scss */
.excalidraw .Spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  margin-left: auto;
  margin-right: auto;
  --spinner-color: var(--icon-fill-color);
}
.excalidraw .Spinner svg {
  animation: rotate 1.6s linear infinite;
  animation-delay: var(--spinner-delay);
  transform-origin: center center;
}
.excalidraw .Spinner circle {
  stroke: var(--spinner-color);
  animation: dash 1.6s linear 0s infinite;
  stroke-linecap: round;
}
@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}
@keyframes dash {
  0% {
    stroke-dasharray: 1, 300;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 150, 300;
    stroke-dashoffset: -200;
  }
  100% {
    stroke-dasharray: 1, 300;
    stroke-dashoffset: -280;
  }
}

/* components/ToolIcon.scss */
.excalidraw .ToolIcon {
  border-radius: var(--border-radius-lg);
  display: inline-flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}
.excalidraw .ToolIcon__hidden {
  display: none !important;
}
.excalidraw .ToolIcon.fillable .ToolIcon_type_radio:checked + .ToolIcon__icon,
.excalidraw .ToolIcon.fillable .ToolIcon_type_checkbox:checked + .ToolIcon__icon {
  --icon-fill-color: var(--color-on-primary-container);
}
.excalidraw .ToolIcon.fillable .ToolIcon_type_radio:checked + .ToolIcon__icon svg,
.excalidraw .ToolIcon.fillable .ToolIcon_type_checkbox:checked + .ToolIcon__icon svg {
  fill: var(--icon-fill-color);
}
.excalidraw .ToolIcon .ToolIcon_type_radio:checked + .ToolIcon__icon,
.excalidraw .ToolIcon .ToolIcon_type_checkbox:checked + .ToolIcon__icon {
  background: var(--color-surface-primary-container);
  --keybinding-color: var(--color-on-primary-container);
}
.excalidraw .ToolIcon .ToolIcon_type_radio:checked + .ToolIcon__icon svg,
.excalidraw .ToolIcon .ToolIcon_type_checkbox:checked + .ToolIcon__icon svg {
  color: var(--color-on-primary-container);
}
.excalidraw .ToolIcon .ToolIcon__keybinding {
  bottom: 4px;
  right: 4px;
}
.excalidraw .ToolIcon .ToolIcon__icon:hover {
  background: var(--button-hover-bg);
}
.excalidraw .ToolIcon .ToolIcon__icon:active {
  background: var(--button-hover-bg);
  border: 1px solid var(--button-active-border);
}
.excalidraw .ToolIcon .ToolIcon__icon:active svg {
  color: var(--color-on-primary-container);
}
.excalidraw .ToolIcon .ToolIcon__icon[aria-disabled=true] {
  background: initial;
  border: none;
}
.excalidraw .ToolIcon .ToolIcon__icon[aria-disabled=true] svg {
  color: var(--color-disabled);
}
.excalidraw .ToolIcon--plain {
  background-color: transparent;
}
.excalidraw .ToolIcon--plain .ToolIcon__icon {
  width: 2rem;
  height: 2rem;
}
.excalidraw .ToolIcon_type_radio,
.excalidraw .ToolIcon_type_checkbox {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}
.excalidraw .ToolIcon__icon {
  box-sizing: border-box;
  width: var(--default-button-size);
  height: var(--default-button-size);
  color: var(--icon-fill-color);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--border-radius-lg);
}
.excalidraw .ToolIcon__icon + .ToolIcon__label {
  margin-inline-start: 0;
}
.excalidraw .ToolIcon__icon svg {
  position: relative;
  width: var(--default-icon-size);
  height: var(--default-icon-size);
  color: var(--icon-fill-color);
}
.excalidraw .ToolIcon__label {
  display: flex;
  align-items: center;
  color: var(--icon-fill-color);
  font-family: var(--ui-font);
  margin: 0 0.8em;
  text-overflow: ellipsis;
}
.excalidraw .ToolIcon__label .Spinner {
  margin-left: 0.6em;
}
.excalidraw .ToolIcon_size_small .ToolIcon__icon {
  width: 2rem;
  height: 2rem;
  font-size: 0.8em;
}
.excalidraw .ToolIcon_type_button,
.excalidraw .Modal .ToolIcon_type_button {
  padding: 0;
  border: none;
  margin: 0;
  font-size: inherit;
  background-color: initial;
}
.excalidraw .ToolIcon_type_button:focus-visible,
.excalidraw .Modal .ToolIcon_type_button:focus-visible {
  box-shadow: 0 0 0 2px var(--focus-highlight-color);
}
.excalidraw .ToolIcon_type_button.ToolIcon--selected,
.excalidraw .Modal .ToolIcon_type_button.ToolIcon--selected {
  background-color: var(--button-gray-2);
}
.excalidraw .ToolIcon_type_button.ToolIcon--selected:active,
.excalidraw .Modal .ToolIcon_type_button.ToolIcon--selected:active {
  background-color: var(--button-gray-3);
}
.excalidraw .ToolIcon_type_button:active,
.excalidraw .Modal .ToolIcon_type_button:active {
  background-color: var(--button-gray-3);
}
.excalidraw .ToolIcon_type_button:disabled,
.excalidraw .Modal .ToolIcon_type_button:disabled {
  cursor: default;
}
.excalidraw .ToolIcon_type_button:disabled:active,
.excalidraw .ToolIcon_type_button:disabled:focus-visible,
.excalidraw .ToolIcon_type_button:disabled:hover,
.excalidraw .Modal .ToolIcon_type_button:disabled:active,
.excalidraw .Modal .ToolIcon_type_button:disabled:focus-visible,
.excalidraw .Modal .ToolIcon_type_button:disabled:hover {
  background-color: initial;
  border: none;
  box-shadow: none;
}
.excalidraw .ToolIcon_type_button:disabled svg,
.excalidraw .Modal .ToolIcon_type_button:disabled svg {
  color: var(--color-disabled);
}
.excalidraw .ToolIcon_type_button--show,
.excalidraw .Modal .ToolIcon_type_button--show {
  visibility: visible;
}
.excalidraw .ToolIcon_type_button--hide,
.excalidraw .Modal .ToolIcon_type_button--hide {
  display: none !important;
}
.excalidraw .ToolIcon_type_floating {
  background-color: transparent;
}
.excalidraw .ToolIcon_type_floating:hover {
  background-color: transparent;
}
.excalidraw .ToolIcon_type_floating:active {
  background-color: transparent;
}
.excalidraw .ToolIcon_type_floating .ToolIcon__icon {
  background-color: var(--button-gray-1);
  width: 2rem;
  height: 2rem;
}
.excalidraw .ToolIcon_type_floating .ToolIcon__icon:hover {
  background-color: var(--button-gray-2);
}
.excalidraw .ToolIcon_type_floating .ToolIcon__icon:active {
  background-color: var(--button-gray-3);
}
.excalidraw .ToolIcon__keybinding {
  position: absolute;
  bottom: 2px;
  right: 3px;
  font-size: 0.625rem;
  color: var(--keybinding-color);
  font-family: var(--ui-font);
  user-select: none;
}
:root[dir=ltr] .excalidraw .unlocked-icon {
  left: 2px;
}
:root[dir=rtl] .excalidraw .unlocked-icon {
  right: 2px;
}
.excalidraw .App-toolbar-container .ToolIcon__icon {
  width: var(--lg-button-size);
  height: var(--lg-button-size);
}
@media screen and (max-width: 450px) {
  .excalidraw .App-toolbar-container .ToolIcon__icon {
    width: 1.8rem;
    height: 1.8rem;
  }
}
@media screen and (max-width: 379px) {
  .excalidraw .App-toolbar-container .ToolIcon__icon {
    width: 1.5rem;
    height: 1.5rem;
  }
}
.excalidraw .App-toolbar-container .ToolIcon__icon svg {
  width: var(--lg-icon-size);
  height: var(--lg-icon-size);
}
.excalidraw .App-toolbar-container .ToolIcon__LaserPointer .ToolIcon__icon,
.excalidraw .App-toolbar-container .ToolIcon__MagicButton .ToolIcon__icon {
  width: var(--default-button-size);
  height: var(--default-button-size);
}

/* components/ButtonIcon.scss */
.excalidraw {
  --theme-filter: none;
  --button-destructive-bg-color: #ffe3e3;
  --button-destructive-color: #c92a2a;
  --button-gray-1: #e9ecef;
  --button-gray-2: #ced4da;
  --button-gray-3: #adb5bd;
  --button-special-active-bg-color: #ebfbee;
  --dialog-border-color: var(--color-gray-20);
  --dropdown-icon: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="292.4" height="292.4" viewBox="0 0 292 292"><path d="M287 197L159 69c-4-3-8-5-13-5s-9 2-13 5L5 197c-3 4-5 8-5 13s2 9 5 13c4 4 8 5 13 5h256c5 0 9-1 13-5s5-8 5-13-1-9-5-13z"/></svg>');
  --focus-highlight-color: #a5d8ff;
  --icon-fill-color: var(--color-on-surface);
  --icon-green-fill-color: #2b8a3e;
  --default-bg-color: #ffffff;
  --input-bg-color: #ffffff;
  --input-border-color: #ced4da;
  --input-hover-bg-color: #f1f3f5;
  --input-label-color: #495057;
  --island-bg-color: #ffffff;
  --keybinding-color: var(--color-gray-40);
  --link-color: #1c7ed6;
  --overlay-bg-color: rgba(255, 255, 255, 0.88);
  --popup-bg-color: var(--island-bg-color);
  --popup-secondary-bg-color: #f1f3f5;
  --popup-text-color: #000000;
  --popup-text-inverted-color: #ffffff;
  --select-highlight-color: #339af0;
  --shadow-island:
    0px 0px 0.9310142993927002px 0px rgba(0, 0, 0, 0.17),
    0px 0px 3.1270833015441895px 0px rgba(0, 0, 0, 0.08),
    0px 7px 14px 0px rgba(0, 0, 0, 0.05);
  --button-hover-bg: var(--color-surface-high);
  --button-active-bg: var(--color-surface-high);
  --button-active-border: var(--color-brand-active);
  --default-border-color: var(--color-surface-high);
  --default-button-size: 2rem;
  --default-icon-size: 1rem;
  --lg-button-size: 2.25rem;
  --lg-icon-size: 1rem;
  --editor-container-padding: 1rem;
  --scrollbar-thumb: var(--button-gray-2);
  --scrollbar-thumb-hover: var(--button-gray-3);
  --color-slider-track: hsl(240, 100%, 90%);
  --color-slider-thumb: var(--color-gray-80);
  --modal-shadow:
    0px 100px 80px rgba(0, 0, 0, 0.07),
    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),
    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),
    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),
    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),
    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);
  --avatar-border-color: var(--color-gray-20);
  --sidebar-shadow:
    0px 100px 80px rgba(0, 0, 0, 0.07),
    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),
    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),
    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),
    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),
    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);
  --sidebar-border-color: var(--color-surface-high);
  --sidebar-bg-color: var(--island-bg-color);
  --library-dropdown-shadow:
    0px 15px 6px rgba(0, 0, 0, 0.01),
    0px 8px 5px rgba(0, 0, 0, 0.05),
    0px 4px 4px rgba(0, 0, 0, 0.09),
    0px 1px 2px rgba(0, 0, 0, 0.1),
    0px 0px 0px rgba(0, 0, 0, 0.1);
  --space-factor: 0.25rem;
  --text-primary-color: var(--color-on-surface);
  --color-selection: #6965db;
  --color-icon-white: #ffffff;
  --color-primary: #6965db;
  --color-primary-darker: #5b57d1;
  --color-primary-darkest: #4a47b1;
  --color-primary-light: #e3e2fe;
  --color-primary-light-darker: #d7d5ff;
  --color-primary-hover: #5753d0;
  --color-gray-10: #f5f5f5;
  --color-gray-20: #ebebeb;
  --color-gray-30: #d6d6d6;
  --color-gray-40: #b8b8b8;
  --color-gray-50: #999999;
  --color-gray-60: #7a7a7a;
  --color-gray-70: #5c5c5c;
  --color-gray-80: #3d3d3d;
  --color-gray-85: #242424;
  --color-gray-90: #1e1e1e;
  --color-gray-100: #121212;
  --color-disabled: var(--color-gray-40);
  --color-warning: #fceeca;
  --color-warning-dark: #f5c354;
  --color-warning-darker: #f3ab2c;
  --color-warning-darkest: #ec8b14;
  --color-text-warning: var(--text-primary-color);
  --color-danger: #db6965;
  --color-danger-dark: #db6965;
  --color-danger-darker: #d65550;
  --color-danger-darkest: #d1413c;
  --color-danger-text: black;
  --color-danger-background: #fff0f0;
  --color-danger-icon-background: #ffdad6;
  --color-danger-color: #700000;
  --color-danger-icon-color: #700000;
  --color-warning-background: var(--color-warning);
  --color-warning-icon-background: var(--color-warning-dark);
  --color-warning-color: var(--text-primary-color);
  --color-warning-icon-color: var(--text-primary-color);
  --color-muted: var(--color-gray-30);
  --color-muted-darker: var(--color-gray-60);
  --color-muted-darkest: var(--color-gray-100);
  --color-muted-background: var(--color-gray-80);
  --color-muted-background-darker: var(--color-gray-100);
  --color-promo: var(--color-primary);
  --color-success: #cafccc;
  --color-success-darker: #bafabc;
  --color-success-darkest: #a5eba8;
  --color-success-text: #268029;
  --color-success-contrast: #65bb6a;
  --color-success-contrast-hover: #6bcf70;
  --color-success-contrast-active: #6edf74;
  --color-logo-icon: var(--color-primary);
  --color-logo-text: #190064;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --color-surface-high: #f1f0ff;
  --color-surface-mid: #f2f2f7;
  --color-surface-low: #ececf4;
  --color-surface-lowest: #ffffff;
  --color-on-surface: #1b1b1f;
  --color-brand-hover: #5753d0;
  --color-on-primary-container: #030064;
  --color-surface-primary-container: #e0dfff;
  --color-brand-active: #4440bf;
  --color-border-outline: #767680;
  --color-border-outline-variant: #c5c5d0;
  --color-surface-primary-container: #e0dfff;
  --color-badge: #0b6513;
  --background-color-badge: #d3ffd2;
}
@media screen and (min-device-width: 1921px) {
  .excalidraw {
    --lg-button-size: 2.5rem;
    --lg-icon-size: 1.25rem;
    --default-button-size: 2.25rem;
    --default-icon-size: 1.25rem;
  }
}
.excalidraw.theme--dark.theme--dark-background-none {
  background: none;
}
.excalidraw.theme--dark {
  --theme-filter: invert(93%) hue-rotate(180deg);
  --button-destructive-bg-color: #5a0000;
  --button-destructive-color: #ffa8a8;
  --button-gray-1: #363636;
  --button-gray-2: #272727;
  --button-gray-3: #222;
  --button-special-active-bg-color: #204624;
  --dialog-border-color: var(--color-gray-80);
  --dropdown-icon: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="292.4" height="292.4" viewBox="0 0 292 292"><path fill="%23ced4da" d="M287 197L159 69c-4-3-8-5-13-5s-9 2-13 5L5 197c-3 4-5 8-5 13s2 9 5 13c4 4 8 5 13 5h256c5 0 9-1 13-5s5-8 5-13-1-9-5-13z"/></svg>');
  --focus-highlight-color: #228be6;
  --icon-green-fill-color: #69db7c;
  --default-bg-color: #121212;
  --input-bg-color: #121212;
  --input-border-color: #2e2e2e;
  --input-hover-bg-color: #181818;
  --input-label-color: #e9ecef;
  --island-bg-color: #232329;
  --keybinding-color: var(--color-gray-60);
  --link-color: #4dabf7;
  --overlay-bg-color: rgba(52, 58, 64, 0.12);
  --popup-secondary-bg-color: #222;
  --popup-text-color: #ced4da;
  --popup-text-inverted-color: #2c2c2c;
  --select-highlight-color: #4dabf7;
  --shadow-island:
    0px 0px 0.9310142993927002px 0px rgba(0, 0, 0, 0.17),
    0px 0px 3.1270833015441895px 0px rgba(0, 0, 0, 0.08),
    0px 7px 14px 0px rgba(0, 0, 0, 0.05);
  --modal-shadow:
    0px 100px 80px rgba(0, 0, 0, 0.07),
    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),
    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),
    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),
    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),
    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);
  --avatar-border-color: var(--color-gray-85);
  --scrollbar-thumb: #343a40;
  --scrollbar-thumb-hover: #495057;
  --color-slider-track: hsl(244, 23%, 39%);
  --color-selection: #3530c4;
  --color-icon-white: var(--color-gray-90);
  --color-primary: #a8a5ff;
  --color-primary-darker: #b2aeff;
  --color-primary-darkest: #beb9ff;
  --color-primary-light: #4f4d6f;
  --color-primary-light-darker: #43415e;
  --color-primary-hover: #bbb8ff;
  --color-disabled: var(--color-gray-70);
  --color-text-warning: var(--color-gray-80);
  --color-danger: #ffa8a5;
  --color-danger-dark: #672120;
  --color-danger-darker: #8f2625;
  --color-danger-darkest: #ac2b29;
  --color-danger-text: #fbcbcc;
  --color-danger-background: #fbcbcc;
  --color-danger-icon-background: #672120;
  --color-danger-color: #261919;
  --color-danger-icon-color: #fbcbcc;
  --color-warning-background: var(--color-warning);
  --color-warning-icon-background: var(--color-warning-dark);
  --color-warning-color: var(--color-gray-80);
  --color-warning-icon-color: var(--color-gray-80);
  --color-muted: var(--color-gray-80);
  --color-muted-darker: var(--color-gray-60);
  --color-muted-darkest: var(--color-gray-20);
  --color-muted-background: var(--color-gray-40);
  --color-muted-background-darker: var(--color-gray-20);
  --color-logo-text: #e2dfff;
  --color-surface-high: hsl(245, 10%, 21%);
  --color-surface-low: hsl(240, 8%, 15%);
  --color-surface-mid: hsl(240 6% 10%);
  --color-surface-lowest: hsl(0, 0%, 7%);
  --color-on-surface: #e3e3e8;
  --color-brand-hover: #bbb8ff;
  --color-on-primary-container: #e0dfff;
  --color-surface-primary-container: #403e6a;
  --color-brand-active: #d0ccff;
  --color-border-outline: #8e8d9c;
  --color-border-outline-variant: #46464f;
  --color-surface-primary-container: #403e6a;
}
.excalidraw button.standalone {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.625rem;
  width: var(--button-width, var(--default-button-size));
  height: var(--button-height, var(--default-button-size));
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: var(--button-border, var(--default-border-color));
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  background-color: var(--button-bg, var(--island-bg-color));
  color: var(--button-color, var(--color-on-surface));
  font-family: var(--ui-font);
  padding: 0;
}
.excalidraw button.standalone svg {
  width: var(--button-width, var(--lg-icon-size));
  height: var(--button-height, var(--lg-icon-size));
}
.excalidraw button.standalone:hover {
  background-color: var(--button-hover-bg, var(--island-bg-color));
  border-color: var(--button-hover-border, var(--button-border, var(--default-border-color)));
  color: var(--button-hover-color, var(--button-color, var(--text-primary-color, inherit)));
}
.excalidraw button.standalone:active {
  background-color: var(--button-active-bg, var(--island-bg-color));
  border-color: var(--button-active-border, var(--color-primary-darkest));
}
.excalidraw button.standalone.active {
  background-color: var(--button-selected-bg, var(--color-surface-primary-container));
  border-color: var(--button-selected-border, var(--color-surface-primary-container));
}
.excalidraw button.standalone.active:hover {
  background-color: var(--button-selected-hover-bg, var(--color-surface-primary-container));
}
.excalidraw button.standalone.active svg {
  color: var(--button-color, var(--color-on-primary-container));
}
.excalidraw button.standalone svg {
  width: var(--default-icon-size);
  height: var(--default-icon-size);
}
.excalidraw button.standalone > * {
  pointer-events: none;
}

/* components/EyeDropper.scss */
.excalidraw .excalidraw-eye-dropper-container,
.excalidraw .excalidraw-eye-dropper-backdrop {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: var(--zIndex-eyeDropperBackdrop);
  touch-action: none;
}
.excalidraw .excalidraw-eye-dropper-container {
  pointer-events: none;
}
.excalidraw .excalidraw-eye-dropper-backdrop {
  pointer-events: all;
}
.excalidraw .excalidraw-eye-dropper-preview {
  pointer-events: none;
  width: 3rem;
  height: 3rem;
  position: fixed;
  z-index: var(--zIndex-eyeDropperPreview);
  border-radius: 1rem;
  border: 1px solid var(--default-border-color);
  filter: var(--theme-filter);
}
.excalidraw .excalidraw-eye-dropper-trigger {
  width: 1.25rem;
  height: 1.25rem;
  cursor: pointer;
  padding: 4px;
  margin-right: -4px;
  margin-left: -2px;
  border-radius: 0.5rem;
  color: var(--icon-fill-color);
}
.excalidraw .excalidraw-eye-dropper-trigger:hover {
  background: var(--button-hover-bg);
}
.excalidraw .excalidraw-eye-dropper-trigger.selected {
  color: var(--color-primary);
  background: var(--color-primary-light);
}

/* components/Island.scss */
.excalidraw .Island {
  --padding: 0;
  box-sizing: border-box;
  background-color: var(--island-bg-color);
  box-shadow: var(--shadow-island);
  border-radius: var(--border-radius-lg);
  padding: calc(var(--padding) * var(--space-factor));
  position: relative;
  transition: box-shadow 0.5s ease-in-out;
}
.excalidraw .Island.zen-mode {
  box-shadow: none;
}

/* components/ColorPicker/ColorPicker.scss */
.excalidraw .focus-visible-none:focus-visible {
  outline: none !important;
}
.excalidraw .color-picker__heading {
  padding: 0 0.5rem;
  font-size: 0.75rem;
  text-align: left;
}
.excalidraw .color-picker-container {
  display: grid;
  grid-template-columns: 1fr 20px 1.625rem;
  padding: 0.25rem 0px;
  align-items: center;
}
.excalidraw--mobile.excalidraw .color-picker-container {
  max-width: 11rem;
}
.excalidraw .color-picker__top-picks {
  display: flex;
  justify-content: space-between;
}
.excalidraw .color-picker__button {
  --radius: 0.25rem;
  padding: 0;
  margin: 0;
  width: 1.35rem;
  height: 1.35rem;
  border: 1px solid var(--color-gray-30);
  border-radius: var(--radius);
  filter: var(--theme-filter);
  background-color: var(--swatch-color);
  background-position: left center;
  position: relative;
  font-family: inherit;
  box-sizing: border-box;
}
.excalidraw .color-picker__button:hover::after {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  box-shadow: 0 0 0 1px var(--color-gray-30);
  border-radius: calc(var(--radius) + 1px);
  filter: var(--theme-filter);
}
.excalidraw .color-picker__button.active .color-picker__button-outline {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  box-shadow: 0 0 0 1px var(--color-primary-darkest);
  z-index: 1;
  border-radius: calc(var(--radius) + 1px);
  filter: var(--theme-filter);
}
.excalidraw .color-picker__button:focus-visible {
  outline: none;
}
.excalidraw .color-picker__button:focus-visible::after {
  content: "";
  position: absolute;
  top: -4px;
  right: -4px;
  bottom: -4px;
  left: -4px;
  border: 3px solid var(--focus-highlight-color);
  border-radius: calc(var(--radius) + 1px);
}
.excalidraw .color-picker__button:focus-visible.active .color-picker__button-outline {
  display: none;
}
.excalidraw .color-picker__button--large {
  --radius: 0.5rem;
  width: 1.875rem;
  height: 1.875rem;
}
.excalidraw .color-picker__button.is-transparent {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==);
}
.excalidraw .color-picker__button--no-focus-visible {
  border: 0;
}
.excalidraw .color-picker__button--no-focus-visible::after {
  display: none;
}
.excalidraw .color-picker__button--no-focus-visible:focus-visible {
  outline: none !important;
}
.excalidraw .color-picker__button.active-color {
  border-radius: calc(var(--radius) + 1px);
  width: 1.625rem;
  height: 1.625rem;
}
.excalidraw .color-picker__button__hotkey-label {
  position: absolute;
  right: 4px;
  bottom: 4px;
  filter: none;
  font-size: 11px;
}
.excalidraw .color-picker {
  background: var(--popup-bg-color);
  border: 0 solid rgba(255, 255, 255, 0.25);
  box-shadow: rgba(0, 0, 0, 0.25) 0 1px 4px;
  border-radius: 4px;
  position: absolute;
}
:root[dir=ltr] .excalidraw .color-picker {
  left: -5.5px;
}
:root[dir=rtl] .excalidraw .color-picker {
  right: -5.5px;
}
.excalidraw .color-picker-control-container {
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
  column-gap: 0.5rem;
}
.excalidraw .color-picker-control-container + .popover {
  position: static;
}
.excalidraw .color-picker-popover-container {
  margin-top: -0.25rem;
}
:root[dir=ltr] .excalidraw .color-picker-popover-container {
  margin-left: 0.5rem;
}
:root[dir=rtl] .excalidraw .color-picker-popover-container {
  margin-left: -3rem;
}
.excalidraw .color-picker-triangle {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 9px 10px;
  border-color: transparent transparent var(--popup-bg-color);
  position: absolute;
  top: 10px;
}
:root[dir=ltr] .excalidraw .color-picker-triangle {
  transform: rotate(270deg);
  left: -14px;
}
:root[dir=rtl] .excalidraw .color-picker-triangle {
  transform: rotate(90deg);
  right: -14px;
}
.excalidraw .color-picker-triangle-shadow {
  border-color: transparent transparent rgba(0, 0, 0, 0.1);
}
:root[dir=ltr] .excalidraw .color-picker-triangle-shadow {
  left: -14px;
}
:root[dir=rtl] .excalidraw .color-picker-triangle-shadow {
  right: -16px;
}
.excalidraw .color-picker-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  outline: none;
}
.excalidraw .color-picker-content--default {
  padding: 0.5rem;
  display: grid;
  grid-template-columns: repeat(5, 1.875rem);
  grid-gap: 0.25rem;
  border-radius: 4px;
}
.excalidraw .color-picker-content--default:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--focus-highlight-color);
}
.excalidraw .color-picker-content--canvas {
  display: flex;
  flex-direction: column;
  padding: 0.25rem;
}
.excalidraw .color-picker-content--canvas-title {
  color: #868e96;
  font-size: 12px;
  padding: 0 0.25rem;
}
.excalidraw .color-picker-content--canvas-colors {
  padding: 0.5rem 0;
}
.excalidraw .color-picker-content--canvas-colors .color-picker-swatch {
  margin: 0 0.25rem;
}
.excalidraw .color-picker-content .color-input-container {
  grid-column: 1/span 5;
}
.excalidraw .color-picker-swatch {
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  cursor: pointer;
  border-radius: 4px;
  margin: 0;
  box-sizing: border-box;
  border: 1px solid #ddd;
  background-color: currentColor !important;
  filter: var(--theme-filter);
}
.excalidraw .color-picker-swatch:focus {
  box-shadow: 0 0 4px 1px currentColor;
  border-color: var(--select-highlight-color);
}
.excalidraw .color-picker-transparent {
  border-radius: 4px;
  box-shadow: rgba(0, 0, 0, 0.1) 0 0 0 1px inset;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.excalidraw .color-picker-transparent,
.excalidraw .color-picker-label-swatch {
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==) left center;
}
.excalidraw .color-picker-hash {
  height: var(--default-button-size);
  flex-shrink: 0;
  padding: 0.5rem 0.5rem 0.5rem 0.75rem;
  border: 1px solid var(--default-border-color);
  border-right: 0;
  box-sizing: border-box;
  color: var(--input-label-color);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
:root[dir=ltr] .excalidraw .color-picker-hash {
  border-radius: var(--border-radius-lg) 0 0 var(--border-radius-lg);
}
:root[dir=rtl] .excalidraw .color-picker-hash {
  border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
  border-right: 1px solid var(--default-border-color);
  border-left: 0;
}
.excalidraw .color-input-container {
  display: flex;
}
.excalidraw .color-input-container:focus-within {
  box-shadow: 0 0 0 1px var(--color-primary-darkest);
  border-radius: var(--border-radius-lg);
}
.excalidraw .color-picker__input-label {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  gap: 8px;
  align-items: center;
  border: 1px solid var(--default-border-color);
  border-radius: 8px;
  padding: 0 12px;
  margin: 8px;
  box-sizing: border-box;
}
.excalidraw .color-picker__input-label:focus-within {
  box-shadow: 0 0 0 1px var(--color-primary-darkest);
  border-radius: var(--border-radius-lg);
}
.excalidraw .color-picker__input-hash {
  padding: 0 0.25rem;
}
.excalidraw .color-picker-input {
  box-sizing: border-box;
  width: 100%;
  margin: 0;
  font-size: 0.875rem;
  font-family: inherit;
  background-color: transparent;
  color: var(--text-primary-color);
  border: 0;
  outline: none;
  height: var(--default-button-size);
  border: 1px solid var(--default-border-color);
  border-left: 0;
  letter-spacing: 0.4px;
  padding: 0.5rem;
  padding-left: 0.25rem;
  appearance: none;
}
:root[dir=ltr] .excalidraw .color-picker-input {
  border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
}
:root[dir=rtl] .excalidraw .color-picker-input {
  border-radius: var(--border-radius-lg) 0 0 var(--border-radius-lg);
  border-left: 1px solid var(--default-border-color);
  border-right: 0;
}
.excalidraw .color-picker-input:focus-visible {
  box-shadow: none;
}
.excalidraw .color-picker-label-swatch-container {
  border: 1px solid var(--default-border-color);
  border-radius: var(--border-radius-lg);
  width: var(--default-button-size);
  height: var(--default-button-size);
  box-sizing: border-box;
  overflow: hidden;
}
.excalidraw .color-picker-label-swatch {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.625rem;
  width: var(--button-width, var(--default-button-size));
  height: var(--button-height, var(--default-button-size));
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: var(--button-border, var(--default-border-color));
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  background-color: var(--button-bg, var(--island-bg-color));
  color: var(--button-color, var(--color-on-surface));
  font-family: var(--ui-font);
  background-color: var(--swatch-color) !important;
  overflow: hidden;
  position: relative;
  filter: var(--theme-filter);
  border: 0 !important;
}
.excalidraw .color-picker-label-swatch svg {
  width: var(--button-width, var(--lg-icon-size));
  height: var(--button-height, var(--lg-icon-size));
}
.excalidraw .color-picker-label-swatch:hover {
  background-color: var(--button-hover-bg, var(--island-bg-color));
  border-color: var(--button-hover-border, var(--button-border, var(--default-border-color)));
  color: var(--button-hover-color, var(--button-color, var(--text-primary-color, inherit)));
}
.excalidraw .color-picker-label-swatch:active {
  background-color: var(--button-active-bg, var(--island-bg-color));
  border-color: var(--button-active-border, var(--color-primary-darkest));
}
.excalidraw .color-picker-label-swatch.active {
  background-color: var(--button-selected-bg, var(--color-surface-primary-container));
  border-color: var(--button-selected-border, var(--color-surface-primary-container));
}
.excalidraw .color-picker-label-swatch.active:hover {
  background-color: var(--button-selected-hover-bg, var(--color-surface-primary-container));
}
.excalidraw .color-picker-label-swatch.active svg {
  color: var(--button-color, var(--color-on-primary-container));
}
.excalidraw .color-picker-label-swatch:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--swatch-color);
}
.excalidraw .color-picker-keybinding {
  position: absolute;
  bottom: 2px;
  font-size: 0.7em;
}
:root[dir=ltr] .excalidraw .color-picker-keybinding {
  right: 2px;
}
:root[dir=rtl] .excalidraw .color-picker-keybinding {
  left: 2px;
}
.excalidraw--mobile.excalidraw .color-picker-keybinding {
  display: none;
}
.excalidraw .color-picker-type-canvasBackground .color-picker-keybinding {
  color: #aaa;
}
.excalidraw .color-picker-type-elementBackground .color-picker-keybinding {
  color: #ffffff;
}
.excalidraw .color-picker-swatch[aria-label=transparent] .color-picker-keybinding {
  color: #aaa;
}
.excalidraw .color-picker-type-elementStroke .color-picker-keybinding {
  color: #d4d4d4;
}
.excalidraw.theme--dark .color-picker-type-elementBackground .color-picker-keybinding {
  color: #000000;
}
.excalidraw.theme--dark .color-picker-swatch[aria-label=transparent] .color-picker-keybinding {
  color: #000000;
}

/* components/IconPicker.scss */
.excalidraw .picker {
  padding: 0.5rem;
  background: var(--popup-bg-color);
  border: 0 solid rgba(255, 255, 255, 0.25);
  box-shadow: var(--shadow-island);
  border-radius: 4px;
  position: absolute;
}
:root[dir=rtl] .excalidraw .picker {
  padding: 0.4rem;
}
.excalidraw .picker-container button,
.excalidraw .picker button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.excalidraw .picker-container button:focus-visible,
.excalidraw .picker button:focus-visible {
  outline: transparent;
  background-color: var(--button-gray-2);
}
.excalidraw .picker-container button:focus-visible svg,
.excalidraw .picker button:focus-visible svg {
  opacity: 1;
}
.excalidraw .picker-container button:hover,
.excalidraw .picker button:hover {
  background-color: var(--button-gray-2);
}
.excalidraw .picker-container button:active,
.excalidraw .picker button:active {
  background-color: var(--button-gray-3);
}
.excalidraw .picker-container button:disabled,
.excalidraw .picker button:disabled {
  cursor: not-allowed;
}
.excalidraw .picker-container button svg,
.excalidraw .picker button svg {
  margin: 0;
  width: 36px;
  height: 18px;
  pointer-events: none;
}
.excalidraw .picker button {
  padding: 0.25rem 0.28rem 0.35rem 0.25rem;
}
.excalidraw .picker-content {
  display: grid;
  grid-template-columns: repeat(4, auto);
  grid-gap: 0.5rem;
  border-radius: 4px;
}
.excalidraw .picker-collapsible {
  font-size: 0.75rem;
  padding: 0.5rem 0;
}
.excalidraw .picker-keybinding {
  position: absolute;
  bottom: 2px;
  font-size: 0.7em;
  color: var(--keybinding-color);
}
:root[dir=ltr] .excalidraw .picker-keybinding {
  right: 2px;
}
:root[dir=rtl] .excalidraw .picker-keybinding {
  left: 2px;
}
.excalidraw--mobile.excalidraw .picker-keybinding {
  display: none;
}
.excalidraw .picker-type-canvasBackground .picker-keybinding {
  color: #aaa;
}
.excalidraw .picker-type-elementBackground .picker-keybinding {
  color: #ffffff;
}
.excalidraw .picker-swatch[aria-label=transparent] .picker-keybinding {
  color: #aaa;
}
.excalidraw .picker-type-elementStroke .picker-keybinding {
  color: #d4d4d4;
}
.excalidraw.theme--dark .picker-type-elementBackground .picker-keybinding {
  color: #000000;
}
.excalidraw.theme--dark .picker-swatch[aria-label=transparent] .picker-keybinding {
  color: #000000;
}

/* components/QuickSearch.scss */
.excalidraw {
  --list-border-color: var(--color-gray-20);
}
.excalidraw .QuickSearch__wrapper {
  position: relative;
  height: 2.6rem;
  border-bottom: 1px solid var(--list-border-color);
}
.excalidraw .QuickSearch__wrapper svg {
  position: absolute;
  top: 47.5%;
  transform: translateY(-50%);
  left: 0.75rem;
  width: 1.25rem;
  height: 1.25rem;
  color: var(--color-gray-40);
  z-index: 1;
}
.excalidraw.theme--dark {
  --list-border-color: var(--color-gray-80);
}
.excalidraw.theme--dark .QuickSearch__wrapper {
  border-bottom: none;
}
.excalidraw .QuickSearch__input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  box-sizing: border-box;
  border: 0 !important;
  font-size: 0.875rem;
  padding-left: 2.5rem !important;
  padding-right: 0.75rem !important;
}
.excalidraw .QuickSearch__input::placeholder {
  color: var(--color-gray-40);
}
.excalidraw .QuickSearch__input:focus {
  box-shadow: none !important;
}

/* components/ScrollableList.scss */
.excalidraw .ScrollableList__wrapper {
  position: static !important;
  border: none;
  font-size: 0.875rem;
  overflow-y: auto;
}
.excalidraw .ScrollableList__wrapper > .empty,
.excalidraw .ScrollableList__wrapper > .hint {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem;
  font-size: 0.75rem;
  color: var(--color-gray-60);
  overflow: hidden;
  text-align: center;
  line-height: 150%;
}

/* components/FontPicker/FontPicker.scss */
.excalidraw .FontPicker__container {
  display: grid;
  grid-template-columns: calc(1rem + 3 * var(--default-button-size)) 1rem 1fr;
  align-items: center;
}
.excalidraw--mobile.excalidraw .FontPicker__container {
  max-width: calc(2rem + 4 * var(--default-button-size));
}

/* components/Range.scss */
.excalidraw {
  --slider-thumb-size: 16px;
}
.excalidraw .range-wrapper {
  position: relative;
  padding-top: 10px;
  padding-bottom: 30px;
}
.excalidraw .range-input {
  width: 100%;
  height: 4px;
  -webkit-appearance: none;
  background: var(--color-slider-track);
  border-radius: 2px;
  outline: none;
}
.excalidraw .range-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: var(--slider-thumb-size);
  height: var(--slider-thumb-size);
  background: var(--color-slider-thumb);
  border-radius: 50%;
  cursor: pointer;
  border: none;
}
.excalidraw .range-input::-moz-range-thumb {
  width: var(--slider-thumb-size);
  height: var(--slider-thumb-size);
  background: var(--color-slider-thumb);
  border-radius: 50%;
  cursor: pointer;
  border: none;
}
.excalidraw .value-bubble {
  position: absolute;
  bottom: 0;
  transform: translateX(-50%);
  font-size: 12px;
  color: var(--text-primary-color);
}
.excalidraw .zero-label {
  position: absolute;
  bottom: 0;
  left: 4px;
  font-size: 12px;
  color: var(--text-primary-color);
}

/* components/Tooltip.scss */
.excalidraw-tooltip {
  --ui-font:
    Assistant,
    system-ui,
    BlinkMacSystemFont,
    -apple-system,
    Segoe UI,
    Roboto,
    Helvetica,
    Arial,
    sans-serif;
  font-family: var(--ui-font);
  position: fixed;
  z-index: var(--zIndex-popup);
  padding: 8px;
  border-radius: 6px;
  box-sizing: border-box;
  pointer-events: none;
  word-wrap: break-word;
  background: #000000;
  line-height: 1.5;
  text-align: center;
  font-size: 13px;
  font-weight: 500;
  color: #ffffff;
  display: none;
}
.excalidraw-tooltip.excalidraw-tooltip--visible {
  display: block;
}
.excalidraw-tooltip-wrapper {
  display: flex;
}
.excalidraw-tooltip-icon {
  width: 0.9em;
  height: 0.9em;
  margin-left: 5px;
  margin-top: 1px;
  display: flex;
}
.excalidraw--mobile.excalidraw-tooltip-icon {
  display: none;
}

/* components/TextInput.scss */
.excalidraw .TextInput {
  display: inline-block;
}

/* components/ProjectName.scss */
.ProjectName {
  margin: auto;
  display: flex;
  align-items: center;
}
.ProjectName .TextInput {
  height: calc(1rem - 3px);
  width: 200px;
  overflow: hidden;
  text-align: center;
  margin-left: 8px;
  text-overflow: ellipsis;
}
.ProjectName .TextInput--readonly {
  background: none;
  border: none;
  width: auto;
  max-width: 200px;
  padding-left: 2px;
}
.ProjectName .TextInput--readonly:hover {
  background: none;
}

/* components/CheckboxItem.scss */
.excalidraw .Checkbox {
  margin: 4px 0.3em;
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}
.excalidraw .Checkbox:hover:not(.is-checked) .Checkbox-box:not(:focus) {
  box-shadow: 0 0 0 2px #4dabf7;
}
.excalidraw .Checkbox:hover:not(.is-checked) .Checkbox-box:not(:focus) svg {
  display: block;
  opacity: 0.3;
}
.excalidraw .Checkbox:active .Checkbox-box {
  box-shadow: 0 0 2px 1px inset #1c7ed6 !important;
}
.excalidraw .Checkbox:hover .Checkbox-box {
  background-color: rgba(208, 235, 255, 0.2);
}
.excalidraw .Checkbox.is-checked .Checkbox-box {
  background-color: #d0ebff;
}
.excalidraw .Checkbox.is-checked .Checkbox-box svg {
  display: block;
}
.excalidraw .Checkbox.is-checked:hover .Checkbox-box {
  background-color: #a5d8ff;
}
.excalidraw .Checkbox .Checkbox-box {
  width: 22px;
  height: 22px;
  padding: 0;
  flex: 0 0 auto;
  margin: 0 1em;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 0 2px #1c7ed6;
  background-color: transparent;
  border-radius: 4px;
  color: #1c7ed6;
  border: 0;
}
.excalidraw .Checkbox .Checkbox-box:focus {
  box-shadow: 0 0 0 3px #1c7ed6;
}
.excalidraw .Checkbox .Checkbox-box svg {
  display: none;
  width: 16px;
  height: 16px;
  stroke-width: 3px;
}
.excalidraw .Checkbox .Checkbox-label {
  display: flex;
  align-items: center;
}
.excalidraw .Checkbox .excalidraw-tooltip-icon {
  width: 1em;
  height: 1em;
}

/* components/Avatar.scss */
.excalidraw .Avatar {
  width: var(--avatar-size, 1.5rem);
  height: var(--avatar-size, 1.5rem);
  position: relative;
  border-radius: 100%;
  outline-offset: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 700;
  line-height: 1;
  color: var(--color-gray-90);
  flex: 0 0 auto;
}
.excalidraw .Avatar:active {
  transform: scale(0.94);
}
.excalidraw .Avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 100%;
}
.excalidraw .Avatar::before {
  content: "";
  position: absolute;
  top: -3px;
  right: -3px;
  bottom: -3px;
  left: -3px;
  border-radius: 100%;
}
.excalidraw .Avatar.is-followed::before {
  border-color: var(--color-primary-hover);
  box-shadow: 0 0 0 1px var(--color-primary-hover);
}
.excalidraw .Avatar.is-current-user {
  cursor: auto;
}

/* components/hyperlink/Hyperlink.scss */
.excalidraw-hyperlinkContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: absolute;
  box-shadow: 0px 2px 4px 0 rgba(0, 0, 0, 0.3);
  z-index: var(--zIndex-hyperlinkContainer);
  background: var(--island-bg-color);
  border-radius: var(--border-radius-md);
  box-sizing: border-box;
  min-height: 42px;
}
.excalidraw-hyperlinkContainer-input,
.excalidraw-hyperlinkContainer button {
  z-index: 100;
}
.excalidraw-hyperlinkContainer-input,
.excalidraw-hyperlinkContainer-link {
  height: 24px;
  padding: 0 8px;
  line-height: 24px;
  font-size: 0.9rem;
  font-weight: 500;
  font-family: var(--ui-font);
}
.excalidraw-hyperlinkContainer-input {
  width: 18rem;
  border: none;
  background-color: transparent;
  color: var(--text-primary-color);
  outline: none;
  border: none;
  box-shadow: none !important;
}
.excalidraw-hyperlinkContainer-link {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 15rem;
}
.excalidraw-hyperlinkContainer button {
  color: #228be6;
  background-color: transparent !important;
  font-weight: 500;
}
.excalidraw-hyperlinkContainer button.excalidraw-hyperlinkContainer--remove {
  color: #c92a2a;
}
.excalidraw-hyperlinkContainer--remove .ToolIcon__icon svg {
  color: #fa5252;
}
.excalidraw-hyperlinkContainer .ToolIcon__icon {
  width: 2rem;
  height: 2rem;
}
.excalidraw-hyperlinkContainer__buttons {
  flex: 0 0 auto;
}

/* components/Dialog.scss */
.excalidraw .Dialog {
  user-select: text;
  cursor: auto;
}
.excalidraw .Dialog__title {
  margin: 0;
  text-align: left;
  font-size: 1.25rem;
  border-bottom: 1px solid var(--dialog-border-color);
  padding: 0 0 0.75rem;
  margin-bottom: 1.5rem;
}
.excalidraw .Dialog__close {
  color: var(--color-gray-40);
  margin: 0;
  position: absolute;
  top: 0.75rem;
  right: 0.5rem;
  border: 0;
  background-color: transparent;
  line-height: 0;
  cursor: pointer;
}
.excalidraw .Dialog__close:hover {
  color: var(--color-gray-60);
}
.excalidraw .Dialog__close:active {
  color: var(--color-gray-40);
}
.excalidraw .Dialog__close svg {
  width: 1.5rem;
  height: 1.5rem;
}
.excalidraw .Dialog__close + .Dialog__content {
  --offset: 28px;
  height: calc(100% - var(--offset)) !important;
  margin-top: var(--offset) !important;
}
.excalidraw .Dialog--fullscreen .Dialog__close {
  top: 1.25rem;
  right: 1.25rem;
}

/* components/Modal.scss */
.excalidraw.excalidraw-modal-container {
  position: absolute;
  z-index: var(--zIndex-modal);
}
.excalidraw .Modal {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
  padding: calc(var(--space-factor) * 10);
  display: flex;
  flex-direction: column;
}
.excalidraw .Modal .Island {
  padding: 2.5rem;
  border: 0;
  box-shadow: none;
  border-radius: 0;
}
.excalidraw .Modal.animations-disabled .Modal__background {
  animation: none;
}
.excalidraw .Modal.animations-disabled .Modal__content {
  animation: none;
  opacity: 1;
}
.excalidraw .Modal__background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  background-color: rgba(18, 18, 18, 0.2);
  animation: Modal__background__fade-in 0.1s linear forwards;
}
.excalidraw .Modal__content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: var(--max-width);
  max-height: 100%;
  opacity: 0;
  transform: translateY(10px);
  animation: Modal__content_fade-in 0.025s ease-out 0s forwards;
  position: relative;
  overflow-y: auto;
  background: var(--island-bg-color);
  border: 1px solid var(--dialog-border-color);
  box-shadow: var(--modal-shadow);
  border-radius: 0.75rem;
  box-sizing: border-box;
}
.excalidraw .Modal__content:focus {
  outline: none;
}
@keyframes Modal__background__fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes Modal__content_fade-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
.excalidraw .Modal__close {
  color: var(--icon-fill-color);
  margin: 0;
  padding: 0.375rem;
  position: absolute;
  top: 1rem;
  right: 1rem;
  border: 0;
  background-color: transparent;
  line-height: 0;
  cursor: pointer;
}
.excalidraw .Modal__close svg {
  width: 1.5rem;
  height: 1.5rem;
}
.excalidraw .Dialog--fullscreen .Modal {
  padding: 0;
}
.excalidraw .Dialog--fullscreen .Modal__content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  max-width: 100%;
  border: 0;
  border-radius: 0;
}

/* components/Stack.scss */
.excalidraw .Stack {
  --gap: 0;
  display: grid;
  gap: calc(var(--space-factor) * var(--gap));
}
.excalidraw .Stack_vertical {
  grid-template-columns: auto;
  grid-auto-flow: row;
  grid-auto-rows: min-content;
}
.excalidraw .Stack_horizontal {
  grid-template-rows: auto;
  grid-auto-flow: column;
  grid-auto-columns: min-content;
}

/* components/ConfirmDialog.scss */
.excalidraw .confirm-dialog-buttons {
  display: flex;
  column-gap: 0.5rem;
  justify-content: flex-end;
}

/* components/DialogActionButton.scss */
.excalidraw .Dialog__action-button {
  position: relative;
  display: flex;
  column-gap: 0.5rem;
  align-items: center;
  padding: 0.5rem 1.5rem;
  border: 1px solid var(--default-border-color);
  background-color: transparent;
  height: 3rem;
  border-radius: var(--border-radius-lg);
  letter-spacing: 0.4px;
  color: inherit;
  font-family: inherit;
  font-size: 0.875rem;
  font-weight: 600;
  user-select: none;
}
.excalidraw .Dialog__action-button svg {
  display: block;
  width: 1rem;
  height: 1rem;
}
.excalidraw .Dialog__action-button--danger {
  background-color: var(--color-danger);
  border-color: var(--color-danger);
  color: #fff;
}
.excalidraw .Dialog__action-button--primary {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: #fff;
}
.excalidraw.theme--dark .Dialog__action-button--danger {
  color: var(--color-gray-100);
}
.excalidraw.theme--dark .Dialog__action-button--primary {
  color: var(--color-gray-100);
}

/* components/PublishLibrary.scss */
.excalidraw .publish-library__fields {
  display: flex;
  flex-direction: column;
}
.excalidraw .publish-library__fields label {
  padding: 1em 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.excalidraw .publish-library__fields label span {
  font-weight: 500;
  font-size: 1rem;
  color: #868e96;
}
.excalidraw .publish-library__fields label input,
.excalidraw .publish-library__fields label textarea {
  width: 70%;
  padding: 0.6em;
  font-family: var(--ui-font);
}
.excalidraw .publish-library__fields label .required {
  color: #e03131;
  margin: 0.2rem;
}
.excalidraw .publish-library__buttons {
  display: flex;
  padding: 0.2rem 0;
  justify-content: flex-end;
  gap: 0.5rem;
}
.excalidraw .publish-library__buttons .ToolIcon__icon {
  min-width: 2.5rem;
  width: auto;
  font-size: 1rem;
}
.excalidraw .publish-library__buttons .ToolIcon_type_button {
  margin-left: 1rem;
  padding: 0 0.5rem;
}
.excalidraw .publish-library__buttons--confirm.ToolIcon_type_button {
  background-color: #228be6;
}
.excalidraw .publish-library__buttons--confirm.ToolIcon_type_button:hover {
  background-color: #1971c2;
}
.excalidraw .publish-library__buttons--cancel.ToolIcon_type_button {
  background-color: #adb5bd;
}
.excalidraw .publish-library__buttons--cancel.ToolIcon_type_button:hover {
  background-color: #868e96;
}
.excalidraw .publish-library__buttons .ToolIcon__icon {
  color: #ffffff;
}
.excalidraw .publish-library__buttons .ToolIcon__icon .Spinner {
  --spinner-color: #fff;
}
.excalidraw .publish-library__buttons .ToolIcon__icon .Spinner svg {
  padding: 0.5rem;
}
.excalidraw .publish-library .selected-library-items {
  display: flex;
  flex-wrap: wrap;
}
.excalidraw .publish-library .selected-library-items .single-library-item-wrapper {
  width: 9rem;
}
.excalidraw .publish-library-warning {
  color: #fa5252;
}
.excalidraw .publish-library-note {
  padding: 1em 0;
  font-style: italic;
  font-size: 14px;
  display: block;
}
.excalidraw .single-library-item {
  position: relative;
}
.excalidraw .single-library-item-status {
  position: absolute;
  top: 0.3rem;
  left: 0.3rem;
  font-size: 0.7rem;
  color: #f03e3e;
  background: rgba(255, 255, 255, 0.9);
  padding: 0.1rem 0.2rem;
  border-radius: 0.2rem;
}
.excalidraw .single-library-item__svg {
  background-color: #ffffff;
  padding: 0.3rem;
  width: 7.5rem;
  height: 7.5rem;
  border: 1px solid var(--button-gray-2);
}
.excalidraw .single-library-item__svg svg {
  width: 100%;
  height: 100%;
}
.excalidraw .single-library-item .ToolIcon__icon {
  background-color: #ffffff;
  width: auto;
  height: auto;
  margin: 0 0.5rem;
}
.excalidraw .single-library-item .ToolIcon,
.excalidraw .single-library-item .ToolIcon_type_button:hover {
  background-color: white;
}
.excalidraw .single-library-item .required,
.excalidraw .single-library-item .error {
  color: #e03131;
  font-weight: 700;
  font-size: 1rem;
  margin: 0.2rem;
}
.excalidraw .single-library-item .error {
  font-weight: 500;
  margin: 0;
  padding: 0.3em 0;
}
.excalidraw .single-library-item--remove {
  position: absolute;
  top: 0.2rem;
  right: 1rem;
}
.excalidraw .single-library-item--remove .ToolIcon__icon {
  margin: 0;
}
.excalidraw .single-library-item--remove .ToolIcon__icon {
  background-color: #fa5252;
}
.excalidraw .single-library-item--remove .ToolIcon__icon:hover {
  background-color: #f03e3e;
}
.excalidraw .single-library-item--remove .ToolIcon__icon:active {
  background-color: #e03131;
}
.excalidraw .single-library-item--remove svg {
  color: #ffffff;
  padding: 0.26rem;
  border-radius: 0.3em;
  width: 1rem;
  height: 1rem;
}

/* components/dropdownMenu/DropdownMenu.scss */
.excalidraw .dropdown-menu {
  position: absolute;
  top: 100%;
  margin-top: 0.5rem;
}
.excalidraw .dropdown-menu--mobile {
  left: 0;
  width: 100%;
  row-gap: 0.75rem;
}
.excalidraw .dropdown-menu--mobile .dropdown-menu-container {
  padding: 8px 8px;
  box-sizing: border-box;
  box-shadow: var(--shadow-island);
  border-radius: var(--border-radius-lg);
  position: relative;
  transition: box-shadow 0.5s ease-in-out;
}
.excalidraw .dropdown-menu--mobile .dropdown-menu-container.zen-mode {
  box-shadow: none;
}
.excalidraw .dropdown-menu .dropdown-menu-container {
  background-color: var(--island-bg-color);
  max-height: calc(100vh - 150px);
  overflow-y: auto;
  --gap: 2;
}
.excalidraw .dropdown-menu .dropdown-menu-item-base {
  display: flex;
  column-gap: 0.625rem;
  font-size: 0.875rem;
  color: var(--color-on-surface);
  width: 100%;
  box-sizing: border-box;
  font-weight: 400;
  font-family: inherit;
}
.excalidraw .dropdown-menu.manual-hover .dropdown-menu-item:hover {
  background-color: transparent;
}
.excalidraw .dropdown-menu.manual-hover .dropdown-menu-item--hovered {
  background-color: var(--button-hover-bg) !important;
}
.excalidraw .dropdown-menu.manual-hover .dropdown-menu-item--selected {
  background-color: var(--color-primary-light) !important;
}
.excalidraw .dropdown-menu.fonts {
  margin-top: 1rem;
  max-height: calc(7 * (2rem + 2px) + 2.95rem + 1rem);
}
@media screen and (min-width: 1921px) {
  .excalidraw .dropdown-menu.fonts {
    max-height: calc(7 * (2.25rem + 2px) + 2.95rem + 1rem);
  }
}
.excalidraw .dropdown-menu.fonts .dropdown-menu-item-base {
  display: inline-flex;
}
.excalidraw .dropdown-menu.fonts .dropdown-menu-group:not(:first-child) {
  margin-top: 1rem;
}
.excalidraw .dropdown-menu.fonts .dropdown-menu-group-title {
  font-size: 0.75rem;
  text-align: left;
  font-weight: 400;
  margin: 0 0 0.5rem;
  line-height: 1.3;
}
.excalidraw .dropdown-menu .dropdown-menu-item {
  height: 2rem;
  margin: 1px;
  padding: 0 0.5rem;
  width: calc(100% - 2px);
  background-color: transparent;
  border: 1px solid transparent;
  align-items: center;
  cursor: pointer;
  border-radius: var(--border-radius-md);
}
@media screen and (min-width: 1921px) {
  .excalidraw .dropdown-menu .dropdown-menu-item {
    height: 2.25rem;
  }
}
.excalidraw .dropdown-menu .dropdown-menu-item__text {
  display: flex;
  align-items: center;
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  gap: 0.75rem;
}
.excalidraw .dropdown-menu .dropdown-menu-item__shortcut {
  margin-inline-start: auto;
  opacity: 0.5;
}
.excalidraw .dropdown-menu .dropdown-menu-item__shortcut--orphaned {
  text-align: right;
  font-size: 0.875rem;
  padding: 0 0.625rem;
}
.excalidraw .dropdown-menu .dropdown-menu-item--selected {
  background: var(--color-primary-light);
  --icon-fill-color: var(--color-primary-darker);
}
.excalidraw .dropdown-menu .dropdown-menu-item:hover {
  background-color: var(--button-hover-bg);
  text-decoration: none;
}
.excalidraw .dropdown-menu .dropdown-menu-item:active {
  background-color: var(--button-hover-bg);
  border-color: var(--color-brand-active);
}
.excalidraw .dropdown-menu .dropdown-menu-item svg {
  width: 1rem;
  height: 1rem;
  display: block;
}
.excalidraw .dropdown-menu .dropdown-menu-item-bare {
  align-items: center;
  height: 2rem;
  justify-content: space-between;
}
@media screen and (min-width: 1921px) {
  .excalidraw .dropdown-menu .dropdown-menu-item-bare {
    height: 2.25rem;
  }
}
.excalidraw .dropdown-menu .dropdown-menu-item-bare svg {
  width: 1rem;
  height: 1rem;
  display: block;
}
.excalidraw .dropdown-menu .dropdown-menu-item-custom {
  margin-top: 0.5rem;
}
.excalidraw .dropdown-menu .dropdown-menu-group-title {
  font-size: 14px;
  text-align: left;
  margin: 10px 0;
  font-weight: 500;
}
.excalidraw .dropdown-menu-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.625rem;
  width: var(--button-width, var(--default-button-size));
  height: var(--button-height, var(--default-button-size));
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: var(--button-border, var(--default-border-color));
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  background-color: var(--button-bg, var(--island-bg-color));
  color: var(--button-color, var(--color-on-surface));
  font-family: var(--ui-font);
  width: var(--lg-button-size);
  height: var(--lg-button-size);
  --background: var(--color-surface-mid);
  background-color: var(--background);
}
.excalidraw .dropdown-menu-button svg {
  width: var(--button-width, var(--lg-icon-size));
  height: var(--button-height, var(--lg-icon-size));
}
.excalidraw .dropdown-menu-button:hover {
  background-color: var(--button-hover-bg, var(--island-bg-color));
  border-color: var(--button-hover-border, var(--button-border, var(--default-border-color)));
  color: var(--button-hover-color, var(--button-color, var(--text-primary-color, inherit)));
}
.excalidraw .dropdown-menu-button:active {
  background-color: var(--button-active-bg, var(--island-bg-color));
  border-color: var(--button-active-border, var(--color-primary-darkest));
}
.excalidraw .dropdown-menu-button.active {
  background-color: var(--button-selected-bg, var(--color-surface-primary-container));
  border-color: var(--button-selected-border, var(--color-surface-primary-container));
}
.excalidraw .dropdown-menu-button.active:hover {
  background-color: var(--button-selected-hover-bg, var(--color-surface-primary-container));
}
.excalidraw .dropdown-menu-button.active svg {
  color: var(--button-color, var(--color-on-primary-container));
}
.excalidraw.theme--dark.excalidraw .dropdown-menu-button {
  --background: var(--color-surface-high);
}
.excalidraw.theme--dark.excalidraw .dropdown-menu-button:hover {
  --background: #363541;
}
.excalidraw .dropdown-menu-button:hover {
  --background: var(--color-surface-high);
  background-color: var(--background);
  text-decoration: none;
}
.excalidraw .dropdown-menu-button:active {
  border-color: var(--color-primary);
}
.excalidraw .dropdown-menu-button svg {
  width: var(--lg-icon-size);
  height: var(--lg-icon-size);
}
.excalidraw .dropdown-menu-button--mobile {
  border: none;
  margin: 0;
  padding: 0;
  width: var(--default-button-size);
  height: var(--default-button-size);
}

/* components/LibraryUnit.scss */
.excalidraw .library-unit {
  align-items: center;
  border: 1px solid transparent;
  display: flex;
  justify-content: center;
  position: relative;
  width: 55px;
  height: 55px;
  box-sizing: border-box;
  border-radius: var(--border-radius-lg);
}
.excalidraw .library-unit svg {
  pointer-events: none;
}
.excalidraw .library-unit--hover {
  border-color: var(--color-primary);
}
.excalidraw .library-unit--selected {
  border-color: var(--color-primary);
  border-width: 1px;
}
.excalidraw .library-unit--skeleton {
  opacity: 0.5;
  background:
    linear-gradient(
      -45deg,
      var(--color-gray-10),
      var(--color-gray-20),
      var(--color-gray-10));
  background-size: 200% 200%;
  animation: library-unit__skeleton-opacity-animation 0.2s linear;
}
.excalidraw.theme--dark .library-unit--skeleton {
  background-image:
    linear-gradient(
      -45deg,
      var(--color-gray-100),
      var(--color-gray-80),
      var(--color-gray-100));
}
.excalidraw .library-unit__dragger {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}
.excalidraw .library-unit__dragger > svg {
  filter: var(--theme-filter);
  flex-grow: 1;
  max-height: 100%;
  max-width: 100%;
}
.excalidraw .library-unit__checkbox-container,
.excalidraw .library-unit__checkbox-container:hover,
.excalidraw .library-unit__checkbox-container:active {
  align-items: center;
  background: none;
  border: none;
  color: var(--icon-fill-color);
  display: flex;
  justify-content: center;
  margin: 0;
  padding: 0.5rem;
  position: absolute;
  left: 2rem;
  bottom: 2rem;
  cursor: pointer;
}
.excalidraw .library-unit__checkbox-container input,
.excalidraw .library-unit__checkbox-container:hover input,
.excalidraw .library-unit__checkbox-container:active input {
  cursor: pointer;
}
.excalidraw .library-unit__checkbox {
  position: absolute;
  top: 0.125rem;
  right: 0.125rem;
  margin: 0;
}
.excalidraw .library-unit__checkbox .Checkbox-box {
  margin: 0;
  width: 1rem;
  height: 1rem;
  border-radius: 4px;
  background-color: var(--color-primary-light);
  border: 1px solid var(--color-primary);
  box-shadow: none !important;
  padding: 2px;
}
.excalidraw .library-unit__checkbox.Checkbox:hover .Checkbox-box {
  background-color: var(--color-primary-light);
}
.excalidraw .library-unit__checkbox.is-checked .Checkbox-box {
  background-color: var(--color-primary) !important;
}
.excalidraw .library-unit__checkbox.is-checked .Checkbox-box svg {
  color: var(--color-primary-light);
}
.excalidraw .library-unit__removeFromLibrary > svg {
  height: 16px;
  width: 16px;
}
.excalidraw .library-unit__adder {
  transform: scale(1);
  animation: library-unit__adder-animation 1s ease-in infinite;
  position: absolute;
  width: 1.5rem;
  height: 1.5rem;
  background-color: var(--color-primary);
  border-radius: var(--border-radius-md);
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
}
.excalidraw .library-unit__adder svg {
  color: var(--color-primary-light);
  width: 1rem;
  height: 1rem;
}
.excalidraw .library-unit:active .library-unit__adder {
  animation: none;
  transform: scale(0.8);
}
.excalidraw .library-unit__active {
  cursor: pointer;
}
@keyframes library-unit__adder-animation {
  0% {
    transform: scale(0.85);
  }
  50% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.85);
  }
}
@keyframes library-unit__skeleton-opacity-animation {
  0% {
    opacity: 0;
  }
  75% {
    opacity: 0;
  }
  100% {
    opacity: 0.5;
  }
}

/* components/LibraryMenuItems.scss */
.excalidraw {
  --container-padding-y: 1.5rem;
  --container-padding-x: 0.75rem;
}
.excalidraw .library-menu-items__no-items {
  text-align: center;
  color: var(--color-gray-70);
  line-height: 1.5;
  font-size: 0.875rem;
  width: 100%;
}
.excalidraw .library-menu-items__no-items__label {
  color: var(--color-primary);
  font-weight: 700;
  font-size: 1.125rem;
  margin-bottom: 0.75rem;
}
.excalidraw.theme--dark .library-menu-items__no-items {
  color: var(--color-gray-40);
}
.excalidraw .library-menu-items-container {
  width: 100%;
  display: flex;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0;
  overflow-y: auto;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  margin: 0;
  position: relative;
}
.excalidraw .library-menu-items-container > div {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.excalidraw .library-menu-items-container__row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}
.excalidraw .library-menu-items-container__items {
  row-gap: 0.5rem;
  padding: var(--container-padding-y) 0;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  margin-bottom: 1rem;
}
.excalidraw .library-menu-items-container__header {
  color: var(--color-primary);
  font-size: 1.125rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  width: 100%;
  padding-right: 4rem;
  box-sizing: border-box;
}
.excalidraw .library-menu-items-container__header--excal {
  margin-top: 2rem;
}
.excalidraw .library-menu-items-container__grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-gap: 1rem;
}
.excalidraw .library-menu-items-container .separator {
  width: 100%;
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 0.9rem;
  margin: 0.6em 0.2em;
  color: var(--text-primary-color);
}
.excalidraw .library-menu-items-private-library-container {
  min-height: 3.75rem;
  width: 100%;
}

/* components/LibraryMenu.scss */
.excalidraw .layer-ui__library {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
}
.excalidraw .library-actions-counter {
  background-color: var(--color-primary);
  color: var(--color-primary-light);
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 1rem;
  height: 1rem;
  position: absolute;
  bottom: -0.25rem;
  right: -0.25rem;
  font-size: 0.625rem;
  pointer-events: none;
}
.excalidraw .layer-ui__library-message {
  padding: 2rem;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-grow: 1;
  justify-content: center;
}
.excalidraw .layer-ui__library-message span {
  font-size: 0.8em;
}
.excalidraw .publish-library-success .Dialog__content {
  display: flex;
  flex-direction: column;
}
.excalidraw .publish-library-success-close.ToolIcon_type_button {
  background-color: #228be6;
  align-self: flex-end;
}
.excalidraw .publish-library-success-close.ToolIcon_type_button:hover {
  background-color: #1971c2;
}
.excalidraw .publish-library-success-close.ToolIcon_type_button .ToolIcon__icon {
  width: auto;
  font-size: 1rem;
  color: #ffffff;
  padding: 0 0.5rem;
}
.excalidraw .library-menu-control-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
  position: relative;
}
.excalidraw .library-menu-control-buttons--at-bottom::before {
  content: "";
  width: calc(100% - 1.5rem);
  height: 1px;
  position: absolute;
  top: -1px;
  background: var(--sidebar-border-color);
}
.excalidraw .library-menu-browse-button {
  flex: 1;
  height: var(--lg-button-size);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  border-radius: var(--border-radius-lg);
  background-color: var(--color-primary);
  color: #ffffff;
  text-align: center;
  white-space: nowrap;
  text-decoration: none !important;
  font-weight: 600;
  font-size: 0.75rem;
}
.excalidraw .library-menu-browse-button:hover {
  background-color: var(--color-brand-hover);
}
.excalidraw .library-menu-browse-button:active {
  background-color: var(--color-brand-active);
}
.excalidraw.theme--dark .library-menu-browse-button {
  color: var(--color-gray-100);
}
.excalidraw.excalidraw--mobile .library-menu-browse-button {
  height: var(--default-button-size);
}
.excalidraw .layer-ui__library .dropdown-menu {
  width: auto;
  top: initial;
  right: 0;
  left: initial;
  bottom: 100%;
  margin-bottom: 0.625rem;
}
.excalidraw .layer-ui__library .dropdown-menu .dropdown-menu-container {
  width: 196px;
  box-shadow: var(--library-dropdown-shadow);
  border-radius: var(--border-radius-lg);
  padding: 0.25rem 0.5rem;
}
.excalidraw .layer-ui__library .library-menu-dropdown-container {
  position: relative;
}
.excalidraw .layer-ui__library .library-menu-dropdown-container--in-heading {
  padding: 0;
  position: absolute;
  top: 1rem;
  right: 0.75rem;
  z-index: 1;
}
.excalidraw .layer-ui__library .library-menu-dropdown-container--in-heading .dropdown-menu {
  top: 100%;
}

/* components/TextField.scss */
.excalidraw {
  --ExcTextField--color: var(--color-on-surface);
  --ExcTextField--label-color: var(--color-on-surface);
  --ExcTextField--background: var(--color-surface-low);
  --ExcTextField--readonly--background: var(--color-surface-high);
  --ExcTextField--readonly--color: var(--color-on-surface);
  --ExcTextField--border: var(--color-gray-20);
  --ExcTextField--readonly--border: var(--color-border-outline-variant);
  --ExcTextField--border-hover: var(--color-brand-hover);
  --ExcTextField--border-active: var(--color-brand-active);
  --ExcTextField--placeholder: var(--color-border-outline-variant);
}
.excalidraw .ExcTextField {
  position: relative;
}
.excalidraw .ExcTextField svg {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0.75rem;
  width: 1.25rem;
  height: 1.25rem;
  color: var(--color-gray-40);
  z-index: 1;
}
.excalidraw .ExcTextField--fullWidth {
  width: 100%;
  flex-grow: 1;
}
.excalidraw .ExcTextField__label {
  font-family: "Assistant";
  font-style: normal;
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 150%;
  color: var(--ExcTextField--label-color);
  margin-bottom: 0.25rem;
  user-select: none;
}
.excalidraw .ExcTextField__input {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 3rem;
  background: var(--ExcTextField--background);
  border: 1px solid var(--ExcTextField--border);
  border-radius: 0.5rem;
  padding: 0 0.75rem;
}
.excalidraw .ExcTextField__input:not(.excalidraw .ExcTextField__input--readonly):hover {
  border-color: var(--ExcTextField--border-hover);
}
.excalidraw .ExcTextField__input:not(.excalidraw .ExcTextField__input--readonly):active,
.excalidraw .ExcTextField__input:not(.excalidraw .ExcTextField__input--readonly):focus-within {
  border-color: var(--ExcTextField--border-active);
}
.excalidraw .ExcTextField__input input {
  display: flex;
  align-items: center;
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
  height: 1.5rem;
  color: var(--ExcTextField--color);
  font-family: "Assistant";
  font-style: normal;
  font-weight: 400;
  font-size: 1rem;
  line-height: 150%;
  text-overflow: ellipsis;
  background: transparent;
  width: 100%;
}
.excalidraw .ExcTextField__input input:not(:focus):hover {
  background-color: initial;
}
.excalidraw .ExcTextField__input input:focus {
  outline: initial;
  box-shadow: initial;
}
.excalidraw .ExcTextField__input--readonly {
  background: var(--ExcTextField--readonly--background);
  border-color: var(--ExcTextField--readonly--border);
}
.excalidraw .ExcTextField__input--readonly input {
  color: var(--ExcTextField--readonly--color);
}
.excalidraw .ExcTextField--hasIcon .ExcTextField__input {
  padding-left: 2.5rem;
}

/* components/Button.scss */
.excalidraw {
  --theme-filter: none;
  --button-destructive-bg-color: #ffe3e3;
  --button-destructive-color: #c92a2a;
  --button-gray-1: #e9ecef;
  --button-gray-2: #ced4da;
  --button-gray-3: #adb5bd;
  --button-special-active-bg-color: #ebfbee;
  --dialog-border-color: var(--color-gray-20);
  --dropdown-icon: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="292.4" height="292.4" viewBox="0 0 292 292"><path d="M287 197L159 69c-4-3-8-5-13-5s-9 2-13 5L5 197c-3 4-5 8-5 13s2 9 5 13c4 4 8 5 13 5h256c5 0 9-1 13-5s5-8 5-13-1-9-5-13z"/></svg>');
  --focus-highlight-color: #a5d8ff;
  --icon-fill-color: var(--color-on-surface);
  --icon-green-fill-color: #2b8a3e;
  --default-bg-color: #ffffff;
  --input-bg-color: #ffffff;
  --input-border-color: #ced4da;
  --input-hover-bg-color: #f1f3f5;
  --input-label-color: #495057;
  --island-bg-color: #ffffff;
  --keybinding-color: var(--color-gray-40);
  --link-color: #1c7ed6;
  --overlay-bg-color: rgba(255, 255, 255, 0.88);
  --popup-bg-color: var(--island-bg-color);
  --popup-secondary-bg-color: #f1f3f5;
  --popup-text-color: #000000;
  --popup-text-inverted-color: #ffffff;
  --select-highlight-color: #339af0;
  --shadow-island:
    0px 0px 0.9310142993927002px 0px rgba(0, 0, 0, 0.17),
    0px 0px 3.1270833015441895px 0px rgba(0, 0, 0, 0.08),
    0px 7px 14px 0px rgba(0, 0, 0, 0.05);
  --button-hover-bg: var(--color-surface-high);
  --button-active-bg: var(--color-surface-high);
  --button-active-border: var(--color-brand-active);
  --default-border-color: var(--color-surface-high);
  --default-button-size: 2rem;
  --default-icon-size: 1rem;
  --lg-button-size: 2.25rem;
  --lg-icon-size: 1rem;
  --editor-container-padding: 1rem;
  --scrollbar-thumb: var(--button-gray-2);
  --scrollbar-thumb-hover: var(--button-gray-3);
  --color-slider-track: hsl(240, 100%, 90%);
  --color-slider-thumb: var(--color-gray-80);
  --modal-shadow:
    0px 100px 80px rgba(0, 0, 0, 0.07),
    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),
    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),
    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),
    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),
    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);
  --avatar-border-color: var(--color-gray-20);
  --sidebar-shadow:
    0px 100px 80px rgba(0, 0, 0, 0.07),
    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),
    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),
    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),
    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),
    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);
  --sidebar-border-color: var(--color-surface-high);
  --sidebar-bg-color: var(--island-bg-color);
  --library-dropdown-shadow:
    0px 15px 6px rgba(0, 0, 0, 0.01),
    0px 8px 5px rgba(0, 0, 0, 0.05),
    0px 4px 4px rgba(0, 0, 0, 0.09),
    0px 1px 2px rgba(0, 0, 0, 0.1),
    0px 0px 0px rgba(0, 0, 0, 0.1);
  --space-factor: 0.25rem;
  --text-primary-color: var(--color-on-surface);
  --color-selection: #6965db;
  --color-icon-white: #ffffff;
  --color-primary: #6965db;
  --color-primary-darker: #5b57d1;
  --color-primary-darkest: #4a47b1;
  --color-primary-light: #e3e2fe;
  --color-primary-light-darker: #d7d5ff;
  --color-primary-hover: #5753d0;
  --color-gray-10: #f5f5f5;
  --color-gray-20: #ebebeb;
  --color-gray-30: #d6d6d6;
  --color-gray-40: #b8b8b8;
  --color-gray-50: #999999;
  --color-gray-60: #7a7a7a;
  --color-gray-70: #5c5c5c;
  --color-gray-80: #3d3d3d;
  --color-gray-85: #242424;
  --color-gray-90: #1e1e1e;
  --color-gray-100: #121212;
  --color-disabled: var(--color-gray-40);
  --color-warning: #fceeca;
  --color-warning-dark: #f5c354;
  --color-warning-darker: #f3ab2c;
  --color-warning-darkest: #ec8b14;
  --color-text-warning: var(--text-primary-color);
  --color-danger: #db6965;
  --color-danger-dark: #db6965;
  --color-danger-darker: #d65550;
  --color-danger-darkest: #d1413c;
  --color-danger-text: black;
  --color-danger-background: #fff0f0;
  --color-danger-icon-background: #ffdad6;
  --color-danger-color: #700000;
  --color-danger-icon-color: #700000;
  --color-warning-background: var(--color-warning);
  --color-warning-icon-background: var(--color-warning-dark);
  --color-warning-color: var(--text-primary-color);
  --color-warning-icon-color: var(--text-primary-color);
  --color-muted: var(--color-gray-30);
  --color-muted-darker: var(--color-gray-60);
  --color-muted-darkest: var(--color-gray-100);
  --color-muted-background: var(--color-gray-80);
  --color-muted-background-darker: var(--color-gray-100);
  --color-promo: var(--color-primary);
  --color-success: #cafccc;
  --color-success-darker: #bafabc;
  --color-success-darkest: #a5eba8;
  --color-success-text: #268029;
  --color-success-contrast: #65bb6a;
  --color-success-contrast-hover: #6bcf70;
  --color-success-contrast-active: #6edf74;
  --color-logo-icon: var(--color-primary);
  --color-logo-text: #190064;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --color-surface-high: #f1f0ff;
  --color-surface-mid: #f2f2f7;
  --color-surface-low: #ececf4;
  --color-surface-lowest: #ffffff;
  --color-on-surface: #1b1b1f;
  --color-brand-hover: #5753d0;
  --color-on-primary-container: #030064;
  --color-surface-primary-container: #e0dfff;
  --color-brand-active: #4440bf;
  --color-border-outline: #767680;
  --color-border-outline-variant: #c5c5d0;
  --color-surface-primary-container: #e0dfff;
  --color-badge: #0b6513;
  --background-color-badge: #d3ffd2;
}
@media screen and (min-device-width: 1921px) {
  .excalidraw {
    --lg-button-size: 2.5rem;
    --lg-icon-size: 1.25rem;
    --default-button-size: 2.25rem;
    --default-icon-size: 1.25rem;
  }
}
.excalidraw.theme--dark.theme--dark-background-none {
  background: none;
}
.excalidraw.theme--dark {
  --theme-filter: invert(93%) hue-rotate(180deg);
  --button-destructive-bg-color: #5a0000;
  --button-destructive-color: #ffa8a8;
  --button-gray-1: #363636;
  --button-gray-2: #272727;
  --button-gray-3: #222;
  --button-special-active-bg-color: #204624;
  --dialog-border-color: var(--color-gray-80);
  --dropdown-icon: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="292.4" height="292.4" viewBox="0 0 292 292"><path fill="%23ced4da" d="M287 197L159 69c-4-3-8-5-13-5s-9 2-13 5L5 197c-3 4-5 8-5 13s2 9 5 13c4 4 8 5 13 5h256c5 0 9-1 13-5s5-8 5-13-1-9-5-13z"/></svg>');
  --focus-highlight-color: #228be6;
  --icon-green-fill-color: #69db7c;
  --default-bg-color: #121212;
  --input-bg-color: #121212;
  --input-border-color: #2e2e2e;
  --input-hover-bg-color: #181818;
  --input-label-color: #e9ecef;
  --island-bg-color: #232329;
  --keybinding-color: var(--color-gray-60);
  --link-color: #4dabf7;
  --overlay-bg-color: rgba(52, 58, 64, 0.12);
  --popup-secondary-bg-color: #222;
  --popup-text-color: #ced4da;
  --popup-text-inverted-color: #2c2c2c;
  --select-highlight-color: #4dabf7;
  --shadow-island:
    0px 0px 0.9310142993927002px 0px rgba(0, 0, 0, 0.17),
    0px 0px 3.1270833015441895px 0px rgba(0, 0, 0, 0.08),
    0px 7px 14px 0px rgba(0, 0, 0, 0.05);
  --modal-shadow:
    0px 100px 80px rgba(0, 0, 0, 0.07),
    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),
    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),
    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),
    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),
    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);
  --avatar-border-color: var(--color-gray-85);
  --scrollbar-thumb: #343a40;
  --scrollbar-thumb-hover: #495057;
  --color-slider-track: hsl(244, 23%, 39%);
  --color-selection: #3530c4;
  --color-icon-white: var(--color-gray-90);
  --color-primary: #a8a5ff;
  --color-primary-darker: #b2aeff;
  --color-primary-darkest: #beb9ff;
  --color-primary-light: #4f4d6f;
  --color-primary-light-darker: #43415e;
  --color-primary-hover: #bbb8ff;
  --color-disabled: var(--color-gray-70);
  --color-text-warning: var(--color-gray-80);
  --color-danger: #ffa8a5;
  --color-danger-dark: #672120;
  --color-danger-darker: #8f2625;
  --color-danger-darkest: #ac2b29;
  --color-danger-text: #fbcbcc;
  --color-danger-background: #fbcbcc;
  --color-danger-icon-background: #672120;
  --color-danger-color: #261919;
  --color-danger-icon-color: #fbcbcc;
  --color-warning-background: var(--color-warning);
  --color-warning-icon-background: var(--color-warning-dark);
  --color-warning-color: var(--color-gray-80);
  --color-warning-icon-color: var(--color-gray-80);
  --color-muted: var(--color-gray-80);
  --color-muted-darker: var(--color-gray-60);
  --color-muted-darkest: var(--color-gray-20);
  --color-muted-background: var(--color-gray-40);
  --color-muted-background-darker: var(--color-gray-20);
  --color-logo-text: #e2dfff;
  --color-surface-high: hsl(245, 10%, 21%);
  --color-surface-low: hsl(240, 8%, 15%);
  --color-surface-mid: hsl(240 6% 10%);
  --color-surface-lowest: hsl(0, 0%, 7%);
  --color-on-surface: #e3e3e8;
  --color-brand-hover: #bbb8ff;
  --color-on-primary-container: #e0dfff;
  --color-surface-primary-container: #403e6a;
  --color-brand-active: #d0ccff;
  --color-border-outline: #8e8d9c;
  --color-border-outline-variant: #46464f;
  --color-surface-primary-container: #403e6a;
}
.excalidraw .excalidraw-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.625rem;
  width: var(--button-width, var(--default-button-size));
  height: var(--button-height, var(--default-button-size));
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: var(--button-border, var(--default-border-color));
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  background-color: var(--button-bg, var(--island-bg-color));
  color: var(--button-color, var(--color-on-surface));
  font-family: var(--ui-font);
}
.excalidraw .excalidraw-button svg {
  width: var(--button-width, var(--lg-icon-size));
  height: var(--button-height, var(--lg-icon-size));
}
.excalidraw .excalidraw-button:hover {
  background-color: var(--button-hover-bg, var(--island-bg-color));
  border-color: var(--button-hover-border, var(--button-border, var(--default-border-color)));
  color: var(--button-hover-color, var(--button-color, var(--text-primary-color, inherit)));
}
.excalidraw .excalidraw-button:active {
  background-color: var(--button-active-bg, var(--island-bg-color));
  border-color: var(--button-active-border, var(--color-primary-darkest));
}
.excalidraw .excalidraw-button.active {
  background-color: var(--button-selected-bg, var(--color-surface-primary-container));
  border-color: var(--button-selected-border, var(--color-surface-primary-container));
}
.excalidraw .excalidraw-button.active:hover {
  background-color: var(--button-selected-hover-bg, var(--color-surface-primary-container));
}
.excalidraw .excalidraw-button.active svg {
  color: var(--button-color, var(--color-on-primary-container));
}

/* components/Actions.scss */
.zoom-actions,
.undo-redo-buttons {
  background-color: var(--island-bg-color);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 0 0 1px var(--color-surface-lowest);
}
.zoom-button,
.undo-redo-buttons button {
  border-radius: 0 !important;
  background-color: var(--color-surface-low) !important;
  font-size: 0.875rem !important;
  width: var(--lg-button-size);
  height: var(--lg-button-size);
}
.zoom-button svg,
.undo-redo-buttons button svg {
  width: var(--lg-icon-size) !important;
  height: var(--lg-icon-size) !important;
}
.zoom-button .ToolIcon__icon,
.undo-redo-buttons button .ToolIcon__icon {
  width: 100%;
  height: 100%;
}
.reset-zoom-button {
  border-left: 0 !important;
  border-right: 0 !important;
  padding: 0 0.625rem !important;
  width: 3.75rem !important;
  justify-content: center;
  color: var(--text-primary-color);
}
.zoom-out-button {
  border-top-left-radius: var(--border-radius-lg) !important;
  border-bottom-left-radius: var(--border-radius-lg) !important;
}
:root[dir=rtl] .zoom-out-button {
  transform: scaleX(-1);
}
.zoom-out-button .ToolIcon__icon {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
.zoom-in-button {
  border-top-right-radius: var(--border-radius-lg) !important;
  border-bottom-right-radius: var(--border-radius-lg) !important;
}
:root[dir=rtl] .zoom-in-button {
  transform: scaleX(-1);
}
.zoom-in-button .ToolIcon__icon {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.undo-redo-buttons .undo-button-container button {
  border-top-left-radius: var(--border-radius-lg) !important;
  border-bottom-left-radius: var(--border-radius-lg) !important;
  border-right: 0 !important;
}
:root[dir=rtl] .undo-redo-buttons .undo-button-container button {
  transform: scaleX(-1);
}
.undo-redo-buttons .undo-button-container button .ToolIcon__icon {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
.undo-redo-buttons .redo-button-container button {
  border-top-right-radius: var(--border-radius-lg) !important;
  border-bottom-right-radius: var(--border-radius-lg) !important;
}
:root[dir=rtl] .undo-redo-buttons .redo-button-container button {
  transform: scaleX(-1);
}
.undo-redo-buttons .redo-button-container button .ToolIcon__icon {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

/* components/CommandPalette/CommandPalette.scss */
.excalidraw .command-palette-dialog {
  user-select: none;
}
.excalidraw .command-palette-dialog .Modal__content {
  height: auto;
  max-height: 100%;
}
@media screen and (min-width: 861px) {
  .excalidraw .command-palette-dialog .Modal__content {
    max-height: 750px;
    height: 100%;
  }
}
.excalidraw .command-palette-dialog .Modal__content .Island {
  height: 100%;
  padding: 1.5rem;
}
.excalidraw .command-palette-dialog .Modal__content .Dialog__content {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.excalidraw .command-palette-dialog .shortcuts-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 12px;
  gap: 1.5rem;
}
.excalidraw .command-palette-dialog .shortcut {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 16px;
  font-size: 10px;
  gap: 0.25rem;
}
.excalidraw .command-palette-dialog .shortcut .shortcut-wrapper {
  display: flex;
}
.excalidraw .command-palette-dialog .shortcut .shortcut-plus {
  margin: 0px 4px;
}
.excalidraw .command-palette-dialog .shortcut .shortcut-key {
  padding: 0px 4px;
  height: 16px;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--color-primary-light);
}
.excalidraw .command-palette-dialog .shortcut .shortcut-desc {
  margin-left: 4px;
  color: var(--color-gray-50);
}
.excalidraw .command-palette-dialog .commands {
  overflow-y: auto;
  box-sizing: border-box;
  margin-top: 12px;
  color: var(--popup-text-color);
  user-select: none;
}
.excalidraw .command-palette-dialog .commands .command-category {
  display: flex;
  flex-direction: column;
  padding: 12px 0px;
  margin-right: 0.25rem;
}
.excalidraw .command-palette-dialog .commands .command-category-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
}
.excalidraw .command-palette-dialog .commands .command-item {
  color: var(--popup-text-color);
  height: 2.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 0 0.5rem;
  border-radius: var(--border-radius-lg);
  cursor: pointer;
}
.excalidraw .command-palette-dialog .commands .command-item:active {
  background-color: var(--color-surface-low);
}
.excalidraw .command-palette-dialog .commands .command-item .name {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}
.excalidraw .command-palette-dialog .commands .item-selected {
  background-color: var(--color-surface-mid);
}
.excalidraw .command-palette-dialog .commands .item-disabled {
  opacity: 0.3;
  cursor: not-allowed;
}
.excalidraw .command-palette-dialog .commands .no-match {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 36px;
}
.excalidraw .command-palette-dialog .icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

/* components/Popover.scss */
.excalidraw .popover {
  position: absolute;
  z-index: 10;
  padding: 5px 0 5px;
  outline: none;
}

/* components/ContextMenu.scss */
.excalidraw .context-menu {
  position: relative;
  border-radius: 4px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  padding: 0;
  list-style: none;
  user-select: none;
  margin: -0.25rem 0 0 0.125rem;
  padding: 0.5rem 0;
  background-color: var(--popup-secondary-bg-color);
  border: 1px solid var(--button-gray-3);
  cursor: default;
}
.excalidraw .context-menu button {
  color: var(--popup-text-color);
}
.excalidraw .context-menu-item {
  position: relative;
  width: 100%;
  min-width: 9.5rem;
  margin: 0;
  padding: 0.25rem 1rem 0.25rem 1.25rem;
  text-align: start;
  border-radius: 0;
  background-color: transparent;
  border: none;
  white-space: nowrap;
  font-family: inherit;
  display: grid;
  grid-template-columns: 1fr 0.2fr;
  align-items: center;
}
.excalidraw .context-menu-item.checkmark::before {
  position: absolute;
  left: 6px;
  margin-bottom: 1px;
  content: "\2713";
}
.excalidraw .context-menu-item.dangerous .context-menu-item__label {
  color: #f03e3e;
}
.excalidraw .context-menu-item .context-menu-item__label {
  justify-self: start;
  margin-inline-end: 20px;
}
.excalidraw .context-menu-item .context-menu-item__shortcut {
  justify-self: end;
  opacity: 0.6;
  font-family: inherit;
  font-size: 0.7rem;
}
.excalidraw .context-menu-item:hover {
  color: var(--popup-bg-color);
  background-color: var(--select-highlight-color);
}
.excalidraw .context-menu-item:hover.dangerous {
  background-color: #fa5252;
}
.excalidraw .context-menu-item:hover.dangerous .context-menu-item__label {
  color: var(--popup-bg-color);
}
.excalidraw .context-menu-item:focus {
  z-index: 1;
}
.excalidraw--mobile.excalidraw .context-menu-item {
  display: block;
}
.excalidraw--mobile.excalidraw .context-menu-item .context-menu-item__label {
  margin-inline-end: 0;
}
.excalidraw--mobile.excalidraw .context-menu-item .context-menu-item__shortcut {
  display: none;
}
.excalidraw .context-menu-item-separator {
  border: none;
  border-top: 1px solid #adb5bd;
}

/* components/RadioGroup.scss */
.excalidraw {
  --RadioGroup-background: var(--island-bg-color);
  --RadioGroup-border: var(--color-surface-high);
  --RadioGroup-choice-color-off: var(--color-primary);
  --RadioGroup-choice-color-off-hover: var(--color-brand-hover);
  --RadioGroup-choice-background-off: var(--island-bg-color);
  --RadioGroup-choice-background-off-active: var(--color-surface-high);
  --RadioGroup-choice-color-on: var(--color-surface-lowest);
  --RadioGroup-choice-background-on: var(--color-primary);
  --RadioGroup-choice-background-on-hover: var(--color-brand-hover);
  --RadioGroup-choice-background-on-active: var(--color-brand-active);
}
.excalidraw .RadioGroup {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 3px;
  border-radius: 10px;
  background: var(--RadioGroup-background);
  border: 1px solid var(--RadioGroup-border);
}
.excalidraw .RadioGroup__choice {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 24px;
  color: var(--RadioGroup-choice-color-off);
  background: var(--RadioGroup-choice-background-off);
  border-radius: 8px;
  font-family: "Assistant";
  font-style: normal;
  font-weight: 600;
  font-size: 0.75rem;
  line-height: 100%;
  user-select: none;
  letter-spacing: 0.4px;
  transition: all 75ms ease-out;
}
.excalidraw .RadioGroup__choice:hover {
  color: var(--RadioGroup-choice-color-off-hover);
}
.excalidraw .RadioGroup__choice:active {
  background: var(--RadioGroup-choice-background-off-active);
}
.excalidraw .RadioGroup__choice.active {
  color: var(--RadioGroup-choice-color-on);
  background: var(--RadioGroup-choice-background-on);
}
.excalidraw .RadioGroup__choice.active:hover {
  background: var(--RadioGroup-choice-background-on-hover);
}
.excalidraw .RadioGroup__choice.active:active {
  background: var(--RadioGroup-choice-background-on-active);
}
.excalidraw .RadioGroup__choice input {
  z-index: 1;
  position: absolute;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  border-radius: 8px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
}

/* components/Switch.scss */
.excalidraw {
  --Switch-disabled-color: var(--color-border-outline);
  --Switch-disabled-toggled-background: var(--color-border-outline-variant);
  --Switch-disabled-border: var(--color-border-outline-variant);
  --Switch-track-background: var(--island-bg-color);
  --Switch-thumb-background: var(--color-on-surface);
  --Switch-hover-background: var(--color-brand-hover);
  --Switch-active-background: var(--color-brand-active);
}
.excalidraw .Switch {
  position: relative;
  box-sizing: border-box;
  width: 40px;
  height: 20px;
  border-radius: 12px;
  transition-property: background, border;
  transition-duration: 150ms;
  transition-timing-function: ease-out;
  background: var(--Switch-track-background);
  border: 1px solid var(--Switch-disabled-color);
}
.excalidraw .Switch:hover {
  background: var(--Switch-track-background);
  border: 1px solid var(--Switch-hover-background);
}
.excalidraw .Switch:active {
  border: 1px solid var(--Switch-active-background);
}
.excalidraw .Switch.toggled {
  background: var(--color-primary);
  border: 1px solid var(--color-primary);
}
.excalidraw .Switch.toggled:hover {
  background: var(--color-primary-darker);
  border: 1px solid var(--color-primary-darker);
}
.excalidraw .Switch.disabled {
  background: var(--Switch-track-background);
  border: 1px solid var(--Switch-disabled-border);
}
.excalidraw .Switch.disabled.toggled {
  background: var(--Switch-disabled-toggled-background);
  border: 1px solid var(--Switch-disabled-toggled-background);
}
.excalidraw .Switch:before {
  content: "";
  box-sizing: border-box;
  display: block;
  pointer-events: none;
  position: absolute;
  border-radius: 100%;
  transition: all 150ms ease-out;
  width: 10px;
  height: 10px;
  top: 4px;
  left: 4px;
  background: var(--Switch-thumb-background);
}
.excalidraw .Switch:active:before {
  width: 12px;
}
.excalidraw .Switch.toggled:before {
  width: 14px;
  height: 14px;
  left: 22px;
  top: 2px;
  background: var(--Switch-track-background);
}
.excalidraw .Switch.toggled:active:before {
  width: 16px;
  left: 20px;
}
.excalidraw .Switch.disabled:before {
  background: var(--Switch-disabled-color);
}
.excalidraw .Switch.disabled.toggled:before {
  background: var(--Switch-disabled-color);
}
.excalidraw .Switch input {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 12px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
}
.excalidraw .Switch input:disabled {
  cursor: unset;
}

/* components/ImageExportDialog.scss */
.excalidraw {
  --ImageExportModal-preview-border: #d6d6d6;
}
.excalidraw.theme--dark {
  --ImageExportModal-preview-border: #5c5c5c;
}
.excalidraw .ImageExportModal {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  user-select: none;
}
.excalidraw .ImageExportModal h3 {
  font-family: "Assistant";
  font-style: normal;
  font-weight: 700;
  font-size: 1.313rem;
  line-height: 130%;
  padding: 0;
  margin: 0;
}
.excalidraw--mobile.excalidraw .ImageExportModal h3 {
  display: none;
}
.excalidraw .ImageExportModal > h3 {
  display: none;
}
.excalidraw--mobile.excalidraw .ImageExportModal > h3 {
  display: block;
}
.excalidraw--mobile.excalidraw .ImageExportModal {
  flex-direction: column;
  height: calc(100vh - 5rem);
}
.excalidraw .ImageExportModal__preview {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 360px;
  width: 55%;
  margin-right: 1.5rem;
}
.excalidraw--mobile.excalidraw .ImageExportModal__preview {
  max-width: unset;
  margin-right: unset;
  width: 100%;
  height: unset;
  flex-grow: 1;
}
.excalidraw .ImageExportModal__preview__filename > input {
  margin-top: 1rem;
}
.excalidraw .ImageExportModal__preview__canvas {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  flex-grow: 1;
  justify-content: center;
  align-items: center;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==) left center;
  border: 1px solid var(--ImageExportModal-preview-border);
  border-radius: 12px;
  overflow: hidden;
  padding: 1rem;
}
.excalidraw .ImageExportModal__preview__canvas > canvas {
  max-width: calc(100% - 2rem);
  max-height: calc(100% - 2rem);
  filter: none !important;
}
.excalidraw--mobile.excalidraw .ImageExportModal__preview__canvas > canvas {
  max-height: 100%;
}
.excalidraw--mobile.excalidraw .ImageExportModal__preview__canvas {
  margin-top: 24px;
  max-width: unset;
}
.excalidraw .ImageExportModal__settings {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 18px;
}
.excalidraw--mobile.excalidraw .ImageExportModal__settings {
  margin-left: unset;
  margin-top: 1rem;
  flex-direction: row;
  gap: 6px 34px;
  align-content: flex-start;
}
.excalidraw .ImageExportModal__settings__setting {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.excalidraw--mobile.excalidraw .ImageExportModal__settings__setting {
  flex-direction: column;
  align-items: start;
  justify-content: unset;
  height: 52px;
}
.excalidraw .ImageExportModal__settings__setting__label {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-family: "Assistant";
  font-weight: 600;
  font-size: 1rem;
  line-height: 150%;
}
.excalidraw .ImageExportModal__settings__setting__label svg {
  width: 20px;
  height: 20px;
  margin-left: 10px;
}
.excalidraw .ImageExportModal__settings__setting__content {
  display: flex;
  height: 100%;
  align-items: center;
}
.excalidraw .ImageExportModal__settings__buttons {
  flex-grow: 1;
  flex-wrap: wrap;
  display: flex;
  flex-direction: row;
  gap: 11px;
  align-items: flex-end;
  align-content: flex-end;
}
.excalidraw--mobile.excalidraw .ImageExportModal__settings__buttons {
  padding-top: 32px;
  flex-basis: 100%;
  justify-content: center;
}

/* components/FilledButton.scss */
@keyframes successStatusAnimation {
  0% {
    transform: scale(0.35);
  }
  50% {
    transform: scale(1.25);
  }
  100% {
    transform: scale(1);
  }
}
.excalidraw .ExcButton {
  --text-color: transparent;
  --border-color: transparent;
  --back-color: transparent;
  color: var(--text-color);
  background-color: var(--back-color);
  border-color: var(--border-color);
  border-radius: 0.5rem;
  border-width: 1px;
  border-style: solid;
  font-family: var(--font-family);
  user-select: none;
}
.excalidraw .ExcButton:hover {
  transition: all 150ms ease-out;
}
.excalidraw .ExcButton .Spinner {
  --spinner-color: var(--color-surface-lowest);
}
.excalidraw .ExcButton .ExcButton__statusIcon {
  visibility: visible;
  position: absolute;
  width: 1.2rem;
  height: 1.2rem;
  animation: successStatusAnimation 0.5s cubic-bezier(0.3, 1, 0.6, 1);
}
.excalidraw .ExcButton.ExcButton--status-loading,
.excalidraw .ExcButton.ExcButton--status-success {
  pointer-events: none;
}
.excalidraw .ExcButton.ExcButton--status-loading .ExcButton__contents,
.excalidraw .ExcButton.ExcButton--status-success .ExcButton__contents {
  visibility: hidden;
}
.excalidraw .ExcButton[disabled] {
  pointer-events: none;
}
.excalidraw .ExcButton,
.excalidraw .ExcButton__contents {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  flex-wrap: nowrap;
  position: relative;
}
.excalidraw .ExcButton--color-primary.ExcButton--variant-filled {
  --text-color: var(--color-surface-lowest);
  --back-color: var(--color-primary);
}
.excalidraw .ExcButton--color-primary.ExcButton--variant-filled .Spinner {
  --spinner-color: var(--text-color);
}
.excalidraw .ExcButton--color-primary.ExcButton--variant-filled:hover {
  --back-color: var(--color-brand-hover);
}
.excalidraw .ExcButton--color-primary.ExcButton--variant-filled:active {
  --back-color: var(--color-brand-active);
}
.excalidraw .ExcButton--color-primary.ExcButton--variant-outlined,
.excalidraw .ExcButton--color-primary.ExcButton--variant-icon {
  --text-color: var(--color-primary);
  --border-color: var(--color-primary);
  --back-color: transparent;
}
.excalidraw .ExcButton--color-primary.ExcButton--variant-outlined .Spinner,
.excalidraw .ExcButton--color-primary.ExcButton--variant-icon .Spinner {
  --spinner-color: var(--text-color);
}
.excalidraw .ExcButton--color-primary.ExcButton--variant-outlined:hover,
.excalidraw .ExcButton--color-primary.ExcButton--variant-icon:hover {
  --text-color: var(--color-brand-hover);
  --border-color: var(--color-brand-hover);
}
.excalidraw .ExcButton--color-primary.ExcButton--variant-outlined:active,
.excalidraw .ExcButton--color-primary.ExcButton--variant-icon:active {
  --text-color: var(--color-brand-active);
  --border-color: var(--color-brand-active);
}
.excalidraw .ExcButton--color-danger.ExcButton--variant-filled {
  --text-color: var(--color-danger-text);
  --back-color: var(--color-danger-dark);
}
.excalidraw .ExcButton--color-danger.ExcButton--variant-filled .Spinner {
  --spinner-color: var(--text-color);
}
.excalidraw .ExcButton--color-danger.ExcButton--variant-filled:hover {
  --back-color: var(--color-danger-darker);
}
.excalidraw .ExcButton--color-danger.ExcButton--variant-filled:active {
  --back-color: var(--color-danger-darkest);
}
.excalidraw .ExcButton--color-danger.ExcButton--variant-outlined,
.excalidraw .ExcButton--color-danger.ExcButton--variant-icon {
  --text-color: var(--color-danger);
  --border-color: var(--color-danger);
  --back-color: transparent;
}
.excalidraw .ExcButton--color-danger.ExcButton--variant-outlined .Spinner,
.excalidraw .ExcButton--color-danger.ExcButton--variant-icon .Spinner {
  --spinner-color: var(--text-color);
}
.excalidraw .ExcButton--color-danger.ExcButton--variant-outlined:hover,
.excalidraw .ExcButton--color-danger.ExcButton--variant-icon:hover {
  --text-color: var(--color-danger-darkest);
  --border-color: var(--color-danger-darkest);
}
.excalidraw .ExcButton--color-danger.ExcButton--variant-outlined:active,
.excalidraw .ExcButton--color-danger.ExcButton--variant-icon:active {
  --text-color: var(--color-danger-darker);
  --border-color: var(--color-danger-darker);
}
.excalidraw .ExcButton--color-success.ExcButton--variant-filled {
  --text-color: var(--color-success-text);
  --back-color: var(--color-success);
}
.excalidraw .ExcButton--color-success.ExcButton--variant-filled .Spinner {
  --spinner-color: var(--color-success);
}
.excalidraw .ExcButton--color-success.ExcButton--variant-filled:hover {
  --back-color: var(--color-success-darker);
}
.excalidraw .ExcButton--color-success.ExcButton--variant-filled:active {
  --back-color: var(--color-success-darkest);
}
.excalidraw .ExcButton--color-success.ExcButton--variant-outlined,
.excalidraw .ExcButton--color-success.ExcButton--variant-icon {
  --text-color: var(--color-success-contrast);
  --border-color: var(--color-success-contrast);
  --back-color: transparent;
}
.excalidraw .ExcButton--color-success.ExcButton--variant-outlined .Spinner,
.excalidraw .ExcButton--color-success.ExcButton--variant-icon .Spinner {
  --spinner-color: var(--color-success-contrast);
}
.excalidraw .ExcButton--color-success.ExcButton--variant-outlined:hover,
.excalidraw .ExcButton--color-success.ExcButton--variant-icon:hover {
  --text-color: var(--color-success-contrast-hover);
  --border-color: var(--color-success-contrast-hover);
}
.excalidraw .ExcButton--color-success.ExcButton--variant-outlined:active,
.excalidraw .ExcButton--color-success.ExcButton--variant-icon:active {
  --text-color: var(--color-success-contrast-active);
  --border-color: var(--color-success-contrast-active);
}
.excalidraw .ExcButton--color-muted.ExcButton--variant-filled {
  --text-color: var(--island-bg-color);
  --back-color: var(--color-gray-50);
}
.excalidraw .ExcButton--color-muted.ExcButton--variant-filled .Spinner {
  --spinner-color: var(--text-color);
}
.excalidraw .ExcButton--color-muted.ExcButton--variant-filled:hover {
  --back-color: var(--color-gray-60);
}
.excalidraw .ExcButton--color-muted.ExcButton--variant-filled:active {
  --back-color: var(--color-gray-80);
}
.excalidraw .ExcButton--color-muted.ExcButton--variant-outlined,
.excalidraw .ExcButton--color-muted.ExcButton--variant-icon {
  --text-color: var(--color-muted-background);
  --border-color: var(--color-muted);
  --back-color: var(--island-bg-color);
}
.excalidraw .ExcButton--color-muted.ExcButton--variant-outlined .Spinner,
.excalidraw .ExcButton--color-muted.ExcButton--variant-icon .Spinner {
  --spinner-color: var(--text-color);
}
.excalidraw .ExcButton--color-muted.ExcButton--variant-outlined:hover,
.excalidraw .ExcButton--color-muted.ExcButton--variant-icon:hover {
  --text-color: var(--color-muted-background-darker);
  --border-color: var(--color-muted-darker);
}
.excalidraw .ExcButton--color-muted.ExcButton--variant-outlined:active,
.excalidraw .ExcButton--color-muted.ExcButton--variant-icon:active {
  --text-color: var(--color-muted-background-darker);
  --border-color: var(--color-muted-darkest);
}
.excalidraw .ExcButton--color-warning.ExcButton--variant-filled {
  --text-color: black;
  --back-color: var(--color-warning-dark);
}
.excalidraw .ExcButton--color-warning.ExcButton--variant-filled .Spinner {
  --spinner-color: var(--text-color);
}
.excalidraw .ExcButton--color-warning.ExcButton--variant-filled:hover {
  --back-color: var(--color-warning-darker);
}
.excalidraw .ExcButton--color-warning.ExcButton--variant-filled:active {
  --back-color: var(--color-warning-darkest);
}
.excalidraw .ExcButton--color-warning.ExcButton--variant-outlined,
.excalidraw .ExcButton--color-warning.ExcButton--variant-icon {
  --text-color: var(--color-warning-dark);
  --border-color: var(--color-warning-dark);
  --back-color: var(--input-bg-color);
}
.excalidraw .ExcButton--color-warning.ExcButton--variant-outlined .Spinner,
.excalidraw .ExcButton--color-warning.ExcButton--variant-icon .Spinner {
  --spinner-color: var(--text-color);
}
.excalidraw .ExcButton--color-warning.ExcButton--variant-outlined:hover,
.excalidraw .ExcButton--color-warning.ExcButton--variant-icon:hover {
  --text-color: var(--color-warning-darker);
  --border-color: var(--color-warning-darker);
}
.excalidraw .ExcButton--color-warning.ExcButton--variant-outlined:active,
.excalidraw .ExcButton--color-warning.ExcButton--variant-icon:active {
  --text-color: var(--color-warning-darkest);
  --border-color: var(--color-warning-darkest);
}
.excalidraw .ExcButton--size-large {
  font-weight: 600;
  font-size: 0.875rem;
  min-height: 3rem;
  padding: 0.5rem 1.5rem;
  letter-spacing: 0.4px;
}
.excalidraw .ExcButton--size-large .ExcButton__contents {
  gap: 0.75rem;
}
.excalidraw .ExcButton--size-medium {
  font-weight: 600;
  font-size: 0.75rem;
  min-height: 2.5rem;
  padding: 0.5rem 1rem;
  letter-spacing: normal;
}
.excalidraw .ExcButton--size-medium .ExcButton__contents {
  gap: 0.5rem;
}
.excalidraw .ExcButton--variant-icon {
  padding: 0.5rem 0.75rem;
  width: 3rem;
}
.excalidraw .ExcButton--fullWidth {
  width: 100%;
}
.excalidraw .ExcButton__icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* components/FixedSideContainer.scss */
.excalidraw .FixedSideContainer {
  position: absolute;
  pointer-events: none;
}
.excalidraw .FixedSideContainer > * {
  pointer-events: var(--ui-pointerEvents);
}
.excalidraw .FixedSideContainer_side_top {
  left: var(--editor-container-padding);
  top: var(--editor-container-padding);
  right: var(--editor-container-padding);
  bottom: var(--editor-container-padding);
}
.excalidraw .FixedSideContainer_side_top.zen-mode {
  right: 42px;
}

/* components/HintViewer.scss */
.excalidraw .HintViewer {
  pointer-events: none;
  box-sizing: border-box;
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: center;
  left: 0;
  top: 100%;
  max-width: 100%;
  width: 100%;
  margin-top: 0.5rem;
  text-align: center;
  color: var(--color-gray-40);
  font-size: 0.75rem;
}
.excalidraw--mobile.excalidraw .HintViewer {
  position: static;
  padding-right: 2rem;
}
.excalidraw .HintViewer > span {
  padding: 0.25rem;
}
.excalidraw.theme--dark .HintViewer {
  color: var(--color-gray-60);
}

/* components/PasteChartDialog.scss */
.excalidraw--mobile.excalidraw .PasteChartDialog .Island {
  display: flex;
  flex-direction: column;
}
.excalidraw .PasteChartDialog .container {
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-wrap: wrap;
}
.excalidraw--mobile.excalidraw .PasteChartDialog .container {
  flex-direction: column;
  justify-content: center;
}
.excalidraw .PasteChartDialog .ChartPreview {
  margin: 8px;
  text-align: center;
  width: 192px;
  height: 128px;
  border-radius: 2px;
  padding: 1px;
  border: 1px solid #ced4da;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
}
.excalidraw .PasteChartDialog .ChartPreview div {
  display: inline-block;
}
.excalidraw .PasteChartDialog .ChartPreview svg {
  max-height: 120px;
  max-width: 186px;
}
.excalidraw .PasteChartDialog .ChartPreview:hover {
  padding: 0;
  border: 2px solid #339af0;
}

/* components/HelpDialog.scss */
.excalidraw .HelpDialog .Modal__content {
  max-width: 960px;
}
.excalidraw .HelpDialog h3 {
  margin: 1.5rem 0;
  font-weight: 700;
  font-size: 1.125rem;
}
.excalidraw .HelpDialog__header {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}
.excalidraw .HelpDialog__btn {
  --background: var(--color-surface-mid);
  display: flex;
  column-gap: 0.5rem;
  align-items: center;
  background-color: var(--background);
  padding: 0.625rem 1rem;
  border: 1px solid var(--background);
  border-radius: var(--border-radius-lg);
  color: var(--text-primary-color);
  font-weight: 600;
  font-size: 0.75rem;
  letter-spacing: 0.4px;
}
.excalidraw.theme--dark.excalidraw .HelpDialog__btn {
  --background: var(--color-surface-high);
}
.excalidraw.theme--dark.excalidraw .HelpDialog__btn:hover {
  --background: #363541;
}
.excalidraw .HelpDialog__btn:hover {
  --background: var(--color-surface-high);
  text-decoration: none;
}
.excalidraw .HelpDialog__btn:active {
  border-color: var(--color-primary);
}
.excalidraw .HelpDialog__link-icon {
  line-height: 0;
}
.excalidraw .HelpDialog__link-icon svg {
  width: 1rem;
  height: 1rem;
}
.excalidraw .HelpDialog__islands-container {
  display: grid;
  grid-column-gap: 1.5rem;
  grid-row-gap: 2rem;
}
@media screen and (min-width: 1024px) {
  .excalidraw .HelpDialog__islands-container {
    grid-template-columns: 1fr 1fr;
  }
}
@media screen and (min-width: 1024px) {
  .excalidraw .HelpDialog__island--tools {
    grid-area: 1/1/2/2;
  }
  .excalidraw .HelpDialog__island--view {
    grid-area: 2/1/3/2;
  }
  .excalidraw .HelpDialog__island--editor {
    grid-area: 1/2/3/3;
  }
}
.excalidraw .HelpDialog__island h4 {
  font-size: 1rem;
  font-weight: 700;
  margin: 0;
  margin-bottom: 0.625rem;
}
.excalidraw .HelpDialog__island-content {
  border: 1px solid var(--dialog-border-color);
  border-radius: var(--border-radius-lg);
}
.excalidraw .HelpDialog__shortcut {
  border-bottom: 1px solid var(--dialog-border-color);
  padding: 0.375rem 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  column-gap: 0.5rem;
}
.excalidraw .HelpDialog__shortcut:last-child {
  border-bottom: none;
}
.excalidraw .HelpDialog__key-container {
  display: flex;
  align-items: center;
  column-gap: 0.25rem;
  flex-shrink: 0;
}
.excalidraw .HelpDialog__key {
  display: flex;
  box-sizing: border-box;
  font-size: 0.625rem;
  background-color: var(--color-primary-light);
  border-radius: var(--border-radius-md);
  padding: 0.5rem;
  word-break: keep-all;
  align-items: center;
  font-family: inherit;
  line-height: 1;
}

/* components/UserList.scss */
.excalidraw {
  --avatar-size: 1.75rem;
  --avatarList-gap: 0.625rem;
  --userList-padding: var(--space-factor);
  --userlist-hint-bg-color: var(--color-gray-10);
  --userlist-hint-heading-color: var(--color-gray-80);
  --userlist-hint-text-color: var(--color-gray-60);
  --userlist-collaborators-border-color: var(--color-gray-20);
}
.excalidraw .UserList__wrapper {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  align-items: center;
  pointer-events: none !important;
}
.excalidraw .UserList {
  pointer-events: none;
  padding: var(--userList-padding);
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;
  gap: var(--avatarList-gap);
  box-sizing: border-box;
  --max-size: calc( var(--avatar-size) * var(--max-avatars, 2) + var(--avatarList-gap) * (var(--max-avatars, 2) - 1) + var(--userList-padding) * 2 );
  max-height: var(--max-size);
  max-width: var(--max-size);
}
.excalidraw .UserList > * {
  pointer-events: var(--ui-pointerEvents);
}
.excalidraw .UserList_mobile {
  padding: 0;
  justify-content: normal;
  margin: 0.5rem 0;
  max-width: none;
  max-height: none;
}
.excalidraw .UserList__more {
  width: var(--avatar-size, 1.5rem);
  height: var(--avatar-size, 1.5rem);
  position: relative;
  border-radius: 100%;
  outline-offset: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 700;
  line-height: 1;
  color: var(--color-gray-90);
  flex: 0 0 auto;
  background-color: var(--color-gray-20);
  border: 0 !important;
  font-size: 0.625rem;
  font-weight: 400;
  flex-shrink: 0;
  color: var(--color-gray-100);
  font-weight: bold;
}
.excalidraw .UserList__more:active {
  transform: scale(0.94);
}
.excalidraw .UserList__more-img {
  width: 100%;
  height: 100%;
  border-radius: 100%;
}
.excalidraw .UserList__more::before {
  content: "";
  position: absolute;
  top: -3px;
  right: -3px;
  bottom: -3px;
  left: -3px;
  border-radius: 100%;
}
.excalidraw .UserList__more.is-followed::before {
  border-color: var(--color-primary-hover);
  box-shadow: 0 0 0 1px var(--color-primary-hover);
}
.excalidraw .UserList__more.is-current-user {
  cursor: auto;
}
.excalidraw .UserList__collaborator-name {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.excalidraw .UserList__collaborator--avatar-only {
  position: relative;
  display: flex;
  flex: 0 0 auto;
}
.excalidraw .UserList__collaborator--avatar-only .UserList__collaborator-status-icon {
  --size: 14px;
  position: absolute;
  display: flex;
  flex: 0 0 auto;
  bottom: -0.25rem;
  right: -0.25rem;
  width: var(--size);
  height: var(--size);
}
.excalidraw .UserList__collaborator--avatar-only .UserList__collaborator-status-icon svg {
  flex: 0 0 auto;
  width: var(--size);
  height: var(--size);
}
.excalidraw .UserList__collaborator-status-icons {
  margin-left: auto;
  flex: 0 0 auto;
  min-width: 2.25rem;
  gap: 0.25rem;
  justify-content: flex-end;
  display: flex;
}
.excalidraw .UserList__collaborator.is-muted .UserList__collaborator-status-icon-microphone-muted {
  color: var(--color-danger);
  filter: drop-shadow(0px 0px 0px rgba(0, 0, 0, 0.5));
}
.excalidraw .UserList__collaborator-status-icon-speaking-indicator {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: space-between;
  width: 1rem;
  padding: 0 3px;
  box-sizing: border-box;
}
.excalidraw .UserList__collaborator-status-icon-speaking-indicator div {
  width: 0.125rem;
  height: 0.4rem;
  background-color: #a2f1a6;
}
.excalidraw .UserList__collaborator-status-icon-speaking-indicator div:nth-of-type(1) {
  animation: speaking-indicator-anim 1s -0.45s ease-in-out infinite;
}
.excalidraw .UserList__collaborator-status-icon-speaking-indicator div:nth-of-type(2) {
  animation: speaking-indicator-anim 1s -0.9s ease-in-out infinite;
}
.excalidraw .UserList__collaborator-status-icon-speaking-indicator div:nth-of-type(3) {
  animation: speaking-indicator-anim 1s -0.15s ease-in-out infinite;
}
@keyframes speaking-indicator-anim {
  0%, 100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(2);
  }
}
.excalidraw.theme--dark {
  --userlist-hint-bg-color: var(--color-gray-90);
  --userlist-hint-heading-color: var(--color-gray-30);
  --userlist-hint-text-color: var(--color-gray-40);
  --userlist-collaborators-border-color: var(--color-gray-80);
}
.excalidraw .UserList__collaborators {
  top: auto;
  max-height: 50vh;
}

/* components/Card.scss */
.excalidraw .Card {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 290px;
  margin: 1em;
  text-align: center;
}
.excalidraw .Card .Card-icon {
  font-size: 2.6em;
  display: flex;
  flex: 0 0 auto;
  padding: 1.4rem;
  border-radius: 50%;
  background: var(--card-color);
  color: #ffffff;
}
.excalidraw .Card .Card-icon svg {
  width: 2.8rem;
  height: 2.8rem;
}
.excalidraw .Card .Card-details {
  font-size: 0.96em;
  min-height: 90px;
  padding: 0 1em;
  margin-bottom: auto;
}
.excalidraw .Card .Card-button.ToolIcon_type_button {
  height: 2.5rem;
  margin-top: 1em;
  margin-bottom: 0.3em;
  background-color: var(--card-color);
}
.excalidraw .Card .Card-button.ToolIcon_type_button:hover {
  background-color: var(--card-color-darker);
}
.excalidraw .Card .Card-button.ToolIcon_type_button:active {
  background-color: var(--card-color-darkest);
}
.excalidraw .Card .Card-button.ToolIcon_type_button .ToolIcon__label {
  color: #ffffff;
}
.excalidraw .Card .Card-button.ToolIcon_type_button .Spinner {
  --spinner-color: #fff;
}

/* components/ExportDialog.scss */
.excalidraw .ExportDialog__preview {
  --preview-padding: calc(var(--space-factor) * 4);
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==) left center;
  text-align: center;
  padding: var(--preview-padding);
  margin-bottom: calc(var(--space-factor) * 3);
  display: flex;
  justify-content: center;
  align-items: center;
}
.excalidraw .ExportDialog__preview canvas {
  max-width: calc(100% - var(--preview-padding) * 2);
  max-height: 25rem;
}
.excalidraw.theme--dark .ExportDialog__preview canvas {
  filter: none;
}
.excalidraw .ExportDialog__actions {
  width: 100%;
  display: flex;
  grid-gap: calc(var(--space-factor) * 2);
  align-items: top;
  justify-content: space-between;
}
.excalidraw--mobile.excalidraw .ExportDialog {
  display: flex;
  flex-direction: column;
}
.excalidraw--mobile.excalidraw .ExportDialog__actions {
  flex-direction: column;
  align-items: center;
}
.excalidraw--mobile.excalidraw .ExportDialog__actions > * {
  margin-bottom: calc(var(--space-factor) * 3);
}
.excalidraw--mobile.excalidraw .ExportDialog__preview canvas {
  max-height: 30vh;
}
.excalidraw--mobile.excalidraw .ExportDialog__dialog,
.excalidraw--mobile.excalidraw .ExportDialog__dialog .Island {
  height: 100%;
  box-sizing: border-box;
}
.excalidraw--mobile.excalidraw .ExportDialog__dialog .Island {
  overflow-y: auto;
}
.excalidraw .ExportDialog--json .ExportDialog-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  justify-items: center;
  row-gap: 2em;
}
@media (max-width: 460px) {
  .excalidraw .ExportDialog--json .ExportDialog-cards {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
  .excalidraw .ExportDialog--json .ExportDialog-cards .Card-details {
    min-height: 40px;
  }
}
.excalidraw .ExportDialog--json .ExportDialog-cards .ProjectName {
  width: fit-content;
  margin: 1em auto;
  align-items: flex-start;
  flex-direction: column;
}
.excalidraw .ExportDialog--json .ExportDialog-cards .ProjectName .TextInput {
  width: auto;
}
.excalidraw .ExportDialog--json .ExportDialog-cards .ProjectName-label {
  margin: 0.625em 0;
  font-weight: bold;
}
.excalidraw button.ExportDialog-imageExportButton {
  border: 0;
  width: 5rem;
  height: 5rem;
  margin: 0 0.2em;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1rem;
  background-color: var(--button-color);
  box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.28), 0 6px 10px 0 rgba(0, 0, 0, 0.14);
  font-family: Cascadia;
  font-size: 1.8em;
  color: #ffffff;
}
.excalidraw button.ExportDialog-imageExportButton:hover {
  background-color: var(--button-color-darker);
}
.excalidraw button.ExportDialog-imageExportButton:active {
  background-color: var(--button-color-darkest);
  box-shadow: none;
}
.excalidraw button.ExportDialog-imageExportButton svg {
  width: 0.9em;
}

/* components/Sidebar/SidebarTrigger.scss */
.excalidraw .sidebar-trigger {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.625rem;
  width: var(--button-width, var(--default-button-size));
  height: var(--button-height, var(--default-button-size));
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: var(--button-border, var(--default-border-color));
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  background-color: var(--button-bg, var(--island-bg-color));
  color: var(--button-color, var(--color-on-surface));
  font-family: var(--ui-font);
  border: none;
  box-shadow: 0 0 0 1px var(--color-surface-lowest);
  background-color: var(--color-surface-low);
  width: auto;
  height: var(--lg-button-size);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  line-height: 0;
  font-size: 0.75rem;
  letter-spacing: 0.4px;
}
.excalidraw .sidebar-trigger svg {
  width: var(--button-width, var(--lg-icon-size));
  height: var(--button-height, var(--lg-icon-size));
}
.excalidraw .sidebar-trigger:hover {
  background-color: var(--button-hover-bg, var(--island-bg-color));
  border-color: var(--button-hover-border, var(--button-border, var(--default-border-color)));
  color: var(--button-hover-color, var(--button-color, var(--text-primary-color, inherit)));
}
.excalidraw .sidebar-trigger:active {
  background-color: var(--button-active-bg, var(--island-bg-color));
  border-color: var(--button-active-border, var(--color-primary-darkest));
}
.excalidraw .sidebar-trigger.active {
  background-color: var(--button-selected-bg, var(--color-surface-primary-container));
  border-color: var(--button-selected-border, var(--color-surface-primary-container));
}
.excalidraw .sidebar-trigger.active:hover {
  background-color: var(--button-selected-hover-bg, var(--color-surface-primary-container));
}
.excalidraw .sidebar-trigger.active svg {
  color: var(--button-color, var(--color-on-primary-container));
}
.excalidraw .sidebar-trigger:active {
  box-shadow: 0 0 0 1px var(--color-brand-active);
}
.excalidraw .sidebar-trigger svg {
  width: var(--lg-icon-size);
  height: var(--lg-icon-size);
}
.excalidraw .sidebar-trigger__label-element {
  align-self: flex-start;
}
.excalidraw .default-sidebar-trigger .sidebar-trigger__label {
  display: block;
  white-space: nowrap;
}
.excalidraw.excalidraw--mobile .default-sidebar-trigger .sidebar-trigger__label {
  display: none;
}

/* components/Sidebar/Sidebar.scss */
.excalidraw .sidebar {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: 5;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background-color: var(--sidebar-bg-color);
  box-shadow: var(--sidebar-shadow);
  pointer-events: var(--ui-pointerEvents);
  overflow: hidden;
  border-radius: 0;
  width: calc(var(--right-sidebar-width) - var(--space-factor) * 2);
  border-left: 1px solid var(--sidebar-border-color);
}
:root[dir=rtl] .excalidraw .sidebar {
  left: 0;
  right: auto;
}
.excalidraw .sidebar--docked {
  box-shadow: none;
}
:root[dir=rtl] .excalidraw .sidebar {
  border-right: 1px solid var(--sidebar-border-color);
  border-left: 0;
}
.excalidraw .sidebar__header {
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 1rem 0.75rem;
  position: relative;
}
.excalidraw .sidebar__header::after {
  content: "";
  width: calc(100% - 1.5rem);
  height: 1px;
  background: var(--sidebar-border-color);
  position: absolute;
  bottom: -1px;
}
.excalidraw .sidebar__header__buttons {
  gap: 0;
  display: flex;
  align-items: center;
  margin-left: auto;
}
.excalidraw .sidebar__header__buttons button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.625rem;
  width: var(--button-width, var(--default-button-size));
  height: var(--button-height, var(--default-button-size));
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: var(--button-border, var(--default-border-color));
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  background-color: var(--button-bg, var(--island-bg-color));
  color: var(--button-color, var(--color-on-surface));
  font-family: var(--ui-font);
  --button-bg: transparent;
  border: 0 !important;
  width: var(--lg-button-size);
  height: var(--lg-button-size);
  padding: 0;
}
.excalidraw .sidebar__header__buttons button svg {
  width: var(--button-width, var(--lg-icon-size));
  height: var(--button-height, var(--lg-icon-size));
}
.excalidraw .sidebar__header__buttons button:hover {
  background-color: var(--button-hover-bg, var(--island-bg-color));
  border-color: var(--button-hover-border, var(--button-border, var(--default-border-color)));
  color: var(--button-hover-color, var(--button-color, var(--text-primary-color, inherit)));
}
.excalidraw .sidebar__header__buttons button:active {
  background-color: var(--button-active-bg, var(--island-bg-color));
  border-color: var(--button-active-border, var(--color-primary-darkest));
}
.excalidraw .sidebar__header__buttons button.active {
  background-color: var(--button-selected-bg, var(--color-surface-primary-container));
  border-color: var(--button-selected-border, var(--color-surface-primary-container));
}
.excalidraw .sidebar__header__buttons button.active:hover {
  background-color: var(--button-selected-hover-bg, var(--color-surface-primary-container));
}
.excalidraw .sidebar__header__buttons button.active svg {
  color: var(--button-color, var(--color-on-primary-container));
}
.excalidraw .sidebar__header__buttons button svg {
  width: var(--lg-icon-size);
  height: var(--lg-icon-size);
}
.excalidraw .sidebar__header__buttons button:hover {
  background: var(--button-hover-bg, var(--island-bg-color));
}
.excalidraw .sidebar__header__buttons .sidebar__dock.selected svg {
  stroke: var(--color-primary);
  fill: var(--color-primary);
}
.excalidraw .sidebar-tabs-root {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  padding: 1rem 0;
}
.excalidraw .sidebar-tabs-root [role=tabpanel] {
  flex: 1;
  outline: none;
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  outline: none;
}
.excalidraw .sidebar-tabs-root [role=tabpanel][data-state=inactive] {
  display: none !important;
}
.excalidraw .sidebar-tabs-root [role=tablist] {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
}
.excalidraw .sidebar-tabs-root > .sidebar__header {
  padding-top: 0;
  padding-bottom: 1rem;
}
.excalidraw .sidebar-tab-trigger {
  --button-width: auto;
  --button-bg: transparent;
  --button-hover-bg: transparent;
  --button-active-bg: var(--color-primary);
  --button-hover-color: var(--color-primary);
  --button-hover-border: var(--color-primary);
}
.excalidraw .sidebar-tab-trigger[data-state=active] {
  --button-bg: var(--color-primary);
  --button-hover-bg: var(--color-primary-darker);
  --button-hover-color: var(--color-icon-white);
  --button-border: var(--color-primary);
  color: var(--color-icon-white);
}
.excalidraw .default-sidebar {
  display: flex;
  flex-direction: column;
}
.excalidraw .default-sidebar .sidebar-triggers {
  display: flex;
  gap: 0;
  padding: 2px;
  margin-top: -3px;
  margin-bottom: -3px;
  border: 1px solid var(--sidebar-border-color);
  background: var(--default-bg-color);
  border-radius: 0.625rem;
}
.excalidraw .default-sidebar .sidebar-triggers .sidebar-tab-trigger {
  height: var(--lg-button-size);
  width: var(--lg-button-size);
  border: none;
}

/* components/main-menu/DefaultItems.scss */
.excalidraw .ActiveFile .ActiveFile__fileName {
  display: flex;
  align-items: center;
}
.excalidraw .ActiveFile .ActiveFile__fileName span {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: 9.3em;
}
.excalidraw .ActiveFile .ActiveFile__fileName svg {
  width: 1.15em;
  margin-inline-end: 0.3em;
  transform: scaleY(0.9);
}

/* components/OverwriteConfirm/OverwriteConfirm.scss */
.excalidraw .OverwriteConfirm {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  isolation: isolate;
}
.excalidraw .OverwriteConfirm h3 {
  margin: 0;
  font-weight: 700;
  font-size: 1.3125rem;
  line-height: 130%;
  align-self: flex-start;
  color: var(--text-primary-color);
}
.excalidraw .OverwriteConfirm__Description {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  gap: 1rem;
  padding: 2.5rem;
  background: var(--color-danger-background);
  border-radius: 0.5rem;
  font-family: "Assistant";
  font-style: normal;
  font-weight: 400;
  font-size: 1rem;
  line-height: 150%;
  color: var(--color-danger-color);
}
.excalidraw--mobile.excalidraw .OverwriteConfirm__Description {
  flex-direction: column;
  text-align: center;
}
.excalidraw .OverwriteConfirm__Description__spacer {
  flex-grow: 1;
}
.excalidraw .OverwriteConfirm__Description__icon {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2.5rem;
  background: var(--color-danger-icon-background);
  width: 3.5rem;
  height: 3.5rem;
  padding: 0.75rem;
}
.excalidraw .OverwriteConfirm__Description__icon svg {
  color: var(--color-danger-icon-color);
  width: 1.5rem;
  height: 1.5rem;
}
.excalidraw .OverwriteConfirm__Description.OverwriteConfirm__Description--color-warning {
  background: var(--color-warning-background);
  color: var(--color-warning-color);
}
.excalidraw .OverwriteConfirm__Description.OverwriteConfirm__Description--color-warning .OverwriteConfirm__Description__icon {
  background: var(--color-warning-icon-background);
  flex: 0 0 auto;
}
.excalidraw .OverwriteConfirm__Description.OverwriteConfirm__Description--color-warning .OverwriteConfirm__Description__icon svg {
  color: var(--color-warning-icon-color);
}
.excalidraw .OverwriteConfirm__Actions {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-items: stretch;
  justify-content: center;
  gap: 1.5rem;
}
.excalidraw--mobile.excalidraw .OverwriteConfirm__Actions {
  flex-direction: column;
}
.excalidraw .OverwriteConfirm__Actions__Action {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  gap: 0.75rem;
  flex-basis: 50%;
  flex-grow: 0;
}
.excalidraw .OverwriteConfirm__Actions__Action__content {
  height: 100%;
  font-size: 0.875rem;
  text-align: center;
}
.excalidraw .OverwriteConfirm__Actions__Action h4 {
  font-weight: 700;
  font-size: 1.125rem;
  line-height: 130%;
  margin: 0;
  color: var(--text-primary-color);
}

/* components/SearchMenu.scss */
.excalidraw .layer-ui__search {
  flex: 1 0 auto;
  display: flex;
  flex-direction: column;
  padding: 8px 0 0 0;
}
.excalidraw .layer-ui__search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0.75rem;
}
.excalidraw .layer-ui__search-header .ExcTextField {
  flex: 1 0 auto;
}
.excalidraw .layer-ui__search-header .ExcTextField__input {
  background-color: #f5f5f9;
  border-radius: var(--border-radius-md);
  border: 0;
}
.excalidraw.theme--dark.excalidraw .layer-ui__search-header .ExcTextField__input {
  background-color: #31303b;
}
.excalidraw .layer-ui__search-header .ExcTextField__input input::placeholder {
  font-size: 0.9rem;
}
.excalidraw .layer-ui__search-count {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 8px 0 8px;
  margin: 0 0.75rem 0.25rem 0.75rem;
  font-size: 0.8em;
}
.excalidraw .layer-ui__search-count .result-nav {
  display: flex;
}
.excalidraw .layer-ui__search-count .result-nav .result-nav-btn {
  width: 36px;
  height: 36px;
  --button-border: transparent;
}
.excalidraw .layer-ui__search-count .result-nav .result-nav-btn:active {
  background-color: var(--color-surface-high);
}
.excalidraw .layer-ui__search-count .result-nav .result-nav-btn:first-child {
  margin-right: 4px;
}
.excalidraw .layer-ui__search-result-container {
  overflow-y: auto;
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}
.excalidraw .layer-ui__result-item {
  display: flex;
  align-items: center;
  min-height: 2rem;
  flex: 0 0 auto;
  padding: 0.25rem 0.75rem;
  cursor: pointer;
  border: 1px solid transparent;
  outline: none;
  margin: 0 0.75rem;
  border-radius: var(--border-radius-md);
}
.excalidraw .layer-ui__result-item .text-icon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.75rem;
}
.excalidraw .layer-ui__result-item .preview-text {
  flex: 1;
  max-height: 48px;
  line-height: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
.excalidraw .layer-ui__result-item:hover {
  background-color: var(--color-surface-high);
}
.excalidraw .layer-ui__result-item:active {
  border-color: var(--color-primary);
}
.excalidraw .layer-ui__result-item.active {
  background-color: var(--color-surface-high);
}

/* components/TTDDialog/MermaidToExcalidraw.scss */
.excalidraw .dialog-mermaid-title {
  margin-block: 0.25rem;
  font-size: 1.25rem;
  font-weight: 700;
  padding-inline: 2.5rem;
}

/* components/TTDDialog/TTDDialog.scss */
.excalidraw .Modal.Dialog.ttd-dialog {
  padding: 1.25rem;
}
.excalidraw .Modal.Dialog.ttd-dialog.Dialog--fullscreen {
  margin-top: 0;
}
.excalidraw .Modal.Dialog.ttd-dialog .Island {
  padding-inline: 0 !important;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  box-shadow: none;
}
.excalidraw .Modal.Dialog.ttd-dialog .Modal__content {
  height: auto;
  max-height: 100%;
}
@media screen and (min-width: 861px) {
  .excalidraw .Modal.Dialog.ttd-dialog .Modal__content {
    max-height: 750px;
    height: 100%;
  }
}
.excalidraw .Modal.Dialog.ttd-dialog .Dialog__content {
  flex: 1 1 auto;
}
.excalidraw .ttd-dialog-desc {
  font-size: 15px;
  font-style: italic;
  font-weight: 500;
  margin-bottom: 1.5rem;
}
.excalidraw .ttd-dialog-tabs-root {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.excalidraw .ttd-dialog-tab-trigger {
  color: var(--color-on-surface);
  font-size: 0.875rem;
  margin: 0;
  padding: 0 1rem;
  background-color: transparent;
  border: 0;
  height: 2.875rem;
  font-weight: 600;
  font-family: inherit;
  letter-spacing: 0.4px;
}
.excalidraw .ttd-dialog-tab-trigger[data-state=active] {
  border-bottom: 2px solid var(--color-primary);
}
.excalidraw .ttd-dialog-triggers {
  border-bottom: 1px solid var(--color-surface-high);
  margin-bottom: 1.5rem;
  padding-inline: 2.5rem;
}
.excalidraw .ttd-dialog-content {
  padding-inline: 2.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.excalidraw .ttd-dialog-content[hidden] {
  display: none;
}
.excalidraw .ttd-dialog-input {
  width: auto;
  height: 10rem;
  resize: none;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--dialog-border-color);
  white-space: pre-wrap;
  padding: 0.85rem;
  box-sizing: border-box;
  font-family: monospace;
}
@media screen and (min-width: 861px) {
  .excalidraw .ttd-dialog-input {
    width: 100%;
    height: 100%;
  }
}
.excalidraw .ttd-dialog-output-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.85rem;
  box-sizing: border-box;
  flex-grow: 1;
  position: relative;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==) left center;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--dialog-border-color);
  height: 400px;
  width: auto;
}
@media screen and (min-width: 861px) {
  .excalidraw .ttd-dialog-output-wrapper {
    width: 100%;
    height: 200px;
  }
}
.excalidraw .ttd-dialog-output-wrapper canvas {
  max-width: 100%;
  max-height: 100%;
}
.excalidraw .ttd-dialog-output-canvas-container {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
}
.excalidraw .ttd-dialog-output-error {
  color: red;
  font-weight: 700;
  font-size: 30px;
  word-break: break-word;
  overflow: auto;
  max-height: 100%;
  height: 100%;
  width: 100%;
  text-align: center;
  position: absolute;
  z-index: 10;
}
.excalidraw .ttd-dialog-output-error p {
  font-weight: 500;
  font-family: Cascadia;
  text-align: left;
  white-space: pre-wrap;
  font-size: 0.875rem;
  padding: 0 10px;
}
.excalidraw .ttd-dialog-panels {
  height: 100%;
}
@media screen and (min-width: 861px) {
  .excalidraw .ttd-dialog-panels {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }
}
.excalidraw .ttd-dialog-panel {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.excalidraw .ttd-dialog-panel__header {
  display: flex;
  margin: 0px 4px 4px 4px;
  align-items: center;
  gap: 1rem;
}
.excalidraw .ttd-dialog-panel__header label {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
}
.excalidraw .ttd-dialog-panel:first-child .ttd-dialog-panel-button-container:not(.invisible) {
  margin-bottom: 4rem;
}
@media screen and (min-width: 861px) {
  .excalidraw .ttd-dialog-panel .ttd-dialog-panel-button-container:not(.invisible) {
    margin-bottom: 0.5rem !important;
  }
}
.excalidraw .ttd-dialog-panel textarea {
  height: 100%;
  resize: none;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--dialog-border-color);
  white-space: pre-wrap;
  padding: 0.85rem;
  box-sizing: border-box;
  width: 100%;
  font-family: monospace;
}
@media screen and (max-width: 861px) {
  .excalidraw .ttd-dialog-panel textarea {
    width: auto;
    height: 10rem;
  }
}
.excalidraw .ttd-dialog-panel-button-container {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}
.excalidraw .ttd-dialog-panel-button-container.invisible .ttd-dialog-panel-button {
  display: none;
}
@media screen and (min-width: 861px) {
  .excalidraw .ttd-dialog-panel-button-container.invisible .ttd-dialog-panel-button {
    display: block;
    visibility: hidden;
  }
}
.excalidraw .ttd-dialog-panel-button {
  position: relative;
}
.excalidraw .ttd-dialog-panel-button.excalidraw-button {
  font-family: inherit;
  font-weight: 600;
  height: 2.5rem;
  font-size: 12px;
  color: #ffffff;
  background-color: var(--color-primary);
  width: 100%;
}
.excalidraw .ttd-dialog-panel-button.excalidraw-button:hover {
  background-color: var(--color-primary-darker);
}
.excalidraw .ttd-dialog-panel-button.excalidraw-button:active {
  background-color: var(--color-primary-darkest);
}
.excalidraw .ttd-dialog-panel-button.excalidraw-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.excalidraw .ttd-dialog-panel-button.excalidraw-button:disabled:hover {
  background-color: var(--color-primary);
}
@media screen and (min-width: 861px) {
  .excalidraw .ttd-dialog-panel-button.excalidraw-button {
    width: auto;
    min-width: 7.5rem;
  }
}
.excalidraw.theme--dark.excalidraw .ttd-dialog-panel-button.excalidraw-button {
  color: var(--color-gray-100);
}
.excalidraw .ttd-dialog-panel-button div {
  display: contents;
}
.excalidraw .ttd-dialog-panel-button div.invisible {
  visibility: hidden;
}
.excalidraw .ttd-dialog-panel-button div.Spinner {
  display: flex !important;
  position: absolute;
  inset: 0;
  --spinner-color: white;
}
.excalidraw.theme--dark.excalidraw .ttd-dialog-panel-button div.Spinner {
  --spinner-color: var(--color-gray-100);
}
.excalidraw .ttd-dialog-panel-button div span {
  padding-left: 0.5rem;
  display: flex;
}
.excalidraw .ttd-dialog-submit-shortcut {
  margin-inline-start: 0.5rem;
  font-size: 0.625rem;
  opacity: 0.6;
  display: flex;
  gap: 0.125rem;
}
.excalidraw .ttd-dialog-submit-shortcut__key {
  border: 1px solid gray;
  padding: 2px 3px;
  border-radius: 4px;
}

/* components/Stats/DragInput.scss */
.excalidraw .drag-input-container {
  display: flex;
  width: 100%;
}
.excalidraw .drag-input-container:focus-within {
  box-shadow: 0 0 0 1px var(--color-primary-darkest);
  border-radius: var(--border-radius-md);
}
.excalidraw .disabled {
  opacity: 0.5;
  pointer-events: none;
}
.excalidraw .drag-input-label {
  flex-shrink: 0;
  border: 1px solid var(--default-border-color);
  border-right: 0;
  padding: 0 0.5rem 0 0.75rem;
  min-width: 1rem;
  height: 2rem;
  box-sizing: border-box;
  color: var(--popup-text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
:root[dir=ltr] .excalidraw .drag-input-label {
  border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}
:root[dir=rtl] .excalidraw .drag-input-label {
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
  border-right: 1px solid var(--default-border-color);
  border-left: 0;
}
.excalidraw .drag-input {
  box-sizing: border-box;
  width: 100%;
  margin: 0;
  font-size: 0.875rem;
  font-family: inherit;
  background-color: transparent;
  color: var(--text-primary-color);
  border: 0;
  outline: none;
  height: 2rem;
  border: 1px solid var(--default-border-color);
  border-left: 0;
  letter-spacing: 0.4px;
  padding: 0.5rem;
  padding-left: 0.25rem;
  appearance: none;
}
:root[dir=ltr] .excalidraw .drag-input {
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}
:root[dir=rtl] .excalidraw .drag-input {
  border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
  border-left: 1px solid var(--default-border-color);
  border-right: 0;
}
.excalidraw .drag-input:focus-visible {
  box-shadow: none;
}

/* components/Stats/Stats.scss */
.exc-stats {
  width: 204px;
  position: absolute;
  top: 60px;
  font-size: 12px;
  z-index: var(--zIndex-layerUI);
  pointer-events: var(--ui-pointerEvents);
}
:root[dir=rtl] .exc-stats {
  left: 12px;
  right: initial;
}
.exc-stats h2 {
  font-size: 1.5em;
  margin-block-start: 0.83em;
  margin-block-end: 0.83em;
  font-weight: bold;
}
.exc-stats h3 {
  white-space: nowrap;
  font-size: 1.17em;
  margin: 0;
  font-weight: bold;
}
.exc-stats__rows {
  display: flex;
  flex-direction: column;
  gap: 0.3125rem;
}
.exc-stats__row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  display: grid;
  gap: 4px;
}
.exc-stats__row div + div {
  text-align: right;
}
.exc-stats__row--heading {
  text-align: center;
  font-weight: bold;
  margin: 0.25rem 0;
}
.exc-stats .title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.exc-stats .title h2 {
  margin: 0;
}
.exc-stats .close {
  height: 16px;
  width: 16px;
  cursor: pointer;
}
.exc-stats .close svg {
  width: 100%;
  height: 100%;
}

/* components/ElementLinkDialog.scss */
.excalidraw .ElementLinkDialog {
  position: absolute;
  top: var(--editor-container-padding);
  left: var(--editor-container-padding);
  z-index: var(--zIndex-modal);
  border-radius: 10px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-shadow: var(--shadow-island);
  background-color: var(--island-bg-color);
}
.excalidraw--mobile.excalidraw .ElementLinkDialog {
  left: 0;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  width: calc(100% - 1rem);
  box-sizing: border-box;
  z-index: 5;
}
.excalidraw .ElementLinkDialog .ElementLinkDialog__header {
  margin-bottom: 1.5rem;
}
.excalidraw .ElementLinkDialog .ElementLinkDialog__header h2 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}
.excalidraw--mobile.excalidraw .ElementLinkDialog .ElementLinkDialog__header h2 {
  font-size: 1.25rem;
}
.excalidraw .ElementLinkDialog .ElementLinkDialog__header p {
  margin: 0;
}
.excalidraw--mobile.excalidraw .ElementLinkDialog .ElementLinkDialog__header p {
  font-size: 0.875rem;
}
.excalidraw--mobile.excalidraw .ElementLinkDialog .ElementLinkDialog__header {
  margin-bottom: 1rem;
}
.excalidraw .ElementLinkDialog .ElementLinkDialog__input {
  display: flex;
}
.excalidraw .ElementLinkDialog .ElementLinkDialog__input .ElementLinkDialog__input-field {
  flex: 1;
}
.excalidraw .ElementLinkDialog .ElementLinkDialog__input .ElementLinkDialog__remove {
  color: #c92a2a;
  margin-left: 1rem;
}
.excalidraw .ElementLinkDialog .ElementLinkDialog__input .ElementLinkDialog__remove .ToolIcon__icon {
  width: 2rem;
  height: 2rem;
}
.excalidraw .ElementLinkDialog .ElementLinkDialog__input .ElementLinkDialog__remove .ToolIcon__icon svg {
  color: #fa5252;
}
.excalidraw .ElementLinkDialog .ElementLinkDialog__actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
}
.excalidraw--mobile.excalidraw .ElementLinkDialog .ElementLinkDialog__actions {
  font-size: 0.875rem;
  margin-top: 1rem;
}

/* components/LayerUI.scss */
.excalidraw .layer-ui__wrapper.animate {
  transition: width 0.1s ease-in-out;
}
.excalidraw .layer-ui__wrapper {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: var(--zIndex-layerUI);
}
.excalidraw .layer-ui__wrapper__top-right {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  gap: 0.75rem;
  pointer-events: none !important;
}
.excalidraw .layer-ui__wrapper__top-right > * {
  pointer-events: var(--ui-pointerEvents);
}
.excalidraw .layer-ui__wrapper__footer {
  width: 100%;
}
.excalidraw .layer-ui__wrapper__footer-right {
  z-index: 100;
  display: flex;
}
.excalidraw .layer-ui__wrapper .zen-mode-transition {
  transition: transform 0.5s ease-in-out;
}
:root[dir=ltr] .excalidraw .layer-ui__wrapper .zen-mode-transition.transition-left {
  transform: translate(-999px, 0);
}
:root[dir=ltr] .excalidraw .layer-ui__wrapper .zen-mode-transition.transition-right {
  transform: translate(999px, 0);
}
:root[dir=rtl] .excalidraw .layer-ui__wrapper .zen-mode-transition.transition-left {
  transform: translate(999px, 0);
}
:root[dir=rtl] .excalidraw .layer-ui__wrapper .zen-mode-transition.transition-right {
  transform: translate(-999px, 0);
}
.excalidraw .layer-ui__wrapper .zen-mode-transition.layer-ui__wrapper__footer-left--transition-bottom {
  transform: translate(0, 92px);
}
.excalidraw .layer-ui__wrapper .disable-zen-mode {
  padding: 10px;
  position: absolute;
  bottom: 0;
  opacity: 0;
  visibility: hidden;
  transition: visibility 0s linear 0s, opacity 0.5s;
  font-family: var(--ui-font);
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--default-border-color);
  background-color: var(--island-bg-color);
  color: var(--text-primary-color);
}
[dir=ltr] .excalidraw .layer-ui__wrapper .disable-zen-mode {
  right: 1rem;
}
[dir=rtl] .excalidraw .layer-ui__wrapper .disable-zen-mode {
  left: 1rem;
}
.excalidraw .layer-ui__wrapper .disable-zen-mode:hover {
  background-color: var(--button-hover-bg);
}
.excalidraw .layer-ui__wrapper .disable-zen-mode:active {
  border-color: var(--color-primary);
}
.excalidraw .layer-ui__wrapper .disable-zen-mode--visible {
  opacity: 1;
  visibility: visible;
  transition: visibility 0s linear 300ms, opacity 0.5s;
  transition-delay: 0.8s;
  pointer-events: var(--ui-pointerEvents);
}
.excalidraw .layer-ui__wrapper .layer-ui__wrapper__footer-left > *,
.excalidraw .layer-ui__wrapper .footer-center > *,
.excalidraw .layer-ui__wrapper .layer-ui__wrapper__footer-right > * {
  pointer-events: var(--ui-pointerEvents);
}
.excalidraw .layer-ui__wrapper .layer-ui__wrapper__footer-right {
  margin-top: auto;
  margin-bottom: auto;
}

/* components/Toolbar.scss */
.excalidraw .App-toolbar.zen-mode .ToolIcon__keybinding,
.excalidraw .App-toolbar.zen-mode .HintViewer {
  display: none;
}
.excalidraw .App-toolbar__divider {
  width: 1px;
  height: 1.5rem;
  align-self: center;
  background-color: var(--default-border-color);
  margin: 0 0.25rem;
}
.excalidraw--mobile.excalidraw .App-toolbar__divider {
  margin: 0;
}
.excalidraw .App-toolbar__extra-tools-trigger {
  box-shadow: none;
  border: 0;
  background-color: transparent;
}
.excalidraw .App-toolbar__extra-tools-trigger:active {
  background-color: var(--button-hover-bg);
  box-shadow: 0 0 0 1px var(--button-active-border, var(--color-primary-darkest)) inset;
}
.excalidraw .App-toolbar__extra-tools-trigger--selected,
.excalidraw .App-toolbar__extra-tools-trigger--selected:hover {
  background: var(--color-primary-light);
  color: var(--color-primary);
}
.excalidraw .App-toolbar__extra-tools-dropdown {
  margin-top: 0.375rem;
  right: 0;
  min-width: 11.875rem;
  z-index: 1;
}

/* components/Toast.scss */
.excalidraw .Toast {
  animation: fade-in 0.5s;
  background-color: var(--button-gray-1);
  border-radius: 4px;
  bottom: 10px;
  box-sizing: border-box;
  cursor: default;
  left: 50%;
  margin-left: -150px;
  padding: 4px 0;
  position: absolute;
  text-align: center;
  width: 300px;
  z-index: 999999;
}
.excalidraw .Toast .Toast__message {
  padding: 0 1.6rem;
  color: var(--popup-text-color);
  white-space: pre-wrap;
}
.excalidraw .Toast .close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.4rem;
}
.excalidraw .Toast .close .ToolIcon__icon {
  width: 1.2rem;
  height: 1.2rem;
}
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* components/SVGLayer.scss */
.excalidraw .SVGLayer {
  pointer-events: none;
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--zIndex-svgLayer);
}
.excalidraw .SVGLayer svg {
  image-rendering: auto;
  overflow: visible;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

/* element/ElementCanvasButtons.scss */
.excalidraw .excalidraw-canvas-buttons {
  position: absolute;
  box-shadow: 0px 2px 4px 0 rgba(0, 0, 0, 0.3);
  z-index: var(--zIndex-canvasButtons);
  background: var(--island-bg-color);
  border-radius: var(--border-radius-lg);
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}

/* components/FollowMode/FollowMode.scss */
.excalidraw .follow-mode {
  position: absolute;
  box-sizing: border-box;
  pointer-events: none;
  border: 2px solid var(--color-primary-hover);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.excalidraw .follow-mode__badge {
  background-color: var(--color-primary-hover);
  color: var(--color-primary-light);
  padding: 0.25rem 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: 0.5rem;
  pointer-events: all;
  font-size: 0.75rem;
  display: flex;
  gap: 0.5rem;
  align-items: center;
}
.excalidraw .follow-mode__badge__label {
  display: flex;
  white-space: pre-wrap;
  line-height: 1;
}
.excalidraw .follow-mode__badge__username {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}
.excalidraw .follow-mode__disconnect-btn {
  all: unset;
  cursor: pointer;
  border-radius: 0.25rem;
}
.excalidraw .follow-mode__disconnect-btn:hover {
  background-color: var(--color-primary-darker);
}
.excalidraw .follow-mode__disconnect-btn:active {
  background-color: var(--color-primary-darkest);
}
.excalidraw .follow-mode__disconnect-btn svg {
  display: block;
  width: 1rem;
  height: 1rem;
}

/* css/app.scss */
.visually-hidden {
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(1px, 1px, 1px, 1px);
  white-space: nowrap;
  user-select: none;
}
.LoadingMessage {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}
.LoadingMessage .Spinner {
  font-size: 2.8em;
}
.LoadingMessage .LoadingMessage-text {
  margin-top: 1em;
  font-size: 0.8em;
}
.LoadingMessage--dark {
  background-color: #121212;
  color: #ced4da;
}

/* css/styles.scss */
.excalidraw {
  --theme-filter: none;
  --button-destructive-bg-color: #ffe3e3;
  --button-destructive-color: #c92a2a;
  --button-gray-1: #e9ecef;
  --button-gray-2: #ced4da;
  --button-gray-3: #adb5bd;
  --button-special-active-bg-color: #ebfbee;
  --dialog-border-color: var(--color-gray-20);
  --dropdown-icon: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="292.4" height="292.4" viewBox="0 0 292 292"><path d="M287 197L159 69c-4-3-8-5-13-5s-9 2-13 5L5 197c-3 4-5 8-5 13s2 9 5 13c4 4 8 5 13 5h256c5 0 9-1 13-5s5-8 5-13-1-9-5-13z"/></svg>');
  --focus-highlight-color: #a5d8ff;
  --icon-fill-color: var(--color-on-surface);
  --icon-green-fill-color: #2b8a3e;
  --default-bg-color: #ffffff;
  --input-bg-color: #ffffff;
  --input-border-color: #ced4da;
  --input-hover-bg-color: #f1f3f5;
  --input-label-color: #495057;
  --island-bg-color: #ffffff;
  --keybinding-color: var(--color-gray-40);
  --link-color: #1c7ed6;
  --overlay-bg-color: rgba(255, 255, 255, 0.88);
  --popup-bg-color: var(--island-bg-color);
  --popup-secondary-bg-color: #f1f3f5;
  --popup-text-color: #000000;
  --popup-text-inverted-color: #ffffff;
  --select-highlight-color: #339af0;
  --shadow-island:
    0px 0px 0.9310142993927002px 0px rgba(0, 0, 0, 0.17),
    0px 0px 3.1270833015441895px 0px rgba(0, 0, 0, 0.08),
    0px 7px 14px 0px rgba(0, 0, 0, 0.05);
  --button-hover-bg: var(--color-surface-high);
  --button-active-bg: var(--color-surface-high);
  --button-active-border: var(--color-brand-active);
  --default-border-color: var(--color-surface-high);
  --default-button-size: 2rem;
  --default-icon-size: 1rem;
  --lg-button-size: 2.25rem;
  --lg-icon-size: 1rem;
  --editor-container-padding: 1rem;
  --scrollbar-thumb: var(--button-gray-2);
  --scrollbar-thumb-hover: var(--button-gray-3);
  --color-slider-track: hsl(240, 100%, 90%);
  --color-slider-thumb: var(--color-gray-80);
  --modal-shadow:
    0px 100px 80px rgba(0, 0, 0, 0.07),
    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),
    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),
    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),
    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),
    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);
  --avatar-border-color: var(--color-gray-20);
  --sidebar-shadow:
    0px 100px 80px rgba(0, 0, 0, 0.07),
    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),
    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),
    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),
    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),
    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);
  --sidebar-border-color: var(--color-surface-high);
  --sidebar-bg-color: var(--island-bg-color);
  --library-dropdown-shadow:
    0px 15px 6px rgba(0, 0, 0, 0.01),
    0px 8px 5px rgba(0, 0, 0, 0.05),
    0px 4px 4px rgba(0, 0, 0, 0.09),
    0px 1px 2px rgba(0, 0, 0, 0.1),
    0px 0px 0px rgba(0, 0, 0, 0.1);
  --space-factor: 0.25rem;
  --text-primary-color: var(--color-on-surface);
  --color-selection: #6965db;
  --color-icon-white: #ffffff;
  --color-primary: #6965db;
  --color-primary-darker: #5b57d1;
  --color-primary-darkest: #4a47b1;
  --color-primary-light: #e3e2fe;
  --color-primary-light-darker: #d7d5ff;
  --color-primary-hover: #5753d0;
  --color-gray-10: #f5f5f5;
  --color-gray-20: #ebebeb;
  --color-gray-30: #d6d6d6;
  --color-gray-40: #b8b8b8;
  --color-gray-50: #999999;
  --color-gray-60: #7a7a7a;
  --color-gray-70: #5c5c5c;
  --color-gray-80: #3d3d3d;
  --color-gray-85: #242424;
  --color-gray-90: #1e1e1e;
  --color-gray-100: #121212;
  --color-disabled: var(--color-gray-40);
  --color-warning: #fceeca;
  --color-warning-dark: #f5c354;
  --color-warning-darker: #f3ab2c;
  --color-warning-darkest: #ec8b14;
  --color-text-warning: var(--text-primary-color);
  --color-danger: #db6965;
  --color-danger-dark: #db6965;
  --color-danger-darker: #d65550;
  --color-danger-darkest: #d1413c;
  --color-danger-text: black;
  --color-danger-background: #fff0f0;
  --color-danger-icon-background: #ffdad6;
  --color-danger-color: #700000;
  --color-danger-icon-color: #700000;
  --color-warning-background: var(--color-warning);
  --color-warning-icon-background: var(--color-warning-dark);
  --color-warning-color: var(--text-primary-color);
  --color-warning-icon-color: var(--text-primary-color);
  --color-muted: var(--color-gray-30);
  --color-muted-darker: var(--color-gray-60);
  --color-muted-darkest: var(--color-gray-100);
  --color-muted-background: var(--color-gray-80);
  --color-muted-background-darker: var(--color-gray-100);
  --color-promo: var(--color-primary);
  --color-success: #cafccc;
  --color-success-darker: #bafabc;
  --color-success-darkest: #a5eba8;
  --color-success-text: #268029;
  --color-success-contrast: #65bb6a;
  --color-success-contrast-hover: #6bcf70;
  --color-success-contrast-active: #6edf74;
  --color-logo-icon: var(--color-primary);
  --color-logo-text: #190064;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --color-surface-high: #f1f0ff;
  --color-surface-mid: #f2f2f7;
  --color-surface-low: #ececf4;
  --color-surface-lowest: #ffffff;
  --color-on-surface: #1b1b1f;
  --color-brand-hover: #5753d0;
  --color-on-primary-container: #030064;
  --color-surface-primary-container: #e0dfff;
  --color-brand-active: #4440bf;
  --color-border-outline: #767680;
  --color-border-outline-variant: #c5c5d0;
  --color-surface-primary-container: #e0dfff;
  --color-badge: #0b6513;
  --background-color-badge: #d3ffd2;
}
@media screen and (min-device-width: 1921px) {
  .excalidraw {
    --lg-button-size: 2.5rem;
    --lg-icon-size: 1.25rem;
    --default-button-size: 2.25rem;
    --default-icon-size: 1.25rem;
  }
}
.excalidraw.theme--dark.theme--dark-background-none {
  background: none;
}
.excalidraw.theme--dark {
  --theme-filter: invert(93%) hue-rotate(180deg);
  --button-destructive-bg-color: #5a0000;
  --button-destructive-color: #ffa8a8;
  --button-gray-1: #363636;
  --button-gray-2: #272727;
  --button-gray-3: #222;
  --button-special-active-bg-color: #204624;
  --dialog-border-color: var(--color-gray-80);
  --dropdown-icon: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="292.4" height="292.4" viewBox="0 0 292 292"><path fill="%23ced4da" d="M287 197L159 69c-4-3-8-5-13-5s-9 2-13 5L5 197c-3 4-5 8-5 13s2 9 5 13c4 4 8 5 13 5h256c5 0 9-1 13-5s5-8 5-13-1-9-5-13z"/></svg>');
  --focus-highlight-color: #228be6;
  --icon-green-fill-color: #69db7c;
  --default-bg-color: #121212;
  --input-bg-color: #121212;
  --input-border-color: #2e2e2e;
  --input-hover-bg-color: #181818;
  --input-label-color: #e9ecef;
  --island-bg-color: #232329;
  --keybinding-color: var(--color-gray-60);
  --link-color: #4dabf7;
  --overlay-bg-color: rgba(52, 58, 64, 0.12);
  --popup-secondary-bg-color: #222;
  --popup-text-color: #ced4da;
  --popup-text-inverted-color: #2c2c2c;
  --select-highlight-color: #4dabf7;
  --shadow-island:
    0px 0px 0.9310142993927002px 0px rgba(0, 0, 0, 0.17),
    0px 0px 3.1270833015441895px 0px rgba(0, 0, 0, 0.08),
    0px 7px 14px 0px rgba(0, 0, 0, 0.05);
  --modal-shadow:
    0px 100px 80px rgba(0, 0, 0, 0.07),
    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),
    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),
    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),
    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),
    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);
  --avatar-border-color: var(--color-gray-85);
  --scrollbar-thumb: #343a40;
  --scrollbar-thumb-hover: #495057;
  --color-slider-track: hsl(244, 23%, 39%);
  --color-selection: #3530c4;
  --color-icon-white: var(--color-gray-90);
  --color-primary: #a8a5ff;
  --color-primary-darker: #b2aeff;
  --color-primary-darkest: #beb9ff;
  --color-primary-light: #4f4d6f;
  --color-primary-light-darker: #43415e;
  --color-primary-hover: #bbb8ff;
  --color-disabled: var(--color-gray-70);
  --color-text-warning: var(--color-gray-80);
  --color-danger: #ffa8a5;
  --color-danger-dark: #672120;
  --color-danger-darker: #8f2625;
  --color-danger-darkest: #ac2b29;
  --color-danger-text: #fbcbcc;
  --color-danger-background: #fbcbcc;
  --color-danger-icon-background: #672120;
  --color-danger-color: #261919;
  --color-danger-icon-color: #fbcbcc;
  --color-warning-background: var(--color-warning);
  --color-warning-icon-background: var(--color-warning-dark);
  --color-warning-color: var(--color-gray-80);
  --color-warning-icon-color: var(--color-gray-80);
  --color-muted: var(--color-gray-80);
  --color-muted-darker: var(--color-gray-60);
  --color-muted-darkest: var(--color-gray-20);
  --color-muted-background: var(--color-gray-40);
  --color-muted-background-darker: var(--color-gray-20);
  --color-logo-text: #e2dfff;
  --color-surface-high: hsl(245, 10%, 21%);
  --color-surface-low: hsl(240, 8%, 15%);
  --color-surface-mid: hsl(240 6% 10%);
  --color-surface-lowest: hsl(0, 0%, 7%);
  --color-on-surface: #e3e3e8;
  --color-brand-hover: #bbb8ff;
  --color-on-primary-container: #e0dfff;
  --color-surface-primary-container: #403e6a;
  --color-brand-active: #d0ccff;
  --color-border-outline: #8e8d9c;
  --color-border-outline-variant: #46464f;
  --color-surface-primary-container: #403e6a;
}
:root {
  --zIndex-canvas: 1;
  --zIndex-interactiveCanvas: 2;
  --zIndex-svgLayer: 3;
  --zIndex-wysiwyg: 3;
  --zIndex-canvasButtons: 3;
  --zIndex-layerUI: 4;
  --zIndex-eyeDropperBackdrop: 5;
  --zIndex-eyeDropperPreview: 6;
  --zIndex-hyperlinkContainer: 7;
  --zIndex-modal: 1000;
  --zIndex-popup: 1001;
  --zIndex-toast: 999999;
  --sab: env(safe-area-inset-bottom);
  --sal: env(safe-area-inset-left);
  --sar: env(safe-area-inset-right);
  --sat: env(safe-area-inset-top);
}
body.excalidraw-cursor-resize,
body.excalidraw-cursor-resize a:hover,
body.excalidraw-cursor-resize * {
  cursor: ew-resize;
}
.excalidraw {
  --ui-font:
    Assistant,
    system-ui,
    BlinkMacSystemFont,
    -apple-system,
    Segoe UI,
    Roboto,
    Helvetica,
    Arial,
    sans-serif;
  font-family: var(--ui-font);
  position: relative;
  overflow: hidden;
  color: var(--text-primary-color);
  display: flex;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
  user-select: none;
}
.excalidraw button {
  cursor: pointer;
  user-select: none;
}
.excalidraw:focus {
  outline: none;
}
.excalidraw .excalidraw-link,
.excalidraw a {
  font-weight: 500;
  text-decoration: none;
  color: var(--link-color);
  user-select: none;
  cursor: pointer;
}
.excalidraw .excalidraw-link:hover,
.excalidraw a:hover {
  text-decoration: underline;
}
.excalidraw .excalidraw-link:active,
.excalidraw a:active {
  text-decoration: none;
}
.excalidraw canvas {
  touch-action: none;
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  z-index: var(--zIndex-canvas);
}
.excalidraw canvas.interactive {
  z-index: var(--zIndex-interactiveCanvas);
}
.excalidraw__canvas-wrapper,
.excalidraw__canvas.static {
  pointer-events: none;
}
.excalidraw__canvas {
  position: absolute;
}
.excalidraw__embeddable {
  width: 100%;
  height: 100%;
  border: 0;
}
.excalidraw__embeddable-container {
  position: absolute;
  z-index: 2;
  transform-origin: top left;
  pointer-events: none;
}
.excalidraw.theme--dark canvas {
  filter: var(--theme-filter);
}
.excalidraw .FixedSideContainer {
  padding-top: var(--sat, 0);
  padding-right: var(--sar, 0);
  padding-bottom: var(--sab, 0);
  padding-left: var(--sal, 0);
}
.excalidraw .panelRow {
  display: flex;
  justify-content: space-between;
}
.excalidraw .panelColumn {
  display: flex;
  flex-direction: column;
  row-gap: 0.75rem;
}
.excalidraw .panelColumn h3,
.excalidraw .panelColumn legend,
.excalidraw .panelColumn .control-label {
  margin: 0;
  margin-bottom: 0.25rem;
  font-size: 0.75rem;
  color: var(--text-primary-color);
  font-weight: 400;
  display: block;
}
.excalidraw .panelColumn .control-label input {
  display: block;
  width: 100%;
}
.excalidraw .panelColumn legend {
  padding: 0;
}
.excalidraw .panelColumn .iconSelectList {
  flex-wrap: wrap;
  position: relative;
}
.excalidraw .panelColumn .buttonList {
  flex-wrap: wrap;
  display: flex;
  column-gap: 0.5rem;
  row-gap: 0.5rem;
}
.excalidraw .panelColumn .buttonList label {
  font-size: 0.75rem;
}
.excalidraw .panelColumn .buttonList input[type=radio],
.excalidraw .panelColumn .buttonList input[type=button] {
  opacity: 0;
  position: absolute;
  pointer-events: none;
}
.excalidraw .panelColumn .buttonList .iconRow {
  margin-top: 8px;
}
.excalidraw .panelColumn fieldset {
  margin: 0;
  padding: 0;
  border: none;
}
.excalidraw .divider {
  width: 1px;
  background-color: #e9ecef;
  margin: 1px;
}
.excalidraw .welcome-screen-menu-item:focus-visible,
.excalidraw .dropdown-menu-item:focus-visible,
.excalidraw button:focus-visible,
.excalidraw .buttonList label:focus-within,
.excalidraw input:focus-visible {
  outline: transparent;
  box-shadow: 0 0 0 1px var(--color-brand-hover);
}
.excalidraw .buttonList .ToolIcon__icon {
  all: unset !important;
  display: flex !important;
}
.excalidraw .buttonList button {
  background-color: transparent;
}
.excalidraw .buttonList label,
.excalidraw .buttonList button,
.excalidraw .buttonList .zIndexButton {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.625rem;
  width: var(--button-width, var(--default-button-size));
  height: var(--button-height, var(--default-button-size));
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: var(--button-border, var(--default-border-color));
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  background-color: var(--button-bg, var(--island-bg-color));
  color: var(--button-color, var(--color-on-surface));
  font-family: var(--ui-font);
  padding: 0;
}
.excalidraw .buttonList label svg,
.excalidraw .buttonList button svg,
.excalidraw .buttonList .zIndexButton svg {
  width: var(--button-width, var(--lg-icon-size));
  height: var(--button-height, var(--lg-icon-size));
}
.excalidraw .buttonList label:hover,
.excalidraw .buttonList button:hover,
.excalidraw .buttonList .zIndexButton:hover {
  background-color: var(--button-hover-bg, var(--island-bg-color));
  border-color: var(--button-hover-border, var(--button-border, var(--default-border-color)));
  color: var(--button-hover-color, var(--button-color, var(--text-primary-color, inherit)));
}
.excalidraw .buttonList label:active,
.excalidraw .buttonList button:active,
.excalidraw .buttonList .zIndexButton:active {
  background-color: var(--button-active-bg, var(--island-bg-color));
  border-color: var(--button-active-border, var(--color-primary-darkest));
}
.excalidraw .buttonList label.active,
.excalidraw .buttonList button.active,
.excalidraw .buttonList .zIndexButton.active {
  background-color: var(--button-selected-bg, var(--color-surface-primary-container));
  border-color: var(--button-selected-border, var(--color-surface-primary-container));
}
.excalidraw .buttonList label.active:hover,
.excalidraw .buttonList button.active:hover,
.excalidraw .buttonList .zIndexButton.active:hover {
  background-color: var(--button-selected-hover-bg, var(--color-surface-primary-container));
}
.excalidraw .buttonList label.active svg,
.excalidraw .buttonList button.active svg,
.excalidraw .buttonList .zIndexButton.active svg {
  color: var(--button-color, var(--color-on-primary-container));
}
.excalidraw .buttonList label svg,
.excalidraw .buttonList button svg,
.excalidraw .buttonList .zIndexButton svg {
  width: var(--default-icon-size);
  height: var(--default-icon-size);
}
.excalidraw .App-top-bar {
  z-index: var(--zIndex-layerUI);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.excalidraw .App-bottom-bar {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  --bar-padding: calc(4 * var(--space-factor));
  padding-top: max(var(--bar-padding), var(--sat,0));
  padding-right: var(--sar, 0);
  padding-bottom: var(--sab, 0);
  padding-left: var(--sal, 0);
  z-index: 4;
  display: flex;
  align-items: flex-end;
  pointer-events: none;
}
.excalidraw .App-bottom-bar > .Island {
  width: 100%;
  max-width: 100%;
  min-width: 100%;
  box-sizing: border-box;
  max-height: 100%;
  display: flex;
  flex-direction: column;
  pointer-events: var(--ui-pointerEvents);
}
.excalidraw .App-bottom-bar > .Island .panelColumn {
  padding: 8px 8px 0 8px;
}
.excalidraw .App-toolbar {
  width: 100%;
}
.excalidraw .App-toolbar .eraser.ToolIcon:hover {
  --icon-fill-color: #fff;
  --keybinding-color: #fff;
}
.excalidraw .App-toolbar .eraser.active {
  background-color: var(--color-primary);
}
.excalidraw .App-toolbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
}
.excalidraw .App-toolbar-content .dropdown-menu--mobile {
  bottom: 55px;
  top: auto;
}
.excalidraw .App-mobile-menu {
  width: 100%;
  overflow-x: visible;
  overflow-y: auto;
  box-sizing: border-box;
  margin-bottom: var(--bar-padding);
}
.excalidraw .App-menu {
  display: grid;
  color: var(--icon-fill-color);
}
.excalidraw .shapes-section {
  display: flex;
  justify-content: center;
  pointer-events: none !important;
}
.excalidraw .shapes-section > * {
  pointer-events: var(--ui-pointerEvents);
}
.excalidraw .App-menu_top {
  grid-template-columns: 1fr 2fr 1fr;
  grid-gap: 2rem;
  align-items: flex-start;
  cursor: default;
  pointer-events: none !important;
}
.excalidraw .App-menu_top > * {
  pointer-events: var(--ui-pointerEvents);
}
@media (min-width: 1536px) {
  .excalidraw .App-menu_top {
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 3rem;
  }
}
.excalidraw .App-menu_top > *:first-child {
  justify-self: flex-start;
}
.excalidraw .App-menu_top > *:last-child {
  justify-self: flex-end;
}
.excalidraw .App-menu_bottom {
  position: absolute;
  bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  cursor: default;
  pointer-events: none !important;
  box-sizing: border-box;
  padding: 0 1rem;
}
.excalidraw .App-menu_bottom--transition-left section {
  width: 185px;
}
.excalidraw .App-menu_bottom section {
  display: flex;
}
.excalidraw .App-menu_bottom > *:first-child {
  justify-self: flex-start;
}
.excalidraw .App-menu_bottom > *:last-child {
  justify-self: flex-end;
}
.excalidraw .App-menu_left {
  grid-template-rows: 1fr auto 1fr;
  height: 100%;
}
.excalidraw .App-menu_right {
  grid-template-rows: 1fr;
  height: 100%;
}
.excalidraw .App-menu__left {
  overflow-y: auto;
  padding: 0.75rem;
  width: 12.5rem;
  box-sizing: border-box;
  position: absolute;
}
.excalidraw .dropdown-select {
  height: 1.5rem;
  padding: 0;
  padding-inline-start: 0.5rem;
  padding-inline-end: 1.5rem;
  color: var(--icon-fill-color);
  background-color: var(--button-gray-1);
  border-radius: var(--space-factor);
  border: 1px solid var(--button-gray-2);
  font-size: 0.8rem;
  font-family: inherit;
  outline: none;
  appearance: none;
  background-image: var(--dropdown-icon);
  background-repeat: no-repeat;
  background-position: right 0.7rem top 50%, 0 0;
  background-size: 0.65em auto, 100%;
}
:root[dir=rtl] .excalidraw .dropdown-select {
  background-position: left 0.7rem top 50%, 0 0;
}
.excalidraw .dropdown-select:focus {
  box-shadow: 0 0 0 2px var(--focus-highlight-color);
}
.excalidraw .dropdown-select:hover {
  background-color: var(--button-gray-2);
}
.excalidraw .dropdown-select:active {
  background-color: var(--button-gray-2);
}
.excalidraw .dropdown-select__language {
  height: 2rem;
  background-color: var(--island-bg-color);
  border-color: var(--default-border-color) !important;
  cursor: pointer;
}
.excalidraw .dropdown-select__language:hover {
  background-color: var(--island-bg-color);
}
.excalidraw .scroll-back-to-content {
  border-radius: var(--border-radius-lg);
  background-color: var(--island-bg-color);
  color: var(--icon-fill-color);
  border: 1px solid var(--default-border-color);
  padding: 10px 20px;
  position: absolute;
  left: 50%;
  bottom: 30px;
  transform: translateX(-50%);
  pointer-events: var(--ui-pointerEvents);
  font-family: inherit;
}
.excalidraw .scroll-back-to-content:hover {
  background-color: var(--button-hover-bg);
}
.excalidraw .scroll-back-to-content:active {
  border: 1px solid var(--button-active-border);
}
.excalidraw .help-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.625rem;
  width: var(--button-width, var(--default-button-size));
  height: var(--button-height, var(--default-button-size));
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: var(--button-border, var(--default-border-color));
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  background-color: var(--button-bg, var(--island-bg-color));
  color: var(--button-color, var(--color-on-surface));
  font-family: var(--ui-font);
  border: none;
  box-shadow: 0 0 0 1px var(--color-surface-lowest);
  background-color: var(--color-surface-low);
  width: var(--lg-button-size);
  height: var(--lg-button-size);
}
.excalidraw .help-icon svg {
  width: var(--button-width, var(--lg-icon-size));
  height: var(--button-height, var(--lg-icon-size));
}
.excalidraw .help-icon:hover {
  background-color: var(--button-hover-bg, var(--island-bg-color));
  border-color: var(--button-hover-border, var(--button-border, var(--default-border-color)));
  color: var(--button-hover-color, var(--button-color, var(--text-primary-color, inherit)));
}
.excalidraw .help-icon:active {
  background-color: var(--button-active-bg, var(--island-bg-color));
  border-color: var(--button-active-border, var(--color-primary-darkest));
}
.excalidraw .help-icon.active {
  background-color: var(--button-selected-bg, var(--color-surface-primary-container));
  border-color: var(--button-selected-border, var(--color-surface-primary-container));
}
.excalidraw .help-icon.active:hover {
  background-color: var(--button-selected-hover-bg, var(--color-surface-primary-container));
}
.excalidraw .help-icon.active svg {
  color: var(--button-color, var(--color-on-primary-container));
}
.excalidraw .help-icon:active {
  box-shadow: 0 0 0 1px var(--color-brand-active);
}
.excalidraw .help-icon svg {
  width: var(--lg-icon-size);
  height: var(--lg-icon-size);
}
.excalidraw .reset-zoom-button {
  font-family: var(--ui-font);
}
.excalidraw .finalize-button {
  display: grid;
  grid-auto-flow: column;
  gap: 0.4em;
  margin-top: auto;
  margin-bottom: auto;
  margin-inline-start: 0.6em;
}
.excalidraw .undo-redo-buttons,
.excalidraw .eraser-buttons {
  display: grid;
  grid-auto-flow: column;
  margin-top: auto;
  margin-bottom: auto;
  margin-inline-start: 0.6em;
}
.excalidraw--mobile.excalidraw aside {
  display: none;
}
.excalidraw--mobile.excalidraw .scroll-back-to-content {
  bottom: calc(80px + var(--sab, 0));
  z-index: -1;
}
:root[dir=rtl] .excalidraw .rtl-mirror {
  transform: scaleX(-1);
}
.excalidraw .zen-mode-visibility {
  visibility: visible;
  opacity: 1;
  height: auto;
  width: auto;
  transition: opacity 0.5s;
}
.excalidraw .zen-mode-visibility.zen-mode-visibility--hidden {
  visibility: hidden;
  opacity: 0;
  height: 0;
  width: 0;
  transition: opacity 0.5s;
}
.excalidraw .disable-pointerEvents {
  pointer-events: none !important;
}
.excalidraw.excalidraw--view-mode .App-menu {
  display: flex;
  justify-content: space-between;
}
.excalidraw input.is-redacted {
  -webkit-text-security: disc;
}
.excalidraw input[type=text],
.excalidraw textarea:not(.excalidraw-wysiwyg) {
  color: var(--text-primary-color);
  border: 1.5px solid var(--input-border-color);
  padding: 0.75rem;
  white-space: nowrap;
  border-radius: var(--space-factor);
  background-color: var(--input-bg-color);
}
.excalidraw input[type=text]:not(:focus):hover,
.excalidraw textarea:not(.excalidraw-wysiwyg):not(:focus):hover {
  border-color: var(--color-brand-hover);
}
.excalidraw input[type=text]:focus,
.excalidraw textarea:not(.excalidraw-wysiwyg):focus {
  outline: none;
  border-color: var(--color-brand-hover);
}
@media print {
  .excalidraw .App-bottom-bar,
  .excalidraw .FixedSideContainer,
  .excalidraw .layer-ui__wrapper {
    display: none;
  }
}
.excalidraw ::-webkit-scrollbar {
  width: 4px;
  height: 3px;
}
.excalidraw select::-webkit-scrollbar {
  width: 10px;
}
.excalidraw ::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 10px;
}
.excalidraw ::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}
.excalidraw ::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb);
}
.excalidraw .mobile-misc-tools-container {
  position: absolute;
  top: calc(5rem - var(--editor-container-padding));
  right: calc(var(--editor-container-padding) * -1);
  display: flex;
  flex-direction: column;
  border: 1px solid var(--sidebar-border-color);
  border-top-left-radius: var(--border-radius-lg);
  border-bottom-left-radius: var(--border-radius-lg);
  border-right: 0;
  overflow: hidden;
  background-color: var(--island-bg-color);
}
.excalidraw .mobile-misc-tools-container .ToolIcon__icon {
  width: 2rem;
  height: 2rem;
  border-radius: 0;
}
.excalidraw .mobile-misc-tools-container .default-sidebar-trigger {
  border: 0;
}
.excalidraw .App-toolbar--mobile {
  overflow: visible;
  max-width: 98vw;
}
.excalidraw .App-toolbar--mobile .ToolIcon__keybinding {
  display: none;
}
.excalidraw .UserList-Wrapper {
  margin: 0;
  padding: 0;
  border: none;
  text-align: left;
}
.excalidraw .UserList-Wrapper legend {
  display: block;
  font-size: 0.75rem;
  font-weight: 400;
  margin: 0 0 0.25rem;
  padding: 0;
}
.excalidraw .main-menu-trigger {
  border: none;
  box-shadow: 0 0 0 1px var(--color-surface-lowest);
  background-color: var(--color-surface-low);
}
.excalidraw .main-menu-trigger:active {
  box-shadow: 0 0 0 1px var(--color-brand-active);
}
.excalidraw .App-mobile-menu,
.excalidraw .App-menu__left {
  --button-border: transparent;
  --button-bg: var(--color-surface-mid);
}
.excalidraw.theme--dark.excalidraw .App-mobile-menu,
.excalidraw.theme--dark.excalidraw .App-menu__left {
  --button-hover-bg: #363541;
  --button-bg: var(--color-surface-high);
}
.excalidraw .App-menu__left .buttonList {
  padding: 0.25rem 0;
}
.excalidraw .excalidraw__paragraph {
  margin: 1rem 0;
}
.excalidraw .Modal__content .excalidraw__paragraph:first-child {
  margin-top: 0;
}
.excalidraw .Modal__content .excalidraw__paragraph + .excalidraw__paragraph {
  margin-top: 0rem;
}
.ErrorSplash.excalidraw {
  min-height: 100vh;
  padding: 20px 0;
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: text;
}
.ErrorSplash.excalidraw .ErrorSplash-messageContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background-color: #ffe3e3;
  border: 3px solid #c92a2a;
}
.ErrorSplash.excalidraw .ErrorSplash-paragraph {
  margin: 15px 0;
  max-width: 600px;
}
.ErrorSplash.excalidraw .ErrorSplash-paragraph.align-center {
  text-align: center;
}
.ErrorSplash.excalidraw .bigger,
.ErrorSplash.excalidraw .bigger button {
  font-size: 1.1em;
}
.ErrorSplash.excalidraw .smaller,
.ErrorSplash.excalidraw .smaller button {
  font-size: 0.9em;
}
.ErrorSplash.excalidraw .ErrorSplash-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.ErrorSplash.excalidraw .ErrorSplash-details textarea {
  width: 100%;
  margin: 10px 0;
  font-family: "Cascadia";
  font-size: 0.8em;
}
.excalidraw__embeddable-container .excalidraw__embeddable-container__inner {
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--embeddable-radius);
}
.excalidraw__embeddable-container .excalidraw__embeddable__outer {
  width: 100%;
  height: 100%;
}
.excalidraw__embeddable-container .excalidraw__embeddable__outer > * {
  border-radius: var(--embeddable-radius);
}
.excalidraw__embeddable-container .excalidraw__embeddable-hint {
  position: absolute;
  z-index: 1;
  background: rgba(0, 0, 0, 0.5);
  padding: 1rem 1.6rem;
  border-radius: 12px;
  color: #fff;
  font-weight: 700;
  letter-spacing: 0.6px;
  font-family: "Assistant";
}

/* fonts/fonts.css */
@font-face {
  font-family: "Assistant";
  src: url("./fonts/Assistant/Assistant-Regular.woff2") format("woff2");
  font-weight: 400;
  style: normal;
  display: swap;
}
@font-face {
  font-family: "Assistant";
  src: url("./fonts/Assistant/Assistant-Medium.woff2") format("woff2");
  font-weight: 500;
  style: normal;
  display: swap;
}
@font-face {
  font-family: "Assistant";
  src: url("./fonts/Assistant/Assistant-SemiBold.woff2") format("woff2");
  font-weight: 600;
  style: normal;
  display: swap;
}
@font-face {
  font-family: "Assistant";
  src: url("./fonts/Assistant/Assistant-Bold.woff2") format("woff2");
  font-weight: 700;
  style: normal;
  display: swap;
}

/* components/footer/FooterCenter.scss */
.footer-center {
  pointer-events: none;
  display: flex;
  width: 100%;
  justify-content: flex-start;
  margin-inline-end: 0.6rem;
}
.footer-center > * {
  pointer-events: var(--ui-pointerEvents);
}

/* components/ExcalidrawLogo.scss */
.excalidraw .ExcalidrawLogo {
  --logo-icon--xs: 2rem;
  --logo-text--xs: 1.5rem;
  --logo-icon--small: 2.5rem;
  --logo-text--small: 1.75rem;
  --logo-icon--normal: 3rem;
  --logo-text--normal: 2.2rem;
  --logo-icon--large: 90px;
  --logo-text--large: 65px;
  display: flex;
  align-items: center;
}
.excalidraw .ExcalidrawLogo svg {
  flex: 0 0 auto;
}
.excalidraw .ExcalidrawLogo .ExcalidrawLogo-icon {
  width: auto;
  color: var(--color-logo-icon);
}
.excalidraw .ExcalidrawLogo .ExcalidrawLogo-text {
  margin-left: 0.75rem;
  width: auto;
  color: var(--color-logo-text);
}
.excalidraw .ExcalidrawLogo.is-xs .ExcalidrawLogo-icon {
  height: var(--logo-icon--xs);
}
.excalidraw .ExcalidrawLogo.is-xs .ExcalidrawLogo-text {
  height: var(--logo-text--xs);
}
.excalidraw .ExcalidrawLogo.is-small .ExcalidrawLogo-icon {
  height: var(--logo-icon--small);
}
.excalidraw .ExcalidrawLogo.is-small .ExcalidrawLogo-text {
  height: var(--logo-text--small);
}
.excalidraw .ExcalidrawLogo.is-normal .ExcalidrawLogo-icon {
  height: var(--logo-icon--normal);
}
.excalidraw .ExcalidrawLogo.is-normal .ExcalidrawLogo-text {
  height: var(--logo-text--normal);
}
.excalidraw .ExcalidrawLogo.is-large .ExcalidrawLogo-icon {
  height: var(--logo-icon--large);
}
.excalidraw .ExcalidrawLogo.is-large .ExcalidrawLogo-text {
  height: var(--logo-text--large);
}

/* components/welcome-screen/WelcomeScreen.scss */
.excalidraw .excalifont {
  font-family: "Excalifont", "Xiaolai";
}
.excalidraw .welcome-screen-decor {
  pointer-events: none;
  color: var(--color-gray-40);
}
.excalidraw .welcome-screen-decor a {
  --color: var(--color-primary);
  color: var(--color);
  text-decoration: none;
  margin-bottom: -6px;
}
.excalidraw.theme--dark .welcome-screen-decor {
  color: var(--color-gray-60);
}
@media (max-height: 599px) {
  .excalidraw .welcome-screen-decor-hint {
    display: none !important;
  }
}
@media (max-width: 1024px), (max-width: 800px) {
  .excalidraw .welcome-screen-decor-hint .welcome-screen-decor--help,
  .excalidraw .welcome-screen-decor-hint .welcome-screen-decor--menu {
    display: none;
  }
}
.excalidraw .welcome-screen-decor-hint--help {
  display: flex;
  position: absolute;
  right: 0;
  bottom: 100%;
}
:root[dir=rtl] .excalidraw .welcome-screen-decor-hint--help {
  left: 0;
  right: auto;
}
.excalidraw .welcome-screen-decor-hint--help svg {
  margin-top: 0.5rem;
  width: 85px;
  height: 71px;
  transform: scaleX(-1) rotate(80deg);
}
:root[dir=rtl] .excalidraw .welcome-screen-decor-hint--help svg {
  transform: rotate(80deg);
}
.excalidraw .welcome-screen-decor-hint--toolbar {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 2.5rem;
  display: flex;
  align-items: baseline;
}
.excalidraw .welcome-screen-decor-hint--toolbar .welcome-screen-decor-hint__label {
  width: 120px;
  position: relative;
  top: -0.5rem;
}
.excalidraw .welcome-screen-decor-hint--toolbar svg {
  width: 38px;
  height: 78px;
}
:root[dir=rtl] .excalidraw .welcome-screen-decor-hint--toolbar svg {
  transform: scaleX(-1);
}
.excalidraw .welcome-screen-decor-hint--menu {
  position: absolute;
  width: 320px;
  font-size: 1rem;
  top: 100%;
  margin-top: 0.25rem;
  margin-inline-start: 0.6rem;
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
}
.excalidraw .welcome-screen-decor-hint--menu svg {
  width: 41px;
  height: 94px;
}
:root[dir=rtl] .excalidraw .welcome-screen-decor-hint--menu svg {
  transform: scaleX(-1);
}
@media (max-width: 860px) {
  .excalidraw .welcome-screen-decor-hint--menu .welcome-screen-decor-hint__label {
    max-width: 160px;
  }
}
.excalidraw .welcome-screen-center {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  justify-content: center;
  align-items: center;
  position: absolute;
  pointer-events: none;
  left: 1rem;
  top: 1rem;
  right: 1rem;
  bottom: 1rem;
}
.excalidraw .welcome-screen-center__logo {
  display: flex;
  align-items: center;
  column-gap: 0.75rem;
  font-size: 2.25rem;
}
.excalidraw .welcome-screen-center__heading {
  font-size: 1.125rem;
  text-align: center;
}
.excalidraw .welcome-screen-menu {
  display: flex;
  flex-direction: column;
  gap: 2px;
  justify-content: center;
  align-items: center;
}
.excalidraw .welcome-screen-menu-item {
  box-sizing: border-box;
  pointer-events: var(--ui-pointerEvents);
  color: var(--color-gray-50);
  font-size: 0.875rem;
  width: 100%;
  min-width: 300px;
  max-width: 400px;
  display: grid;
  align-items: center;
  justify-content: space-between;
  background: none;
  border: 1px solid transparent;
  padding: 0.75rem;
  border-radius: var(--border-radius-md);
  grid-template-columns: calc(var(--default-icon-size) + 0.5rem) 1fr 3rem;
}
.excalidraw .welcome-screen-menu-item__text {
  display: flex;
  align-items: center;
  margin-right: auto;
  text-align: left;
  column-gap: 0.5rem;
}
.excalidraw .welcome-screen-menu-item__icon {
  width: var(--default-icon-size);
  height: var(--default-icon-size);
}
.excalidraw .welcome-screen-menu-item__shortcut {
  margin-left: auto;
  color: var(--color-gray-40);
  font-size: 0.75rem;
}
.excalidraw .welcome-screen-menu-item:hover {
  text-decoration: none;
  background: var(--button-hover-bg);
}
.excalidraw .welcome-screen-menu-item:hover .welcome-screen-menu-item__shortcut,
.excalidraw .welcome-screen-menu-item:hover .welcome-screen-menu-item__icon,
.excalidraw .welcome-screen-menu-item:hover .welcome-screen-menu-item__text {
  color: var(--color-gray-100);
}
.excalidraw .welcome-screen-menu-item:active {
  background: var(--button-hover-bg);
  border-color: var(--color-brand-active);
}
.excalidraw .welcome-screen-menu-item:active .welcome-screen-menu-item__shortcut,
.excalidraw .welcome-screen-menu-item:active .welcome-screen-menu-item__icon,
.excalidraw .welcome-screen-menu-item:active .welcome-screen-menu-item__text {
  color: var(--color-gray-100);
}
.excalidraw.theme--dark .welcome-screen-menu-item {
  color: var(--color-gray-60);
}
.excalidraw.theme--dark .welcome-screen-menu-item__shortcut {
  color: var(--color-gray-60);
}
.excalidraw.theme--dark .welcome-screen-menu-item:hover {
  background-color: var(--color-surface-low);
}
.excalidraw.theme--dark .welcome-screen-menu-item:hover .welcome-screen-menu-item__icon,
.excalidraw.theme--dark .welcome-screen-menu-item:hover .welcome-screen-menu-item__shortcut,
.excalidraw.theme--dark .welcome-screen-menu-item:hover .welcome-screen-menu-item__text {
  color: var(--color-gray-10);
}
.excalidraw.theme--dark .welcome-screen-menu-item:active .welcome-screen-menu-item__icon,
.excalidraw.theme--dark .welcome-screen-menu-item:active .welcome-screen-menu-item__shortcut,
.excalidraw.theme--dark .welcome-screen-menu-item:active .welcome-screen-menu-item__text {
  color: var(--color-gray-10);
}
@media (max-height: 599px) {
  .excalidraw .welcome-screen-center {
    margin-top: 4rem;
  }
}
@media (min-height: 600px) and (max-height: 900px) {
  .excalidraw .welcome-screen-center {
    margin-top: 8rem;
  }
}
@media (max-height: 500px), (max-width: 320px) {
  .excalidraw .welcome-screen-center {
    display: none;
  }
}

/* components/live-collaboration/LiveCollaborationTrigger.scss */
.excalidraw .collab-button {
  --button-bg: var(--color-primary);
  --button-color: var(--color-surface-lowest);
  --button-border: var(--color-primary);
  --button-width: var(--lg-button-size);
  --button-height: var(--lg-button-size);
  --button-hover-bg: var(--color-primary-darker);
  --button-hover-border: var(--color-primary-darker);
  --button-active-bg: var(--color-primary-darker);
  box-shadow: 0 0 0 1px var(--color-surface-lowest);
  flex-shrink: 0;
}
.excalidraw .collab-button.active.active {
  background-color: #0fb884;
  border-color: #0fb884;
}
.excalidraw .collab-button.active.active svg {
  color: #fff;
}
.excalidraw .collab-button.active.active:hover,
.excalidraw .collab-button.active.active:active {
  background-color: #0fb884;
  border-color: #0fb884;
}
.excalidraw .CollabButton.is-collaborating {
  background-color: var(--button-special-active-bg-color);
}
.excalidraw .CollabButton.is-collaborating .ToolIcon__icon svg,
.excalidraw .CollabButton.is-collaborating .ToolIcon__label {
  color: var(--icon-green-fill-color);
}
.excalidraw .CollabButton-collaborators {
  min-width: 1em;
  min-height: 1em;
  line-height: 1;
  position: absolute;
  bottom: -5px;
  padding: 3px;
  border-radius: 50%;
  background-color: #b2f2bb;
  color: #2b8a3e;
  font-size: 0.6rem;
  font-family: "Cascadia";
}
:root[dir=ltr] .excalidraw .CollabButton-collaborators {
  right: -5px;
}
:root[dir=rtl] .excalidraw .CollabButton-collaborators {
  left: -5px;
}
/*# sourceMappingURL=index.css.map */
