"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_language_html_htmlMode_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/language/html/htmlMode.js":
/*!**********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/language/html/htmlMode.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompletionAdapter: () => (/* binding */ CompletionAdapter),\n/* harmony export */   DefinitionAdapter: () => (/* binding */ DefinitionAdapter),\n/* harmony export */   DiagnosticsAdapter: () => (/* binding */ DiagnosticsAdapter),\n/* harmony export */   DocumentColorAdapter: () => (/* binding */ DocumentColorAdapter),\n/* harmony export */   DocumentFormattingEditProvider: () => (/* binding */ DocumentFormattingEditProvider),\n/* harmony export */   DocumentHighlightAdapter: () => (/* binding */ DocumentHighlightAdapter),\n/* harmony export */   DocumentLinkAdapter: () => (/* binding */ DocumentLinkAdapter),\n/* harmony export */   DocumentRangeFormattingEditProvider: () => (/* binding */ DocumentRangeFormattingEditProvider),\n/* harmony export */   DocumentSymbolAdapter: () => (/* binding */ DocumentSymbolAdapter),\n/* harmony export */   FoldingRangeAdapter: () => (/* binding */ FoldingRangeAdapter),\n/* harmony export */   HoverAdapter: () => (/* binding */ HoverAdapter),\n/* harmony export */   ReferenceAdapter: () => (/* binding */ ReferenceAdapter),\n/* harmony export */   RenameAdapter: () => (/* binding */ RenameAdapter),\n/* harmony export */   SelectionRangeAdapter: () => (/* binding */ SelectionRangeAdapter),\n/* harmony export */   WorkerManager: () => (/* binding */ WorkerManager),\n/* harmony export */   fromPosition: () => (/* binding */ fromPosition),\n/* harmony export */   fromRange: () => (/* binding */ fromRange),\n/* harmony export */   setupMode: () => (/* binding */ setupMode),\n/* harmony export */   setupMode1: () => (/* binding */ setupMode1),\n/* harmony export */   toRange: () => (/* binding */ toRange),\n/* harmony export */   toTextEdit: () => (/* binding */ toTextEdit)\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.api.js\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/language/html/workerManager.ts\nvar STOP_WHEN_IDLE_FOR = 2 * 60 * 1e3;\nvar WorkerManager = class {\n  constructor(defaults) {\n    this._defaults = defaults;\n    this._worker = null;\n    this._client = null;\n    this._idleCheckInterval = window.setInterval(() => this._checkIfIdle(), 30 * 1e3);\n    this._lastUsedTime = 0;\n    this._configChangeListener = this._defaults.onDidChange(() => this._stopWorker());\n  }\n  _stopWorker() {\n    if (this._worker) {\n      this._worker.dispose();\n      this._worker = null;\n    }\n    this._client = null;\n  }\n  dispose() {\n    clearInterval(this._idleCheckInterval);\n    this._configChangeListener.dispose();\n    this._stopWorker();\n  }\n  _checkIfIdle() {\n    if (!this._worker) {\n      return;\n    }\n    let timePassedSinceLastUsed = Date.now() - this._lastUsedTime;\n    if (timePassedSinceLastUsed > STOP_WHEN_IDLE_FOR) {\n      this._stopWorker();\n    }\n  }\n  _getClient() {\n    this._lastUsedTime = Date.now();\n    if (!this._client) {\n      this._worker = monaco_editor_core_exports.editor.createWebWorker({\n        // module that exports the create() method and returns a `HTMLWorker` instance\n        moduleId: \"vs/language/html/htmlWorker\",\n        // passed in to the create() method\n        createData: {\n          languageSettings: this._defaults.options,\n          languageId: this._defaults.languageId\n        },\n        label: this._defaults.languageId\n      });\n      this._client = this._worker.getProxy();\n    }\n    return this._client;\n  }\n  getLanguageServiceWorker(...resources) {\n    let _client;\n    return this._getClient().then((client) => {\n      _client = client;\n    }).then((_) => {\n      if (this._worker) {\n        return this._worker.withSyncedResources(resources);\n      }\n    }).then((_) => _client);\n  }\n};\n\n// node_modules/vscode-languageserver-types/lib/esm/main.js\nvar DocumentUri;\n(function(DocumentUri2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  DocumentUri2.is = is;\n})(DocumentUri || (DocumentUri = {}));\nvar URI;\n(function(URI2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  URI2.is = is;\n})(URI || (URI = {}));\nvar integer;\n(function(integer2) {\n  integer2.MIN_VALUE = -2147483648;\n  integer2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && integer2.MIN_VALUE <= value && value <= integer2.MAX_VALUE;\n  }\n  integer2.is = is;\n})(integer || (integer = {}));\nvar uinteger;\n(function(uinteger2) {\n  uinteger2.MIN_VALUE = 0;\n  uinteger2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && uinteger2.MIN_VALUE <= value && value <= uinteger2.MAX_VALUE;\n  }\n  uinteger2.is = is;\n})(uinteger || (uinteger = {}));\nvar Position;\n(function(Position3) {\n  function create(line, character) {\n    if (line === Number.MAX_VALUE) {\n      line = uinteger.MAX_VALUE;\n    }\n    if (character === Number.MAX_VALUE) {\n      character = uinteger.MAX_VALUE;\n    }\n    return { line, character };\n  }\n  Position3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n  }\n  Position3.is = is;\n})(Position || (Position = {}));\nvar Range;\n(function(Range3) {\n  function create(one, two, three, four) {\n    if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n      return { start: Position.create(one, two), end: Position.create(three, four) };\n    } else if (Position.is(one) && Position.is(two)) {\n      return { start: one, end: two };\n    } else {\n      throw new Error(`Range#create called with invalid arguments[${one}, ${two}, ${three}, ${four}]`);\n    }\n  }\n  Range3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n  }\n  Range3.is = is;\n})(Range || (Range = {}));\nvar Location;\n(function(Location2) {\n  function create(uri, range) {\n    return { uri, range };\n  }\n  Location2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n  }\n  Location2.is = is;\n})(Location || (Location = {}));\nvar LocationLink;\n(function(LocationLink2) {\n  function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n    return { targetUri, targetRange, targetSelectionRange, originSelectionRange };\n  }\n  LocationLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri) && Range.is(candidate.targetSelectionRange) && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n  }\n  LocationLink2.is = is;\n})(LocationLink || (LocationLink = {}));\nvar Color;\n(function(Color2) {\n  function create(red, green, blue, alpha) {\n    return {\n      red,\n      green,\n      blue,\n      alpha\n    };\n  }\n  Color2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1) && Is.numberRange(candidate.green, 0, 1) && Is.numberRange(candidate.blue, 0, 1) && Is.numberRange(candidate.alpha, 0, 1);\n  }\n  Color2.is = is;\n})(Color || (Color = {}));\nvar ColorInformation;\n(function(ColorInformation2) {\n  function create(range, color) {\n    return {\n      range,\n      color\n    };\n  }\n  ColorInformation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);\n  }\n  ColorInformation2.is = is;\n})(ColorInformation || (ColorInformation = {}));\nvar ColorPresentation;\n(function(ColorPresentation2) {\n  function create(label, textEdit, additionalTextEdits) {\n    return {\n      label,\n      textEdit,\n      additionalTextEdits\n    };\n  }\n  ColorPresentation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate)) && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n  }\n  ColorPresentation2.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\nvar FoldingRangeKind;\n(function(FoldingRangeKind2) {\n  FoldingRangeKind2.Comment = \"comment\";\n  FoldingRangeKind2.Imports = \"imports\";\n  FoldingRangeKind2.Region = \"region\";\n})(FoldingRangeKind || (FoldingRangeKind = {}));\nvar FoldingRange;\n(function(FoldingRange2) {\n  function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {\n    const result = {\n      startLine,\n      endLine\n    };\n    if (Is.defined(startCharacter)) {\n      result.startCharacter = startCharacter;\n    }\n    if (Is.defined(endCharacter)) {\n      result.endCharacter = endCharacter;\n    }\n    if (Is.defined(kind)) {\n      result.kind = kind;\n    }\n    if (Is.defined(collapsedText)) {\n      result.collapsedText = collapsedText;\n    }\n    return result;\n  }\n  FoldingRange2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine) && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter)) && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter)) && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n  }\n  FoldingRange2.is = is;\n})(FoldingRange || (FoldingRange = {}));\nvar DiagnosticRelatedInformation;\n(function(DiagnosticRelatedInformation2) {\n  function create(location, message) {\n    return {\n      location,\n      message\n    };\n  }\n  DiagnosticRelatedInformation2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n  }\n  DiagnosticRelatedInformation2.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\nvar DiagnosticSeverity;\n(function(DiagnosticSeverity2) {\n  DiagnosticSeverity2.Error = 1;\n  DiagnosticSeverity2.Warning = 2;\n  DiagnosticSeverity2.Information = 3;\n  DiagnosticSeverity2.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\nvar DiagnosticTag;\n(function(DiagnosticTag2) {\n  DiagnosticTag2.Unnecessary = 1;\n  DiagnosticTag2.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\nvar CodeDescription;\n(function(CodeDescription2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.href);\n  }\n  CodeDescription2.is = is;\n})(CodeDescription || (CodeDescription = {}));\nvar Diagnostic;\n(function(Diagnostic2) {\n  function create(range, message, severity, code, source, relatedInformation) {\n    let result = { range, message };\n    if (Is.defined(severity)) {\n      result.severity = severity;\n    }\n    if (Is.defined(code)) {\n      result.code = code;\n    }\n    if (Is.defined(source)) {\n      result.source = source;\n    }\n    if (Is.defined(relatedInformation)) {\n      result.relatedInformation = relatedInformation;\n    }\n    return result;\n  }\n  Diagnostic2.create = create;\n  function is(value) {\n    var _a;\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && Is.string(candidate.message) && (Is.number(candidate.severity) || Is.undefined(candidate.severity)) && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code)) && (Is.undefined(candidate.codeDescription) || Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)) && (Is.string(candidate.source) || Is.undefined(candidate.source)) && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n  }\n  Diagnostic2.is = is;\n})(Diagnostic || (Diagnostic = {}));\nvar Command;\n(function(Command2) {\n  function create(title, command, ...args) {\n    let result = { title, command };\n    if (Is.defined(args) && args.length > 0) {\n      result.arguments = args;\n    }\n    return result;\n  }\n  Command2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n  }\n  Command2.is = is;\n})(Command || (Command = {}));\nvar TextEdit;\n(function(TextEdit2) {\n  function replace(range, newText) {\n    return { range, newText };\n  }\n  TextEdit2.replace = replace;\n  function insert(position, newText) {\n    return { range: { start: position, end: position }, newText };\n  }\n  TextEdit2.insert = insert;\n  function del(range) {\n    return { range, newText: \"\" };\n  }\n  TextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.newText) && Range.is(candidate.range);\n  }\n  TextEdit2.is = is;\n})(TextEdit || (TextEdit = {}));\nvar ChangeAnnotation;\n(function(ChangeAnnotation2) {\n  function create(label, needsConfirmation, description) {\n    const result = { label };\n    if (needsConfirmation !== void 0) {\n      result.needsConfirmation = needsConfirmation;\n    }\n    if (description !== void 0) {\n      result.description = description;\n    }\n    return result;\n  }\n  ChangeAnnotation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  ChangeAnnotation2.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nvar ChangeAnnotationIdentifier;\n(function(ChangeAnnotationIdentifier2) {\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate);\n  }\n  ChangeAnnotationIdentifier2.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nvar AnnotatedTextEdit;\n(function(AnnotatedTextEdit2) {\n  function replace(range, newText, annotation) {\n    return { range, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.replace = replace;\n  function insert(position, newText, annotation) {\n    return { range: { start: position, end: position }, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.insert = insert;\n  function del(range, annotation) {\n    return { range, newText: \"\", annotationId: annotation };\n  }\n  AnnotatedTextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  AnnotatedTextEdit2.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\nvar TextDocumentEdit;\n(function(TextDocumentEdit2) {\n  function create(textDocument, edits) {\n    return { textDocument, edits };\n  }\n  TextDocumentEdit2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument) && Array.isArray(candidate.edits);\n  }\n  TextDocumentEdit2.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nvar CreateFile;\n(function(CreateFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"create\",\n      uri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  CreateFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"create\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  CreateFile2.is = is;\n})(CreateFile || (CreateFile = {}));\nvar RenameFile;\n(function(RenameFile2) {\n  function create(oldUri, newUri, options, annotation) {\n    let result = {\n      kind: \"rename\",\n      oldUri,\n      newUri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  RenameFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"rename\" && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  RenameFile2.is = is;\n})(RenameFile || (RenameFile = {}));\nvar DeleteFile;\n(function(DeleteFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"delete\",\n      uri\n    };\n    if (options !== void 0 && (options.recursive !== void 0 || options.ignoreIfNotExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  DeleteFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"delete\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.recursive === void 0 || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === void 0 || Is.boolean(candidate.options.ignoreIfNotExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  DeleteFile2.is = is;\n})(DeleteFile || (DeleteFile = {}));\nvar WorkspaceEdit;\n(function(WorkspaceEdit2) {\n  function is(value) {\n    let candidate = value;\n    return candidate && (candidate.changes !== void 0 || candidate.documentChanges !== void 0) && (candidate.documentChanges === void 0 || candidate.documentChanges.every((change) => {\n      if (Is.string(change.kind)) {\n        return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n      } else {\n        return TextDocumentEdit.is(change);\n      }\n    }));\n  }\n  WorkspaceEdit2.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nvar TextDocumentIdentifier;\n(function(TextDocumentIdentifier2) {\n  function create(uri) {\n    return { uri };\n  }\n  TextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri);\n  }\n  TextDocumentIdentifier2.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\nvar VersionedTextDocumentIdentifier;\n(function(VersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  VersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n  }\n  VersionedTextDocumentIdentifier2.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\nvar OptionalVersionedTextDocumentIdentifier;\n(function(OptionalVersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  OptionalVersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n  }\n  OptionalVersionedTextDocumentIdentifier2.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\nvar TextDocumentItem;\n(function(TextDocumentItem2) {\n  function create(uri, languageId, version, text) {\n    return { uri, languageId, version, text };\n  }\n  TextDocumentItem2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n  }\n  TextDocumentItem2.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\nvar MarkupKind;\n(function(MarkupKind2) {\n  MarkupKind2.PlainText = \"plaintext\";\n  MarkupKind2.Markdown = \"markdown\";\n  function is(value) {\n    const candidate = value;\n    return candidate === MarkupKind2.PlainText || candidate === MarkupKind2.Markdown;\n  }\n  MarkupKind2.is = is;\n})(MarkupKind || (MarkupKind = {}));\nvar MarkupContent;\n(function(MarkupContent2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n  }\n  MarkupContent2.is = is;\n})(MarkupContent || (MarkupContent = {}));\nvar CompletionItemKind;\n(function(CompletionItemKind2) {\n  CompletionItemKind2.Text = 1;\n  CompletionItemKind2.Method = 2;\n  CompletionItemKind2.Function = 3;\n  CompletionItemKind2.Constructor = 4;\n  CompletionItemKind2.Field = 5;\n  CompletionItemKind2.Variable = 6;\n  CompletionItemKind2.Class = 7;\n  CompletionItemKind2.Interface = 8;\n  CompletionItemKind2.Module = 9;\n  CompletionItemKind2.Property = 10;\n  CompletionItemKind2.Unit = 11;\n  CompletionItemKind2.Value = 12;\n  CompletionItemKind2.Enum = 13;\n  CompletionItemKind2.Keyword = 14;\n  CompletionItemKind2.Snippet = 15;\n  CompletionItemKind2.Color = 16;\n  CompletionItemKind2.File = 17;\n  CompletionItemKind2.Reference = 18;\n  CompletionItemKind2.Folder = 19;\n  CompletionItemKind2.EnumMember = 20;\n  CompletionItemKind2.Constant = 21;\n  CompletionItemKind2.Struct = 22;\n  CompletionItemKind2.Event = 23;\n  CompletionItemKind2.Operator = 24;\n  CompletionItemKind2.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\nvar InsertTextFormat;\n(function(InsertTextFormat2) {\n  InsertTextFormat2.PlainText = 1;\n  InsertTextFormat2.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\nvar CompletionItemTag;\n(function(CompletionItemTag2) {\n  CompletionItemTag2.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\nvar InsertReplaceEdit;\n(function(InsertReplaceEdit2) {\n  function create(newText, insert, replace) {\n    return { newText, insert, replace };\n  }\n  InsertReplaceEdit2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n  }\n  InsertReplaceEdit2.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\nvar InsertTextMode;\n(function(InsertTextMode2) {\n  InsertTextMode2.asIs = 1;\n  InsertTextMode2.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nvar CompletionItemLabelDetails;\n(function(CompletionItemLabelDetails2) {\n  function is(value) {\n    const candidate = value;\n    return candidate && (Is.string(candidate.detail) || candidate.detail === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  CompletionItemLabelDetails2.is = is;\n})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));\nvar CompletionItem;\n(function(CompletionItem2) {\n  function create(label) {\n    return { label };\n  }\n  CompletionItem2.create = create;\n})(CompletionItem || (CompletionItem = {}));\nvar CompletionList;\n(function(CompletionList2) {\n  function create(items, isIncomplete) {\n    return { items: items ? items : [], isIncomplete: !!isIncomplete };\n  }\n  CompletionList2.create = create;\n})(CompletionList || (CompletionList = {}));\nvar MarkedString;\n(function(MarkedString2) {\n  function fromPlainText(plainText) {\n    return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\");\n  }\n  MarkedString2.fromPlainText = fromPlainText;\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate) || Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value);\n  }\n  MarkedString2.is = is;\n})(MarkedString || (MarkedString = {}));\nvar Hover;\n(function(Hover2) {\n  function is(value) {\n    let candidate = value;\n    return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) || MarkedString.is(candidate.contents) || Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === void 0 || Range.is(value.range));\n  }\n  Hover2.is = is;\n})(Hover || (Hover = {}));\nvar ParameterInformation;\n(function(ParameterInformation2) {\n  function create(label, documentation) {\n    return documentation ? { label, documentation } : { label };\n  }\n  ParameterInformation2.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\nvar SignatureInformation;\n(function(SignatureInformation2) {\n  function create(label, documentation, ...parameters) {\n    let result = { label };\n    if (Is.defined(documentation)) {\n      result.documentation = documentation;\n    }\n    if (Is.defined(parameters)) {\n      result.parameters = parameters;\n    } else {\n      result.parameters = [];\n    }\n    return result;\n  }\n  SignatureInformation2.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\nvar DocumentHighlightKind;\n(function(DocumentHighlightKind2) {\n  DocumentHighlightKind2.Text = 1;\n  DocumentHighlightKind2.Read = 2;\n  DocumentHighlightKind2.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\nvar DocumentHighlight;\n(function(DocumentHighlight2) {\n  function create(range, kind) {\n    let result = { range };\n    if (Is.number(kind)) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  DocumentHighlight2.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\nvar SymbolKind;\n(function(SymbolKind2) {\n  SymbolKind2.File = 1;\n  SymbolKind2.Module = 2;\n  SymbolKind2.Namespace = 3;\n  SymbolKind2.Package = 4;\n  SymbolKind2.Class = 5;\n  SymbolKind2.Method = 6;\n  SymbolKind2.Property = 7;\n  SymbolKind2.Field = 8;\n  SymbolKind2.Constructor = 9;\n  SymbolKind2.Enum = 10;\n  SymbolKind2.Interface = 11;\n  SymbolKind2.Function = 12;\n  SymbolKind2.Variable = 13;\n  SymbolKind2.Constant = 14;\n  SymbolKind2.String = 15;\n  SymbolKind2.Number = 16;\n  SymbolKind2.Boolean = 17;\n  SymbolKind2.Array = 18;\n  SymbolKind2.Object = 19;\n  SymbolKind2.Key = 20;\n  SymbolKind2.Null = 21;\n  SymbolKind2.EnumMember = 22;\n  SymbolKind2.Struct = 23;\n  SymbolKind2.Event = 24;\n  SymbolKind2.Operator = 25;\n  SymbolKind2.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\nvar SymbolTag;\n(function(SymbolTag2) {\n  SymbolTag2.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nvar SymbolInformation;\n(function(SymbolInformation2) {\n  function create(name, kind, range, uri, containerName) {\n    let result = {\n      name,\n      kind,\n      location: { uri, range }\n    };\n    if (containerName) {\n      result.containerName = containerName;\n    }\n    return result;\n  }\n  SymbolInformation2.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nvar WorkspaceSymbol;\n(function(WorkspaceSymbol2) {\n  function create(name, kind, uri, range) {\n    return range !== void 0 ? { name, kind, location: { uri, range } } : { name, kind, location: { uri } };\n  }\n  WorkspaceSymbol2.create = create;\n})(WorkspaceSymbol || (WorkspaceSymbol = {}));\nvar DocumentSymbol;\n(function(DocumentSymbol2) {\n  function create(name, detail, kind, range, selectionRange, children) {\n    let result = {\n      name,\n      detail,\n      kind,\n      range,\n      selectionRange\n    };\n    if (children !== void 0) {\n      result.children = children;\n    }\n    return result;\n  }\n  DocumentSymbol2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.name) && Is.number(candidate.kind) && Range.is(candidate.range) && Range.is(candidate.selectionRange) && (candidate.detail === void 0 || Is.string(candidate.detail)) && (candidate.deprecated === void 0 || Is.boolean(candidate.deprecated)) && (candidate.children === void 0 || Array.isArray(candidate.children)) && (candidate.tags === void 0 || Array.isArray(candidate.tags));\n  }\n  DocumentSymbol2.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\nvar CodeActionKind;\n(function(CodeActionKind2) {\n  CodeActionKind2.Empty = \"\";\n  CodeActionKind2.QuickFix = \"quickfix\";\n  CodeActionKind2.Refactor = \"refactor\";\n  CodeActionKind2.RefactorExtract = \"refactor.extract\";\n  CodeActionKind2.RefactorInline = \"refactor.inline\";\n  CodeActionKind2.RefactorRewrite = \"refactor.rewrite\";\n  CodeActionKind2.Source = \"source\";\n  CodeActionKind2.SourceOrganizeImports = \"source.organizeImports\";\n  CodeActionKind2.SourceFixAll = \"source.fixAll\";\n})(CodeActionKind || (CodeActionKind = {}));\nvar CodeActionTriggerKind;\n(function(CodeActionTriggerKind2) {\n  CodeActionTriggerKind2.Invoked = 1;\n  CodeActionTriggerKind2.Automatic = 2;\n})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));\nvar CodeActionContext;\n(function(CodeActionContext2) {\n  function create(diagnostics, only, triggerKind) {\n    let result = { diagnostics };\n    if (only !== void 0 && only !== null) {\n      result.only = only;\n    }\n    if (triggerKind !== void 0 && triggerKind !== null) {\n      result.triggerKind = triggerKind;\n    }\n    return result;\n  }\n  CodeActionContext2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === void 0 || Is.typedArray(candidate.only, Is.string)) && (candidate.triggerKind === void 0 || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);\n  }\n  CodeActionContext2.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nvar CodeAction;\n(function(CodeAction2) {\n  function create(title, kindOrCommandOrEdit, kind) {\n    let result = { title };\n    let checkKind = true;\n    if (typeof kindOrCommandOrEdit === \"string\") {\n      checkKind = false;\n      result.kind = kindOrCommandOrEdit;\n    } else if (Command.is(kindOrCommandOrEdit)) {\n      result.command = kindOrCommandOrEdit;\n    } else {\n      result.edit = kindOrCommandOrEdit;\n    }\n    if (checkKind && kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  CodeAction2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.title) && (candidate.diagnostics === void 0 || Is.typedArray(candidate.diagnostics, Diagnostic.is)) && (candidate.kind === void 0 || Is.string(candidate.kind)) && (candidate.edit !== void 0 || candidate.command !== void 0) && (candidate.command === void 0 || Command.is(candidate.command)) && (candidate.isPreferred === void 0 || Is.boolean(candidate.isPreferred)) && (candidate.edit === void 0 || WorkspaceEdit.is(candidate.edit));\n  }\n  CodeAction2.is = is;\n})(CodeAction || (CodeAction = {}));\nvar CodeLens;\n(function(CodeLens2) {\n  function create(range, data) {\n    let result = { range };\n    if (Is.defined(data)) {\n      result.data = data;\n    }\n    return result;\n  }\n  CodeLens2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n  }\n  CodeLens2.is = is;\n})(CodeLens || (CodeLens = {}));\nvar FormattingOptions;\n(function(FormattingOptions2) {\n  function create(tabSize, insertSpaces) {\n    return { tabSize, insertSpaces };\n  }\n  FormattingOptions2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n  }\n  FormattingOptions2.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\nvar DocumentLink;\n(function(DocumentLink2) {\n  function create(range, target, data) {\n    return { range, target, data };\n  }\n  DocumentLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n  }\n  DocumentLink2.is = is;\n})(DocumentLink || (DocumentLink = {}));\nvar SelectionRange;\n(function(SelectionRange2) {\n  function create(range, parent) {\n    return { range, parent };\n  }\n  SelectionRange2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === void 0 || SelectionRange2.is(candidate.parent));\n  }\n  SelectionRange2.is = is;\n})(SelectionRange || (SelectionRange = {}));\nvar SemanticTokenTypes;\n(function(SemanticTokenTypes2) {\n  SemanticTokenTypes2[\"namespace\"] = \"namespace\";\n  SemanticTokenTypes2[\"type\"] = \"type\";\n  SemanticTokenTypes2[\"class\"] = \"class\";\n  SemanticTokenTypes2[\"enum\"] = \"enum\";\n  SemanticTokenTypes2[\"interface\"] = \"interface\";\n  SemanticTokenTypes2[\"struct\"] = \"struct\";\n  SemanticTokenTypes2[\"typeParameter\"] = \"typeParameter\";\n  SemanticTokenTypes2[\"parameter\"] = \"parameter\";\n  SemanticTokenTypes2[\"variable\"] = \"variable\";\n  SemanticTokenTypes2[\"property\"] = \"property\";\n  SemanticTokenTypes2[\"enumMember\"] = \"enumMember\";\n  SemanticTokenTypes2[\"event\"] = \"event\";\n  SemanticTokenTypes2[\"function\"] = \"function\";\n  SemanticTokenTypes2[\"method\"] = \"method\";\n  SemanticTokenTypes2[\"macro\"] = \"macro\";\n  SemanticTokenTypes2[\"keyword\"] = \"keyword\";\n  SemanticTokenTypes2[\"modifier\"] = \"modifier\";\n  SemanticTokenTypes2[\"comment\"] = \"comment\";\n  SemanticTokenTypes2[\"string\"] = \"string\";\n  SemanticTokenTypes2[\"number\"] = \"number\";\n  SemanticTokenTypes2[\"regexp\"] = \"regexp\";\n  SemanticTokenTypes2[\"operator\"] = \"operator\";\n  SemanticTokenTypes2[\"decorator\"] = \"decorator\";\n})(SemanticTokenTypes || (SemanticTokenTypes = {}));\nvar SemanticTokenModifiers;\n(function(SemanticTokenModifiers2) {\n  SemanticTokenModifiers2[\"declaration\"] = \"declaration\";\n  SemanticTokenModifiers2[\"definition\"] = \"definition\";\n  SemanticTokenModifiers2[\"readonly\"] = \"readonly\";\n  SemanticTokenModifiers2[\"static\"] = \"static\";\n  SemanticTokenModifiers2[\"deprecated\"] = \"deprecated\";\n  SemanticTokenModifiers2[\"abstract\"] = \"abstract\";\n  SemanticTokenModifiers2[\"async\"] = \"async\";\n  SemanticTokenModifiers2[\"modification\"] = \"modification\";\n  SemanticTokenModifiers2[\"documentation\"] = \"documentation\";\n  SemanticTokenModifiers2[\"defaultLibrary\"] = \"defaultLibrary\";\n})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));\nvar SemanticTokens;\n(function(SemanticTokens2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.resultId === void 0 || typeof candidate.resultId === \"string\") && Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === \"number\");\n  }\n  SemanticTokens2.is = is;\n})(SemanticTokens || (SemanticTokens = {}));\nvar InlineValueText;\n(function(InlineValueText2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  InlineValueText2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);\n  }\n  InlineValueText2.is = is;\n})(InlineValueText || (InlineValueText = {}));\nvar InlineValueVariableLookup;\n(function(InlineValueVariableLookup2) {\n  function create(range, variableName, caseSensitiveLookup) {\n    return { range, variableName, caseSensitiveLookup };\n  }\n  InlineValueVariableLookup2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup) && (Is.string(candidate.variableName) || candidate.variableName === void 0);\n  }\n  InlineValueVariableLookup2.is = is;\n})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));\nvar InlineValueEvaluatableExpression;\n(function(InlineValueEvaluatableExpression2) {\n  function create(range, expression) {\n    return { range, expression };\n  }\n  InlineValueEvaluatableExpression2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && (Is.string(candidate.expression) || candidate.expression === void 0);\n  }\n  InlineValueEvaluatableExpression2.is = is;\n})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));\nvar InlineValueContext;\n(function(InlineValueContext2) {\n  function create(frameId, stoppedLocation) {\n    return { frameId, stoppedLocation };\n  }\n  InlineValueContext2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.defined(candidate) && Range.is(value.stoppedLocation);\n  }\n  InlineValueContext2.is = is;\n})(InlineValueContext || (InlineValueContext = {}));\nvar InlayHintKind;\n(function(InlayHintKind2) {\n  InlayHintKind2.Type = 1;\n  InlayHintKind2.Parameter = 2;\n  function is(value) {\n    return value === 1 || value === 2;\n  }\n  InlayHintKind2.is = is;\n})(InlayHintKind || (InlayHintKind = {}));\nvar InlayHintLabelPart;\n(function(InlayHintLabelPart2) {\n  function create(value) {\n    return { value };\n  }\n  InlayHintLabelPart2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.location === void 0 || Location.is(candidate.location)) && (candidate.command === void 0 || Command.is(candidate.command));\n  }\n  InlayHintLabelPart2.is = is;\n})(InlayHintLabelPart || (InlayHintLabelPart = {}));\nvar InlayHint;\n(function(InlayHint2) {\n  function create(position, label, kind) {\n    const result = { position, label };\n    if (kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  InlayHint2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.position) && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is)) && (candidate.kind === void 0 || InlayHintKind.is(candidate.kind)) && candidate.textEdits === void 0 || Is.typedArray(candidate.textEdits, TextEdit.is) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.paddingLeft === void 0 || Is.boolean(candidate.paddingLeft)) && (candidate.paddingRight === void 0 || Is.boolean(candidate.paddingRight));\n  }\n  InlayHint2.is = is;\n})(InlayHint || (InlayHint = {}));\nvar StringValue;\n(function(StringValue2) {\n  function createSnippet(value) {\n    return { kind: \"snippet\", value };\n  }\n  StringValue2.createSnippet = createSnippet;\n})(StringValue || (StringValue = {}));\nvar InlineCompletionItem;\n(function(InlineCompletionItem2) {\n  function create(insertText, filterText, range, command) {\n    return { insertText, filterText, range, command };\n  }\n  InlineCompletionItem2.create = create;\n})(InlineCompletionItem || (InlineCompletionItem = {}));\nvar InlineCompletionList;\n(function(InlineCompletionList2) {\n  function create(items) {\n    return { items };\n  }\n  InlineCompletionList2.create = create;\n})(InlineCompletionList || (InlineCompletionList = {}));\nvar InlineCompletionTriggerKind;\n(function(InlineCompletionTriggerKind2) {\n  InlineCompletionTriggerKind2.Invoked = 0;\n  InlineCompletionTriggerKind2.Automatic = 1;\n})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));\nvar SelectedCompletionInfo;\n(function(SelectedCompletionInfo2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  SelectedCompletionInfo2.create = create;\n})(SelectedCompletionInfo || (SelectedCompletionInfo = {}));\nvar InlineCompletionContext;\n(function(InlineCompletionContext2) {\n  function create(triggerKind, selectedCompletionInfo) {\n    return { triggerKind, selectedCompletionInfo };\n  }\n  InlineCompletionContext2.create = create;\n})(InlineCompletionContext || (InlineCompletionContext = {}));\nvar WorkspaceFolder;\n(function(WorkspaceFolder2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);\n  }\n  WorkspaceFolder2.is = is;\n})(WorkspaceFolder || (WorkspaceFolder = {}));\nvar TextDocument;\n(function(TextDocument2) {\n  function create(uri, languageId, version, content) {\n    return new FullTextDocument(uri, languageId, version, content);\n  }\n  TextDocument2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount) && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n  }\n  TextDocument2.is = is;\n  function applyEdits(document, edits) {\n    let text = document.getText();\n    let sortedEdits = mergeSort(edits, (a, b) => {\n      let diff = a.range.start.line - b.range.start.line;\n      if (diff === 0) {\n        return a.range.start.character - b.range.start.character;\n      }\n      return diff;\n    });\n    let lastModifiedOffset = text.length;\n    for (let i = sortedEdits.length - 1; i >= 0; i--) {\n      let e = sortedEdits[i];\n      let startOffset = document.offsetAt(e.range.start);\n      let endOffset = document.offsetAt(e.range.end);\n      if (endOffset <= lastModifiedOffset) {\n        text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n      } else {\n        throw new Error(\"Overlapping edit\");\n      }\n      lastModifiedOffset = startOffset;\n    }\n    return text;\n  }\n  TextDocument2.applyEdits = applyEdits;\n  function mergeSort(data, compare) {\n    if (data.length <= 1) {\n      return data;\n    }\n    const p = data.length / 2 | 0;\n    const left = data.slice(0, p);\n    const right = data.slice(p);\n    mergeSort(left, compare);\n    mergeSort(right, compare);\n    let leftIdx = 0;\n    let rightIdx = 0;\n    let i = 0;\n    while (leftIdx < left.length && rightIdx < right.length) {\n      let ret = compare(left[leftIdx], right[rightIdx]);\n      if (ret <= 0) {\n        data[i++] = left[leftIdx++];\n      } else {\n        data[i++] = right[rightIdx++];\n      }\n    }\n    while (leftIdx < left.length) {\n      data[i++] = left[leftIdx++];\n    }\n    while (rightIdx < right.length) {\n      data[i++] = right[rightIdx++];\n    }\n    return data;\n  }\n})(TextDocument || (TextDocument = {}));\nvar FullTextDocument = class {\n  constructor(uri, languageId, version, content) {\n    this._uri = uri;\n    this._languageId = languageId;\n    this._version = version;\n    this._content = content;\n    this._lineOffsets = void 0;\n  }\n  get uri() {\n    return this._uri;\n  }\n  get languageId() {\n    return this._languageId;\n  }\n  get version() {\n    return this._version;\n  }\n  getText(range) {\n    if (range) {\n      let start = this.offsetAt(range.start);\n      let end = this.offsetAt(range.end);\n      return this._content.substring(start, end);\n    }\n    return this._content;\n  }\n  update(event, version) {\n    this._content = event.text;\n    this._version = version;\n    this._lineOffsets = void 0;\n  }\n  getLineOffsets() {\n    if (this._lineOffsets === void 0) {\n      let lineOffsets = [];\n      let text = this._content;\n      let isLineStart = true;\n      for (let i = 0; i < text.length; i++) {\n        if (isLineStart) {\n          lineOffsets.push(i);\n          isLineStart = false;\n        }\n        let ch = text.charAt(i);\n        isLineStart = ch === \"\\r\" || ch === \"\\n\";\n        if (ch === \"\\r\" && i + 1 < text.length && text.charAt(i + 1) === \"\\n\") {\n          i++;\n        }\n      }\n      if (isLineStart && text.length > 0) {\n        lineOffsets.push(text.length);\n      }\n      this._lineOffsets = lineOffsets;\n    }\n    return this._lineOffsets;\n  }\n  positionAt(offset) {\n    offset = Math.max(Math.min(offset, this._content.length), 0);\n    let lineOffsets = this.getLineOffsets();\n    let low = 0, high = lineOffsets.length;\n    if (high === 0) {\n      return Position.create(0, offset);\n    }\n    while (low < high) {\n      let mid = Math.floor((low + high) / 2);\n      if (lineOffsets[mid] > offset) {\n        high = mid;\n      } else {\n        low = mid + 1;\n      }\n    }\n    let line = low - 1;\n    return Position.create(line, offset - lineOffsets[line]);\n  }\n  offsetAt(position) {\n    let lineOffsets = this.getLineOffsets();\n    if (position.line >= lineOffsets.length) {\n      return this._content.length;\n    } else if (position.line < 0) {\n      return 0;\n    }\n    let lineOffset = lineOffsets[position.line];\n    let nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;\n    return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n  }\n  get lineCount() {\n    return this.getLineOffsets().length;\n  }\n};\nvar Is;\n(function(Is2) {\n  const toString = Object.prototype.toString;\n  function defined(value) {\n    return typeof value !== \"undefined\";\n  }\n  Is2.defined = defined;\n  function undefined2(value) {\n    return typeof value === \"undefined\";\n  }\n  Is2.undefined = undefined2;\n  function boolean(value) {\n    return value === true || value === false;\n  }\n  Is2.boolean = boolean;\n  function string(value) {\n    return toString.call(value) === \"[object String]\";\n  }\n  Is2.string = string;\n  function number(value) {\n    return toString.call(value) === \"[object Number]\";\n  }\n  Is2.number = number;\n  function numberRange(value, min, max) {\n    return toString.call(value) === \"[object Number]\" && min <= value && value <= max;\n  }\n  Is2.numberRange = numberRange;\n  function integer2(value) {\n    return toString.call(value) === \"[object Number]\" && -2147483648 <= value && value <= 2147483647;\n  }\n  Is2.integer = integer2;\n  function uinteger2(value) {\n    return toString.call(value) === \"[object Number]\" && 0 <= value && value <= 2147483647;\n  }\n  Is2.uinteger = uinteger2;\n  function func(value) {\n    return toString.call(value) === \"[object Function]\";\n  }\n  Is2.func = func;\n  function objectLiteral(value) {\n    return value !== null && typeof value === \"object\";\n  }\n  Is2.objectLiteral = objectLiteral;\n  function typedArray(value, check) {\n    return Array.isArray(value) && value.every(check);\n  }\n  Is2.typedArray = typedArray;\n})(Is || (Is = {}));\n\n// src/language/common/lspLanguageFeatures.ts\nvar DiagnosticsAdapter = class {\n  constructor(_languageId, _worker, configChangeEvent) {\n    this._languageId = _languageId;\n    this._worker = _worker;\n    this._disposables = [];\n    this._listener = /* @__PURE__ */ Object.create(null);\n    const onModelAdd = (model) => {\n      let modeId = model.getLanguageId();\n      if (modeId !== this._languageId) {\n        return;\n      }\n      let handle;\n      this._listener[model.uri.toString()] = model.onDidChangeContent(() => {\n        window.clearTimeout(handle);\n        handle = window.setTimeout(() => this._doValidate(model.uri, modeId), 500);\n      });\n      this._doValidate(model.uri, modeId);\n    };\n    const onModelRemoved = (model) => {\n      monaco_editor_core_exports.editor.setModelMarkers(model, this._languageId, []);\n      let uriStr = model.uri.toString();\n      let listener = this._listener[uriStr];\n      if (listener) {\n        listener.dispose();\n        delete this._listener[uriStr];\n      }\n    };\n    this._disposables.push(monaco_editor_core_exports.editor.onDidCreateModel(onModelAdd));\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel(onModelRemoved));\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        onModelRemoved(event.model);\n        onModelAdd(event.model);\n      })\n    );\n    this._disposables.push(\n      configChangeEvent((_) => {\n        monaco_editor_core_exports.editor.getModels().forEach((model) => {\n          if (model.getLanguageId() === this._languageId) {\n            onModelRemoved(model);\n            onModelAdd(model);\n          }\n        });\n      })\n    );\n    this._disposables.push({\n      dispose: () => {\n        monaco_editor_core_exports.editor.getModels().forEach(onModelRemoved);\n        for (let key in this._listener) {\n          this._listener[key].dispose();\n        }\n      }\n    });\n    monaco_editor_core_exports.editor.getModels().forEach(onModelAdd);\n  }\n  dispose() {\n    this._disposables.forEach((d) => d && d.dispose());\n    this._disposables.length = 0;\n  }\n  _doValidate(resource, languageId) {\n    this._worker(resource).then((worker) => {\n      return worker.doValidation(resource.toString());\n    }).then((diagnostics) => {\n      const markers = diagnostics.map((d) => toDiagnostics(resource, d));\n      let model = monaco_editor_core_exports.editor.getModel(resource);\n      if (model && model.getLanguageId() === languageId) {\n        monaco_editor_core_exports.editor.setModelMarkers(model, languageId, markers);\n      }\n    }).then(void 0, (err) => {\n      console.error(err);\n    });\n  }\n};\nfunction toSeverity(lsSeverity) {\n  switch (lsSeverity) {\n    case DiagnosticSeverity.Error:\n      return monaco_editor_core_exports.MarkerSeverity.Error;\n    case DiagnosticSeverity.Warning:\n      return monaco_editor_core_exports.MarkerSeverity.Warning;\n    case DiagnosticSeverity.Information:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n    case DiagnosticSeverity.Hint:\n      return monaco_editor_core_exports.MarkerSeverity.Hint;\n    default:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n  }\n}\nfunction toDiagnostics(resource, diag) {\n  let code = typeof diag.code === \"number\" ? String(diag.code) : diag.code;\n  return {\n    severity: toSeverity(diag.severity),\n    startLineNumber: diag.range.start.line + 1,\n    startColumn: diag.range.start.character + 1,\n    endLineNumber: diag.range.end.line + 1,\n    endColumn: diag.range.end.character + 1,\n    message: diag.message,\n    code,\n    source: diag.source\n  };\n}\nvar CompletionAdapter = class {\n  constructor(_worker, _triggerCharacters) {\n    this._worker = _worker;\n    this._triggerCharacters = _triggerCharacters;\n  }\n  get triggerCharacters() {\n    return this._triggerCharacters;\n  }\n  provideCompletionItems(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doComplete(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      const wordInfo = model.getWordUntilPosition(position);\n      const wordRange = new monaco_editor_core_exports.Range(\n        position.lineNumber,\n        wordInfo.startColumn,\n        position.lineNumber,\n        wordInfo.endColumn\n      );\n      const items = info.items.map((entry) => {\n        const item = {\n          label: entry.label,\n          insertText: entry.insertText || entry.label,\n          sortText: entry.sortText,\n          filterText: entry.filterText,\n          documentation: entry.documentation,\n          detail: entry.detail,\n          command: toCommand(entry.command),\n          range: wordRange,\n          kind: toCompletionItemKind(entry.kind)\n        };\n        if (entry.textEdit) {\n          if (isInsertReplaceEdit(entry.textEdit)) {\n            item.range = {\n              insert: toRange(entry.textEdit.insert),\n              replace: toRange(entry.textEdit.replace)\n            };\n          } else {\n            item.range = toRange(entry.textEdit.range);\n          }\n          item.insertText = entry.textEdit.newText;\n        }\n        if (entry.additionalTextEdits) {\n          item.additionalTextEdits = entry.additionalTextEdits.map(toTextEdit);\n        }\n        if (entry.insertTextFormat === InsertTextFormat.Snippet) {\n          item.insertTextRules = monaco_editor_core_exports.languages.CompletionItemInsertTextRule.InsertAsSnippet;\n        }\n        return item;\n      });\n      return {\n        isIncomplete: info.isIncomplete,\n        suggestions: items\n      };\n    });\n  }\n};\nfunction fromPosition(position) {\n  if (!position) {\n    return void 0;\n  }\n  return { character: position.column - 1, line: position.lineNumber - 1 };\n}\nfunction fromRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return {\n    start: {\n      line: range.startLineNumber - 1,\n      character: range.startColumn - 1\n    },\n    end: { line: range.endLineNumber - 1, character: range.endColumn - 1 }\n  };\n}\nfunction toRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return new monaco_editor_core_exports.Range(\n    range.start.line + 1,\n    range.start.character + 1,\n    range.end.line + 1,\n    range.end.character + 1\n  );\n}\nfunction isInsertReplaceEdit(edit) {\n  return typeof edit.insert !== \"undefined\" && typeof edit.replace !== \"undefined\";\n}\nfunction toCompletionItemKind(kind) {\n  const mItemKind = monaco_editor_core_exports.languages.CompletionItemKind;\n  switch (kind) {\n    case CompletionItemKind.Text:\n      return mItemKind.Text;\n    case CompletionItemKind.Method:\n      return mItemKind.Method;\n    case CompletionItemKind.Function:\n      return mItemKind.Function;\n    case CompletionItemKind.Constructor:\n      return mItemKind.Constructor;\n    case CompletionItemKind.Field:\n      return mItemKind.Field;\n    case CompletionItemKind.Variable:\n      return mItemKind.Variable;\n    case CompletionItemKind.Class:\n      return mItemKind.Class;\n    case CompletionItemKind.Interface:\n      return mItemKind.Interface;\n    case CompletionItemKind.Module:\n      return mItemKind.Module;\n    case CompletionItemKind.Property:\n      return mItemKind.Property;\n    case CompletionItemKind.Unit:\n      return mItemKind.Unit;\n    case CompletionItemKind.Value:\n      return mItemKind.Value;\n    case CompletionItemKind.Enum:\n      return mItemKind.Enum;\n    case CompletionItemKind.Keyword:\n      return mItemKind.Keyword;\n    case CompletionItemKind.Snippet:\n      return mItemKind.Snippet;\n    case CompletionItemKind.Color:\n      return mItemKind.Color;\n    case CompletionItemKind.File:\n      return mItemKind.File;\n    case CompletionItemKind.Reference:\n      return mItemKind.Reference;\n  }\n  return mItemKind.Property;\n}\nfunction toTextEdit(textEdit) {\n  if (!textEdit) {\n    return void 0;\n  }\n  return {\n    range: toRange(textEdit.range),\n    text: textEdit.newText\n  };\n}\nfunction toCommand(c) {\n  return c && c.command === \"editor.action.triggerSuggest\" ? { id: c.command, title: c.title, arguments: c.arguments } : void 0;\n}\nvar HoverAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideHover(model, position, token) {\n    let resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doHover(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      return {\n        range: toRange(info.range),\n        contents: toMarkedStringArray(info.contents)\n      };\n    });\n  }\n};\nfunction isMarkupContent(thing) {\n  return thing && typeof thing === \"object\" && typeof thing.kind === \"string\";\n}\nfunction toMarkdownString(entry) {\n  if (typeof entry === \"string\") {\n    return {\n      value: entry\n    };\n  }\n  if (isMarkupContent(entry)) {\n    if (entry.kind === \"plaintext\") {\n      return {\n        value: entry.value.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\")\n      };\n    }\n    return {\n      value: entry.value\n    };\n  }\n  return { value: \"```\" + entry.language + \"\\n\" + entry.value + \"\\n```\\n\" };\n}\nfunction toMarkedStringArray(contents) {\n  if (!contents) {\n    return void 0;\n  }\n  if (Array.isArray(contents)) {\n    return contents.map(toMarkdownString);\n  }\n  return [toMarkdownString(contents)];\n}\nvar DocumentHighlightAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentHighlights(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentHighlights(resource.toString(), fromPosition(position))).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map((entry) => {\n        return {\n          range: toRange(entry.range),\n          kind: toDocumentHighlightKind(entry.kind)\n        };\n      });\n    });\n  }\n};\nfunction toDocumentHighlightKind(kind) {\n  switch (kind) {\n    case DocumentHighlightKind.Read:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Read;\n    case DocumentHighlightKind.Write:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Write;\n    case DocumentHighlightKind.Text:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n  }\n  return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n}\nvar DefinitionAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDefinition(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.findDefinition(resource.toString(), fromPosition(position));\n    }).then((definition) => {\n      if (!definition) {\n        return;\n      }\n      return [toLocation(definition)];\n    });\n  }\n};\nfunction toLocation(location) {\n  return {\n    uri: monaco_editor_core_exports.Uri.parse(location.uri),\n    range: toRange(location.range)\n  };\n}\nvar ReferenceAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideReferences(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.findReferences(resource.toString(), fromPosition(position));\n    }).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map(toLocation);\n    });\n  }\n};\nvar RenameAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideRenameEdits(model, position, newName, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doRename(resource.toString(), fromPosition(position), newName);\n    }).then((edit) => {\n      return toWorkspaceEdit(edit);\n    });\n  }\n};\nfunction toWorkspaceEdit(edit) {\n  if (!edit || !edit.changes) {\n    return void 0;\n  }\n  let resourceEdits = [];\n  for (let uri in edit.changes) {\n    const _uri = monaco_editor_core_exports.Uri.parse(uri);\n    for (let e of edit.changes[uri]) {\n      resourceEdits.push({\n        resource: _uri,\n        versionId: void 0,\n        textEdit: {\n          range: toRange(e.range),\n          text: e.newText\n        }\n      });\n    }\n  }\n  return {\n    edits: resourceEdits\n  };\n}\nvar DocumentSymbolAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentSymbols(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentSymbols(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return items.map((item) => {\n        if (isDocumentSymbol(item)) {\n          return toDocumentSymbol(item);\n        }\n        return {\n          name: item.name,\n          detail: \"\",\n          containerName: item.containerName,\n          kind: toSymbolKind(item.kind),\n          range: toRange(item.location.range),\n          selectionRange: toRange(item.location.range),\n          tags: []\n        };\n      });\n    });\n  }\n};\nfunction isDocumentSymbol(symbol) {\n  return \"children\" in symbol;\n}\nfunction toDocumentSymbol(symbol) {\n  return {\n    name: symbol.name,\n    detail: symbol.detail ?? \"\",\n    kind: toSymbolKind(symbol.kind),\n    range: toRange(symbol.range),\n    selectionRange: toRange(symbol.selectionRange),\n    tags: symbol.tags ?? [],\n    children: (symbol.children ?? []).map((item) => toDocumentSymbol(item))\n  };\n}\nfunction toSymbolKind(kind) {\n  let mKind = monaco_editor_core_exports.languages.SymbolKind;\n  switch (kind) {\n    case SymbolKind.File:\n      return mKind.File;\n    case SymbolKind.Module:\n      return mKind.Module;\n    case SymbolKind.Namespace:\n      return mKind.Namespace;\n    case SymbolKind.Package:\n      return mKind.Package;\n    case SymbolKind.Class:\n      return mKind.Class;\n    case SymbolKind.Method:\n      return mKind.Method;\n    case SymbolKind.Property:\n      return mKind.Property;\n    case SymbolKind.Field:\n      return mKind.Field;\n    case SymbolKind.Constructor:\n      return mKind.Constructor;\n    case SymbolKind.Enum:\n      return mKind.Enum;\n    case SymbolKind.Interface:\n      return mKind.Interface;\n    case SymbolKind.Function:\n      return mKind.Function;\n    case SymbolKind.Variable:\n      return mKind.Variable;\n    case SymbolKind.Constant:\n      return mKind.Constant;\n    case SymbolKind.String:\n      return mKind.String;\n    case SymbolKind.Number:\n      return mKind.Number;\n    case SymbolKind.Boolean:\n      return mKind.Boolean;\n    case SymbolKind.Array:\n      return mKind.Array;\n  }\n  return mKind.Function;\n}\nvar DocumentLinkAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideLinks(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentLinks(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return {\n        links: items.map((item) => ({\n          range: toRange(item.range),\n          url: item.target\n        }))\n      };\n    });\n  }\n};\nvar DocumentFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentFormattingEdits(model, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.format(resource.toString(), null, fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nvar DocumentRangeFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n    this.canFormatMultipleRanges = false;\n  }\n  provideDocumentRangeFormattingEdits(model, range, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.format(resource.toString(), fromRange(range), fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nfunction fromFormattingOptions(options) {\n  return {\n    tabSize: options.tabSize,\n    insertSpaces: options.insertSpaces\n  };\n}\nvar DocumentColorAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentColors(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentColors(resource.toString())).then((infos) => {\n      if (!infos) {\n        return;\n      }\n      return infos.map((item) => ({\n        color: item.color,\n        range: toRange(item.range)\n      }));\n    });\n  }\n  provideColorPresentations(model, info, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker) => worker.getColorPresentations(resource.toString(), info.color, fromRange(info.range))\n    ).then((presentations) => {\n      if (!presentations) {\n        return;\n      }\n      return presentations.map((presentation) => {\n        let item = {\n          label: presentation.label\n        };\n        if (presentation.textEdit) {\n          item.textEdit = toTextEdit(presentation.textEdit);\n        }\n        if (presentation.additionalTextEdits) {\n          item.additionalTextEdits = presentation.additionalTextEdits.map(toTextEdit);\n        }\n        return item;\n      });\n    });\n  }\n};\nvar FoldingRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideFoldingRanges(model, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.getFoldingRanges(resource.toString(), context)).then((ranges) => {\n      if (!ranges) {\n        return;\n      }\n      return ranges.map((range) => {\n        const result = {\n          start: range.startLine + 1,\n          end: range.endLine + 1\n        };\n        if (typeof range.kind !== \"undefined\") {\n          result.kind = toFoldingRangeKind(range.kind);\n        }\n        return result;\n      });\n    });\n  }\n};\nfunction toFoldingRangeKind(kind) {\n  switch (kind) {\n    case FoldingRangeKind.Comment:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Comment;\n    case FoldingRangeKind.Imports:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Imports;\n    case FoldingRangeKind.Region:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Region;\n  }\n  return void 0;\n}\nvar SelectionRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideSelectionRanges(model, positions, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker) => worker.getSelectionRanges(\n        resource.toString(),\n        positions.map(fromPosition)\n      )\n    ).then((selectionRanges) => {\n      if (!selectionRanges) {\n        return;\n      }\n      return selectionRanges.map((selectionRange) => {\n        const result = [];\n        while (selectionRange) {\n          result.push({ range: toRange(selectionRange.range) });\n          selectionRange = selectionRange.parent;\n        }\n        return result;\n      });\n    });\n  }\n};\n\n// src/language/html/htmlMode.ts\nvar HTMLCompletionAdapter = class extends CompletionAdapter {\n  constructor(worker) {\n    super(worker, [\".\", \":\", \"<\", '\"', \"=\", \"/\"]);\n  }\n};\nfunction setupMode1(defaults) {\n  const client = new WorkerManager(defaults);\n  const worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  let languageId = defaults.languageId;\n  monaco_editor_core_exports.languages.registerCompletionItemProvider(languageId, new HTMLCompletionAdapter(worker));\n  monaco_editor_core_exports.languages.registerHoverProvider(languageId, new HoverAdapter(worker));\n  monaco_editor_core_exports.languages.registerDocumentHighlightProvider(\n    languageId,\n    new DocumentHighlightAdapter(worker)\n  );\n  monaco_editor_core_exports.languages.registerLinkProvider(languageId, new DocumentLinkAdapter(worker));\n  monaco_editor_core_exports.languages.registerFoldingRangeProvider(\n    languageId,\n    new FoldingRangeAdapter(worker)\n  );\n  monaco_editor_core_exports.languages.registerDocumentSymbolProvider(\n    languageId,\n    new DocumentSymbolAdapter(worker)\n  );\n  monaco_editor_core_exports.languages.registerSelectionRangeProvider(\n    languageId,\n    new SelectionRangeAdapter(worker)\n  );\n  monaco_editor_core_exports.languages.registerRenameProvider(languageId, new RenameAdapter(worker));\n  if (languageId === \"html\") {\n    monaco_editor_core_exports.languages.registerDocumentFormattingEditProvider(\n      languageId,\n      new DocumentFormattingEditProvider(worker)\n    );\n    monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(\n      languageId,\n      new DocumentRangeFormattingEditProvider(worker)\n    );\n  }\n}\nfunction setupMode(defaults) {\n  const disposables = [];\n  const providers = [];\n  const client = new WorkerManager(defaults);\n  disposables.push(client);\n  const worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  function registerProviders() {\n    const { languageId, modeConfiguration } = defaults;\n    disposeAll(providers);\n    if (modeConfiguration.completionItems) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCompletionItemProvider(languageId, new HTMLCompletionAdapter(worker))\n      );\n    }\n    if (modeConfiguration.hovers) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerHoverProvider(languageId, new HoverAdapter(worker))\n      );\n    }\n    if (modeConfiguration.documentHighlights) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentHighlightProvider(\n          languageId,\n          new DocumentHighlightAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.links) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerLinkProvider(languageId, new DocumentLinkAdapter(worker))\n      );\n    }\n    if (modeConfiguration.documentSymbols) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentSymbolProvider(\n          languageId,\n          new DocumentSymbolAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.rename) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerRenameProvider(languageId, new RenameAdapter(worker))\n      );\n    }\n    if (modeConfiguration.foldingRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerFoldingRangeProvider(\n          languageId,\n          new FoldingRangeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.selectionRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerSelectionRangeProvider(\n          languageId,\n          new SelectionRangeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentFormattingEditProvider(\n          languageId,\n          new DocumentFormattingEditProvider(worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentRangeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(\n          languageId,\n          new DocumentRangeFormattingEditProvider(worker)\n        )\n      );\n    }\n  }\n  registerProviders();\n  disposables.push(asDisposable(providers));\n  return asDisposable(disposables);\n}\nfunction asDisposable(disposables) {\n  return { dispose: () => disposeAll(disposables) };\n}\nfunction disposeAll(disposables) {\n  while (disposables.length) {\n    disposables.pop().dispose();\n  }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/language/html/htmlMode.js\n"));

/***/ })

}]);