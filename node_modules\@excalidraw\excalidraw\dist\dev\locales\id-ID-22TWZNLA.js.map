{"version": 3, "sources": ["../../../locales/id-ID.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Tempel\",\n    \"pasteAsPlaintext\": \"Tempel sebagai teks biasa\",\n    \"pasteCharts\": \"Tempel diagram\",\n    \"selectAll\": \"<PERSON><PERSON><PERSON> semua\",\n    \"multiSelect\": \"Tambahkan elemen ke pilihan\",\n    \"moveCanvas\": \"Pindahkan kanvas\",\n    \"cut\": \"Potong\",\n    \"copy\": \"Salin\",\n    \"copyAsPng\": \"Salin ke papan klip sebagai PNG\",\n    \"copyAsSvg\": \"Salin ke papan klip sebagai SVG\",\n    \"copyText\": \"Salin ke papan klip sebagai teks\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Bawa maju\",\n    \"sendToBack\": \"<PERSON><PERSON> ke belakang\",\n    \"bringToFront\": \"Bawa ke depan\",\n    \"sendBackward\": \"Kirim mundur\",\n    \"delete\": \"Hapus\",\n    \"copyStyles\": \"<PERSON><PERSON> gaya\",\n    \"pasteStyles\": \"Tempelkan gaya\",\n    \"stroke\": \"Guratan\",\n    \"background\": \"Latar\",\n    \"fill\": \"Isian\",\n    \"strokeWidth\": \"Lebar guratan\",\n    \"strokeStyle\": \"Gaya guratan\",\n    \"strokeStyle_solid\": \"Padat\",\n    \"strokeStyle_dashed\": \"Putus-putus\",\n    \"strokeStyle_dotted\": \"Titik-titik\",\n    \"sloppiness\": \"Kecerobohan\",\n    \"opacity\": \"Keburaman\",\n    \"textAlign\": \"Perataan teks\",\n    \"edges\": \"Tepi\",\n    \"sharp\": \"Tajam\",\n    \"round\": \"Bulat\",\n    \"arrowheads\": \"Mata panah\",\n    \"arrowhead_none\": \"Tidak ada\",\n    \"arrowhead_arrow\": \"Panah\",\n    \"arrowhead_bar\": \"Batang\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Segitiga\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Ukuran font\",\n    \"fontFamily\": \"Jenis font\",\n    \"addWatermark\": \"Tambahkan \\\"Dibuat dengan Excalidraw\\\"\",\n    \"handDrawn\": \"Tulisan tangan\",\n    \"normal\": \"Normal\",\n    \"code\": \"Kode\",\n    \"small\": \"Kecil\",\n    \"medium\": \"Sedang\",\n    \"large\": \"Besar\",\n    \"veryLarge\": \"Sangat besar\",\n    \"solid\": \"Padat\",\n    \"hachure\": \"Garis-garis\",\n    \"zigzag\": \"Zigzag\",\n    \"crossHatch\": \"Asiran silang\",\n    \"thin\": \"Lembut\",\n    \"bold\": \"Tebal\",\n    \"left\": \"Kiri\",\n    \"center\": \"Tengah\",\n    \"right\": \"Kanan\",\n    \"extraBold\": \"Sangat tebal\",\n    \"architect\": \"Arsitek\",\n    \"artist\": \"Artis\",\n    \"cartoonist\": \"Kartunis\",\n    \"fileTitle\": \"Nama file\",\n    \"colorPicker\": \"Pilihan Warna\",\n    \"canvasColors\": \"Digunakan di kanvas\",\n    \"canvasBackground\": \"Latar Kanvas\",\n    \"drawingCanvas\": \"Kanvas\",\n    \"layers\": \"Lapisan\",\n    \"actions\": \"Aksi\",\n    \"language\": \"Bahasa\",\n    \"liveCollaboration\": \"Kolaborasi langsung...\",\n    \"duplicateSelection\": \"Duplikat\",\n    \"untitled\": \"Tanpa judul\",\n    \"name\": \"Nama\",\n    \"yourName\": \"Nama Anda\",\n    \"madeWithExcalidraw\": \"Dibuat dengan Excalidraw\",\n    \"group\": \"Kelompokan pilihan\",\n    \"ungroup\": \"Pisahkan pilihan\",\n    \"collaborators\": \"Kolaborator\",\n    \"showGrid\": \"Tampilkan grid\",\n    \"addToLibrary\": \"Tambahkan ke pustaka\",\n    \"removeFromLibrary\": \"Hapus dari pustaka\",\n    \"libraryLoadingMessage\": \"Memuat pustaka…\",\n    \"libraries\": \"Telusur pustaka\",\n    \"loadingScene\": \"Memuat pemandangan…\",\n    \"align\": \"Perataan\",\n    \"alignTop\": \"Rata atas\",\n    \"alignBottom\": \"Rata bawah\",\n    \"alignLeft\": \"Rata kiri\",\n    \"alignRight\": \"Rata kanan\",\n    \"centerVertically\": \"Pusatkan secara vertikal\",\n    \"centerHorizontally\": \"Pusatkan secara horizontal\",\n    \"distributeHorizontally\": \"Distribusikan horizontal\",\n    \"distributeVertically\": \"Distribusikan vertikal\",\n    \"flipHorizontal\": \"Balikkan horizontal\",\n    \"flipVertical\": \"Balikkan vertikal\",\n    \"viewMode\": \"Mode tampilan\",\n    \"share\": \"Bagikan\",\n    \"showStroke\": \"Tampilkan garis pengambil warna\",\n    \"showBackground\": \"Tampilkan latar pengambil warna\",\n    \"toggleTheme\": \"Ubah tema\",\n    \"personalLib\": \"Pustaka Pribadi\",\n    \"excalidrawLib\": \"Pustaka Excalidraw\",\n    \"decreaseFontSize\": \"Kecilkan ukuran font\",\n    \"increaseFontSize\": \"Besarkan ukuran font\",\n    \"unbindText\": \"Lepas teks\",\n    \"bindText\": \"Kunci teks ke kontainer\",\n    \"createContainerFromText\": \"Bungkus teks dalam kontainer\",\n    \"link\": {\n      \"edit\": \"Edit tautan\",\n      \"editEmbed\": \"\",\n      \"create\": \"Buat tautan\",\n      \"createEmbed\": \"\",\n      \"label\": \"Tautan\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Edit tautan\",\n      \"exit\": \"Keluar editor garis\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Kunci\",\n      \"unlock\": \"Lepas\",\n      \"lockAll\": \"Kunci semua\",\n      \"unlockAll\": \"Lepas semua\"\n    },\n    \"statusPublished\": \"Telah terbit\",\n    \"sidebarLock\": \"Biarkan sidebar tetap terbuka\",\n    \"selectAllElementsInFrame\": \"Pilih semua elemen di bingkai\",\n    \"removeAllElementsFromFrame\": \"Hapus semua elemen dari bingkai\",\n    \"eyeDropper\": \"Ambil warna dari kanvas\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Belum ada item yang ditambahkan...\",\n    \"hint_emptyLibrary\": \"Pilih item pada kanvas untuk menambahkan nya di sini, atau pasang pustaka dari gudang di bawah ini.\",\n    \"hint_emptyPrivateLibrary\": \"Pilih item pada kanvas untuk menambahkan nya di sini.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Setel Ulang Kanvas\",\n    \"exportJSON\": \"Ekspor ke file\",\n    \"exportImage\": \"Ekspor gambar...\",\n    \"export\": \"Simpan ke...\",\n    \"copyToClipboard\": \"Salin ke Papan Klip\",\n    \"save\": \"Simpan ke file sekarang\",\n    \"saveAs\": \"Simpan sebagai\",\n    \"load\": \"Buka\",\n    \"getShareableLink\": \"Buat Tautan yang Bisa Dibagian\",\n    \"close\": \"Tutup\",\n    \"selectLanguage\": \"Pilih bahasa\",\n    \"scrollBackToContent\": \"Gulir kembali ke konten\",\n    \"zoomIn\": \"Besarkan\",\n    \"zoomOut\": \"Kecilkan\",\n    \"resetZoom\": \"Reset Pembesaran\",\n    \"menu\": \"Menu\",\n    \"done\": \"Selesai\",\n    \"edit\": \"Edit\",\n    \"undo\": \"Urungkan\",\n    \"redo\": \"Ulangi\",\n    \"resetLibrary\": \"Reset pustaka\",\n    \"createNewRoom\": \"Buat ruang baru\",\n    \"fullScreen\": \"Layar penuh\",\n    \"darkMode\": \"Mode gelap\",\n    \"lightMode\": \"Mode terang\",\n    \"zenMode\": \"Mode zen\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Keluar dari mode zen\",\n    \"cancel\": \"Batal\",\n    \"clear\": \"Hapus\",\n    \"remove\": \"Hapus\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Terbitkan\",\n    \"submit\": \"Kirimkan\",\n    \"confirm\": \"Konfirmasi\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Ini akan menghapus semua yang ada dikanvas. Apakah kamu yakin ?\",\n    \"couldNotCreateShareableLink\": \"Tidak bisa membuat tautan yang bisa dibagikan\",\n    \"couldNotCreateShareableLinkTooBig\": \"Tidak dapat membuat tautan yang dapat dibagikan: pemandangan terlalu besar\",\n    \"couldNotLoadInvalidFile\": \"Tidak dapat memuat berkas yang tidak valid\",\n    \"importBackendFailed\": \"Gagal mengimpor dari backend\",\n    \"cannotExportEmptyCanvas\": \"Tidak bisa mengekspor kanvas kosong\",\n    \"couldNotCopyToClipboard\": \"Tidak bisa menyalin ke papan klip.\",\n    \"decryptFailed\": \"Tidak dapat mengdekripsi data.\",\n    \"uploadedSecurly\": \"Pengunggahan ini telah diamankan menggunakan enkripsi end-to-end, artinya server Excalidraw dan pihak ketiga tidak data membaca nya\",\n    \"loadSceneOverridePrompt\": \"Memuat gambar external akan mengganti konten Anda yang ada. Apakah Anda ingin melanjutkan?\",\n    \"collabStopOverridePrompt\": \"Menghentikan sesi akan menimpa gambar Anda yang tersimpan secara lokal. Anda yakin?\\n\\n(Jika Anda ingin menyimpan gambar lokal Anda, gantinya cukup tutup tab browser.)\",\n    \"errorAddingToLibrary\": \"Tidak dapat menambahkan item ke pustaka\",\n    \"errorRemovingFromLibrary\": \"Tidak dapat membuang item dari pustaka\",\n    \"confirmAddLibrary\": \"Ini akan menambahkan {{numShapes}} bentuk ke pustaka Anda. Anda yakin?\",\n    \"imageDoesNotContainScene\": \"Gambar ini sepertinya tidak terdapat data pemandangan. Sudahkah Anda mengaktifkan penyematan pemandangan ketika ekspor?\",\n    \"cannotRestoreFromImage\": \"Pemandangan tidak dapat dipulihkan dari file gambar ini\",\n    \"invalidSceneUrl\": \"Tidak dapat impor pemandangan dari URL. Kemungkinan URL itu rusak atau tidak berisi data JSON Excalidraw yang valid.\",\n    \"resetLibrary\": \"Ini akan menghapus pustaka Anda. Anda yakin?\",\n    \"removeItemsFromsLibrary\": \"Hapus {{count}} item dari pustaka?\",\n    \"invalidEncryptionKey\": \"Sandi enkripsi harus 22 karakter. Kolaborasi langsung dinonaktifkan.\",\n    \"collabOfflineWarning\": \"Tidak ada koneksi internet.\\nPerubahan tidak akan disimpan!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Tipe file tidak didukung.\",\n    \"imageInsertError\": \"Tidak dapat menyisipkan gambar. Coba lagi nanti...\",\n    \"fileTooBig\": \"File terlalu besar. Ukuran maksimum yang dibolehkan {{maxSize}}.\",\n    \"svgImageInsertError\": \"Tidak dapat menyisipkan gambar SVG. Markup SVG sepertinya tidak valid.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"SVG tidak valid.\",\n    \"cannotResolveCollabServer\": \"Tidak dapat terhubung ke server kolab. Muat ulang laman dan coba lagi.\",\n    \"importLibraryError\": \"Tidak dapat memuat pustaka\",\n    \"collabSaveFailed\": \"Tidak dapat menyimpan ke dalam basis data server. Jika masih berlanjut, Anda sebaiknya simpan berkas Anda secara lokal untuk memastikan pekerjaan Anda tidak hilang.\",\n    \"collabSaveFailed_sizeExceeded\": \"Tidak dapat menyimpan ke dalam basis data server, tampaknya ukuran kanvas terlalu besar. Anda sebaiknya simpan berkas Anda secara lokal untuk memastikan pekerjaan Anda tidak hilang.\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Sepertinya Anda menggunkan peramban Brave dengan pengaturan <bold>Blokir Fingerprinting yang Agresif</bold> diaktifkan.\",\n      \"line2\": \"Ini dapat membuat <bold>Elemen Teks</bold> dalam gambar mu.\",\n      \"line3\": \"Kami sangat menyarankan mematikan pengaturan ini. Anda dapat mengikuti <link>langkah-langkah ini</link> untuk melakukannya.\",\n      \"line4\": \"Jika mematikan pengaturan ini tidak membenarkan tampilan elemen teks, mohon buka\\n<issueLink>isu</issueLink> di GitHub kami, atau chat kami di <discordLink>Discord</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Pilihan\",\n    \"image\": \"Sisipkan gambar\",\n    \"rectangle\": \"Persegi\",\n    \"diamond\": \"Berlian\",\n    \"ellipse\": \"Elips\",\n    \"arrow\": \"Panah\",\n    \"line\": \"Garis\",\n    \"freedraw\": \"Gambar\",\n    \"text\": \"Teks\",\n    \"library\": \"Pustaka\",\n    \"lock\": \"Biarkan alat yang dipilih aktif setelah menggambar\",\n    \"penMode\": \"Mode pena - mencegah sentuhan\",\n    \"link\": \"Tambah/Perbarui tautan untuk bentuk yang dipilih\",\n    \"eraser\": \"Penghapus\",\n    \"frame\": \"Alat bingkai\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"Tangan (alat panning)\",\n    \"extraTools\": \"Alat-alat lain\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Opsi Kanvas\",\n    \"selectedShapeActions\": \"Opsi bentuk yang dipilih\",\n    \"shapes\": \"Bentuk\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Untuk memindahkan kanvas, tekan roda mouse atau spacebar sambil menyeret, atau menggunakan alat tangan\",\n    \"linearElement\": \"Klik untuk memulai banyak poin, seret untuk satu baris\",\n    \"freeDraw\": \"Klik dan seret, lepaskan jika Anda selesai\",\n    \"text\": \"Tip: Anda juga dapat menambahkan teks dengan klik ganda di mana saja dengan alat pemilihan\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"Klik ganda atau tekan ENTER untuk edit teks\",\n    \"text_editing\": \"Tekan Escape atau CtrlAtauCmd+ENTER untuk selesai mengedit\",\n    \"linearElementMulti\": \"Klik pada titik akhir atau tekan Escape atau Enter untuk menyelesaikan\",\n    \"lockAngle\": \"Anda dapat menjaga sudut dengan menahan SHIFT\",\n    \"resize\": \"Anda dapat menjaga proposi dengan menekan SHIFT sambil mengubah ukuran,\\ntekan AlT untuk mengubah ukuran dari tengah\",\n    \"resizeImage\": \"Anda dapat mengubah secara bebas dengan menekan SHIFT,\\nTekan ALT untuk mengubah dari tengah\",\n    \"rotate\": \"Anda dapat menjaga sudut dengan menahan SHIFT sambil memutar\",\n    \"lineEditor_info\": \"Tekan Ctrl/Cmd dan Dobel-klik atau tekan Ctrl/Cmd +Enter untuk mengedit poin\",\n    \"lineEditor_pointSelected\": \"Tekan Delete untuk menghapus titik, Ctrl/Cmd + D untuk menduplikasi, atau seret untuk memindahkan\",\n    \"lineEditor_nothingSelected\": \"Pilih titik untuk mengedit (tekan SHIFT untuk pilih banyak), atau tekan Alt dan klik untuk tambahkan titik baru\",\n    \"placeImage\": \"Klik untuk tempatkan gambar, atau klik dan jatuhkan untuk tetapkan ukuran secara manual\",\n    \"publishLibrary\": \"Terbitkan pustaka Anda\",\n    \"bindTextToElement\": \"Tekan enter untuk tambahkan teks\",\n    \"deepBoxSelect\": \"Tekan Ctrl atau Cmd untuk memilih yang di dalam, dan mencegah penggeseran\",\n    \"eraserRevert\": \"Tahan Alt untuk mengembalikan elemen yang ditandai untuk dihapus\",\n    \"firefox_clipboard_write\": \"Fitur ini dapat diaktifkan melalui pengaturan flag \\\"dom.events.asyncClipboard.clipboardItem\\\" ke \\\"true\\\". Untuk mengganti flag di Firefox, pergi ke laman \\\"about:config\\\".\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Tidak dapat menampilkan pratinjau\",\n    \"canvasTooBig\": \"Kanvas mungkin terlalu besar.\",\n    \"canvasTooBigTip\": \"Tip: coba pindahkan elemen-terjauh lebih dekat bersama.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Mengalami sebuah kesalahan. Cobalah <button>muat ulang halaman.</button>\",\n    \"clearCanvasMessage\": \"Jika memuat ulang tidak bekerja, cobalah <button>bersihkan canvas.</button>\",\n    \"clearCanvasCaveat\": \" Ini akan menghasilkan hilangnya pekerjaan \",\n    \"trackedToSentry\": \"Kesalahan dengan pengidentifikasi {{eventId}} dilacak di sistem kami.\",\n    \"openIssueMessage\": \"Kami sangat berhati-hati untuk tidak menyertakan informasi pemandangan Anda pada kesalahan. Jika pemandangan Anda tidak bersifat pribadi, mohon pertimbangkan menindak lanjut pada <button>pelacak bug.</button> Mohon sertakan informasi dibawah ini dengan menyalin dan menempelkan di Github issue.\",\n    \"sceneContent\": \"Pemandangan konten:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Anda dapat mengundang orang ke pemandangan Anda saat ini untuk berkolaborasi dengan Anda.\",\n    \"desc_privacy\": \"Jangan khawatir, sesi menggunakan enkripsi end-to-end, sehingga apa pun yang Anda gambar akan tetap bersifat pribadi. Bahkan server kami tidak dapat melihat apa yang Anda lakukan.\",\n    \"button_startSession\": \"Mulai sesi\",\n    \"button_stopSession\": \"Hentikan sesi\",\n    \"desc_inProgressIntro\": \"Sesi kolaborasi sedang berlangsung sekarang.\",\n    \"desc_shareLink\": \"Bagikan tautan ini dengan siapa pun yang Anda inginkan untuk kolaborasi bersama:\",\n    \"desc_exitSession\": \"Menghentikan sesi akan memutuskan hubungan Anda dari ruangan, tetapi Anda dapat melanjutkan bekerja dengan pemandangan Anda secara lokal. Perhatikan bahwa ini tidak memengaruhi orang lain, dan mereka masih dapat berkolaborasi pada versi mereka.\",\n    \"shareTitle\": \"Gabung sesi kolaborasi langsung di Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Kesalahan\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Simpan ke disk\",\n    \"disk_details\": \"Ekspor data pemandangan ke file yang mana Anda dapat impor nanti.\",\n    \"disk_button\": \"Simpan ke file\",\n    \"link_title\": \"Tautan\",\n    \"link_details\": \"Ekspor sebagai tautan yang hanya dibaca.\",\n    \"link_button\": \"Ekspor ke tautan\",\n    \"excalidrawplus_description\": \"Simpan pemandangan ke ruang kerja Excalidraw+ Anda.\",\n    \"excalidrawplus_button\": \"Ekspor\",\n    \"excalidrawplus_exportError\": \"Tidak dapat ekspor ke Excalidraw+ saat ini...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Baca blog kami\",\n    \"click\": \"klik\",\n    \"deepSelect\": \"Pilih dalam\",\n    \"deepBoxSelect\": \"Pilih dalam kotak, dan cegah penggeseran\",\n    \"curvedArrow\": \"Panah lengkung\",\n    \"curvedLine\": \"Garis lengkung\",\n    \"documentation\": \"Dokumentasi\",\n    \"doubleClick\": \"klik-ganda\",\n    \"drag\": \"seret\",\n    \"editor\": \"Editor\",\n    \"editLineArrowPoints\": \"Edit titik garis/panah\",\n    \"editText\": \"Edit teks / tambah label\",\n    \"github\": \"Menemukan masalah? Kirimkan\",\n    \"howto\": \"Ikuti panduan kami\",\n    \"or\": \"atau\",\n    \"preventBinding\": \"Cegah pengikatan panah\",\n    \"tools\": \"Alat\",\n    \"shortcuts\": \"Pintasan keyboard\",\n    \"textFinish\": \"Selesai mengedit (editor teks)\",\n    \"textNewLine\": \"Tambahkan garis baru (editor teks)\",\n    \"title\": \"Bantuan\",\n    \"view\": \"Tampilan\",\n    \"zoomToFit\": \"Perbesar agar sesuai dengan semua elemen\",\n    \"zoomToSelection\": \"Perbesar ke seleksi\",\n    \"toggleElementLock\": \"Kunci/lepas seleksi\",\n    \"movePageUpDown\": \"Pindah halaman keatas/kebawah\",\n    \"movePageLeftRight\": \"Pindah halaman kebawah/keatas\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Hapus kanvas\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Terbitkan pustaka\",\n    \"itemName\": \"Nama item\",\n    \"authorName\": \"Nama pembuat\",\n    \"githubUsername\": \"Nama pengguna github\",\n    \"twitterUsername\": \"Nama pengguna Twitter\",\n    \"libraryName\": \"Nama Pustaka\",\n    \"libraryDesc\": \"Deskripsi pustaka\",\n    \"website\": \"Situs Web\",\n    \"placeholder\": {\n      \"authorName\": \"Nama atau nama pengguna Anda\",\n      \"libraryName\": \"Nama dari pustaka Anda\",\n      \"libraryDesc\": \"Deskripsi pustaka Anda untuk membantu orang mengerti penggunaannya\",\n      \"githubHandle\": \"Akun GitHub (opsional), jadi Anda dapat mengubah pustaka ketika diserahkan untuk review\",\n      \"twitterHandle\": \"Nama pengguna Twitter (opsional), jadi kami tahu siapa dipuji ketika mempromosikannya melalui Twitter\",\n      \"website\": \"Hubungkan ke situs personal Anda atau lainnya (opsional)\"\n    },\n    \"errors\": {\n      \"required\": \"Dibutuhkan\",\n      \"website\": \"Masukkan URL valid\"\n    },\n    \"noteDescription\": \"Kirimkan pustaka Anda untuk disertakan di <link>repositori pustaka publik</link>untuk orang lain menggunakannya dalam gambar mereka.\",\n    \"noteGuidelines\": \"Pustaka butuh disetujui secara manual terlebih dahulu. Baca <link>pedoman</link> sebelum mengirim. Anda butuh akun GitHub untuk berkomunikasi dan membuat perubahan jika dibutuhkan, tetapi tidak wajib dibutukan.\",\n    \"noteLicense\": \"Dengan mengkirimkannya, Anda setuju pustaka akan diterbitkan dibawah <link>Lisensi MIT, </link>yang artinya siapa pun dapat menggunakannya tanpa batasan.\",\n    \"noteItems\": \"Setiap item pustaka harus memiliki nama, sehingga bisa disortir. Item pustaka di bawah ini akan dimasukan:\",\n    \"atleastOneLibItem\": \"Pilih setidaknya satu item pustaka untuk mulai\",\n    \"republishWarning\": \"Catatan: beberapa item yang dipilih telah ditandai sebagai sudah dipublikasikan/diserahkan. Anda hanya dapat menyerahkan kembali item-item ketika memperbarui pustaka atau pengumpulan.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Pustaka telah dikirm\",\n    \"content\": \"Terima kasih {{authorName}}. pustaka Anda telah diserahkan untuk ditinjau ulang. Anda dapat cek statusnya<link>di sini</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Reset pustaka\",\n    \"removeItemsFromLib\": \"Hapus item yang dipilih dari pustaka\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Ekspor gambar\",\n    \"label\": {\n      \"withBackground\": \"Latar\",\n      \"onlySelected\": \"Hanya yang dipilih\",\n      \"darkMode\": \"Mode gelap\",\n      \"embedScene\": \"Sematkan pemandangan\",\n      \"scale\": \"Skala\",\n      \"padding\": \"Lapisan\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"Data pemandangan akan disimpan dalam file PNG/SVG yang diekspor sehingga pemandangan itu dapat dipulihkan darinya.\\nAkan membesarkan ukuran file yang diekspor.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Ekspor ke PNG\",\n      \"exportToSvg\": \"Ekspor ke SVG\",\n      \"copyPngToClipboard\": \"Salin PNG ke papan klip\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Salin ke papan klip\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Gambar anda terenkripsi end-to-end sehingga server Excalidraw tidak akan pernah dapat melihatnya.\",\n    \"link\": \"Pos blog tentang enkripsi ujung ke ujung di Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Sudut\",\n    \"element\": \"Elemen\",\n    \"elements\": \"Elemen\",\n    \"height\": \"Tinggi\",\n    \"scene\": \"Pemandangan\",\n    \"selected\": \"Terpilih\",\n    \"storage\": \"Penyimpanan\",\n    \"title\": \"Statistik untuk nerd\",\n    \"total\": \"Total\",\n    \"version\": \"Versi\",\n    \"versionCopy\": \"Klik untuk salin\",\n    \"versionNotAvailable\": \"Versi tidak tersedia\",\n    \"width\": \"Lebar\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Tambahkan ke pustaka\",\n    \"copyStyles\": \"Gaya tersalin.\",\n    \"copyToClipboard\": \"Tersalin ke papan klip.\",\n    \"copyToClipboardAsPng\": \"Tersalin {{exportSelection}} ke clipboard sebagai PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"File tersimpan.\",\n    \"fileSavedToFilename\": \"Disimpan ke {filename}\",\n    \"canvas\": \"kanvas\",\n    \"selection\": \"pilihan\",\n    \"pasteAsSingleElement\": \"Gunakan {{shortcut}} untuk menempelkan sebagai satu elemen,\\natau tempelkan ke teks editor yang ada\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Transparan\",\n    \"black\": \"Hitam\",\n    \"white\": \"Putih\",\n    \"red\": \"Merah\",\n    \"pink\": \"Pink\",\n    \"grape\": \"Ungu\",\n    \"violet\": \"Violet\",\n    \"gray\": \"Abu-abu\",\n    \"blue\": \"Biru\",\n    \"cyan\": \"Cyan\",\n    \"teal\": \"Teal\",\n    \"green\": \"Hijau\",\n    \"yellow\": \"Kuning\",\n    \"orange\": \"Jingga\",\n    \"bronze\": \"Tembaga\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Semua data Anda disimpan secara lokal di peramban Anda.\",\n      \"center_heading_plus\": \"Apa Anda ingin berpindah ke Excalidraw+?\",\n      \"menuHint\": \"Ekspor, preferensi, bahasa, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Ekspor, preferensi, dan selebihnya...\",\n      \"center_heading\": \"Diagram. Menjadi. Mudah.\",\n      \"toolbarHint\": \"Pilih alat & mulai menggambar!\",\n      \"helpHint\": \"Pintasan & bantuan\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Warna yang sering dipakai\",\n    \"colors\": \"Warna\",\n    \"shades\": \"Nuansa\",\n    \"hexCode\": \"Kode hexa\",\n    \"noShades\": \"Tidak ada nuansa untuk warna ini\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Ekspor sebagai gambar\",\n        \"button\": \"Ekspor sebagai gambar\",\n        \"description\": \"Ekspor data pemandangan sebagai gambar yang dapat anda impor nanti.\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Simpan ke disk\",\n        \"button\": \"Simpan ke disk\",\n        \"description\": \"Ekspor data pemandangan ke file yang dapat Anda dapat impor nanti.\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Ekspor ke Excalidraw+\",\n        \"description\": \"Simpan pemandangan ke ruang kerja Excalidraw+ Anda.\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Muat dari file\",\n        \"button\": \"Muat dari file\",\n        \"description\": \"Memuat dari file yang akan <bold>menggantikan konten Anda sekarang</bold>.<br></br>Anda dapat mencadangkan gambar anda dulu menggunakan opsi-opsi ini.\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Muat dari link\",\n        \"button\": \"Ganti konten saya\",\n        \"description\": \"Memuat dari file yang akan <bold>menggantikan konten Anda sekarang</bold>.<br></br>Anda dapat mencadangkan gambar anda dulu menggunakan opsi-opsi ini.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}