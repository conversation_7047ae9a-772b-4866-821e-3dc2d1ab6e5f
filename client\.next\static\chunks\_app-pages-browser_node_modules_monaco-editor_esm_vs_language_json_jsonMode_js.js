"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_language_json_jsonMode_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/language/json/jsonMode.js":
/*!**********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/language/json/jsonMode.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompletionAdapter: () => (/* binding */ CompletionAdapter),\n/* harmony export */   DefinitionAdapter: () => (/* binding */ DefinitionAdapter),\n/* harmony export */   DiagnosticsAdapter: () => (/* binding */ DiagnosticsAdapter),\n/* harmony export */   DocumentColorAdapter: () => (/* binding */ DocumentColorAdapter),\n/* harmony export */   DocumentFormattingEditProvider: () => (/* binding */ DocumentFormattingEditProvider),\n/* harmony export */   DocumentHighlightAdapter: () => (/* binding */ DocumentHighlightAdapter),\n/* harmony export */   DocumentLinkAdapter: () => (/* binding */ DocumentLinkAdapter),\n/* harmony export */   DocumentRangeFormattingEditProvider: () => (/* binding */ DocumentRangeFormattingEditProvider),\n/* harmony export */   DocumentSymbolAdapter: () => (/* binding */ DocumentSymbolAdapter),\n/* harmony export */   FoldingRangeAdapter: () => (/* binding */ FoldingRangeAdapter),\n/* harmony export */   HoverAdapter: () => (/* binding */ HoverAdapter),\n/* harmony export */   ReferenceAdapter: () => (/* binding */ ReferenceAdapter),\n/* harmony export */   RenameAdapter: () => (/* binding */ RenameAdapter),\n/* harmony export */   SelectionRangeAdapter: () => (/* binding */ SelectionRangeAdapter),\n/* harmony export */   WorkerManager: () => (/* binding */ WorkerManager),\n/* harmony export */   fromPosition: () => (/* binding */ fromPosition),\n/* harmony export */   fromRange: () => (/* binding */ fromRange),\n/* harmony export */   getWorker: () => (/* binding */ getWorker),\n/* harmony export */   setupMode: () => (/* binding */ setupMode),\n/* harmony export */   toRange: () => (/* binding */ toRange),\n/* harmony export */   toTextEdit: () => (/* binding */ toTextEdit)\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.api.js\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/language/json/workerManager.ts\nvar STOP_WHEN_IDLE_FOR = 2 * 60 * 1e3;\nvar WorkerManager = class {\n  constructor(defaults) {\n    this._defaults = defaults;\n    this._worker = null;\n    this._client = null;\n    this._idleCheckInterval = window.setInterval(() => this._checkIfIdle(), 30 * 1e3);\n    this._lastUsedTime = 0;\n    this._configChangeListener = this._defaults.onDidChange(() => this._stopWorker());\n  }\n  _stopWorker() {\n    if (this._worker) {\n      this._worker.dispose();\n      this._worker = null;\n    }\n    this._client = null;\n  }\n  dispose() {\n    clearInterval(this._idleCheckInterval);\n    this._configChangeListener.dispose();\n    this._stopWorker();\n  }\n  _checkIfIdle() {\n    if (!this._worker) {\n      return;\n    }\n    let timePassedSinceLastUsed = Date.now() - this._lastUsedTime;\n    if (timePassedSinceLastUsed > STOP_WHEN_IDLE_FOR) {\n      this._stopWorker();\n    }\n  }\n  _getClient() {\n    this._lastUsedTime = Date.now();\n    if (!this._client) {\n      this._worker = monaco_editor_core_exports.editor.createWebWorker({\n        // module that exports the create() method and returns a `JSONWorker` instance\n        moduleId: \"vs/language/json/jsonWorker\",\n        label: this._defaults.languageId,\n        // passed in to the create() method\n        createData: {\n          languageSettings: this._defaults.diagnosticsOptions,\n          languageId: this._defaults.languageId,\n          enableSchemaRequest: this._defaults.diagnosticsOptions.enableSchemaRequest\n        }\n      });\n      this._client = this._worker.getProxy();\n    }\n    return this._client;\n  }\n  getLanguageServiceWorker(...resources) {\n    let _client;\n    return this._getClient().then((client) => {\n      _client = client;\n    }).then((_) => {\n      if (this._worker) {\n        return this._worker.withSyncedResources(resources);\n      }\n    }).then((_) => _client);\n  }\n};\n\n// node_modules/vscode-languageserver-types/lib/esm/main.js\nvar DocumentUri;\n(function(DocumentUri2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  DocumentUri2.is = is;\n})(DocumentUri || (DocumentUri = {}));\nvar URI;\n(function(URI2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  URI2.is = is;\n})(URI || (URI = {}));\nvar integer;\n(function(integer2) {\n  integer2.MIN_VALUE = -2147483648;\n  integer2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && integer2.MIN_VALUE <= value && value <= integer2.MAX_VALUE;\n  }\n  integer2.is = is;\n})(integer || (integer = {}));\nvar uinteger;\n(function(uinteger2) {\n  uinteger2.MIN_VALUE = 0;\n  uinteger2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && uinteger2.MIN_VALUE <= value && value <= uinteger2.MAX_VALUE;\n  }\n  uinteger2.is = is;\n})(uinteger || (uinteger = {}));\nvar Position;\n(function(Position3) {\n  function create(line, character) {\n    if (line === Number.MAX_VALUE) {\n      line = uinteger.MAX_VALUE;\n    }\n    if (character === Number.MAX_VALUE) {\n      character = uinteger.MAX_VALUE;\n    }\n    return { line, character };\n  }\n  Position3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n  }\n  Position3.is = is;\n})(Position || (Position = {}));\nvar Range;\n(function(Range3) {\n  function create(one, two, three, four) {\n    if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n      return { start: Position.create(one, two), end: Position.create(three, four) };\n    } else if (Position.is(one) && Position.is(two)) {\n      return { start: one, end: two };\n    } else {\n      throw new Error(`Range#create called with invalid arguments[${one}, ${two}, ${three}, ${four}]`);\n    }\n  }\n  Range3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n  }\n  Range3.is = is;\n})(Range || (Range = {}));\nvar Location;\n(function(Location2) {\n  function create(uri, range) {\n    return { uri, range };\n  }\n  Location2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n  }\n  Location2.is = is;\n})(Location || (Location = {}));\nvar LocationLink;\n(function(LocationLink2) {\n  function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n    return { targetUri, targetRange, targetSelectionRange, originSelectionRange };\n  }\n  LocationLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri) && Range.is(candidate.targetSelectionRange) && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n  }\n  LocationLink2.is = is;\n})(LocationLink || (LocationLink = {}));\nvar Color;\n(function(Color2) {\n  function create(red, green, blue, alpha) {\n    return {\n      red,\n      green,\n      blue,\n      alpha\n    };\n  }\n  Color2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1) && Is.numberRange(candidate.green, 0, 1) && Is.numberRange(candidate.blue, 0, 1) && Is.numberRange(candidate.alpha, 0, 1);\n  }\n  Color2.is = is;\n})(Color || (Color = {}));\nvar ColorInformation;\n(function(ColorInformation2) {\n  function create(range, color) {\n    return {\n      range,\n      color\n    };\n  }\n  ColorInformation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);\n  }\n  ColorInformation2.is = is;\n})(ColorInformation || (ColorInformation = {}));\nvar ColorPresentation;\n(function(ColorPresentation2) {\n  function create(label, textEdit, additionalTextEdits) {\n    return {\n      label,\n      textEdit,\n      additionalTextEdits\n    };\n  }\n  ColorPresentation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate)) && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n  }\n  ColorPresentation2.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\nvar FoldingRangeKind;\n(function(FoldingRangeKind2) {\n  FoldingRangeKind2.Comment = \"comment\";\n  FoldingRangeKind2.Imports = \"imports\";\n  FoldingRangeKind2.Region = \"region\";\n})(FoldingRangeKind || (FoldingRangeKind = {}));\nvar FoldingRange;\n(function(FoldingRange2) {\n  function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {\n    const result = {\n      startLine,\n      endLine\n    };\n    if (Is.defined(startCharacter)) {\n      result.startCharacter = startCharacter;\n    }\n    if (Is.defined(endCharacter)) {\n      result.endCharacter = endCharacter;\n    }\n    if (Is.defined(kind)) {\n      result.kind = kind;\n    }\n    if (Is.defined(collapsedText)) {\n      result.collapsedText = collapsedText;\n    }\n    return result;\n  }\n  FoldingRange2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine) && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter)) && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter)) && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n  }\n  FoldingRange2.is = is;\n})(FoldingRange || (FoldingRange = {}));\nvar DiagnosticRelatedInformation;\n(function(DiagnosticRelatedInformation2) {\n  function create(location, message) {\n    return {\n      location,\n      message\n    };\n  }\n  DiagnosticRelatedInformation2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n  }\n  DiagnosticRelatedInformation2.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\nvar DiagnosticSeverity;\n(function(DiagnosticSeverity2) {\n  DiagnosticSeverity2.Error = 1;\n  DiagnosticSeverity2.Warning = 2;\n  DiagnosticSeverity2.Information = 3;\n  DiagnosticSeverity2.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\nvar DiagnosticTag;\n(function(DiagnosticTag2) {\n  DiagnosticTag2.Unnecessary = 1;\n  DiagnosticTag2.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\nvar CodeDescription;\n(function(CodeDescription2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.href);\n  }\n  CodeDescription2.is = is;\n})(CodeDescription || (CodeDescription = {}));\nvar Diagnostic;\n(function(Diagnostic2) {\n  function create(range, message, severity, code, source, relatedInformation) {\n    let result = { range, message };\n    if (Is.defined(severity)) {\n      result.severity = severity;\n    }\n    if (Is.defined(code)) {\n      result.code = code;\n    }\n    if (Is.defined(source)) {\n      result.source = source;\n    }\n    if (Is.defined(relatedInformation)) {\n      result.relatedInformation = relatedInformation;\n    }\n    return result;\n  }\n  Diagnostic2.create = create;\n  function is(value) {\n    var _a;\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && Is.string(candidate.message) && (Is.number(candidate.severity) || Is.undefined(candidate.severity)) && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code)) && (Is.undefined(candidate.codeDescription) || Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)) && (Is.string(candidate.source) || Is.undefined(candidate.source)) && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n  }\n  Diagnostic2.is = is;\n})(Diagnostic || (Diagnostic = {}));\nvar Command;\n(function(Command2) {\n  function create(title, command, ...args) {\n    let result = { title, command };\n    if (Is.defined(args) && args.length > 0) {\n      result.arguments = args;\n    }\n    return result;\n  }\n  Command2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n  }\n  Command2.is = is;\n})(Command || (Command = {}));\nvar TextEdit;\n(function(TextEdit2) {\n  function replace(range, newText) {\n    return { range, newText };\n  }\n  TextEdit2.replace = replace;\n  function insert(position, newText) {\n    return { range: { start: position, end: position }, newText };\n  }\n  TextEdit2.insert = insert;\n  function del(range) {\n    return { range, newText: \"\" };\n  }\n  TextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.newText) && Range.is(candidate.range);\n  }\n  TextEdit2.is = is;\n})(TextEdit || (TextEdit = {}));\nvar ChangeAnnotation;\n(function(ChangeAnnotation2) {\n  function create(label, needsConfirmation, description) {\n    const result = { label };\n    if (needsConfirmation !== void 0) {\n      result.needsConfirmation = needsConfirmation;\n    }\n    if (description !== void 0) {\n      result.description = description;\n    }\n    return result;\n  }\n  ChangeAnnotation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  ChangeAnnotation2.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nvar ChangeAnnotationIdentifier;\n(function(ChangeAnnotationIdentifier2) {\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate);\n  }\n  ChangeAnnotationIdentifier2.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nvar AnnotatedTextEdit;\n(function(AnnotatedTextEdit2) {\n  function replace(range, newText, annotation) {\n    return { range, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.replace = replace;\n  function insert(position, newText, annotation) {\n    return { range: { start: position, end: position }, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.insert = insert;\n  function del(range, annotation) {\n    return { range, newText: \"\", annotationId: annotation };\n  }\n  AnnotatedTextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  AnnotatedTextEdit2.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\nvar TextDocumentEdit;\n(function(TextDocumentEdit2) {\n  function create(textDocument, edits) {\n    return { textDocument, edits };\n  }\n  TextDocumentEdit2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument) && Array.isArray(candidate.edits);\n  }\n  TextDocumentEdit2.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nvar CreateFile;\n(function(CreateFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"create\",\n      uri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  CreateFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"create\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  CreateFile2.is = is;\n})(CreateFile || (CreateFile = {}));\nvar RenameFile;\n(function(RenameFile2) {\n  function create(oldUri, newUri, options, annotation) {\n    let result = {\n      kind: \"rename\",\n      oldUri,\n      newUri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  RenameFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"rename\" && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  RenameFile2.is = is;\n})(RenameFile || (RenameFile = {}));\nvar DeleteFile;\n(function(DeleteFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"delete\",\n      uri\n    };\n    if (options !== void 0 && (options.recursive !== void 0 || options.ignoreIfNotExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  DeleteFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"delete\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.recursive === void 0 || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === void 0 || Is.boolean(candidate.options.ignoreIfNotExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  DeleteFile2.is = is;\n})(DeleteFile || (DeleteFile = {}));\nvar WorkspaceEdit;\n(function(WorkspaceEdit2) {\n  function is(value) {\n    let candidate = value;\n    return candidate && (candidate.changes !== void 0 || candidate.documentChanges !== void 0) && (candidate.documentChanges === void 0 || candidate.documentChanges.every((change) => {\n      if (Is.string(change.kind)) {\n        return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n      } else {\n        return TextDocumentEdit.is(change);\n      }\n    }));\n  }\n  WorkspaceEdit2.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nvar TextDocumentIdentifier;\n(function(TextDocumentIdentifier2) {\n  function create(uri) {\n    return { uri };\n  }\n  TextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri);\n  }\n  TextDocumentIdentifier2.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\nvar VersionedTextDocumentIdentifier;\n(function(VersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  VersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n  }\n  VersionedTextDocumentIdentifier2.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\nvar OptionalVersionedTextDocumentIdentifier;\n(function(OptionalVersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  OptionalVersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n  }\n  OptionalVersionedTextDocumentIdentifier2.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\nvar TextDocumentItem;\n(function(TextDocumentItem2) {\n  function create(uri, languageId, version, text) {\n    return { uri, languageId, version, text };\n  }\n  TextDocumentItem2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n  }\n  TextDocumentItem2.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\nvar MarkupKind;\n(function(MarkupKind2) {\n  MarkupKind2.PlainText = \"plaintext\";\n  MarkupKind2.Markdown = \"markdown\";\n  function is(value) {\n    const candidate = value;\n    return candidate === MarkupKind2.PlainText || candidate === MarkupKind2.Markdown;\n  }\n  MarkupKind2.is = is;\n})(MarkupKind || (MarkupKind = {}));\nvar MarkupContent;\n(function(MarkupContent2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n  }\n  MarkupContent2.is = is;\n})(MarkupContent || (MarkupContent = {}));\nvar CompletionItemKind;\n(function(CompletionItemKind2) {\n  CompletionItemKind2.Text = 1;\n  CompletionItemKind2.Method = 2;\n  CompletionItemKind2.Function = 3;\n  CompletionItemKind2.Constructor = 4;\n  CompletionItemKind2.Field = 5;\n  CompletionItemKind2.Variable = 6;\n  CompletionItemKind2.Class = 7;\n  CompletionItemKind2.Interface = 8;\n  CompletionItemKind2.Module = 9;\n  CompletionItemKind2.Property = 10;\n  CompletionItemKind2.Unit = 11;\n  CompletionItemKind2.Value = 12;\n  CompletionItemKind2.Enum = 13;\n  CompletionItemKind2.Keyword = 14;\n  CompletionItemKind2.Snippet = 15;\n  CompletionItemKind2.Color = 16;\n  CompletionItemKind2.File = 17;\n  CompletionItemKind2.Reference = 18;\n  CompletionItemKind2.Folder = 19;\n  CompletionItemKind2.EnumMember = 20;\n  CompletionItemKind2.Constant = 21;\n  CompletionItemKind2.Struct = 22;\n  CompletionItemKind2.Event = 23;\n  CompletionItemKind2.Operator = 24;\n  CompletionItemKind2.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\nvar InsertTextFormat;\n(function(InsertTextFormat2) {\n  InsertTextFormat2.PlainText = 1;\n  InsertTextFormat2.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\nvar CompletionItemTag;\n(function(CompletionItemTag2) {\n  CompletionItemTag2.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\nvar InsertReplaceEdit;\n(function(InsertReplaceEdit2) {\n  function create(newText, insert, replace) {\n    return { newText, insert, replace };\n  }\n  InsertReplaceEdit2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n  }\n  InsertReplaceEdit2.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\nvar InsertTextMode;\n(function(InsertTextMode2) {\n  InsertTextMode2.asIs = 1;\n  InsertTextMode2.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nvar CompletionItemLabelDetails;\n(function(CompletionItemLabelDetails2) {\n  function is(value) {\n    const candidate = value;\n    return candidate && (Is.string(candidate.detail) || candidate.detail === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  CompletionItemLabelDetails2.is = is;\n})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));\nvar CompletionItem;\n(function(CompletionItem2) {\n  function create(label) {\n    return { label };\n  }\n  CompletionItem2.create = create;\n})(CompletionItem || (CompletionItem = {}));\nvar CompletionList;\n(function(CompletionList2) {\n  function create(items, isIncomplete) {\n    return { items: items ? items : [], isIncomplete: !!isIncomplete };\n  }\n  CompletionList2.create = create;\n})(CompletionList || (CompletionList = {}));\nvar MarkedString;\n(function(MarkedString2) {\n  function fromPlainText(plainText) {\n    return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\");\n  }\n  MarkedString2.fromPlainText = fromPlainText;\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate) || Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value);\n  }\n  MarkedString2.is = is;\n})(MarkedString || (MarkedString = {}));\nvar Hover;\n(function(Hover2) {\n  function is(value) {\n    let candidate = value;\n    return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) || MarkedString.is(candidate.contents) || Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === void 0 || Range.is(value.range));\n  }\n  Hover2.is = is;\n})(Hover || (Hover = {}));\nvar ParameterInformation;\n(function(ParameterInformation2) {\n  function create(label, documentation) {\n    return documentation ? { label, documentation } : { label };\n  }\n  ParameterInformation2.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\nvar SignatureInformation;\n(function(SignatureInformation2) {\n  function create(label, documentation, ...parameters) {\n    let result = { label };\n    if (Is.defined(documentation)) {\n      result.documentation = documentation;\n    }\n    if (Is.defined(parameters)) {\n      result.parameters = parameters;\n    } else {\n      result.parameters = [];\n    }\n    return result;\n  }\n  SignatureInformation2.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\nvar DocumentHighlightKind;\n(function(DocumentHighlightKind2) {\n  DocumentHighlightKind2.Text = 1;\n  DocumentHighlightKind2.Read = 2;\n  DocumentHighlightKind2.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\nvar DocumentHighlight;\n(function(DocumentHighlight2) {\n  function create(range, kind) {\n    let result = { range };\n    if (Is.number(kind)) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  DocumentHighlight2.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\nvar SymbolKind;\n(function(SymbolKind2) {\n  SymbolKind2.File = 1;\n  SymbolKind2.Module = 2;\n  SymbolKind2.Namespace = 3;\n  SymbolKind2.Package = 4;\n  SymbolKind2.Class = 5;\n  SymbolKind2.Method = 6;\n  SymbolKind2.Property = 7;\n  SymbolKind2.Field = 8;\n  SymbolKind2.Constructor = 9;\n  SymbolKind2.Enum = 10;\n  SymbolKind2.Interface = 11;\n  SymbolKind2.Function = 12;\n  SymbolKind2.Variable = 13;\n  SymbolKind2.Constant = 14;\n  SymbolKind2.String = 15;\n  SymbolKind2.Number = 16;\n  SymbolKind2.Boolean = 17;\n  SymbolKind2.Array = 18;\n  SymbolKind2.Object = 19;\n  SymbolKind2.Key = 20;\n  SymbolKind2.Null = 21;\n  SymbolKind2.EnumMember = 22;\n  SymbolKind2.Struct = 23;\n  SymbolKind2.Event = 24;\n  SymbolKind2.Operator = 25;\n  SymbolKind2.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\nvar SymbolTag;\n(function(SymbolTag2) {\n  SymbolTag2.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nvar SymbolInformation;\n(function(SymbolInformation2) {\n  function create(name, kind, range, uri, containerName) {\n    let result = {\n      name,\n      kind,\n      location: { uri, range }\n    };\n    if (containerName) {\n      result.containerName = containerName;\n    }\n    return result;\n  }\n  SymbolInformation2.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nvar WorkspaceSymbol;\n(function(WorkspaceSymbol2) {\n  function create(name, kind, uri, range) {\n    return range !== void 0 ? { name, kind, location: { uri, range } } : { name, kind, location: { uri } };\n  }\n  WorkspaceSymbol2.create = create;\n})(WorkspaceSymbol || (WorkspaceSymbol = {}));\nvar DocumentSymbol;\n(function(DocumentSymbol2) {\n  function create(name, detail, kind, range, selectionRange, children) {\n    let result = {\n      name,\n      detail,\n      kind,\n      range,\n      selectionRange\n    };\n    if (children !== void 0) {\n      result.children = children;\n    }\n    return result;\n  }\n  DocumentSymbol2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.name) && Is.number(candidate.kind) && Range.is(candidate.range) && Range.is(candidate.selectionRange) && (candidate.detail === void 0 || Is.string(candidate.detail)) && (candidate.deprecated === void 0 || Is.boolean(candidate.deprecated)) && (candidate.children === void 0 || Array.isArray(candidate.children)) && (candidate.tags === void 0 || Array.isArray(candidate.tags));\n  }\n  DocumentSymbol2.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\nvar CodeActionKind;\n(function(CodeActionKind2) {\n  CodeActionKind2.Empty = \"\";\n  CodeActionKind2.QuickFix = \"quickfix\";\n  CodeActionKind2.Refactor = \"refactor\";\n  CodeActionKind2.RefactorExtract = \"refactor.extract\";\n  CodeActionKind2.RefactorInline = \"refactor.inline\";\n  CodeActionKind2.RefactorRewrite = \"refactor.rewrite\";\n  CodeActionKind2.Source = \"source\";\n  CodeActionKind2.SourceOrganizeImports = \"source.organizeImports\";\n  CodeActionKind2.SourceFixAll = \"source.fixAll\";\n})(CodeActionKind || (CodeActionKind = {}));\nvar CodeActionTriggerKind;\n(function(CodeActionTriggerKind2) {\n  CodeActionTriggerKind2.Invoked = 1;\n  CodeActionTriggerKind2.Automatic = 2;\n})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));\nvar CodeActionContext;\n(function(CodeActionContext2) {\n  function create(diagnostics, only, triggerKind) {\n    let result = { diagnostics };\n    if (only !== void 0 && only !== null) {\n      result.only = only;\n    }\n    if (triggerKind !== void 0 && triggerKind !== null) {\n      result.triggerKind = triggerKind;\n    }\n    return result;\n  }\n  CodeActionContext2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === void 0 || Is.typedArray(candidate.only, Is.string)) && (candidate.triggerKind === void 0 || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);\n  }\n  CodeActionContext2.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nvar CodeAction;\n(function(CodeAction2) {\n  function create(title, kindOrCommandOrEdit, kind) {\n    let result = { title };\n    let checkKind = true;\n    if (typeof kindOrCommandOrEdit === \"string\") {\n      checkKind = false;\n      result.kind = kindOrCommandOrEdit;\n    } else if (Command.is(kindOrCommandOrEdit)) {\n      result.command = kindOrCommandOrEdit;\n    } else {\n      result.edit = kindOrCommandOrEdit;\n    }\n    if (checkKind && kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  CodeAction2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.title) && (candidate.diagnostics === void 0 || Is.typedArray(candidate.diagnostics, Diagnostic.is)) && (candidate.kind === void 0 || Is.string(candidate.kind)) && (candidate.edit !== void 0 || candidate.command !== void 0) && (candidate.command === void 0 || Command.is(candidate.command)) && (candidate.isPreferred === void 0 || Is.boolean(candidate.isPreferred)) && (candidate.edit === void 0 || WorkspaceEdit.is(candidate.edit));\n  }\n  CodeAction2.is = is;\n})(CodeAction || (CodeAction = {}));\nvar CodeLens;\n(function(CodeLens2) {\n  function create(range, data) {\n    let result = { range };\n    if (Is.defined(data)) {\n      result.data = data;\n    }\n    return result;\n  }\n  CodeLens2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n  }\n  CodeLens2.is = is;\n})(CodeLens || (CodeLens = {}));\nvar FormattingOptions;\n(function(FormattingOptions2) {\n  function create(tabSize, insertSpaces) {\n    return { tabSize, insertSpaces };\n  }\n  FormattingOptions2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n  }\n  FormattingOptions2.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\nvar DocumentLink;\n(function(DocumentLink2) {\n  function create(range, target, data) {\n    return { range, target, data };\n  }\n  DocumentLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n  }\n  DocumentLink2.is = is;\n})(DocumentLink || (DocumentLink = {}));\nvar SelectionRange;\n(function(SelectionRange2) {\n  function create(range, parent) {\n    return { range, parent };\n  }\n  SelectionRange2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === void 0 || SelectionRange2.is(candidate.parent));\n  }\n  SelectionRange2.is = is;\n})(SelectionRange || (SelectionRange = {}));\nvar SemanticTokenTypes;\n(function(SemanticTokenTypes2) {\n  SemanticTokenTypes2[\"namespace\"] = \"namespace\";\n  SemanticTokenTypes2[\"type\"] = \"type\";\n  SemanticTokenTypes2[\"class\"] = \"class\";\n  SemanticTokenTypes2[\"enum\"] = \"enum\";\n  SemanticTokenTypes2[\"interface\"] = \"interface\";\n  SemanticTokenTypes2[\"struct\"] = \"struct\";\n  SemanticTokenTypes2[\"typeParameter\"] = \"typeParameter\";\n  SemanticTokenTypes2[\"parameter\"] = \"parameter\";\n  SemanticTokenTypes2[\"variable\"] = \"variable\";\n  SemanticTokenTypes2[\"property\"] = \"property\";\n  SemanticTokenTypes2[\"enumMember\"] = \"enumMember\";\n  SemanticTokenTypes2[\"event\"] = \"event\";\n  SemanticTokenTypes2[\"function\"] = \"function\";\n  SemanticTokenTypes2[\"method\"] = \"method\";\n  SemanticTokenTypes2[\"macro\"] = \"macro\";\n  SemanticTokenTypes2[\"keyword\"] = \"keyword\";\n  SemanticTokenTypes2[\"modifier\"] = \"modifier\";\n  SemanticTokenTypes2[\"comment\"] = \"comment\";\n  SemanticTokenTypes2[\"string\"] = \"string\";\n  SemanticTokenTypes2[\"number\"] = \"number\";\n  SemanticTokenTypes2[\"regexp\"] = \"regexp\";\n  SemanticTokenTypes2[\"operator\"] = \"operator\";\n  SemanticTokenTypes2[\"decorator\"] = \"decorator\";\n})(SemanticTokenTypes || (SemanticTokenTypes = {}));\nvar SemanticTokenModifiers;\n(function(SemanticTokenModifiers2) {\n  SemanticTokenModifiers2[\"declaration\"] = \"declaration\";\n  SemanticTokenModifiers2[\"definition\"] = \"definition\";\n  SemanticTokenModifiers2[\"readonly\"] = \"readonly\";\n  SemanticTokenModifiers2[\"static\"] = \"static\";\n  SemanticTokenModifiers2[\"deprecated\"] = \"deprecated\";\n  SemanticTokenModifiers2[\"abstract\"] = \"abstract\";\n  SemanticTokenModifiers2[\"async\"] = \"async\";\n  SemanticTokenModifiers2[\"modification\"] = \"modification\";\n  SemanticTokenModifiers2[\"documentation\"] = \"documentation\";\n  SemanticTokenModifiers2[\"defaultLibrary\"] = \"defaultLibrary\";\n})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));\nvar SemanticTokens;\n(function(SemanticTokens2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.resultId === void 0 || typeof candidate.resultId === \"string\") && Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === \"number\");\n  }\n  SemanticTokens2.is = is;\n})(SemanticTokens || (SemanticTokens = {}));\nvar InlineValueText;\n(function(InlineValueText2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  InlineValueText2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);\n  }\n  InlineValueText2.is = is;\n})(InlineValueText || (InlineValueText = {}));\nvar InlineValueVariableLookup;\n(function(InlineValueVariableLookup2) {\n  function create(range, variableName, caseSensitiveLookup) {\n    return { range, variableName, caseSensitiveLookup };\n  }\n  InlineValueVariableLookup2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup) && (Is.string(candidate.variableName) || candidate.variableName === void 0);\n  }\n  InlineValueVariableLookup2.is = is;\n})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));\nvar InlineValueEvaluatableExpression;\n(function(InlineValueEvaluatableExpression2) {\n  function create(range, expression) {\n    return { range, expression };\n  }\n  InlineValueEvaluatableExpression2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && (Is.string(candidate.expression) || candidate.expression === void 0);\n  }\n  InlineValueEvaluatableExpression2.is = is;\n})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));\nvar InlineValueContext;\n(function(InlineValueContext2) {\n  function create(frameId, stoppedLocation) {\n    return { frameId, stoppedLocation };\n  }\n  InlineValueContext2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.defined(candidate) && Range.is(value.stoppedLocation);\n  }\n  InlineValueContext2.is = is;\n})(InlineValueContext || (InlineValueContext = {}));\nvar InlayHintKind;\n(function(InlayHintKind2) {\n  InlayHintKind2.Type = 1;\n  InlayHintKind2.Parameter = 2;\n  function is(value) {\n    return value === 1 || value === 2;\n  }\n  InlayHintKind2.is = is;\n})(InlayHintKind || (InlayHintKind = {}));\nvar InlayHintLabelPart;\n(function(InlayHintLabelPart2) {\n  function create(value) {\n    return { value };\n  }\n  InlayHintLabelPart2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.location === void 0 || Location.is(candidate.location)) && (candidate.command === void 0 || Command.is(candidate.command));\n  }\n  InlayHintLabelPart2.is = is;\n})(InlayHintLabelPart || (InlayHintLabelPart = {}));\nvar InlayHint;\n(function(InlayHint2) {\n  function create(position, label, kind) {\n    const result = { position, label };\n    if (kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  InlayHint2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.position) && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is)) && (candidate.kind === void 0 || InlayHintKind.is(candidate.kind)) && candidate.textEdits === void 0 || Is.typedArray(candidate.textEdits, TextEdit.is) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.paddingLeft === void 0 || Is.boolean(candidate.paddingLeft)) && (candidate.paddingRight === void 0 || Is.boolean(candidate.paddingRight));\n  }\n  InlayHint2.is = is;\n})(InlayHint || (InlayHint = {}));\nvar StringValue;\n(function(StringValue2) {\n  function createSnippet(value) {\n    return { kind: \"snippet\", value };\n  }\n  StringValue2.createSnippet = createSnippet;\n})(StringValue || (StringValue = {}));\nvar InlineCompletionItem;\n(function(InlineCompletionItem2) {\n  function create(insertText, filterText, range, command) {\n    return { insertText, filterText, range, command };\n  }\n  InlineCompletionItem2.create = create;\n})(InlineCompletionItem || (InlineCompletionItem = {}));\nvar InlineCompletionList;\n(function(InlineCompletionList2) {\n  function create(items) {\n    return { items };\n  }\n  InlineCompletionList2.create = create;\n})(InlineCompletionList || (InlineCompletionList = {}));\nvar InlineCompletionTriggerKind;\n(function(InlineCompletionTriggerKind2) {\n  InlineCompletionTriggerKind2.Invoked = 0;\n  InlineCompletionTriggerKind2.Automatic = 1;\n})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));\nvar SelectedCompletionInfo;\n(function(SelectedCompletionInfo2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  SelectedCompletionInfo2.create = create;\n})(SelectedCompletionInfo || (SelectedCompletionInfo = {}));\nvar InlineCompletionContext;\n(function(InlineCompletionContext2) {\n  function create(triggerKind, selectedCompletionInfo) {\n    return { triggerKind, selectedCompletionInfo };\n  }\n  InlineCompletionContext2.create = create;\n})(InlineCompletionContext || (InlineCompletionContext = {}));\nvar WorkspaceFolder;\n(function(WorkspaceFolder2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);\n  }\n  WorkspaceFolder2.is = is;\n})(WorkspaceFolder || (WorkspaceFolder = {}));\nvar TextDocument;\n(function(TextDocument2) {\n  function create(uri, languageId, version, content) {\n    return new FullTextDocument(uri, languageId, version, content);\n  }\n  TextDocument2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount) && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n  }\n  TextDocument2.is = is;\n  function applyEdits(document, edits) {\n    let text = document.getText();\n    let sortedEdits = mergeSort(edits, (a, b) => {\n      let diff = a.range.start.line - b.range.start.line;\n      if (diff === 0) {\n        return a.range.start.character - b.range.start.character;\n      }\n      return diff;\n    });\n    let lastModifiedOffset = text.length;\n    for (let i = sortedEdits.length - 1; i >= 0; i--) {\n      let e = sortedEdits[i];\n      let startOffset = document.offsetAt(e.range.start);\n      let endOffset = document.offsetAt(e.range.end);\n      if (endOffset <= lastModifiedOffset) {\n        text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n      } else {\n        throw new Error(\"Overlapping edit\");\n      }\n      lastModifiedOffset = startOffset;\n    }\n    return text;\n  }\n  TextDocument2.applyEdits = applyEdits;\n  function mergeSort(data, compare) {\n    if (data.length <= 1) {\n      return data;\n    }\n    const p = data.length / 2 | 0;\n    const left = data.slice(0, p);\n    const right = data.slice(p);\n    mergeSort(left, compare);\n    mergeSort(right, compare);\n    let leftIdx = 0;\n    let rightIdx = 0;\n    let i = 0;\n    while (leftIdx < left.length && rightIdx < right.length) {\n      let ret = compare(left[leftIdx], right[rightIdx]);\n      if (ret <= 0) {\n        data[i++] = left[leftIdx++];\n      } else {\n        data[i++] = right[rightIdx++];\n      }\n    }\n    while (leftIdx < left.length) {\n      data[i++] = left[leftIdx++];\n    }\n    while (rightIdx < right.length) {\n      data[i++] = right[rightIdx++];\n    }\n    return data;\n  }\n})(TextDocument || (TextDocument = {}));\nvar FullTextDocument = class {\n  constructor(uri, languageId, version, content) {\n    this._uri = uri;\n    this._languageId = languageId;\n    this._version = version;\n    this._content = content;\n    this._lineOffsets = void 0;\n  }\n  get uri() {\n    return this._uri;\n  }\n  get languageId() {\n    return this._languageId;\n  }\n  get version() {\n    return this._version;\n  }\n  getText(range) {\n    if (range) {\n      let start = this.offsetAt(range.start);\n      let end = this.offsetAt(range.end);\n      return this._content.substring(start, end);\n    }\n    return this._content;\n  }\n  update(event, version) {\n    this._content = event.text;\n    this._version = version;\n    this._lineOffsets = void 0;\n  }\n  getLineOffsets() {\n    if (this._lineOffsets === void 0) {\n      let lineOffsets = [];\n      let text = this._content;\n      let isLineStart = true;\n      for (let i = 0; i < text.length; i++) {\n        if (isLineStart) {\n          lineOffsets.push(i);\n          isLineStart = false;\n        }\n        let ch = text.charAt(i);\n        isLineStart = ch === \"\\r\" || ch === \"\\n\";\n        if (ch === \"\\r\" && i + 1 < text.length && text.charAt(i + 1) === \"\\n\") {\n          i++;\n        }\n      }\n      if (isLineStart && text.length > 0) {\n        lineOffsets.push(text.length);\n      }\n      this._lineOffsets = lineOffsets;\n    }\n    return this._lineOffsets;\n  }\n  positionAt(offset) {\n    offset = Math.max(Math.min(offset, this._content.length), 0);\n    let lineOffsets = this.getLineOffsets();\n    let low = 0, high = lineOffsets.length;\n    if (high === 0) {\n      return Position.create(0, offset);\n    }\n    while (low < high) {\n      let mid = Math.floor((low + high) / 2);\n      if (lineOffsets[mid] > offset) {\n        high = mid;\n      } else {\n        low = mid + 1;\n      }\n    }\n    let line = low - 1;\n    return Position.create(line, offset - lineOffsets[line]);\n  }\n  offsetAt(position) {\n    let lineOffsets = this.getLineOffsets();\n    if (position.line >= lineOffsets.length) {\n      return this._content.length;\n    } else if (position.line < 0) {\n      return 0;\n    }\n    let lineOffset = lineOffsets[position.line];\n    let nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;\n    return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n  }\n  get lineCount() {\n    return this.getLineOffsets().length;\n  }\n};\nvar Is;\n(function(Is2) {\n  const toString = Object.prototype.toString;\n  function defined(value) {\n    return typeof value !== \"undefined\";\n  }\n  Is2.defined = defined;\n  function undefined2(value) {\n    return typeof value === \"undefined\";\n  }\n  Is2.undefined = undefined2;\n  function boolean(value) {\n    return value === true || value === false;\n  }\n  Is2.boolean = boolean;\n  function string(value) {\n    return toString.call(value) === \"[object String]\";\n  }\n  Is2.string = string;\n  function number(value) {\n    return toString.call(value) === \"[object Number]\";\n  }\n  Is2.number = number;\n  function numberRange(value, min, max) {\n    return toString.call(value) === \"[object Number]\" && min <= value && value <= max;\n  }\n  Is2.numberRange = numberRange;\n  function integer2(value) {\n    return toString.call(value) === \"[object Number]\" && -2147483648 <= value && value <= 2147483647;\n  }\n  Is2.integer = integer2;\n  function uinteger2(value) {\n    return toString.call(value) === \"[object Number]\" && 0 <= value && value <= 2147483647;\n  }\n  Is2.uinteger = uinteger2;\n  function func(value) {\n    return toString.call(value) === \"[object Function]\";\n  }\n  Is2.func = func;\n  function objectLiteral(value) {\n    return value !== null && typeof value === \"object\";\n  }\n  Is2.objectLiteral = objectLiteral;\n  function typedArray(value, check) {\n    return Array.isArray(value) && value.every(check);\n  }\n  Is2.typedArray = typedArray;\n})(Is || (Is = {}));\n\n// src/language/common/lspLanguageFeatures.ts\nvar DiagnosticsAdapter = class {\n  constructor(_languageId, _worker, configChangeEvent) {\n    this._languageId = _languageId;\n    this._worker = _worker;\n    this._disposables = [];\n    this._listener = /* @__PURE__ */ Object.create(null);\n    const onModelAdd = (model) => {\n      let modeId = model.getLanguageId();\n      if (modeId !== this._languageId) {\n        return;\n      }\n      let handle;\n      this._listener[model.uri.toString()] = model.onDidChangeContent(() => {\n        window.clearTimeout(handle);\n        handle = window.setTimeout(() => this._doValidate(model.uri, modeId), 500);\n      });\n      this._doValidate(model.uri, modeId);\n    };\n    const onModelRemoved = (model) => {\n      monaco_editor_core_exports.editor.setModelMarkers(model, this._languageId, []);\n      let uriStr = model.uri.toString();\n      let listener = this._listener[uriStr];\n      if (listener) {\n        listener.dispose();\n        delete this._listener[uriStr];\n      }\n    };\n    this._disposables.push(monaco_editor_core_exports.editor.onDidCreateModel(onModelAdd));\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel(onModelRemoved));\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        onModelRemoved(event.model);\n        onModelAdd(event.model);\n      })\n    );\n    this._disposables.push(\n      configChangeEvent((_) => {\n        monaco_editor_core_exports.editor.getModels().forEach((model) => {\n          if (model.getLanguageId() === this._languageId) {\n            onModelRemoved(model);\n            onModelAdd(model);\n          }\n        });\n      })\n    );\n    this._disposables.push({\n      dispose: () => {\n        monaco_editor_core_exports.editor.getModels().forEach(onModelRemoved);\n        for (let key in this._listener) {\n          this._listener[key].dispose();\n        }\n      }\n    });\n    monaco_editor_core_exports.editor.getModels().forEach(onModelAdd);\n  }\n  dispose() {\n    this._disposables.forEach((d) => d && d.dispose());\n    this._disposables.length = 0;\n  }\n  _doValidate(resource, languageId) {\n    this._worker(resource).then((worker2) => {\n      return worker2.doValidation(resource.toString());\n    }).then((diagnostics) => {\n      const markers = diagnostics.map((d) => toDiagnostics(resource, d));\n      let model = monaco_editor_core_exports.editor.getModel(resource);\n      if (model && model.getLanguageId() === languageId) {\n        monaco_editor_core_exports.editor.setModelMarkers(model, languageId, markers);\n      }\n    }).then(void 0, (err) => {\n      console.error(err);\n    });\n  }\n};\nfunction toSeverity(lsSeverity) {\n  switch (lsSeverity) {\n    case DiagnosticSeverity.Error:\n      return monaco_editor_core_exports.MarkerSeverity.Error;\n    case DiagnosticSeverity.Warning:\n      return monaco_editor_core_exports.MarkerSeverity.Warning;\n    case DiagnosticSeverity.Information:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n    case DiagnosticSeverity.Hint:\n      return monaco_editor_core_exports.MarkerSeverity.Hint;\n    default:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n  }\n}\nfunction toDiagnostics(resource, diag) {\n  let code = typeof diag.code === \"number\" ? String(diag.code) : diag.code;\n  return {\n    severity: toSeverity(diag.severity),\n    startLineNumber: diag.range.start.line + 1,\n    startColumn: diag.range.start.character + 1,\n    endLineNumber: diag.range.end.line + 1,\n    endColumn: diag.range.end.character + 1,\n    message: diag.message,\n    code,\n    source: diag.source\n  };\n}\nvar CompletionAdapter = class {\n  constructor(_worker, _triggerCharacters) {\n    this._worker = _worker;\n    this._triggerCharacters = _triggerCharacters;\n  }\n  get triggerCharacters() {\n    return this._triggerCharacters;\n  }\n  provideCompletionItems(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doComplete(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      const wordInfo = model.getWordUntilPosition(position);\n      const wordRange = new monaco_editor_core_exports.Range(\n        position.lineNumber,\n        wordInfo.startColumn,\n        position.lineNumber,\n        wordInfo.endColumn\n      );\n      const items = info.items.map((entry) => {\n        const item = {\n          label: entry.label,\n          insertText: entry.insertText || entry.label,\n          sortText: entry.sortText,\n          filterText: entry.filterText,\n          documentation: entry.documentation,\n          detail: entry.detail,\n          command: toCommand(entry.command),\n          range: wordRange,\n          kind: toCompletionItemKind(entry.kind)\n        };\n        if (entry.textEdit) {\n          if (isInsertReplaceEdit(entry.textEdit)) {\n            item.range = {\n              insert: toRange(entry.textEdit.insert),\n              replace: toRange(entry.textEdit.replace)\n            };\n          } else {\n            item.range = toRange(entry.textEdit.range);\n          }\n          item.insertText = entry.textEdit.newText;\n        }\n        if (entry.additionalTextEdits) {\n          item.additionalTextEdits = entry.additionalTextEdits.map(toTextEdit);\n        }\n        if (entry.insertTextFormat === InsertTextFormat.Snippet) {\n          item.insertTextRules = monaco_editor_core_exports.languages.CompletionItemInsertTextRule.InsertAsSnippet;\n        }\n        return item;\n      });\n      return {\n        isIncomplete: info.isIncomplete,\n        suggestions: items\n      };\n    });\n  }\n};\nfunction fromPosition(position) {\n  if (!position) {\n    return void 0;\n  }\n  return { character: position.column - 1, line: position.lineNumber - 1 };\n}\nfunction fromRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return {\n    start: {\n      line: range.startLineNumber - 1,\n      character: range.startColumn - 1\n    },\n    end: { line: range.endLineNumber - 1, character: range.endColumn - 1 }\n  };\n}\nfunction toRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return new monaco_editor_core_exports.Range(\n    range.start.line + 1,\n    range.start.character + 1,\n    range.end.line + 1,\n    range.end.character + 1\n  );\n}\nfunction isInsertReplaceEdit(edit) {\n  return typeof edit.insert !== \"undefined\" && typeof edit.replace !== \"undefined\";\n}\nfunction toCompletionItemKind(kind) {\n  const mItemKind = monaco_editor_core_exports.languages.CompletionItemKind;\n  switch (kind) {\n    case CompletionItemKind.Text:\n      return mItemKind.Text;\n    case CompletionItemKind.Method:\n      return mItemKind.Method;\n    case CompletionItemKind.Function:\n      return mItemKind.Function;\n    case CompletionItemKind.Constructor:\n      return mItemKind.Constructor;\n    case CompletionItemKind.Field:\n      return mItemKind.Field;\n    case CompletionItemKind.Variable:\n      return mItemKind.Variable;\n    case CompletionItemKind.Class:\n      return mItemKind.Class;\n    case CompletionItemKind.Interface:\n      return mItemKind.Interface;\n    case CompletionItemKind.Module:\n      return mItemKind.Module;\n    case CompletionItemKind.Property:\n      return mItemKind.Property;\n    case CompletionItemKind.Unit:\n      return mItemKind.Unit;\n    case CompletionItemKind.Value:\n      return mItemKind.Value;\n    case CompletionItemKind.Enum:\n      return mItemKind.Enum;\n    case CompletionItemKind.Keyword:\n      return mItemKind.Keyword;\n    case CompletionItemKind.Snippet:\n      return mItemKind.Snippet;\n    case CompletionItemKind.Color:\n      return mItemKind.Color;\n    case CompletionItemKind.File:\n      return mItemKind.File;\n    case CompletionItemKind.Reference:\n      return mItemKind.Reference;\n  }\n  return mItemKind.Property;\n}\nfunction toTextEdit(textEdit) {\n  if (!textEdit) {\n    return void 0;\n  }\n  return {\n    range: toRange(textEdit.range),\n    text: textEdit.newText\n  };\n}\nfunction toCommand(c) {\n  return c && c.command === \"editor.action.triggerSuggest\" ? { id: c.command, title: c.title, arguments: c.arguments } : void 0;\n}\nvar HoverAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideHover(model, position, token) {\n    let resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doHover(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      return {\n        range: toRange(info.range),\n        contents: toMarkedStringArray(info.contents)\n      };\n    });\n  }\n};\nfunction isMarkupContent(thing) {\n  return thing && typeof thing === \"object\" && typeof thing.kind === \"string\";\n}\nfunction toMarkdownString(entry) {\n  if (typeof entry === \"string\") {\n    return {\n      value: entry\n    };\n  }\n  if (isMarkupContent(entry)) {\n    if (entry.kind === \"plaintext\") {\n      return {\n        value: entry.value.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\")\n      };\n    }\n    return {\n      value: entry.value\n    };\n  }\n  return { value: \"```\" + entry.language + \"\\n\" + entry.value + \"\\n```\\n\" };\n}\nfunction toMarkedStringArray(contents) {\n  if (!contents) {\n    return void 0;\n  }\n  if (Array.isArray(contents)) {\n    return contents.map(toMarkdownString);\n  }\n  return [toMarkdownString(contents)];\n}\nvar DocumentHighlightAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentHighlights(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentHighlights(resource.toString(), fromPosition(position))).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map((entry) => {\n        return {\n          range: toRange(entry.range),\n          kind: toDocumentHighlightKind(entry.kind)\n        };\n      });\n    });\n  }\n};\nfunction toDocumentHighlightKind(kind) {\n  switch (kind) {\n    case DocumentHighlightKind.Read:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Read;\n    case DocumentHighlightKind.Write:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Write;\n    case DocumentHighlightKind.Text:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n  }\n  return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n}\nvar DefinitionAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDefinition(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.findDefinition(resource.toString(), fromPosition(position));\n    }).then((definition) => {\n      if (!definition) {\n        return;\n      }\n      return [toLocation(definition)];\n    });\n  }\n};\nfunction toLocation(location) {\n  return {\n    uri: monaco_editor_core_exports.Uri.parse(location.uri),\n    range: toRange(location.range)\n  };\n}\nvar ReferenceAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideReferences(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.findReferences(resource.toString(), fromPosition(position));\n    }).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map(toLocation);\n    });\n  }\n};\nvar RenameAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideRenameEdits(model, position, newName, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doRename(resource.toString(), fromPosition(position), newName);\n    }).then((edit) => {\n      return toWorkspaceEdit(edit);\n    });\n  }\n};\nfunction toWorkspaceEdit(edit) {\n  if (!edit || !edit.changes) {\n    return void 0;\n  }\n  let resourceEdits = [];\n  for (let uri in edit.changes) {\n    const _uri = monaco_editor_core_exports.Uri.parse(uri);\n    for (let e of edit.changes[uri]) {\n      resourceEdits.push({\n        resource: _uri,\n        versionId: void 0,\n        textEdit: {\n          range: toRange(e.range),\n          text: e.newText\n        }\n      });\n    }\n  }\n  return {\n    edits: resourceEdits\n  };\n}\nvar DocumentSymbolAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentSymbols(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentSymbols(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return items.map((item) => {\n        if (isDocumentSymbol(item)) {\n          return toDocumentSymbol(item);\n        }\n        return {\n          name: item.name,\n          detail: \"\",\n          containerName: item.containerName,\n          kind: toSymbolKind(item.kind),\n          range: toRange(item.location.range),\n          selectionRange: toRange(item.location.range),\n          tags: []\n        };\n      });\n    });\n  }\n};\nfunction isDocumentSymbol(symbol) {\n  return \"children\" in symbol;\n}\nfunction toDocumentSymbol(symbol) {\n  return {\n    name: symbol.name,\n    detail: symbol.detail ?? \"\",\n    kind: toSymbolKind(symbol.kind),\n    range: toRange(symbol.range),\n    selectionRange: toRange(symbol.selectionRange),\n    tags: symbol.tags ?? [],\n    children: (symbol.children ?? []).map((item) => toDocumentSymbol(item))\n  };\n}\nfunction toSymbolKind(kind) {\n  let mKind = monaco_editor_core_exports.languages.SymbolKind;\n  switch (kind) {\n    case SymbolKind.File:\n      return mKind.File;\n    case SymbolKind.Module:\n      return mKind.Module;\n    case SymbolKind.Namespace:\n      return mKind.Namespace;\n    case SymbolKind.Package:\n      return mKind.Package;\n    case SymbolKind.Class:\n      return mKind.Class;\n    case SymbolKind.Method:\n      return mKind.Method;\n    case SymbolKind.Property:\n      return mKind.Property;\n    case SymbolKind.Field:\n      return mKind.Field;\n    case SymbolKind.Constructor:\n      return mKind.Constructor;\n    case SymbolKind.Enum:\n      return mKind.Enum;\n    case SymbolKind.Interface:\n      return mKind.Interface;\n    case SymbolKind.Function:\n      return mKind.Function;\n    case SymbolKind.Variable:\n      return mKind.Variable;\n    case SymbolKind.Constant:\n      return mKind.Constant;\n    case SymbolKind.String:\n      return mKind.String;\n    case SymbolKind.Number:\n      return mKind.Number;\n    case SymbolKind.Boolean:\n      return mKind.Boolean;\n    case SymbolKind.Array:\n      return mKind.Array;\n  }\n  return mKind.Function;\n}\nvar DocumentLinkAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideLinks(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentLinks(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return {\n        links: items.map((item) => ({\n          range: toRange(item.range),\n          url: item.target\n        }))\n      };\n    });\n  }\n};\nvar DocumentFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentFormattingEdits(model, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.format(resource.toString(), null, fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nvar DocumentRangeFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n    this.canFormatMultipleRanges = false;\n  }\n  provideDocumentRangeFormattingEdits(model, range, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.format(resource.toString(), fromRange(range), fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nfunction fromFormattingOptions(options) {\n  return {\n    tabSize: options.tabSize,\n    insertSpaces: options.insertSpaces\n  };\n}\nvar DocumentColorAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentColors(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentColors(resource.toString())).then((infos) => {\n      if (!infos) {\n        return;\n      }\n      return infos.map((item) => ({\n        color: item.color,\n        range: toRange(item.range)\n      }));\n    });\n  }\n  provideColorPresentations(model, info, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker2) => worker2.getColorPresentations(resource.toString(), info.color, fromRange(info.range))\n    ).then((presentations) => {\n      if (!presentations) {\n        return;\n      }\n      return presentations.map((presentation) => {\n        let item = {\n          label: presentation.label\n        };\n        if (presentation.textEdit) {\n          item.textEdit = toTextEdit(presentation.textEdit);\n        }\n        if (presentation.additionalTextEdits) {\n          item.additionalTextEdits = presentation.additionalTextEdits.map(toTextEdit);\n        }\n        return item;\n      });\n    });\n  }\n};\nvar FoldingRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideFoldingRanges(model, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.getFoldingRanges(resource.toString(), context)).then((ranges) => {\n      if (!ranges) {\n        return;\n      }\n      return ranges.map((range) => {\n        const result = {\n          start: range.startLine + 1,\n          end: range.endLine + 1\n        };\n        if (typeof range.kind !== \"undefined\") {\n          result.kind = toFoldingRangeKind(range.kind);\n        }\n        return result;\n      });\n    });\n  }\n};\nfunction toFoldingRangeKind(kind) {\n  switch (kind) {\n    case FoldingRangeKind.Comment:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Comment;\n    case FoldingRangeKind.Imports:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Imports;\n    case FoldingRangeKind.Region:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Region;\n  }\n  return void 0;\n}\nvar SelectionRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideSelectionRanges(model, positions, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker2) => worker2.getSelectionRanges(\n        resource.toString(),\n        positions.map(fromPosition)\n      )\n    ).then((selectionRanges) => {\n      if (!selectionRanges) {\n        return;\n      }\n      return selectionRanges.map((selectionRange) => {\n        const result = [];\n        while (selectionRange) {\n          result.push({ range: toRange(selectionRange.range) });\n          selectionRange = selectionRange.parent;\n        }\n        return result;\n      });\n    });\n  }\n};\n\n// node_modules/jsonc-parser/lib/esm/impl/scanner.js\nfunction createScanner(text, ignoreTrivia = false) {\n  const len = text.length;\n  let pos = 0, value = \"\", tokenOffset = 0, token = 16, lineNumber = 0, lineStartOffset = 0, tokenLineStartOffset = 0, prevTokenLineStartOffset = 0, scanError = 0;\n  function scanHexDigits(count, exact) {\n    let digits = 0;\n    let value2 = 0;\n    while (digits < count || !exact) {\n      let ch = text.charCodeAt(pos);\n      if (ch >= 48 && ch <= 57) {\n        value2 = value2 * 16 + ch - 48;\n      } else if (ch >= 65 && ch <= 70) {\n        value2 = value2 * 16 + ch - 65 + 10;\n      } else if (ch >= 97 && ch <= 102) {\n        value2 = value2 * 16 + ch - 97 + 10;\n      } else {\n        break;\n      }\n      pos++;\n      digits++;\n    }\n    if (digits < count) {\n      value2 = -1;\n    }\n    return value2;\n  }\n  function setPosition(newPosition) {\n    pos = newPosition;\n    value = \"\";\n    tokenOffset = 0;\n    token = 16;\n    scanError = 0;\n  }\n  function scanNumber() {\n    let start = pos;\n    if (text.charCodeAt(pos) === 48) {\n      pos++;\n    } else {\n      pos++;\n      while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n      }\n    }\n    if (pos < text.length && text.charCodeAt(pos) === 46) {\n      pos++;\n      if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n        while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n          pos++;\n        }\n      } else {\n        scanError = 3;\n        return text.substring(start, pos);\n      }\n    }\n    let end = pos;\n    if (pos < text.length && (text.charCodeAt(pos) === 69 || text.charCodeAt(pos) === 101)) {\n      pos++;\n      if (pos < text.length && text.charCodeAt(pos) === 43 || text.charCodeAt(pos) === 45) {\n        pos++;\n      }\n      if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n        while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n          pos++;\n        }\n        end = pos;\n      } else {\n        scanError = 3;\n      }\n    }\n    return text.substring(start, end);\n  }\n  function scanString() {\n    let result = \"\", start = pos;\n    while (true) {\n      if (pos >= len) {\n        result += text.substring(start, pos);\n        scanError = 2;\n        break;\n      }\n      const ch = text.charCodeAt(pos);\n      if (ch === 34) {\n        result += text.substring(start, pos);\n        pos++;\n        break;\n      }\n      if (ch === 92) {\n        result += text.substring(start, pos);\n        pos++;\n        if (pos >= len) {\n          scanError = 2;\n          break;\n        }\n        const ch2 = text.charCodeAt(pos++);\n        switch (ch2) {\n          case 34:\n            result += '\"';\n            break;\n          case 92:\n            result += \"\\\\\";\n            break;\n          case 47:\n            result += \"/\";\n            break;\n          case 98:\n            result += \"\\b\";\n            break;\n          case 102:\n            result += \"\\f\";\n            break;\n          case 110:\n            result += \"\\n\";\n            break;\n          case 114:\n            result += \"\\r\";\n            break;\n          case 116:\n            result += \"\t\";\n            break;\n          case 117:\n            const ch3 = scanHexDigits(4, true);\n            if (ch3 >= 0) {\n              result += String.fromCharCode(ch3);\n            } else {\n              scanError = 4;\n            }\n            break;\n          default:\n            scanError = 5;\n        }\n        start = pos;\n        continue;\n      }\n      if (ch >= 0 && ch <= 31) {\n        if (isLineBreak(ch)) {\n          result += text.substring(start, pos);\n          scanError = 2;\n          break;\n        } else {\n          scanError = 6;\n        }\n      }\n      pos++;\n    }\n    return result;\n  }\n  function scanNext() {\n    value = \"\";\n    scanError = 0;\n    tokenOffset = pos;\n    lineStartOffset = lineNumber;\n    prevTokenLineStartOffset = tokenLineStartOffset;\n    if (pos >= len) {\n      tokenOffset = len;\n      return token = 17;\n    }\n    let code = text.charCodeAt(pos);\n    if (isWhiteSpace(code)) {\n      do {\n        pos++;\n        value += String.fromCharCode(code);\n        code = text.charCodeAt(pos);\n      } while (isWhiteSpace(code));\n      return token = 15;\n    }\n    if (isLineBreak(code)) {\n      pos++;\n      value += String.fromCharCode(code);\n      if (code === 13 && text.charCodeAt(pos) === 10) {\n        pos++;\n        value += \"\\n\";\n      }\n      lineNumber++;\n      tokenLineStartOffset = pos;\n      return token = 14;\n    }\n    switch (code) {\n      case 123:\n        pos++;\n        return token = 1;\n      case 125:\n        pos++;\n        return token = 2;\n      case 91:\n        pos++;\n        return token = 3;\n      case 93:\n        pos++;\n        return token = 4;\n      case 58:\n        pos++;\n        return token = 6;\n      case 44:\n        pos++;\n        return token = 5;\n      case 34:\n        pos++;\n        value = scanString();\n        return token = 10;\n      case 47:\n        const start = pos - 1;\n        if (text.charCodeAt(pos + 1) === 47) {\n          pos += 2;\n          while (pos < len) {\n            if (isLineBreak(text.charCodeAt(pos))) {\n              break;\n            }\n            pos++;\n          }\n          value = text.substring(start, pos);\n          return token = 12;\n        }\n        if (text.charCodeAt(pos + 1) === 42) {\n          pos += 2;\n          const safeLength = len - 1;\n          let commentClosed = false;\n          while (pos < safeLength) {\n            const ch = text.charCodeAt(pos);\n            if (ch === 42 && text.charCodeAt(pos + 1) === 47) {\n              pos += 2;\n              commentClosed = true;\n              break;\n            }\n            pos++;\n            if (isLineBreak(ch)) {\n              if (ch === 13 && text.charCodeAt(pos) === 10) {\n                pos++;\n              }\n              lineNumber++;\n              tokenLineStartOffset = pos;\n            }\n          }\n          if (!commentClosed) {\n            pos++;\n            scanError = 1;\n          }\n          value = text.substring(start, pos);\n          return token = 13;\n        }\n        value += String.fromCharCode(code);\n        pos++;\n        return token = 16;\n      case 45:\n        value += String.fromCharCode(code);\n        pos++;\n        if (pos === len || !isDigit(text.charCodeAt(pos))) {\n          return token = 16;\n        }\n      case 48:\n      case 49:\n      case 50:\n      case 51:\n      case 52:\n      case 53:\n      case 54:\n      case 55:\n      case 56:\n      case 57:\n        value += scanNumber();\n        return token = 11;\n      default:\n        while (pos < len && isUnknownContentCharacter(code)) {\n          pos++;\n          code = text.charCodeAt(pos);\n        }\n        if (tokenOffset !== pos) {\n          value = text.substring(tokenOffset, pos);\n          switch (value) {\n            case \"true\":\n              return token = 8;\n            case \"false\":\n              return token = 9;\n            case \"null\":\n              return token = 7;\n          }\n          return token = 16;\n        }\n        value += String.fromCharCode(code);\n        pos++;\n        return token = 16;\n    }\n  }\n  function isUnknownContentCharacter(code) {\n    if (isWhiteSpace(code) || isLineBreak(code)) {\n      return false;\n    }\n    switch (code) {\n      case 125:\n      case 93:\n      case 123:\n      case 91:\n      case 34:\n      case 58:\n      case 44:\n      case 47:\n        return false;\n    }\n    return true;\n  }\n  function scanNextNonTrivia() {\n    let result;\n    do {\n      result = scanNext();\n    } while (result >= 12 && result <= 15);\n    return result;\n  }\n  return {\n    setPosition,\n    getPosition: () => pos,\n    scan: ignoreTrivia ? scanNextNonTrivia : scanNext,\n    getToken: () => token,\n    getTokenValue: () => value,\n    getTokenOffset: () => tokenOffset,\n    getTokenLength: () => pos - tokenOffset,\n    getTokenStartLine: () => lineStartOffset,\n    getTokenStartCharacter: () => tokenOffset - prevTokenLineStartOffset,\n    getTokenError: () => scanError\n  };\n}\nfunction isWhiteSpace(ch) {\n  return ch === 32 || ch === 9;\n}\nfunction isLineBreak(ch) {\n  return ch === 10 || ch === 13;\n}\nfunction isDigit(ch) {\n  return ch >= 48 && ch <= 57;\n}\nvar CharacterCodes;\n(function(CharacterCodes2) {\n  CharacterCodes2[CharacterCodes2[\"lineFeed\"] = 10] = \"lineFeed\";\n  CharacterCodes2[CharacterCodes2[\"carriageReturn\"] = 13] = \"carriageReturn\";\n  CharacterCodes2[CharacterCodes2[\"space\"] = 32] = \"space\";\n  CharacterCodes2[CharacterCodes2[\"_0\"] = 48] = \"_0\";\n  CharacterCodes2[CharacterCodes2[\"_1\"] = 49] = \"_1\";\n  CharacterCodes2[CharacterCodes2[\"_2\"] = 50] = \"_2\";\n  CharacterCodes2[CharacterCodes2[\"_3\"] = 51] = \"_3\";\n  CharacterCodes2[CharacterCodes2[\"_4\"] = 52] = \"_4\";\n  CharacterCodes2[CharacterCodes2[\"_5\"] = 53] = \"_5\";\n  CharacterCodes2[CharacterCodes2[\"_6\"] = 54] = \"_6\";\n  CharacterCodes2[CharacterCodes2[\"_7\"] = 55] = \"_7\";\n  CharacterCodes2[CharacterCodes2[\"_8\"] = 56] = \"_8\";\n  CharacterCodes2[CharacterCodes2[\"_9\"] = 57] = \"_9\";\n  CharacterCodes2[CharacterCodes2[\"a\"] = 97] = \"a\";\n  CharacterCodes2[CharacterCodes2[\"b\"] = 98] = \"b\";\n  CharacterCodes2[CharacterCodes2[\"c\"] = 99] = \"c\";\n  CharacterCodes2[CharacterCodes2[\"d\"] = 100] = \"d\";\n  CharacterCodes2[CharacterCodes2[\"e\"] = 101] = \"e\";\n  CharacterCodes2[CharacterCodes2[\"f\"] = 102] = \"f\";\n  CharacterCodes2[CharacterCodes2[\"g\"] = 103] = \"g\";\n  CharacterCodes2[CharacterCodes2[\"h\"] = 104] = \"h\";\n  CharacterCodes2[CharacterCodes2[\"i\"] = 105] = \"i\";\n  CharacterCodes2[CharacterCodes2[\"j\"] = 106] = \"j\";\n  CharacterCodes2[CharacterCodes2[\"k\"] = 107] = \"k\";\n  CharacterCodes2[CharacterCodes2[\"l\"] = 108] = \"l\";\n  CharacterCodes2[CharacterCodes2[\"m\"] = 109] = \"m\";\n  CharacterCodes2[CharacterCodes2[\"n\"] = 110] = \"n\";\n  CharacterCodes2[CharacterCodes2[\"o\"] = 111] = \"o\";\n  CharacterCodes2[CharacterCodes2[\"p\"] = 112] = \"p\";\n  CharacterCodes2[CharacterCodes2[\"q\"] = 113] = \"q\";\n  CharacterCodes2[CharacterCodes2[\"r\"] = 114] = \"r\";\n  CharacterCodes2[CharacterCodes2[\"s\"] = 115] = \"s\";\n  CharacterCodes2[CharacterCodes2[\"t\"] = 116] = \"t\";\n  CharacterCodes2[CharacterCodes2[\"u\"] = 117] = \"u\";\n  CharacterCodes2[CharacterCodes2[\"v\"] = 118] = \"v\";\n  CharacterCodes2[CharacterCodes2[\"w\"] = 119] = \"w\";\n  CharacterCodes2[CharacterCodes2[\"x\"] = 120] = \"x\";\n  CharacterCodes2[CharacterCodes2[\"y\"] = 121] = \"y\";\n  CharacterCodes2[CharacterCodes2[\"z\"] = 122] = \"z\";\n  CharacterCodes2[CharacterCodes2[\"A\"] = 65] = \"A\";\n  CharacterCodes2[CharacterCodes2[\"B\"] = 66] = \"B\";\n  CharacterCodes2[CharacterCodes2[\"C\"] = 67] = \"C\";\n  CharacterCodes2[CharacterCodes2[\"D\"] = 68] = \"D\";\n  CharacterCodes2[CharacterCodes2[\"E\"] = 69] = \"E\";\n  CharacterCodes2[CharacterCodes2[\"F\"] = 70] = \"F\";\n  CharacterCodes2[CharacterCodes2[\"G\"] = 71] = \"G\";\n  CharacterCodes2[CharacterCodes2[\"H\"] = 72] = \"H\";\n  CharacterCodes2[CharacterCodes2[\"I\"] = 73] = \"I\";\n  CharacterCodes2[CharacterCodes2[\"J\"] = 74] = \"J\";\n  CharacterCodes2[CharacterCodes2[\"K\"] = 75] = \"K\";\n  CharacterCodes2[CharacterCodes2[\"L\"] = 76] = \"L\";\n  CharacterCodes2[CharacterCodes2[\"M\"] = 77] = \"M\";\n  CharacterCodes2[CharacterCodes2[\"N\"] = 78] = \"N\";\n  CharacterCodes2[CharacterCodes2[\"O\"] = 79] = \"O\";\n  CharacterCodes2[CharacterCodes2[\"P\"] = 80] = \"P\";\n  CharacterCodes2[CharacterCodes2[\"Q\"] = 81] = \"Q\";\n  CharacterCodes2[CharacterCodes2[\"R\"] = 82] = \"R\";\n  CharacterCodes2[CharacterCodes2[\"S\"] = 83] = \"S\";\n  CharacterCodes2[CharacterCodes2[\"T\"] = 84] = \"T\";\n  CharacterCodes2[CharacterCodes2[\"U\"] = 85] = \"U\";\n  CharacterCodes2[CharacterCodes2[\"V\"] = 86] = \"V\";\n  CharacterCodes2[CharacterCodes2[\"W\"] = 87] = \"W\";\n  CharacterCodes2[CharacterCodes2[\"X\"] = 88] = \"X\";\n  CharacterCodes2[CharacterCodes2[\"Y\"] = 89] = \"Y\";\n  CharacterCodes2[CharacterCodes2[\"Z\"] = 90] = \"Z\";\n  CharacterCodes2[CharacterCodes2[\"asterisk\"] = 42] = \"asterisk\";\n  CharacterCodes2[CharacterCodes2[\"backslash\"] = 92] = \"backslash\";\n  CharacterCodes2[CharacterCodes2[\"closeBrace\"] = 125] = \"closeBrace\";\n  CharacterCodes2[CharacterCodes2[\"closeBracket\"] = 93] = \"closeBracket\";\n  CharacterCodes2[CharacterCodes2[\"colon\"] = 58] = \"colon\";\n  CharacterCodes2[CharacterCodes2[\"comma\"] = 44] = \"comma\";\n  CharacterCodes2[CharacterCodes2[\"dot\"] = 46] = \"dot\";\n  CharacterCodes2[CharacterCodes2[\"doubleQuote\"] = 34] = \"doubleQuote\";\n  CharacterCodes2[CharacterCodes2[\"minus\"] = 45] = \"minus\";\n  CharacterCodes2[CharacterCodes2[\"openBrace\"] = 123] = \"openBrace\";\n  CharacterCodes2[CharacterCodes2[\"openBracket\"] = 91] = \"openBracket\";\n  CharacterCodes2[CharacterCodes2[\"plus\"] = 43] = \"plus\";\n  CharacterCodes2[CharacterCodes2[\"slash\"] = 47] = \"slash\";\n  CharacterCodes2[CharacterCodes2[\"formFeed\"] = 12] = \"formFeed\";\n  CharacterCodes2[CharacterCodes2[\"tab\"] = 9] = \"tab\";\n})(CharacterCodes || (CharacterCodes = {}));\n\n// node_modules/jsonc-parser/lib/esm/impl/string-intern.js\nvar cachedSpaces = new Array(20).fill(0).map((_, index) => {\n  return \" \".repeat(index);\n});\nvar maxCachedValues = 200;\nvar cachedBreakLinesWithSpaces = {\n  \" \": {\n    \"\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\n\" + \" \".repeat(index);\n    }),\n    \"\\r\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\" + \" \".repeat(index);\n    }),\n    \"\\r\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\\n\" + \" \".repeat(index);\n    })\n  },\n  \"\t\": {\n    \"\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\n\" + \"\t\".repeat(index);\n    }),\n    \"\\r\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\" + \"\t\".repeat(index);\n    }),\n    \"\\r\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\\n\" + \"\t\".repeat(index);\n    })\n  }\n};\n\n// node_modules/jsonc-parser/lib/esm/impl/parser.js\nvar ParseOptions;\n(function(ParseOptions2) {\n  ParseOptions2.DEFAULT = {\n    allowTrailingComma: false\n  };\n})(ParseOptions || (ParseOptions = {}));\n\n// node_modules/jsonc-parser/lib/esm/main.js\nvar createScanner2 = createScanner;\nvar ScanError;\n(function(ScanError2) {\n  ScanError2[ScanError2[\"None\"] = 0] = \"None\";\n  ScanError2[ScanError2[\"UnexpectedEndOfComment\"] = 1] = \"UnexpectedEndOfComment\";\n  ScanError2[ScanError2[\"UnexpectedEndOfString\"] = 2] = \"UnexpectedEndOfString\";\n  ScanError2[ScanError2[\"UnexpectedEndOfNumber\"] = 3] = \"UnexpectedEndOfNumber\";\n  ScanError2[ScanError2[\"InvalidUnicode\"] = 4] = \"InvalidUnicode\";\n  ScanError2[ScanError2[\"InvalidEscapeCharacter\"] = 5] = \"InvalidEscapeCharacter\";\n  ScanError2[ScanError2[\"InvalidCharacter\"] = 6] = \"InvalidCharacter\";\n})(ScanError || (ScanError = {}));\nvar SyntaxKind;\n(function(SyntaxKind2) {\n  SyntaxKind2[SyntaxKind2[\"OpenBraceToken\"] = 1] = \"OpenBraceToken\";\n  SyntaxKind2[SyntaxKind2[\"CloseBraceToken\"] = 2] = \"CloseBraceToken\";\n  SyntaxKind2[SyntaxKind2[\"OpenBracketToken\"] = 3] = \"OpenBracketToken\";\n  SyntaxKind2[SyntaxKind2[\"CloseBracketToken\"] = 4] = \"CloseBracketToken\";\n  SyntaxKind2[SyntaxKind2[\"CommaToken\"] = 5] = \"CommaToken\";\n  SyntaxKind2[SyntaxKind2[\"ColonToken\"] = 6] = \"ColonToken\";\n  SyntaxKind2[SyntaxKind2[\"NullKeyword\"] = 7] = \"NullKeyword\";\n  SyntaxKind2[SyntaxKind2[\"TrueKeyword\"] = 8] = \"TrueKeyword\";\n  SyntaxKind2[SyntaxKind2[\"FalseKeyword\"] = 9] = \"FalseKeyword\";\n  SyntaxKind2[SyntaxKind2[\"StringLiteral\"] = 10] = \"StringLiteral\";\n  SyntaxKind2[SyntaxKind2[\"NumericLiteral\"] = 11] = \"NumericLiteral\";\n  SyntaxKind2[SyntaxKind2[\"LineCommentTrivia\"] = 12] = \"LineCommentTrivia\";\n  SyntaxKind2[SyntaxKind2[\"BlockCommentTrivia\"] = 13] = \"BlockCommentTrivia\";\n  SyntaxKind2[SyntaxKind2[\"LineBreakTrivia\"] = 14] = \"LineBreakTrivia\";\n  SyntaxKind2[SyntaxKind2[\"Trivia\"] = 15] = \"Trivia\";\n  SyntaxKind2[SyntaxKind2[\"Unknown\"] = 16] = \"Unknown\";\n  SyntaxKind2[SyntaxKind2[\"EOF\"] = 17] = \"EOF\";\n})(SyntaxKind || (SyntaxKind = {}));\nvar ParseErrorCode;\n(function(ParseErrorCode2) {\n  ParseErrorCode2[ParseErrorCode2[\"InvalidSymbol\"] = 1] = \"InvalidSymbol\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidNumberFormat\"] = 2] = \"InvalidNumberFormat\";\n  ParseErrorCode2[ParseErrorCode2[\"PropertyNameExpected\"] = 3] = \"PropertyNameExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"ValueExpected\"] = 4] = \"ValueExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"ColonExpected\"] = 5] = \"ColonExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CommaExpected\"] = 6] = \"CommaExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CloseBraceExpected\"] = 7] = \"CloseBraceExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CloseBracketExpected\"] = 8] = \"CloseBracketExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"EndOfFileExpected\"] = 9] = \"EndOfFileExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidCommentToken\"] = 10] = \"InvalidCommentToken\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfComment\"] = 11] = \"UnexpectedEndOfComment\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfString\"] = 12] = \"UnexpectedEndOfString\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfNumber\"] = 13] = \"UnexpectedEndOfNumber\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidUnicode\"] = 14] = \"InvalidUnicode\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidEscapeCharacter\"] = 15] = \"InvalidEscapeCharacter\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidCharacter\"] = 16] = \"InvalidCharacter\";\n})(ParseErrorCode || (ParseErrorCode = {}));\n\n// src/language/json/tokenization.ts\nfunction createTokenizationSupport(supportComments) {\n  return {\n    getInitialState: () => new JSONState(null, null, false, null),\n    tokenize: (line, state) => tokenize(supportComments, line, state)\n  };\n}\nvar TOKEN_DELIM_OBJECT = \"delimiter.bracket.json\";\nvar TOKEN_DELIM_ARRAY = \"delimiter.array.json\";\nvar TOKEN_DELIM_COLON = \"delimiter.colon.json\";\nvar TOKEN_DELIM_COMMA = \"delimiter.comma.json\";\nvar TOKEN_VALUE_BOOLEAN = \"keyword.json\";\nvar TOKEN_VALUE_NULL = \"keyword.json\";\nvar TOKEN_VALUE_STRING = \"string.value.json\";\nvar TOKEN_VALUE_NUMBER = \"number.json\";\nvar TOKEN_PROPERTY_NAME = \"string.key.json\";\nvar TOKEN_COMMENT_BLOCK = \"comment.block.json\";\nvar TOKEN_COMMENT_LINE = \"comment.line.json\";\nvar ParentsStack = class _ParentsStack {\n  constructor(parent, type) {\n    this.parent = parent;\n    this.type = type;\n  }\n  static pop(parents) {\n    if (parents) {\n      return parents.parent;\n    }\n    return null;\n  }\n  static push(parents, type) {\n    return new _ParentsStack(parents, type);\n  }\n  static equals(a, b) {\n    if (!a && !b) {\n      return true;\n    }\n    if (!a || !b) {\n      return false;\n    }\n    while (a && b) {\n      if (a === b) {\n        return true;\n      }\n      if (a.type !== b.type) {\n        return false;\n      }\n      a = a.parent;\n      b = b.parent;\n    }\n    return true;\n  }\n};\nvar JSONState = class _JSONState {\n  constructor(state, scanError, lastWasColon, parents) {\n    this._state = state;\n    this.scanError = scanError;\n    this.lastWasColon = lastWasColon;\n    this.parents = parents;\n  }\n  clone() {\n    return new _JSONState(this._state, this.scanError, this.lastWasColon, this.parents);\n  }\n  equals(other) {\n    if (other === this) {\n      return true;\n    }\n    if (!other || !(other instanceof _JSONState)) {\n      return false;\n    }\n    return this.scanError === other.scanError && this.lastWasColon === other.lastWasColon && ParentsStack.equals(this.parents, other.parents);\n  }\n  getStateData() {\n    return this._state;\n  }\n  setStateData(state) {\n    this._state = state;\n  }\n};\nfunction tokenize(comments, line, state, offsetDelta = 0) {\n  let numberOfInsertedCharacters = 0;\n  let adjustOffset = false;\n  switch (state.scanError) {\n    case 2 /* UnexpectedEndOfString */:\n      line = '\"' + line;\n      numberOfInsertedCharacters = 1;\n      break;\n    case 1 /* UnexpectedEndOfComment */:\n      line = \"/*\" + line;\n      numberOfInsertedCharacters = 2;\n      break;\n  }\n  const scanner = createScanner2(line);\n  let lastWasColon = state.lastWasColon;\n  let parents = state.parents;\n  const ret = {\n    tokens: [],\n    endState: state.clone()\n  };\n  while (true) {\n    let offset = offsetDelta + scanner.getPosition();\n    let type = \"\";\n    const kind = scanner.scan();\n    if (kind === 17 /* EOF */) {\n      break;\n    }\n    if (offset === offsetDelta + scanner.getPosition()) {\n      throw new Error(\n        \"Scanner did not advance, next 3 characters are: \" + line.substr(scanner.getPosition(), 3)\n      );\n    }\n    if (adjustOffset) {\n      offset -= numberOfInsertedCharacters;\n    }\n    adjustOffset = numberOfInsertedCharacters > 0;\n    switch (kind) {\n      case 1 /* OpenBraceToken */:\n        parents = ParentsStack.push(parents, 0 /* Object */);\n        type = TOKEN_DELIM_OBJECT;\n        lastWasColon = false;\n        break;\n      case 2 /* CloseBraceToken */:\n        parents = ParentsStack.pop(parents);\n        type = TOKEN_DELIM_OBJECT;\n        lastWasColon = false;\n        break;\n      case 3 /* OpenBracketToken */:\n        parents = ParentsStack.push(parents, 1 /* Array */);\n        type = TOKEN_DELIM_ARRAY;\n        lastWasColon = false;\n        break;\n      case 4 /* CloseBracketToken */:\n        parents = ParentsStack.pop(parents);\n        type = TOKEN_DELIM_ARRAY;\n        lastWasColon = false;\n        break;\n      case 6 /* ColonToken */:\n        type = TOKEN_DELIM_COLON;\n        lastWasColon = true;\n        break;\n      case 5 /* CommaToken */:\n        type = TOKEN_DELIM_COMMA;\n        lastWasColon = false;\n        break;\n      case 8 /* TrueKeyword */:\n      case 9 /* FalseKeyword */:\n        type = TOKEN_VALUE_BOOLEAN;\n        lastWasColon = false;\n        break;\n      case 7 /* NullKeyword */:\n        type = TOKEN_VALUE_NULL;\n        lastWasColon = false;\n        break;\n      case 10 /* StringLiteral */:\n        const currentParent = parents ? parents.type : 0 /* Object */;\n        const inArray = currentParent === 1 /* Array */;\n        type = lastWasColon || inArray ? TOKEN_VALUE_STRING : TOKEN_PROPERTY_NAME;\n        lastWasColon = false;\n        break;\n      case 11 /* NumericLiteral */:\n        type = TOKEN_VALUE_NUMBER;\n        lastWasColon = false;\n        break;\n    }\n    if (comments) {\n      switch (kind) {\n        case 12 /* LineCommentTrivia */:\n          type = TOKEN_COMMENT_LINE;\n          break;\n        case 13 /* BlockCommentTrivia */:\n          type = TOKEN_COMMENT_BLOCK;\n          break;\n      }\n    }\n    ret.endState = new JSONState(\n      state.getStateData(),\n      scanner.getTokenError(),\n      lastWasColon,\n      parents\n    );\n    ret.tokens.push({\n      startIndex: offset,\n      scopes: type\n    });\n  }\n  return ret;\n}\n\n// src/language/json/jsonMode.ts\nvar worker;\nfunction getWorker() {\n  return new Promise((resolve, reject) => {\n    if (!worker) {\n      return reject(\"JSON not registered!\");\n    }\n    resolve(worker);\n  });\n}\nvar JSONDiagnosticsAdapter = class extends DiagnosticsAdapter {\n  constructor(languageId, worker2, defaults) {\n    super(languageId, worker2, defaults.onDidChange);\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onWillDisposeModel((model) => {\n        this._resetSchema(model.uri);\n      })\n    );\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        this._resetSchema(event.model.uri);\n      })\n    );\n  }\n  _resetSchema(resource) {\n    this._worker().then((worker2) => {\n      worker2.resetSchema(resource.toString());\n    });\n  }\n};\nfunction setupMode(defaults) {\n  const disposables = [];\n  const providers = [];\n  const client = new WorkerManager(defaults);\n  disposables.push(client);\n  worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  function registerProviders() {\n    const { languageId, modeConfiguration: modeConfiguration2 } = defaults;\n    disposeAll(providers);\n    if (modeConfiguration2.documentFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentFormattingEditProvider(\n          languageId,\n          new DocumentFormattingEditProvider(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.documentRangeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(\n          languageId,\n          new DocumentRangeFormattingEditProvider(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.completionItems) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCompletionItemProvider(\n          languageId,\n          new CompletionAdapter(worker, [\" \", \":\", '\"'])\n        )\n      );\n    }\n    if (modeConfiguration2.hovers) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerHoverProvider(languageId, new HoverAdapter(worker))\n      );\n    }\n    if (modeConfiguration2.documentSymbols) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentSymbolProvider(\n          languageId,\n          new DocumentSymbolAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.tokens) {\n      providers.push(monaco_editor_core_exports.languages.setTokensProvider(languageId, createTokenizationSupport(true)));\n    }\n    if (modeConfiguration2.colors) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerColorProvider(\n          languageId,\n          new DocumentColorAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.foldingRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerFoldingRangeProvider(\n          languageId,\n          new FoldingRangeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.diagnostics) {\n      providers.push(new JSONDiagnosticsAdapter(languageId, worker, defaults));\n    }\n    if (modeConfiguration2.selectionRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerSelectionRangeProvider(\n          languageId,\n          new SelectionRangeAdapter(worker)\n        )\n      );\n    }\n  }\n  registerProviders();\n  disposables.push(monaco_editor_core_exports.languages.setLanguageConfiguration(defaults.languageId, richEditConfiguration));\n  let modeConfiguration = defaults.modeConfiguration;\n  defaults.onDidChange((newDefaults) => {\n    if (newDefaults.modeConfiguration !== modeConfiguration) {\n      modeConfiguration = newDefaults.modeConfiguration;\n      registerProviders();\n    }\n  });\n  disposables.push(asDisposable(providers));\n  return asDisposable(disposables);\n}\nfunction asDisposable(disposables) {\n  return { dispose: () => disposeAll(disposables) };\n}\nfunction disposeAll(disposables) {\n  while (disposables.length) {\n    disposables.pop().dispose();\n  }\n}\nvar richEditConfiguration = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\[\\{\\]\\}\\:\\\"\\,\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ]\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbW9uYWNvLWVkaXRvci9lc20vdnMvbGFuZ3VhZ2UvanNvbi9qc29uTW9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsNEZBQTRGO0FBQ3pIO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSx1Q0FBdUMsa0RBQXVCO0FBQ1E7O0FBRXRFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxrQ0FBa0M7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxrQkFBa0I7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsMEJBQTBCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDRCQUE0QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDRCQUE0QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWU7QUFDZixNQUFNO0FBQ04sZUFBZTtBQUNmLE1BQU07QUFDTixvRUFBb0UsSUFBSSxJQUFJLElBQUksSUFBSSxNQUFNLElBQUksS0FBSztBQUNuRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxzQkFBc0I7QUFDdkI7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw0QkFBNEI7QUFDN0I7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQ0FBb0M7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHNCQUFzQjtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw0Q0FBNEM7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw4Q0FBOEM7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsNENBQTRDO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsb0NBQW9DO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLG9FQUFvRTtBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGdEQUFnRDtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsc0NBQXNDO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwQ0FBMEM7QUFDM0M7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsZ0NBQWdDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwQkFBMEI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGFBQWEsU0FBUyxnQ0FBZ0M7QUFDdEQ7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw0QkFBNEI7QUFDN0I7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDRDQUE0QztBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsZ0VBQWdFO0FBQ2pFO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxhQUFhLFNBQVMsZ0NBQWdDO0FBQ3REO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsOENBQThDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsNENBQTRDO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsZ0NBQWdDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxnQ0FBZ0M7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxnQ0FBZ0M7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsQ0FBQyxzQ0FBc0M7QUFDdkM7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyx3REFBd0Q7QUFDekQ7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwRUFBMEU7QUFDM0U7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwRkFBMEY7QUFDM0Y7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw0Q0FBNEM7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxnQ0FBZ0M7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHNDQUFzQztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGdEQUFnRDtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsNENBQTRDO0FBQzdDO0FBQ0E7QUFDQTtBQUNBLENBQUMsOENBQThDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsOENBQThDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyx3Q0FBd0M7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGdFQUFnRTtBQUNqRTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLENBQUMsd0NBQXdDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsQ0FBQyx3Q0FBd0M7QUFDekM7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQ0FBb0M7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHNCQUFzQjtBQUN2QjtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsdUJBQXVCLElBQUk7QUFDeEQ7QUFDQTtBQUNBLENBQUMsb0RBQW9EO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLG9EQUFvRDtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxzREFBc0Q7QUFDdkQ7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsOENBQThDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxnQ0FBZ0M7QUFDakM7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw4QkFBOEI7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw4Q0FBOEM7QUFDL0M7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLHdCQUF3QixlQUFlLElBQUksd0JBQXdCO0FBQ25HO0FBQ0E7QUFDQSxDQUFDLDBDQUEwQztBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHdDQUF3QztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyx3Q0FBd0M7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHNEQUFzRDtBQUN2RDtBQUNBO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsOENBQThDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsZ0NBQWdDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw0QkFBNEI7QUFDN0I7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw4Q0FBOEM7QUFDL0M7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQ0FBb0M7QUFDckM7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyx3Q0FBd0M7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGdEQUFnRDtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHdEQUF3RDtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsd0NBQXdDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsMENBQTBDO0FBQzNDO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsOERBQThEO0FBQy9EO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsNEVBQTRFO0FBQzdFO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsZ0RBQWdEO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHNDQUFzQztBQUN2QztBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGdEQUFnRDtBQUNqRDtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsOEJBQThCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsQ0FBQyxrQ0FBa0M7QUFDbkM7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxDQUFDLG9EQUFvRDtBQUNyRDtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLENBQUMsb0RBQW9EO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxrRUFBa0U7QUFDbkU7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxDQUFDLHdEQUF3RDtBQUN6RDtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLENBQUMsMERBQTBEO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwQ0FBMEM7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSx5Q0FBeUMsUUFBUTtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsb0NBQW9DO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsaUJBQWlCO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsZ0JBQWdCOztBQUVqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0Qsd0RBQXdEO0FBQ3ZIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHNDQUFzQztBQUM5RDtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHdDQUF3Qzs7QUFFekM7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQ0FBb0M7O0FBRXJDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDhCQUE4QjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsZ0NBQWdDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsd0NBQXdDOztBQUV6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksb0RBQW9EO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsSUFBSTtBQUMxQztBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxPQUFPLEtBQUs7QUFDWjtBQUNBO0FBQ0E7QUFDQSxNQUFNLFFBQVEsWUFBWSxzQkFBc0I7QUFDaEQsTUFBTSwwQ0FBMEM7QUFDaEQsTUFBTTtBQUNOO0FBQ0E7QUF1QkUiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0XFxyZWFsY29kZVxcbm9kZV9tb2R1bGVzXFxtb25hY28tZWRpdG9yXFxlc21cXHZzXFxsYW5ndWFnZVxcanNvblxcanNvbk1vZGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyohLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICogVmVyc2lvbjogMC41Mi4yKDQwNDU0NWJkZWQxZGY2ZmZhNDFlYTBhZjRlOGRkYjIxOTAxOGM2YzEpXG4gKiBSZWxlYXNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2VcbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9taWNyb3NvZnQvbW9uYWNvLWVkaXRvci9ibG9iL21haW4vTElDRU5TRS50eHRcbiAqLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG52YXIgX19kZWZQcm9wID0gT2JqZWN0LmRlZmluZVByb3BlcnR5O1xudmFyIF9fZ2V0T3duUHJvcERlc2MgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yO1xudmFyIF9fZ2V0T3duUHJvcE5hbWVzID0gT2JqZWN0LmdldE93blByb3BlcnR5TmFtZXM7XG52YXIgX19oYXNPd25Qcm9wID0gT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eTtcbnZhciBfX2NvcHlQcm9wcyA9ICh0bywgZnJvbSwgZXhjZXB0LCBkZXNjKSA9PiB7XG4gIGlmIChmcm9tICYmIHR5cGVvZiBmcm9tID09PSBcIm9iamVjdFwiIHx8IHR5cGVvZiBmcm9tID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICBmb3IgKGxldCBrZXkgb2YgX19nZXRPd25Qcm9wTmFtZXMoZnJvbSkpXG4gICAgICBpZiAoIV9faGFzT3duUHJvcC5jYWxsKHRvLCBrZXkpICYmIGtleSAhPT0gZXhjZXB0KVxuICAgICAgICBfX2RlZlByb3AodG8sIGtleSwgeyBnZXQ6ICgpID0+IGZyb21ba2V5XSwgZW51bWVyYWJsZTogIShkZXNjID0gX19nZXRPd25Qcm9wRGVzYyhmcm9tLCBrZXkpKSB8fCBkZXNjLmVudW1lcmFibGUgfSk7XG4gIH1cbiAgcmV0dXJuIHRvO1xufTtcbnZhciBfX3JlRXhwb3J0ID0gKHRhcmdldCwgbW9kLCBzZWNvbmRUYXJnZXQpID0+IChfX2NvcHlQcm9wcyh0YXJnZXQsIG1vZCwgXCJkZWZhdWx0XCIpLCBzZWNvbmRUYXJnZXQgJiYgX19jb3B5UHJvcHMoc2Vjb25kVGFyZ2V0LCBtb2QsIFwiZGVmYXVsdFwiKSk7XG5cbi8vIHNyYy9maWxsZXJzL21vbmFjby1lZGl0b3ItY29yZS50c1xudmFyIG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzID0ge307XG5fX3JlRXhwb3J0KG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLCBtb25hY29fZWRpdG9yX2NvcmVfc3Rhcik7XG5pbXBvcnQgKiBhcyBtb25hY29fZWRpdG9yX2NvcmVfc3RhciBmcm9tIFwiLi4vLi4vZWRpdG9yL2VkaXRvci5hcGkuanNcIjtcblxuLy8gc3JjL2xhbmd1YWdlL2pzb24vd29ya2VyTWFuYWdlci50c1xudmFyIFNUT1BfV0hFTl9JRExFX0ZPUiA9IDIgKiA2MCAqIDFlMztcbnZhciBXb3JrZXJNYW5hZ2VyID0gY2xhc3Mge1xuICBjb25zdHJ1Y3RvcihkZWZhdWx0cykge1xuICAgIHRoaXMuX2RlZmF1bHRzID0gZGVmYXVsdHM7XG4gICAgdGhpcy5fd29ya2VyID0gbnVsbDtcbiAgICB0aGlzLl9jbGllbnQgPSBudWxsO1xuICAgIHRoaXMuX2lkbGVDaGVja0ludGVydmFsID0gd2luZG93LnNldEludGVydmFsKCgpID0+IHRoaXMuX2NoZWNrSWZJZGxlKCksIDMwICogMWUzKTtcbiAgICB0aGlzLl9sYXN0VXNlZFRpbWUgPSAwO1xuICAgIHRoaXMuX2NvbmZpZ0NoYW5nZUxpc3RlbmVyID0gdGhpcy5fZGVmYXVsdHMub25EaWRDaGFuZ2UoKCkgPT4gdGhpcy5fc3RvcFdvcmtlcigpKTtcbiAgfVxuICBfc3RvcFdvcmtlcigpIHtcbiAgICBpZiAodGhpcy5fd29ya2VyKSB7XG4gICAgICB0aGlzLl93b3JrZXIuZGlzcG9zZSgpO1xuICAgICAgdGhpcy5fd29ya2VyID0gbnVsbDtcbiAgICB9XG4gICAgdGhpcy5fY2xpZW50ID0gbnVsbDtcbiAgfVxuICBkaXNwb3NlKCkge1xuICAgIGNsZWFySW50ZXJ2YWwodGhpcy5faWRsZUNoZWNrSW50ZXJ2YWwpO1xuICAgIHRoaXMuX2NvbmZpZ0NoYW5nZUxpc3RlbmVyLmRpc3Bvc2UoKTtcbiAgICB0aGlzLl9zdG9wV29ya2VyKCk7XG4gIH1cbiAgX2NoZWNrSWZJZGxlKCkge1xuICAgIGlmICghdGhpcy5fd29ya2VyKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIGxldCB0aW1lUGFzc2VkU2luY2VMYXN0VXNlZCA9IERhdGUubm93KCkgLSB0aGlzLl9sYXN0VXNlZFRpbWU7XG4gICAgaWYgKHRpbWVQYXNzZWRTaW5jZUxhc3RVc2VkID4gU1RPUF9XSEVOX0lETEVfRk9SKSB7XG4gICAgICB0aGlzLl9zdG9wV29ya2VyKCk7XG4gICAgfVxuICB9XG4gIF9nZXRDbGllbnQoKSB7XG4gICAgdGhpcy5fbGFzdFVzZWRUaW1lID0gRGF0ZS5ub3coKTtcbiAgICBpZiAoIXRoaXMuX2NsaWVudCkge1xuICAgICAgdGhpcy5fd29ya2VyID0gbW9uYWNvX2VkaXRvcl9jb3JlX2V4cG9ydHMuZWRpdG9yLmNyZWF0ZVdlYldvcmtlcih7XG4gICAgICAgIC8vIG1vZHVsZSB0aGF0IGV4cG9ydHMgdGhlIGNyZWF0ZSgpIG1ldGhvZCBhbmQgcmV0dXJucyBhIGBKU09OV29ya2VyYCBpbnN0YW5jZVxuICAgICAgICBtb2R1bGVJZDogXCJ2cy9sYW5ndWFnZS9qc29uL2pzb25Xb3JrZXJcIixcbiAgICAgICAgbGFiZWw6IHRoaXMuX2RlZmF1bHRzLmxhbmd1YWdlSWQsXG4gICAgICAgIC8vIHBhc3NlZCBpbiB0byB0aGUgY3JlYXRlKCkgbWV0aG9kXG4gICAgICAgIGNyZWF0ZURhdGE6IHtcbiAgICAgICAgICBsYW5ndWFnZVNldHRpbmdzOiB0aGlzLl9kZWZhdWx0cy5kaWFnbm9zdGljc09wdGlvbnMsXG4gICAgICAgICAgbGFuZ3VhZ2VJZDogdGhpcy5fZGVmYXVsdHMubGFuZ3VhZ2VJZCxcbiAgICAgICAgICBlbmFibGVTY2hlbWFSZXF1ZXN0OiB0aGlzLl9kZWZhdWx0cy5kaWFnbm9zdGljc09wdGlvbnMuZW5hYmxlU2NoZW1hUmVxdWVzdFxuICAgICAgICB9XG4gICAgICB9KTtcbiAgICAgIHRoaXMuX2NsaWVudCA9IHRoaXMuX3dvcmtlci5nZXRQcm94eSgpO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy5fY2xpZW50O1xuICB9XG4gIGdldExhbmd1YWdlU2VydmljZVdvcmtlciguLi5yZXNvdXJjZXMpIHtcbiAgICBsZXQgX2NsaWVudDtcbiAgICByZXR1cm4gdGhpcy5fZ2V0Q2xpZW50KCkudGhlbigoY2xpZW50KSA9PiB7XG4gICAgICBfY2xpZW50ID0gY2xpZW50O1xuICAgIH0pLnRoZW4oKF8pID0+IHtcbiAgICAgIGlmICh0aGlzLl93b3JrZXIpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3dvcmtlci53aXRoU3luY2VkUmVzb3VyY2VzKHJlc291cmNlcyk7XG4gICAgICB9XG4gICAgfSkudGhlbigoXykgPT4gX2NsaWVudCk7XG4gIH1cbn07XG5cbi8vIG5vZGVfbW9kdWxlcy92c2NvZGUtbGFuZ3VhZ2VzZXJ2ZXItdHlwZXMvbGliL2VzbS9tYWluLmpzXG52YXIgRG9jdW1lbnRVcmk7XG4oZnVuY3Rpb24oRG9jdW1lbnRVcmkyKSB7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgcmV0dXJuIHR5cGVvZiB2YWx1ZSA9PT0gXCJzdHJpbmdcIjtcbiAgfVxuICBEb2N1bWVudFVyaTIuaXMgPSBpcztcbn0pKERvY3VtZW50VXJpIHx8IChEb2N1bWVudFVyaSA9IHt9KSk7XG52YXIgVVJJO1xuKGZ1bmN0aW9uKFVSSTIpIHtcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICByZXR1cm4gdHlwZW9mIHZhbHVlID09PSBcInN0cmluZ1wiO1xuICB9XG4gIFVSSTIuaXMgPSBpcztcbn0pKFVSSSB8fCAoVVJJID0ge30pKTtcbnZhciBpbnRlZ2VyO1xuKGZ1bmN0aW9uKGludGVnZXIyKSB7XG4gIGludGVnZXIyLk1JTl9WQUxVRSA9IC0yMTQ3NDgzNjQ4O1xuICBpbnRlZ2VyMi5NQVhfVkFMVUUgPSAyMTQ3NDgzNjQ3O1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIHJldHVybiB0eXBlb2YgdmFsdWUgPT09IFwibnVtYmVyXCIgJiYgaW50ZWdlcjIuTUlOX1ZBTFVFIDw9IHZhbHVlICYmIHZhbHVlIDw9IGludGVnZXIyLk1BWF9WQUxVRTtcbiAgfVxuICBpbnRlZ2VyMi5pcyA9IGlzO1xufSkoaW50ZWdlciB8fCAoaW50ZWdlciA9IHt9KSk7XG52YXIgdWludGVnZXI7XG4oZnVuY3Rpb24odWludGVnZXIyKSB7XG4gIHVpbnRlZ2VyMi5NSU5fVkFMVUUgPSAwO1xuICB1aW50ZWdlcjIuTUFYX1ZBTFVFID0gMjE0NzQ4MzY0NztcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICByZXR1cm4gdHlwZW9mIHZhbHVlID09PSBcIm51bWJlclwiICYmIHVpbnRlZ2VyMi5NSU5fVkFMVUUgPD0gdmFsdWUgJiYgdmFsdWUgPD0gdWludGVnZXIyLk1BWF9WQUxVRTtcbiAgfVxuICB1aW50ZWdlcjIuaXMgPSBpcztcbn0pKHVpbnRlZ2VyIHx8ICh1aW50ZWdlciA9IHt9KSk7XG52YXIgUG9zaXRpb247XG4oZnVuY3Rpb24oUG9zaXRpb24zKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZShsaW5lLCBjaGFyYWN0ZXIpIHtcbiAgICBpZiAobGluZSA9PT0gTnVtYmVyLk1BWF9WQUxVRSkge1xuICAgICAgbGluZSA9IHVpbnRlZ2VyLk1BWF9WQUxVRTtcbiAgICB9XG4gICAgaWYgKGNoYXJhY3RlciA9PT0gTnVtYmVyLk1BWF9WQUxVRSkge1xuICAgICAgY2hhcmFjdGVyID0gdWludGVnZXIuTUFYX1ZBTFVFO1xuICAgIH1cbiAgICByZXR1cm4geyBsaW5lLCBjaGFyYWN0ZXIgfTtcbiAgfVxuICBQb3NpdGlvbjMuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGxldCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gSXMub2JqZWN0TGl0ZXJhbChjYW5kaWRhdGUpICYmIElzLnVpbnRlZ2VyKGNhbmRpZGF0ZS5saW5lKSAmJiBJcy51aW50ZWdlcihjYW5kaWRhdGUuY2hhcmFjdGVyKTtcbiAgfVxuICBQb3NpdGlvbjMuaXMgPSBpcztcbn0pKFBvc2l0aW9uIHx8IChQb3NpdGlvbiA9IHt9KSk7XG52YXIgUmFuZ2U7XG4oZnVuY3Rpb24oUmFuZ2UzKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZShvbmUsIHR3bywgdGhyZWUsIGZvdXIpIHtcbiAgICBpZiAoSXMudWludGVnZXIob25lKSAmJiBJcy51aW50ZWdlcih0d28pICYmIElzLnVpbnRlZ2VyKHRocmVlKSAmJiBJcy51aW50ZWdlcihmb3VyKSkge1xuICAgICAgcmV0dXJuIHsgc3RhcnQ6IFBvc2l0aW9uLmNyZWF0ZShvbmUsIHR3byksIGVuZDogUG9zaXRpb24uY3JlYXRlKHRocmVlLCBmb3VyKSB9O1xuICAgIH0gZWxzZSBpZiAoUG9zaXRpb24uaXMob25lKSAmJiBQb3NpdGlvbi5pcyh0d28pKSB7XG4gICAgICByZXR1cm4geyBzdGFydDogb25lLCBlbmQ6IHR3byB9O1xuICAgIH0gZWxzZSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFJhbmdlI2NyZWF0ZSBjYWxsZWQgd2l0aCBpbnZhbGlkIGFyZ3VtZW50c1ske29uZX0sICR7dHdvfSwgJHt0aHJlZX0sICR7Zm91cn1dYCk7XG4gICAgfVxuICB9XG4gIFJhbmdlMy5jcmVhdGUgPSBjcmVhdGU7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgbGV0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBJcy5vYmplY3RMaXRlcmFsKGNhbmRpZGF0ZSkgJiYgUG9zaXRpb24uaXMoY2FuZGlkYXRlLnN0YXJ0KSAmJiBQb3NpdGlvbi5pcyhjYW5kaWRhdGUuZW5kKTtcbiAgfVxuICBSYW5nZTMuaXMgPSBpcztcbn0pKFJhbmdlIHx8IChSYW5nZSA9IHt9KSk7XG52YXIgTG9jYXRpb247XG4oZnVuY3Rpb24oTG9jYXRpb24yKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZSh1cmksIHJhbmdlKSB7XG4gICAgcmV0dXJuIHsgdXJpLCByYW5nZSB9O1xuICB9XG4gIExvY2F0aW9uMi5jcmVhdGUgPSBjcmVhdGU7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgbGV0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBJcy5vYmplY3RMaXRlcmFsKGNhbmRpZGF0ZSkgJiYgUmFuZ2UuaXMoY2FuZGlkYXRlLnJhbmdlKSAmJiAoSXMuc3RyaW5nKGNhbmRpZGF0ZS51cmkpIHx8IElzLnVuZGVmaW5lZChjYW5kaWRhdGUudXJpKSk7XG4gIH1cbiAgTG9jYXRpb24yLmlzID0gaXM7XG59KShMb2NhdGlvbiB8fCAoTG9jYXRpb24gPSB7fSkpO1xudmFyIExvY2F0aW9uTGluaztcbihmdW5jdGlvbihMb2NhdGlvbkxpbmsyKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZSh0YXJnZXRVcmksIHRhcmdldFJhbmdlLCB0YXJnZXRTZWxlY3Rpb25SYW5nZSwgb3JpZ2luU2VsZWN0aW9uUmFuZ2UpIHtcbiAgICByZXR1cm4geyB0YXJnZXRVcmksIHRhcmdldFJhbmdlLCB0YXJnZXRTZWxlY3Rpb25SYW5nZSwgb3JpZ2luU2VsZWN0aW9uUmFuZ2UgfTtcbiAgfVxuICBMb2NhdGlvbkxpbmsyLmNyZWF0ZSA9IGNyZWF0ZTtcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICBsZXQgY2FuZGlkYXRlID0gdmFsdWU7XG4gICAgcmV0dXJuIElzLm9iamVjdExpdGVyYWwoY2FuZGlkYXRlKSAmJiBSYW5nZS5pcyhjYW5kaWRhdGUudGFyZ2V0UmFuZ2UpICYmIElzLnN0cmluZyhjYW5kaWRhdGUudGFyZ2V0VXJpKSAmJiBSYW5nZS5pcyhjYW5kaWRhdGUudGFyZ2V0U2VsZWN0aW9uUmFuZ2UpICYmIChSYW5nZS5pcyhjYW5kaWRhdGUub3JpZ2luU2VsZWN0aW9uUmFuZ2UpIHx8IElzLnVuZGVmaW5lZChjYW5kaWRhdGUub3JpZ2luU2VsZWN0aW9uUmFuZ2UpKTtcbiAgfVxuICBMb2NhdGlvbkxpbmsyLmlzID0gaXM7XG59KShMb2NhdGlvbkxpbmsgfHwgKExvY2F0aW9uTGluayA9IHt9KSk7XG52YXIgQ29sb3I7XG4oZnVuY3Rpb24oQ29sb3IyKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZShyZWQsIGdyZWVuLCBibHVlLCBhbHBoYSkge1xuICAgIHJldHVybiB7XG4gICAgICByZWQsXG4gICAgICBncmVlbixcbiAgICAgIGJsdWUsXG4gICAgICBhbHBoYVxuICAgIH07XG4gIH1cbiAgQ29sb3IyLmNyZWF0ZSA9IGNyZWF0ZTtcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICBjb25zdCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gSXMub2JqZWN0TGl0ZXJhbChjYW5kaWRhdGUpICYmIElzLm51bWJlclJhbmdlKGNhbmRpZGF0ZS5yZWQsIDAsIDEpICYmIElzLm51bWJlclJhbmdlKGNhbmRpZGF0ZS5ncmVlbiwgMCwgMSkgJiYgSXMubnVtYmVyUmFuZ2UoY2FuZGlkYXRlLmJsdWUsIDAsIDEpICYmIElzLm51bWJlclJhbmdlKGNhbmRpZGF0ZS5hbHBoYSwgMCwgMSk7XG4gIH1cbiAgQ29sb3IyLmlzID0gaXM7XG59KShDb2xvciB8fCAoQ29sb3IgPSB7fSkpO1xudmFyIENvbG9ySW5mb3JtYXRpb247XG4oZnVuY3Rpb24oQ29sb3JJbmZvcm1hdGlvbjIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKHJhbmdlLCBjb2xvcikge1xuICAgIHJldHVybiB7XG4gICAgICByYW5nZSxcbiAgICAgIGNvbG9yXG4gICAgfTtcbiAgfVxuICBDb2xvckluZm9ybWF0aW9uMi5jcmVhdGUgPSBjcmVhdGU7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgY29uc3QgY2FuZGlkYXRlID0gdmFsdWU7XG4gICAgcmV0dXJuIElzLm9iamVjdExpdGVyYWwoY2FuZGlkYXRlKSAmJiBSYW5nZS5pcyhjYW5kaWRhdGUucmFuZ2UpICYmIENvbG9yLmlzKGNhbmRpZGF0ZS5jb2xvcik7XG4gIH1cbiAgQ29sb3JJbmZvcm1hdGlvbjIuaXMgPSBpcztcbn0pKENvbG9ySW5mb3JtYXRpb24gfHwgKENvbG9ySW5mb3JtYXRpb24gPSB7fSkpO1xudmFyIENvbG9yUHJlc2VudGF0aW9uO1xuKGZ1bmN0aW9uKENvbG9yUHJlc2VudGF0aW9uMikge1xuICBmdW5jdGlvbiBjcmVhdGUobGFiZWwsIHRleHRFZGl0LCBhZGRpdGlvbmFsVGV4dEVkaXRzKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGxhYmVsLFxuICAgICAgdGV4dEVkaXQsXG4gICAgICBhZGRpdGlvbmFsVGV4dEVkaXRzXG4gICAgfTtcbiAgfVxuICBDb2xvclByZXNlbnRhdGlvbjIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGNvbnN0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBJcy5vYmplY3RMaXRlcmFsKGNhbmRpZGF0ZSkgJiYgSXMuc3RyaW5nKGNhbmRpZGF0ZS5sYWJlbCkgJiYgKElzLnVuZGVmaW5lZChjYW5kaWRhdGUudGV4dEVkaXQpIHx8IFRleHRFZGl0LmlzKGNhbmRpZGF0ZSkpICYmIChJcy51bmRlZmluZWQoY2FuZGlkYXRlLmFkZGl0aW9uYWxUZXh0RWRpdHMpIHx8IElzLnR5cGVkQXJyYXkoY2FuZGlkYXRlLmFkZGl0aW9uYWxUZXh0RWRpdHMsIFRleHRFZGl0LmlzKSk7XG4gIH1cbiAgQ29sb3JQcmVzZW50YXRpb24yLmlzID0gaXM7XG59KShDb2xvclByZXNlbnRhdGlvbiB8fCAoQ29sb3JQcmVzZW50YXRpb24gPSB7fSkpO1xudmFyIEZvbGRpbmdSYW5nZUtpbmQ7XG4oZnVuY3Rpb24oRm9sZGluZ1JhbmdlS2luZDIpIHtcbiAgRm9sZGluZ1JhbmdlS2luZDIuQ29tbWVudCA9IFwiY29tbWVudFwiO1xuICBGb2xkaW5nUmFuZ2VLaW5kMi5JbXBvcnRzID0gXCJpbXBvcnRzXCI7XG4gIEZvbGRpbmdSYW5nZUtpbmQyLlJlZ2lvbiA9IFwicmVnaW9uXCI7XG59KShGb2xkaW5nUmFuZ2VLaW5kIHx8IChGb2xkaW5nUmFuZ2VLaW5kID0ge30pKTtcbnZhciBGb2xkaW5nUmFuZ2U7XG4oZnVuY3Rpb24oRm9sZGluZ1JhbmdlMikge1xuICBmdW5jdGlvbiBjcmVhdGUoc3RhcnRMaW5lLCBlbmRMaW5lLCBzdGFydENoYXJhY3RlciwgZW5kQ2hhcmFjdGVyLCBraW5kLCBjb2xsYXBzZWRUZXh0KSB7XG4gICAgY29uc3QgcmVzdWx0ID0ge1xuICAgICAgc3RhcnRMaW5lLFxuICAgICAgZW5kTGluZVxuICAgIH07XG4gICAgaWYgKElzLmRlZmluZWQoc3RhcnRDaGFyYWN0ZXIpKSB7XG4gICAgICByZXN1bHQuc3RhcnRDaGFyYWN0ZXIgPSBzdGFydENoYXJhY3RlcjtcbiAgICB9XG4gICAgaWYgKElzLmRlZmluZWQoZW5kQ2hhcmFjdGVyKSkge1xuICAgICAgcmVzdWx0LmVuZENoYXJhY3RlciA9IGVuZENoYXJhY3RlcjtcbiAgICB9XG4gICAgaWYgKElzLmRlZmluZWQoa2luZCkpIHtcbiAgICAgIHJlc3VsdC5raW5kID0ga2luZDtcbiAgICB9XG4gICAgaWYgKElzLmRlZmluZWQoY29sbGFwc2VkVGV4dCkpIHtcbiAgICAgIHJlc3VsdC5jb2xsYXBzZWRUZXh0ID0gY29sbGFwc2VkVGV4dDtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxuICBGb2xkaW5nUmFuZ2UyLmNyZWF0ZSA9IGNyZWF0ZTtcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICBjb25zdCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gSXMub2JqZWN0TGl0ZXJhbChjYW5kaWRhdGUpICYmIElzLnVpbnRlZ2VyKGNhbmRpZGF0ZS5zdGFydExpbmUpICYmIElzLnVpbnRlZ2VyKGNhbmRpZGF0ZS5zdGFydExpbmUpICYmIChJcy51bmRlZmluZWQoY2FuZGlkYXRlLnN0YXJ0Q2hhcmFjdGVyKSB8fCBJcy51aW50ZWdlcihjYW5kaWRhdGUuc3RhcnRDaGFyYWN0ZXIpKSAmJiAoSXMudW5kZWZpbmVkKGNhbmRpZGF0ZS5lbmRDaGFyYWN0ZXIpIHx8IElzLnVpbnRlZ2VyKGNhbmRpZGF0ZS5lbmRDaGFyYWN0ZXIpKSAmJiAoSXMudW5kZWZpbmVkKGNhbmRpZGF0ZS5raW5kKSB8fCBJcy5zdHJpbmcoY2FuZGlkYXRlLmtpbmQpKTtcbiAgfVxuICBGb2xkaW5nUmFuZ2UyLmlzID0gaXM7XG59KShGb2xkaW5nUmFuZ2UgfHwgKEZvbGRpbmdSYW5nZSA9IHt9KSk7XG52YXIgRGlhZ25vc3RpY1JlbGF0ZWRJbmZvcm1hdGlvbjtcbihmdW5jdGlvbihEaWFnbm9zdGljUmVsYXRlZEluZm9ybWF0aW9uMikge1xuICBmdW5jdGlvbiBjcmVhdGUobG9jYXRpb24sIG1lc3NhZ2UpIHtcbiAgICByZXR1cm4ge1xuICAgICAgbG9jYXRpb24sXG4gICAgICBtZXNzYWdlXG4gICAgfTtcbiAgfVxuICBEaWFnbm9zdGljUmVsYXRlZEluZm9ybWF0aW9uMi5jcmVhdGUgPSBjcmVhdGU7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgbGV0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBJcy5kZWZpbmVkKGNhbmRpZGF0ZSkgJiYgTG9jYXRpb24uaXMoY2FuZGlkYXRlLmxvY2F0aW9uKSAmJiBJcy5zdHJpbmcoY2FuZGlkYXRlLm1lc3NhZ2UpO1xuICB9XG4gIERpYWdub3N0aWNSZWxhdGVkSW5mb3JtYXRpb24yLmlzID0gaXM7XG59KShEaWFnbm9zdGljUmVsYXRlZEluZm9ybWF0aW9uIHx8IChEaWFnbm9zdGljUmVsYXRlZEluZm9ybWF0aW9uID0ge30pKTtcbnZhciBEaWFnbm9zdGljU2V2ZXJpdHk7XG4oZnVuY3Rpb24oRGlhZ25vc3RpY1NldmVyaXR5Mikge1xuICBEaWFnbm9zdGljU2V2ZXJpdHkyLkVycm9yID0gMTtcbiAgRGlhZ25vc3RpY1NldmVyaXR5Mi5XYXJuaW5nID0gMjtcbiAgRGlhZ25vc3RpY1NldmVyaXR5Mi5JbmZvcm1hdGlvbiA9IDM7XG4gIERpYWdub3N0aWNTZXZlcml0eTIuSGludCA9IDQ7XG59KShEaWFnbm9zdGljU2V2ZXJpdHkgfHwgKERpYWdub3N0aWNTZXZlcml0eSA9IHt9KSk7XG52YXIgRGlhZ25vc3RpY1RhZztcbihmdW5jdGlvbihEaWFnbm9zdGljVGFnMikge1xuICBEaWFnbm9zdGljVGFnMi5Vbm5lY2Vzc2FyeSA9IDE7XG4gIERpYWdub3N0aWNUYWcyLkRlcHJlY2F0ZWQgPSAyO1xufSkoRGlhZ25vc3RpY1RhZyB8fCAoRGlhZ25vc3RpY1RhZyA9IHt9KSk7XG52YXIgQ29kZURlc2NyaXB0aW9uO1xuKGZ1bmN0aW9uKENvZGVEZXNjcmlwdGlvbjIpIHtcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICBjb25zdCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gSXMub2JqZWN0TGl0ZXJhbChjYW5kaWRhdGUpICYmIElzLnN0cmluZyhjYW5kaWRhdGUuaHJlZik7XG4gIH1cbiAgQ29kZURlc2NyaXB0aW9uMi5pcyA9IGlzO1xufSkoQ29kZURlc2NyaXB0aW9uIHx8IChDb2RlRGVzY3JpcHRpb24gPSB7fSkpO1xudmFyIERpYWdub3N0aWM7XG4oZnVuY3Rpb24oRGlhZ25vc3RpYzIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKHJhbmdlLCBtZXNzYWdlLCBzZXZlcml0eSwgY29kZSwgc291cmNlLCByZWxhdGVkSW5mb3JtYXRpb24pIHtcbiAgICBsZXQgcmVzdWx0ID0geyByYW5nZSwgbWVzc2FnZSB9O1xuICAgIGlmIChJcy5kZWZpbmVkKHNldmVyaXR5KSkge1xuICAgICAgcmVzdWx0LnNldmVyaXR5ID0gc2V2ZXJpdHk7XG4gICAgfVxuICAgIGlmIChJcy5kZWZpbmVkKGNvZGUpKSB7XG4gICAgICByZXN1bHQuY29kZSA9IGNvZGU7XG4gICAgfVxuICAgIGlmIChJcy5kZWZpbmVkKHNvdXJjZSkpIHtcbiAgICAgIHJlc3VsdC5zb3VyY2UgPSBzb3VyY2U7XG4gICAgfVxuICAgIGlmIChJcy5kZWZpbmVkKHJlbGF0ZWRJbmZvcm1hdGlvbikpIHtcbiAgICAgIHJlc3VsdC5yZWxhdGVkSW5mb3JtYXRpb24gPSByZWxhdGVkSW5mb3JtYXRpb247XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbiAgRGlhZ25vc3RpYzIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIHZhciBfYTtcbiAgICBsZXQgY2FuZGlkYXRlID0gdmFsdWU7XG4gICAgcmV0dXJuIElzLmRlZmluZWQoY2FuZGlkYXRlKSAmJiBSYW5nZS5pcyhjYW5kaWRhdGUucmFuZ2UpICYmIElzLnN0cmluZyhjYW5kaWRhdGUubWVzc2FnZSkgJiYgKElzLm51bWJlcihjYW5kaWRhdGUuc2V2ZXJpdHkpIHx8IElzLnVuZGVmaW5lZChjYW5kaWRhdGUuc2V2ZXJpdHkpKSAmJiAoSXMuaW50ZWdlcihjYW5kaWRhdGUuY29kZSkgfHwgSXMuc3RyaW5nKGNhbmRpZGF0ZS5jb2RlKSB8fCBJcy51bmRlZmluZWQoY2FuZGlkYXRlLmNvZGUpKSAmJiAoSXMudW5kZWZpbmVkKGNhbmRpZGF0ZS5jb2RlRGVzY3JpcHRpb24pIHx8IElzLnN0cmluZygoX2EgPSBjYW5kaWRhdGUuY29kZURlc2NyaXB0aW9uKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuaHJlZikpICYmIChJcy5zdHJpbmcoY2FuZGlkYXRlLnNvdXJjZSkgfHwgSXMudW5kZWZpbmVkKGNhbmRpZGF0ZS5zb3VyY2UpKSAmJiAoSXMudW5kZWZpbmVkKGNhbmRpZGF0ZS5yZWxhdGVkSW5mb3JtYXRpb24pIHx8IElzLnR5cGVkQXJyYXkoY2FuZGlkYXRlLnJlbGF0ZWRJbmZvcm1hdGlvbiwgRGlhZ25vc3RpY1JlbGF0ZWRJbmZvcm1hdGlvbi5pcykpO1xuICB9XG4gIERpYWdub3N0aWMyLmlzID0gaXM7XG59KShEaWFnbm9zdGljIHx8IChEaWFnbm9zdGljID0ge30pKTtcbnZhciBDb21tYW5kO1xuKGZ1bmN0aW9uKENvbW1hbmQyKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZSh0aXRsZSwgY29tbWFuZCwgLi4uYXJncykge1xuICAgIGxldCByZXN1bHQgPSB7IHRpdGxlLCBjb21tYW5kIH07XG4gICAgaWYgKElzLmRlZmluZWQoYXJncykgJiYgYXJncy5sZW5ndGggPiAwKSB7XG4gICAgICByZXN1bHQuYXJndW1lbnRzID0gYXJncztcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxuICBDb21tYW5kMi5jcmVhdGUgPSBjcmVhdGU7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgbGV0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBJcy5kZWZpbmVkKGNhbmRpZGF0ZSkgJiYgSXMuc3RyaW5nKGNhbmRpZGF0ZS50aXRsZSkgJiYgSXMuc3RyaW5nKGNhbmRpZGF0ZS5jb21tYW5kKTtcbiAgfVxuICBDb21tYW5kMi5pcyA9IGlzO1xufSkoQ29tbWFuZCB8fCAoQ29tbWFuZCA9IHt9KSk7XG52YXIgVGV4dEVkaXQ7XG4oZnVuY3Rpb24oVGV4dEVkaXQyKSB7XG4gIGZ1bmN0aW9uIHJlcGxhY2UocmFuZ2UsIG5ld1RleHQpIHtcbiAgICByZXR1cm4geyByYW5nZSwgbmV3VGV4dCB9O1xuICB9XG4gIFRleHRFZGl0Mi5yZXBsYWNlID0gcmVwbGFjZTtcbiAgZnVuY3Rpb24gaW5zZXJ0KHBvc2l0aW9uLCBuZXdUZXh0KSB7XG4gICAgcmV0dXJuIHsgcmFuZ2U6IHsgc3RhcnQ6IHBvc2l0aW9uLCBlbmQ6IHBvc2l0aW9uIH0sIG5ld1RleHQgfTtcbiAgfVxuICBUZXh0RWRpdDIuaW5zZXJ0ID0gaW5zZXJ0O1xuICBmdW5jdGlvbiBkZWwocmFuZ2UpIHtcbiAgICByZXR1cm4geyByYW5nZSwgbmV3VGV4dDogXCJcIiB9O1xuICB9XG4gIFRleHRFZGl0Mi5kZWwgPSBkZWw7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgY29uc3QgY2FuZGlkYXRlID0gdmFsdWU7XG4gICAgcmV0dXJuIElzLm9iamVjdExpdGVyYWwoY2FuZGlkYXRlKSAmJiBJcy5zdHJpbmcoY2FuZGlkYXRlLm5ld1RleHQpICYmIFJhbmdlLmlzKGNhbmRpZGF0ZS5yYW5nZSk7XG4gIH1cbiAgVGV4dEVkaXQyLmlzID0gaXM7XG59KShUZXh0RWRpdCB8fCAoVGV4dEVkaXQgPSB7fSkpO1xudmFyIENoYW5nZUFubm90YXRpb247XG4oZnVuY3Rpb24oQ2hhbmdlQW5ub3RhdGlvbjIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKGxhYmVsLCBuZWVkc0NvbmZpcm1hdGlvbiwgZGVzY3JpcHRpb24pIHtcbiAgICBjb25zdCByZXN1bHQgPSB7IGxhYmVsIH07XG4gICAgaWYgKG5lZWRzQ29uZmlybWF0aW9uICE9PSB2b2lkIDApIHtcbiAgICAgIHJlc3VsdC5uZWVkc0NvbmZpcm1hdGlvbiA9IG5lZWRzQ29uZmlybWF0aW9uO1xuICAgIH1cbiAgICBpZiAoZGVzY3JpcHRpb24gIT09IHZvaWQgMCkge1xuICAgICAgcmVzdWx0LmRlc2NyaXB0aW9uID0gZGVzY3JpcHRpb247XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbiAgQ2hhbmdlQW5ub3RhdGlvbjIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGNvbnN0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBJcy5vYmplY3RMaXRlcmFsKGNhbmRpZGF0ZSkgJiYgSXMuc3RyaW5nKGNhbmRpZGF0ZS5sYWJlbCkgJiYgKElzLmJvb2xlYW4oY2FuZGlkYXRlLm5lZWRzQ29uZmlybWF0aW9uKSB8fCBjYW5kaWRhdGUubmVlZHNDb25maXJtYXRpb24gPT09IHZvaWQgMCkgJiYgKElzLnN0cmluZyhjYW5kaWRhdGUuZGVzY3JpcHRpb24pIHx8IGNhbmRpZGF0ZS5kZXNjcmlwdGlvbiA9PT0gdm9pZCAwKTtcbiAgfVxuICBDaGFuZ2VBbm5vdGF0aW9uMi5pcyA9IGlzO1xufSkoQ2hhbmdlQW5ub3RhdGlvbiB8fCAoQ2hhbmdlQW5ub3RhdGlvbiA9IHt9KSk7XG52YXIgQ2hhbmdlQW5ub3RhdGlvbklkZW50aWZpZXI7XG4oZnVuY3Rpb24oQ2hhbmdlQW5ub3RhdGlvbklkZW50aWZpZXIyKSB7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgY29uc3QgY2FuZGlkYXRlID0gdmFsdWU7XG4gICAgcmV0dXJuIElzLnN0cmluZyhjYW5kaWRhdGUpO1xuICB9XG4gIENoYW5nZUFubm90YXRpb25JZGVudGlmaWVyMi5pcyA9IGlzO1xufSkoQ2hhbmdlQW5ub3RhdGlvbklkZW50aWZpZXIgfHwgKENoYW5nZUFubm90YXRpb25JZGVudGlmaWVyID0ge30pKTtcbnZhciBBbm5vdGF0ZWRUZXh0RWRpdDtcbihmdW5jdGlvbihBbm5vdGF0ZWRUZXh0RWRpdDIpIHtcbiAgZnVuY3Rpb24gcmVwbGFjZShyYW5nZSwgbmV3VGV4dCwgYW5ub3RhdGlvbikge1xuICAgIHJldHVybiB7IHJhbmdlLCBuZXdUZXh0LCBhbm5vdGF0aW9uSWQ6IGFubm90YXRpb24gfTtcbiAgfVxuICBBbm5vdGF0ZWRUZXh0RWRpdDIucmVwbGFjZSA9IHJlcGxhY2U7XG4gIGZ1bmN0aW9uIGluc2VydChwb3NpdGlvbiwgbmV3VGV4dCwgYW5ub3RhdGlvbikge1xuICAgIHJldHVybiB7IHJhbmdlOiB7IHN0YXJ0OiBwb3NpdGlvbiwgZW5kOiBwb3NpdGlvbiB9LCBuZXdUZXh0LCBhbm5vdGF0aW9uSWQ6IGFubm90YXRpb24gfTtcbiAgfVxuICBBbm5vdGF0ZWRUZXh0RWRpdDIuaW5zZXJ0ID0gaW5zZXJ0O1xuICBmdW5jdGlvbiBkZWwocmFuZ2UsIGFubm90YXRpb24pIHtcbiAgICByZXR1cm4geyByYW5nZSwgbmV3VGV4dDogXCJcIiwgYW5ub3RhdGlvbklkOiBhbm5vdGF0aW9uIH07XG4gIH1cbiAgQW5ub3RhdGVkVGV4dEVkaXQyLmRlbCA9IGRlbDtcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICBjb25zdCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gVGV4dEVkaXQuaXMoY2FuZGlkYXRlKSAmJiAoQ2hhbmdlQW5ub3RhdGlvbi5pcyhjYW5kaWRhdGUuYW5ub3RhdGlvbklkKSB8fCBDaGFuZ2VBbm5vdGF0aW9uSWRlbnRpZmllci5pcyhjYW5kaWRhdGUuYW5ub3RhdGlvbklkKSk7XG4gIH1cbiAgQW5ub3RhdGVkVGV4dEVkaXQyLmlzID0gaXM7XG59KShBbm5vdGF0ZWRUZXh0RWRpdCB8fCAoQW5ub3RhdGVkVGV4dEVkaXQgPSB7fSkpO1xudmFyIFRleHREb2N1bWVudEVkaXQ7XG4oZnVuY3Rpb24oVGV4dERvY3VtZW50RWRpdDIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKHRleHREb2N1bWVudCwgZWRpdHMpIHtcbiAgICByZXR1cm4geyB0ZXh0RG9jdW1lbnQsIGVkaXRzIH07XG4gIH1cbiAgVGV4dERvY3VtZW50RWRpdDIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGxldCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gSXMuZGVmaW5lZChjYW5kaWRhdGUpICYmIE9wdGlvbmFsVmVyc2lvbmVkVGV4dERvY3VtZW50SWRlbnRpZmllci5pcyhjYW5kaWRhdGUudGV4dERvY3VtZW50KSAmJiBBcnJheS5pc0FycmF5KGNhbmRpZGF0ZS5lZGl0cyk7XG4gIH1cbiAgVGV4dERvY3VtZW50RWRpdDIuaXMgPSBpcztcbn0pKFRleHREb2N1bWVudEVkaXQgfHwgKFRleHREb2N1bWVudEVkaXQgPSB7fSkpO1xudmFyIENyZWF0ZUZpbGU7XG4oZnVuY3Rpb24oQ3JlYXRlRmlsZTIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKHVyaSwgb3B0aW9ucywgYW5ub3RhdGlvbikge1xuICAgIGxldCByZXN1bHQgPSB7XG4gICAgICBraW5kOiBcImNyZWF0ZVwiLFxuICAgICAgdXJpXG4gICAgfTtcbiAgICBpZiAob3B0aW9ucyAhPT0gdm9pZCAwICYmIChvcHRpb25zLm92ZXJ3cml0ZSAhPT0gdm9pZCAwIHx8IG9wdGlvbnMuaWdub3JlSWZFeGlzdHMgIT09IHZvaWQgMCkpIHtcbiAgICAgIHJlc3VsdC5vcHRpb25zID0gb3B0aW9ucztcbiAgICB9XG4gICAgaWYgKGFubm90YXRpb24gIT09IHZvaWQgMCkge1xuICAgICAgcmVzdWx0LmFubm90YXRpb25JZCA9IGFubm90YXRpb247XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbiAgQ3JlYXRlRmlsZTIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGxldCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gY2FuZGlkYXRlICYmIGNhbmRpZGF0ZS5raW5kID09PSBcImNyZWF0ZVwiICYmIElzLnN0cmluZyhjYW5kaWRhdGUudXJpKSAmJiAoY2FuZGlkYXRlLm9wdGlvbnMgPT09IHZvaWQgMCB8fCAoY2FuZGlkYXRlLm9wdGlvbnMub3ZlcndyaXRlID09PSB2b2lkIDAgfHwgSXMuYm9vbGVhbihjYW5kaWRhdGUub3B0aW9ucy5vdmVyd3JpdGUpKSAmJiAoY2FuZGlkYXRlLm9wdGlvbnMuaWdub3JlSWZFeGlzdHMgPT09IHZvaWQgMCB8fCBJcy5ib29sZWFuKGNhbmRpZGF0ZS5vcHRpb25zLmlnbm9yZUlmRXhpc3RzKSkpICYmIChjYW5kaWRhdGUuYW5ub3RhdGlvbklkID09PSB2b2lkIDAgfHwgQ2hhbmdlQW5ub3RhdGlvbklkZW50aWZpZXIuaXMoY2FuZGlkYXRlLmFubm90YXRpb25JZCkpO1xuICB9XG4gIENyZWF0ZUZpbGUyLmlzID0gaXM7XG59KShDcmVhdGVGaWxlIHx8IChDcmVhdGVGaWxlID0ge30pKTtcbnZhciBSZW5hbWVGaWxlO1xuKGZ1bmN0aW9uKFJlbmFtZUZpbGUyKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZShvbGRVcmksIG5ld1VyaSwgb3B0aW9ucywgYW5ub3RhdGlvbikge1xuICAgIGxldCByZXN1bHQgPSB7XG4gICAgICBraW5kOiBcInJlbmFtZVwiLFxuICAgICAgb2xkVXJpLFxuICAgICAgbmV3VXJpXG4gICAgfTtcbiAgICBpZiAob3B0aW9ucyAhPT0gdm9pZCAwICYmIChvcHRpb25zLm92ZXJ3cml0ZSAhPT0gdm9pZCAwIHx8IG9wdGlvbnMuaWdub3JlSWZFeGlzdHMgIT09IHZvaWQgMCkpIHtcbiAgICAgIHJlc3VsdC5vcHRpb25zID0gb3B0aW9ucztcbiAgICB9XG4gICAgaWYgKGFubm90YXRpb24gIT09IHZvaWQgMCkge1xuICAgICAgcmVzdWx0LmFubm90YXRpb25JZCA9IGFubm90YXRpb247XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbiAgUmVuYW1lRmlsZTIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGxldCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gY2FuZGlkYXRlICYmIGNhbmRpZGF0ZS5raW5kID09PSBcInJlbmFtZVwiICYmIElzLnN0cmluZyhjYW5kaWRhdGUub2xkVXJpKSAmJiBJcy5zdHJpbmcoY2FuZGlkYXRlLm5ld1VyaSkgJiYgKGNhbmRpZGF0ZS5vcHRpb25zID09PSB2b2lkIDAgfHwgKGNhbmRpZGF0ZS5vcHRpb25zLm92ZXJ3cml0ZSA9PT0gdm9pZCAwIHx8IElzLmJvb2xlYW4oY2FuZGlkYXRlLm9wdGlvbnMub3ZlcndyaXRlKSkgJiYgKGNhbmRpZGF0ZS5vcHRpb25zLmlnbm9yZUlmRXhpc3RzID09PSB2b2lkIDAgfHwgSXMuYm9vbGVhbihjYW5kaWRhdGUub3B0aW9ucy5pZ25vcmVJZkV4aXN0cykpKSAmJiAoY2FuZGlkYXRlLmFubm90YXRpb25JZCA9PT0gdm9pZCAwIHx8IENoYW5nZUFubm90YXRpb25JZGVudGlmaWVyLmlzKGNhbmRpZGF0ZS5hbm5vdGF0aW9uSWQpKTtcbiAgfVxuICBSZW5hbWVGaWxlMi5pcyA9IGlzO1xufSkoUmVuYW1lRmlsZSB8fCAoUmVuYW1lRmlsZSA9IHt9KSk7XG52YXIgRGVsZXRlRmlsZTtcbihmdW5jdGlvbihEZWxldGVGaWxlMikge1xuICBmdW5jdGlvbiBjcmVhdGUodXJpLCBvcHRpb25zLCBhbm5vdGF0aW9uKSB7XG4gICAgbGV0IHJlc3VsdCA9IHtcbiAgICAgIGtpbmQ6IFwiZGVsZXRlXCIsXG4gICAgICB1cmlcbiAgICB9O1xuICAgIGlmIChvcHRpb25zICE9PSB2b2lkIDAgJiYgKG9wdGlvbnMucmVjdXJzaXZlICE9PSB2b2lkIDAgfHwgb3B0aW9ucy5pZ25vcmVJZk5vdEV4aXN0cyAhPT0gdm9pZCAwKSkge1xuICAgICAgcmVzdWx0Lm9wdGlvbnMgPSBvcHRpb25zO1xuICAgIH1cbiAgICBpZiAoYW5ub3RhdGlvbiAhPT0gdm9pZCAwKSB7XG4gICAgICByZXN1bHQuYW5ub3RhdGlvbklkID0gYW5ub3RhdGlvbjtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxuICBEZWxldGVGaWxlMi5jcmVhdGUgPSBjcmVhdGU7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgbGV0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBjYW5kaWRhdGUgJiYgY2FuZGlkYXRlLmtpbmQgPT09IFwiZGVsZXRlXCIgJiYgSXMuc3RyaW5nKGNhbmRpZGF0ZS51cmkpICYmIChjYW5kaWRhdGUub3B0aW9ucyA9PT0gdm9pZCAwIHx8IChjYW5kaWRhdGUub3B0aW9ucy5yZWN1cnNpdmUgPT09IHZvaWQgMCB8fCBJcy5ib29sZWFuKGNhbmRpZGF0ZS5vcHRpb25zLnJlY3Vyc2l2ZSkpICYmIChjYW5kaWRhdGUub3B0aW9ucy5pZ25vcmVJZk5vdEV4aXN0cyA9PT0gdm9pZCAwIHx8IElzLmJvb2xlYW4oY2FuZGlkYXRlLm9wdGlvbnMuaWdub3JlSWZOb3RFeGlzdHMpKSkgJiYgKGNhbmRpZGF0ZS5hbm5vdGF0aW9uSWQgPT09IHZvaWQgMCB8fCBDaGFuZ2VBbm5vdGF0aW9uSWRlbnRpZmllci5pcyhjYW5kaWRhdGUuYW5ub3RhdGlvbklkKSk7XG4gIH1cbiAgRGVsZXRlRmlsZTIuaXMgPSBpcztcbn0pKERlbGV0ZUZpbGUgfHwgKERlbGV0ZUZpbGUgPSB7fSkpO1xudmFyIFdvcmtzcGFjZUVkaXQ7XG4oZnVuY3Rpb24oV29ya3NwYWNlRWRpdDIpIHtcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICBsZXQgY2FuZGlkYXRlID0gdmFsdWU7XG4gICAgcmV0dXJuIGNhbmRpZGF0ZSAmJiAoY2FuZGlkYXRlLmNoYW5nZXMgIT09IHZvaWQgMCB8fCBjYW5kaWRhdGUuZG9jdW1lbnRDaGFuZ2VzICE9PSB2b2lkIDApICYmIChjYW5kaWRhdGUuZG9jdW1lbnRDaGFuZ2VzID09PSB2b2lkIDAgfHwgY2FuZGlkYXRlLmRvY3VtZW50Q2hhbmdlcy5ldmVyeSgoY2hhbmdlKSA9PiB7XG4gICAgICBpZiAoSXMuc3RyaW5nKGNoYW5nZS5raW5kKSkge1xuICAgICAgICByZXR1cm4gQ3JlYXRlRmlsZS5pcyhjaGFuZ2UpIHx8IFJlbmFtZUZpbGUuaXMoY2hhbmdlKSB8fCBEZWxldGVGaWxlLmlzKGNoYW5nZSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gVGV4dERvY3VtZW50RWRpdC5pcyhjaGFuZ2UpO1xuICAgICAgfVxuICAgIH0pKTtcbiAgfVxuICBXb3Jrc3BhY2VFZGl0Mi5pcyA9IGlzO1xufSkoV29ya3NwYWNlRWRpdCB8fCAoV29ya3NwYWNlRWRpdCA9IHt9KSk7XG52YXIgVGV4dERvY3VtZW50SWRlbnRpZmllcjtcbihmdW5jdGlvbihUZXh0RG9jdW1lbnRJZGVudGlmaWVyMikge1xuICBmdW5jdGlvbiBjcmVhdGUodXJpKSB7XG4gICAgcmV0dXJuIHsgdXJpIH07XG4gIH1cbiAgVGV4dERvY3VtZW50SWRlbnRpZmllcjIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGxldCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gSXMuZGVmaW5lZChjYW5kaWRhdGUpICYmIElzLnN0cmluZyhjYW5kaWRhdGUudXJpKTtcbiAgfVxuICBUZXh0RG9jdW1lbnRJZGVudGlmaWVyMi5pcyA9IGlzO1xufSkoVGV4dERvY3VtZW50SWRlbnRpZmllciB8fCAoVGV4dERvY3VtZW50SWRlbnRpZmllciA9IHt9KSk7XG52YXIgVmVyc2lvbmVkVGV4dERvY3VtZW50SWRlbnRpZmllcjtcbihmdW5jdGlvbihWZXJzaW9uZWRUZXh0RG9jdW1lbnRJZGVudGlmaWVyMikge1xuICBmdW5jdGlvbiBjcmVhdGUodXJpLCB2ZXJzaW9uKSB7XG4gICAgcmV0dXJuIHsgdXJpLCB2ZXJzaW9uIH07XG4gIH1cbiAgVmVyc2lvbmVkVGV4dERvY3VtZW50SWRlbnRpZmllcjIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGxldCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gSXMuZGVmaW5lZChjYW5kaWRhdGUpICYmIElzLnN0cmluZyhjYW5kaWRhdGUudXJpKSAmJiBJcy5pbnRlZ2VyKGNhbmRpZGF0ZS52ZXJzaW9uKTtcbiAgfVxuICBWZXJzaW9uZWRUZXh0RG9jdW1lbnRJZGVudGlmaWVyMi5pcyA9IGlzO1xufSkoVmVyc2lvbmVkVGV4dERvY3VtZW50SWRlbnRpZmllciB8fCAoVmVyc2lvbmVkVGV4dERvY3VtZW50SWRlbnRpZmllciA9IHt9KSk7XG52YXIgT3B0aW9uYWxWZXJzaW9uZWRUZXh0RG9jdW1lbnRJZGVudGlmaWVyO1xuKGZ1bmN0aW9uKE9wdGlvbmFsVmVyc2lvbmVkVGV4dERvY3VtZW50SWRlbnRpZmllcjIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKHVyaSwgdmVyc2lvbikge1xuICAgIHJldHVybiB7IHVyaSwgdmVyc2lvbiB9O1xuICB9XG4gIE9wdGlvbmFsVmVyc2lvbmVkVGV4dERvY3VtZW50SWRlbnRpZmllcjIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGxldCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gSXMuZGVmaW5lZChjYW5kaWRhdGUpICYmIElzLnN0cmluZyhjYW5kaWRhdGUudXJpKSAmJiAoY2FuZGlkYXRlLnZlcnNpb24gPT09IG51bGwgfHwgSXMuaW50ZWdlcihjYW5kaWRhdGUudmVyc2lvbikpO1xuICB9XG4gIE9wdGlvbmFsVmVyc2lvbmVkVGV4dERvY3VtZW50SWRlbnRpZmllcjIuaXMgPSBpcztcbn0pKE9wdGlvbmFsVmVyc2lvbmVkVGV4dERvY3VtZW50SWRlbnRpZmllciB8fCAoT3B0aW9uYWxWZXJzaW9uZWRUZXh0RG9jdW1lbnRJZGVudGlmaWVyID0ge30pKTtcbnZhciBUZXh0RG9jdW1lbnRJdGVtO1xuKGZ1bmN0aW9uKFRleHREb2N1bWVudEl0ZW0yKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZSh1cmksIGxhbmd1YWdlSWQsIHZlcnNpb24sIHRleHQpIHtcbiAgICByZXR1cm4geyB1cmksIGxhbmd1YWdlSWQsIHZlcnNpb24sIHRleHQgfTtcbiAgfVxuICBUZXh0RG9jdW1lbnRJdGVtMi5jcmVhdGUgPSBjcmVhdGU7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgbGV0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBJcy5kZWZpbmVkKGNhbmRpZGF0ZSkgJiYgSXMuc3RyaW5nKGNhbmRpZGF0ZS51cmkpICYmIElzLnN0cmluZyhjYW5kaWRhdGUubGFuZ3VhZ2VJZCkgJiYgSXMuaW50ZWdlcihjYW5kaWRhdGUudmVyc2lvbikgJiYgSXMuc3RyaW5nKGNhbmRpZGF0ZS50ZXh0KTtcbiAgfVxuICBUZXh0RG9jdW1lbnRJdGVtMi5pcyA9IGlzO1xufSkoVGV4dERvY3VtZW50SXRlbSB8fCAoVGV4dERvY3VtZW50SXRlbSA9IHt9KSk7XG52YXIgTWFya3VwS2luZDtcbihmdW5jdGlvbihNYXJrdXBLaW5kMikge1xuICBNYXJrdXBLaW5kMi5QbGFpblRleHQgPSBcInBsYWludGV4dFwiO1xuICBNYXJrdXBLaW5kMi5NYXJrZG93biA9IFwibWFya2Rvd25cIjtcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICBjb25zdCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gY2FuZGlkYXRlID09PSBNYXJrdXBLaW5kMi5QbGFpblRleHQgfHwgY2FuZGlkYXRlID09PSBNYXJrdXBLaW5kMi5NYXJrZG93bjtcbiAgfVxuICBNYXJrdXBLaW5kMi5pcyA9IGlzO1xufSkoTWFya3VwS2luZCB8fCAoTWFya3VwS2luZCA9IHt9KSk7XG52YXIgTWFya3VwQ29udGVudDtcbihmdW5jdGlvbihNYXJrdXBDb250ZW50Mikge1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGNvbnN0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBJcy5vYmplY3RMaXRlcmFsKHZhbHVlKSAmJiBNYXJrdXBLaW5kLmlzKGNhbmRpZGF0ZS5raW5kKSAmJiBJcy5zdHJpbmcoY2FuZGlkYXRlLnZhbHVlKTtcbiAgfVxuICBNYXJrdXBDb250ZW50Mi5pcyA9IGlzO1xufSkoTWFya3VwQ29udGVudCB8fCAoTWFya3VwQ29udGVudCA9IHt9KSk7XG52YXIgQ29tcGxldGlvbkl0ZW1LaW5kO1xuKGZ1bmN0aW9uKENvbXBsZXRpb25JdGVtS2luZDIpIHtcbiAgQ29tcGxldGlvbkl0ZW1LaW5kMi5UZXh0ID0gMTtcbiAgQ29tcGxldGlvbkl0ZW1LaW5kMi5NZXRob2QgPSAyO1xuICBDb21wbGV0aW9uSXRlbUtpbmQyLkZ1bmN0aW9uID0gMztcbiAgQ29tcGxldGlvbkl0ZW1LaW5kMi5Db25zdHJ1Y3RvciA9IDQ7XG4gIENvbXBsZXRpb25JdGVtS2luZDIuRmllbGQgPSA1O1xuICBDb21wbGV0aW9uSXRlbUtpbmQyLlZhcmlhYmxlID0gNjtcbiAgQ29tcGxldGlvbkl0ZW1LaW5kMi5DbGFzcyA9IDc7XG4gIENvbXBsZXRpb25JdGVtS2luZDIuSW50ZXJmYWNlID0gODtcbiAgQ29tcGxldGlvbkl0ZW1LaW5kMi5Nb2R1bGUgPSA5O1xuICBDb21wbGV0aW9uSXRlbUtpbmQyLlByb3BlcnR5ID0gMTA7XG4gIENvbXBsZXRpb25JdGVtS2luZDIuVW5pdCA9IDExO1xuICBDb21wbGV0aW9uSXRlbUtpbmQyLlZhbHVlID0gMTI7XG4gIENvbXBsZXRpb25JdGVtS2luZDIuRW51bSA9IDEzO1xuICBDb21wbGV0aW9uSXRlbUtpbmQyLktleXdvcmQgPSAxNDtcbiAgQ29tcGxldGlvbkl0ZW1LaW5kMi5TbmlwcGV0ID0gMTU7XG4gIENvbXBsZXRpb25JdGVtS2luZDIuQ29sb3IgPSAxNjtcbiAgQ29tcGxldGlvbkl0ZW1LaW5kMi5GaWxlID0gMTc7XG4gIENvbXBsZXRpb25JdGVtS2luZDIuUmVmZXJlbmNlID0gMTg7XG4gIENvbXBsZXRpb25JdGVtS2luZDIuRm9sZGVyID0gMTk7XG4gIENvbXBsZXRpb25JdGVtS2luZDIuRW51bU1lbWJlciA9IDIwO1xuICBDb21wbGV0aW9uSXRlbUtpbmQyLkNvbnN0YW50ID0gMjE7XG4gIENvbXBsZXRpb25JdGVtS2luZDIuU3RydWN0ID0gMjI7XG4gIENvbXBsZXRpb25JdGVtS2luZDIuRXZlbnQgPSAyMztcbiAgQ29tcGxldGlvbkl0ZW1LaW5kMi5PcGVyYXRvciA9IDI0O1xuICBDb21wbGV0aW9uSXRlbUtpbmQyLlR5cGVQYXJhbWV0ZXIgPSAyNTtcbn0pKENvbXBsZXRpb25JdGVtS2luZCB8fCAoQ29tcGxldGlvbkl0ZW1LaW5kID0ge30pKTtcbnZhciBJbnNlcnRUZXh0Rm9ybWF0O1xuKGZ1bmN0aW9uKEluc2VydFRleHRGb3JtYXQyKSB7XG4gIEluc2VydFRleHRGb3JtYXQyLlBsYWluVGV4dCA9IDE7XG4gIEluc2VydFRleHRGb3JtYXQyLlNuaXBwZXQgPSAyO1xufSkoSW5zZXJ0VGV4dEZvcm1hdCB8fCAoSW5zZXJ0VGV4dEZvcm1hdCA9IHt9KSk7XG52YXIgQ29tcGxldGlvbkl0ZW1UYWc7XG4oZnVuY3Rpb24oQ29tcGxldGlvbkl0ZW1UYWcyKSB7XG4gIENvbXBsZXRpb25JdGVtVGFnMi5EZXByZWNhdGVkID0gMTtcbn0pKENvbXBsZXRpb25JdGVtVGFnIHx8IChDb21wbGV0aW9uSXRlbVRhZyA9IHt9KSk7XG52YXIgSW5zZXJ0UmVwbGFjZUVkaXQ7XG4oZnVuY3Rpb24oSW5zZXJ0UmVwbGFjZUVkaXQyKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZShuZXdUZXh0LCBpbnNlcnQsIHJlcGxhY2UpIHtcbiAgICByZXR1cm4geyBuZXdUZXh0LCBpbnNlcnQsIHJlcGxhY2UgfTtcbiAgfVxuICBJbnNlcnRSZXBsYWNlRWRpdDIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGNvbnN0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBjYW5kaWRhdGUgJiYgSXMuc3RyaW5nKGNhbmRpZGF0ZS5uZXdUZXh0KSAmJiBSYW5nZS5pcyhjYW5kaWRhdGUuaW5zZXJ0KSAmJiBSYW5nZS5pcyhjYW5kaWRhdGUucmVwbGFjZSk7XG4gIH1cbiAgSW5zZXJ0UmVwbGFjZUVkaXQyLmlzID0gaXM7XG59KShJbnNlcnRSZXBsYWNlRWRpdCB8fCAoSW5zZXJ0UmVwbGFjZUVkaXQgPSB7fSkpO1xudmFyIEluc2VydFRleHRNb2RlO1xuKGZ1bmN0aW9uKEluc2VydFRleHRNb2RlMikge1xuICBJbnNlcnRUZXh0TW9kZTIuYXNJcyA9IDE7XG4gIEluc2VydFRleHRNb2RlMi5hZGp1c3RJbmRlbnRhdGlvbiA9IDI7XG59KShJbnNlcnRUZXh0TW9kZSB8fCAoSW5zZXJ0VGV4dE1vZGUgPSB7fSkpO1xudmFyIENvbXBsZXRpb25JdGVtTGFiZWxEZXRhaWxzO1xuKGZ1bmN0aW9uKENvbXBsZXRpb25JdGVtTGFiZWxEZXRhaWxzMikge1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGNvbnN0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBjYW5kaWRhdGUgJiYgKElzLnN0cmluZyhjYW5kaWRhdGUuZGV0YWlsKSB8fCBjYW5kaWRhdGUuZGV0YWlsID09PSB2b2lkIDApICYmIChJcy5zdHJpbmcoY2FuZGlkYXRlLmRlc2NyaXB0aW9uKSB8fCBjYW5kaWRhdGUuZGVzY3JpcHRpb24gPT09IHZvaWQgMCk7XG4gIH1cbiAgQ29tcGxldGlvbkl0ZW1MYWJlbERldGFpbHMyLmlzID0gaXM7XG59KShDb21wbGV0aW9uSXRlbUxhYmVsRGV0YWlscyB8fCAoQ29tcGxldGlvbkl0ZW1MYWJlbERldGFpbHMgPSB7fSkpO1xudmFyIENvbXBsZXRpb25JdGVtO1xuKGZ1bmN0aW9uKENvbXBsZXRpb25JdGVtMikge1xuICBmdW5jdGlvbiBjcmVhdGUobGFiZWwpIHtcbiAgICByZXR1cm4geyBsYWJlbCB9O1xuICB9XG4gIENvbXBsZXRpb25JdGVtMi5jcmVhdGUgPSBjcmVhdGU7XG59KShDb21wbGV0aW9uSXRlbSB8fCAoQ29tcGxldGlvbkl0ZW0gPSB7fSkpO1xudmFyIENvbXBsZXRpb25MaXN0O1xuKGZ1bmN0aW9uKENvbXBsZXRpb25MaXN0Mikge1xuICBmdW5jdGlvbiBjcmVhdGUoaXRlbXMsIGlzSW5jb21wbGV0ZSkge1xuICAgIHJldHVybiB7IGl0ZW1zOiBpdGVtcyA/IGl0ZW1zIDogW10sIGlzSW5jb21wbGV0ZTogISFpc0luY29tcGxldGUgfTtcbiAgfVxuICBDb21wbGV0aW9uTGlzdDIuY3JlYXRlID0gY3JlYXRlO1xufSkoQ29tcGxldGlvbkxpc3QgfHwgKENvbXBsZXRpb25MaXN0ID0ge30pKTtcbnZhciBNYXJrZWRTdHJpbmc7XG4oZnVuY3Rpb24oTWFya2VkU3RyaW5nMikge1xuICBmdW5jdGlvbiBmcm9tUGxhaW5UZXh0KHBsYWluVGV4dCkge1xuICAgIHJldHVybiBwbGFpblRleHQucmVwbGFjZSgvW1xcXFxgKl97fVtcXF0oKSMrXFwtLiFdL2csIFwiXFxcXCQmXCIpO1xuICB9XG4gIE1hcmtlZFN0cmluZzIuZnJvbVBsYWluVGV4dCA9IGZyb21QbGFpblRleHQ7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgY29uc3QgY2FuZGlkYXRlID0gdmFsdWU7XG4gICAgcmV0dXJuIElzLnN0cmluZyhjYW5kaWRhdGUpIHx8IElzLm9iamVjdExpdGVyYWwoY2FuZGlkYXRlKSAmJiBJcy5zdHJpbmcoY2FuZGlkYXRlLmxhbmd1YWdlKSAmJiBJcy5zdHJpbmcoY2FuZGlkYXRlLnZhbHVlKTtcbiAgfVxuICBNYXJrZWRTdHJpbmcyLmlzID0gaXM7XG59KShNYXJrZWRTdHJpbmcgfHwgKE1hcmtlZFN0cmluZyA9IHt9KSk7XG52YXIgSG92ZXI7XG4oZnVuY3Rpb24oSG92ZXIyKSB7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgbGV0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiAhIWNhbmRpZGF0ZSAmJiBJcy5vYmplY3RMaXRlcmFsKGNhbmRpZGF0ZSkgJiYgKE1hcmt1cENvbnRlbnQuaXMoY2FuZGlkYXRlLmNvbnRlbnRzKSB8fCBNYXJrZWRTdHJpbmcuaXMoY2FuZGlkYXRlLmNvbnRlbnRzKSB8fCBJcy50eXBlZEFycmF5KGNhbmRpZGF0ZS5jb250ZW50cywgTWFya2VkU3RyaW5nLmlzKSkgJiYgKHZhbHVlLnJhbmdlID09PSB2b2lkIDAgfHwgUmFuZ2UuaXModmFsdWUucmFuZ2UpKTtcbiAgfVxuICBIb3ZlcjIuaXMgPSBpcztcbn0pKEhvdmVyIHx8IChIb3ZlciA9IHt9KSk7XG52YXIgUGFyYW1ldGVySW5mb3JtYXRpb247XG4oZnVuY3Rpb24oUGFyYW1ldGVySW5mb3JtYXRpb24yKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZShsYWJlbCwgZG9jdW1lbnRhdGlvbikge1xuICAgIHJldHVybiBkb2N1bWVudGF0aW9uID8geyBsYWJlbCwgZG9jdW1lbnRhdGlvbiB9IDogeyBsYWJlbCB9O1xuICB9XG4gIFBhcmFtZXRlckluZm9ybWF0aW9uMi5jcmVhdGUgPSBjcmVhdGU7XG59KShQYXJhbWV0ZXJJbmZvcm1hdGlvbiB8fCAoUGFyYW1ldGVySW5mb3JtYXRpb24gPSB7fSkpO1xudmFyIFNpZ25hdHVyZUluZm9ybWF0aW9uO1xuKGZ1bmN0aW9uKFNpZ25hdHVyZUluZm9ybWF0aW9uMikge1xuICBmdW5jdGlvbiBjcmVhdGUobGFiZWwsIGRvY3VtZW50YXRpb24sIC4uLnBhcmFtZXRlcnMpIHtcbiAgICBsZXQgcmVzdWx0ID0geyBsYWJlbCB9O1xuICAgIGlmIChJcy5kZWZpbmVkKGRvY3VtZW50YXRpb24pKSB7XG4gICAgICByZXN1bHQuZG9jdW1lbnRhdGlvbiA9IGRvY3VtZW50YXRpb247XG4gICAgfVxuICAgIGlmIChJcy5kZWZpbmVkKHBhcmFtZXRlcnMpKSB7XG4gICAgICByZXN1bHQucGFyYW1ldGVycyA9IHBhcmFtZXRlcnM7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJlc3VsdC5wYXJhbWV0ZXJzID0gW107XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbiAgU2lnbmF0dXJlSW5mb3JtYXRpb24yLmNyZWF0ZSA9IGNyZWF0ZTtcbn0pKFNpZ25hdHVyZUluZm9ybWF0aW9uIHx8IChTaWduYXR1cmVJbmZvcm1hdGlvbiA9IHt9KSk7XG52YXIgRG9jdW1lbnRIaWdobGlnaHRLaW5kO1xuKGZ1bmN0aW9uKERvY3VtZW50SGlnaGxpZ2h0S2luZDIpIHtcbiAgRG9jdW1lbnRIaWdobGlnaHRLaW5kMi5UZXh0ID0gMTtcbiAgRG9jdW1lbnRIaWdobGlnaHRLaW5kMi5SZWFkID0gMjtcbiAgRG9jdW1lbnRIaWdobGlnaHRLaW5kMi5Xcml0ZSA9IDM7XG59KShEb2N1bWVudEhpZ2hsaWdodEtpbmQgfHwgKERvY3VtZW50SGlnaGxpZ2h0S2luZCA9IHt9KSk7XG52YXIgRG9jdW1lbnRIaWdobGlnaHQ7XG4oZnVuY3Rpb24oRG9jdW1lbnRIaWdobGlnaHQyKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZShyYW5nZSwga2luZCkge1xuICAgIGxldCByZXN1bHQgPSB7IHJhbmdlIH07XG4gICAgaWYgKElzLm51bWJlcihraW5kKSkge1xuICAgICAgcmVzdWx0LmtpbmQgPSBraW5kO1xuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xuICB9XG4gIERvY3VtZW50SGlnaGxpZ2h0Mi5jcmVhdGUgPSBjcmVhdGU7XG59KShEb2N1bWVudEhpZ2hsaWdodCB8fCAoRG9jdW1lbnRIaWdobGlnaHQgPSB7fSkpO1xudmFyIFN5bWJvbEtpbmQ7XG4oZnVuY3Rpb24oU3ltYm9sS2luZDIpIHtcbiAgU3ltYm9sS2luZDIuRmlsZSA9IDE7XG4gIFN5bWJvbEtpbmQyLk1vZHVsZSA9IDI7XG4gIFN5bWJvbEtpbmQyLk5hbWVzcGFjZSA9IDM7XG4gIFN5bWJvbEtpbmQyLlBhY2thZ2UgPSA0O1xuICBTeW1ib2xLaW5kMi5DbGFzcyA9IDU7XG4gIFN5bWJvbEtpbmQyLk1ldGhvZCA9IDY7XG4gIFN5bWJvbEtpbmQyLlByb3BlcnR5ID0gNztcbiAgU3ltYm9sS2luZDIuRmllbGQgPSA4O1xuICBTeW1ib2xLaW5kMi5Db25zdHJ1Y3RvciA9IDk7XG4gIFN5bWJvbEtpbmQyLkVudW0gPSAxMDtcbiAgU3ltYm9sS2luZDIuSW50ZXJmYWNlID0gMTE7XG4gIFN5bWJvbEtpbmQyLkZ1bmN0aW9uID0gMTI7XG4gIFN5bWJvbEtpbmQyLlZhcmlhYmxlID0gMTM7XG4gIFN5bWJvbEtpbmQyLkNvbnN0YW50ID0gMTQ7XG4gIFN5bWJvbEtpbmQyLlN0cmluZyA9IDE1O1xuICBTeW1ib2xLaW5kMi5OdW1iZXIgPSAxNjtcbiAgU3ltYm9sS2luZDIuQm9vbGVhbiA9IDE3O1xuICBTeW1ib2xLaW5kMi5BcnJheSA9IDE4O1xuICBTeW1ib2xLaW5kMi5PYmplY3QgPSAxOTtcbiAgU3ltYm9sS2luZDIuS2V5ID0gMjA7XG4gIFN5bWJvbEtpbmQyLk51bGwgPSAyMTtcbiAgU3ltYm9sS2luZDIuRW51bU1lbWJlciA9IDIyO1xuICBTeW1ib2xLaW5kMi5TdHJ1Y3QgPSAyMztcbiAgU3ltYm9sS2luZDIuRXZlbnQgPSAyNDtcbiAgU3ltYm9sS2luZDIuT3BlcmF0b3IgPSAyNTtcbiAgU3ltYm9sS2luZDIuVHlwZVBhcmFtZXRlciA9IDI2O1xufSkoU3ltYm9sS2luZCB8fCAoU3ltYm9sS2luZCA9IHt9KSk7XG52YXIgU3ltYm9sVGFnO1xuKGZ1bmN0aW9uKFN5bWJvbFRhZzIpIHtcbiAgU3ltYm9sVGFnMi5EZXByZWNhdGVkID0gMTtcbn0pKFN5bWJvbFRhZyB8fCAoU3ltYm9sVGFnID0ge30pKTtcbnZhciBTeW1ib2xJbmZvcm1hdGlvbjtcbihmdW5jdGlvbihTeW1ib2xJbmZvcm1hdGlvbjIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKG5hbWUsIGtpbmQsIHJhbmdlLCB1cmksIGNvbnRhaW5lck5hbWUpIHtcbiAgICBsZXQgcmVzdWx0ID0ge1xuICAgICAgbmFtZSxcbiAgICAgIGtpbmQsXG4gICAgICBsb2NhdGlvbjogeyB1cmksIHJhbmdlIH1cbiAgICB9O1xuICAgIGlmIChjb250YWluZXJOYW1lKSB7XG4gICAgICByZXN1bHQuY29udGFpbmVyTmFtZSA9IGNvbnRhaW5lck5hbWU7XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbiAgU3ltYm9sSW5mb3JtYXRpb24yLmNyZWF0ZSA9IGNyZWF0ZTtcbn0pKFN5bWJvbEluZm9ybWF0aW9uIHx8IChTeW1ib2xJbmZvcm1hdGlvbiA9IHt9KSk7XG52YXIgV29ya3NwYWNlU3ltYm9sO1xuKGZ1bmN0aW9uKFdvcmtzcGFjZVN5bWJvbDIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKG5hbWUsIGtpbmQsIHVyaSwgcmFuZ2UpIHtcbiAgICByZXR1cm4gcmFuZ2UgIT09IHZvaWQgMCA/IHsgbmFtZSwga2luZCwgbG9jYXRpb246IHsgdXJpLCByYW5nZSB9IH0gOiB7IG5hbWUsIGtpbmQsIGxvY2F0aW9uOiB7IHVyaSB9IH07XG4gIH1cbiAgV29ya3NwYWNlU3ltYm9sMi5jcmVhdGUgPSBjcmVhdGU7XG59KShXb3Jrc3BhY2VTeW1ib2wgfHwgKFdvcmtzcGFjZVN5bWJvbCA9IHt9KSk7XG52YXIgRG9jdW1lbnRTeW1ib2w7XG4oZnVuY3Rpb24oRG9jdW1lbnRTeW1ib2wyKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZShuYW1lLCBkZXRhaWwsIGtpbmQsIHJhbmdlLCBzZWxlY3Rpb25SYW5nZSwgY2hpbGRyZW4pIHtcbiAgICBsZXQgcmVzdWx0ID0ge1xuICAgICAgbmFtZSxcbiAgICAgIGRldGFpbCxcbiAgICAgIGtpbmQsXG4gICAgICByYW5nZSxcbiAgICAgIHNlbGVjdGlvblJhbmdlXG4gICAgfTtcbiAgICBpZiAoY2hpbGRyZW4gIT09IHZvaWQgMCkge1xuICAgICAgcmVzdWx0LmNoaWxkcmVuID0gY2hpbGRyZW47XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbiAgRG9jdW1lbnRTeW1ib2wyLmNyZWF0ZSA9IGNyZWF0ZTtcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICBsZXQgY2FuZGlkYXRlID0gdmFsdWU7XG4gICAgcmV0dXJuIGNhbmRpZGF0ZSAmJiBJcy5zdHJpbmcoY2FuZGlkYXRlLm5hbWUpICYmIElzLm51bWJlcihjYW5kaWRhdGUua2luZCkgJiYgUmFuZ2UuaXMoY2FuZGlkYXRlLnJhbmdlKSAmJiBSYW5nZS5pcyhjYW5kaWRhdGUuc2VsZWN0aW9uUmFuZ2UpICYmIChjYW5kaWRhdGUuZGV0YWlsID09PSB2b2lkIDAgfHwgSXMuc3RyaW5nKGNhbmRpZGF0ZS5kZXRhaWwpKSAmJiAoY2FuZGlkYXRlLmRlcHJlY2F0ZWQgPT09IHZvaWQgMCB8fCBJcy5ib29sZWFuKGNhbmRpZGF0ZS5kZXByZWNhdGVkKSkgJiYgKGNhbmRpZGF0ZS5jaGlsZHJlbiA9PT0gdm9pZCAwIHx8IEFycmF5LmlzQXJyYXkoY2FuZGlkYXRlLmNoaWxkcmVuKSkgJiYgKGNhbmRpZGF0ZS50YWdzID09PSB2b2lkIDAgfHwgQXJyYXkuaXNBcnJheShjYW5kaWRhdGUudGFncykpO1xuICB9XG4gIERvY3VtZW50U3ltYm9sMi5pcyA9IGlzO1xufSkoRG9jdW1lbnRTeW1ib2wgfHwgKERvY3VtZW50U3ltYm9sID0ge30pKTtcbnZhciBDb2RlQWN0aW9uS2luZDtcbihmdW5jdGlvbihDb2RlQWN0aW9uS2luZDIpIHtcbiAgQ29kZUFjdGlvbktpbmQyLkVtcHR5ID0gXCJcIjtcbiAgQ29kZUFjdGlvbktpbmQyLlF1aWNrRml4ID0gXCJxdWlja2ZpeFwiO1xuICBDb2RlQWN0aW9uS2luZDIuUmVmYWN0b3IgPSBcInJlZmFjdG9yXCI7XG4gIENvZGVBY3Rpb25LaW5kMi5SZWZhY3RvckV4dHJhY3QgPSBcInJlZmFjdG9yLmV4dHJhY3RcIjtcbiAgQ29kZUFjdGlvbktpbmQyLlJlZmFjdG9ySW5saW5lID0gXCJyZWZhY3Rvci5pbmxpbmVcIjtcbiAgQ29kZUFjdGlvbktpbmQyLlJlZmFjdG9yUmV3cml0ZSA9IFwicmVmYWN0b3IucmV3cml0ZVwiO1xuICBDb2RlQWN0aW9uS2luZDIuU291cmNlID0gXCJzb3VyY2VcIjtcbiAgQ29kZUFjdGlvbktpbmQyLlNvdXJjZU9yZ2FuaXplSW1wb3J0cyA9IFwic291cmNlLm9yZ2FuaXplSW1wb3J0c1wiO1xuICBDb2RlQWN0aW9uS2luZDIuU291cmNlRml4QWxsID0gXCJzb3VyY2UuZml4QWxsXCI7XG59KShDb2RlQWN0aW9uS2luZCB8fCAoQ29kZUFjdGlvbktpbmQgPSB7fSkpO1xudmFyIENvZGVBY3Rpb25UcmlnZ2VyS2luZDtcbihmdW5jdGlvbihDb2RlQWN0aW9uVHJpZ2dlcktpbmQyKSB7XG4gIENvZGVBY3Rpb25UcmlnZ2VyS2luZDIuSW52b2tlZCA9IDE7XG4gIENvZGVBY3Rpb25UcmlnZ2VyS2luZDIuQXV0b21hdGljID0gMjtcbn0pKENvZGVBY3Rpb25UcmlnZ2VyS2luZCB8fCAoQ29kZUFjdGlvblRyaWdnZXJLaW5kID0ge30pKTtcbnZhciBDb2RlQWN0aW9uQ29udGV4dDtcbihmdW5jdGlvbihDb2RlQWN0aW9uQ29udGV4dDIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKGRpYWdub3N0aWNzLCBvbmx5LCB0cmlnZ2VyS2luZCkge1xuICAgIGxldCByZXN1bHQgPSB7IGRpYWdub3N0aWNzIH07XG4gICAgaWYgKG9ubHkgIT09IHZvaWQgMCAmJiBvbmx5ICE9PSBudWxsKSB7XG4gICAgICByZXN1bHQub25seSA9IG9ubHk7XG4gICAgfVxuICAgIGlmICh0cmlnZ2VyS2luZCAhPT0gdm9pZCAwICYmIHRyaWdnZXJLaW5kICE9PSBudWxsKSB7XG4gICAgICByZXN1bHQudHJpZ2dlcktpbmQgPSB0cmlnZ2VyS2luZDtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxuICBDb2RlQWN0aW9uQ29udGV4dDIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGxldCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gSXMuZGVmaW5lZChjYW5kaWRhdGUpICYmIElzLnR5cGVkQXJyYXkoY2FuZGlkYXRlLmRpYWdub3N0aWNzLCBEaWFnbm9zdGljLmlzKSAmJiAoY2FuZGlkYXRlLm9ubHkgPT09IHZvaWQgMCB8fCBJcy50eXBlZEFycmF5KGNhbmRpZGF0ZS5vbmx5LCBJcy5zdHJpbmcpKSAmJiAoY2FuZGlkYXRlLnRyaWdnZXJLaW5kID09PSB2b2lkIDAgfHwgY2FuZGlkYXRlLnRyaWdnZXJLaW5kID09PSBDb2RlQWN0aW9uVHJpZ2dlcktpbmQuSW52b2tlZCB8fCBjYW5kaWRhdGUudHJpZ2dlcktpbmQgPT09IENvZGVBY3Rpb25UcmlnZ2VyS2luZC5BdXRvbWF0aWMpO1xuICB9XG4gIENvZGVBY3Rpb25Db250ZXh0Mi5pcyA9IGlzO1xufSkoQ29kZUFjdGlvbkNvbnRleHQgfHwgKENvZGVBY3Rpb25Db250ZXh0ID0ge30pKTtcbnZhciBDb2RlQWN0aW9uO1xuKGZ1bmN0aW9uKENvZGVBY3Rpb24yKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZSh0aXRsZSwga2luZE9yQ29tbWFuZE9yRWRpdCwga2luZCkge1xuICAgIGxldCByZXN1bHQgPSB7IHRpdGxlIH07XG4gICAgbGV0IGNoZWNrS2luZCA9IHRydWU7XG4gICAgaWYgKHR5cGVvZiBraW5kT3JDb21tYW5kT3JFZGl0ID09PSBcInN0cmluZ1wiKSB7XG4gICAgICBjaGVja0tpbmQgPSBmYWxzZTtcbiAgICAgIHJlc3VsdC5raW5kID0ga2luZE9yQ29tbWFuZE9yRWRpdDtcbiAgICB9IGVsc2UgaWYgKENvbW1hbmQuaXMoa2luZE9yQ29tbWFuZE9yRWRpdCkpIHtcbiAgICAgIHJlc3VsdC5jb21tYW5kID0ga2luZE9yQ29tbWFuZE9yRWRpdDtcbiAgICB9IGVsc2Uge1xuICAgICAgcmVzdWx0LmVkaXQgPSBraW5kT3JDb21tYW5kT3JFZGl0O1xuICAgIH1cbiAgICBpZiAoY2hlY2tLaW5kICYmIGtpbmQgIT09IHZvaWQgMCkge1xuICAgICAgcmVzdWx0LmtpbmQgPSBraW5kO1xuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xuICB9XG4gIENvZGVBY3Rpb24yLmNyZWF0ZSA9IGNyZWF0ZTtcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICBsZXQgY2FuZGlkYXRlID0gdmFsdWU7XG4gICAgcmV0dXJuIGNhbmRpZGF0ZSAmJiBJcy5zdHJpbmcoY2FuZGlkYXRlLnRpdGxlKSAmJiAoY2FuZGlkYXRlLmRpYWdub3N0aWNzID09PSB2b2lkIDAgfHwgSXMudHlwZWRBcnJheShjYW5kaWRhdGUuZGlhZ25vc3RpY3MsIERpYWdub3N0aWMuaXMpKSAmJiAoY2FuZGlkYXRlLmtpbmQgPT09IHZvaWQgMCB8fCBJcy5zdHJpbmcoY2FuZGlkYXRlLmtpbmQpKSAmJiAoY2FuZGlkYXRlLmVkaXQgIT09IHZvaWQgMCB8fCBjYW5kaWRhdGUuY29tbWFuZCAhPT0gdm9pZCAwKSAmJiAoY2FuZGlkYXRlLmNvbW1hbmQgPT09IHZvaWQgMCB8fCBDb21tYW5kLmlzKGNhbmRpZGF0ZS5jb21tYW5kKSkgJiYgKGNhbmRpZGF0ZS5pc1ByZWZlcnJlZCA9PT0gdm9pZCAwIHx8IElzLmJvb2xlYW4oY2FuZGlkYXRlLmlzUHJlZmVycmVkKSkgJiYgKGNhbmRpZGF0ZS5lZGl0ID09PSB2b2lkIDAgfHwgV29ya3NwYWNlRWRpdC5pcyhjYW5kaWRhdGUuZWRpdCkpO1xuICB9XG4gIENvZGVBY3Rpb24yLmlzID0gaXM7XG59KShDb2RlQWN0aW9uIHx8IChDb2RlQWN0aW9uID0ge30pKTtcbnZhciBDb2RlTGVucztcbihmdW5jdGlvbihDb2RlTGVuczIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKHJhbmdlLCBkYXRhKSB7XG4gICAgbGV0IHJlc3VsdCA9IHsgcmFuZ2UgfTtcbiAgICBpZiAoSXMuZGVmaW5lZChkYXRhKSkge1xuICAgICAgcmVzdWx0LmRhdGEgPSBkYXRhO1xuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xuICB9XG4gIENvZGVMZW5zMi5jcmVhdGUgPSBjcmVhdGU7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgbGV0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBJcy5kZWZpbmVkKGNhbmRpZGF0ZSkgJiYgUmFuZ2UuaXMoY2FuZGlkYXRlLnJhbmdlKSAmJiAoSXMudW5kZWZpbmVkKGNhbmRpZGF0ZS5jb21tYW5kKSB8fCBDb21tYW5kLmlzKGNhbmRpZGF0ZS5jb21tYW5kKSk7XG4gIH1cbiAgQ29kZUxlbnMyLmlzID0gaXM7XG59KShDb2RlTGVucyB8fCAoQ29kZUxlbnMgPSB7fSkpO1xudmFyIEZvcm1hdHRpbmdPcHRpb25zO1xuKGZ1bmN0aW9uKEZvcm1hdHRpbmdPcHRpb25zMikge1xuICBmdW5jdGlvbiBjcmVhdGUodGFiU2l6ZSwgaW5zZXJ0U3BhY2VzKSB7XG4gICAgcmV0dXJuIHsgdGFiU2l6ZSwgaW5zZXJ0U3BhY2VzIH07XG4gIH1cbiAgRm9ybWF0dGluZ09wdGlvbnMyLmNyZWF0ZSA9IGNyZWF0ZTtcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICBsZXQgY2FuZGlkYXRlID0gdmFsdWU7XG4gICAgcmV0dXJuIElzLmRlZmluZWQoY2FuZGlkYXRlKSAmJiBJcy51aW50ZWdlcihjYW5kaWRhdGUudGFiU2l6ZSkgJiYgSXMuYm9vbGVhbihjYW5kaWRhdGUuaW5zZXJ0U3BhY2VzKTtcbiAgfVxuICBGb3JtYXR0aW5nT3B0aW9uczIuaXMgPSBpcztcbn0pKEZvcm1hdHRpbmdPcHRpb25zIHx8IChGb3JtYXR0aW5nT3B0aW9ucyA9IHt9KSk7XG52YXIgRG9jdW1lbnRMaW5rO1xuKGZ1bmN0aW9uKERvY3VtZW50TGluazIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKHJhbmdlLCB0YXJnZXQsIGRhdGEpIHtcbiAgICByZXR1cm4geyByYW5nZSwgdGFyZ2V0LCBkYXRhIH07XG4gIH1cbiAgRG9jdW1lbnRMaW5rMi5jcmVhdGUgPSBjcmVhdGU7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgbGV0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBJcy5kZWZpbmVkKGNhbmRpZGF0ZSkgJiYgUmFuZ2UuaXMoY2FuZGlkYXRlLnJhbmdlKSAmJiAoSXMudW5kZWZpbmVkKGNhbmRpZGF0ZS50YXJnZXQpIHx8IElzLnN0cmluZyhjYW5kaWRhdGUudGFyZ2V0KSk7XG4gIH1cbiAgRG9jdW1lbnRMaW5rMi5pcyA9IGlzO1xufSkoRG9jdW1lbnRMaW5rIHx8IChEb2N1bWVudExpbmsgPSB7fSkpO1xudmFyIFNlbGVjdGlvblJhbmdlO1xuKGZ1bmN0aW9uKFNlbGVjdGlvblJhbmdlMikge1xuICBmdW5jdGlvbiBjcmVhdGUocmFuZ2UsIHBhcmVudCkge1xuICAgIHJldHVybiB7IHJhbmdlLCBwYXJlbnQgfTtcbiAgfVxuICBTZWxlY3Rpb25SYW5nZTIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGxldCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gSXMub2JqZWN0TGl0ZXJhbChjYW5kaWRhdGUpICYmIFJhbmdlLmlzKGNhbmRpZGF0ZS5yYW5nZSkgJiYgKGNhbmRpZGF0ZS5wYXJlbnQgPT09IHZvaWQgMCB8fCBTZWxlY3Rpb25SYW5nZTIuaXMoY2FuZGlkYXRlLnBhcmVudCkpO1xuICB9XG4gIFNlbGVjdGlvblJhbmdlMi5pcyA9IGlzO1xufSkoU2VsZWN0aW9uUmFuZ2UgfHwgKFNlbGVjdGlvblJhbmdlID0ge30pKTtcbnZhciBTZW1hbnRpY1Rva2VuVHlwZXM7XG4oZnVuY3Rpb24oU2VtYW50aWNUb2tlblR5cGVzMikge1xuICBTZW1hbnRpY1Rva2VuVHlwZXMyW1wibmFtZXNwYWNlXCJdID0gXCJuYW1lc3BhY2VcIjtcbiAgU2VtYW50aWNUb2tlblR5cGVzMltcInR5cGVcIl0gPSBcInR5cGVcIjtcbiAgU2VtYW50aWNUb2tlblR5cGVzMltcImNsYXNzXCJdID0gXCJjbGFzc1wiO1xuICBTZW1hbnRpY1Rva2VuVHlwZXMyW1wiZW51bVwiXSA9IFwiZW51bVwiO1xuICBTZW1hbnRpY1Rva2VuVHlwZXMyW1wiaW50ZXJmYWNlXCJdID0gXCJpbnRlcmZhY2VcIjtcbiAgU2VtYW50aWNUb2tlblR5cGVzMltcInN0cnVjdFwiXSA9IFwic3RydWN0XCI7XG4gIFNlbWFudGljVG9rZW5UeXBlczJbXCJ0eXBlUGFyYW1ldGVyXCJdID0gXCJ0eXBlUGFyYW1ldGVyXCI7XG4gIFNlbWFudGljVG9rZW5UeXBlczJbXCJwYXJhbWV0ZXJcIl0gPSBcInBhcmFtZXRlclwiO1xuICBTZW1hbnRpY1Rva2VuVHlwZXMyW1widmFyaWFibGVcIl0gPSBcInZhcmlhYmxlXCI7XG4gIFNlbWFudGljVG9rZW5UeXBlczJbXCJwcm9wZXJ0eVwiXSA9IFwicHJvcGVydHlcIjtcbiAgU2VtYW50aWNUb2tlblR5cGVzMltcImVudW1NZW1iZXJcIl0gPSBcImVudW1NZW1iZXJcIjtcbiAgU2VtYW50aWNUb2tlblR5cGVzMltcImV2ZW50XCJdID0gXCJldmVudFwiO1xuICBTZW1hbnRpY1Rva2VuVHlwZXMyW1wiZnVuY3Rpb25cIl0gPSBcImZ1bmN0aW9uXCI7XG4gIFNlbWFudGljVG9rZW5UeXBlczJbXCJtZXRob2RcIl0gPSBcIm1ldGhvZFwiO1xuICBTZW1hbnRpY1Rva2VuVHlwZXMyW1wibWFjcm9cIl0gPSBcIm1hY3JvXCI7XG4gIFNlbWFudGljVG9rZW5UeXBlczJbXCJrZXl3b3JkXCJdID0gXCJrZXl3b3JkXCI7XG4gIFNlbWFudGljVG9rZW5UeXBlczJbXCJtb2RpZmllclwiXSA9IFwibW9kaWZpZXJcIjtcbiAgU2VtYW50aWNUb2tlblR5cGVzMltcImNvbW1lbnRcIl0gPSBcImNvbW1lbnRcIjtcbiAgU2VtYW50aWNUb2tlblR5cGVzMltcInN0cmluZ1wiXSA9IFwic3RyaW5nXCI7XG4gIFNlbWFudGljVG9rZW5UeXBlczJbXCJudW1iZXJcIl0gPSBcIm51bWJlclwiO1xuICBTZW1hbnRpY1Rva2VuVHlwZXMyW1wicmVnZXhwXCJdID0gXCJyZWdleHBcIjtcbiAgU2VtYW50aWNUb2tlblR5cGVzMltcIm9wZXJhdG9yXCJdID0gXCJvcGVyYXRvclwiO1xuICBTZW1hbnRpY1Rva2VuVHlwZXMyW1wiZGVjb3JhdG9yXCJdID0gXCJkZWNvcmF0b3JcIjtcbn0pKFNlbWFudGljVG9rZW5UeXBlcyB8fCAoU2VtYW50aWNUb2tlblR5cGVzID0ge30pKTtcbnZhciBTZW1hbnRpY1Rva2VuTW9kaWZpZXJzO1xuKGZ1bmN0aW9uKFNlbWFudGljVG9rZW5Nb2RpZmllcnMyKSB7XG4gIFNlbWFudGljVG9rZW5Nb2RpZmllcnMyW1wiZGVjbGFyYXRpb25cIl0gPSBcImRlY2xhcmF0aW9uXCI7XG4gIFNlbWFudGljVG9rZW5Nb2RpZmllcnMyW1wiZGVmaW5pdGlvblwiXSA9IFwiZGVmaW5pdGlvblwiO1xuICBTZW1hbnRpY1Rva2VuTW9kaWZpZXJzMltcInJlYWRvbmx5XCJdID0gXCJyZWFkb25seVwiO1xuICBTZW1hbnRpY1Rva2VuTW9kaWZpZXJzMltcInN0YXRpY1wiXSA9IFwic3RhdGljXCI7XG4gIFNlbWFudGljVG9rZW5Nb2RpZmllcnMyW1wiZGVwcmVjYXRlZFwiXSA9IFwiZGVwcmVjYXRlZFwiO1xuICBTZW1hbnRpY1Rva2VuTW9kaWZpZXJzMltcImFic3RyYWN0XCJdID0gXCJhYnN0cmFjdFwiO1xuICBTZW1hbnRpY1Rva2VuTW9kaWZpZXJzMltcImFzeW5jXCJdID0gXCJhc3luY1wiO1xuICBTZW1hbnRpY1Rva2VuTW9kaWZpZXJzMltcIm1vZGlmaWNhdGlvblwiXSA9IFwibW9kaWZpY2F0aW9uXCI7XG4gIFNlbWFudGljVG9rZW5Nb2RpZmllcnMyW1wiZG9jdW1lbnRhdGlvblwiXSA9IFwiZG9jdW1lbnRhdGlvblwiO1xuICBTZW1hbnRpY1Rva2VuTW9kaWZpZXJzMltcImRlZmF1bHRMaWJyYXJ5XCJdID0gXCJkZWZhdWx0TGlicmFyeVwiO1xufSkoU2VtYW50aWNUb2tlbk1vZGlmaWVycyB8fCAoU2VtYW50aWNUb2tlbk1vZGlmaWVycyA9IHt9KSk7XG52YXIgU2VtYW50aWNUb2tlbnM7XG4oZnVuY3Rpb24oU2VtYW50aWNUb2tlbnMyKSB7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgY29uc3QgY2FuZGlkYXRlID0gdmFsdWU7XG4gICAgcmV0dXJuIElzLm9iamVjdExpdGVyYWwoY2FuZGlkYXRlKSAmJiAoY2FuZGlkYXRlLnJlc3VsdElkID09PSB2b2lkIDAgfHwgdHlwZW9mIGNhbmRpZGF0ZS5yZXN1bHRJZCA9PT0gXCJzdHJpbmdcIikgJiYgQXJyYXkuaXNBcnJheShjYW5kaWRhdGUuZGF0YSkgJiYgKGNhbmRpZGF0ZS5kYXRhLmxlbmd0aCA9PT0gMCB8fCB0eXBlb2YgY2FuZGlkYXRlLmRhdGFbMF0gPT09IFwibnVtYmVyXCIpO1xuICB9XG4gIFNlbWFudGljVG9rZW5zMi5pcyA9IGlzO1xufSkoU2VtYW50aWNUb2tlbnMgfHwgKFNlbWFudGljVG9rZW5zID0ge30pKTtcbnZhciBJbmxpbmVWYWx1ZVRleHQ7XG4oZnVuY3Rpb24oSW5saW5lVmFsdWVUZXh0Mikge1xuICBmdW5jdGlvbiBjcmVhdGUocmFuZ2UsIHRleHQpIHtcbiAgICByZXR1cm4geyByYW5nZSwgdGV4dCB9O1xuICB9XG4gIElubGluZVZhbHVlVGV4dDIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGNvbnN0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBjYW5kaWRhdGUgIT09IHZvaWQgMCAmJiBjYW5kaWRhdGUgIT09IG51bGwgJiYgUmFuZ2UuaXMoY2FuZGlkYXRlLnJhbmdlKSAmJiBJcy5zdHJpbmcoY2FuZGlkYXRlLnRleHQpO1xuICB9XG4gIElubGluZVZhbHVlVGV4dDIuaXMgPSBpcztcbn0pKElubGluZVZhbHVlVGV4dCB8fCAoSW5saW5lVmFsdWVUZXh0ID0ge30pKTtcbnZhciBJbmxpbmVWYWx1ZVZhcmlhYmxlTG9va3VwO1xuKGZ1bmN0aW9uKElubGluZVZhbHVlVmFyaWFibGVMb29rdXAyKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZShyYW5nZSwgdmFyaWFibGVOYW1lLCBjYXNlU2Vuc2l0aXZlTG9va3VwKSB7XG4gICAgcmV0dXJuIHsgcmFuZ2UsIHZhcmlhYmxlTmFtZSwgY2FzZVNlbnNpdGl2ZUxvb2t1cCB9O1xuICB9XG4gIElubGluZVZhbHVlVmFyaWFibGVMb29rdXAyLmNyZWF0ZSA9IGNyZWF0ZTtcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICBjb25zdCBjYW5kaWRhdGUgPSB2YWx1ZTtcbiAgICByZXR1cm4gY2FuZGlkYXRlICE9PSB2b2lkIDAgJiYgY2FuZGlkYXRlICE9PSBudWxsICYmIFJhbmdlLmlzKGNhbmRpZGF0ZS5yYW5nZSkgJiYgSXMuYm9vbGVhbihjYW5kaWRhdGUuY2FzZVNlbnNpdGl2ZUxvb2t1cCkgJiYgKElzLnN0cmluZyhjYW5kaWRhdGUudmFyaWFibGVOYW1lKSB8fCBjYW5kaWRhdGUudmFyaWFibGVOYW1lID09PSB2b2lkIDApO1xuICB9XG4gIElubGluZVZhbHVlVmFyaWFibGVMb29rdXAyLmlzID0gaXM7XG59KShJbmxpbmVWYWx1ZVZhcmlhYmxlTG9va3VwIHx8IChJbmxpbmVWYWx1ZVZhcmlhYmxlTG9va3VwID0ge30pKTtcbnZhciBJbmxpbmVWYWx1ZUV2YWx1YXRhYmxlRXhwcmVzc2lvbjtcbihmdW5jdGlvbihJbmxpbmVWYWx1ZUV2YWx1YXRhYmxlRXhwcmVzc2lvbjIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKHJhbmdlLCBleHByZXNzaW9uKSB7XG4gICAgcmV0dXJuIHsgcmFuZ2UsIGV4cHJlc3Npb24gfTtcbiAgfVxuICBJbmxpbmVWYWx1ZUV2YWx1YXRhYmxlRXhwcmVzc2lvbjIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGNvbnN0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBjYW5kaWRhdGUgIT09IHZvaWQgMCAmJiBjYW5kaWRhdGUgIT09IG51bGwgJiYgUmFuZ2UuaXMoY2FuZGlkYXRlLnJhbmdlKSAmJiAoSXMuc3RyaW5nKGNhbmRpZGF0ZS5leHByZXNzaW9uKSB8fCBjYW5kaWRhdGUuZXhwcmVzc2lvbiA9PT0gdm9pZCAwKTtcbiAgfVxuICBJbmxpbmVWYWx1ZUV2YWx1YXRhYmxlRXhwcmVzc2lvbjIuaXMgPSBpcztcbn0pKElubGluZVZhbHVlRXZhbHVhdGFibGVFeHByZXNzaW9uIHx8IChJbmxpbmVWYWx1ZUV2YWx1YXRhYmxlRXhwcmVzc2lvbiA9IHt9KSk7XG52YXIgSW5saW5lVmFsdWVDb250ZXh0O1xuKGZ1bmN0aW9uKElubGluZVZhbHVlQ29udGV4dDIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKGZyYW1lSWQsIHN0b3BwZWRMb2NhdGlvbikge1xuICAgIHJldHVybiB7IGZyYW1lSWQsIHN0b3BwZWRMb2NhdGlvbiB9O1xuICB9XG4gIElubGluZVZhbHVlQ29udGV4dDIuY3JlYXRlID0gY3JlYXRlO1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGNvbnN0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBJcy5kZWZpbmVkKGNhbmRpZGF0ZSkgJiYgUmFuZ2UuaXModmFsdWUuc3RvcHBlZExvY2F0aW9uKTtcbiAgfVxuICBJbmxpbmVWYWx1ZUNvbnRleHQyLmlzID0gaXM7XG59KShJbmxpbmVWYWx1ZUNvbnRleHQgfHwgKElubGluZVZhbHVlQ29udGV4dCA9IHt9KSk7XG52YXIgSW5sYXlIaW50S2luZDtcbihmdW5jdGlvbihJbmxheUhpbnRLaW5kMikge1xuICBJbmxheUhpbnRLaW5kMi5UeXBlID0gMTtcbiAgSW5sYXlIaW50S2luZDIuUGFyYW1ldGVyID0gMjtcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICByZXR1cm4gdmFsdWUgPT09IDEgfHwgdmFsdWUgPT09IDI7XG4gIH1cbiAgSW5sYXlIaW50S2luZDIuaXMgPSBpcztcbn0pKElubGF5SGludEtpbmQgfHwgKElubGF5SGludEtpbmQgPSB7fSkpO1xudmFyIElubGF5SGludExhYmVsUGFydDtcbihmdW5jdGlvbihJbmxheUhpbnRMYWJlbFBhcnQyKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZSh2YWx1ZSkge1xuICAgIHJldHVybiB7IHZhbHVlIH07XG4gIH1cbiAgSW5sYXlIaW50TGFiZWxQYXJ0Mi5jcmVhdGUgPSBjcmVhdGU7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgY29uc3QgY2FuZGlkYXRlID0gdmFsdWU7XG4gICAgcmV0dXJuIElzLm9iamVjdExpdGVyYWwoY2FuZGlkYXRlKSAmJiAoY2FuZGlkYXRlLnRvb2x0aXAgPT09IHZvaWQgMCB8fCBJcy5zdHJpbmcoY2FuZGlkYXRlLnRvb2x0aXApIHx8IE1hcmt1cENvbnRlbnQuaXMoY2FuZGlkYXRlLnRvb2x0aXApKSAmJiAoY2FuZGlkYXRlLmxvY2F0aW9uID09PSB2b2lkIDAgfHwgTG9jYXRpb24uaXMoY2FuZGlkYXRlLmxvY2F0aW9uKSkgJiYgKGNhbmRpZGF0ZS5jb21tYW5kID09PSB2b2lkIDAgfHwgQ29tbWFuZC5pcyhjYW5kaWRhdGUuY29tbWFuZCkpO1xuICB9XG4gIElubGF5SGludExhYmVsUGFydDIuaXMgPSBpcztcbn0pKElubGF5SGludExhYmVsUGFydCB8fCAoSW5sYXlIaW50TGFiZWxQYXJ0ID0ge30pKTtcbnZhciBJbmxheUhpbnQ7XG4oZnVuY3Rpb24oSW5sYXlIaW50Mikge1xuICBmdW5jdGlvbiBjcmVhdGUocG9zaXRpb24sIGxhYmVsLCBraW5kKSB7XG4gICAgY29uc3QgcmVzdWx0ID0geyBwb3NpdGlvbiwgbGFiZWwgfTtcbiAgICBpZiAoa2luZCAhPT0gdm9pZCAwKSB7XG4gICAgICByZXN1bHQua2luZCA9IGtpbmQ7XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbiAgSW5sYXlIaW50Mi5jcmVhdGUgPSBjcmVhdGU7XG4gIGZ1bmN0aW9uIGlzKHZhbHVlKSB7XG4gICAgY29uc3QgY2FuZGlkYXRlID0gdmFsdWU7XG4gICAgcmV0dXJuIElzLm9iamVjdExpdGVyYWwoY2FuZGlkYXRlKSAmJiBQb3NpdGlvbi5pcyhjYW5kaWRhdGUucG9zaXRpb24pICYmIChJcy5zdHJpbmcoY2FuZGlkYXRlLmxhYmVsKSB8fCBJcy50eXBlZEFycmF5KGNhbmRpZGF0ZS5sYWJlbCwgSW5sYXlIaW50TGFiZWxQYXJ0LmlzKSkgJiYgKGNhbmRpZGF0ZS5raW5kID09PSB2b2lkIDAgfHwgSW5sYXlIaW50S2luZC5pcyhjYW5kaWRhdGUua2luZCkpICYmIGNhbmRpZGF0ZS50ZXh0RWRpdHMgPT09IHZvaWQgMCB8fCBJcy50eXBlZEFycmF5KGNhbmRpZGF0ZS50ZXh0RWRpdHMsIFRleHRFZGl0LmlzKSAmJiAoY2FuZGlkYXRlLnRvb2x0aXAgPT09IHZvaWQgMCB8fCBJcy5zdHJpbmcoY2FuZGlkYXRlLnRvb2x0aXApIHx8IE1hcmt1cENvbnRlbnQuaXMoY2FuZGlkYXRlLnRvb2x0aXApKSAmJiAoY2FuZGlkYXRlLnBhZGRpbmdMZWZ0ID09PSB2b2lkIDAgfHwgSXMuYm9vbGVhbihjYW5kaWRhdGUucGFkZGluZ0xlZnQpKSAmJiAoY2FuZGlkYXRlLnBhZGRpbmdSaWdodCA9PT0gdm9pZCAwIHx8IElzLmJvb2xlYW4oY2FuZGlkYXRlLnBhZGRpbmdSaWdodCkpO1xuICB9XG4gIElubGF5SGludDIuaXMgPSBpcztcbn0pKElubGF5SGludCB8fCAoSW5sYXlIaW50ID0ge30pKTtcbnZhciBTdHJpbmdWYWx1ZTtcbihmdW5jdGlvbihTdHJpbmdWYWx1ZTIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlU25pcHBldCh2YWx1ZSkge1xuICAgIHJldHVybiB7IGtpbmQ6IFwic25pcHBldFwiLCB2YWx1ZSB9O1xuICB9XG4gIFN0cmluZ1ZhbHVlMi5jcmVhdGVTbmlwcGV0ID0gY3JlYXRlU25pcHBldDtcbn0pKFN0cmluZ1ZhbHVlIHx8IChTdHJpbmdWYWx1ZSA9IHt9KSk7XG52YXIgSW5saW5lQ29tcGxldGlvbkl0ZW07XG4oZnVuY3Rpb24oSW5saW5lQ29tcGxldGlvbkl0ZW0yKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZShpbnNlcnRUZXh0LCBmaWx0ZXJUZXh0LCByYW5nZSwgY29tbWFuZCkge1xuICAgIHJldHVybiB7IGluc2VydFRleHQsIGZpbHRlclRleHQsIHJhbmdlLCBjb21tYW5kIH07XG4gIH1cbiAgSW5saW5lQ29tcGxldGlvbkl0ZW0yLmNyZWF0ZSA9IGNyZWF0ZTtcbn0pKElubGluZUNvbXBsZXRpb25JdGVtIHx8IChJbmxpbmVDb21wbGV0aW9uSXRlbSA9IHt9KSk7XG52YXIgSW5saW5lQ29tcGxldGlvbkxpc3Q7XG4oZnVuY3Rpb24oSW5saW5lQ29tcGxldGlvbkxpc3QyKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZShpdGVtcykge1xuICAgIHJldHVybiB7IGl0ZW1zIH07XG4gIH1cbiAgSW5saW5lQ29tcGxldGlvbkxpc3QyLmNyZWF0ZSA9IGNyZWF0ZTtcbn0pKElubGluZUNvbXBsZXRpb25MaXN0IHx8IChJbmxpbmVDb21wbGV0aW9uTGlzdCA9IHt9KSk7XG52YXIgSW5saW5lQ29tcGxldGlvblRyaWdnZXJLaW5kO1xuKGZ1bmN0aW9uKElubGluZUNvbXBsZXRpb25UcmlnZ2VyS2luZDIpIHtcbiAgSW5saW5lQ29tcGxldGlvblRyaWdnZXJLaW5kMi5JbnZva2VkID0gMDtcbiAgSW5saW5lQ29tcGxldGlvblRyaWdnZXJLaW5kMi5BdXRvbWF0aWMgPSAxO1xufSkoSW5saW5lQ29tcGxldGlvblRyaWdnZXJLaW5kIHx8IChJbmxpbmVDb21wbGV0aW9uVHJpZ2dlcktpbmQgPSB7fSkpO1xudmFyIFNlbGVjdGVkQ29tcGxldGlvbkluZm87XG4oZnVuY3Rpb24oU2VsZWN0ZWRDb21wbGV0aW9uSW5mbzIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKHJhbmdlLCB0ZXh0KSB7XG4gICAgcmV0dXJuIHsgcmFuZ2UsIHRleHQgfTtcbiAgfVxuICBTZWxlY3RlZENvbXBsZXRpb25JbmZvMi5jcmVhdGUgPSBjcmVhdGU7XG59KShTZWxlY3RlZENvbXBsZXRpb25JbmZvIHx8IChTZWxlY3RlZENvbXBsZXRpb25JbmZvID0ge30pKTtcbnZhciBJbmxpbmVDb21wbGV0aW9uQ29udGV4dDtcbihmdW5jdGlvbihJbmxpbmVDb21wbGV0aW9uQ29udGV4dDIpIHtcbiAgZnVuY3Rpb24gY3JlYXRlKHRyaWdnZXJLaW5kLCBzZWxlY3RlZENvbXBsZXRpb25JbmZvKSB7XG4gICAgcmV0dXJuIHsgdHJpZ2dlcktpbmQsIHNlbGVjdGVkQ29tcGxldGlvbkluZm8gfTtcbiAgfVxuICBJbmxpbmVDb21wbGV0aW9uQ29udGV4dDIuY3JlYXRlID0gY3JlYXRlO1xufSkoSW5saW5lQ29tcGxldGlvbkNvbnRleHQgfHwgKElubGluZUNvbXBsZXRpb25Db250ZXh0ID0ge30pKTtcbnZhciBXb3Jrc3BhY2VGb2xkZXI7XG4oZnVuY3Rpb24oV29ya3NwYWNlRm9sZGVyMikge1xuICBmdW5jdGlvbiBpcyh2YWx1ZSkge1xuICAgIGNvbnN0IGNhbmRpZGF0ZSA9IHZhbHVlO1xuICAgIHJldHVybiBJcy5vYmplY3RMaXRlcmFsKGNhbmRpZGF0ZSkgJiYgVVJJLmlzKGNhbmRpZGF0ZS51cmkpICYmIElzLnN0cmluZyhjYW5kaWRhdGUubmFtZSk7XG4gIH1cbiAgV29ya3NwYWNlRm9sZGVyMi5pcyA9IGlzO1xufSkoV29ya3NwYWNlRm9sZGVyIHx8IChXb3Jrc3BhY2VGb2xkZXIgPSB7fSkpO1xudmFyIFRleHREb2N1bWVudDtcbihmdW5jdGlvbihUZXh0RG9jdW1lbnQyKSB7XG4gIGZ1bmN0aW9uIGNyZWF0ZSh1cmksIGxhbmd1YWdlSWQsIHZlcnNpb24sIGNvbnRlbnQpIHtcbiAgICByZXR1cm4gbmV3IEZ1bGxUZXh0RG9jdW1lbnQodXJpLCBsYW5ndWFnZUlkLCB2ZXJzaW9uLCBjb250ZW50KTtcbiAgfVxuICBUZXh0RG9jdW1lbnQyLmNyZWF0ZSA9IGNyZWF0ZTtcbiAgZnVuY3Rpb24gaXModmFsdWUpIHtcbiAgICBsZXQgY2FuZGlkYXRlID0gdmFsdWU7XG4gICAgcmV0dXJuIElzLmRlZmluZWQoY2FuZGlkYXRlKSAmJiBJcy5zdHJpbmcoY2FuZGlkYXRlLnVyaSkgJiYgKElzLnVuZGVmaW5lZChjYW5kaWRhdGUubGFuZ3VhZ2VJZCkgfHwgSXMuc3RyaW5nKGNhbmRpZGF0ZS5sYW5ndWFnZUlkKSkgJiYgSXMudWludGVnZXIoY2FuZGlkYXRlLmxpbmVDb3VudCkgJiYgSXMuZnVuYyhjYW5kaWRhdGUuZ2V0VGV4dCkgJiYgSXMuZnVuYyhjYW5kaWRhdGUucG9zaXRpb25BdCkgJiYgSXMuZnVuYyhjYW5kaWRhdGUub2Zmc2V0QXQpID8gdHJ1ZSA6IGZhbHNlO1xuICB9XG4gIFRleHREb2N1bWVudDIuaXMgPSBpcztcbiAgZnVuY3Rpb24gYXBwbHlFZGl0cyhkb2N1bWVudCwgZWRpdHMpIHtcbiAgICBsZXQgdGV4dCA9IGRvY3VtZW50LmdldFRleHQoKTtcbiAgICBsZXQgc29ydGVkRWRpdHMgPSBtZXJnZVNvcnQoZWRpdHMsIChhLCBiKSA9PiB7XG4gICAgICBsZXQgZGlmZiA9IGEucmFuZ2Uuc3RhcnQubGluZSAtIGIucmFuZ2Uuc3RhcnQubGluZTtcbiAgICAgIGlmIChkaWZmID09PSAwKSB7XG4gICAgICAgIHJldHVybiBhLnJhbmdlLnN0YXJ0LmNoYXJhY3RlciAtIGIucmFuZ2Uuc3RhcnQuY2hhcmFjdGVyO1xuICAgICAgfVxuICAgICAgcmV0dXJuIGRpZmY7XG4gICAgfSk7XG4gICAgbGV0IGxhc3RNb2RpZmllZE9mZnNldCA9IHRleHQubGVuZ3RoO1xuICAgIGZvciAobGV0IGkgPSBzb3J0ZWRFZGl0cy5sZW5ndGggLSAxOyBpID49IDA7IGktLSkge1xuICAgICAgbGV0IGUgPSBzb3J0ZWRFZGl0c1tpXTtcbiAgICAgIGxldCBzdGFydE9mZnNldCA9IGRvY3VtZW50Lm9mZnNldEF0KGUucmFuZ2Uuc3RhcnQpO1xuICAgICAgbGV0IGVuZE9mZnNldCA9IGRvY3VtZW50Lm9mZnNldEF0KGUucmFuZ2UuZW5kKTtcbiAgICAgIGlmIChlbmRPZmZzZXQgPD0gbGFzdE1vZGlmaWVkT2Zmc2V0KSB7XG4gICAgICAgIHRleHQgPSB0ZXh0LnN1YnN0cmluZygwLCBzdGFydE9mZnNldCkgKyBlLm5ld1RleHQgKyB0ZXh0LnN1YnN0cmluZyhlbmRPZmZzZXQsIHRleHQubGVuZ3RoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIk92ZXJsYXBwaW5nIGVkaXRcIik7XG4gICAgICB9XG4gICAgICBsYXN0TW9kaWZpZWRPZmZzZXQgPSBzdGFydE9mZnNldDtcbiAgICB9XG4gICAgcmV0dXJuIHRleHQ7XG4gIH1cbiAgVGV4dERvY3VtZW50Mi5hcHBseUVkaXRzID0gYXBwbHlFZGl0cztcbiAgZnVuY3Rpb24gbWVyZ2VTb3J0KGRhdGEsIGNvbXBhcmUpIHtcbiAgICBpZiAoZGF0YS5sZW5ndGggPD0gMSkge1xuICAgICAgcmV0dXJuIGRhdGE7XG4gICAgfVxuICAgIGNvbnN0IHAgPSBkYXRhLmxlbmd0aCAvIDIgfCAwO1xuICAgIGNvbnN0IGxlZnQgPSBkYXRhLnNsaWNlKDAsIHApO1xuICAgIGNvbnN0IHJpZ2h0ID0gZGF0YS5zbGljZShwKTtcbiAgICBtZXJnZVNvcnQobGVmdCwgY29tcGFyZSk7XG4gICAgbWVyZ2VTb3J0KHJpZ2h0LCBjb21wYXJlKTtcbiAgICBsZXQgbGVmdElkeCA9IDA7XG4gICAgbGV0IHJpZ2h0SWR4ID0gMDtcbiAgICBsZXQgaSA9IDA7XG4gICAgd2hpbGUgKGxlZnRJZHggPCBsZWZ0Lmxlbmd0aCAmJiByaWdodElkeCA8IHJpZ2h0Lmxlbmd0aCkge1xuICAgICAgbGV0IHJldCA9IGNvbXBhcmUobGVmdFtsZWZ0SWR4XSwgcmlnaHRbcmlnaHRJZHhdKTtcbiAgICAgIGlmIChyZXQgPD0gMCkge1xuICAgICAgICBkYXRhW2krK10gPSBsZWZ0W2xlZnRJZHgrK107XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBkYXRhW2krK10gPSByaWdodFtyaWdodElkeCsrXTtcbiAgICAgIH1cbiAgICB9XG4gICAgd2hpbGUgKGxlZnRJZHggPCBsZWZ0Lmxlbmd0aCkge1xuICAgICAgZGF0YVtpKytdID0gbGVmdFtsZWZ0SWR4KytdO1xuICAgIH1cbiAgICB3aGlsZSAocmlnaHRJZHggPCByaWdodC5sZW5ndGgpIHtcbiAgICAgIGRhdGFbaSsrXSA9IHJpZ2h0W3JpZ2h0SWR4KytdO1xuICAgIH1cbiAgICByZXR1cm4gZGF0YTtcbiAgfVxufSkoVGV4dERvY3VtZW50IHx8IChUZXh0RG9jdW1lbnQgPSB7fSkpO1xudmFyIEZ1bGxUZXh0RG9jdW1lbnQgPSBjbGFzcyB7XG4gIGNvbnN0cnVjdG9yKHVyaSwgbGFuZ3VhZ2VJZCwgdmVyc2lvbiwgY29udGVudCkge1xuICAgIHRoaXMuX3VyaSA9IHVyaTtcbiAgICB0aGlzLl9sYW5ndWFnZUlkID0gbGFuZ3VhZ2VJZDtcbiAgICB0aGlzLl92ZXJzaW9uID0gdmVyc2lvbjtcbiAgICB0aGlzLl9jb250ZW50ID0gY29udGVudDtcbiAgICB0aGlzLl9saW5lT2Zmc2V0cyA9IHZvaWQgMDtcbiAgfVxuICBnZXQgdXJpKCkge1xuICAgIHJldHVybiB0aGlzLl91cmk7XG4gIH1cbiAgZ2V0IGxhbmd1YWdlSWQoKSB7XG4gICAgcmV0dXJuIHRoaXMuX2xhbmd1YWdlSWQ7XG4gIH1cbiAgZ2V0IHZlcnNpb24oKSB7XG4gICAgcmV0dXJuIHRoaXMuX3ZlcnNpb247XG4gIH1cbiAgZ2V0VGV4dChyYW5nZSkge1xuICAgIGlmIChyYW5nZSkge1xuICAgICAgbGV0IHN0YXJ0ID0gdGhpcy5vZmZzZXRBdChyYW5nZS5zdGFydCk7XG4gICAgICBsZXQgZW5kID0gdGhpcy5vZmZzZXRBdChyYW5nZS5lbmQpO1xuICAgICAgcmV0dXJuIHRoaXMuX2NvbnRlbnQuc3Vic3RyaW5nKHN0YXJ0LCBlbmQpO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy5fY29udGVudDtcbiAgfVxuICB1cGRhdGUoZXZlbnQsIHZlcnNpb24pIHtcbiAgICB0aGlzLl9jb250ZW50ID0gZXZlbnQudGV4dDtcbiAgICB0aGlzLl92ZXJzaW9uID0gdmVyc2lvbjtcbiAgICB0aGlzLl9saW5lT2Zmc2V0cyA9IHZvaWQgMDtcbiAgfVxuICBnZXRMaW5lT2Zmc2V0cygpIHtcbiAgICBpZiAodGhpcy5fbGluZU9mZnNldHMgPT09IHZvaWQgMCkge1xuICAgICAgbGV0IGxpbmVPZmZzZXRzID0gW107XG4gICAgICBsZXQgdGV4dCA9IHRoaXMuX2NvbnRlbnQ7XG4gICAgICBsZXQgaXNMaW5lU3RhcnQgPSB0cnVlO1xuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0ZXh0Lmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGlmIChpc0xpbmVTdGFydCkge1xuICAgICAgICAgIGxpbmVPZmZzZXRzLnB1c2goaSk7XG4gICAgICAgICAgaXNMaW5lU3RhcnQgPSBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBsZXQgY2ggPSB0ZXh0LmNoYXJBdChpKTtcbiAgICAgICAgaXNMaW5lU3RhcnQgPSBjaCA9PT0gXCJcXHJcIiB8fCBjaCA9PT0gXCJcXG5cIjtcbiAgICAgICAgaWYgKGNoID09PSBcIlxcclwiICYmIGkgKyAxIDwgdGV4dC5sZW5ndGggJiYgdGV4dC5jaGFyQXQoaSArIDEpID09PSBcIlxcblwiKSB7XG4gICAgICAgICAgaSsrO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBpZiAoaXNMaW5lU3RhcnQgJiYgdGV4dC5sZW5ndGggPiAwKSB7XG4gICAgICAgIGxpbmVPZmZzZXRzLnB1c2godGV4dC5sZW5ndGgpO1xuICAgICAgfVxuICAgICAgdGhpcy5fbGluZU9mZnNldHMgPSBsaW5lT2Zmc2V0cztcbiAgICB9XG4gICAgcmV0dXJuIHRoaXMuX2xpbmVPZmZzZXRzO1xuICB9XG4gIHBvc2l0aW9uQXQob2Zmc2V0KSB7XG4gICAgb2Zmc2V0ID0gTWF0aC5tYXgoTWF0aC5taW4ob2Zmc2V0LCB0aGlzLl9jb250ZW50Lmxlbmd0aCksIDApO1xuICAgIGxldCBsaW5lT2Zmc2V0cyA9IHRoaXMuZ2V0TGluZU9mZnNldHMoKTtcbiAgICBsZXQgbG93ID0gMCwgaGlnaCA9IGxpbmVPZmZzZXRzLmxlbmd0aDtcbiAgICBpZiAoaGlnaCA9PT0gMCkge1xuICAgICAgcmV0dXJuIFBvc2l0aW9uLmNyZWF0ZSgwLCBvZmZzZXQpO1xuICAgIH1cbiAgICB3aGlsZSAobG93IDwgaGlnaCkge1xuICAgICAgbGV0IG1pZCA9IE1hdGguZmxvb3IoKGxvdyArIGhpZ2gpIC8gMik7XG4gICAgICBpZiAobGluZU9mZnNldHNbbWlkXSA+IG9mZnNldCkge1xuICAgICAgICBoaWdoID0gbWlkO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgbG93ID0gbWlkICsgMTtcbiAgICAgIH1cbiAgICB9XG4gICAgbGV0IGxpbmUgPSBsb3cgLSAxO1xuICAgIHJldHVybiBQb3NpdGlvbi5jcmVhdGUobGluZSwgb2Zmc2V0IC0gbGluZU9mZnNldHNbbGluZV0pO1xuICB9XG4gIG9mZnNldEF0KHBvc2l0aW9uKSB7XG4gICAgbGV0IGxpbmVPZmZzZXRzID0gdGhpcy5nZXRMaW5lT2Zmc2V0cygpO1xuICAgIGlmIChwb3NpdGlvbi5saW5lID49IGxpbmVPZmZzZXRzLmxlbmd0aCkge1xuICAgICAgcmV0dXJuIHRoaXMuX2NvbnRlbnQubGVuZ3RoO1xuICAgIH0gZWxzZSBpZiAocG9zaXRpb24ubGluZSA8IDApIHtcbiAgICAgIHJldHVybiAwO1xuICAgIH1cbiAgICBsZXQgbGluZU9mZnNldCA9IGxpbmVPZmZzZXRzW3Bvc2l0aW9uLmxpbmVdO1xuICAgIGxldCBuZXh0TGluZU9mZnNldCA9IHBvc2l0aW9uLmxpbmUgKyAxIDwgbGluZU9mZnNldHMubGVuZ3RoID8gbGluZU9mZnNldHNbcG9zaXRpb24ubGluZSArIDFdIDogdGhpcy5fY29udGVudC5sZW5ndGg7XG4gICAgcmV0dXJuIE1hdGgubWF4KE1hdGgubWluKGxpbmVPZmZzZXQgKyBwb3NpdGlvbi5jaGFyYWN0ZXIsIG5leHRMaW5lT2Zmc2V0KSwgbGluZU9mZnNldCk7XG4gIH1cbiAgZ2V0IGxpbmVDb3VudCgpIHtcbiAgICByZXR1cm4gdGhpcy5nZXRMaW5lT2Zmc2V0cygpLmxlbmd0aDtcbiAgfVxufTtcbnZhciBJcztcbihmdW5jdGlvbihJczIpIHtcbiAgY29uc3QgdG9TdHJpbmcgPSBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nO1xuICBmdW5jdGlvbiBkZWZpbmVkKHZhbHVlKSB7XG4gICAgcmV0dXJuIHR5cGVvZiB2YWx1ZSAhPT0gXCJ1bmRlZmluZWRcIjtcbiAgfVxuICBJczIuZGVmaW5lZCA9IGRlZmluZWQ7XG4gIGZ1bmN0aW9uIHVuZGVmaW5lZDIodmFsdWUpIHtcbiAgICByZXR1cm4gdHlwZW9mIHZhbHVlID09PSBcInVuZGVmaW5lZFwiO1xuICB9XG4gIElzMi51bmRlZmluZWQgPSB1bmRlZmluZWQyO1xuICBmdW5jdGlvbiBib29sZWFuKHZhbHVlKSB7XG4gICAgcmV0dXJuIHZhbHVlID09PSB0cnVlIHx8IHZhbHVlID09PSBmYWxzZTtcbiAgfVxuICBJczIuYm9vbGVhbiA9IGJvb2xlYW47XG4gIGZ1bmN0aW9uIHN0cmluZyh2YWx1ZSkge1xuICAgIHJldHVybiB0b1N0cmluZy5jYWxsKHZhbHVlKSA9PT0gXCJbb2JqZWN0IFN0cmluZ11cIjtcbiAgfVxuICBJczIuc3RyaW5nID0gc3RyaW5nO1xuICBmdW5jdGlvbiBudW1iZXIodmFsdWUpIHtcbiAgICByZXR1cm4gdG9TdHJpbmcuY2FsbCh2YWx1ZSkgPT09IFwiW29iamVjdCBOdW1iZXJdXCI7XG4gIH1cbiAgSXMyLm51bWJlciA9IG51bWJlcjtcbiAgZnVuY3Rpb24gbnVtYmVyUmFuZ2UodmFsdWUsIG1pbiwgbWF4KSB7XG4gICAgcmV0dXJuIHRvU3RyaW5nLmNhbGwodmFsdWUpID09PSBcIltvYmplY3QgTnVtYmVyXVwiICYmIG1pbiA8PSB2YWx1ZSAmJiB2YWx1ZSA8PSBtYXg7XG4gIH1cbiAgSXMyLm51bWJlclJhbmdlID0gbnVtYmVyUmFuZ2U7XG4gIGZ1bmN0aW9uIGludGVnZXIyKHZhbHVlKSB7XG4gICAgcmV0dXJuIHRvU3RyaW5nLmNhbGwodmFsdWUpID09PSBcIltvYmplY3QgTnVtYmVyXVwiICYmIC0yMTQ3NDgzNjQ4IDw9IHZhbHVlICYmIHZhbHVlIDw9IDIxNDc0ODM2NDc7XG4gIH1cbiAgSXMyLmludGVnZXIgPSBpbnRlZ2VyMjtcbiAgZnVuY3Rpb24gdWludGVnZXIyKHZhbHVlKSB7XG4gICAgcmV0dXJuIHRvU3RyaW5nLmNhbGwodmFsdWUpID09PSBcIltvYmplY3QgTnVtYmVyXVwiICYmIDAgPD0gdmFsdWUgJiYgdmFsdWUgPD0gMjE0NzQ4MzY0NztcbiAgfVxuICBJczIudWludGVnZXIgPSB1aW50ZWdlcjI7XG4gIGZ1bmN0aW9uIGZ1bmModmFsdWUpIHtcbiAgICByZXR1cm4gdG9TdHJpbmcuY2FsbCh2YWx1ZSkgPT09IFwiW29iamVjdCBGdW5jdGlvbl1cIjtcbiAgfVxuICBJczIuZnVuYyA9IGZ1bmM7XG4gIGZ1bmN0aW9uIG9iamVjdExpdGVyYWwodmFsdWUpIHtcbiAgICByZXR1cm4gdmFsdWUgIT09IG51bGwgJiYgdHlwZW9mIHZhbHVlID09PSBcIm9iamVjdFwiO1xuICB9XG4gIElzMi5vYmplY3RMaXRlcmFsID0gb2JqZWN0TGl0ZXJhbDtcbiAgZnVuY3Rpb24gdHlwZWRBcnJheSh2YWx1ZSwgY2hlY2spIHtcbiAgICByZXR1cm4gQXJyYXkuaXNBcnJheSh2YWx1ZSkgJiYgdmFsdWUuZXZlcnkoY2hlY2spO1xuICB9XG4gIElzMi50eXBlZEFycmF5ID0gdHlwZWRBcnJheTtcbn0pKElzIHx8IChJcyA9IHt9KSk7XG5cbi8vIHNyYy9sYW5ndWFnZS9jb21tb24vbHNwTGFuZ3VhZ2VGZWF0dXJlcy50c1xudmFyIERpYWdub3N0aWNzQWRhcHRlciA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoX2xhbmd1YWdlSWQsIF93b3JrZXIsIGNvbmZpZ0NoYW5nZUV2ZW50KSB7XG4gICAgdGhpcy5fbGFuZ3VhZ2VJZCA9IF9sYW5ndWFnZUlkO1xuICAgIHRoaXMuX3dvcmtlciA9IF93b3JrZXI7XG4gICAgdGhpcy5fZGlzcG9zYWJsZXMgPSBbXTtcbiAgICB0aGlzLl9saXN0ZW5lciA9IC8qIEBfX1BVUkVfXyAqLyBPYmplY3QuY3JlYXRlKG51bGwpO1xuICAgIGNvbnN0IG9uTW9kZWxBZGQgPSAobW9kZWwpID0+IHtcbiAgICAgIGxldCBtb2RlSWQgPSBtb2RlbC5nZXRMYW5ndWFnZUlkKCk7XG4gICAgICBpZiAobW9kZUlkICE9PSB0aGlzLl9sYW5ndWFnZUlkKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGxldCBoYW5kbGU7XG4gICAgICB0aGlzLl9saXN0ZW5lclttb2RlbC51cmkudG9TdHJpbmcoKV0gPSBtb2RlbC5vbkRpZENoYW5nZUNvbnRlbnQoKCkgPT4ge1xuICAgICAgICB3aW5kb3cuY2xlYXJUaW1lb3V0KGhhbmRsZSk7XG4gICAgICAgIGhhbmRsZSA9IHdpbmRvdy5zZXRUaW1lb3V0KCgpID0+IHRoaXMuX2RvVmFsaWRhdGUobW9kZWwudXJpLCBtb2RlSWQpLCA1MDApO1xuICAgICAgfSk7XG4gICAgICB0aGlzLl9kb1ZhbGlkYXRlKG1vZGVsLnVyaSwgbW9kZUlkKTtcbiAgICB9O1xuICAgIGNvbnN0IG9uTW9kZWxSZW1vdmVkID0gKG1vZGVsKSA9PiB7XG4gICAgICBtb25hY29fZWRpdG9yX2NvcmVfZXhwb3J0cy5lZGl0b3Iuc2V0TW9kZWxNYXJrZXJzKG1vZGVsLCB0aGlzLl9sYW5ndWFnZUlkLCBbXSk7XG4gICAgICBsZXQgdXJpU3RyID0gbW9kZWwudXJpLnRvU3RyaW5nKCk7XG4gICAgICBsZXQgbGlzdGVuZXIgPSB0aGlzLl9saXN0ZW5lclt1cmlTdHJdO1xuICAgICAgaWYgKGxpc3RlbmVyKSB7XG4gICAgICAgIGxpc3RlbmVyLmRpc3Bvc2UoKTtcbiAgICAgICAgZGVsZXRlIHRoaXMuX2xpc3RlbmVyW3VyaVN0cl07XG4gICAgICB9XG4gICAgfTtcbiAgICB0aGlzLl9kaXNwb3NhYmxlcy5wdXNoKG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLmVkaXRvci5vbkRpZENyZWF0ZU1vZGVsKG9uTW9kZWxBZGQpKTtcbiAgICB0aGlzLl9kaXNwb3NhYmxlcy5wdXNoKG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLmVkaXRvci5vbldpbGxEaXNwb3NlTW9kZWwob25Nb2RlbFJlbW92ZWQpKTtcbiAgICB0aGlzLl9kaXNwb3NhYmxlcy5wdXNoKFxuICAgICAgbW9uYWNvX2VkaXRvcl9jb3JlX2V4cG9ydHMuZWRpdG9yLm9uRGlkQ2hhbmdlTW9kZWxMYW5ndWFnZSgoZXZlbnQpID0+IHtcbiAgICAgICAgb25Nb2RlbFJlbW92ZWQoZXZlbnQubW9kZWwpO1xuICAgICAgICBvbk1vZGVsQWRkKGV2ZW50Lm1vZGVsKTtcbiAgICAgIH0pXG4gICAgKTtcbiAgICB0aGlzLl9kaXNwb3NhYmxlcy5wdXNoKFxuICAgICAgY29uZmlnQ2hhbmdlRXZlbnQoKF8pID0+IHtcbiAgICAgICAgbW9uYWNvX2VkaXRvcl9jb3JlX2V4cG9ydHMuZWRpdG9yLmdldE1vZGVscygpLmZvckVhY2goKG1vZGVsKSA9PiB7XG4gICAgICAgICAgaWYgKG1vZGVsLmdldExhbmd1YWdlSWQoKSA9PT0gdGhpcy5fbGFuZ3VhZ2VJZCkge1xuICAgICAgICAgICAgb25Nb2RlbFJlbW92ZWQobW9kZWwpO1xuICAgICAgICAgICAgb25Nb2RlbEFkZChtb2RlbCk7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH0pXG4gICAgKTtcbiAgICB0aGlzLl9kaXNwb3NhYmxlcy5wdXNoKHtcbiAgICAgIGRpc3Bvc2U6ICgpID0+IHtcbiAgICAgICAgbW9uYWNvX2VkaXRvcl9jb3JlX2V4cG9ydHMuZWRpdG9yLmdldE1vZGVscygpLmZvckVhY2gob25Nb2RlbFJlbW92ZWQpO1xuICAgICAgICBmb3IgKGxldCBrZXkgaW4gdGhpcy5fbGlzdGVuZXIpIHtcbiAgICAgICAgICB0aGlzLl9saXN0ZW5lcltrZXldLmRpc3Bvc2UoKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pO1xuICAgIG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLmVkaXRvci5nZXRNb2RlbHMoKS5mb3JFYWNoKG9uTW9kZWxBZGQpO1xuICB9XG4gIGRpc3Bvc2UoKSB7XG4gICAgdGhpcy5fZGlzcG9zYWJsZXMuZm9yRWFjaCgoZCkgPT4gZCAmJiBkLmRpc3Bvc2UoKSk7XG4gICAgdGhpcy5fZGlzcG9zYWJsZXMubGVuZ3RoID0gMDtcbiAgfVxuICBfZG9WYWxpZGF0ZShyZXNvdXJjZSwgbGFuZ3VhZ2VJZCkge1xuICAgIHRoaXMuX3dvcmtlcihyZXNvdXJjZSkudGhlbigod29ya2VyMikgPT4ge1xuICAgICAgcmV0dXJuIHdvcmtlcjIuZG9WYWxpZGF0aW9uKHJlc291cmNlLnRvU3RyaW5nKCkpO1xuICAgIH0pLnRoZW4oKGRpYWdub3N0aWNzKSA9PiB7XG4gICAgICBjb25zdCBtYXJrZXJzID0gZGlhZ25vc3RpY3MubWFwKChkKSA9PiB0b0RpYWdub3N0aWNzKHJlc291cmNlLCBkKSk7XG4gICAgICBsZXQgbW9kZWwgPSBtb25hY29fZWRpdG9yX2NvcmVfZXhwb3J0cy5lZGl0b3IuZ2V0TW9kZWwocmVzb3VyY2UpO1xuICAgICAgaWYgKG1vZGVsICYmIG1vZGVsLmdldExhbmd1YWdlSWQoKSA9PT0gbGFuZ3VhZ2VJZCkge1xuICAgICAgICBtb25hY29fZWRpdG9yX2NvcmVfZXhwb3J0cy5lZGl0b3Iuc2V0TW9kZWxNYXJrZXJzKG1vZGVsLCBsYW5ndWFnZUlkLCBtYXJrZXJzKTtcbiAgICAgIH1cbiAgICB9KS50aGVuKHZvaWQgMCwgKGVycikgPT4ge1xuICAgICAgY29uc29sZS5lcnJvcihlcnIpO1xuICAgIH0pO1xuICB9XG59O1xuZnVuY3Rpb24gdG9TZXZlcml0eShsc1NldmVyaXR5KSB7XG4gIHN3aXRjaCAobHNTZXZlcml0eSkge1xuICAgIGNhc2UgRGlhZ25vc3RpY1NldmVyaXR5LkVycm9yOlxuICAgICAgcmV0dXJuIG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLk1hcmtlclNldmVyaXR5LkVycm9yO1xuICAgIGNhc2UgRGlhZ25vc3RpY1NldmVyaXR5Lldhcm5pbmc6XG4gICAgICByZXR1cm4gbW9uYWNvX2VkaXRvcl9jb3JlX2V4cG9ydHMuTWFya2VyU2V2ZXJpdHkuV2FybmluZztcbiAgICBjYXNlIERpYWdub3N0aWNTZXZlcml0eS5JbmZvcm1hdGlvbjpcbiAgICAgIHJldHVybiBtb25hY29fZWRpdG9yX2NvcmVfZXhwb3J0cy5NYXJrZXJTZXZlcml0eS5JbmZvO1xuICAgIGNhc2UgRGlhZ25vc3RpY1NldmVyaXR5LkhpbnQ6XG4gICAgICByZXR1cm4gbW9uYWNvX2VkaXRvcl9jb3JlX2V4cG9ydHMuTWFya2VyU2V2ZXJpdHkuSGludDtcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLk1hcmtlclNldmVyaXR5LkluZm87XG4gIH1cbn1cbmZ1bmN0aW9uIHRvRGlhZ25vc3RpY3MocmVzb3VyY2UsIGRpYWcpIHtcbiAgbGV0IGNvZGUgPSB0eXBlb2YgZGlhZy5jb2RlID09PSBcIm51bWJlclwiID8gU3RyaW5nKGRpYWcuY29kZSkgOiBkaWFnLmNvZGU7XG4gIHJldHVybiB7XG4gICAgc2V2ZXJpdHk6IHRvU2V2ZXJpdHkoZGlhZy5zZXZlcml0eSksXG4gICAgc3RhcnRMaW5lTnVtYmVyOiBkaWFnLnJhbmdlLnN0YXJ0LmxpbmUgKyAxLFxuICAgIHN0YXJ0Q29sdW1uOiBkaWFnLnJhbmdlLnN0YXJ0LmNoYXJhY3RlciArIDEsXG4gICAgZW5kTGluZU51bWJlcjogZGlhZy5yYW5nZS5lbmQubGluZSArIDEsXG4gICAgZW5kQ29sdW1uOiBkaWFnLnJhbmdlLmVuZC5jaGFyYWN0ZXIgKyAxLFxuICAgIG1lc3NhZ2U6IGRpYWcubWVzc2FnZSxcbiAgICBjb2RlLFxuICAgIHNvdXJjZTogZGlhZy5zb3VyY2VcbiAgfTtcbn1cbnZhciBDb21wbGV0aW9uQWRhcHRlciA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoX3dvcmtlciwgX3RyaWdnZXJDaGFyYWN0ZXJzKSB7XG4gICAgdGhpcy5fd29ya2VyID0gX3dvcmtlcjtcbiAgICB0aGlzLl90cmlnZ2VyQ2hhcmFjdGVycyA9IF90cmlnZ2VyQ2hhcmFjdGVycztcbiAgfVxuICBnZXQgdHJpZ2dlckNoYXJhY3RlcnMoKSB7XG4gICAgcmV0dXJuIHRoaXMuX3RyaWdnZXJDaGFyYWN0ZXJzO1xuICB9XG4gIHByb3ZpZGVDb21wbGV0aW9uSXRlbXMobW9kZWwsIHBvc2l0aW9uLCBjb250ZXh0LCB0b2tlbikge1xuICAgIGNvbnN0IHJlc291cmNlID0gbW9kZWwudXJpO1xuICAgIHJldHVybiB0aGlzLl93b3JrZXIocmVzb3VyY2UpLnRoZW4oKHdvcmtlcjIpID0+IHtcbiAgICAgIHJldHVybiB3b3JrZXIyLmRvQ29tcGxldGUocmVzb3VyY2UudG9TdHJpbmcoKSwgZnJvbVBvc2l0aW9uKHBvc2l0aW9uKSk7XG4gICAgfSkudGhlbigoaW5mbykgPT4ge1xuICAgICAgaWYgKCFpbmZvKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGNvbnN0IHdvcmRJbmZvID0gbW9kZWwuZ2V0V29yZFVudGlsUG9zaXRpb24ocG9zaXRpb24pO1xuICAgICAgY29uc3Qgd29yZFJhbmdlID0gbmV3IG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLlJhbmdlKFxuICAgICAgICBwb3NpdGlvbi5saW5lTnVtYmVyLFxuICAgICAgICB3b3JkSW5mby5zdGFydENvbHVtbixcbiAgICAgICAgcG9zaXRpb24ubGluZU51bWJlcixcbiAgICAgICAgd29yZEluZm8uZW5kQ29sdW1uXG4gICAgICApO1xuICAgICAgY29uc3QgaXRlbXMgPSBpbmZvLml0ZW1zLm1hcCgoZW50cnkpID0+IHtcbiAgICAgICAgY29uc3QgaXRlbSA9IHtcbiAgICAgICAgICBsYWJlbDogZW50cnkubGFiZWwsXG4gICAgICAgICAgaW5zZXJ0VGV4dDogZW50cnkuaW5zZXJ0VGV4dCB8fCBlbnRyeS5sYWJlbCxcbiAgICAgICAgICBzb3J0VGV4dDogZW50cnkuc29ydFRleHQsXG4gICAgICAgICAgZmlsdGVyVGV4dDogZW50cnkuZmlsdGVyVGV4dCxcbiAgICAgICAgICBkb2N1bWVudGF0aW9uOiBlbnRyeS5kb2N1bWVudGF0aW9uLFxuICAgICAgICAgIGRldGFpbDogZW50cnkuZGV0YWlsLFxuICAgICAgICAgIGNvbW1hbmQ6IHRvQ29tbWFuZChlbnRyeS5jb21tYW5kKSxcbiAgICAgICAgICByYW5nZTogd29yZFJhbmdlLFxuICAgICAgICAgIGtpbmQ6IHRvQ29tcGxldGlvbkl0ZW1LaW5kKGVudHJ5LmtpbmQpXG4gICAgICAgIH07XG4gICAgICAgIGlmIChlbnRyeS50ZXh0RWRpdCkge1xuICAgICAgICAgIGlmIChpc0luc2VydFJlcGxhY2VFZGl0KGVudHJ5LnRleHRFZGl0KSkge1xuICAgICAgICAgICAgaXRlbS5yYW5nZSA9IHtcbiAgICAgICAgICAgICAgaW5zZXJ0OiB0b1JhbmdlKGVudHJ5LnRleHRFZGl0Lmluc2VydCksXG4gICAgICAgICAgICAgIHJlcGxhY2U6IHRvUmFuZ2UoZW50cnkudGV4dEVkaXQucmVwbGFjZSlcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGl0ZW0ucmFuZ2UgPSB0b1JhbmdlKGVudHJ5LnRleHRFZGl0LnJhbmdlKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgaXRlbS5pbnNlcnRUZXh0ID0gZW50cnkudGV4dEVkaXQubmV3VGV4dDtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZW50cnkuYWRkaXRpb25hbFRleHRFZGl0cykge1xuICAgICAgICAgIGl0ZW0uYWRkaXRpb25hbFRleHRFZGl0cyA9IGVudHJ5LmFkZGl0aW9uYWxUZXh0RWRpdHMubWFwKHRvVGV4dEVkaXQpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChlbnRyeS5pbnNlcnRUZXh0Rm9ybWF0ID09PSBJbnNlcnRUZXh0Rm9ybWF0LlNuaXBwZXQpIHtcbiAgICAgICAgICBpdGVtLmluc2VydFRleHRSdWxlcyA9IG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLmxhbmd1YWdlcy5Db21wbGV0aW9uSXRlbUluc2VydFRleHRSdWxlLkluc2VydEFzU25pcHBldDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gaXRlbTtcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgaXNJbmNvbXBsZXRlOiBpbmZvLmlzSW5jb21wbGV0ZSxcbiAgICAgICAgc3VnZ2VzdGlvbnM6IGl0ZW1zXG4gICAgICB9O1xuICAgIH0pO1xuICB9XG59O1xuZnVuY3Rpb24gZnJvbVBvc2l0aW9uKHBvc2l0aW9uKSB7XG4gIGlmICghcG9zaXRpb24pIHtcbiAgICByZXR1cm4gdm9pZCAwO1xuICB9XG4gIHJldHVybiB7IGNoYXJhY3RlcjogcG9zaXRpb24uY29sdW1uIC0gMSwgbGluZTogcG9zaXRpb24ubGluZU51bWJlciAtIDEgfTtcbn1cbmZ1bmN0aW9uIGZyb21SYW5nZShyYW5nZSkge1xuICBpZiAoIXJhbmdlKSB7XG4gICAgcmV0dXJuIHZvaWQgMDtcbiAgfVxuICByZXR1cm4ge1xuICAgIHN0YXJ0OiB7XG4gICAgICBsaW5lOiByYW5nZS5zdGFydExpbmVOdW1iZXIgLSAxLFxuICAgICAgY2hhcmFjdGVyOiByYW5nZS5zdGFydENvbHVtbiAtIDFcbiAgICB9LFxuICAgIGVuZDogeyBsaW5lOiByYW5nZS5lbmRMaW5lTnVtYmVyIC0gMSwgY2hhcmFjdGVyOiByYW5nZS5lbmRDb2x1bW4gLSAxIH1cbiAgfTtcbn1cbmZ1bmN0aW9uIHRvUmFuZ2UocmFuZ2UpIHtcbiAgaWYgKCFyYW5nZSkge1xuICAgIHJldHVybiB2b2lkIDA7XG4gIH1cbiAgcmV0dXJuIG5ldyBtb25hY29fZWRpdG9yX2NvcmVfZXhwb3J0cy5SYW5nZShcbiAgICByYW5nZS5zdGFydC5saW5lICsgMSxcbiAgICByYW5nZS5zdGFydC5jaGFyYWN0ZXIgKyAxLFxuICAgIHJhbmdlLmVuZC5saW5lICsgMSxcbiAgICByYW5nZS5lbmQuY2hhcmFjdGVyICsgMVxuICApO1xufVxuZnVuY3Rpb24gaXNJbnNlcnRSZXBsYWNlRWRpdChlZGl0KSB7XG4gIHJldHVybiB0eXBlb2YgZWRpdC5pbnNlcnQgIT09IFwidW5kZWZpbmVkXCIgJiYgdHlwZW9mIGVkaXQucmVwbGFjZSAhPT0gXCJ1bmRlZmluZWRcIjtcbn1cbmZ1bmN0aW9uIHRvQ29tcGxldGlvbkl0ZW1LaW5kKGtpbmQpIHtcbiAgY29uc3QgbUl0ZW1LaW5kID0gbW9uYWNvX2VkaXRvcl9jb3JlX2V4cG9ydHMubGFuZ3VhZ2VzLkNvbXBsZXRpb25JdGVtS2luZDtcbiAgc3dpdGNoIChraW5kKSB7XG4gICAgY2FzZSBDb21wbGV0aW9uSXRlbUtpbmQuVGV4dDpcbiAgICAgIHJldHVybiBtSXRlbUtpbmQuVGV4dDtcbiAgICBjYXNlIENvbXBsZXRpb25JdGVtS2luZC5NZXRob2Q6XG4gICAgICByZXR1cm4gbUl0ZW1LaW5kLk1ldGhvZDtcbiAgICBjYXNlIENvbXBsZXRpb25JdGVtS2luZC5GdW5jdGlvbjpcbiAgICAgIHJldHVybiBtSXRlbUtpbmQuRnVuY3Rpb247XG4gICAgY2FzZSBDb21wbGV0aW9uSXRlbUtpbmQuQ29uc3RydWN0b3I6XG4gICAgICByZXR1cm4gbUl0ZW1LaW5kLkNvbnN0cnVjdG9yO1xuICAgIGNhc2UgQ29tcGxldGlvbkl0ZW1LaW5kLkZpZWxkOlxuICAgICAgcmV0dXJuIG1JdGVtS2luZC5GaWVsZDtcbiAgICBjYXNlIENvbXBsZXRpb25JdGVtS2luZC5WYXJpYWJsZTpcbiAgICAgIHJldHVybiBtSXRlbUtpbmQuVmFyaWFibGU7XG4gICAgY2FzZSBDb21wbGV0aW9uSXRlbUtpbmQuQ2xhc3M6XG4gICAgICByZXR1cm4gbUl0ZW1LaW5kLkNsYXNzO1xuICAgIGNhc2UgQ29tcGxldGlvbkl0ZW1LaW5kLkludGVyZmFjZTpcbiAgICAgIHJldHVybiBtSXRlbUtpbmQuSW50ZXJmYWNlO1xuICAgIGNhc2UgQ29tcGxldGlvbkl0ZW1LaW5kLk1vZHVsZTpcbiAgICAgIHJldHVybiBtSXRlbUtpbmQuTW9kdWxlO1xuICAgIGNhc2UgQ29tcGxldGlvbkl0ZW1LaW5kLlByb3BlcnR5OlxuICAgICAgcmV0dXJuIG1JdGVtS2luZC5Qcm9wZXJ0eTtcbiAgICBjYXNlIENvbXBsZXRpb25JdGVtS2luZC5Vbml0OlxuICAgICAgcmV0dXJuIG1JdGVtS2luZC5Vbml0O1xuICAgIGNhc2UgQ29tcGxldGlvbkl0ZW1LaW5kLlZhbHVlOlxuICAgICAgcmV0dXJuIG1JdGVtS2luZC5WYWx1ZTtcbiAgICBjYXNlIENvbXBsZXRpb25JdGVtS2luZC5FbnVtOlxuICAgICAgcmV0dXJuIG1JdGVtS2luZC5FbnVtO1xuICAgIGNhc2UgQ29tcGxldGlvbkl0ZW1LaW5kLktleXdvcmQ6XG4gICAgICByZXR1cm4gbUl0ZW1LaW5kLktleXdvcmQ7XG4gICAgY2FzZSBDb21wbGV0aW9uSXRlbUtpbmQuU25pcHBldDpcbiAgICAgIHJldHVybiBtSXRlbUtpbmQuU25pcHBldDtcbiAgICBjYXNlIENvbXBsZXRpb25JdGVtS2luZC5Db2xvcjpcbiAgICAgIHJldHVybiBtSXRlbUtpbmQuQ29sb3I7XG4gICAgY2FzZSBDb21wbGV0aW9uSXRlbUtpbmQuRmlsZTpcbiAgICAgIHJldHVybiBtSXRlbUtpbmQuRmlsZTtcbiAgICBjYXNlIENvbXBsZXRpb25JdGVtS2luZC5SZWZlcmVuY2U6XG4gICAgICByZXR1cm4gbUl0ZW1LaW5kLlJlZmVyZW5jZTtcbiAgfVxuICByZXR1cm4gbUl0ZW1LaW5kLlByb3BlcnR5O1xufVxuZnVuY3Rpb24gdG9UZXh0RWRpdCh0ZXh0RWRpdCkge1xuICBpZiAoIXRleHRFZGl0KSB7XG4gICAgcmV0dXJuIHZvaWQgMDtcbiAgfVxuICByZXR1cm4ge1xuICAgIHJhbmdlOiB0b1JhbmdlKHRleHRFZGl0LnJhbmdlKSxcbiAgICB0ZXh0OiB0ZXh0RWRpdC5uZXdUZXh0XG4gIH07XG59XG5mdW5jdGlvbiB0b0NvbW1hbmQoYykge1xuICByZXR1cm4gYyAmJiBjLmNvbW1hbmQgPT09IFwiZWRpdG9yLmFjdGlvbi50cmlnZ2VyU3VnZ2VzdFwiID8geyBpZDogYy5jb21tYW5kLCB0aXRsZTogYy50aXRsZSwgYXJndW1lbnRzOiBjLmFyZ3VtZW50cyB9IDogdm9pZCAwO1xufVxudmFyIEhvdmVyQWRhcHRlciA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoX3dvcmtlcikge1xuICAgIHRoaXMuX3dvcmtlciA9IF93b3JrZXI7XG4gIH1cbiAgcHJvdmlkZUhvdmVyKG1vZGVsLCBwb3NpdGlvbiwgdG9rZW4pIHtcbiAgICBsZXQgcmVzb3VyY2UgPSBtb2RlbC51cmk7XG4gICAgcmV0dXJuIHRoaXMuX3dvcmtlcihyZXNvdXJjZSkudGhlbigod29ya2VyMikgPT4ge1xuICAgICAgcmV0dXJuIHdvcmtlcjIuZG9Ib3ZlcihyZXNvdXJjZS50b1N0cmluZygpLCBmcm9tUG9zaXRpb24ocG9zaXRpb24pKTtcbiAgICB9KS50aGVuKChpbmZvKSA9PiB7XG4gICAgICBpZiAoIWluZm8pIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgcmFuZ2U6IHRvUmFuZ2UoaW5mby5yYW5nZSksXG4gICAgICAgIGNvbnRlbnRzOiB0b01hcmtlZFN0cmluZ0FycmF5KGluZm8uY29udGVudHMpXG4gICAgICB9O1xuICAgIH0pO1xuICB9XG59O1xuZnVuY3Rpb24gaXNNYXJrdXBDb250ZW50KHRoaW5nKSB7XG4gIHJldHVybiB0aGluZyAmJiB0eXBlb2YgdGhpbmcgPT09IFwib2JqZWN0XCIgJiYgdHlwZW9mIHRoaW5nLmtpbmQgPT09IFwic3RyaW5nXCI7XG59XG5mdW5jdGlvbiB0b01hcmtkb3duU3RyaW5nKGVudHJ5KSB7XG4gIGlmICh0eXBlb2YgZW50cnkgPT09IFwic3RyaW5nXCIpIHtcbiAgICByZXR1cm4ge1xuICAgICAgdmFsdWU6IGVudHJ5XG4gICAgfTtcbiAgfVxuICBpZiAoaXNNYXJrdXBDb250ZW50KGVudHJ5KSkge1xuICAgIGlmIChlbnRyeS5raW5kID09PSBcInBsYWludGV4dFwiKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICB2YWx1ZTogZW50cnkudmFsdWUucmVwbGFjZSgvW1xcXFxgKl97fVtcXF0oKSMrXFwtLiFdL2csIFwiXFxcXCQmXCIpXG4gICAgICB9O1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgdmFsdWU6IGVudHJ5LnZhbHVlXG4gICAgfTtcbiAgfVxuICByZXR1cm4geyB2YWx1ZTogXCJgYGBcIiArIGVudHJ5Lmxhbmd1YWdlICsgXCJcXG5cIiArIGVudHJ5LnZhbHVlICsgXCJcXG5gYGBcXG5cIiB9O1xufVxuZnVuY3Rpb24gdG9NYXJrZWRTdHJpbmdBcnJheShjb250ZW50cykge1xuICBpZiAoIWNvbnRlbnRzKSB7XG4gICAgcmV0dXJuIHZvaWQgMDtcbiAgfVxuICBpZiAoQXJyYXkuaXNBcnJheShjb250ZW50cykpIHtcbiAgICByZXR1cm4gY29udGVudHMubWFwKHRvTWFya2Rvd25TdHJpbmcpO1xuICB9XG4gIHJldHVybiBbdG9NYXJrZG93blN0cmluZyhjb250ZW50cyldO1xufVxudmFyIERvY3VtZW50SGlnaGxpZ2h0QWRhcHRlciA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoX3dvcmtlcikge1xuICAgIHRoaXMuX3dvcmtlciA9IF93b3JrZXI7XG4gIH1cbiAgcHJvdmlkZURvY3VtZW50SGlnaGxpZ2h0cyhtb2RlbCwgcG9zaXRpb24sIHRva2VuKSB7XG4gICAgY29uc3QgcmVzb3VyY2UgPSBtb2RlbC51cmk7XG4gICAgcmV0dXJuIHRoaXMuX3dvcmtlcihyZXNvdXJjZSkudGhlbigod29ya2VyMikgPT4gd29ya2VyMi5maW5kRG9jdW1lbnRIaWdobGlnaHRzKHJlc291cmNlLnRvU3RyaW5nKCksIGZyb21Qb3NpdGlvbihwb3NpdGlvbikpKS50aGVuKChlbnRyaWVzKSA9PiB7XG4gICAgICBpZiAoIWVudHJpZXMpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgcmV0dXJuIGVudHJpZXMubWFwKChlbnRyeSkgPT4ge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHJhbmdlOiB0b1JhbmdlKGVudHJ5LnJhbmdlKSxcbiAgICAgICAgICBraW5kOiB0b0RvY3VtZW50SGlnaGxpZ2h0S2luZChlbnRyeS5raW5kKVxuICAgICAgICB9O1xuICAgICAgfSk7XG4gICAgfSk7XG4gIH1cbn07XG5mdW5jdGlvbiB0b0RvY3VtZW50SGlnaGxpZ2h0S2luZChraW5kKSB7XG4gIHN3aXRjaCAoa2luZCkge1xuICAgIGNhc2UgRG9jdW1lbnRIaWdobGlnaHRLaW5kLlJlYWQ6XG4gICAgICByZXR1cm4gbW9uYWNvX2VkaXRvcl9jb3JlX2V4cG9ydHMubGFuZ3VhZ2VzLkRvY3VtZW50SGlnaGxpZ2h0S2luZC5SZWFkO1xuICAgIGNhc2UgRG9jdW1lbnRIaWdobGlnaHRLaW5kLldyaXRlOlxuICAgICAgcmV0dXJuIG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLmxhbmd1YWdlcy5Eb2N1bWVudEhpZ2hsaWdodEtpbmQuV3JpdGU7XG4gICAgY2FzZSBEb2N1bWVudEhpZ2hsaWdodEtpbmQuVGV4dDpcbiAgICAgIHJldHVybiBtb25hY29fZWRpdG9yX2NvcmVfZXhwb3J0cy5sYW5ndWFnZXMuRG9jdW1lbnRIaWdobGlnaHRLaW5kLlRleHQ7XG4gIH1cbiAgcmV0dXJuIG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLmxhbmd1YWdlcy5Eb2N1bWVudEhpZ2hsaWdodEtpbmQuVGV4dDtcbn1cbnZhciBEZWZpbml0aW9uQWRhcHRlciA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoX3dvcmtlcikge1xuICAgIHRoaXMuX3dvcmtlciA9IF93b3JrZXI7XG4gIH1cbiAgcHJvdmlkZURlZmluaXRpb24obW9kZWwsIHBvc2l0aW9uLCB0b2tlbikge1xuICAgIGNvbnN0IHJlc291cmNlID0gbW9kZWwudXJpO1xuICAgIHJldHVybiB0aGlzLl93b3JrZXIocmVzb3VyY2UpLnRoZW4oKHdvcmtlcjIpID0+IHtcbiAgICAgIHJldHVybiB3b3JrZXIyLmZpbmREZWZpbml0aW9uKHJlc291cmNlLnRvU3RyaW5nKCksIGZyb21Qb3NpdGlvbihwb3NpdGlvbikpO1xuICAgIH0pLnRoZW4oKGRlZmluaXRpb24pID0+IHtcbiAgICAgIGlmICghZGVmaW5pdGlvbikge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICByZXR1cm4gW3RvTG9jYXRpb24oZGVmaW5pdGlvbildO1xuICAgIH0pO1xuICB9XG59O1xuZnVuY3Rpb24gdG9Mb2NhdGlvbihsb2NhdGlvbikge1xuICByZXR1cm4ge1xuICAgIHVyaTogbW9uYWNvX2VkaXRvcl9jb3JlX2V4cG9ydHMuVXJpLnBhcnNlKGxvY2F0aW9uLnVyaSksXG4gICAgcmFuZ2U6IHRvUmFuZ2UobG9jYXRpb24ucmFuZ2UpXG4gIH07XG59XG52YXIgUmVmZXJlbmNlQWRhcHRlciA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoX3dvcmtlcikge1xuICAgIHRoaXMuX3dvcmtlciA9IF93b3JrZXI7XG4gIH1cbiAgcHJvdmlkZVJlZmVyZW5jZXMobW9kZWwsIHBvc2l0aW9uLCBjb250ZXh0LCB0b2tlbikge1xuICAgIGNvbnN0IHJlc291cmNlID0gbW9kZWwudXJpO1xuICAgIHJldHVybiB0aGlzLl93b3JrZXIocmVzb3VyY2UpLnRoZW4oKHdvcmtlcjIpID0+IHtcbiAgICAgIHJldHVybiB3b3JrZXIyLmZpbmRSZWZlcmVuY2VzKHJlc291cmNlLnRvU3RyaW5nKCksIGZyb21Qb3NpdGlvbihwb3NpdGlvbikpO1xuICAgIH0pLnRoZW4oKGVudHJpZXMpID0+IHtcbiAgICAgIGlmICghZW50cmllcykge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICByZXR1cm4gZW50cmllcy5tYXAodG9Mb2NhdGlvbik7XG4gICAgfSk7XG4gIH1cbn07XG52YXIgUmVuYW1lQWRhcHRlciA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoX3dvcmtlcikge1xuICAgIHRoaXMuX3dvcmtlciA9IF93b3JrZXI7XG4gIH1cbiAgcHJvdmlkZVJlbmFtZUVkaXRzKG1vZGVsLCBwb3NpdGlvbiwgbmV3TmFtZSwgdG9rZW4pIHtcbiAgICBjb25zdCByZXNvdXJjZSA9IG1vZGVsLnVyaTtcbiAgICByZXR1cm4gdGhpcy5fd29ya2VyKHJlc291cmNlKS50aGVuKCh3b3JrZXIyKSA9PiB7XG4gICAgICByZXR1cm4gd29ya2VyMi5kb1JlbmFtZShyZXNvdXJjZS50b1N0cmluZygpLCBmcm9tUG9zaXRpb24ocG9zaXRpb24pLCBuZXdOYW1lKTtcbiAgICB9KS50aGVuKChlZGl0KSA9PiB7XG4gICAgICByZXR1cm4gdG9Xb3Jrc3BhY2VFZGl0KGVkaXQpO1xuICAgIH0pO1xuICB9XG59O1xuZnVuY3Rpb24gdG9Xb3Jrc3BhY2VFZGl0KGVkaXQpIHtcbiAgaWYgKCFlZGl0IHx8ICFlZGl0LmNoYW5nZXMpIHtcbiAgICByZXR1cm4gdm9pZCAwO1xuICB9XG4gIGxldCByZXNvdXJjZUVkaXRzID0gW107XG4gIGZvciAobGV0IHVyaSBpbiBlZGl0LmNoYW5nZXMpIHtcbiAgICBjb25zdCBfdXJpID0gbW9uYWNvX2VkaXRvcl9jb3JlX2V4cG9ydHMuVXJpLnBhcnNlKHVyaSk7XG4gICAgZm9yIChsZXQgZSBvZiBlZGl0LmNoYW5nZXNbdXJpXSkge1xuICAgICAgcmVzb3VyY2VFZGl0cy5wdXNoKHtcbiAgICAgICAgcmVzb3VyY2U6IF91cmksXG4gICAgICAgIHZlcnNpb25JZDogdm9pZCAwLFxuICAgICAgICB0ZXh0RWRpdDoge1xuICAgICAgICAgIHJhbmdlOiB0b1JhbmdlKGUucmFuZ2UpLFxuICAgICAgICAgIHRleHQ6IGUubmV3VGV4dFxuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBlZGl0czogcmVzb3VyY2VFZGl0c1xuICB9O1xufVxudmFyIERvY3VtZW50U3ltYm9sQWRhcHRlciA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoX3dvcmtlcikge1xuICAgIHRoaXMuX3dvcmtlciA9IF93b3JrZXI7XG4gIH1cbiAgcHJvdmlkZURvY3VtZW50U3ltYm9scyhtb2RlbCwgdG9rZW4pIHtcbiAgICBjb25zdCByZXNvdXJjZSA9IG1vZGVsLnVyaTtcbiAgICByZXR1cm4gdGhpcy5fd29ya2VyKHJlc291cmNlKS50aGVuKCh3b3JrZXIyKSA9PiB3b3JrZXIyLmZpbmREb2N1bWVudFN5bWJvbHMocmVzb3VyY2UudG9TdHJpbmcoKSkpLnRoZW4oKGl0ZW1zKSA9PiB7XG4gICAgICBpZiAoIWl0ZW1zKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIHJldHVybiBpdGVtcy5tYXAoKGl0ZW0pID0+IHtcbiAgICAgICAgaWYgKGlzRG9jdW1lbnRTeW1ib2woaXRlbSkpIHtcbiAgICAgICAgICByZXR1cm4gdG9Eb2N1bWVudFN5bWJvbChpdGVtKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIG5hbWU6IGl0ZW0ubmFtZSxcbiAgICAgICAgICBkZXRhaWw6IFwiXCIsXG4gICAgICAgICAgY29udGFpbmVyTmFtZTogaXRlbS5jb250YWluZXJOYW1lLFxuICAgICAgICAgIGtpbmQ6IHRvU3ltYm9sS2luZChpdGVtLmtpbmQpLFxuICAgICAgICAgIHJhbmdlOiB0b1JhbmdlKGl0ZW0ubG9jYXRpb24ucmFuZ2UpLFxuICAgICAgICAgIHNlbGVjdGlvblJhbmdlOiB0b1JhbmdlKGl0ZW0ubG9jYXRpb24ucmFuZ2UpLFxuICAgICAgICAgIHRhZ3M6IFtdXG4gICAgICAgIH07XG4gICAgICB9KTtcbiAgICB9KTtcbiAgfVxufTtcbmZ1bmN0aW9uIGlzRG9jdW1lbnRTeW1ib2woc3ltYm9sKSB7XG4gIHJldHVybiBcImNoaWxkcmVuXCIgaW4gc3ltYm9sO1xufVxuZnVuY3Rpb24gdG9Eb2N1bWVudFN5bWJvbChzeW1ib2wpIHtcbiAgcmV0dXJuIHtcbiAgICBuYW1lOiBzeW1ib2wubmFtZSxcbiAgICBkZXRhaWw6IHN5bWJvbC5kZXRhaWwgPz8gXCJcIixcbiAgICBraW5kOiB0b1N5bWJvbEtpbmQoc3ltYm9sLmtpbmQpLFxuICAgIHJhbmdlOiB0b1JhbmdlKHN5bWJvbC5yYW5nZSksXG4gICAgc2VsZWN0aW9uUmFuZ2U6IHRvUmFuZ2Uoc3ltYm9sLnNlbGVjdGlvblJhbmdlKSxcbiAgICB0YWdzOiBzeW1ib2wudGFncyA/PyBbXSxcbiAgICBjaGlsZHJlbjogKHN5bWJvbC5jaGlsZHJlbiA/PyBbXSkubWFwKChpdGVtKSA9PiB0b0RvY3VtZW50U3ltYm9sKGl0ZW0pKVxuICB9O1xufVxuZnVuY3Rpb24gdG9TeW1ib2xLaW5kKGtpbmQpIHtcbiAgbGV0IG1LaW5kID0gbW9uYWNvX2VkaXRvcl9jb3JlX2V4cG9ydHMubGFuZ3VhZ2VzLlN5bWJvbEtpbmQ7XG4gIHN3aXRjaCAoa2luZCkge1xuICAgIGNhc2UgU3ltYm9sS2luZC5GaWxlOlxuICAgICAgcmV0dXJuIG1LaW5kLkZpbGU7XG4gICAgY2FzZSBTeW1ib2xLaW5kLk1vZHVsZTpcbiAgICAgIHJldHVybiBtS2luZC5Nb2R1bGU7XG4gICAgY2FzZSBTeW1ib2xLaW5kLk5hbWVzcGFjZTpcbiAgICAgIHJldHVybiBtS2luZC5OYW1lc3BhY2U7XG4gICAgY2FzZSBTeW1ib2xLaW5kLlBhY2thZ2U6XG4gICAgICByZXR1cm4gbUtpbmQuUGFja2FnZTtcbiAgICBjYXNlIFN5bWJvbEtpbmQuQ2xhc3M6XG4gICAgICByZXR1cm4gbUtpbmQuQ2xhc3M7XG4gICAgY2FzZSBTeW1ib2xLaW5kLk1ldGhvZDpcbiAgICAgIHJldHVybiBtS2luZC5NZXRob2Q7XG4gICAgY2FzZSBTeW1ib2xLaW5kLlByb3BlcnR5OlxuICAgICAgcmV0dXJuIG1LaW5kLlByb3BlcnR5O1xuICAgIGNhc2UgU3ltYm9sS2luZC5GaWVsZDpcbiAgICAgIHJldHVybiBtS2luZC5GaWVsZDtcbiAgICBjYXNlIFN5bWJvbEtpbmQuQ29uc3RydWN0b3I6XG4gICAgICByZXR1cm4gbUtpbmQuQ29uc3RydWN0b3I7XG4gICAgY2FzZSBTeW1ib2xLaW5kLkVudW06XG4gICAgICByZXR1cm4gbUtpbmQuRW51bTtcbiAgICBjYXNlIFN5bWJvbEtpbmQuSW50ZXJmYWNlOlxuICAgICAgcmV0dXJuIG1LaW5kLkludGVyZmFjZTtcbiAgICBjYXNlIFN5bWJvbEtpbmQuRnVuY3Rpb246XG4gICAgICByZXR1cm4gbUtpbmQuRnVuY3Rpb247XG4gICAgY2FzZSBTeW1ib2xLaW5kLlZhcmlhYmxlOlxuICAgICAgcmV0dXJuIG1LaW5kLlZhcmlhYmxlO1xuICAgIGNhc2UgU3ltYm9sS2luZC5Db25zdGFudDpcbiAgICAgIHJldHVybiBtS2luZC5Db25zdGFudDtcbiAgICBjYXNlIFN5bWJvbEtpbmQuU3RyaW5nOlxuICAgICAgcmV0dXJuIG1LaW5kLlN0cmluZztcbiAgICBjYXNlIFN5bWJvbEtpbmQuTnVtYmVyOlxuICAgICAgcmV0dXJuIG1LaW5kLk51bWJlcjtcbiAgICBjYXNlIFN5bWJvbEtpbmQuQm9vbGVhbjpcbiAgICAgIHJldHVybiBtS2luZC5Cb29sZWFuO1xuICAgIGNhc2UgU3ltYm9sS2luZC5BcnJheTpcbiAgICAgIHJldHVybiBtS2luZC5BcnJheTtcbiAgfVxuICByZXR1cm4gbUtpbmQuRnVuY3Rpb247XG59XG52YXIgRG9jdW1lbnRMaW5rQWRhcHRlciA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoX3dvcmtlcikge1xuICAgIHRoaXMuX3dvcmtlciA9IF93b3JrZXI7XG4gIH1cbiAgcHJvdmlkZUxpbmtzKG1vZGVsLCB0b2tlbikge1xuICAgIGNvbnN0IHJlc291cmNlID0gbW9kZWwudXJpO1xuICAgIHJldHVybiB0aGlzLl93b3JrZXIocmVzb3VyY2UpLnRoZW4oKHdvcmtlcjIpID0+IHdvcmtlcjIuZmluZERvY3VtZW50TGlua3MocmVzb3VyY2UudG9TdHJpbmcoKSkpLnRoZW4oKGl0ZW1zKSA9PiB7XG4gICAgICBpZiAoIWl0ZW1zKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIHJldHVybiB7XG4gICAgICAgIGxpbmtzOiBpdGVtcy5tYXAoKGl0ZW0pID0+ICh7XG4gICAgICAgICAgcmFuZ2U6IHRvUmFuZ2UoaXRlbS5yYW5nZSksXG4gICAgICAgICAgdXJsOiBpdGVtLnRhcmdldFxuICAgICAgICB9KSlcbiAgICAgIH07XG4gICAgfSk7XG4gIH1cbn07XG52YXIgRG9jdW1lbnRGb3JtYXR0aW5nRWRpdFByb3ZpZGVyID0gY2xhc3Mge1xuICBjb25zdHJ1Y3Rvcihfd29ya2VyKSB7XG4gICAgdGhpcy5fd29ya2VyID0gX3dvcmtlcjtcbiAgfVxuICBwcm92aWRlRG9jdW1lbnRGb3JtYXR0aW5nRWRpdHMobW9kZWwsIG9wdGlvbnMsIHRva2VuKSB7XG4gICAgY29uc3QgcmVzb3VyY2UgPSBtb2RlbC51cmk7XG4gICAgcmV0dXJuIHRoaXMuX3dvcmtlcihyZXNvdXJjZSkudGhlbigod29ya2VyMikgPT4ge1xuICAgICAgcmV0dXJuIHdvcmtlcjIuZm9ybWF0KHJlc291cmNlLnRvU3RyaW5nKCksIG51bGwsIGZyb21Gb3JtYXR0aW5nT3B0aW9ucyhvcHRpb25zKSkudGhlbigoZWRpdHMpID0+IHtcbiAgICAgICAgaWYgKCFlZGl0cyB8fCBlZGl0cy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGVkaXRzLm1hcCh0b1RleHRFZGl0KTtcbiAgICAgIH0pO1xuICAgIH0pO1xuICB9XG59O1xudmFyIERvY3VtZW50UmFuZ2VGb3JtYXR0aW5nRWRpdFByb3ZpZGVyID0gY2xhc3Mge1xuICBjb25zdHJ1Y3Rvcihfd29ya2VyKSB7XG4gICAgdGhpcy5fd29ya2VyID0gX3dvcmtlcjtcbiAgICB0aGlzLmNhbkZvcm1hdE11bHRpcGxlUmFuZ2VzID0gZmFsc2U7XG4gIH1cbiAgcHJvdmlkZURvY3VtZW50UmFuZ2VGb3JtYXR0aW5nRWRpdHMobW9kZWwsIHJhbmdlLCBvcHRpb25zLCB0b2tlbikge1xuICAgIGNvbnN0IHJlc291cmNlID0gbW9kZWwudXJpO1xuICAgIHJldHVybiB0aGlzLl93b3JrZXIocmVzb3VyY2UpLnRoZW4oKHdvcmtlcjIpID0+IHtcbiAgICAgIHJldHVybiB3b3JrZXIyLmZvcm1hdChyZXNvdXJjZS50b1N0cmluZygpLCBmcm9tUmFuZ2UocmFuZ2UpLCBmcm9tRm9ybWF0dGluZ09wdGlvbnMob3B0aW9ucykpLnRoZW4oKGVkaXRzKSA9PiB7XG4gICAgICAgIGlmICghZWRpdHMgfHwgZWRpdHMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBlZGl0cy5tYXAodG9UZXh0RWRpdCk7XG4gICAgICB9KTtcbiAgICB9KTtcbiAgfVxufTtcbmZ1bmN0aW9uIGZyb21Gb3JtYXR0aW5nT3B0aW9ucyhvcHRpb25zKSB7XG4gIHJldHVybiB7XG4gICAgdGFiU2l6ZTogb3B0aW9ucy50YWJTaXplLFxuICAgIGluc2VydFNwYWNlczogb3B0aW9ucy5pbnNlcnRTcGFjZXNcbiAgfTtcbn1cbnZhciBEb2N1bWVudENvbG9yQWRhcHRlciA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoX3dvcmtlcikge1xuICAgIHRoaXMuX3dvcmtlciA9IF93b3JrZXI7XG4gIH1cbiAgcHJvdmlkZURvY3VtZW50Q29sb3JzKG1vZGVsLCB0b2tlbikge1xuICAgIGNvbnN0IHJlc291cmNlID0gbW9kZWwudXJpO1xuICAgIHJldHVybiB0aGlzLl93b3JrZXIocmVzb3VyY2UpLnRoZW4oKHdvcmtlcjIpID0+IHdvcmtlcjIuZmluZERvY3VtZW50Q29sb3JzKHJlc291cmNlLnRvU3RyaW5nKCkpKS50aGVuKChpbmZvcykgPT4ge1xuICAgICAgaWYgKCFpbmZvcykge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICByZXR1cm4gaW5mb3MubWFwKChpdGVtKSA9PiAoe1xuICAgICAgICBjb2xvcjogaXRlbS5jb2xvcixcbiAgICAgICAgcmFuZ2U6IHRvUmFuZ2UoaXRlbS5yYW5nZSlcbiAgICAgIH0pKTtcbiAgICB9KTtcbiAgfVxuICBwcm92aWRlQ29sb3JQcmVzZW50YXRpb25zKG1vZGVsLCBpbmZvLCB0b2tlbikge1xuICAgIGNvbnN0IHJlc291cmNlID0gbW9kZWwudXJpO1xuICAgIHJldHVybiB0aGlzLl93b3JrZXIocmVzb3VyY2UpLnRoZW4oXG4gICAgICAod29ya2VyMikgPT4gd29ya2VyMi5nZXRDb2xvclByZXNlbnRhdGlvbnMocmVzb3VyY2UudG9TdHJpbmcoKSwgaW5mby5jb2xvciwgZnJvbVJhbmdlKGluZm8ucmFuZ2UpKVxuICAgICkudGhlbigocHJlc2VudGF0aW9ucykgPT4ge1xuICAgICAgaWYgKCFwcmVzZW50YXRpb25zKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIHJldHVybiBwcmVzZW50YXRpb25zLm1hcCgocHJlc2VudGF0aW9uKSA9PiB7XG4gICAgICAgIGxldCBpdGVtID0ge1xuICAgICAgICAgIGxhYmVsOiBwcmVzZW50YXRpb24ubGFiZWxcbiAgICAgICAgfTtcbiAgICAgICAgaWYgKHByZXNlbnRhdGlvbi50ZXh0RWRpdCkge1xuICAgICAgICAgIGl0ZW0udGV4dEVkaXQgPSB0b1RleHRFZGl0KHByZXNlbnRhdGlvbi50ZXh0RWRpdCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHByZXNlbnRhdGlvbi5hZGRpdGlvbmFsVGV4dEVkaXRzKSB7XG4gICAgICAgICAgaXRlbS5hZGRpdGlvbmFsVGV4dEVkaXRzID0gcHJlc2VudGF0aW9uLmFkZGl0aW9uYWxUZXh0RWRpdHMubWFwKHRvVGV4dEVkaXQpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBpdGVtO1xuICAgICAgfSk7XG4gICAgfSk7XG4gIH1cbn07XG52YXIgRm9sZGluZ1JhbmdlQWRhcHRlciA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoX3dvcmtlcikge1xuICAgIHRoaXMuX3dvcmtlciA9IF93b3JrZXI7XG4gIH1cbiAgcHJvdmlkZUZvbGRpbmdSYW5nZXMobW9kZWwsIGNvbnRleHQsIHRva2VuKSB7XG4gICAgY29uc3QgcmVzb3VyY2UgPSBtb2RlbC51cmk7XG4gICAgcmV0dXJuIHRoaXMuX3dvcmtlcihyZXNvdXJjZSkudGhlbigod29ya2VyMikgPT4gd29ya2VyMi5nZXRGb2xkaW5nUmFuZ2VzKHJlc291cmNlLnRvU3RyaW5nKCksIGNvbnRleHQpKS50aGVuKChyYW5nZXMpID0+IHtcbiAgICAgIGlmICghcmFuZ2VzKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIHJldHVybiByYW5nZXMubWFwKChyYW5nZSkgPT4ge1xuICAgICAgICBjb25zdCByZXN1bHQgPSB7XG4gICAgICAgICAgc3RhcnQ6IHJhbmdlLnN0YXJ0TGluZSArIDEsXG4gICAgICAgICAgZW5kOiByYW5nZS5lbmRMaW5lICsgMVxuICAgICAgICB9O1xuICAgICAgICBpZiAodHlwZW9mIHJhbmdlLmtpbmQgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgICAgICByZXN1bHQua2luZCA9IHRvRm9sZGluZ1JhbmdlS2luZChyYW5nZS5raW5kKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgfSk7XG4gICAgfSk7XG4gIH1cbn07XG5mdW5jdGlvbiB0b0ZvbGRpbmdSYW5nZUtpbmQoa2luZCkge1xuICBzd2l0Y2ggKGtpbmQpIHtcbiAgICBjYXNlIEZvbGRpbmdSYW5nZUtpbmQuQ29tbWVudDpcbiAgICAgIHJldHVybiBtb25hY29fZWRpdG9yX2NvcmVfZXhwb3J0cy5sYW5ndWFnZXMuRm9sZGluZ1JhbmdlS2luZC5Db21tZW50O1xuICAgIGNhc2UgRm9sZGluZ1JhbmdlS2luZC5JbXBvcnRzOlxuICAgICAgcmV0dXJuIG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLmxhbmd1YWdlcy5Gb2xkaW5nUmFuZ2VLaW5kLkltcG9ydHM7XG4gICAgY2FzZSBGb2xkaW5nUmFuZ2VLaW5kLlJlZ2lvbjpcbiAgICAgIHJldHVybiBtb25hY29fZWRpdG9yX2NvcmVfZXhwb3J0cy5sYW5ndWFnZXMuRm9sZGluZ1JhbmdlS2luZC5SZWdpb247XG4gIH1cbiAgcmV0dXJuIHZvaWQgMDtcbn1cbnZhciBTZWxlY3Rpb25SYW5nZUFkYXB0ZXIgPSBjbGFzcyB7XG4gIGNvbnN0cnVjdG9yKF93b3JrZXIpIHtcbiAgICB0aGlzLl93b3JrZXIgPSBfd29ya2VyO1xuICB9XG4gIHByb3ZpZGVTZWxlY3Rpb25SYW5nZXMobW9kZWwsIHBvc2l0aW9ucywgdG9rZW4pIHtcbiAgICBjb25zdCByZXNvdXJjZSA9IG1vZGVsLnVyaTtcbiAgICByZXR1cm4gdGhpcy5fd29ya2VyKHJlc291cmNlKS50aGVuKFxuICAgICAgKHdvcmtlcjIpID0+IHdvcmtlcjIuZ2V0U2VsZWN0aW9uUmFuZ2VzKFxuICAgICAgICByZXNvdXJjZS50b1N0cmluZygpLFxuICAgICAgICBwb3NpdGlvbnMubWFwKGZyb21Qb3NpdGlvbilcbiAgICAgIClcbiAgICApLnRoZW4oKHNlbGVjdGlvblJhbmdlcykgPT4ge1xuICAgICAgaWYgKCFzZWxlY3Rpb25SYW5nZXMpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHNlbGVjdGlvblJhbmdlcy5tYXAoKHNlbGVjdGlvblJhbmdlKSA9PiB7XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IFtdO1xuICAgICAgICB3aGlsZSAoc2VsZWN0aW9uUmFuZ2UpIHtcbiAgICAgICAgICByZXN1bHQucHVzaCh7IHJhbmdlOiB0b1JhbmdlKHNlbGVjdGlvblJhbmdlLnJhbmdlKSB9KTtcbiAgICAgICAgICBzZWxlY3Rpb25SYW5nZSA9IHNlbGVjdGlvblJhbmdlLnBhcmVudDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgfSk7XG4gICAgfSk7XG4gIH1cbn07XG5cbi8vIG5vZGVfbW9kdWxlcy9qc29uYy1wYXJzZXIvbGliL2VzbS9pbXBsL3NjYW5uZXIuanNcbmZ1bmN0aW9uIGNyZWF0ZVNjYW5uZXIodGV4dCwgaWdub3JlVHJpdmlhID0gZmFsc2UpIHtcbiAgY29uc3QgbGVuID0gdGV4dC5sZW5ndGg7XG4gIGxldCBwb3MgPSAwLCB2YWx1ZSA9IFwiXCIsIHRva2VuT2Zmc2V0ID0gMCwgdG9rZW4gPSAxNiwgbGluZU51bWJlciA9IDAsIGxpbmVTdGFydE9mZnNldCA9IDAsIHRva2VuTGluZVN0YXJ0T2Zmc2V0ID0gMCwgcHJldlRva2VuTGluZVN0YXJ0T2Zmc2V0ID0gMCwgc2NhbkVycm9yID0gMDtcbiAgZnVuY3Rpb24gc2NhbkhleERpZ2l0cyhjb3VudCwgZXhhY3QpIHtcbiAgICBsZXQgZGlnaXRzID0gMDtcbiAgICBsZXQgdmFsdWUyID0gMDtcbiAgICB3aGlsZSAoZGlnaXRzIDwgY291bnQgfHwgIWV4YWN0KSB7XG4gICAgICBsZXQgY2ggPSB0ZXh0LmNoYXJDb2RlQXQocG9zKTtcbiAgICAgIGlmIChjaCA+PSA0OCAmJiBjaCA8PSA1Nykge1xuICAgICAgICB2YWx1ZTIgPSB2YWx1ZTIgKiAxNiArIGNoIC0gNDg7XG4gICAgICB9IGVsc2UgaWYgKGNoID49IDY1ICYmIGNoIDw9IDcwKSB7XG4gICAgICAgIHZhbHVlMiA9IHZhbHVlMiAqIDE2ICsgY2ggLSA2NSArIDEwO1xuICAgICAgfSBlbHNlIGlmIChjaCA+PSA5NyAmJiBjaCA8PSAxMDIpIHtcbiAgICAgICAgdmFsdWUyID0gdmFsdWUyICogMTYgKyBjaCAtIDk3ICsgMTA7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIHBvcysrO1xuICAgICAgZGlnaXRzKys7XG4gICAgfVxuICAgIGlmIChkaWdpdHMgPCBjb3VudCkge1xuICAgICAgdmFsdWUyID0gLTE7XG4gICAgfVxuICAgIHJldHVybiB2YWx1ZTI7XG4gIH1cbiAgZnVuY3Rpb24gc2V0UG9zaXRpb24obmV3UG9zaXRpb24pIHtcbiAgICBwb3MgPSBuZXdQb3NpdGlvbjtcbiAgICB2YWx1ZSA9IFwiXCI7XG4gICAgdG9rZW5PZmZzZXQgPSAwO1xuICAgIHRva2VuID0gMTY7XG4gICAgc2NhbkVycm9yID0gMDtcbiAgfVxuICBmdW5jdGlvbiBzY2FuTnVtYmVyKCkge1xuICAgIGxldCBzdGFydCA9IHBvcztcbiAgICBpZiAodGV4dC5jaGFyQ29kZUF0KHBvcykgPT09IDQ4KSB7XG4gICAgICBwb3MrKztcbiAgICB9IGVsc2Uge1xuICAgICAgcG9zKys7XG4gICAgICB3aGlsZSAocG9zIDwgdGV4dC5sZW5ndGggJiYgaXNEaWdpdCh0ZXh0LmNoYXJDb2RlQXQocG9zKSkpIHtcbiAgICAgICAgcG9zKys7XG4gICAgICB9XG4gICAgfVxuICAgIGlmIChwb3MgPCB0ZXh0Lmxlbmd0aCAmJiB0ZXh0LmNoYXJDb2RlQXQocG9zKSA9PT0gNDYpIHtcbiAgICAgIHBvcysrO1xuICAgICAgaWYgKHBvcyA8IHRleHQubGVuZ3RoICYmIGlzRGlnaXQodGV4dC5jaGFyQ29kZUF0KHBvcykpKSB7XG4gICAgICAgIHBvcysrO1xuICAgICAgICB3aGlsZSAocG9zIDwgdGV4dC5sZW5ndGggJiYgaXNEaWdpdCh0ZXh0LmNoYXJDb2RlQXQocG9zKSkpIHtcbiAgICAgICAgICBwb3MrKztcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2NhbkVycm9yID0gMztcbiAgICAgICAgcmV0dXJuIHRleHQuc3Vic3RyaW5nKHN0YXJ0LCBwb3MpO1xuICAgICAgfVxuICAgIH1cbiAgICBsZXQgZW5kID0gcG9zO1xuICAgIGlmIChwb3MgPCB0ZXh0Lmxlbmd0aCAmJiAodGV4dC5jaGFyQ29kZUF0KHBvcykgPT09IDY5IHx8IHRleHQuY2hhckNvZGVBdChwb3MpID09PSAxMDEpKSB7XG4gICAgICBwb3MrKztcbiAgICAgIGlmIChwb3MgPCB0ZXh0Lmxlbmd0aCAmJiB0ZXh0LmNoYXJDb2RlQXQocG9zKSA9PT0gNDMgfHwgdGV4dC5jaGFyQ29kZUF0KHBvcykgPT09IDQ1KSB7XG4gICAgICAgIHBvcysrO1xuICAgICAgfVxuICAgICAgaWYgKHBvcyA8IHRleHQubGVuZ3RoICYmIGlzRGlnaXQodGV4dC5jaGFyQ29kZUF0KHBvcykpKSB7XG4gICAgICAgIHBvcysrO1xuICAgICAgICB3aGlsZSAocG9zIDwgdGV4dC5sZW5ndGggJiYgaXNEaWdpdCh0ZXh0LmNoYXJDb2RlQXQocG9zKSkpIHtcbiAgICAgICAgICBwb3MrKztcbiAgICAgICAgfVxuICAgICAgICBlbmQgPSBwb3M7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzY2FuRXJyb3IgPSAzO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdGV4dC5zdWJzdHJpbmcoc3RhcnQsIGVuZCk7XG4gIH1cbiAgZnVuY3Rpb24gc2NhblN0cmluZygpIHtcbiAgICBsZXQgcmVzdWx0ID0gXCJcIiwgc3RhcnQgPSBwb3M7XG4gICAgd2hpbGUgKHRydWUpIHtcbiAgICAgIGlmIChwb3MgPj0gbGVuKSB7XG4gICAgICAgIHJlc3VsdCArPSB0ZXh0LnN1YnN0cmluZyhzdGFydCwgcG9zKTtcbiAgICAgICAgc2NhbkVycm9yID0gMjtcbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgICBjb25zdCBjaCA9IHRleHQuY2hhckNvZGVBdChwb3MpO1xuICAgICAgaWYgKGNoID09PSAzNCkge1xuICAgICAgICByZXN1bHQgKz0gdGV4dC5zdWJzdHJpbmcoc3RhcnQsIHBvcyk7XG4gICAgICAgIHBvcysrO1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIGlmIChjaCA9PT0gOTIpIHtcbiAgICAgICAgcmVzdWx0ICs9IHRleHQuc3Vic3RyaW5nKHN0YXJ0LCBwb3MpO1xuICAgICAgICBwb3MrKztcbiAgICAgICAgaWYgKHBvcyA+PSBsZW4pIHtcbiAgICAgICAgICBzY2FuRXJyb3IgPSAyO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGNoMiA9IHRleHQuY2hhckNvZGVBdChwb3MrKyk7XG4gICAgICAgIHN3aXRjaCAoY2gyKSB7XG4gICAgICAgICAgY2FzZSAzNDpcbiAgICAgICAgICAgIHJlc3VsdCArPSAnXCInO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSA5MjpcbiAgICAgICAgICAgIHJlc3VsdCArPSBcIlxcXFxcIjtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgNDc6XG4gICAgICAgICAgICByZXN1bHQgKz0gXCIvXCI7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlIDk4OlxuICAgICAgICAgICAgcmVzdWx0ICs9IFwiXFxiXCI7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlIDEwMjpcbiAgICAgICAgICAgIHJlc3VsdCArPSBcIlxcZlwiO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSAxMTA6XG4gICAgICAgICAgICByZXN1bHQgKz0gXCJcXG5cIjtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgMTE0OlxuICAgICAgICAgICAgcmVzdWx0ICs9IFwiXFxyXCI7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlIDExNjpcbiAgICAgICAgICAgIHJlc3VsdCArPSBcIlx0XCI7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlIDExNzpcbiAgICAgICAgICAgIGNvbnN0IGNoMyA9IHNjYW5IZXhEaWdpdHMoNCwgdHJ1ZSk7XG4gICAgICAgICAgICBpZiAoY2gzID49IDApIHtcbiAgICAgICAgICAgICAgcmVzdWx0ICs9IFN0cmluZy5mcm9tQ2hhckNvZGUoY2gzKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgIHNjYW5FcnJvciA9IDQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgc2NhbkVycm9yID0gNTtcbiAgICAgICAgfVxuICAgICAgICBzdGFydCA9IHBvcztcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG4gICAgICBpZiAoY2ggPj0gMCAmJiBjaCA8PSAzMSkge1xuICAgICAgICBpZiAoaXNMaW5lQnJlYWsoY2gpKSB7XG4gICAgICAgICAgcmVzdWx0ICs9IHRleHQuc3Vic3RyaW5nKHN0YXJ0LCBwb3MpO1xuICAgICAgICAgIHNjYW5FcnJvciA9IDI7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgc2NhbkVycm9yID0gNjtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcG9zKys7XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbiAgZnVuY3Rpb24gc2Nhbk5leHQoKSB7XG4gICAgdmFsdWUgPSBcIlwiO1xuICAgIHNjYW5FcnJvciA9IDA7XG4gICAgdG9rZW5PZmZzZXQgPSBwb3M7XG4gICAgbGluZVN0YXJ0T2Zmc2V0ID0gbGluZU51bWJlcjtcbiAgICBwcmV2VG9rZW5MaW5lU3RhcnRPZmZzZXQgPSB0b2tlbkxpbmVTdGFydE9mZnNldDtcbiAgICBpZiAocG9zID49IGxlbikge1xuICAgICAgdG9rZW5PZmZzZXQgPSBsZW47XG4gICAgICByZXR1cm4gdG9rZW4gPSAxNztcbiAgICB9XG4gICAgbGV0IGNvZGUgPSB0ZXh0LmNoYXJDb2RlQXQocG9zKTtcbiAgICBpZiAoaXNXaGl0ZVNwYWNlKGNvZGUpKSB7XG4gICAgICBkbyB7XG4gICAgICAgIHBvcysrO1xuICAgICAgICB2YWx1ZSArPSBTdHJpbmcuZnJvbUNoYXJDb2RlKGNvZGUpO1xuICAgICAgICBjb2RlID0gdGV4dC5jaGFyQ29kZUF0KHBvcyk7XG4gICAgICB9IHdoaWxlIChpc1doaXRlU3BhY2UoY29kZSkpO1xuICAgICAgcmV0dXJuIHRva2VuID0gMTU7XG4gICAgfVxuICAgIGlmIChpc0xpbmVCcmVhayhjb2RlKSkge1xuICAgICAgcG9zKys7XG4gICAgICB2YWx1ZSArPSBTdHJpbmcuZnJvbUNoYXJDb2RlKGNvZGUpO1xuICAgICAgaWYgKGNvZGUgPT09IDEzICYmIHRleHQuY2hhckNvZGVBdChwb3MpID09PSAxMCkge1xuICAgICAgICBwb3MrKztcbiAgICAgICAgdmFsdWUgKz0gXCJcXG5cIjtcbiAgICAgIH1cbiAgICAgIGxpbmVOdW1iZXIrKztcbiAgICAgIHRva2VuTGluZVN0YXJ0T2Zmc2V0ID0gcG9zO1xuICAgICAgcmV0dXJuIHRva2VuID0gMTQ7XG4gICAgfVxuICAgIHN3aXRjaCAoY29kZSkge1xuICAgICAgY2FzZSAxMjM6XG4gICAgICAgIHBvcysrO1xuICAgICAgICByZXR1cm4gdG9rZW4gPSAxO1xuICAgICAgY2FzZSAxMjU6XG4gICAgICAgIHBvcysrO1xuICAgICAgICByZXR1cm4gdG9rZW4gPSAyO1xuICAgICAgY2FzZSA5MTpcbiAgICAgICAgcG9zKys7XG4gICAgICAgIHJldHVybiB0b2tlbiA9IDM7XG4gICAgICBjYXNlIDkzOlxuICAgICAgICBwb3MrKztcbiAgICAgICAgcmV0dXJuIHRva2VuID0gNDtcbiAgICAgIGNhc2UgNTg6XG4gICAgICAgIHBvcysrO1xuICAgICAgICByZXR1cm4gdG9rZW4gPSA2O1xuICAgICAgY2FzZSA0NDpcbiAgICAgICAgcG9zKys7XG4gICAgICAgIHJldHVybiB0b2tlbiA9IDU7XG4gICAgICBjYXNlIDM0OlxuICAgICAgICBwb3MrKztcbiAgICAgICAgdmFsdWUgPSBzY2FuU3RyaW5nKCk7XG4gICAgICAgIHJldHVybiB0b2tlbiA9IDEwO1xuICAgICAgY2FzZSA0NzpcbiAgICAgICAgY29uc3Qgc3RhcnQgPSBwb3MgLSAxO1xuICAgICAgICBpZiAodGV4dC5jaGFyQ29kZUF0KHBvcyArIDEpID09PSA0Nykge1xuICAgICAgICAgIHBvcyArPSAyO1xuICAgICAgICAgIHdoaWxlIChwb3MgPCBsZW4pIHtcbiAgICAgICAgICAgIGlmIChpc0xpbmVCcmVhayh0ZXh0LmNoYXJDb2RlQXQocG9zKSkpIHtcbiAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBwb3MrKztcbiAgICAgICAgICB9XG4gICAgICAgICAgdmFsdWUgPSB0ZXh0LnN1YnN0cmluZyhzdGFydCwgcG9zKTtcbiAgICAgICAgICByZXR1cm4gdG9rZW4gPSAxMjtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGV4dC5jaGFyQ29kZUF0KHBvcyArIDEpID09PSA0Mikge1xuICAgICAgICAgIHBvcyArPSAyO1xuICAgICAgICAgIGNvbnN0IHNhZmVMZW5ndGggPSBsZW4gLSAxO1xuICAgICAgICAgIGxldCBjb21tZW50Q2xvc2VkID0gZmFsc2U7XG4gICAgICAgICAgd2hpbGUgKHBvcyA8IHNhZmVMZW5ndGgpIHtcbiAgICAgICAgICAgIGNvbnN0IGNoID0gdGV4dC5jaGFyQ29kZUF0KHBvcyk7XG4gICAgICAgICAgICBpZiAoY2ggPT09IDQyICYmIHRleHQuY2hhckNvZGVBdChwb3MgKyAxKSA9PT0gNDcpIHtcbiAgICAgICAgICAgICAgcG9zICs9IDI7XG4gICAgICAgICAgICAgIGNvbW1lbnRDbG9zZWQgPSB0cnVlO1xuICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHBvcysrO1xuICAgICAgICAgICAgaWYgKGlzTGluZUJyZWFrKGNoKSkge1xuICAgICAgICAgICAgICBpZiAoY2ggPT09IDEzICYmIHRleHQuY2hhckNvZGVBdChwb3MpID09PSAxMCkge1xuICAgICAgICAgICAgICAgIHBvcysrO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIGxpbmVOdW1iZXIrKztcbiAgICAgICAgICAgICAgdG9rZW5MaW5lU3RhcnRPZmZzZXQgPSBwb3M7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICAgIGlmICghY29tbWVudENsb3NlZCkge1xuICAgICAgICAgICAgcG9zKys7XG4gICAgICAgICAgICBzY2FuRXJyb3IgPSAxO1xuICAgICAgICAgIH1cbiAgICAgICAgICB2YWx1ZSA9IHRleHQuc3Vic3RyaW5nKHN0YXJ0LCBwb3MpO1xuICAgICAgICAgIHJldHVybiB0b2tlbiA9IDEzO1xuICAgICAgICB9XG4gICAgICAgIHZhbHVlICs9IFN0cmluZy5mcm9tQ2hhckNvZGUoY29kZSk7XG4gICAgICAgIHBvcysrO1xuICAgICAgICByZXR1cm4gdG9rZW4gPSAxNjtcbiAgICAgIGNhc2UgNDU6XG4gICAgICAgIHZhbHVlICs9IFN0cmluZy5mcm9tQ2hhckNvZGUoY29kZSk7XG4gICAgICAgIHBvcysrO1xuICAgICAgICBpZiAocG9zID09PSBsZW4gfHwgIWlzRGlnaXQodGV4dC5jaGFyQ29kZUF0KHBvcykpKSB7XG4gICAgICAgICAgcmV0dXJuIHRva2VuID0gMTY7XG4gICAgICAgIH1cbiAgICAgIGNhc2UgNDg6XG4gICAgICBjYXNlIDQ5OlxuICAgICAgY2FzZSA1MDpcbiAgICAgIGNhc2UgNTE6XG4gICAgICBjYXNlIDUyOlxuICAgICAgY2FzZSA1MzpcbiAgICAgIGNhc2UgNTQ6XG4gICAgICBjYXNlIDU1OlxuICAgICAgY2FzZSA1NjpcbiAgICAgIGNhc2UgNTc6XG4gICAgICAgIHZhbHVlICs9IHNjYW5OdW1iZXIoKTtcbiAgICAgICAgcmV0dXJuIHRva2VuID0gMTE7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICB3aGlsZSAocG9zIDwgbGVuICYmIGlzVW5rbm93bkNvbnRlbnRDaGFyYWN0ZXIoY29kZSkpIHtcbiAgICAgICAgICBwb3MrKztcbiAgICAgICAgICBjb2RlID0gdGV4dC5jaGFyQ29kZUF0KHBvcyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRva2VuT2Zmc2V0ICE9PSBwb3MpIHtcbiAgICAgICAgICB2YWx1ZSA9IHRleHQuc3Vic3RyaW5nKHRva2VuT2Zmc2V0LCBwb3MpO1xuICAgICAgICAgIHN3aXRjaCAodmFsdWUpIHtcbiAgICAgICAgICAgIGNhc2UgXCJ0cnVlXCI6XG4gICAgICAgICAgICAgIHJldHVybiB0b2tlbiA9IDg7XG4gICAgICAgICAgICBjYXNlIFwiZmFsc2VcIjpcbiAgICAgICAgICAgICAgcmV0dXJuIHRva2VuID0gOTtcbiAgICAgICAgICAgIGNhc2UgXCJudWxsXCI6XG4gICAgICAgICAgICAgIHJldHVybiB0b2tlbiA9IDc7XG4gICAgICAgICAgfVxuICAgICAgICAgIHJldHVybiB0b2tlbiA9IDE2O1xuICAgICAgICB9XG4gICAgICAgIHZhbHVlICs9IFN0cmluZy5mcm9tQ2hhckNvZGUoY29kZSk7XG4gICAgICAgIHBvcysrO1xuICAgICAgICByZXR1cm4gdG9rZW4gPSAxNjtcbiAgICB9XG4gIH1cbiAgZnVuY3Rpb24gaXNVbmtub3duQ29udGVudENoYXJhY3Rlcihjb2RlKSB7XG4gICAgaWYgKGlzV2hpdGVTcGFjZShjb2RlKSB8fCBpc0xpbmVCcmVhayhjb2RlKSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBzd2l0Y2ggKGNvZGUpIHtcbiAgICAgIGNhc2UgMTI1OlxuICAgICAgY2FzZSA5MzpcbiAgICAgIGNhc2UgMTIzOlxuICAgICAgY2FzZSA5MTpcbiAgICAgIGNhc2UgMzQ6XG4gICAgICBjYXNlIDU4OlxuICAgICAgY2FzZSA0NDpcbiAgICAgIGNhc2UgNDc6XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cbiAgZnVuY3Rpb24gc2Nhbk5leHROb25Ucml2aWEoKSB7XG4gICAgbGV0IHJlc3VsdDtcbiAgICBkbyB7XG4gICAgICByZXN1bHQgPSBzY2FuTmV4dCgpO1xuICAgIH0gd2hpbGUgKHJlc3VsdCA+PSAxMiAmJiByZXN1bHQgPD0gMTUpO1xuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBzZXRQb3NpdGlvbixcbiAgICBnZXRQb3NpdGlvbjogKCkgPT4gcG9zLFxuICAgIHNjYW46IGlnbm9yZVRyaXZpYSA/IHNjYW5OZXh0Tm9uVHJpdmlhIDogc2Nhbk5leHQsXG4gICAgZ2V0VG9rZW46ICgpID0+IHRva2VuLFxuICAgIGdldFRva2VuVmFsdWU6ICgpID0+IHZhbHVlLFxuICAgIGdldFRva2VuT2Zmc2V0OiAoKSA9PiB0b2tlbk9mZnNldCxcbiAgICBnZXRUb2tlbkxlbmd0aDogKCkgPT4gcG9zIC0gdG9rZW5PZmZzZXQsXG4gICAgZ2V0VG9rZW5TdGFydExpbmU6ICgpID0+IGxpbmVTdGFydE9mZnNldCxcbiAgICBnZXRUb2tlblN0YXJ0Q2hhcmFjdGVyOiAoKSA9PiB0b2tlbk9mZnNldCAtIHByZXZUb2tlbkxpbmVTdGFydE9mZnNldCxcbiAgICBnZXRUb2tlbkVycm9yOiAoKSA9PiBzY2FuRXJyb3JcbiAgfTtcbn1cbmZ1bmN0aW9uIGlzV2hpdGVTcGFjZShjaCkge1xuICByZXR1cm4gY2ggPT09IDMyIHx8IGNoID09PSA5O1xufVxuZnVuY3Rpb24gaXNMaW5lQnJlYWsoY2gpIHtcbiAgcmV0dXJuIGNoID09PSAxMCB8fCBjaCA9PT0gMTM7XG59XG5mdW5jdGlvbiBpc0RpZ2l0KGNoKSB7XG4gIHJldHVybiBjaCA+PSA0OCAmJiBjaCA8PSA1Nztcbn1cbnZhciBDaGFyYWN0ZXJDb2RlcztcbihmdW5jdGlvbihDaGFyYWN0ZXJDb2RlczIpIHtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcImxpbmVGZWVkXCJdID0gMTBdID0gXCJsaW5lRmVlZFwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wiY2FycmlhZ2VSZXR1cm5cIl0gPSAxM10gPSBcImNhcnJpYWdlUmV0dXJuXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJzcGFjZVwiXSA9IDMyXSA9IFwic3BhY2VcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcIl8wXCJdID0gNDhdID0gXCJfMFwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wiXzFcIl0gPSA0OV0gPSBcIl8xXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJfMlwiXSA9IDUwXSA9IFwiXzJcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcIl8zXCJdID0gNTFdID0gXCJfM1wiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wiXzRcIl0gPSA1Ml0gPSBcIl80XCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJfNVwiXSA9IDUzXSA9IFwiXzVcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcIl82XCJdID0gNTRdID0gXCJfNlwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wiXzdcIl0gPSA1NV0gPSBcIl83XCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJfOFwiXSA9IDU2XSA9IFwiXzhcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcIl85XCJdID0gNTddID0gXCJfOVwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wiYVwiXSA9IDk3XSA9IFwiYVwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wiYlwiXSA9IDk4XSA9IFwiYlwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wiY1wiXSA9IDk5XSA9IFwiY1wiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wiZFwiXSA9IDEwMF0gPSBcImRcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcImVcIl0gPSAxMDFdID0gXCJlXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJmXCJdID0gMTAyXSA9IFwiZlwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wiZ1wiXSA9IDEwM10gPSBcImdcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcImhcIl0gPSAxMDRdID0gXCJoXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJpXCJdID0gMTA1XSA9IFwiaVwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wialwiXSA9IDEwNl0gPSBcImpcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcImtcIl0gPSAxMDddID0gXCJrXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJsXCJdID0gMTA4XSA9IFwibFwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wibVwiXSA9IDEwOV0gPSBcIm1cIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcIm5cIl0gPSAxMTBdID0gXCJuXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJvXCJdID0gMTExXSA9IFwib1wiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wicFwiXSA9IDExMl0gPSBcInBcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcInFcIl0gPSAxMTNdID0gXCJxXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJyXCJdID0gMTE0XSA9IFwiclwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wic1wiXSA9IDExNV0gPSBcInNcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcInRcIl0gPSAxMTZdID0gXCJ0XCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJ1XCJdID0gMTE3XSA9IFwidVwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1widlwiXSA9IDExOF0gPSBcInZcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcIndcIl0gPSAxMTldID0gXCJ3XCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJ4XCJdID0gMTIwXSA9IFwieFwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wieVwiXSA9IDEyMV0gPSBcInlcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcInpcIl0gPSAxMjJdID0gXCJ6XCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJBXCJdID0gNjVdID0gXCJBXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJCXCJdID0gNjZdID0gXCJCXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJDXCJdID0gNjddID0gXCJDXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJEXCJdID0gNjhdID0gXCJEXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJFXCJdID0gNjldID0gXCJFXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJGXCJdID0gNzBdID0gXCJGXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJHXCJdID0gNzFdID0gXCJHXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJIXCJdID0gNzJdID0gXCJIXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJJXCJdID0gNzNdID0gXCJJXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJKXCJdID0gNzRdID0gXCJKXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJLXCJdID0gNzVdID0gXCJLXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJMXCJdID0gNzZdID0gXCJMXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJNXCJdID0gNzddID0gXCJNXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJOXCJdID0gNzhdID0gXCJOXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJPXCJdID0gNzldID0gXCJPXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJQXCJdID0gODBdID0gXCJQXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJRXCJdID0gODFdID0gXCJRXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJSXCJdID0gODJdID0gXCJSXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJTXCJdID0gODNdID0gXCJTXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJUXCJdID0gODRdID0gXCJUXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJVXCJdID0gODVdID0gXCJVXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJWXCJdID0gODZdID0gXCJWXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJXXCJdID0gODddID0gXCJXXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJYXCJdID0gODhdID0gXCJYXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJZXCJdID0gODldID0gXCJZXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJaXCJdID0gOTBdID0gXCJaXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJhc3Rlcmlza1wiXSA9IDQyXSA9IFwiYXN0ZXJpc2tcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcImJhY2tzbGFzaFwiXSA9IDkyXSA9IFwiYmFja3NsYXNoXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJjbG9zZUJyYWNlXCJdID0gMTI1XSA9IFwiY2xvc2VCcmFjZVwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wiY2xvc2VCcmFja2V0XCJdID0gOTNdID0gXCJjbG9zZUJyYWNrZXRcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcImNvbG9uXCJdID0gNThdID0gXCJjb2xvblwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wiY29tbWFcIl0gPSA0NF0gPSBcImNvbW1hXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJkb3RcIl0gPSA0Nl0gPSBcImRvdFwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wiZG91YmxlUXVvdGVcIl0gPSAzNF0gPSBcImRvdWJsZVF1b3RlXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJtaW51c1wiXSA9IDQ1XSA9IFwibWludXNcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcIm9wZW5CcmFjZVwiXSA9IDEyM10gPSBcIm9wZW5CcmFjZVwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1wib3BlbkJyYWNrZXRcIl0gPSA5MV0gPSBcIm9wZW5CcmFja2V0XCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJwbHVzXCJdID0gNDNdID0gXCJwbHVzXCI7XG4gIENoYXJhY3RlckNvZGVzMltDaGFyYWN0ZXJDb2RlczJbXCJzbGFzaFwiXSA9IDQ3XSA9IFwic2xhc2hcIjtcbiAgQ2hhcmFjdGVyQ29kZXMyW0NoYXJhY3RlckNvZGVzMltcImZvcm1GZWVkXCJdID0gMTJdID0gXCJmb3JtRmVlZFwiO1xuICBDaGFyYWN0ZXJDb2RlczJbQ2hhcmFjdGVyQ29kZXMyW1widGFiXCJdID0gOV0gPSBcInRhYlwiO1xufSkoQ2hhcmFjdGVyQ29kZXMgfHwgKENoYXJhY3RlckNvZGVzID0ge30pKTtcblxuLy8gbm9kZV9tb2R1bGVzL2pzb25jLXBhcnNlci9saWIvZXNtL2ltcGwvc3RyaW5nLWludGVybi5qc1xudmFyIGNhY2hlZFNwYWNlcyA9IG5ldyBBcnJheSgyMCkuZmlsbCgwKS5tYXAoKF8sIGluZGV4KSA9PiB7XG4gIHJldHVybiBcIiBcIi5yZXBlYXQoaW5kZXgpO1xufSk7XG52YXIgbWF4Q2FjaGVkVmFsdWVzID0gMjAwO1xudmFyIGNhY2hlZEJyZWFrTGluZXNXaXRoU3BhY2VzID0ge1xuICBcIiBcIjoge1xuICAgIFwiXFxuXCI6IG5ldyBBcnJheShtYXhDYWNoZWRWYWx1ZXMpLmZpbGwoMCkubWFwKChfLCBpbmRleCkgPT4ge1xuICAgICAgcmV0dXJuIFwiXFxuXCIgKyBcIiBcIi5yZXBlYXQoaW5kZXgpO1xuICAgIH0pLFxuICAgIFwiXFxyXCI6IG5ldyBBcnJheShtYXhDYWNoZWRWYWx1ZXMpLmZpbGwoMCkubWFwKChfLCBpbmRleCkgPT4ge1xuICAgICAgcmV0dXJuIFwiXFxyXCIgKyBcIiBcIi5yZXBlYXQoaW5kZXgpO1xuICAgIH0pLFxuICAgIFwiXFxyXFxuXCI6IG5ldyBBcnJheShtYXhDYWNoZWRWYWx1ZXMpLmZpbGwoMCkubWFwKChfLCBpbmRleCkgPT4ge1xuICAgICAgcmV0dXJuIFwiXFxyXFxuXCIgKyBcIiBcIi5yZXBlYXQoaW5kZXgpO1xuICAgIH0pXG4gIH0sXG4gIFwiXHRcIjoge1xuICAgIFwiXFxuXCI6IG5ldyBBcnJheShtYXhDYWNoZWRWYWx1ZXMpLmZpbGwoMCkubWFwKChfLCBpbmRleCkgPT4ge1xuICAgICAgcmV0dXJuIFwiXFxuXCIgKyBcIlx0XCIucmVwZWF0KGluZGV4KTtcbiAgICB9KSxcbiAgICBcIlxcclwiOiBuZXcgQXJyYXkobWF4Q2FjaGVkVmFsdWVzKS5maWxsKDApLm1hcCgoXywgaW5kZXgpID0+IHtcbiAgICAgIHJldHVybiBcIlxcclwiICsgXCJcdFwiLnJlcGVhdChpbmRleCk7XG4gICAgfSksXG4gICAgXCJcXHJcXG5cIjogbmV3IEFycmF5KG1heENhY2hlZFZhbHVlcykuZmlsbCgwKS5tYXAoKF8sIGluZGV4KSA9PiB7XG4gICAgICByZXR1cm4gXCJcXHJcXG5cIiArIFwiXHRcIi5yZXBlYXQoaW5kZXgpO1xuICAgIH0pXG4gIH1cbn07XG5cbi8vIG5vZGVfbW9kdWxlcy9qc29uYy1wYXJzZXIvbGliL2VzbS9pbXBsL3BhcnNlci5qc1xudmFyIFBhcnNlT3B0aW9ucztcbihmdW5jdGlvbihQYXJzZU9wdGlvbnMyKSB7XG4gIFBhcnNlT3B0aW9uczIuREVGQVVMVCA9IHtcbiAgICBhbGxvd1RyYWlsaW5nQ29tbWE6IGZhbHNlXG4gIH07XG59KShQYXJzZU9wdGlvbnMgfHwgKFBhcnNlT3B0aW9ucyA9IHt9KSk7XG5cbi8vIG5vZGVfbW9kdWxlcy9qc29uYy1wYXJzZXIvbGliL2VzbS9tYWluLmpzXG52YXIgY3JlYXRlU2Nhbm5lcjIgPSBjcmVhdGVTY2FubmVyO1xudmFyIFNjYW5FcnJvcjtcbihmdW5jdGlvbihTY2FuRXJyb3IyKSB7XG4gIFNjYW5FcnJvcjJbU2NhbkVycm9yMltcIk5vbmVcIl0gPSAwXSA9IFwiTm9uZVwiO1xuICBTY2FuRXJyb3IyW1NjYW5FcnJvcjJbXCJVbmV4cGVjdGVkRW5kT2ZDb21tZW50XCJdID0gMV0gPSBcIlVuZXhwZWN0ZWRFbmRPZkNvbW1lbnRcIjtcbiAgU2NhbkVycm9yMltTY2FuRXJyb3IyW1wiVW5leHBlY3RlZEVuZE9mU3RyaW5nXCJdID0gMl0gPSBcIlVuZXhwZWN0ZWRFbmRPZlN0cmluZ1wiO1xuICBTY2FuRXJyb3IyW1NjYW5FcnJvcjJbXCJVbmV4cGVjdGVkRW5kT2ZOdW1iZXJcIl0gPSAzXSA9IFwiVW5leHBlY3RlZEVuZE9mTnVtYmVyXCI7XG4gIFNjYW5FcnJvcjJbU2NhbkVycm9yMltcIkludmFsaWRVbmljb2RlXCJdID0gNF0gPSBcIkludmFsaWRVbmljb2RlXCI7XG4gIFNjYW5FcnJvcjJbU2NhbkVycm9yMltcIkludmFsaWRFc2NhcGVDaGFyYWN0ZXJcIl0gPSA1XSA9IFwiSW52YWxpZEVzY2FwZUNoYXJhY3RlclwiO1xuICBTY2FuRXJyb3IyW1NjYW5FcnJvcjJbXCJJbnZhbGlkQ2hhcmFjdGVyXCJdID0gNl0gPSBcIkludmFsaWRDaGFyYWN0ZXJcIjtcbn0pKFNjYW5FcnJvciB8fCAoU2NhbkVycm9yID0ge30pKTtcbnZhciBTeW50YXhLaW5kO1xuKGZ1bmN0aW9uKFN5bnRheEtpbmQyKSB7XG4gIFN5bnRheEtpbmQyW1N5bnRheEtpbmQyW1wiT3BlbkJyYWNlVG9rZW5cIl0gPSAxXSA9IFwiT3BlbkJyYWNlVG9rZW5cIjtcbiAgU3ludGF4S2luZDJbU3ludGF4S2luZDJbXCJDbG9zZUJyYWNlVG9rZW5cIl0gPSAyXSA9IFwiQ2xvc2VCcmFjZVRva2VuXCI7XG4gIFN5bnRheEtpbmQyW1N5bnRheEtpbmQyW1wiT3BlbkJyYWNrZXRUb2tlblwiXSA9IDNdID0gXCJPcGVuQnJhY2tldFRva2VuXCI7XG4gIFN5bnRheEtpbmQyW1N5bnRheEtpbmQyW1wiQ2xvc2VCcmFja2V0VG9rZW5cIl0gPSA0XSA9IFwiQ2xvc2VCcmFja2V0VG9rZW5cIjtcbiAgU3ludGF4S2luZDJbU3ludGF4S2luZDJbXCJDb21tYVRva2VuXCJdID0gNV0gPSBcIkNvbW1hVG9rZW5cIjtcbiAgU3ludGF4S2luZDJbU3ludGF4S2luZDJbXCJDb2xvblRva2VuXCJdID0gNl0gPSBcIkNvbG9uVG9rZW5cIjtcbiAgU3ludGF4S2luZDJbU3ludGF4S2luZDJbXCJOdWxsS2V5d29yZFwiXSA9IDddID0gXCJOdWxsS2V5d29yZFwiO1xuICBTeW50YXhLaW5kMltTeW50YXhLaW5kMltcIlRydWVLZXl3b3JkXCJdID0gOF0gPSBcIlRydWVLZXl3b3JkXCI7XG4gIFN5bnRheEtpbmQyW1N5bnRheEtpbmQyW1wiRmFsc2VLZXl3b3JkXCJdID0gOV0gPSBcIkZhbHNlS2V5d29yZFwiO1xuICBTeW50YXhLaW5kMltTeW50YXhLaW5kMltcIlN0cmluZ0xpdGVyYWxcIl0gPSAxMF0gPSBcIlN0cmluZ0xpdGVyYWxcIjtcbiAgU3ludGF4S2luZDJbU3ludGF4S2luZDJbXCJOdW1lcmljTGl0ZXJhbFwiXSA9IDExXSA9IFwiTnVtZXJpY0xpdGVyYWxcIjtcbiAgU3ludGF4S2luZDJbU3ludGF4S2luZDJbXCJMaW5lQ29tbWVudFRyaXZpYVwiXSA9IDEyXSA9IFwiTGluZUNvbW1lbnRUcml2aWFcIjtcbiAgU3ludGF4S2luZDJbU3ludGF4S2luZDJbXCJCbG9ja0NvbW1lbnRUcml2aWFcIl0gPSAxM10gPSBcIkJsb2NrQ29tbWVudFRyaXZpYVwiO1xuICBTeW50YXhLaW5kMltTeW50YXhLaW5kMltcIkxpbmVCcmVha1RyaXZpYVwiXSA9IDE0XSA9IFwiTGluZUJyZWFrVHJpdmlhXCI7XG4gIFN5bnRheEtpbmQyW1N5bnRheEtpbmQyW1wiVHJpdmlhXCJdID0gMTVdID0gXCJUcml2aWFcIjtcbiAgU3ludGF4S2luZDJbU3ludGF4S2luZDJbXCJVbmtub3duXCJdID0gMTZdID0gXCJVbmtub3duXCI7XG4gIFN5bnRheEtpbmQyW1N5bnRheEtpbmQyW1wiRU9GXCJdID0gMTddID0gXCJFT0ZcIjtcbn0pKFN5bnRheEtpbmQgfHwgKFN5bnRheEtpbmQgPSB7fSkpO1xudmFyIFBhcnNlRXJyb3JDb2RlO1xuKGZ1bmN0aW9uKFBhcnNlRXJyb3JDb2RlMikge1xuICBQYXJzZUVycm9yQ29kZTJbUGFyc2VFcnJvckNvZGUyW1wiSW52YWxpZFN5bWJvbFwiXSA9IDFdID0gXCJJbnZhbGlkU3ltYm9sXCI7XG4gIFBhcnNlRXJyb3JDb2RlMltQYXJzZUVycm9yQ29kZTJbXCJJbnZhbGlkTnVtYmVyRm9ybWF0XCJdID0gMl0gPSBcIkludmFsaWROdW1iZXJGb3JtYXRcIjtcbiAgUGFyc2VFcnJvckNvZGUyW1BhcnNlRXJyb3JDb2RlMltcIlByb3BlcnR5TmFtZUV4cGVjdGVkXCJdID0gM10gPSBcIlByb3BlcnR5TmFtZUV4cGVjdGVkXCI7XG4gIFBhcnNlRXJyb3JDb2RlMltQYXJzZUVycm9yQ29kZTJbXCJWYWx1ZUV4cGVjdGVkXCJdID0gNF0gPSBcIlZhbHVlRXhwZWN0ZWRcIjtcbiAgUGFyc2VFcnJvckNvZGUyW1BhcnNlRXJyb3JDb2RlMltcIkNvbG9uRXhwZWN0ZWRcIl0gPSA1XSA9IFwiQ29sb25FeHBlY3RlZFwiO1xuICBQYXJzZUVycm9yQ29kZTJbUGFyc2VFcnJvckNvZGUyW1wiQ29tbWFFeHBlY3RlZFwiXSA9IDZdID0gXCJDb21tYUV4cGVjdGVkXCI7XG4gIFBhcnNlRXJyb3JDb2RlMltQYXJzZUVycm9yQ29kZTJbXCJDbG9zZUJyYWNlRXhwZWN0ZWRcIl0gPSA3XSA9IFwiQ2xvc2VCcmFjZUV4cGVjdGVkXCI7XG4gIFBhcnNlRXJyb3JDb2RlMltQYXJzZUVycm9yQ29kZTJbXCJDbG9zZUJyYWNrZXRFeHBlY3RlZFwiXSA9IDhdID0gXCJDbG9zZUJyYWNrZXRFeHBlY3RlZFwiO1xuICBQYXJzZUVycm9yQ29kZTJbUGFyc2VFcnJvckNvZGUyW1wiRW5kT2ZGaWxlRXhwZWN0ZWRcIl0gPSA5XSA9IFwiRW5kT2ZGaWxlRXhwZWN0ZWRcIjtcbiAgUGFyc2VFcnJvckNvZGUyW1BhcnNlRXJyb3JDb2RlMltcIkludmFsaWRDb21tZW50VG9rZW5cIl0gPSAxMF0gPSBcIkludmFsaWRDb21tZW50VG9rZW5cIjtcbiAgUGFyc2VFcnJvckNvZGUyW1BhcnNlRXJyb3JDb2RlMltcIlVuZXhwZWN0ZWRFbmRPZkNvbW1lbnRcIl0gPSAxMV0gPSBcIlVuZXhwZWN0ZWRFbmRPZkNvbW1lbnRcIjtcbiAgUGFyc2VFcnJvckNvZGUyW1BhcnNlRXJyb3JDb2RlMltcIlVuZXhwZWN0ZWRFbmRPZlN0cmluZ1wiXSA9IDEyXSA9IFwiVW5leHBlY3RlZEVuZE9mU3RyaW5nXCI7XG4gIFBhcnNlRXJyb3JDb2RlMltQYXJzZUVycm9yQ29kZTJbXCJVbmV4cGVjdGVkRW5kT2ZOdW1iZXJcIl0gPSAxM10gPSBcIlVuZXhwZWN0ZWRFbmRPZk51bWJlclwiO1xuICBQYXJzZUVycm9yQ29kZTJbUGFyc2VFcnJvckNvZGUyW1wiSW52YWxpZFVuaWNvZGVcIl0gPSAxNF0gPSBcIkludmFsaWRVbmljb2RlXCI7XG4gIFBhcnNlRXJyb3JDb2RlMltQYXJzZUVycm9yQ29kZTJbXCJJbnZhbGlkRXNjYXBlQ2hhcmFjdGVyXCJdID0gMTVdID0gXCJJbnZhbGlkRXNjYXBlQ2hhcmFjdGVyXCI7XG4gIFBhcnNlRXJyb3JDb2RlMltQYXJzZUVycm9yQ29kZTJbXCJJbnZhbGlkQ2hhcmFjdGVyXCJdID0gMTZdID0gXCJJbnZhbGlkQ2hhcmFjdGVyXCI7XG59KShQYXJzZUVycm9yQ29kZSB8fCAoUGFyc2VFcnJvckNvZGUgPSB7fSkpO1xuXG4vLyBzcmMvbGFuZ3VhZ2UvanNvbi90b2tlbml6YXRpb24udHNcbmZ1bmN0aW9uIGNyZWF0ZVRva2VuaXphdGlvblN1cHBvcnQoc3VwcG9ydENvbW1lbnRzKSB7XG4gIHJldHVybiB7XG4gICAgZ2V0SW5pdGlhbFN0YXRlOiAoKSA9PiBuZXcgSlNPTlN0YXRlKG51bGwsIG51bGwsIGZhbHNlLCBudWxsKSxcbiAgICB0b2tlbml6ZTogKGxpbmUsIHN0YXRlKSA9PiB0b2tlbml6ZShzdXBwb3J0Q29tbWVudHMsIGxpbmUsIHN0YXRlKVxuICB9O1xufVxudmFyIFRPS0VOX0RFTElNX09CSkVDVCA9IFwiZGVsaW1pdGVyLmJyYWNrZXQuanNvblwiO1xudmFyIFRPS0VOX0RFTElNX0FSUkFZID0gXCJkZWxpbWl0ZXIuYXJyYXkuanNvblwiO1xudmFyIFRPS0VOX0RFTElNX0NPTE9OID0gXCJkZWxpbWl0ZXIuY29sb24uanNvblwiO1xudmFyIFRPS0VOX0RFTElNX0NPTU1BID0gXCJkZWxpbWl0ZXIuY29tbWEuanNvblwiO1xudmFyIFRPS0VOX1ZBTFVFX0JPT0xFQU4gPSBcImtleXdvcmQuanNvblwiO1xudmFyIFRPS0VOX1ZBTFVFX05VTEwgPSBcImtleXdvcmQuanNvblwiO1xudmFyIFRPS0VOX1ZBTFVFX1NUUklORyA9IFwic3RyaW5nLnZhbHVlLmpzb25cIjtcbnZhciBUT0tFTl9WQUxVRV9OVU1CRVIgPSBcIm51bWJlci5qc29uXCI7XG52YXIgVE9LRU5fUFJPUEVSVFlfTkFNRSA9IFwic3RyaW5nLmtleS5qc29uXCI7XG52YXIgVE9LRU5fQ09NTUVOVF9CTE9DSyA9IFwiY29tbWVudC5ibG9jay5qc29uXCI7XG52YXIgVE9LRU5fQ09NTUVOVF9MSU5FID0gXCJjb21tZW50LmxpbmUuanNvblwiO1xudmFyIFBhcmVudHNTdGFjayA9IGNsYXNzIF9QYXJlbnRzU3RhY2sge1xuICBjb25zdHJ1Y3RvcihwYXJlbnQsIHR5cGUpIHtcbiAgICB0aGlzLnBhcmVudCA9IHBhcmVudDtcbiAgICB0aGlzLnR5cGUgPSB0eXBlO1xuICB9XG4gIHN0YXRpYyBwb3AocGFyZW50cykge1xuICAgIGlmIChwYXJlbnRzKSB7XG4gICAgICByZXR1cm4gcGFyZW50cy5wYXJlbnQ7XG4gICAgfVxuICAgIHJldHVybiBudWxsO1xuICB9XG4gIHN0YXRpYyBwdXNoKHBhcmVudHMsIHR5cGUpIHtcbiAgICByZXR1cm4gbmV3IF9QYXJlbnRzU3RhY2socGFyZW50cywgdHlwZSk7XG4gIH1cbiAgc3RhdGljIGVxdWFscyhhLCBiKSB7XG4gICAgaWYgKCFhICYmICFiKSB7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgaWYgKCFhIHx8ICFiKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIHdoaWxlIChhICYmIGIpIHtcbiAgICAgIGlmIChhID09PSBiKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfVxuICAgICAgaWYgKGEudHlwZSAhPT0gYi50eXBlKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICAgIGEgPSBhLnBhcmVudDtcbiAgICAgIGIgPSBiLnBhcmVudDtcbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cbn07XG52YXIgSlNPTlN0YXRlID0gY2xhc3MgX0pTT05TdGF0ZSB7XG4gIGNvbnN0cnVjdG9yKHN0YXRlLCBzY2FuRXJyb3IsIGxhc3RXYXNDb2xvbiwgcGFyZW50cykge1xuICAgIHRoaXMuX3N0YXRlID0gc3RhdGU7XG4gICAgdGhpcy5zY2FuRXJyb3IgPSBzY2FuRXJyb3I7XG4gICAgdGhpcy5sYXN0V2FzQ29sb24gPSBsYXN0V2FzQ29sb247XG4gICAgdGhpcy5wYXJlbnRzID0gcGFyZW50cztcbiAgfVxuICBjbG9uZSgpIHtcbiAgICByZXR1cm4gbmV3IF9KU09OU3RhdGUodGhpcy5fc3RhdGUsIHRoaXMuc2NhbkVycm9yLCB0aGlzLmxhc3RXYXNDb2xvbiwgdGhpcy5wYXJlbnRzKTtcbiAgfVxuICBlcXVhbHMob3RoZXIpIHtcbiAgICBpZiAob3RoZXIgPT09IHRoaXMpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICBpZiAoIW90aGVyIHx8ICEob3RoZXIgaW5zdGFuY2VvZiBfSlNPTlN0YXRlKSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy5zY2FuRXJyb3IgPT09IG90aGVyLnNjYW5FcnJvciAmJiB0aGlzLmxhc3RXYXNDb2xvbiA9PT0gb3RoZXIubGFzdFdhc0NvbG9uICYmIFBhcmVudHNTdGFjay5lcXVhbHModGhpcy5wYXJlbnRzLCBvdGhlci5wYXJlbnRzKTtcbiAgfVxuICBnZXRTdGF0ZURhdGEoKSB7XG4gICAgcmV0dXJuIHRoaXMuX3N0YXRlO1xuICB9XG4gIHNldFN0YXRlRGF0YShzdGF0ZSkge1xuICAgIHRoaXMuX3N0YXRlID0gc3RhdGU7XG4gIH1cbn07XG5mdW5jdGlvbiB0b2tlbml6ZShjb21tZW50cywgbGluZSwgc3RhdGUsIG9mZnNldERlbHRhID0gMCkge1xuICBsZXQgbnVtYmVyT2ZJbnNlcnRlZENoYXJhY3RlcnMgPSAwO1xuICBsZXQgYWRqdXN0T2Zmc2V0ID0gZmFsc2U7XG4gIHN3aXRjaCAoc3RhdGUuc2NhbkVycm9yKSB7XG4gICAgY2FzZSAyIC8qIFVuZXhwZWN0ZWRFbmRPZlN0cmluZyAqLzpcbiAgICAgIGxpbmUgPSAnXCInICsgbGluZTtcbiAgICAgIG51bWJlck9mSW5zZXJ0ZWRDaGFyYWN0ZXJzID0gMTtcbiAgICAgIGJyZWFrO1xuICAgIGNhc2UgMSAvKiBVbmV4cGVjdGVkRW5kT2ZDb21tZW50ICovOlxuICAgICAgbGluZSA9IFwiLypcIiArIGxpbmU7XG4gICAgICBudW1iZXJPZkluc2VydGVkQ2hhcmFjdGVycyA9IDI7XG4gICAgICBicmVhaztcbiAgfVxuICBjb25zdCBzY2FubmVyID0gY3JlYXRlU2Nhbm5lcjIobGluZSk7XG4gIGxldCBsYXN0V2FzQ29sb24gPSBzdGF0ZS5sYXN0V2FzQ29sb247XG4gIGxldCBwYXJlbnRzID0gc3RhdGUucGFyZW50cztcbiAgY29uc3QgcmV0ID0ge1xuICAgIHRva2VuczogW10sXG4gICAgZW5kU3RhdGU6IHN0YXRlLmNsb25lKClcbiAgfTtcbiAgd2hpbGUgKHRydWUpIHtcbiAgICBsZXQgb2Zmc2V0ID0gb2Zmc2V0RGVsdGEgKyBzY2FubmVyLmdldFBvc2l0aW9uKCk7XG4gICAgbGV0IHR5cGUgPSBcIlwiO1xuICAgIGNvbnN0IGtpbmQgPSBzY2FubmVyLnNjYW4oKTtcbiAgICBpZiAoa2luZCA9PT0gMTcgLyogRU9GICovKSB7XG4gICAgICBicmVhaztcbiAgICB9XG4gICAgaWYgKG9mZnNldCA9PT0gb2Zmc2V0RGVsdGEgKyBzY2FubmVyLmdldFBvc2l0aW9uKCkpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgXCJTY2FubmVyIGRpZCBub3QgYWR2YW5jZSwgbmV4dCAzIGNoYXJhY3RlcnMgYXJlOiBcIiArIGxpbmUuc3Vic3RyKHNjYW5uZXIuZ2V0UG9zaXRpb24oKSwgMylcbiAgICAgICk7XG4gICAgfVxuICAgIGlmIChhZGp1c3RPZmZzZXQpIHtcbiAgICAgIG9mZnNldCAtPSBudW1iZXJPZkluc2VydGVkQ2hhcmFjdGVycztcbiAgICB9XG4gICAgYWRqdXN0T2Zmc2V0ID0gbnVtYmVyT2ZJbnNlcnRlZENoYXJhY3RlcnMgPiAwO1xuICAgIHN3aXRjaCAoa2luZCkge1xuICAgICAgY2FzZSAxIC8qIE9wZW5CcmFjZVRva2VuICovOlxuICAgICAgICBwYXJlbnRzID0gUGFyZW50c1N0YWNrLnB1c2gocGFyZW50cywgMCAvKiBPYmplY3QgKi8pO1xuICAgICAgICB0eXBlID0gVE9LRU5fREVMSU1fT0JKRUNUO1xuICAgICAgICBsYXN0V2FzQ29sb24gPSBmYWxzZTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIDIgLyogQ2xvc2VCcmFjZVRva2VuICovOlxuICAgICAgICBwYXJlbnRzID0gUGFyZW50c1N0YWNrLnBvcChwYXJlbnRzKTtcbiAgICAgICAgdHlwZSA9IFRPS0VOX0RFTElNX09CSkVDVDtcbiAgICAgICAgbGFzdFdhc0NvbG9uID0gZmFsc2U7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAzIC8qIE9wZW5CcmFja2V0VG9rZW4gKi86XG4gICAgICAgIHBhcmVudHMgPSBQYXJlbnRzU3RhY2sucHVzaChwYXJlbnRzLCAxIC8qIEFycmF5ICovKTtcbiAgICAgICAgdHlwZSA9IFRPS0VOX0RFTElNX0FSUkFZO1xuICAgICAgICBsYXN0V2FzQ29sb24gPSBmYWxzZTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIDQgLyogQ2xvc2VCcmFja2V0VG9rZW4gKi86XG4gICAgICAgIHBhcmVudHMgPSBQYXJlbnRzU3RhY2sucG9wKHBhcmVudHMpO1xuICAgICAgICB0eXBlID0gVE9LRU5fREVMSU1fQVJSQVk7XG4gICAgICAgIGxhc3RXYXNDb2xvbiA9IGZhbHNlO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgNiAvKiBDb2xvblRva2VuICovOlxuICAgICAgICB0eXBlID0gVE9LRU5fREVMSU1fQ09MT047XG4gICAgICAgIGxhc3RXYXNDb2xvbiA9IHRydWU7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSA1IC8qIENvbW1hVG9rZW4gKi86XG4gICAgICAgIHR5cGUgPSBUT0tFTl9ERUxJTV9DT01NQTtcbiAgICAgICAgbGFzdFdhc0NvbG9uID0gZmFsc2U7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSA4IC8qIFRydWVLZXl3b3JkICovOlxuICAgICAgY2FzZSA5IC8qIEZhbHNlS2V5d29yZCAqLzpcbiAgICAgICAgdHlwZSA9IFRPS0VOX1ZBTFVFX0JPT0xFQU47XG4gICAgICAgIGxhc3RXYXNDb2xvbiA9IGZhbHNlO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgNyAvKiBOdWxsS2V5d29yZCAqLzpcbiAgICAgICAgdHlwZSA9IFRPS0VOX1ZBTFVFX05VTEw7XG4gICAgICAgIGxhc3RXYXNDb2xvbiA9IGZhbHNlO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgMTAgLyogU3RyaW5nTGl0ZXJhbCAqLzpcbiAgICAgICAgY29uc3QgY3VycmVudFBhcmVudCA9IHBhcmVudHMgPyBwYXJlbnRzLnR5cGUgOiAwIC8qIE9iamVjdCAqLztcbiAgICAgICAgY29uc3QgaW5BcnJheSA9IGN1cnJlbnRQYXJlbnQgPT09IDEgLyogQXJyYXkgKi87XG4gICAgICAgIHR5cGUgPSBsYXN0V2FzQ29sb24gfHwgaW5BcnJheSA/IFRPS0VOX1ZBTFVFX1NUUklORyA6IFRPS0VOX1BST1BFUlRZX05BTUU7XG4gICAgICAgIGxhc3RXYXNDb2xvbiA9IGZhbHNlO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgMTEgLyogTnVtZXJpY0xpdGVyYWwgKi86XG4gICAgICAgIHR5cGUgPSBUT0tFTl9WQUxVRV9OVU1CRVI7XG4gICAgICAgIGxhc3RXYXNDb2xvbiA9IGZhbHNlO1xuICAgICAgICBicmVhaztcbiAgICB9XG4gICAgaWYgKGNvbW1lbnRzKSB7XG4gICAgICBzd2l0Y2ggKGtpbmQpIHtcbiAgICAgICAgY2FzZSAxMiAvKiBMaW5lQ29tbWVudFRyaXZpYSAqLzpcbiAgICAgICAgICB0eXBlID0gVE9LRU5fQ09NTUVOVF9MSU5FO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDEzIC8qIEJsb2NrQ29tbWVudFRyaXZpYSAqLzpcbiAgICAgICAgICB0eXBlID0gVE9LRU5fQ09NTUVOVF9CTE9DSztcbiAgICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0LmVuZFN0YXRlID0gbmV3IEpTT05TdGF0ZShcbiAgICAgIHN0YXRlLmdldFN0YXRlRGF0YSgpLFxuICAgICAgc2Nhbm5lci5nZXRUb2tlbkVycm9yKCksXG4gICAgICBsYXN0V2FzQ29sb24sXG4gICAgICBwYXJlbnRzXG4gICAgKTtcbiAgICByZXQudG9rZW5zLnB1c2goe1xuICAgICAgc3RhcnRJbmRleDogb2Zmc2V0LFxuICAgICAgc2NvcGVzOiB0eXBlXG4gICAgfSk7XG4gIH1cbiAgcmV0dXJuIHJldDtcbn1cblxuLy8gc3JjL2xhbmd1YWdlL2pzb24vanNvbk1vZGUudHNcbnZhciB3b3JrZXI7XG5mdW5jdGlvbiBnZXRXb3JrZXIoKSB7XG4gIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgaWYgKCF3b3JrZXIpIHtcbiAgICAgIHJldHVybiByZWplY3QoXCJKU09OIG5vdCByZWdpc3RlcmVkIVwiKTtcbiAgICB9XG4gICAgcmVzb2x2ZSh3b3JrZXIpO1xuICB9KTtcbn1cbnZhciBKU09ORGlhZ25vc3RpY3NBZGFwdGVyID0gY2xhc3MgZXh0ZW5kcyBEaWFnbm9zdGljc0FkYXB0ZXIge1xuICBjb25zdHJ1Y3RvcihsYW5ndWFnZUlkLCB3b3JrZXIyLCBkZWZhdWx0cykge1xuICAgIHN1cGVyKGxhbmd1YWdlSWQsIHdvcmtlcjIsIGRlZmF1bHRzLm9uRGlkQ2hhbmdlKTtcbiAgICB0aGlzLl9kaXNwb3NhYmxlcy5wdXNoKFxuICAgICAgbW9uYWNvX2VkaXRvcl9jb3JlX2V4cG9ydHMuZWRpdG9yLm9uV2lsbERpc3Bvc2VNb2RlbCgobW9kZWwpID0+IHtcbiAgICAgICAgdGhpcy5fcmVzZXRTY2hlbWEobW9kZWwudXJpKTtcbiAgICAgIH0pXG4gICAgKTtcbiAgICB0aGlzLl9kaXNwb3NhYmxlcy5wdXNoKFxuICAgICAgbW9uYWNvX2VkaXRvcl9jb3JlX2V4cG9ydHMuZWRpdG9yLm9uRGlkQ2hhbmdlTW9kZWxMYW5ndWFnZSgoZXZlbnQpID0+IHtcbiAgICAgICAgdGhpcy5fcmVzZXRTY2hlbWEoZXZlbnQubW9kZWwudXJpKTtcbiAgICAgIH0pXG4gICAgKTtcbiAgfVxuICBfcmVzZXRTY2hlbWEocmVzb3VyY2UpIHtcbiAgICB0aGlzLl93b3JrZXIoKS50aGVuKCh3b3JrZXIyKSA9PiB7XG4gICAgICB3b3JrZXIyLnJlc2V0U2NoZW1hKHJlc291cmNlLnRvU3RyaW5nKCkpO1xuICAgIH0pO1xuICB9XG59O1xuZnVuY3Rpb24gc2V0dXBNb2RlKGRlZmF1bHRzKSB7XG4gIGNvbnN0IGRpc3Bvc2FibGVzID0gW107XG4gIGNvbnN0IHByb3ZpZGVycyA9IFtdO1xuICBjb25zdCBjbGllbnQgPSBuZXcgV29ya2VyTWFuYWdlcihkZWZhdWx0cyk7XG4gIGRpc3Bvc2FibGVzLnB1c2goY2xpZW50KTtcbiAgd29ya2VyID0gKC4uLnVyaXMpID0+IHtcbiAgICByZXR1cm4gY2xpZW50LmdldExhbmd1YWdlU2VydmljZVdvcmtlciguLi51cmlzKTtcbiAgfTtcbiAgZnVuY3Rpb24gcmVnaXN0ZXJQcm92aWRlcnMoKSB7XG4gICAgY29uc3QgeyBsYW5ndWFnZUlkLCBtb2RlQ29uZmlndXJhdGlvbjogbW9kZUNvbmZpZ3VyYXRpb24yIH0gPSBkZWZhdWx0cztcbiAgICBkaXNwb3NlQWxsKHByb3ZpZGVycyk7XG4gICAgaWYgKG1vZGVDb25maWd1cmF0aW9uMi5kb2N1bWVudEZvcm1hdHRpbmdFZGl0cykge1xuICAgICAgcHJvdmlkZXJzLnB1c2goXG4gICAgICAgIG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLmxhbmd1YWdlcy5yZWdpc3RlckRvY3VtZW50Rm9ybWF0dGluZ0VkaXRQcm92aWRlcihcbiAgICAgICAgICBsYW5ndWFnZUlkLFxuICAgICAgICAgIG5ldyBEb2N1bWVudEZvcm1hdHRpbmdFZGl0UHJvdmlkZXIod29ya2VyKVxuICAgICAgICApXG4gICAgICApO1xuICAgIH1cbiAgICBpZiAobW9kZUNvbmZpZ3VyYXRpb24yLmRvY3VtZW50UmFuZ2VGb3JtYXR0aW5nRWRpdHMpIHtcbiAgICAgIHByb3ZpZGVycy5wdXNoKFxuICAgICAgICBtb25hY29fZWRpdG9yX2NvcmVfZXhwb3J0cy5sYW5ndWFnZXMucmVnaXN0ZXJEb2N1bWVudFJhbmdlRm9ybWF0dGluZ0VkaXRQcm92aWRlcihcbiAgICAgICAgICBsYW5ndWFnZUlkLFxuICAgICAgICAgIG5ldyBEb2N1bWVudFJhbmdlRm9ybWF0dGluZ0VkaXRQcm92aWRlcih3b3JrZXIpXG4gICAgICAgIClcbiAgICAgICk7XG4gICAgfVxuICAgIGlmIChtb2RlQ29uZmlndXJhdGlvbjIuY29tcGxldGlvbkl0ZW1zKSB7XG4gICAgICBwcm92aWRlcnMucHVzaChcbiAgICAgICAgbW9uYWNvX2VkaXRvcl9jb3JlX2V4cG9ydHMubGFuZ3VhZ2VzLnJlZ2lzdGVyQ29tcGxldGlvbkl0ZW1Qcm92aWRlcihcbiAgICAgICAgICBsYW5ndWFnZUlkLFxuICAgICAgICAgIG5ldyBDb21wbGV0aW9uQWRhcHRlcih3b3JrZXIsIFtcIiBcIiwgXCI6XCIsICdcIiddKVxuICAgICAgICApXG4gICAgICApO1xuICAgIH1cbiAgICBpZiAobW9kZUNvbmZpZ3VyYXRpb24yLmhvdmVycykge1xuICAgICAgcHJvdmlkZXJzLnB1c2goXG4gICAgICAgIG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLmxhbmd1YWdlcy5yZWdpc3RlckhvdmVyUHJvdmlkZXIobGFuZ3VhZ2VJZCwgbmV3IEhvdmVyQWRhcHRlcih3b3JrZXIpKVxuICAgICAgKTtcbiAgICB9XG4gICAgaWYgKG1vZGVDb25maWd1cmF0aW9uMi5kb2N1bWVudFN5bWJvbHMpIHtcbiAgICAgIHByb3ZpZGVycy5wdXNoKFxuICAgICAgICBtb25hY29fZWRpdG9yX2NvcmVfZXhwb3J0cy5sYW5ndWFnZXMucmVnaXN0ZXJEb2N1bWVudFN5bWJvbFByb3ZpZGVyKFxuICAgICAgICAgIGxhbmd1YWdlSWQsXG4gICAgICAgICAgbmV3IERvY3VtZW50U3ltYm9sQWRhcHRlcih3b3JrZXIpXG4gICAgICAgIClcbiAgICAgICk7XG4gICAgfVxuICAgIGlmIChtb2RlQ29uZmlndXJhdGlvbjIudG9rZW5zKSB7XG4gICAgICBwcm92aWRlcnMucHVzaChtb25hY29fZWRpdG9yX2NvcmVfZXhwb3J0cy5sYW5ndWFnZXMuc2V0VG9rZW5zUHJvdmlkZXIobGFuZ3VhZ2VJZCwgY3JlYXRlVG9rZW5pemF0aW9uU3VwcG9ydCh0cnVlKSkpO1xuICAgIH1cbiAgICBpZiAobW9kZUNvbmZpZ3VyYXRpb24yLmNvbG9ycykge1xuICAgICAgcHJvdmlkZXJzLnB1c2goXG4gICAgICAgIG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLmxhbmd1YWdlcy5yZWdpc3RlckNvbG9yUHJvdmlkZXIoXG4gICAgICAgICAgbGFuZ3VhZ2VJZCxcbiAgICAgICAgICBuZXcgRG9jdW1lbnRDb2xvckFkYXB0ZXIod29ya2VyKVxuICAgICAgICApXG4gICAgICApO1xuICAgIH1cbiAgICBpZiAobW9kZUNvbmZpZ3VyYXRpb24yLmZvbGRpbmdSYW5nZXMpIHtcbiAgICAgIHByb3ZpZGVycy5wdXNoKFxuICAgICAgICBtb25hY29fZWRpdG9yX2NvcmVfZXhwb3J0cy5sYW5ndWFnZXMucmVnaXN0ZXJGb2xkaW5nUmFuZ2VQcm92aWRlcihcbiAgICAgICAgICBsYW5ndWFnZUlkLFxuICAgICAgICAgIG5ldyBGb2xkaW5nUmFuZ2VBZGFwdGVyKHdvcmtlcilcbiAgICAgICAgKVxuICAgICAgKTtcbiAgICB9XG4gICAgaWYgKG1vZGVDb25maWd1cmF0aW9uMi5kaWFnbm9zdGljcykge1xuICAgICAgcHJvdmlkZXJzLnB1c2gobmV3IEpTT05EaWFnbm9zdGljc0FkYXB0ZXIobGFuZ3VhZ2VJZCwgd29ya2VyLCBkZWZhdWx0cykpO1xuICAgIH1cbiAgICBpZiAobW9kZUNvbmZpZ3VyYXRpb24yLnNlbGVjdGlvblJhbmdlcykge1xuICAgICAgcHJvdmlkZXJzLnB1c2goXG4gICAgICAgIG1vbmFjb19lZGl0b3JfY29yZV9leHBvcnRzLmxhbmd1YWdlcy5yZWdpc3RlclNlbGVjdGlvblJhbmdlUHJvdmlkZXIoXG4gICAgICAgICAgbGFuZ3VhZ2VJZCxcbiAgICAgICAgICBuZXcgU2VsZWN0aW9uUmFuZ2VBZGFwdGVyKHdvcmtlcilcbiAgICAgICAgKVxuICAgICAgKTtcbiAgICB9XG4gIH1cbiAgcmVnaXN0ZXJQcm92aWRlcnMoKTtcbiAgZGlzcG9zYWJsZXMucHVzaChtb25hY29fZWRpdG9yX2NvcmVfZXhwb3J0cy5sYW5ndWFnZXMuc2V0TGFuZ3VhZ2VDb25maWd1cmF0aW9uKGRlZmF1bHRzLmxhbmd1YWdlSWQsIHJpY2hFZGl0Q29uZmlndXJhdGlvbikpO1xuICBsZXQgbW9kZUNvbmZpZ3VyYXRpb24gPSBkZWZhdWx0cy5tb2RlQ29uZmlndXJhdGlvbjtcbiAgZGVmYXVsdHMub25EaWRDaGFuZ2UoKG5ld0RlZmF1bHRzKSA9PiB7XG4gICAgaWYgKG5ld0RlZmF1bHRzLm1vZGVDb25maWd1cmF0aW9uICE9PSBtb2RlQ29uZmlndXJhdGlvbikge1xuICAgICAgbW9kZUNvbmZpZ3VyYXRpb24gPSBuZXdEZWZhdWx0cy5tb2RlQ29uZmlndXJhdGlvbjtcbiAgICAgIHJlZ2lzdGVyUHJvdmlkZXJzKCk7XG4gICAgfVxuICB9KTtcbiAgZGlzcG9zYWJsZXMucHVzaChhc0Rpc3Bvc2FibGUocHJvdmlkZXJzKSk7XG4gIHJldHVybiBhc0Rpc3Bvc2FibGUoZGlzcG9zYWJsZXMpO1xufVxuZnVuY3Rpb24gYXNEaXNwb3NhYmxlKGRpc3Bvc2FibGVzKSB7XG4gIHJldHVybiB7IGRpc3Bvc2U6ICgpID0+IGRpc3Bvc2VBbGwoZGlzcG9zYWJsZXMpIH07XG59XG5mdW5jdGlvbiBkaXNwb3NlQWxsKGRpc3Bvc2FibGVzKSB7XG4gIHdoaWxlIChkaXNwb3NhYmxlcy5sZW5ndGgpIHtcbiAgICBkaXNwb3NhYmxlcy5wb3AoKS5kaXNwb3NlKCk7XG4gIH1cbn1cbnZhciByaWNoRWRpdENvbmZpZ3VyYXRpb24gPSB7XG4gIHdvcmRQYXR0ZXJuOiAvKC0/XFxkKlxcLlxcZFxcdyopfChbXlxcW1xce1xcXVxcfVxcOlxcXCJcXCxcXHNdKykvZyxcbiAgY29tbWVudHM6IHtcbiAgICBsaW5lQ29tbWVudDogXCIvL1wiLFxuICAgIGJsb2NrQ29tbWVudDogW1wiLypcIiwgXCIqL1wiXVxuICB9LFxuICBicmFja2V0czogW1xuICAgIFtcIntcIiwgXCJ9XCJdLFxuICAgIFtcIltcIiwgXCJdXCJdXG4gIF0sXG4gIGF1dG9DbG9zaW5nUGFpcnM6IFtcbiAgICB7IG9wZW46IFwie1wiLCBjbG9zZTogXCJ9XCIsIG5vdEluOiBbXCJzdHJpbmdcIl0gfSxcbiAgICB7IG9wZW46IFwiW1wiLCBjbG9zZTogXCJdXCIsIG5vdEluOiBbXCJzdHJpbmdcIl0gfSxcbiAgICB7IG9wZW46ICdcIicsIGNsb3NlOiAnXCInLCBub3RJbjogW1wic3RyaW5nXCJdIH1cbiAgXVxufTtcbmV4cG9ydCB7XG4gIENvbXBsZXRpb25BZGFwdGVyLFxuICBEZWZpbml0aW9uQWRhcHRlcixcbiAgRGlhZ25vc3RpY3NBZGFwdGVyLFxuICBEb2N1bWVudENvbG9yQWRhcHRlcixcbiAgRG9jdW1lbnRGb3JtYXR0aW5nRWRpdFByb3ZpZGVyLFxuICBEb2N1bWVudEhpZ2hsaWdodEFkYXB0ZXIsXG4gIERvY3VtZW50TGlua0FkYXB0ZXIsXG4gIERvY3VtZW50UmFuZ2VGb3JtYXR0aW5nRWRpdFByb3ZpZGVyLFxuICBEb2N1bWVudFN5bWJvbEFkYXB0ZXIsXG4gIEZvbGRpbmdSYW5nZUFkYXB0ZXIsXG4gIEhvdmVyQWRhcHRlcixcbiAgUmVmZXJlbmNlQWRhcHRlcixcbiAgUmVuYW1lQWRhcHRlcixcbiAgU2VsZWN0aW9uUmFuZ2VBZGFwdGVyLFxuICBXb3JrZXJNYW5hZ2VyLFxuICBmcm9tUG9zaXRpb24sXG4gIGZyb21SYW5nZSxcbiAgZ2V0V29ya2VyLFxuICBzZXR1cE1vZGUsXG4gIHRvUmFuZ2UsXG4gIHRvVGV4dEVkaXRcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/language/json/jsonMode.js\n"));

/***/ })

}]);