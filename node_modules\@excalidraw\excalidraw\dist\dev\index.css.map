{"version": 3, "sources": ["../../components/Spinner.scss", "../../components/ToolIcon.scss", "../../css/variables.module.scss", "../../css/theme.scss", "../../components/ButtonIcon.scss", "../../css/variables.module.scss", "../../components/EyeDropper.scss", "../../components/Island.scss", "../../components/ColorPicker/ColorPicker.scss", "../../css/variables.module.scss", "../../../../node_modules/open-color/open-color.scss", "../../components/IconPicker.scss", "../../css/variables.module.scss", "../../../../node_modules/open-color/open-color.scss", "../../components/QuickSearch.scss", "../../components/ScrollableList.scss", "../../components/FontPicker/FontPicker.scss", "../../css/variables.module.scss", "../../components/Range.scss", "../../components/Tooltip.scss", "../../../../node_modules/open-color/open-color.scss", "../../css/variables.module.scss", "../../components/TextInput.scss", "../../components/ProjectName.scss", "../../components/CheckboxItem.scss", "../../components/Avatar.scss", "../../css/variables.module.scss", "../../components/hyperlink/Hyperlink.scss", "../../../../node_modules/open-color/open-color.scss", "../../components/Dialog.scss", "../../components/Modal.scss", "../../components/Stack.scss", "../../components/ConfirmDialog.scss", "../../components/DialogActionButton.scss", "../../components/PublishLibrary.scss", "../../../../node_modules/open-color/open-color.scss", "../../components/dropdownMenu/DropdownMenu.scss", "../../css/variables.module.scss", "../../components/LibraryUnit.scss", "../../components/LibraryMenuItems.scss", "../../components/LibraryMenu.scss", "../../../../node_modules/open-color/open-color.scss", "../../components/TextField.scss", "../../css/theme.scss", "../../components/Button.scss", "../../css/variables.module.scss", "../../components/Actions.scss", "../../components/CommandPalette/CommandPalette.scss", "../../components/Popover.scss", "../../components/ContextMenu.scss", "../../../../node_modules/open-color/open-color.scss", "../../components/RadioGroup.scss", "../../components/Switch.scss", "../../components/ImageExportDialog.scss", "../../css/variables.module.scss", "../../components/FilledButton.scss", "../../components/FixedSideContainer.scss", "../../components/HintViewer.scss", "../../css/variables.module.scss", "../../components/PasteChartDialog.scss", "../../css/variables.module.scss", "../../components/HelpDialog.scss", "../../components/UserList.scss", "../../css/variables.module.scss", "../../components/Card.scss", "../../../../node_modules/open-color/open-color.scss", "../../components/ExportDialog.scss", "../../../../node_modules/open-color/open-color.scss", "../../components/Sidebar/SidebarTrigger.scss", "../../css/variables.module.scss", "../../components/Sidebar/Sidebar.scss", "../../css/variables.module.scss", "../../components/main-menu/DefaultItems.scss", "../../components/OverwriteConfirm/OverwriteConfirm.scss", "../../css/variables.module.scss", "../../components/SearchMenu.scss", "../../components/TTDDialog/MermaidToExcalidraw.scss", "../../components/TTDDialog/TTDDialog.scss", "../../../../node_modules/open-color/open-color.scss", "../../components/Stats/DragInput.scss", "../../components/Stats/Stats.scss", "../../components/ElementLinkDialog.scss", "../../css/variables.module.scss", "../../../../node_modules/open-color/open-color.scss", "../../components/LayerUI.scss", "../../components/Toolbar.scss", "../../css/variables.module.scss", "../../components/Toast.scss", "../../components/SVGLayer.scss", "../../element/ElementCanvasButtons.scss", "../../components/FollowMode/FollowMode.scss", "../../css/app.scss", "../../css/theme.scss", "../../css/styles.scss", "../../../../node_modules/open-color/open-color.scss", "../../css/variables.module.scss", "../../fonts/fonts.css", "../../components/footer/FooterCenter.scss", "../../components/ExcalidrawLogo.scss", "../../components/welcome-screen/WelcomeScreen.scss", "../../components/live-collaboration/LiveCollaborationTrigger.scss", "../../../../node_modules/open-color/open-color.scss"], "sourcesContent": ["@import \"open-color/open-color.scss\";\n\n$duration: 1.6s;\n\n.excalidraw {\n  .Spinner {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n    margin-left: auto;\n    margin-right: auto;\n\n    --spinner-color: var(--icon-fill-color);\n\n    svg {\n      animation: rotate $duration linear infinite;\n      animation-delay: var(--spinner-delay);\n      transform-origin: center center;\n    }\n\n    circle {\n      stroke: var(--spinner-color);\n      animation: dash $duration linear 0s infinite;\n      stroke-linecap: round;\n    }\n  }\n\n  @keyframes rotate {\n    100% {\n      transform: rotate(360deg);\n    }\n  }\n\n  @keyframes dash {\n    0% {\n      stroke-dasharray: 1, 300;\n      stroke-dashoffset: 0;\n    }\n    50% {\n      stroke-dasharray: 150, 300;\n      stroke-dashoffset: -200;\n    }\n    100% {\n      stroke-dasharray: 1, 300;\n      stroke-dashoffset: -280;\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .ToolIcon {\n    border-radius: var(--border-radius-lg);\n    display: inline-flex;\n    align-items: center;\n    position: relative;\n    cursor: pointer;\n    -webkit-tap-highlight-color: transparent;\n    user-select: none;\n\n    &__hidden {\n      display: none !important;\n    }\n\n    @include toolbarButtonColorStates;\n  }\n\n  .ToolIcon--plain {\n    background-color: transparent;\n    .ToolIcon__icon {\n      width: 2rem;\n      height: 2rem;\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    position: absolute;\n    opacity: 0;\n    pointer-events: none;\n  }\n\n  .ToolIcon__icon {\n    box-sizing: border-box;\n    width: var(--default-button-size);\n    height: var(--default-button-size);\n    color: var(--icon-fill-color);\n\n    display: flex;\n    justify-content: center;\n    align-items: center;\n\n    border-radius: var(--border-radius-lg);\n\n    & + .ToolIcon__label {\n      margin-inline-start: 0;\n    }\n\n    svg {\n      position: relative;\n      width: var(--default-icon-size);\n      height: var(--default-icon-size);\n      color: var(--icon-fill-color);\n    }\n  }\n\n  .ToolIcon__label {\n    display: flex;\n    align-items: center;\n    color: var(--icon-fill-color);\n    font-family: var(--ui-font);\n    margin: 0 0.8em;\n    text-overflow: ellipsis;\n\n    .Spinner {\n      margin-left: 0.6em;\n    }\n  }\n\n  .ToolIcon_size_small .ToolIcon__icon {\n    width: 2rem;\n    height: 2rem;\n    font-size: 0.8em;\n  }\n\n  .ToolIcon_type_button,\n  .Modal .ToolIcon_type_button {\n    padding: 0;\n    border: none;\n    margin: 0;\n    font-size: inherit;\n    background-color: initial;\n\n    &:focus-visible {\n      box-shadow: 0 0 0 2px var(--focus-highlight-color);\n    }\n\n    &.ToolIcon--selected {\n      background-color: var(--button-gray-2);\n\n      &:active {\n        background-color: var(--button-gray-3);\n      }\n    }\n\n    &:active {\n      background-color: var(--button-gray-3);\n    }\n\n    &:disabled {\n      cursor: default;\n\n      &:active,\n      &:focus-visible,\n      &:hover {\n        background-color: initial;\n        border: none;\n        box-shadow: none;\n      }\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n\n    &--show {\n      visibility: visible;\n    }\n\n    &--hide {\n      display: none !important;\n    }\n  }\n\n  .ToolIcon_type_floating {\n    background-color: transparent;\n\n    &:hover {\n      background-color: transparent;\n    }\n\n    &:active {\n      background-color: transparent;\n    }\n\n    .ToolIcon__icon {\n      background-color: var(--button-gray-1);\n      &:hover {\n        background-color: var(--button-gray-2);\n      }\n      &:active {\n        background-color: var(--button-gray-3);\n      }\n\n      width: 2rem;\n      height: 2rem;\n    }\n  }\n\n  .ToolIcon__keybinding {\n    position: absolute;\n    bottom: 2px;\n    right: 3px;\n    font-size: 0.625rem;\n    color: var(--keybinding-color);\n    font-family: var(--ui-font);\n    user-select: none;\n  }\n\n  .unlocked-icon {\n    :root[dir=\"ltr\"] & {\n      left: 2px;\n    }\n\n    :root[dir=\"rtl\"] & {\n      right: 2px;\n    }\n  }\n\n  .App-toolbar-container {\n    .ToolIcon__icon {\n      width: var(--lg-button-size);\n      height: var(--lg-button-size);\n\n      @media screen and (max-width: 450px) {\n        width: 1.8rem;\n        height: 1.8rem;\n      }\n      @media screen and (max-width: 379px) {\n        width: 1.5rem;\n        height: 1.5rem;\n      }\n\n      svg {\n        width: var(--lg-icon-size);\n        height: var(--lg-icon-size);\n      }\n    }\n\n    .ToolIcon__LaserPointer .ToolIcon__icon,\n    .ToolIcon__MagicButton .ToolIcon__icon {\n      width: var(--default-button-size);\n      height: var(--default-button-size);\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "@import \"open-color/open-color.scss\";\n@import \"./variables.module.scss\";\n\n.excalidraw {\n  --theme-filter: none;\n  --button-destructive-bg-color: #{$oc-red-1};\n  --button-destructive-color: #{$oc-red-9};\n  --button-gray-1: #{$oc-gray-2};\n  --button-gray-2: #{$oc-gray-4};\n  --button-gray-3: #{$oc-gray-5};\n  --button-special-active-bg-color: #{$oc-green-0};\n  --dialog-border-color: var(--color-gray-20);\n  --dropdown-icon: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"292.4\" height=\"292.4\" viewBox=\"0 0 292 292\"><path d=\"M287 197L159 69c-4-3-8-5-13-5s-9 2-13 5L5 197c-3 4-5 8-5 13s2 9 5 13c4 4 8 5 13 5h256c5 0 9-1 13-5s5-8 5-13-1-9-5-13z\"/></svg>');\n  --focus-highlight-color: #{$oc-blue-2};\n  --icon-fill-color: var(--color-on-surface);\n  --icon-green-fill-color: #{$oc-green-9};\n  --default-bg-color: #{$oc-white};\n  --input-bg-color: #{$oc-white};\n  --input-border-color: #{$oc-gray-4};\n  --input-hover-bg-color: #{$oc-gray-1};\n  --input-label-color: #{$oc-gray-7};\n  --island-bg-color: #ffffff;\n  --keybinding-color: var(--color-gray-40);\n  --link-color: #{$oc-blue-7};\n  --overlay-bg-color: #{transparentize($oc-white, 0.12)};\n  --popup-bg-color: var(--island-bg-color);\n  --popup-secondary-bg-color: #{$oc-gray-1};\n  --popup-text-color: #{$oc-black};\n  --popup-text-inverted-color: #{$oc-white};\n  --select-highlight-color: #{$oc-blue-5};\n  --shadow-island: 0px 0px 0.9310142993927002px 0px rgba(0, 0, 0, 0.17),\n    0px 0px 3.1270833015441895px 0px rgba(0, 0, 0, 0.08),\n    0px 7px 14px 0px rgba(0, 0, 0, 0.05);\n\n  --button-hover-bg: var(--color-surface-high);\n  --button-active-bg: var(--color-surface-high);\n  --button-active-border: var(--color-brand-active);\n  --default-border-color: var(--color-surface-high);\n\n  --default-button-size: 2rem;\n  --default-icon-size: 1rem;\n  --lg-button-size: 2.25rem;\n  --lg-icon-size: 1rem;\n  --editor-container-padding: 1rem;\n\n  @media screen and (min-device-width: 1921px) {\n    --lg-button-size: 2.5rem;\n    --lg-icon-size: 1.25rem;\n    --default-button-size: 2.25rem;\n    --default-icon-size: 1.25rem;\n  }\n\n  --scrollbar-thumb: var(--button-gray-2);\n  --scrollbar-thumb-hover: var(--button-gray-3);\n\n  --color-slider-track: hsl(240, 100%, 90%);\n  --color-slider-thumb: var(--color-gray-80);\n\n  --modal-shadow: 0px 100px 80px rgba(0, 0, 0, 0.07),\n    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),\n    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),\n    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),\n    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),\n    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);\n  --avatar-border-color: var(--color-gray-20);\n  --sidebar-shadow: 0px 100px 80px rgba(0, 0, 0, 0.07),\n    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),\n    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),\n    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),\n    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),\n    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);\n  --sidebar-border-color: var(--color-surface-high);\n  --sidebar-bg-color: var(--island-bg-color);\n  --library-dropdown-shadow: 0px 15px 6px rgba(0, 0, 0, 0.01),\n    0px 8px 5px rgba(0, 0, 0, 0.05), 0px 4px 4px rgba(0, 0, 0, 0.09),\n    0px 1px 2px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);\n\n  --space-factor: 0.25rem;\n  --text-primary-color: var(--color-on-surface);\n\n  --color-selection: #6965db;\n\n  --color-icon-white: #{$oc-white};\n\n  --color-primary: #6965db;\n  --color-primary-darker: #5b57d1;\n  --color-primary-darkest: #4a47b1;\n  --color-primary-light: #e3e2fe;\n  --color-primary-light-darker: #d7d5ff;\n  --color-primary-hover: #5753d0;\n\n  --color-gray-10: #f5f5f5;\n  --color-gray-20: #ebebeb;\n  --color-gray-30: #d6d6d6;\n  --color-gray-40: #b8b8b8;\n  --color-gray-50: #999999;\n  --color-gray-60: #7a7a7a;\n  --color-gray-70: #5c5c5c;\n  --color-gray-80: #3d3d3d;\n  --color-gray-85: #242424;\n  --color-gray-90: #1e1e1e;\n  --color-gray-100: #121212;\n\n  --color-disabled: var(--color-gray-40);\n\n  --color-warning: #fceeca;\n  --color-warning-dark: #f5c354;\n  --color-warning-darker: #f3ab2c;\n  --color-warning-darkest: #ec8b14;\n  --color-text-warning: var(--text-primary-color);\n\n  --color-danger: #db6965;\n  --color-danger-dark: #db6965;\n  --color-danger-darker: #d65550;\n  --color-danger-darkest: #d1413c;\n  --color-danger-text: black;\n\n  --color-danger-background: #fff0f0;\n  --color-danger-icon-background: #ffdad6;\n  --color-danger-color: #700000;\n  --color-danger-icon-color: #700000;\n\n  --color-warning-background: var(--color-warning);\n  --color-warning-icon-background: var(--color-warning-dark);\n  --color-warning-color: var(--text-primary-color);\n  --color-warning-icon-color: var(--text-primary-color);\n\n  --color-muted: var(--color-gray-30);\n  --color-muted-darker: var(--color-gray-60);\n  --color-muted-darkest: var(--color-gray-100);\n  --color-muted-background: var(--color-gray-80);\n  --color-muted-background-darker: var(--color-gray-100);\n\n  --color-promo: var(--color-primary);\n\n  --color-success: #cafccc;\n  --color-success-darker: #bafabc;\n  --color-success-darkest: #a5eba8;\n  --color-success-text: #268029;\n  --color-success-contrast: #65bb6a;\n  --color-success-contrast-hover: #6bcf70;\n  --color-success-contrast-active: #6edf74;\n\n  --color-logo-icon: var(--color-primary);\n  --color-logo-text: #190064;\n\n  --border-radius-md: 0.375rem;\n  --border-radius-lg: 0.5rem;\n\n  --color-surface-high: #f1f0ff;\n  --color-surface-mid: #f2f2f7;\n  --color-surface-low: #ececf4;\n  --color-surface-lowest: #ffffff;\n  --color-on-surface: #1b1b1f;\n  --color-brand-hover: #5753d0;\n  --color-on-primary-container: #030064;\n  --color-surface-primary-container: #e0dfff;\n  --color-brand-active: #4440bf;\n  --color-border-outline: #767680;\n  --color-border-outline-variant: #c5c5d0;\n  --color-surface-primary-container: #e0dfff;\n\n  --color-badge: #0b6513;\n  --background-color-badge: #d3ffd2;\n\n  &.theme--dark {\n    &.theme--dark-background-none {\n      background: none;\n    }\n  }\n\n  &.theme--dark {\n    --theme-filter: invert(93%) hue-rotate(180deg);\n    --button-destructive-bg-color: #5a0000;\n    --button-destructive-color: #{$oc-red-3};\n\n    --button-gray-1: #363636;\n    --button-gray-2: #272727;\n    --button-gray-3: #222;\n    --button-special-active-bg-color: #204624;\n    --dialog-border-color: var(--color-gray-80);\n    --dropdown-icon: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"292.4\" height=\"292.4\" viewBox=\"0 0 292 292\"><path fill=\"%23ced4da\" d=\"M287 197L159 69c-4-3-8-5-13-5s-9 2-13 5L5 197c-3 4-5 8-5 13s2 9 5 13c4 4 8 5 13 5h256c5 0 9-1 13-5s5-8 5-13-1-9-5-13z\"/></svg>');\n    --focus-highlight-color: #{$oc-blue-6};\n    --icon-green-fill-color: #{$oc-green-4};\n    --default-bg-color: #121212;\n    --input-bg-color: #121212;\n    --input-border-color: #2e2e2e;\n    --input-hover-bg-color: #181818;\n    --input-label-color: #{$oc-gray-2};\n    --island-bg-color: #232329;\n    --keybinding-color: var(--color-gray-60);\n    --link-color: #{$oc-blue-4};\n    --overlay-bg-color: #{transparentize($oc-gray-8, 0.88)};\n    --popup-secondary-bg-color: #222;\n    --popup-text-color: #{$oc-gray-4};\n    --popup-text-inverted-color: #2c2c2c;\n    --select-highlight-color: #{$oc-blue-4};\n    --shadow-island: 0px 0px 0.9310142993927002px 0px rgba(0, 0, 0, 0.17),\n      0px 0px 3.1270833015441895px 0px rgba(0, 0, 0, 0.08),\n      0px 7px 14px 0px rgba(0, 0, 0, 0.05);\n\n    --modal-shadow: 0px 100px 80px rgba(0, 0, 0, 0.07),\n      0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),\n      0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),\n      0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),\n      0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),\n      0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);\n    --avatar-border-color: var(--color-gray-85);\n\n    --scrollbar-thumb: #{$oc-gray-8};\n    --scrollbar-thumb-hover: #{$oc-gray-7};\n\n    --color-slider-track: hsl(244, 23%, 39%);\n\n    // will be inverted to a lighter color.\n    --color-selection: #3530c4;\n\n    --color-icon-white: var(--color-gray-90);\n\n    --color-primary: #a8a5ff;\n    --color-primary-darker: #b2aeff;\n    --color-primary-darkest: #beb9ff;\n    --color-primary-light: #4f4d6f;\n    --color-primary-light-darker: #43415e;\n    --color-primary-hover: #bbb8ff;\n\n    --color-disabled: var(--color-gray-70);\n\n    --color-text-warning: var(--color-gray-80);\n\n    --color-danger: #ffa8a5;\n    --color-danger-dark: #672120;\n    --color-danger-darker: #8f2625;\n    --color-danger-darkest: #ac2b29;\n    --color-danger-text: #fbcbcc;\n\n    --color-danger-background: #fbcbcc;\n    --color-danger-icon-background: #672120;\n    --color-danger-color: #261919;\n    --color-danger-icon-color: #fbcbcc;\n\n    --color-warning-background: var(--color-warning);\n    --color-warning-icon-background: var(--color-warning-dark);\n    --color-warning-color: var(--color-gray-80);\n    --color-warning-icon-color: var(--color-gray-80);\n\n    --color-muted: var(--color-gray-80);\n    --color-muted-darker: var(--color-gray-60);\n    --color-muted-darkest: var(--color-gray-20);\n    --color-muted-background: var(--color-gray-40);\n    --color-muted-background-darker: var(--color-gray-20);\n\n    --color-logo-text: #e2dfff;\n\n    --color-surface-high: hsl(245, 10%, 21%);\n    --color-surface-low: hsl(240, 8%, 15%);\n    --color-surface-mid: hsl(240 6% 10%);\n    --color-surface-lowest: hsl(0, 0%, 7%);\n    --color-on-surface: #e3e3e8;\n    --color-brand-hover: #bbb8ff;\n    --color-on-primary-container: #e0dfff;\n    --color-surface-primary-container: #403e6a;\n    --color-brand-active: #d0ccff;\n    --color-border-outline: #8e8d9c;\n    --color-border-outline-variant: #46464f;\n    --color-surface-primary-container: #403e6a;\n  }\n}\n", "@import \"../css/theme\";\n\n.excalidraw {\n  button.standalone {\n    @include outlineButtonIconStyles;\n\n    & > * {\n      // dissalow pointer events on children, so we always have event.target on the button itself\n      pointer-events: none;\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", ".excalidraw {\n  .excalidraw-eye-dropper-container,\n  .excalidraw-eye-dropper-backdrop {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    z-index: var(--zIndex-eyeDropperBackdrop);\n    touch-action: none;\n  }\n\n  .excalidraw-eye-dropper-container {\n    pointer-events: none;\n  }\n\n  .excalidraw-eye-dropper-backdrop {\n    pointer-events: all;\n  }\n\n  .excalidraw-eye-dropper-preview {\n    pointer-events: none;\n    width: 3rem;\n    height: 3rem;\n    position: fixed;\n    z-index: var(--zIndex-eyeDropperPreview);\n    border-radius: 1rem;\n    border: 1px solid var(--default-border-color);\n    filter: var(--theme-filter);\n  }\n\n  .excalidraw-eye-dropper-trigger {\n    width: 1.25rem;\n    height: 1.25rem;\n    cursor: pointer;\n    padding: 4px;\n    margin-right: -4px;\n    margin-left: -2px;\n    border-radius: 0.5rem;\n    color: var(--icon-fill-color);\n\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n    &.selected {\n      color: var(--color-primary);\n      background: var(--color-primary-light);\n    }\n  }\n}\n", ".excalidraw {\n  .Island {\n    --padding: 0;\n    box-sizing: border-box;\n    background-color: var(--island-bg-color);\n    box-shadow: var(--shadow-island);\n    border-radius: var(--border-radius-lg);\n    padding: calc(var(--padding) * var(--space-factor));\n    position: relative;\n    transition: box-shadow 0.5s ease-in-out;\n\n    &.zen-mode {\n      box-shadow: none;\n    }\n  }\n}\n", "@import \"../../css/variables.module.scss\";\n\n.excalidraw {\n  .focus-visible-none {\n    &:focus-visible {\n      outline: none !important;\n    }\n  }\n\n  .color-picker__heading {\n    padding: 0 0.5rem;\n    font-size: 0.75rem;\n    text-align: left;\n  }\n\n  .color-picker-container {\n    display: grid;\n    grid-template-columns: 1fr 20px 1.625rem;\n    padding: 0.25rem 0px;\n    align-items: center;\n\n    @include isMobile {\n      max-width: 11rem;\n    }\n  }\n\n  .color-picker__top-picks {\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .color-picker__button {\n    --radius: 0.25rem;\n\n    padding: 0;\n    margin: 0;\n    width: 1.35rem;\n    height: 1.35rem;\n    border: 1px solid var(--color-gray-30);\n    border-radius: var(--radius);\n    filter: var(--theme-filter);\n    background-color: var(--swatch-color);\n    background-position: left center;\n    position: relative;\n    font-family: inherit;\n    box-sizing: border-box;\n\n    &:hover {\n      &::after {\n        content: \"\";\n        position: absolute;\n        top: -2px;\n        left: -2px;\n        right: -2px;\n        bottom: -2px;\n        box-shadow: 0 0 0 1px var(--color-gray-30);\n        border-radius: calc(var(--radius) + 1px);\n        filter: var(--theme-filter);\n      }\n    }\n\n    &.active {\n      .color-picker__button-outline {\n        position: absolute;\n        top: -2px;\n        left: -2px;\n        right: -2px;\n        bottom: -2px;\n        box-shadow: 0 0 0 1px var(--color-primary-darkest);\n        z-index: 1; // due hover state so this has preference\n        border-radius: calc(var(--radius) + 1px);\n        filter: var(--theme-filter);\n      }\n    }\n\n    &:focus-visible {\n      outline: none;\n\n      &::after {\n        content: \"\";\n        position: absolute;\n        top: -4px;\n        right: -4px;\n        bottom: -4px;\n        left: -4px;\n        border: 3px solid var(--focus-highlight-color);\n        border-radius: calc(var(--radius) + 1px);\n      }\n\n      &.active {\n        .color-picker__button-outline {\n          display: none;\n        }\n      }\n    }\n\n    &--large {\n      --radius: 0.5rem;\n      width: 1.875rem;\n      height: 1.875rem;\n    }\n\n    &.is-transparent {\n      background-image: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==\");\n    }\n\n    &--no-focus-visible {\n      border: 0;\n      &::after {\n        display: none;\n      }\n      &:focus-visible {\n        outline: none !important;\n      }\n    }\n\n    &.active-color {\n      border-radius: calc(var(--radius) + 1px);\n      width: 1.625rem;\n      height: 1.625rem;\n    }\n  }\n\n  .color-picker__button__hotkey-label {\n    position: absolute;\n    right: 4px;\n    bottom: 4px;\n    filter: none;\n    font-size: 11px;\n  }\n\n  .color-picker {\n    background: var(--popup-bg-color);\n    border: 0 solid transparentize($oc-white, 0.75);\n    box-shadow: transparentize($oc-black, 0.75) 0 1px 4px;\n    border-radius: 4px;\n    position: absolute;\n\n    :root[dir=\"ltr\"] & {\n      left: -5.5px;\n    }\n\n    :root[dir=\"rtl\"] & {\n      right: -5.5px;\n    }\n  }\n\n  .color-picker-control-container {\n    display: grid;\n    grid-template-columns: auto 1fr;\n    align-items: center;\n    column-gap: 0.5rem;\n  }\n\n  .color-picker-control-container + .popover {\n    position: static;\n  }\n\n  .color-picker-popover-container {\n    margin-top: -0.25rem;\n\n    :root[dir=\"ltr\"] & {\n      margin-left: 0.5rem;\n    }\n\n    :root[dir=\"rtl\"] & {\n      margin-left: -3rem;\n    }\n  }\n\n  .color-picker-triangle {\n    width: 0;\n    height: 0;\n    border-style: solid;\n    border-width: 0 9px 10px;\n    border-color: transparent transparent var(--popup-bg-color);\n    position: absolute;\n    top: 10px;\n\n    :root[dir=\"ltr\"] & {\n      transform: rotate(270deg);\n      left: -14px;\n    }\n\n    :root[dir=\"rtl\"] & {\n      transform: rotate(90deg);\n      right: -14px;\n    }\n  }\n\n  .color-picker-triangle-shadow {\n    border-color: transparent transparent transparentize($oc-black, 0.9);\n\n    :root[dir=\"ltr\"] & {\n      left: -14px;\n    }\n\n    :root[dir=\"rtl\"] & {\n      right: -16px;\n    }\n  }\n\n  .color-picker-content {\n    display: flex;\n    flex-direction: column;\n    gap: 0.75rem;\n    outline: none;\n  }\n\n  .color-picker-content--default {\n    padding: 0.5rem;\n    display: grid;\n    grid-template-columns: repeat(5, 1.875rem);\n    grid-gap: 0.25rem;\n    border-radius: 4px;\n\n    &:focus {\n      outline: none;\n      box-shadow: 0 0 0 2px var(--focus-highlight-color);\n    }\n  }\n\n  .color-picker-content--canvas {\n    display: flex;\n    flex-direction: column;\n    padding: 0.25rem;\n\n    &-title {\n      color: $oc-gray-6;\n      font-size: 12px;\n      padding: 0 0.25rem;\n    }\n\n    &-colors {\n      padding: 0.5rem 0;\n\n      .color-picker-swatch {\n        margin: 0 0.25rem;\n      }\n    }\n  }\n\n  .color-picker-content .color-input-container {\n    grid-column: 1 / span 5;\n  }\n\n  .color-picker-swatch {\n    position: relative;\n    height: 1.875rem;\n    width: 1.875rem;\n    cursor: pointer;\n    border-radius: 4px;\n    margin: 0;\n    box-sizing: border-box;\n    border: 1px solid #ddd;\n    background-color: currentColor !important;\n    filter: var(--theme-filter);\n\n    &:focus {\n      /* TODO: only show the border when the color is too light to see as a shadow */\n      box-shadow: 0 0 4px 1px currentColor;\n      border-color: var(--select-highlight-color);\n    }\n  }\n\n  .color-picker-transparent {\n    border-radius: 4px;\n    box-shadow: transparentize($oc-black, 0.9) 0 0 0 1px inset;\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n  }\n\n  .color-picker-transparent,\n  .color-picker-label-swatch {\n    background: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==\")\n      left center;\n  }\n\n  .color-picker-hash {\n    height: var(--default-button-size);\n    flex-shrink: 0;\n    padding: 0.5rem 0.5rem 0.5rem 0.75rem;\n    border: 1px solid var(--default-border-color);\n    border-right: 0;\n    box-sizing: border-box;\n\n    :root[dir=\"ltr\"] & {\n      border-radius: var(--border-radius-lg) 0 0 var(--border-radius-lg);\n    }\n\n    :root[dir=\"rtl\"] & {\n      border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;\n      border-right: 1px solid var(--default-border-color);\n      border-left: 0;\n    }\n\n    color: var(--input-label-color);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    position: relative;\n  }\n\n  .color-input-container {\n    display: flex;\n\n    &:focus-within {\n      box-shadow: 0 0 0 1px var(--color-primary-darkest);\n      border-radius: var(--border-radius-lg);\n    }\n  }\n\n  .color-picker__input-label {\n    display: grid;\n    grid-template-columns: auto 1fr auto auto;\n    gap: 8px;\n    align-items: center;\n    border: 1px solid var(--default-border-color);\n    border-radius: 8px;\n    padding: 0 12px;\n    margin: 8px;\n    box-sizing: border-box;\n\n    &:focus-within {\n      box-shadow: 0 0 0 1px var(--color-primary-darkest);\n      border-radius: var(--border-radius-lg);\n    }\n  }\n\n  .color-picker__input-hash {\n    padding: 0 0.25rem;\n  }\n\n  .color-picker-input {\n    box-sizing: border-box;\n    width: 100%;\n    margin: 0;\n    font-size: 0.875rem;\n    font-family: inherit;\n    background-color: transparent;\n    color: var(--text-primary-color);\n    border: 0;\n    outline: none;\n    height: var(--default-button-size);\n    border: 1px solid var(--default-border-color);\n    border-left: 0;\n    letter-spacing: 0.4px;\n\n    :root[dir=\"ltr\"] & {\n      border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;\n    }\n\n    :root[dir=\"rtl\"] & {\n      border-radius: var(--border-radius-lg) 0 0 var(--border-radius-lg);\n      border-left: 1px solid var(--default-border-color);\n      border-right: 0;\n    }\n\n    padding: 0.5rem;\n    padding-left: 0.25rem;\n    appearance: none;\n\n    &:focus-visible {\n      box-shadow: none;\n    }\n  }\n\n  .color-picker-label-swatch-container {\n    border: 1px solid var(--default-border-color);\n    border-radius: var(--border-radius-lg);\n    width: var(--default-button-size);\n    height: var(--default-button-size);\n    box-sizing: border-box;\n    overflow: hidden;\n  }\n\n  .color-picker-label-swatch {\n    @include outlineButtonStyles;\n    background-color: var(--swatch-color) !important;\n    overflow: hidden;\n    position: relative;\n    filter: var(--theme-filter);\n    border: 0 !important;\n\n    &:after {\n      content: \"\";\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background: var(--swatch-color);\n    }\n  }\n\n  .color-picker-keybinding {\n    position: absolute;\n    bottom: 2px;\n    font-size: 0.7em;\n\n    :root[dir=\"ltr\"] & {\n      right: 2px;\n    }\n\n    :root[dir=\"rtl\"] & {\n      left: 2px;\n    }\n\n    @include isMobile {\n      display: none;\n    }\n  }\n\n  .color-picker-type-canvasBackground .color-picker-keybinding {\n    color: #aaa;\n  }\n\n  .color-picker-type-elementBackground .color-picker-keybinding {\n    color: $oc-white;\n  }\n\n  .color-picker-swatch[aria-label=\"transparent\"] .color-picker-keybinding {\n    color: #aaa;\n  }\n\n  .color-picker-type-elementStroke .color-picker-keybinding {\n    color: #d4d4d4;\n  }\n\n  &.theme--dark {\n    .color-picker-type-elementBackground .color-picker-keybinding {\n      color: $oc-black;\n    }\n    .color-picker-swatch[aria-label=\"transparent\"] .color-picker-keybinding {\n      color: $oc-black;\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "//\n//\n//  𝗖 𝗢 𝗟 𝗢 𝗥\n//  v 1.9.1\n//\n//  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n\n//  General\n//  ───────────────────────────────────\n\n$oc-white:         #ffffff;\n$oc-black:         #000000;\n\n\n//  Gray\n//  ───────────────────────────────────\n\n$oc-gray-list: (\n  \"0\": #f8f9fa,\n  \"1\": #f1f3f5,\n  \"2\": #e9ecef,\n  \"3\": #dee2e6,\n  \"4\": #ced4da,\n  \"5\": #adb5bd,\n  \"6\": #868e96,\n  \"7\": #495057,\n  \"8\": #343a40,\n  \"9\": #212529\n);\n\n$oc-gray-0: map-get($oc-gray-list, \"0\");\n$oc-gray-1: map-get($oc-gray-list, \"1\");\n$oc-gray-2: map-get($oc-gray-list, \"2\");\n$oc-gray-3: map-get($oc-gray-list, \"3\");\n$oc-gray-4: map-get($oc-gray-list, \"4\");\n$oc-gray-5: map-get($oc-gray-list, \"5\");\n$oc-gray-6: map-get($oc-gray-list, \"6\");\n$oc-gray-7: map-get($oc-gray-list, \"7\");\n$oc-gray-8: map-get($oc-gray-list, \"8\");\n$oc-gray-9: map-get($oc-gray-list, \"9\");\n\n\n//  Red\n//  ───────────────────────────────────\n\n$oc-red-list: (\n  \"0\": #fff5f5,\n  \"1\": #ffe3e3,\n  \"2\": #ffc9c9,\n  \"3\": #ffa8a8,\n  \"4\": #ff8787,\n  \"5\": #ff6b6b,\n  \"6\": #fa5252,\n  \"7\": #f03e3e,\n  \"8\": #e03131,\n  \"9\": #c92a2a\n);\n\n$oc-red-0: map-get($oc-red-list, \"0\");\n$oc-red-1: map-get($oc-red-list, \"1\");\n$oc-red-2: map-get($oc-red-list, \"2\");\n$oc-red-3: map-get($oc-red-list, \"3\");\n$oc-red-4: map-get($oc-red-list, \"4\");\n$oc-red-5: map-get($oc-red-list, \"5\");\n$oc-red-6: map-get($oc-red-list, \"6\");\n$oc-red-7: map-get($oc-red-list, \"7\");\n$oc-red-8: map-get($oc-red-list, \"8\");\n$oc-red-9: map-get($oc-red-list, \"9\");\n\n\n//  Pink\n//  ───────────────────────────────────\n\n$oc-pink-list: (\n  \"0\": #fff0f6,\n  \"1\": #ffdeeb,\n  \"2\": #fcc2d7,\n  \"3\": #faa2c1,\n  \"4\": #f783ac,\n  \"5\": #f06595,\n  \"6\": #e64980,\n  \"7\": #d6336c,\n  \"8\": #c2255c,\n  \"9\": #a61e4d\n);\n\n$oc-pink-0: map-get($oc-pink-list, \"0\");\n$oc-pink-1: map-get($oc-pink-list, \"1\");\n$oc-pink-2: map-get($oc-pink-list, \"2\");\n$oc-pink-3: map-get($oc-pink-list, \"3\");\n$oc-pink-4: map-get($oc-pink-list, \"4\");\n$oc-pink-5: map-get($oc-pink-list, \"5\");\n$oc-pink-6: map-get($oc-pink-list, \"6\");\n$oc-pink-7: map-get($oc-pink-list, \"7\");\n$oc-pink-8: map-get($oc-pink-list, \"8\");\n$oc-pink-9: map-get($oc-pink-list, \"9\");\n\n\n//  Grape\n//  ───────────────────────────────────\n\n$oc-grape-list: (\n  \"0\": #f8f0fc,\n  \"1\": #f3d9fa,\n  \"2\": #eebefa,\n  \"3\": #e599f7,\n  \"4\": #da77f2,\n  \"5\": #cc5de8,\n  \"6\": #be4bdb,\n  \"7\": #ae3ec9,\n  \"8\": #9c36b5,\n  \"9\": #862e9c\n);\n\n$oc-grape-0: map-get($oc-grape-list, \"0\");\n$oc-grape-1: map-get($oc-grape-list, \"1\");\n$oc-grape-2: map-get($oc-grape-list, \"2\");\n$oc-grape-3: map-get($oc-grape-list, \"3\");\n$oc-grape-4: map-get($oc-grape-list, \"4\");\n$oc-grape-5: map-get($oc-grape-list, \"5\");\n$oc-grape-6: map-get($oc-grape-list, \"6\");\n$oc-grape-7: map-get($oc-grape-list, \"7\");\n$oc-grape-8: map-get($oc-grape-list, \"8\");\n$oc-grape-9: map-get($oc-grape-list, \"9\");\n\n\n//  Violet\n//  ───────────────────────────────────\n\n$oc-violet-list: (\n  \"0\": #f3f0ff,\n  \"1\": #e5dbff,\n  \"2\": #d0bfff,\n  \"3\": #b197fc,\n  \"4\": #9775fa,\n  \"5\": #845ef7,\n  \"6\": #7950f2,\n  \"7\": #7048e8,\n  \"8\": #6741d9,\n  \"9\": #5f3dc4\n);\n\n$oc-violet-0: map-get($oc-violet-list, \"0\");\n$oc-violet-1: map-get($oc-violet-list, \"1\");\n$oc-violet-2: map-get($oc-violet-list, \"2\");\n$oc-violet-3: map-get($oc-violet-list, \"3\");\n$oc-violet-4: map-get($oc-violet-list, \"4\");\n$oc-violet-5: map-get($oc-violet-list, \"5\");\n$oc-violet-6: map-get($oc-violet-list, \"6\");\n$oc-violet-7: map-get($oc-violet-list, \"7\");\n$oc-violet-8: map-get($oc-violet-list, \"8\");\n$oc-violet-9: map-get($oc-violet-list, \"9\");\n\n\n//  Indigo\n//  ───────────────────────────────────\n\n$oc-indigo-list: (\n  \"0\": #edf2ff,\n  \"1\": #dbe4ff,\n  \"2\": #bac8ff,\n  \"3\": #91a7ff,\n  \"4\": #748ffc,\n  \"5\": #5c7cfa,\n  \"6\": #4c6ef5,\n  \"7\": #4263eb,\n  \"8\": #3b5bdb,\n  \"9\": #364fc7\n);\n\n$oc-indigo-0: map-get($oc-indigo-list, \"0\");\n$oc-indigo-1: map-get($oc-indigo-list, \"1\");\n$oc-indigo-2: map-get($oc-indigo-list, \"2\");\n$oc-indigo-3: map-get($oc-indigo-list, \"3\");\n$oc-indigo-4: map-get($oc-indigo-list, \"4\");\n$oc-indigo-5: map-get($oc-indigo-list, \"5\");\n$oc-indigo-6: map-get($oc-indigo-list, \"6\");\n$oc-indigo-7: map-get($oc-indigo-list, \"7\");\n$oc-indigo-8: map-get($oc-indigo-list, \"8\");\n$oc-indigo-9: map-get($oc-indigo-list, \"9\");\n\n\n//  Blue\n//  ───────────────────────────────────\n\n$oc-blue-list: (\n  \"0\": #e7f5ff,\n  \"1\": #d0ebff,\n  \"2\": #a5d8ff,\n  \"3\": #74c0fc,\n  \"4\": #4dabf7,\n  \"5\": #339af0,\n  \"6\": #228be6,\n  \"7\": #1c7ed6,\n  \"8\": #1971c2,\n  \"9\": #1864ab\n);\n\n$oc-blue-0: map-get($oc-blue-list, \"0\");\n$oc-blue-1: map-get($oc-blue-list, \"1\");\n$oc-blue-2: map-get($oc-blue-list, \"2\");\n$oc-blue-3: map-get($oc-blue-list, \"3\");\n$oc-blue-4: map-get($oc-blue-list, \"4\");\n$oc-blue-5: map-get($oc-blue-list, \"5\");\n$oc-blue-6: map-get($oc-blue-list, \"6\");\n$oc-blue-7: map-get($oc-blue-list, \"7\");\n$oc-blue-8: map-get($oc-blue-list, \"8\");\n$oc-blue-9: map-get($oc-blue-list, \"9\");\n\n\n//  Cyan\n//  ───────────────────────────────────\n\n$oc-cyan-list: (\n  \"0\": #e3fafc,\n  \"1\": #c5f6fa,\n  \"2\": #99e9f2,\n  \"3\": #66d9e8,\n  \"4\": #3bc9db,\n  \"5\": #22b8cf,\n  \"6\": #15aabf,\n  \"7\": #1098ad,\n  \"8\": #0c8599,\n  \"9\": #0b7285\n);\n\n$oc-cyan-0: map-get($oc-cyan-list, \"0\");\n$oc-cyan-1: map-get($oc-cyan-list, \"1\");\n$oc-cyan-2: map-get($oc-cyan-list, \"2\");\n$oc-cyan-3: map-get($oc-cyan-list, \"3\");\n$oc-cyan-4: map-get($oc-cyan-list, \"4\");\n$oc-cyan-5: map-get($oc-cyan-list, \"5\");\n$oc-cyan-6: map-get($oc-cyan-list, \"6\");\n$oc-cyan-7: map-get($oc-cyan-list, \"7\");\n$oc-cyan-8: map-get($oc-cyan-list, \"8\");\n$oc-cyan-9: map-get($oc-cyan-list, \"9\");\n\n\n//  Teal\n//  ───────────────────────────────────\n\n$oc-teal-list: (\n  \"0\": #e6fcf5,\n  \"1\": #c3fae8,\n  \"2\": #96f2d7,\n  \"3\": #63e6be,\n  \"4\": #38d9a9,\n  \"5\": #20c997,\n  \"6\": #12b886,\n  \"7\": #0ca678,\n  \"8\": #099268,\n  \"9\": #087f5b\n);\n\n$oc-teal-0: map-get($oc-teal-list, \"0\");\n$oc-teal-1: map-get($oc-teal-list, \"1\");\n$oc-teal-2: map-get($oc-teal-list, \"2\");\n$oc-teal-3: map-get($oc-teal-list, \"3\");\n$oc-teal-4: map-get($oc-teal-list, \"4\");\n$oc-teal-5: map-get($oc-teal-list, \"5\");\n$oc-teal-6: map-get($oc-teal-list, \"6\");\n$oc-teal-7: map-get($oc-teal-list, \"7\");\n$oc-teal-8: map-get($oc-teal-list, \"8\");\n$oc-teal-9: map-get($oc-teal-list, \"9\");\n\n\n//  Green\n//  ───────────────────────────────────\n\n$oc-green-list: (\n  \"0\": #ebfbee,\n  \"1\": #d3f9d8,\n  \"2\": #b2f2bb,\n  \"3\": #8ce99a,\n  \"4\": #69db7c,\n  \"5\": #51cf66,\n  \"6\": #40c057,\n  \"7\": #37b24d,\n  \"8\": #2f9e44,\n  \"9\": #2b8a3e\n);\n\n$oc-green-0: map-get($oc-green-list, \"0\");\n$oc-green-1: map-get($oc-green-list, \"1\");\n$oc-green-2: map-get($oc-green-list, \"2\");\n$oc-green-3: map-get($oc-green-list, \"3\");\n$oc-green-4: map-get($oc-green-list, \"4\");\n$oc-green-5: map-get($oc-green-list, \"5\");\n$oc-green-6: map-get($oc-green-list, \"6\");\n$oc-green-7: map-get($oc-green-list, \"7\");\n$oc-green-8: map-get($oc-green-list, \"8\");\n$oc-green-9: map-get($oc-green-list, \"9\");\n\n\n//  Lime\n//  ───────────────────────────────────\n\n$oc-lime-list: (\n  \"0\": #f4fce3,\n  \"1\": #e9fac8,\n  \"2\": #d8f5a2,\n  \"3\": #c0eb75,\n  \"4\": #a9e34b,\n  \"5\": #94d82d,\n  \"6\": #82c91e,\n  \"7\": #74b816,\n  \"8\": #66a80f,\n  \"9\": #5c940d\n);\n\n$oc-lime-0: map-get($oc-lime-list, \"0\");\n$oc-lime-1: map-get($oc-lime-list, \"1\");\n$oc-lime-2: map-get($oc-lime-list, \"2\");\n$oc-lime-3: map-get($oc-lime-list, \"3\");\n$oc-lime-4: map-get($oc-lime-list, \"4\");\n$oc-lime-5: map-get($oc-lime-list, \"5\");\n$oc-lime-6: map-get($oc-lime-list, \"6\");\n$oc-lime-7: map-get($oc-lime-list, \"7\");\n$oc-lime-8: map-get($oc-lime-list, \"8\");\n$oc-lime-9: map-get($oc-lime-list, \"9\");\n\n\n//  Yellow\n//  ───────────────────────────────────\n\n$oc-yellow-list: (\n  \"0\": #fff9db,\n  \"1\": #fff3bf,\n  \"2\": #ffec99,\n  \"3\": #ffe066,\n  \"4\": #ffd43b,\n  \"5\": #fcc419,\n  \"6\": #fab005,\n  \"7\": #f59f00,\n  \"8\": #f08c00,\n  \"9\": #e67700\n);\n\n$oc-yellow-0: map-get($oc-yellow-list, \"0\");\n$oc-yellow-1: map-get($oc-yellow-list, \"1\");\n$oc-yellow-2: map-get($oc-yellow-list, \"2\");\n$oc-yellow-3: map-get($oc-yellow-list, \"3\");\n$oc-yellow-4: map-get($oc-yellow-list, \"4\");\n$oc-yellow-5: map-get($oc-yellow-list, \"5\");\n$oc-yellow-6: map-get($oc-yellow-list, \"6\");\n$oc-yellow-7: map-get($oc-yellow-list, \"7\");\n$oc-yellow-8: map-get($oc-yellow-list, \"8\");\n$oc-yellow-9: map-get($oc-yellow-list, \"9\");\n\n\n//  Orange\n//  ───────────────────────────────────\n\n$oc-orange-list: (\n  \"0\": #fff4e6,\n  \"1\": #ffe8cc,\n  \"2\": #ffd8a8,\n  \"3\": #ffc078,\n  \"4\": #ffa94d,\n  \"5\": #ff922b,\n  \"6\": #fd7e14,\n  \"7\": #f76707,\n  \"8\": #e8590c,\n  \"9\": #d9480f\n);\n\n$oc-orange-0: map-get($oc-orange-list, \"0\");\n$oc-orange-1: map-get($oc-orange-list, \"1\");\n$oc-orange-2: map-get($oc-orange-list, \"2\");\n$oc-orange-3: map-get($oc-orange-list, \"3\");\n$oc-orange-4: map-get($oc-orange-list, \"4\");\n$oc-orange-5: map-get($oc-orange-list, \"5\");\n$oc-orange-6: map-get($oc-orange-list, \"6\");\n$oc-orange-7: map-get($oc-orange-list, \"7\");\n$oc-orange-8: map-get($oc-orange-list, \"8\");\n$oc-orange-9: map-get($oc-orange-list, \"9\");\n\n\n//  Color list\n//  ───────────────────────────────────\n\n$oc-color-spectrum:   9;\n\n$oc-color-list: (\n  $oc-gray-list:      \"gray\",\n  $oc-red-list:      \"red\",\n  $oc-pink-list:      \"pink\",\n  $oc-grape-list:      \"grape\",\n  $oc-violet-list:      \"violet\",\n  $oc-indigo-list:      \"indigo\",\n  $oc-blue-list:      \"blue\",\n  $oc-cyan-list:      \"cyan\",\n  $oc-teal-list:      \"teal\",\n  $oc-green-list:      \"green\",\n  $oc-lime-list:      \"lime\",\n  $oc-yellow-list:      \"yellow\",\n  $oc-orange-list:      \"orange\"\n);\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .picker {\n    padding: 0.5rem;\n    background: var(--popup-bg-color);\n    border: 0 solid transparentize($oc-white, 0.75);\n    box-shadow: var(--shadow-island);\n    border-radius: 4px;\n    position: absolute;\n    :root[dir=\"rtl\"] & {\n      padding: 0.4rem;\n    }\n  }\n\n  .picker-container button,\n  .picker button {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    &:focus-visible {\n      outline: transparent;\n      background-color: var(--button-gray-2);\n      & svg {\n        opacity: 1;\n      }\n    }\n\n    &:hover {\n      background-color: var(--button-gray-2);\n    }\n\n    &:active {\n      background-color: var(--button-gray-3);\n    }\n\n    &:disabled {\n      cursor: not-allowed;\n    }\n\n    svg {\n      margin: 0;\n      width: 36px;\n      height: 18px;\n      pointer-events: none;\n    }\n  }\n\n  .picker button {\n    padding: 0.25rem 0.28rem 0.35rem 0.25rem;\n  }\n\n  .picker-content {\n    display: grid;\n    grid-template-columns: repeat(4, auto);\n    grid-gap: 0.5rem;\n    border-radius: 4px;\n  }\n\n  .picker-collapsible {\n    font-size: 0.75rem;\n    padding: 0.5rem 0;\n  }\n\n  .picker-keybinding {\n    position: absolute;\n    bottom: 2px;\n    font-size: 0.7em;\n    color: var(--keybinding-color);\n\n    :root[dir=\"ltr\"] & {\n      right: 2px;\n    }\n\n    :root[dir=\"rtl\"] & {\n      left: 2px;\n    }\n    @include isMobile {\n      display: none;\n    }\n  }\n\n  .picker-type-canvasBackground .picker-keybinding {\n    color: #aaa;\n  }\n\n  .picker-type-elementBackground .picker-keybinding {\n    color: $oc-white;\n  }\n\n  .picker-swatch[aria-label=\"transparent\"] .picker-keybinding {\n    color: #aaa;\n  }\n\n  .picker-type-elementStroke .picker-keybinding {\n    color: #d4d4d4;\n  }\n\n  &.theme--dark {\n    .picker-type-elementBackground .picker-keybinding {\n      color: $oc-black;\n    }\n    .picker-swatch[aria-label=\"transparent\"] .picker-keybinding {\n      color: $oc-black;\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "//\n//\n//  𝗖 𝗢 𝗟 𝗢 𝗥\n//  v 1.9.1\n//\n//  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n\n//  General\n//  ───────────────────────────────────\n\n$oc-white:         #ffffff;\n$oc-black:         #000000;\n\n\n//  Gray\n//  ───────────────────────────────────\n\n$oc-gray-list: (\n  \"0\": #f8f9fa,\n  \"1\": #f1f3f5,\n  \"2\": #e9ecef,\n  \"3\": #dee2e6,\n  \"4\": #ced4da,\n  \"5\": #adb5bd,\n  \"6\": #868e96,\n  \"7\": #495057,\n  \"8\": #343a40,\n  \"9\": #212529\n);\n\n$oc-gray-0: map-get($oc-gray-list, \"0\");\n$oc-gray-1: map-get($oc-gray-list, \"1\");\n$oc-gray-2: map-get($oc-gray-list, \"2\");\n$oc-gray-3: map-get($oc-gray-list, \"3\");\n$oc-gray-4: map-get($oc-gray-list, \"4\");\n$oc-gray-5: map-get($oc-gray-list, \"5\");\n$oc-gray-6: map-get($oc-gray-list, \"6\");\n$oc-gray-7: map-get($oc-gray-list, \"7\");\n$oc-gray-8: map-get($oc-gray-list, \"8\");\n$oc-gray-9: map-get($oc-gray-list, \"9\");\n\n\n//  Red\n//  ───────────────────────────────────\n\n$oc-red-list: (\n  \"0\": #fff5f5,\n  \"1\": #ffe3e3,\n  \"2\": #ffc9c9,\n  \"3\": #ffa8a8,\n  \"4\": #ff8787,\n  \"5\": #ff6b6b,\n  \"6\": #fa5252,\n  \"7\": #f03e3e,\n  \"8\": #e03131,\n  \"9\": #c92a2a\n);\n\n$oc-red-0: map-get($oc-red-list, \"0\");\n$oc-red-1: map-get($oc-red-list, \"1\");\n$oc-red-2: map-get($oc-red-list, \"2\");\n$oc-red-3: map-get($oc-red-list, \"3\");\n$oc-red-4: map-get($oc-red-list, \"4\");\n$oc-red-5: map-get($oc-red-list, \"5\");\n$oc-red-6: map-get($oc-red-list, \"6\");\n$oc-red-7: map-get($oc-red-list, \"7\");\n$oc-red-8: map-get($oc-red-list, \"8\");\n$oc-red-9: map-get($oc-red-list, \"9\");\n\n\n//  Pink\n//  ───────────────────────────────────\n\n$oc-pink-list: (\n  \"0\": #fff0f6,\n  \"1\": #ffdeeb,\n  \"2\": #fcc2d7,\n  \"3\": #faa2c1,\n  \"4\": #f783ac,\n  \"5\": #f06595,\n  \"6\": #e64980,\n  \"7\": #d6336c,\n  \"8\": #c2255c,\n  \"9\": #a61e4d\n);\n\n$oc-pink-0: map-get($oc-pink-list, \"0\");\n$oc-pink-1: map-get($oc-pink-list, \"1\");\n$oc-pink-2: map-get($oc-pink-list, \"2\");\n$oc-pink-3: map-get($oc-pink-list, \"3\");\n$oc-pink-4: map-get($oc-pink-list, \"4\");\n$oc-pink-5: map-get($oc-pink-list, \"5\");\n$oc-pink-6: map-get($oc-pink-list, \"6\");\n$oc-pink-7: map-get($oc-pink-list, \"7\");\n$oc-pink-8: map-get($oc-pink-list, \"8\");\n$oc-pink-9: map-get($oc-pink-list, \"9\");\n\n\n//  Grape\n//  ───────────────────────────────────\n\n$oc-grape-list: (\n  \"0\": #f8f0fc,\n  \"1\": #f3d9fa,\n  \"2\": #eebefa,\n  \"3\": #e599f7,\n  \"4\": #da77f2,\n  \"5\": #cc5de8,\n  \"6\": #be4bdb,\n  \"7\": #ae3ec9,\n  \"8\": #9c36b5,\n  \"9\": #862e9c\n);\n\n$oc-grape-0: map-get($oc-grape-list, \"0\");\n$oc-grape-1: map-get($oc-grape-list, \"1\");\n$oc-grape-2: map-get($oc-grape-list, \"2\");\n$oc-grape-3: map-get($oc-grape-list, \"3\");\n$oc-grape-4: map-get($oc-grape-list, \"4\");\n$oc-grape-5: map-get($oc-grape-list, \"5\");\n$oc-grape-6: map-get($oc-grape-list, \"6\");\n$oc-grape-7: map-get($oc-grape-list, \"7\");\n$oc-grape-8: map-get($oc-grape-list, \"8\");\n$oc-grape-9: map-get($oc-grape-list, \"9\");\n\n\n//  Violet\n//  ───────────────────────────────────\n\n$oc-violet-list: (\n  \"0\": #f3f0ff,\n  \"1\": #e5dbff,\n  \"2\": #d0bfff,\n  \"3\": #b197fc,\n  \"4\": #9775fa,\n  \"5\": #845ef7,\n  \"6\": #7950f2,\n  \"7\": #7048e8,\n  \"8\": #6741d9,\n  \"9\": #5f3dc4\n);\n\n$oc-violet-0: map-get($oc-violet-list, \"0\");\n$oc-violet-1: map-get($oc-violet-list, \"1\");\n$oc-violet-2: map-get($oc-violet-list, \"2\");\n$oc-violet-3: map-get($oc-violet-list, \"3\");\n$oc-violet-4: map-get($oc-violet-list, \"4\");\n$oc-violet-5: map-get($oc-violet-list, \"5\");\n$oc-violet-6: map-get($oc-violet-list, \"6\");\n$oc-violet-7: map-get($oc-violet-list, \"7\");\n$oc-violet-8: map-get($oc-violet-list, \"8\");\n$oc-violet-9: map-get($oc-violet-list, \"9\");\n\n\n//  Indigo\n//  ───────────────────────────────────\n\n$oc-indigo-list: (\n  \"0\": #edf2ff,\n  \"1\": #dbe4ff,\n  \"2\": #bac8ff,\n  \"3\": #91a7ff,\n  \"4\": #748ffc,\n  \"5\": #5c7cfa,\n  \"6\": #4c6ef5,\n  \"7\": #4263eb,\n  \"8\": #3b5bdb,\n  \"9\": #364fc7\n);\n\n$oc-indigo-0: map-get($oc-indigo-list, \"0\");\n$oc-indigo-1: map-get($oc-indigo-list, \"1\");\n$oc-indigo-2: map-get($oc-indigo-list, \"2\");\n$oc-indigo-3: map-get($oc-indigo-list, \"3\");\n$oc-indigo-4: map-get($oc-indigo-list, \"4\");\n$oc-indigo-5: map-get($oc-indigo-list, \"5\");\n$oc-indigo-6: map-get($oc-indigo-list, \"6\");\n$oc-indigo-7: map-get($oc-indigo-list, \"7\");\n$oc-indigo-8: map-get($oc-indigo-list, \"8\");\n$oc-indigo-9: map-get($oc-indigo-list, \"9\");\n\n\n//  Blue\n//  ───────────────────────────────────\n\n$oc-blue-list: (\n  \"0\": #e7f5ff,\n  \"1\": #d0ebff,\n  \"2\": #a5d8ff,\n  \"3\": #74c0fc,\n  \"4\": #4dabf7,\n  \"5\": #339af0,\n  \"6\": #228be6,\n  \"7\": #1c7ed6,\n  \"8\": #1971c2,\n  \"9\": #1864ab\n);\n\n$oc-blue-0: map-get($oc-blue-list, \"0\");\n$oc-blue-1: map-get($oc-blue-list, \"1\");\n$oc-blue-2: map-get($oc-blue-list, \"2\");\n$oc-blue-3: map-get($oc-blue-list, \"3\");\n$oc-blue-4: map-get($oc-blue-list, \"4\");\n$oc-blue-5: map-get($oc-blue-list, \"5\");\n$oc-blue-6: map-get($oc-blue-list, \"6\");\n$oc-blue-7: map-get($oc-blue-list, \"7\");\n$oc-blue-8: map-get($oc-blue-list, \"8\");\n$oc-blue-9: map-get($oc-blue-list, \"9\");\n\n\n//  Cyan\n//  ───────────────────────────────────\n\n$oc-cyan-list: (\n  \"0\": #e3fafc,\n  \"1\": #c5f6fa,\n  \"2\": #99e9f2,\n  \"3\": #66d9e8,\n  \"4\": #3bc9db,\n  \"5\": #22b8cf,\n  \"6\": #15aabf,\n  \"7\": #1098ad,\n  \"8\": #0c8599,\n  \"9\": #0b7285\n);\n\n$oc-cyan-0: map-get($oc-cyan-list, \"0\");\n$oc-cyan-1: map-get($oc-cyan-list, \"1\");\n$oc-cyan-2: map-get($oc-cyan-list, \"2\");\n$oc-cyan-3: map-get($oc-cyan-list, \"3\");\n$oc-cyan-4: map-get($oc-cyan-list, \"4\");\n$oc-cyan-5: map-get($oc-cyan-list, \"5\");\n$oc-cyan-6: map-get($oc-cyan-list, \"6\");\n$oc-cyan-7: map-get($oc-cyan-list, \"7\");\n$oc-cyan-8: map-get($oc-cyan-list, \"8\");\n$oc-cyan-9: map-get($oc-cyan-list, \"9\");\n\n\n//  Teal\n//  ───────────────────────────────────\n\n$oc-teal-list: (\n  \"0\": #e6fcf5,\n  \"1\": #c3fae8,\n  \"2\": #96f2d7,\n  \"3\": #63e6be,\n  \"4\": #38d9a9,\n  \"5\": #20c997,\n  \"6\": #12b886,\n  \"7\": #0ca678,\n  \"8\": #099268,\n  \"9\": #087f5b\n);\n\n$oc-teal-0: map-get($oc-teal-list, \"0\");\n$oc-teal-1: map-get($oc-teal-list, \"1\");\n$oc-teal-2: map-get($oc-teal-list, \"2\");\n$oc-teal-3: map-get($oc-teal-list, \"3\");\n$oc-teal-4: map-get($oc-teal-list, \"4\");\n$oc-teal-5: map-get($oc-teal-list, \"5\");\n$oc-teal-6: map-get($oc-teal-list, \"6\");\n$oc-teal-7: map-get($oc-teal-list, \"7\");\n$oc-teal-8: map-get($oc-teal-list, \"8\");\n$oc-teal-9: map-get($oc-teal-list, \"9\");\n\n\n//  Green\n//  ───────────────────────────────────\n\n$oc-green-list: (\n  \"0\": #ebfbee,\n  \"1\": #d3f9d8,\n  \"2\": #b2f2bb,\n  \"3\": #8ce99a,\n  \"4\": #69db7c,\n  \"5\": #51cf66,\n  \"6\": #40c057,\n  \"7\": #37b24d,\n  \"8\": #2f9e44,\n  \"9\": #2b8a3e\n);\n\n$oc-green-0: map-get($oc-green-list, \"0\");\n$oc-green-1: map-get($oc-green-list, \"1\");\n$oc-green-2: map-get($oc-green-list, \"2\");\n$oc-green-3: map-get($oc-green-list, \"3\");\n$oc-green-4: map-get($oc-green-list, \"4\");\n$oc-green-5: map-get($oc-green-list, \"5\");\n$oc-green-6: map-get($oc-green-list, \"6\");\n$oc-green-7: map-get($oc-green-list, \"7\");\n$oc-green-8: map-get($oc-green-list, \"8\");\n$oc-green-9: map-get($oc-green-list, \"9\");\n\n\n//  Lime\n//  ───────────────────────────────────\n\n$oc-lime-list: (\n  \"0\": #f4fce3,\n  \"1\": #e9fac8,\n  \"2\": #d8f5a2,\n  \"3\": #c0eb75,\n  \"4\": #a9e34b,\n  \"5\": #94d82d,\n  \"6\": #82c91e,\n  \"7\": #74b816,\n  \"8\": #66a80f,\n  \"9\": #5c940d\n);\n\n$oc-lime-0: map-get($oc-lime-list, \"0\");\n$oc-lime-1: map-get($oc-lime-list, \"1\");\n$oc-lime-2: map-get($oc-lime-list, \"2\");\n$oc-lime-3: map-get($oc-lime-list, \"3\");\n$oc-lime-4: map-get($oc-lime-list, \"4\");\n$oc-lime-5: map-get($oc-lime-list, \"5\");\n$oc-lime-6: map-get($oc-lime-list, \"6\");\n$oc-lime-7: map-get($oc-lime-list, \"7\");\n$oc-lime-8: map-get($oc-lime-list, \"8\");\n$oc-lime-9: map-get($oc-lime-list, \"9\");\n\n\n//  Yellow\n//  ───────────────────────────────────\n\n$oc-yellow-list: (\n  \"0\": #fff9db,\n  \"1\": #fff3bf,\n  \"2\": #ffec99,\n  \"3\": #ffe066,\n  \"4\": #ffd43b,\n  \"5\": #fcc419,\n  \"6\": #fab005,\n  \"7\": #f59f00,\n  \"8\": #f08c00,\n  \"9\": #e67700\n);\n\n$oc-yellow-0: map-get($oc-yellow-list, \"0\");\n$oc-yellow-1: map-get($oc-yellow-list, \"1\");\n$oc-yellow-2: map-get($oc-yellow-list, \"2\");\n$oc-yellow-3: map-get($oc-yellow-list, \"3\");\n$oc-yellow-4: map-get($oc-yellow-list, \"4\");\n$oc-yellow-5: map-get($oc-yellow-list, \"5\");\n$oc-yellow-6: map-get($oc-yellow-list, \"6\");\n$oc-yellow-7: map-get($oc-yellow-list, \"7\");\n$oc-yellow-8: map-get($oc-yellow-list, \"8\");\n$oc-yellow-9: map-get($oc-yellow-list, \"9\");\n\n\n//  Orange\n//  ───────────────────────────────────\n\n$oc-orange-list: (\n  \"0\": #fff4e6,\n  \"1\": #ffe8cc,\n  \"2\": #ffd8a8,\n  \"3\": #ffc078,\n  \"4\": #ffa94d,\n  \"5\": #ff922b,\n  \"6\": #fd7e14,\n  \"7\": #f76707,\n  \"8\": #e8590c,\n  \"9\": #d9480f\n);\n\n$oc-orange-0: map-get($oc-orange-list, \"0\");\n$oc-orange-1: map-get($oc-orange-list, \"1\");\n$oc-orange-2: map-get($oc-orange-list, \"2\");\n$oc-orange-3: map-get($oc-orange-list, \"3\");\n$oc-orange-4: map-get($oc-orange-list, \"4\");\n$oc-orange-5: map-get($oc-orange-list, \"5\");\n$oc-orange-6: map-get($oc-orange-list, \"6\");\n$oc-orange-7: map-get($oc-orange-list, \"7\");\n$oc-orange-8: map-get($oc-orange-list, \"8\");\n$oc-orange-9: map-get($oc-orange-list, \"9\");\n\n\n//  Color list\n//  ───────────────────────────────────\n\n$oc-color-spectrum:   9;\n\n$oc-color-list: (\n  $oc-gray-list:      \"gray\",\n  $oc-red-list:      \"red\",\n  $oc-pink-list:      \"pink\",\n  $oc-grape-list:      \"grape\",\n  $oc-violet-list:      \"violet\",\n  $oc-indigo-list:      \"indigo\",\n  $oc-blue-list:      \"blue\",\n  $oc-cyan-list:      \"cyan\",\n  $oc-teal-list:      \"teal\",\n  $oc-green-list:      \"green\",\n  $oc-lime-list:      \"lime\",\n  $oc-yellow-list:      \"yellow\",\n  $oc-orange-list:      \"orange\"\n);\n", ".excalidraw {\n  --list-border-color: var(--color-gray-20);\n\n  .QuickSearch__wrapper {\n    position: relative;\n    height: 2.6rem; // added +0.1 due to Safari\n    border-bottom: 1px solid var(--list-border-color);\n\n    svg {\n      position: absolute;\n      top: 47.5%; // 50% is not exactly in the center of the input\n      transform: translateY(-50%);\n      left: 0.75rem;\n      width: 1.25rem;\n      height: 1.25rem;\n      color: var(--color-gray-40);\n      z-index: 1;\n    }\n  }\n\n  &.theme--dark {\n    --list-border-color: var(--color-gray-80);\n\n    .QuickSearch__wrapper {\n      border-bottom: none;\n    }\n  }\n\n  .QuickSearch__input {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    box-sizing: border-box;\n    border: 0 !important;\n    font-size: 0.875rem;\n    padding-left: 2.5rem !important;\n    padding-right: 0.75rem !important;\n\n    &::placeholder {\n      color: var(--color-gray-40);\n    }\n\n    &:focus {\n      box-shadow: none !important;\n    }\n  }\n}\n", ".excalidraw {\n  .ScrollableList__wrapper {\n    position: static !important;\n    border: none;\n    font-size: 0.875rem;\n    overflow-y: auto;\n\n    & > .empty,\n    & > .hint {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      padding: 0.5rem;\n      font-size: 0.75rem;\n      color: var(--color-gray-60);\n      overflow: hidden;\n      text-align: center;\n      line-height: 150%;\n    }\n  }\n}\n", "@import \"../../css/variables.module.scss\";\n\n.excalidraw {\n  .FontPicker__container {\n    display: grid;\n    grid-template-columns: calc(1rem + 3 * var(--default-button-size)) 1rem 1fr; // calc ~ 2 gaps + 4 buttons\n    align-items: center;\n\n    @include isMobile {\n      max-width: calc(\n        2rem + 4 * var(--default-button-size)\n      ); // 4 gaps + 4 buttons\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  --slider-thumb-size: 16px;\n\n  .range-wrapper {\n    position: relative;\n    padding-top: 10px;\n    padding-bottom: 30px;\n  }\n\n  .range-input {\n    width: 100%;\n    height: 4px;\n    -webkit-appearance: none;\n    background: var(--color-slider-track);\n    border-radius: 2px;\n    outline: none;\n  }\n\n  .range-input::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: var(--slider-thumb-size);\n    height: var(--slider-thumb-size);\n    background: var(--color-slider-thumb);\n    border-radius: 50%;\n    cursor: pointer;\n    border: none;\n  }\n\n  .range-input::-moz-range-thumb {\n    width: var(--slider-thumb-size);\n    height: var(--slider-thumb-size);\n    background: var(--color-slider-thumb);\n    border-radius: 50%;\n    cursor: pointer;\n    border: none;\n  }\n\n  .value-bubble {\n    position: absolute;\n    bottom: 0;\n    transform: translateX(-50%);\n    font-size: 12px;\n    color: var(--text-primary-color);\n  }\n\n  .zero-label {\n    position: absolute;\n    bottom: 0;\n    left: 4px;\n    font-size: 12px;\n    color: var(--text-primary-color);\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n// container in body where the actual tooltip is appended to\n.excalidraw-tooltip {\n  --ui-font: Assistant, system-ui, BlinkMacSystemFont, -apple-system, Segoe UI,\n    Roboto, Helvetica, Arial, sans-serif;\n  font-family: var(--ui-font);\n  position: fixed;\n  z-index: var(--zIndex-popup);\n\n  padding: 8px;\n  border-radius: 6px;\n  box-sizing: border-box;\n  pointer-events: none;\n  word-wrap: break-word;\n\n  background: $oc-black;\n\n  line-height: 1.5;\n  text-align: center;\n  font-size: 13px;\n  font-weight: 500;\n  color: $oc-white;\n\n  display: none;\n\n  &.excalidraw-tooltip--visible {\n    display: block;\n  }\n}\n\n// wraps the element we want to apply the tooltip to\n.excalidraw-tooltip-wrapper {\n  display: flex;\n}\n\n.excalidraw-tooltip-icon {\n  width: 0.9em;\n  height: 0.9em;\n  margin-left: 5px;\n  margin-top: 1px;\n  display: flex;\n\n  @include isMobile {\n    display: none;\n  }\n}\n", "//\n//\n//  𝗖 𝗢 𝗟 𝗢 𝗥\n//  v 1.9.1\n//\n//  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n\n//  General\n//  ───────────────────────────────────\n\n$oc-white:         #ffffff;\n$oc-black:         #000000;\n\n\n//  Gray\n//  ───────────────────────────────────\n\n$oc-gray-list: (\n  \"0\": #f8f9fa,\n  \"1\": #f1f3f5,\n  \"2\": #e9ecef,\n  \"3\": #dee2e6,\n  \"4\": #ced4da,\n  \"5\": #adb5bd,\n  \"6\": #868e96,\n  \"7\": #495057,\n  \"8\": #343a40,\n  \"9\": #212529\n);\n\n$oc-gray-0: map-get($oc-gray-list, \"0\");\n$oc-gray-1: map-get($oc-gray-list, \"1\");\n$oc-gray-2: map-get($oc-gray-list, \"2\");\n$oc-gray-3: map-get($oc-gray-list, \"3\");\n$oc-gray-4: map-get($oc-gray-list, \"4\");\n$oc-gray-5: map-get($oc-gray-list, \"5\");\n$oc-gray-6: map-get($oc-gray-list, \"6\");\n$oc-gray-7: map-get($oc-gray-list, \"7\");\n$oc-gray-8: map-get($oc-gray-list, \"8\");\n$oc-gray-9: map-get($oc-gray-list, \"9\");\n\n\n//  Red\n//  ───────────────────────────────────\n\n$oc-red-list: (\n  \"0\": #fff5f5,\n  \"1\": #ffe3e3,\n  \"2\": #ffc9c9,\n  \"3\": #ffa8a8,\n  \"4\": #ff8787,\n  \"5\": #ff6b6b,\n  \"6\": #fa5252,\n  \"7\": #f03e3e,\n  \"8\": #e03131,\n  \"9\": #c92a2a\n);\n\n$oc-red-0: map-get($oc-red-list, \"0\");\n$oc-red-1: map-get($oc-red-list, \"1\");\n$oc-red-2: map-get($oc-red-list, \"2\");\n$oc-red-3: map-get($oc-red-list, \"3\");\n$oc-red-4: map-get($oc-red-list, \"4\");\n$oc-red-5: map-get($oc-red-list, \"5\");\n$oc-red-6: map-get($oc-red-list, \"6\");\n$oc-red-7: map-get($oc-red-list, \"7\");\n$oc-red-8: map-get($oc-red-list, \"8\");\n$oc-red-9: map-get($oc-red-list, \"9\");\n\n\n//  Pink\n//  ───────────────────────────────────\n\n$oc-pink-list: (\n  \"0\": #fff0f6,\n  \"1\": #ffdeeb,\n  \"2\": #fcc2d7,\n  \"3\": #faa2c1,\n  \"4\": #f783ac,\n  \"5\": #f06595,\n  \"6\": #e64980,\n  \"7\": #d6336c,\n  \"8\": #c2255c,\n  \"9\": #a61e4d\n);\n\n$oc-pink-0: map-get($oc-pink-list, \"0\");\n$oc-pink-1: map-get($oc-pink-list, \"1\");\n$oc-pink-2: map-get($oc-pink-list, \"2\");\n$oc-pink-3: map-get($oc-pink-list, \"3\");\n$oc-pink-4: map-get($oc-pink-list, \"4\");\n$oc-pink-5: map-get($oc-pink-list, \"5\");\n$oc-pink-6: map-get($oc-pink-list, \"6\");\n$oc-pink-7: map-get($oc-pink-list, \"7\");\n$oc-pink-8: map-get($oc-pink-list, \"8\");\n$oc-pink-9: map-get($oc-pink-list, \"9\");\n\n\n//  Grape\n//  ───────────────────────────────────\n\n$oc-grape-list: (\n  \"0\": #f8f0fc,\n  \"1\": #f3d9fa,\n  \"2\": #eebefa,\n  \"3\": #e599f7,\n  \"4\": #da77f2,\n  \"5\": #cc5de8,\n  \"6\": #be4bdb,\n  \"7\": #ae3ec9,\n  \"8\": #9c36b5,\n  \"9\": #862e9c\n);\n\n$oc-grape-0: map-get($oc-grape-list, \"0\");\n$oc-grape-1: map-get($oc-grape-list, \"1\");\n$oc-grape-2: map-get($oc-grape-list, \"2\");\n$oc-grape-3: map-get($oc-grape-list, \"3\");\n$oc-grape-4: map-get($oc-grape-list, \"4\");\n$oc-grape-5: map-get($oc-grape-list, \"5\");\n$oc-grape-6: map-get($oc-grape-list, \"6\");\n$oc-grape-7: map-get($oc-grape-list, \"7\");\n$oc-grape-8: map-get($oc-grape-list, \"8\");\n$oc-grape-9: map-get($oc-grape-list, \"9\");\n\n\n//  Violet\n//  ───────────────────────────────────\n\n$oc-violet-list: (\n  \"0\": #f3f0ff,\n  \"1\": #e5dbff,\n  \"2\": #d0bfff,\n  \"3\": #b197fc,\n  \"4\": #9775fa,\n  \"5\": #845ef7,\n  \"6\": #7950f2,\n  \"7\": #7048e8,\n  \"8\": #6741d9,\n  \"9\": #5f3dc4\n);\n\n$oc-violet-0: map-get($oc-violet-list, \"0\");\n$oc-violet-1: map-get($oc-violet-list, \"1\");\n$oc-violet-2: map-get($oc-violet-list, \"2\");\n$oc-violet-3: map-get($oc-violet-list, \"3\");\n$oc-violet-4: map-get($oc-violet-list, \"4\");\n$oc-violet-5: map-get($oc-violet-list, \"5\");\n$oc-violet-6: map-get($oc-violet-list, \"6\");\n$oc-violet-7: map-get($oc-violet-list, \"7\");\n$oc-violet-8: map-get($oc-violet-list, \"8\");\n$oc-violet-9: map-get($oc-violet-list, \"9\");\n\n\n//  Indigo\n//  ───────────────────────────────────\n\n$oc-indigo-list: (\n  \"0\": #edf2ff,\n  \"1\": #dbe4ff,\n  \"2\": #bac8ff,\n  \"3\": #91a7ff,\n  \"4\": #748ffc,\n  \"5\": #5c7cfa,\n  \"6\": #4c6ef5,\n  \"7\": #4263eb,\n  \"8\": #3b5bdb,\n  \"9\": #364fc7\n);\n\n$oc-indigo-0: map-get($oc-indigo-list, \"0\");\n$oc-indigo-1: map-get($oc-indigo-list, \"1\");\n$oc-indigo-2: map-get($oc-indigo-list, \"2\");\n$oc-indigo-3: map-get($oc-indigo-list, \"3\");\n$oc-indigo-4: map-get($oc-indigo-list, \"4\");\n$oc-indigo-5: map-get($oc-indigo-list, \"5\");\n$oc-indigo-6: map-get($oc-indigo-list, \"6\");\n$oc-indigo-7: map-get($oc-indigo-list, \"7\");\n$oc-indigo-8: map-get($oc-indigo-list, \"8\");\n$oc-indigo-9: map-get($oc-indigo-list, \"9\");\n\n\n//  Blue\n//  ───────────────────────────────────\n\n$oc-blue-list: (\n  \"0\": #e7f5ff,\n  \"1\": #d0ebff,\n  \"2\": #a5d8ff,\n  \"3\": #74c0fc,\n  \"4\": #4dabf7,\n  \"5\": #339af0,\n  \"6\": #228be6,\n  \"7\": #1c7ed6,\n  \"8\": #1971c2,\n  \"9\": #1864ab\n);\n\n$oc-blue-0: map-get($oc-blue-list, \"0\");\n$oc-blue-1: map-get($oc-blue-list, \"1\");\n$oc-blue-2: map-get($oc-blue-list, \"2\");\n$oc-blue-3: map-get($oc-blue-list, \"3\");\n$oc-blue-4: map-get($oc-blue-list, \"4\");\n$oc-blue-5: map-get($oc-blue-list, \"5\");\n$oc-blue-6: map-get($oc-blue-list, \"6\");\n$oc-blue-7: map-get($oc-blue-list, \"7\");\n$oc-blue-8: map-get($oc-blue-list, \"8\");\n$oc-blue-9: map-get($oc-blue-list, \"9\");\n\n\n//  Cyan\n//  ───────────────────────────────────\n\n$oc-cyan-list: (\n  \"0\": #e3fafc,\n  \"1\": #c5f6fa,\n  \"2\": #99e9f2,\n  \"3\": #66d9e8,\n  \"4\": #3bc9db,\n  \"5\": #22b8cf,\n  \"6\": #15aabf,\n  \"7\": #1098ad,\n  \"8\": #0c8599,\n  \"9\": #0b7285\n);\n\n$oc-cyan-0: map-get($oc-cyan-list, \"0\");\n$oc-cyan-1: map-get($oc-cyan-list, \"1\");\n$oc-cyan-2: map-get($oc-cyan-list, \"2\");\n$oc-cyan-3: map-get($oc-cyan-list, \"3\");\n$oc-cyan-4: map-get($oc-cyan-list, \"4\");\n$oc-cyan-5: map-get($oc-cyan-list, \"5\");\n$oc-cyan-6: map-get($oc-cyan-list, \"6\");\n$oc-cyan-7: map-get($oc-cyan-list, \"7\");\n$oc-cyan-8: map-get($oc-cyan-list, \"8\");\n$oc-cyan-9: map-get($oc-cyan-list, \"9\");\n\n\n//  Teal\n//  ───────────────────────────────────\n\n$oc-teal-list: (\n  \"0\": #e6fcf5,\n  \"1\": #c3fae8,\n  \"2\": #96f2d7,\n  \"3\": #63e6be,\n  \"4\": #38d9a9,\n  \"5\": #20c997,\n  \"6\": #12b886,\n  \"7\": #0ca678,\n  \"8\": #099268,\n  \"9\": #087f5b\n);\n\n$oc-teal-0: map-get($oc-teal-list, \"0\");\n$oc-teal-1: map-get($oc-teal-list, \"1\");\n$oc-teal-2: map-get($oc-teal-list, \"2\");\n$oc-teal-3: map-get($oc-teal-list, \"3\");\n$oc-teal-4: map-get($oc-teal-list, \"4\");\n$oc-teal-5: map-get($oc-teal-list, \"5\");\n$oc-teal-6: map-get($oc-teal-list, \"6\");\n$oc-teal-7: map-get($oc-teal-list, \"7\");\n$oc-teal-8: map-get($oc-teal-list, \"8\");\n$oc-teal-9: map-get($oc-teal-list, \"9\");\n\n\n//  Green\n//  ───────────────────────────────────\n\n$oc-green-list: (\n  \"0\": #ebfbee,\n  \"1\": #d3f9d8,\n  \"2\": #b2f2bb,\n  \"3\": #8ce99a,\n  \"4\": #69db7c,\n  \"5\": #51cf66,\n  \"6\": #40c057,\n  \"7\": #37b24d,\n  \"8\": #2f9e44,\n  \"9\": #2b8a3e\n);\n\n$oc-green-0: map-get($oc-green-list, \"0\");\n$oc-green-1: map-get($oc-green-list, \"1\");\n$oc-green-2: map-get($oc-green-list, \"2\");\n$oc-green-3: map-get($oc-green-list, \"3\");\n$oc-green-4: map-get($oc-green-list, \"4\");\n$oc-green-5: map-get($oc-green-list, \"5\");\n$oc-green-6: map-get($oc-green-list, \"6\");\n$oc-green-7: map-get($oc-green-list, \"7\");\n$oc-green-8: map-get($oc-green-list, \"8\");\n$oc-green-9: map-get($oc-green-list, \"9\");\n\n\n//  Lime\n//  ───────────────────────────────────\n\n$oc-lime-list: (\n  \"0\": #f4fce3,\n  \"1\": #e9fac8,\n  \"2\": #d8f5a2,\n  \"3\": #c0eb75,\n  \"4\": #a9e34b,\n  \"5\": #94d82d,\n  \"6\": #82c91e,\n  \"7\": #74b816,\n  \"8\": #66a80f,\n  \"9\": #5c940d\n);\n\n$oc-lime-0: map-get($oc-lime-list, \"0\");\n$oc-lime-1: map-get($oc-lime-list, \"1\");\n$oc-lime-2: map-get($oc-lime-list, \"2\");\n$oc-lime-3: map-get($oc-lime-list, \"3\");\n$oc-lime-4: map-get($oc-lime-list, \"4\");\n$oc-lime-5: map-get($oc-lime-list, \"5\");\n$oc-lime-6: map-get($oc-lime-list, \"6\");\n$oc-lime-7: map-get($oc-lime-list, \"7\");\n$oc-lime-8: map-get($oc-lime-list, \"8\");\n$oc-lime-9: map-get($oc-lime-list, \"9\");\n\n\n//  Yellow\n//  ───────────────────────────────────\n\n$oc-yellow-list: (\n  \"0\": #fff9db,\n  \"1\": #fff3bf,\n  \"2\": #ffec99,\n  \"3\": #ffe066,\n  \"4\": #ffd43b,\n  \"5\": #fcc419,\n  \"6\": #fab005,\n  \"7\": #f59f00,\n  \"8\": #f08c00,\n  \"9\": #e67700\n);\n\n$oc-yellow-0: map-get($oc-yellow-list, \"0\");\n$oc-yellow-1: map-get($oc-yellow-list, \"1\");\n$oc-yellow-2: map-get($oc-yellow-list, \"2\");\n$oc-yellow-3: map-get($oc-yellow-list, \"3\");\n$oc-yellow-4: map-get($oc-yellow-list, \"4\");\n$oc-yellow-5: map-get($oc-yellow-list, \"5\");\n$oc-yellow-6: map-get($oc-yellow-list, \"6\");\n$oc-yellow-7: map-get($oc-yellow-list, \"7\");\n$oc-yellow-8: map-get($oc-yellow-list, \"8\");\n$oc-yellow-9: map-get($oc-yellow-list, \"9\");\n\n\n//  Orange\n//  ───────────────────────────────────\n\n$oc-orange-list: (\n  \"0\": #fff4e6,\n  \"1\": #ffe8cc,\n  \"2\": #ffd8a8,\n  \"3\": #ffc078,\n  \"4\": #ffa94d,\n  \"5\": #ff922b,\n  \"6\": #fd7e14,\n  \"7\": #f76707,\n  \"8\": #e8590c,\n  \"9\": #d9480f\n);\n\n$oc-orange-0: map-get($oc-orange-list, \"0\");\n$oc-orange-1: map-get($oc-orange-list, \"1\");\n$oc-orange-2: map-get($oc-orange-list, \"2\");\n$oc-orange-3: map-get($oc-orange-list, \"3\");\n$oc-orange-4: map-get($oc-orange-list, \"4\");\n$oc-orange-5: map-get($oc-orange-list, \"5\");\n$oc-orange-6: map-get($oc-orange-list, \"6\");\n$oc-orange-7: map-get($oc-orange-list, \"7\");\n$oc-orange-8: map-get($oc-orange-list, \"8\");\n$oc-orange-9: map-get($oc-orange-list, \"9\");\n\n\n//  Color list\n//  ───────────────────────────────────\n\n$oc-color-spectrum:   9;\n\n$oc-color-list: (\n  $oc-gray-list:      \"gray\",\n  $oc-red-list:      \"red\",\n  $oc-pink-list:      \"pink\",\n  $oc-grape-list:      \"grape\",\n  $oc-violet-list:      \"violet\",\n  $oc-indigo-list:      \"indigo\",\n  $oc-blue-list:      \"blue\",\n  $oc-cyan-list:      \"cyan\",\n  $oc-teal-list:      \"teal\",\n  $oc-green-list:      \"green\",\n  $oc-lime-list:      \"lime\",\n  $oc-yellow-list:      \"yellow\",\n  $oc-orange-list:      \"orange\"\n);\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .TextInput {\n    display: inline-block;\n  }\n}\n", ".ProjectName {\n  margin: auto;\n  display: flex;\n  align-items: center;\n\n  .TextInput {\n    height: calc(1rem - 3px);\n    width: 200px;\n    overflow: hidden;\n    text-align: center;\n    margin-left: 8px;\n    text-overflow: ellipsis;\n\n    &--readonly {\n      background: none;\n      border: none;\n      &:hover {\n        background: none;\n      }\n      width: auto;\n      max-width: 200px;\n      padding-left: 2px;\n    }\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .Checkbox {\n    margin: 4px 0.3em;\n    display: flex;\n    align-items: center;\n\n    cursor: pointer;\n    user-select: none;\n\n    -webkit-tap-highlight-color: transparent;\n\n    &:hover:not(.is-checked) .Checkbox-box:not(:focus) {\n      box-shadow: 0 0 0 2px #{$oc-blue-4};\n    }\n\n    &:hover:not(.is-checked) .Checkbox-box:not(:focus) {\n      svg {\n        display: block;\n        opacity: 0.3;\n      }\n    }\n\n    &:active {\n      .Checkbox-box {\n        box-shadow: 0 0 2px 1px inset #{$oc-blue-7} !important;\n      }\n    }\n\n    &:hover {\n      .Checkbox-box {\n        background-color: fade-out($oc-blue-1, 0.8);\n      }\n    }\n\n    &.is-checked {\n      .Checkbox-box {\n        background-color: #{$oc-blue-1};\n        svg {\n          display: block;\n        }\n      }\n      &:hover .Checkbox-box {\n        background-color: #{$oc-blue-2};\n      }\n    }\n\n    .Checkbox-box {\n      width: 22px;\n      height: 22px;\n      padding: 0;\n      flex: 0 0 auto;\n\n      margin: 0 1em;\n\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      box-shadow: 0 0 0 2px #{$oc-blue-7};\n      background-color: transparent;\n      border-radius: 4px;\n\n      color: #{$oc-blue-7};\n\n      border: 0;\n\n      &:focus {\n        box-shadow: 0 0 0 3px #{$oc-blue-7};\n      }\n\n      svg {\n        display: none;\n        width: 16px;\n        height: 16px;\n        stroke-width: 3px;\n      }\n    }\n\n    .Checkbox-label {\n      display: flex;\n      align-items: center;\n    }\n\n    .excalidraw-tooltip-icon {\n      width: 1em;\n      height: 1em;\n    }\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .Avatar {\n    @include avatarStyles;\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "@import \"../../css/variables.module.scss\";\n\n.excalidraw-hyperlinkContainer {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: absolute;\n  box-shadow: 0px 2px 4px 0 rgb(0 0 0 / 30%);\n  z-index: var(--zIndex-hyperlinkContainer);\n  background: var(--island-bg-color);\n  border-radius: var(--border-radius-md);\n  box-sizing: border-box;\n  // to account for LS due to rendering icons after new link created\n  min-height: 42px;\n\n  &-input,\n  button {\n    z-index: 100;\n  }\n\n  &-input,\n  &-link {\n    height: 24px;\n    padding: 0 8px;\n    line-height: 24px;\n    font-size: 0.9rem;\n    font-weight: 500;\n    font-family: var(--ui-font);\n  }\n\n  &-input {\n    width: 18rem;\n    border: none;\n    background-color: transparent;\n    color: var(--text-primary-color);\n\n    outline: none;\n    border: none;\n    box-shadow: none !important;\n  }\n\n  &-link {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    max-width: 15rem;\n  }\n\n  button {\n    color: $oc-blue-6;\n    background-color: transparent !important;\n    font-weight: 500;\n    &.excalidraw-hyperlinkContainer--remove {\n      color: $oc-red-9;\n    }\n  }\n\n  &--remove .ToolIcon__icon svg {\n    color: $oc-red-6;\n  }\n\n  .ToolIcon__icon {\n    width: 2rem;\n    height: 2rem;\n  }\n\n  &__buttons {\n    flex: 0 0 auto;\n  }\n}\n", "//\n//\n//  𝗖 𝗢 𝗟 𝗢 𝗥\n//  v 1.9.1\n//\n//  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n\n//  General\n//  ───────────────────────────────────\n\n$oc-white:         #ffffff;\n$oc-black:         #000000;\n\n\n//  Gray\n//  ───────────────────────────────────\n\n$oc-gray-list: (\n  \"0\": #f8f9fa,\n  \"1\": #f1f3f5,\n  \"2\": #e9ecef,\n  \"3\": #dee2e6,\n  \"4\": #ced4da,\n  \"5\": #adb5bd,\n  \"6\": #868e96,\n  \"7\": #495057,\n  \"8\": #343a40,\n  \"9\": #212529\n);\n\n$oc-gray-0: map-get($oc-gray-list, \"0\");\n$oc-gray-1: map-get($oc-gray-list, \"1\");\n$oc-gray-2: map-get($oc-gray-list, \"2\");\n$oc-gray-3: map-get($oc-gray-list, \"3\");\n$oc-gray-4: map-get($oc-gray-list, \"4\");\n$oc-gray-5: map-get($oc-gray-list, \"5\");\n$oc-gray-6: map-get($oc-gray-list, \"6\");\n$oc-gray-7: map-get($oc-gray-list, \"7\");\n$oc-gray-8: map-get($oc-gray-list, \"8\");\n$oc-gray-9: map-get($oc-gray-list, \"9\");\n\n\n//  Red\n//  ───────────────────────────────────\n\n$oc-red-list: (\n  \"0\": #fff5f5,\n  \"1\": #ffe3e3,\n  \"2\": #ffc9c9,\n  \"3\": #ffa8a8,\n  \"4\": #ff8787,\n  \"5\": #ff6b6b,\n  \"6\": #fa5252,\n  \"7\": #f03e3e,\n  \"8\": #e03131,\n  \"9\": #c92a2a\n);\n\n$oc-red-0: map-get($oc-red-list, \"0\");\n$oc-red-1: map-get($oc-red-list, \"1\");\n$oc-red-2: map-get($oc-red-list, \"2\");\n$oc-red-3: map-get($oc-red-list, \"3\");\n$oc-red-4: map-get($oc-red-list, \"4\");\n$oc-red-5: map-get($oc-red-list, \"5\");\n$oc-red-6: map-get($oc-red-list, \"6\");\n$oc-red-7: map-get($oc-red-list, \"7\");\n$oc-red-8: map-get($oc-red-list, \"8\");\n$oc-red-9: map-get($oc-red-list, \"9\");\n\n\n//  Pink\n//  ───────────────────────────────────\n\n$oc-pink-list: (\n  \"0\": #fff0f6,\n  \"1\": #ffdeeb,\n  \"2\": #fcc2d7,\n  \"3\": #faa2c1,\n  \"4\": #f783ac,\n  \"5\": #f06595,\n  \"6\": #e64980,\n  \"7\": #d6336c,\n  \"8\": #c2255c,\n  \"9\": #a61e4d\n);\n\n$oc-pink-0: map-get($oc-pink-list, \"0\");\n$oc-pink-1: map-get($oc-pink-list, \"1\");\n$oc-pink-2: map-get($oc-pink-list, \"2\");\n$oc-pink-3: map-get($oc-pink-list, \"3\");\n$oc-pink-4: map-get($oc-pink-list, \"4\");\n$oc-pink-5: map-get($oc-pink-list, \"5\");\n$oc-pink-6: map-get($oc-pink-list, \"6\");\n$oc-pink-7: map-get($oc-pink-list, \"7\");\n$oc-pink-8: map-get($oc-pink-list, \"8\");\n$oc-pink-9: map-get($oc-pink-list, \"9\");\n\n\n//  Grape\n//  ───────────────────────────────────\n\n$oc-grape-list: (\n  \"0\": #f8f0fc,\n  \"1\": #f3d9fa,\n  \"2\": #eebefa,\n  \"3\": #e599f7,\n  \"4\": #da77f2,\n  \"5\": #cc5de8,\n  \"6\": #be4bdb,\n  \"7\": #ae3ec9,\n  \"8\": #9c36b5,\n  \"9\": #862e9c\n);\n\n$oc-grape-0: map-get($oc-grape-list, \"0\");\n$oc-grape-1: map-get($oc-grape-list, \"1\");\n$oc-grape-2: map-get($oc-grape-list, \"2\");\n$oc-grape-3: map-get($oc-grape-list, \"3\");\n$oc-grape-4: map-get($oc-grape-list, \"4\");\n$oc-grape-5: map-get($oc-grape-list, \"5\");\n$oc-grape-6: map-get($oc-grape-list, \"6\");\n$oc-grape-7: map-get($oc-grape-list, \"7\");\n$oc-grape-8: map-get($oc-grape-list, \"8\");\n$oc-grape-9: map-get($oc-grape-list, \"9\");\n\n\n//  Violet\n//  ───────────────────────────────────\n\n$oc-violet-list: (\n  \"0\": #f3f0ff,\n  \"1\": #e5dbff,\n  \"2\": #d0bfff,\n  \"3\": #b197fc,\n  \"4\": #9775fa,\n  \"5\": #845ef7,\n  \"6\": #7950f2,\n  \"7\": #7048e8,\n  \"8\": #6741d9,\n  \"9\": #5f3dc4\n);\n\n$oc-violet-0: map-get($oc-violet-list, \"0\");\n$oc-violet-1: map-get($oc-violet-list, \"1\");\n$oc-violet-2: map-get($oc-violet-list, \"2\");\n$oc-violet-3: map-get($oc-violet-list, \"3\");\n$oc-violet-4: map-get($oc-violet-list, \"4\");\n$oc-violet-5: map-get($oc-violet-list, \"5\");\n$oc-violet-6: map-get($oc-violet-list, \"6\");\n$oc-violet-7: map-get($oc-violet-list, \"7\");\n$oc-violet-8: map-get($oc-violet-list, \"8\");\n$oc-violet-9: map-get($oc-violet-list, \"9\");\n\n\n//  Indigo\n//  ───────────────────────────────────\n\n$oc-indigo-list: (\n  \"0\": #edf2ff,\n  \"1\": #dbe4ff,\n  \"2\": #bac8ff,\n  \"3\": #91a7ff,\n  \"4\": #748ffc,\n  \"5\": #5c7cfa,\n  \"6\": #4c6ef5,\n  \"7\": #4263eb,\n  \"8\": #3b5bdb,\n  \"9\": #364fc7\n);\n\n$oc-indigo-0: map-get($oc-indigo-list, \"0\");\n$oc-indigo-1: map-get($oc-indigo-list, \"1\");\n$oc-indigo-2: map-get($oc-indigo-list, \"2\");\n$oc-indigo-3: map-get($oc-indigo-list, \"3\");\n$oc-indigo-4: map-get($oc-indigo-list, \"4\");\n$oc-indigo-5: map-get($oc-indigo-list, \"5\");\n$oc-indigo-6: map-get($oc-indigo-list, \"6\");\n$oc-indigo-7: map-get($oc-indigo-list, \"7\");\n$oc-indigo-8: map-get($oc-indigo-list, \"8\");\n$oc-indigo-9: map-get($oc-indigo-list, \"9\");\n\n\n//  Blue\n//  ───────────────────────────────────\n\n$oc-blue-list: (\n  \"0\": #e7f5ff,\n  \"1\": #d0ebff,\n  \"2\": #a5d8ff,\n  \"3\": #74c0fc,\n  \"4\": #4dabf7,\n  \"5\": #339af0,\n  \"6\": #228be6,\n  \"7\": #1c7ed6,\n  \"8\": #1971c2,\n  \"9\": #1864ab\n);\n\n$oc-blue-0: map-get($oc-blue-list, \"0\");\n$oc-blue-1: map-get($oc-blue-list, \"1\");\n$oc-blue-2: map-get($oc-blue-list, \"2\");\n$oc-blue-3: map-get($oc-blue-list, \"3\");\n$oc-blue-4: map-get($oc-blue-list, \"4\");\n$oc-blue-5: map-get($oc-blue-list, \"5\");\n$oc-blue-6: map-get($oc-blue-list, \"6\");\n$oc-blue-7: map-get($oc-blue-list, \"7\");\n$oc-blue-8: map-get($oc-blue-list, \"8\");\n$oc-blue-9: map-get($oc-blue-list, \"9\");\n\n\n//  Cyan\n//  ───────────────────────────────────\n\n$oc-cyan-list: (\n  \"0\": #e3fafc,\n  \"1\": #c5f6fa,\n  \"2\": #99e9f2,\n  \"3\": #66d9e8,\n  \"4\": #3bc9db,\n  \"5\": #22b8cf,\n  \"6\": #15aabf,\n  \"7\": #1098ad,\n  \"8\": #0c8599,\n  \"9\": #0b7285\n);\n\n$oc-cyan-0: map-get($oc-cyan-list, \"0\");\n$oc-cyan-1: map-get($oc-cyan-list, \"1\");\n$oc-cyan-2: map-get($oc-cyan-list, \"2\");\n$oc-cyan-3: map-get($oc-cyan-list, \"3\");\n$oc-cyan-4: map-get($oc-cyan-list, \"4\");\n$oc-cyan-5: map-get($oc-cyan-list, \"5\");\n$oc-cyan-6: map-get($oc-cyan-list, \"6\");\n$oc-cyan-7: map-get($oc-cyan-list, \"7\");\n$oc-cyan-8: map-get($oc-cyan-list, \"8\");\n$oc-cyan-9: map-get($oc-cyan-list, \"9\");\n\n\n//  Teal\n//  ───────────────────────────────────\n\n$oc-teal-list: (\n  \"0\": #e6fcf5,\n  \"1\": #c3fae8,\n  \"2\": #96f2d7,\n  \"3\": #63e6be,\n  \"4\": #38d9a9,\n  \"5\": #20c997,\n  \"6\": #12b886,\n  \"7\": #0ca678,\n  \"8\": #099268,\n  \"9\": #087f5b\n);\n\n$oc-teal-0: map-get($oc-teal-list, \"0\");\n$oc-teal-1: map-get($oc-teal-list, \"1\");\n$oc-teal-2: map-get($oc-teal-list, \"2\");\n$oc-teal-3: map-get($oc-teal-list, \"3\");\n$oc-teal-4: map-get($oc-teal-list, \"4\");\n$oc-teal-5: map-get($oc-teal-list, \"5\");\n$oc-teal-6: map-get($oc-teal-list, \"6\");\n$oc-teal-7: map-get($oc-teal-list, \"7\");\n$oc-teal-8: map-get($oc-teal-list, \"8\");\n$oc-teal-9: map-get($oc-teal-list, \"9\");\n\n\n//  Green\n//  ───────────────────────────────────\n\n$oc-green-list: (\n  \"0\": #ebfbee,\n  \"1\": #d3f9d8,\n  \"2\": #b2f2bb,\n  \"3\": #8ce99a,\n  \"4\": #69db7c,\n  \"5\": #51cf66,\n  \"6\": #40c057,\n  \"7\": #37b24d,\n  \"8\": #2f9e44,\n  \"9\": #2b8a3e\n);\n\n$oc-green-0: map-get($oc-green-list, \"0\");\n$oc-green-1: map-get($oc-green-list, \"1\");\n$oc-green-2: map-get($oc-green-list, \"2\");\n$oc-green-3: map-get($oc-green-list, \"3\");\n$oc-green-4: map-get($oc-green-list, \"4\");\n$oc-green-5: map-get($oc-green-list, \"5\");\n$oc-green-6: map-get($oc-green-list, \"6\");\n$oc-green-7: map-get($oc-green-list, \"7\");\n$oc-green-8: map-get($oc-green-list, \"8\");\n$oc-green-9: map-get($oc-green-list, \"9\");\n\n\n//  Lime\n//  ───────────────────────────────────\n\n$oc-lime-list: (\n  \"0\": #f4fce3,\n  \"1\": #e9fac8,\n  \"2\": #d8f5a2,\n  \"3\": #c0eb75,\n  \"4\": #a9e34b,\n  \"5\": #94d82d,\n  \"6\": #82c91e,\n  \"7\": #74b816,\n  \"8\": #66a80f,\n  \"9\": #5c940d\n);\n\n$oc-lime-0: map-get($oc-lime-list, \"0\");\n$oc-lime-1: map-get($oc-lime-list, \"1\");\n$oc-lime-2: map-get($oc-lime-list, \"2\");\n$oc-lime-3: map-get($oc-lime-list, \"3\");\n$oc-lime-4: map-get($oc-lime-list, \"4\");\n$oc-lime-5: map-get($oc-lime-list, \"5\");\n$oc-lime-6: map-get($oc-lime-list, \"6\");\n$oc-lime-7: map-get($oc-lime-list, \"7\");\n$oc-lime-8: map-get($oc-lime-list, \"8\");\n$oc-lime-9: map-get($oc-lime-list, \"9\");\n\n\n//  Yellow\n//  ───────────────────────────────────\n\n$oc-yellow-list: (\n  \"0\": #fff9db,\n  \"1\": #fff3bf,\n  \"2\": #ffec99,\n  \"3\": #ffe066,\n  \"4\": #ffd43b,\n  \"5\": #fcc419,\n  \"6\": #fab005,\n  \"7\": #f59f00,\n  \"8\": #f08c00,\n  \"9\": #e67700\n);\n\n$oc-yellow-0: map-get($oc-yellow-list, \"0\");\n$oc-yellow-1: map-get($oc-yellow-list, \"1\");\n$oc-yellow-2: map-get($oc-yellow-list, \"2\");\n$oc-yellow-3: map-get($oc-yellow-list, \"3\");\n$oc-yellow-4: map-get($oc-yellow-list, \"4\");\n$oc-yellow-5: map-get($oc-yellow-list, \"5\");\n$oc-yellow-6: map-get($oc-yellow-list, \"6\");\n$oc-yellow-7: map-get($oc-yellow-list, \"7\");\n$oc-yellow-8: map-get($oc-yellow-list, \"8\");\n$oc-yellow-9: map-get($oc-yellow-list, \"9\");\n\n\n//  Orange\n//  ───────────────────────────────────\n\n$oc-orange-list: (\n  \"0\": #fff4e6,\n  \"1\": #ffe8cc,\n  \"2\": #ffd8a8,\n  \"3\": #ffc078,\n  \"4\": #ffa94d,\n  \"5\": #ff922b,\n  \"6\": #fd7e14,\n  \"7\": #f76707,\n  \"8\": #e8590c,\n  \"9\": #d9480f\n);\n\n$oc-orange-0: map-get($oc-orange-list, \"0\");\n$oc-orange-1: map-get($oc-orange-list, \"1\");\n$oc-orange-2: map-get($oc-orange-list, \"2\");\n$oc-orange-3: map-get($oc-orange-list, \"3\");\n$oc-orange-4: map-get($oc-orange-list, \"4\");\n$oc-orange-5: map-get($oc-orange-list, \"5\");\n$oc-orange-6: map-get($oc-orange-list, \"6\");\n$oc-orange-7: map-get($oc-orange-list, \"7\");\n$oc-orange-8: map-get($oc-orange-list, \"8\");\n$oc-orange-9: map-get($oc-orange-list, \"9\");\n\n\n//  Color list\n//  ───────────────────────────────────\n\n$oc-color-spectrum:   9;\n\n$oc-color-list: (\n  $oc-gray-list:      \"gray\",\n  $oc-red-list:      \"red\",\n  $oc-pink-list:      \"pink\",\n  $oc-grape-list:      \"grape\",\n  $oc-violet-list:      \"violet\",\n  $oc-indigo-list:      \"indigo\",\n  $oc-blue-list:      \"blue\",\n  $oc-cyan-list:      \"cyan\",\n  $oc-teal-list:      \"teal\",\n  $oc-green-list:      \"green\",\n  $oc-lime-list:      \"lime\",\n  $oc-yellow-list:      \"yellow\",\n  $oc-orange-list:      \"orange\"\n);\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .Dialog {\n    user-select: text;\n    cursor: auto;\n  }\n\n  .Dialog__title {\n    margin: 0;\n    text-align: left;\n    font-size: 1.25rem;\n    border-bottom: 1px solid var(--dialog-border-color);\n    padding: 0 0 0.75rem;\n    margin-bottom: 1.5rem;\n  }\n\n  .Dialog__close {\n    color: var(--color-gray-40);\n    margin: 0;\n    position: absolute;\n    top: 0.75rem;\n    right: 0.5rem;\n    border: 0;\n    background-color: transparent;\n    line-height: 0;\n    cursor: pointer;\n\n    &:hover {\n      color: var(--color-gray-60);\n    }\n    &:active {\n      color: var(--color-gray-40);\n    }\n\n    svg {\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    & + .Dialog__content {\n      --offset: 28px;\n      height: calc(100% - var(--offset)) !important;\n      margin-top: var(--offset) !important;\n    }\n  }\n\n  .Dialog--fullscreen {\n    .Dialog__close {\n      top: 1.25rem;\n      right: 1.25rem;\n    }\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  &.excalidraw-modal-container {\n    position: absolute;\n    z-index: var(--zIndex-modal);\n  }\n\n  .Modal {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    overflow: auto;\n    padding: calc(var(--space-factor) * 10);\n\n    display: flex;\n    flex-direction: column;\n\n    .Island {\n      padding: 2.5rem;\n      border: 0;\n      box-shadow: none;\n      border-radius: 0;\n    }\n\n    &.animations-disabled {\n      .Modal__background {\n        animation: none;\n      }\n\n      .Modal__content {\n        animation: none;\n        opacity: 1;\n      }\n    }\n  }\n\n  .Modal__background {\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 1;\n    background-color: rgba(#121212, 0.2);\n\n    animation: Modal__background__fade-in 0.1s linear forwards;\n  }\n\n  .Modal__content {\n    position: relative;\n    z-index: 2;\n    width: 100%;\n    max-width: var(--max-width);\n    max-height: 100%;\n\n    opacity: 0;\n    transform: translateY(10px);\n    animation: Modal__content_fade-in 0.025s ease-out 0s forwards;\n\n    position: relative;\n    overflow-y: auto;\n\n    // for modals, reset blurry bg\n    background: var(--island-bg-color);\n\n    border: 1px solid var(--dialog-border-color);\n    box-shadow: var(--modal-shadow);\n    border-radius: 0.75rem;\n    box-sizing: border-box;\n\n    &:focus {\n      outline: none;\n    }\n  }\n\n  @keyframes Modal__background__fade-in {\n    from {\n      opacity: 0;\n    }\n    to {\n      opacity: 1;\n    }\n  }\n\n  @keyframes Modal__content_fade-in {\n    from {\n      opacity: 0;\n      transform: scale(0.9);\n    }\n    to {\n      opacity: 1;\n      transform: scale(1);\n    }\n  }\n\n  .Modal__close {\n    color: var(--icon-fill-color);\n    margin: 0;\n    padding: 0.375rem;\n    position: absolute;\n    top: 1rem;\n    right: 1rem;\n    border: 0;\n    background-color: transparent;\n    line-height: 0;\n    cursor: pointer;\n\n    svg {\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n  }\n\n  .Dialog--fullscreen {\n    .Modal {\n      padding: 0;\n    }\n\n    .Modal__content {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      max-width: 100%;\n      border: 0;\n      border-radius: 0;\n    }\n  }\n}\n", ".excalidraw {\n  .Stack {\n    --gap: 0;\n    display: grid;\n    gap: calc(var(--space-factor) * var(--gap));\n  }\n\n  .Stack_vertical {\n    grid-template-columns: auto;\n    grid-auto-flow: row;\n    grid-auto-rows: min-content;\n  }\n\n  .Stack_horizontal {\n    grid-template-rows: auto;\n    grid-auto-flow: column;\n    grid-auto-columns: min-content;\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .confirm-dialog {\n    &-buttons {\n      display: flex;\n      column-gap: 0.5rem;\n      justify-content: flex-end;\n    }\n  }\n}\n", ".excalidraw {\n  .Dialog__action-button {\n    position: relative;\n    display: flex;\n    column-gap: 0.5rem;\n    align-items: center;\n    padding: 0.5rem 1.5rem;\n    border: 1px solid var(--default-border-color);\n    background-color: transparent;\n    height: 3rem;\n    border-radius: var(--border-radius-lg);\n    letter-spacing: 0.4px;\n    color: inherit;\n    font-family: inherit;\n    font-size: 0.875rem;\n    font-weight: 600;\n    user-select: none;\n\n    svg {\n      display: block;\n      width: 1rem;\n      height: 1rem;\n    }\n\n    &--danger {\n      background-color: var(--color-danger);\n      border-color: var(--color-danger);\n      color: #fff;\n    }\n\n    &--primary {\n      background-color: var(--color-primary);\n      border-color: var(--color-primary);\n      color: #fff;\n    }\n  }\n\n  &.theme--dark {\n    .Dialog__action-button--danger {\n      color: var(--color-gray-100);\n    }\n\n    .Dialog__action-button--primary {\n      color: var(--color-gray-100);\n    }\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .publish-library {\n    &__fields {\n      display: flex;\n      flex-direction: column;\n\n      label {\n        padding: 1em 0;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        span {\n          font-weight: 500;\n          font-size: 1rem;\n          color: $oc-gray-6;\n        }\n        input,\n        textarea {\n          width: 70%;\n          padding: 0.6em;\n          font-family: var(--ui-font);\n        }\n\n        .required {\n          color: $oc-red-8;\n          margin: 0.2rem;\n        }\n      }\n    }\n\n    &__buttons {\n      display: flex;\n      padding: 0.2rem 0;\n      justify-content: flex-end;\n      gap: 0.5rem;\n\n      .ToolIcon__icon {\n        min-width: 2.5rem;\n        width: auto;\n        font-size: 1rem;\n      }\n\n      .ToolIcon_type_button {\n        margin-left: 1rem;\n        padding: 0 0.5rem;\n      }\n\n      &--confirm.ToolIcon_type_button {\n        background-color: $oc-blue-6;\n\n        &:hover {\n          background-color: $oc-blue-8;\n        }\n      }\n\n      &--cancel.ToolIcon_type_button {\n        background-color: $oc-gray-5;\n        &:hover {\n          background-color: $oc-gray-6;\n        }\n      }\n\n      .ToolIcon__icon {\n        color: $oc-white;\n        .Spinner {\n          --spinner-color: #fff;\n          svg {\n            padding: 0.5rem;\n          }\n        }\n      }\n    }\n\n    .selected-library-items {\n      display: flex;\n      flex-wrap: wrap;\n\n      .single-library-item-wrapper {\n        width: 9rem;\n      }\n    }\n\n    &-warning {\n      color: $oc-red-6;\n    }\n\n    &-note {\n      padding: 1em 0;\n      font-style: italic;\n      font-size: 14px;\n      display: block;\n    }\n  }\n\n  .single-library-item {\n    position: relative;\n\n    &-status {\n      position: absolute;\n      top: 0.3rem;\n      left: 0.3rem;\n      font-size: 0.7rem;\n      color: $oc-red-7;\n      background: rgba(255, 255, 255, 0.9);\n      padding: 0.1rem 0.2rem;\n      border-radius: 0.2rem;\n    }\n\n    &__svg {\n      background-color: $oc-white;\n      padding: 0.3rem;\n      width: 7.5rem;\n      height: 7.5rem;\n      border: 1px solid var(--button-gray-2);\n      svg {\n        width: 100%;\n        height: 100%;\n      }\n    }\n\n    .ToolIcon__icon {\n      background-color: $oc-white;\n      width: auto;\n      height: auto;\n      margin: 0 0.5rem;\n    }\n    .ToolIcon,\n    .ToolIcon_type_button:hover {\n      background-color: white;\n    }\n    .required,\n    .error {\n      color: $oc-red-8;\n      font-weight: 700;\n      font-size: 1rem;\n      margin: 0.2rem;\n    }\n    .error {\n      font-weight: 500;\n      margin: 0;\n      padding: 0.3em 0;\n    }\n\n    &--remove {\n      position: absolute;\n      top: 0.2rem;\n      right: 1rem;\n\n      .ToolIcon__icon {\n        margin: 0;\n      }\n      .ToolIcon__icon {\n        background-color: $oc-red-6;\n        &:hover {\n          background-color: $oc-red-7;\n        }\n        &:active {\n          background-color: $oc-red-8;\n        }\n      }\n      svg {\n        color: $oc-white;\n        padding: 0.26rem;\n        border-radius: 0.3em;\n        width: 1rem;\n        height: 1rem;\n      }\n    }\n  }\n}\n", "//\n//\n//  𝗖 𝗢 𝗟 𝗢 𝗥\n//  v 1.9.1\n//\n//  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n\n//  General\n//  ───────────────────────────────────\n\n$oc-white:         #ffffff;\n$oc-black:         #000000;\n\n\n//  Gray\n//  ───────────────────────────────────\n\n$oc-gray-list: (\n  \"0\": #f8f9fa,\n  \"1\": #f1f3f5,\n  \"2\": #e9ecef,\n  \"3\": #dee2e6,\n  \"4\": #ced4da,\n  \"5\": #adb5bd,\n  \"6\": #868e96,\n  \"7\": #495057,\n  \"8\": #343a40,\n  \"9\": #212529\n);\n\n$oc-gray-0: map-get($oc-gray-list, \"0\");\n$oc-gray-1: map-get($oc-gray-list, \"1\");\n$oc-gray-2: map-get($oc-gray-list, \"2\");\n$oc-gray-3: map-get($oc-gray-list, \"3\");\n$oc-gray-4: map-get($oc-gray-list, \"4\");\n$oc-gray-5: map-get($oc-gray-list, \"5\");\n$oc-gray-6: map-get($oc-gray-list, \"6\");\n$oc-gray-7: map-get($oc-gray-list, \"7\");\n$oc-gray-8: map-get($oc-gray-list, \"8\");\n$oc-gray-9: map-get($oc-gray-list, \"9\");\n\n\n//  Red\n//  ───────────────────────────────────\n\n$oc-red-list: (\n  \"0\": #fff5f5,\n  \"1\": #ffe3e3,\n  \"2\": #ffc9c9,\n  \"3\": #ffa8a8,\n  \"4\": #ff8787,\n  \"5\": #ff6b6b,\n  \"6\": #fa5252,\n  \"7\": #f03e3e,\n  \"8\": #e03131,\n  \"9\": #c92a2a\n);\n\n$oc-red-0: map-get($oc-red-list, \"0\");\n$oc-red-1: map-get($oc-red-list, \"1\");\n$oc-red-2: map-get($oc-red-list, \"2\");\n$oc-red-3: map-get($oc-red-list, \"3\");\n$oc-red-4: map-get($oc-red-list, \"4\");\n$oc-red-5: map-get($oc-red-list, \"5\");\n$oc-red-6: map-get($oc-red-list, \"6\");\n$oc-red-7: map-get($oc-red-list, \"7\");\n$oc-red-8: map-get($oc-red-list, \"8\");\n$oc-red-9: map-get($oc-red-list, \"9\");\n\n\n//  Pink\n//  ───────────────────────────────────\n\n$oc-pink-list: (\n  \"0\": #fff0f6,\n  \"1\": #ffdeeb,\n  \"2\": #fcc2d7,\n  \"3\": #faa2c1,\n  \"4\": #f783ac,\n  \"5\": #f06595,\n  \"6\": #e64980,\n  \"7\": #d6336c,\n  \"8\": #c2255c,\n  \"9\": #a61e4d\n);\n\n$oc-pink-0: map-get($oc-pink-list, \"0\");\n$oc-pink-1: map-get($oc-pink-list, \"1\");\n$oc-pink-2: map-get($oc-pink-list, \"2\");\n$oc-pink-3: map-get($oc-pink-list, \"3\");\n$oc-pink-4: map-get($oc-pink-list, \"4\");\n$oc-pink-5: map-get($oc-pink-list, \"5\");\n$oc-pink-6: map-get($oc-pink-list, \"6\");\n$oc-pink-7: map-get($oc-pink-list, \"7\");\n$oc-pink-8: map-get($oc-pink-list, \"8\");\n$oc-pink-9: map-get($oc-pink-list, \"9\");\n\n\n//  Grape\n//  ───────────────────────────────────\n\n$oc-grape-list: (\n  \"0\": #f8f0fc,\n  \"1\": #f3d9fa,\n  \"2\": #eebefa,\n  \"3\": #e599f7,\n  \"4\": #da77f2,\n  \"5\": #cc5de8,\n  \"6\": #be4bdb,\n  \"7\": #ae3ec9,\n  \"8\": #9c36b5,\n  \"9\": #862e9c\n);\n\n$oc-grape-0: map-get($oc-grape-list, \"0\");\n$oc-grape-1: map-get($oc-grape-list, \"1\");\n$oc-grape-2: map-get($oc-grape-list, \"2\");\n$oc-grape-3: map-get($oc-grape-list, \"3\");\n$oc-grape-4: map-get($oc-grape-list, \"4\");\n$oc-grape-5: map-get($oc-grape-list, \"5\");\n$oc-grape-6: map-get($oc-grape-list, \"6\");\n$oc-grape-7: map-get($oc-grape-list, \"7\");\n$oc-grape-8: map-get($oc-grape-list, \"8\");\n$oc-grape-9: map-get($oc-grape-list, \"9\");\n\n\n//  Violet\n//  ───────────────────────────────────\n\n$oc-violet-list: (\n  \"0\": #f3f0ff,\n  \"1\": #e5dbff,\n  \"2\": #d0bfff,\n  \"3\": #b197fc,\n  \"4\": #9775fa,\n  \"5\": #845ef7,\n  \"6\": #7950f2,\n  \"7\": #7048e8,\n  \"8\": #6741d9,\n  \"9\": #5f3dc4\n);\n\n$oc-violet-0: map-get($oc-violet-list, \"0\");\n$oc-violet-1: map-get($oc-violet-list, \"1\");\n$oc-violet-2: map-get($oc-violet-list, \"2\");\n$oc-violet-3: map-get($oc-violet-list, \"3\");\n$oc-violet-4: map-get($oc-violet-list, \"4\");\n$oc-violet-5: map-get($oc-violet-list, \"5\");\n$oc-violet-6: map-get($oc-violet-list, \"6\");\n$oc-violet-7: map-get($oc-violet-list, \"7\");\n$oc-violet-8: map-get($oc-violet-list, \"8\");\n$oc-violet-9: map-get($oc-violet-list, \"9\");\n\n\n//  Indigo\n//  ───────────────────────────────────\n\n$oc-indigo-list: (\n  \"0\": #edf2ff,\n  \"1\": #dbe4ff,\n  \"2\": #bac8ff,\n  \"3\": #91a7ff,\n  \"4\": #748ffc,\n  \"5\": #5c7cfa,\n  \"6\": #4c6ef5,\n  \"7\": #4263eb,\n  \"8\": #3b5bdb,\n  \"9\": #364fc7\n);\n\n$oc-indigo-0: map-get($oc-indigo-list, \"0\");\n$oc-indigo-1: map-get($oc-indigo-list, \"1\");\n$oc-indigo-2: map-get($oc-indigo-list, \"2\");\n$oc-indigo-3: map-get($oc-indigo-list, \"3\");\n$oc-indigo-4: map-get($oc-indigo-list, \"4\");\n$oc-indigo-5: map-get($oc-indigo-list, \"5\");\n$oc-indigo-6: map-get($oc-indigo-list, \"6\");\n$oc-indigo-7: map-get($oc-indigo-list, \"7\");\n$oc-indigo-8: map-get($oc-indigo-list, \"8\");\n$oc-indigo-9: map-get($oc-indigo-list, \"9\");\n\n\n//  Blue\n//  ───────────────────────────────────\n\n$oc-blue-list: (\n  \"0\": #e7f5ff,\n  \"1\": #d0ebff,\n  \"2\": #a5d8ff,\n  \"3\": #74c0fc,\n  \"4\": #4dabf7,\n  \"5\": #339af0,\n  \"6\": #228be6,\n  \"7\": #1c7ed6,\n  \"8\": #1971c2,\n  \"9\": #1864ab\n);\n\n$oc-blue-0: map-get($oc-blue-list, \"0\");\n$oc-blue-1: map-get($oc-blue-list, \"1\");\n$oc-blue-2: map-get($oc-blue-list, \"2\");\n$oc-blue-3: map-get($oc-blue-list, \"3\");\n$oc-blue-4: map-get($oc-blue-list, \"4\");\n$oc-blue-5: map-get($oc-blue-list, \"5\");\n$oc-blue-6: map-get($oc-blue-list, \"6\");\n$oc-blue-7: map-get($oc-blue-list, \"7\");\n$oc-blue-8: map-get($oc-blue-list, \"8\");\n$oc-blue-9: map-get($oc-blue-list, \"9\");\n\n\n//  Cyan\n//  ───────────────────────────────────\n\n$oc-cyan-list: (\n  \"0\": #e3fafc,\n  \"1\": #c5f6fa,\n  \"2\": #99e9f2,\n  \"3\": #66d9e8,\n  \"4\": #3bc9db,\n  \"5\": #22b8cf,\n  \"6\": #15aabf,\n  \"7\": #1098ad,\n  \"8\": #0c8599,\n  \"9\": #0b7285\n);\n\n$oc-cyan-0: map-get($oc-cyan-list, \"0\");\n$oc-cyan-1: map-get($oc-cyan-list, \"1\");\n$oc-cyan-2: map-get($oc-cyan-list, \"2\");\n$oc-cyan-3: map-get($oc-cyan-list, \"3\");\n$oc-cyan-4: map-get($oc-cyan-list, \"4\");\n$oc-cyan-5: map-get($oc-cyan-list, \"5\");\n$oc-cyan-6: map-get($oc-cyan-list, \"6\");\n$oc-cyan-7: map-get($oc-cyan-list, \"7\");\n$oc-cyan-8: map-get($oc-cyan-list, \"8\");\n$oc-cyan-9: map-get($oc-cyan-list, \"9\");\n\n\n//  Teal\n//  ───────────────────────────────────\n\n$oc-teal-list: (\n  \"0\": #e6fcf5,\n  \"1\": #c3fae8,\n  \"2\": #96f2d7,\n  \"3\": #63e6be,\n  \"4\": #38d9a9,\n  \"5\": #20c997,\n  \"6\": #12b886,\n  \"7\": #0ca678,\n  \"8\": #099268,\n  \"9\": #087f5b\n);\n\n$oc-teal-0: map-get($oc-teal-list, \"0\");\n$oc-teal-1: map-get($oc-teal-list, \"1\");\n$oc-teal-2: map-get($oc-teal-list, \"2\");\n$oc-teal-3: map-get($oc-teal-list, \"3\");\n$oc-teal-4: map-get($oc-teal-list, \"4\");\n$oc-teal-5: map-get($oc-teal-list, \"5\");\n$oc-teal-6: map-get($oc-teal-list, \"6\");\n$oc-teal-7: map-get($oc-teal-list, \"7\");\n$oc-teal-8: map-get($oc-teal-list, \"8\");\n$oc-teal-9: map-get($oc-teal-list, \"9\");\n\n\n//  Green\n//  ───────────────────────────────────\n\n$oc-green-list: (\n  \"0\": #ebfbee,\n  \"1\": #d3f9d8,\n  \"2\": #b2f2bb,\n  \"3\": #8ce99a,\n  \"4\": #69db7c,\n  \"5\": #51cf66,\n  \"6\": #40c057,\n  \"7\": #37b24d,\n  \"8\": #2f9e44,\n  \"9\": #2b8a3e\n);\n\n$oc-green-0: map-get($oc-green-list, \"0\");\n$oc-green-1: map-get($oc-green-list, \"1\");\n$oc-green-2: map-get($oc-green-list, \"2\");\n$oc-green-3: map-get($oc-green-list, \"3\");\n$oc-green-4: map-get($oc-green-list, \"4\");\n$oc-green-5: map-get($oc-green-list, \"5\");\n$oc-green-6: map-get($oc-green-list, \"6\");\n$oc-green-7: map-get($oc-green-list, \"7\");\n$oc-green-8: map-get($oc-green-list, \"8\");\n$oc-green-9: map-get($oc-green-list, \"9\");\n\n\n//  Lime\n//  ───────────────────────────────────\n\n$oc-lime-list: (\n  \"0\": #f4fce3,\n  \"1\": #e9fac8,\n  \"2\": #d8f5a2,\n  \"3\": #c0eb75,\n  \"4\": #a9e34b,\n  \"5\": #94d82d,\n  \"6\": #82c91e,\n  \"7\": #74b816,\n  \"8\": #66a80f,\n  \"9\": #5c940d\n);\n\n$oc-lime-0: map-get($oc-lime-list, \"0\");\n$oc-lime-1: map-get($oc-lime-list, \"1\");\n$oc-lime-2: map-get($oc-lime-list, \"2\");\n$oc-lime-3: map-get($oc-lime-list, \"3\");\n$oc-lime-4: map-get($oc-lime-list, \"4\");\n$oc-lime-5: map-get($oc-lime-list, \"5\");\n$oc-lime-6: map-get($oc-lime-list, \"6\");\n$oc-lime-7: map-get($oc-lime-list, \"7\");\n$oc-lime-8: map-get($oc-lime-list, \"8\");\n$oc-lime-9: map-get($oc-lime-list, \"9\");\n\n\n//  Yellow\n//  ───────────────────────────────────\n\n$oc-yellow-list: (\n  \"0\": #fff9db,\n  \"1\": #fff3bf,\n  \"2\": #ffec99,\n  \"3\": #ffe066,\n  \"4\": #ffd43b,\n  \"5\": #fcc419,\n  \"6\": #fab005,\n  \"7\": #f59f00,\n  \"8\": #f08c00,\n  \"9\": #e67700\n);\n\n$oc-yellow-0: map-get($oc-yellow-list, \"0\");\n$oc-yellow-1: map-get($oc-yellow-list, \"1\");\n$oc-yellow-2: map-get($oc-yellow-list, \"2\");\n$oc-yellow-3: map-get($oc-yellow-list, \"3\");\n$oc-yellow-4: map-get($oc-yellow-list, \"4\");\n$oc-yellow-5: map-get($oc-yellow-list, \"5\");\n$oc-yellow-6: map-get($oc-yellow-list, \"6\");\n$oc-yellow-7: map-get($oc-yellow-list, \"7\");\n$oc-yellow-8: map-get($oc-yellow-list, \"8\");\n$oc-yellow-9: map-get($oc-yellow-list, \"9\");\n\n\n//  Orange\n//  ───────────────────────────────────\n\n$oc-orange-list: (\n  \"0\": #fff4e6,\n  \"1\": #ffe8cc,\n  \"2\": #ffd8a8,\n  \"3\": #ffc078,\n  \"4\": #ffa94d,\n  \"5\": #ff922b,\n  \"6\": #fd7e14,\n  \"7\": #f76707,\n  \"8\": #e8590c,\n  \"9\": #d9480f\n);\n\n$oc-orange-0: map-get($oc-orange-list, \"0\");\n$oc-orange-1: map-get($oc-orange-list, \"1\");\n$oc-orange-2: map-get($oc-orange-list, \"2\");\n$oc-orange-3: map-get($oc-orange-list, \"3\");\n$oc-orange-4: map-get($oc-orange-list, \"4\");\n$oc-orange-5: map-get($oc-orange-list, \"5\");\n$oc-orange-6: map-get($oc-orange-list, \"6\");\n$oc-orange-7: map-get($oc-orange-list, \"7\");\n$oc-orange-8: map-get($oc-orange-list, \"8\");\n$oc-orange-9: map-get($oc-orange-list, \"9\");\n\n\n//  Color list\n//  ───────────────────────────────────\n\n$oc-color-spectrum:   9;\n\n$oc-color-list: (\n  $oc-gray-list:      \"gray\",\n  $oc-red-list:      \"red\",\n  $oc-pink-list:      \"pink\",\n  $oc-grape-list:      \"grape\",\n  $oc-violet-list:      \"violet\",\n  $oc-indigo-list:      \"indigo\",\n  $oc-blue-list:      \"blue\",\n  $oc-cyan-list:      \"cyan\",\n  $oc-teal-list:      \"teal\",\n  $oc-green-list:      \"green\",\n  $oc-lime-list:      \"lime\",\n  $oc-yellow-list:      \"yellow\",\n  $oc-orange-list:      \"orange\"\n);\n", "@import \"../../css/variables.module.scss\";\n\n.excalidraw {\n  .dropdown-menu {\n    position: absolute;\n    top: 100%;\n    margin-top: 0.5rem;\n\n    &--mobile {\n      left: 0;\n      width: 100%;\n      row-gap: 0.75rem;\n\n      .dropdown-menu-container {\n        padding: 8px 8px;\n        box-sizing: border-box;\n        // background-color: var(--island-bg-color);\n        box-shadow: var(--shadow-island);\n        border-radius: var(--border-radius-lg);\n        position: relative;\n        transition: box-shadow 0.5s ease-in-out;\n\n        &.zen-mode {\n          box-shadow: none;\n        }\n      }\n    }\n\n    .dropdown-menu-container {\n      background-color: var(--island-bg-color);\n      max-height: calc(100vh - 150px);\n      overflow-y: auto;\n      --gap: 2;\n    }\n\n    .dropdown-menu-item-base {\n      display: flex;\n      column-gap: 0.625rem;\n      font-size: 0.875rem;\n      color: var(--color-on-surface);\n      width: 100%;\n      box-sizing: border-box;\n      font-weight: 400;\n      font-family: inherit;\n    }\n\n    &.manual-hover {\n      // disable built-in hover due to keyboard navigation\n      .dropdown-menu-item {\n        &:hover {\n          background-color: transparent;\n        }\n\n        &--hovered {\n          background-color: var(--button-hover-bg) !important;\n        }\n\n        &--selected {\n          background-color: var(--color-primary-light) !important;\n        }\n      }\n    }\n\n    &.fonts {\n      margin-top: 1rem;\n      // display max 7 items per list, where each has 2rem (2.25) height and 1px margin top & bottom\n      // count in 2 groups, where each allocates 1.3*0.75rem font-size and 0.5rem margin bottom, plus one extra 1rem margin top\n      max-height: calc(7 * (2rem + 2px) + 2 * (0.5rem + 1.3 * 0.75rem) + 1rem);\n\n      @media screen and (min-width: 1921px) {\n        max-height: calc(\n          7 * (2.25rem + 2px) + 2 * (0.5rem + 1.3 * 0.75rem) + 1rem\n        );\n      }\n\n      .dropdown-menu-item-base {\n        display: inline-flex;\n      }\n\n      .dropdown-menu-group:not(:first-child) {\n        margin-top: 1rem;\n      }\n\n      .dropdown-menu-group-title {\n        font-size: 0.75rem;\n        text-align: left;\n        font-weight: 400;\n        margin: 0 0 0.5rem;\n        line-height: 1.3;\n      }\n    }\n\n    .dropdown-menu-item {\n      height: 2rem;\n      margin: 1px;\n      padding: 0 0.5rem;\n      width: calc(100% - 2px);\n      background-color: transparent;\n      border: 1px solid transparent;\n      align-items: center;\n      cursor: pointer;\n      border-radius: var(--border-radius-md);\n\n      @media screen and (min-width: 1921px) {\n        height: 2.25rem;\n      }\n\n      &__text {\n        display: flex;\n        align-items: center;\n        width: 100%;\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap;\n        gap: 0.75rem;\n      }\n\n      &__shortcut {\n        margin-inline-start: auto;\n        opacity: 0.5;\n\n        &--orphaned {\n          text-align: right;\n          font-size: 0.875rem;\n          padding: 0 0.625rem;\n        }\n      }\n\n      &--selected {\n        background: var(--color-primary-light);\n        --icon-fill-color: var(--color-primary-darker);\n      }\n\n      &:hover {\n        background-color: var(--button-hover-bg);\n        text-decoration: none;\n      }\n\n      &:active {\n        background-color: var(--button-hover-bg);\n        border-color: var(--color-brand-active);\n      }\n\n      svg {\n        width: 1rem;\n        height: 1rem;\n        display: block;\n      }\n    }\n\n    .dropdown-menu-item-bare {\n      align-items: center;\n      height: 2rem;\n      justify-content: space-between;\n\n      @media screen and (min-width: 1921px) {\n        height: 2.25rem;\n      }\n\n      svg {\n        width: 1rem;\n        height: 1rem;\n        display: block;\n      }\n    }\n\n    .dropdown-menu-item-custom {\n      margin-top: 0.5rem;\n    }\n\n    .dropdown-menu-group-title {\n      font-size: 14px;\n      text-align: left;\n      margin: 10px 0;\n      font-weight: 500;\n    }\n  }\n\n  .dropdown-menu-button {\n    @include outlineButtonStyles;\n    width: var(--lg-button-size);\n    height: var(--lg-button-size);\n\n    --background: var(--color-surface-mid);\n\n    background-color: var(--background);\n\n    @at-root .excalidraw.theme--dark#{&} {\n      --background: var(--color-surface-high);\n      &:hover {\n        --background: #363541;\n      }\n    }\n\n    &:hover {\n      --background: var(--color-surface-high);\n      background-color: var(--background);\n      text-decoration: none;\n    }\n\n    &:active {\n      border-color: var(--color-primary);\n    }\n\n    svg {\n      width: var(--lg-icon-size);\n      height: var(--lg-icon-size);\n    }\n\n    &--mobile {\n      border: none;\n      margin: 0;\n      padding: 0;\n      width: var(--default-button-size);\n      height: var(--default-button-size);\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .library-unit {\n    align-items: center;\n    border: 1px solid transparent;\n    display: flex;\n    justify-content: center;\n    position: relative;\n    width: 55px;\n    height: 55px;\n    box-sizing: border-box;\n    border-radius: var(--border-radius-lg);\n\n    svg {\n      // to prevent clicks on links and such\n      pointer-events: none;\n    }\n\n    &--hover {\n      border-color: var(--color-primary);\n    }\n\n    &--selected {\n      border-color: var(--color-primary);\n      border-width: 1px;\n    }\n\n    &--skeleton {\n      opacity: 0.5;\n      background: linear-gradient(\n        -45deg,\n        var(--color-gray-10),\n        var(--color-gray-20),\n        var(--color-gray-10)\n      );\n      background-size: 200% 200%;\n      animation: library-unit__skeleton-opacity-animation 0.2s linear;\n    }\n  }\n\n  &.theme--dark .library-unit--skeleton {\n    background-image: linear-gradient(\n      -45deg,\n      var(--color-gray-100),\n      var(--color-gray-80),\n      var(--color-gray-100)\n    );\n  }\n\n  .library-unit__dragger {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n    width: 100%;\n  }\n\n  .library-unit__dragger > svg {\n    filter: var(--theme-filter);\n    flex-grow: 1;\n    max-height: 100%;\n    max-width: 100%;\n  }\n\n  .library-unit__checkbox-container,\n  .library-unit__checkbox-container:hover,\n  .library-unit__checkbox-container:active {\n    align-items: center;\n    background: none;\n    border: none;\n    color: var(--icon-fill-color);\n    display: flex;\n    justify-content: center;\n    margin: 0;\n    padding: 0.5rem;\n    position: absolute;\n    left: 2rem;\n    bottom: 2rem;\n    cursor: pointer;\n\n    input {\n      cursor: pointer;\n    }\n  }\n\n  .library-unit__checkbox {\n    position: absolute;\n    top: 0.125rem;\n    right: 0.125rem;\n    margin: 0;\n\n    .Checkbox-box {\n      margin: 0;\n      width: 1rem;\n      height: 1rem;\n      border-radius: 4px;\n      background-color: var(--color-primary-light);\n      border: 1px solid var(--color-primary);\n      box-shadow: none !important;\n      padding: 2px;\n    }\n\n    &.Checkbox:hover {\n      .Checkbox-box {\n        background-color: var(--color-primary-light);\n      }\n    }\n\n    &.is-checked {\n      .Checkbox-box {\n        background-color: var(--color-primary) !important;\n\n        svg {\n          color: var(--color-primary-light);\n        }\n      }\n    }\n  }\n\n  .library-unit__removeFromLibrary > svg {\n    height: 16px;\n    width: 16px;\n  }\n\n  .library-unit__adder {\n    transform: scale(1);\n    animation: library-unit__adder-animation 1s ease-in infinite;\n\n    position: absolute;\n    width: 1.5rem;\n    height: 1.5rem;\n    background-color: var(--color-primary);\n    border-radius: var(--border-radius-md);\n\n    display: flex;\n    justify-content: center;\n    align-items: center;\n\n    pointer-events: none;\n\n    svg {\n      color: var(--color-primary-light);\n      width: 1rem;\n      height: 1rem;\n    }\n  }\n\n  .library-unit:active .library-unit__adder {\n    animation: none;\n    transform: scale(0.8);\n  }\n\n  .library-unit__active {\n    cursor: pointer;\n  }\n\n  @keyframes library-unit__adder-animation {\n    0% {\n      transform: scale(0.85);\n    }\n\n    50% {\n      transform: scale(1);\n    }\n\n    100% {\n      transform: scale(0.85);\n    }\n  }\n\n  @keyframes library-unit__skeleton-opacity-animation {\n    0% {\n      opacity: 0;\n    }\n\n    75% {\n      opacity: 0;\n    }\n\n    100% {\n      opacity: 0.5;\n    }\n  }\n}\n", "@import \"open-color/open-color\";\n\n.excalidraw {\n  --container-padding-y: 1.5rem;\n  --container-padding-x: 0.75rem;\n\n  .library-menu-items__no-items {\n    text-align: center;\n    color: var(--color-gray-70);\n    line-height: 1.5;\n    font-size: 0.875rem;\n    width: 100%;\n\n    &__label {\n      color: var(--color-primary);\n      font-weight: 700;\n      font-size: 1.125rem;\n      margin-bottom: 0.75rem;\n    }\n  }\n\n  &.theme--dark {\n    .library-menu-items__no-items {\n      color: var(--color-gray-40);\n    }\n  }\n\n  .library-menu-items-container {\n    width: 100%;\n    display: flex;\n    flex-grow: 1;\n    flex-shrink: 1;\n    flex-basis: 0;\n    overflow-y: auto;\n    flex-direction: column;\n    height: 100%;\n    justify-content: center;\n    margin: 0;\n\n    position: relative;\n\n    & > div {\n      padding-left: 0.75rem;\n      padding-right: 0.75rem;\n    }\n\n    &__row {\n      display: grid;\n      grid-template-columns: repeat(4, 1fr);\n      gap: 1rem;\n    }\n\n    &__items {\n      row-gap: 0.5rem;\n      padding: var(--container-padding-y) 0;\n      flex: 1;\n      overflow-y: auto;\n      overflow-x: hidden;\n      margin-bottom: 1rem;\n    }\n\n    &__header {\n      color: var(--color-primary);\n      font-size: 1.125rem;\n      font-weight: 700;\n      margin-bottom: 0.75rem;\n      width: 100%;\n      padding-right: 4rem; // due to dropdown button\n      box-sizing: border-box;\n\n      &--excal {\n        margin-top: 2rem;\n      }\n    }\n\n    &__grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr 1fr 1fr;\n      grid-gap: 1rem;\n    }\n\n    .separator {\n      width: 100%;\n      display: flex;\n      align-items: center;\n      font-weight: 500;\n      font-size: 0.9rem;\n      margin: 0.6em 0.2em;\n      color: var(--text-primary-color);\n    }\n  }\n\n  .library-menu-items-private-library-container {\n    // so that when you toggle between pending item and no items, there's\n    // no layout shift (this is hardcoded and works only with ENG locale)\n    min-height: 3.75rem;\n    width: 100%;\n  }\n}\n", "@import \"open-color/open-color\";\n\n.excalidraw {\n  .layer-ui__library {\n    display: flex;\n    flex-direction: column;\n\n    flex: 1 1 auto;\n  }\n\n  .library-actions-counter {\n    background-color: var(--color-primary);\n    color: var(--color-primary-light);\n    font-weight: 700;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 50%;\n    width: 1rem;\n    height: 1rem;\n    position: absolute;\n    bottom: -0.25rem;\n    right: -0.25rem;\n    font-size: 0.625rem;\n    pointer-events: none;\n  }\n\n  .layer-ui__library-message {\n    padding: 2rem;\n    min-width: 200px;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    flex-grow: 1;\n    justify-content: center;\n\n    span {\n      font-size: 0.8em;\n    }\n  }\n\n  .publish-library-success {\n    .Dialog__content {\n      display: flex;\n      flex-direction: column;\n    }\n\n    &-close.ToolIcon_type_button {\n      background-color: $oc-blue-6;\n      align-self: flex-end;\n      &:hover {\n        background-color: $oc-blue-8;\n      }\n      .ToolIcon__icon {\n        width: auto;\n        font-size: 1rem;\n        color: $oc-white;\n        padding: 0 0.5rem;\n      }\n    }\n  }\n\n  .library-menu-control-buttons {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 0.625rem;\n    position: relative;\n\n    &--at-bottom::before {\n      content: \"\";\n      width: calc(100% - 1.5rem);\n      height: 1px;\n      position: absolute;\n      top: -1px;\n      background: var(--sidebar-border-color);\n    }\n  }\n\n  .library-menu-browse-button {\n    flex: 1;\n\n    height: var(--lg-button-size);\n\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    overflow: hidden;\n    position: relative;\n\n    border-radius: var(--border-radius-lg);\n    background-color: var(--color-primary);\n    color: $oc-white;\n    text-align: center;\n    white-space: nowrap;\n    text-decoration: none !important;\n\n    font-weight: 600;\n    font-size: 0.75rem;\n\n    &:hover {\n      background-color: var(--color-brand-hover);\n    }\n    &:active {\n      background-color: var(--color-brand-active);\n    }\n  }\n\n  &.theme--dark {\n    .library-menu-browse-button {\n      color: var(--color-gray-100);\n    }\n  }\n\n  &.excalidraw--mobile .library-menu-browse-button {\n    height: var(--default-button-size);\n  }\n\n  .layer-ui__library .dropdown-menu {\n    width: auto;\n    top: initial;\n    right: 0;\n    left: initial;\n    bottom: 100%;\n    margin-bottom: 0.625rem;\n\n    .dropdown-menu-container {\n      width: 196px;\n      box-shadow: var(--library-dropdown-shadow);\n      border-radius: var(--border-radius-lg);\n      padding: 0.25rem 0.5rem;\n    }\n  }\n\n  .layer-ui__library .library-menu-dropdown-container {\n    position: relative;\n\n    &--in-heading {\n      padding: 0;\n      position: absolute;\n      top: 1rem;\n      right: 0.75rem;\n      z-index: 1;\n\n      .dropdown-menu {\n        top: 100%;\n      }\n    }\n  }\n}\n", "//\n//\n//  𝗖 𝗢 𝗟 𝗢 𝗥\n//  v 1.9.1\n//\n//  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n\n//  General\n//  ───────────────────────────────────\n\n$oc-white:         #ffffff;\n$oc-black:         #000000;\n\n\n//  Gray\n//  ───────────────────────────────────\n\n$oc-gray-list: (\n  \"0\": #f8f9fa,\n  \"1\": #f1f3f5,\n  \"2\": #e9ecef,\n  \"3\": #dee2e6,\n  \"4\": #ced4da,\n  \"5\": #adb5bd,\n  \"6\": #868e96,\n  \"7\": #495057,\n  \"8\": #343a40,\n  \"9\": #212529\n);\n\n$oc-gray-0: map-get($oc-gray-list, \"0\");\n$oc-gray-1: map-get($oc-gray-list, \"1\");\n$oc-gray-2: map-get($oc-gray-list, \"2\");\n$oc-gray-3: map-get($oc-gray-list, \"3\");\n$oc-gray-4: map-get($oc-gray-list, \"4\");\n$oc-gray-5: map-get($oc-gray-list, \"5\");\n$oc-gray-6: map-get($oc-gray-list, \"6\");\n$oc-gray-7: map-get($oc-gray-list, \"7\");\n$oc-gray-8: map-get($oc-gray-list, \"8\");\n$oc-gray-9: map-get($oc-gray-list, \"9\");\n\n\n//  Red\n//  ───────────────────────────────────\n\n$oc-red-list: (\n  \"0\": #fff5f5,\n  \"1\": #ffe3e3,\n  \"2\": #ffc9c9,\n  \"3\": #ffa8a8,\n  \"4\": #ff8787,\n  \"5\": #ff6b6b,\n  \"6\": #fa5252,\n  \"7\": #f03e3e,\n  \"8\": #e03131,\n  \"9\": #c92a2a\n);\n\n$oc-red-0: map-get($oc-red-list, \"0\");\n$oc-red-1: map-get($oc-red-list, \"1\");\n$oc-red-2: map-get($oc-red-list, \"2\");\n$oc-red-3: map-get($oc-red-list, \"3\");\n$oc-red-4: map-get($oc-red-list, \"4\");\n$oc-red-5: map-get($oc-red-list, \"5\");\n$oc-red-6: map-get($oc-red-list, \"6\");\n$oc-red-7: map-get($oc-red-list, \"7\");\n$oc-red-8: map-get($oc-red-list, \"8\");\n$oc-red-9: map-get($oc-red-list, \"9\");\n\n\n//  Pink\n//  ───────────────────────────────────\n\n$oc-pink-list: (\n  \"0\": #fff0f6,\n  \"1\": #ffdeeb,\n  \"2\": #fcc2d7,\n  \"3\": #faa2c1,\n  \"4\": #f783ac,\n  \"5\": #f06595,\n  \"6\": #e64980,\n  \"7\": #d6336c,\n  \"8\": #c2255c,\n  \"9\": #a61e4d\n);\n\n$oc-pink-0: map-get($oc-pink-list, \"0\");\n$oc-pink-1: map-get($oc-pink-list, \"1\");\n$oc-pink-2: map-get($oc-pink-list, \"2\");\n$oc-pink-3: map-get($oc-pink-list, \"3\");\n$oc-pink-4: map-get($oc-pink-list, \"4\");\n$oc-pink-5: map-get($oc-pink-list, \"5\");\n$oc-pink-6: map-get($oc-pink-list, \"6\");\n$oc-pink-7: map-get($oc-pink-list, \"7\");\n$oc-pink-8: map-get($oc-pink-list, \"8\");\n$oc-pink-9: map-get($oc-pink-list, \"9\");\n\n\n//  Grape\n//  ───────────────────────────────────\n\n$oc-grape-list: (\n  \"0\": #f8f0fc,\n  \"1\": #f3d9fa,\n  \"2\": #eebefa,\n  \"3\": #e599f7,\n  \"4\": #da77f2,\n  \"5\": #cc5de8,\n  \"6\": #be4bdb,\n  \"7\": #ae3ec9,\n  \"8\": #9c36b5,\n  \"9\": #862e9c\n);\n\n$oc-grape-0: map-get($oc-grape-list, \"0\");\n$oc-grape-1: map-get($oc-grape-list, \"1\");\n$oc-grape-2: map-get($oc-grape-list, \"2\");\n$oc-grape-3: map-get($oc-grape-list, \"3\");\n$oc-grape-4: map-get($oc-grape-list, \"4\");\n$oc-grape-5: map-get($oc-grape-list, \"5\");\n$oc-grape-6: map-get($oc-grape-list, \"6\");\n$oc-grape-7: map-get($oc-grape-list, \"7\");\n$oc-grape-8: map-get($oc-grape-list, \"8\");\n$oc-grape-9: map-get($oc-grape-list, \"9\");\n\n\n//  Violet\n//  ───────────────────────────────────\n\n$oc-violet-list: (\n  \"0\": #f3f0ff,\n  \"1\": #e5dbff,\n  \"2\": #d0bfff,\n  \"3\": #b197fc,\n  \"4\": #9775fa,\n  \"5\": #845ef7,\n  \"6\": #7950f2,\n  \"7\": #7048e8,\n  \"8\": #6741d9,\n  \"9\": #5f3dc4\n);\n\n$oc-violet-0: map-get($oc-violet-list, \"0\");\n$oc-violet-1: map-get($oc-violet-list, \"1\");\n$oc-violet-2: map-get($oc-violet-list, \"2\");\n$oc-violet-3: map-get($oc-violet-list, \"3\");\n$oc-violet-4: map-get($oc-violet-list, \"4\");\n$oc-violet-5: map-get($oc-violet-list, \"5\");\n$oc-violet-6: map-get($oc-violet-list, \"6\");\n$oc-violet-7: map-get($oc-violet-list, \"7\");\n$oc-violet-8: map-get($oc-violet-list, \"8\");\n$oc-violet-9: map-get($oc-violet-list, \"9\");\n\n\n//  Indigo\n//  ───────────────────────────────────\n\n$oc-indigo-list: (\n  \"0\": #edf2ff,\n  \"1\": #dbe4ff,\n  \"2\": #bac8ff,\n  \"3\": #91a7ff,\n  \"4\": #748ffc,\n  \"5\": #5c7cfa,\n  \"6\": #4c6ef5,\n  \"7\": #4263eb,\n  \"8\": #3b5bdb,\n  \"9\": #364fc7\n);\n\n$oc-indigo-0: map-get($oc-indigo-list, \"0\");\n$oc-indigo-1: map-get($oc-indigo-list, \"1\");\n$oc-indigo-2: map-get($oc-indigo-list, \"2\");\n$oc-indigo-3: map-get($oc-indigo-list, \"3\");\n$oc-indigo-4: map-get($oc-indigo-list, \"4\");\n$oc-indigo-5: map-get($oc-indigo-list, \"5\");\n$oc-indigo-6: map-get($oc-indigo-list, \"6\");\n$oc-indigo-7: map-get($oc-indigo-list, \"7\");\n$oc-indigo-8: map-get($oc-indigo-list, \"8\");\n$oc-indigo-9: map-get($oc-indigo-list, \"9\");\n\n\n//  Blue\n//  ───────────────────────────────────\n\n$oc-blue-list: (\n  \"0\": #e7f5ff,\n  \"1\": #d0ebff,\n  \"2\": #a5d8ff,\n  \"3\": #74c0fc,\n  \"4\": #4dabf7,\n  \"5\": #339af0,\n  \"6\": #228be6,\n  \"7\": #1c7ed6,\n  \"8\": #1971c2,\n  \"9\": #1864ab\n);\n\n$oc-blue-0: map-get($oc-blue-list, \"0\");\n$oc-blue-1: map-get($oc-blue-list, \"1\");\n$oc-blue-2: map-get($oc-blue-list, \"2\");\n$oc-blue-3: map-get($oc-blue-list, \"3\");\n$oc-blue-4: map-get($oc-blue-list, \"4\");\n$oc-blue-5: map-get($oc-blue-list, \"5\");\n$oc-blue-6: map-get($oc-blue-list, \"6\");\n$oc-blue-7: map-get($oc-blue-list, \"7\");\n$oc-blue-8: map-get($oc-blue-list, \"8\");\n$oc-blue-9: map-get($oc-blue-list, \"9\");\n\n\n//  Cyan\n//  ───────────────────────────────────\n\n$oc-cyan-list: (\n  \"0\": #e3fafc,\n  \"1\": #c5f6fa,\n  \"2\": #99e9f2,\n  \"3\": #66d9e8,\n  \"4\": #3bc9db,\n  \"5\": #22b8cf,\n  \"6\": #15aabf,\n  \"7\": #1098ad,\n  \"8\": #0c8599,\n  \"9\": #0b7285\n);\n\n$oc-cyan-0: map-get($oc-cyan-list, \"0\");\n$oc-cyan-1: map-get($oc-cyan-list, \"1\");\n$oc-cyan-2: map-get($oc-cyan-list, \"2\");\n$oc-cyan-3: map-get($oc-cyan-list, \"3\");\n$oc-cyan-4: map-get($oc-cyan-list, \"4\");\n$oc-cyan-5: map-get($oc-cyan-list, \"5\");\n$oc-cyan-6: map-get($oc-cyan-list, \"6\");\n$oc-cyan-7: map-get($oc-cyan-list, \"7\");\n$oc-cyan-8: map-get($oc-cyan-list, \"8\");\n$oc-cyan-9: map-get($oc-cyan-list, \"9\");\n\n\n//  Teal\n//  ───────────────────────────────────\n\n$oc-teal-list: (\n  \"0\": #e6fcf5,\n  \"1\": #c3fae8,\n  \"2\": #96f2d7,\n  \"3\": #63e6be,\n  \"4\": #38d9a9,\n  \"5\": #20c997,\n  \"6\": #12b886,\n  \"7\": #0ca678,\n  \"8\": #099268,\n  \"9\": #087f5b\n);\n\n$oc-teal-0: map-get($oc-teal-list, \"0\");\n$oc-teal-1: map-get($oc-teal-list, \"1\");\n$oc-teal-2: map-get($oc-teal-list, \"2\");\n$oc-teal-3: map-get($oc-teal-list, \"3\");\n$oc-teal-4: map-get($oc-teal-list, \"4\");\n$oc-teal-5: map-get($oc-teal-list, \"5\");\n$oc-teal-6: map-get($oc-teal-list, \"6\");\n$oc-teal-7: map-get($oc-teal-list, \"7\");\n$oc-teal-8: map-get($oc-teal-list, \"8\");\n$oc-teal-9: map-get($oc-teal-list, \"9\");\n\n\n//  Green\n//  ───────────────────────────────────\n\n$oc-green-list: (\n  \"0\": #ebfbee,\n  \"1\": #d3f9d8,\n  \"2\": #b2f2bb,\n  \"3\": #8ce99a,\n  \"4\": #69db7c,\n  \"5\": #51cf66,\n  \"6\": #40c057,\n  \"7\": #37b24d,\n  \"8\": #2f9e44,\n  \"9\": #2b8a3e\n);\n\n$oc-green-0: map-get($oc-green-list, \"0\");\n$oc-green-1: map-get($oc-green-list, \"1\");\n$oc-green-2: map-get($oc-green-list, \"2\");\n$oc-green-3: map-get($oc-green-list, \"3\");\n$oc-green-4: map-get($oc-green-list, \"4\");\n$oc-green-5: map-get($oc-green-list, \"5\");\n$oc-green-6: map-get($oc-green-list, \"6\");\n$oc-green-7: map-get($oc-green-list, \"7\");\n$oc-green-8: map-get($oc-green-list, \"8\");\n$oc-green-9: map-get($oc-green-list, \"9\");\n\n\n//  Lime\n//  ───────────────────────────────────\n\n$oc-lime-list: (\n  \"0\": #f4fce3,\n  \"1\": #e9fac8,\n  \"2\": #d8f5a2,\n  \"3\": #c0eb75,\n  \"4\": #a9e34b,\n  \"5\": #94d82d,\n  \"6\": #82c91e,\n  \"7\": #74b816,\n  \"8\": #66a80f,\n  \"9\": #5c940d\n);\n\n$oc-lime-0: map-get($oc-lime-list, \"0\");\n$oc-lime-1: map-get($oc-lime-list, \"1\");\n$oc-lime-2: map-get($oc-lime-list, \"2\");\n$oc-lime-3: map-get($oc-lime-list, \"3\");\n$oc-lime-4: map-get($oc-lime-list, \"4\");\n$oc-lime-5: map-get($oc-lime-list, \"5\");\n$oc-lime-6: map-get($oc-lime-list, \"6\");\n$oc-lime-7: map-get($oc-lime-list, \"7\");\n$oc-lime-8: map-get($oc-lime-list, \"8\");\n$oc-lime-9: map-get($oc-lime-list, \"9\");\n\n\n//  Yellow\n//  ───────────────────────────────────\n\n$oc-yellow-list: (\n  \"0\": #fff9db,\n  \"1\": #fff3bf,\n  \"2\": #ffec99,\n  \"3\": #ffe066,\n  \"4\": #ffd43b,\n  \"5\": #fcc419,\n  \"6\": #fab005,\n  \"7\": #f59f00,\n  \"8\": #f08c00,\n  \"9\": #e67700\n);\n\n$oc-yellow-0: map-get($oc-yellow-list, \"0\");\n$oc-yellow-1: map-get($oc-yellow-list, \"1\");\n$oc-yellow-2: map-get($oc-yellow-list, \"2\");\n$oc-yellow-3: map-get($oc-yellow-list, \"3\");\n$oc-yellow-4: map-get($oc-yellow-list, \"4\");\n$oc-yellow-5: map-get($oc-yellow-list, \"5\");\n$oc-yellow-6: map-get($oc-yellow-list, \"6\");\n$oc-yellow-7: map-get($oc-yellow-list, \"7\");\n$oc-yellow-8: map-get($oc-yellow-list, \"8\");\n$oc-yellow-9: map-get($oc-yellow-list, \"9\");\n\n\n//  Orange\n//  ───────────────────────────────────\n\n$oc-orange-list: (\n  \"0\": #fff4e6,\n  \"1\": #ffe8cc,\n  \"2\": #ffd8a8,\n  \"3\": #ffc078,\n  \"4\": #ffa94d,\n  \"5\": #ff922b,\n  \"6\": #fd7e14,\n  \"7\": #f76707,\n  \"8\": #e8590c,\n  \"9\": #d9480f\n);\n\n$oc-orange-0: map-get($oc-orange-list, \"0\");\n$oc-orange-1: map-get($oc-orange-list, \"1\");\n$oc-orange-2: map-get($oc-orange-list, \"2\");\n$oc-orange-3: map-get($oc-orange-list, \"3\");\n$oc-orange-4: map-get($oc-orange-list, \"4\");\n$oc-orange-5: map-get($oc-orange-list, \"5\");\n$oc-orange-6: map-get($oc-orange-list, \"6\");\n$oc-orange-7: map-get($oc-orange-list, \"7\");\n$oc-orange-8: map-get($oc-orange-list, \"8\");\n$oc-orange-9: map-get($oc-orange-list, \"9\");\n\n\n//  Color list\n//  ───────────────────────────────────\n\n$oc-color-spectrum:   9;\n\n$oc-color-list: (\n  $oc-gray-list:      \"gray\",\n  $oc-red-list:      \"red\",\n  $oc-pink-list:      \"pink\",\n  $oc-grape-list:      \"grape\",\n  $oc-violet-list:      \"violet\",\n  $oc-indigo-list:      \"indigo\",\n  $oc-blue-list:      \"blue\",\n  $oc-cyan-list:      \"cyan\",\n  $oc-teal-list:      \"teal\",\n  $oc-green-list:      \"green\",\n  $oc-lime-list:      \"lime\",\n  $oc-yellow-list:      \"yellow\",\n  $oc-orange-list:      \"orange\"\n);\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  --ExcTextField--color: var(--color-on-surface);\n  --ExcTextField--label-color: var(--color-on-surface);\n  --ExcTextField--background: var(--color-surface-low);\n  --ExcTextField--readonly--background: var(--color-surface-high);\n  --ExcTextField--readonly--color: var(--color-on-surface);\n  --ExcTextField--border: var(--color-gray-20);\n  --ExcTextField--readonly--border: var(--color-border-outline-variant);\n  --ExcTextField--border-hover: var(--color-brand-hover);\n  --ExcTextField--border-active: var(--color-brand-active);\n  --ExcTextField--placeholder: var(--color-border-outline-variant);\n\n  .ExcTextField {\n    position: relative;\n\n    svg {\n      position: absolute;\n      top: 50%; // 50% is not exactly in the center of the input\n      transform: translateY(-50%);\n      left: 0.75rem;\n      width: 1.25rem;\n      height: 1.25rem;\n      color: var(--color-gray-40);\n      z-index: 1;\n    }\n\n    &--fullWidth {\n      width: 100%;\n      flex-grow: 1;\n    }\n\n    &__label {\n      font-family: \"Assistant\";\n      font-style: normal;\n      font-weight: 600;\n      font-size: 0.875rem;\n      line-height: 150%;\n\n      color: var(--ExcTextField--label-color);\n\n      margin-bottom: 0.25rem;\n      user-select: none;\n    }\n\n    &__input {\n      box-sizing: border-box;\n\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n\n      height: 3rem;\n\n      background: var(--ExcTextField--background);\n      border: 1px solid var(--ExcTextField--border);\n      border-radius: 0.5rem;\n\n      padding: 0 0.75rem;\n\n      &:not(&--readonly) {\n        &:hover {\n          border-color: var(--ExcTextField--border-hover);\n        }\n\n        &:active,\n        &:focus-within {\n          border-color: var(--ExcTextField--border-active);\n        }\n      }\n\n      & input {\n        display: flex;\n        align-items: center;\n\n        border: none;\n        outline: none;\n        padding: 0;\n        margin: 0;\n\n        height: 1.5rem;\n\n        color: var(--ExcTextField--color);\n\n        font-family: \"Assistant\";\n        font-style: normal;\n        font-weight: 400;\n        font-size: 1rem;\n        line-height: 150%;\n        text-overflow: ellipsis;\n\n        background: transparent;\n\n        width: 100%;\n\n        &:not(:focus) {\n          &:hover {\n            background-color: initial;\n          }\n        }\n\n        &:focus {\n          outline: initial;\n          box-shadow: initial;\n        }\n      }\n\n      &--readonly {\n        background: var(--ExcTextField--readonly--background);\n        border-color: var(--ExcTextField--readonly--border);\n\n        & input {\n          color: var(--ExcTextField--readonly--color);\n        }\n      }\n    }\n\n    &--hasIcon .ExcTextField__input {\n      padding-left: 2.5rem;\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n@import \"./variables.module.scss\";\n\n.excalidraw {\n  --theme-filter: none;\n  --button-destructive-bg-color: #{$oc-red-1};\n  --button-destructive-color: #{$oc-red-9};\n  --button-gray-1: #{$oc-gray-2};\n  --button-gray-2: #{$oc-gray-4};\n  --button-gray-3: #{$oc-gray-5};\n  --button-special-active-bg-color: #{$oc-green-0};\n  --dialog-border-color: var(--color-gray-20);\n  --dropdown-icon: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"292.4\" height=\"292.4\" viewBox=\"0 0 292 292\"><path d=\"M287 197L159 69c-4-3-8-5-13-5s-9 2-13 5L5 197c-3 4-5 8-5 13s2 9 5 13c4 4 8 5 13 5h256c5 0 9-1 13-5s5-8 5-13-1-9-5-13z\"/></svg>');\n  --focus-highlight-color: #{$oc-blue-2};\n  --icon-fill-color: var(--color-on-surface);\n  --icon-green-fill-color: #{$oc-green-9};\n  --default-bg-color: #{$oc-white};\n  --input-bg-color: #{$oc-white};\n  --input-border-color: #{$oc-gray-4};\n  --input-hover-bg-color: #{$oc-gray-1};\n  --input-label-color: #{$oc-gray-7};\n  --island-bg-color: #ffffff;\n  --keybinding-color: var(--color-gray-40);\n  --link-color: #{$oc-blue-7};\n  --overlay-bg-color: #{transparentize($oc-white, 0.12)};\n  --popup-bg-color: var(--island-bg-color);\n  --popup-secondary-bg-color: #{$oc-gray-1};\n  --popup-text-color: #{$oc-black};\n  --popup-text-inverted-color: #{$oc-white};\n  --select-highlight-color: #{$oc-blue-5};\n  --shadow-island: 0px 0px 0.9310142993927002px 0px rgba(0, 0, 0, 0.17),\n    0px 0px 3.1270833015441895px 0px rgba(0, 0, 0, 0.08),\n    0px 7px 14px 0px rgba(0, 0, 0, 0.05);\n\n  --button-hover-bg: var(--color-surface-high);\n  --button-active-bg: var(--color-surface-high);\n  --button-active-border: var(--color-brand-active);\n  --default-border-color: var(--color-surface-high);\n\n  --default-button-size: 2rem;\n  --default-icon-size: 1rem;\n  --lg-button-size: 2.25rem;\n  --lg-icon-size: 1rem;\n  --editor-container-padding: 1rem;\n\n  @media screen and (min-device-width: 1921px) {\n    --lg-button-size: 2.5rem;\n    --lg-icon-size: 1.25rem;\n    --default-button-size: 2.25rem;\n    --default-icon-size: 1.25rem;\n  }\n\n  --scrollbar-thumb: var(--button-gray-2);\n  --scrollbar-thumb-hover: var(--button-gray-3);\n\n  --color-slider-track: hsl(240, 100%, 90%);\n  --color-slider-thumb: var(--color-gray-80);\n\n  --modal-shadow: 0px 100px 80px rgba(0, 0, 0, 0.07),\n    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),\n    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),\n    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),\n    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),\n    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);\n  --avatar-border-color: var(--color-gray-20);\n  --sidebar-shadow: 0px 100px 80px rgba(0, 0, 0, 0.07),\n    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),\n    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),\n    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),\n    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),\n    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);\n  --sidebar-border-color: var(--color-surface-high);\n  --sidebar-bg-color: var(--island-bg-color);\n  --library-dropdown-shadow: 0px 15px 6px rgba(0, 0, 0, 0.01),\n    0px 8px 5px rgba(0, 0, 0, 0.05), 0px 4px 4px rgba(0, 0, 0, 0.09),\n    0px 1px 2px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);\n\n  --space-factor: 0.25rem;\n  --text-primary-color: var(--color-on-surface);\n\n  --color-selection: #6965db;\n\n  --color-icon-white: #{$oc-white};\n\n  --color-primary: #6965db;\n  --color-primary-darker: #5b57d1;\n  --color-primary-darkest: #4a47b1;\n  --color-primary-light: #e3e2fe;\n  --color-primary-light-darker: #d7d5ff;\n  --color-primary-hover: #5753d0;\n\n  --color-gray-10: #f5f5f5;\n  --color-gray-20: #ebebeb;\n  --color-gray-30: #d6d6d6;\n  --color-gray-40: #b8b8b8;\n  --color-gray-50: #999999;\n  --color-gray-60: #7a7a7a;\n  --color-gray-70: #5c5c5c;\n  --color-gray-80: #3d3d3d;\n  --color-gray-85: #242424;\n  --color-gray-90: #1e1e1e;\n  --color-gray-100: #121212;\n\n  --color-disabled: var(--color-gray-40);\n\n  --color-warning: #fceeca;\n  --color-warning-dark: #f5c354;\n  --color-warning-darker: #f3ab2c;\n  --color-warning-darkest: #ec8b14;\n  --color-text-warning: var(--text-primary-color);\n\n  --color-danger: #db6965;\n  --color-danger-dark: #db6965;\n  --color-danger-darker: #d65550;\n  --color-danger-darkest: #d1413c;\n  --color-danger-text: black;\n\n  --color-danger-background: #fff0f0;\n  --color-danger-icon-background: #ffdad6;\n  --color-danger-color: #700000;\n  --color-danger-icon-color: #700000;\n\n  --color-warning-background: var(--color-warning);\n  --color-warning-icon-background: var(--color-warning-dark);\n  --color-warning-color: var(--text-primary-color);\n  --color-warning-icon-color: var(--text-primary-color);\n\n  --color-muted: var(--color-gray-30);\n  --color-muted-darker: var(--color-gray-60);\n  --color-muted-darkest: var(--color-gray-100);\n  --color-muted-background: var(--color-gray-80);\n  --color-muted-background-darker: var(--color-gray-100);\n\n  --color-promo: var(--color-primary);\n\n  --color-success: #cafccc;\n  --color-success-darker: #bafabc;\n  --color-success-darkest: #a5eba8;\n  --color-success-text: #268029;\n  --color-success-contrast: #65bb6a;\n  --color-success-contrast-hover: #6bcf70;\n  --color-success-contrast-active: #6edf74;\n\n  --color-logo-icon: var(--color-primary);\n  --color-logo-text: #190064;\n\n  --border-radius-md: 0.375rem;\n  --border-radius-lg: 0.5rem;\n\n  --color-surface-high: #f1f0ff;\n  --color-surface-mid: #f2f2f7;\n  --color-surface-low: #ececf4;\n  --color-surface-lowest: #ffffff;\n  --color-on-surface: #1b1b1f;\n  --color-brand-hover: #5753d0;\n  --color-on-primary-container: #030064;\n  --color-surface-primary-container: #e0dfff;\n  --color-brand-active: #4440bf;\n  --color-border-outline: #767680;\n  --color-border-outline-variant: #c5c5d0;\n  --color-surface-primary-container: #e0dfff;\n\n  --color-badge: #0b6513;\n  --background-color-badge: #d3ffd2;\n\n  &.theme--dark {\n    &.theme--dark-background-none {\n      background: none;\n    }\n  }\n\n  &.theme--dark {\n    --theme-filter: invert(93%) hue-rotate(180deg);\n    --button-destructive-bg-color: #5a0000;\n    --button-destructive-color: #{$oc-red-3};\n\n    --button-gray-1: #363636;\n    --button-gray-2: #272727;\n    --button-gray-3: #222;\n    --button-special-active-bg-color: #204624;\n    --dialog-border-color: var(--color-gray-80);\n    --dropdown-icon: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"292.4\" height=\"292.4\" viewBox=\"0 0 292 292\"><path fill=\"%23ced4da\" d=\"M287 197L159 69c-4-3-8-5-13-5s-9 2-13 5L5 197c-3 4-5 8-5 13s2 9 5 13c4 4 8 5 13 5h256c5 0 9-1 13-5s5-8 5-13-1-9-5-13z\"/></svg>');\n    --focus-highlight-color: #{$oc-blue-6};\n    --icon-green-fill-color: #{$oc-green-4};\n    --default-bg-color: #121212;\n    --input-bg-color: #121212;\n    --input-border-color: #2e2e2e;\n    --input-hover-bg-color: #181818;\n    --input-label-color: #{$oc-gray-2};\n    --island-bg-color: #232329;\n    --keybinding-color: var(--color-gray-60);\n    --link-color: #{$oc-blue-4};\n    --overlay-bg-color: #{transparentize($oc-gray-8, 0.88)};\n    --popup-secondary-bg-color: #222;\n    --popup-text-color: #{$oc-gray-4};\n    --popup-text-inverted-color: #2c2c2c;\n    --select-highlight-color: #{$oc-blue-4};\n    --shadow-island: 0px 0px 0.9310142993927002px 0px rgba(0, 0, 0, 0.17),\n      0px 0px 3.1270833015441895px 0px rgba(0, 0, 0, 0.08),\n      0px 7px 14px 0px rgba(0, 0, 0, 0.05);\n\n    --modal-shadow: 0px 100px 80px rgba(0, 0, 0, 0.07),\n      0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),\n      0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),\n      0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),\n      0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),\n      0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);\n    --avatar-border-color: var(--color-gray-85);\n\n    --scrollbar-thumb: #{$oc-gray-8};\n    --scrollbar-thumb-hover: #{$oc-gray-7};\n\n    --color-slider-track: hsl(244, 23%, 39%);\n\n    // will be inverted to a lighter color.\n    --color-selection: #3530c4;\n\n    --color-icon-white: var(--color-gray-90);\n\n    --color-primary: #a8a5ff;\n    --color-primary-darker: #b2aeff;\n    --color-primary-darkest: #beb9ff;\n    --color-primary-light: #4f4d6f;\n    --color-primary-light-darker: #43415e;\n    --color-primary-hover: #bbb8ff;\n\n    --color-disabled: var(--color-gray-70);\n\n    --color-text-warning: var(--color-gray-80);\n\n    --color-danger: #ffa8a5;\n    --color-danger-dark: #672120;\n    --color-danger-darker: #8f2625;\n    --color-danger-darkest: #ac2b29;\n    --color-danger-text: #fbcbcc;\n\n    --color-danger-background: #fbcbcc;\n    --color-danger-icon-background: #672120;\n    --color-danger-color: #261919;\n    --color-danger-icon-color: #fbcbcc;\n\n    --color-warning-background: var(--color-warning);\n    --color-warning-icon-background: var(--color-warning-dark);\n    --color-warning-color: var(--color-gray-80);\n    --color-warning-icon-color: var(--color-gray-80);\n\n    --color-muted: var(--color-gray-80);\n    --color-muted-darker: var(--color-gray-60);\n    --color-muted-darkest: var(--color-gray-20);\n    --color-muted-background: var(--color-gray-40);\n    --color-muted-background-darker: var(--color-gray-20);\n\n    --color-logo-text: #e2dfff;\n\n    --color-surface-high: hsl(245, 10%, 21%);\n    --color-surface-low: hsl(240, 8%, 15%);\n    --color-surface-mid: hsl(240 6% 10%);\n    --color-surface-lowest: hsl(0, 0%, 7%);\n    --color-on-surface: #e3e3e8;\n    --color-brand-hover: #bbb8ff;\n    --color-on-primary-container: #e0dfff;\n    --color-surface-primary-container: #403e6a;\n    --color-brand-active: #d0ccff;\n    --color-border-outline: #8e8d9c;\n    --color-border-outline-variant: #46464f;\n    --color-surface-primary-container: #403e6a;\n  }\n}\n", "@import \"../css/theme\";\n\n.excalidraw {\n  .excalidraw-button {\n    @include outlineButtonStyles;\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", ".zoom-actions,\n.undo-redo-buttons {\n  background-color: var(--island-bg-color);\n  border-radius: var(--border-radius-lg);\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n}\n\n.zoom-button,\n.undo-redo-buttons button {\n  border-radius: 0 !important;\n  background-color: var(--color-surface-low) !important;\n  font-size: 0.875rem !important;\n  width: var(--lg-button-size);\n  height: var(--lg-button-size);\n\n  svg {\n    width: var(--lg-icon-size) !important;\n    height: var(--lg-icon-size) !important;\n  }\n\n  .ToolIcon__icon {\n    width: 100%;\n    height: 100%;\n  }\n}\n\n.reset-zoom-button {\n  border-left: 0 !important;\n  border-right: 0 !important;\n  padding: 0 0.625rem !important;\n  width: 3.75rem !important;\n  justify-content: center;\n  color: var(--text-primary-color);\n}\n\n.zoom-out-button {\n  border-top-left-radius: var(--border-radius-lg) !important;\n  border-bottom-left-radius: var(--border-radius-lg) !important;\n\n  :root[dir=\"rtl\"] & {\n    transform: scaleX(-1);\n  }\n\n  .ToolIcon__icon {\n    border-top-right-radius: 0 !important;\n    border-bottom-right-radius: 0 !important;\n  }\n}\n\n.zoom-in-button {\n  border-top-right-radius: var(--border-radius-lg) !important;\n  border-bottom-right-radius: var(--border-radius-lg) !important;\n\n  :root[dir=\"rtl\"] & {\n    transform: scaleX(-1);\n  }\n\n  .ToolIcon__icon {\n    border-top-left-radius: 0 !important;\n    border-bottom-left-radius: 0 !important;\n  }\n}\n\n.undo-redo-buttons {\n  .undo-button-container button {\n    border-top-left-radius: var(--border-radius-lg) !important;\n    border-bottom-left-radius: var(--border-radius-lg) !important;\n    border-right: 0 !important;\n\n    :root[dir=\"rtl\"] & {\n      transform: scaleX(-1);\n    }\n\n    .ToolIcon__icon {\n      border-top-right-radius: 0 !important;\n      border-bottom-right-radius: 0 !important;\n    }\n  }\n\n  .redo-button-container button {\n    border-top-right-radius: var(--border-radius-lg) !important;\n    border-bottom-right-radius: var(--border-radius-lg) !important;\n\n    :root[dir=\"rtl\"] & {\n      transform: scaleX(-1);\n    }\n\n    .ToolIcon__icon {\n      border-top-left-radius: 0 !important;\n      border-bottom-left-radius: 0 !important;\n    }\n  }\n}\n", "@import \"../../css/variables.module.scss\";\n\n$verticalBreakpoint: 861px;\n\n.excalidraw {\n  .command-palette-dialog {\n    user-select: none;\n\n    .Modal__content {\n      height: auto;\n      max-height: 100%;\n\n      @media screen and (min-width: $verticalBreakpoint) {\n        max-height: 750px;\n        height: 100%;\n      }\n\n      .Island {\n        height: 100%;\n        padding: 1.5rem;\n      }\n\n      .Dialog__content {\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n      }\n    }\n\n    .shortcuts-wrapper {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      margin-top: 12px;\n      gap: 1.5rem;\n    }\n\n    .shortcut {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      height: 16px;\n      font-size: 10px;\n      gap: 0.25rem;\n\n      .shortcut-wrapper {\n        display: flex;\n      }\n\n      .shortcut-plus {\n        margin: 0px 4px;\n      }\n\n      .shortcut-key {\n        padding: 0px 4px;\n        height: 16px;\n        border-radius: 4px;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        background-color: var(--color-primary-light);\n      }\n\n      .shortcut-desc {\n        margin-left: 4px;\n        color: var(--color-gray-50);\n      }\n    }\n\n    .commands {\n      overflow-y: auto;\n      box-sizing: border-box;\n      margin-top: 12px;\n      color: var(--popup-text-color);\n      user-select: none;\n\n      .command-category {\n        display: flex;\n        flex-direction: column;\n        padding: 12px 0px;\n        margin-right: 0.25rem;\n      }\n\n      .command-category-title {\n        font-size: 1rem;\n        font-weight: 600;\n        margin-bottom: 6px;\n        display: flex;\n        align-items: center;\n      }\n\n      .command-item {\n        color: var(--popup-text-color);\n        height: 2.5rem;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        box-sizing: border-box;\n        padding: 0 0.5rem;\n        border-radius: var(--border-radius-lg);\n        cursor: pointer;\n\n        &:active {\n          background-color: var(--color-surface-low);\n        }\n\n        .name {\n          display: flex;\n          align-items: center;\n          gap: 0.25rem;\n        }\n      }\n\n      .item-selected {\n        background-color: var(--color-surface-mid);\n      }\n\n      .item-disabled {\n        opacity: 0.3;\n        cursor: not-allowed;\n      }\n\n      .no-match {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        margin-top: 36px;\n      }\n    }\n\n    .icon {\n      width: 16px;\n      height: 16px;\n      margin-right: 6px;\n    }\n  }\n}\n", ".excalidraw {\n  .popover {\n    position: absolute;\n    z-index: 10;\n    padding: 5px 0 5px;\n    outline: none;\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .context-menu {\n    position: relative;\n    border-radius: 4px;\n    box-shadow: 0 3px 10px transparentize($oc-black, 0.8);\n    padding: 0;\n    list-style: none;\n    user-select: none;\n    margin: -0.25rem 0 0 0.125rem;\n    padding: 0.5rem 0;\n    background-color: var(--popup-secondary-bg-color);\n    border: 1px solid var(--button-gray-3);\n    cursor: default;\n  }\n\n  .context-menu button {\n    color: var(--popup-text-color);\n  }\n\n  .context-menu-item {\n    position: relative;\n    width: 100%;\n    min-width: 9.5rem;\n    margin: 0;\n    padding: 0.25rem 1rem 0.25rem 1.25rem;\n    text-align: start;\n    border-radius: 0;\n    background-color: transparent;\n    border: none;\n    white-space: nowrap;\n    font-family: inherit;\n\n    display: grid;\n    grid-template-columns: 1fr 0.2fr;\n    align-items: center;\n\n    &.checkmark::before {\n      position: absolute;\n      left: 6px;\n      margin-bottom: 1px;\n      content: \"\\2713\";\n    }\n\n    &.dangerous {\n      .context-menu-item__label {\n        color: $oc-red-7;\n      }\n    }\n\n    .context-menu-item__label {\n      justify-self: start;\n      margin-inline-end: 20px;\n    }\n    .context-menu-item__shortcut {\n      justify-self: end;\n      opacity: 0.6;\n      font-family: inherit;\n      font-size: 0.7rem;\n    }\n  }\n\n  .context-menu-item:hover {\n    color: var(--popup-bg-color);\n    background-color: var(--select-highlight-color);\n\n    &.dangerous {\n      .context-menu-item__label {\n        color: var(--popup-bg-color);\n      }\n      background-color: $oc-red-6;\n    }\n  }\n\n  .context-menu-item:focus {\n    z-index: 1;\n  }\n\n  @include isMobile {\n    .context-menu-item {\n      display: block;\n\n      .context-menu-item__label {\n        margin-inline-end: 0;\n      }\n\n      .context-menu-item__shortcut {\n        display: none;\n      }\n    }\n  }\n\n  .context-menu-item-separator {\n    border: none;\n    border-top: 1px solid $oc-gray-5;\n  }\n}\n", "//\n//\n//  𝗖 𝗢 𝗟 𝗢 𝗥\n//  v 1.9.1\n//\n//  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n\n//  General\n//  ───────────────────────────────────\n\n$oc-white:         #ffffff;\n$oc-black:         #000000;\n\n\n//  Gray\n//  ───────────────────────────────────\n\n$oc-gray-list: (\n  \"0\": #f8f9fa,\n  \"1\": #f1f3f5,\n  \"2\": #e9ecef,\n  \"3\": #dee2e6,\n  \"4\": #ced4da,\n  \"5\": #adb5bd,\n  \"6\": #868e96,\n  \"7\": #495057,\n  \"8\": #343a40,\n  \"9\": #212529\n);\n\n$oc-gray-0: map-get($oc-gray-list, \"0\");\n$oc-gray-1: map-get($oc-gray-list, \"1\");\n$oc-gray-2: map-get($oc-gray-list, \"2\");\n$oc-gray-3: map-get($oc-gray-list, \"3\");\n$oc-gray-4: map-get($oc-gray-list, \"4\");\n$oc-gray-5: map-get($oc-gray-list, \"5\");\n$oc-gray-6: map-get($oc-gray-list, \"6\");\n$oc-gray-7: map-get($oc-gray-list, \"7\");\n$oc-gray-8: map-get($oc-gray-list, \"8\");\n$oc-gray-9: map-get($oc-gray-list, \"9\");\n\n\n//  Red\n//  ───────────────────────────────────\n\n$oc-red-list: (\n  \"0\": #fff5f5,\n  \"1\": #ffe3e3,\n  \"2\": #ffc9c9,\n  \"3\": #ffa8a8,\n  \"4\": #ff8787,\n  \"5\": #ff6b6b,\n  \"6\": #fa5252,\n  \"7\": #f03e3e,\n  \"8\": #e03131,\n  \"9\": #c92a2a\n);\n\n$oc-red-0: map-get($oc-red-list, \"0\");\n$oc-red-1: map-get($oc-red-list, \"1\");\n$oc-red-2: map-get($oc-red-list, \"2\");\n$oc-red-3: map-get($oc-red-list, \"3\");\n$oc-red-4: map-get($oc-red-list, \"4\");\n$oc-red-5: map-get($oc-red-list, \"5\");\n$oc-red-6: map-get($oc-red-list, \"6\");\n$oc-red-7: map-get($oc-red-list, \"7\");\n$oc-red-8: map-get($oc-red-list, \"8\");\n$oc-red-9: map-get($oc-red-list, \"9\");\n\n\n//  Pink\n//  ───────────────────────────────────\n\n$oc-pink-list: (\n  \"0\": #fff0f6,\n  \"1\": #ffdeeb,\n  \"2\": #fcc2d7,\n  \"3\": #faa2c1,\n  \"4\": #f783ac,\n  \"5\": #f06595,\n  \"6\": #e64980,\n  \"7\": #d6336c,\n  \"8\": #c2255c,\n  \"9\": #a61e4d\n);\n\n$oc-pink-0: map-get($oc-pink-list, \"0\");\n$oc-pink-1: map-get($oc-pink-list, \"1\");\n$oc-pink-2: map-get($oc-pink-list, \"2\");\n$oc-pink-3: map-get($oc-pink-list, \"3\");\n$oc-pink-4: map-get($oc-pink-list, \"4\");\n$oc-pink-5: map-get($oc-pink-list, \"5\");\n$oc-pink-6: map-get($oc-pink-list, \"6\");\n$oc-pink-7: map-get($oc-pink-list, \"7\");\n$oc-pink-8: map-get($oc-pink-list, \"8\");\n$oc-pink-9: map-get($oc-pink-list, \"9\");\n\n\n//  Grape\n//  ───────────────────────────────────\n\n$oc-grape-list: (\n  \"0\": #f8f0fc,\n  \"1\": #f3d9fa,\n  \"2\": #eebefa,\n  \"3\": #e599f7,\n  \"4\": #da77f2,\n  \"5\": #cc5de8,\n  \"6\": #be4bdb,\n  \"7\": #ae3ec9,\n  \"8\": #9c36b5,\n  \"9\": #862e9c\n);\n\n$oc-grape-0: map-get($oc-grape-list, \"0\");\n$oc-grape-1: map-get($oc-grape-list, \"1\");\n$oc-grape-2: map-get($oc-grape-list, \"2\");\n$oc-grape-3: map-get($oc-grape-list, \"3\");\n$oc-grape-4: map-get($oc-grape-list, \"4\");\n$oc-grape-5: map-get($oc-grape-list, \"5\");\n$oc-grape-6: map-get($oc-grape-list, \"6\");\n$oc-grape-7: map-get($oc-grape-list, \"7\");\n$oc-grape-8: map-get($oc-grape-list, \"8\");\n$oc-grape-9: map-get($oc-grape-list, \"9\");\n\n\n//  Violet\n//  ───────────────────────────────────\n\n$oc-violet-list: (\n  \"0\": #f3f0ff,\n  \"1\": #e5dbff,\n  \"2\": #d0bfff,\n  \"3\": #b197fc,\n  \"4\": #9775fa,\n  \"5\": #845ef7,\n  \"6\": #7950f2,\n  \"7\": #7048e8,\n  \"8\": #6741d9,\n  \"9\": #5f3dc4\n);\n\n$oc-violet-0: map-get($oc-violet-list, \"0\");\n$oc-violet-1: map-get($oc-violet-list, \"1\");\n$oc-violet-2: map-get($oc-violet-list, \"2\");\n$oc-violet-3: map-get($oc-violet-list, \"3\");\n$oc-violet-4: map-get($oc-violet-list, \"4\");\n$oc-violet-5: map-get($oc-violet-list, \"5\");\n$oc-violet-6: map-get($oc-violet-list, \"6\");\n$oc-violet-7: map-get($oc-violet-list, \"7\");\n$oc-violet-8: map-get($oc-violet-list, \"8\");\n$oc-violet-9: map-get($oc-violet-list, \"9\");\n\n\n//  Indigo\n//  ───────────────────────────────────\n\n$oc-indigo-list: (\n  \"0\": #edf2ff,\n  \"1\": #dbe4ff,\n  \"2\": #bac8ff,\n  \"3\": #91a7ff,\n  \"4\": #748ffc,\n  \"5\": #5c7cfa,\n  \"6\": #4c6ef5,\n  \"7\": #4263eb,\n  \"8\": #3b5bdb,\n  \"9\": #364fc7\n);\n\n$oc-indigo-0: map-get($oc-indigo-list, \"0\");\n$oc-indigo-1: map-get($oc-indigo-list, \"1\");\n$oc-indigo-2: map-get($oc-indigo-list, \"2\");\n$oc-indigo-3: map-get($oc-indigo-list, \"3\");\n$oc-indigo-4: map-get($oc-indigo-list, \"4\");\n$oc-indigo-5: map-get($oc-indigo-list, \"5\");\n$oc-indigo-6: map-get($oc-indigo-list, \"6\");\n$oc-indigo-7: map-get($oc-indigo-list, \"7\");\n$oc-indigo-8: map-get($oc-indigo-list, \"8\");\n$oc-indigo-9: map-get($oc-indigo-list, \"9\");\n\n\n//  Blue\n//  ───────────────────────────────────\n\n$oc-blue-list: (\n  \"0\": #e7f5ff,\n  \"1\": #d0ebff,\n  \"2\": #a5d8ff,\n  \"3\": #74c0fc,\n  \"4\": #4dabf7,\n  \"5\": #339af0,\n  \"6\": #228be6,\n  \"7\": #1c7ed6,\n  \"8\": #1971c2,\n  \"9\": #1864ab\n);\n\n$oc-blue-0: map-get($oc-blue-list, \"0\");\n$oc-blue-1: map-get($oc-blue-list, \"1\");\n$oc-blue-2: map-get($oc-blue-list, \"2\");\n$oc-blue-3: map-get($oc-blue-list, \"3\");\n$oc-blue-4: map-get($oc-blue-list, \"4\");\n$oc-blue-5: map-get($oc-blue-list, \"5\");\n$oc-blue-6: map-get($oc-blue-list, \"6\");\n$oc-blue-7: map-get($oc-blue-list, \"7\");\n$oc-blue-8: map-get($oc-blue-list, \"8\");\n$oc-blue-9: map-get($oc-blue-list, \"9\");\n\n\n//  Cyan\n//  ───────────────────────────────────\n\n$oc-cyan-list: (\n  \"0\": #e3fafc,\n  \"1\": #c5f6fa,\n  \"2\": #99e9f2,\n  \"3\": #66d9e8,\n  \"4\": #3bc9db,\n  \"5\": #22b8cf,\n  \"6\": #15aabf,\n  \"7\": #1098ad,\n  \"8\": #0c8599,\n  \"9\": #0b7285\n);\n\n$oc-cyan-0: map-get($oc-cyan-list, \"0\");\n$oc-cyan-1: map-get($oc-cyan-list, \"1\");\n$oc-cyan-2: map-get($oc-cyan-list, \"2\");\n$oc-cyan-3: map-get($oc-cyan-list, \"3\");\n$oc-cyan-4: map-get($oc-cyan-list, \"4\");\n$oc-cyan-5: map-get($oc-cyan-list, \"5\");\n$oc-cyan-6: map-get($oc-cyan-list, \"6\");\n$oc-cyan-7: map-get($oc-cyan-list, \"7\");\n$oc-cyan-8: map-get($oc-cyan-list, \"8\");\n$oc-cyan-9: map-get($oc-cyan-list, \"9\");\n\n\n//  Teal\n//  ───────────────────────────────────\n\n$oc-teal-list: (\n  \"0\": #e6fcf5,\n  \"1\": #c3fae8,\n  \"2\": #96f2d7,\n  \"3\": #63e6be,\n  \"4\": #38d9a9,\n  \"5\": #20c997,\n  \"6\": #12b886,\n  \"7\": #0ca678,\n  \"8\": #099268,\n  \"9\": #087f5b\n);\n\n$oc-teal-0: map-get($oc-teal-list, \"0\");\n$oc-teal-1: map-get($oc-teal-list, \"1\");\n$oc-teal-2: map-get($oc-teal-list, \"2\");\n$oc-teal-3: map-get($oc-teal-list, \"3\");\n$oc-teal-4: map-get($oc-teal-list, \"4\");\n$oc-teal-5: map-get($oc-teal-list, \"5\");\n$oc-teal-6: map-get($oc-teal-list, \"6\");\n$oc-teal-7: map-get($oc-teal-list, \"7\");\n$oc-teal-8: map-get($oc-teal-list, \"8\");\n$oc-teal-9: map-get($oc-teal-list, \"9\");\n\n\n//  Green\n//  ───────────────────────────────────\n\n$oc-green-list: (\n  \"0\": #ebfbee,\n  \"1\": #d3f9d8,\n  \"2\": #b2f2bb,\n  \"3\": #8ce99a,\n  \"4\": #69db7c,\n  \"5\": #51cf66,\n  \"6\": #40c057,\n  \"7\": #37b24d,\n  \"8\": #2f9e44,\n  \"9\": #2b8a3e\n);\n\n$oc-green-0: map-get($oc-green-list, \"0\");\n$oc-green-1: map-get($oc-green-list, \"1\");\n$oc-green-2: map-get($oc-green-list, \"2\");\n$oc-green-3: map-get($oc-green-list, \"3\");\n$oc-green-4: map-get($oc-green-list, \"4\");\n$oc-green-5: map-get($oc-green-list, \"5\");\n$oc-green-6: map-get($oc-green-list, \"6\");\n$oc-green-7: map-get($oc-green-list, \"7\");\n$oc-green-8: map-get($oc-green-list, \"8\");\n$oc-green-9: map-get($oc-green-list, \"9\");\n\n\n//  Lime\n//  ───────────────────────────────────\n\n$oc-lime-list: (\n  \"0\": #f4fce3,\n  \"1\": #e9fac8,\n  \"2\": #d8f5a2,\n  \"3\": #c0eb75,\n  \"4\": #a9e34b,\n  \"5\": #94d82d,\n  \"6\": #82c91e,\n  \"7\": #74b816,\n  \"8\": #66a80f,\n  \"9\": #5c940d\n);\n\n$oc-lime-0: map-get($oc-lime-list, \"0\");\n$oc-lime-1: map-get($oc-lime-list, \"1\");\n$oc-lime-2: map-get($oc-lime-list, \"2\");\n$oc-lime-3: map-get($oc-lime-list, \"3\");\n$oc-lime-4: map-get($oc-lime-list, \"4\");\n$oc-lime-5: map-get($oc-lime-list, \"5\");\n$oc-lime-6: map-get($oc-lime-list, \"6\");\n$oc-lime-7: map-get($oc-lime-list, \"7\");\n$oc-lime-8: map-get($oc-lime-list, \"8\");\n$oc-lime-9: map-get($oc-lime-list, \"9\");\n\n\n//  Yellow\n//  ───────────────────────────────────\n\n$oc-yellow-list: (\n  \"0\": #fff9db,\n  \"1\": #fff3bf,\n  \"2\": #ffec99,\n  \"3\": #ffe066,\n  \"4\": #ffd43b,\n  \"5\": #fcc419,\n  \"6\": #fab005,\n  \"7\": #f59f00,\n  \"8\": #f08c00,\n  \"9\": #e67700\n);\n\n$oc-yellow-0: map-get($oc-yellow-list, \"0\");\n$oc-yellow-1: map-get($oc-yellow-list, \"1\");\n$oc-yellow-2: map-get($oc-yellow-list, \"2\");\n$oc-yellow-3: map-get($oc-yellow-list, \"3\");\n$oc-yellow-4: map-get($oc-yellow-list, \"4\");\n$oc-yellow-5: map-get($oc-yellow-list, \"5\");\n$oc-yellow-6: map-get($oc-yellow-list, \"6\");\n$oc-yellow-7: map-get($oc-yellow-list, \"7\");\n$oc-yellow-8: map-get($oc-yellow-list, \"8\");\n$oc-yellow-9: map-get($oc-yellow-list, \"9\");\n\n\n//  Orange\n//  ───────────────────────────────────\n\n$oc-orange-list: (\n  \"0\": #fff4e6,\n  \"1\": #ffe8cc,\n  \"2\": #ffd8a8,\n  \"3\": #ffc078,\n  \"4\": #ffa94d,\n  \"5\": #ff922b,\n  \"6\": #fd7e14,\n  \"7\": #f76707,\n  \"8\": #e8590c,\n  \"9\": #d9480f\n);\n\n$oc-orange-0: map-get($oc-orange-list, \"0\");\n$oc-orange-1: map-get($oc-orange-list, \"1\");\n$oc-orange-2: map-get($oc-orange-list, \"2\");\n$oc-orange-3: map-get($oc-orange-list, \"3\");\n$oc-orange-4: map-get($oc-orange-list, \"4\");\n$oc-orange-5: map-get($oc-orange-list, \"5\");\n$oc-orange-6: map-get($oc-orange-list, \"6\");\n$oc-orange-7: map-get($oc-orange-list, \"7\");\n$oc-orange-8: map-get($oc-orange-list, \"8\");\n$oc-orange-9: map-get($oc-orange-list, \"9\");\n\n\n//  Color list\n//  ───────────────────────────────────\n\n$oc-color-spectrum:   9;\n\n$oc-color-list: (\n  $oc-gray-list:      \"gray\",\n  $oc-red-list:      \"red\",\n  $oc-pink-list:      \"pink\",\n  $oc-grape-list:      \"grape\",\n  $oc-violet-list:      \"violet\",\n  $oc-indigo-list:      \"indigo\",\n  $oc-blue-list:      \"blue\",\n  $oc-cyan-list:      \"cyan\",\n  $oc-teal-list:      \"teal\",\n  $oc-green-list:      \"green\",\n  $oc-lime-list:      \"lime\",\n  $oc-yellow-list:      \"yellow\",\n  $oc-orange-list:      \"orange\"\n);\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  --RadioGroup-background: var(--island-bg-color);\n  --RadioGroup-border: var(--color-surface-high);\n\n  --RadioGroup-choice-color-off: var(--color-primary);\n  --RadioGroup-choice-color-off-hover: var(--color-brand-hover);\n  --RadioGroup-choice-background-off: var(--island-bg-color);\n  --RadioGroup-choice-background-off-active: var(--color-surface-high);\n\n  --RadioGroup-choice-color-on: var(--color-surface-lowest);\n  --RadioGroup-choice-background-on: var(--color-primary);\n  --RadioGroup-choice-background-on-hover: var(--color-brand-hover);\n  --RadioGroup-choice-background-on-active: var(--color-brand-active);\n\n  .RadioGroup {\n    box-sizing: border-box;\n    display: flex;\n    flex-direction: row;\n    align-items: flex-start;\n\n    padding: 3px;\n    border-radius: 10px;\n\n    background: var(--RadioGroup-background);\n    border: 1px solid var(--RadioGroup-border);\n\n    &__choice {\n      position: relative;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: 32px;\n      height: 24px;\n\n      color: var(--RadioGroup-choice-color-off);\n      background: var(--RadioGroup-choice-background-off);\n\n      border-radius: 8px;\n\n      font-family: \"Assistant\";\n      font-style: normal;\n      font-weight: 600;\n      font-size: 0.75rem;\n      line-height: 100%;\n      user-select: none;\n      letter-spacing: 0.4px;\n\n      transition: all 75ms ease-out;\n\n      &:hover {\n        color: var(--RadioGroup-choice-color-off-hover);\n      }\n\n      &:active {\n        background: var(--RadioGroup-choice-background-off-active);\n      }\n\n      &.active {\n        color: var(--RadioGroup-choice-color-on);\n        background: var(--RadioGroup-choice-background-on);\n\n        &:hover {\n          background: var(--RadioGroup-choice-background-on-hover);\n        }\n\n        &:active {\n          background: var(--RadioGroup-choice-background-on-active);\n        }\n      }\n\n      & input {\n        z-index: 1;\n        position: absolute;\n        width: 100%;\n        height: 100%;\n        margin: 0;\n        padding: 0;\n\n        border-radius: 8px;\n\n        -webkit-appearance: none;\n        -moz-appearance: none;\n        appearance: none;\n\n        cursor: pointer;\n      }\n    }\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  --Switch-disabled-color: var(--color-border-outline);\n  --Switch-disabled-toggled-background: var(--color-border-outline-variant);\n  --Switch-disabled-border: var(--color-border-outline-variant);\n  --Switch-track-background: var(--island-bg-color);\n  --Switch-thumb-background: var(--color-on-surface);\n  --Switch-hover-background: var(--color-brand-hover);\n  --Switch-active-background: var(--color-brand-active);\n\n  .Switch {\n    position: relative;\n    box-sizing: border-box;\n\n    width: 40px;\n    height: 20px;\n    border-radius: 12px;\n\n    transition-property: background, border;\n    transition-duration: 150ms;\n    transition-timing-function: ease-out;\n\n    background: var(--Switch-track-background);\n    border: 1px solid var(--Switch-disabled-color);\n\n    &:hover {\n      background: var(--Switch-track-background);\n      border: 1px solid var(--Switch-hover-background);\n    }\n\n    &:active {\n      border: 1px solid var(--Switch-active-background);\n    }\n\n    &.toggled {\n      background: var(--color-primary);\n      border: 1px solid var(--color-primary);\n\n      &:hover {\n        background: var(--color-primary-darker);\n        border: 1px solid var(--color-primary-darker);\n      }\n    }\n\n    &.disabled {\n      background: var(--Switch-track-background);\n      border: 1px solid var(--Switch-disabled-border);\n\n      &.toggled {\n        background: var(--Switch-disabled-toggled-background);\n        border: 1px solid var(--Switch-disabled-toggled-background);\n      }\n    }\n\n    &:before {\n      content: \"\";\n      box-sizing: border-box;\n      display: block;\n      pointer-events: none;\n      position: absolute;\n\n      border-radius: 100%;\n      transition: all 150ms ease-out;\n\n      width: 10px;\n      height: 10px;\n      top: 4px;\n      left: 4px;\n\n      background: var(--Switch-thumb-background);\n    }\n\n    &:active:before {\n      width: 12px;\n    }\n\n    &.toggled:before {\n      width: 14px;\n      height: 14px;\n      left: 22px;\n      top: 2px;\n\n      background: var(--Switch-track-background);\n    }\n\n    &.toggled:active:before {\n      width: 16px;\n      left: 20px;\n    }\n\n    &.disabled:before {\n      background: var(--Switch-disabled-color);\n    }\n\n    &.disabled.toggled:before {\n      background: var(--Switch-disabled-color);\n    }\n\n    & input {\n      width: 100%;\n      height: 100%;\n      margin: 0;\n\n      border-radius: 12px;\n\n      -webkit-appearance: none;\n      -moz-appearance: none;\n      appearance: none;\n\n      cursor: pointer;\n\n      &:disabled {\n        cursor: unset;\n      }\n    }\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  --ImageExportModal-preview-border: #d6d6d6;\n\n  &.theme--dark {\n    --ImageExportModal-preview-border: #5c5c5c;\n  }\n\n  .ImageExportModal {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-between;\n\n    user-select: none;\n\n    & h3 {\n      font-family: \"Assistant\";\n      font-style: normal;\n      font-weight: 700;\n      font-size: 1.313rem;\n      line-height: 130%;\n      padding: 0;\n      margin: 0;\n\n      @include isMobile {\n        display: none;\n      }\n    }\n\n    & > h3 {\n      display: none;\n\n      @include isMobile {\n        display: block;\n      }\n    }\n\n    @include isMobile {\n      flex-direction: column;\n      height: calc(100vh - 5rem);\n    }\n\n    &__preview {\n      box-sizing: border-box;\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      height: 360px;\n      width: 55%;\n\n      margin-right: 1.5rem;\n\n      @include isMobile {\n        max-width: unset;\n        margin-right: unset;\n\n        width: 100%;\n        height: unset;\n        flex-grow: 1;\n      }\n\n      &__filename {\n        & > input {\n          margin-top: 1rem;\n        }\n      }\n\n      &__canvas {\n        box-sizing: border-box;\n        width: 100%;\n        height: 100%;\n        display: flex;\n        flex-grow: 1;\n        justify-content: center;\n        align-items: center;\n\n        background: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==\")\n          left center;\n\n        border: 1px solid var(--ImageExportModal-preview-border);\n        border-radius: 12px;\n\n        overflow: hidden;\n        padding: 1rem;\n\n        & > canvas {\n          max-width: calc(100% - 2rem);\n          max-height: calc(100% - 2rem);\n\n          filter: none !important;\n\n          @include isMobile {\n            max-height: 100%;\n          }\n        }\n\n        @include isMobile {\n          margin-top: 24px;\n          max-width: unset;\n        }\n      }\n    }\n\n    &__settings {\n      display: flex;\n      flex-direction: column;\n      flex-wrap: wrap;\n      gap: 18px;\n\n      @include isMobile {\n        margin-left: unset;\n        margin-top: 1rem;\n        flex-direction: row;\n        gap: 6px 34px;\n\n        align-content: flex-start;\n      }\n\n      &__setting {\n        display: flex;\n        flex-direction: row;\n        justify-content: space-between;\n        align-items: center;\n\n        @include isMobile {\n          flex-direction: column;\n          align-items: start;\n          justify-content: unset;\n          height: 52px;\n        }\n\n        &__label {\n          display: flex;\n          flex-direction: row;\n          align-items: center;\n\n          font-family: \"Assistant\";\n          font-weight: 600;\n          font-size: 1rem;\n          line-height: 150%;\n\n          & svg {\n            width: 20px;\n            height: 20px;\n            margin-left: 10px;\n          }\n        }\n\n        &__content {\n          display: flex;\n          height: 100%;\n          align-items: center;\n        }\n      }\n\n      &__buttons {\n        flex-grow: 1;\n        flex-wrap: wrap;\n        display: flex;\n        flex-direction: row;\n        gap: 11px;\n\n        align-items: flex-end;\n        align-content: flex-end;\n\n        @include isMobile {\n          padding-top: 32px;\n          flex-basis: 100%;\n          justify-content: center;\n        }\n      }\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n@keyframes successStatusAnimation {\n  0% {\n    transform: scale(0.35);\n  }\n\n  50% {\n    transform: scale(1.25);\n  }\n\n  100% {\n    transform: scale(1);\n  }\n}\n\n.excalidraw {\n  .ExcButton {\n    --text-color: transparent;\n    --border-color: transparent;\n    --back-color: transparent;\n\n    color: var(--text-color);\n    background-color: var(--back-color);\n    border-color: var(--border-color);\n\n    &:hover {\n      transition: all 150ms ease-out;\n    }\n\n    .Spinner {\n      --spinner-color: var(--color-surface-lowest);\n    }\n\n    .ExcButton__statusIcon {\n      visibility: visible;\n      position: absolute;\n\n      width: 1.2rem;\n      height: 1.2rem;\n\n      animation: successStatusAnimation 0.5s cubic-bezier(0.3, 1, 0.6, 1);\n    }\n\n    &.ExcButton--status-loading,\n    &.ExcButton--status-success {\n      pointer-events: none;\n\n      .ExcButton__contents {\n        visibility: hidden;\n      }\n    }\n\n    &[disabled] {\n      pointer-events: none;\n    }\n\n    &,\n    &__contents {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      flex-shrink: 0;\n      flex-wrap: nowrap;\n      // needed because of .Spinner\n      position: relative;\n    }\n\n    &--color-primary {\n      &.ExcButton--variant-filled {\n        --text-color: var(--color-surface-lowest);\n        --back-color: var(--color-primary);\n\n        .Spinner {\n          --spinner-color: var(--text-color);\n        }\n\n        &:hover {\n          --back-color: var(--color-brand-hover);\n        }\n\n        &:active {\n          --back-color: var(--color-brand-active);\n        }\n      }\n\n      &.ExcButton--variant-outlined,\n      &.ExcButton--variant-icon {\n        --text-color: var(--color-primary);\n        --border-color: var(--color-primary);\n        --back-color: transparent;\n\n        .Spinner {\n          --spinner-color: var(--text-color);\n        }\n\n        &:hover {\n          --text-color: var(--color-brand-hover);\n          --border-color: var(--color-brand-hover);\n        }\n\n        &:active {\n          --text-color: var(--color-brand-active);\n          --border-color: var(--color-brand-active);\n        }\n      }\n    }\n\n    &--color-danger {\n      &.ExcButton--variant-filled {\n        --text-color: var(--color-danger-text);\n        --back-color: var(--color-danger-dark);\n\n        .Spinner {\n          --spinner-color: var(--text-color);\n        }\n\n        &:hover {\n          --back-color: var(--color-danger-darker);\n        }\n\n        &:active {\n          --back-color: var(--color-danger-darkest);\n        }\n      }\n\n      &.ExcButton--variant-outlined,\n      &.ExcButton--variant-icon {\n        --text-color: var(--color-danger);\n        --border-color: var(--color-danger);\n        --back-color: transparent;\n\n        .Spinner {\n          --spinner-color: var(--text-color);\n        }\n\n        &:hover {\n          --text-color: var(--color-danger-darkest);\n          --border-color: var(--color-danger-darkest);\n        }\n\n        &:active {\n          --text-color: var(--color-danger-darker);\n          --border-color: var(--color-danger-darker);\n        }\n      }\n    }\n\n    &--color-success {\n      &.ExcButton--variant-filled {\n        --text-color: var(--color-success-text);\n        --back-color: var(--color-success);\n\n        .Spinner {\n          --spinner-color: var(--color-success);\n        }\n\n        &:hover {\n          --back-color: var(--color-success-darker);\n        }\n\n        &:active {\n          --back-color: var(--color-success-darkest);\n        }\n      }\n\n      &.ExcButton--variant-outlined,\n      &.ExcButton--variant-icon {\n        --text-color: var(--color-success-contrast);\n        --border-color: var(--color-success-contrast);\n        --back-color: transparent;\n\n        .Spinner {\n          --spinner-color: var(--color-success-contrast);\n        }\n\n        &:hover {\n          --text-color: var(--color-success-contrast-hover);\n          --border-color: var(--color-success-contrast-hover);\n        }\n\n        &:active {\n          --text-color: var(--color-success-contrast-active);\n          --border-color: var(--color-success-contrast-active);\n        }\n      }\n    }\n\n    &--color-muted {\n      &.ExcButton--variant-filled {\n        --text-color: var(--island-bg-color);\n        --back-color: var(--color-gray-50);\n\n        .Spinner {\n          --spinner-color: var(--text-color);\n        }\n\n        &:hover {\n          --back-color: var(--color-gray-60);\n        }\n\n        &:active {\n          --back-color: var(--color-gray-80);\n        }\n      }\n\n      &.ExcButton--variant-outlined,\n      &.ExcButton--variant-icon {\n        --text-color: var(--color-muted-background);\n        --border-color: var(--color-muted);\n        --back-color: var(--island-bg-color);\n\n        .Spinner {\n          --spinner-color: var(--text-color);\n        }\n\n        &:hover {\n          --text-color: var(--color-muted-background-darker);\n          --border-color: var(--color-muted-darker);\n        }\n\n        &:active {\n          --text-color: var(--color-muted-background-darker);\n          --border-color: var(--color-muted-darkest);\n        }\n      }\n    }\n\n    &--color-warning {\n      &.ExcButton--variant-filled {\n        --text-color: black;\n        --back-color: var(--color-warning-dark);\n\n        .Spinner {\n          --spinner-color: var(--text-color);\n        }\n\n        &:hover {\n          --back-color: var(--color-warning-darker);\n        }\n\n        &:active {\n          --back-color: var(--color-warning-darkest);\n        }\n      }\n\n      &.ExcButton--variant-outlined,\n      &.ExcButton--variant-icon {\n        --text-color: var(--color-warning-dark);\n        --border-color: var(--color-warning-dark);\n        --back-color: var(--input-bg-color);\n\n        .Spinner {\n          --spinner-color: var(--text-color);\n        }\n\n        &:hover {\n          --text-color: var(--color-warning-darker);\n          --border-color: var(--color-warning-darker);\n        }\n\n        &:active {\n          --text-color: var(--color-warning-darkest);\n          --border-color: var(--color-warning-darkest);\n        }\n      }\n    }\n\n    border-radius: 0.5rem;\n    border-width: 1px;\n    border-style: solid;\n\n    font-family: var(--font-family);\n\n    user-select: none;\n\n    &--size-large {\n      font-weight: 600;\n      font-size: 0.875rem;\n      min-height: 3rem;\n      padding: 0.5rem 1.5rem;\n\n      letter-spacing: 0.4px;\n\n      .ExcButton__contents {\n        gap: 0.75rem;\n      }\n    }\n\n    &--size-medium {\n      font-weight: 600;\n      font-size: 0.75rem;\n      min-height: 2.5rem;\n      padding: 0.5rem 1rem;\n\n      letter-spacing: normal;\n\n      .ExcButton__contents {\n        gap: 0.5rem;\n      }\n    }\n\n    &--variant-icon {\n      padding: 0.5rem 0.75rem;\n      width: 3rem;\n    }\n\n    &--fullWidth {\n      width: 100%;\n    }\n\n    &__icon {\n      width: 1.25rem;\n      height: 1.25rem;\n    }\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .FixedSideContainer {\n    position: absolute;\n    pointer-events: none;\n  }\n\n  .FixedSideContainer > * {\n    pointer-events: var(--ui-pointerEvents);\n  }\n\n  .FixedSideContainer_side_top {\n    left: var(--editor-container-padding);\n    top: var(--editor-container-padding);\n    right: var(--editor-container-padding);\n    bottom: var(--editor-container-padding);\n  }\n\n  .FixedSideContainer_side_top.zen-mode {\n    right: 42px;\n  }\n}\n\n/* TODO: if these are used, make sure to implement RTL support\n.FixedSideContainer_side_left {\n  left: var(--space-factor);\n  top: var(--space-factor);\n  bottom: var(--space-factor);\n  z-index: 1;\n}\n\n.FixedSideContainer_side_right {\n  right: var(--space-factor);\n  top: var(--space-factor);\n  bottom: var(--space-factor);\n  z-index: 3;\n}\n*/\n", "@import \"../css/variables.module.scss\";\n\n// this is loosely based on the longest hint text\n$wide-viewport-width: 1000px;\n\n.excalidraw {\n  .HintViewer {\n    pointer-events: none;\n    box-sizing: border-box;\n    position: absolute;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    left: 0;\n    top: 100%;\n    max-width: 100%;\n    width: 100%;\n    margin-top: 0.5rem;\n    text-align: center;\n    color: var(--color-gray-40);\n    font-size: 0.75rem;\n\n    @include isMobile {\n      position: static;\n      padding-right: 2rem;\n    }\n\n    > span {\n      padding: 0.25rem;\n    }\n  }\n\n  &.theme--dark {\n    .HintViewer {\n      color: var(--color-gray-60);\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .PasteChartDialog {\n    @include isMobile {\n      .Island {\n        display: flex;\n        flex-direction: column;\n      }\n    }\n    .container {\n      display: flex;\n      align-items: center;\n      justify-content: space-around;\n      flex-wrap: wrap;\n      @include isMobile {\n        flex-direction: column;\n        justify-content: center;\n      }\n    }\n    .ChartPreview {\n      margin: 8px;\n      text-align: center;\n      width: 192px;\n      height: 128px;\n      border-radius: 2px;\n      padding: 1px;\n      border: 1px solid $oc-gray-4;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: transparent;\n      div {\n        display: inline-block;\n      }\n      svg {\n        max-height: 120px;\n        max-width: 186px;\n      }\n      &:hover {\n        padding: 0;\n        border: 2px solid $oc-blue-5;\n      }\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .HelpDialog {\n    .Modal__content {\n      max-width: 960px;\n    }\n\n    h3 {\n      margin: 1.5rem 0;\n      font-weight: 700;\n      font-size: 1.125rem;\n    }\n\n    &__header {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 0.75rem;\n    }\n\n    &__btn {\n      --background: var(--color-surface-mid);\n\n      display: flex;\n      column-gap: 0.5rem;\n      align-items: center;\n      background-color: var(--background);\n      padding: 0.625rem 1rem;\n      border: 1px solid var(--background);\n      border-radius: var(--border-radius-lg);\n      color: var(--text-primary-color);\n      font-weight: 600;\n      font-size: 0.75rem;\n      letter-spacing: 0.4px;\n\n      @at-root .excalidraw.theme--dark#{&} {\n        --background: var(--color-surface-high);\n        &:hover {\n          --background: #363541;\n        }\n      }\n\n      &:hover {\n        --background: var(--color-surface-high);\n        text-decoration: none;\n      }\n\n      &:active {\n        border-color: var(--color-primary);\n      }\n    }\n\n    &__link-icon {\n      line-height: 0;\n      svg {\n        width: 1rem;\n        height: 1rem;\n      }\n    }\n\n    &__islands-container {\n      display: grid;\n      @media screen and (min-width: 1024px) {\n        grid-template-columns: 1fr 1fr;\n      }\n      grid-column-gap: 1.5rem;\n      grid-row-gap: 2rem;\n    }\n\n    @media screen and (min-width: 1024px) {\n      &__island--tools {\n        grid-area: 1 / 1 / 2 / 2;\n      }\n      &__island--view {\n        grid-area: 2 / 1 / 3 / 2;\n      }\n      &__island--editor {\n        grid-area: 1 / 2 / 3 / 3;\n      }\n    }\n\n    &__island {\n      h4 {\n        font-size: 1rem;\n        font-weight: 700;\n        margin: 0;\n        margin-bottom: 0.625rem;\n      }\n\n      &-content {\n        border: 1px solid var(--dialog-border-color);\n        border-radius: var(--border-radius-lg);\n      }\n    }\n\n    &__shortcut {\n      border-bottom: 1px solid var(--dialog-border-color);\n      padding: 0.375rem 0.75rem;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      font-size: 0.875rem;\n      column-gap: 0.5rem;\n\n      &:last-child {\n        border-bottom: none;\n      }\n    }\n\n    &__key-container {\n      display: flex;\n      align-items: center;\n      column-gap: 0.25rem;\n      flex-shrink: 0;\n    }\n\n    &__key {\n      display: flex;\n      box-sizing: border-box;\n      font-size: 0.625rem;\n      background-color: var(--color-primary-light);\n      border-radius: var(--border-radius-md);\n      padding: 0.5rem;\n      word-break: keep-all;\n      align-items: center;\n      font-family: inherit;\n      line-height: 1;\n    }\n  }\n}\n", "@import \"../css/variables.module\";\n\n.excalidraw {\n  --avatar-size: 1.75rem;\n  --avatarList-gap: 0.625rem;\n  --userList-padding: var(--space-factor);\n\n  .UserList__wrapper {\n    display: flex;\n    width: 100%;\n    justify-content: flex-end;\n    align-items: center;\n    pointer-events: none !important;\n  }\n\n  .UserList {\n    pointer-events: none;\n    padding: var(--userList-padding);\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: flex-end;\n    align-items: center;\n    gap: var(--avatarList-gap);\n\n    box-sizing: border-box;\n\n    --max-size: calc(\n      var(--avatar-size) * var(--max-avatars, 2) + var(--avatarList-gap) *\n        (var(--max-avatars, 2) - 1) + var(--userList-padding) * 2\n    );\n\n    // max width & height set to fix the max-avatars\n    max-height: var(--max-size);\n    max-width: var(--max-size);\n\n    // Tweak in 30px increments to fit more/fewer avatars in a row/column ^^\n  }\n\n  .UserList > * {\n    pointer-events: var(--ui-pointerEvents);\n  }\n\n  .UserList_mobile {\n    padding: 0;\n    justify-content: normal;\n    margin: 0.5rem 0;\n    max-width: none;\n    max-height: none;\n  }\n\n  .UserList__more {\n    @include avatarStyles;\n    background-color: var(--color-gray-20);\n    border: 0 !important;\n    font-size: 0.625rem;\n    font-weight: 400;\n    flex-shrink: 0;\n    color: var(--color-gray-100);\n    font-weight: bold;\n  }\n\n  .UserList__collaborator-name {\n    text-overflow: ellipsis;\n    overflow: hidden;\n    white-space: nowrap;\n  }\n\n  .UserList__collaborator--avatar-only {\n    position: relative;\n    display: flex;\n    flex: 0 0 auto;\n    .UserList__collaborator-status-icon {\n      --size: 14px;\n      position: absolute;\n      display: flex;\n      flex: 0 0 auto;\n      bottom: -0.25rem;\n      right: -0.25rem;\n      width: var(--size);\n      height: var(--size);\n      svg {\n        flex: 0 0 auto;\n        width: var(--size);\n        height: var(--size);\n      }\n    }\n  }\n\n  .UserList__collaborator-status-icons {\n    margin-left: auto;\n    flex: 0 0 auto;\n    min-width: 2.25rem;\n    gap: 0.25rem;\n    justify-content: flex-end;\n    display: flex;\n  }\n\n  .UserList__collaborator.is-muted\n    .UserList__collaborator-status-icon-microphone-muted {\n    color: var(--color-danger);\n    filter: drop-shadow(0px 0px 0px rgba(0, 0, 0, 0.5));\n  }\n\n  .UserList__collaborator-status-icon-speaking-indicator {\n    display: flex;\n    flex-flow: row nowrap;\n    align-items: center;\n    justify-content: space-between;\n    width: 1rem;\n    padding: 0 3px;\n    box-sizing: border-box;\n\n    div {\n      width: 0.125rem;\n      height: 0.4rem;\n      // keep this in sync with constants.ts\n      background-color: #a2f1a6;\n    }\n\n    div:nth-of-type(1) {\n      animation: speaking-indicator-anim 1s -0.45s ease-in-out infinite;\n    }\n\n    div:nth-of-type(2) {\n      animation: speaking-indicator-anim 1s -0.9s ease-in-out infinite;\n    }\n\n    div:nth-of-type(3) {\n      animation: speaking-indicator-anim 1s -0.15s ease-in-out infinite;\n    }\n  }\n\n  @keyframes speaking-indicator-anim {\n    0%,\n    100% {\n      transform: scaleY(1);\n    }\n\n    50% {\n      transform: scaleY(2);\n    }\n  }\n\n  --userlist-hint-bg-color: var(--color-gray-10);\n  --userlist-hint-heading-color: var(--color-gray-80);\n  --userlist-hint-text-color: var(--color-gray-60);\n  --userlist-collaborators-border-color: var(--color-gray-20);\n\n  &.theme--dark {\n    --userlist-hint-bg-color: var(--color-gray-90);\n    --userlist-hint-heading-color: var(--color-gray-30);\n    --userlist-hint-text-color: var(--color-gray-40);\n    --userlist-collaborators-border-color: var(--color-gray-80);\n  }\n\n  .UserList__collaborators {\n    top: auto;\n    max-height: 50vh;\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .Card {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n\n    max-width: 290px;\n\n    margin: 1em;\n\n    text-align: center;\n\n    .Card-icon {\n      font-size: 2.6em;\n      display: flex;\n      flex: 0 0 auto;\n      padding: 1.4rem;\n      border-radius: 50%;\n      background: var(--card-color);\n      color: $oc-white;\n\n      svg {\n        width: 2.8rem;\n        height: 2.8rem;\n      }\n    }\n\n    .Card-details {\n      font-size: 0.96em;\n      min-height: 90px;\n      padding: 0 1em;\n      margin-bottom: auto;\n    }\n\n    & .Card-button.ToolIcon_type_button {\n      height: 2.5rem;\n      margin-top: 1em;\n      margin-bottom: 0.3em;\n      background-color: var(--card-color);\n      &:hover {\n        background-color: var(--card-color-darker);\n      }\n      &:active {\n        background-color: var(--card-color-darkest);\n      }\n      .ToolIcon__label {\n        color: $oc-white;\n      }\n\n      .Spinner {\n        --spinner-color: #fff;\n      }\n    }\n  }\n}\n", "//\n//\n//  𝗖 𝗢 𝗟 𝗢 𝗥\n//  v 1.9.1\n//\n//  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n\n//  General\n//  ───────────────────────────────────\n\n$oc-white:         #ffffff;\n$oc-black:         #000000;\n\n\n//  Gray\n//  ───────────────────────────────────\n\n$oc-gray-list: (\n  \"0\": #f8f9fa,\n  \"1\": #f1f3f5,\n  \"2\": #e9ecef,\n  \"3\": #dee2e6,\n  \"4\": #ced4da,\n  \"5\": #adb5bd,\n  \"6\": #868e96,\n  \"7\": #495057,\n  \"8\": #343a40,\n  \"9\": #212529\n);\n\n$oc-gray-0: map-get($oc-gray-list, \"0\");\n$oc-gray-1: map-get($oc-gray-list, \"1\");\n$oc-gray-2: map-get($oc-gray-list, \"2\");\n$oc-gray-3: map-get($oc-gray-list, \"3\");\n$oc-gray-4: map-get($oc-gray-list, \"4\");\n$oc-gray-5: map-get($oc-gray-list, \"5\");\n$oc-gray-6: map-get($oc-gray-list, \"6\");\n$oc-gray-7: map-get($oc-gray-list, \"7\");\n$oc-gray-8: map-get($oc-gray-list, \"8\");\n$oc-gray-9: map-get($oc-gray-list, \"9\");\n\n\n//  Red\n//  ───────────────────────────────────\n\n$oc-red-list: (\n  \"0\": #fff5f5,\n  \"1\": #ffe3e3,\n  \"2\": #ffc9c9,\n  \"3\": #ffa8a8,\n  \"4\": #ff8787,\n  \"5\": #ff6b6b,\n  \"6\": #fa5252,\n  \"7\": #f03e3e,\n  \"8\": #e03131,\n  \"9\": #c92a2a\n);\n\n$oc-red-0: map-get($oc-red-list, \"0\");\n$oc-red-1: map-get($oc-red-list, \"1\");\n$oc-red-2: map-get($oc-red-list, \"2\");\n$oc-red-3: map-get($oc-red-list, \"3\");\n$oc-red-4: map-get($oc-red-list, \"4\");\n$oc-red-5: map-get($oc-red-list, \"5\");\n$oc-red-6: map-get($oc-red-list, \"6\");\n$oc-red-7: map-get($oc-red-list, \"7\");\n$oc-red-8: map-get($oc-red-list, \"8\");\n$oc-red-9: map-get($oc-red-list, \"9\");\n\n\n//  Pink\n//  ───────────────────────────────────\n\n$oc-pink-list: (\n  \"0\": #fff0f6,\n  \"1\": #ffdeeb,\n  \"2\": #fcc2d7,\n  \"3\": #faa2c1,\n  \"4\": #f783ac,\n  \"5\": #f06595,\n  \"6\": #e64980,\n  \"7\": #d6336c,\n  \"8\": #c2255c,\n  \"9\": #a61e4d\n);\n\n$oc-pink-0: map-get($oc-pink-list, \"0\");\n$oc-pink-1: map-get($oc-pink-list, \"1\");\n$oc-pink-2: map-get($oc-pink-list, \"2\");\n$oc-pink-3: map-get($oc-pink-list, \"3\");\n$oc-pink-4: map-get($oc-pink-list, \"4\");\n$oc-pink-5: map-get($oc-pink-list, \"5\");\n$oc-pink-6: map-get($oc-pink-list, \"6\");\n$oc-pink-7: map-get($oc-pink-list, \"7\");\n$oc-pink-8: map-get($oc-pink-list, \"8\");\n$oc-pink-9: map-get($oc-pink-list, \"9\");\n\n\n//  Grape\n//  ───────────────────────────────────\n\n$oc-grape-list: (\n  \"0\": #f8f0fc,\n  \"1\": #f3d9fa,\n  \"2\": #eebefa,\n  \"3\": #e599f7,\n  \"4\": #da77f2,\n  \"5\": #cc5de8,\n  \"6\": #be4bdb,\n  \"7\": #ae3ec9,\n  \"8\": #9c36b5,\n  \"9\": #862e9c\n);\n\n$oc-grape-0: map-get($oc-grape-list, \"0\");\n$oc-grape-1: map-get($oc-grape-list, \"1\");\n$oc-grape-2: map-get($oc-grape-list, \"2\");\n$oc-grape-3: map-get($oc-grape-list, \"3\");\n$oc-grape-4: map-get($oc-grape-list, \"4\");\n$oc-grape-5: map-get($oc-grape-list, \"5\");\n$oc-grape-6: map-get($oc-grape-list, \"6\");\n$oc-grape-7: map-get($oc-grape-list, \"7\");\n$oc-grape-8: map-get($oc-grape-list, \"8\");\n$oc-grape-9: map-get($oc-grape-list, \"9\");\n\n\n//  Violet\n//  ───────────────────────────────────\n\n$oc-violet-list: (\n  \"0\": #f3f0ff,\n  \"1\": #e5dbff,\n  \"2\": #d0bfff,\n  \"3\": #b197fc,\n  \"4\": #9775fa,\n  \"5\": #845ef7,\n  \"6\": #7950f2,\n  \"7\": #7048e8,\n  \"8\": #6741d9,\n  \"9\": #5f3dc4\n);\n\n$oc-violet-0: map-get($oc-violet-list, \"0\");\n$oc-violet-1: map-get($oc-violet-list, \"1\");\n$oc-violet-2: map-get($oc-violet-list, \"2\");\n$oc-violet-3: map-get($oc-violet-list, \"3\");\n$oc-violet-4: map-get($oc-violet-list, \"4\");\n$oc-violet-5: map-get($oc-violet-list, \"5\");\n$oc-violet-6: map-get($oc-violet-list, \"6\");\n$oc-violet-7: map-get($oc-violet-list, \"7\");\n$oc-violet-8: map-get($oc-violet-list, \"8\");\n$oc-violet-9: map-get($oc-violet-list, \"9\");\n\n\n//  Indigo\n//  ───────────────────────────────────\n\n$oc-indigo-list: (\n  \"0\": #edf2ff,\n  \"1\": #dbe4ff,\n  \"2\": #bac8ff,\n  \"3\": #91a7ff,\n  \"4\": #748ffc,\n  \"5\": #5c7cfa,\n  \"6\": #4c6ef5,\n  \"7\": #4263eb,\n  \"8\": #3b5bdb,\n  \"9\": #364fc7\n);\n\n$oc-indigo-0: map-get($oc-indigo-list, \"0\");\n$oc-indigo-1: map-get($oc-indigo-list, \"1\");\n$oc-indigo-2: map-get($oc-indigo-list, \"2\");\n$oc-indigo-3: map-get($oc-indigo-list, \"3\");\n$oc-indigo-4: map-get($oc-indigo-list, \"4\");\n$oc-indigo-5: map-get($oc-indigo-list, \"5\");\n$oc-indigo-6: map-get($oc-indigo-list, \"6\");\n$oc-indigo-7: map-get($oc-indigo-list, \"7\");\n$oc-indigo-8: map-get($oc-indigo-list, \"8\");\n$oc-indigo-9: map-get($oc-indigo-list, \"9\");\n\n\n//  Blue\n//  ───────────────────────────────────\n\n$oc-blue-list: (\n  \"0\": #e7f5ff,\n  \"1\": #d0ebff,\n  \"2\": #a5d8ff,\n  \"3\": #74c0fc,\n  \"4\": #4dabf7,\n  \"5\": #339af0,\n  \"6\": #228be6,\n  \"7\": #1c7ed6,\n  \"8\": #1971c2,\n  \"9\": #1864ab\n);\n\n$oc-blue-0: map-get($oc-blue-list, \"0\");\n$oc-blue-1: map-get($oc-blue-list, \"1\");\n$oc-blue-2: map-get($oc-blue-list, \"2\");\n$oc-blue-3: map-get($oc-blue-list, \"3\");\n$oc-blue-4: map-get($oc-blue-list, \"4\");\n$oc-blue-5: map-get($oc-blue-list, \"5\");\n$oc-blue-6: map-get($oc-blue-list, \"6\");\n$oc-blue-7: map-get($oc-blue-list, \"7\");\n$oc-blue-8: map-get($oc-blue-list, \"8\");\n$oc-blue-9: map-get($oc-blue-list, \"9\");\n\n\n//  Cyan\n//  ───────────────────────────────────\n\n$oc-cyan-list: (\n  \"0\": #e3fafc,\n  \"1\": #c5f6fa,\n  \"2\": #99e9f2,\n  \"3\": #66d9e8,\n  \"4\": #3bc9db,\n  \"5\": #22b8cf,\n  \"6\": #15aabf,\n  \"7\": #1098ad,\n  \"8\": #0c8599,\n  \"9\": #0b7285\n);\n\n$oc-cyan-0: map-get($oc-cyan-list, \"0\");\n$oc-cyan-1: map-get($oc-cyan-list, \"1\");\n$oc-cyan-2: map-get($oc-cyan-list, \"2\");\n$oc-cyan-3: map-get($oc-cyan-list, \"3\");\n$oc-cyan-4: map-get($oc-cyan-list, \"4\");\n$oc-cyan-5: map-get($oc-cyan-list, \"5\");\n$oc-cyan-6: map-get($oc-cyan-list, \"6\");\n$oc-cyan-7: map-get($oc-cyan-list, \"7\");\n$oc-cyan-8: map-get($oc-cyan-list, \"8\");\n$oc-cyan-9: map-get($oc-cyan-list, \"9\");\n\n\n//  Teal\n//  ───────────────────────────────────\n\n$oc-teal-list: (\n  \"0\": #e6fcf5,\n  \"1\": #c3fae8,\n  \"2\": #96f2d7,\n  \"3\": #63e6be,\n  \"4\": #38d9a9,\n  \"5\": #20c997,\n  \"6\": #12b886,\n  \"7\": #0ca678,\n  \"8\": #099268,\n  \"9\": #087f5b\n);\n\n$oc-teal-0: map-get($oc-teal-list, \"0\");\n$oc-teal-1: map-get($oc-teal-list, \"1\");\n$oc-teal-2: map-get($oc-teal-list, \"2\");\n$oc-teal-3: map-get($oc-teal-list, \"3\");\n$oc-teal-4: map-get($oc-teal-list, \"4\");\n$oc-teal-5: map-get($oc-teal-list, \"5\");\n$oc-teal-6: map-get($oc-teal-list, \"6\");\n$oc-teal-7: map-get($oc-teal-list, \"7\");\n$oc-teal-8: map-get($oc-teal-list, \"8\");\n$oc-teal-9: map-get($oc-teal-list, \"9\");\n\n\n//  Green\n//  ───────────────────────────────────\n\n$oc-green-list: (\n  \"0\": #ebfbee,\n  \"1\": #d3f9d8,\n  \"2\": #b2f2bb,\n  \"3\": #8ce99a,\n  \"4\": #69db7c,\n  \"5\": #51cf66,\n  \"6\": #40c057,\n  \"7\": #37b24d,\n  \"8\": #2f9e44,\n  \"9\": #2b8a3e\n);\n\n$oc-green-0: map-get($oc-green-list, \"0\");\n$oc-green-1: map-get($oc-green-list, \"1\");\n$oc-green-2: map-get($oc-green-list, \"2\");\n$oc-green-3: map-get($oc-green-list, \"3\");\n$oc-green-4: map-get($oc-green-list, \"4\");\n$oc-green-5: map-get($oc-green-list, \"5\");\n$oc-green-6: map-get($oc-green-list, \"6\");\n$oc-green-7: map-get($oc-green-list, \"7\");\n$oc-green-8: map-get($oc-green-list, \"8\");\n$oc-green-9: map-get($oc-green-list, \"9\");\n\n\n//  Lime\n//  ───────────────────────────────────\n\n$oc-lime-list: (\n  \"0\": #f4fce3,\n  \"1\": #e9fac8,\n  \"2\": #d8f5a2,\n  \"3\": #c0eb75,\n  \"4\": #a9e34b,\n  \"5\": #94d82d,\n  \"6\": #82c91e,\n  \"7\": #74b816,\n  \"8\": #66a80f,\n  \"9\": #5c940d\n);\n\n$oc-lime-0: map-get($oc-lime-list, \"0\");\n$oc-lime-1: map-get($oc-lime-list, \"1\");\n$oc-lime-2: map-get($oc-lime-list, \"2\");\n$oc-lime-3: map-get($oc-lime-list, \"3\");\n$oc-lime-4: map-get($oc-lime-list, \"4\");\n$oc-lime-5: map-get($oc-lime-list, \"5\");\n$oc-lime-6: map-get($oc-lime-list, \"6\");\n$oc-lime-7: map-get($oc-lime-list, \"7\");\n$oc-lime-8: map-get($oc-lime-list, \"8\");\n$oc-lime-9: map-get($oc-lime-list, \"9\");\n\n\n//  Yellow\n//  ───────────────────────────────────\n\n$oc-yellow-list: (\n  \"0\": #fff9db,\n  \"1\": #fff3bf,\n  \"2\": #ffec99,\n  \"3\": #ffe066,\n  \"4\": #ffd43b,\n  \"5\": #fcc419,\n  \"6\": #fab005,\n  \"7\": #f59f00,\n  \"8\": #f08c00,\n  \"9\": #e67700\n);\n\n$oc-yellow-0: map-get($oc-yellow-list, \"0\");\n$oc-yellow-1: map-get($oc-yellow-list, \"1\");\n$oc-yellow-2: map-get($oc-yellow-list, \"2\");\n$oc-yellow-3: map-get($oc-yellow-list, \"3\");\n$oc-yellow-4: map-get($oc-yellow-list, \"4\");\n$oc-yellow-5: map-get($oc-yellow-list, \"5\");\n$oc-yellow-6: map-get($oc-yellow-list, \"6\");\n$oc-yellow-7: map-get($oc-yellow-list, \"7\");\n$oc-yellow-8: map-get($oc-yellow-list, \"8\");\n$oc-yellow-9: map-get($oc-yellow-list, \"9\");\n\n\n//  Orange\n//  ───────────────────────────────────\n\n$oc-orange-list: (\n  \"0\": #fff4e6,\n  \"1\": #ffe8cc,\n  \"2\": #ffd8a8,\n  \"3\": #ffc078,\n  \"4\": #ffa94d,\n  \"5\": #ff922b,\n  \"6\": #fd7e14,\n  \"7\": #f76707,\n  \"8\": #e8590c,\n  \"9\": #d9480f\n);\n\n$oc-orange-0: map-get($oc-orange-list, \"0\");\n$oc-orange-1: map-get($oc-orange-list, \"1\");\n$oc-orange-2: map-get($oc-orange-list, \"2\");\n$oc-orange-3: map-get($oc-orange-list, \"3\");\n$oc-orange-4: map-get($oc-orange-list, \"4\");\n$oc-orange-5: map-get($oc-orange-list, \"5\");\n$oc-orange-6: map-get($oc-orange-list, \"6\");\n$oc-orange-7: map-get($oc-orange-list, \"7\");\n$oc-orange-8: map-get($oc-orange-list, \"8\");\n$oc-orange-9: map-get($oc-orange-list, \"9\");\n\n\n//  Color list\n//  ───────────────────────────────────\n\n$oc-color-spectrum:   9;\n\n$oc-color-list: (\n  $oc-gray-list:      \"gray\",\n  $oc-red-list:      \"red\",\n  $oc-pink-list:      \"pink\",\n  $oc-grape-list:      \"grape\",\n  $oc-violet-list:      \"violet\",\n  $oc-indigo-list:      \"indigo\",\n  $oc-blue-list:      \"blue\",\n  $oc-cyan-list:      \"cyan\",\n  $oc-teal-list:      \"teal\",\n  $oc-green-list:      \"green\",\n  $oc-lime-list:      \"lime\",\n  $oc-yellow-list:      \"yellow\",\n  $oc-orange-list:      \"orange\"\n);\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .ExportDialog__preview {\n    --preview-padding: calc(var(--space-factor) * 4);\n\n    background: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==\")\n      left center;\n    text-align: center;\n    padding: var(--preview-padding);\n    margin-bottom: calc(var(--space-factor) * 3);\n\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n\n  .ExportDialog__preview canvas {\n    max-width: calc(100% - var(--preview-padding) * 2);\n    max-height: 25rem;\n  }\n\n  &.theme--dark .ExportDialog__preview canvas {\n    filter: none;\n  }\n\n  .ExportDialog__actions {\n    width: 100%;\n    display: flex;\n    grid-gap: calc(var(--space-factor) * 2);\n    align-items: top;\n    justify-content: space-between;\n  }\n\n  @include isMobile {\n    .ExportDialog {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .ExportDialog__actions {\n      flex-direction: column;\n      align-items: center;\n    }\n\n    .ExportDialog__actions > * {\n      margin-bottom: calc(var(--space-factor) * 3);\n    }\n\n    .ExportDialog__preview canvas {\n      max-height: 30vh;\n    }\n\n    .ExportDialog__dialog,\n    .ExportDialog__dialog .Island {\n      height: 100%;\n      box-sizing: border-box;\n    }\n\n    .ExportDialog__dialog .Island {\n      overflow-y: auto;\n    }\n  }\n\n  .ExportDialog--json {\n    .ExportDialog-cards {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      justify-items: center;\n      row-gap: 2em;\n\n      @media (max-width: 460px) {\n        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n        .Card-details {\n          min-height: 40px;\n        }\n      }\n\n      .ProjectName {\n        width: fit-content;\n        margin: 1em auto;\n        align-items: flex-start;\n        flex-direction: column;\n\n        .TextInput {\n          width: auto;\n        }\n      }\n\n      .ProjectName-label {\n        margin: 0.625em 0;\n        font-weight: bold;\n      }\n    }\n  }\n\n  button.ExportDialog-imageExportButton {\n    border: 0;\n\n    width: 5rem;\n    height: 5rem;\n    margin: 0 0.2em;\n    padding: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    border-radius: 1rem;\n    background-color: var(--button-color);\n    box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.28),\n      0 6px 10px 0 rgba(0, 0, 0, 0.14);\n\n    font-family: Cascadia;\n    font-size: 1.8em;\n    color: $oc-white;\n\n    &:hover {\n      background-color: var(--button-color-darker);\n    }\n    &:active {\n      background-color: var(--button-color-darkest);\n      box-shadow: none;\n    }\n\n    svg {\n      width: 0.9em;\n    }\n  }\n}\n", "//\n//\n//  𝗖 𝗢 𝗟 𝗢 𝗥\n//  v 1.9.1\n//\n//  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n\n//  General\n//  ───────────────────────────────────\n\n$oc-white:         #ffffff;\n$oc-black:         #000000;\n\n\n//  Gray\n//  ───────────────────────────────────\n\n$oc-gray-list: (\n  \"0\": #f8f9fa,\n  \"1\": #f1f3f5,\n  \"2\": #e9ecef,\n  \"3\": #dee2e6,\n  \"4\": #ced4da,\n  \"5\": #adb5bd,\n  \"6\": #868e96,\n  \"7\": #495057,\n  \"8\": #343a40,\n  \"9\": #212529\n);\n\n$oc-gray-0: map-get($oc-gray-list, \"0\");\n$oc-gray-1: map-get($oc-gray-list, \"1\");\n$oc-gray-2: map-get($oc-gray-list, \"2\");\n$oc-gray-3: map-get($oc-gray-list, \"3\");\n$oc-gray-4: map-get($oc-gray-list, \"4\");\n$oc-gray-5: map-get($oc-gray-list, \"5\");\n$oc-gray-6: map-get($oc-gray-list, \"6\");\n$oc-gray-7: map-get($oc-gray-list, \"7\");\n$oc-gray-8: map-get($oc-gray-list, \"8\");\n$oc-gray-9: map-get($oc-gray-list, \"9\");\n\n\n//  Red\n//  ───────────────────────────────────\n\n$oc-red-list: (\n  \"0\": #fff5f5,\n  \"1\": #ffe3e3,\n  \"2\": #ffc9c9,\n  \"3\": #ffa8a8,\n  \"4\": #ff8787,\n  \"5\": #ff6b6b,\n  \"6\": #fa5252,\n  \"7\": #f03e3e,\n  \"8\": #e03131,\n  \"9\": #c92a2a\n);\n\n$oc-red-0: map-get($oc-red-list, \"0\");\n$oc-red-1: map-get($oc-red-list, \"1\");\n$oc-red-2: map-get($oc-red-list, \"2\");\n$oc-red-3: map-get($oc-red-list, \"3\");\n$oc-red-4: map-get($oc-red-list, \"4\");\n$oc-red-5: map-get($oc-red-list, \"5\");\n$oc-red-6: map-get($oc-red-list, \"6\");\n$oc-red-7: map-get($oc-red-list, \"7\");\n$oc-red-8: map-get($oc-red-list, \"8\");\n$oc-red-9: map-get($oc-red-list, \"9\");\n\n\n//  Pink\n//  ───────────────────────────────────\n\n$oc-pink-list: (\n  \"0\": #fff0f6,\n  \"1\": #ffdeeb,\n  \"2\": #fcc2d7,\n  \"3\": #faa2c1,\n  \"4\": #f783ac,\n  \"5\": #f06595,\n  \"6\": #e64980,\n  \"7\": #d6336c,\n  \"8\": #c2255c,\n  \"9\": #a61e4d\n);\n\n$oc-pink-0: map-get($oc-pink-list, \"0\");\n$oc-pink-1: map-get($oc-pink-list, \"1\");\n$oc-pink-2: map-get($oc-pink-list, \"2\");\n$oc-pink-3: map-get($oc-pink-list, \"3\");\n$oc-pink-4: map-get($oc-pink-list, \"4\");\n$oc-pink-5: map-get($oc-pink-list, \"5\");\n$oc-pink-6: map-get($oc-pink-list, \"6\");\n$oc-pink-7: map-get($oc-pink-list, \"7\");\n$oc-pink-8: map-get($oc-pink-list, \"8\");\n$oc-pink-9: map-get($oc-pink-list, \"9\");\n\n\n//  Grape\n//  ───────────────────────────────────\n\n$oc-grape-list: (\n  \"0\": #f8f0fc,\n  \"1\": #f3d9fa,\n  \"2\": #eebefa,\n  \"3\": #e599f7,\n  \"4\": #da77f2,\n  \"5\": #cc5de8,\n  \"6\": #be4bdb,\n  \"7\": #ae3ec9,\n  \"8\": #9c36b5,\n  \"9\": #862e9c\n);\n\n$oc-grape-0: map-get($oc-grape-list, \"0\");\n$oc-grape-1: map-get($oc-grape-list, \"1\");\n$oc-grape-2: map-get($oc-grape-list, \"2\");\n$oc-grape-3: map-get($oc-grape-list, \"3\");\n$oc-grape-4: map-get($oc-grape-list, \"4\");\n$oc-grape-5: map-get($oc-grape-list, \"5\");\n$oc-grape-6: map-get($oc-grape-list, \"6\");\n$oc-grape-7: map-get($oc-grape-list, \"7\");\n$oc-grape-8: map-get($oc-grape-list, \"8\");\n$oc-grape-9: map-get($oc-grape-list, \"9\");\n\n\n//  Violet\n//  ───────────────────────────────────\n\n$oc-violet-list: (\n  \"0\": #f3f0ff,\n  \"1\": #e5dbff,\n  \"2\": #d0bfff,\n  \"3\": #b197fc,\n  \"4\": #9775fa,\n  \"5\": #845ef7,\n  \"6\": #7950f2,\n  \"7\": #7048e8,\n  \"8\": #6741d9,\n  \"9\": #5f3dc4\n);\n\n$oc-violet-0: map-get($oc-violet-list, \"0\");\n$oc-violet-1: map-get($oc-violet-list, \"1\");\n$oc-violet-2: map-get($oc-violet-list, \"2\");\n$oc-violet-3: map-get($oc-violet-list, \"3\");\n$oc-violet-4: map-get($oc-violet-list, \"4\");\n$oc-violet-5: map-get($oc-violet-list, \"5\");\n$oc-violet-6: map-get($oc-violet-list, \"6\");\n$oc-violet-7: map-get($oc-violet-list, \"7\");\n$oc-violet-8: map-get($oc-violet-list, \"8\");\n$oc-violet-9: map-get($oc-violet-list, \"9\");\n\n\n//  Indigo\n//  ───────────────────────────────────\n\n$oc-indigo-list: (\n  \"0\": #edf2ff,\n  \"1\": #dbe4ff,\n  \"2\": #bac8ff,\n  \"3\": #91a7ff,\n  \"4\": #748ffc,\n  \"5\": #5c7cfa,\n  \"6\": #4c6ef5,\n  \"7\": #4263eb,\n  \"8\": #3b5bdb,\n  \"9\": #364fc7\n);\n\n$oc-indigo-0: map-get($oc-indigo-list, \"0\");\n$oc-indigo-1: map-get($oc-indigo-list, \"1\");\n$oc-indigo-2: map-get($oc-indigo-list, \"2\");\n$oc-indigo-3: map-get($oc-indigo-list, \"3\");\n$oc-indigo-4: map-get($oc-indigo-list, \"4\");\n$oc-indigo-5: map-get($oc-indigo-list, \"5\");\n$oc-indigo-6: map-get($oc-indigo-list, \"6\");\n$oc-indigo-7: map-get($oc-indigo-list, \"7\");\n$oc-indigo-8: map-get($oc-indigo-list, \"8\");\n$oc-indigo-9: map-get($oc-indigo-list, \"9\");\n\n\n//  Blue\n//  ───────────────────────────────────\n\n$oc-blue-list: (\n  \"0\": #e7f5ff,\n  \"1\": #d0ebff,\n  \"2\": #a5d8ff,\n  \"3\": #74c0fc,\n  \"4\": #4dabf7,\n  \"5\": #339af0,\n  \"6\": #228be6,\n  \"7\": #1c7ed6,\n  \"8\": #1971c2,\n  \"9\": #1864ab\n);\n\n$oc-blue-0: map-get($oc-blue-list, \"0\");\n$oc-blue-1: map-get($oc-blue-list, \"1\");\n$oc-blue-2: map-get($oc-blue-list, \"2\");\n$oc-blue-3: map-get($oc-blue-list, \"3\");\n$oc-blue-4: map-get($oc-blue-list, \"4\");\n$oc-blue-5: map-get($oc-blue-list, \"5\");\n$oc-blue-6: map-get($oc-blue-list, \"6\");\n$oc-blue-7: map-get($oc-blue-list, \"7\");\n$oc-blue-8: map-get($oc-blue-list, \"8\");\n$oc-blue-9: map-get($oc-blue-list, \"9\");\n\n\n//  Cyan\n//  ───────────────────────────────────\n\n$oc-cyan-list: (\n  \"0\": #e3fafc,\n  \"1\": #c5f6fa,\n  \"2\": #99e9f2,\n  \"3\": #66d9e8,\n  \"4\": #3bc9db,\n  \"5\": #22b8cf,\n  \"6\": #15aabf,\n  \"7\": #1098ad,\n  \"8\": #0c8599,\n  \"9\": #0b7285\n);\n\n$oc-cyan-0: map-get($oc-cyan-list, \"0\");\n$oc-cyan-1: map-get($oc-cyan-list, \"1\");\n$oc-cyan-2: map-get($oc-cyan-list, \"2\");\n$oc-cyan-3: map-get($oc-cyan-list, \"3\");\n$oc-cyan-4: map-get($oc-cyan-list, \"4\");\n$oc-cyan-5: map-get($oc-cyan-list, \"5\");\n$oc-cyan-6: map-get($oc-cyan-list, \"6\");\n$oc-cyan-7: map-get($oc-cyan-list, \"7\");\n$oc-cyan-8: map-get($oc-cyan-list, \"8\");\n$oc-cyan-9: map-get($oc-cyan-list, \"9\");\n\n\n//  Teal\n//  ───────────────────────────────────\n\n$oc-teal-list: (\n  \"0\": #e6fcf5,\n  \"1\": #c3fae8,\n  \"2\": #96f2d7,\n  \"3\": #63e6be,\n  \"4\": #38d9a9,\n  \"5\": #20c997,\n  \"6\": #12b886,\n  \"7\": #0ca678,\n  \"8\": #099268,\n  \"9\": #087f5b\n);\n\n$oc-teal-0: map-get($oc-teal-list, \"0\");\n$oc-teal-1: map-get($oc-teal-list, \"1\");\n$oc-teal-2: map-get($oc-teal-list, \"2\");\n$oc-teal-3: map-get($oc-teal-list, \"3\");\n$oc-teal-4: map-get($oc-teal-list, \"4\");\n$oc-teal-5: map-get($oc-teal-list, \"5\");\n$oc-teal-6: map-get($oc-teal-list, \"6\");\n$oc-teal-7: map-get($oc-teal-list, \"7\");\n$oc-teal-8: map-get($oc-teal-list, \"8\");\n$oc-teal-9: map-get($oc-teal-list, \"9\");\n\n\n//  Green\n//  ───────────────────────────────────\n\n$oc-green-list: (\n  \"0\": #ebfbee,\n  \"1\": #d3f9d8,\n  \"2\": #b2f2bb,\n  \"3\": #8ce99a,\n  \"4\": #69db7c,\n  \"5\": #51cf66,\n  \"6\": #40c057,\n  \"7\": #37b24d,\n  \"8\": #2f9e44,\n  \"9\": #2b8a3e\n);\n\n$oc-green-0: map-get($oc-green-list, \"0\");\n$oc-green-1: map-get($oc-green-list, \"1\");\n$oc-green-2: map-get($oc-green-list, \"2\");\n$oc-green-3: map-get($oc-green-list, \"3\");\n$oc-green-4: map-get($oc-green-list, \"4\");\n$oc-green-5: map-get($oc-green-list, \"5\");\n$oc-green-6: map-get($oc-green-list, \"6\");\n$oc-green-7: map-get($oc-green-list, \"7\");\n$oc-green-8: map-get($oc-green-list, \"8\");\n$oc-green-9: map-get($oc-green-list, \"9\");\n\n\n//  Lime\n//  ───────────────────────────────────\n\n$oc-lime-list: (\n  \"0\": #f4fce3,\n  \"1\": #e9fac8,\n  \"2\": #d8f5a2,\n  \"3\": #c0eb75,\n  \"4\": #a9e34b,\n  \"5\": #94d82d,\n  \"6\": #82c91e,\n  \"7\": #74b816,\n  \"8\": #66a80f,\n  \"9\": #5c940d\n);\n\n$oc-lime-0: map-get($oc-lime-list, \"0\");\n$oc-lime-1: map-get($oc-lime-list, \"1\");\n$oc-lime-2: map-get($oc-lime-list, \"2\");\n$oc-lime-3: map-get($oc-lime-list, \"3\");\n$oc-lime-4: map-get($oc-lime-list, \"4\");\n$oc-lime-5: map-get($oc-lime-list, \"5\");\n$oc-lime-6: map-get($oc-lime-list, \"6\");\n$oc-lime-7: map-get($oc-lime-list, \"7\");\n$oc-lime-8: map-get($oc-lime-list, \"8\");\n$oc-lime-9: map-get($oc-lime-list, \"9\");\n\n\n//  Yellow\n//  ───────────────────────────────────\n\n$oc-yellow-list: (\n  \"0\": #fff9db,\n  \"1\": #fff3bf,\n  \"2\": #ffec99,\n  \"3\": #ffe066,\n  \"4\": #ffd43b,\n  \"5\": #fcc419,\n  \"6\": #fab005,\n  \"7\": #f59f00,\n  \"8\": #f08c00,\n  \"9\": #e67700\n);\n\n$oc-yellow-0: map-get($oc-yellow-list, \"0\");\n$oc-yellow-1: map-get($oc-yellow-list, \"1\");\n$oc-yellow-2: map-get($oc-yellow-list, \"2\");\n$oc-yellow-3: map-get($oc-yellow-list, \"3\");\n$oc-yellow-4: map-get($oc-yellow-list, \"4\");\n$oc-yellow-5: map-get($oc-yellow-list, \"5\");\n$oc-yellow-6: map-get($oc-yellow-list, \"6\");\n$oc-yellow-7: map-get($oc-yellow-list, \"7\");\n$oc-yellow-8: map-get($oc-yellow-list, \"8\");\n$oc-yellow-9: map-get($oc-yellow-list, \"9\");\n\n\n//  Orange\n//  ───────────────────────────────────\n\n$oc-orange-list: (\n  \"0\": #fff4e6,\n  \"1\": #ffe8cc,\n  \"2\": #ffd8a8,\n  \"3\": #ffc078,\n  \"4\": #ffa94d,\n  \"5\": #ff922b,\n  \"6\": #fd7e14,\n  \"7\": #f76707,\n  \"8\": #e8590c,\n  \"9\": #d9480f\n);\n\n$oc-orange-0: map-get($oc-orange-list, \"0\");\n$oc-orange-1: map-get($oc-orange-list, \"1\");\n$oc-orange-2: map-get($oc-orange-list, \"2\");\n$oc-orange-3: map-get($oc-orange-list, \"3\");\n$oc-orange-4: map-get($oc-orange-list, \"4\");\n$oc-orange-5: map-get($oc-orange-list, \"5\");\n$oc-orange-6: map-get($oc-orange-list, \"6\");\n$oc-orange-7: map-get($oc-orange-list, \"7\");\n$oc-orange-8: map-get($oc-orange-list, \"8\");\n$oc-orange-9: map-get($oc-orange-list, \"9\");\n\n\n//  Color list\n//  ───────────────────────────────────\n\n$oc-color-spectrum:   9;\n\n$oc-color-list: (\n  $oc-gray-list:      \"gray\",\n  $oc-red-list:      \"red\",\n  $oc-pink-list:      \"pink\",\n  $oc-grape-list:      \"grape\",\n  $oc-violet-list:      \"violet\",\n  $oc-indigo-list:      \"indigo\",\n  $oc-blue-list:      \"blue\",\n  $oc-cyan-list:      \"cyan\",\n  $oc-teal-list:      \"teal\",\n  $oc-green-list:      \"green\",\n  $oc-lime-list:      \"lime\",\n  $oc-yellow-list:      \"yellow\",\n  $oc-orange-list:      \"orange\"\n);\n", "@import \"../../css/variables.module.scss\";\n\n.excalidraw {\n  .sidebar-trigger {\n    @include outlineButtonStyles;\n    @include filledButtonOnCanvas;\n\n    width: auto;\n    height: var(--lg-button-size);\n\n    display: flex;\n    align-items: center;\n    gap: 0.5rem;\n\n    line-height: 0;\n\n    font-size: 0.75rem;\n    letter-spacing: 0.4px;\n\n    svg {\n      width: var(--lg-icon-size);\n      height: var(--lg-icon-size);\n    }\n\n    &__label-element {\n      align-self: flex-start;\n    }\n  }\n\n  .default-sidebar-trigger .sidebar-trigger__label {\n    display: block;\n    white-space: nowrap;\n  }\n\n  &.excalidraw--mobile .default-sidebar-trigger .sidebar-trigger__label {\n    display: none;\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "@import \"open-color/open-color\";\n@import \"../../css/variables.module.scss\";\n\n.excalidraw {\n  .sidebar {\n    display: flex;\n    flex-direction: column;\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    right: 0;\n    z-index: 5;\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n\n    background-color: var(--sidebar-bg-color);\n    box-shadow: var(--sidebar-shadow);\n\n    pointer-events: var(--ui-pointerEvents);\n\n    :root[dir=\"rtl\"] & {\n      left: 0;\n      right: auto;\n    }\n\n    &--docked {\n      box-shadow: none;\n    }\n\n    overflow: hidden;\n    border-radius: 0;\n    width: calc(var(--right-sidebar-width) - var(--space-factor) * 2);\n\n    border-left: 1px solid var(--sidebar-border-color);\n\n    :root[dir=\"rtl\"] & {\n      border-right: 1px solid var(--sidebar-border-color);\n      border-left: 0;\n    }\n  }\n\n  // ---------------------------- sidebar header ------------------------------\n\n  .sidebar__header {\n    box-sizing: border-box;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    width: 100%;\n    padding: 1rem 0.75rem;\n    position: relative;\n\n    &::after {\n      content: \"\";\n      width: calc(100% - 1.5rem);\n      height: 1px;\n      background: var(--sidebar-border-color);\n      position: absolute;\n      bottom: -1px;\n    }\n  }\n\n  .sidebar__header__buttons {\n    gap: 0;\n    display: flex;\n    align-items: center;\n    margin-left: auto;\n\n    button {\n      @include outlineButtonStyles;\n      --button-bg: transparent;\n      border: 0 !important;\n\n      width: var(--lg-button-size);\n      height: var(--lg-button-size);\n      padding: 0;\n\n      svg {\n        width: var(--lg-icon-size);\n        height: var(--lg-icon-size);\n      }\n\n      &:hover {\n        background: var(--button-hover-bg, var(--island-bg-color));\n      }\n    }\n\n    .sidebar__dock.selected {\n      svg {\n        stroke: var(--color-primary);\n        fill: var(--color-primary);\n      }\n    }\n  }\n\n  // ---------------------------- sidebar tabs ------------------------------\n\n  .sidebar-tabs-root {\n    display: flex;\n    flex-direction: column;\n    flex: 1 1 auto;\n    padding: 1rem 0;\n\n    [role=\"tabpanel\"] {\n      flex: 1;\n      outline: none;\n\n      flex: 1 1 auto;\n      display: flex;\n      flex-direction: column;\n      outline: none;\n    }\n\n    [role=\"tabpanel\"][data-state=\"inactive\"] {\n      display: none !important;\n    }\n\n    [role=\"tablist\"] {\n      display: grid;\n      gap: 1rem;\n      grid-template-columns: repeat(auto-fit, minmax(0, 1fr));\n    }\n  }\n\n  .sidebar-tabs-root > .sidebar__header {\n    padding-top: 0;\n    padding-bottom: 1rem;\n  }\n\n  .sidebar-tab-trigger {\n    --button-width: auto;\n    --button-bg: transparent;\n    --button-hover-bg: transparent;\n    --button-active-bg: var(--color-primary);\n    --button-hover-color: var(--color-primary);\n    --button-hover-border: var(--color-primary);\n\n    &[data-state=\"active\"] {\n      --button-bg: var(--color-primary);\n      --button-hover-bg: var(--color-primary-darker);\n      --button-hover-color: var(--color-icon-white);\n      --button-border: var(--color-primary);\n      color: var(--color-icon-white);\n    }\n  }\n\n  // ---------------------------- default sidebar ------------------------------\n\n  .default-sidebar {\n    display: flex;\n    flex-direction: column;\n\n    .sidebar-triggers {\n      $padding: 2px;\n      $border: 1px;\n      display: flex;\n      gap: 0;\n      padding: $padding;\n      // offset by padding + border to vertically center the list with sibling\n      // buttons (both from top and bototm, due to flex layout)\n      margin-top: -#{$padding + $border};\n      margin-bottom: -#{$padding + $border};\n      border: $border solid var(--sidebar-border-color);\n      background: var(--default-bg-color);\n      border-radius: 0.625rem;\n\n      .sidebar-tab-trigger {\n        height: var(--lg-button-size);\n        width: var(--lg-button-size);\n\n        border: none;\n      }\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", ".excalidraw {\n  .ActiveFile {\n    .ActiveFile__fileName {\n      display: flex;\n      align-items: center;\n\n      span {\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap;\n        width: 9.3em;\n      }\n\n      svg {\n        width: 1.15em;\n        margin-inline-end: 0.3em;\n        transform: scaleY(0.9);\n      }\n    }\n  }\n}\n", "@import \"../../css/variables.module.scss\";\n\n.excalidraw {\n  .OverwriteConfirm {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 0.75rem;\n    isolation: isolate;\n\n    h3 {\n      margin: 0;\n\n      font-weight: 700;\n      font-size: 1.3125rem;\n      line-height: 130%;\n      align-self: flex-start;\n\n      color: var(--text-primary-color);\n    }\n\n    &__Description {\n      box-sizing: border-box;\n\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n      width: 100%;\n      gap: 1rem;\n\n      @include isMobile {\n        flex-direction: column;\n        text-align: center;\n      }\n\n      padding: 2.5rem;\n\n      background: var(--color-danger-background);\n      border-radius: 0.5rem;\n\n      font-family: \"Assistant\";\n      font-style: normal;\n      font-weight: 400;\n      font-size: 1rem;\n      line-height: 150%;\n\n      color: var(--color-danger-color);\n\n      &__spacer {\n        flex-grow: 1;\n      }\n\n      &__icon {\n        box-sizing: border-box;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        border-radius: 2.5rem;\n        background: var(--color-danger-icon-background);\n        width: 3.5rem;\n        height: 3.5rem;\n\n        padding: 0.75rem;\n\n        svg {\n          color: var(--color-danger-icon-color);\n          width: 1.5rem;\n          height: 1.5rem;\n        }\n      }\n\n      &.OverwriteConfirm__Description--color-warning {\n        background: var(--color-warning-background);\n        color: var(--color-warning-color);\n\n        .OverwriteConfirm__Description__icon {\n          background: var(--color-warning-icon-background);\n          flex: 0 0 auto;\n\n          svg {\n            color: var(--color-warning-icon-color);\n          }\n        }\n      }\n    }\n\n    &__Actions {\n      display: flex;\n      flex-direction: row;\n      align-items: stretch;\n      justify-items: stretch;\n      justify-content: center;\n      gap: 1.5rem;\n\n      @include isMobile {\n        flex-direction: column;\n      }\n\n      &__Action {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        padding: 1.5rem;\n        gap: 0.75rem;\n        flex-basis: 50%;\n        flex-grow: 0;\n\n        &__content {\n          height: 100%;\n          font-size: 0.875rem;\n          text-align: center;\n        }\n\n        h4 {\n          font-weight: 700;\n          font-size: 1.125rem;\n          line-height: 130%;\n\n          margin: 0;\n\n          color: var(--text-primary-color);\n        }\n      }\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "@import \"open-color/open-color\";\n\n.excalidraw {\n  .layer-ui__search {\n    flex: 1 0 auto;\n    display: flex;\n    flex-direction: column;\n    padding: 8px 0 0 0;\n  }\n\n  .layer-ui__search-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 0 0.75rem;\n    .ExcTextField {\n      flex: 1 0 auto;\n    }\n\n    .ExcTextField__input {\n      background-color: #f5f5f9;\n      @at-root .excalidraw.theme--dark#{&} {\n        background-color: #31303b;\n      }\n\n      border-radius: var(--border-radius-md);\n      border: 0;\n\n      input::placeholder {\n        font-size: 0.9rem;\n      }\n    }\n  }\n\n  .layer-ui__search-count {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 8px 8px 0 8px;\n    margin: 0 0.75rem 0.25rem 0.75rem;\n    font-size: 0.8em;\n\n    .result-nav {\n      display: flex;\n\n      .result-nav-btn {\n        width: 36px;\n        height: 36px;\n        --button-border: transparent;\n\n        &:active {\n          background-color: var(--color-surface-high);\n        }\n\n        &:first-child {\n          margin-right: 4px;\n        }\n      }\n    }\n  }\n\n  .layer-ui__search-result-container {\n    overflow-y: auto;\n    flex: 1 1 0;\n    display: flex;\n    flex-direction: column;\n\n    gap: 0.125rem;\n  }\n\n  .layer-ui__result-item {\n    display: flex;\n    align-items: center;\n    min-height: 2rem;\n    flex: 0 0 auto;\n    padding: 0.25rem 0.75rem;\n    cursor: pointer;\n    border: 1px solid transparent;\n    outline: none;\n\n    margin: 0 0.75rem;\n    border-radius: var(--border-radius-md);\n\n    .text-icon {\n      width: 1rem;\n      height: 1rem;\n      margin-right: 0.75rem;\n    }\n\n    .preview-text {\n      flex: 1;\n      max-height: 48px;\n      line-height: 24px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      word-break: break-all;\n    }\n\n    &:hover {\n      background-color: var(--color-surface-high);\n    }\n    &:active {\n      border-color: var(--color-primary);\n    }\n\n    &.active {\n      background-color: var(--color-surface-high);\n    }\n  }\n}\n", ".excalidraw {\n  .dialog-mermaid {\n    &-title {\n      margin-block: 0.25rem;\n      font-size: 1.25rem;\n      font-weight: 700;\n      padding-inline: 2.5rem;\n    }\n  }\n}\n", "@import \"../../css/variables.module.scss\";\n\n$verticalBreakpoint: 861px;\n\n.excalidraw {\n  .Modal.Dialog.ttd-dialog {\n    padding: 1.25rem;\n\n    &.Dialog--fullscreen {\n      margin-top: 0;\n    }\n\n    .Island {\n      padding-inline: 0 !important;\n      height: 100%;\n      display: flex;\n      flex-direction: column;\n      flex: 1 1 auto;\n      box-shadow: none;\n    }\n\n    .Modal__content {\n      height: auto;\n      max-height: 100%;\n\n      @media screen and (min-width: $verticalBreakpoint) {\n        max-height: 750px;\n        height: 100%;\n      }\n    }\n\n    .Dialog__content {\n      flex: 1 1 auto;\n    }\n  }\n\n  .ttd-dialog-desc {\n    font-size: 15px;\n    font-style: italic;\n    font-weight: 500;\n    margin-bottom: 1.5rem;\n  }\n\n  .ttd-dialog-tabs-root {\n    width: 100%;\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n  }\n\n  .ttd-dialog-tab-trigger {\n    color: var(--color-on-surface);\n    font-size: 0.875rem;\n    margin: 0;\n    padding: 0 1rem;\n    background-color: transparent;\n    border: 0;\n    height: 2.875rem;\n    font-weight: 600;\n    font-family: inherit;\n    letter-spacing: 0.4px;\n\n    &[data-state=\"active\"] {\n      border-bottom: 2px solid var(--color-primary);\n    }\n  }\n\n  .ttd-dialog-triggers {\n    border-bottom: 1px solid var(--color-surface-high);\n    margin-bottom: 1.5rem;\n    padding-inline: 2.5rem;\n  }\n\n  .ttd-dialog-content {\n    padding-inline: 2.5rem;\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n\n    &[hidden] {\n      display: none;\n    }\n  }\n\n  .ttd-dialog-input {\n    width: auto;\n    height: 10rem;\n    resize: none;\n    border-radius: var(--border-radius-lg);\n    border: 1px solid var(--dialog-border-color);\n    white-space: pre-wrap;\n    padding: 0.85rem;\n    box-sizing: border-box;\n    font-family: monospace;\n\n    @media screen and (min-width: $verticalBreakpoint) {\n      width: 100%;\n      height: 100%;\n    }\n  }\n\n  .ttd-dialog-output-wrapper {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 0.85rem;\n    box-sizing: border-box;\n    flex-grow: 1;\n    position: relative;\n\n    background: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==\")\n      left center;\n    border-radius: var(--border-radius-lg);\n    border: 1px solid var(--dialog-border-color);\n\n    height: 400px;\n    width: auto;\n\n    @media screen and (min-width: $verticalBreakpoint) {\n      width: 100%;\n      // acts as min-height\n      height: 200px;\n    }\n\n    canvas {\n      max-width: 100%;\n      max-height: 100%;\n    }\n  }\n\n  .ttd-dialog-output-canvas-container {\n    display: flex;\n    width: 100%;\n    height: 100%;\n    align-items: center;\n    justify-content: center;\n    flex-grow: 1;\n  }\n\n  .ttd-dialog-output-error {\n    color: red;\n    font-weight: 700;\n    font-size: 30px;\n    word-break: break-word;\n    overflow: auto;\n    max-height: 100%;\n    height: 100%;\n    width: 100%;\n    text-align: center;\n    position: absolute;\n    z-index: 10;\n\n    p {\n      font-weight: 500;\n      font-family: Cascadia;\n      text-align: left;\n      white-space: pre-wrap;\n      font-size: 0.875rem;\n      padding: 0 10px;\n    }\n  }\n\n  .ttd-dialog-panels {\n    height: 100%;\n\n    @media screen and (min-width: $verticalBreakpoint) {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 4rem;\n    }\n  }\n\n  .ttd-dialog-panel {\n    display: flex;\n    flex-direction: column;\n    width: 100%;\n\n    &__header {\n      display: flex;\n      margin: 0px 4px 4px 4px;\n      align-items: center;\n      gap: 1rem;\n\n      label {\n        font-size: 14px;\n        font-style: normal;\n        font-weight: 600;\n      }\n    }\n\n    &:first-child {\n      .ttd-dialog-panel-button-container:not(.invisible) {\n        margin-bottom: 4rem;\n      }\n    }\n\n    @media screen and (min-width: $verticalBreakpoint) {\n      .ttd-dialog-panel-button-container:not(.invisible) {\n        margin-bottom: 0.5rem !important;\n      }\n    }\n\n    textarea {\n      height: 100%;\n      resize: none;\n      border-radius: var(--border-radius-lg);\n      border: 1px solid var(--dialog-border-color);\n      white-space: pre-wrap;\n      padding: 0.85rem;\n      box-sizing: border-box;\n      width: 100%;\n      font-family: monospace;\n\n      @media screen and (max-width: $verticalBreakpoint) {\n        width: auto;\n        height: 10rem;\n      }\n    }\n  }\n\n  .ttd-dialog-panel-button-container {\n    margin-top: 1rem;\n    margin-bottom: 0.5rem;\n\n    &.invisible {\n      .ttd-dialog-panel-button {\n        display: none;\n\n        @media screen and (min-width: $verticalBreakpoint) {\n          display: block;\n          visibility: hidden;\n        }\n      }\n    }\n  }\n\n  .ttd-dialog-panel-button {\n    &.excalidraw-button {\n      font-family: inherit;\n      font-weight: 600;\n      height: 2.5rem;\n\n      font-size: 12px;\n      color: $oc-white;\n      background-color: var(--color-primary);\n      width: 100%;\n\n      &:hover {\n        background-color: var(--color-primary-darker);\n      }\n      &:active {\n        background-color: var(--color-primary-darkest);\n      }\n\n      &:disabled {\n        opacity: 0.5;\n        cursor: not-allowed;\n\n        &:hover {\n          background-color: var(--color-primary);\n        }\n      }\n\n      @media screen and (min-width: $verticalBreakpoint) {\n        width: auto;\n        min-width: 7.5rem;\n      }\n\n      @at-root .excalidraw.theme--dark#{&} {\n        color: var(--color-gray-100);\n      }\n    }\n\n    position: relative;\n\n    div {\n      display: contents;\n\n      &.invisible {\n        visibility: hidden;\n      }\n\n      &.Spinner {\n        display: flex !important;\n        position: absolute;\n        inset: 0;\n\n        --spinner-color: white;\n\n        @at-root .excalidraw.theme--dark#{&} {\n          --spinner-color: var(--color-gray-100);\n        }\n      }\n\n      span {\n        padding-left: 0.5rem;\n        display: flex;\n      }\n    }\n  }\n\n  .ttd-dialog-submit-shortcut {\n    margin-inline-start: 0.5rem;\n    font-size: 0.625rem;\n    opacity: 0.6;\n    display: flex;\n    gap: 0.125rem;\n\n    &__key {\n      border: 1px solid gray;\n      padding: 2px 3px;\n      border-radius: 4px;\n    }\n  }\n}\n", "//\n//\n//  𝗖 𝗢 𝗟 𝗢 𝗥\n//  v 1.9.1\n//\n//  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n\n//  General\n//  ───────────────────────────────────\n\n$oc-white:         #ffffff;\n$oc-black:         #000000;\n\n\n//  Gray\n//  ───────────────────────────────────\n\n$oc-gray-list: (\n  \"0\": #f8f9fa,\n  \"1\": #f1f3f5,\n  \"2\": #e9ecef,\n  \"3\": #dee2e6,\n  \"4\": #ced4da,\n  \"5\": #adb5bd,\n  \"6\": #868e96,\n  \"7\": #495057,\n  \"8\": #343a40,\n  \"9\": #212529\n);\n\n$oc-gray-0: map-get($oc-gray-list, \"0\");\n$oc-gray-1: map-get($oc-gray-list, \"1\");\n$oc-gray-2: map-get($oc-gray-list, \"2\");\n$oc-gray-3: map-get($oc-gray-list, \"3\");\n$oc-gray-4: map-get($oc-gray-list, \"4\");\n$oc-gray-5: map-get($oc-gray-list, \"5\");\n$oc-gray-6: map-get($oc-gray-list, \"6\");\n$oc-gray-7: map-get($oc-gray-list, \"7\");\n$oc-gray-8: map-get($oc-gray-list, \"8\");\n$oc-gray-9: map-get($oc-gray-list, \"9\");\n\n\n//  Red\n//  ───────────────────────────────────\n\n$oc-red-list: (\n  \"0\": #fff5f5,\n  \"1\": #ffe3e3,\n  \"2\": #ffc9c9,\n  \"3\": #ffa8a8,\n  \"4\": #ff8787,\n  \"5\": #ff6b6b,\n  \"6\": #fa5252,\n  \"7\": #f03e3e,\n  \"8\": #e03131,\n  \"9\": #c92a2a\n);\n\n$oc-red-0: map-get($oc-red-list, \"0\");\n$oc-red-1: map-get($oc-red-list, \"1\");\n$oc-red-2: map-get($oc-red-list, \"2\");\n$oc-red-3: map-get($oc-red-list, \"3\");\n$oc-red-4: map-get($oc-red-list, \"4\");\n$oc-red-5: map-get($oc-red-list, \"5\");\n$oc-red-6: map-get($oc-red-list, \"6\");\n$oc-red-7: map-get($oc-red-list, \"7\");\n$oc-red-8: map-get($oc-red-list, \"8\");\n$oc-red-9: map-get($oc-red-list, \"9\");\n\n\n//  Pink\n//  ───────────────────────────────────\n\n$oc-pink-list: (\n  \"0\": #fff0f6,\n  \"1\": #ffdeeb,\n  \"2\": #fcc2d7,\n  \"3\": #faa2c1,\n  \"4\": #f783ac,\n  \"5\": #f06595,\n  \"6\": #e64980,\n  \"7\": #d6336c,\n  \"8\": #c2255c,\n  \"9\": #a61e4d\n);\n\n$oc-pink-0: map-get($oc-pink-list, \"0\");\n$oc-pink-1: map-get($oc-pink-list, \"1\");\n$oc-pink-2: map-get($oc-pink-list, \"2\");\n$oc-pink-3: map-get($oc-pink-list, \"3\");\n$oc-pink-4: map-get($oc-pink-list, \"4\");\n$oc-pink-5: map-get($oc-pink-list, \"5\");\n$oc-pink-6: map-get($oc-pink-list, \"6\");\n$oc-pink-7: map-get($oc-pink-list, \"7\");\n$oc-pink-8: map-get($oc-pink-list, \"8\");\n$oc-pink-9: map-get($oc-pink-list, \"9\");\n\n\n//  Grape\n//  ───────────────────────────────────\n\n$oc-grape-list: (\n  \"0\": #f8f0fc,\n  \"1\": #f3d9fa,\n  \"2\": #eebefa,\n  \"3\": #e599f7,\n  \"4\": #da77f2,\n  \"5\": #cc5de8,\n  \"6\": #be4bdb,\n  \"7\": #ae3ec9,\n  \"8\": #9c36b5,\n  \"9\": #862e9c\n);\n\n$oc-grape-0: map-get($oc-grape-list, \"0\");\n$oc-grape-1: map-get($oc-grape-list, \"1\");\n$oc-grape-2: map-get($oc-grape-list, \"2\");\n$oc-grape-3: map-get($oc-grape-list, \"3\");\n$oc-grape-4: map-get($oc-grape-list, \"4\");\n$oc-grape-5: map-get($oc-grape-list, \"5\");\n$oc-grape-6: map-get($oc-grape-list, \"6\");\n$oc-grape-7: map-get($oc-grape-list, \"7\");\n$oc-grape-8: map-get($oc-grape-list, \"8\");\n$oc-grape-9: map-get($oc-grape-list, \"9\");\n\n\n//  Violet\n//  ───────────────────────────────────\n\n$oc-violet-list: (\n  \"0\": #f3f0ff,\n  \"1\": #e5dbff,\n  \"2\": #d0bfff,\n  \"3\": #b197fc,\n  \"4\": #9775fa,\n  \"5\": #845ef7,\n  \"6\": #7950f2,\n  \"7\": #7048e8,\n  \"8\": #6741d9,\n  \"9\": #5f3dc4\n);\n\n$oc-violet-0: map-get($oc-violet-list, \"0\");\n$oc-violet-1: map-get($oc-violet-list, \"1\");\n$oc-violet-2: map-get($oc-violet-list, \"2\");\n$oc-violet-3: map-get($oc-violet-list, \"3\");\n$oc-violet-4: map-get($oc-violet-list, \"4\");\n$oc-violet-5: map-get($oc-violet-list, \"5\");\n$oc-violet-6: map-get($oc-violet-list, \"6\");\n$oc-violet-7: map-get($oc-violet-list, \"7\");\n$oc-violet-8: map-get($oc-violet-list, \"8\");\n$oc-violet-9: map-get($oc-violet-list, \"9\");\n\n\n//  Indigo\n//  ───────────────────────────────────\n\n$oc-indigo-list: (\n  \"0\": #edf2ff,\n  \"1\": #dbe4ff,\n  \"2\": #bac8ff,\n  \"3\": #91a7ff,\n  \"4\": #748ffc,\n  \"5\": #5c7cfa,\n  \"6\": #4c6ef5,\n  \"7\": #4263eb,\n  \"8\": #3b5bdb,\n  \"9\": #364fc7\n);\n\n$oc-indigo-0: map-get($oc-indigo-list, \"0\");\n$oc-indigo-1: map-get($oc-indigo-list, \"1\");\n$oc-indigo-2: map-get($oc-indigo-list, \"2\");\n$oc-indigo-3: map-get($oc-indigo-list, \"3\");\n$oc-indigo-4: map-get($oc-indigo-list, \"4\");\n$oc-indigo-5: map-get($oc-indigo-list, \"5\");\n$oc-indigo-6: map-get($oc-indigo-list, \"6\");\n$oc-indigo-7: map-get($oc-indigo-list, \"7\");\n$oc-indigo-8: map-get($oc-indigo-list, \"8\");\n$oc-indigo-9: map-get($oc-indigo-list, \"9\");\n\n\n//  Blue\n//  ───────────────────────────────────\n\n$oc-blue-list: (\n  \"0\": #e7f5ff,\n  \"1\": #d0ebff,\n  \"2\": #a5d8ff,\n  \"3\": #74c0fc,\n  \"4\": #4dabf7,\n  \"5\": #339af0,\n  \"6\": #228be6,\n  \"7\": #1c7ed6,\n  \"8\": #1971c2,\n  \"9\": #1864ab\n);\n\n$oc-blue-0: map-get($oc-blue-list, \"0\");\n$oc-blue-1: map-get($oc-blue-list, \"1\");\n$oc-blue-2: map-get($oc-blue-list, \"2\");\n$oc-blue-3: map-get($oc-blue-list, \"3\");\n$oc-blue-4: map-get($oc-blue-list, \"4\");\n$oc-blue-5: map-get($oc-blue-list, \"5\");\n$oc-blue-6: map-get($oc-blue-list, \"6\");\n$oc-blue-7: map-get($oc-blue-list, \"7\");\n$oc-blue-8: map-get($oc-blue-list, \"8\");\n$oc-blue-9: map-get($oc-blue-list, \"9\");\n\n\n//  Cyan\n//  ───────────────────────────────────\n\n$oc-cyan-list: (\n  \"0\": #e3fafc,\n  \"1\": #c5f6fa,\n  \"2\": #99e9f2,\n  \"3\": #66d9e8,\n  \"4\": #3bc9db,\n  \"5\": #22b8cf,\n  \"6\": #15aabf,\n  \"7\": #1098ad,\n  \"8\": #0c8599,\n  \"9\": #0b7285\n);\n\n$oc-cyan-0: map-get($oc-cyan-list, \"0\");\n$oc-cyan-1: map-get($oc-cyan-list, \"1\");\n$oc-cyan-2: map-get($oc-cyan-list, \"2\");\n$oc-cyan-3: map-get($oc-cyan-list, \"3\");\n$oc-cyan-4: map-get($oc-cyan-list, \"4\");\n$oc-cyan-5: map-get($oc-cyan-list, \"5\");\n$oc-cyan-6: map-get($oc-cyan-list, \"6\");\n$oc-cyan-7: map-get($oc-cyan-list, \"7\");\n$oc-cyan-8: map-get($oc-cyan-list, \"8\");\n$oc-cyan-9: map-get($oc-cyan-list, \"9\");\n\n\n//  Teal\n//  ───────────────────────────────────\n\n$oc-teal-list: (\n  \"0\": #e6fcf5,\n  \"1\": #c3fae8,\n  \"2\": #96f2d7,\n  \"3\": #63e6be,\n  \"4\": #38d9a9,\n  \"5\": #20c997,\n  \"6\": #12b886,\n  \"7\": #0ca678,\n  \"8\": #099268,\n  \"9\": #087f5b\n);\n\n$oc-teal-0: map-get($oc-teal-list, \"0\");\n$oc-teal-1: map-get($oc-teal-list, \"1\");\n$oc-teal-2: map-get($oc-teal-list, \"2\");\n$oc-teal-3: map-get($oc-teal-list, \"3\");\n$oc-teal-4: map-get($oc-teal-list, \"4\");\n$oc-teal-5: map-get($oc-teal-list, \"5\");\n$oc-teal-6: map-get($oc-teal-list, \"6\");\n$oc-teal-7: map-get($oc-teal-list, \"7\");\n$oc-teal-8: map-get($oc-teal-list, \"8\");\n$oc-teal-9: map-get($oc-teal-list, \"9\");\n\n\n//  Green\n//  ───────────────────────────────────\n\n$oc-green-list: (\n  \"0\": #ebfbee,\n  \"1\": #d3f9d8,\n  \"2\": #b2f2bb,\n  \"3\": #8ce99a,\n  \"4\": #69db7c,\n  \"5\": #51cf66,\n  \"6\": #40c057,\n  \"7\": #37b24d,\n  \"8\": #2f9e44,\n  \"9\": #2b8a3e\n);\n\n$oc-green-0: map-get($oc-green-list, \"0\");\n$oc-green-1: map-get($oc-green-list, \"1\");\n$oc-green-2: map-get($oc-green-list, \"2\");\n$oc-green-3: map-get($oc-green-list, \"3\");\n$oc-green-4: map-get($oc-green-list, \"4\");\n$oc-green-5: map-get($oc-green-list, \"5\");\n$oc-green-6: map-get($oc-green-list, \"6\");\n$oc-green-7: map-get($oc-green-list, \"7\");\n$oc-green-8: map-get($oc-green-list, \"8\");\n$oc-green-9: map-get($oc-green-list, \"9\");\n\n\n//  Lime\n//  ───────────────────────────────────\n\n$oc-lime-list: (\n  \"0\": #f4fce3,\n  \"1\": #e9fac8,\n  \"2\": #d8f5a2,\n  \"3\": #c0eb75,\n  \"4\": #a9e34b,\n  \"5\": #94d82d,\n  \"6\": #82c91e,\n  \"7\": #74b816,\n  \"8\": #66a80f,\n  \"9\": #5c940d\n);\n\n$oc-lime-0: map-get($oc-lime-list, \"0\");\n$oc-lime-1: map-get($oc-lime-list, \"1\");\n$oc-lime-2: map-get($oc-lime-list, \"2\");\n$oc-lime-3: map-get($oc-lime-list, \"3\");\n$oc-lime-4: map-get($oc-lime-list, \"4\");\n$oc-lime-5: map-get($oc-lime-list, \"5\");\n$oc-lime-6: map-get($oc-lime-list, \"6\");\n$oc-lime-7: map-get($oc-lime-list, \"7\");\n$oc-lime-8: map-get($oc-lime-list, \"8\");\n$oc-lime-9: map-get($oc-lime-list, \"9\");\n\n\n//  Yellow\n//  ───────────────────────────────────\n\n$oc-yellow-list: (\n  \"0\": #fff9db,\n  \"1\": #fff3bf,\n  \"2\": #ffec99,\n  \"3\": #ffe066,\n  \"4\": #ffd43b,\n  \"5\": #fcc419,\n  \"6\": #fab005,\n  \"7\": #f59f00,\n  \"8\": #f08c00,\n  \"9\": #e67700\n);\n\n$oc-yellow-0: map-get($oc-yellow-list, \"0\");\n$oc-yellow-1: map-get($oc-yellow-list, \"1\");\n$oc-yellow-2: map-get($oc-yellow-list, \"2\");\n$oc-yellow-3: map-get($oc-yellow-list, \"3\");\n$oc-yellow-4: map-get($oc-yellow-list, \"4\");\n$oc-yellow-5: map-get($oc-yellow-list, \"5\");\n$oc-yellow-6: map-get($oc-yellow-list, \"6\");\n$oc-yellow-7: map-get($oc-yellow-list, \"7\");\n$oc-yellow-8: map-get($oc-yellow-list, \"8\");\n$oc-yellow-9: map-get($oc-yellow-list, \"9\");\n\n\n//  Orange\n//  ───────────────────────────────────\n\n$oc-orange-list: (\n  \"0\": #fff4e6,\n  \"1\": #ffe8cc,\n  \"2\": #ffd8a8,\n  \"3\": #ffc078,\n  \"4\": #ffa94d,\n  \"5\": #ff922b,\n  \"6\": #fd7e14,\n  \"7\": #f76707,\n  \"8\": #e8590c,\n  \"9\": #d9480f\n);\n\n$oc-orange-0: map-get($oc-orange-list, \"0\");\n$oc-orange-1: map-get($oc-orange-list, \"1\");\n$oc-orange-2: map-get($oc-orange-list, \"2\");\n$oc-orange-3: map-get($oc-orange-list, \"3\");\n$oc-orange-4: map-get($oc-orange-list, \"4\");\n$oc-orange-5: map-get($oc-orange-list, \"5\");\n$oc-orange-6: map-get($oc-orange-list, \"6\");\n$oc-orange-7: map-get($oc-orange-list, \"7\");\n$oc-orange-8: map-get($oc-orange-list, \"8\");\n$oc-orange-9: map-get($oc-orange-list, \"9\");\n\n\n//  Color list\n//  ───────────────────────────────────\n\n$oc-color-spectrum:   9;\n\n$oc-color-list: (\n  $oc-gray-list:      \"gray\",\n  $oc-red-list:      \"red\",\n  $oc-pink-list:      \"pink\",\n  $oc-grape-list:      \"grape\",\n  $oc-violet-list:      \"violet\",\n  $oc-indigo-list:      \"indigo\",\n  $oc-blue-list:      \"blue\",\n  $oc-cyan-list:      \"cyan\",\n  $oc-teal-list:      \"teal\",\n  $oc-green-list:      \"green\",\n  $oc-lime-list:      \"lime\",\n  $oc-yellow-list:      \"yellow\",\n  $oc-orange-list:      \"orange\"\n);\n", ".excalidraw {\n  .drag-input-container {\n    display: flex;\n    width: 100%;\n\n    &:focus-within {\n      box-shadow: 0 0 0 1px var(--color-primary-darkest);\n      border-radius: var(--border-radius-md);\n    }\n  }\n\n  .disabled {\n    opacity: 0.5;\n    pointer-events: none;\n  }\n\n  .drag-input-label {\n    flex-shrink: 0;\n    border: 1px solid var(--default-border-color);\n    border-right: 0;\n    padding: 0 0.5rem 0 0.75rem;\n    min-width: 1rem;\n    height: 2rem;\n    box-sizing: border-box;\n    color: var(--popup-text-color);\n\n    :root[dir=\"ltr\"] & {\n      border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);\n    }\n\n    :root[dir=\"rtl\"] & {\n      border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;\n      border-right: 1px solid var(--default-border-color);\n      border-left: 0;\n    }\n\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    position: relative;\n  }\n\n  .drag-input {\n    box-sizing: border-box;\n    width: 100%;\n    margin: 0;\n    font-size: 0.875rem;\n    font-family: inherit;\n    background-color: transparent;\n    color: var(--text-primary-color);\n    border: 0;\n    outline: none;\n    height: 2rem;\n    border: 1px solid var(--default-border-color);\n    border-left: 0;\n    letter-spacing: 0.4px;\n\n    :root[dir=\"ltr\"] & {\n      border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;\n    }\n\n    :root[dir=\"rtl\"] & {\n      border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);\n      border-left: 1px solid var(--default-border-color);\n      border-right: 0;\n    }\n\n    padding: 0.5rem;\n    padding-left: 0.25rem;\n    appearance: none;\n\n    &:focus-visible {\n      box-shadow: none;\n    }\n  }\n}\n", ".exc-stats {\n  width: 204px;\n  position: absolute;\n  top: 60px;\n  font-size: 12px;\n  z-index: var(--zIndex-layerUI);\n  pointer-events: var(--ui-pointerEvents);\n\n  :root[dir=\"rtl\"] & {\n    left: 12px;\n    right: initial;\n  }\n\n  h2 {\n    font-size: 1.5em;\n    margin-block-start: 0.83em;\n    margin-block-end: 0.83em;\n    font-weight: bold;\n  }\n  h3 {\n    white-space: nowrap;\n    font-size: 1.17em;\n    margin: 0;\n    font-weight: bold;\n  }\n\n  &__rows {\n    display: flex;\n    flex-direction: column;\n    gap: 0.3125rem;\n  }\n\n  &__row {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    display: grid;\n    gap: 4px;\n\n    div + div {\n      text-align: right;\n    }\n  }\n\n  &__row--heading {\n    text-align: center;\n    font-weight: bold;\n    margin: 0.25rem 0;\n  }\n\n  .title {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 12px;\n\n    h2 {\n      margin: 0;\n    }\n  }\n\n  .close {\n    height: 16px;\n    width: 16px;\n    cursor: pointer;\n    svg {\n      width: 100%;\n      height: 100%;\n    }\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .ElementLinkDialog {\n    position: absolute;\n    top: var(--editor-container-padding);\n    left: var(--editor-container-padding);\n\n    z-index: var(--zIndex-modal);\n\n    border-radius: 10px;\n    padding: 1.5rem;\n\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n    box-shadow: var(--shadow-island);\n    background-color: var(--island-bg-color);\n\n    @include isMobile {\n      left: 0;\n      margin-left: 0.5rem;\n      margin-right: 0.5rem;\n      width: calc(100% - 1rem);\n      box-sizing: border-box;\n      z-index: 5;\n    }\n\n    .ElementLinkDialog__header {\n      h2 {\n        margin-top: 0;\n        margin-bottom: 0.5rem;\n\n        @include isMobile {\n          font-size: 1.25rem;\n        }\n      }\n\n      p {\n        margin: 0;\n\n        @include isMobile {\n          font-size: 0.875rem;\n        }\n      }\n\n      margin-bottom: 1.5rem;\n\n      @include isMobile {\n        margin-bottom: 1rem;\n      }\n    }\n\n    .ElementLinkDialog__input {\n      display: flex;\n\n      .ElementLinkDialog__input-field {\n        flex: 1;\n      }\n\n      .ElementLinkDialog__remove {\n        color: $oc-red-9;\n        margin-left: 1rem;\n\n        .ToolIcon__icon {\n          width: 2rem;\n          height: 2rem;\n        }\n\n        .ToolIcon__icon svg {\n          color: $oc-red-6;\n        }\n      }\n    }\n\n    .ElementLinkDialog__actions {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 1.5rem;\n\n      @include isMobile {\n        font-size: 0.875rem;\n        margin-top: 1rem;\n      }\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "//\n//\n//  𝗖 𝗢 𝗟 𝗢 𝗥\n//  v 1.9.1\n//\n//  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n\n//  General\n//  ───────────────────────────────────\n\n$oc-white:         #ffffff;\n$oc-black:         #000000;\n\n\n//  Gray\n//  ───────────────────────────────────\n\n$oc-gray-list: (\n  \"0\": #f8f9fa,\n  \"1\": #f1f3f5,\n  \"2\": #e9ecef,\n  \"3\": #dee2e6,\n  \"4\": #ced4da,\n  \"5\": #adb5bd,\n  \"6\": #868e96,\n  \"7\": #495057,\n  \"8\": #343a40,\n  \"9\": #212529\n);\n\n$oc-gray-0: map-get($oc-gray-list, \"0\");\n$oc-gray-1: map-get($oc-gray-list, \"1\");\n$oc-gray-2: map-get($oc-gray-list, \"2\");\n$oc-gray-3: map-get($oc-gray-list, \"3\");\n$oc-gray-4: map-get($oc-gray-list, \"4\");\n$oc-gray-5: map-get($oc-gray-list, \"5\");\n$oc-gray-6: map-get($oc-gray-list, \"6\");\n$oc-gray-7: map-get($oc-gray-list, \"7\");\n$oc-gray-8: map-get($oc-gray-list, \"8\");\n$oc-gray-9: map-get($oc-gray-list, \"9\");\n\n\n//  Red\n//  ───────────────────────────────────\n\n$oc-red-list: (\n  \"0\": #fff5f5,\n  \"1\": #ffe3e3,\n  \"2\": #ffc9c9,\n  \"3\": #ffa8a8,\n  \"4\": #ff8787,\n  \"5\": #ff6b6b,\n  \"6\": #fa5252,\n  \"7\": #f03e3e,\n  \"8\": #e03131,\n  \"9\": #c92a2a\n);\n\n$oc-red-0: map-get($oc-red-list, \"0\");\n$oc-red-1: map-get($oc-red-list, \"1\");\n$oc-red-2: map-get($oc-red-list, \"2\");\n$oc-red-3: map-get($oc-red-list, \"3\");\n$oc-red-4: map-get($oc-red-list, \"4\");\n$oc-red-5: map-get($oc-red-list, \"5\");\n$oc-red-6: map-get($oc-red-list, \"6\");\n$oc-red-7: map-get($oc-red-list, \"7\");\n$oc-red-8: map-get($oc-red-list, \"8\");\n$oc-red-9: map-get($oc-red-list, \"9\");\n\n\n//  Pink\n//  ───────────────────────────────────\n\n$oc-pink-list: (\n  \"0\": #fff0f6,\n  \"1\": #ffdeeb,\n  \"2\": #fcc2d7,\n  \"3\": #faa2c1,\n  \"4\": #f783ac,\n  \"5\": #f06595,\n  \"6\": #e64980,\n  \"7\": #d6336c,\n  \"8\": #c2255c,\n  \"9\": #a61e4d\n);\n\n$oc-pink-0: map-get($oc-pink-list, \"0\");\n$oc-pink-1: map-get($oc-pink-list, \"1\");\n$oc-pink-2: map-get($oc-pink-list, \"2\");\n$oc-pink-3: map-get($oc-pink-list, \"3\");\n$oc-pink-4: map-get($oc-pink-list, \"4\");\n$oc-pink-5: map-get($oc-pink-list, \"5\");\n$oc-pink-6: map-get($oc-pink-list, \"6\");\n$oc-pink-7: map-get($oc-pink-list, \"7\");\n$oc-pink-8: map-get($oc-pink-list, \"8\");\n$oc-pink-9: map-get($oc-pink-list, \"9\");\n\n\n//  Grape\n//  ───────────────────────────────────\n\n$oc-grape-list: (\n  \"0\": #f8f0fc,\n  \"1\": #f3d9fa,\n  \"2\": #eebefa,\n  \"3\": #e599f7,\n  \"4\": #da77f2,\n  \"5\": #cc5de8,\n  \"6\": #be4bdb,\n  \"7\": #ae3ec9,\n  \"8\": #9c36b5,\n  \"9\": #862e9c\n);\n\n$oc-grape-0: map-get($oc-grape-list, \"0\");\n$oc-grape-1: map-get($oc-grape-list, \"1\");\n$oc-grape-2: map-get($oc-grape-list, \"2\");\n$oc-grape-3: map-get($oc-grape-list, \"3\");\n$oc-grape-4: map-get($oc-grape-list, \"4\");\n$oc-grape-5: map-get($oc-grape-list, \"5\");\n$oc-grape-6: map-get($oc-grape-list, \"6\");\n$oc-grape-7: map-get($oc-grape-list, \"7\");\n$oc-grape-8: map-get($oc-grape-list, \"8\");\n$oc-grape-9: map-get($oc-grape-list, \"9\");\n\n\n//  Violet\n//  ───────────────────────────────────\n\n$oc-violet-list: (\n  \"0\": #f3f0ff,\n  \"1\": #e5dbff,\n  \"2\": #d0bfff,\n  \"3\": #b197fc,\n  \"4\": #9775fa,\n  \"5\": #845ef7,\n  \"6\": #7950f2,\n  \"7\": #7048e8,\n  \"8\": #6741d9,\n  \"9\": #5f3dc4\n);\n\n$oc-violet-0: map-get($oc-violet-list, \"0\");\n$oc-violet-1: map-get($oc-violet-list, \"1\");\n$oc-violet-2: map-get($oc-violet-list, \"2\");\n$oc-violet-3: map-get($oc-violet-list, \"3\");\n$oc-violet-4: map-get($oc-violet-list, \"4\");\n$oc-violet-5: map-get($oc-violet-list, \"5\");\n$oc-violet-6: map-get($oc-violet-list, \"6\");\n$oc-violet-7: map-get($oc-violet-list, \"7\");\n$oc-violet-8: map-get($oc-violet-list, \"8\");\n$oc-violet-9: map-get($oc-violet-list, \"9\");\n\n\n//  Indigo\n//  ───────────────────────────────────\n\n$oc-indigo-list: (\n  \"0\": #edf2ff,\n  \"1\": #dbe4ff,\n  \"2\": #bac8ff,\n  \"3\": #91a7ff,\n  \"4\": #748ffc,\n  \"5\": #5c7cfa,\n  \"6\": #4c6ef5,\n  \"7\": #4263eb,\n  \"8\": #3b5bdb,\n  \"9\": #364fc7\n);\n\n$oc-indigo-0: map-get($oc-indigo-list, \"0\");\n$oc-indigo-1: map-get($oc-indigo-list, \"1\");\n$oc-indigo-2: map-get($oc-indigo-list, \"2\");\n$oc-indigo-3: map-get($oc-indigo-list, \"3\");\n$oc-indigo-4: map-get($oc-indigo-list, \"4\");\n$oc-indigo-5: map-get($oc-indigo-list, \"5\");\n$oc-indigo-6: map-get($oc-indigo-list, \"6\");\n$oc-indigo-7: map-get($oc-indigo-list, \"7\");\n$oc-indigo-8: map-get($oc-indigo-list, \"8\");\n$oc-indigo-9: map-get($oc-indigo-list, \"9\");\n\n\n//  Blue\n//  ───────────────────────────────────\n\n$oc-blue-list: (\n  \"0\": #e7f5ff,\n  \"1\": #d0ebff,\n  \"2\": #a5d8ff,\n  \"3\": #74c0fc,\n  \"4\": #4dabf7,\n  \"5\": #339af0,\n  \"6\": #228be6,\n  \"7\": #1c7ed6,\n  \"8\": #1971c2,\n  \"9\": #1864ab\n);\n\n$oc-blue-0: map-get($oc-blue-list, \"0\");\n$oc-blue-1: map-get($oc-blue-list, \"1\");\n$oc-blue-2: map-get($oc-blue-list, \"2\");\n$oc-blue-3: map-get($oc-blue-list, \"3\");\n$oc-blue-4: map-get($oc-blue-list, \"4\");\n$oc-blue-5: map-get($oc-blue-list, \"5\");\n$oc-blue-6: map-get($oc-blue-list, \"6\");\n$oc-blue-7: map-get($oc-blue-list, \"7\");\n$oc-blue-8: map-get($oc-blue-list, \"8\");\n$oc-blue-9: map-get($oc-blue-list, \"9\");\n\n\n//  Cyan\n//  ───────────────────────────────────\n\n$oc-cyan-list: (\n  \"0\": #e3fafc,\n  \"1\": #c5f6fa,\n  \"2\": #99e9f2,\n  \"3\": #66d9e8,\n  \"4\": #3bc9db,\n  \"5\": #22b8cf,\n  \"6\": #15aabf,\n  \"7\": #1098ad,\n  \"8\": #0c8599,\n  \"9\": #0b7285\n);\n\n$oc-cyan-0: map-get($oc-cyan-list, \"0\");\n$oc-cyan-1: map-get($oc-cyan-list, \"1\");\n$oc-cyan-2: map-get($oc-cyan-list, \"2\");\n$oc-cyan-3: map-get($oc-cyan-list, \"3\");\n$oc-cyan-4: map-get($oc-cyan-list, \"4\");\n$oc-cyan-5: map-get($oc-cyan-list, \"5\");\n$oc-cyan-6: map-get($oc-cyan-list, \"6\");\n$oc-cyan-7: map-get($oc-cyan-list, \"7\");\n$oc-cyan-8: map-get($oc-cyan-list, \"8\");\n$oc-cyan-9: map-get($oc-cyan-list, \"9\");\n\n\n//  Teal\n//  ───────────────────────────────────\n\n$oc-teal-list: (\n  \"0\": #e6fcf5,\n  \"1\": #c3fae8,\n  \"2\": #96f2d7,\n  \"3\": #63e6be,\n  \"4\": #38d9a9,\n  \"5\": #20c997,\n  \"6\": #12b886,\n  \"7\": #0ca678,\n  \"8\": #099268,\n  \"9\": #087f5b\n);\n\n$oc-teal-0: map-get($oc-teal-list, \"0\");\n$oc-teal-1: map-get($oc-teal-list, \"1\");\n$oc-teal-2: map-get($oc-teal-list, \"2\");\n$oc-teal-3: map-get($oc-teal-list, \"3\");\n$oc-teal-4: map-get($oc-teal-list, \"4\");\n$oc-teal-5: map-get($oc-teal-list, \"5\");\n$oc-teal-6: map-get($oc-teal-list, \"6\");\n$oc-teal-7: map-get($oc-teal-list, \"7\");\n$oc-teal-8: map-get($oc-teal-list, \"8\");\n$oc-teal-9: map-get($oc-teal-list, \"9\");\n\n\n//  Green\n//  ───────────────────────────────────\n\n$oc-green-list: (\n  \"0\": #ebfbee,\n  \"1\": #d3f9d8,\n  \"2\": #b2f2bb,\n  \"3\": #8ce99a,\n  \"4\": #69db7c,\n  \"5\": #51cf66,\n  \"6\": #40c057,\n  \"7\": #37b24d,\n  \"8\": #2f9e44,\n  \"9\": #2b8a3e\n);\n\n$oc-green-0: map-get($oc-green-list, \"0\");\n$oc-green-1: map-get($oc-green-list, \"1\");\n$oc-green-2: map-get($oc-green-list, \"2\");\n$oc-green-3: map-get($oc-green-list, \"3\");\n$oc-green-4: map-get($oc-green-list, \"4\");\n$oc-green-5: map-get($oc-green-list, \"5\");\n$oc-green-6: map-get($oc-green-list, \"6\");\n$oc-green-7: map-get($oc-green-list, \"7\");\n$oc-green-8: map-get($oc-green-list, \"8\");\n$oc-green-9: map-get($oc-green-list, \"9\");\n\n\n//  Lime\n//  ───────────────────────────────────\n\n$oc-lime-list: (\n  \"0\": #f4fce3,\n  \"1\": #e9fac8,\n  \"2\": #d8f5a2,\n  \"3\": #c0eb75,\n  \"4\": #a9e34b,\n  \"5\": #94d82d,\n  \"6\": #82c91e,\n  \"7\": #74b816,\n  \"8\": #66a80f,\n  \"9\": #5c940d\n);\n\n$oc-lime-0: map-get($oc-lime-list, \"0\");\n$oc-lime-1: map-get($oc-lime-list, \"1\");\n$oc-lime-2: map-get($oc-lime-list, \"2\");\n$oc-lime-3: map-get($oc-lime-list, \"3\");\n$oc-lime-4: map-get($oc-lime-list, \"4\");\n$oc-lime-5: map-get($oc-lime-list, \"5\");\n$oc-lime-6: map-get($oc-lime-list, \"6\");\n$oc-lime-7: map-get($oc-lime-list, \"7\");\n$oc-lime-8: map-get($oc-lime-list, \"8\");\n$oc-lime-9: map-get($oc-lime-list, \"9\");\n\n\n//  Yellow\n//  ───────────────────────────────────\n\n$oc-yellow-list: (\n  \"0\": #fff9db,\n  \"1\": #fff3bf,\n  \"2\": #ffec99,\n  \"3\": #ffe066,\n  \"4\": #ffd43b,\n  \"5\": #fcc419,\n  \"6\": #fab005,\n  \"7\": #f59f00,\n  \"8\": #f08c00,\n  \"9\": #e67700\n);\n\n$oc-yellow-0: map-get($oc-yellow-list, \"0\");\n$oc-yellow-1: map-get($oc-yellow-list, \"1\");\n$oc-yellow-2: map-get($oc-yellow-list, \"2\");\n$oc-yellow-3: map-get($oc-yellow-list, \"3\");\n$oc-yellow-4: map-get($oc-yellow-list, \"4\");\n$oc-yellow-5: map-get($oc-yellow-list, \"5\");\n$oc-yellow-6: map-get($oc-yellow-list, \"6\");\n$oc-yellow-7: map-get($oc-yellow-list, \"7\");\n$oc-yellow-8: map-get($oc-yellow-list, \"8\");\n$oc-yellow-9: map-get($oc-yellow-list, \"9\");\n\n\n//  Orange\n//  ───────────────────────────────────\n\n$oc-orange-list: (\n  \"0\": #fff4e6,\n  \"1\": #ffe8cc,\n  \"2\": #ffd8a8,\n  \"3\": #ffc078,\n  \"4\": #ffa94d,\n  \"5\": #ff922b,\n  \"6\": #fd7e14,\n  \"7\": #f76707,\n  \"8\": #e8590c,\n  \"9\": #d9480f\n);\n\n$oc-orange-0: map-get($oc-orange-list, \"0\");\n$oc-orange-1: map-get($oc-orange-list, \"1\");\n$oc-orange-2: map-get($oc-orange-list, \"2\");\n$oc-orange-3: map-get($oc-orange-list, \"3\");\n$oc-orange-4: map-get($oc-orange-list, \"4\");\n$oc-orange-5: map-get($oc-orange-list, \"5\");\n$oc-orange-6: map-get($oc-orange-list, \"6\");\n$oc-orange-7: map-get($oc-orange-list, \"7\");\n$oc-orange-8: map-get($oc-orange-list, \"8\");\n$oc-orange-9: map-get($oc-orange-list, \"9\");\n\n\n//  Color list\n//  ───────────────────────────────────\n\n$oc-color-spectrum:   9;\n\n$oc-color-list: (\n  $oc-gray-list:      \"gray\",\n  $oc-red-list:      \"red\",\n  $oc-pink-list:      \"pink\",\n  $oc-grape-list:      \"grape\",\n  $oc-violet-list:      \"violet\",\n  $oc-indigo-list:      \"indigo\",\n  $oc-blue-list:      \"blue\",\n  $oc-cyan-list:      \"cyan\",\n  $oc-teal-list:      \"teal\",\n  $oc-green-list:      \"green\",\n  $oc-lime-list:      \"lime\",\n  $oc-yellow-list:      \"yellow\",\n  $oc-orange-list:      \"orange\"\n);\n", "@import \"open-color/open-color\";\n@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .layer-ui__wrapper.animate {\n    transition: width 0.1s ease-in-out;\n  }\n  .layer-ui__wrapper {\n    // when the rightside sidebar is docked, we need to resize the UI by its\n    // width, making the nested UI content shift to the left. To do this,\n    // we need the UI container to actually have dimensions set, but\n    // then we also need to disable pointer events else the canvas below\n    // wouldn't be interactive.\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    pointer-events: none;\n    z-index: var(--zIndex-layerUI);\n\n    &__top-right {\n      display: flex;\n      width: 100%;\n      justify-content: flex-end;\n      gap: 0.75rem;\n      pointer-events: none !important;\n\n      & > * {\n        pointer-events: var(--ui-pointerEvents);\n      }\n    }\n\n    &__footer {\n      width: 100%;\n\n      &-right {\n        z-index: 100;\n        display: flex;\n      }\n    }\n\n    .zen-mode-transition {\n      transition: transform 0.5s ease-in-out;\n\n      :root[dir=\"ltr\"] &.transition-left {\n        transform: translate(-999px, 0);\n      }\n\n      :root[dir=\"ltr\"] &.transition-right {\n        transform: translate(999px, 0);\n      }\n\n      :root[dir=\"rtl\"] &.transition-left {\n        transform: translate(999px, 0);\n      }\n\n      :root[dir=\"rtl\"] &.transition-right {\n        transform: translate(-999px, 0);\n      }\n\n      &.layer-ui__wrapper__footer-left--transition-bottom {\n        transform: translate(0, 92px);\n      }\n    }\n\n    .disable-zen-mode {\n      padding: 10px;\n      position: absolute;\n      bottom: 0;\n      [dir=\"ltr\"] & {\n        right: 1rem;\n      }\n      [dir=\"rtl\"] & {\n        left: 1rem;\n      }\n      opacity: 0;\n      visibility: hidden;\n      transition: visibility 0s linear 0s, opacity 0.5s;\n\n      font-family: var(--ui-font);\n      font-size: 0.75rem;\n      font-weight: 500;\n      line-height: 1;\n\n      border-radius: var(--border-radius-lg);\n      border: 1px solid var(--default-border-color);\n      background-color: var(--island-bg-color);\n      color: var(--text-primary-color);\n\n      &:hover {\n        background-color: var(--button-hover-bg);\n      }\n      &:active {\n        border-color: var(--color-primary);\n      }\n\n      &--visible {\n        opacity: 1;\n        visibility: visible;\n        transition: visibility 0s linear 300ms, opacity 0.5s;\n        transition-delay: 0.8s;\n\n        pointer-events: var(--ui-pointerEvents);\n      }\n    }\n\n    .layer-ui__wrapper__footer-left,\n    .footer-center,\n    .layer-ui__wrapper__footer-right {\n      & > * {\n        pointer-events: var(--ui-pointerEvents);\n      }\n    }\n\n    .layer-ui__wrapper__footer-right {\n      margin-top: auto;\n      margin-bottom: auto;\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .App-toolbar {\n    &.zen-mode {\n      .ToolIcon__keybinding,\n      .HintViewer {\n        display: none;\n      }\n    }\n\n    &__divider {\n      width: 1px;\n      height: 1.5rem;\n      align-self: center;\n      background-color: var(--default-border-color);\n      margin: 0 0.25rem;\n\n      @include isMobile {\n        margin: 0;\n      }\n    }\n  }\n\n  .App-toolbar__extra-tools-trigger {\n    box-shadow: none;\n    border: 0;\n    background-color: transparent;\n\n    &:active {\n      background-color: var(--button-hover-bg);\n      box-shadow: 0 0 0 1px\n        var(--button-active-border, var(--color-primary-darkest)) inset;\n    }\n\n    &--selected,\n    &--selected:hover {\n      background: var(--color-primary-light);\n      color: var(--color-primary);\n    }\n  }\n\n  .App-toolbar__extra-tools-dropdown {\n    margin-top: 0.375rem;\n    right: 0;\n    min-width: 11.875rem;\n    z-index: 1;\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .Toast {\n    $closeButtonSize: 1.2rem;\n    $closeButtonPadding: 0.4rem;\n\n    animation: fade-in 0.5s;\n    background-color: var(--button-gray-1);\n    border-radius: 4px;\n    bottom: 10px;\n    box-sizing: border-box;\n    cursor: default;\n    left: 50%;\n    margin-left: -150px;\n    padding: 4px 0;\n    position: absolute;\n    text-align: center;\n    width: 300px;\n    z-index: 999999;\n\n    .Toast__message {\n      padding: 0 $closeButtonSize + ($closeButtonPadding);\n      color: var(--popup-text-color);\n      white-space: pre-wrap;\n    }\n\n    .close {\n      position: absolute;\n      top: 0;\n      right: 0;\n      padding: $closeButtonPadding;\n\n      .ToolIcon__icon {\n        width: $closeButtonSize;\n        height: $closeButtonSize;\n      }\n    }\n  }\n\n  @keyframes fade-in {\n    from {\n      opacity: 0;\n    }\n    to {\n      opacity: 1;\n    }\n  }\n}\n", "@import \"../css/variables.module.scss\";\n\n.excalidraw {\n  .SVGLayer {\n    pointer-events: none;\n    width: 100vw;\n    height: 100vh;\n    position: fixed;\n    top: 0;\n    left: 0;\n\n    z-index: var(--zIndex-svgLayer);\n\n    & svg {\n      image-rendering: auto;\n      overflow: visible;\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      top: 0;\n      left: 0;\n    }\n  }\n}\n", ".excalidraw {\n  .excalidraw-canvas-buttons {\n    position: absolute;\n\n    box-shadow: 0px 2px 4px 0 rgb(0 0 0 / 30%);\n    z-index: var(--zIndex-canvasButtons);\n    background: var(--island-bg-color);\n    border-radius: var(--border-radius-lg);\n\n    display: flex;\n    flex-direction: column;\n    gap: 0.375rem;\n  }\n}\n", ".excalidraw {\n  .follow-mode {\n    position: absolute;\n    box-sizing: border-box;\n    pointer-events: none;\n    border: 2px solid var(--color-primary-hover);\n    z-index: 9999;\n    display: flex;\n    align-items: flex-end;\n    justify-content: center;\n\n    &__badge {\n      background-color: var(--color-primary-hover);\n      color: var(--color-primary-light);\n      padding: 0.25rem 0.5rem;\n      margin-bottom: 0.5rem;\n      border-radius: 0.5rem;\n      pointer-events: all;\n      font-size: 0.75rem;\n      display: flex;\n      gap: 0.5rem;\n      align-items: center;\n\n      &__label {\n        display: flex;\n        white-space: pre-wrap;\n        line-height: 1;\n      }\n\n      &__username {\n        display: block;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        max-width: 100px;\n      }\n    }\n\n    &__disconnect-btn {\n      all: unset;\n      cursor: pointer;\n      border-radius: 0.25rem;\n\n      &:hover {\n        background-color: var(--color-primary-darker);\n      }\n\n      &:active {\n        background-color: var(--color-primary-darkest);\n      }\n\n      svg {\n        display: block;\n        width: 1rem;\n        height: 1rem;\n      }\n    }\n  }\n}\n", "@import \"open-color/open-color.scss\";\n\n.visually-hidden {\n  position: absolute !important;\n  height: 1px;\n  width: 1px;\n  overflow: hidden;\n  clip: rect(1px, 1px, 1px, 1px);\n  white-space: nowrap; /* added line */\n  user-select: none;\n}\n\n.LoadingMessage {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 999;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  pointer-events: none;\n\n  .Spinner {\n    font-size: 2.8em;\n  }\n\n  .LoadingMessage-text {\n    margin-top: 1em;\n    font-size: 0.8em;\n  }\n}\n\n.LoadingMessage--dark {\n  background-color: #121212;\n  color: #ced4da;\n}\n", "@import \"open-color/open-color.scss\";\n@import \"./variables.module.scss\";\n\n.excalidraw {\n  --theme-filter: none;\n  --button-destructive-bg-color: #{$oc-red-1};\n  --button-destructive-color: #{$oc-red-9};\n  --button-gray-1: #{$oc-gray-2};\n  --button-gray-2: #{$oc-gray-4};\n  --button-gray-3: #{$oc-gray-5};\n  --button-special-active-bg-color: #{$oc-green-0};\n  --dialog-border-color: var(--color-gray-20);\n  --dropdown-icon: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"292.4\" height=\"292.4\" viewBox=\"0 0 292 292\"><path d=\"M287 197L159 69c-4-3-8-5-13-5s-9 2-13 5L5 197c-3 4-5 8-5 13s2 9 5 13c4 4 8 5 13 5h256c5 0 9-1 13-5s5-8 5-13-1-9-5-13z\"/></svg>');\n  --focus-highlight-color: #{$oc-blue-2};\n  --icon-fill-color: var(--color-on-surface);\n  --icon-green-fill-color: #{$oc-green-9};\n  --default-bg-color: #{$oc-white};\n  --input-bg-color: #{$oc-white};\n  --input-border-color: #{$oc-gray-4};\n  --input-hover-bg-color: #{$oc-gray-1};\n  --input-label-color: #{$oc-gray-7};\n  --island-bg-color: #ffffff;\n  --keybinding-color: var(--color-gray-40);\n  --link-color: #{$oc-blue-7};\n  --overlay-bg-color: #{transparentize($oc-white, 0.12)};\n  --popup-bg-color: var(--island-bg-color);\n  --popup-secondary-bg-color: #{$oc-gray-1};\n  --popup-text-color: #{$oc-black};\n  --popup-text-inverted-color: #{$oc-white};\n  --select-highlight-color: #{$oc-blue-5};\n  --shadow-island: 0px 0px 0.9310142993927002px 0px rgba(0, 0, 0, 0.17),\n    0px 0px 3.1270833015441895px 0px rgba(0, 0, 0, 0.08),\n    0px 7px 14px 0px rgba(0, 0, 0, 0.05);\n\n  --button-hover-bg: var(--color-surface-high);\n  --button-active-bg: var(--color-surface-high);\n  --button-active-border: var(--color-brand-active);\n  --default-border-color: var(--color-surface-high);\n\n  --default-button-size: 2rem;\n  --default-icon-size: 1rem;\n  --lg-button-size: 2.25rem;\n  --lg-icon-size: 1rem;\n  --editor-container-padding: 1rem;\n\n  @media screen and (min-device-width: 1921px) {\n    --lg-button-size: 2.5rem;\n    --lg-icon-size: 1.25rem;\n    --default-button-size: 2.25rem;\n    --default-icon-size: 1.25rem;\n  }\n\n  --scrollbar-thumb: var(--button-gray-2);\n  --scrollbar-thumb-hover: var(--button-gray-3);\n\n  --color-slider-track: hsl(240, 100%, 90%);\n  --color-slider-thumb: var(--color-gray-80);\n\n  --modal-shadow: 0px 100px 80px rgba(0, 0, 0, 0.07),\n    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),\n    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),\n    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),\n    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),\n    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);\n  --avatar-border-color: var(--color-gray-20);\n  --sidebar-shadow: 0px 100px 80px rgba(0, 0, 0, 0.07),\n    0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),\n    0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),\n    0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),\n    0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),\n    0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);\n  --sidebar-border-color: var(--color-surface-high);\n  --sidebar-bg-color: var(--island-bg-color);\n  --library-dropdown-shadow: 0px 15px 6px rgba(0, 0, 0, 0.01),\n    0px 8px 5px rgba(0, 0, 0, 0.05), 0px 4px 4px rgba(0, 0, 0, 0.09),\n    0px 1px 2px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);\n\n  --space-factor: 0.25rem;\n  --text-primary-color: var(--color-on-surface);\n\n  --color-selection: #6965db;\n\n  --color-icon-white: #{$oc-white};\n\n  --color-primary: #6965db;\n  --color-primary-darker: #5b57d1;\n  --color-primary-darkest: #4a47b1;\n  --color-primary-light: #e3e2fe;\n  --color-primary-light-darker: #d7d5ff;\n  --color-primary-hover: #5753d0;\n\n  --color-gray-10: #f5f5f5;\n  --color-gray-20: #ebebeb;\n  --color-gray-30: #d6d6d6;\n  --color-gray-40: #b8b8b8;\n  --color-gray-50: #999999;\n  --color-gray-60: #7a7a7a;\n  --color-gray-70: #5c5c5c;\n  --color-gray-80: #3d3d3d;\n  --color-gray-85: #242424;\n  --color-gray-90: #1e1e1e;\n  --color-gray-100: #121212;\n\n  --color-disabled: var(--color-gray-40);\n\n  --color-warning: #fceeca;\n  --color-warning-dark: #f5c354;\n  --color-warning-darker: #f3ab2c;\n  --color-warning-darkest: #ec8b14;\n  --color-text-warning: var(--text-primary-color);\n\n  --color-danger: #db6965;\n  --color-danger-dark: #db6965;\n  --color-danger-darker: #d65550;\n  --color-danger-darkest: #d1413c;\n  --color-danger-text: black;\n\n  --color-danger-background: #fff0f0;\n  --color-danger-icon-background: #ffdad6;\n  --color-danger-color: #700000;\n  --color-danger-icon-color: #700000;\n\n  --color-warning-background: var(--color-warning);\n  --color-warning-icon-background: var(--color-warning-dark);\n  --color-warning-color: var(--text-primary-color);\n  --color-warning-icon-color: var(--text-primary-color);\n\n  --color-muted: var(--color-gray-30);\n  --color-muted-darker: var(--color-gray-60);\n  --color-muted-darkest: var(--color-gray-100);\n  --color-muted-background: var(--color-gray-80);\n  --color-muted-background-darker: var(--color-gray-100);\n\n  --color-promo: var(--color-primary);\n\n  --color-success: #cafccc;\n  --color-success-darker: #bafabc;\n  --color-success-darkest: #a5eba8;\n  --color-success-text: #268029;\n  --color-success-contrast: #65bb6a;\n  --color-success-contrast-hover: #6bcf70;\n  --color-success-contrast-active: #6edf74;\n\n  --color-logo-icon: var(--color-primary);\n  --color-logo-text: #190064;\n\n  --border-radius-md: 0.375rem;\n  --border-radius-lg: 0.5rem;\n\n  --color-surface-high: #f1f0ff;\n  --color-surface-mid: #f2f2f7;\n  --color-surface-low: #ececf4;\n  --color-surface-lowest: #ffffff;\n  --color-on-surface: #1b1b1f;\n  --color-brand-hover: #5753d0;\n  --color-on-primary-container: #030064;\n  --color-surface-primary-container: #e0dfff;\n  --color-brand-active: #4440bf;\n  --color-border-outline: #767680;\n  --color-border-outline-variant: #c5c5d0;\n  --color-surface-primary-container: #e0dfff;\n\n  --color-badge: #0b6513;\n  --background-color-badge: #d3ffd2;\n\n  &.theme--dark {\n    &.theme--dark-background-none {\n      background: none;\n    }\n  }\n\n  &.theme--dark {\n    --theme-filter: invert(93%) hue-rotate(180deg);\n    --button-destructive-bg-color: #5a0000;\n    --button-destructive-color: #{$oc-red-3};\n\n    --button-gray-1: #363636;\n    --button-gray-2: #272727;\n    --button-gray-3: #222;\n    --button-special-active-bg-color: #204624;\n    --dialog-border-color: var(--color-gray-80);\n    --dropdown-icon: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"292.4\" height=\"292.4\" viewBox=\"0 0 292 292\"><path fill=\"%23ced4da\" d=\"M287 197L159 69c-4-3-8-5-13-5s-9 2-13 5L5 197c-3 4-5 8-5 13s2 9 5 13c4 4 8 5 13 5h256c5 0 9-1 13-5s5-8 5-13-1-9-5-13z\"/></svg>');\n    --focus-highlight-color: #{$oc-blue-6};\n    --icon-green-fill-color: #{$oc-green-4};\n    --default-bg-color: #121212;\n    --input-bg-color: #121212;\n    --input-border-color: #2e2e2e;\n    --input-hover-bg-color: #181818;\n    --input-label-color: #{$oc-gray-2};\n    --island-bg-color: #232329;\n    --keybinding-color: var(--color-gray-60);\n    --link-color: #{$oc-blue-4};\n    --overlay-bg-color: #{transparentize($oc-gray-8, 0.88)};\n    --popup-secondary-bg-color: #222;\n    --popup-text-color: #{$oc-gray-4};\n    --popup-text-inverted-color: #2c2c2c;\n    --select-highlight-color: #{$oc-blue-4};\n    --shadow-island: 0px 0px 0.9310142993927002px 0px rgba(0, 0, 0, 0.17),\n      0px 0px 3.1270833015441895px 0px rgba(0, 0, 0, 0.08),\n      0px 7px 14px 0px rgba(0, 0, 0, 0.05);\n\n    --modal-shadow: 0px 100px 80px rgba(0, 0, 0, 0.07),\n      0px 41.7776px 33.4221px rgba(0, 0, 0, 0.0503198),\n      0px 22.3363px 17.869px rgba(0, 0, 0, 0.0417275),\n      0px 12.5216px 10.0172px rgba(0, 0, 0, 0.035),\n      0px 6.6501px 5.32008px rgba(0, 0, 0, 0.0282725),\n      0px 2.76726px 2.21381px rgba(0, 0, 0, 0.0196802);\n    --avatar-border-color: var(--color-gray-85);\n\n    --scrollbar-thumb: #{$oc-gray-8};\n    --scrollbar-thumb-hover: #{$oc-gray-7};\n\n    --color-slider-track: hsl(244, 23%, 39%);\n\n    // will be inverted to a lighter color.\n    --color-selection: #3530c4;\n\n    --color-icon-white: var(--color-gray-90);\n\n    --color-primary: #a8a5ff;\n    --color-primary-darker: #b2aeff;\n    --color-primary-darkest: #beb9ff;\n    --color-primary-light: #4f4d6f;\n    --color-primary-light-darker: #43415e;\n    --color-primary-hover: #bbb8ff;\n\n    --color-disabled: var(--color-gray-70);\n\n    --color-text-warning: var(--color-gray-80);\n\n    --color-danger: #ffa8a5;\n    --color-danger-dark: #672120;\n    --color-danger-darker: #8f2625;\n    --color-danger-darkest: #ac2b29;\n    --color-danger-text: #fbcbcc;\n\n    --color-danger-background: #fbcbcc;\n    --color-danger-icon-background: #672120;\n    --color-danger-color: #261919;\n    --color-danger-icon-color: #fbcbcc;\n\n    --color-warning-background: var(--color-warning);\n    --color-warning-icon-background: var(--color-warning-dark);\n    --color-warning-color: var(--color-gray-80);\n    --color-warning-icon-color: var(--color-gray-80);\n\n    --color-muted: var(--color-gray-80);\n    --color-muted-darker: var(--color-gray-60);\n    --color-muted-darkest: var(--color-gray-20);\n    --color-muted-background: var(--color-gray-40);\n    --color-muted-background-darker: var(--color-gray-20);\n\n    --color-logo-text: #e2dfff;\n\n    --color-surface-high: hsl(245, 10%, 21%);\n    --color-surface-low: hsl(240, 8%, 15%);\n    --color-surface-mid: hsl(240 6% 10%);\n    --color-surface-lowest: hsl(0, 0%, 7%);\n    --color-on-surface: #e3e3e8;\n    --color-brand-hover: #bbb8ff;\n    --color-on-primary-container: #e0dfff;\n    --color-surface-primary-container: #403e6a;\n    --color-brand-active: #d0ccff;\n    --color-border-outline: #8e8d9c;\n    --color-border-outline-variant: #46464f;\n    --color-surface-primary-container: #403e6a;\n  }\n}\n", "@import \"./variables.module.scss\";\n@import \"./theme\";\n\n:root {\n  --zIndex-canvas: 1;\n  --zIndex-interactiveCanvas: 2;\n  --zIndex-svgLayer: 3;\n  --zIndex-wysiwyg: 3;\n  --zIndex-canvasButtons: 3;\n  --zIndex-layerUI: 4;\n  --zIndex-eyeDropperBackdrop: 5;\n  --zIndex-eyeDropperPreview: 6;\n  --zIndex-hyperlinkContainer: 7;\n\n  --zIndex-modal: 1000;\n  --zIndex-popup: 1001;\n  --zIndex-toast: 999999;\n\n  --sab: env(safe-area-inset-bottom);\n  --sal: env(safe-area-inset-left);\n  --sar: env(safe-area-inset-right);\n  --sat: env(safe-area-inset-top);\n}\n\nbody.excalidraw-cursor-resize,\nbody.excalidraw-cursor-resize a:hover,\nbody.excalidraw-cursor-resize * {\n  cursor: ew-resize;\n}\n\n.excalidraw {\n  --ui-font: Assistant, system-ui, BlinkMacSystemFont, -apple-system, Segoe UI,\n    Roboto, Helvetica, Arial, sans-serif;\n  font-family: var(--ui-font);\n\n  position: relative;\n  overflow: hidden;\n  color: var(--text-primary-color);\n  display: flex;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 100%;\n  width: 100%;\n\n  button {\n    cursor: pointer;\n    user-select: none;\n  }\n\n  &:focus {\n    outline: none;\n  }\n\n  // serves 2 purposes:\n  // 1. prevent selecting text outside the component when double-clicking or\n  //    dragging inside it (e.g. on canvas)\n  // 2. prevent selecting UI, both from the inside, and from outside the\n  //    component (e.g. if you select text in a sidebar)\n  user-select: none;\n\n  .excalidraw-link,\n  a {\n    font-weight: 500;\n    text-decoration: none;\n    color: var(--link-color);\n    user-select: none;\n    cursor: pointer;\n\n    &:hover {\n      text-decoration: underline;\n    }\n    &:active {\n      text-decoration: none;\n    }\n  }\n\n  canvas {\n    touch-action: none;\n\n    // following props improve blurriness at certain devicePixelRatios.\n    // AFAIK it doesn't affect export (in fact, export seems sharp either way).\n\n    image-rendering: pixelated; // chromium\n    // NOTE: must be declared *after* the above\n    image-rendering: -moz-crisp-edges; // FF\n\n    z-index: var(--zIndex-canvas);\n\n    &.interactive {\n      z-index: var(--zIndex-interactiveCanvas);\n    }\n\n    // Remove the main canvas from document flow to avoid resizeObserver\n    // feedback loop (see https://github.com/excalidraw/excalidraw/pull/3379)\n  }\n\n  &__canvas-wrapper,\n  &__canvas.static {\n    pointer-events: none;\n  }\n\n  &__canvas {\n    position: absolute;\n  }\n\n  &__embeddable {\n    width: 100%;\n    height: 100%;\n    border: 0;\n  }\n\n  &__embeddable-container {\n    position: absolute;\n    z-index: 2;\n    transform-origin: top left;\n    pointer-events: none;\n  }\n\n  &.theme--dark {\n    // The percentage is inspired by\n    // https://material.io/design/color/dark-theme.html#properties, which\n    // recommends surface color of #121212, 93% yields #111111 for #FFF\n\n    canvas {\n      filter: var(--theme-filter);\n    }\n  }\n\n  .FixedSideContainer {\n    padding-top: var(--sat, 0);\n    padding-right: var(--sar, 0);\n    padding-bottom: var(--sab, 0);\n    padding-left: var(--sal, 0);\n  }\n\n  .panelRow {\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .panelColumn {\n    display: flex;\n    flex-direction: column;\n    row-gap: 0.75rem;\n\n    h3,\n    legend,\n    .control-label {\n      margin: 0;\n      margin-bottom: 0.25rem;\n      font-size: 0.75rem;\n      color: var(--text-primary-color);\n      font-weight: 400;\n      display: block;\n    }\n\n    .control-label input {\n      display: block;\n      width: 100%;\n    }\n\n    legend {\n      padding: 0;\n    }\n\n    .iconSelectList {\n      flex-wrap: wrap;\n      position: relative;\n    }\n\n    .buttonList {\n      flex-wrap: wrap;\n      display: flex;\n      column-gap: 0.5rem;\n      row-gap: 0.5rem;\n\n      label {\n        font-size: 0.75rem;\n      }\n\n      input[type=\"radio\"],\n      input[type=\"button\"] {\n        opacity: 0;\n        position: absolute;\n        pointer-events: none;\n      }\n\n      .iconRow {\n        margin-top: 8px;\n      }\n    }\n\n    fieldset {\n      margin: 0;\n      padding: 0;\n      border: none;\n    }\n  }\n\n  .divider {\n    width: 1px;\n    background-color: $oc-gray-2;\n    margin: 1px;\n  }\n\n  .welcome-screen-menu-item:focus-visible,\n  .dropdown-menu-item:focus-visible,\n  button:focus-visible,\n  .buttonList label:focus-within,\n  input:focus-visible {\n    outline: transparent;\n    box-shadow: 0 0 0 1px var(--color-brand-hover);\n  }\n\n  .buttonList {\n    .ToolIcon__icon {\n      all: unset !important;\n      display: flex !important;\n    }\n\n    button {\n      background-color: transparent;\n    }\n\n    label,\n    button,\n    .zIndexButton {\n      @include outlineButtonIconStyles;\n    }\n  }\n\n  .App-top-bar {\n    z-index: var(--zIndex-layerUI);\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n  }\n\n  .App-bottom-bar {\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    --bar-padding: calc(4 * var(--space-factor));\n    padding-top: #{\"max(var(--bar-padding), var(--sat,0))\"};\n    padding-right: var(--sar, 0);\n    padding-bottom: var(--sab, 0);\n    padding-left: var(--sal, 0);\n    z-index: 4;\n    display: flex;\n    align-items: flex-end;\n    pointer-events: none;\n\n    > .Island {\n      width: 100%;\n      max-width: 100%;\n      min-width: 100%;\n      box-sizing: border-box;\n      max-height: 100%;\n      display: flex;\n      flex-direction: column;\n      pointer-events: var(--ui-pointerEvents);\n\n      .panelColumn {\n        padding: 8px 8px 0 8px;\n      }\n    }\n  }\n\n  .App-toolbar {\n    width: 100%;\n\n    .eraser {\n      &.ToolIcon:hover {\n        --icon-fill-color: #fff;\n        --keybinding-color: #fff;\n      }\n      &.active {\n        background-color: var(--color-primary);\n      }\n    }\n  }\n\n  .App-toolbar-content {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 8px;\n\n    .dropdown-menu--mobile {\n      bottom: 55px;\n      top: auto;\n    }\n  }\n\n  .App-mobile-menu {\n    width: 100%;\n    overflow-x: visible;\n    overflow-y: auto;\n    box-sizing: border-box;\n    margin-bottom: var(--bar-padding);\n  }\n\n  .App-menu {\n    display: grid;\n    color: var(--icon-fill-color);\n  }\n\n  .shapes-section {\n    display: flex;\n    justify-content: center;\n    pointer-events: none !important;\n\n    & > * {\n      pointer-events: var(--ui-pointerEvents);\n    }\n  }\n\n  .App-menu_top {\n    grid-template-columns: 1fr 2fr 1fr;\n    grid-gap: 2rem;\n    align-items: flex-start;\n    cursor: default;\n    pointer-events: none !important;\n\n    & > * {\n      pointer-events: var(--ui-pointerEvents);\n    }\n\n    @media (min-width: 1536px) {\n      grid-template-columns: 1fr 1fr 1fr;\n      grid-gap: 3rem;\n    }\n  }\n\n  .App-menu_top > *:first-child {\n    justify-self: flex-start;\n  }\n\n  .App-menu_top > *:last-child {\n    justify-self: flex-end;\n  }\n\n  .App-menu_bottom {\n    position: absolute;\n    bottom: 1rem;\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    cursor: default;\n    pointer-events: none !important;\n    box-sizing: border-box;\n    padding: 0 1rem;\n\n    &--transition-left {\n      section {\n        width: 185px;\n      }\n    }\n\n    section {\n      display: flex;\n    }\n  }\n\n  .App-menu_bottom > *:first-child {\n    justify-self: flex-start;\n  }\n\n  .App-menu_bottom > *:last-child {\n    justify-self: flex-end;\n  }\n\n  .App-menu_left {\n    grid-template-rows: 1fr auto 1fr;\n    height: 100%;\n  }\n\n  .App-menu_right {\n    grid-template-rows: 1fr;\n    height: 100%;\n  }\n\n  .App-menu__left {\n    overflow-y: auto;\n    padding: 0.75rem;\n    width: 12.5rem;\n    box-sizing: border-box;\n    position: absolute;\n  }\n\n  .dropdown-select {\n    height: 1.5rem;\n    padding: 0;\n    padding-inline-start: 0.5rem;\n    padding-inline-end: 1.5rem;\n    color: var(--icon-fill-color);\n    background-color: var(--button-gray-1);\n    border-radius: var(--space-factor);\n    border: 1px solid var(--button-gray-2);\n    font-size: 0.8rem;\n    font-family: inherit;\n    outline: none;\n    appearance: none;\n    background-image: var(--dropdown-icon);\n    background-repeat: no-repeat;\n    background-position: right 0.7rem top 50%, 0 0;\n\n    :root[dir=\"rtl\"] & {\n      background-position: left 0.7rem top 50%, 0 0;\n    }\n\n    background-size: 0.65em auto, 100%;\n\n    &:focus {\n      box-shadow: 0 0 0 2px var(--focus-highlight-color);\n    }\n\n    &:hover {\n      background-color: var(--button-gray-2);\n    }\n\n    &:active {\n      background-color: var(--button-gray-2);\n    }\n\n    &__language {\n      height: 2rem;\n      background-color: var(--island-bg-color);\n      border-color: var(--default-border-color) !important;\n      cursor: pointer;\n\n      &:hover {\n        background-color: var(--island-bg-color);\n      }\n    }\n  }\n\n  .scroll-back-to-content {\n    border-radius: var(--border-radius-lg);\n    background-color: var(--island-bg-color);\n    color: var(--icon-fill-color);\n\n    border: 1px solid var(--default-border-color);\n    padding: 10px 20px;\n    position: absolute;\n    left: 50%;\n    bottom: 30px;\n    transform: translateX(-50%);\n    pointer-events: var(--ui-pointerEvents);\n    font-family: inherit;\n\n    &:hover {\n      background-color: var(--button-hover-bg);\n    }\n\n    &:active {\n      border: 1px solid var(--button-active-border);\n    }\n  }\n\n  .help-icon {\n    @include outlineButtonStyles;\n    @include filledButtonOnCanvas;\n\n    width: var(--lg-button-size);\n    height: var(--lg-button-size);\n\n    svg {\n      width: var(--lg-icon-size);\n      height: var(--lg-icon-size);\n    }\n  }\n\n  .reset-zoom-button {\n    font-family: var(--ui-font);\n  }\n\n  .finalize-button {\n    display: grid;\n    grid-auto-flow: column;\n    gap: 0.4em;\n    margin-top: auto;\n    margin-bottom: auto;\n    margin-inline-start: 0.6em;\n  }\n\n  .undo-redo-buttons,\n  .eraser-buttons {\n    display: grid;\n    grid-auto-flow: column;\n    margin-top: auto;\n    margin-bottom: auto;\n    margin-inline-start: 0.6em;\n  }\n\n  @include isMobile {\n    aside {\n      display: none;\n    }\n    .scroll-back-to-content {\n      bottom: calc(80px + var(--sab, 0));\n      z-index: -1;\n    }\n  }\n\n  .rtl-mirror {\n    :root[dir=\"rtl\"] & {\n      transform: scaleX(-1);\n    }\n  }\n\n  .zen-mode-visibility {\n    visibility: visible;\n    opacity: 1;\n    height: auto;\n    width: auto;\n    transition: opacity 0.5s;\n\n    &.zen-mode-visibility--hidden {\n      visibility: hidden;\n      opacity: 0;\n      height: 0;\n      width: 0;\n      transition: opacity 0.5s;\n    }\n  }\n\n  .disable-pointerEvents {\n    pointer-events: none !important;\n  }\n\n  &.excalidraw--view-mode {\n    .App-menu {\n      display: flex;\n      justify-content: space-between;\n    }\n  }\n\n  input.is-redacted {\n    // we don't use type=password because browsers (chrome?) prompt\n    // you to save it which is annoying\n    -webkit-text-security: disc;\n  }\n\n  input[type=\"text\"],\n  textarea:not(.excalidraw-wysiwyg) {\n    color: var(--text-primary-color);\n    border: 1.5px solid var(--input-border-color);\n    padding: 0.75rem;\n    white-space: nowrap;\n    border-radius: var(--space-factor);\n    background-color: var(--input-bg-color);\n\n    &:not(:focus) {\n      &:hover {\n        border-color: var(--color-brand-hover);\n      }\n    }\n\n    &:focus {\n      outline: none;\n      border-color: var(--color-brand-hover);\n    }\n  }\n\n  @media print {\n    .App-bottom-bar,\n    .FixedSideContainer,\n    .layer-ui__wrapper {\n      display: none;\n    }\n  }\n\n  // use custom, minimalistic scrollbar\n  // (doesn't work in Firefox)\n  ::-webkit-scrollbar {\n    width: 4px;\n    height: 3px;\n  }\n\n  select::-webkit-scrollbar {\n    width: 10px;\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: var(--scrollbar-thumb);\n    border-radius: 10px;\n  }\n  ::-webkit-scrollbar-thumb:hover {\n    background: var(--scrollbar-thumb-hover);\n  }\n  ::-webkit-scrollbar-thumb:active {\n    background: var(--scrollbar-thumb);\n  }\n\n  .mobile-misc-tools-container {\n    position: absolute;\n    top: calc(5rem - var(--editor-container-padding));\n    right: calc(var(--editor-container-padding) * -1);\n    display: flex;\n    flex-direction: column;\n    border: 1px solid var(--sidebar-border-color);\n    border-top-left-radius: var(--border-radius-lg);\n    border-bottom-left-radius: var(--border-radius-lg);\n    border-right: 0;\n    overflow: hidden;\n\n    background-color: var(--island-bg-color);\n\n    .ToolIcon__icon {\n      width: 2rem;\n      height: 2rem;\n      border-radius: 0;\n    }\n\n    .default-sidebar-trigger {\n      border: 0;\n    }\n  }\n\n  .App-toolbar--mobile {\n    overflow: visible;\n    max-width: 98vw;\n\n    .ToolIcon__keybinding {\n      display: none;\n    }\n  }\n  .UserList-Wrapper {\n    margin: 0;\n    padding: 0;\n    border: none;\n    text-align: left;\n\n    legend {\n      display: block;\n      font-size: 0.75rem;\n      font-weight: 400;\n      margin: 0 0 0.25rem;\n      padding: 0;\n    }\n  }\n\n  .main-menu-trigger {\n    @include filledButtonOnCanvas;\n  }\n\n  .App-mobile-menu,\n  .App-menu__left {\n    --button-border: transparent;\n    --button-bg: var(--color-surface-mid);\n  }\n\n  @at-root .excalidraw.theme--dark#{&} {\n    .App-mobile-menu,\n    .App-menu__left {\n      --button-hover-bg: #363541;\n      --button-bg: var(--color-surface-high);\n    }\n  }\n\n  .App-menu__left {\n    .buttonList {\n      padding: 0.25rem 0;\n    }\n  }\n\n  .excalidraw__paragraph {\n    margin: 1rem 0;\n  }\n\n  .Modal__content {\n    .excalidraw__paragraph:first-child {\n      margin-top: 0;\n    }\n    .excalidraw__paragraph + .excalidraw__paragraph {\n      margin-top: 0rem;\n    }\n  }\n}\n\n.ErrorSplash.excalidraw {\n  min-height: 100vh;\n  padding: 20px 0;\n  overflow: auto;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  user-select: text;\n\n  .ErrorSplash-messageContainer {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n\n    padding: 40px;\n    background-color: $oc-red-1;\n    border: 3px solid $oc-red-9;\n  }\n\n  .ErrorSplash-paragraph {\n    margin: 15px 0;\n    max-width: 600px;\n\n    &.align-center {\n      text-align: center;\n    }\n  }\n\n  .bigger,\n  .bigger button {\n    font-size: 1.1em;\n  }\n\n  .smaller,\n  .smaller button {\n    font-size: 0.9em;\n  }\n\n  .ErrorSplash-details {\n    display: flex;\n    flex-direction: column;\n    align-items: flex-start;\n\n    textarea {\n      width: 100%;\n      margin: 10px 0;\n      font-family: \"Cascadia\";\n      font-size: 0.8em;\n    }\n  }\n}\n\n.excalidraw__embeddable-container {\n  .excalidraw__embeddable-container__inner {\n    overflow: hidden;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: var(--embeddable-radius);\n  }\n\n  .excalidraw__embeddable__outer {\n    width: 100%;\n    height: 100%;\n    & > * {\n      border-radius: var(--embeddable-radius);\n    }\n  }\n\n  .excalidraw__embeddable-hint {\n    position: absolute;\n    z-index: 1;\n    background: rgba(0, 0, 0, 0.5);\n    padding: 1rem 1.6rem;\n    border-radius: 12px;\n    color: #fff;\n    font-weight: 700;\n    letter-spacing: 0.6px;\n    font-family: \"Assistant\";\n  }\n}\n", "//\n//\n//  𝗖 𝗢 𝗟 𝗢 𝗥\n//  v 1.9.1\n//\n//  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n\n//  General\n//  ───────────────────────────────────\n\n$oc-white:         #ffffff;\n$oc-black:         #000000;\n\n\n//  Gray\n//  ───────────────────────────────────\n\n$oc-gray-list: (\n  \"0\": #f8f9fa,\n  \"1\": #f1f3f5,\n  \"2\": #e9ecef,\n  \"3\": #dee2e6,\n  \"4\": #ced4da,\n  \"5\": #adb5bd,\n  \"6\": #868e96,\n  \"7\": #495057,\n  \"8\": #343a40,\n  \"9\": #212529\n);\n\n$oc-gray-0: map-get($oc-gray-list, \"0\");\n$oc-gray-1: map-get($oc-gray-list, \"1\");\n$oc-gray-2: map-get($oc-gray-list, \"2\");\n$oc-gray-3: map-get($oc-gray-list, \"3\");\n$oc-gray-4: map-get($oc-gray-list, \"4\");\n$oc-gray-5: map-get($oc-gray-list, \"5\");\n$oc-gray-6: map-get($oc-gray-list, \"6\");\n$oc-gray-7: map-get($oc-gray-list, \"7\");\n$oc-gray-8: map-get($oc-gray-list, \"8\");\n$oc-gray-9: map-get($oc-gray-list, \"9\");\n\n\n//  Red\n//  ───────────────────────────────────\n\n$oc-red-list: (\n  \"0\": #fff5f5,\n  \"1\": #ffe3e3,\n  \"2\": #ffc9c9,\n  \"3\": #ffa8a8,\n  \"4\": #ff8787,\n  \"5\": #ff6b6b,\n  \"6\": #fa5252,\n  \"7\": #f03e3e,\n  \"8\": #e03131,\n  \"9\": #c92a2a\n);\n\n$oc-red-0: map-get($oc-red-list, \"0\");\n$oc-red-1: map-get($oc-red-list, \"1\");\n$oc-red-2: map-get($oc-red-list, \"2\");\n$oc-red-3: map-get($oc-red-list, \"3\");\n$oc-red-4: map-get($oc-red-list, \"4\");\n$oc-red-5: map-get($oc-red-list, \"5\");\n$oc-red-6: map-get($oc-red-list, \"6\");\n$oc-red-7: map-get($oc-red-list, \"7\");\n$oc-red-8: map-get($oc-red-list, \"8\");\n$oc-red-9: map-get($oc-red-list, \"9\");\n\n\n//  Pink\n//  ───────────────────────────────────\n\n$oc-pink-list: (\n  \"0\": #fff0f6,\n  \"1\": #ffdeeb,\n  \"2\": #fcc2d7,\n  \"3\": #faa2c1,\n  \"4\": #f783ac,\n  \"5\": #f06595,\n  \"6\": #e64980,\n  \"7\": #d6336c,\n  \"8\": #c2255c,\n  \"9\": #a61e4d\n);\n\n$oc-pink-0: map-get($oc-pink-list, \"0\");\n$oc-pink-1: map-get($oc-pink-list, \"1\");\n$oc-pink-2: map-get($oc-pink-list, \"2\");\n$oc-pink-3: map-get($oc-pink-list, \"3\");\n$oc-pink-4: map-get($oc-pink-list, \"4\");\n$oc-pink-5: map-get($oc-pink-list, \"5\");\n$oc-pink-6: map-get($oc-pink-list, \"6\");\n$oc-pink-7: map-get($oc-pink-list, \"7\");\n$oc-pink-8: map-get($oc-pink-list, \"8\");\n$oc-pink-9: map-get($oc-pink-list, \"9\");\n\n\n//  Grape\n//  ───────────────────────────────────\n\n$oc-grape-list: (\n  \"0\": #f8f0fc,\n  \"1\": #f3d9fa,\n  \"2\": #eebefa,\n  \"3\": #e599f7,\n  \"4\": #da77f2,\n  \"5\": #cc5de8,\n  \"6\": #be4bdb,\n  \"7\": #ae3ec9,\n  \"8\": #9c36b5,\n  \"9\": #862e9c\n);\n\n$oc-grape-0: map-get($oc-grape-list, \"0\");\n$oc-grape-1: map-get($oc-grape-list, \"1\");\n$oc-grape-2: map-get($oc-grape-list, \"2\");\n$oc-grape-3: map-get($oc-grape-list, \"3\");\n$oc-grape-4: map-get($oc-grape-list, \"4\");\n$oc-grape-5: map-get($oc-grape-list, \"5\");\n$oc-grape-6: map-get($oc-grape-list, \"6\");\n$oc-grape-7: map-get($oc-grape-list, \"7\");\n$oc-grape-8: map-get($oc-grape-list, \"8\");\n$oc-grape-9: map-get($oc-grape-list, \"9\");\n\n\n//  Violet\n//  ───────────────────────────────────\n\n$oc-violet-list: (\n  \"0\": #f3f0ff,\n  \"1\": #e5dbff,\n  \"2\": #d0bfff,\n  \"3\": #b197fc,\n  \"4\": #9775fa,\n  \"5\": #845ef7,\n  \"6\": #7950f2,\n  \"7\": #7048e8,\n  \"8\": #6741d9,\n  \"9\": #5f3dc4\n);\n\n$oc-violet-0: map-get($oc-violet-list, \"0\");\n$oc-violet-1: map-get($oc-violet-list, \"1\");\n$oc-violet-2: map-get($oc-violet-list, \"2\");\n$oc-violet-3: map-get($oc-violet-list, \"3\");\n$oc-violet-4: map-get($oc-violet-list, \"4\");\n$oc-violet-5: map-get($oc-violet-list, \"5\");\n$oc-violet-6: map-get($oc-violet-list, \"6\");\n$oc-violet-7: map-get($oc-violet-list, \"7\");\n$oc-violet-8: map-get($oc-violet-list, \"8\");\n$oc-violet-9: map-get($oc-violet-list, \"9\");\n\n\n//  Indigo\n//  ───────────────────────────────────\n\n$oc-indigo-list: (\n  \"0\": #edf2ff,\n  \"1\": #dbe4ff,\n  \"2\": #bac8ff,\n  \"3\": #91a7ff,\n  \"4\": #748ffc,\n  \"5\": #5c7cfa,\n  \"6\": #4c6ef5,\n  \"7\": #4263eb,\n  \"8\": #3b5bdb,\n  \"9\": #364fc7\n);\n\n$oc-indigo-0: map-get($oc-indigo-list, \"0\");\n$oc-indigo-1: map-get($oc-indigo-list, \"1\");\n$oc-indigo-2: map-get($oc-indigo-list, \"2\");\n$oc-indigo-3: map-get($oc-indigo-list, \"3\");\n$oc-indigo-4: map-get($oc-indigo-list, \"4\");\n$oc-indigo-5: map-get($oc-indigo-list, \"5\");\n$oc-indigo-6: map-get($oc-indigo-list, \"6\");\n$oc-indigo-7: map-get($oc-indigo-list, \"7\");\n$oc-indigo-8: map-get($oc-indigo-list, \"8\");\n$oc-indigo-9: map-get($oc-indigo-list, \"9\");\n\n\n//  Blue\n//  ───────────────────────────────────\n\n$oc-blue-list: (\n  \"0\": #e7f5ff,\n  \"1\": #d0ebff,\n  \"2\": #a5d8ff,\n  \"3\": #74c0fc,\n  \"4\": #4dabf7,\n  \"5\": #339af0,\n  \"6\": #228be6,\n  \"7\": #1c7ed6,\n  \"8\": #1971c2,\n  \"9\": #1864ab\n);\n\n$oc-blue-0: map-get($oc-blue-list, \"0\");\n$oc-blue-1: map-get($oc-blue-list, \"1\");\n$oc-blue-2: map-get($oc-blue-list, \"2\");\n$oc-blue-3: map-get($oc-blue-list, \"3\");\n$oc-blue-4: map-get($oc-blue-list, \"4\");\n$oc-blue-5: map-get($oc-blue-list, \"5\");\n$oc-blue-6: map-get($oc-blue-list, \"6\");\n$oc-blue-7: map-get($oc-blue-list, \"7\");\n$oc-blue-8: map-get($oc-blue-list, \"8\");\n$oc-blue-9: map-get($oc-blue-list, \"9\");\n\n\n//  Cyan\n//  ───────────────────────────────────\n\n$oc-cyan-list: (\n  \"0\": #e3fafc,\n  \"1\": #c5f6fa,\n  \"2\": #99e9f2,\n  \"3\": #66d9e8,\n  \"4\": #3bc9db,\n  \"5\": #22b8cf,\n  \"6\": #15aabf,\n  \"7\": #1098ad,\n  \"8\": #0c8599,\n  \"9\": #0b7285\n);\n\n$oc-cyan-0: map-get($oc-cyan-list, \"0\");\n$oc-cyan-1: map-get($oc-cyan-list, \"1\");\n$oc-cyan-2: map-get($oc-cyan-list, \"2\");\n$oc-cyan-3: map-get($oc-cyan-list, \"3\");\n$oc-cyan-4: map-get($oc-cyan-list, \"4\");\n$oc-cyan-5: map-get($oc-cyan-list, \"5\");\n$oc-cyan-6: map-get($oc-cyan-list, \"6\");\n$oc-cyan-7: map-get($oc-cyan-list, \"7\");\n$oc-cyan-8: map-get($oc-cyan-list, \"8\");\n$oc-cyan-9: map-get($oc-cyan-list, \"9\");\n\n\n//  Teal\n//  ───────────────────────────────────\n\n$oc-teal-list: (\n  \"0\": #e6fcf5,\n  \"1\": #c3fae8,\n  \"2\": #96f2d7,\n  \"3\": #63e6be,\n  \"4\": #38d9a9,\n  \"5\": #20c997,\n  \"6\": #12b886,\n  \"7\": #0ca678,\n  \"8\": #099268,\n  \"9\": #087f5b\n);\n\n$oc-teal-0: map-get($oc-teal-list, \"0\");\n$oc-teal-1: map-get($oc-teal-list, \"1\");\n$oc-teal-2: map-get($oc-teal-list, \"2\");\n$oc-teal-3: map-get($oc-teal-list, \"3\");\n$oc-teal-4: map-get($oc-teal-list, \"4\");\n$oc-teal-5: map-get($oc-teal-list, \"5\");\n$oc-teal-6: map-get($oc-teal-list, \"6\");\n$oc-teal-7: map-get($oc-teal-list, \"7\");\n$oc-teal-8: map-get($oc-teal-list, \"8\");\n$oc-teal-9: map-get($oc-teal-list, \"9\");\n\n\n//  Green\n//  ───────────────────────────────────\n\n$oc-green-list: (\n  \"0\": #ebfbee,\n  \"1\": #d3f9d8,\n  \"2\": #b2f2bb,\n  \"3\": #8ce99a,\n  \"4\": #69db7c,\n  \"5\": #51cf66,\n  \"6\": #40c057,\n  \"7\": #37b24d,\n  \"8\": #2f9e44,\n  \"9\": #2b8a3e\n);\n\n$oc-green-0: map-get($oc-green-list, \"0\");\n$oc-green-1: map-get($oc-green-list, \"1\");\n$oc-green-2: map-get($oc-green-list, \"2\");\n$oc-green-3: map-get($oc-green-list, \"3\");\n$oc-green-4: map-get($oc-green-list, \"4\");\n$oc-green-5: map-get($oc-green-list, \"5\");\n$oc-green-6: map-get($oc-green-list, \"6\");\n$oc-green-7: map-get($oc-green-list, \"7\");\n$oc-green-8: map-get($oc-green-list, \"8\");\n$oc-green-9: map-get($oc-green-list, \"9\");\n\n\n//  Lime\n//  ───────────────────────────────────\n\n$oc-lime-list: (\n  \"0\": #f4fce3,\n  \"1\": #e9fac8,\n  \"2\": #d8f5a2,\n  \"3\": #c0eb75,\n  \"4\": #a9e34b,\n  \"5\": #94d82d,\n  \"6\": #82c91e,\n  \"7\": #74b816,\n  \"8\": #66a80f,\n  \"9\": #5c940d\n);\n\n$oc-lime-0: map-get($oc-lime-list, \"0\");\n$oc-lime-1: map-get($oc-lime-list, \"1\");\n$oc-lime-2: map-get($oc-lime-list, \"2\");\n$oc-lime-3: map-get($oc-lime-list, \"3\");\n$oc-lime-4: map-get($oc-lime-list, \"4\");\n$oc-lime-5: map-get($oc-lime-list, \"5\");\n$oc-lime-6: map-get($oc-lime-list, \"6\");\n$oc-lime-7: map-get($oc-lime-list, \"7\");\n$oc-lime-8: map-get($oc-lime-list, \"8\");\n$oc-lime-9: map-get($oc-lime-list, \"9\");\n\n\n//  Yellow\n//  ───────────────────────────────────\n\n$oc-yellow-list: (\n  \"0\": #fff9db,\n  \"1\": #fff3bf,\n  \"2\": #ffec99,\n  \"3\": #ffe066,\n  \"4\": #ffd43b,\n  \"5\": #fcc419,\n  \"6\": #fab005,\n  \"7\": #f59f00,\n  \"8\": #f08c00,\n  \"9\": #e67700\n);\n\n$oc-yellow-0: map-get($oc-yellow-list, \"0\");\n$oc-yellow-1: map-get($oc-yellow-list, \"1\");\n$oc-yellow-2: map-get($oc-yellow-list, \"2\");\n$oc-yellow-3: map-get($oc-yellow-list, \"3\");\n$oc-yellow-4: map-get($oc-yellow-list, \"4\");\n$oc-yellow-5: map-get($oc-yellow-list, \"5\");\n$oc-yellow-6: map-get($oc-yellow-list, \"6\");\n$oc-yellow-7: map-get($oc-yellow-list, \"7\");\n$oc-yellow-8: map-get($oc-yellow-list, \"8\");\n$oc-yellow-9: map-get($oc-yellow-list, \"9\");\n\n\n//  Orange\n//  ───────────────────────────────────\n\n$oc-orange-list: (\n  \"0\": #fff4e6,\n  \"1\": #ffe8cc,\n  \"2\": #ffd8a8,\n  \"3\": #ffc078,\n  \"4\": #ffa94d,\n  \"5\": #ff922b,\n  \"6\": #fd7e14,\n  \"7\": #f76707,\n  \"8\": #e8590c,\n  \"9\": #d9480f\n);\n\n$oc-orange-0: map-get($oc-orange-list, \"0\");\n$oc-orange-1: map-get($oc-orange-list, \"1\");\n$oc-orange-2: map-get($oc-orange-list, \"2\");\n$oc-orange-3: map-get($oc-orange-list, \"3\");\n$oc-orange-4: map-get($oc-orange-list, \"4\");\n$oc-orange-5: map-get($oc-orange-list, \"5\");\n$oc-orange-6: map-get($oc-orange-list, \"6\");\n$oc-orange-7: map-get($oc-orange-list, \"7\");\n$oc-orange-8: map-get($oc-orange-list, \"8\");\n$oc-orange-9: map-get($oc-orange-list, \"9\");\n\n\n//  Color list\n//  ───────────────────────────────────\n\n$oc-color-spectrum:   9;\n\n$oc-color-list: (\n  $oc-gray-list:      \"gray\",\n  $oc-red-list:      \"red\",\n  $oc-pink-list:      \"pink\",\n  $oc-grape-list:      \"grape\",\n  $oc-violet-list:      \"violet\",\n  $oc-indigo-list:      \"indigo\",\n  $oc-blue-list:      \"blue\",\n  $oc-cyan-list:      \"cyan\",\n  $oc-teal-list:      \"teal\",\n  $oc-green-list:      \"green\",\n  $oc-lime-list:      \"lime\",\n  $oc-yellow-list:      \"yellow\",\n  $oc-orange-list:      \"orange\"\n);\n", "@import \"open-color/open-color.scss\";\n\n@mixin isMobile() {\n  @at-root .excalidraw--mobile#{&} {\n    @content;\n  }\n}\n\n@mixin toolbarButtonColorStates {\n  &.fillable {\n    .ToolIcon_type_radio,\n    .ToolIcon_type_checkbox {\n      &:checked + .ToolIcon__icon {\n        --icon-fill-color: var(--color-on-primary-container);\n\n        svg {\n          fill: var(--icon-fill-color);\n        }\n      }\n    }\n  }\n\n  .ToolIcon_type_radio,\n  .ToolIcon_type_checkbox {\n    &:checked + .ToolIcon__icon {\n      background: var(--color-surface-primary-container);\n      --keybinding-color: var(--color-on-primary-container);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n  }\n\n  .ToolIcon__keybinding {\n    bottom: 4px;\n    right: 4px;\n  }\n\n  .ToolIcon__icon {\n    &:hover {\n      background: var(--button-hover-bg);\n    }\n\n    &:active {\n      background: var(--button-hover-bg);\n      border: 1px solid var(--button-active-border);\n\n      svg {\n        color: var(--color-on-primary-container);\n      }\n    }\n\n    &[aria-disabled=\"true\"] {\n      background: initial;\n      border: none;\n\n      svg {\n        color: var(--color-disabled);\n      }\n    }\n  }\n}\n\n@mixin outlineButtonStyles {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0.625rem;\n  width: var(--button-width, var(--default-button-size));\n  height: var(--button-height, var(--default-button-size));\n  box-sizing: border-box;\n  border-width: 1px;\n  border-style: solid;\n  border-color: var(--button-border, var(--default-border-color));\n  border-radius: var(--border-radius-lg);\n  cursor: pointer;\n  background-color: var(--button-bg, var(--island-bg-color));\n  color: var(--button-color, var(--color-on-surface));\n  font-family: var(--ui-font);\n\n  svg {\n    width: var(--button-width, var(--lg-icon-size));\n    height: var(--button-height, var(--lg-icon-size));\n  }\n\n  &:hover {\n    background-color: var(--button-hover-bg, var(--island-bg-color));\n    border-color: var(\n      --button-hover-border,\n      var(--button-border, var(--default-border-color))\n    );\n    color: var(\n      --button-hover-color,\n      var(--button-color, var(--text-primary-color, inherit))\n    );\n  }\n\n  &:active {\n    background-color: var(--button-active-bg, var(--island-bg-color));\n    border-color: var(--button-active-border, var(--color-primary-darkest));\n  }\n\n  &.active {\n    background-color: var(\n      --button-selected-bg,\n      var(--color-surface-primary-container)\n    );\n    border-color: var(\n      --button-selected-border,\n      var(--color-surface-primary-container)\n    );\n\n    &:hover {\n      background-color: var(\n        --button-selected-hover-bg,\n        var(--color-surface-primary-container)\n      );\n    }\n\n    svg {\n      color: var(--button-color, var(--color-on-primary-container));\n    }\n  }\n}\n\n@mixin outlineButtonIconStyles {\n  @include outlineButtonStyles;\n  padding: 0;\n\n  svg {\n    width: var(--default-icon-size);\n    height: var(--default-icon-size);\n  }\n}\n\n@mixin avatarStyles {\n  width: var(--avatar-size, 1.5rem);\n  height: var(--avatar-size, 1.5rem);\n  position: relative;\n  border-radius: 100%;\n  outline-offset: 2px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  font-size: 0.75rem;\n  font-weight: 700;\n  line-height: 1;\n  color: var(--color-gray-90);\n  flex: 0 0 auto;\n\n  &:active {\n    transform: scale(0.94);\n  }\n\n  &-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 100%;\n  }\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: -3px;\n    right: -3px;\n    bottom: -3px;\n    left: -3px;\n    border-radius: 100%;\n  }\n\n  &.is-followed::before {\n    border-color: var(--color-primary-hover);\n    box-shadow: 0 0 0 1px var(--color-primary-hover);\n  }\n  &.is-current-user {\n    cursor: auto;\n  }\n}\n\n@mixin filledButtonOnCanvas {\n  border: none;\n  box-shadow: 0 0 0 1px var(--color-surface-lowest);\n  background-color: var(--color-surface-low);\n\n  &:active {\n    box-shadow: 0 0 0 1px var(--color-brand-active);\n  }\n}\n", "/* Only UI fonts here, which are needed before the editor initializes. */\n/* These cannot be dynamically prepended with `EXCALIDRAW_ASSET_PATH`. */\n/* WARN: The following content is replaced during excalidraw-app build  */\n\n@font-face {\n  font-family: \"Assistant\";\n  src: url(../fonts/Assistant/Assistant-Regular.woff2) format(\"woff2\");\n  font-weight: 400;\n  style: normal;\n  display: swap;\n}\n\n@font-face {\n  font-family: \"Assistant\";\n  src: url(../fonts/Assistant/Assistant-Medium.woff2) format(\"woff2\");\n  font-weight: 500;\n  style: normal;\n  display: swap;\n}\n\n@font-face {\n  font-family: \"Assistant\";\n  src: url(../fonts/Assistant/Assistant-SemiBold.woff2) format(\"woff2\");\n  font-weight: 600;\n  style: normal;\n  display: swap;\n}\n\n@font-face {\n  font-family: \"Assistant\";\n  src: url(../fonts/Assistant/Assistant-Bold.woff2) format(\"woff2\");\n  font-weight: 700;\n  style: normal;\n  display: swap;\n}\n", ".footer-center {\n  pointer-events: none;\n  & > * {\n    pointer-events: var(--ui-pointerEvents);\n  }\n\n  display: flex;\n  width: 100%;\n  justify-content: flex-start;\n  margin-inline-end: 0.6rem;\n}\n", ".excalidraw {\n  .ExcalidrawLogo {\n    --logo-icon--xs: 2rem;\n    --logo-text--xs: 1.5rem;\n\n    --logo-icon--small: 2.5rem;\n    --logo-text--small: 1.75rem;\n\n    --logo-icon--normal: 3rem;\n    --logo-text--normal: 2.2rem;\n\n    --logo-icon--large: 90px;\n    --logo-text--large: 65px;\n\n    display: flex;\n    align-items: center;\n\n    svg {\n      flex: 0 0 auto;\n    }\n\n    .ExcalidrawLogo-icon {\n      width: auto;\n      color: var(--color-logo-icon);\n    }\n\n    .ExcalidrawLogo-text {\n      margin-left: 0.75rem;\n      width: auto;\n      color: var(--color-logo-text);\n    }\n\n    &.is-xs {\n      .ExcalidrawLogo-icon {\n        height: var(--logo-icon--xs);\n      }\n\n      .ExcalidrawLogo-text {\n        height: var(--logo-text--xs);\n      }\n    }\n\n    &.is-small {\n      .ExcalidrawLogo-icon {\n        height: var(--logo-icon--small);\n      }\n\n      .ExcalidrawLogo-text {\n        height: var(--logo-text--small);\n      }\n    }\n\n    &.is-normal {\n      .ExcalidrawLogo-icon {\n        height: var(--logo-icon--normal);\n      }\n\n      .ExcalidrawLogo-text {\n        height: var(--logo-text--normal);\n      }\n    }\n\n    &.is-large {\n      .ExcalidrawLogo-icon {\n        height: var(--logo-icon--large);\n      }\n\n      .ExcalidrawLogo-text {\n        height: var(--logo-text--large);\n      }\n    }\n  }\n}\n", ".excalidraw {\n  .excalifont {\n    font-family: \"Excalifont\", \"<PERSON><PERSON>\";\n  }\n\n  // WelcomeSreen common\n  // ---------------------------------------------------------------------------\n\n  .welcome-screen-decor {\n    pointer-events: none;\n\n    color: var(--color-gray-40);\n\n    a {\n      --color: var(--color-primary);\n      color: var(--color);\n      text-decoration: none;\n      margin-bottom: -6px;\n    }\n  }\n\n  &.theme--dark {\n    .welcome-screen-decor {\n      color: var(--color-gray-60);\n    }\n  }\n\n  // WelcomeScreen.Hints\n  // ---------------------------------------------------------------------------\n\n  .welcome-screen-decor-hint {\n    @media (max-height: 599px) {\n      display: none !important;\n    }\n\n    @media (max-width: 1024px), (max-width: 800px) {\n      .welcome-screen-decor {\n        &--help,\n        &--menu {\n          display: none;\n        }\n      }\n    }\n\n    &--help {\n      display: flex;\n      position: absolute;\n      right: 0;\n      bottom: 100%;\n\n      :root[dir=\"rtl\"] & {\n        left: 0;\n        right: auto;\n      }\n\n      svg {\n        margin-top: 0.5rem;\n        width: 85px;\n        height: 71px;\n\n        transform: scaleX(-1) rotate(80deg);\n\n        :root[dir=\"rtl\"] & {\n          transform: rotate(80deg);\n        }\n      }\n    }\n\n    &--toolbar {\n      position: absolute;\n      top: 100%;\n      left: 50%;\n      transform: translateX(-50%);\n      margin-top: 2.5rem;\n      display: flex;\n      align-items: baseline;\n\n      .welcome-screen-decor-hint__label {\n        width: 120px;\n        position: relative;\n        top: -0.5rem;\n      }\n\n      svg {\n        width: 38px;\n        height: 78px;\n\n        :root[dir=\"rtl\"] & {\n          transform: scaleX(-1);\n        }\n      }\n    }\n\n    &--menu {\n      position: absolute;\n      width: 320px;\n      font-size: 1rem;\n\n      top: 100%;\n      margin-top: 0.25rem;\n      margin-inline-start: 0.6rem;\n\n      display: flex;\n      align-items: flex-end;\n      gap: 0.5rem;\n\n      svg {\n        width: 41px;\n        height: 94px;\n\n        :root[dir=\"rtl\"] & {\n          transform: scaleX(-1);\n        }\n      }\n\n      @media (max-width: 860px) {\n        .welcome-screen-decor-hint__label {\n          max-width: 160px;\n        }\n      }\n    }\n  }\n\n  // WelcomeSreen.Center\n  // ---------------------------------------------------------------------------\n\n  .welcome-screen-center {\n    display: flex;\n    flex-direction: column;\n    gap: 2rem;\n    justify-content: center;\n    align-items: center;\n    position: absolute;\n    pointer-events: none;\n    left: 1rem;\n    top: 1rem;\n    right: 1rem;\n    bottom: 1rem;\n  }\n\n  .welcome-screen-center__logo {\n    display: flex;\n    align-items: center;\n    column-gap: 0.75rem;\n    font-size: 2.25rem;\n  }\n\n  .welcome-screen-center__heading {\n    font-size: 1.125rem;\n    text-align: center;\n  }\n\n  .welcome-screen-menu {\n    display: flex;\n    flex-direction: column;\n    gap: 2px;\n    justify-content: center;\n    align-items: center;\n  }\n\n  .welcome-screen-menu-item {\n    box-sizing: border-box;\n\n    pointer-events: var(--ui-pointerEvents);\n\n    color: var(--color-gray-50);\n    font-size: 0.875rem;\n\n    width: 100%;\n    min-width: 300px;\n    max-width: 400px;\n    display: grid;\n    align-items: center;\n    justify-content: space-between;\n\n    background: none;\n    border: 1px solid transparent;\n\n    padding: 0.75rem;\n\n    border-radius: var(--border-radius-md);\n\n    grid-template-columns: calc(var(--default-icon-size) + 0.5rem) 1fr 3rem;\n\n    &__text {\n      display: flex;\n      align-items: center;\n      margin-right: auto;\n      text-align: left;\n      column-gap: 0.5rem;\n    }\n\n    &__icon {\n      width: var(--default-icon-size);\n      height: var(--default-icon-size);\n    }\n\n    &__shortcut {\n      margin-left: auto;\n      color: var(--color-gray-40);\n      font-size: 0.75rem;\n    }\n  }\n\n  .welcome-screen-menu-item:hover {\n    text-decoration: none;\n    background: var(--button-hover-bg);\n\n    .welcome-screen-menu-item__shortcut,\n    .welcome-screen-menu-item__icon,\n    .welcome-screen-menu-item__text {\n      color: var(--color-gray-100);\n    }\n  }\n\n  .welcome-screen-menu-item:active {\n    background: var(--button-hover-bg);\n    border-color: var(--color-brand-active);\n\n    .welcome-screen-menu-item__shortcut,\n    .welcome-screen-menu-item__icon,\n    .welcome-screen-menu-item__text {\n      color: var(--color-gray-100);\n    }\n  }\n\n  &.theme--dark {\n    .welcome-screen-menu-item {\n      color: var(--color-gray-60);\n\n      &__shortcut {\n        color: var(--color-gray-60);\n      }\n    }\n\n    .welcome-screen-menu-item:hover {\n      background-color: var(--color-surface-low);\n\n      .welcome-screen-menu-item__icon,\n      .welcome-screen-menu-item__shortcut,\n      .welcome-screen-menu-item__text {\n        color: var(--color-gray-10);\n      }\n    }\n\n    .welcome-screen-menu-item:active {\n      .welcome-screen-menu-item__icon,\n      .welcome-screen-menu-item__shortcut,\n      .welcome-screen-menu-item__text {\n        color: var(--color-gray-10);\n      }\n    }\n  }\n\n  @media (max-height: 599px) {\n    .welcome-screen-center {\n      margin-top: 4rem;\n    }\n  }\n  @media (min-height: 600px) and (max-height: 900px) {\n    .welcome-screen-center {\n      margin-top: 8rem;\n    }\n  }\n  @media (max-height: 500px), (max-width: 320px) {\n    .welcome-screen-center {\n      display: none;\n    }\n  }\n\n  // ---------------------------------------------------------------------------\n}\n", "@import \"../../css/variables.module.scss\";\n\n.excalidraw {\n  .collab-button {\n    --button-bg: var(--color-primary);\n    --button-color: var(--color-surface-lowest);\n    --button-border: var(--color-primary);\n\n    --button-width: var(--lg-button-size);\n    --button-height: var(--lg-button-size);\n\n    --button-hover-bg: var(--color-primary-darker);\n    --button-hover-border: var(--color-primary-darker);\n\n    --button-active-bg: var(--color-primary-darker);\n\n    box-shadow: 0 0 0 1px var(--color-surface-lowest);\n\n    flex-shrink: 0;\n\n    // double .active to force specificity\n    &.active.active {\n      background-color: #0fb884;\n      border-color: #0fb884;\n\n      svg {\n        color: #fff;\n      }\n\n      &:hover,\n      &:active {\n        background-color: #0fb884;\n        border-color: #0fb884;\n      }\n    }\n  }\n\n  .CollabButton.is-collaborating {\n    background-color: var(--button-special-active-bg-color);\n\n    .ToolIcon__icon svg,\n    .ToolIcon__label {\n      color: var(--icon-green-fill-color);\n    }\n  }\n\n  .CollabButton-collaborators {\n    :root[dir=\"ltr\"] & {\n      right: -5px;\n    }\n    :root[dir=\"rtl\"] & {\n      left: -5px;\n    }\n    min-width: 1em;\n    min-height: 1em;\n    line-height: 1;\n    position: absolute;\n    bottom: -5px;\n    padding: 3px;\n    border-radius: 50%;\n    background-color: $oc-green-2;\n    color: $oc-green-9;\n    font-size: 0.6rem;\n    font-family: \"Cascadia\";\n  }\n}\n", "//\n//\n//  𝗖 𝗢 𝗟 𝗢 𝗥\n//  v 1.9.1\n//\n//  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n\n//  General\n//  ───────────────────────────────────\n\n$oc-white:         #ffffff;\n$oc-black:         #000000;\n\n\n//  Gray\n//  ───────────────────────────────────\n\n$oc-gray-list: (\n  \"0\": #f8f9fa,\n  \"1\": #f1f3f5,\n  \"2\": #e9ecef,\n  \"3\": #dee2e6,\n  \"4\": #ced4da,\n  \"5\": #adb5bd,\n  \"6\": #868e96,\n  \"7\": #495057,\n  \"8\": #343a40,\n  \"9\": #212529\n);\n\n$oc-gray-0: map-get($oc-gray-list, \"0\");\n$oc-gray-1: map-get($oc-gray-list, \"1\");\n$oc-gray-2: map-get($oc-gray-list, \"2\");\n$oc-gray-3: map-get($oc-gray-list, \"3\");\n$oc-gray-4: map-get($oc-gray-list, \"4\");\n$oc-gray-5: map-get($oc-gray-list, \"5\");\n$oc-gray-6: map-get($oc-gray-list, \"6\");\n$oc-gray-7: map-get($oc-gray-list, \"7\");\n$oc-gray-8: map-get($oc-gray-list, \"8\");\n$oc-gray-9: map-get($oc-gray-list, \"9\");\n\n\n//  Red\n//  ───────────────────────────────────\n\n$oc-red-list: (\n  \"0\": #fff5f5,\n  \"1\": #ffe3e3,\n  \"2\": #ffc9c9,\n  \"3\": #ffa8a8,\n  \"4\": #ff8787,\n  \"5\": #ff6b6b,\n  \"6\": #fa5252,\n  \"7\": #f03e3e,\n  \"8\": #e03131,\n  \"9\": #c92a2a\n);\n\n$oc-red-0: map-get($oc-red-list, \"0\");\n$oc-red-1: map-get($oc-red-list, \"1\");\n$oc-red-2: map-get($oc-red-list, \"2\");\n$oc-red-3: map-get($oc-red-list, \"3\");\n$oc-red-4: map-get($oc-red-list, \"4\");\n$oc-red-5: map-get($oc-red-list, \"5\");\n$oc-red-6: map-get($oc-red-list, \"6\");\n$oc-red-7: map-get($oc-red-list, \"7\");\n$oc-red-8: map-get($oc-red-list, \"8\");\n$oc-red-9: map-get($oc-red-list, \"9\");\n\n\n//  Pink\n//  ───────────────────────────────────\n\n$oc-pink-list: (\n  \"0\": #fff0f6,\n  \"1\": #ffdeeb,\n  \"2\": #fcc2d7,\n  \"3\": #faa2c1,\n  \"4\": #f783ac,\n  \"5\": #f06595,\n  \"6\": #e64980,\n  \"7\": #d6336c,\n  \"8\": #c2255c,\n  \"9\": #a61e4d\n);\n\n$oc-pink-0: map-get($oc-pink-list, \"0\");\n$oc-pink-1: map-get($oc-pink-list, \"1\");\n$oc-pink-2: map-get($oc-pink-list, \"2\");\n$oc-pink-3: map-get($oc-pink-list, \"3\");\n$oc-pink-4: map-get($oc-pink-list, \"4\");\n$oc-pink-5: map-get($oc-pink-list, \"5\");\n$oc-pink-6: map-get($oc-pink-list, \"6\");\n$oc-pink-7: map-get($oc-pink-list, \"7\");\n$oc-pink-8: map-get($oc-pink-list, \"8\");\n$oc-pink-9: map-get($oc-pink-list, \"9\");\n\n\n//  Grape\n//  ───────────────────────────────────\n\n$oc-grape-list: (\n  \"0\": #f8f0fc,\n  \"1\": #f3d9fa,\n  \"2\": #eebefa,\n  \"3\": #e599f7,\n  \"4\": #da77f2,\n  \"5\": #cc5de8,\n  \"6\": #be4bdb,\n  \"7\": #ae3ec9,\n  \"8\": #9c36b5,\n  \"9\": #862e9c\n);\n\n$oc-grape-0: map-get($oc-grape-list, \"0\");\n$oc-grape-1: map-get($oc-grape-list, \"1\");\n$oc-grape-2: map-get($oc-grape-list, \"2\");\n$oc-grape-3: map-get($oc-grape-list, \"3\");\n$oc-grape-4: map-get($oc-grape-list, \"4\");\n$oc-grape-5: map-get($oc-grape-list, \"5\");\n$oc-grape-6: map-get($oc-grape-list, \"6\");\n$oc-grape-7: map-get($oc-grape-list, \"7\");\n$oc-grape-8: map-get($oc-grape-list, \"8\");\n$oc-grape-9: map-get($oc-grape-list, \"9\");\n\n\n//  Violet\n//  ───────────────────────────────────\n\n$oc-violet-list: (\n  \"0\": #f3f0ff,\n  \"1\": #e5dbff,\n  \"2\": #d0bfff,\n  \"3\": #b197fc,\n  \"4\": #9775fa,\n  \"5\": #845ef7,\n  \"6\": #7950f2,\n  \"7\": #7048e8,\n  \"8\": #6741d9,\n  \"9\": #5f3dc4\n);\n\n$oc-violet-0: map-get($oc-violet-list, \"0\");\n$oc-violet-1: map-get($oc-violet-list, \"1\");\n$oc-violet-2: map-get($oc-violet-list, \"2\");\n$oc-violet-3: map-get($oc-violet-list, \"3\");\n$oc-violet-4: map-get($oc-violet-list, \"4\");\n$oc-violet-5: map-get($oc-violet-list, \"5\");\n$oc-violet-6: map-get($oc-violet-list, \"6\");\n$oc-violet-7: map-get($oc-violet-list, \"7\");\n$oc-violet-8: map-get($oc-violet-list, \"8\");\n$oc-violet-9: map-get($oc-violet-list, \"9\");\n\n\n//  Indigo\n//  ───────────────────────────────────\n\n$oc-indigo-list: (\n  \"0\": #edf2ff,\n  \"1\": #dbe4ff,\n  \"2\": #bac8ff,\n  \"3\": #91a7ff,\n  \"4\": #748ffc,\n  \"5\": #5c7cfa,\n  \"6\": #4c6ef5,\n  \"7\": #4263eb,\n  \"8\": #3b5bdb,\n  \"9\": #364fc7\n);\n\n$oc-indigo-0: map-get($oc-indigo-list, \"0\");\n$oc-indigo-1: map-get($oc-indigo-list, \"1\");\n$oc-indigo-2: map-get($oc-indigo-list, \"2\");\n$oc-indigo-3: map-get($oc-indigo-list, \"3\");\n$oc-indigo-4: map-get($oc-indigo-list, \"4\");\n$oc-indigo-5: map-get($oc-indigo-list, \"5\");\n$oc-indigo-6: map-get($oc-indigo-list, \"6\");\n$oc-indigo-7: map-get($oc-indigo-list, \"7\");\n$oc-indigo-8: map-get($oc-indigo-list, \"8\");\n$oc-indigo-9: map-get($oc-indigo-list, \"9\");\n\n\n//  Blue\n//  ───────────────────────────────────\n\n$oc-blue-list: (\n  \"0\": #e7f5ff,\n  \"1\": #d0ebff,\n  \"2\": #a5d8ff,\n  \"3\": #74c0fc,\n  \"4\": #4dabf7,\n  \"5\": #339af0,\n  \"6\": #228be6,\n  \"7\": #1c7ed6,\n  \"8\": #1971c2,\n  \"9\": #1864ab\n);\n\n$oc-blue-0: map-get($oc-blue-list, \"0\");\n$oc-blue-1: map-get($oc-blue-list, \"1\");\n$oc-blue-2: map-get($oc-blue-list, \"2\");\n$oc-blue-3: map-get($oc-blue-list, \"3\");\n$oc-blue-4: map-get($oc-blue-list, \"4\");\n$oc-blue-5: map-get($oc-blue-list, \"5\");\n$oc-blue-6: map-get($oc-blue-list, \"6\");\n$oc-blue-7: map-get($oc-blue-list, \"7\");\n$oc-blue-8: map-get($oc-blue-list, \"8\");\n$oc-blue-9: map-get($oc-blue-list, \"9\");\n\n\n//  Cyan\n//  ───────────────────────────────────\n\n$oc-cyan-list: (\n  \"0\": #e3fafc,\n  \"1\": #c5f6fa,\n  \"2\": #99e9f2,\n  \"3\": #66d9e8,\n  \"4\": #3bc9db,\n  \"5\": #22b8cf,\n  \"6\": #15aabf,\n  \"7\": #1098ad,\n  \"8\": #0c8599,\n  \"9\": #0b7285\n);\n\n$oc-cyan-0: map-get($oc-cyan-list, \"0\");\n$oc-cyan-1: map-get($oc-cyan-list, \"1\");\n$oc-cyan-2: map-get($oc-cyan-list, \"2\");\n$oc-cyan-3: map-get($oc-cyan-list, \"3\");\n$oc-cyan-4: map-get($oc-cyan-list, \"4\");\n$oc-cyan-5: map-get($oc-cyan-list, \"5\");\n$oc-cyan-6: map-get($oc-cyan-list, \"6\");\n$oc-cyan-7: map-get($oc-cyan-list, \"7\");\n$oc-cyan-8: map-get($oc-cyan-list, \"8\");\n$oc-cyan-9: map-get($oc-cyan-list, \"9\");\n\n\n//  Teal\n//  ───────────────────────────────────\n\n$oc-teal-list: (\n  \"0\": #e6fcf5,\n  \"1\": #c3fae8,\n  \"2\": #96f2d7,\n  \"3\": #63e6be,\n  \"4\": #38d9a9,\n  \"5\": #20c997,\n  \"6\": #12b886,\n  \"7\": #0ca678,\n  \"8\": #099268,\n  \"9\": #087f5b\n);\n\n$oc-teal-0: map-get($oc-teal-list, \"0\");\n$oc-teal-1: map-get($oc-teal-list, \"1\");\n$oc-teal-2: map-get($oc-teal-list, \"2\");\n$oc-teal-3: map-get($oc-teal-list, \"3\");\n$oc-teal-4: map-get($oc-teal-list, \"4\");\n$oc-teal-5: map-get($oc-teal-list, \"5\");\n$oc-teal-6: map-get($oc-teal-list, \"6\");\n$oc-teal-7: map-get($oc-teal-list, \"7\");\n$oc-teal-8: map-get($oc-teal-list, \"8\");\n$oc-teal-9: map-get($oc-teal-list, \"9\");\n\n\n//  Green\n//  ───────────────────────────────────\n\n$oc-green-list: (\n  \"0\": #ebfbee,\n  \"1\": #d3f9d8,\n  \"2\": #b2f2bb,\n  \"3\": #8ce99a,\n  \"4\": #69db7c,\n  \"5\": #51cf66,\n  \"6\": #40c057,\n  \"7\": #37b24d,\n  \"8\": #2f9e44,\n  \"9\": #2b8a3e\n);\n\n$oc-green-0: map-get($oc-green-list, \"0\");\n$oc-green-1: map-get($oc-green-list, \"1\");\n$oc-green-2: map-get($oc-green-list, \"2\");\n$oc-green-3: map-get($oc-green-list, \"3\");\n$oc-green-4: map-get($oc-green-list, \"4\");\n$oc-green-5: map-get($oc-green-list, \"5\");\n$oc-green-6: map-get($oc-green-list, \"6\");\n$oc-green-7: map-get($oc-green-list, \"7\");\n$oc-green-8: map-get($oc-green-list, \"8\");\n$oc-green-9: map-get($oc-green-list, \"9\");\n\n\n//  Lime\n//  ───────────────────────────────────\n\n$oc-lime-list: (\n  \"0\": #f4fce3,\n  \"1\": #e9fac8,\n  \"2\": #d8f5a2,\n  \"3\": #c0eb75,\n  \"4\": #a9e34b,\n  \"5\": #94d82d,\n  \"6\": #82c91e,\n  \"7\": #74b816,\n  \"8\": #66a80f,\n  \"9\": #5c940d\n);\n\n$oc-lime-0: map-get($oc-lime-list, \"0\");\n$oc-lime-1: map-get($oc-lime-list, \"1\");\n$oc-lime-2: map-get($oc-lime-list, \"2\");\n$oc-lime-3: map-get($oc-lime-list, \"3\");\n$oc-lime-4: map-get($oc-lime-list, \"4\");\n$oc-lime-5: map-get($oc-lime-list, \"5\");\n$oc-lime-6: map-get($oc-lime-list, \"6\");\n$oc-lime-7: map-get($oc-lime-list, \"7\");\n$oc-lime-8: map-get($oc-lime-list, \"8\");\n$oc-lime-9: map-get($oc-lime-list, \"9\");\n\n\n//  Yellow\n//  ───────────────────────────────────\n\n$oc-yellow-list: (\n  \"0\": #fff9db,\n  \"1\": #fff3bf,\n  \"2\": #ffec99,\n  \"3\": #ffe066,\n  \"4\": #ffd43b,\n  \"5\": #fcc419,\n  \"6\": #fab005,\n  \"7\": #f59f00,\n  \"8\": #f08c00,\n  \"9\": #e67700\n);\n\n$oc-yellow-0: map-get($oc-yellow-list, \"0\");\n$oc-yellow-1: map-get($oc-yellow-list, \"1\");\n$oc-yellow-2: map-get($oc-yellow-list, \"2\");\n$oc-yellow-3: map-get($oc-yellow-list, \"3\");\n$oc-yellow-4: map-get($oc-yellow-list, \"4\");\n$oc-yellow-5: map-get($oc-yellow-list, \"5\");\n$oc-yellow-6: map-get($oc-yellow-list, \"6\");\n$oc-yellow-7: map-get($oc-yellow-list, \"7\");\n$oc-yellow-8: map-get($oc-yellow-list, \"8\");\n$oc-yellow-9: map-get($oc-yellow-list, \"9\");\n\n\n//  Orange\n//  ───────────────────────────────────\n\n$oc-orange-list: (\n  \"0\": #fff4e6,\n  \"1\": #ffe8cc,\n  \"2\": #ffd8a8,\n  \"3\": #ffc078,\n  \"4\": #ffa94d,\n  \"5\": #ff922b,\n  \"6\": #fd7e14,\n  \"7\": #f76707,\n  \"8\": #e8590c,\n  \"9\": #d9480f\n);\n\n$oc-orange-0: map-get($oc-orange-list, \"0\");\n$oc-orange-1: map-get($oc-orange-list, \"1\");\n$oc-orange-2: map-get($oc-orange-list, \"2\");\n$oc-orange-3: map-get($oc-orange-list, \"3\");\n$oc-orange-4: map-get($oc-orange-list, \"4\");\n$oc-orange-5: map-get($oc-orange-list, \"5\");\n$oc-orange-6: map-get($oc-orange-list, \"6\");\n$oc-orange-7: map-get($oc-orange-list, \"7\");\n$oc-orange-8: map-get($oc-orange-list, \"8\");\n$oc-orange-9: map-get($oc-orange-list, \"9\");\n\n\n//  Color list\n//  ───────────────────────────────────\n\n$oc-color-spectrum:   9;\n\n$oc-color-list: (\n  $oc-gray-list:      \"gray\",\n  $oc-red-list:      \"red\",\n  $oc-pink-list:      \"pink\",\n  $oc-grape-list:      \"grape\",\n  $oc-violet-list:      \"violet\",\n  $oc-indigo-list:      \"indigo\",\n  $oc-blue-list:      \"blue\",\n  $oc-cyan-list:      \"cyan\",\n  $oc-teal-list:      \"teal\",\n  $oc-green-list:      \"green\",\n  $oc-lime-list:      \"lime\",\n  $oc-yellow-list:      \"yellow\",\n  $oc-orange-list:      \"orange\"\n);\n"], "mappings": ";;;AAKE,CAAA,WAAA,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,UAAA;AACA,eAAA;AACA,gBAAA;AAEA,mBAAA,IAAA;;AAEA,CAVF,WAUE,CAVF,QAUE;AACE,aAAA,OAAA,KAAA,OAAA;AACA,mBAAA,IAAA;AACA,oBAAA,OAAA;;AAGF,CAhBF,WAgBE,CAhBF,QAgBE;AACE,UAAA,IAAA;AACA,aAAA,KAAA,KAAA,OAAA,GAAA;AACA,kBAAA;;AAIJ,WAZI;AAaF;AACE,eAAA,OAAA;;;AAIJ,WAXI;AAYF;AACE,sBAAA,CAAA,EAAA;AACA,uBAAA;;AAEF;AACE,sBAAA,GAAA,EAAA;AACA,uBAAA;;AAEF;AACE,sBAAA,CAAA,EAAA;AACA,uBAAA;;;;;ACzCJ,CAAA,WAAA,CAAA;AACE,iBAAA,IAAA;AACA,WAAA;AACA,eAAA;AACA,YAAA;AACA,UAAA;AACA,+BAAA;AACA,eAAA;;AAEA,CATF,WASE,CAAA;AACE,WAAA;;ACFA,CDRJ,WCQI,CDRJ,QCQI,CAAA,SAAA,CAAA,mBAAA,SAAA,EAAA,CAAA;AAAA,CDRJ,WCQI,CDRJ,QCQI,CAAA,SAAA,CAAA,sBAAA,SAAA,EAAA,CAAA;AACE,qBAAA,IAAA;;AAEA,CDXN,WCWM,CDXN,QCWM,CAHF,SAGE,CAHF,mBAGE,SAAA,EAAA,CAHF,eAGE;AAAA,CDXN,WCWM,CDXN,QCWM,CAHF,SAGE,CAHF,sBAGE,SAAA,EAAA,CAHF,eAGE;AACE,QAAA,IAAA;;AAQN,CDpBF,WCoBE,CDpBF,SCoBE,CAZE,mBAYF,SAAA,EAAA,CAZE;AAYF,CDpBF,WCoBE,CDpBF,SCoBE,CAZE,sBAYF,SAAA,EAAA,CAZE;AAaA,cAAA,IAAA;AACA,sBAAA,IAAA;;AAEA,CDxBJ,WCwBI,CDxBJ,SCwBI,CAhBA,mBAgBA,SAAA,EAAA,CAhBA,eAgBA;AAAA,CDxBJ,WCwBI,CDxBJ,SCwBI,CAhBA,sBAgBA,SAAA,EAAA,CAhBA,eAgBA;AACE,SAAA,IAAA;;AAKN,CD9BA,WC8BA,CD9BA,SC8BA,CAAA;AACE,UAAA;AACA,SAAA;;AAIA,CDpCF,WCoCE,CDpCF,SCoCE,CA5BE,cA4BF;AACE,cAAA,IAAA;;AAGF,CDxCF,WCwCE,CDxCF,SCwCE,CAhCE,cAgCF;AACE,cAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;;AAEA,CD5CJ,WC4CI,CD5CJ,SC4CI,CApCA,cAoCA,QAAA;AACE,SAAA,IAAA;;AAIJ,CDjDF,WCiDE,CDjDF,SCiDE,CAzCE,cAyCF,CAAA;AACE,cAAA;AACA,UAAA;;AAEA,CDrDJ,WCqDI,CDrDJ,SCqDI,CA7CA,cA6CA,CAAA,oBAAA;AACE,SAAA,IAAA;;ADtCN,CAhBA,WAgBA,CAAA;AACE,oBAAA;;AACA,CAlBF,WAkBE,CAFF,gBAEE,CCVE;ADWA,SAAA;AACA,UAAA;;AAIJ,CAxBA,WAwBA,CChBI;ADgBJ,CAxBA,WAwBA,CChBI;ADkBF,YAAA;AACA,WAAA;AACA,kBAAA;;AAGF,CA/BA,WA+BA,CCvBI;ADwBF,cAAA;AACA,SAAA,IAAA;AACA,UAAA,IAAA;AACA,SAAA,IAAA;AAEA,WAAA;AACA,mBAAA;AACA,eAAA;AAEA,iBAAA,IAAA;;AAEA,CA3CF,WA2CE,CCnCE,eDmCF,EAAA,CAAA;AACE,uBAAA;;AAGF,CA/CF,WA+CE,CCvCE,eDuCF;AACE,YAAA;AACA,SAAA,IAAA;AACA,UAAA,IAAA;AACA,SAAA,IAAA;;AAIJ,CAvDA,WAuDA,CAZE;AAaA,WAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,eAAA,IAAA;AACA,UAAA,EAAA;AACA,iBAAA;;AAEA,CA/DF,WA+DE,CApBA,gBAoBA,CAAA;AACE,eAAA;;AAIJ,CApEA,WAoEA,CAAA,oBAAA,CC5DI;AD6DF,SAAA;AACA,UAAA;AACA,aAAA;;AAGF,CA1EA,WA0EA,CAAA;AAAA,CA1EA,WA0EA,CAAA,MAAA,CAAA;AAEE,WAAA;AACA,UAAA;AACA,UAAA;AACA,aAAA;AACA,oBAAA;;AAEA,CAlFF,WAkFE,CARF,oBAQE;AAAA,CAlFF,WAkFE,CARF,MAQE,CARF,oBAQE;AACE,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;;AAGF,CAtFF,WAsFE,CAZF,oBAYE,CAAA;AAAA,CAtFF,WAsFE,CAZF,MAYE,CAZF,oBAYE,CAAA;AACE,oBAAA,IAAA;;AAEA,CAzFJ,WAyFI,CAfJ,oBAeI,CAHF,kBAGE;AAAA,CAzFJ,WAyFI,CAfJ,MAeI,CAfJ,oBAeI,CAHF,kBAGE;AACE,oBAAA,IAAA;;AAIJ,CA9FF,WA8FE,CApBF,oBAoBE;AAAA,CA9FF,WA8FE,CApBF,MAoBE,CApBF,oBAoBE;AACE,oBAAA,IAAA;;AAGF,CAlGF,WAkGE,CAxBF,oBAwBE;AAAA,CAlGF,WAkGE,CAxBF,MAwBE,CAxBF,oBAwBE;AACE,UAAA;;AAEA,CArGJ,WAqGI,CA3BJ,oBA2BI,SAAA;AAAA,CArGJ,WAqGI,CA3BJ,oBA2BI,SAAA;AAAA,CArGJ,WAqGI,CA3BJ,oBA2BI,SAAA;AAAA,CArGJ,WAqGI,CA3BJ,MA2BI,CA3BJ,oBA2BI,SAAA;AAAA,CArGJ,WAqGI,CA3BJ,MA2BI,CA3BJ,oBA2BI,SAAA;AAAA,CArGJ,WAqGI,CA3BJ,MA2BI,CA3BJ,oBA2BI,SAAA;AAGE,oBAAA;AACA,UAAA;AACA,cAAA;;AAGF,CA7GJ,WA6GI,CAnCJ,oBAmCI,UAAA;AAAA,CA7GJ,WA6GI,CAnCJ,MAmCI,CAnCJ,oBAmCI,UAAA;AACE,SAAA,IAAA;;AAIJ,CAlHF,WAkHE,CAAA;AAAA,CAlHF,WAkHE,CAxCF,MAwCE,CAAA;AACE,cAAA;;AAGF,CAtHF,WAsHE,CAAA;AAAA,CAtHF,WAsHE,CA5CF,MA4CE,CAAA;AACE,WAAA;;AAIJ,CA3HA,WA2HA,CAAA;AACE,oBAAA;;AAEA,CA9HF,WA8HE,CAHF,sBAGE;AACE,oBAAA;;AAGF,CAlIF,WAkIE,CAPF,sBAOE;AACE,oBAAA;;AAGF,CAtIF,WAsIE,CAXF,uBAWE,CC9HE;AD+HA,oBAAA,IAAA;AAQA,SAAA;AACA,UAAA;;AARA,CAxIJ,WAwII,CAbJ,uBAaI,CChIA,cDgIA;AACE,oBAAA,IAAA;;AAEF,CA3IJ,WA2II,CAhBJ,uBAgBI,CCnIA,cDmIA;AACE,oBAAA,IAAA;;AAQN,CApJA,WAoJA,CCtHA;ADuHE,YAAA;AACA,UAAA;AACA,SAAA;AACA,aAAA;AACA,SAAA,IAAA;AACA,eAAA,IAAA;AACA,eAAA;;AAIA,KAAA,CAAA,SAAA,CA/JF,WA+JE,CAAA;AACE,QAAA;;AAGF,KAAA,CAAA,SAAA,CAnKF,WAmKE,CAJA;AAKE,SAAA;;AAKF,CAzKF,WAyKE,CAAA,sBAAA,CCjKE;ADkKA,SAAA,IAAA;AACA,UAAA,IAAA;;AAEA,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AAJF,GAzKF,WAyKE,CAAA,sBAAA,CCjKE;ADsKE,WAAA;AACA,YAAA;;;AAEF,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AARF,GAzKF,WAyKE,CAAA,sBAAA,CCjKE;AD0KE,WAAA;AACA,YAAA;;;AAGF,CAtLJ,WAsLI,CAbF,sBAaE,CC9KA,eD8KA;AACE,SAAA,IAAA;AACA,UAAA,IAAA;;AAIJ,CA5LF,WA4LE,CAnBA,sBAmBA,CAAA,uBAAA,CCpLE;ADoLF,CA5LF,WA4LE,CAnBA,sBAmBA,CAAA,sBAAA,CCpLE;ADsLA,SAAA,IAAA;AACA,UAAA,IAAA;;;;AEhMN,CAAA;AACE,kBAAA;AACA,iCAAA;AACA,8BAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,oCAAA;AACA,yBAAA,IAAA;AACA,mBAAA;AACA,2BAAA;AACA,qBAAA,IAAA;AACA,2BAAA;AACA,sBAAA;AACA,oBAAA;AACA,wBAAA;AACA,0BAAA;AACA,uBAAA;AACA,qBAAA;AACA,sBAAA,IAAA;AACA,gBAAA;AACA,sBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,oBAAA,IAAA;AACA,8BAAA;AACA,sBAAA;AACA,+BAAA;AACA,4BAAA;AACA;IAAA,IAAA,IAAA,qBAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,qBAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,KAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAIA,qBAAA,IAAA;AACA,sBAAA,IAAA;AACA,0BAAA,IAAA;AACA,0BAAA,IAAA;AAEA,yBAAA;AACA,uBAAA;AACA,oBAAA;AACA,kBAAA;AACA,8BAAA;AASA,qBAAA,IAAA;AACA,2BAAA,IAAA;AAEA,wBAAA,IAAA,GAAA,EAAA,IAAA,EAAA;AACA,wBAAA,IAAA;AAEA;IAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,MAAA;IAAA,IAAA,SAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAMA,yBAAA,IAAA;AACA;IAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,MAAA;IAAA,IAAA,SAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAMA,0BAAA,IAAA;AACA,sBAAA,IAAA;AACA;IAAA,IAAA,KAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,IAAA;IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAIA,kBAAA;AACA,wBAAA,IAAA;AAEA,qBAAA;AAEA,sBAAA;AAEA,mBAAA;AACA,0BAAA;AACA,2BAAA;AACA,yBAAA;AACA,gCAAA;AACA,yBAAA;AAEA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,oBAAA;AAEA,oBAAA,IAAA;AAEA,mBAAA;AACA,wBAAA;AACA,0BAAA;AACA,2BAAA;AACA,wBAAA,IAAA;AAEA,kBAAA;AACA,uBAAA;AACA,yBAAA;AACA,0BAAA;AACA,uBAAA;AAEA,6BAAA;AACA,kCAAA;AACA,wBAAA;AACA,6BAAA;AAEA,8BAAA,IAAA;AACA,mCAAA,IAAA;AACA,yBAAA,IAAA;AACA,8BAAA,IAAA;AAEA,iBAAA,IAAA;AACA,wBAAA,IAAA;AACA,yBAAA,IAAA;AACA,4BAAA,IAAA;AACA,mCAAA,IAAA;AAEA,iBAAA,IAAA;AAEA,mBAAA;AACA,0BAAA;AACA,2BAAA;AACA,wBAAA;AACA,4BAAA;AACA,kCAAA;AACA,mCAAA;AAEA,qBAAA,IAAA;AACA,qBAAA;AAEA,sBAAA;AACA,sBAAA;AAEA,wBAAA;AACA,uBAAA;AACA,uBAAA;AACA,0BAAA;AACA,sBAAA;AACA,uBAAA;AACA,gCAAA;AACA,qCAAA;AACA,wBAAA;AACA,0BAAA;AACA,kCAAA;AACA,qCAAA;AAEA,iBAAA;AACA,4BAAA;;AAtHA,OAAA,OAAA,IAAA,CAAA,gBAAA,EAAA;AA1CF,GAAA;AA2CI,sBAAA;AACA,oBAAA;AACA,2BAAA;AACA,yBAAA;;;AAqHA,CAnKJ,UAmKI,CAAA,WAAA,CAAA;AACE,cAAA;;AAIJ,CAxKF,UAwKE,CALE;AAMA,kBAAA,OAAA,KAAA,WAAA;AACA,iCAAA;AACA,8BAAA;AAEA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,oCAAA;AACA,yBAAA,IAAA;AACA,mBAAA;AACA,2BAAA;AACA,2BAAA;AACA,sBAAA;AACA,oBAAA;AACA,wBAAA;AACA,0BAAA;AACA,uBAAA;AACA,qBAAA;AACA,sBAAA,IAAA;AACA,gBAAA;AACA,sBAAA,KAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA;AACA,8BAAA;AACA,sBAAA;AACA,+BAAA;AACA,4BAAA;AACA;IAAA,IAAA,IAAA,qBAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,qBAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,KAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAIA;IAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,MAAA;IAAA,IAAA,SAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAMA,yBAAA,IAAA;AAEA,qBAAA;AACA,2BAAA;AAEA,wBAAA,IAAA,GAAA,EAAA,GAAA,EAAA;AAGA,qBAAA;AAEA,sBAAA,IAAA;AAEA,mBAAA;AACA,0BAAA;AACA,2BAAA;AACA,yBAAA;AACA,gCAAA;AACA,yBAAA;AAEA,oBAAA,IAAA;AAEA,wBAAA,IAAA;AAEA,kBAAA;AACA,uBAAA;AACA,yBAAA;AACA,0BAAA;AACA,uBAAA;AAEA,6BAAA;AACA,kCAAA;AACA,wBAAA;AACA,6BAAA;AAEA,8BAAA,IAAA;AACA,mCAAA,IAAA;AACA,yBAAA,IAAA;AACA,8BAAA,IAAA;AAEA,iBAAA,IAAA;AACA,wBAAA,IAAA;AACA,yBAAA,IAAA;AACA,4BAAA,IAAA;AACA,mCAAA,IAAA;AAEA,qBAAA;AAEA,wBAAA,IAAA,GAAA,EAAA,GAAA,EAAA;AACA,uBAAA,IAAA,GAAA,EAAA,EAAA,EAAA;AACA,uBAAA,IAAA,IAAA,GAAA;AACA,0BAAA,IAAA,CAAA,EAAA,EAAA,EAAA;AACA,sBAAA;AACA,uBAAA;AACA,gCAAA;AACA,qCAAA;AACA,wBAAA;AACA,0BAAA;AACA,kCAAA;AACA,qCAAA;;ACtQF,CDAF,WCAE,MAAA,CAAA;AC8DA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;AACA,cAAA;AACA,gBAAA;AACA,gBAAA;AACA,gBAAA,IAAA,eAAA,EAAA,IAAA;AACA,iBAAA,IAAA;AACA,UAAA;AACA,oBAAA,IAAA,WAAA,EAAA,IAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,eAAA,IAAA;AAiDA,WAAA;;AA/CA,CF9EF,WE8EE,MAAA,CD9EA,WC8EA;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;;AAGF,CFnFF,WEmFE,MAAA,CDnFA,UCmFA;AACE,oBAAA,IAAA,iBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,qBAAA,EAAA,IAAA,eAAA,EAAA,IAAA;AAIA,SAAA,IAAA,oBAAA,EAAA,IAAA,cAAA,EAAA,IAAA,oBAAA,EAAA;;AAMF,CF/FF,WE+FE,MAAA,CD/FA,UC+FA;AACE,oBAAA,IAAA,kBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,sBAAA,EAAA,IAAA;;AAGF,CFpGF,WEoGE,MAAA,CDpGA,UCoGA,CAAA;AACE,oBAAA,IAAA,oBAAA,EAAA,IAAA;AAIA,gBAAA,IAAA,wBAAA,EAAA,IAAA;;AAKA,CF9GJ,WE8GI,MAAA,CD9GF,UC8GE,CAVF,MAUE;AACE,oBAAA,IAAA,0BAAA,EAAA,IAAA;;AAMF,CFrHJ,WEqHI,MAAA,CDrHF,UCqHE,CAjBF,OAiBE;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;;AASJ,CF/HF,WE+HE,MAAA,CD/HA,WC+HA;AACE,SAAA,IAAA;AACA,UAAA,IAAA;;AD9HA,CDHJ,WCGI,MAAA,CAHF,WAGE,EAAA;AAEE,kBAAA;;;;AEPJ,CAAA,WAAA,CAAA;AAAA,CAAA,WAAA,CAAA;AAEE,YAAA;AACA,SAAA;AACA,UAAA;AACA,WAAA,IAAA;AACA,gBAAA;;AAGF,CATA,WASA,CATA;AAUE,kBAAA;;AAGF,CAbA,WAaA,CAbA;AAcE,kBAAA;;AAGF,CAjBA,WAiBA,CAAA;AACE,kBAAA;AACA,SAAA;AACA,UAAA;AACA,YAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,UAAA,IAAA;;AAGF,CA5BA,WA4BA,CAAA;AACE,SAAA;AACA,UAAA;AACA,UAAA;AACA,WAAA;AACA,gBAAA;AACA,eAAA;AACA,iBAAA;AACA,SAAA,IAAA;;AAEA,CAtCF,WAsCE,CAVF,8BAUE;AACE,cAAA,IAAA;;AAEF,CAzCF,WAyCE,CAbF,8BAaE,CAAA;AACE,SAAA,IAAA;AACA,cAAA,IAAA;;;;AC3CJ,CAAA,WAAA,CAAA;AACE,aAAA;AACA,cAAA;AACA,oBAAA,IAAA;AACA,cAAA,IAAA;AACA,iBAAA,IAAA;AACA,WAAA,KAAA,IAAA,WAAA,EAAA,IAAA;AACA,YAAA;AACA,cAAA,WAAA,KAAA;;AAEA,CAVF,WAUE,CAVF,MAUE,CAAA;AACE,cAAA;;;;ACRF,CAAA,WAAA,CAAA,kBAAA;AACE,WAAA;;AAIJ,CALE,WAKF,CAAA;AACE,WAAA,EAAA;AACA,aAAA;AACA,cAAA;;AAGF,CAXE,WAWF,CAAA;AACE,WAAA;AACA,yBAAA,IAAA,KAAA;AACA,WAAA,QAAA;AACA,eAAA;;AChBO,CAAA,kBAAA,CDCP,WCDO,CDYT;AAOI,aAAA;;AAIJ,CAtBE,WAsBF,CAAA;AACE,WAAA;AACA,mBAAA;;AAGF,CA3BE,WA2BF,CAAA;AACE,YAAA;AAEA,WAAA;AACA,UAAA;AACA,SAAA;AACA,UAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,iBAAA,IAAA;AACA,UAAA,IAAA;AACA,oBAAA,IAAA;AACA,uBAAA,KAAA;AACA,YAAA;AACA,eAAA;AACA,cAAA;;AAGE,CA5CF,WA4CE,CAjBJ,oBAiBI,MAAA;AACE,WAAA;AACA,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;AACA,iBAAA,KAAA,IAAA,UAAA,EAAA;AACA,UAAA,IAAA;;AAKF,CA1DF,WA0DE,CA/BJ,oBA+BI,CAAA,OAAA,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;AACA,WAAA;AACA,iBAAA,KAAA,IAAA,UAAA,EAAA;AACA,UAAA,IAAA;;AAIJ,CAvEA,WAuEA,CA5CF,oBA4CE;AACE,WAAA;;AAEA,CA1EF,WA0EE,CA/CJ,oBA+CI,cAAA;AACE,WAAA;AACA,YAAA;AACA,OAAA;AACA,SAAA;AACA,UAAA;AACA,QAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,iBAAA,KAAA,IAAA,UAAA,EAAA;;AAIA,CAtFJ,WAsFI,CA3DN,oBA2DM,cAAA,CA5BF,OA4BE,CA5BF;AA6BI,WAAA;;AAKN,CA5FA,WA4FA,CAAA;AACE,YAAA;AACA,SAAA;AACA,UAAA;;AAGF,CAlGA,WAkGA,CAvEF,oBAuEE,CAAA;AACE,oBAAA;;AAGF,CAtGA,WAsGA,CAAA;AACE,UAAA;;AACA,CAxGF,WAwGE,CAFF,sCAEE;AACE,WAAA;;AAEF,CA3GF,WA2GE,CALF,sCAKE;AACE,WAAA;;AAIJ,CAhHA,WAgHA,CArFF,oBAqFE,CAAA;AACE,iBAAA,KAAA,IAAA,UAAA,EAAA;AACA,SAAA;AACA,UAAA;;AAIJ,CAvHE,WAuHF,CAAA;AACE,YAAA;AACA,SAAA;AACA,UAAA;AACA,UAAA;AACA,aAAA;;AAGF,CA/HE,WA+HF,CAAA;AACE,cAAA,IAAA;AACA,UAAA,EAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,MAAA,EAAA,IAAA;AACA,iBAAA;AACA,YAAA;;AAEA,KAAA,CAAA,SAAA,CAtIA,WAsIA,CAPF;AAQI,QAAA;;AAGF,KAAA,CAAA,SAAA,CA1IA,WA0IA,CAXF;AAYI,SAAA;;AAIJ,CA/IE,WA+IF,CAAA;AACE,WAAA;AACA,yBAAA,KAAA;AACA,eAAA;AACA,cAAA;;AAGF,CAtJE,WAsJF,CAPA,+BAOA,EAAA,CAAA;AACE,YAAA;;AAGF,CA1JE,WA0JF,CAAA;AACE,cAAA;;AAEA,KAAA,CAAA,SAAA,CA7JA,WA6JA,CAHF;AAII,eAAA;;AAGF,KAAA,CAAA,SAAA,CAjKA,WAiKA,CAPF;AAQI,eAAA;;AAIJ,CAtKE,WAsKF,CAAA;AACE,SAAA;AACA,UAAA;AACA,gBAAA;AACA,gBAAA,EAAA,IAAA;AACA,gBAAA,YAAA,YAAA,IAAA;AACA,YAAA;AACA,OAAA;;AAEA,KAAA,CAAA,SAAA,CA/KA,WA+KA,CATF;AAUI,aAAA,OAAA;AACA,QAAA;;AAGF,KAAA,CAAA,SAAA,CApLA,WAoLA,CAdF;AAeI,aAAA,OAAA;AACA,SAAA;;AAIJ,CA1LE,WA0LF,CAAA;AACE,gBAAA,YAAA,YAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,KAAA,CAAA,SAAA,CA7LA,WA6LA,CAHF;AAII,QAAA;;AAGF,KAAA,CAAA,SAAA,CAjMA,WAiMA,CAPF;AAQI,SAAA;;AAIJ,CAtME,WAsMF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,WAAA;;AAGF,CA7ME,WA6MF,CAAA;AACE,WAAA;AACA,WAAA;AACA,yBAAA,OAAA,CAAA,EAAA;AACA,YAAA;AACA,iBAAA;;AAEA,CApNA,WAoNA,CAPF,6BAOE;AACE,WAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;;AAIJ,CA1NE,WA0NF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,WAAA;;AAEA,CA/NA,WA+NA,CAAA;AACE,SE/LM;AFgMN,aAAA;AACA,WAAA,EAAA;;AAGF,CArOA,WAqOA,CAAA;AACE,WAAA,OAAA;;AAEA,CAxOF,WAwOE,CAHF,oCAGE,CAAA;AACE,UAAA,EAAA;;AAKN,CA9OE,WA8OF,CAxCA,qBAwCA,CAAA;AACE,eAAA,CAAA,CAAA,KAAA;;AAGF,CAlPE,WAkPF,CAVI;AAWF,YAAA;AACA,UAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,UAAA;AACA,cAAA;AACA,UAAA,IAAA,MAAA;AACA,oBAAA;AACA,UAAA,IAAA;;AAEA,CA9PA,WA8PA,CAtBE,mBAsBF;AAEE,cAAA,EAAA,EAAA,IAAA,IAAA;AACA,gBAAA,IAAA;;AAIJ,CArQE,WAqQF,CAAA;AACE,iBAAA;AACA,cAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,EAAA,IAAA;AACA,YAAA;AACA,OAAA;AACA,SAAA;AACA,UAAA;AACA,QAAA;;AAGF,CA/QE,WA+QF,CAVA;AAUA,CA/QE,WA+QF,CAAA;AAEE,cAAA,4KAAA,KAAA;;AAIF,CArRE,WAqRF,CAAA;AACE,UAAA,IAAA;AACA,eAAA;AACA,WAAA,OAAA,OAAA,OAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,gBAAA;AACA,cAAA;AAYA,SAAA,IAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,YAAA;;AAdA,KAAA,CAAA,SAAA,CA7RA,WA6RA,CARF;AASI,iBAAA,IAAA,oBAAA,EAAA,EAAA,IAAA;;AAGF,KAAA,CAAA,SAAA,CAjSA,WAiSA,CAZF;AAaI,iBAAA,EAAA,IAAA,oBAAA,IAAA,oBAAA;AACA,gBAAA,IAAA,MAAA,IAAA;AACA,eAAA;;AAUJ,CA9SE,WA8SF,CAhEA;AAiEE,WAAA;;AAEA,CAjTA,WAiTA,CAnEF,qBAmEE;AACE,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;AACA,iBAAA,IAAA;;AAIJ,CAvTE,WAuTF,CAAA;AACE,WAAA;AACA,yBAAA,KAAA,IAAA,KAAA;AACA,OAAA;AACA,eAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,iBAAA;AACA,WAAA,EAAA;AACA,UAAA;AACA,cAAA;;AAEA,CAlUA,WAkUA,CAXF,yBAWE;AACE,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;AACA,iBAAA,IAAA;;AAIJ,CAxUE,WAwUF,CAAA;AACE,WAAA,EAAA;;AAGF,CA5UE,WA4UF,CAAA;AACE,cAAA;AACA,SAAA;AACA,UAAA;AACA,aAAA;AACA,eAAA;AACA,oBAAA;AACA,SAAA,IAAA;AACA,UAAA;AACA,WAAA;AACA,UAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,eAAA;AACA,kBAAA;AAYA,WAAA;AACA,gBAAA;AACA,cAAA;;AAZA,KAAA,CAAA,SAAA,CA3VA,WA2VA,CAfF;AAgBI,iBAAA,EAAA,IAAA,oBAAA,IAAA,oBAAA;;AAGF,KAAA,CAAA,SAAA,CA/VA,WA+VA,CAnBF;AAoBI,iBAAA,IAAA,oBAAA,EAAA,EAAA,IAAA;AACA,eAAA,IAAA,MAAA,IAAA;AACA,gBAAA;;AAOF,CAzWA,WAyWA,CA7BF,kBA6BE;AACE,cAAA;;AAIJ,CA9WE,WA8WF,CAAA;AACE,UAAA,IAAA,MAAA,IAAA;AACA,iBAAA,IAAA;AACA,SAAA,IAAA;AACA,UAAA,IAAA;AACA,cAAA;AACA,YAAA;;AAGF,CAvXE,WAuXF,CAxGA;AClNA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;AACA,cAAA;AACA,gBAAA;AACA,gBAAA;AACA,gBAAA,IAAA,eAAA,EAAA,IAAA;AACA,iBAAA,IAAA;AACA,UAAA;AACA,oBAAA,IAAA,WAAA,EAAA,IAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,eAAA,IAAA;AD8SE,oBAAA,IAAA;AACA,YAAA;AACA,YAAA;AACA,UAAA,IAAA;AACA,UAAA;;AChTF,CD7EE,WC6EF,CDkMA,0BClMA;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;;AAGF,CDlFE,WCkFF,CD6LA,yBC7LA;AACE,oBAAA,IAAA,iBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,qBAAA,EAAA,IAAA,eAAA,EAAA,IAAA;AAIA,SAAA,IAAA,oBAAA,EAAA,IAAA,cAAA,EAAA,IAAA,oBAAA,EAAA;;AAMF,CD9FE,WC8FF,CDiLA,yBCjLA;AACE,oBAAA,IAAA,kBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,sBAAA,EAAA,IAAA;;AAGF,CDnGE,WCmGF,CD4KA,yBC5KA,CDzCI;AC0CF,oBAAA,IAAA,oBAAA,EAAA,IAAA;AAIA,gBAAA,IAAA,wBAAA,EAAA,IAAA;;AAKA,CD7GA,WC6GA,CDkKF,yBClKE,CDnDE,MCmDF;AACE,oBAAA,IAAA,0BAAA,EAAA,IAAA;;AAMF,CDpHA,WCoHA,CD2JF,yBC3JE,CD1DE,OC0DF;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;;AD0QF,CA/XA,WA+XA,CAhHF,yBAgHE;AACE,WAAA;AACA,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA,IAAA;;AAIJ,CA1YE,WA0YF,CAAA;AACE,YAAA;AACA,UAAA;AACA,aAAA;;AAEA,KAAA,CAAA,SAAA,CA/YA,WA+YA,CALF;AAMI,SAAA;;AAGF,KAAA,CAAA,SAAA,CAnZA,WAmZA,CATF;AAUI,QAAA;;ACrZK,CAAA,kBAAA,CDCP,WCDO,CD2YT;AAcI,WAAA;;AAIJ,CA5ZE,WA4ZF,CAAA,mCAAA,CAlBA;AAmBE,SAAA;;AAGF,CAhaE,WAgaF,CAAA,oCAAA,CAtBA;AAuBE,SE1Ze;;AF6ZjB,CApaE,WAoaF,CA5LI,mBA4LJ,CAAA,wBAAA,CA1BA;AA2BE,SAAA;;AAGF,CAxaE,WAwaF,CAAA,gCAAA,CA9BA;AA+BE,SAAA;;AAIA,CA7aA,UA6aA,CAAA,YAAA,CAbF,oCAaE,CAnCF;AAoCI,SEtaa;;AFwaf,CAhbA,UAgbA,CAHA,YAGA,CAxME,mBAwMF,CAAA,wBAAA,CAtCF;AAuCI,SEzaa;;;;ACTjB,CAAA,WAAA,CAAA;AACE,WAAA;AACA,cAAA,IAAA;AACA,UAAA,EAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA,IAAA;AACA,iBAAA;AACA,YAAA;;AACA,KAAA,CAAA,SAAA,CAPF,WAOE,CAPF;AAQI,WAAA;;AAIJ,CAZA,WAYA,CAAA,iBAAA;AAAA,CAZA,WAYA,CAZA,OAYA;AAEE,YAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;;AAEA,CAnBF,WAmBE,CAPF,iBAOE,MAAA;AAAA,CAnBF,WAmBE,CAnBF,OAmBE,MAAA;AACE,WAAA;AACA,oBAAA,IAAA;;AACA,CAtBJ,WAsBI,CAVJ,iBAUI,MAAA,eAAA;AAAA,CAtBJ,WAsBI,CAtBJ,OAsBI,MAAA,eAAA;AACE,WAAA;;AAIJ,CA3BF,WA2BE,CAfF,iBAeE,MAAA;AAAA,CA3BF,WA2BE,CA3BF,OA2BE,MAAA;AACE,oBAAA,IAAA;;AAGF,CA/BF,WA+BE,CAnBF,iBAmBE,MAAA;AAAA,CA/BF,WA+BE,CA/BF,OA+BE,MAAA;AACE,oBAAA,IAAA;;AAGF,CAnCF,WAmCE,CAvBF,iBAuBE,MAAA;AAAA,CAnCF,WAmCE,CAnCF,OAmCE,MAAA;AACE,UAAA;;AAGF,CAvCF,WAuCE,CA3BF,iBA2BE,OAAA;AAAA,CAvCF,WAuCE,CAvCF,OAuCE,OAAA;AACE,UAAA;AACA,SAAA;AACA,UAAA;AACA,kBAAA;;AAIJ,CA/CA,WA+CA,CA/CA,OA+CA;AACE,WAAA,QAAA,QAAA,QAAA;;AAGF,CAnDA,WAmDA,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,CAAA,EAAA;AACA,YAAA;AACA,iBAAA;;AAGF,CA1DA,WA0DA,CAAA;AACE,aAAA;AACA,WAAA,OAAA;;AAGF,CA/DA,WA+DA,CAAA;AACE,YAAA;AACA,UAAA;AACA,aAAA;AACA,SAAA,IAAA;;AAEA,KAAA,CAAA,SAAA,CArEF,WAqEE,CANF;AAOI,SAAA;;AAGF,KAAA,CAAA,SAAA,CAzEF,WAyEE,CAVF;AAWI,QAAA;;AC1EK,CAAA,kBAAA,CDAT,WCAS,CD+DT;AAcI,WAAA;;AAIJ,CAjFA,WAiFA,CAAA,6BAAA,CAlBA;AAmBE,SAAA;;AAGF,CArFA,WAqFA,CAAA,8BAAA,CAtBA;AAuBE,SE9Ee;;AFiFjB,CAzFA,WAyFA,CAAA,aAAA,CAAA,wBAAA,CA1BA;AA2BE,SAAA;;AAGF,CA7FA,WA6FA,CAAA,0BAAA,CA9BA;AA+BE,SAAA;;AAIA,CAlGF,UAkGE,CAAA,YAAA,CAbF,8BAaE,CAnCF;AAoCI,SE1Fa;;AF4Ff,CArGF,UAqGE,CAHA,YAGA,CAZF,aAYE,CAAA,wBAAA,CAtCF;AAuCI,SE7Fa;;;;ACZnB,CAAA;AACE,uBAAA,IAAA;;AAEA,CAHF,WAGE,CAAA;AACE,YAAA;AACA,UAAA;AACA,iBAAA,IAAA,MAAA,IAAA;;AAEA,CARJ,WAQI,CALF,qBAKE;AACE,YAAA;AACA,OAAA;AACA,aAAA,WAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA,IAAA;AACA,WAAA;;AAIJ,CApBF,UAoBE,CAAA;AACE,uBAAA,IAAA;;AAEA,CAvBJ,UAuBI,CAHF,YAGE,CApBF;AAqBI,iBAAA;;AAIJ,CA5BF,WA4BE,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,cAAA;AACA,UAAA;AACA,aAAA;AACA,gBAAA;AACA,iBAAA;;AAEA,CAvCJ,WAuCI,CAXF,kBAWE;AACE,SAAA,IAAA;;AAGF,CA3CJ,WA2CI,CAfF,kBAeE;AACE,cAAA;;;;AC3CJ,CAAA,WAAA,CAAA;AACE,YAAA;AACA,UAAA;AACA,aAAA;AACA,cAAA;;AAEA,CANF,WAME,CANF,wBAME,EAAA,CAAA;AAAA,CANF,WAME,CANF,wBAME,EAAA,CAAA;AAEE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,aAAA;AACA,SAAA,IAAA;AACA,YAAA;AACA,cAAA;AACA,eAAA;;;;ACdJ,CAAA,WAAA,CAAA;AACE,WAAA;AACA,yBAAA,KAAA,KAAA,EAAA,EAAA,EAAA,IAAA,wBAAA,KAAA;AACA,eAAA;;ACHO,CAAA,kBAAA,CDAT,WCAS,CDAT;AAMI,aAAA,KAAA,KAAA,EAAA,EAAA,EAAA,IAAA;;;;AEPN,CAAA;AACE,uBAAA;;AAEA,CAHF,WAGE,CAAA;AACE,YAAA;AACA,eAAA;AACA,kBAAA;;AAGF,CATF,WASE,CAAA;AACE,SAAA;AACA,UAAA;AACA,sBAAA;AACA,cAAA,IAAA;AACA,iBAAA;AACA,WAAA;;AAGF,CAlBF,WAkBE,CATA,WASA;AACE,sBAAA;AACA,cAAA;AACA,SAAA,IAAA;AACA,UAAA,IAAA;AACA,cAAA,IAAA;AACA,iBAAA;AACA,UAAA;AACA,UAAA;;AAGF,CA7BF,WA6BE,CApBA,WAoBA;AACE,SAAA,IAAA;AACA,UAAA,IAAA;AACA,cAAA,IAAA;AACA,iBAAA;AACA,UAAA;AACA,UAAA;;AAGF,CAtCF,WAsCE,CAAA;AACE,YAAA;AACA,UAAA;AACA,aAAA,WAAA;AACA,aAAA;AACA,SAAA,IAAA;;AAGF,CA9CF,WA8CE,CAAA;AACE,YAAA;AACA,UAAA;AACA,QAAA;AACA,aAAA;AACA,SAAA,IAAA;;;;AClDJ,CAAA;AACE;IAAA,SAAA;IAAA,SAAA;IAAA,kBAAA;IAAA,aAAA;IAAA,MAAA,EAAA;IAAA,MAAA;IAAA,SAAA;IAAA,KAAA;IAAA;AAEA,eAAA,IAAA;AACA,YAAA;AACA,WAAA,IAAA;AAEA,WAAA;AACA,iBAAA;AACA,cAAA;AACA,kBAAA;AACA,aAAA;AAEA,cCJiB;ADMjB,eAAA;AACA,cAAA;AACA,aAAA;AACA,eAAA;AACA,SCXiB;ADajB,WAAA;;AAEA,CAvBF,kBAuBE,CAAA;AACE,WAAA;;AAKJ,CAAA;AACE,WAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,eAAA;AACA,cAAA;AACA,WAAA;;AEtCS,CAAA,kBAAA,CFiCX;AAQI,WAAA;;;;AGzCF,CAAA,WAAA,CAAA;AACE,WAAA;;;;ACJJ,CAAA;AACE,UAAA;AACA,WAAA;AACA,eAAA;;AAEA,CALF,YAKE,CAAA;AACE,UAAA,KAAA,KAAA,EAAA;AACA,SAAA;AACA,YAAA;AACA,cAAA;AACA,eAAA;AACA,iBAAA;;AAEA,CAbJ,YAaI,CAAA;AACE,cAAA;AACA,UAAA;AAIA,SAAA;AACA,aAAA;AACA,gBAAA;;AALA,CAhBN,YAgBM,CAHF,mBAGE;AACE,cAAA;;;;ACdN,CAAA,WAAA,CAAA;AACE,UAAA,IAAA;AACA,WAAA;AACA,eAAA;AAEA,UAAA;AACA,eAAA;AAEA,+BAAA;;AAEA,CAVF,WAUE,CAVF,QAUE,MAAA,KAAA,CAAA,YAAA,CAAA,YAAA,KAAA;AACE,cAAA,EAAA,EAAA,EAAA,IAAA;;AAIA,CAfJ,WAeI,CAfJ,QAeI,MAAA,KAAA,CALF,YAKE,CALF,YAKE,KAAA,QAAA;AACE,WAAA;AACA,WAAA;;AAKF,CAtBJ,WAsBI,CAtBJ,QAsBI,QAAA,CAZF;AAaI,cAAA,EAAA,EAAA,IAAA,IAAA,MAAA;;AAKF,CA5BJ,WA4BI,CA5BJ,QA4BI,OAAA,CAlBF;AAmBI,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAKF,CAlCJ,WAkCI,CAlCJ,QAkCI,CAxBF,WAwBE,CAxBF;AAyBI,oBAAA;;AACA,CApCN,WAoCM,CApCN,QAoCM,CA1BJ,WA0BI,CA1BJ,aA0BI;AACE,WAAA;;AAGJ,CAxCJ,WAwCI,CAxCJ,QAwCI,CA9BF,UA8BE,OAAA,CA9BF;AA+BI,oBAAA;;AAIJ,CA7CF,WA6CE,CA7CF,SA6CE,CAnCA;AAoCE,SAAA;AACA,UAAA;AACA,WAAA;AACA,QAAA,EAAA,EAAA;AAEA,UAAA,EAAA;AAEA,WAAA;AACA,eAAA;AACA,mBAAA;AAEA,cAAA,EAAA,EAAA,EAAA,IAAA;AACA,oBAAA;AACA,iBAAA;AAEA,SAAA;AAEA,UAAA;;AAEA,CAjEJ,WAiEI,CAjEJ,SAiEI,CAvDF,YAuDE;AACE,cAAA,EAAA,EAAA,EAAA,IAAA;;AAGF,CArEJ,WAqEI,CArEJ,SAqEI,CA3DF,aA2DE;AACE,WAAA;AACA,SAAA;AACA,UAAA;AACA,gBAAA;;AAIJ,CA7EF,WA6EE,CA7EF,SA6EE,CAAA;AACE,WAAA;AACA,eAAA;;AAGF,CAlFF,WAkFE,CAlFF,SAkFE,CAAA;AACE,SAAA;AACA,UAAA;;;;ACpFJ,CAAA,WAAA,CAAA;ACsIA,SAAA,IAAA,aAAA,EAAA;AACA,UAAA,IAAA,aAAA,EAAA;AACA,YAAA;AACA,iBAAA;AACA,kBAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,UAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,QAAA,EAAA,EAAA;;AAEA,CDrJA,WCqJA,CDrJA,MCqJA;AACE,aAAA,MAAA;;AAGF,CDzJA,WCyJA,CAAA;AACE,SAAA;AACA,UAAA;AACA,iBAAA;;AAGF,CD/JA,WC+JA,CD/JA,MC+JA;AACE,WAAA;AACA,YAAA;AACA,OAAA;AACA,SAAA;AACA,UAAA;AACA,QAAA;AACA,iBAAA;;AAGF,CDzKA,WCyKA,CDzKA,MCyKA,CAAA,WAAA;AACE,gBAAA,IAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;;AAEF,CD7KA,WC6KA,CD7KA,MC6KA,CAAA;AACE,UAAA;;;;AC/KJ,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,YAAA;AACA,cAAA,IAAA,IAAA,IAAA,EAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA,IAAA;AACA,cAAA,IAAA;AACA,iBAAA,IAAA;AACA,cAAA;AAEA,cAAA;;AAEA,CAAA;AAAA,CAbF,8BAaE;AAEE,WAAA;;AAGF,CALA;AAKA,CAAA;AAEE,UAAA;AACA,WAAA,EAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA,IAAA;;AAGF,CAfA;AAgBE,SAAA;AACA,UAAA;AACA,oBAAA;AACA,SAAA,IAAA;AAEA,WAAA;AACA,UAAA;AACA,cAAA;;AAGF,CArBA;AAsBE,YAAA;AACA,iBAAA;AACA,eAAA;AACA,aAAA;;AAGF,CA9CF,8BA8CE;AACE,SC4JQ;AD3JR,oBAAA;AACA,eAAA;;AACA,CAlDJ,8BAkDI,MAAA,CAAA;AACE,SCeK;;ADXT,CALE,sCAKF,CAAA,eAAA;AACE,SCOO;;ADJT,CA3DF,8BA2DE,CAJA;AAKE,SAAA;AACA,UAAA;;AAGF,CAAA;AACE,QAAA,EAAA,EAAA;;;;AEhEF,CAAA,WAAA,CAAA;AACE,eAAA;AACA,UAAA;;AAGF,CALA,WAKA,CAAA;AACE,UAAA;AACA,cAAA;AACA,aAAA;AACA,iBAAA,IAAA,MAAA,IAAA;AACA,WAAA,EAAA,EAAA;AACA,iBAAA;;AAGF,CAdA,WAcA,CAAA;AACE,SAAA,IAAA;AACA,UAAA;AACA,YAAA;AACA,OAAA;AACA,SAAA;AACA,UAAA;AACA,oBAAA;AACA,eAAA;AACA,UAAA;;AAEA,CAzBF,WAyBE,CAXF,aAWE;AACE,SAAA,IAAA;;AAEF,CA5BF,WA4BE,CAdF,aAcE;AACE,SAAA,IAAA;;AAGF,CAhCF,WAgCE,CAlBF,cAkBE;AACE,SAAA;AACA,UAAA;;AAGF,CArCF,WAqCE,CAvBF,cAuBE,EAAA,CAAA;AACE,YAAA;AACA,UAAA,KAAA,KAAA,EAAA,IAAA;AACA,cAAA,IAAA;;AAKF,CA7CF,WA6CE,CAAA,mBAAA,CA/BF;AAgCI,OAAA;AACA,SAAA;;;;AC/CJ,CAAA,UAAA,CAAA;AACE,YAAA;AACA,WAAA,IAAA;;AAGF,CALA,WAKA,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,YAAA;AACA,WAAA,KAAA,IAAA,gBAAA,EAAA;AAEA,WAAA;AACA,kBAAA;;AAEA,CApBF,WAoBE,CAfF,MAeE,CAAA;AACE,WAAA;AACA,UAAA;AACA,cAAA;AACA,iBAAA;;AAIA,CA5BJ,WA4BI,CAvBJ,KAuBI,CAAA,oBAAA,CAAA;AACE,aAAA;;AAGF,CAhCJ,WAgCI,CA3BJ,KA2BI,CAJA,oBAIA,CAAA;AACE,aAAA;AACA,WAAA;;AAKN,CAvCA,WAuCA,CAXI;AAYF,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,WAAA;AACA,oBAAA,KAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA;AAEA,aAAA,2BAAA,KAAA,OAAA;;AAGF,CAnDA,WAmDA,CAnBI;AAoBF,YAAA;AACA,WAAA;AACA,SAAA;AACA,aAAA,IAAA;AACA,cAAA;AAEA,WAAA;AACA,aAAA,WAAA;AACA,aAAA,uBAAA,OAAA,SAAA,GAAA;AAEA,YAAA;AACA,cAAA;AAGA,cAAA,IAAA;AAEA,UAAA,IAAA,MAAA,IAAA;AACA,cAAA,IAAA;AACA,iBAAA;AACA,cAAA;;AAEA,CAzEF,WAyEE,CAzCE,cAyCF;AACE,WAAA;;AAIJ,WA9BE;AA+BA;AACE,aAAA;;AAEF;AACE,aAAA;;;AAIJ,WA3BE;AA4BA;AACE,aAAA;AACA,eAAA,MAAA;;AAEF;AACE,aAAA;AACA,eAAA,MAAA;;;AAIJ,CAlGA,WAkGA,CAAA;AACE,SAAA,IAAA;AACA,UAAA;AACA,WAAA;AACA,YAAA;AACA,OAAA;AACA,SAAA;AACA,UAAA;AACA,oBAAA;AACA,eAAA;AACA,UAAA;;AAEA,CA9GF,WA8GE,CAZF,aAYE;AACE,SAAA;AACA,UAAA;;AAKF,CArHF,WAqHE,CAAA,mBAAA,CAhHF;AAiHI,WAAA;;AAGF,CAzHF,WAyHE,CAJA,mBAIA,CAzFE;AA0FA,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,aAAA;AACA,UAAA;AACA,iBAAA;;;;ACnIJ,CAAA,WAAA,CAAA;AACE,SAAA;AACA,WAAA;AACA,OAAA,KAAA,IAAA,gBAAA,EAAA,IAAA;;AAGF,CANA,WAMA,CAAA;AACE,yBAAA;AACA,kBAAA;AACA,kBAAA;;AAGF,CAZA,WAYA,CAAA;AACE,sBAAA;AACA,kBAAA;AACA,qBAAA;;;;ACZA,CAAA,WAAA,CAAA;AACE,WAAA;AACA,cAAA;AACA,mBAAA;;;;ACNJ,CAAA,WAAA,CAAA;AACE,YAAA;AACA,WAAA;AACA,cAAA;AACA,eAAA;AACA,WAAA,OAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,oBAAA;AACA,UAAA;AACA,iBAAA,IAAA;AACA,kBAAA;AACA,SAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;;AAEA,CAjBF,WAiBE,CAjBF,sBAiBE;AACE,WAAA;AACA,SAAA;AACA,UAAA;;AAGF,CAvBF,WAuBE,CAAA;AACE,oBAAA,IAAA;AACA,gBAAA,IAAA;AACA,SAAA;;AAGF,CA7BF,WA6BE,CAAA;AACE,oBAAA,IAAA;AACA,gBAAA,IAAA;AACA,SAAA;;AAKF,CArCF,UAqCE,CAAA,YAAA,CAdA;AAeE,SAAA,IAAA;;AAGF,CAzCF,UAyCE,CAJA,YAIA,CAZA;AAaE,SAAA,IAAA;;;;ACvCF,CAAA,WAAA,CAAA;AACE,WAAA;AACA,kBAAA;;AAEA,CAJF,WAIE,CAJF,wBAIE;AACE,WAAA,IAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;;AACA,CATJ,WASI,CATJ,wBASI,MAAA;AACE,eAAA;AACA,aAAA;AACA,SCqBE;;ADnBJ,CAdJ,WAcI,CAdJ,wBAcI,MAAA;AAAA,CAdJ,WAcI,CAdJ,wBAcI,MAAA;AAEE,SAAA;AACA,WAAA;AACA,eAAA,IAAA;;AAGF,CArBJ,WAqBI,CArBJ,wBAqBI,MAAA,CAAA;AACE,SCyCC;ADxCD,UAAA;;AAKN,CA5BA,WA4BA,CAAA;AACE,WAAA;AACA,WAAA,OAAA;AACA,mBAAA;AACA,OAAA;;AAEA,CAlCF,WAkCE,CANF,yBAME,CAAA;AACE,aAAA;AACA,SAAA;AACA,aAAA;;AAGF,CAxCF,WAwCE,CAZF,yBAYE,CAAA;AACE,eAAA;AACA,WAAA,EAAA;;AAGF,CA7CF,WA6CE,CAAA,iCAAA,CALA;AAME,oBC2JI;;ADzJJ,CAhDJ,WAgDI,CAHF,iCAGE,CARF,oBAQE;AACE,oBC0JE;;ADtJN,CArDF,WAqDE,CAAA,gCAAA,CAbA;AAcE,oBCtBI;;ADuBJ,CAvDJ,WAuDI,CAFF,gCAEE,CAfF,oBAeE;AACE,oBCvBE;;AD2BN,CA5DF,WA4DE,CAhCF,yBAgCE,CA1BA;AA2BE,SCtDW;;ADuDX,CA9DJ,WA8DI,CAlCJ,yBAkCI,CA5BF,eA4BE,CAAA;AACE,mBAAA;;AACA,CAhEN,WAgEM,CApCN,yBAoCM,CA9BJ,eA8BI,CAFF,QAEE;AACE,WAAA;;AAMR,CAvEA,WAuEA,CAAA,gBAAA,CAAA;AACE,WAAA;AACA,aAAA;;AAEA,CA3EF,WA2EE,CAJF,gBAIE,CAJF,uBAIE,CAAA;AACE,SAAA;;AAIJ,CAhFA,WAgFA,CAAA;AACE,SCpBK;;ADuBP,CApFA,WAoFA,CAAA;AACE,WAAA,IAAA;AACA,cAAA;AACA,aAAA;AACA,WAAA;;AAIJ,CA5FE,WA4FF,CAAA;AACE,YAAA;;AAEA,CA/FA,WA+FA,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,aAAA;AACA,SCtCK;ADuCL,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA,OAAA;AACA,iBAAA;;AAGF,CA1GA,WA0GA,CAAA;AACE,oBCpGa;ADqGb,WAAA;AACA,SAAA;AACA,UAAA;AACA,UAAA,IAAA,MAAA,IAAA;;AACA,CAhHF,WAgHE,CANF,yBAME;AACE,SAAA;AACA,UAAA;;AAIJ,CAtHA,WAsHA,CA1BF,oBA0BE,CApFE;AAqFA,oBChHa;ADiHb,SAAA;AACA,UAAA;AACA,UAAA,EAAA;;AAEF,CA5HA,WA4HA,CAhCF,oBAgCE,CAAA;AAAA,CA5HA,WA4HA,CAhCF,oBAgCE,CApFE,oBAoFF;AAEE,oBAAA;;AAEF,CAhIA,WAgIA,CApCF,oBAoCE,CA3GI;AA2GJ,CAhIA,WAgIA,CApCF,oBAoCE,CAAA;AAEE,SCnEK;ADoEL,eAAA;AACA,aAAA;AACA,UAAA;;AAEF,CAvIA,WAuIA,CA3CF,oBA2CE,CAPA;AAQE,eAAA;AACA,UAAA;AACA,WAAA,MAAA;;AAGF,CA7IA,WA6IA,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;;AAEA,CAlJF,WAkJE,CALF,4BAKE,CAhHA;AAiHE,UAAA;;AAEF,CArJF,WAqJE,CARF,4BAQE,CAnHA;AAoHE,oBCzFG;;AD0FH,CAvJJ,WAuJI,CAVJ,4BAUI,CArHF,cAqHE;AACE,oBC1FC;;AD4FH,CA1JJ,WA0JI,CAbJ,4BAaI,CAxHF,cAwHE;AACE,oBC5FC;;AD+FL,CA9JF,WA8JE,CAjBF,4BAiBE;AACE,SCxJW;ADyJX,WAAA;AACA,iBAAA;AACA,SAAA;AACA,UAAA;;;;AEpKN,CAAA,WAAA,CAAA;AACE,YAAA;AACA,OAAA;AACA,cAAA;;AAEA,CALF,WAKE,CAAA;AACE,QAAA;AACA,SAAA;AACA,WAAA;;AAEA,CAVJ,WAUI,CALF,sBAKE,CAAA;AACE,WAAA,IAAA;AACA,cAAA;AAEA,cAAA,IAAA;AACA,iBAAA,IAAA;AACA,YAAA;AACA,cAAA,WAAA,KAAA;;AAEA,CAnBN,WAmBM,CAdJ,sBAcI,CATF,uBASE,CAAA;AACE,cAAA;;AAKN,CAzBF,WAyBE,CAzBF,cAyBE,CAfE;AAgBA,oBAAA,IAAA;AACA,cAAA,KAAA,MAAA,EAAA;AACA,cAAA;AACA,SAAA;;AAGF,CAhCF,WAgCE,CAhCF,cAgCE,CAAA;AACE,WAAA;AACA,cAAA;AACA,aAAA;AACA,SAAA,IAAA;AACA,SAAA;AACA,cAAA;AACA,eAAA;AACA,eAAA;;AAME,CA9CN,WA8CM,CA9CN,aA8CM,CAAA,aAAA,CAAA,kBAAA;AACE,oBAAA;;AAGF,CAlDN,WAkDM,CAlDN,aAkDM,CAJA,aAIA,CAAA;AACE,oBAAA,IAAA;;AAGF,CAtDN,WAsDM,CAtDN,aAsDM,CARA,aAQA,CAAA;AACE,oBAAA,IAAA;;AAKN,CA5DF,WA4DE,CA5DF,aA4DE,CAAA;AACE,cAAA;AAGA,cAAA,KAAA,EAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EAAA,QAAA,EAAA;;AAEA,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AANF,GA5DF,WA4DE,CA5DF,aA4DE,CAAA;AAOI,gBAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,KAAA,EAAA,QAAA,EAAA;;;AAKF,CAxEJ,WAwEI,CAxEJ,aAwEI,CAZF,MAYE,CAxCF;AAyCI,WAAA;;AAGF,CA5EJ,WA4EI,CA5EJ,aA4EI,CAhBF,MAgBE,CAAA,mBAAA,KAAA;AACE,cAAA;;AAGF,CAhFJ,WAgFI,CAhFJ,aAgFI,CApBF,MAoBE,CAAA;AACE,aAAA;AACA,cAAA;AACA,eAAA;AACA,UAAA,EAAA,EAAA;AACA,eAAA;;AAIJ,CAzFF,WAyFE,CAzFF,cAyFE,CA3CI;AA4CF,UAAA;AACA,UAAA;AACA,WAAA,EAAA;AACA,SAAA,KAAA,KAAA,EAAA;AACA,oBAAA;AACA,UAAA,IAAA,MAAA;AACA,eAAA;AACA,UAAA;AACA,iBAAA,IAAA;;AAEA,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AAXF,GAzFF,WAyFE,CAzFF,cAyFE,CA3CI;AAuDA,YAAA;;;AAGF,CAxGJ,WAwGI,CAxGJ,cAwGI,CAAA;AACE,WAAA;AACA,eAAA;AACA,SAAA;AACA,iBAAA;AACA,YAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAlHJ,WAkHI,CAlHJ,cAkHI,CAAA;AACE,uBAAA;AACA,WAAA;;AAEA,CAtHN,WAsHM,CAtHN,cAsHM,CAAA;AACE,cAAA;AACA,aAAA;AACA,WAAA,EAAA;;AAIJ,CA7HJ,WA6HI,CA7HJ,cA6HI,CAvEE;AAwEA,cAAA,IAAA;AACA,qBAAA,IAAA;;AAGF,CAlIJ,WAkII,CAlIJ,cAkII,CApFE,kBAoFF;AACE,oBAAA,IAAA;AACA,mBAAA;;AAGF,CAvIJ,WAuII,CAvIJ,cAuII,CAzFE,kBAyFF;AACE,oBAAA,IAAA;AACA,gBAAA,IAAA;;AAGF,CA5IJ,WA4II,CA5IJ,cA4II,CA9FE,mBA8FF;AACE,SAAA;AACA,UAAA;AACA,WAAA;;AAIJ,CAnJF,WAmJE,CAnJF,cAmJE,CAAA;AACE,eAAA;AACA,UAAA;AACA,mBAAA;;AAEA,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AALF,GAnJF,WAmJE,CAnJF,cAmJE,CAAA;AAMI,YAAA;;;AAGF,CA5JJ,WA4JI,CA5JJ,cA4JI,CATF,wBASE;AACE,SAAA;AACA,UAAA;AACA,WAAA;;AAIJ,CAnKF,WAmKE,CAnKF,cAmKE,CAAA;AACE,cAAA;;AAGF,CAvKF,WAuKE,CAvKF,cAuKE,CAvFE;AAwFA,aAAA;AACA,cAAA;AACA,UAAA,KAAA;AACA,eAAA;;AAIJ,CA/KA,WA+KA,CAAA;ACjHA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;AACA,cAAA;AACA,gBAAA;AACA,gBAAA;AACA,gBAAA,IAAA,eAAA,EAAA,IAAA;AACA,iBAAA,IAAA;AACA,UAAA;AACA,oBAAA,IAAA,WAAA,EAAA,IAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,eAAA,IAAA;ADqGE,SAAA,IAAA;AACA,UAAA,IAAA;AAEA,gBAAA,IAAA;AAEA,oBAAA,IAAA;;ACxGF,CD9EA,WC8EA,CDiGA,qBCjGA;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;;AAGF,CDnFA,WCmFA,CD4FA,oBC5FA;AACE,oBAAA,IAAA,iBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,qBAAA,EAAA,IAAA,eAAA,EAAA,IAAA;AAIA,SAAA,IAAA,oBAAA,EAAA,IAAA,cAAA,EAAA,IAAA,oBAAA,EAAA;;AAMF,CD/FA,WC+FA,CDgFA,oBChFA;AACE,oBAAA,IAAA,kBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,sBAAA,EAAA,IAAA;;AAGF,CDpGA,WCoGA,CD2EA,oBC3EA,CAAA;AACE,oBAAA,IAAA,oBAAA,EAAA,IAAA;AAIA,gBAAA,IAAA,wBAAA,EAAA,IAAA;;AAKA,CD9GF,WC8GE,CDiEF,oBCjEE,CAVF,MAUE;AACE,oBAAA,IAAA,0BAAA,EAAA,IAAA;;AAMF,CDrHF,WCqHE,CD0DF,oBC1DE,CAjBF,OAiBE;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;;ADkEO,CAxLX,UAwLW,CAAA,WAAA,CAxLX,WAwLW,CATX;AAUI,gBAAA,IAAA;;AACA,CA1LJ,UA0LI,CAFO,WAEP,CA1LJ,WA0LI,CAXJ,oBAWI;AACE,gBAAA;;AAIJ,CA/LF,WA+LE,CAhBF,oBAgBE;AACE,gBAAA,IAAA;AACA,oBAAA,IAAA;AACA,mBAAA;;AAGF,CArMF,WAqME,CAtBF,oBAsBE;AACE,gBAAA,IAAA;;AAGF,CAzMF,WAyME,CA1BF,qBA0BE;AACE,SAAA,IAAA;AACA,UAAA,IAAA;;AAGF,CA9MF,WA8ME,CAAA;AACE,UAAA;AACA,UAAA;AACA,WAAA;AACA,SAAA,IAAA;AACA,UAAA,IAAA;;;;AEnNJ,CAAA,WAAA,CAAA;AACE,eAAA;AACA,UAAA,IAAA,MAAA;AACA,WAAA;AACA,mBAAA;AACA,YAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA;AACA,iBAAA,IAAA;;AAEA,CAXF,WAWE,CAXF,aAWE;AAEE,kBAAA;;AAGF,CAhBF,WAgBE,CAAA;AACE,gBAAA,IAAA;;AAGF,CApBF,WAoBE,CAAA;AACE,gBAAA,IAAA;AACA,gBAAA;;AAGF,CAzBF,WAyBE,CAAA;AACE,WAAA;AACA;IAAA;MAAA,MAAA;MAAA,IAAA,gBAAA;MAAA,IAAA,gBAAA;MAAA,IAAA;AAMA,mBAAA,KAAA;AACA,aAAA,yCAAA,KAAA;;AAIJ,CAtCA,UAsCA,CAAA,YAAA,CAbE;AAcA;IAAA;MAAA,MAAA;MAAA,IAAA,iBAAA;MAAA,IAAA,gBAAA;MAAA,IAAA;;AAQF,CA/CA,WA+CA,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,UAAA;AACA,SAAA;;AAGF,CAvDA,WAuDA,CARA,sBAQA,EAAA;AACE,UAAA,IAAA;AACA,aAAA;AACA,cAAA;AACA,aAAA;;AAGF,CA9DA,WA8DA,CAAA;AAAA,CA9DA,WA8DA,CAAA,gCAAA;AAAA,CA9DA,WA8DA,CAAA,gCAAA;AAGE,eAAA;AACA,cAAA;AACA,UAAA;AACA,SAAA,IAAA;AACA,WAAA;AACA,mBAAA;AACA,UAAA;AACA,WAAA;AACA,YAAA;AACA,QAAA;AACA,UAAA;AACA,UAAA;;AAEA,CA9EF,WA8EE,CAhBF,iCAgBE;AAAA,CA9EF,WA8EE,CAhBF,gCAgBE,OAAA;AAAA,CA9EF,WA8EE,CAhBF,gCAgBE,QAAA;AACE,UAAA;;AAIJ,CAnFA,WAmFA,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,UAAA;;AAEA,CAzFF,WAyFE,CANF,uBAME,CAAA;AACE,UAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,oBAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,cAAA;AACA,WAAA;;AAIA,CArGJ,WAqGI,CAlBJ,sBAkBI,CAAA,QAAA,OAAA,CAZF;AAaI,oBAAA,IAAA;;AAKF,CA3GJ,WA2GI,CAxBJ,sBAwBI,CAAA,WAAA,CAlBF;AAmBI,oBAAA,IAAA;;AAEA,CA9GN,WA8GM,CA3BN,sBA2BM,CAHF,WAGE,CArBJ,aAqBI;AACE,SAAA,IAAA;;AAMR,CArHA,WAqHA,CAAA,gCAAA,EAAA;AACE,UAAA;AACA,SAAA;;AAGF,CA1HA,WA0HA,CAAA;AACE,aAAA,MAAA;AACA,aAAA,8BAAA,GAAA,QAAA;AAEA,YAAA;AACA,SAAA;AACA,UAAA;AACA,oBAAA,IAAA;AACA,iBAAA,IAAA;AAEA,WAAA;AACA,mBAAA;AACA,eAAA;AAEA,kBAAA;;AAEA,CA1IF,WA0IE,CAhBF,oBAgBE;AACE,SAAA,IAAA;AACA,SAAA;AACA,UAAA;;AAIJ,CAjJA,WAiJA,CAjJA,YAiJA,QAAA,CAvBA;AAwBE,aAAA;AACA,aAAA,MAAA;;AAGF,CAtJA,WAsJA,CAAA;AACE,UAAA;;AAGF,WA9BE;AA+BA;AACE,eAAA,MAAA;;AAGF;AACE,eAAA,MAAA;;AAGF;AACE,eAAA,MAAA;;;AAIJ,WAtII;AAuIF;AACE,aAAA;;AAGF;AACE,aAAA;;AAGF;AACE,aAAA;;;;;ACnLN,CAAA;AACE,yBAAA;AACA,yBAAA;;AAEA,CAJF,WAIE,CAAA;AACE,cAAA;AACA,SAAA,IAAA;AACA,eAAA;AACA,aAAA;AACA,SAAA;;AAEA,CAXJ,WAWI,CAAA;AACE,SAAA,IAAA;AACA,eAAA;AACA,aAAA;AACA,iBAAA;;AAKF,CApBJ,UAoBI,CAAA,YAAA,CAhBF;AAiBI,SAAA,IAAA;;AAIJ,CAzBF,WAyBE,CAAA;AACE,SAAA;AACA,WAAA;AACA,aAAA;AACA,eAAA;AACA,cAAA;AACA,cAAA;AACA,kBAAA;AACA,UAAA;AACA,mBAAA;AACA,UAAA;AAEA,YAAA;;AAEA,CAvCJ,WAuCI,CAdF,6BAcE,EAAA;AACE,gBAAA;AACA,iBAAA;;AAGF,CA5CJ,WA4CI,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,CAAA,EAAA;AACA,OAAA;;AAGF,CAlDJ,WAkDI,CAAA;AACE,WAAA;AACA,WAAA,IAAA,uBAAA;AACA,QAAA;AACA,cAAA;AACA,cAAA;AACA,iBAAA;;AAGF,CA3DJ,WA2DI,CAAA;AACE,SAAA,IAAA;AACA,aAAA;AACA,eAAA;AACA,iBAAA;AACA,SAAA;AACA,iBAAA;AACA,cAAA;;AAEA,CApEN,WAoEM,CAAA;AACE,cAAA;;AAIJ,CAzEJ,WAyEI,CAAA;AACE,WAAA;AACA,yBAAA,IAAA,IAAA,IAAA;AACA,YAAA;;AAGF,CA/EJ,WA+EI,CAtDF,6BAsDE,CAAA;AACE,SAAA;AACA,WAAA;AACA,eAAA;AACA,eAAA;AACA,aAAA;AACA,UAAA,MAAA;AACA,SAAA,IAAA;;AAIJ,CA1FF,WA0FE,CAAA;AAGE,cAAA;AACA,SAAA;;;;AC7FF,CAAA,WAAA,CAAA;AACE,WAAA;AACA,kBAAA;AAEA,QAAA,EAAA,EAAA;;AAGF,CAPA,WAOA,CAAA;AACE,oBAAA,IAAA;AACA,SAAA,IAAA;AACA,eAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,iBAAA;AACA,SAAA;AACA,UAAA;AACA,YAAA;AACA,UAAA;AACA,SAAA;AACA,aAAA;AACA,kBAAA;;AAGF,CAxBA,WAwBA,CAAA;AACE,WAAA;AACA,aAAA;AACA,WAAA;AACA,kBAAA;AACA,eAAA;AACA,aAAA;AACA,mBAAA;;AAEA,CAjCF,WAiCE,CATF,0BASE;AACE,aAAA;;AAKF,CAvCF,WAuCE,CAAA,wBAAA,CAAA;AACE,WAAA;AACA,kBAAA;;AAGF,CA5CF,WA4CE,CAAA,6BAAA,CAAA;AACE,oBC6JM;AD5JN,cAAA;;AACA,CA/CJ,WA+CI,CAHF,6BAGE,CAHF,oBAGE;AACE,oBC4JI;;AD1JN,CAlDJ,WAkDI,CANF,6BAME,CANF,qBAME,CAAA;AACE,SAAA;AACA,aAAA;AACA,SC7CW;AD8CX,WAAA,EAAA;;AAKN,CA3DA,WA2DA,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA;AACA,YAAA;;AAEA,CAlEF,WAkEE,CAAA,uCAAA;AACE,WAAA;AACA,SAAA,KAAA,KAAA,EAAA;AACA,UAAA;AACA,YAAA;AACA,OAAA;AACA,cAAA,IAAA;;AAIJ,CA5EA,WA4EA,CAAA;AACE,QAAA;AAEA,UAAA,IAAA;AAEA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,YAAA;AACA,YAAA;AAEA,iBAAA,IAAA;AACA,oBAAA,IAAA;AACA,SCjFe;ADkFf,cAAA;AACA,eAAA;AACA,mBAAA;AAEA,eAAA;AACA,aAAA;;AAEA,CAjGF,WAiGE,CArBF,0BAqBE;AACE,oBAAA,IAAA;;AAEF,CApGF,WAoGE,CAxBF,0BAwBE;AACE,oBAAA,IAAA;;AAKF,CA1GF,UA0GE,CAAA,YAAA,CA9BF;AA+BI,SAAA,IAAA;;AAIJ,CA/GA,UA+GA,CAAA,mBAAA,CAnCA;AAoCE,UAAA,IAAA;;AAGF,CAnHA,WAmHA,CAnHA,kBAmHA,CAAA;AACE,SAAA;AACA,OAAA;AACA,SAAA;AACA,QAAA;AACA,UAAA;AACA,iBAAA;;AAEA,CA3HF,WA2HE,CA3HF,kBA2HE,CARF,cAQE,CAAA;AACE,SAAA;AACA,cAAA,IAAA;AACA,iBAAA,IAAA;AACA,WAAA,QAAA;;AAIJ,CAnIA,WAmIA,CAnIA,kBAmIA,CAAA;AACE,YAAA;;AAEA,CAtIF,WAsIE,CAtIF,kBAsIE,CAAA;AACE,WAAA;AACA,YAAA;AACA,OAAA;AACA,SAAA;AACA,WAAA;;AAEA,CA7IJ,WA6II,CA7IJ,kBA6II,CAPF,4CAOE,CA1BJ;AA2BM,OAAA;;;;AE/IR,CAAA;AACE,yBAAA,IAAA;AACA,+BAAA,IAAA;AACA,8BAAA,IAAA;AACA,wCAAA,IAAA;AACA,mCAAA,IAAA;AACA,0BAAA,IAAA;AACA,oCAAA,IAAA;AACA,gCAAA,IAAA;AACA,iCAAA,IAAA;AACA,+BAAA,IAAA;;AAEA,CAZF,WAYE,CAAA;AACE,YAAA;;AAEA,CAfJ,WAeI,CAHF,aAGE;AACE,YAAA;AACA,OAAA;AACA,aAAA,WAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA,IAAA;AACA,WAAA;;AAGF,CA1BJ,WA0BI,CAAA;AACE,SAAA;AACA,aAAA;;AAGF,CA/BJ,WA+BI,CAAA;AACE,eAAA;AACA,cAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AAEA,SAAA,IAAA;AAEA,iBAAA;AACA,eAAA;;AAGF,CA5CJ,WA4CI,CAAA;AACE,cAAA;AAEA,WAAA;AACA,kBAAA;AACA,eAAA;AAEA,UAAA;AAEA,cAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,iBAAA;AAEA,WAAA,EAAA;;AAGE,CA5DR,WA4DQ,CAhBJ,mBAgBI,KAAA,CA5DR,WA4DQ,CAAA,8BAAA;AACE,gBAAA,IAAA;;AAGF,CAhER,WAgEQ,CApBJ,mBAoBI,KAAA,CAhER,WAgEQ,CAJA,8BAIA;AAAA,CAhER,WAgEQ,CApBJ,mBAoBI,KAAA,CAhER,WAgEQ,CAJA,8BAIA;AAEE,gBAAA,IAAA;;AAIJ,CAtEN,WAsEM,CA1BF,oBA0BE;AACE,WAAA;AACA,eAAA;AAEA,UAAA;AACA,WAAA;AACA,WAAA;AACA,UAAA;AAEA,UAAA;AAEA,SAAA,IAAA;AAEA,eAAA;AACA,cAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,iBAAA;AAEA,cAAA;AAEA,SAAA;;AAGE,CA/FV,WA+FU,CAnDN,oBAmDM,KAAA,KAAA,OAAA;AACE,oBAAA;;AAIJ,CApGR,WAoGQ,CAxDJ,oBAwDI,KAAA;AACE,WAAA;AACA,cAAA;;AAIJ,CA1GN,WA0GM,CA9CE;AA+CA,cAAA,IAAA;AACA,gBAAA,IAAA;;AAEA,CA9GR,WA8GQ,CAlDA,8BAkDA;AACE,SAAA,IAAA;;AAKN,CApHJ,WAoHI,CAAA,sBAAA,CAxEA;AAyEE,gBAAA;;;;ACpHN,CAAA;AACE,kBAAA;AACA,iCAAA;AACA,8BAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,oCAAA;AACA,yBAAA,IAAA;AACA,mBAAA;AACA,2BAAA;AACA,qBAAA,IAAA;AACA,2BAAA;AACA,sBAAA;AACA,oBAAA;AACA,wBAAA;AACA,0BAAA;AACA,uBAAA;AACA,qBAAA;AACA,sBAAA,IAAA;AACA,gBAAA;AACA,sBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,oBAAA,IAAA;AACA,8BAAA;AACA,sBAAA;AACA,+BAAA;AACA,4BAAA;AACA;IAAA,IAAA,IAAA,qBAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,qBAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,KAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAIA,qBAAA,IAAA;AACA,sBAAA,IAAA;AACA,0BAAA,IAAA;AACA,0BAAA,IAAA;AAEA,yBAAA;AACA,uBAAA;AACA,oBAAA;AACA,kBAAA;AACA,8BAAA;AASA,qBAAA,IAAA;AACA,2BAAA,IAAA;AAEA,wBAAA,IAAA,GAAA,EAAA,IAAA,EAAA;AACA,wBAAA,IAAA;AAEA;IAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,MAAA;IAAA,IAAA,SAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAMA,yBAAA,IAAA;AACA;IAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,MAAA;IAAA,IAAA,SAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAMA,0BAAA,IAAA;AACA,sBAAA,IAAA;AACA;IAAA,IAAA,KAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,IAAA;IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAIA,kBAAA;AACA,wBAAA,IAAA;AAEA,qBAAA;AAEA,sBAAA;AAEA,mBAAA;AACA,0BAAA;AACA,2BAAA;AACA,yBAAA;AACA,gCAAA;AACA,yBAAA;AAEA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,oBAAA;AAEA,oBAAA,IAAA;AAEA,mBAAA;AACA,wBAAA;AACA,0BAAA;AACA,2BAAA;AACA,wBAAA,IAAA;AAEA,kBAAA;AACA,uBAAA;AACA,yBAAA;AACA,0BAAA;AACA,uBAAA;AAEA,6BAAA;AACA,kCAAA;AACA,wBAAA;AACA,6BAAA;AAEA,8BAAA,IAAA;AACA,mCAAA,IAAA;AACA,yBAAA,IAAA;AACA,8BAAA,IAAA;AAEA,iBAAA,IAAA;AACA,wBAAA,IAAA;AACA,yBAAA,IAAA;AACA,4BAAA,IAAA;AACA,mCAAA,IAAA;AAEA,iBAAA,IAAA;AAEA,mBAAA;AACA,0BAAA;AACA,2BAAA;AACA,wBAAA;AACA,4BAAA;AACA,kCAAA;AACA,mCAAA;AAEA,qBAAA,IAAA;AACA,qBAAA;AAEA,sBAAA;AACA,sBAAA;AAEA,wBAAA;AACA,uBAAA;AACA,uBAAA;AACA,0BAAA;AACA,sBAAA;AACA,uBAAA;AACA,gCAAA;AACA,qCAAA;AACA,wBAAA;AACA,0BAAA;AACA,kCAAA;AACA,qCAAA;AAEA,iBAAA;AACA,4BAAA;;AAtHA,OAAA,OAAA,IAAA,CAAA,gBAAA,EAAA;AA1CF,GAAA;AA2CI,sBAAA;AACA,oBAAA;AACA,2BAAA;AACA,yBAAA;;;AAqHA,CAnKJ,UAmKI,CAAA,WAAA,CAAA;AACE,cAAA;;AAIJ,CAxKF,UAwKE,CALE;AAMA,kBAAA,OAAA,KAAA,WAAA;AACA,iCAAA;AACA,8BAAA;AAEA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,oCAAA;AACA,yBAAA,IAAA;AACA,mBAAA;AACA,2BAAA;AACA,2BAAA;AACA,sBAAA;AACA,oBAAA;AACA,wBAAA;AACA,0BAAA;AACA,uBAAA;AACA,qBAAA;AACA,sBAAA,IAAA;AACA,gBAAA;AACA,sBAAA,KAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA;AACA,8BAAA;AACA,sBAAA;AACA,+BAAA;AACA,4BAAA;AACA;IAAA,IAAA,IAAA,qBAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,qBAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,KAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAIA;IAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,MAAA;IAAA,IAAA,SAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAMA,yBAAA,IAAA;AAEA,qBAAA;AACA,2BAAA;AAEA,wBAAA,IAAA,GAAA,EAAA,GAAA,EAAA;AAGA,qBAAA;AAEA,sBAAA,IAAA;AAEA,mBAAA;AACA,0BAAA;AACA,2BAAA;AACA,yBAAA;AACA,gCAAA;AACA,yBAAA;AAEA,oBAAA,IAAA;AAEA,wBAAA,IAAA;AAEA,kBAAA;AACA,uBAAA;AACA,yBAAA;AACA,0BAAA;AACA,uBAAA;AAEA,6BAAA;AACA,kCAAA;AACA,wBAAA;AACA,6BAAA;AAEA,8BAAA,IAAA;AACA,mCAAA,IAAA;AACA,yBAAA,IAAA;AACA,8BAAA,IAAA;AAEA,iBAAA,IAAA;AACA,wBAAA,IAAA;AACA,yBAAA,IAAA;AACA,4BAAA,IAAA;AACA,mCAAA,IAAA;AAEA,qBAAA;AAEA,wBAAA,IAAA,GAAA,EAAA,GAAA,EAAA;AACA,uBAAA,IAAA,GAAA,EAAA,EAAA,EAAA;AACA,uBAAA,IAAA,IAAA,GAAA;AACA,0BAAA,IAAA,CAAA,EAAA,EAAA,EAAA;AACA,sBAAA;AACA,uBAAA;AACA,gCAAA;AACA,qCAAA;AACA,wBAAA;AACA,0BAAA;AACA,kCAAA;AACA,qCAAA;;ACtQF,CDAF,WCAE,CAAA;AC8DA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;AACA,cAAA;AACA,gBAAA;AACA,gBAAA;AACA,gBAAA,IAAA,eAAA,EAAA,IAAA;AACA,iBAAA,IAAA;AACA,UAAA;AACA,oBAAA,IAAA,WAAA,EAAA,IAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,eAAA,IAAA;;AAEA,CF9EF,WE8EE,CD9EA,kBC8EA;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;;AAGF,CFnFF,WEmFE,CDnFA,iBCmFA;AACE,oBAAA,IAAA,iBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,qBAAA,EAAA,IAAA,eAAA,EAAA,IAAA;AAIA,SAAA,IAAA,oBAAA,EAAA,IAAA,cAAA,EAAA,IAAA,oBAAA,EAAA;;AAMF,CF/FF,WE+FE,CD/FA,iBC+FA;AACE,oBAAA,IAAA,kBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,sBAAA,EAAA,IAAA;;AAGF,CFpGF,WEoGE,CDpGA,iBCoGA,CAAA;AACE,oBAAA,IAAA,oBAAA,EAAA,IAAA;AAIA,gBAAA,IAAA,wBAAA,EAAA,IAAA;;AAKA,CF9GJ,WE8GI,CD9GF,iBC8GE,CAVF,MAUE;AACE,oBAAA,IAAA,0BAAA,EAAA,IAAA;;AAMF,CFrHJ,WEqHI,CDrHF,iBCqHE,CAjBF,OAiBE;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;;;;ACzHN,CAAA;AAAA,CAAA;AAEE,oBAAA,IAAA;AACA,iBAAA,IAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;;AAGF,CAAA;AAAA,CAPA,kBAOA;AAEE,iBAAA;AACA,oBAAA,IAAA;AACA,aAAA;AACA,SAAA,IAAA;AACA,UAAA,IAAA;;AAEA,CARF,YAQE;AAAA,CAfF,kBAeE,OAAA;AACE,SAAA,IAAA;AACA,UAAA,IAAA;;AAGF,CAbF,YAaE,CAAA;AAAA,CApBF,kBAoBE,OAAA,CAAA;AACE,SAAA;AACA,UAAA;;AAIJ,CAAA;AACE,eAAA;AACA,gBAAA;AACA,WAAA,EAAA;AACA,SAAA;AACA,mBAAA;AACA,SAAA,IAAA;;AAGF,CAAA;AACE,0BAAA,IAAA;AACA,6BAAA,IAAA;;AAEA,KAAA,CAAA,SAAA,CAJF;AAKI,aAAA,OAAA;;AAGF,CARF,gBAQE,CAvBA;AAwBE,2BAAA;AACA,8BAAA;;AAIJ,CAAA;AACE,2BAAA,IAAA;AACA,8BAAA,IAAA;;AAEA,KAAA,CAAA,SAAA,CAJF;AAKI,aAAA,OAAA;;AAGF,CARF,eAQE,CArCA;AAsCE,0BAAA;AACA,6BAAA;;AAKF,CAhEF,kBAgEE,CAAA,sBAAA;AACE,0BAAA,IAAA;AACA,6BAAA,IAAA;AACA,gBAAA;;AAEA,KAAA,CAAA,SAAA,CArEJ,kBAqEI,CALF,sBAKE;AACE,aAAA,OAAA;;AAGF,CAzEJ,kBAyEI,CATF,sBASE,OAAA,CArDF;AAsDI,2BAAA;AACA,8BAAA;;AAIJ,CA/EF,kBA+EE,CAAA,sBAAA;AACE,2BAAA,IAAA;AACA,8BAAA,IAAA;;AAEA,KAAA,CAAA,SAAA,CAnFJ,kBAmFI,CAJF,sBAIE;AACE,aAAA,OAAA;;AAGF,CAvFJ,kBAuFI,CARF,sBAQE,OAAA,CAnEF;AAoEI,0BAAA;AACA,6BAAA;;;;ACpFJ,CAAA,WAAA,CAAA;AACE,eAAA;;AAEA,CAHF,WAGE,CAHF,uBAGE,CAAA;AACE,UAAA;AACA,cAAA;;AAEA,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AAJF,GAHF,WAGE,CAHF,uBAGE,CAAA;AAKI,gBAAA;AACA,YAAA;;;AAGF,CAZJ,WAYI,CAZJ,uBAYI,CATF,eASE,CAAA;AACE,UAAA;AACA,WAAA;;AAGF,CAjBJ,WAiBI,CAjBJ,uBAiBI,CAdF,eAcE,CAAA;AACE,UAAA;AACA,WAAA;AACA,kBAAA;;AAIJ,CAxBF,WAwBE,CAxBF,uBAwBE,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA,OAAA;;AAGF,CAhCF,WAgCE,CAhCF,uBAgCE,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,UAAA;AACA,aAAA;AACA,OAAA;;AAEA,CAxCJ,WAwCI,CAxCJ,uBAwCI,CARF,SAQE,CAAA;AACE,WAAA;;AAGF,CA5CJ,WA4CI,CA5CJ,uBA4CI,CAZF,SAYE,CAAA;AACE,UAAA,IAAA;;AAGF,CAhDJ,WAgDI,CAhDJ,uBAgDI,CAhBF,SAgBE,CAAA;AACE,WAAA,IAAA;AACA,UAAA;AACA,iBAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,oBAAA,IAAA;;AAGF,CA1DJ,WA0DI,CA1DJ,uBA0DI,CA1BF,SA0BE,CAAA;AACE,eAAA;AACA,SAAA,IAAA;;AAIJ,CAhEF,WAgEE,CAhEF,uBAgEE,CAAA;AACE,cAAA;AACA,cAAA;AACA,cAAA;AACA,SAAA,IAAA;AACA,eAAA;;AAEA,CAvEJ,WAuEI,CAvEJ,uBAuEI,CAPF,SAOE,CAAA;AACE,WAAA;AACA,kBAAA;AACA,WAAA,KAAA;AACA,gBAAA;;AAGF,CA9EJ,WA8EI,CA9EJ,uBA8EI,CAdF,SAcE,CAAA;AACE,aAAA;AACA,eAAA;AACA,iBAAA;AACA,WAAA;AACA,eAAA;;AAGF,CAtFJ,WAsFI,CAtFJ,uBAsFI,CAtBF,SAsBE,CAAA;AACE,SAAA,IAAA;AACA,UAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA,WAAA,EAAA;AACA,iBAAA,IAAA;AACA,UAAA;;AAEA,CAjGN,WAiGM,CAjGN,uBAiGM,CAjCJ,SAiCI,CAXF,YAWE;AACE,oBAAA,IAAA;;AAGF,CArGN,WAqGM,CArGN,uBAqGM,CArCJ,SAqCI,CAfF,aAeE,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAIJ,CA5GJ,WA4GI,CA5GJ,uBA4GI,CA5CF,SA4CE,CAAA;AACE,oBAAA,IAAA;;AAGF,CAhHJ,WAgHI,CAhHJ,uBAgHI,CAhDF,SAgDE,CAAA;AACE,WAAA;AACA,UAAA;;AAGF,CArHJ,WAqHI,CArHJ,uBAqHI,CArDF,SAqDE,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;;AAIJ,CA7HF,WA6HE,CA7HF,uBA6HE,CAAA;AACE,SAAA;AACA,UAAA;AACA,gBAAA;;;;ACpIJ,CAAA,WAAA,CAAA;AACE,YAAA;AACA,WAAA;AACA,WAAA,IAAA,EAAA;AACA,WAAA;;;;ACFF,CAAA,WAAA,CAAA;AACE,YAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA;AACA,cAAA;AACA,eAAA;AACA,UAAA,SAAA,EAAA,EAAA;AACA,WAAA,OAAA;AACA,oBAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,UAAA;;AAGF,CAdA,WAcA,CAdA,aAcA;AACE,SAAA,IAAA;;AAGF,CAlBA,WAkBA,CAAA;AACE,YAAA;AACA,SAAA;AACA,aAAA;AACA,UAAA;AACA,WAAA,QAAA,KAAA,QAAA;AACA,cAAA;AACA,iBAAA;AACA,oBAAA;AACA,UAAA;AACA,eAAA;AACA,eAAA;AAEA,WAAA;AACA,yBAAA,IAAA;AACA,eAAA;;AAEA,CAnCF,WAmCE,CAjBF,iBAiBE,CAAA,SAAA;AACE,YAAA;AACA,QAAA;AACA,iBAAA;AACA,WAAA;;AAIA,CA3CJ,WA2CI,CAzBJ,iBAyBI,CAAA,UAAA,CAAA;AACE,SCmBG;;ADfP,CAhDF,WAgDE,CA9BF,kBA8BE,CALE;AAMA,gBAAA;AACA,qBAAA;;AAEF,CApDF,WAoDE,CAlCF,kBAkCE,CAAA;AACE,gBAAA;AACA,WAAA;AACA,eAAA;AACA,aAAA;;AAIJ,CA5DA,WA4DA,CA1CA,iBA0CA;AACE,SAAA,IAAA;AACA,oBAAA,IAAA;;AAEA,CAhEF,WAgEE,CA9CF,iBA8CE,MAAA,CArBE;AAyBA,oBCNK;;ADGL,CAjEJ,WAiEI,CA/CJ,iBA+CI,MAAA,CAtBA,UAsBA,CAtBA;AAuBE,SAAA,IAAA;;AAMN,CAxEA,WAwEA,CAtDA,iBAsDA;AACE,WAAA;;AAIA,CAAA,kBAAA,CA7EF,WA6EE,CA3DF;AA4DI,WAAA;;AAEA,CAHF,kBAGE,CAhFJ,WAgFI,CA9DJ,kBA8DI,CArCA;AAsCE,qBAAA;;AAGF,CAPF,kBAOE,CApFJ,WAoFI,CAlEJ,kBAkEI,CAhCF;AAiCI,WAAA;;AAKN,CA1FA,WA0FA,CAAA;AACE,UAAA;AACA,cAAA,IAAA,MAAA;;;;AE7FJ,CAAA;AACE,2BAAA,IAAA;AACA,uBAAA,IAAA;AAEA,iCAAA,IAAA;AACA,uCAAA,IAAA;AACA,sCAAA,IAAA;AACA,6CAAA,IAAA;AAEA,gCAAA,IAAA;AACA,qCAAA,IAAA;AACA,2CAAA,IAAA;AACA,4CAAA,IAAA;;AAEA,CAdF,WAcE,CAAA;AACE,cAAA;AACA,WAAA;AACA,kBAAA;AACA,eAAA;AAEA,WAAA;AACA,iBAAA;AAEA,cAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;;AAEA,CA1BJ,WA0BI,CAAA;AACE,YAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,SAAA;AACA,UAAA;AAEA,SAAA,IAAA;AACA,cAAA,IAAA;AAEA,iBAAA;AAEA,eAAA;AACA,cAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,kBAAA;AAEA,cAAA,IAAA,KAAA;;AAEA,CAjDN,WAiDM,CAvBF,kBAuBE;AACE,SAAA,IAAA;;AAGF,CArDN,WAqDM,CA3BF,kBA2BE;AACE,cAAA,IAAA;;AAGF,CAzDN,WAyDM,CA/BF,kBA+BE,CAAA;AACE,SAAA,IAAA;AACA,cAAA,IAAA;;AAEA,CA7DR,WA6DQ,CAnCJ,kBAmCI,CAJF,MAIE;AACE,cAAA,IAAA;;AAGF,CAjER,WAiEQ,CAvCJ,kBAuCI,CARF,MAQE;AACE,cAAA,IAAA;;AAIJ,CAtEN,WAsEM,CA5CF,mBA4CE;AACE,WAAA;AACA,YAAA;AACA,SAAA;AACA,UAAA;AACA,UAAA;AACA,WAAA;AAEA,iBAAA;AAEA,sBAAA;AACA,mBAAA;AACA,cAAA;AAEA,UAAA;;;;ACpFR,CAAA;AACE,2BAAA,IAAA;AACA,wCAAA,IAAA;AACA,4BAAA,IAAA;AACA,6BAAA,IAAA;AACA,6BAAA,IAAA;AACA,6BAAA,IAAA;AACA,8BAAA,IAAA;;AAEA,CATF,WASE,CAAA;AACE,YAAA;AACA,cAAA;AAEA,SAAA;AACA,UAAA;AACA,iBAAA;AAEA,uBAAA,UAAA,EAAA;AACA,uBAAA;AACA,8BAAA;AAEA,cAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;;AAEA,CAxBJ,WAwBI,CAfF,MAeE;AACE,cAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;;AAGF,CA7BJ,WA6BI,CApBF,MAoBE;AACE,UAAA,IAAA,MAAA,IAAA;;AAGF,CAjCJ,WAiCI,CAxBF,MAwBE,CAAA;AACE,cAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;;AAEA,CArCN,WAqCM,CA5BJ,MA4BI,CAJF,OAIE;AACE,cAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;;AAIJ,CA3CJ,WA2CI,CAlCF,MAkCE,CAAA;AACE,cAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;;AAEA,CA/CN,WA+CM,CAtCJ,MAsCI,CAJF,QAIE,CAdF;AAeI,cAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;;AAIJ,CArDJ,WAqDI,CA5CF,MA4CE;AACE,WAAA;AACA,cAAA;AACA,WAAA;AACA,kBAAA;AACA,YAAA;AAEA,iBAAA;AACA,cAAA,IAAA,MAAA;AAEA,SAAA;AACA,UAAA;AACA,OAAA;AACA,QAAA;AAEA,cAAA,IAAA;;AAGF,CAvEJ,WAuEI,CA9DF,MA8DE,OAAA;AACE,SAAA;;AAGF,CA3EJ,WA2EI,CAlEF,MAkEE,CA1CA,OA0CA;AACE,SAAA;AACA,UAAA;AACA,QAAA;AACA,OAAA;AAEA,cAAA,IAAA;;AAGF,CApFJ,WAoFI,CA3EF,MA2EE,CAnDA,OAmDA,OAAA;AACE,SAAA;AACA,QAAA;;AAGF,CAzFJ,WAyFI,CAhFF,MAgFE,CA9CA,QA8CA;AACE,cAAA,IAAA;;AAGF,CA7FJ,WA6FI,CApFF,MAoFE,CAlDA,QAkDA,CA5DA,OA4DA;AACE,cAAA,IAAA;;AAGF,CAjGJ,WAiGI,CAxFF,OAwFE;AACE,SAAA;AACA,UAAA;AACA,UAAA;AAEA,iBAAA;AAEA,sBAAA;AACA,mBAAA;AACA,cAAA;AAEA,UAAA;;AAEA,CA9GN,WA8GM,CArGJ,OAqGI,KAAA;AACE,UAAA;;;;AC/GR,CAAA;AACE,qCAAA;;AAEA,CAHF,UAGE,CAAA;AACE,qCAAA;;AAGF,CAPF,WAOE,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AAEA,eAAA;;AAEA,CAdJ,WAcI,CAPF,iBAOE;AACE,eAAA;AACA,cAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,WAAA;AACA,UAAA;;ACpBK,CAAA,kBAAA,CDDX,WCCW,CDMT,iBCNS;ADuBH,WAAA;;AAIJ,CA5BJ,WA4BI,CArBF,iBAqBE,EAAA;AACE,WAAA;;AC5BK,CAAA,kBAAA,CDDX,WCCW,CDMT,iBCNS,EAAA;AD+BH,WAAA;;AC/BG,CAAA,kBAAA,CDDX,WCCW,CDMT;AA8BI,kBAAA;AACA,UAAA,KAAA,MAAA,EAAA;;AAGF,CAzCJ,WAyCI,CAAA;AACE,cAAA;AACA,WAAA;AACA,kBAAA;AACA,eAAA;AACA,UAAA;AACA,SAAA;AAEA,gBAAA;;AChDK,CAAA,kBAAA,CDDX,WCCW,CDwCP;AAWI,aAAA;AACA,gBAAA;AAEA,SAAA;AACA,UAAA;AACA,aAAA;;AAIA,CA7DR,WA6DQ,CAAA,oCAAA,EAAA;AACE,cAAA;;AAIJ,CAlEN,WAkEM,CAAA;AACE,cAAA;AACA,SAAA;AACA,UAAA;AACA,WAAA;AACA,aAAA;AACA,mBAAA;AACA,eAAA;AAEA,cAAA,4KAAA,KAAA;AAGA,UAAA,IAAA,MAAA,IAAA;AACA,iBAAA;AAEA,YAAA;AACA,WAAA;;AAEA,CApFR,WAoFQ,CAlBF,kCAkBE,EAAA;AACE,aAAA,KAAA,KAAA,EAAA;AACA,cAAA,KAAA,KAAA,EAAA;AAEA,UAAA;;ACvFC,CAAA,kBAAA,CDDX,WCCW,CDiEL,kCCjEK,EAAA;AD0FC,cAAA;;AC1FD,CAAA,kBAAA,CDDX,WCCW,CDiEL;AA8BI,cAAA;AACA,aAAA;;AAKN,CAtGJ,WAsGI,CAAA;AACE,WAAA;AACA,kBAAA;AACA,aAAA;AACA,OAAA;;ACzGK,CAAA,kBAAA,CDDX,WCCW,CDqGP;AAOI,eAAA;AACA,cAAA;AACA,kBAAA;AACA,OAAA,IAAA;AAEA,iBAAA;;AAGF,CArHN,WAqHM,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;;ACxHG,CAAA,kBAAA,CDDX,WCCW,CDoHL;AAOI,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,UAAA;;AAGF,CAlIR,WAkIQ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AAEA,eAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;;AAEA,CA5IV,WA4IU,CAVF,2CAUE;AACE,SAAA;AACA,UAAA;AACA,eAAA;;AAIJ,CAnJR,WAmJQ,CAAA;AACE,WAAA;AACA,UAAA;AACA,eAAA;;AAIJ,CA1JN,WA0JM,CAAA;AACE,aAAA;AACA,aAAA;AACA,WAAA;AACA,kBAAA;AACA,OAAA;AAEA,eAAA;AACA,iBAAA;;ACjKG,CAAA,kBAAA,CDDX,WCCW,CDyJL;AAWI,eAAA;AACA,cAAA;AACA,mBAAA;;;;AEvKV,WAAA;AACE;AACE,eAAA,MAAA;;AAGF;AACE,eAAA,MAAA;;AAGF;AACE,eAAA,MAAA;;;AAKF,CAAA,WAAA,CAAA;AACE,gBAAA;AACA,kBAAA;AACA,gBAAA;AAEA,SAAA,IAAA;AACA,oBAAA,IAAA;AACA,gBAAA,IAAA;AAoPA,iBAAA;AACA,gBAAA;AACA,gBAAA;AAEA,eAAA,IAAA;AAEA,eAAA;;AAxPA,CATF,WASE,CATF,SASE;AACE,cAAA,IAAA,MAAA;;AAGF,CAbF,WAaE,CAbF,UAaE,CAAA;AACE,mBAAA,IAAA;;AAGF,CAjBF,WAiBE,CAjBF,UAiBE,CAAA;AACE,cAAA;AACA,YAAA;AAEA,SAAA;AACA,UAAA;AAEA,aAAA,uBAAA,KAAA,aAAA,GAAA,EAAA,CAAA,EAAA,GAAA,EAAA;;AAGF,CA3BF,WA2BE,CA3BF,SA2BE,CAAA;AAAA,CA3BF,WA2BE,CA3BF,SA2BE,CAAA;AAEE,kBAAA;;AAEA,CA/BJ,WA+BI,CA/BJ,SA+BI,CAJF,0BAIE,CAAA;AAAA,CA/BJ,WA+BI,CA/BJ,SA+BI,CAJF,0BAIE,CAAA;AACE,cAAA;;AAIJ,CApCF,WAoCE,CApCF,SAoCE,CAAA;AACE,kBAAA;;AAGF,CAxCF,WAwCE,CAxCF;AAwCE,CAxCF,WAwCE,CATE;AAWA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,eAAA;AACA,aAAA;AAEA,YAAA;;AAIA,CApDJ,WAoDI,CAAA,wBAAA,CAAA;AACE,gBAAA,IAAA;AACA,gBAAA,IAAA;;AAEA,CAxDN,WAwDM,CAJF,wBAIE,CAJF,0BAIE,CA3CJ;AA4CM,mBAAA,IAAA;;AAGF,CA5DN,WA4DM,CARF,wBAQE,CARF,yBAQE;AACE,gBAAA,IAAA;;AAGF,CAhEN,WAgEM,CAZF,wBAYE,CAZF,yBAYE;AACE,gBAAA,IAAA;;AAIJ,CArEJ,WAqEI,CAjBA,wBAiBA,CAAA;AAAA,CArEJ,WAqEI,CAjBA,wBAiBA,CAAA;AAEE,gBAAA,IAAA;AACA,kBAAA,IAAA;AACA,gBAAA;;AAEA,CA3EN,WA2EM,CAvBF,wBAuBE,CANF,4BAME,CA9DJ;AA8DI,CA3EN,WA2EM,CAvBF,wBAuBE,CANF,wBAME,CA9DJ;AA+DM,mBAAA,IAAA;;AAGF,CA/EN,WA+EM,CA3BF,wBA2BE,CAVF,2BAUE;AAAA,CA/EN,WA+EM,CA3BF,wBA2BE,CAVF,uBAUE;AACE,gBAAA,IAAA;AACA,kBAAA,IAAA;;AAGF,CApFN,WAoFM,CAhCF,wBAgCE,CAfF,2BAeE;AAAA,CApFN,WAoFM,CAhCF,wBAgCE,CAfF,uBAeE;AACE,gBAAA,IAAA;AACA,kBAAA,IAAA;;AAMJ,CA5FJ,WA4FI,CAAA,uBAAA,CAxCA;AAyCE,gBAAA,IAAA;AACA,gBAAA,IAAA;;AAEA,CAhGN,WAgGM,CAJF,uBAIE,CA5CF,0BA4CE,CAnFJ;AAoFM,mBAAA,IAAA;;AAGF,CApGN,WAoGM,CARF,uBAQE,CAhDF,yBAgDE;AACE,gBAAA,IAAA;;AAGF,CAxGN,WAwGM,CAZF,uBAYE,CApDF,yBAoDE;AACE,gBAAA,IAAA;;AAIJ,CA7GJ,WA6GI,CAjBA,uBAiBA,CAxCA;AAwCA,CA7GJ,WA6GI,CAjBA,uBAiBA,CAxCA;AA0CE,gBAAA,IAAA;AACA,kBAAA,IAAA;AACA,gBAAA;;AAEA,CAnHN,WAmHM,CAvBF,uBAuBE,CA9CF,4BA8CE,CAtGJ;AAsGI,CAnHN,WAmHM,CAvBF,uBAuBE,CA9CF,wBA8CE,CAtGJ;AAuGM,mBAAA,IAAA;;AAGF,CAvHN,WAuHM,CA3BF,uBA2BE,CAlDF,2BAkDE;AAAA,CAvHN,WAuHM,CA3BF,uBA2BE,CAlDF,uBAkDE;AACE,gBAAA,IAAA;AACA,kBAAA,IAAA;;AAGF,CA5HN,WA4HM,CAhCF,uBAgCE,CAvDF,2BAuDE;AAAA,CA5HN,WA4HM,CAhCF,uBAgCE,CAvDF,uBAuDE;AACE,gBAAA,IAAA;AACA,kBAAA,IAAA;;AAMJ,CApIJ,WAoII,CAAA,wBAAA,CAhFA;AAiFE,gBAAA,IAAA;AACA,gBAAA,IAAA;;AAEA,CAxIN,WAwIM,CAJF,wBAIE,CApFF,0BAoFE,CA3HJ;AA4HM,mBAAA,IAAA;;AAGF,CA5IN,WA4IM,CARF,wBAQE,CAxFF,yBAwFE;AACE,gBAAA,IAAA;;AAGF,CAhJN,WAgJM,CAZF,wBAYE,CA5FF,yBA4FE;AACE,gBAAA,IAAA;;AAIJ,CArJJ,WAqJI,CAjBA,wBAiBA,CAhFA;AAgFA,CArJJ,WAqJI,CAjBA,wBAiBA,CAhFA;AAkFE,gBAAA,IAAA;AACA,kBAAA,IAAA;AACA,gBAAA;;AAEA,CA3JN,WA2JM,CAvBF,wBAuBE,CAtFF,4BAsFE,CA9IJ;AA8II,CA3JN,WA2JM,CAvBF,wBAuBE,CAtFF,wBAsFE,CA9IJ;AA+IM,mBAAA,IAAA;;AAGF,CA/JN,WA+JM,CA3BF,wBA2BE,CA1FF,2BA0FE;AAAA,CA/JN,WA+JM,CA3BF,wBA2BE,CA1FF,uBA0FE;AACE,gBAAA,IAAA;AACA,kBAAA,IAAA;;AAGF,CApKN,WAoKM,CAhCF,wBAgCE,CA/FF,2BA+FE;AAAA,CApKN,WAoKM,CAhCF,wBAgCE,CA/FF,uBA+FE;AACE,gBAAA,IAAA;AACA,kBAAA,IAAA;;AAMJ,CA5KJ,WA4KI,CAAA,sBAAA,CAxHA;AAyHE,gBAAA,IAAA;AACA,gBAAA,IAAA;;AAEA,CAhLN,WAgLM,CAJF,sBAIE,CA5HF,0BA4HE,CAnKJ;AAoKM,mBAAA,IAAA;;AAGF,CApLN,WAoLM,CARF,sBAQE,CAhIF,yBAgIE;AACE,gBAAA,IAAA;;AAGF,CAxLN,WAwLM,CAZF,sBAYE,CApIF,yBAoIE;AACE,gBAAA,IAAA;;AAIJ,CA7LJ,WA6LI,CAjBA,sBAiBA,CAxHA;AAwHA,CA7LJ,WA6LI,CAjBA,sBAiBA,CAxHA;AA0HE,gBAAA,IAAA;AACA,kBAAA,IAAA;AACA,gBAAA,IAAA;;AAEA,CAnMN,WAmMM,CAvBF,sBAuBE,CA9HF,4BA8HE,CAtLJ;AAsLI,CAnMN,WAmMM,CAvBF,sBAuBE,CA9HF,wBA8HE,CAtLJ;AAuLM,mBAAA,IAAA;;AAGF,CAvMN,WAuMM,CA3BF,sBA2BE,CAlIF,2BAkIE;AAAA,CAvMN,WAuMM,CA3BF,sBA2BE,CAlIF,uBAkIE;AACE,gBAAA,IAAA;AACA,kBAAA,IAAA;;AAGF,CA5MN,WA4MM,CAhCF,sBAgCE,CAvIF,2BAuIE;AAAA,CA5MN,WA4MM,CAhCF,sBAgCE,CAvIF,uBAuIE;AACE,gBAAA,IAAA;AACA,kBAAA,IAAA;;AAMJ,CApNJ,WAoNI,CAAA,wBAAA,CAhKA;AAiKE,gBAAA;AACA,gBAAA,IAAA;;AAEA,CAxNN,WAwNM,CAJF,wBAIE,CApKF,0BAoKE,CA3MJ;AA4MM,mBAAA,IAAA;;AAGF,CA5NN,WA4NM,CARF,wBAQE,CAxKF,yBAwKE;AACE,gBAAA,IAAA;;AAGF,CAhON,WAgOM,CAZF,wBAYE,CA5KF,yBA4KE;AACE,gBAAA,IAAA;;AAIJ,CArOJ,WAqOI,CAjBA,wBAiBA,CAhKA;AAgKA,CArOJ,WAqOI,CAjBA,wBAiBA,CAhKA;AAkKE,gBAAA,IAAA;AACA,kBAAA,IAAA;AACA,gBAAA,IAAA;;AAEA,CA3ON,WA2OM,CAvBF,wBAuBE,CAtKF,4BAsKE,CA9NJ;AA8NI,CA3ON,WA2OM,CAvBF,wBAuBE,CAtKF,wBAsKE,CA9NJ;AA+NM,mBAAA,IAAA;;AAGF,CA/ON,WA+OM,CA3BF,wBA2BE,CA1KF,2BA0KE;AAAA,CA/ON,WA+OM,CA3BF,wBA2BE,CA1KF,uBA0KE;AACE,gBAAA,IAAA;AACA,kBAAA,IAAA;;AAGF,CApPN,WAoPM,CAhCF,wBAgCE,CA/KF,2BA+KE;AAAA,CApPN,WAoPM,CAhCF,wBAgCE,CA/KF,uBA+KE;AACE,gBAAA,IAAA;AACA,kBAAA,IAAA;;AAaN,CAnQF,WAmQE,CAAA;AACE,eAAA;AACA,aAAA;AACA,cAAA;AACA,WAAA,OAAA;AAEA,kBAAA;;AAEA,CA3QJ,WA2QI,CARF,sBAQE,CA5OA;AA6OE,OAAA;;AAIJ,CAhRF,WAgRE,CAAA;AACE,eAAA;AACA,aAAA;AACA,cAAA;AACA,WAAA,OAAA;AAEA,kBAAA;;AAEA,CAxRJ,WAwRI,CARF,uBAQE,CAzPA;AA0PE,OAAA;;AAIJ,CA7RF,WA6RE,CAxNE;AAyNA,WAAA,OAAA;AACA,SAAA;;AAGF,CAlSF,WAkSE,CAAA;AACE,SAAA;;AAGF,CAtSF,WAsSE,CAAA;AACE,SAAA;AACA,UAAA;;;;ACtTJ,CAAA,WAAA,CAAA;AACE,YAAA;AACA,kBAAA;;AAGF,CALA,WAKA,CALA,mBAKA,EAAA;AACE,kBAAA,IAAA;;AAGF,CATA,WASA,CAAA;AACE,QAAA,IAAA;AACA,OAAA,IAAA;AACA,SAAA,IAAA;AACA,UAAA,IAAA;;AAGF,CAhBA,WAgBA,CAPA,2BAOA,CAAA;AACE,SAAA;;;;ACdF,CAAA,WAAA,CAAA;AACE,kBAAA;AACA,cAAA;AACA,YAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,QAAA;AACA,OAAA;AACA,aAAA;AACA,SAAA;AACA,cAAA;AACA,cAAA;AACA,SAAA,IAAA;AACA,aAAA;;ACjBO,CAAA,kBAAA,CDGT,WCHS,CDGT;AAiBI,YAAA;AACA,iBAAA;;AAGF,CArBF,WAqBE,CArBF,WAqBE,EAAA;AACE,WAAA;;AAKF,CA3BF,UA2BE,CAAA,YAAA,CA3BF;AA4BI,SAAA,IAAA;;;;AE7BA,CAAA,kBAAA,CAAA,WAAA,CAAA,iBAAA,CAAA;AACE,WAAA;AACA,kBAAA;;AAGJ,CALE,WAKF,CALE,iBAKF,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,aAAA;;ACXK,CDEL,kBCFK,CDEL,WCFK,CDEL,iBCFK,CDOP;AAMI,kBAAA;AACA,mBAAA;;AAGJ,CAfE,WAeF,CAfE,iBAeF,CAAA;AACE,UAAA;AACA,cAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,WAAA;AACA,UAAA,IAAA,MAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,cAAA;;AACA,CA3BA,WA2BA,CA3BA,iBA2BA,CAZF,aAYE;AACE,WAAA;;AAEF,CA9BA,WA8BA,CA9BA,iBA8BA,CAfF,aAeE;AACE,cAAA;AACA,aAAA;;AAEF,CAlCA,WAkCA,CAlCA,iBAkCA,CAnBF,YAmBE;AACE,WAAA;AACA,UAAA,IAAA,MAAA;;;;AErCJ,CAAA,WAAA,CAAA,WAAA,CAAA;AACE,aAAA;;AAGF,CAJA,WAIA,CAJA,WAIA;AACE,UAAA,OAAA;AACA,eAAA;AACA,aAAA;;AAGF,CAVA,WAUA,CAAA;AACE,WAAA;AACA,aAAA;AACA,OAAA;;AAGF,CAhBA,WAgBA,CAAA;AACE,gBAAA,IAAA;AAEA,WAAA;AACA,cAAA;AACA,eAAA;AACA,oBAAA,IAAA;AACA,WAAA,SAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,iBAAA,IAAA;AACA,SAAA,IAAA;AACA,eAAA;AACA,aAAA;AACA,kBAAA;;AAES,CA/BX,UA+BW,CAAA,WAAA,CA/BX,WA+BW,CAfX;AAgBI,gBAAA,IAAA;;AACA,CAjCJ,UAiCI,CAFO,WAEP,CAjCJ,WAiCI,CAjBJ,eAiBI;AACE,gBAAA;;AAIJ,CAtCF,WAsCE,CAtBF,eAsBE;AACE,gBAAA,IAAA;AACA,mBAAA;;AAGF,CA3CF,WA2CE,CA3BF,eA2BE;AACE,gBAAA,IAAA;;AAIJ,CAhDA,WAgDA,CAAA;AACE,eAAA;;AACA,CAlDF,WAkDE,CAFF,sBAEE;AACE,SAAA;AACA,UAAA;;AAIJ,CAxDA,WAwDA,CAAA;AACE,WAAA;AAIA,mBAAA;AACA,gBAAA;;AAJA,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AAFF,GAxDA,WAwDA,CAAA;AAGI,2BAAA,IAAA;;;AAMJ,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AACE,GAlEF,WAkEE,CAAA;AACE,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;AAEF,GArEF,WAqEE,CAAA;AACE,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;AAEF,GAxEF,WAwEE,CAAA;AACE,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;;AAKF,CA9EF,WA8EE,CAAA,mBAAA;AACE,aAAA;AACA,eAAA;AACA,UAAA;AACA,iBAAA;;AAGF,CArFF,WAqFE,CAAA;AACE,UAAA,IAAA,MAAA,IAAA;AACA,iBAAA,IAAA;;AAIJ,CA3FA,WA2FA,CAAA;AACE,iBAAA,IAAA,MAAA,IAAA;AACA,WAAA,SAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,aAAA;AACA,cAAA;;AAEA,CApGF,WAoGE,CATF,oBASE;AACE,iBAAA;;AAIJ,CAzGA,WAyGA,CAAA;AACE,WAAA;AACA,eAAA;AACA,cAAA;AACA,eAAA;;AAGF,CAhHA,WAgHA,CAAA;AACE,WAAA;AACA,cAAA;AACA,aAAA;AACA,oBAAA,IAAA;AACA,iBAAA,IAAA;AACA,WAAA;AACA,cAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;;;;AC5HN,CAAA;AACE,iBAAA;AACA,oBAAA;AACA,sBAAA,IAAA;AA0IA,4BAAA,IAAA;AACA,iCAAA,IAAA;AACA,8BAAA,IAAA;AACA,yCAAA,IAAA;;AA3IA,CALF,WAKE,CAAA;AACE,WAAA;AACA,SAAA;AACA,mBAAA;AACA,eAAA;AACA,kBAAA;;AAGF,CAbF,WAaE,CAAA;AACE,kBAAA;AACA,WAAA,IAAA;AACA,WAAA;AACA,aAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA,IAAA;AAEA,cAAA;AAEA,cAAA,MAAA,IAAA,eAAA,EAAA,IAAA,aAAA,EAAA,GAAA,EAAA,IAAA,kBAAA,EAAA,CAAA,IAAA,aAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,oBAAA,EAAA;AAMA,cAAA,IAAA;AACA,aAAA,IAAA;;AAKF,CApCF,WAoCE,CAvBA,SAuBA,EAAA;AACE,kBAAA,IAAA;;AAGF,CAxCF,WAwCE,CAAA;AACE,WAAA;AACA,mBAAA;AACA,UAAA,OAAA;AACA,aAAA;AACA,cAAA;;AAGF,CAhDF,WAgDE,CAAA;ACuFA,SAAA,IAAA,aAAA,EAAA;AACA,UAAA,IAAA,aAAA,EAAA;AACA,YAAA;AACA,iBAAA;AACA,kBAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,UAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,QAAA,EAAA,EAAA;ADlGE,oBAAA,IAAA;AACA,UAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,eAAA;;AC8FF,CDtJF,WCsJE,CDtGA,cCsGA;AACE,aAAA,MAAA;;AAGF,CD1JF,WC0JE,CAAA;AACE,SAAA;AACA,UAAA;AACA,iBAAA;;AAGF,CDhKF,WCgKE,CDhHA,cCgHA;AACE,WAAA;AACA,YAAA;AACA,OAAA;AACA,SAAA;AACA,UAAA;AACA,QAAA;AACA,iBAAA;;AAGF,CD1KF,WC0KE,CD1HA,cC0HA,CAAA,WAAA;AACE,gBAAA,IAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;;AAEF,CD9KF,WC8KE,CD9HA,cC8HA,CAAA;AACE,UAAA;;ADpHF,CA3DF,WA2DE,CAAA;AACE,iBAAA;AACA,YAAA;AACA,eAAA;;AAGF,CAjEF,WAiEE,CAAA;AACE,YAAA;AACA,WAAA;AACA,QAAA,EAAA,EAAA;;AACA,CArEJ,WAqEI,CAJF,oCAIE,CAAA;AACE,UAAA;AACA,YAAA;AACA,WAAA;AACA,QAAA,EAAA,EAAA;AACA,UAAA;AACA,SAAA;AACA,SAAA,IAAA;AACA,UAAA,IAAA;;AACA,CA9EN,WA8EM,CAbJ,oCAaI,CATF,mCASE;AACE,QAAA,EAAA,EAAA;AACA,SAAA,IAAA;AACA,UAAA,IAAA;;AAKN,CAtFF,WAsFE,CAAA;AACE,eAAA;AACA,QAAA,EAAA,EAAA;AACA,aAAA;AACA,OAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CA/FF,WA+FE,CAAA,sBAAA,CAAA,SAAA,CAAA;AAEE,SAAA,IAAA;AACA,UAAA,YAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CArGF,WAqGE,CAAA;AACE,WAAA;AACA,aAAA,IAAA;AACA,eAAA;AACA,mBAAA;AACA,SAAA;AACA,WAAA,EAAA;AACA,cAAA;;AAEA,CA9GJ,WA8GI,CATF,sDASE;AACE,SAAA;AACA,UAAA;AAEA,oBAAA;;AAGF,CArHJ,WAqHI,CAhBF,sDAgBE,GAAA;AACE,aAAA,wBAAA,GAAA,OAAA,YAAA;;AAGF,CAzHJ,WAyHI,CApBF,sDAoBE,GAAA;AACE,aAAA,wBAAA,GAAA,MAAA,YAAA;;AAGF,CA7HJ,WA6HI,CAxBF,sDAwBE,GAAA;AACE,aAAA,wBAAA,GAAA,OAAA,YAAA;;AAIJ,WAZI;AAaF;AAEE,eAAA,OAAA;;AAGF;AACE,eAAA,OAAA;;;AASJ,CAlJF,UAkJE,CAAA;AACE,4BAAA,IAAA;AACA,iCAAA,IAAA;AACA,8BAAA,IAAA;AACA,yCAAA,IAAA;;AAGF,CAzJF,WAyJE,CAAA;AACE,OAAA;AACA,cAAA;;;;AE1JF,CAAA,WAAA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AAEA,aAAA;AAEA,UAAA;AAEA,cAAA;;AAEA,CAXF,WAWE,CAXF,KAWE,CAAA;AACE,aAAA;AACA,WAAA;AACA,QAAA,EAAA,EAAA;AACA,WAAA;AACA,iBAAA;AACA,cAAA,IAAA;AACA,SCVa;;ADYb,CApBJ,WAoBI,CApBJ,KAoBI,CATF,UASE;AACE,SAAA;AACA,UAAA;;AAIJ,CA1BF,WA0BE,CA1BF,KA0BE,CAAA;AACE,aAAA;AACA,cAAA;AACA,WAAA,EAAA;AACA,iBAAA;;AAGF,CAjCF,WAiCE,CAjCF,KAiCE,CAAA,WAAA,CAAA;AACE,UAAA;AACA,cAAA;AACA,iBAAA;AACA,oBAAA,IAAA;;AACA,CAtCJ,WAsCI,CAtCJ,KAsCI,CALF,WAKE,CALF,oBAKE;AACE,oBAAA,IAAA;;AAEF,CAzCJ,WAyCI,CAzCJ,KAyCI,CARF,WAQE,CARF,oBAQE;AACE,oBAAA,IAAA;;AAEF,CA5CJ,WA4CI,CA5CJ,KA4CI,CAXF,WAWE,CAXF,qBAWE,CAAA;AACE,SCrCW;;ADwCb,CAhDJ,WAgDI,CAhDJ,KAgDI,CAfF,WAeE,CAfF,qBAeE,CAAA;AACE,mBAAA;;;;AEjDN,CAAA,WAAA,CAAA;AACE,qBAAA,KAAA,IAAA,gBAAA,EAAA;AAEA,cAAA,4KAAA,KAAA;AAEA,cAAA;AACA,WAAA,IAAA;AACA,iBAAA,KAAA,IAAA,gBAAA,EAAA;AAEA,WAAA;AACA,mBAAA;AACA,eAAA;;AAGF,CAdA,WAcA,CAdA,sBAcA;AACE,aAAA,KAAA,KAAA,EAAA,IAAA,mBAAA,EAAA;AACA,cAAA;;AAGF,CAnBA,UAmBA,CAAA,YAAA,CAnBA,sBAmBA;AACE,UAAA;;AAGF,CAvBA,WAuBA,CAAA;AACE,SAAA;AACA,WAAA;AACA,YAAA,KAAA,IAAA,gBAAA,EAAA;AACA,eAAA;AACA,mBAAA;;AAIA,CAAA,kBAAA,CAhCF,WAgCE,CAAA;AACE,WAAA;AACA,kBAAA;;AAGF,CALA,kBAKA,CArCF,WAqCE,CAdF;AAeI,kBAAA;AACA,eAAA;;AAGF,CAVA,kBAUA,CA1CF,WA0CE,CAnBF,sBAmBE,EAAA;AACE,iBAAA,KAAA,IAAA,gBAAA,EAAA;;AAGF,CAdA,kBAcA,CA9CF,WA8CE,CA9CF,sBA8CE;AACE,cAAA;;AAGF,CAlBA,kBAkBA,CAlDF,WAkDE,CAAA;AAAA,CAlBA,kBAkBA,CAlDF,WAkDE,CAAA,qBAAA,CAAA;AAEE,UAAA;AACA,cAAA;;AAGF,CAxBA,kBAwBA,CAxDF,WAwDE,CANA,qBAMA,CANA;AAOE,cAAA;;AAKF,CA9DF,WA8DE,CAAA,mBAAA,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,iBAAA;AACA,WAAA;;AAEA,OAAA,CAAA,SAAA,EAAA;AANF,GA9DF,WA8DE,CAAA,mBAAA,CAAA;AAOI,2BAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;;AACA,GAtEN,WAsEM,CARJ,mBAQI,CARJ,mBAQI,CAAA;AACE,gBAAA;;;AAIJ,CA3EJ,WA2EI,CAbF,mBAaE,CAbF,mBAaE,CAAA;AACE,SAAA;AACA,UAAA,IAAA;AACA,eAAA;AACA,kBAAA;;AAEA,CAjFN,WAiFM,CAnBJ,mBAmBI,CAnBJ,mBAmBI,CANF,YAME,CAAA;AACE,SAAA;;AAIJ,CAtFJ,WAsFI,CAxBF,mBAwBE,CAxBF,mBAwBE,CAAA;AACE,UAAA,QAAA;AACA,eAAA;;AAKN,CA7FA,WA6FA,MAAA,CAAA;AACE,UAAA;AAEA,SAAA;AACA,UAAA;AACA,UAAA,EAAA;AACA,WAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AAEA,iBAAA;AACA,oBAAA,IAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,IAAA,KAAA,EAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAGA,eAAA;AACA,aAAA;AACA,SCvGe;;ADyGf,CAjHF,WAiHE,MAAA,CApBF,8BAoBE;AACE,oBAAA,IAAA;;AAEF,CApHF,WAoHE,MAAA,CAvBF,8BAuBE;AACE,oBAAA,IAAA;AACA,cAAA;;AAGF,CAzHF,WAyHE,MAAA,CA5BF,+BA4BE;AACE,SAAA;;;;AE1HJ,CAAA,WAAA,CAAA;AC8DA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;AACA,cAAA;AACA,gBAAA;AACA,gBAAA;AACA,gBAAA,IAAA,eAAA,EAAA,IAAA;AACA,iBAAA,IAAA;AACA,UAAA;AACA,oBAAA,IAAA,WAAA,EAAA,IAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,eAAA,IAAA;AAuGA,UAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;AACA,oBAAA,IAAA;ADjLE,SAAA;AACA,UAAA,IAAA;AAEA,WAAA;AACA,eAAA;AACA,OAAA;AAEA,eAAA;AAEA,aAAA;AACA,kBAAA;;ACgEF,CD9EA,WC8EA,CD9EA,gBC8EA;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;;AAGF,CDnFA,WCmFA,CDnFA,eCmFA;AACE,oBAAA,IAAA,iBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,qBAAA,EAAA,IAAA,eAAA,EAAA,IAAA;AAIA,SAAA,IAAA,oBAAA,EAAA,IAAA,cAAA,EAAA,IAAA,oBAAA,EAAA;;AAMF,CD/FA,WC+FA,CD/FA,eC+FA;AACE,oBAAA,IAAA,kBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,sBAAA,EAAA,IAAA;;AAGF,CDpGA,WCoGA,CDpGA,eCoGA,CAAA;AACE,oBAAA,IAAA,oBAAA,EAAA,IAAA;AAIA,gBAAA,IAAA,wBAAA,EAAA,IAAA;;AAKA,CD9GF,WC8GE,CD9GF,eC8GE,CAVF,MAUE;AACE,oBAAA,IAAA,0BAAA,EAAA,IAAA;;AAMF,CDrHF,WCqHE,CDrHF,eCqHE,CAjBF,OAiBE;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;;AAiEJ,CDvLA,WCuLA,CDvLA,eCuLA;AACE,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;;ADxKA,CAhBF,WAgBE,CAhBF,gBAgBE;AACE,SAAA,IAAA;AACA,UAAA,IAAA;;AAGF,CArBF,WAqBE,CAAA;AACE,cAAA;;AAIJ,CA1BA,WA0BA,CAAA,wBAAA,CAAA;AACE,WAAA;AACA,eAAA;;AAGF,CA/BA,UA+BA,CAAA,mBAAA,CALA,wBAKA,CALA;AAME,WAAA;;;;AE/BF,CAAA,WAAA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,YAAA;AACA,OAAA;AACA,UAAA;AACA,SAAA;AACA,WAAA;AACA,UAAA;AACA,WAAA;AACA,cAAA;AAEA,oBAAA,IAAA;AACA,cAAA,IAAA;AAEA,kBAAA,IAAA;AAWA,YAAA;AACA,iBAAA;AACA,SAAA,KAAA,IAAA,uBAAA,EAAA,IAAA,gBAAA,EAAA;AAEA,eAAA,IAAA,MAAA,IAAA;;AAbA,KAAA,CAAA,SAAA,CAjBF,WAiBE,CAjBF;AAkBI,QAAA;AACA,SAAA;;AAGF,CAtBF,WAsBE,CAAA;AACE,cAAA;;AASF,KAAA,CAAA,SAAA,CAhCF,WAgCE,CAhCF;AAiCI,gBAAA,IAAA,MAAA,IAAA;AACA,eAAA;;AAMJ,CAxCA,WAwCA,CAAA;AACE,cAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,SAAA;AACA,WAAA,KAAA;AACA,YAAA;;AAEA,CAjDF,WAiDE,CATF,eASE;AACE,WAAA;AACA,SAAA,KAAA,KAAA,EAAA;AACA,UAAA;AACA,cAAA,IAAA;AACA,YAAA;AACA,UAAA;;AAIJ,CA3DA,WA2DA,CAAA;AACE,OAAA;AACA,WAAA;AACA,eAAA;AACA,eAAA;;AAEA,CAjEF,WAiEE,CANF,yBAME;ACJF,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;AACA,cAAA;AACA,gBAAA;AACA,gBAAA;AACA,gBAAA,IAAA,eAAA,EAAA,IAAA;AACA,iBAAA,IAAA;AACA,UAAA;AACA,oBAAA,IAAA,WAAA,EAAA,IAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,eAAA,IAAA;ADRI,eAAA;AACA,UAAA;AAEA,SAAA,IAAA;AACA,UAAA,IAAA;AACA,WAAA;;ACKJ,CD7EA,WC6EA,CDlBA,yBCkBA,OAAA;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;;AAGF,CDlFA,WCkFA,CDvBA,yBCuBA,MAAA;AACE,oBAAA,IAAA,iBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,qBAAA,EAAA,IAAA,eAAA,EAAA,IAAA;AAIA,SAAA,IAAA,oBAAA,EAAA,IAAA,cAAA,EAAA,IAAA,oBAAA,EAAA;;AAMF,CD9FA,WC8FA,CDnCA,yBCmCA,MAAA;AACE,oBAAA,IAAA,kBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,sBAAA,EAAA,IAAA;;AAGF,CDnGA,WCmGA,CDxCA,yBCwCA,MAAA,CAAA;AACE,oBAAA,IAAA,oBAAA,EAAA,IAAA;AAIA,gBAAA,IAAA,wBAAA,EAAA,IAAA;;AAKA,CD7GF,WC6GE,CDlDF,yBCkDE,MAAA,CAVF,MAUE;AACE,oBAAA,IAAA,0BAAA,EAAA,IAAA;;AAMF,CDpHF,WCoHE,CDzDF,yBCyDE,MAAA,CAjBF,OAiBE;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;;AD3CA,CA1EJ,WA0EI,CAfJ,yBAeI,OAAA;AACE,SAAA,IAAA;AACA,UAAA,IAAA;;AAGF,CA/EJ,WA+EI,CApBJ,yBAoBI,MAAA;AACE,cAAA,IAAA,iBAAA,EAAA,IAAA;;AAKF,CArFJ,WAqFI,CA1BJ,yBA0BI,CAAA,aAAA,CAAA,SAAA;AACE,UAAA,IAAA;AACA,QAAA,IAAA;;AAON,CA9FA,WA8FA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,QAAA,EAAA,EAAA;AACA,WAAA,KAAA;;AAEA,CApGF,WAoGE,CANF,kBAME,CAAA;AACE,QAAA;AACA,WAAA;AAEA,QAAA,EAAA,EAAA;AACA,WAAA;AACA,kBAAA;AACA,WAAA;;AAGF,CA9GF,WA8GE,CAhBF,kBAgBE,CAAA,cAAA,CAAA;AACE,WAAA;;AAGF,CAlHF,WAkHE,CApBF,kBAoBE,CAAA;AACE,WAAA;AACA,OAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,CAAA,EAAA;;AAIJ,CAzHA,WAyHA,CA3BA,kBA2BA,EAAA,CAjFA;AAkFE,eAAA;AACA,kBAAA;;AAGF,CA9HA,WA8HA,CAAA;AACE,kBAAA;AACA,eAAA;AACA,qBAAA;AACA,sBAAA,IAAA;AACA,wBAAA,IAAA;AACA,yBAAA,IAAA;;AAEA,CAtIF,WAsIE,CARF,mBAQE,CAAA;AACE,eAAA,IAAA;AACA,qBAAA,IAAA;AACA,wBAAA,IAAA;AACA,mBAAA,IAAA;AACA,SAAA,IAAA;;AAMJ,CAjJA,WAiJA,CAAA;AACE,WAAA;AACA,kBAAA;;AAEA,CArJF,WAqJE,CAJF,gBAIE,CAAA;AAGE,WAAA;AACA,OAAA;AACA,WAJU;AAOV,cAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,cAAA,IAAA;AACA,iBAAA;;AAEA,CAnKJ,WAmKI,CAlBJ,gBAkBI,CAdF,iBAcE,CArCJ;AAsCM,UAAA,IAAA;AACA,SAAA,IAAA;AAEA,UAAA;;;;AEzKJ,CAAA,WAAA,CAAA,WAAA,CAAA;AACE,WAAA;AACA,eAAA;;AAEA,CAJF,WAIE,CAJF,WAIE,CAJF,qBAIE;AACE,iBAAA;AACA,YAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAXF,WAWE,CAXF,WAWE,CAXF,qBAWE;AACE,SAAA;AACA,qBAAA;AACA,aAAA,OAAA;;;;ACbN,CAAA,WAAA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,OAAA;AACA,aAAA;;AAEA,CAPF,WAOE,CAPF,iBAOE;AACE,UAAA;AAEA,eAAA;AACA,aAAA;AACA,eAAA;AACA,cAAA;AAEA,SAAA,IAAA;;AAGF,CAlBF,WAkBE,CAAA;AACE,cAAA;AAEA,WAAA;AACA,kBAAA;AACA,eAAA;AACA,SAAA;AACA,OAAA;AAOA,WAAA;AAEA,cAAA,IAAA;AACA,iBAAA;AAEA,eAAA;AACA,cAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AAEA,SAAA,IAAA;;AC3CK,CAAA,kBAAA,CDAT,WCAS,CDkBP;AAUI,kBAAA;AACA,cAAA;;AAgBF,CA7CJ,WA6CI,CAAA;AACE,aAAA;;AAGF,CAjDJ,WAiDI,CAAA;AACE,cAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,iBAAA;AACA,cAAA,IAAA;AACA,SAAA;AACA,UAAA;AAEA,WAAA;;AAEA,CA7DN,WA6DM,CAZF,oCAYE;AACE,SAAA,IAAA;AACA,SAAA;AACA,UAAA;;AAIJ,CApEJ,WAoEI,CAlDF,6BAkDE,CAAA;AACE,cAAA,IAAA;AACA,SAAA,IAAA;;AAEA,CAxEN,WAwEM,CAtDJ,6BAsDI,CAJF,6CAIE,CAvBF;AAwBI,cAAA,IAAA;AACA,QAAA,EAAA,EAAA;;AAEA,CA5ER,WA4EQ,CA1DN,6BA0DM,CARJ,6CAQI,CA3BJ,oCA2BI;AACE,SAAA,IAAA;;AAMR,CAnFF,WAmFE,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,iBAAA;AACA,mBAAA;AACA,OAAA;;ACzFK,CAAA,kBAAA,CDAT,WCAS,CDmFP;AASI,kBAAA;;AAGF,CA/FJ,WA+FI,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,WAAA;AACA,OAAA;AACA,cAAA;AACA,aAAA;;AAEA,CAxGN,WAwGM,CAAA;AACE,UAAA;AACA,aAAA;AACA,cAAA;;AAGF,CA9GN,WA8GM,CAfF,kCAeE;AACE,eAAA;AACA,aAAA;AACA,eAAA;AAEA,UAAA;AAEA,SAAA,IAAA;;;;AErHR,CAAA,WAAA,CAAA;AACE,QAAA,EAAA,EAAA;AACA,WAAA;AACA,kBAAA;AACA,WAAA,IAAA,EAAA,EAAA;;AAGF,CAPA,WAOA,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA,EAAA;;AACA,CAZF,WAYE,CALF,wBAKE,CAAA;AACE,QAAA,EAAA,EAAA;;AAGF,CAhBF,WAgBE,CATF,wBASE,CAAA;AACE,oBAAA;AAKA,iBAAA,IAAA;AACA,UAAA;;AALS,CAlBb,UAkBa,CAAA,WAAA,CAlBb,WAkBa,CAXb,wBAWa,CAFX;AAGI,oBAAA;;AAMF,CAzBJ,WAyBI,CAlBJ,wBAkBI,CATF,oBASE,KAAA;AACE,aAAA;;AAKN,CA/BA,WA+BA,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA,IAAA,IAAA,EAAA;AACA,UAAA,EAAA,QAAA,QAAA;AACA,aAAA;;AAEA,CAvCF,WAuCE,CARF,uBAQE,CAAA;AACE,WAAA;;AAEA,CA1CJ,WA0CI,CAXJ,uBAWI,CAHF,WAGE,CAAA;AACE,SAAA;AACA,UAAA;AACA,mBAAA;;AAEA,CA/CN,WA+CM,CAhBN,uBAgBM,CARJ,WAQI,CALF,cAKE;AACE,oBAAA,IAAA;;AAGF,CAnDN,WAmDM,CApBN,uBAoBM,CAZJ,WAYI,CATF,cASE;AACE,gBAAA;;AAMR,CA1DA,WA0DA,CAAA;AACE,cAAA;AACA,QAAA,EAAA,EAAA;AACA,WAAA;AACA,kBAAA;AAEA,OAAA;;AAGF,CAnEA,WAmEA,CAAA;AACE,WAAA;AACA,eAAA;AACA,cAAA;AACA,QAAA,EAAA,EAAA;AACA,WAAA,QAAA;AACA,UAAA;AACA,UAAA,IAAA,MAAA;AACA,WAAA;AAEA,UAAA,EAAA;AACA,iBAAA,IAAA;;AAEA,CAhFF,WAgFE,CAbF,sBAaE,CAAA;AACE,SAAA;AACA,UAAA;AACA,gBAAA;;AAGF,CAtFF,WAsFE,CAnBF,sBAmBE,CAAA;AACE,QAAA;AACA,cAAA;AACA,eAAA;AACA,YAAA;AACA,iBAAA;AACA,cAAA;;AAGF,CA/FF,WA+FE,CA5BF,qBA4BE;AACE,oBAAA,IAAA;;AAEF,CAlGF,WAkGE,CA/BF,qBA+BE;AACE,gBAAA,IAAA;;AAGF,CAtGF,WAsGE,CAnCF,qBAmCE,CAAA;AACE,oBAAA,IAAA;;;;ACxGF,CAAA,WAAA,CAAA;AACE,gBAAA;AACA,aAAA;AACA,eAAA;AACA,kBAAA;;;;ACDJ,CAAA,WAAA,CAAA,KAAA,CAAA,MAAA,CAAA;AACE,WAAA;;AAEA,CAHF,WAGE,CAHF,KAGE,CAHF,MAGE,CAHF,UAGE,CAAA;AACE,cAAA;;AAGF,CAPF,WAOE,CAPF,KAOE,CAPF,MAOE,CAPF,WAOE,CAAA;AACE,kBAAA;AACA,UAAA;AACA,WAAA;AACA,kBAAA;AACA,QAAA,EAAA,EAAA;AACA,cAAA;;AAGF,CAhBF,WAgBE,CAhBF,KAgBE,CAhBF,MAgBE,CAhBF,WAgBE,CAAA;AACE,UAAA;AACA,cAAA;;AAEA,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AAJF,GAhBF,WAgBE,CAhBF,KAgBE,CAhBF,MAgBE,CAhBF,WAgBE,CAAA;AAKI,gBAAA;AACA,YAAA;;;AAIJ,CA1BF,WA0BE,CA1BF,KA0BE,CA1BF,MA0BE,CA1BF,WA0BE,CAAA;AACE,QAAA,EAAA,EAAA;;AAIJ,CA/BA,WA+BA,CAAA;AACE,aAAA;AACA,cAAA;AACA,eAAA;AACA,iBAAA;;AAGF,CAtCA,WAsCA,CAAA;AACE,SAAA;AACA,UAAA;AACA,WAAA;AACA,kBAAA;;AAGF,CA7CA,WA6CA,CAAA;AACE,SAAA,IAAA;AACA,aAAA;AACA,UAAA;AACA,WAAA,EAAA;AACA,oBAAA;AACA,UAAA;AACA,UAAA;AACA,eAAA;AACA,eAAA;AACA,kBAAA;;AAEA,CAzDF,WAyDE,CAZF,sBAYE,CAAA;AACE,iBAAA,IAAA,MAAA,IAAA;;AAIJ,CA9DA,WA8DA,CAAA;AACE,iBAAA,IAAA,MAAA,IAAA;AACA,iBAAA;AACA,kBAAA;;AAGF,CApEA,WAoEA,CAAA;AACE,kBAAA;AACA,UAAA;AACA,WAAA;AACA,kBAAA;;AAEA,CA1EF,WA0EE,CANF,kBAME,CAAA;AACE,WAAA;;AAIJ,CA/EA,WA+EA,CAAA;AACE,SAAA;AACA,UAAA;AACA,UAAA;AACA,iBAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,eAAA;AACA,WAAA;AACA,cAAA;AACA,eAAA;;AAEA,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AAXF,GA/EA,WA+EA,CAAA;AAYI,WAAA;AACA,YAAA;;;AAIJ,CAhGA,WAgGA,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA;AACA,cAAA;AACA,aAAA;AACA,YAAA;AAEA,cAAA,4KAAA,KAAA;AAEA,iBAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;AAEA,UAAA;AACA,SAAA;;AAEA,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AAjBF,GAhGA,WAgGA,CAAA;AAkBI,WAAA;AAEA,YAAA;;;AAGF,CAvHF,WAuHE,CAvBF,0BAuBE;AACE,aAAA;AACA,cAAA;;AAIJ,CA7HA,WA6HA,CAAA;AACE,WAAA;AACA,SAAA;AACA,UAAA;AACA,eAAA;AACA,mBAAA;AACA,aAAA;;AAGF,CAtIA,WAsIA,CAAA;AACE,SAAA;AACA,eAAA;AACA,aAAA;AACA,cAAA;AACA,YAAA;AACA,cAAA;AACA,UAAA;AACA,SAAA;AACA,cAAA;AACA,YAAA;AACA,WAAA;;AAEA,CAnJF,WAmJE,CAbF,wBAaE;AACE,eAAA;AACA,eAAA;AACA,cAAA;AACA,eAAA;AACA,aAAA;AACA,WAAA,EAAA;;AAIJ,CA7JA,WA6JA,CAAA;AACE,UAAA;;AAEA,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AAHF,GA7JA,WA6JA,CAAA;AAII,aAAA;AACA,2BAAA,IAAA;AACA,SAAA;;;AAIJ,CAvKA,WAuKA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,SAAA;;AAEA,CA5KF,WA4KE,CAAA;AACE,WAAA;AACA,UAAA,IAAA,IAAA,IAAA;AACA,eAAA;AACA,OAAA;;AAEA,CAlLJ,WAkLI,CANF,yBAME;AACE,aAAA;AACA,cAAA;AACA,eAAA;;AAKF,CA1LJ,WA0LI,CAnBJ,gBAmBI,aAAA,CAAA,iCAAA,KAAA,CAAA;AACE,iBAAA;;AAIJ,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AACE,GAhMJ,WAgMI,CAzBJ,iBAyBI,CANA,iCAMA,KAAA,CANA;AAOE,mBAAA;;;AAIJ,CArMF,WAqME,CA9BF,iBA8BE;AACE,UAAA;AACA,UAAA;AACA,iBAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,eAAA;AACA,WAAA;AACA,cAAA;AACA,SAAA;AACA,eAAA;;AAEA,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AAXF,GArMF,WAqME,CA9BF,iBA8BE;AAYI,WAAA;AACA,YAAA;;;AAKN,CAvNA,WAuNA,CA7BI;AA8BF,cAAA;AACA,iBAAA;;AAGE,CA5NJ,WA4NI,CAlCA,iCAkCA,CAlCA,UAkCA,CAAA;AACE,WAAA;;AAEA,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AAHF,GA5NJ,WA4NI,CAlCA,iCAkCA,CAlCA,UAkCA,CAAA;AAII,aAAA;AACA,gBAAA;;;AAMR,CAvOA,WAuOA,CAXI;AAgDF,YAAA;;AApCA,CAxOF,WAwOE,CAZE,uBAYF,CAAA;AACE,eAAA;AACA,eAAA;AACA,UAAA;AAEA,aAAA;AACA,SCxOa;ADyOb,oBAAA,IAAA;AACA,SAAA;;AAEA,CAlPJ,WAkPI,CAtBA,uBAsBA,CAVF,iBAUE;AACE,oBAAA,IAAA;;AAEF,CArPJ,WAqPI,CAzBA,uBAyBA,CAbF,iBAaE;AACE,oBAAA,IAAA;;AAGF,CAzPJ,WAyPI,CA7BA,uBA6BA,CAjBF,iBAiBE;AACE,WAAA;AACA,UAAA;;AAEA,CA7PN,WA6PM,CAjCF,uBAiCE,CArBJ,iBAqBI,SAAA;AACE,oBAAA,IAAA;;AAIJ,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AA1BF,GAxOF,WAwOE,CAZE,uBAYF,CAAA;AA2BI,WAAA;AACA,eAAA;;;AAGO,CAvQb,UAuQa,CAAA,WAAA,CAvQb,WAuQa,CA3CT,uBA2CS,CA/BX;AAgCI,SAAA,IAAA;;AAMJ,CA9QF,WA8QE,CAlDE,wBAkDF;AACE,WAAA;;AAEA,CAjRJ,WAiRI,CArDA,wBAqDA,GAAA,CAvFA;AAwFE,cAAA;;AAGF,CArRJ,WAqRI,CAzDA,wBAyDA,GAAA,CAAA;AACE,WAAA;AACA,YAAA;AACA,SAAA;AAEA,mBAAA;;AAES,CA5Rf,UA4Re,CArBF,WAqBE,CA5Rf,WA4Re,CAhEX,wBAgEW,GAAA,CAPX;AAQI,mBAAA,IAAA;;AAIJ,CAjSJ,WAiSI,CArEA,wBAqEA,IAAA;AACE,gBAAA;AACA,WAAA;;AAKN,CAxSA,WAwSA,CAAA;AACE,uBAAA;AACA,aAAA;AACA,WAAA;AACA,WAAA;AACA,OAAA;;AAEA,CA/SF,WA+SE,CAAA;AACE,UAAA,IAAA,MAAA;AACA,WAAA,IAAA;AACA,iBAAA;;;;AEtTJ,CAAA,WAAA,CAAA;AACE,WAAA;AACA,SAAA;;AAEA,CAJF,WAIE,CAJF,oBAIE;AACE,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;AACA,iBAAA,IAAA;;AAIJ,CAVA,WAUA,CAAA;AACE,WAAA;AACA,kBAAA;;AAGF,CAfA,WAeA,CAAA;AACE,eAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,gBAAA;AACA,WAAA,EAAA,OAAA,EAAA;AACA,aAAA;AACA,UAAA;AACA,cAAA;AACA,SAAA,IAAA;AAYA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,YAAA;;AAbA,KAAA,CAAA,SAAA,CAzBF,WAyBE,CAVF;AAWI,iBAAA,IAAA,oBAAA,EAAA,EAAA,IAAA;;AAGF,KAAA,CAAA,SAAA,CA7BF,WA6BE,CAdF;AAeI,iBAAA,EAAA,IAAA,oBAAA,IAAA,oBAAA;AACA,gBAAA,IAAA,MAAA,IAAA;AACA,eAAA;;AASJ,CAzCA,WAyCA,CAAA;AACE,cAAA;AACA,SAAA;AACA,UAAA;AACA,aAAA;AACA,eAAA;AACA,oBAAA;AACA,SAAA,IAAA;AACA,UAAA;AACA,WAAA;AACA,UAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,eAAA;AACA,kBAAA;AAYA,WAAA;AACA,gBAAA;AACA,cAAA;;AAZA,KAAA,CAAA,SAAA,CAxDF,WAwDE,CAfF;AAgBI,iBAAA,EAAA,IAAA,oBAAA,IAAA,oBAAA;;AAGF,KAAA,CAAA,SAAA,CA5DF,WA4DE,CAnBF;AAoBI,iBAAA,IAAA,oBAAA,EAAA,EAAA,IAAA;AACA,eAAA,IAAA,MAAA,IAAA;AACA,gBAAA;;AAOF,CAtEF,WAsEE,CA7BF,UA6BE;AACE,cAAA;;;;ACxEN,CAAA;AACE,SAAA;AACA,YAAA;AACA,OAAA;AACA,aAAA;AACA,WAAA,IAAA;AACA,kBAAA,IAAA;;AAEA,KAAA,CAAA,SAAA,CARF;AASI,QAAA;AACA,SAAA;;AAGF,CAbF,UAaE;AACE,aAAA;AACA,sBAAA;AACA,oBAAA;AACA,eAAA;;AAEF,CAnBF,UAmBE;AACE,eAAA;AACA,aAAA;AACA,UAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AAEA,WAAA;AACA,OAAA;;AAEA,CARF,eAQE,IAAA,EAAA;AACE,cAAA;;AAIJ,CAAA;AACE,cAAA;AACA,eAAA;AACA,UAAA,QAAA;;AAGF,CAnDF,UAmDE,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;;AAEA,CAzDJ,UAyDI,CANF,MAME;AACE,UAAA;;AAIJ,CA9DF,UA8DE,CAAA;AACE,UAAA;AACA,SAAA;AACA,UAAA;;AACA,CAlEJ,UAkEI,CAJF,MAIE;AACE,SAAA;AACA,UAAA;;;;ACjEJ,CAAA,WAAA,CAAA;AACE,YAAA;AACA,OAAA,IAAA;AACA,QAAA,IAAA;AAEA,WAAA,IAAA;AAEA,iBAAA;AACA,WAAA;AAEA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,cAAA,IAAA;AACA,oBAAA,IAAA;;ACdO,CAAA,kBAAA,CDAT,WCAS,CDAT;AAiBI,QAAA;AACA,eAAA;AACA,gBAAA;AACA,SAAA,KAAA,KAAA,EAAA;AACA,cAAA;AACA,WAAA;;AAGF,CAzBF,WAyBE,CAzBF,kBAyBE,CAAA;AAkBE,iBAAA;;AAjBA,CA1BJ,WA0BI,CA1BJ,kBA0BI,CADF,0BACE;AACE,cAAA;AACA,iBAAA;;AC5BG,CAAA,kBAAA,CDAT,WCAS,CDAT,kBCAS,CDyBP,0BCzBO;AD+BD,aAAA;;AAIJ,CAnCJ,WAmCI,CAnCJ,kBAmCI,CAVF,0BAUE;AACE,UAAA;;ACpCG,CAAA,kBAAA,CDAT,WCAS,CDAT,kBCAS,CDyBP,0BCzBO;ADuCD,aAAA;;ACvCC,CAAA,kBAAA,CDAT,WCAS,CDAT,kBCAS,CDyBP;AAqBI,iBAAA;;AAIJ,CAlDF,WAkDE,CAlDF,kBAkDE,CAAA;AACE,WAAA;;AAEA,CArDJ,WAqDI,CArDJ,kBAqDI,CAHF,yBAGE,CAAA;AACE,QAAA;;AAGF,CAzDJ,WAyDI,CAzDJ,kBAyDI,CAPF,yBAOE,CAAA;AACE,SEOG;AFNH,eAAA;;AAEA,CA7DN,WA6DM,CA7DN,kBA6DM,CAXJ,yBAWI,CAJF,0BAIE,CAAA;AACE,SAAA;AACA,UAAA;;AAGF,CAlEN,WAkEM,CAlEN,kBAkEM,CAhBJ,yBAgBI,CATF,0BASE,CALA,eAKA;AACE,SELC;;AFUP,CAxEF,WAwEE,CAxEF,kBAwEE,CAAA;AACE,WAAA;AACA,mBAAA;AACA,cAAA;;AC3EK,CAAA,kBAAA,CDAT,WCAS,CDAT,kBCAS,CDwEP;AAMI,aAAA;AACA,cAAA;;;;AG9EN,CAAA,WAAA,CAAA,iBAAA,CAAA;AACE,cAAA,MAAA,KAAA;;AAEF,CAHA,WAGA,CAHA;AASE,YAAA;AACA,SAAA;AACA,UAAA;AACA,kBAAA;AACA,WAAA,IAAA;;AAEA,CAfF,WAeE,CAAA;AACE,WAAA;AACA,SAAA;AACA,mBAAA;AACA,OAAA;AACA,kBAAA;;AAEA,CAtBJ,WAsBI,CAPF,6BAOE,EAAA;AACE,kBAAA,IAAA;;AAIJ,CA3BF,WA2BE,CAAA;AACE,SAAA;;AAEA,CA9BJ,WA8BI,CAAA;AACE,WAAA;AACA,WAAA;;AAIJ,CApCF,WAoCE,CApCF,kBAoCE,CAAA;AACE,cAAA,UAAA,KAAA;;AAEA,KAAA,CAAA,SAAA,CAvCJ,WAuCI,CAvCJ,kBAuCI,CAHF,mBAGE,CAAA;AACE,aAAA,UAAA,MAAA,EAAA;;AAGF,KAAA,CAAA,SAAA,CA3CJ,WA2CI,CA3CJ,kBA2CI,CAPF,mBAOE,CAAA;AACE,aAAA,UAAA,KAAA,EAAA;;AAGF,KAAA,CAAA,SAAA,CA/CJ,WA+CI,CA/CJ,kBA+CI,CAXF,mBAWE,CARA;AASE,aAAA,UAAA,KAAA,EAAA;;AAGF,KAAA,CAAA,SAAA,CAnDJ,WAmDI,CAnDJ,kBAmDI,CAfF,mBAeE,CARA;AASE,aAAA,UAAA,MAAA,EAAA;;AAGF,CAvDJ,WAuDI,CAvDJ,kBAuDI,CAnBF,mBAmBE,CAAA;AACE,aAAA,UAAA,CAAA,EAAA;;AAIJ,CA5DF,WA4DE,CA5DF,kBA4DE,CAAA;AACE,WAAA;AACA,YAAA;AACA,UAAA;AAOA,WAAA;AACA,cAAA;AACA,cAAA,WAAA,GAAA,OAAA,EAAA,EAAA,QAAA;AAEA,eAAA,IAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AAEA,iBAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,oBAAA,IAAA;AACA,SAAA,IAAA;;AAlBA,CAAA,SAAA,CAhEJ,WAgEI,CAhEJ,kBAgEI,CAJF;AAKI,SAAA;;AAEF,CAAA,SAAA,CAnEJ,WAmEI,CAnEJ,kBAmEI,CAPF;AAQI,QAAA;;AAgBF,CApFJ,WAoFI,CApFJ,kBAoFI,CAxBF,gBAwBE;AACE,oBAAA,IAAA;;AAEF,CAvFJ,WAuFI,CAvFJ,kBAuFI,CA3BF,gBA2BE;AACE,gBAAA,IAAA;;AAGF,CA3FJ,WA2FI,CA3FJ,kBA2FI,CAAA;AACE,WAAA;AACA,cAAA;AACA,cAAA,WAAA,GAAA,OAAA,KAAA,EAAA,QAAA;AACA,oBAAA;AAEA,kBAAA,IAAA;;AAOF,CAxGJ,WAwGI,CAxGJ,kBAwGI,CAAA,+BAAA,EAAA;AAAA,CAxGJ,WAwGI,CAxGJ,kBAwGI,CAAA,cAAA,EAAA;AAAA,CAxGJ,WAwGI,CAxGJ,kBAwGI,CA1EA,gCA0EA,EAAA;AACE,kBAAA,IAAA;;AAIJ,CA7GF,WA6GE,CA7GF,kBA6GE,CA/EE;AAgFA,cAAA;AACA,iBAAA;;;;AC7GA,CAAA,WAAA,CAAA,WAAA,CAAA,SAAA,CAAA;AAAA,CAAA,WAAA,CAAA,WAAA,CAAA,SAAA,CAAA;AAEE,WAAA;;AAIJ,CANE,WAMF,CAAA;AACE,SAAA;AACA,UAAA;AACA,cAAA;AACA,oBAAA,IAAA;AACA,UAAA,EAAA;;ACdK,CAAA,kBAAA,CDGL,WCHK,CDSP;AAQI,UAAA;;AAKN,CAnBI,WAmBJ,CAAA;AACE,cAAA;AACA,UAAA;AACA,oBAAA;;AAEA,CAxBE,WAwBF,CALF,gCAKE;AACE,oBAAA,IAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA,sBAAA,EAAA,IAAA,0BAAA;;AAIF,CA9BE,WA8BF,CAAA;AAAA,CA9BE,WA8BF,CAAA,0CAAA;AAEE,cAAA,IAAA;AACA,SAAA,IAAA;;AAIJ,CArCI,WAqCJ,CAAA;AACE,cAAA;AACA,SAAA;AACA,aAAA;AACA,WAAA;;;;AE5CF,CAAA,WAAA,CAAA;AAIE,aAAA,QAAA;AACA,oBAAA,IAAA;AACA,iBAAA;AACA,UAAA;AACA,cAAA;AACA,UAAA;AACA,QAAA;AACA,eAAA;AACA,WAAA,IAAA;AACA,YAAA;AACA,cAAA;AACA,SAAA;AACA,WAAA;;AAEA,CAlBF,WAkBE,CAlBF,MAkBE,CAAA;AACE,WAAA,EAAA;AACA,SAAA,IAAA;AACA,eAAA;;AAGF,CAxBF,WAwBE,CAxBF,MAwBE,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,WA1BmB;;AA4BnB,CA9BJ,WA8BI,CA9BJ,MA8BI,CANF,MAME,CAAA;AACE,SA9Bc;AA+Bd,UA/Bc;;AAoCpB,WAjCE;AAkCA;AACE,aAAA;;AAEF;AACE,aAAA;;;;;AC1CJ,CAAA,WAAA,CAAA;AACE,kBAAA;AACA,SAAA;AACA,UAAA;AACA,YAAA;AACA,OAAA;AACA,QAAA;AAEA,WAAA,IAAA;;AAEA,CAVF,WAUE,CAVF,SAUE;AACE,mBAAA;AACA,YAAA;AACA,YAAA;AACA,SAAA;AACA,UAAA;AACA,OAAA;AACA,QAAA;;;;ACnBJ,CAAA,WAAA,CAAA;AACE,YAAA;AAEA,cAAA,IAAA,IAAA,IAAA,EAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA,IAAA;AACA,cAAA,IAAA;AACA,iBAAA,IAAA;AAEA,WAAA;AACA,kBAAA;AACA,OAAA;;;;ACVF,CAAA,WAAA,CAAA;AACE,YAAA;AACA,cAAA;AACA,kBAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,WAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;;AAEA,CAVF,WAUE,CAAA;AACE,oBAAA,IAAA;AACA,SAAA,IAAA;AACA,WAAA,QAAA;AACA,iBAAA;AACA,iBAAA;AACA,kBAAA;AACA,aAAA;AACA,WAAA;AACA,OAAA;AACA,eAAA;;AAEA,CAtBJ,WAsBI,CAAA;AACE,WAAA;AACA,eAAA;AACA,eAAA;;AAGF,CA5BJ,WA4BI,CAAA;AACE,WAAA;AACA,eAAA;AACA,YAAA;AACA,iBAAA;AACA,aAAA;;AAIJ,CArCF,WAqCE,CAAA;AACE,OAAA;AACA,UAAA;AACA,iBAAA;;AAEA,CA1CJ,WA0CI,CALF,2BAKE;AACE,oBAAA,IAAA;;AAGF,CA9CJ,WA8CI,CATF,2BASE;AACE,oBAAA,IAAA;;AAGF,CAlDJ,WAkDI,CAbF,4BAaE;AACE,WAAA;AACA,SAAA;AACA,UAAA;;;;ACpDR,CAAA;AACE,YAAA;AACA,UAAA;AACA,SAAA;AACA,YAAA;AACA,QAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,eAAA;AACA,eAAA;;AAGF,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,UAAA;AACA,QAAA;AACA,WAAA;AACA,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,kBAAA;;AAEA,CAbF,eAaE,CAAA;AACE,aAAA;;AAGF,CAjBF,eAiBE,CAAA;AACE,cAAA;AACA,aAAA;;AAIJ,CAAA;AACE,oBAAA;AACA,SAAA;;;;AClCF,CAAA;AACE,kBAAA;AACA,iCAAA;AACA,8BAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,oCAAA;AACA,yBAAA,IAAA;AACA,mBAAA;AACA,2BAAA;AACA,qBAAA,IAAA;AACA,2BAAA;AACA,sBAAA;AACA,oBAAA;AACA,wBAAA;AACA,0BAAA;AACA,uBAAA;AACA,qBAAA;AACA,sBAAA,IAAA;AACA,gBAAA;AACA,sBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,oBAAA,IAAA;AACA,8BAAA;AACA,sBAAA;AACA,+BAAA;AACA,4BAAA;AACA;IAAA,IAAA,IAAA,qBAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,qBAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,KAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAIA,qBAAA,IAAA;AACA,sBAAA,IAAA;AACA,0BAAA,IAAA;AACA,0BAAA,IAAA;AAEA,yBAAA;AACA,uBAAA;AACA,oBAAA;AACA,kBAAA;AACA,8BAAA;AASA,qBAAA,IAAA;AACA,2BAAA,IAAA;AAEA,wBAAA,IAAA,GAAA,EAAA,IAAA,EAAA;AACA,wBAAA,IAAA;AAEA;IAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,MAAA;IAAA,IAAA,SAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAMA,yBAAA,IAAA;AACA;IAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,MAAA;IAAA,IAAA,SAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAMA,0BAAA,IAAA;AACA,sBAAA,IAAA;AACA;IAAA,IAAA,KAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,IAAA;IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAIA,kBAAA;AACA,wBAAA,IAAA;AAEA,qBAAA;AAEA,sBAAA;AAEA,mBAAA;AACA,0BAAA;AACA,2BAAA;AACA,yBAAA;AACA,gCAAA;AACA,yBAAA;AAEA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,oBAAA;AAEA,oBAAA,IAAA;AAEA,mBAAA;AACA,wBAAA;AACA,0BAAA;AACA,2BAAA;AACA,wBAAA,IAAA;AAEA,kBAAA;AACA,uBAAA;AACA,yBAAA;AACA,0BAAA;AACA,uBAAA;AAEA,6BAAA;AACA,kCAAA;AACA,wBAAA;AACA,6BAAA;AAEA,8BAAA,IAAA;AACA,mCAAA,IAAA;AACA,yBAAA,IAAA;AACA,8BAAA,IAAA;AAEA,iBAAA,IAAA;AACA,wBAAA,IAAA;AACA,yBAAA,IAAA;AACA,4BAAA,IAAA;AACA,mCAAA,IAAA;AAEA,iBAAA,IAAA;AAEA,mBAAA;AACA,0BAAA;AACA,2BAAA;AACA,wBAAA;AACA,4BAAA;AACA,kCAAA;AACA,mCAAA;AAEA,qBAAA,IAAA;AACA,qBAAA;AAEA,sBAAA;AACA,sBAAA;AAEA,wBAAA;AACA,uBAAA;AACA,uBAAA;AACA,0BAAA;AACA,sBAAA;AACA,uBAAA;AACA,gCAAA;AACA,qCAAA;AACA,wBAAA;AACA,0BAAA;AACA,kCAAA;AACA,qCAAA;AAEA,iBAAA;AACA,4BAAA;;AAtHA,OAAA,OAAA,IAAA,CAAA,gBAAA,EAAA;AA1CF,GAAA;AA2CI,sBAAA;AACA,oBAAA;AACA,2BAAA;AACA,yBAAA;;;AAqHA,CAnKJ,UAmKI,CAAA,WAAA,CAAA;AACE,cAAA;;AAIJ,CAxKF,UAwKE,CALE;AAMA,kBAAA,OAAA,KAAA,WAAA;AACA,iCAAA;AACA,8BAAA;AAEA,mBAAA;AACA,mBAAA;AACA,mBAAA;AACA,oCAAA;AACA,yBAAA,IAAA;AACA,mBAAA;AACA,2BAAA;AACA,2BAAA;AACA,sBAAA;AACA,oBAAA;AACA,wBAAA;AACA,0BAAA;AACA,uBAAA;AACA,qBAAA;AACA,sBAAA,IAAA;AACA,gBAAA;AACA,sBAAA,KAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA;AACA,8BAAA;AACA,sBAAA;AACA,+BAAA;AACA,4BAAA;AACA;IAAA,IAAA,IAAA,qBAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,qBAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,IAAA,KAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAIA;IAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,MAAA;IAAA,IAAA,SAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,UAAA;IAAA,IAAA,UAAA,UAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAMA,yBAAA,IAAA;AAEA,qBAAA;AACA,2BAAA;AAEA,wBAAA,IAAA,GAAA,EAAA,GAAA,EAAA;AAGA,qBAAA;AAEA,sBAAA,IAAA;AAEA,mBAAA;AACA,0BAAA;AACA,2BAAA;AACA,yBAAA;AACA,gCAAA;AACA,yBAAA;AAEA,oBAAA,IAAA;AAEA,wBAAA,IAAA;AAEA,kBAAA;AACA,uBAAA;AACA,yBAAA;AACA,0BAAA;AACA,uBAAA;AAEA,6BAAA;AACA,kCAAA;AACA,wBAAA;AACA,6BAAA;AAEA,8BAAA,IAAA;AACA,mCAAA,IAAA;AACA,yBAAA,IAAA;AACA,8BAAA,IAAA;AAEA,iBAAA,IAAA;AACA,wBAAA,IAAA;AACA,yBAAA,IAAA;AACA,4BAAA,IAAA;AACA,mCAAA,IAAA;AAEA,qBAAA;AAEA,wBAAA,IAAA,GAAA,EAAA,GAAA,EAAA;AACA,uBAAA,IAAA,GAAA,EAAA,EAAA,EAAA;AACA,uBAAA,IAAA,IAAA,GAAA;AACA,0BAAA,IAAA,CAAA,EAAA,EAAA,EAAA;AACA,sBAAA;AACA,uBAAA;AACA,gCAAA;AACA,qCAAA;AACA,wBAAA;AACA,0BAAA;AACA,kCAAA;AACA,qCAAA;;ACtQJ;AACE,mBAAA;AACA,8BAAA;AACA,qBAAA;AACA,oBAAA;AACA,0BAAA;AACA,oBAAA;AACA,+BAAA;AACA,8BAAA;AACA,+BAAA;AAEA,kBAAA;AACA,kBAAA;AACA,kBAAA;AAEA,SAAA,IAAA;AACA,SAAA,IAAA;AACA,SAAA,IAAA;AACA,SAAA,IAAA;;AAGF,IAAA,CAAA;AAAA,IAAA,CAAA,yBAAA,CAAA;AAAA,IAAA,CAAA,yBAAA;AAGE,UAAA;;AAGF,CD3BA;AC4BE;IAAA,SAAA;IAAA,SAAA;IAAA,kBAAA;IAAA,aAAA;IAAA,MAAA,EAAA;IAAA,MAAA;IAAA,SAAA;IAAA,KAAA;IAAA;AAEA,eAAA,IAAA;AAEA,YAAA;AACA,YAAA;AACA,SAAA,IAAA;AACA,WAAA;AACA,OAAA;AACA,UAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA;AAgBA,eAAA;;AAdA,CD3CF,WC2CE;AACE,UAAA;AACA,eAAA;;AAGF,CDhDF,UCgDE;AACE,WAAA;;AAUF,CD3DF,WC2DE,CAAA;AAAA,CD3DF,WC2DE;AAEE,eAAA;AACA,mBAAA;AACA,SAAA,IAAA;AACA,eAAA;AACA,UAAA;;AAEA,CDnEJ,WCmEI,CARF,eAQE;AAAA,CDnEJ,WCmEI,CAAA;AACE,mBAAA;;AAEF,CDtEJ,WCsEI,CAXF,eAWE;AAAA,CDtEJ,WCsEI,CAAA;AACE,mBAAA;;AAIJ,CD3EF,WC2EE;AACE,gBAAA;AAKA,mBAAA;AAEA,mBAAA;AAEA,WAAA,IAAA;;AAEA,CDvFJ,WCuFI,MAAA,CAAA;AACE,WAAA,IAAA;;AAOJ,CAAA;AAAA,CAAA,kBAAA,CAAA;AAEE,kBAAA;;AAGF,CALA;AAME,YAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,UAAA;;AAGF,CAAA;AACE,YAAA;AACA,WAAA;AACA,oBAAA,IAAA;AACA,kBAAA;;AAQA,CD1HJ,UC0HI,CDyCA,YCzCA;AACE,UAAA,IAAA;;AAIJ,CD/HF,WC+HE,CAAA;AACE,eAAA,IAAA,KAAA,EAAA;AACA,iBAAA,IAAA,KAAA,EAAA;AACA,kBAAA,IAAA,KAAA,EAAA;AACA,gBAAA,IAAA,KAAA,EAAA;;AAGF,CDtIF,WCsIE,CAAA;AACE,WAAA;AACA,mBAAA;;AAGF,CD3IF,WC2IE,CAAA;AACE,WAAA;AACA,kBAAA;AACA,WAAA;;AAEA,CDhJJ,WCgJI,CALF,YAKE;AAAA,CDhJJ,WCgJI,CALF,YAKE;AAAA,CDhJJ,WCgJI,CALF,YAKE,CAAA;AAGE,UAAA;AACA,iBAAA;AACA,aAAA;AACA,SAAA,IAAA;AACA,eAAA;AACA,WAAA;;AAGF,CD3JJ,WC2JI,CAhBF,YAgBE,CAXA,cAWA;AACE,WAAA;AACA,SAAA;;AAGF,CDhKJ,WCgKI,CArBF,YAqBE;AACE,WAAA;;AAGF,CDpKJ,WCoKI,CAzBF,YAyBE,CAAA;AACE,aAAA;AACA,YAAA;;AAGF,CDzKJ,WCyKI,CA9BF,YA8BE,CAAA;AACE,aAAA;AACA,WAAA;AACA,cAAA;AACA,WAAA;;AAEA,CD/KN,WC+KM,CApCJ,YAoCI,CANF,WAME;AACE,aAAA;;AAGF,CDnLN,WCmLM,CAxCJ,YAwCI,CAVF,WAUE,KAAA,CAAA;AAAA,CDnLN,WCmLM,CAxCJ,YAwCI,CAVF,WAUE,KAAA,CAAA;AAEE,WAAA;AACA,YAAA;AACA,kBAAA;;AAGF,CD1LN,WC0LM,CA/CJ,YA+CI,CAjBF,WAiBE,CAAA;AACE,cAAA;;AAIJ,CD/LJ,WC+LI,CApDF,YAoDE;AACE,UAAA;AACA,WAAA;AACA,UAAA;;AAIJ,CDtMF,WCsME,CAAA;AACE,SAAA;AACA,oBC1KQ;AD2KR,UAAA;;AAGF,CD5MF,WC4ME,CAAA,wBAAA;AAAA,CD5MF,WC4ME,CAAA,kBAAA;AAAA,CD5MF,WC4ME,MAAA;AAAA,CD5MF,WC4ME,CAnCE,WAmCF,KAAA;AAAA,CD5MF,WC4ME,KAAA;AAKE,WAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;;AAIA,CDtNJ,WCsNI,CA7CA,WA6CA,CAAA;AACE,OAAA;AACA,WAAA;;AAGF,CD3NJ,WC2NI,CAlDA,WAkDA;AACE,oBAAA;;AAGF,CD/NJ,WC+NI,CAtDA,WAsDA;AAAA,CD/NJ,WC+NI,CAtDA,WAsDA;AAAA,CD/NJ,WC+NI,CAtDA,WAsDA,CAAA;AEjKF,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;AACA,cAAA;AACA,gBAAA;AACA,gBAAA;AACA,gBAAA,IAAA,eAAA,EAAA,IAAA;AACA,iBAAA,IAAA;AACA,UAAA;AACA,oBAAA,IAAA,WAAA,EAAA,IAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,eAAA,IAAA;AAiDA,WAAA;;AA/CA,CH9EF,WG8EE,CF2FE,WE3FF,MAAA;AAAA,CH9EF,WG8EE,CF2FE,WE3FF,OAAA;AAAA,CH9EF,WG8EE,CF2FE,WE3FF,CFiJE,aEjJF;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;;AAGF,CHnFF,WGmFE,CFsFE,WEtFF,KAAA;AAAA,CHnFF,WGmFE,CFsFE,WEtFF,MAAA;AAAA,CHnFF,WGmFE,CFsFE,WEtFF,CF4IE,YE5IF;AACE,oBAAA,IAAA,iBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,qBAAA,EAAA,IAAA,eAAA,EAAA,IAAA;AAIA,SAAA,IAAA,oBAAA,EAAA,IAAA,cAAA,EAAA,IAAA,oBAAA,EAAA;;AAMF,CH/FF,WG+FE,CF0EE,WE1EF,KAAA;AAAA,CH/FF,WG+FE,CF0EE,WE1EF,MAAA;AAAA,CH/FF,WG+FE,CF0EE,WE1EF,CFgIE,YEhIF;AACE,oBAAA,IAAA,kBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,sBAAA,EAAA,IAAA;;AAGF,CHpGF,WGoGE,CFqEE,WErEF,KAAA,CAAA;AAAA,CHpGF,WGoGE,CFqEE,WErEF,MAAA,CAAA;AAAA,CHpGF,WGoGE,CFqEE,WErEF,CF2HE,YE3HF,CAAA;AACE,oBAAA,IAAA,oBAAA,EAAA,IAAA;AAIA,gBAAA,IAAA,wBAAA,EAAA,IAAA;;AAKA,CH9GJ,WG8GI,CF2DA,WE3DA,KAAA,CAVF,MAUE;AAAA,CH9GJ,WG8GI,CF2DA,WE3DA,MAAA,CAVF,MAUE;AAAA,CH9GJ,WG8GI,CF2DA,WE3DA,CFiHA,YEjHA,CAVF,MAUE;AACE,oBAAA,IAAA,0BAAA,EAAA,IAAA;;AAMF,CHrHJ,WGqHI,CFoDA,WEpDA,KAAA,CAjBF,OAiBE;AAAA,CHrHJ,WGqHI,CFoDA,WEpDA,MAAA,CAjBF,OAiBE;AAAA,CHrHJ,WGqHI,CFoDA,WEpDA,CF0GA,YE1GA,CAjBF,OAiBE;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;;AASJ,CH/HF,WG+HE,CF0CE,WE1CF,MAAA;AAAA,CH/HF,WG+HE,CF0CE,WE1CF,OAAA;AAAA,CH/HF,WG+HE,CF0CE,WE1CF,CFgGE,aEhGF;AACE,SAAA,IAAA;AACA,UAAA,IAAA;;AFqGF,CDtOF,WCsOE,CAAA;AACE,WAAA,IAAA;AACA,WAAA;AACA,kBAAA;AACA,eAAA;;AAGF,CD7OF,WC6OE,CAAA;AACE,YAAA;AACA,OAAA;AACA,UAAA;AACA,QAAA;AACA,SAAA;AACA,iBAAA,KAAA,EAAA,EAAA,IAAA;AACA,eAAA,IAAA,IAAA,cAAA,EAAA,IAAA,KAAA,CAAA;AACA,iBAAA,IAAA,KAAA,EAAA;AACA,kBAAA,IAAA,KAAA,EAAA;AACA,gBAAA,IAAA,KAAA,EAAA;AACA,WAAA;AACA,WAAA;AACA,eAAA;AACA,kBAAA;;AAEA,CD7PJ,WC6PI,CAhBF,eAgBE,EAAA,CAAA;AACE,SAAA;AACA,aAAA;AACA,aAAA;AACA,cAAA;AACA,cAAA;AACA,WAAA;AACA,kBAAA;AACA,kBAAA,IAAA;;AAEA,CDvQN,WCuQM,CA1BJ,eA0BI,EAAA,CAVF,OAUE,CA5HJ;AA6HM,WAAA,IAAA,IAAA,EAAA;;AAKN,CD7QF,WC6QE,CAAA;AACE,SAAA;;AAGE,CDjRN,WCiRM,CAJJ,YAII,CAAA,MAAA,CAAA,QAAA;AACE,qBAAA;AACA,sBAAA;;AAEF,CDrRN,WCqRM,CARJ,YAQI,CAJA,MAIA,CEjLJ;AFkLM,oBAAA,IAAA;;AAKN,CD3RF,WC2RE,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA;;AAEA,CDjSJ,WCiSI,CANF,oBAME,CAAA;AACE,UAAA;AACA,OAAA;;AAIJ,CDvSF,WCuSE,CAAA;AACE,SAAA;AACA,cAAA;AACA,cAAA;AACA,cAAA;AACA,iBAAA,IAAA;;AAGF,CD/SF,WC+SE,CAAA;AACE,WAAA;AACA,SAAA,IAAA;;AAGF,CDpTF,WCoTE,CAAA;AACE,WAAA;AACA,mBAAA;AACA,kBAAA;;AAEA,CDzTJ,WCyTI,CALF,eAKE,EAAA;AACE,kBAAA,IAAA;;AAIJ,CD9TF,WC8TE,CAAA;AACE,yBAAA,IAAA,IAAA;AACA,YAAA;AACA,eAAA;AACA,UAAA;AACA,kBAAA;;AAEA,CDrUJ,WCqUI,CAPF,aAOE,EAAA;AACE,kBAAA,IAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AAXF,GD9TF,WC8TE,CAAA;AAYI,2BAAA,IAAA,IAAA;AACA,cAAA;;;AAIJ,CD/UF,WC+UE,CAjBA,aAiBA,EAAA,CAAA;AACE,gBAAA;;AAGF,CDnVF,WCmVE,CArBA,aAqBA,EAAA,CAAA;AACE,gBAAA;;AAGF,CDvVF,WCuVE,CAAA;AACE,YAAA;AACA,UAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,UAAA;AACA,kBAAA;AACA,cAAA;AACA,WAAA,EAAA;;AAGE,CDnWN,WCmWM,CAAA,iCAAA;AACE,SAAA;;AAIJ,CDxWJ,WCwWI,CAjBF,gBAiBE;AACE,WAAA;;AAIJ,CD7WF,WC6WE,CAtBA,gBAsBA,EAAA,CAAA;AACE,gBAAA;;AAGF,CDjXF,WCiXE,CA1BA,gBA0BA,EAAA,CAAA;AACE,gBAAA;;AAGF,CDrXF,WCqXE,CAAA;AACE,sBAAA,IAAA,KAAA;AACA,UAAA;;AAGF,CD1XF,WC0XE,CAAA;AACE,sBAAA;AACA,UAAA;;AAGF,CD/XF,WC+XE,CAAA;AACE,cAAA;AACA,WAAA;AACA,SAAA;AACA,cAAA;AACA,YAAA;;AAGF,CDvYF,WCuYE,CAAA;AACE,UAAA;AACA,WAAA;AACA,wBAAA;AACA,sBAAA;AACA,SAAA,IAAA;AACA,oBAAA,IAAA;AACA,iBAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,aAAA;AACA,eAAA;AACA,WAAA;AACA,cAAA;AACA,oBAAA,IAAA;AACA,qBAAA;AACA,uBAAA,MAAA,OAAA,IAAA,GAAA,EAAA,EAAA;AAMA,mBAAA,OAAA,IAAA,EAAA;;AAJA,KAAA,CAAA,SAAA,CDxZJ,WCwZI,CAjBF;AAkBI,uBAAA,KAAA,OAAA,IAAA,GAAA,EAAA,EAAA;;AAKF,CD9ZJ,WC8ZI,CAvBF,eAuBE;AACE,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;;AAGF,CDlaJ,WCkaI,CA3BF,eA2BE;AACE,oBAAA,IAAA;;AAGF,CDtaJ,WCsaI,CA/BF,eA+BE;AACE,oBAAA,IAAA;;AAGF,CD1aJ,WC0aI,CAAA;AACE,UAAA;AACA,oBAAA,IAAA;AACA,gBAAA,IAAA;AACA,UAAA;;AAEA,CDhbN,WCgbM,CANF,yBAME;AACE,oBAAA,IAAA;;AAKN,CDtbF,WCsbE,CAAA;AACE,iBAAA,IAAA;AACA,oBAAA,IAAA;AACA,SAAA,IAAA;AAEA,UAAA,IAAA,MAAA,IAAA;AACA,WAAA,KAAA;AACA,YAAA;AACA,QAAA;AACA,UAAA;AACA,aAAA,WAAA;AACA,kBAAA,IAAA;AACA,eAAA;;AAEA,CDpcJ,WCocI,CAdF,sBAcE;AACE,oBAAA,IAAA;;AAGF,CDxcJ,WCwcI,CAlBF,sBAkBE;AACE,UAAA,IAAA,MAAA,IAAA;;AAIJ,CD7cF,WC6cE,CAAA;AE/YA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;AACA,cAAA;AACA,gBAAA;AACA,gBAAA;AACA,gBAAA,IAAA,eAAA,EAAA,IAAA;AACA,iBAAA,IAAA;AACA,UAAA;AACA,oBAAA,IAAA,WAAA,EAAA,IAAA;AACA,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,eAAA,IAAA;AAuGA,UAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;AACA,oBAAA,IAAA;AF4RE,SAAA,IAAA;AACA,UAAA,IAAA;;AEpYF,CH9EF,WG8EE,CF+XA,UE/XA;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;AACA,UAAA,IAAA,eAAA,EAAA,IAAA;;AAGF,CHnFF,WGmFE,CF0XA,SE1XA;AACE,oBAAA,IAAA,iBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,qBAAA,EAAA,IAAA,eAAA,EAAA,IAAA;AAIA,SAAA,IAAA,oBAAA,EAAA,IAAA,cAAA,EAAA,IAAA,oBAAA,EAAA;;AAMF,CH/FF,WG+FE,CF8WA,SE9WA;AACE,oBAAA,IAAA,kBAAA,EAAA,IAAA;AACA,gBAAA,IAAA,sBAAA,EAAA,IAAA;;AAGF,CHpGF,WGoGE,CFyWA,SEzWA,CAAA;AACE,oBAAA,IAAA,oBAAA,EAAA,IAAA;AAIA,gBAAA,IAAA,wBAAA,EAAA,IAAA;;AAKA,CH9GJ,WG8GI,CF+VF,SE/VE,CAVF,MAUE;AACE,oBAAA,IAAA,0BAAA,EAAA,IAAA;;AAMF,CHrHJ,WGqHI,CFwVF,SExVE,CAjBF,OAiBE;AACE,SAAA,IAAA,cAAA,EAAA,IAAA;;AAiEJ,CHvLF,WGuLE,CFsRA,SEtRA;AACE,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;;AF4RA,CDpdJ,WCodI,CAPF,UAOE;AACE,SAAA,IAAA;AACA,UAAA,IAAA;;AAIJ,CD1dF,WC0dE,CAAA;AACE,eAAA,IAAA;;AAGF,CD9dF,WC8dE,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,cAAA;AACA,iBAAA;AACA,uBAAA;;AAGF,CDveF,WCueE,CAAA;AAAA,CDveF,WCueE,CAAA;AAEE,WAAA;AACA,kBAAA;AACA,cAAA;AACA,iBAAA;AACA,uBAAA;;AAIA,CAAA,kBAAA,CDjfJ,WCifI;AACE,WAAA;;AAEF,CAHA,kBAGA,CDpfJ,WCofI,CA9DF;AA+DI,UAAA,KAAA,KAAA,EAAA,IAAA,KAAA,EAAA;AACA,WAAA;;AAKF,KAAA,CAAA,SAAA,CD3fJ,WC2fI,CAAA;AACE,aAAA,OAAA;;AAIJ,CDhgBF,WCggBE,CAAA;AACE,cAAA;AACA,WAAA;AACA,UAAA;AACA,SAAA;AACA,cAAA,QAAA;;AAEA,CDvgBJ,WCugBI,CAPF,mBAOE,CAAA;AACE,cAAA;AACA,WAAA;AACA,UAAA;AACA,SAAA;AACA,cAAA,QAAA;;AAIJ,CDhhBF,WCghBE,CAAA;AACE,kBAAA;;AAIA,CDrhBJ,UCqhBI,CAAA,sBAAA,CAtOF;AAuOI,WAAA;AACA,mBAAA;;AAIJ,CD3hBF,WC2hBE,KAAA,CAAA;AAGE,yBAAA;;AAGF,CDjiBF,WCiiBE,KAAA,CAAA;AAAA,CDjiBF,WCiiBE,QAAA,KAAA,CAAA;AAEE,SAAA,IAAA;AACA,UAAA,MAAA,MAAA,IAAA;AACA,WAAA;AACA,eAAA;AACA,iBAAA,IAAA;AACA,oBAAA,IAAA;;AAGE,CD3iBN,WC2iBM,KAAA,CAAA,UAAA,KAAA,OAAA;AAAA,CD3iBN,WC2iBM,QAAA,KAAA,CAVJ,mBAUI,KAAA,OAAA;AACE,gBAAA,IAAA;;AAIJ,CDhjBJ,WCgjBI,KAAA,CAAA,UAAA;AAAA,CDhjBJ,WCgjBI,QAAA,KAAA,CAfF,mBAeE;AACE,WAAA;AACA,gBAAA,IAAA;;AAIJ,OAAA;AACE,GDvjBJ,WCujBI,CA1UF;EA0UE,CDvjBJ,WCujBI,CAxbF;EAwbE,CDvjBJ,WCujBI,CAAA;AAGE,aAAA;;;AAMJ,CDhkBF,WCgkBE;AACE,SAAA;AACA,UAAA;;AAGF,CDrkBF,WCqkBE,MAAA;AACE,SAAA;;AAGF,CDzkBF,WCykBE;AACE,cAAA,IAAA;AACA,iBAAA;;AAEF,CD7kBF,WC6kBE,yBAAA;AACE,cAAA,IAAA;;AAEF,CDhlBF,WCglBE,yBAAA;AACE,cAAA,IAAA;;AAGF,CDplBF,WColBE,CAAA;AACE,YAAA;AACA,OAAA,KAAA,KAAA,EAAA,IAAA;AACA,SAAA,KAAA,IAAA,4BAAA,EAAA;AACA,WAAA;AACA,kBAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,0BAAA,IAAA;AACA,6BAAA,IAAA;AACA,gBAAA;AACA,YAAA;AAEA,oBAAA,IAAA;;AAEA,CDlmBJ,WCkmBI,CAdF,4BAcE,CA5YA;AA6YE,SAAA;AACA,UAAA;AACA,iBAAA;;AAGF,CDxmBJ,WCwmBI,CApBF,4BAoBE,CAAA;AACE,UAAA;;AAIJ,CD7mBF,WC6mBE,CAAA;AACE,YAAA;AACA,aAAA;;AAEA,CDjnBJ,WCinBI,CAJF,oBAIE,CAAA;AACE,WAAA;;AAGJ,CDrnBF,WCqnBE,CAAA;AACE,UAAA;AACA,WAAA;AACA,UAAA;AACA,cAAA;;AAEA,CD3nBJ,WC2nBI,CANF,iBAME;AACE,WAAA;AACA,aAAA;AACA,eAAA;AACA,UAAA,EAAA,EAAA;AACA,WAAA;;AAIJ,CDpoBF,WCooBE,CAAA;AEjdA,UAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;AACA,oBAAA,IAAA;;AAEA,CHvLF,WGuLE,CF6cA,iBE7cA;AACE,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;;AFgdF,CDxoBF,WCwoBE,CAjWA;AAiWA,CDxoBF,WCwoBE,CAzQA;AA2QE,mBAAA;AACA,eAAA,IAAA;;AAIA,CD/oBJ,UC+oBI,CD5eA,WC4eA,CD/oBJ,WC+oBI,CAxWF;AAwWE,CD/oBJ,UC+oBI,CD5eA,WC4eA,CD/oBJ,WC+oBI,CAhRF;AAkRI,qBAAA;AACA,eAAA,IAAA;;AAKF,CDvpBJ,WCupBI,CAxRF,eAwRE,CA9eA;AA+eE,WAAA,QAAA;;AAIJ,CD5pBF,WC4pBE,CAAA;AACE,UAAA,KAAA;;AAIA,CDjqBJ,WCiqBI,CAAA,eAAA,CALF,qBAKE;AACE,cAAA;;AAEF,CDpqBJ,WCoqBI,CAHA,eAGA,CARF,sBAQE,EAAA,CARF;AASI,cAAA;;AAKN,CAAA,WAAA,CD1qBA;AC2qBE,cAAA;AACA,WAAA,KAAA;AACA,YAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,eAAA;;AAEA,CATF,WASE,CDnrBF,WCmrBE,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AAEA,WAAA;AACA,oBCjoBO;ADkoBP,UAAA,IAAA,MAAA;;AAGF,CApBF,WAoBE,CD9rBF,WC8rBE,CAAA;AACE,UAAA,KAAA;AACA,aAAA;;AAEA,CAxBJ,WAwBI,CDlsBJ,WCksBI,CAJF,qBAIE,CAAA;AACE,cAAA;;AAIJ,CA7BF,WA6BE,CDvsBF,WCusBE,CAAA;AAAA,CA7BF,WA6BE,CDvsBF,WCusBE,CAAA,OAAA;AAEE,aAAA;;AAGF,CAlCF,WAkCE,CD5sBF,WC4sBE,CAAA;AAAA,CAlCF,WAkCE,CD5sBF,WC4sBE,CAAA,QAAA;AAEE,aAAA;;AAGF,CAvCF,WAuCE,CDjtBF,WCitBE,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;;AAEA,CA5CJ,WA4CI,CDttBJ,WCstBI,CALF,oBAKE;AACE,SAAA;AACA,UAAA,KAAA;AACA,eAAA;AACA,aAAA;;AAMJ,CAlnBA,iCAknBA,CAAA;AACE,YAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,iBAAA,IAAA;;AAGF,CA1nBA,iCA0nBA,CAAA;AACE,SAAA;AACA,UAAA;;AACA,CA7nBF,iCA6nBE,CAHF,8BAGE,EAAA;AACE,iBAAA,IAAA;;AAIJ,CAloBA,iCAkoBA,CAAA;AACE,YAAA;AACA,WAAA;AACA,cAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA,KAAA;AACA,iBAAA;AACA,SAAA;AACA,eAAA;AACA,kBAAA;AACA,eAAA;;;;AGxvBJ;AACE,eAAa;AACb,OAAK,iDAAgD,OAAO;AAC5D,eAAa;AACb,SAAO;AACP,WAAS;AACX;AAEA;AACE,eAAa;AACb,OAAK,gDAA+C,OAAO;AAC3D,eAAa;AACb,SAAO;AACP,WAAS;AACX;AAEA;AACE,eAAa;AACb,OAAK,kDAAiD,OAAO;AAC7D,eAAa;AACb,SAAO;AACP,WAAS;AACX;AAEA;AACE,eAAa;AACb,OAAK,8CAA6C,OAAO;AACzD,eAAa;AACb,SAAO;AACP,WAAS;AACX;;;AClCA,CAAA;AACE,kBAAA;AAKA,WAAA;AACA,SAAA;AACA,mBAAA;AACA,qBAAA;;AAPA,CAFF,cAEE,EAAA;AACE,kBAAA,IAAA;;;;ACFF,CAAA,WAAA,CAAA;AACE,mBAAA;AACA,mBAAA;AAEA,sBAAA;AACA,sBAAA;AAEA,uBAAA;AACA,uBAAA;AAEA,sBAAA;AACA,sBAAA;AAEA,WAAA;AACA,eAAA;;AAEA,CAhBF,WAgBE,CAhBF,eAgBE;AACE,QAAA,EAAA,EAAA;;AAGF,CApBF,WAoBE,CApBF,eAoBE,CAAA;AACE,SAAA;AACA,SAAA,IAAA;;AAGF,CAzBF,WAyBE,CAzBF,eAyBE,CAAA;AACE,eAAA;AACA,SAAA;AACA,SAAA,IAAA;;AAIA,CAhCJ,WAgCI,CAhCJ,cAgCI,CAAA,MAAA,CAZF;AAaI,UAAA,IAAA;;AAGF,CApCJ,WAoCI,CApCJ,cAoCI,CAJA,MAIA,CAXF;AAYI,UAAA,IAAA;;AAKF,CA1CJ,WA0CI,CA1CJ,cA0CI,CAAA,SAAA,CAtBF;AAuBI,UAAA,IAAA;;AAGF,CA9CJ,WA8CI,CA9CJ,cA8CI,CAJA,SAIA,CArBF;AAsBI,UAAA,IAAA;;AAKF,CApDJ,WAoDI,CApDJ,cAoDI,CAAA,UAAA,CAhCF;AAiCI,UAAA,IAAA;;AAGF,CAxDJ,WAwDI,CAxDJ,cAwDI,CAJA,UAIA,CA/BF;AAgCI,UAAA,IAAA;;AAKF,CA9DJ,WA8DI,CA9DJ,cA8DI,CAAA,SAAA,CA1CF;AA2CI,UAAA,IAAA;;AAGF,CAlEJ,WAkEI,CAlEJ,cAkEI,CAJA,SAIA,CAzCF;AA0CI,UAAA,IAAA;;;;ACnEN,CAAA,WAAA,CAAA;AACE,eAAA,YAAA,EAAA;;AAMF,CAPA,WAOA,CAAA;AACE,kBAAA;AAEA,SAAA,IAAA;;AAEA,CAZF,WAYE,CALF,qBAKE;AACE,WAAA,IAAA;AACA,SAAA,IAAA;AACA,mBAAA;AACA,iBAAA;;AAKF,CArBF,UAqBE,CAAA,YAAA,CAdF;AAeI,SAAA,IAAA;;AAQF,OAAA,CAAA,UAAA,EAAA;AADF,GA7BA,WA6BA,CAAA;AAEI,aAAA;;;AAGF,OAAA,CAAA,SAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA;AAEI,GApCN,WAoCM,CAPN,0BAOM,CAAA;EAAA,CApCN,WAoCM,CAPN,0BAOM,CAAA;AAEE,aAAA;;;AAKN,CA3CF,WA2CE,CAAA;AACE,WAAA;AACA,YAAA;AACA,SAAA;AACA,UAAA;;AAEA,KAAA,CAAA,SAAA,CAjDJ,WAiDI,CANF;AAOI,QAAA;AACA,SAAA;;AAGF,CAtDJ,WAsDI,CAXF,gCAWE;AACE,cAAA;AACA,SAAA;AACA,UAAA;AAEA,aAAA,OAAA,IAAA,OAAA;;AAEA,KAAA,CAAA,SAAA,CA7DN,WA6DM,CAlBJ,gCAkBI;AACE,aAAA,OAAA;;AAKN,CAnEF,WAmEE,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,aAAA,WAAA;AACA,cAAA;AACA,WAAA;AACA,eAAA;;AAEA,CA5EJ,WA4EI,CATF,mCASE,CAAA;AACE,SAAA;AACA,YAAA;AACA,OAAA;;AAGF,CAlFJ,WAkFI,CAfF,mCAeE;AACE,SAAA;AACA,UAAA;;AAEA,KAAA,CAAA,SAAA,CAtFN,WAsFM,CAnBJ,mCAmBI;AACE,aAAA,OAAA;;AAKN,CA5FF,WA4FE,CAAA;AACE,YAAA;AACA,SAAA;AACA,aAAA;AAEA,OAAA;AACA,cAAA;AACA,uBAAA;AAEA,WAAA;AACA,eAAA;AACA,OAAA;;AAEA,CAzGJ,WAyGI,CAbF,gCAaE;AACE,SAAA;AACA,UAAA;;AAEA,KAAA,CAAA,SAAA,CA7GN,WA6GM,CAjBJ,gCAiBI;AACE,aAAA,OAAA;;AAIJ,OAAA,CAAA,SAAA,EAAA;AACE,GAnHN,WAmHM,CAvBJ,gCAuBI,CAvCF;AAwCI,eAAA;;;AASR,CA7HA,WA6HA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,mBAAA;AACA,eAAA;AACA,YAAA;AACA,kBAAA;AACA,QAAA;AACA,OAAA;AACA,SAAA;AACA,UAAA;;AAGF,CA3IA,WA2IA,CAAA;AACE,WAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;;AAGF,CAlJA,WAkJA,CAAA;AACE,aAAA;AACA,cAAA;;AAGF,CAvJA,WAuJA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,mBAAA;AACA,eAAA;;AAGF,CA/JA,WA+JA,CAAA;AACE,cAAA;AAEA,kBAAA,IAAA;AAEA,SAAA,IAAA;AACA,aAAA;AAEA,SAAA;AACA,aAAA;AACA,aAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AAEA,cAAA;AACA,UAAA,IAAA,MAAA;AAEA,WAAA;AAEA,iBAAA,IAAA;AAEA,yBAAA,KAAA,IAAA,qBAAA,EAAA,QAAA,IAAA;;AAEA,CAvLF,WAuLE,CAAA;AACE,WAAA;AACA,eAAA;AACA,gBAAA;AACA,cAAA;AACA,cAAA;;AAGF,CA/LF,WA+LE,CAAA;AACE,SAAA,IAAA;AACA,UAAA,IAAA;;AAGF,CApMF,WAoME,CAAA;AACE,eAAA;AACA,SAAA,IAAA;AACA,aAAA;;AAIJ,CA3MA,WA2MA,CA5CA,wBA4CA;AACE,mBAAA;AACA,cAAA,IAAA;;AAEA,CA/MF,WA+ME,CAhDF,wBAgDE,OAAA,CAXA;AAWA,CA/MF,WA+ME,CAhDF,wBAgDE,OAAA,CAhBA;AAgBA,CA/MF,WA+ME,CAhDF,wBAgDE,OAAA,CAxBA;AA2BE,SAAA,IAAA;;AAIJ,CAtNA,WAsNA,CAvDA,wBAuDA;AACE,cAAA,IAAA;AACA,gBAAA,IAAA;;AAEA,CA1NF,WA0NE,CA3DF,wBA2DE,QAAA,CAtBA;AAsBA,CA1NF,WA0NE,CA3DF,wBA2DE,QAAA,CA3BA;AA2BA,CA1NF,WA0NE,CA3DF,wBA2DE,QAAA,CAnCA;AAsCE,SAAA,IAAA;;AAKF,CAlOF,UAkOE,CA7MA,YA6MA,CAnEF;AAoEI,SAAA,IAAA;;AAEA,CArOJ,UAqOI,CAhNF,YAgNE,CAjCF;AAkCI,SAAA,IAAA;;AAIJ,CA1OF,UA0OE,CArNA,YAqNA,CA3EF,wBA2EE;AACE,oBAAA,IAAA;;AAEA,CA7OJ,UA6OI,CAxNF,YAwNE,CA9EJ,wBA8EI,OAAA,CA9CF;AA8CE,CA7OJ,UA6OI,CAxNF,YAwNE,CA9EJ,wBA8EI,OAAA,CAzCF;AAyCE,CA7OJ,UA6OI,CAxNF,YAwNE,CA9EJ,wBA8EI,OAAA,CAtDF;AAyDI,SAAA,IAAA;;AAKF,CArPJ,UAqPI,CAhOF,YAgOE,CAtFJ,wBAsFI,QAAA,CAtDF;AAsDE,CArPJ,UAqPI,CAhOF,YAgOE,CAtFJ,wBAsFI,QAAA,CAjDF;AAiDE,CArPJ,UAqPI,CAhOF,YAgOE,CAtFJ,wBAsFI,QAAA,CA9DF;AAiEI,SAAA,IAAA;;AAKN,OAAA,CAAA,UAAA,EAAA;AACE,GA9PF,WA8PE,CAjIF;AAkII,gBAAA;;;AAGJ,OAAA,CAAA,UAAA,EAAA,OAAA,IAAA,CAAA,UAAA,EAAA;AACE,GAnQF,WAmQE,CAtIF;AAuII,gBAAA;;;AAGJ,OAAA,CAAA,UAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA;AACE,GAxQF,WAwQE,CA3IF;AA4II,aAAA;;;;;ACvQJ,CAAA,WAAA,CAAA;AACE,eAAA,IAAA;AACA,kBAAA,IAAA;AACA,mBAAA,IAAA;AAEA,kBAAA,IAAA;AACA,mBAAA,IAAA;AAEA,qBAAA,IAAA;AACA,yBAAA,IAAA;AAEA,sBAAA,IAAA;AAEA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;AAEA,eAAA;;AAGA,CAlBF,WAkBE,CAlBF,aAkBE,CAAA,MAAA,CAAA;AACE,oBAAA;AACA,gBAAA;;AAEA,CAtBJ,WAsBI,CAtBJ,aAsBI,CAJF,MAIE,CAJF,OAIE;AACE,SAAA;;AAGF,CA1BJ,WA0BI,CA1BJ,aA0BI,CARF,MAQE,CARF,MAQE;AAAA,CA1BJ,WA0BI,CA1BJ,aA0BI,CARF,MAQE,CARF,MAQE;AAEE,oBAAA;AACA,gBAAA;;AAKN,CAlCA,WAkCA,CAAA,YAAA,CAAA;AACE,oBAAA,IAAA;;AAEA,CArCF,WAqCE,CAHF,YAGE,CAHF,iBAGE,CAAA,eAAA;AAAA,CArCF,WAqCE,CAHF,YAGE,CAHF,iBAGE,CAAA;AAEE,SAAA,IAAA;;AAIJ,CA3CA,WA2CA,CAAA;AAOE,aAAA;AACA,cAAA;AACA,eAAA;AACA,YAAA;AACA,UAAA;AACA,WAAA;AACA,iBAAA;AACA,oBCiOS;ADhOT,SCuOS;ADtOT,aAAA;AACA,eAAA;;AAhBA,KAAA,CAAA,SAAA,CA5CF,WA4CE,CADF;AAEI,SAAA;;AAEF,KAAA,CAAA,SAAA,CA/CF,WA+CE,CAJF;AAKI,QAAA;;", "names": []}