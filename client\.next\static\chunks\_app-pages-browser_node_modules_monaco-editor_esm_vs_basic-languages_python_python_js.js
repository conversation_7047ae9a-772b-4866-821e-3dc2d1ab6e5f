"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_python_python_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/python/python.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/python/python.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.api.js\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/basic-languages/python/python.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"'''\", \"'''\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(\n        \"^\\\\s*(?:def|class|for|if|elif|else|while|try|with|finally|except|async|match|case).*?:\\\\s*$\"\n      ),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ],\n  folding: {\n    offSide: true,\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".python\",\n  keywords: [\n    // This section is the result of running\n    // `import keyword; for k in sorted(keyword.kwlist + keyword.softkwlist): print(\"  '\" + k + \"',\")`\n    // in a Python REPL,\n    // though note that the output from Python 3 is not a strict superset of the\n    // output from Python 2.\n    \"False\",\n    // promoted to keyword.kwlist in Python 3\n    \"None\",\n    // promoted to keyword.kwlist in Python 3\n    \"True\",\n    // promoted to keyword.kwlist in Python 3\n    \"_\",\n    // new in Python 3.10\n    \"and\",\n    \"as\",\n    \"assert\",\n    \"async\",\n    // new in Python 3\n    \"await\",\n    // new in Python 3\n    \"break\",\n    \"case\",\n    // new in Python 3.10\n    \"class\",\n    \"continue\",\n    \"def\",\n    \"del\",\n    \"elif\",\n    \"else\",\n    \"except\",\n    \"exec\",\n    // Python 2, but not 3.\n    \"finally\",\n    \"for\",\n    \"from\",\n    \"global\",\n    \"if\",\n    \"import\",\n    \"in\",\n    \"is\",\n    \"lambda\",\n    \"match\",\n    // new in Python 3.10\n    \"nonlocal\",\n    // new in Python 3\n    \"not\",\n    \"or\",\n    \"pass\",\n    \"print\",\n    // Python 2, but not 3.\n    \"raise\",\n    \"return\",\n    \"try\",\n    \"type\",\n    // new in Python 3.12\n    \"while\",\n    \"with\",\n    \"yield\",\n    \"int\",\n    \"float\",\n    \"long\",\n    \"complex\",\n    \"hex\",\n    \"abs\",\n    \"all\",\n    \"any\",\n    \"apply\",\n    \"basestring\",\n    \"bin\",\n    \"bool\",\n    \"buffer\",\n    \"bytearray\",\n    \"callable\",\n    \"chr\",\n    \"classmethod\",\n    \"cmp\",\n    \"coerce\",\n    \"compile\",\n    \"complex\",\n    \"delattr\",\n    \"dict\",\n    \"dir\",\n    \"divmod\",\n    \"enumerate\",\n    \"eval\",\n    \"execfile\",\n    \"file\",\n    \"filter\",\n    \"format\",\n    \"frozenset\",\n    \"getattr\",\n    \"globals\",\n    \"hasattr\",\n    \"hash\",\n    \"help\",\n    \"id\",\n    \"input\",\n    \"intern\",\n    \"isinstance\",\n    \"issubclass\",\n    \"iter\",\n    \"len\",\n    \"locals\",\n    \"list\",\n    \"map\",\n    \"max\",\n    \"memoryview\",\n    \"min\",\n    \"next\",\n    \"object\",\n    \"oct\",\n    \"open\",\n    \"ord\",\n    \"pow\",\n    \"print\",\n    \"property\",\n    \"reversed\",\n    \"range\",\n    \"raw_input\",\n    \"reduce\",\n    \"reload\",\n    \"repr\",\n    \"reversed\",\n    \"round\",\n    \"self\",\n    \"set\",\n    \"setattr\",\n    \"slice\",\n    \"sorted\",\n    \"staticmethod\",\n    \"str\",\n    \"sum\",\n    \"super\",\n    \"tuple\",\n    \"type\",\n    \"unichr\",\n    \"unicode\",\n    \"vars\",\n    \"xrange\",\n    \"zip\",\n    \"__dict__\",\n    \"__methods__\",\n    \"__members__\",\n    \"__class__\",\n    \"__bases__\",\n    \"__name__\",\n    \"__mro__\",\n    \"__subclasses__\",\n    \"__init__\",\n    \"__import__\"\n  ],\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[,:;]/, \"delimiter\"],\n      [/[{}\\[\\]()]/, \"@brackets\"],\n      [/@[a-zA-Z_]\\w*/, \"tag\"],\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    // Deal with white space, including single and multi-line comments\n    whitespace: [\n      [/\\s+/, \"white\"],\n      [/(^#.*$)/, \"comment\"],\n      [/'''/, \"string\", \"@endDocString\"],\n      [/\"\"\"/, \"string\", \"@endDblDocString\"]\n    ],\n    endDocString: [\n      [/[^']+/, \"string\"],\n      [/\\\\'/, \"string\"],\n      [/'''/, \"string\", \"@popall\"],\n      [/'/, \"string\"]\n    ],\n    endDblDocString: [\n      [/[^\"]+/, \"string\"],\n      [/\\\\\"/, \"string\"],\n      [/\"\"\"/, \"string\", \"@popall\"],\n      [/\"/, \"string\"]\n    ],\n    // Recognize hex, negatives, decimals, imaginaries, longs, and scientific notation\n    numbers: [\n      [/-?0x([abcdef]|[ABCDEF]|\\d)+[lL]?/, \"number.hex\"],\n      [/-?(\\d*\\.)?\\d+([eE][+\\-]?\\d+)?[jJ]?[lL]?/, \"number\"]\n    ],\n    // Recognize strings, including those broken across lines with \\ (but not without)\n    strings: [\n      [/'$/, \"string.escape\", \"@popall\"],\n      [/f'{1,3}/, \"string.escape\", \"@fStringBody\"],\n      [/'/, \"string.escape\", \"@stringBody\"],\n      [/\"$/, \"string.escape\", \"@popall\"],\n      [/f\"{1,3}/, \"string.escape\", \"@fDblStringBody\"],\n      [/\"/, \"string.escape\", \"@dblStringBody\"]\n    ],\n    fStringBody: [\n      [/[^\\\\'\\{\\}]+$/, \"string\", \"@popall\"],\n      [/[^\\\\'\\{\\}]+/, \"string\"],\n      [/\\{[^\\}':!=]+/, \"identifier\", \"@fStringDetail\"],\n      [/\\\\./, \"string\"],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    stringBody: [\n      [/[^\\\\']+$/, \"string\", \"@popall\"],\n      [/[^\\\\']+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    fDblStringBody: [\n      [/[^\\\\\"\\{\\}]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"\\{\\}]+/, \"string\"],\n      [/\\{[^\\}':!=]+/, \"identifier\", \"@fStringDetail\"],\n      [/\\\\./, \"string\"],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    dblStringBody: [\n      [/[^\\\\\"]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    fStringDetail: [\n      [/[:][^}]+/, \"string\"],\n      [/[!][ars]/, \"string\"],\n      // only !a, !r, !s are supported by f-strings: https://docs.python.org/3/tutorial/inputoutput.html#formatted-string-literals\n      [/=/, \"string\"],\n      [/\\}/, \"identifier\", \"@pop\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/python/python.js\n"));

/***/ })

}]);