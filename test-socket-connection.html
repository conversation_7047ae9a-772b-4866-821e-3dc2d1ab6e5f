<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Socket.IO Connection Test</title>
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .status.connecting {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status.connected {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .log-entry.info {
            background: #e7f3ff;
            border-left: 3px solid #007bff;
        }
        .log-entry.success {
            background: #e8f5e8;
            border-left: 3px solid #28a745;
        }
        .log-entry.error {
            background: #ffe6e6;
            border-left: 3px solid #dc3545;
        }
        .log-entry.warning {
            background: #fff3cd;
            border-left: 3px solid #ffc107;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 Socket.IO Connection Test</h1>
        
        <div id="status" class="status connecting">
            🔄 Connecting to server...
        </div>
        
        <div>
            <button onclick="testConnection()">🔄 Test Connection</button>
            <button onclick="testPolling()">📡 Test Polling Only</button>
            <button onclick="testWebSocket()">🌐 Test WebSocket Only</button>
            <button onclick="clearLog()">🧹 Clear Log</button>
        </div>
        
        <div class="log" id="log">
            <div class="log-entry info">Starting connection test...</div>
        </div>
    </div>

    <script>
        let socket = null;
        const SERVER_URL = 'http://localhost:5002';
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function testConnection() {
            log('🔄 Testing default connection...', 'info');
            
            if (socket) {
                socket.disconnect();
            }
            
            socket = io(SERVER_URL, {
                transports: ['polling', 'websocket'],
                timeout: 10000,
                reconnectionAttempts: 3
            });
            
            setupSocketListeners();
        }
        
        function testPolling() {
            log('📡 Testing polling transport only...', 'info');
            
            if (socket) {
                socket.disconnect();
            }
            
            socket = io(SERVER_URL, {
                transports: ['polling'],
                timeout: 10000,
                reconnectionAttempts: 3,
                upgrade: false
            });
            
            setupSocketListeners();
        }
        
        function testWebSocket() {
            log('🌐 Testing WebSocket transport only...', 'info');
            
            if (socket) {
                socket.disconnect();
            }
            
            socket = io(SERVER_URL, {
                transports: ['websocket'],
                timeout: 10000,
                reconnectionAttempts: 3
            });
            
            setupSocketListeners();
        }
        
        function setupSocketListeners() {
            socket.on('connect', () => {
                log(`✅ Connected successfully! Socket ID: ${socket.id}`, 'success');
                log(`🔌 Transport: ${socket.io.engine.transport.name}`, 'info');
                updateStatus('✅ Connected', 'connected');
            });
            
            socket.on('disconnect', (reason) => {
                log(`❌ Disconnected. Reason: ${reason}`, 'error');
                updateStatus('❌ Disconnected', 'error');
            });
            
            socket.on('connect_error', (error) => {
                log(`🚨 Connection error: ${error.message}`, 'error');
                log(`Error details: ${JSON.stringify(error)}`, 'error');
                updateStatus('🚨 Connection Error', 'error');
            });
            
            socket.io.on('error', (error) => {
                log(`🚨 Transport error: ${error}`, 'error');
            });
            
            socket.io.on('reconnect_attempt', (attempt) => {
                log(`🔄 Reconnection attempt ${attempt}`, 'warning');
                updateStatus('🔄 Reconnecting...', 'connecting');
            });
            
            socket.io.on('reconnect', (attemptNumber) => {
                log(`✅ Reconnected after ${attemptNumber} attempts`, 'success');
                updateStatus('✅ Reconnected', 'connected');
            });
            
            socket.io.on('reconnect_error', (error) => {
                log(`🚨 Reconnection error: ${error}`, 'error');
            });
            
            socket.io.on('reconnect_failed', () => {
                log(`❌ Reconnection failed after all attempts`, 'error');
                updateStatus('❌ Reconnection Failed', 'error');
            });
            
            // Test basic functionality
            socket.on('connect', () => {
                setTimeout(() => {
                    log('🧪 Testing basic emit/receive...', 'info');
                    socket.emit('test-message', { message: 'Hello from test client!' });
                }, 1000);
            });
        }
        
        // Start initial connection test
        testConnection();
    </script>
</body>
</html>
