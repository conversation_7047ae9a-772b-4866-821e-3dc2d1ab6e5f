{"version": 3, "sources": ["../../../locales/fi-FI.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"<PERSON>it<PERSON>\",\n    \"pasteAsPlaintext\": \"<PERSON><PERSON><PERSON> pelkkänä tekstinä\",\n    \"pasteCharts\": \"<PERSON><PERSON><PERSON> kaaviot\",\n    \"selectAll\": \"<PERSON><PERSON><PERSON> kaikki\",\n    \"multiSelect\": \"<PERSON>s<PERSON><PERSON> kohde valintaan\",\n    \"moveCanvas\": \"<PERSON><PERSON><PERSON><PERSON> piirtoaluetta\",\n    \"cut\": \"<PERSON><PERSON>ka<PERSON>\",\n    \"copy\": \"Kopio<PERSON>\",\n    \"copyAsPng\": \"Kopioi leikepöydälle PNG-tiedostona\",\n    \"copyAsSvg\": \"Kopioi leikepöydälle SVG-tiedostona\",\n    \"copyText\": \"Kopioi tekstinä\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Tuo eteenpäin\",\n    \"sendToBack\": \"Vie taakse\",\n    \"bringToFront\": \"Tuo eteen\",\n    \"sendBackward\": \"Vie taaksepäin\",\n    \"delete\": \"Poista\",\n    \"copyStyles\": \"Kopioi tyyli\",\n    \"pasteStyles\": \"<PERSON><PERSON><PERSON> tyyli\",\n    \"stroke\": \"Piirto\",\n    \"background\": \"Tau<PERSON>\",\n    \"fill\": \"T<PERSON>ytt<PERSON>\",\n    \"strokeWidth\": \"Viivan leveys\",\n    \"strokeStyle\": \"Viivan tyyli\",\n    \"strokeStyle_solid\": \"Yhtenäinen\",\n    \"strokeStyle_dashed\": \"Katkoviiva\",\n    \"strokeStyle_dotted\": \"Pisteviiva\",\n    \"sloppiness\": \"Viivan tarkkuus\",\n    \"opacity\": \"Peittävyys\",\n    \"textAlign\": \"Tekstin tasaus\",\n    \"edges\": \"Reunat\",\n    \"sharp\": \"Terävä\",\n    \"round\": \"Pyöristetty\",\n    \"arrowheads\": \"Nuolenkärjet\",\n    \"arrowhead_none\": \"Ei mitään\",\n    \"arrowhead_arrow\": \"Nuoli\",\n    \"arrowhead_bar\": \"Tasapää\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Kolmio\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Kirjasinkoko\",\n    \"fontFamily\": \"Kirjasintyyppi\",\n    \"addWatermark\": \"Lisää \\\"Tehty Excalidrawilla\\\"\",\n    \"handDrawn\": \"Käsinpiirretty\",\n    \"normal\": \"Tavallinen\",\n    \"code\": \"Koodi\",\n    \"small\": \"Pieni\",\n    \"medium\": \"Keskikoko\",\n    \"large\": \"Suuri\",\n    \"veryLarge\": \"Erittäin suuri\",\n    \"solid\": \"Yhtenäinen\",\n    \"hachure\": \"Vinoviivoitus\",\n    \"zigzag\": \"\",\n    \"crossHatch\": \"Ristiviivoitus\",\n    \"thin\": \"Ohut\",\n    \"bold\": \"Lihavoitu\",\n    \"left\": \"Vasen\",\n    \"center\": \"Keskitä\",\n    \"right\": \"Oikea\",\n    \"extraBold\": \"Erittäin lihavoitu\",\n    \"architect\": \"Arkkitehti\",\n    \"artist\": \"Taiteilija\",\n    \"cartoonist\": \"Sarjakuva\",\n    \"fileTitle\": \"Tiedostonimi\",\n    \"colorPicker\": \"Värin valinta\",\n    \"canvasColors\": \"Käytössä piirtoalueella\",\n    \"canvasBackground\": \"Piirtoalueen tausta\",\n    \"drawingCanvas\": \"Piirtoalue\",\n    \"layers\": \"Tasot\",\n    \"actions\": \"Toiminnot\",\n    \"language\": \"Kieli\",\n    \"liveCollaboration\": \"Live Yhteistyö...\",\n    \"duplicateSelection\": \"Monista\",\n    \"untitled\": \"Nimetön\",\n    \"name\": \"Nimi\",\n    \"yourName\": \"Nimesi\",\n    \"madeWithExcalidraw\": \"Tehty Excalidrawilla\",\n    \"group\": \"Ryhmitä valinta\",\n    \"ungroup\": \"Pura valittu ryhmä\",\n    \"collaborators\": \"Yhteistyökumppanit\",\n    \"showGrid\": \"Näytä ruudukko\",\n    \"addToLibrary\": \"Lisää kirjastoon\",\n    \"removeFromLibrary\": \"Poista kirjastosta\",\n    \"libraryLoadingMessage\": \"Ladataan kirjastoa…\",\n    \"libraries\": \"Selaa kirjastoja\",\n    \"loadingScene\": \"Ladataan työtä…\",\n    \"align\": \"Tasaa\",\n    \"alignTop\": \"Tasaa ylös\",\n    \"alignBottom\": \"Tasaa alas\",\n    \"alignLeft\": \"Tasaa vasemmalle\",\n    \"alignRight\": \"Tasaa oikealle\",\n    \"centerVertically\": \"Keskitä pystysuunnassa\",\n    \"centerHorizontally\": \"Keskitä vaakasuunnassa\",\n    \"distributeHorizontally\": \"Jaa vaakasuunnassa\",\n    \"distributeVertically\": \"Jaa pystysuunnassa\",\n    \"flipHorizontal\": \"Käännä vaakasuunnassa\",\n    \"flipVertical\": \"Käännä pystysuunnassa\",\n    \"viewMode\": \"Katselutila\",\n    \"share\": \"Jaa\",\n    \"showStroke\": \"Näytä viivan värin valitsin\",\n    \"showBackground\": \"Näytä taustavärin valitsin\",\n    \"toggleTheme\": \"Vaihda teema\",\n    \"personalLib\": \"Oma kirjasto\",\n    \"excalidrawLib\": \"Excalidraw kirjasto\",\n    \"decreaseFontSize\": \"Pienennä kirjasinkokoa\",\n    \"increaseFontSize\": \"Kasvata kirjasinkokoa\",\n    \"unbindText\": \"Irroita teksti\",\n    \"bindText\": \"Kiinnitä teksti säiliöön\",\n    \"createContainerFromText\": \"\",\n    \"link\": {\n      \"edit\": \"Muokkaa linkkiä\",\n      \"editEmbed\": \"\",\n      \"create\": \"Luo linkki\",\n      \"createEmbed\": \"\",\n      \"label\": \"Linkki\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Muokkaa riviä\",\n      \"exit\": \"Poistu rivieditorista\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Lukitse\",\n      \"unlock\": \"Poista lukitus\",\n      \"lockAll\": \"Lukitse kaikki\",\n      \"unlockAll\": \"Poista lukitus kaikista\"\n    },\n    \"statusPublished\": \"Julkaistu\",\n    \"sidebarLock\": \"Pidä sivupalkki avoinna\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Kirjastossa ei ole vielä yhtään kohdetta...\",\n    \"hint_emptyLibrary\": \"Valitse lisättävä kohde piirtoalueelta, tai asenna alta julkinen kirjasto.\",\n    \"hint_emptyPrivateLibrary\": \"Valitse lisättävä kohde piirtoalueelta.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Tyhjennä piirtoalue\",\n    \"exportJSON\": \"Vie tiedostoon\",\n    \"exportImage\": \"Vie kuva...\",\n    \"export\": \"Tallenna nimellä...\",\n    \"copyToClipboard\": \"Kopioi leikepöydälle\",\n    \"save\": \"Tallenna nykyiseen tiedostoon\",\n    \"saveAs\": \"Tallenna nimellä\",\n    \"load\": \"Avaa\",\n    \"getShareableLink\": \"Hae jaettava linkki\",\n    \"close\": \"Sulje\",\n    \"selectLanguage\": \"Valitse kieli\",\n    \"scrollBackToContent\": \"Näytä sisältö\",\n    \"zoomIn\": \"Lähennä\",\n    \"zoomOut\": \"Loitonna\",\n    \"resetZoom\": \"Nollaa suurennuksen taso\",\n    \"menu\": \"Valikko\",\n    \"done\": \"Valmis\",\n    \"edit\": \"Muokkaa\",\n    \"undo\": \"Kumoa\",\n    \"redo\": \"Tee uudelleen\",\n    \"resetLibrary\": \"Tyhjennä kirjasto\",\n    \"createNewRoom\": \"Luo huone\",\n    \"fullScreen\": \"Koko näyttö\",\n    \"darkMode\": \"Tumma tila\",\n    \"lightMode\": \"Vaalea tila\",\n    \"zenMode\": \"Zen-tila\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Poistu zen-tilasta\",\n    \"cancel\": \"Peruuta\",\n    \"clear\": \"Pyyhi\",\n    \"remove\": \"Poista\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Julkaise\",\n    \"submit\": \"Lähetä\",\n    \"confirm\": \"Vahvista\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Tämä tyhjentää koko piirtoalueen. Jatketaanko?\",\n    \"couldNotCreateShareableLink\": \"Jaettavan linkin luominen epäonnistui.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Jaettavaa linkkiä ei voitu luoda: teos on liian suuri\",\n    \"couldNotLoadInvalidFile\": \"Virheellistä tiedostoa ei voitu avata\",\n    \"importBackendFailed\": \"Palvelimelta tuonti epäonnistui.\",\n    \"cannotExportEmptyCanvas\": \"Tyhjää piirtoaluetta ei voi viedä.\",\n    \"couldNotCopyToClipboard\": \"Leikepöydälle vieminen epäonnistui.\",\n    \"decryptFailed\": \"Salauksen purkaminen epäonnistui.\",\n    \"uploadedSecurly\": \"Lähetys on turvattu päästä-päähän-salauksella. Excalidrawin palvelin ja kolmannet osapuolet eivät voi lukea sisältöä.\",\n    \"loadSceneOverridePrompt\": \"Ulkopuolisen piirroksen lataaminen korvaa nykyisen sisältösi. Jatketaanko?\",\n    \"collabStopOverridePrompt\": \"Istunnon lopettaminen korvaa aiemman, paikallisesti tallennetun piirustuksen. Jatketaanko?\\n\\n(Jos haluat säilyttää paikallisesti tallennetun piirustuksen, sulje selaimen välilehti lopettamisen sijaan.)\",\n    \"errorAddingToLibrary\": \"Kohdetta ei voitu lisätä kirjastoon\",\n    \"errorRemovingFromLibrary\": \"Kohdetta ei voitu poistaa kirjastosta\",\n    \"confirmAddLibrary\": \"Tämä lisää {{numShapes}} muotoa kirjastoosi. Jatketaanko?\",\n    \"imageDoesNotContainScene\": \"Tämä kuva ei näytä sisältävän piirrostietoja. Oletko ottanut käyttöön piirroksen tallennuksen viennin aikana?\",\n    \"cannotRestoreFromImage\": \"Teosta ei voitu palauttaa tästä kuvatiedostosta\",\n    \"invalidSceneUrl\": \"Teosta ei voitu tuoda annetusta URL-osoitteesta. Tallenne on vioittunut, tai osoitteessa ei ole Excalidraw JSON-dataa.\",\n    \"resetLibrary\": \"Tämä tyhjentää kirjastosi. Jatketaanko?\",\n    \"removeItemsFromsLibrary\": \"Poista {{count}} kohdetta kirjastosta?\",\n    \"invalidEncryptionKey\": \"Salausavaimen on oltava 22 merkkiä pitkä. Live-yhteistyö ei ole käytössä.\",\n    \"collabOfflineWarning\": \"Internet-yhteyttä ei ole saatavilla.\\nMuutoksiasi ei tallenneta!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Tiedostotyyppiä ei tueta.\",\n    \"imageInsertError\": \"Kuvan lisääminen epäonnistui. Yritä myöhemmin uudelleen...\",\n    \"fileTooBig\": \"Tiedosto on liian suuri. Suurin sallittu koko on {{maxSize}}.\",\n    \"svgImageInsertError\": \"SVG- kuvaa ei voitu lisätä. Tiedoston SVG-sisältö näyttää virheelliseltä.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"Virheellinen SVG.\",\n    \"cannotResolveCollabServer\": \"Yhteyden muodostaminen collab-palvelimeen epäonnistui. Virkistä sivu ja yritä uudelleen.\",\n    \"importLibraryError\": \"Kokoelman lataaminen epäonnistui\",\n    \"collabSaveFailed\": \"Ei voitu tallentaan palvelimen tietokantaan. Jos ongelmia esiintyy, sinun kannatta tallentaa tallentaa tiedosto paikallisesti varmistaaksesi, että et menetä työtäsi.\",\n    \"collabSaveFailed_sizeExceeded\": \"Ei voitu tallentaan palvelimen tietokantaan. Jos ongelmia esiintyy, sinun kannatta tallentaa tallentaa tiedosto paikallisesti varmistaaksesi, että et menetä työtäsi.\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Valinta\",\n    \"image\": \"Lisää kuva\",\n    \"rectangle\": \"Suorakulmio\",\n    \"diamond\": \"Vinoneliö\",\n    \"ellipse\": \"Soikio\",\n    \"arrow\": \"Nuoli\",\n    \"line\": \"Viiva\",\n    \"freedraw\": \"Piirrä\",\n    \"text\": \"Teksti\",\n    \"library\": \"Kirjasto\",\n    \"lock\": \"Pidä valittu työkalu aktiivisena piirron jälkeen\",\n    \"penMode\": \"Kynätila - estä kosketus\",\n    \"link\": \"Lisää/päivitä linkki valitulle muodolle\",\n    \"eraser\": \"Poistotyökalu\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"Käsi (panning-työkalu)\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Piirtoalueen toiminnot\",\n    \"selectedShapeActions\": \"Valitun muodon toiminnot\",\n    \"shapes\": \"Muodot\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Piirtoalueen liikuttamiseksi pidä hiiren pyörää tai välilyöntiä pohjassa tai käytä käsityökalua\",\n    \"linearElement\": \"Klikkaa piirtääksesi useampi piste, raahaa piirtääksesi yksittäinen viiva\",\n    \"freeDraw\": \"Paina ja raahaa, päästä irti kun olet valmis\",\n    \"text\": \"Vinkki: voit myös lisätä tekstiä kaksoisnapsauttamalla mihin tahansa valintatyökalulla\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"Kaksoisnapsauta tai paina ENTER muokataksesi tekstiä\",\n    \"text_editing\": \"Paina Escape tai CtrlOrCmd+ENTER lopettaaksesi muokkaamisen\",\n    \"linearElementMulti\": \"Lopeta klikkaamalla viimeistä pistettä, painamalla Escape- tai Enter-näppäintä\",\n    \"lockAngle\": \"Voit rajoittaa kulmaa pitämällä SHIFT-näppäintä alaspainettuna\",\n    \"resize\": \"Voit rajoittaa mittasuhteet pitämällä SHIFT-näppäintä alaspainettuna kun muutat kokoa, pidä ALT-näppäintä alaspainettuna muuttaaksesi kokoa keskipisteen suhteen\",\n    \"resizeImage\": \"Voit muuttaa kokoa vapaasti pitämällä SHIFTiä pohjassa, pidä ALT pohjassa muuttaaksesi kokoa keskipisteen ympäri\",\n    \"rotate\": \"Voit rajoittaa kulman pitämällä SHIFT pohjassa pyörittäessäsi\",\n    \"lineEditor_info\": \"Pidä CtrlOrCmd pohjassa ja kaksoisnapsauta tai paina CtrlOrCmd + Enter muokataksesi pisteitä\",\n    \"lineEditor_pointSelected\": \"Poista piste(et) painamalla delete, monista painamalla CtrlOrCmd+D, tai liikuta raahaamalla\",\n    \"lineEditor_nothingSelected\": \"Valitse muokattava piste (monivalinta pitämällä SHIFT pohjassa), tai paina Alt ja klikkaa lisätäksesi uusia pisteitä\",\n    \"placeImage\": \"Klikkaa asettaaksesi kuvan, tai klikkaa ja raahaa asettaaksesi sen koon manuaalisesti\",\n    \"publishLibrary\": \"Julkaise oma kirjasto\",\n    \"bindTextToElement\": \"Lisää tekstiä painamalla enter\",\n    \"deepBoxSelect\": \"Käytä syvävalintaa ja estä raahaus painamalla CtrlOrCmd\",\n    \"eraserRevert\": \"Pidä Alt alaspainettuna, kumotaksesi merkittyjen elementtien poistamisen\",\n    \"firefox_clipboard_write\": \"Tämä ominaisuus voidaan todennäköisesti ottaa käyttöön asettamalla \\\"dom.events.asyncClipboard.clipboardItem\\\" kohta \\\"true\\\":ksi. Vaihtaaksesi selaimen kohdan Firefoxissa, käy \\\"about:config\\\" sivulla.\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Esikatselua ei voitu näyttää\",\n    \"canvasTooBig\": \"Piirtoalue saattaa olla liian suuri.\",\n    \"canvasTooBigTip\": \"Vinkki: yritä siirtää kaukaisimpia elementtejä hieman lähemmäs toisiaan.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Tapahtui virhe. Yritä <button>sivun lataamista uudelleen.</button>\",\n    \"clearCanvasMessage\": \"Mikäli sivun lataaminen uudelleen ei auta, yritä <button>tyhjentää piirtoalue.</button>\",\n    \"clearCanvasCaveat\": \" Tämä johtaa työn menetykseen \",\n    \"trackedToSentry\": \"Virhe tunnisteella {{eventId}} tallennettiin järjestelmäämme.\",\n    \"openIssueMessage\": \"Olimme varovaisia emmekä sisällyttäneet tietoa piirroksestasi virheeseen. Mikäli piirroksesi ei ole yksityinen, harkitsethan kertovasi meille <button>virheenseurantajärjestelmässämme.</button> Sisällytä alla olevat tiedot kopioimalla ne GitHub-ongelmaan.\",\n    \"sceneContent\": \"Piirroksen tiedot:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Voit kutsua ihmisiä piirrokseesi tekemään yhteistyötä kanssasi.\",\n    \"desc_privacy\": \"Älä huoli, istunto käyttää päästä-päähän-salausta, joten mitä tahansa piirrätkin, se pysyy salassa. Edes palvelimemme eivät näe mitä keksit.\",\n    \"button_startSession\": \"Aloita istunto\",\n    \"button_stopSession\": \"Lopeta istunto\",\n    \"desc_inProgressIntro\": \"Jaettu istunto on nyt käynnissä.\",\n    \"desc_shareLink\": \"Jaa tämä linkki kenelle tahansa, jonka kanssa haluat tehdä yhteistyötä:\",\n    \"desc_exitSession\": \"Istunnon pysäyttäminen katkaisee yhteyden huoneeseen, mutta voit vielä jatkaa työskentelyä paikallisesti. Huomaa, että tämä ei vaikuta muihin käyttäjiin ja he voivat jatkaa oman versionsa parissa työskentelyä.\",\n    \"shareTitle\": \"Liity Excalidraw live-yhteistyöistuntoon\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Virhe\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Tallenna levylle\",\n    \"disk_details\": \"Vie työn tiedot tiedostoon, josta sen voi tuoda myöhemmin.\",\n    \"disk_button\": \"Tallenna tiedostoon\",\n    \"link_title\": \"Jaettava linkki\",\n    \"link_details\": \"Vie vain luku -linkkinä.\",\n    \"link_button\": \"Vie linkkinä\",\n    \"excalidrawplus_description\": \"Tallenna teos Excalidraw+ tilaan.\",\n    \"excalidrawplus_button\": \"Vie\",\n    \"excalidrawplus_exportError\": \"Ei voitu viedä Excalidraw+-palveluun tällä hetkellä...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Lue blogiamme\",\n    \"click\": \"klikkaa\",\n    \"deepSelect\": \"Syvävalinta\",\n    \"deepBoxSelect\": \"Käytä syvävalintaa ja estä raahaus\",\n    \"curvedArrow\": \"Kaareva nuoli\",\n    \"curvedLine\": \"Kaareva viiva\",\n    \"documentation\": \"Käyttöohjeet\",\n    \"doubleClick\": \"kaksoisnapsautus\",\n    \"drag\": \"vedä\",\n    \"editor\": \"Muokkausohjelma\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"Löysitkö ongelman? Kerro meille\",\n    \"howto\": \"Seuraa oppaitamme\",\n    \"or\": \"tai\",\n    \"preventBinding\": \"Estä nuolten kiinnitys\",\n    \"tools\": \"Työkalut\",\n    \"shortcuts\": \"Pikanäppäimet\",\n    \"textFinish\": \"Lopeta muokkaus (tekstieditori)\",\n    \"textNewLine\": \"Lisää uusi rivi (tekstieditori)\",\n    \"title\": \"Ohjeet\",\n    \"view\": \"Näkymä\",\n    \"zoomToFit\": \"Näytä kaikki elementit\",\n    \"zoomToSelection\": \"Näytä valinta\",\n    \"toggleElementLock\": \"Lukitse / poista lukitus valinta\",\n    \"movePageUpDown\": \"Siirrä sivua ylös/alas\",\n    \"movePageLeftRight\": \"Siirrä sivua vasemmalle/oikealle\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Pyyhi piirtoalue\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Julkaise kirjasto\",\n    \"itemName\": \"Kohteen nimi\",\n    \"authorName\": \"Tekijän nimi\",\n    \"githubUsername\": \"GitHub-käyttäjätunnus\",\n    \"twitterUsername\": \"Twitter-käyttäjätunnus\",\n    \"libraryName\": \"Kirjaston nimi\",\n    \"libraryDesc\": \"Kirjaston kuvaus\",\n    \"website\": \"Verkkosivu\",\n    \"placeholder\": {\n      \"authorName\": \"Nimesi tai käyttäjänimesi\",\n      \"libraryName\": \"Kirjastosi nimi\",\n      \"libraryDesc\": \"Kirjaston kuvaus, joka auttaa ihmisiä ymmärtämään sen käyttötarkoitukset\",\n      \"githubHandle\": \"GitHub-tunnuksesi (valinnainen), jotta voit muokata kirjastoa sen jälkeen kun se on lähetetty tarkastettavaksi\",\n      \"twitterHandle\": \"Twitter-tunnus (valinnainen), jotta tiedämme ketä kiittää kun viestimme Twitterissä\",\n      \"website\": \"Linkki henkilökohtaiselle verkkosivustollesi tai muualle (valinnainen)\"\n    },\n    \"errors\": {\n      \"required\": \"Pakollinen\",\n      \"website\": \"Syötä oikeamuotoinen URL-osoite\"\n    },\n    \"noteDescription\": \"Lähetä kirjastosi, jotta se voidaan sisällyttää <link>julkisessa kirjastolistauksessa</link>muiden käyttöön omissa piirrustuksissaan.\",\n    \"noteGuidelines\": \"Kirjasto on ensin hyväksyttävä manuaalisesti. Ole hyvä ja lue <link>ohjeet</link> ennen lähettämistä. Tarvitset GitHub-tilin, jotta voit viestiä ja tehdä muutoksia pyydettäessä, mutta se ei ole ehdottoman välttämätöntä.\",\n    \"noteLicense\": \"Lähettämällä hyväksyt että kirjasto julkaistaan <link>MIT-lisenssin </link>alla, mikä lyhyesti antaa muiden käyttää sitä ilman rajoituksia.\",\n    \"noteItems\": \"Jokaisella kirjaston kohteella on oltava oma nimensä suodatusta varten. Seuraavat kirjaston kohteet sisältyvät:\",\n    \"atleastOneLibItem\": \"Valitse vähintään yksi kirjaston kohde aloittaaksesi\",\n    \"republishWarning\": \"Huom! Osa valituista kohteista on merkitty jo julkaistu/lähetetyiksi. Lähetä kohteita uudelleen vain päivitettäessä olemassa olevaa kirjastoa tai ehdotusta.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Kirjasto lähetetty\",\n    \"content\": \"Kiitos {{authorName}}. Kirjastosi on lähetetty tarkistettavaksi. Voit seurata sen tilaa<link>täällä</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Tyhjennä kirjasto\",\n    \"removeItemsFromLib\": \"Poista valitut kohteet kirjastosta\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Piirroksesi ovat päästä-päähän-salattuja, joten Excalidrawin palvelimet eivät koskaan näe niitä.\",\n    \"link\": \"Blogiartikkeli päästä päähän -salauksesta Excalidraw:ssa\"\n  },\n  \"stats\": {\n    \"angle\": \"Kulma\",\n    \"element\": \"Elementti\",\n    \"elements\": \"Elementit\",\n    \"height\": \"Korkeus\",\n    \"scene\": \"Teos\",\n    \"selected\": \"Valitut\",\n    \"storage\": \"Tallennustila\",\n    \"title\": \"Tilastoja nörteille\",\n    \"total\": \"Yhteensä\",\n    \"version\": \"Versio\",\n    \"versionCopy\": \"Klikkaa kopioidaksesi\",\n    \"versionNotAvailable\": \"Versio ei saatavilla\",\n    \"width\": \"Leveys\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Lisätty kirjastoon\",\n    \"copyStyles\": \"Tyylit kopioitiin.\",\n    \"copyToClipboard\": \"Kopioitiin leikepöydälle.\",\n    \"copyToClipboardAsPng\": \"Kopioitiin {{exportSelection}} leikepöydälle PNG:nä\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Tiedosto tallennettu.\",\n    \"fileSavedToFilename\": \"Tallennettiin kohteeseen {filename}\",\n    \"canvas\": \"piirtoalue\",\n    \"selection\": \"valinta\",\n    \"pasteAsSingleElement\": \"Käytä {{shortcut}} liittääksesi yhtenä elementtinä,\\ntai liittääksesi olemassa olevaan tekstieditoriin\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Läpinäkyvä\",\n    \"black\": \"\",\n    \"white\": \"\",\n    \"red\": \"\",\n    \"pink\": \"\",\n    \"grape\": \"\",\n    \"violet\": \"\",\n    \"gray\": \"\",\n    \"blue\": \"\",\n    \"cyan\": \"\",\n    \"teal\": \"\",\n    \"green\": \"\",\n    \"yellow\": \"\",\n    \"orange\": \"\",\n    \"bronze\": \"\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Kaikki tietosi on tallennettu paikallisesti selaimellesi.\",\n      \"center_heading_plus\": \"Haluatko sen sijaan mennä Excalidraw+:aan?\",\n      \"menuHint\": \"Vie, asetukset, kielet, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Vie, asetukset ja lisää...\",\n      \"center_heading\": \"Kaaviot. Tehty. Yksinkertaiseksi.\",\n      \"toolbarHint\": \"Valitse työkalu ja aloita piirtäminen!\",\n      \"helpHint\": \"Pikanäppäimet & ohje\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"\",\n    \"shades\": \"\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}