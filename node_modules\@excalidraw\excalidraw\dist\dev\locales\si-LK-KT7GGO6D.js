import "../chunk-XDFCUUT6.js";

// locales/si-LK.json
var labels = {
  paste: "\u0D85\u0DBD\u0DC0\u0DB1\u0DCA\u0DB1",
  pasteAsPlaintext: "",
  pasteCharts: "\u0DB4\u0DCA\u200D\u0DBB\u0DC3\u0DCA\u0DAE\u0DCF\u0DBB\u0DBA",
  selectAll: "\u0DC3\u0DD2\u0DBA\u0DBD\u0DCA\u0DBD\u0DB8",
  multiSelect: "\u0DAD\u0DDD\u0DBB\u0DCF \u0D9C\u0DD0\u0DB1\u0DD3\u0DB8\u0DA7 \u0D85\u0D82\u0D9C\u0DBA \u0D91\u0D9A\u0DAD\u0DD4 \u0D9A\u0DBB\u0DB1\u0DCA\u0DB1",
  moveCanvas: "\u0D9A\u0DD0\u0DB1\u0DCA\u0DC0\u0DC3\u0DBA \u0DA0\u0DBD\u0DB1\u0DBA \u0D9A\u0DBB\u0DB1\u0DCA\u0DB1",
  cut: "\u0D9A\u0DB4\u0DB1\u0DCA\u0DB1",
  copy: "\u0DB4\u0DD2\u0DA7\u0DB4\u0DAD\u0DCA \u0D9A\u0DBB\u0DB1\u0DCA\u0DB1",
  copyAsPng: "PNG \u0DBD\u0DD9\u0DC3 \u0DB4\u0DD2\u0DA7\u0DB4\u0DAD\u0DCA \u0D9A\u0DBB\u0DB1\u0DCA\u0DB1",
  copyAsSvg: "SVG \u0DBD\u0DD9\u0DC3 \u0DB4\u0DD2\u0DA7\u0DB4\u0DAD\u0DCA \u0D9A\u0DBB\u0DB1\u0DCA\u0DB1",
  copyText: "",
  copySource: "",
  convertToCode: "",
  bringForward: "\u0D89\u0DAF\u0DD2\u0DBB\u0DD2\u0DBA\u0DA7 \u0D9C\u0DD9\u0DB1\u0DCA\u0DB1",
  sendToBack: "\u0DB4\u0DC3\u0DD4\u0DB4\u0DC3\u0DA7\u0DB8 \u0D9C\u0DD9\u0DB1\u0DD2\u0DBA\u0DB1\u0DCA\u0DB1",
  bringToFront: "\u0D89\u0DAF\u0DD2\u0DBB\u0DD2\u0DBA\u0DA7\u0DB8 \u0D9C\u0DD9\u0DB1\u0DCA\u0DB1",
  sendBackward: "\u0DB4\u0DC3\u0DD4\u0DB4\u0DC3\u0DA7 \u0D9C\u0DD9\u0DB1\u0DD2\u0DBA\u0DB1\u0DCA\u0DB1",
  delete: "\u0DB8\u0D9A\u0DB1\u0DCA\u0DB1",
  copyStyles: "",
  pasteStyles: "",
  stroke: "",
  background: "",
  fill: "",
  strokeWidth: "",
  strokeStyle: "",
  strokeStyle_solid: "",
  strokeStyle_dashed: "",
  strokeStyle_dotted: "",
  sloppiness: "",
  opacity: "",
  textAlign: "",
  edges: "",
  sharp: "",
  round: "",
  arrowheads: "",
  arrowhead_none: "",
  arrowhead_arrow: "",
  arrowhead_bar: "",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "",
  fontFamily: "",
  addWatermark: "",
  handDrawn: "",
  normal: "",
  code: "",
  small: "",
  medium: "",
  large: "",
  veryLarge: "\u0D89\u0DAD\u0DCF \u0DC0\u0DD2\u0DC1\u0DCF\u0DBD",
  solid: "\u0DC0\u0DD2\u0DC1\u0DCF\u0DBD",
  hachure: "\u0DB8\u0DB0\u0DCA\u200D\u0DBA\u0DB8",
  zigzag: "",
  crossHatch: "",
  thin: "\u0D9A\u0DD9\u0DA7\u0DCA\u0DA7\u0DD4",
  bold: "\u0DAD\u0DAF",
  left: "\u0DC0\u0DB8",
  center: "\u0DB8\u0DD0\u0DAF",
  right: "\u0DAF\u0D9A\u0DD4\u0DAB",
  extraBold: "\u0D89\u0DAD\u0DCF \u0DAD\u0DAF",
  architect: "\u0DC0\u0DCF\u0DC3\u0DCA\u0DAD\u0DD4\u0DC0\u0DDA\u0DAF\u0DD3\u0DBA\u0DCF",
  artist: "\u0D9A\u0DBD\u0DCF\u0D9A\u0DBB\u0DD4",
  cartoonist: "\u0DC3\u0DD0\u0D9A\u0DD2\u0DBD\u0DD2\u0DBB\u0DD6\u0D9A\u0DBB\u0DD4",
  fileTitle: "\u0D9C\u0DDC\u0DB1\u0DD4 \u0DB1\u0DCF\u0DB8\u0DBA",
  colorPicker: "\u0DB4\u0DCF\u0DA7 \u0DAD\u0DDD\u0DBB\u0D9A\u0DBA",
  canvasColors: "",
  canvasBackground: "\u0D9A\u0DD0\u0DB1\u0DCA\u0DC0\u0DC3 \u0DB4\u0DC3\u0DD4\u0DB6\u0DD2\u0DB8",
  drawingCanvas: "\u0DA0\u0DD2\u0DAD\u0DCA\u200D\u0DBB\u0D9A \u0D9A\u0DD0\u0DB1\u0DCA\u0DC0\u0DC3\u0DBA",
  layers: "\u0DBD\u0DDA\u0DBA\u0DBB",
  actions: "\u0D9A\u0DCA\u200D\u0DBB\u0DD2\u0DBA\u0DCF\u0D9A\u0DCF\u0DBB\u0D9A\u0DB8",
  language: "\u0DB7\u0DCF\u0DC2\u0DCF\u0DC0 ",
  liveCollaboration: "",
  duplicateSelection: "",
  untitled: "",
  name: "\u0DB1\u0DB8",
  yourName: "",
  madeWithExcalidraw: "",
  group: "",
  ungroup: "",
  collaborators: "",
  showGrid: "",
  addToLibrary: "",
  removeFromLibrary: "",
  libraryLoadingMessage: "",
  libraries: "",
  loadingScene: "",
  align: "",
  alignTop: "",
  alignBottom: "",
  alignLeft: "",
  alignRight: "",
  centerVertically: "",
  centerHorizontally: "",
  distributeHorizontally: "",
  distributeVertically: "",
  flipHorizontal: "",
  flipVertical: "",
  viewMode: "",
  share: "",
  showStroke: "",
  showBackground: "",
  toggleTheme: "",
  personalLib: "",
  excalidrawLib: "",
  decreaseFontSize: "",
  increaseFontSize: "",
  unbindText: "",
  bindText: "",
  createContainerFromText: "",
  link: {
    edit: "",
    editEmbed: "",
    create: "",
    createEmbed: "",
    label: "",
    labelEmbed: "",
    empty: ""
  },
  lineEditor: {
    edit: "",
    exit: ""
  },
  elementLock: {
    lock: "",
    unlock: "",
    lockAll: "",
    unlockAll: ""
  },
  statusPublished: "",
  sidebarLock: "",
  selectAllElementsInFrame: "",
  removeAllElementsFromFrame: "",
  eyeDropper: "",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "",
  hint_emptyLibrary: "",
  hint_emptyPrivateLibrary: ""
};
var buttons = {
  clearReset: "",
  exportJSON: "",
  exportImage: "",
  export: "",
  copyToClipboard: "",
  save: "",
  saveAs: "",
  load: "",
  getShareableLink: "",
  close: "",
  selectLanguage: "",
  scrollBackToContent: "",
  zoomIn: "",
  zoomOut: "",
  resetZoom: "",
  menu: "",
  done: "",
  edit: "",
  undo: "",
  redo: "",
  resetLibrary: "",
  createNewRoom: "",
  fullScreen: "",
  darkMode: "",
  lightMode: "",
  zenMode: "",
  objectsSnapMode: "",
  exitZenMode: "",
  cancel: "",
  clear: "",
  remove: "",
  embed: "",
  publishLibrary: "",
  submit: "",
  confirm: "",
  embeddableInteractionButton: ""
};
var alerts = {
  clearReset: "",
  couldNotCreateShareableLink: "",
  couldNotCreateShareableLinkTooBig: "",
  couldNotLoadInvalidFile: "",
  importBackendFailed: "",
  cannotExportEmptyCanvas: "",
  couldNotCopyToClipboard: "",
  decryptFailed: "",
  uploadedSecurly: "",
  loadSceneOverridePrompt: "",
  collabStopOverridePrompt: "",
  errorAddingToLibrary: "",
  errorRemovingFromLibrary: "",
  confirmAddLibrary: "",
  imageDoesNotContainScene: "",
  cannotRestoreFromImage: "",
  invalidSceneUrl: "",
  resetLibrary: "",
  removeItemsFromsLibrary: "",
  invalidEncryptionKey: "",
  collabOfflineWarning: ""
};
var errors = {
  unsupportedFileType: "",
  imageInsertError: "",
  fileTooBig: "",
  svgImageInsertError: "",
  failedToFetchImage: "",
  invalidSVGString: "",
  cannotResolveCollabServer: "",
  importLibraryError: "",
  collabSaveFailed: "",
  collabSaveFailed_sizeExceeded: "",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "",
    line2: "",
    line3: "",
    line4: ""
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "",
    image: ""
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "",
  image: "",
  rectangle: "",
  diamond: "",
  ellipse: "",
  arrow: "",
  line: "",
  freedraw: "",
  text: "",
  library: "",
  lock: "",
  penMode: "",
  link: "",
  eraser: "",
  frame: "",
  magicframe: "",
  embeddable: "",
  laser: "",
  hand: "",
  extraTools: "",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "",
  selectedShapeActions: "",
  shapes: ""
};
var hints = {
  canvasPanning: "",
  linearElement: "",
  freeDraw: "",
  text: "",
  embeddable: "",
  text_selected: "",
  text_editing: "",
  linearElementMulti: "",
  lockAngle: "",
  resize: "",
  resizeImage: "",
  rotate: "",
  lineEditor_info: "",
  lineEditor_pointSelected: "",
  lineEditor_nothingSelected: "",
  placeImage: "",
  publishLibrary: "",
  bindTextToElement: "",
  deepBoxSelect: "",
  eraserRevert: "",
  firefox_clipboard_write: "",
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "",
  canvasTooBig: "",
  canvasTooBigTip: ""
};
var errorSplash = {
  headingMain: "",
  clearCanvasMessage: "",
  clearCanvasCaveat: "",
  trackedToSentry: "",
  openIssueMessage: "",
  sceneContent: ""
};
var roomDialog = {
  desc_intro: "",
  desc_privacy: "",
  button_startSession: "",
  button_stopSession: "",
  desc_inProgressIntro: "",
  desc_shareLink: "",
  desc_exitSession: "",
  shareTitle: ""
};
var errorDialog = {
  title: ""
};
var exportDialog = {
  disk_title: "",
  disk_details: "",
  disk_button: "",
  link_title: "",
  link_details: "",
  link_button: "",
  excalidrawplus_description: "",
  excalidrawplus_button: "",
  excalidrawplus_exportError: ""
};
var helpDialog = {
  blog: "",
  click: "",
  deepSelect: "",
  deepBoxSelect: "",
  curvedArrow: "",
  curvedLine: "",
  documentation: "",
  doubleClick: "",
  drag: "",
  editor: "",
  editLineArrowPoints: "",
  editText: "",
  github: "",
  howto: "",
  or: "",
  preventBinding: "",
  tools: "",
  shortcuts: "",
  textFinish: "",
  textNewLine: "",
  title: "",
  view: "",
  zoomToFit: "",
  zoomToSelection: "",
  toggleElementLock: "",
  movePageUpDown: "",
  movePageLeftRight: ""
};
var clearCanvasDialog = {
  title: ""
};
var publishDialog = {
  title: "",
  itemName: "",
  authorName: "",
  githubUsername: "",
  twitterUsername: "",
  libraryName: "",
  libraryDesc: "",
  website: "",
  placeholder: {
    authorName: "",
    libraryName: "",
    libraryDesc: "",
    githubHandle: "",
    twitterHandle: "",
    website: ""
  },
  errors: {
    required: "",
    website: ""
  },
  noteDescription: "",
  noteGuidelines: "",
  noteLicense: "",
  noteItems: "",
  atleastOneLibItem: "",
  republishWarning: ""
};
var publishSuccessDialog = {
  title: "",
  content: ""
};
var confirmDialog = {
  resetLibrary: "",
  removeItemsFromLib: ""
};
var imageExportDialog = {
  header: "",
  label: {
    withBackground: "",
    onlySelected: "",
    darkMode: "",
    embedScene: "",
    scale: "",
    padding: ""
  },
  tooltip: {
    embedScene: ""
  },
  title: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  },
  button: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  }
};
var encrypted = {
  tooltip: "",
  link: ""
};
var stats = {
  angle: "",
  element: "",
  elements: "",
  height: "",
  scene: "",
  selected: "",
  storage: "",
  title: "",
  total: "",
  version: "",
  versionCopy: "",
  versionNotAvailable: "",
  width: ""
};
var toast = {
  addedToLibrary: "",
  copyStyles: "",
  copyToClipboard: "",
  copyToClipboardAsPng: "",
  fileSaved: "",
  fileSavedToFilename: "",
  canvas: "",
  selection: "",
  pasteAsSingleElement: "",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "",
  black: "",
  white: "",
  red: "",
  pink: "",
  grape: "",
  violet: "",
  gray: "",
  blue: "",
  cyan: "",
  teal: "",
  green: "",
  yellow: "",
  orange: "",
  bronze: ""
};
var welcomeScreen = {
  app: {
    center_heading: "",
    center_heading_plus: "",
    menuHint: ""
  },
  defaults: {
    menuHint: "",
    center_heading: "",
    toolbarHint: "",
    helpHint: ""
  }
};
var colorPicker = {
  mostUsedCustomColors: "",
  colors: "",
  shades: "",
  hexCode: "",
  noShades: ""
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "",
      button: "",
      description: ""
    },
    saveToDisk: {
      title: "",
      button: "",
      description: ""
    },
    excalidrawPlus: {
      title: "",
      button: "",
      description: ""
    }
  },
  modal: {
    loadFromFile: {
      title: "",
      button: "",
      description: ""
    },
    shareableLink: {
      title: "",
      button: "",
      description: ""
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var si_LK_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  si_LK_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=si-LK-KT7GGO6D.js.map
