{"version": 3, "sources": ["../../../locales/de-DE.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Einfügen\",\n    \"pasteAsPlaintext\": \"Als reinen Text einfügen\",\n    \"pasteCharts\": \"Diagramme einfügen\",\n    \"selectAll\": \"Alle auswählen\",\n    \"multiSelect\": \"Element zur Auswahl hinzufügen\",\n    \"moveCanvas\": \"<PERSON>inwand verschieben\",\n    \"cut\": \"Ausschneiden\",\n    \"copy\": \"Kopieren\",\n    \"copyAsPng\": \"In Zwischenablage kopieren (PNG)\",\n    \"copyAsSvg\": \"In Zwischenablage kopieren (SVG)\",\n    \"copyText\": \"In die Zwischenablage als Text kopieren\",\n    \"copySource\": \"Quelle in Zwischenablage kopieren\",\n    \"convertToCode\": \"In Code konvertieren\",\n    \"bringForward\": \"Nach vorne\",\n    \"sendToBack\": \"In den Hintergrund\",\n    \"bringToFront\": \"In den Vordergrund\",\n    \"sendBackward\": \"Nach hinten\",\n    \"delete\": \"Löschen\",\n    \"copyStyles\": \"Formatierung kopieren\",\n    \"pasteStyles\": \"Formatierung übernehmen\",\n    \"stroke\": \"Strich\",\n    \"background\": \"Hintergrund\",\n    \"fill\": \"Füllung\",\n    \"strokeWidth\": \"Strichstärke\",\n    \"strokeStyle\": \"Konturstil\",\n    \"strokeStyle_solid\": \"Durchgezogen\",\n    \"strokeStyle_dashed\": \"Gestrichelt\",\n    \"strokeStyle_dotted\": \"Gepunktet\",\n    \"sloppiness\": \"Sauberkeit\",\n    \"opacity\": \"Deckkraft\",\n    \"textAlign\": \"Textausrichtung\",\n    \"edges\": \"Kanten\",\n    \"sharp\": \"Scharf\",\n    \"round\": \"Rund\",\n    \"arrowheads\": \"Pfeilspitzen\",\n    \"arrowhead_none\": \"Keine\",\n    \"arrowhead_arrow\": \"Pfeil\",\n    \"arrowhead_bar\": \"Balken\",\n    \"arrowhead_circle\": \"Kreis\",\n    \"arrowhead_circle_outline\": \"Kreis (Umrandung)\",\n    \"arrowhead_triangle\": \"Dreieck\",\n    \"arrowhead_triangle_outline\": \"Dreieck (Umrandung)\",\n    \"arrowhead_diamond\": \"Raute\",\n    \"arrowhead_diamond_outline\": \"Raute (Umrandung)\",\n    \"fontSize\": \"Schriftgröße\",\n    \"fontFamily\": \"Schriftfamilie\",\n    \"addWatermark\": \"\\\"Made with Excalidraw\\\" hinzufügen\",\n    \"handDrawn\": \"Handgezeichnet\",\n    \"normal\": \"Normal\",\n    \"code\": \"Code\",\n    \"small\": \"Klein\",\n    \"medium\": \"Mittel\",\n    \"large\": \"Groß\",\n    \"veryLarge\": \"Sehr groß\",\n    \"solid\": \"Deckend\",\n    \"hachure\": \"Schraffiert\",\n    \"zigzag\": \"Zickzack\",\n    \"crossHatch\": \"Kreuzschraffiert\",\n    \"thin\": \"Dünn\",\n    \"bold\": \"Fett\",\n    \"left\": \"Links\",\n    \"center\": \"Zentriert\",\n    \"right\": \"Rechts\",\n    \"extraBold\": \"Extra Fett\",\n    \"architect\": \"Architekt\",\n    \"artist\": \"Künstler\",\n    \"cartoonist\": \"Karikaturist\",\n    \"fileTitle\": \"Dateiname\",\n    \"colorPicker\": \"Farbauswähler\",\n    \"canvasColors\": \"Auf Leinwand verwendet\",\n    \"canvasBackground\": \"Zeichenflächenhintergrund\",\n    \"drawingCanvas\": \"Leinwand\",\n    \"layers\": \"Ebenen\",\n    \"actions\": \"Aktionen\",\n    \"language\": \"Sprache\",\n    \"liveCollaboration\": \"Live-Zusammenarbeit...\",\n    \"duplicateSelection\": \"Duplizieren\",\n    \"untitled\": \"Unbenannt\",\n    \"name\": \"Name\",\n    \"yourName\": \"Dein Name\",\n    \"madeWithExcalidraw\": \"Made with Excalidraw\",\n    \"group\": \"Auswahl gruppieren\",\n    \"ungroup\": \"Gruppierung aufheben\",\n    \"collaborators\": \"Mitarbeitende\",\n    \"showGrid\": \"Raster anzeigen\",\n    \"addToLibrary\": \"Zur Bibliothek hinzufügen\",\n    \"removeFromLibrary\": \"Aus Bibliothek entfernen\",\n    \"libraryLoadingMessage\": \"Lade Bibliothek…\",\n    \"libraries\": \"Bibliotheken durchsuchen\",\n    \"loadingScene\": \"Lade Zeichnung…\",\n    \"align\": \"Ausrichten\",\n    \"alignTop\": \"Obere Kanten\",\n    \"alignBottom\": \"Untere Kanten\",\n    \"alignLeft\": \"Linke Kanten\",\n    \"alignRight\": \"Rechte Kanten\",\n    \"centerVertically\": \"Vertikal zentrieren\",\n    \"centerHorizontally\": \"Horizontal zentrieren\",\n    \"distributeHorizontally\": \"Horizontal verteilen\",\n    \"distributeVertically\": \"Vertikal verteilen\",\n    \"flipHorizontal\": \"Horizontal spiegeln\",\n    \"flipVertical\": \"Vertikal spiegeln\",\n    \"viewMode\": \"Ansichtsmodus\",\n    \"share\": \"Teilen\",\n    \"showStroke\": \"Auswahl für Strichfarbe anzeigen\",\n    \"showBackground\": \"Hintergrundfarbe auswählen\",\n    \"toggleTheme\": \"Design umschalten\",\n    \"personalLib\": \"Persönliche Bibliothek\",\n    \"excalidrawLib\": \"Excalidraw Bibliothek\",\n    \"decreaseFontSize\": \"Schriftgröße verkleinern\",\n    \"increaseFontSize\": \"Schrift vergrößern\",\n    \"unbindText\": \"Text lösen\",\n    \"bindText\": \"Text an Container binden\",\n    \"createContainerFromText\": \"Text in Container einbetten\",\n    \"link\": {\n      \"edit\": \"Link bearbeiten\",\n      \"editEmbed\": \"Link bearbeiten & einbetten\",\n      \"create\": \"Link erstellen\",\n      \"createEmbed\": \"Link erstellen & einbetten\",\n      \"label\": \"Link\",\n      \"labelEmbed\": \"Verlinken & einbetten\",\n      \"empty\": \"Kein Link festgelegt\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Linie bearbeiten\",\n      \"exit\": \"Linieneditor verlassen\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Sperren\",\n      \"unlock\": \"Entsperren\",\n      \"lockAll\": \"Alle sperren\",\n      \"unlockAll\": \"Alle entsperren\"\n    },\n    \"statusPublished\": \"Veröffentlicht\",\n    \"sidebarLock\": \"Seitenleiste offen lassen\",\n    \"selectAllElementsInFrame\": \"Alle Elemente im Rahmen auswählen\",\n    \"removeAllElementsFromFrame\": \"Alle Elemente aus dem Rahmen entfernen\",\n    \"eyeDropper\": \"Farbe von der Zeichenfläche auswählen\",\n    \"textToDiagram\": \"Text zu Diagramm\",\n    \"prompt\": \"Eingabe\"\n  },\n  \"library\": {\n    \"noItems\": \"Noch keine Elemente hinzugefügt...\",\n    \"hint_emptyLibrary\": \"Wähle ein Element auf der Zeichenfläche, um es hier hinzuzufügen. Oder installiere eine Bibliothek aus dem öffentlichen Verzeichnis.\",\n    \"hint_emptyPrivateLibrary\": \"Wähle ein Element von der Zeichenfläche, um es hier hinzuzufügen.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Zeichenfläche löschen & Hintergrundfarbe zurücksetzen\",\n    \"exportJSON\": \"In Datei exportieren\",\n    \"exportImage\": \"Exportiere Bild...\",\n    \"export\": \"Speichern als...\",\n    \"copyToClipboard\": \"In Zwischenablage kopieren\",\n    \"save\": \"In aktueller Datei speichern\",\n    \"saveAs\": \"Speichern unter\",\n    \"load\": \"Öffnen\",\n    \"getShareableLink\": \"Teilbaren Link erhalten\",\n    \"close\": \"Schließen\",\n    \"selectLanguage\": \"Sprache auswählen\",\n    \"scrollBackToContent\": \"Zurück zum Inhalt\",\n    \"zoomIn\": \"Vergrößern\",\n    \"zoomOut\": \"Verkleinern\",\n    \"resetZoom\": \"Zoom zurücksetzen\",\n    \"menu\": \"Menü\",\n    \"done\": \"Fertig\",\n    \"edit\": \"Bearbeiten\",\n    \"undo\": \"Rückgängig machen\",\n    \"redo\": \"Wiederholen\",\n    \"resetLibrary\": \"Bibliothek zurücksetzen\",\n    \"createNewRoom\": \"Neuen Raum erstellen\",\n    \"fullScreen\": \"Vollbildanzeige\",\n    \"darkMode\": \"Dunkles Design\",\n    \"lightMode\": \"Helles Design\",\n    \"zenMode\": \"Zen-Modus\",\n    \"objectsSnapMode\": \"Einrasten an Objekten\",\n    \"exitZenMode\": \"Zen-Modus verlassen\",\n    \"cancel\": \"Abbrechen\",\n    \"clear\": \"Löschen\",\n    \"remove\": \"Entfernen\",\n    \"embed\": \"Einbettung umschalten\",\n    \"publishLibrary\": \"Veröffentlichen\",\n    \"submit\": \"Absenden\",\n    \"confirm\": \"Bestätigen\",\n    \"embeddableInteractionButton\": \"Klicken, um zu interagieren\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Dies wird die ganze Zeichenfläche löschen. Bist du dir sicher?\",\n    \"couldNotCreateShareableLink\": \"Konnte keinen teilbaren Link erstellen.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Konnte keinen teilbaren Link erstellen: Die Zeichnung ist zu groß\",\n    \"couldNotLoadInvalidFile\": \"Ungültige Datei konnte nicht geladen werden\",\n    \"importBackendFailed\": \"Import vom Server ist fehlgeschlagen.\",\n    \"cannotExportEmptyCanvas\": \"Leere Zeichenfläche kann nicht exportiert werden.\",\n    \"couldNotCopyToClipboard\": \"Kopieren in die Zwischenablage fehlgeschlagen.\",\n    \"decryptFailed\": \"Daten konnten nicht entschlüsselt werden.\",\n    \"uploadedSecurly\": \"Der Upload wurde mit Ende-zu-Ende-Verschlüsselung gespeichert. Weder Excalidraw noch Dritte können den Inhalt einsehen.\",\n    \"loadSceneOverridePrompt\": \"Das Laden einer externen Zeichnung ersetzt den vorhandenen Inhalt. Möchtest du fortfahren?\",\n    \"collabStopOverridePrompt\": \"Das Stoppen der Sitzung wird deine vorherige, lokal gespeicherte Zeichnung überschreiben. Bist du dir sicher?\\n\\n(Wenn du deine lokale Zeichnung behalten möchtest, schließe stattdessen den Browser-Tab.)\",\n    \"errorAddingToLibrary\": \"Das Element konnte nicht zur Bibliothek hinzugefügt werden\",\n    \"errorRemovingFromLibrary\": \"Das Element konnte nicht aus der Bibliothek entfernt werden\",\n    \"confirmAddLibrary\": \"Dies fügt {{numShapes}} Form(en) zu deiner Bibliothek hinzu. Bist du dir sicher?\",\n    \"imageDoesNotContainScene\": \"Dieses Bild scheint keine Szenendaten zu enthalten. Hast Du das Einbetten der Szene während des Exports aktiviert?\",\n    \"cannotRestoreFromImage\": \"Die Zeichnung konnte aus dieser Bilddatei nicht wiederhergestellt werden\",\n    \"invalidSceneUrl\": \"Die Szene konnte nicht von der angegebenen URL importiert werden. Sie ist entweder fehlerhaft oder enthält keine gültigen Excalidraw JSON-Daten.\",\n    \"resetLibrary\": \"Dieses löscht deine Bibliothek. Bist du sicher?\",\n    \"removeItemsFromsLibrary\": \"{{count}} Element(e) aus der Bibliothek löschen?\",\n    \"invalidEncryptionKey\": \"Verschlüsselungsschlüssel muss 22 Zeichen lang sein. Die Live-Zusammenarbeit ist deaktiviert.\",\n    \"collabOfflineWarning\": \"Keine Internetverbindung verfügbar.\\nDeine Änderungen werden nicht gespeichert!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Nicht unterstützter Dateityp.\",\n    \"imageInsertError\": \"Das Bild konnte nicht eingefügt werden. Versuche es später erneut...\",\n    \"fileTooBig\": \"Die Datei ist zu groß. Die maximal zulässige Größe ist {{maxSize}}.\",\n    \"svgImageInsertError\": \"SVG-Bild konnte nicht eingefügt werden. Das SVG-Markup sieht ungültig aus.\",\n    \"failedToFetchImage\": \"Bild konnte nicht abgerufen werden.\",\n    \"invalidSVGString\": \"Ungültige SVG.\",\n    \"cannotResolveCollabServer\": \"Konnte keine Verbindung zum Collab-Server herstellen. Bitte lade die Seite neu und versuche es erneut.\",\n    \"importLibraryError\": \"Bibliothek konnte nicht geladen werden\",\n    \"collabSaveFailed\": \"Keine Speicherung in der Backend-Datenbank möglich. Wenn die Probleme weiterhin bestehen, solltest Du Deine Datei lokal speichern, um sicherzustellen, dass Du Deine Arbeit nicht verlierst.\",\n    \"collabSaveFailed_sizeExceeded\": \"Keine Speicherung in der Backend-Datenbank möglich, die Zeichenfläche scheint zu groß zu sein. Du solltest Deine Datei lokal speichern, um sicherzustellen, dass Du Deine Arbeit nicht verlierst.\",\n    \"imageToolNotSupported\": \"Bilder sind deaktiviert.\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Sieht so aus, als ob Du den Brave-Browser verwendest und die <bold>aggressive Blockierung von Fingerabdrücken</bold> aktiviert hast.\",\n      \"line2\": \"Dies könnte dazu führen, dass die <bold>Textelemente</bold> in Ihren Zeichnungen zerstört werden.\",\n      \"line3\": \"Wir empfehlen dringend, diese Einstellung zu deaktivieren. Dazu kannst Du <link>diesen Schritten</link> folgen.\",\n      \"line4\": \"Wenn die Deaktivierung dieser Einstellung die fehlerhafte Anzeige von Textelementen nicht behebt, öffne bitte ein <issueLink>Ticket</issueLink> auf unserem GitHub oder schreibe uns auf <discordLink>Discord</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"Einbettbare Elemente können der Bibliothek nicht hinzugefügt werden.\",\n      \"iframe\": \"IFrame-Elemente können nicht zur Bibliothek hinzugefügt werden.\",\n      \"image\": \"Unterstützung für das Hinzufügen von Bildern in die Bibliothek kommt bald!\"\n    },\n    \"asyncPasteFailedOnRead\": \"Einfügen fehlgeschlagen (konnte aus der Zwischenablage des Systems nicht gelesen werden).\",\n    \"asyncPasteFailedOnParse\": \"Einfügen fehlgeschlagen.\",\n    \"copyToSystemClipboardFailed\": \"Kopieren in die Zwischenablage fehlgeschlagen.\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Auswahl\",\n    \"image\": \"Bild einfügen\",\n    \"rectangle\": \"Rechteck\",\n    \"diamond\": \"Raute\",\n    \"ellipse\": \"Ellipse\",\n    \"arrow\": \"Pfeil\",\n    \"line\": \"Linie\",\n    \"freedraw\": \"Zeichnen\",\n    \"text\": \"Text\",\n    \"library\": \"Bibliothek\",\n    \"lock\": \"Ausgewähltes Werkzeug nach Zeichnen aktiv lassen\",\n    \"penMode\": \"Stift-Modus - Berührung verhindern\",\n    \"link\": \"Link für ausgewählte Form hinzufügen / aktualisieren\",\n    \"eraser\": \"Radierer\",\n    \"frame\": \"Rahmenwerkzeug\",\n    \"magicframe\": \"Wireframe zu Code\",\n    \"embeddable\": \"Web-Einbettung\",\n    \"laser\": \"Laserpointer\",\n    \"hand\": \"Hand (Schwenkwerkzeug)\",\n    \"extraTools\": \"Weitere Werkzeuge\",\n    \"mermaidToExcalidraw\": \"Mermaid zu Excalidraw\",\n    \"magicSettings\": \"KI-Einstellungen\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Aktionen für Zeichenfläche\",\n    \"selectedShapeActions\": \"Aktionen für Auswahl\",\n    \"shapes\": \"Formen\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Um die Zeichenfläche zu verschieben, halte das Mausrad oder die Leertaste während des Ziehens, oder verwende das Hand-Werkzeug\",\n    \"linearElement\": \"Klicken für Linie mit mehreren Punkten, Ziehen für einzelne Linie\",\n    \"freeDraw\": \"Klicke und ziehe. Lass los, wenn du fertig bist\",\n    \"text\": \"Tipp: Du kannst auch Text hinzufügen, indem du mit dem Auswahlwerkzeug auf eine beliebige Stelle doppelklickst\",\n    \"embeddable\": \"Klicken und ziehen, um eine Webseiten-Einbettung zu erstellen\",\n    \"text_selected\": \"Doppelklicken oder Eingabetaste drücken, um Text zu bearbeiten\",\n    \"text_editing\": \"Drücke Escape oder CtrlOrCmd+Eingabetaste, um die Bearbeitung abzuschließen\",\n    \"linearElementMulti\": \"Zum Beenden auf den letzten Punkt klicken oder Escape oder Eingabe drücken\",\n    \"lockAngle\": \"Du kannst Winkel einschränken, indem du SHIFT gedrückt hältst\",\n    \"resize\": \"Du kannst die Proportionen einschränken, indem du SHIFT während der Größenänderung gedrückt hältst. Halte ALT gedrückt, um die Größe vom Zentrum aus zu ändern\",\n    \"resizeImage\": \"Du kannst die Größe frei ändern, indem du SHIFT gedrückt hältst; halte ALT, um die Größe vom Zentrum aus zu ändern\",\n    \"rotate\": \"Du kannst Winkel einschränken, indem du SHIFT während der Drehung gedrückt hältst\",\n    \"lineEditor_info\": \"CtrlOrCmd halten und Doppelklick oder CtrlOrCmd + Eingabe drücken, um Punkte zu bearbeiten\",\n    \"lineEditor_pointSelected\": \"Drücke Löschen, um Punkt(e) zu entfernen, CtrlOrCmd+D zum Duplizieren oder ziehe zum Verschieben\",\n    \"lineEditor_nothingSelected\": \"Wähle einen zu bearbeitenden Punkt (halte SHIFT gedrückt um mehrere Punkte auszuwählen),\\noder halte Alt gedrückt und klicke um neue Punkte hinzuzufügen\",\n    \"placeImage\": \"Klicken, um das Bild zu platzieren oder klicken und ziehen um seine Größe manuell zu setzen\",\n    \"publishLibrary\": \"Veröffentliche deine eigene Bibliothek\",\n    \"bindTextToElement\": \"Zum Hinzufügen Eingabetaste drücken\",\n    \"deepBoxSelect\": \"Halte CtrlOrCmd gedrückt, um innerhalb der Gruppe auszuwählen, und um Ziehen zu vermeiden\",\n    \"eraserRevert\": \"Halte Alt gedrückt, um die zum Löschen markierten Elemente zurückzusetzen\",\n    \"firefox_clipboard_write\": \"Diese Funktion kann wahrscheinlich aktiviert werden, indem die Einstellung \\\"dom.events.asyncClipboard.clipboardItem\\\" auf \\\"true\\\" gesetzt wird. Um die Browsereinstellungen in Firefox zu ändern, besuche die Seite \\\"about:config\\\".\",\n    \"disableSnapping\": \"Halte CtrlOrCmd gedrückt, um das Einrasten zu deaktivieren\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Vorschau kann nicht angezeigt werden\",\n    \"canvasTooBig\": \"Die Leinwand ist möglicherweise zu groß.\",\n    \"canvasTooBigTip\": \"Tipp: Schiebe die am weitesten entfernten Elemente ein wenig näher zusammen.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Es ist ein Fehler aufgetreten. Versuche <button>die Seite neu zu laden.</button>\",\n    \"clearCanvasMessage\": \"Wenn das Neuladen nicht funktioniert, versuche <button>die Zeichenfläche zu löschen.</button>\",\n    \"clearCanvasCaveat\": \" Dies wird zum Verlust von Daten führen \",\n    \"trackedToSentry\": \"Der Fehler mit der Kennung {{eventId}} wurde in unserem System registriert.\",\n    \"openIssueMessage\": \"Wir waren sehr vorsichtig und haben deine Zeichnungsinformationen nicht in die Fehlerinformationen aufgenommen. Wenn deine Zeichnung nicht privat ist, unterstütze uns bitte über unseren <button>Bug-Tracker</button>. Bitte teile die unten stehenden Informationen mit uns im GitHub Issue (Kopieren und Einfügen).\",\n    \"sceneContent\": \"Zeichnungsinhalt:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Du kannst Leute zu deiner aktuellen Zeichnung einladen um mit ihnen zusammenzuarbeiten.\",\n    \"desc_privacy\": \"Keine Sorge, die Sitzung nutzt eine Ende-zu-Ende-Verschlüsselung. Alles was du zeichnest, bleibt privat. Auch unser Server sieht nicht, was du dir einfallen lässt.\",\n    \"button_startSession\": \"Sitzung starten\",\n    \"button_stopSession\": \"Sitzung beenden\",\n    \"desc_inProgressIntro\": \"Die Live-Sitzung wird nun ausgeführt.\",\n    \"desc_shareLink\": \"Teile diesen Link mit allen, mit denen du zusammenarbeiten möchtest:\",\n    \"desc_exitSession\": \"Wenn du die Sitzung beendest, wird deine Verbindung zum Raum getrennt. Du kannst jedoch lokal weiter an der Zeichnung arbeiten. Beachte, dass dies keine Auswirkungen auf andere hat und diese weiterhin gemeinsam an ihrer Version arbeiten können.\",\n    \"shareTitle\": \"An einer Live-Kollaborationssitzung auf Excalidraw teilnehmen\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Fehler\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Auf Festplatte speichern\",\n    \"disk_details\": \"Exportiere die Zeichnungsdaten in eine Datei, die Du später importieren kannst.\",\n    \"disk_button\": \"Als Datei speichern\",\n    \"link_title\": \"Teilbarer Link\",\n    \"link_details\": \"Als schreibgeschützten Link exportieren.\",\n    \"link_button\": \"Als Link exportieren\",\n    \"excalidrawplus_description\": \"Speichere die Szene in deinem Excalidraw+ Arbeitsbereich.\",\n    \"excalidrawplus_button\": \"Exportieren\",\n    \"excalidrawplus_exportError\": \"Konnte nicht nach Excalidraw+ exportieren...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Lies unseren Blog\",\n    \"click\": \"klicken\",\n    \"deepSelect\": \"Auswahl innerhalb der Gruppe\",\n    \"deepBoxSelect\": \"Auswahl innerhalb der Gruppe, und Ziehen vermeiden\",\n    \"curvedArrow\": \"Gebogener Pfeil\",\n    \"curvedLine\": \"Gebogene Linie\",\n    \"documentation\": \"Dokumentation\",\n    \"doubleClick\": \"doppelklicken\",\n    \"drag\": \"ziehen\",\n    \"editor\": \"Editor\",\n    \"editLineArrowPoints\": \"Linien-/Pfeil-Punkte bearbeiten\",\n    \"editText\": \"Text bearbeiten / Label hinzufügen\",\n    \"github\": \"Ein Problem gefunden? Informiere uns\",\n    \"howto\": \"Folge unseren Anleitungen\",\n    \"or\": \"oder\",\n    \"preventBinding\": \"Pfeil-Bindung verhindern\",\n    \"tools\": \"Werkzeuge\",\n    \"shortcuts\": \"Tastaturkürzel\",\n    \"textFinish\": \"Bearbeitung beenden (Texteditor)\",\n    \"textNewLine\": \"Neue Zeile hinzufügen (Texteditor)\",\n    \"title\": \"Hilfe\",\n    \"view\": \"Ansicht\",\n    \"zoomToFit\": \"Zoomen um alle Elemente einzupassen\",\n    \"zoomToSelection\": \"Auf Auswahl zoomen\",\n    \"toggleElementLock\": \"Auswahl sperren/entsperren\",\n    \"movePageUpDown\": \"Seite nach oben/unten verschieben\",\n    \"movePageLeftRight\": \"Seite nach links/rechts verschieben\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Zeichenfläche löschen\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Bibliothek veröffentlichen\",\n    \"itemName\": \"Elementname\",\n    \"authorName\": \"Name des Autors\",\n    \"githubUsername\": \"GitHub-Benutzername\",\n    \"twitterUsername\": \"Twitter-Benutzername\",\n    \"libraryName\": \"Name der Bibliothek\",\n    \"libraryDesc\": \"Beschreibung der Bibliothek\",\n    \"website\": \"Webseite\",\n    \"placeholder\": {\n      \"authorName\": \"Dein Name oder Benutzername\",\n      \"libraryName\": \"Name deiner Bibliothek\",\n      \"libraryDesc\": \"Beschreibung deiner Bibliothek, um anderen Nutzern bei der Verwendung zu helfen\",\n      \"githubHandle\": \"GitHub-Handle (optional), damit du die Bibliothek bearbeiten kannst, wenn sie zur Überprüfung eingereicht wurde\",\n      \"twitterHandle\": \"Twitter-Benutzername (optional), damit wir wissen, wen wir bei Werbung über Twitter nennen können\",\n      \"website\": \"Link zu deiner persönlichen Webseite oder zu anderer Seite (optional)\"\n    },\n    \"errors\": {\n      \"required\": \"Erforderlich\",\n      \"website\": \"Gültige URL eingeben\"\n    },\n    \"noteDescription\": \"Sende deine Bibliothek ein, um in die <link>öffentliche Bibliotheks-Repository aufgenommen zu werden</link>damit andere Nutzer sie in ihren Zeichnungen verwenden können.\",\n    \"noteGuidelines\": \"Die Bibliothek muss zuerst manuell freigegeben werden. Bitte lies die <link>Richtlinien</link> vor dem Absenden. Du benötigst ein GitHub-Konto, um zu kommunizieren und Änderungen vorzunehmen, falls erforderlich, aber es ist nicht unbedingt erforderlich.\",\n    \"noteLicense\": \"Mit dem Absenden stimmst du zu, dass die Bibliothek unter der <link>MIT-Lizenz, </link>die zusammengefasst beinhaltet, dass jeder sie ohne Einschränkungen nutzen kann.\",\n    \"noteItems\": \"Jedes Bibliothekselement muss einen eigenen Namen haben, damit es gefiltert werden kann. Die folgenden Bibliothekselemente werden hinzugefügt:\",\n    \"atleastOneLibItem\": \"Bitte wähle mindestens ein Bibliothekselement aus, um zu beginnen\",\n    \"republishWarning\": \"Hinweis: Einige der ausgewählten Elemente sind bereits als veröffentlicht/eingereicht markiert. Du solltest Elemente nur erneut einreichen, wenn Du eine existierende Bibliothek oder Einreichung aktualisierst.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Bibliothek übermittelt\",\n    \"content\": \"Vielen Dank {{authorName}}. Deine Bibliothek wurde zur Überprüfung eingereicht. Du kannst den Status verfolgen<link>hier</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Bibliothek zurücksetzen\",\n    \"removeItemsFromLib\": \"Ausgewählte Elemente aus der Bibliothek entfernen\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Bild exportieren\",\n    \"label\": {\n      \"withBackground\": \"Hintergrund\",\n      \"onlySelected\": \"Nur ausgewählte\",\n      \"darkMode\": \"Dunkler Modus\",\n      \"embedScene\": \"Szene einbetten\",\n      \"scale\": \"Skalierung\",\n      \"padding\": \"Abstand\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"Die Zeichnungsdaten werden in der exportierten PNG/SVG-Datei gespeichert, sodass das Dokument später weiter bearbeitet werden kann. \\nDieses wird die exportierte Datei vergrößern.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Als PNG exportieren\",\n      \"exportToSvg\": \"Als SVG exportieren\",\n      \"copyPngToClipboard\": \"PNG in die Zwischenablage kopieren\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"In Zwischenablage kopieren\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Da deine Zeichnungen Ende-zu-Ende verschlüsselt werden, sehen auch unsere Excalidraw-Server sie niemals.\",\n    \"link\": \"Blogbeitrag über Ende-zu-Ende-Verschlüsselung in Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Winkel\",\n    \"element\": \"Element\",\n    \"elements\": \"Elemente\",\n    \"height\": \"Höhe\",\n    \"scene\": \"Zeichnung\",\n    \"selected\": \"Ausgewählt\",\n    \"storage\": \"Speicher\",\n    \"title\": \"Statistiken für Nerds\",\n    \"total\": \"Gesamt\",\n    \"version\": \"Version\",\n    \"versionCopy\": \"Zum Kopieren klicken\",\n    \"versionNotAvailable\": \"Version nicht verfügbar\",\n    \"width\": \"Breite\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Zur Bibliothek hinzugefügt\",\n    \"copyStyles\": \"Formatierungen kopiert.\",\n    \"copyToClipboard\": \"In die Zwischenablage kopiert.\",\n    \"copyToClipboardAsPng\": \"{{exportSelection}} als PNG in die Zwischenablage kopiert\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Datei gespeichert.\",\n    \"fileSavedToFilename\": \"Als {filename} gespeichert\",\n    \"canvas\": \"Zeichenfläche\",\n    \"selection\": \"Auswahl\",\n    \"pasteAsSingleElement\": \"Verwende {{shortcut}} , um als einzelnes Element\\neinzufügen oder in einen existierenden Texteditor einzufügen\",\n    \"unableToEmbed\": \"Einbetten dieser URL ist derzeit nicht zulässig. Erstelle einen Issue auf GitHub, um die URL freigeben zu lassen\",\n    \"unrecognizedLinkFormat\": \"Der Link, den Du eingebettet hast, stimmt nicht mit dem erwarteten Format überein. Bitte versuche den 'embed' String einzufügen, der von der Quellseite zur Verfügung gestellt wird\"\n  },\n  \"colors\": {\n    \"transparent\": \"Transparent\",\n    \"black\": \"Schwarz\",\n    \"white\": \"Weiß\",\n    \"red\": \"Rot\",\n    \"pink\": \"Pink\",\n    \"grape\": \"Traube\",\n    \"violet\": \"Violett\",\n    \"gray\": \"Grau\",\n    \"blue\": \"Blau\",\n    \"cyan\": \"Cyan\",\n    \"teal\": \"Blaugrün\",\n    \"green\": \"Grün\",\n    \"yellow\": \"Gelb\",\n    \"orange\": \"Orange\",\n    \"bronze\": \"Bronze\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Alle Daten werden lokal in Deinem Browser gespeichert.\",\n      \"center_heading_plus\": \"Möchtest du stattdessen zu Excalidraw+ gehen?\",\n      \"menuHint\": \"Exportieren, Einstellungen, Sprachen, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Exportieren, Einstellungen und mehr...\",\n      \"center_heading\": \"Diagramme. Einfach. Gemacht.\",\n      \"toolbarHint\": \"Wähle ein Werkzeug & beginne zu zeichnen!\",\n      \"helpHint\": \"Kurzbefehle & Hilfe\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Beliebteste benutzerdefinierte Farben\",\n    \"colors\": \"Farben\",\n    \"shades\": \"Schattierungen\",\n    \"hexCode\": \"Hex-Code\",\n    \"noShades\": \"Keine Schattierungen für diese Farbe verfügbar\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Als Bild exportieren\",\n        \"button\": \"Als Bild exportieren\",\n        \"description\": \"Exportiere die Zeichnungsdaten als ein Bild, von dem Du später importieren kannst.\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Auf Festplatte speichern\",\n        \"button\": \"Auf Festplatte speichern\",\n        \"description\": \"Exportiere die Zeichnungsdaten in eine Datei, von der Du später importieren kannst.\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Export nach Excalidraw+\",\n        \"description\": \"Speichere die Szene in deinem Excalidraw+-Arbeitsbereich.\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Aus Datei laden\",\n        \"button\": \"Aus Datei laden\",\n        \"description\": \"Das Laden aus einer Datei wird <bold>Deinen vorhandenen Inhalt ersetzen</bold>.<br></br>Du kannst Deine Zeichnung zuerst mit einer der folgenden Optionen sichern.\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Aus Link laden\",\n        \"button\": \"Meinen Inhalt ersetzen\",\n        \"description\": \"Das Laden einer externen Zeichnung wird <bold>Deinen vorhandenen Inhalt ersetzen</bold>.<br></br>Du kannst Deine Zeichnung zuerst mit einer der folgenden Optionen sichern.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"Mermaid zu Excalidraw\",\n    \"button\": \"Einfügen\",\n    \"description\": \"Derzeit werden nur <flowchartLink>Flussdiagramme</flowchartLink>, <sequenceLink>Sequenzdiagramme</sequenceLink> und <classLink>Klassendiagramme</classLink> unterstützt. Die anderen Typen werden als Bild in Excalidraw dargestellt.\",\n    \"syntax\": \"Mermaid-Syntax\",\n    \"preview\": \"Vorschau\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}