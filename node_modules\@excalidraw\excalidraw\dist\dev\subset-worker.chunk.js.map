{"version": 3, "sources": ["../../subset/subset-worker.chunk.ts"], "sourcesContent": ["/**\n * DON'T depend on anything from the outside like `promiseTry`, as this module is part of a separate lazy-loaded chunk.\n *\n * Including anything from the main chunk would include the whole chunk by default.\n * Even it it would be tree-shaken during build, it won't be tree-shaken in dev.\n *\n * In the future consider separating common utils into a separate shared chunk.\n */\n\nimport { Commands, subsetToBinary } from \"./subset-shared.chunk\";\n\n/**\n * Due to this export (and related dynamic import), this worker code will be included in the bundle automatically (as a separate chunk),\n * without the need for esbuild / vite /rollup plugins and special browser / server treatment.\n *\n * `import.meta.url` is undefined in nodejs\n */\nexport const WorkerUrl: URL | undefined = import.meta.url\n  ? new URL(import.meta.url)\n  : undefined;\n\n// run only in the worker context\nif (typeof window === \"undefined\" && typeof self !== \"undefined\") {\n  self.onmessage = async (e: {\n    data: {\n      command: typeof Commands.Subset;\n      arrayBuffer: ArrayBuffer;\n      codePoints: Array<number>;\n    };\n  }) => {\n    switch (e.data.command) {\n      case Commands.Subset:\n        const buffer = await subsetToBinary(\n          e.data.arrayBuffer,\n          e.data.codePoints,\n        );\n\n        self.postMessage(buffer, { transfer: [buffer] });\n        break;\n    }\n  };\n}\n"], "mappings": ";;;;;;;;AAiBO,IAAM,YAA6B,YAAY,MAClD,IAAI,IAAI,YAAY,GAAG,IACvB;AAGJ,IAAI,OAAO,WAAW,eAAe,OAAO,SAAS,aAAa;AAChE,OAAK,YAAY,OAAO,MAMlB;AACJ,YAAQ,EAAE,KAAK,SAAS;AAAA,MACtB,KAAK,SAAS;AACZ,cAAM,SAAS,MAAM;AAAA,UACnB,EAAE,KAAK;AAAA,UACP,EAAE,KAAK;AAAA,QACT;AAEA,aAAK,YAAY,QAAQ,EAAE,UAAU,CAAC,MAAM,EAAE,CAAC;AAC/C;AAAA,IACJ;AAAA,EACF;AACF;", "names": []}