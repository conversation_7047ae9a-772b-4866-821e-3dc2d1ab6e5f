{"version": 3, "sources": ["../../../locales/es-ES.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Pegar\",\n    \"pasteAsPlaintext\": \"Pegar como texto sin formato\",\n    \"pasteCharts\": \"<PERSON>egar gráficos\",\n    \"selectAll\": \"Seleccionar todo\",\n    \"multiSelect\": \"Añadir elemento a la selección\",\n    \"moveCanvas\": \"Mover el lienzo\",\n    \"cut\": \"Cortar\",\n    \"copy\": \"Copiar\",\n    \"copyAsPng\": \"Copiar al portapapeles como PNG\",\n    \"copyAsSvg\": \"Copiar al portapapeles como SVG\",\n    \"copyText\": \"Copiar al portapapeles como texto\",\n    \"copySource\": \"Copiar fuente al portapapeles\",\n    \"convertToCode\": \"Convertir a código\",\n    \"bringForward\": \"Traer hacia delante\",\n    \"sendToBack\": \"Enviar al fondo\",\n    \"bringToFront\": \"Traer al frente\",\n    \"sendBackward\": \"Enviar atrás\",\n    \"delete\": \"<PERSON>rrar\",\n    \"copyStyles\": \"Copiar estilos\",\n    \"pasteStyles\": \"Pegar estilos\",\n    \"stroke\": \"Trazo\",\n    \"background\": \"Fondo\",\n    \"fill\": \"Rellenar\",\n    \"strokeWidth\": \"Grosor del trazo\",\n    \"strokeStyle\": \"Estilo del trazo\",\n    \"strokeStyle_solid\": \"Sólido\",\n    \"strokeStyle_dashed\": \"Discontinua\",\n    \"strokeStyle_dotted\": \"Punteado\",\n    \"sloppiness\": \"Estilo de trazo\",\n    \"opacity\": \"Opacidad\",\n    \"textAlign\": \"Alineado de texto\",\n    \"edges\": \"Bordes\",\n    \"sharp\": \"Afilado\",\n    \"round\": \"Redondo\",\n    \"arrowheads\": \"Puntas de flecha\",\n    \"arrowhead_none\": \"Ninguna\",\n    \"arrowhead_arrow\": \"Flecha\",\n    \"arrowhead_bar\": \"Barra\",\n    \"arrowhead_circle\": \"Círculo\",\n    \"arrowhead_circle_outline\": \"Círculo (contorno)\",\n    \"arrowhead_triangle\": \"Triángulo\",\n    \"arrowhead_triangle_outline\": \"Triángulo (contorno)\",\n    \"arrowhead_diamond\": \"Diamante\",\n    \"arrowhead_diamond_outline\": \"Diamante (contorno)\",\n    \"fontSize\": \"Tamaño de la fuente\",\n    \"fontFamily\": \"Tipo de fuente\",\n    \"addWatermark\": \"Agregar \\\"Hecho con Excalidraw\\\"\",\n    \"handDrawn\": \"Dibujado a mano\",\n    \"normal\": \"Normal\",\n    \"code\": \"Código\",\n    \"small\": \"Pequeña\",\n    \"medium\": \"Mediana\",\n    \"large\": \"Grande\",\n    \"veryLarge\": \"Muy grande\",\n    \"solid\": \"Sólido\",\n    \"hachure\": \"Folleto\",\n    \"zigzag\": \"Zigzag\",\n    \"crossHatch\": \"Rayado transversal\",\n    \"thin\": \"Fino\",\n    \"bold\": \"Grueso\",\n    \"left\": \"Izquierda\",\n    \"center\": \"Centrado\",\n    \"right\": \"Derecha\",\n    \"extraBold\": \"Extra negrita\",\n    \"architect\": \"Arquitecto\",\n    \"artist\": \"Artista\",\n    \"cartoonist\": \"Caricatura\",\n    \"fileTitle\": \"Nombre del archivo\",\n    \"colorPicker\": \"Selector de color\",\n    \"canvasColors\": \"Usado en lienzo\",\n    \"canvasBackground\": \"Fondo del lienzo\",\n    \"drawingCanvas\": \"Lienzo de dibujo\",\n    \"layers\": \"Capas\",\n    \"actions\": \"Acciones\",\n    \"language\": \"Idioma\",\n    \"liveCollaboration\": \"Colaboración en directo...\",\n    \"duplicateSelection\": \"Duplicar\",\n    \"untitled\": \"Sin título\",\n    \"name\": \"Nombre\",\n    \"yourName\": \"Tu nombre\",\n    \"madeWithExcalidraw\": \"Hecho con Excalidraw\",\n    \"group\": \"Agrupar selección\",\n    \"ungroup\": \"Desagrupar selección\",\n    \"collaborators\": \"Colaboradores\",\n    \"showGrid\": \"Mostrar cuadrícula\",\n    \"addToLibrary\": \"Añadir a la biblioteca\",\n    \"removeFromLibrary\": \"Eliminar de la biblioteca\",\n    \"libraryLoadingMessage\": \"Cargando biblioteca…\",\n    \"libraries\": \"Explorar bibliotecas\",\n    \"loadingScene\": \"Cargando escena…\",\n    \"align\": \"Alinear\",\n    \"alignTop\": \"Alineación superior\",\n    \"alignBottom\": \"Alineación inferior\",\n    \"alignLeft\": \"Alinear a la izquierda\",\n    \"alignRight\": \"Alinear a la derecha\",\n    \"centerVertically\": \"Centrar verticalmente\",\n    \"centerHorizontally\": \"Centrar horizontalmente\",\n    \"distributeHorizontally\": \"Distribuir horizontalmente\",\n    \"distributeVertically\": \"Distribuir verticalmente\",\n    \"flipHorizontal\": \"Girar horizontalmente\",\n    \"flipVertical\": \"Girar verticalmente\",\n    \"viewMode\": \"Modo presentación\",\n    \"share\": \"Compartir\",\n    \"showStroke\": \"Mostrar selector de color de trazo\",\n    \"showBackground\": \"Mostrar el selector de color de fondo\",\n    \"toggleTheme\": \"Cambiar tema\",\n    \"personalLib\": \"Biblioteca personal\",\n    \"excalidrawLib\": \"Biblioteca Excalidraw\",\n    \"decreaseFontSize\": \"Disminuir tamaño de letra\",\n    \"increaseFontSize\": \"Aumentar el tamaño de letra\",\n    \"unbindText\": \"Desvincular texto\",\n    \"bindText\": \"Vincular texto al contenedor\",\n    \"createContainerFromText\": \"Envolver el texto en un contenedor\",\n    \"link\": {\n      \"edit\": \"Editar enlace\",\n      \"editEmbed\": \"Editar enlace e incrustar\",\n      \"create\": \"Crear enlace\",\n      \"createEmbed\": \"Crear enlace e incrustar\",\n      \"label\": \"Enlace\",\n      \"labelEmbed\": \"Enlazar e incrustar\",\n      \"empty\": \"No se ha establecido un enlace\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Editar línea\",\n      \"exit\": \"Salir del editor en línea\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Bloquear\",\n      \"unlock\": \"Desbloquear\",\n      \"lockAll\": \"Bloquear todo\",\n      \"unlockAll\": \"Desbloquear todo\"\n    },\n    \"statusPublished\": \"Publicado\",\n    \"sidebarLock\": \"Mantener barra lateral abierta\",\n    \"selectAllElementsInFrame\": \"Seleccionar todos los elementos en el marco\",\n    \"removeAllElementsFromFrame\": \"Eliminar todos los elementos del marco\",\n    \"eyeDropper\": \"Seleccionar un color del lienzo\",\n    \"textToDiagram\": \"Texto a diagrama\",\n    \"prompt\": \"Sugerencia\"\n  },\n  \"library\": {\n    \"noItems\": \"No hay elementos añadidos todavía...\",\n    \"hint_emptyLibrary\": \"Seleccione un elemento en el lienzo para añadirlo aquí, o instale una biblioteca del repositorio público, a continuación.\",\n    \"hint_emptyPrivateLibrary\": \"Seleccione un elemento del lienzo para añadirlo aquí.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Limpiar lienzo y reiniciar el color de fondo\",\n    \"exportJSON\": \"Exportar a archivo\",\n    \"exportImage\": \"Exportar imagen...\",\n    \"export\": \"Guardar en...\",\n    \"copyToClipboard\": \"Copiar al portapapeles\",\n    \"save\": \"Guardar en archivo actual\",\n    \"saveAs\": \"Guardar como\",\n    \"load\": \"Abrir\",\n    \"getShareableLink\": \"Obtener enlace para compartir\",\n    \"close\": \"Cerrar\",\n    \"selectLanguage\": \"Elegir idioma\",\n    \"scrollBackToContent\": \"Volver al contenido\",\n    \"zoomIn\": \"Acercarse\",\n    \"zoomOut\": \"Alejarse\",\n    \"resetZoom\": \"Restablecer zoom\",\n    \"menu\": \"Menú\",\n    \"done\": \"Hecho\",\n    \"edit\": \"Editar\",\n    \"undo\": \"Deshacer\",\n    \"redo\": \"Rehacer\",\n    \"resetLibrary\": \"Reiniciar biblioteca\",\n    \"createNewRoom\": \"Crear nueva sala\",\n    \"fullScreen\": \"Pantalla completa\",\n    \"darkMode\": \"Modo oscuro\",\n    \"lightMode\": \"Modo claro\",\n    \"zenMode\": \"Modo Zen\",\n    \"objectsSnapMode\": \"Ajustar a los objetos\",\n    \"exitZenMode\": \"Salir del modo Zen\",\n    \"cancel\": \"Cancelar\",\n    \"clear\": \"Borrar\",\n    \"remove\": \"Eliminar\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Publicar\",\n    \"submit\": \"Enviar\",\n    \"confirm\": \"Confirmar\",\n    \"embeddableInteractionButton\": \"Pulsa para interactuar\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Esto limpiará todo el lienzo. Estás seguro?\",\n    \"couldNotCreateShareableLink\": \"No se pudo crear un enlace para compartir.\",\n    \"couldNotCreateShareableLinkTooBig\": \"No se pudo crear el enlace para compartir: la escena es demasiado grande\",\n    \"couldNotLoadInvalidFile\": \"No se pudo cargar el archivo no válido\",\n    \"importBackendFailed\": \"La importación falló.\",\n    \"cannotExportEmptyCanvas\": \"No se puede exportar un lienzo vació\",\n    \"couldNotCopyToClipboard\": \"No se pudo copiar al portapapeles.\",\n    \"decryptFailed\": \"No se pudieron descifrar los datos.\",\n    \"uploadedSecurly\": \"La carga ha sido asegurada con cifrado de principio a fin, lo que significa que el servidor de Excalidraw y terceros no pueden leer el contenido.\",\n    \"loadSceneOverridePrompt\": \"Si carga este dibujo externo, reemplazará el que tiene. ¿Desea continuar?\",\n    \"collabStopOverridePrompt\": \"Detener la sesión sobrescribirá su dibujo anterior almacenado localmente. ¿Está seguro?\\n\\n(Si desea mantener su dibujo local, simplemente cierre la pestaña del navegador.)\",\n    \"errorAddingToLibrary\": \"No se pudo agregar el elemento a la biblioteca\",\n    \"errorRemovingFromLibrary\": \"No se pudo quitar el elemento de la biblioteca\",\n    \"confirmAddLibrary\": \"Esto añadirá {{numShapes}} forma(s) a tu biblioteca. ¿Estás seguro?\",\n    \"imageDoesNotContainScene\": \"Esta imagen no parece contener datos de escena. ¿Ha habilitado la inserción de la escena durante la exportación?\",\n    \"cannotRestoreFromImage\": \"No se pudo restaurar la escena desde este archivo de imagen\",\n    \"invalidSceneUrl\": \"No se ha podido importar la escena desde la URL proporcionada. Está mal formada, o no contiene datos de Excalidraw JSON válidos.\",\n    \"resetLibrary\": \"Esto borrará tu biblioteca. ¿Estás seguro?\",\n    \"removeItemsFromsLibrary\": \"¿Eliminar {{count}} elemento(s) de la biblioteca?\",\n    \"invalidEncryptionKey\": \"La clave de cifrado debe tener 22 caracteres. La colaboración en vivo está deshabilitada.\",\n    \"collabOfflineWarning\": \"No hay conexión a internet disponible.\\n¡No se guardarán los cambios!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Tipo de archivo no admitido.\",\n    \"imageInsertError\": \"No se pudo insertar la imagen. Inténtelo de nuevo más tarde...\",\n    \"fileTooBig\": \"Archivo demasiado grande. El tamaño máximo permitido es {{maxSize}}.\",\n    \"svgImageInsertError\": \"No se pudo insertar la imagen SVG. El código SVG parece inválido.\",\n    \"failedToFetchImage\": \"Error al obtener la imagen.\",\n    \"invalidSVGString\": \"SVG no válido.\",\n    \"cannotResolveCollabServer\": \"No se pudo conectar al servidor colaborador. Por favor, vuelva a cargar la página y vuelva a intentarlo.\",\n    \"importLibraryError\": \"No se pudo cargar la librería\",\n    \"collabSaveFailed\": \"No se pudo guardar en la base de datos del backend. Si los problemas persisten, debería guardar su archivo localmente para asegurarse de que no pierde su trabajo.\",\n    \"collabSaveFailed_sizeExceeded\": \"No se pudo guardar en la base de datos del backend, el lienzo parece ser demasiado grande. Debería guardar el archivo localmente para asegurarse de que no pierde su trabajo.\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Parece que estás usando el navegador Brave con el ajuste <bold>Forzar el bloqueo de huellas digitales</bold> habilitado.\",\n      \"line2\": \"Esto podría resultar en errores en los <bold>Elementos de Texto</bold> en tus dibujos.\",\n      \"line3\": \"Recomendamos fuertemente deshabilitar esta configuración. Puedes seguir <link>estos pasos</link> sobre cómo hacerlo.\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"Los elementos IFrame no se pueden agregar a la biblioteca.\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"No se pudo pegar (no se pudo leer desde el portapapeles del sistema).\",\n    \"asyncPasteFailedOnParse\": \"No se pudo pegar.\",\n    \"copyToSystemClipboardFailed\": \"No se pudo copiar al portapapeles.\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Selección\",\n    \"image\": \"Insertar imagen\",\n    \"rectangle\": \"Rectángulo\",\n    \"diamond\": \"Diamante\",\n    \"ellipse\": \"Elipse\",\n    \"arrow\": \"Flecha\",\n    \"line\": \"Línea\",\n    \"freedraw\": \"Dibujar\",\n    \"text\": \"Texto\",\n    \"library\": \"Biblioteca\",\n    \"lock\": \"Mantener la herramienta seleccionada activa después de dibujar\",\n    \"penMode\": \"Modo Lápiz - previene toque\",\n    \"link\": \"Añadir/Actualizar enlace para una forma seleccionada\",\n    \"eraser\": \"Borrar\",\n    \"frame\": \"\",\n    \"magicframe\": \"Esquema a código\",\n    \"embeddable\": \"Incrustar Web\",\n    \"laser\": \"Puntero láser\",\n    \"hand\": \"Mano (herramienta de panoramización)\",\n    \"extraTools\": \"Más herramientas\",\n    \"mermaidToExcalidraw\": \"Mermaid a Excalidraw\",\n    \"magicSettings\": \"Ajustes AI\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Acciones del lienzo\",\n    \"selectedShapeActions\": \"Acciones de la forma seleccionada\",\n    \"shapes\": \"Formas\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Para mover el lienzo, mantenga la rueda del ratón o la barra espaciadora mientras arrastra o utilice la herramienta de mano\",\n    \"linearElement\": \"Haz clic para dibujar múltiples puntos, arrastrar para solo una línea\",\n    \"freeDraw\": \"Haz clic y arrastra, suelta al terminar\",\n    \"text\": \"Consejo: también puedes añadir texto haciendo doble clic en cualquier lugar con la herramienta de selección\",\n    \"embeddable\": \"Haga clic y arrastre para crear un sitio web incrustado\",\n    \"text_selected\": \"Doble clic o pulse ENTER para editar el texto\",\n    \"text_editing\": \"Pulse Escape o Ctrl/Cmd + ENTER para terminar de editar\",\n    \"linearElementMulti\": \"Haz clic en el último punto o presiona Escape o Enter para finalizar\",\n    \"lockAngle\": \"Puedes restringir el ángulo manteniendo presionado el botón SHIFT\",\n    \"resize\": \"Para mantener las proporciones mantén SHIFT presionado mientras modificas el tamaño, \\nmantén presionado ALT para modificar el tamaño desde el centro\",\n    \"resizeImage\": \"Puede redimensionar libremente pulsando SHIFT,\\npulse ALT para redimensionar desde el centro\",\n    \"rotate\": \"Puedes restringir los ángulos manteniendo presionado SHIFT mientras giras\",\n    \"lineEditor_info\": \"Mantenga pulsado CtrlOrCmd y haga doble click o presione CtrlOrCmd + Enter para editar puntos\",\n    \"lineEditor_pointSelected\": \"Presione Suprimir para eliminar el/los punto(s), CtrlOrCmd+D para duplicarlo, o arrástrelo para moverlo\",\n    \"lineEditor_nothingSelected\": \"Seleccione un punto a editar (mantenga MAYÚSCULAS para seleccionar múltiples),\\no mantenga pulsado Alt y haga click para añadir nuevos puntos\",\n    \"placeImage\": \"Haga clic para colocar la imagen o haga click y arrastre para establecer su tamaño manualmente\",\n    \"publishLibrary\": \"Publica tu propia biblioteca\",\n    \"bindTextToElement\": \"Presione Entrar para agregar\",\n    \"deepBoxSelect\": \"Mantén CtrlOrCmd para seleccionar en profundidad, y para evitar arrastrar\",\n    \"eraserRevert\": \"Mantenga pulsado Alt para revertir los elementos marcados para su eliminación\",\n    \"firefox_clipboard_write\": \"Esta característica puede ser habilitada estableciendo la bandera \\\"dom.events.asyncClipboard.clipboardItem\\\" a \\\"true\\\". Para cambiar las banderas del navegador en Firefox, visite la página \\\"about:config\\\".\",\n    \"disableSnapping\": \"Mantén pulsado CtrlOrCmd para desactivar el ajuste\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"No se puede mostrar la vista previa\",\n    \"canvasTooBig\": \"El lienzo podría ser demasiado grande.\",\n    \"canvasTooBigTip\": \"Sugerencia: intenta acercar un poco más los elementos más lejanos.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Se encontró un error. Intente <button>recargando la página.</button>\",\n    \"clearCanvasMessage\": \"Si la recarga no funciona, intente <button>limpiando el lienzo.</button>\",\n    \"clearCanvasCaveat\": \" Esto provocará la pérdida de su trabajo \",\n    \"trackedToSentry\": \"El error con el identificador {{eventId}} fue rastreado en nuestro sistema.\",\n    \"openIssueMessage\": \"Fuimos muy cautelosos de no incluir la información de tu escena en el error. Si tu escena no es privada, por favor considera seguir nuestro <button>rastreador de errores.</button> Por favor, incluya la siguiente información copiándola y pegándola en el issue de GitHub.\",\n    \"sceneContent\": \"Contenido de la escena:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Puede invitar a otras personas a tu actual escena para que colaboren contigo.\",\n    \"desc_privacy\": \"No te preocupes, la sesión usa encriptación de punta a punta, por lo que todo lo que se dibuje se mantendrá privadamente. Ni siquiera nuestro servidor podrá ver lo que haces.\",\n    \"button_startSession\": \"Iniciar sesión\",\n    \"button_stopSession\": \"Detener sesión\",\n    \"desc_inProgressIntro\": \"La sesión de colaboración en vivo está ahora en progreso.\",\n    \"desc_shareLink\": \"Comparte este enlace con cualquier persona con quien quieras colaborar:\",\n    \"desc_exitSession\": \"Detener la sesión te desconectará de la sala, pero podrás seguir trabajando con la escena en su computadora, esto es de modo local. Ten en cuenta que esto no afectará a otras personas, y que las mismas seguirán siendo capaces de colaborar en tu escena.\",\n    \"shareTitle\": \"Únase a una sesión colaborativa en vivo en Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Error\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Guardar en disco\",\n    \"disk_details\": \"Exportar los datos de la escena a un archivo desde el cual pueda importar más tarde.\",\n    \"disk_button\": \"Guardar en archivo\",\n    \"link_title\": \"Enlace para compartir\",\n    \"link_details\": \"Exportar como enlace de sólo lectura.\",\n    \"link_button\": \"Exportar a Link\",\n    \"excalidrawplus_description\": \"Guarde la escena en su espacio de trabajo de Excalidraw+.\",\n    \"excalidrawplus_button\": \"Exportar\",\n    \"excalidrawplus_exportError\": \"No se pudo exportar a Excalidraw+ en este momento...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Lea nuestro blog\",\n    \"click\": \"click\",\n    \"deepSelect\": \"Selección profunda\",\n    \"deepBoxSelect\": \"Seleccione en profundidad dentro de la caja, y evite arrastrar\",\n    \"curvedArrow\": \"Flecha curva\",\n    \"curvedLine\": \"Línea curva\",\n    \"documentation\": \"Documentación\",\n    \"doubleClick\": \"doble clic\",\n    \"drag\": \"arrastrar\",\n    \"editor\": \"Editor\",\n    \"editLineArrowPoints\": \"Editar puntos de línea/flecha\",\n    \"editText\": \"Editar texto / añadir etiqueta\",\n    \"github\": \"¿Ha encontrado un problema? Envíelo\",\n    \"howto\": \"Siga nuestras guías\",\n    \"or\": \"o\",\n    \"preventBinding\": \"Evitar enlace de flechas\",\n    \"tools\": \"Herramientas\",\n    \"shortcuts\": \"Atajos del teclado\",\n    \"textFinish\": \"Finalizar edición (editor de texto)\",\n    \"textNewLine\": \"Añadir nueva linea (editor de texto)\",\n    \"title\": \"Ayuda\",\n    \"view\": \"Vista\",\n    \"zoomToFit\": \"Ajustar la vista para mostrar todos los elementos\",\n    \"zoomToSelection\": \"Ampliar selección\",\n    \"toggleElementLock\": \"Bloquear/desbloquear selección\",\n    \"movePageUpDown\": \"Mover página hacia arriba/abajo\",\n    \"movePageLeftRight\": \"Mover página hacia la izquierda/derecha\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Borrar lienzo\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Publicar biblioteca\",\n    \"itemName\": \"Nombre del artículo\",\n    \"authorName\": \"Nombre del autor\",\n    \"githubUsername\": \"Nombre de usuario de GitHub\",\n    \"twitterUsername\": \"Nombre de usuario de Twitter\",\n    \"libraryName\": \"Nombre de la biblioteca\",\n    \"libraryDesc\": \"Descripción de la biblioteca\",\n    \"website\": \"Sitio Web\",\n    \"placeholder\": {\n      \"authorName\": \"Nombre o nombre de usuario\",\n      \"libraryName\": \"Nombre de tu biblioteca\",\n      \"libraryDesc\": \"Descripción de su biblioteca para ayudar a la gente a entender su uso\",\n      \"githubHandle\": \"Nombre de usuario de GitHub (opcional), así podrá editar la biblioteca una vez enviada para su revisión\",\n      \"twitterHandle\": \"Nombre de usuario de Twitter (opcional), así sabemos a quién acreditar cuando se promociona en Twitter\",\n      \"website\": \"Enlace a su sitio web personal o en cualquier otro lugar (opcional)\"\n    },\n    \"errors\": {\n      \"required\": \"Requerido\",\n      \"website\": \"Introduce una URL válida\"\n    },\n    \"noteDescription\": \"Envía tu biblioteca para ser incluida en el <link>repositorio de librería pública</link>para que otras personas utilicen en sus dibujos.\",\n    \"noteGuidelines\": \"La biblioteca debe ser aprobada manualmente primero. Por favor, lea la <link>pautas</link> antes de enviar. Necesitará una cuenta de GitHub para comunicarse y hacer cambios si se solicita, pero no es estrictamente necesario.\",\n    \"noteLicense\": \"Al enviar, usted acepta que la biblioteca se publicará bajo el <link>Licencia MIT </link>que en breve significa que cualquiera puede utilizarlos sin restricciones.\",\n    \"noteItems\": \"Cada elemento de la biblioteca debe tener su propio nombre para que sea filtrable. Los siguientes elementos de la biblioteca serán incluidos:\",\n    \"atleastOneLibItem\": \"Por favor, seleccione al menos un elemento de la biblioteca para empezar\",\n    \"republishWarning\": \"Nota: algunos de los elementos seleccionados están marcados como ya publicados/enviados. Sólo debería volver a enviar elementos cuando se actualice una biblioteca o envío.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Biblioteca enviada\",\n    \"content\": \"Gracias {{authorName}}. Su biblioteca ha sido enviada para ser revisada. Puede seguir el estado<link>aquí</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Reiniciar biblioteca\",\n    \"removeItemsFromLib\": \"Eliminar elementos seleccionados de la biblioteca\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Exportar imagen\",\n    \"label\": {\n      \"withBackground\": \"Fondo\",\n      \"onlySelected\": \"Sólo seleccionados\",\n      \"darkMode\": \"Modo oscuro\",\n      \"embedScene\": \"Incrustar escena\",\n      \"scale\": \"Escalar\",\n      \"padding\": \"Espaciado\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Exportar a PNG\",\n      \"exportToSvg\": \"Exportar a SVG\",\n      \"copyPngToClipboard\": \"Copiar PNG al portapapeles\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Copiar al portapapeles\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Tus dibujos están cifrados de punto a punto, por lo que los servidores de Excalidraw nunca los verán.\",\n    \"link\": \"Entrada en el blog sobre cifrado de extremo a extremo\"\n  },\n  \"stats\": {\n    \"angle\": \"Ángulo\",\n    \"element\": \"Elemento\",\n    \"elements\": \"Elementos\",\n    \"height\": \"Alto\",\n    \"scene\": \"Escena\",\n    \"selected\": \"Seleccionado\",\n    \"storage\": \"Almacenamiento\",\n    \"title\": \"Estadísticas para nerds\",\n    \"total\": \"Total\",\n    \"version\": \"Versión\",\n    \"versionCopy\": \"Click para copiar\",\n    \"versionNotAvailable\": \"Versión no disponible\",\n    \"width\": \"Ancho\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Añadido a la biblioteca\",\n    \"copyStyles\": \"Estilos copiados.\",\n    \"copyToClipboard\": \"Copiado en el portapapeles.\",\n    \"copyToClipboardAsPng\": \"Copiado {{exportSelection}} al portapapeles como PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Archivo guardado.\",\n    \"fileSavedToFilename\": \"Guardado en {filename}\",\n    \"canvas\": \"lienzo\",\n    \"selection\": \"selección\",\n    \"pasteAsSingleElement\": \"Usa {{shortcut}} para pegar como un solo elemento,\\no pegar en un editor de texto existente\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Transparente\",\n    \"black\": \"Negro\",\n    \"white\": \"Blanco\",\n    \"red\": \"Rojo\",\n    \"pink\": \"Rosa\",\n    \"grape\": \"Uva\",\n    \"violet\": \"Violeta\",\n    \"gray\": \"Gris\",\n    \"blue\": \"Azul\",\n    \"cyan\": \"Cian\",\n    \"teal\": \"Turquesa\",\n    \"green\": \"Verde\",\n    \"yellow\": \"Amarillo\",\n    \"orange\": \"Naranja\",\n    \"bronze\": \"Bronce\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Toda su información es guardada localmente en su navegador.\",\n      \"center_heading_plus\": \"¿Quieres ir a Excalidraw+?\",\n      \"menuHint\": \"Exportar, preferencias, idiomas, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Exportar, preferencias y más...\",\n      \"center_heading\": \"Diagramas. Hecho. Simplemente.\",\n      \"toolbarHint\": \"¡Elige una herramienta y empieza a dibujar!\",\n      \"helpHint\": \"Atajos y ayuda\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Colores personalizados más utilizados\",\n    \"colors\": \"Colores\",\n    \"shades\": \"\",\n    \"hexCode\": \"Código Hexadecimal\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Exportar como imagen\",\n        \"button\": \"Exportar como imagen\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Guardar en el disco\",\n        \"button\": \"Guardar en el disco\",\n        \"description\": \"Exporta los datos de la escena a un archivo desde el cual podrás importar más tarde.\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"Exportar a Excalidraw+\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Cargar desde un archivo\",\n        \"button\": \"Cargar desde un archivo\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Cargar desde un enlace\",\n        \"button\": \"Reemplazar mi contenido\",\n        \"description\": \"Cargar un dibujo externo <bold>reemplazará tu contenido existente</bold>.<br></br>Puedes primero hacer una copia de seguridad de tu dibujo usando una de las opciones de abajo.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"Mermaid a Excalidraw\",\n    \"button\": \"Insertar\",\n    \"description\": \"Actualmente sólo <flowchartLink>Flowchart</flowchartLink>,<sequenceLink> Secuencia, </sequenceLink> y <classLink>Class </classLink>Diagramas son soportados. Los otros tipos se renderizarán como imagen en Excalidraw.\",\n    \"syntax\": \"Sintaxis Mermaid\",\n    \"preview\": \"Vista previa\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}