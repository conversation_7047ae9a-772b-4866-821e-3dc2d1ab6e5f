"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/services/socketService.ts":
/*!***************************************!*\
  !*** ./src/services/socketService.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n// client/src/services/socketService.ts\n\n\nconst SOCKET_URL = \"http://localhost:5002\" || 0;\n// Create a singleton socket instance\nclass SocketService {\n    static getInstance() {\n        if (!SocketService.instance) {\n            SocketService.instance = new SocketService();\n        }\n        return SocketService.instance;\n    }\n    initSocket() {\n        try {\n            console.log('Initializing socket connection to', SOCKET_URL);\n            // Remove listeners from old socket if it exists\n            if (this.socket) {\n                this.socket.removeAllListeners();\n                this.socket.disconnect();\n            }\n            // Start with WebSocket only to avoid XHR polling issues in browser\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                reconnectionAttempts: 10,\n                reconnectionDelay: 1000,\n                timeout: 20000,\n                transports: [\n                    'websocket'\n                ],\n                autoConnect: true,\n                forceNew: true,\n                upgrade: false,\n                withCredentials: false,\n                extraHeaders: {\n                    'Access-Control-Allow-Origin': '*'\n                },\n                // Add additional options for better browser compatibility\n                rememberUpgrade: false,\n                rejectUnauthorized: false\n            });\n            console.log('Socket instance created successfully');\n            this.setupEventListeners();\n        } catch (error) {\n            console.error('Error initializing socket:', error);\n        }\n    }\n    setupEventListeners() {\n        if (!this.socket) return;\n        // Clear any existing listeners to prevent duplicates\n        this.socket.removeAllListeners();\n        // Connection events\n        this.socket.on('connect', ()=>{\n            var _this_socket;\n            console.log('Socket connected successfully:', (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.id);\n            this.connected = true;\n            this.emitEvent('connect', null);\n        });\n        this.socket.on('disconnect', (reason)=>{\n            console.log(\"Socket disconnected. Reason: \".concat(reason));\n            this.connected = false;\n            this.emitEvent('disconnect', reason);\n            if (reason !== 'io client disconnect') {\n                console.log('Reconnection attempt will start in 2 seconds...');\n                setTimeout(()=>{\n                    console.log('Attempting to reconnect...');\n                    this.connect();\n                }, 2000);\n            }\n        });\n        // Transport and reconnection events\n        if (this.socket.io) {\n            this.socket.io.on('error', (error)=>{\n                console.error('Transport error:', error);\n            });\n            this.socket.io.on('reconnect_attempt', (attempt)=>{\n                console.log(\"Reconnection attempt \".concat(attempt));\n            });\n            this.socket.io.on('reconnect', (attemptNumber)=>{\n                this.connected = true;\n                this.emitEvent('connect', null);\n            });\n            this.socket.io.on('reconnect_error', (error)=>{\n                console.error('Reconnection error:', error);\n            });\n            this.socket.io.on('reconnect_failed', ()=>{\n                this.emitEvent('error', 'Failed to reconnect after multiple attempts');\n                // Try a different approach after all reconnection attempts fail\n                setTimeout(()=>{\n                    this.initSocket();\n                }, 3000);\n            });\n        }\n        this.socket.on('connect_error', (error)=>{\n            var _error_message, _error_message1, _error_message2;\n            console.error('Socket connection error:', (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error');\n            this.connected = false;\n            // Handle specific error types\n            if (error === null || error === void 0 ? void 0 : (_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('xhr poll error')) {\n                console.log('XHR polling error detected - will try WebSocket only');\n                // Set a flag to track this specific error\n                if (true) {\n                    window.localStorage.setItem('socket_xhr_error', 'true');\n                }\n                // Emit the error event\n                this.emitEvent('error', 'XHR polling error - switching to WebSocket');\n                // Try WebSocket only after a short delay\n                setTimeout(()=>{\n                    try {\n                        var _this_socket;\n                        console.log('Switching to WebSocket transport only...');\n                        (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.disconnect();\n                        this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                            reconnectionAttempts: 5,\n                            reconnectionDelay: 1000,\n                            timeout: 20000,\n                            transports: [\n                                'websocket'\n                            ],\n                            autoConnect: true,\n                            forceNew: true,\n                            upgrade: false,\n                            withCredentials: false\n                        });\n                        this.setupEventListeners();\n                    } catch (e) {\n                        console.error('Error switching to WebSocket transport:', e);\n                        // If WebSocket also fails, try HTTP fallback\n                        this.emitEvent('error', 'All transports failed - using HTTP fallback');\n                    }\n                }, 1000);\n            } else if ((error === null || error === void 0 ? void 0 : (_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('websocket')) || (error === null || error === void 0 ? void 0 : (_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('WebSocket')) || typeof error === 'object' && 'type' in error && error.type === 'TransportError') {\n                console.log('WebSocket error detected - will try polling transport with enhanced configuration');\n                // Set a flag to track this specific error\n                if (true) {\n                    window.localStorage.setItem('socket_websocket_error', 'true');\n                }\n                // Try polling with enhanced configuration\n                setTimeout(()=>{\n                    try {\n                        var _this_socket;\n                        console.log('Switching to polling transport with CORS headers...');\n                        (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.disconnect();\n                        this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                            reconnectionAttempts: 5,\n                            reconnectionDelay: 2000,\n                            timeout: 30000,\n                            transports: [\n                                'polling'\n                            ],\n                            autoConnect: true,\n                            forceNew: true,\n                            upgrade: false,\n                            withCredentials: false,\n                            extraHeaders: {\n                                'Access-Control-Allow-Origin': '*',\n                                'Access-Control-Allow-Methods': 'GET,POST,OPTIONS',\n                                'Access-Control-Allow-Headers': 'Content-Type'\n                            }\n                        });\n                        this.setupEventListeners();\n                    } catch (e) {\n                        console.error('Error switching to polling transport:', e);\n                        // If both transports fail, emit error for HTTP fallback\n                        this.emitEvent('error', 'All Socket.IO transports failed - using HTTP fallback');\n                    }\n                }, 2000);\n                this.emitEvent('error', 'WebSocket error - trying enhanced polling transport');\n            } else {\n                // For other errors, just emit the error event\n                this.emitEvent('error', (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error');\n                // Try to reconnect after a delay\n                setTimeout(()=>{\n                    this.connect();\n                }, 3000);\n            }\n        });\n        // Application-specific events\n        this.socket.on('code-update', (code)=>{\n            this.emitEvent('code-update', code);\n        });\n        this.socket.on('user-typing', (data)=>{\n            this.emitEvent('user-typing', data);\n        });\n        this.socket.on('user-joined', (users)=>{\n            this.emitEvent('user-joined', users);\n        });\n        this.socket.on('user-left', (users)=>{\n            this.emitEvent('user-left', users);\n        });\n        this.socket.on('highlight-line', (data)=>{\n            this.emitEvent('highlight-line', data);\n        });\n        this.socket.on('cursor-move', (param)=>{\n            let { userId, position } = param;\n            var _this_listeners_get;\n            console.log(\"Cursor move received from user \".concat(userId, \":\"), position);\n            (_this_listeners_get = this.listeners.get('cursor-move')) === null || _this_listeners_get === void 0 ? void 0 : _this_listeners_get.forEach((callback)=>callback({\n                    userId,\n                    position\n                }));\n        });\n        this.socket.on('room-users-updated', (data)=>{\n            this.emitEvent('room-users-updated', data);\n        });\n        // Listen for student list updates (critical for teacher control panel)\n        this.socket.on('update-student-list', (data)=>{\n            console.log('🎯 SocketService received update-student-list:', data);\n            this.emitEvent('update-student-list', data);\n        });\n        // Listen for permission updates\n        this.socket.on('edit-permission', (data)=>{\n            console.log('🎯 SocketService received edit-permission:', data);\n            this.emitEvent('edit-permission', data);\n        });\n        this.socket.on('permission-updated', (data)=>{\n            console.log('🎯 SocketService received permission-updated:', data);\n            this.emitEvent('permission-updated', data);\n        });\n        // Handle request for initial code from new users\n        this.socket.on('get-initial-code', (data)=>{\n            this.emitEvent('get-initial-code', data);\n        });\n        // Handle receiving initial code\n        this.socket.on('initial-code-received', (data)=>{\n            this.emitEvent('initial-code-received', data);\n        });\n        // Handle teacher selection events\n        this.socket.on('teacher-selection', (data)=>{\n            this.emitEvent('teacher-selection', data);\n        });\n        this.socket.on('clear-teacher-selection', (data)=>{\n            this.emitEvent('clear-teacher-selection', data);\n        });\n        // Handle teacher cursor position events\n        this.socket.on('teacher-cursor-position', (data)=>{\n            this.emitEvent('teacher-cursor-position', data);\n        });\n        // Handle teacher text highlight events\n        this.socket.on('teacher-text-highlight', (data)=>{\n            this.emitEvent('teacher-text-highlight', data);\n        });\n        this.socket.on('clear-teacher-text-highlight', (data)=>{\n            this.emitEvent('clear-teacher-text-highlight', data);\n        });\n        // Handle sync events for newly joined users\n        this.socket.on('sync-code', (data)=>{\n            this.emitEvent('sync-code', data);\n        });\n        this.socket.on('sync-teacher-selection', (data)=>{\n            this.emitEvent('sync-teacher-selection', data);\n        });\n        this.socket.on('sync-teacher-cursor', (data)=>{\n            this.emitEvent('sync-teacher-cursor', data);\n        });\n    }\n    // Add event listener\n    on(event, callback) {\n        var _this_listeners_get;\n        if (!this.listeners.has(event)) {\n            this.listeners.set(event, []);\n        }\n        (_this_listeners_get = this.listeners.get(event)) === null || _this_listeners_get === void 0 ? void 0 : _this_listeners_get.push(callback);\n    }\n    // Remove event listener\n    off(event, callback) {\n        if (!this.listeners.has(event)) return;\n        const callbacks = this.listeners.get(event) || [];\n        const index = callbacks.indexOf(callback);\n        if (index !== -1) {\n            callbacks.splice(index, 1);\n        }\n    }\n    // Emit event to listeners\n    emitEvent(event, data) {\n        if (!this.listeners.has(event)) return;\n        const callbacks = this.listeners.get(event) || [];\n        callbacks.forEach((callback)=>{\n            if (typeof callback === 'function') {\n                try {\n                    callback(data);\n                } catch (error) {\n                    console.error(\"Error in \".concat(event, \" listener:\"), error);\n                }\n            }\n        });\n    }\n    // Check if socket is connected\n    isConnected() {\n        var _this_socket;\n        return this.connected && !!((_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.connected);\n    }\n    // Connect to socket server with fallback mechanisms\n    connect() {\n        if (!this.socket) {\n            this.initSocket();\n        } else if (!this.socket.connected) {\n            console.log('Socket exists but not connected, attempting to reconnect...');\n            // Try to reconnect with existing socket\n            try {\n                this.socket.connect();\n            } catch (error) {\n                console.error('Error reconnecting with existing socket:', error);\n                // If reconnection fails, create a new socket\n                this.initSocket();\n            }\n        }\n        // Set a timeout to check if connection was successful\n        setTimeout(()=>{\n            if (!this.isConnected()) {\n                this.tryAlternativeTransport();\n            }\n        }, 5000);\n    }\n    // Try alternative transport method if WebSocket fails\n    tryAlternativeTransport() {\n        try {\n            // Disconnect existing socket if any\n            if (this.socket) {\n                this.socket.disconnect();\n            }\n            // Create new socket with both transports but prioritize polling\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                reconnectionAttempts: 5,\n                reconnectionDelay: 1000,\n                timeout: 10000,\n                transports: [\n                    'polling',\n                    'websocket'\n                ],\n                autoConnect: true,\n                forceNew: true,\n                upgrade: true\n            });\n            this.setupEventListeners();\n        } catch (error) {\n            console.error('Error setting up alternative transport:', error);\n        }\n    }\n    // Disconnect from socket server\n    disconnect() {\n        if (this.socket) {\n            this.socket.disconnect();\n        }\n    }\n    // Create a new room\n    createRoom(username, roomId) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket || !this.isConnected()) {\n                return reject(new Error('Socket not connected'));\n            }\n            // Generate a unique userId if not already stored\n            let userId =  true ? window.localStorage.getItem('userId') : 0;\n            if (!userId) {\n                userId = \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9));\n                if (true) {\n                    window.localStorage.setItem('userId', userId);\n                }\n            }\n            this.socket.emit('create-room', {\n                username,\n                roomId,\n                userId\n            }, (response)=>{\n                if (response.error) {\n                    reject(new Error(response.error));\n                } else {\n                    // If the server validated and possibly changed the username, update it locally\n                    if (response.username && response.username !== username) {\n                        if (true) {\n                            window.localStorage.setItem('username', response.username);\n                        }\n                    }\n                    resolve({\n                        roomId: response.roomId,\n                        username: response.username || username\n                    });\n                }\n            });\n        });\n    }\n    // Join an existing room with fallback to HTTP if socket fails\n    joinRoom(roomId, username) {\n        return new Promise(async (resolve, reject)=>{\n            // Generate a unique userId if not already stored\n            let userId =  true ? window.localStorage.getItem('userId') : 0;\n            if (!userId) {\n                userId = \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9));\n                if (true) {\n                    window.localStorage.setItem('userId', userId);\n                }\n            }\n            // Check if we have a socket connection\n            if (!this.socket || !this.isConnected()) {\n                // Try to connect\n                this.connect();\n                // Wait a bit to see if connection succeeds\n                await new Promise((r)=>setTimeout(r, 2000));\n                // If still not connected, use HTTP fallback\n                if (!this.isConnected()) {\n                    return this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                }\n            }\n            // If we reach here, we have a socket connection, so use it\n            try {\n                if (!this.socket) {\n                    throw new Error('Socket is null');\n                }\n                // Emit join-room with userId, username, and roomId\n                this.socket.emit('join-room', {\n                    roomId,\n                    username,\n                    userId\n                }, (response)=>{\n                    if (response.error) {\n                        // If socket join fails, try HTTP fallback\n                        this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                    } else if (response.success) {\n                        // If the server validated and possibly changed the username, update it locally\n                        if (response.username && response.username !== username) {\n                            if (true) {\n                                window.localStorage.setItem('username', response.username);\n                            }\n                        }\n                        // Mark the current user in the users list\n                        const usersWithCurrentFlag = (response.users || []).map((user)=>({\n                                ...user,\n                                userId: user.userId || '',\n                                isCurrentUser: (user.userId || '') === userId\n                            }));\n                        resolve({\n                            users: usersWithCurrentFlag || [],\n                            username: response.username || username,\n                            role: response.role\n                        });\n                    } else {\n                        // If socket join fails, try HTTP fallback\n                        this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                    }\n                });\n                // Set a timeout in case the callback never fires\n                setTimeout(()=>{\n                    // If we haven't resolved or rejected yet, try HTTP fallback\n                    this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                }, 5000);\n            } catch (error) {\n                // If socket join throws an exception, try HTTP fallback\n                this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n            }\n        });\n    }\n    // HTTP fallback for joining a room when socket fails\n    joinRoomViaHttp(roomId, username, userId, resolve, reject) {\n        // Use axios to make a direct HTTP request to the server\n        const apiUrl = \"\".concat(SOCKET_URL, \"/api/join-room\");\n        axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(apiUrl, {\n            roomId,\n            username,\n            userId\n        }).then((response)=>{\n            if (response.data.error) {\n                reject(new Error(response.data.error));\n            } else {\n                // If the server validated and possibly changed the username, update it locally\n                if (response.data.username && response.data.username !== username) {\n                    if (true) {\n                        window.localStorage.setItem('username', response.data.username);\n                    }\n                }\n                // Create a default user list with at least the current user\n                const users = response.data.users || [\n                    {\n                        userId,\n                        username: response.data.username || username,\n                        socketId: 'http-fallback'\n                    }\n                ];\n                // Fix: When mapping users, always provide a fallback for userId (empty string if undefined)\n                const usersWithCurrentFlag = users.map((user)=>({\n                        ...user,\n                        userId: user.userId || '',\n                        isCurrentUser: (user.userId || '') === userId\n                    }));\n                resolve({\n                    users: usersWithCurrentFlag,\n                    username: response.data.username || username,\n                    role: response.data.role\n                });\n                // Try to reconnect socket after successful HTTP fallback\n                setTimeout(()=>this.connect(), 1000);\n            }\n        }).catch((error)=>{\n            // If HTTP fallback also fails, create a minimal response with just the current user\n            const fallbackUser = {\n                userId,\n                username,\n                isCurrentUser: true\n            };\n            // Resolve with just the current user to allow the UI to function\n            resolve({\n                users: [\n                    fallbackUser\n                ],\n                username,\n                role: 'student' // Default role for fallback\n            });\n            // Try to reconnect socket after error\n            setTimeout(()=>this.connect(), 2000);\n        });\n    }\n    // Send code changes to the server with HTTP fallback\n    sendCodeChange(roomId, code) {\n        // If socket is connected, use it\n        if (this.socket && this.isConnected()) {\n            try {\n                // Use volatile for code changes to prevent queueing\n                // This helps prevent outdated updates from being sent\n                this.socket.volatile.emit('code-change', {\n                    roomId,\n                    code\n                });\n                return true;\n            } catch (error) {\n            // Fall through to HTTP fallback\n            }\n        } else {\n            console.warn('Socket not connected, falling back to HTTP for code change.');\n        }\n        // HTTP fallback for code changes\n        this.sendCodeChangeViaHttp(roomId, code);\n        return true;\n    }\n    // Send initial code to a requesting user\n    sendInitialCode(roomId, code, requestingUserId) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot send initial code');\n            return false;\n        }\n        try {\n            this.socket.emit('send-initial-code', {\n                roomId,\n                code,\n                requestingUserId\n            });\n            console.log(\"Sent initial code to user \".concat(requestingUserId, \" in room \").concat(roomId));\n            return true;\n        } catch (error) {\n            console.error('Error sending initial code:', error);\n            return false;\n        }\n    }\n    // Send teacher text selection to students\n    sendTeacherSelection(roomId, selection) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot send teacher selection');\n            return false;\n        }\n        try {\n            var _this_socket, _this_socket1;\n            // Get teacherName and teacherId from localStorage\n            const teacherName =  true ? window.localStorage.getItem('username') || 'Unknown' : 0;\n            const teacherId =  true ? window.localStorage.getItem('userId') || ((_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.id) || 'unknown' : 0;\n            this.socket.emit('teacher-selection', {\n                roomId,\n                selection,\n                teacherName,\n                teacherId\n            });\n            console.log(\"Sent teacher selection to room \".concat(roomId, \":\"), selection);\n            return true;\n        } catch (error) {\n            console.error('Error sending teacher selection:', error);\n            return false;\n        }\n    }\n    // Send teacher cursor position to students\n    sendTeacherCursorPosition(roomId, position) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot send teacher cursor position');\n            return false;\n        }\n        try {\n            var _this_socket, _this_socket1;\n            const teacherName =  true ? window.localStorage.getItem('username') || 'Unknown' : 0;\n            const teacherId =  true ? window.localStorage.getItem('userId') || ((_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.id) || 'unknown' : 0;\n            this.socket.emit('teacher-cursor-position', {\n                roomId,\n                position,\n                teacherName,\n                teacherId\n            });\n            return true;\n        } catch (error) {\n            console.error('Error sending teacher cursor position:', error);\n            return false;\n        }\n    }\n    // Send teacher text highlight to students\n    sendTeacherTextHighlight(roomId, selection) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot send teacher text highlight');\n            return false;\n        }\n        try {\n            var _this_socket, _this_socket1;\n            const teacherName =  true ? window.localStorage.getItem('username') || 'Unknown' : 0;\n            const teacherId =  true ? window.localStorage.getItem('userId') || ((_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.id) || 'unknown' : 0;\n            this.socket.emit('teacher-text-highlight', {\n                roomId,\n                selection,\n                teacherName,\n                teacherId\n            });\n            return true;\n        } catch (error) {\n            console.error('Error sending teacher text highlight:', error);\n            return false;\n        }\n    }\n    // Clear teacher text selection\n    clearTeacherSelection(roomId) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot clear teacher selection');\n            return false;\n        }\n        try {\n            this.socket.emit('clear-teacher-selection', {\n                roomId\n            });\n            console.log(\"Cleared teacher selection in room \".concat(roomId));\n            return true;\n        } catch (error) {\n            console.error('Error clearing teacher selection:', error);\n            return false;\n        }\n    }\n    // Clear teacher text highlight\n    clearTeacherTextHighlight(roomId) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot clear teacher text highlight');\n            return false;\n        }\n        try {\n            this.socket.emit('clear-teacher-text-highlight', {\n                roomId\n            });\n            return true;\n        } catch (error) {\n            console.error('Error clearing teacher text highlight:', error);\n            return false;\n        }\n    }\n    // Sync current code to a specific user\n    syncCodeToUser(roomId, code, targetSocketId) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot sync code to user');\n            return false;\n        }\n        try {\n            this.socket.emit('sync-code', {\n                roomId,\n                code,\n                targetSocketId\n            });\n            console.log(\"Synced code to user \".concat(targetSocketId, \" in room \").concat(roomId));\n            return true;\n        } catch (error) {\n            console.error('Error syncing code to user:', error);\n            return false;\n        }\n    }\n    // Sync current teacher selection to a specific user\n    syncTeacherSelectionToUser(roomId, selection, targetSocketId) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot sync teacher selection to user');\n            return false;\n        }\n        try {\n            this.socket.emit('sync-teacher-selection', {\n                roomId,\n                selection,\n                targetSocketId\n            });\n            console.log(\"Synced teacher selection to user \".concat(targetSocketId, \" in room \").concat(roomId));\n            return true;\n        } catch (error) {\n            console.error('Error syncing teacher selection to user:', error);\n            return false;\n        }\n    }\n    // Sync current teacher cursor to a specific user\n    syncTeacherCursorToUser(roomId, position, targetSocketId) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot sync teacher cursor to user');\n            return false;\n        }\n        try {\n            this.socket.emit('sync-teacher-cursor', {\n                roomId,\n                position,\n                targetSocketId\n            });\n            console.log(\"Synced teacher cursor to user \".concat(targetSocketId, \" in room \").concat(roomId));\n            return true;\n        } catch (error) {\n            console.error('Error syncing teacher cursor to user:', error);\n            return false;\n        }\n    }\n    // Set edit permission for a user (teacher only)\n    setEditPermission(roomId, targetSocketId, canEdit) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot set edit permission');\n            return false;\n        }\n        try {\n            this.socket.emit('set-edit-permission', {\n                roomId,\n                targetSocketId,\n                canEdit\n            });\n            console.log(\"Set edit permission for \".concat(targetSocketId, \" in room \").concat(roomId, \": \").concat(canEdit));\n            return true;\n        } catch (error) {\n            console.error('Error setting edit permission:', error);\n            return false;\n        }\n    }\n    // HTTP fallback for sending code changes\n    sendCodeChangeViaHttp(roomId, code) {\n        // Only send HTTP fallback for significant changes to reduce traffic\n        // Store the last sent code to avoid sending duplicates\n        const lastSentCode =  true ? window.localStorage.getItem(\"last_http_code_\".concat(roomId)) : 0;\n        if (lastSentCode === code) {\n            return; // Don't send duplicate code\n        }\n        // Use axios to make a direct HTTP request to the server\n        const apiUrl = \"\".concat(SOCKET_URL, \"/api/code-change\");\n        const userId =  true ? window.localStorage.getItem('userId') || '' : 0;\n        axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(apiUrl, {\n            roomId,\n            code,\n            userId\n        }).then((response)=>{\n            // Store the sent code to avoid duplicates\n            if (true) {\n                window.localStorage.setItem(\"last_http_code_\".concat(roomId), code);\n            }\n        }).catch((error)=>{\n            console.error('Error sending code change via HTTP:', error);\n        });\n        // Try to reconnect socket\n        if (!this.isConnected()) {\n            setTimeout(()=>this.connect(), 1000);\n        }\n    }\n    // Send typing notification\n    sendTyping(roomId, username) {\n        if (!this.socket || !this.isConnected()) {\n            return;\n        }\n        // Use the exact username provided by the user without any modifications\n        // This ensures we use exactly what the user entered on the dashboard\n        const validUsername = username;\n        // Get userId from localStorage\n        const userId =  true ? window.localStorage.getItem('userId') || \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9)) : 0;\n        try {\n            this.socket.emit('typing', {\n                roomId,\n                username: validUsername,\n                userId\n            });\n        } catch (error) {}\n    }\n    // Emit a highlight-line event to the server\n    sendHighlightLine(roomId, startLine, endLine, comment) {\n        if (!this.socket || !this.isConnected()) return;\n        try {\n            this.socket.emit('highlight-line', {\n                roomId,\n                startLine,\n                endLine,\n                comment\n            });\n        } catch (error) {\n        // Optionally handle error\n        }\n    }\n    // Leave a room\n    leaveRoom(roomId) {\n        if (!this.socket || !this.isConnected()) return;\n        try {\n            this.socket.emit('leave-room', roomId);\n        } catch (error) {}\n    }\n    // Add a public method to listen for the next connect event\n    onConnect(callback) {\n        if (this.isConnected()) {\n            callback();\n        } else if (this.socket) {\n            this.socket.once('connect', callback);\n        } else {\n            // If socket is not initialized, initialize and then listen\n            this.initSocket();\n            // Wait for the socket to be created, then attach the listener\n            setTimeout(()=>{\n                var _this_socket;\n                (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.once('connect', callback);\n            }, 100);\n        }\n    }\n    getSocket() {\n        return this.socket;\n    }\n    // Emit cursor movement\n    sendCursorMove(roomId, userId, position) {\n        if (this.socket && this.connected) {\n            this.socket.emit(\"cursor-move\", {\n                roomId,\n                userId,\n                position\n            });\n        }\n    }\n    // Add a method to validate if a room exists on the server\n    async validateRoom(roomId) {\n        if (!this.socket || !this.isConnected()) {\n            throw new Error(\"Socket not connected\");\n        }\n        return new Promise((resolve, reject)=>{\n            if (!this.socket) {\n                throw new Error(\"Socket is not initialized\");\n            }\n            this.socket.emit(\"validate-room\", {\n                roomId\n            }, (response)=>{\n                if (response) {\n                    resolve(response);\n                } else {\n                    reject(new Error(\"Failed to validate room\"));\n                }\n            });\n        });\n    }\n    constructor(){\n        this.socket = null;\n        this.listeners = new Map();\n        this.connected = false;\n        this.initSocket();\n    }\n}\n// Export the class itself instead of calling getInstance during export\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SocketService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/socketService.ts\n"));

/***/ })

});