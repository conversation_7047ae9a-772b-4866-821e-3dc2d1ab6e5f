"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var monaco_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! monaco-editor */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.main.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlignLeft,FiCode,FiCopy,FiDownload,FiHelpCircle,FiLoader,FiMoon,FiPlay,FiSearch,FiSun,FiUpload,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_socketService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/socketService */ \"(app-pages-browser)/./src/services/socketService.ts\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/throttle */ \"(app-pages-browser)/./node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/SocketContext */ \"(app-pages-browser)/./src/context/SocketContext.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_formatCode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/formatCode */ \"(app-pages-browser)/./src/utils/formatCode.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/ThemeContext */ \"(app-pages-browser)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CodeEditor(param) {\n    let { roomId, username: initialUsername } = param;\n    _s();\n    const { isConnected } = (0,_context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket)();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"// Start coding...\");\n    // Use a ref to always track the latest code value\n    const latestCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"// Start coding...\");\n    // Get username from localStorage if available, otherwise use initialUsername\n    const storedUsername =  true ? localStorage.getItem(\"username\") : 0;\n    console.log(\"Initial username: \".concat(initialUsername, \", Stored username: \").concat(storedUsername));\n    // Track the username internally to handle server validation\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUsername || initialUsername);\n    const [typingUser, setTypingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeUsers, setActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUserList, setShowUserList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"javascript\");\n    const [copySuccess, setCopySuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get user role from localStorage if available, otherwise null\n    const storedUserRole =  true ? localStorage.getItem(\"userRole\") : 0;\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUserRole);\n    // Code execution states\n    const [isExecuting, setIsExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionOutput, setExecutionOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [executionError, setExecutionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOutput, setShowOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Piston API runtimes\n    const [runtimes, setRuntimes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [runtimesLoaded, setRuntimesLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    // Theme and minimap state (ensure these are always defined at the top)\n    const { theme, setTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme)();\n    const [showShortcuts, setShowShortcuts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [minimapEnabled, setMinimapEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Language-specific code snippets\n    const SNIPPETS = {\n        javascript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            }\n        ],\n        typescript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction(): void {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            },\n            {\n                label: \"Type Declaration\",\n                value: \"type MyType = {\\n  key: string;\\n};\"\n            }\n        ],\n        python3: [\n            {\n                label: \"Function\",\n                value: \"def my_function():\\n    # code\\n    pass\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in range(10):\\n    # code\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition:\\n    # code\"\n            },\n            {\n                label: \"Print\",\n                value: \"print('Hello, World!')\"\n            }\n        ],\n        java: [\n            {\n                label: \"Main Method\",\n                value: \"public static void main(String[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"System.out.println(\\\"Hello, World!\\\");\"\n            }\n        ],\n        csharp: [\n            {\n                label: \"Main Method\",\n                value: \"static void Main(string[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"Console.WriteLine(\\\"Hello, World!\\\");\"\n            }\n        ],\n        c: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"printf(\\\"Hello, World!\\\\n\\\");\"\n            }\n        ],\n        cpp: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"std::cout << \\\"Hello, World!\\\" << std::endl;\"\n            }\n        ],\n        go: [\n            {\n                label: \"Main Function\",\n                value: \"func main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i := 0; i < 10; i++ {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"fmt.Println(\\\"Hello, World!\\\")\"\n            }\n        ],\n        ruby: [\n            {\n                label: \"Method\",\n                value: \"def my_method\\n  # code\\nend\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..9\\n  # code\\nend\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition\\n  # code\\nend\"\n            },\n            {\n                label: \"Print\",\n                value: \"puts 'Hello, World!'\"\n            }\n        ],\n        rust: [\n            {\n                label: \"Main Function\",\n                value: \"fn main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..10 {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"println!(\\\"Hello, World!\\\");\"\n            }\n        ],\n        php: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for ($i = 0; $i < 10; $i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if ($condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"echo 'Hello, World!';\"\n            }\n        ]\n    };\n    // Declare the `socketService` instance at the top of the file\n    const socketService = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n    // Leave room handler\n    const handleLeaveRoom = ()=>{\n        socketService.leaveRoom(roomId);\n        router.push(\"/dashboard\");\n    };\n    // --- Teacher Selection Highlighting and Cursor Sharing ---\n    // Add custom CSS for teacher selection and cursor if not already present\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            if ( true && !document.getElementById('teacher-highlighting-css')) {\n                const style = document.createElement('style');\n                style.id = 'teacher-highlighting-css';\n                style.innerHTML = \"\\n        .teacher-selection-highlight {\\n          background: rgba(255, 230, 80, 0.5) !important;\\n          border-bottom: 2px solid orange !important;\\n          border-radius: 2px;\\n        }\\n        .teacher-cursor-highlight {\\n          border-left: 2px solid orange !important;\\n          margin-left: -1px;\\n          animation: teacher-cursor-blink 1s steps(2, start) infinite;\\n        }\\n        @keyframes teacher-cursor-blink {\\n          0%, 100% { opacity: 1; }\\n          50% { opacity: 0; }\\n        }\\n      \";\n                document.head.appendChild(style);\n            }\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    // Store decoration ids for cleanup\n    const [teacherSelectionDecorations, setTeacherSelectionDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [teacherCursorDecorations, setTeacherCursorDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Add state for teacher cursor position (for custom blinking cursor overlay)\n    const [teacherCursorPosition, setTeacherCursorPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State to track current teacher selection and cursor for syncing to new users\n    const [currentTeacherSelection, setCurrentTeacherSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentTeacherCursor, setCurrentTeacherCursor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Handle editor mounting\n    const handleEditorDidMount = (editor)=>{\n        editorRef.current = editor;\n        setIsLoading(false);\n        // Auto-focus the editor after mounting\n        setTimeout(()=>{\n            if (editor) {\n                editor.focus();\n                // Add keyboard shortcut (Ctrl+Enter) to run code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.CtrlCmd | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.Enter, ()=>{\n                    executeCode();\n                });\n                // Add keyboard shortcut (Shift+Alt+F) to format code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Shift | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Alt | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.KeyF, ()=>{\n                    formatCurrentCode();\n                });\n                // Add teacher cursor and selection listeners\n                if (userRole === 'teacher') {\n                    // Track cursor position changes - emit 'teacher-cursor' event\n                    editor.onDidChangeCursorPosition((e)=>{\n                        const position = e.position;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        if (roomId) {\n                            console.log('🎯 Teacher cursor position changed:', {\n                                lineNumber: position.lineNumber,\n                                column: position.column\n                            });\n                            // Send cursor position to students using teacher-cursor-position event\n                            socketServiceInstance.sendTeacherCursorPosition(roomId, {\n                                lineNumber: position.lineNumber,\n                                column: position.column\n                            });\n                        }\n                    });\n                    // Track selection changes - emit 'teacher-selection' event\n                    editor.onDidChangeCursorSelection((e)=>{\n                        console.log('🎯 Teacher cursor selection changed:', {\n                            selection: e.selection,\n                            isEmpty: e.selection.isEmpty(),\n                            userRole: userRole,\n                            roomId: roomId\n                        });\n                        const selection = e.selection;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        if (!selection.isEmpty() && roomId) {\n                            // Send selection range to students using teacher-selection event\n                            console.log('📤 Teacher sending selection range to students:', {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            });\n                            // Use both teacher-selection (for range highlighting) and teacher-text-highlight (for compatibility)\n                            socketServiceInstance.sendTeacherSelection(roomId, {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            });\n                            socketServiceInstance.sendTeacherTextHighlight(roomId, {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            });\n                        } else if (roomId) {\n                            // Clear selection when teacher deselects\n                            console.log('🧹 Teacher clearing selection');\n                            socketServiceInstance.clearTeacherSelection(roomId);\n                            socketServiceInstance.clearTeacherTextHighlight(roomId);\n                        }\n                    });\n                }\n            }\n        }, 500) // Small delay to ensure the editor is fully ready\n        ;\n    };\n    // Track if the change is from a remote update\n    const isRemoteUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Create a throttled version of the code change handler\n    // This prevents sending too many updates when typing quickly\n    // but ensures updates are sent at regular intervals\n    const throttledCodeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default()({\n        \"CodeEditor.useRef\": (code)=>{\n            if (roomId && isConnected) {\n                console.log(\"Sending throttled code update to room \".concat(roomId, \", length: \").concat(code.length));\n                socketService.sendCodeChange(roomId, code);\n            }\n        }\n    }[\"CodeEditor.useRef\"], 50) // Use throttle with a short interval for more responsive updates\n    ).current;\n    // Create a debounced version of the typing notification\n    const debouncedTypingNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default()({\n        \"CodeEditor.useCallback[debouncedTypingNotification]\": ()=>{\n            if (roomId && isConnected) {\n                // Always send the current username with typing notifications\n                console.log(\"Sending typing notification with username: \".concat(username));\n                socketService.sendTyping(roomId, username);\n            }\n        }\n    }[\"CodeEditor.useCallback[debouncedTypingNotification]\"], 1000), [\n        roomId,\n        isConnected,\n        username\n    ]);\n    // Handle editor change\n    const handleEditorChange = (value)=>{\n        if (typeof value !== \"string\") return;\n        // Make sure loading is off during code changes\n        setIsLoading(false);\n        // If this change is from a remote update, just update the state and return\n        if (isRemoteUpdate.current) {\n            console.log(\"Handling remote update, not emitting\");\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            isRemoteUpdate.current = false;\n            return;\n        }\n        // Only process if the value actually changed\n        if (value !== latestCodeRef.current) {\n            // Update local state immediately\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            // Send typing notification (debounced)\n            debouncedTypingNotification();\n            // Send code update (throttled)\n            throttledCodeChange(value);\n            // If not connected, try to reconnect\n            if (!isConnected) {\n                console.log(\"Socket not connected, attempting to reconnect\");\n                socketService.connect();\n            }\n        }\n    };\n    // Copy code to clipboard\n    const copyCodeToClipboard = ()=>{\n        if (navigator.clipboard && code) {\n            navigator.clipboard.writeText(code).then(()=>{\n                setCopySuccess(true);\n                setTimeout(()=>setCopySuccess(false), 2000);\n            }).catch((err)=>{\n                console.error('Failed to copy code: ', err);\n            });\n        }\n    };\n    // Toggle user list\n    const toggleUserList = ()=>{\n        setShowUserList((prev)=>!prev);\n    };\n    // Change language\n    const changeLanguage = (lang)=>{\n        setLanguage(lang);\n        // Log available runtimes for this language\n        if (runtimesLoaded && runtimes.length > 0) {\n            const availableRuntimes = runtimes.filter((runtime)=>runtime.language === lang || runtime.aliases && runtime.aliases.includes(lang));\n            if (availableRuntimes.length > 0) {\n                console.log(\"Available runtimes for \".concat(lang, \":\"), availableRuntimes);\n            } else {\n                console.log(\"No runtimes available for \".concat(lang));\n            }\n        }\n    };\n    // Format code\n    const formatCurrentCode = ()=>{\n        if (!code || !editorRef.current) return;\n        try {\n            // Format the code based on the current language\n            const formattedCode = (0,_utils_formatCode__WEBPACK_IMPORTED_MODULE_8__.formatCode)(code, language);\n            // Only update if the code actually changed\n            if (formattedCode !== code) {\n                // Update the editor\n                const model = editorRef.current.getModel();\n                if (model) {\n                    // Store current cursor position/selection\n                    const currentPosition = editorRef.current.getPosition();\n                    const currentSelection = editorRef.current.getSelection();\n                    // Update the editor content\n                    editorRef.current.executeEdits('format', [\n                        {\n                            range: model.getFullModelRange(),\n                            text: formattedCode,\n                            forceMoveMarkers: true\n                        }\n                    ]);\n                    // Restore cursor position if possible\n                    if (currentPosition) {\n                        editorRef.current.setPosition(currentPosition);\n                    }\n                    if (currentSelection) {\n                        editorRef.current.setSelection(currentSelection);\n                    }\n                    // Update state and ref\n                    setCode(formattedCode);\n                    latestCodeRef.current = formattedCode;\n                    // Send the formatted code to other users\n                    if (roomId && isConnected) {\n                        console.log(\"Sending formatted code to room \".concat(roomId, \", length: \").concat(formattedCode.length));\n                        socketService.sendCodeChange(roomId, formattedCode);\n                    }\n                    // Show a success message\n                    console.log('Code formatted successfully');\n                }\n            } else {\n                console.log('Code is already formatted');\n            }\n        } catch (error) {\n            console.error('Error formatting code:', error);\n        }\n    };\n    // Clear execution output\n    const clearOutput = ()=>{\n        setExecutionOutput(null);\n        setExecutionError(null);\n    };\n    // Fetch and store available runtimes from Piston API\n    const fetchRuntimes = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get('https://emkc.org/api/v2/piston/runtimes');\n            console.log('Piston API status:', response.status);\n            console.log('Available runtimes:', response.data);\n            // Store the runtimes in state\n            setRuntimes(response.data);\n            setRuntimesLoaded(true);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error fetching Piston API runtimes:', error);\n            return {\n                success: false,\n                error: axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) ? \"\".concat(error.message, \" - \").concat(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) || 'Unknown', \" \").concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText) || '') : String(error)\n            };\n        }\n    };\n    // Check if the Piston API is available\n    const checkPistonAPI = async ()=>{\n        // If we haven't loaded runtimes yet, fetch them\n        if (!runtimesLoaded) {\n            return await fetchRuntimes();\n        }\n        // If we already have runtimes, just return success\n        if (runtimes.length > 0) {\n            return {\n                success: true,\n                data: runtimes\n            };\n        }\n        // If we've tried to load runtimes but have none, try again\n        return await fetchRuntimes();\n    };\n    // Test the Piston API with a hardcoded sample payload\n    const testPistonAPI = async ()=>{\n        setIsExecuting(true);\n        setExecutionError(null);\n        setExecutionOutput(null);\n        setShowOutput(true);\n        try {\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Find JavaScript runtime\n            const jsRuntimes = apiStatus.data.filter((runtime)=>runtime.language === \"javascript\" || runtime.aliases && runtime.aliases.includes(\"javascript\"));\n            if (jsRuntimes.length === 0) {\n                setExecutionError('No JavaScript runtime found. Please try a different language.');\n                return;\n            }\n            const jsRuntime = jsRuntimes[0];\n            console.log(\"Using JavaScript runtime: \".concat(jsRuntime.language, \" \").concat(jsRuntime.version));\n            // Sample JavaScript code that should work\n            const samplePayload = {\n                language: jsRuntime.language,\n                version: jsRuntime.version,\n                files: [\n                    {\n                        name: \"main.js\",\n                        content: \"console.log('Hello, World!');\"\n                    }\n                ],\n                stdin: \"\",\n                args: []\n            };\n            console.log('Testing Piston API with sample payload:', JSON.stringify(samplePayload, null, 2));\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', samplePayload);\n            console.log('Sample execution response:', response.data);\n            if (response.data.run) {\n                setExecutionOutput(\"API Test Successful!\\n\\n\" + \"Output: \" + response.data.run.stdout + \"\\n\\n\" + \"The API is working correctly. You can now run your own code.\");\n            } else {\n                setExecutionError('API test failed. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error testing Piston API:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                setExecutionError(\"API Test Failed: \".concat(error.response.status, \" \").concat(error.response.statusText, \"\\n\\n\") + JSON.stringify(error.response.data, null, 2));\n            } else {\n                setExecutionError(error instanceof Error ? \"API Test Error: \".concat(error.message) : 'An unknown error occurred while testing the API.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // Execute code using Piston API\n    const executeCode = async ()=>{\n        if (!code || isExecuting) return;\n        // Map Monaco editor language to Piston API language\n        const languageMap = {\n            javascript: \"javascript\",\n            typescript: \"typescript\",\n            python: \"python3\",\n            python3: \"python3\",\n            java: \"java\",\n            csharp: \"csharp\",\n            c: \"c\",\n            cpp: \"cpp\",\n            go: \"go\",\n            ruby: \"ruby\",\n            rust: \"rust\",\n            php: \"php\"\n        };\n        // Get the mapped language\n        const pistonLanguage = languageMap[language] || language;\n        // Check if we have runtimes loaded\n        if (!runtimesLoaded || runtimes.length === 0) {\n            setExecutionError('Runtimes not loaded yet. Please try again in a moment.');\n            setShowOutput(true);\n            return;\n        }\n        // Find available runtimes for the selected language\n        const availableRuntimes = runtimes.filter((runtime)=>runtime.language === pistonLanguage || runtime.aliases && runtime.aliases.includes(pistonLanguage));\n        // Check if the language is supported\n        if (availableRuntimes.length === 0) {\n            // Get a list of supported languages from the runtimes\n            const supportedLanguages = [\n                ...new Set(runtimes.flatMap((runtime)=>[\n                        runtime.language,\n                        ...runtime.aliases || []\n                    ]))\n            ].sort();\n            setExecutionError(\"The language '\".concat(language, \"' (mapped to '\").concat(pistonLanguage, \"') is not supported by the Piston API.\\n\\n\") + \"Supported languages: \".concat(supportedLanguages.join(', ')));\n            setShowOutput(true);\n            return;\n        }\n        // Get the latest version of the runtime\n        const selectedRuntime = availableRuntimes[0];\n        try {\n            setIsExecuting(true);\n            setExecutionError(null);\n            setExecutionOutput(null);\n            setShowOutput(true);\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Determine file extension based on language\n            let fileExtension = '';\n            if (selectedRuntime.language === 'python3' || selectedRuntime.language === 'python') fileExtension = '.py';\n            else if (selectedRuntime.language === 'javascript') fileExtension = '.js';\n            else if (selectedRuntime.language === 'typescript') fileExtension = '.ts';\n            else if (selectedRuntime.language === 'java') fileExtension = '.java';\n            else if (selectedRuntime.language === 'csharp') fileExtension = '.cs';\n            else if (selectedRuntime.language === 'c') fileExtension = '.c';\n            else if (selectedRuntime.language === 'cpp') fileExtension = '.cpp';\n            else if (selectedRuntime.language === 'go') fileExtension = '.go';\n            else if (selectedRuntime.language === 'rust') fileExtension = '.rs';\n            else if (selectedRuntime.language === 'ruby') fileExtension = '.rb';\n            else if (selectedRuntime.language === 'php') fileExtension = '.php';\n            else fileExtension = \".\".concat(selectedRuntime.language);\n            console.log(\"Selected runtime: \".concat(selectedRuntime.language, \" \").concat(selectedRuntime.version));\n            // Prepare the payload according to Piston API documentation\n            const payload = {\n                language: selectedRuntime.language,\n                version: selectedRuntime.version,\n                files: [\n                    {\n                        name: \"main\".concat(fileExtension),\n                        content: code\n                    }\n                ],\n                stdin: '',\n                args: [],\n                compile_timeout: 10000,\n                run_timeout: 5000\n            };\n            // Log the payload for debugging\n            console.log(\"Executing \".concat(pistonLanguage, \" code with payload:\"), JSON.stringify(payload, null, 2));\n            // Make the API request\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', payload);\n            console.log('Execution response:', response.data);\n            const result = response.data;\n            if (result.run) {\n                // Format the output\n                let output = '';\n                let hasOutput = false;\n                // Add compile output if available (for compiled languages)\n                if (result.compile && result.compile.stderr) {\n                    output += \"Compilation output:\\n\".concat(result.compile.stderr, \"\\n\\n\");\n                    hasOutput = true;\n                }\n                // Add standard output\n                if (result.run.stdout) {\n                    output += result.run.stdout;\n                    hasOutput = true;\n                }\n                // Add error output\n                if (result.run.stderr) {\n                    if (hasOutput) output += '\\n';\n                    output += \"Error output:\\n\".concat(result.run.stderr);\n                    hasOutput = true;\n                }\n                // Add exit code if non-zero\n                if (result.run.code !== 0) {\n                    if (hasOutput) output += '\\n';\n                    output += \"\\nProcess exited with code \".concat(result.run.code);\n                    hasOutput = true;\n                }\n                if (!hasOutput) {\n                    output = 'Program executed successfully with no output.';\n                }\n                setExecutionOutput(output);\n            } else {\n                setExecutionError('Failed to execute code. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error executing code:', error);\n            // Handle Axios errors with more detailed information\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                const statusCode = error.response.status;\n                const responseData = error.response.data;\n                console.error('API Error Details:', {\n                    status: statusCode,\n                    data: responseData\n                });\n                // Format a more helpful error message\n                if (statusCode === 400) {\n                    setExecutionError(\"API Error (400 Bad Request): \".concat(JSON.stringify(responseData), \"\\n\\n\") + 'This usually means the API request format is incorrect. ' + 'Please check the console for more details.');\n                } else if (statusCode === 429) {\n                    setExecutionError('Rate limit exceeded. Please try again later.');\n                } else {\n                    setExecutionError(\"API Error (\".concat(statusCode, \"): \").concat(JSON.stringify(responseData), \"\\n\\n\") + 'Please check the console for more details.');\n                }\n            } else {\n                // Handle non-Axios errors\n                setExecutionError(error instanceof Error ? \"Error: \".concat(error.message) : 'An unknown error occurred while executing the code.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // This function will be called when we receive a code update from another user\n    const handleCodeUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleCodeUpdate]\": (incomingCode)=>{\n            console.log(\"Remote code update received, length:\", incomingCode.length);\n            // Make sure loading is off during code updates\n            setIsLoading(false);\n            // Only update if the code is different from our latest code\n            if (incomingCode !== latestCodeRef.current) {\n                try {\n                    // Set the flag to indicate this is a remote update\n                    isRemoteUpdate.current = true;\n                    // Update the editor if it's mounted\n                    if (editorRef.current) {\n                        console.log(\"Updating editor with remote code\");\n                        // Use the editor's model to update the value to preserve cursor position\n                        const model = editorRef.current.getModel();\n                        if (model) {\n                            // Store current cursor position/selection\n                            const currentPosition = editorRef.current.getPosition();\n                            const currentSelection = editorRef.current.getSelection();\n                            // Use executeEdits with better handling of cursor position\n                            editorRef.current.executeEdits('remote', [\n                                {\n                                    range: model.getFullModelRange(),\n                                    text: incomingCode,\n                                    forceMoveMarkers: true\n                                }\n                            ]);\n                            // Restore cursor position if possible\n                            if (currentPosition) {\n                                editorRef.current.setPosition(currentPosition);\n                            }\n                            if (currentSelection) {\n                                editorRef.current.setSelection(currentSelection);\n                            }\n                            // Also update our state and ref\n                            setCode(incomingCode);\n                            latestCodeRef.current = incomingCode;\n                        }\n                    } else {\n                        // If editor isn't mounted yet, just update the state and ref\n                        console.log(\"Editor not mounted, updating state only\");\n                        setCode(incomingCode);\n                        latestCodeRef.current = incomingCode;\n                        isRemoteUpdate.current = false;\n                    }\n                } catch (error) {\n                    console.error(\"Error updating editor with remote code:\", error);\n                    isRemoteUpdate.current = false;\n                }\n            } else {\n                console.log(\"Remote code matches current code, ignoring\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleCodeUpdate]\"], []);\n    // State to track cursor positions\n    const [cursorPositions, setCursorPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Emit cursor movement\n    const handleMouseMove = (e)=>{\n        const position = {\n            x: e.clientX,\n            y: e.clientY\n        };\n        _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().sendCursorMove(roomId, username, position);\n    };\n    // Listen for cursor movement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            const handleCursorMove = {\n                \"CodeEditor.useEffect.handleCursorMove\": (param)=>{\n                    let { userId, position } = param;\n                    setCursorPositions({\n                        \"CodeEditor.useEffect.handleCursorMove\": (prev)=>({\n                                ...prev,\n                                [userId]: position\n                            })\n                    }[\"CodeEditor.useEffect.handleCursorMove\"]);\n                }\n            }[\"CodeEditor.useEffect.handleCursorMove\"];\n            _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().on(\"cursor-move\", handleCursorMove);\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().off(\"cursor-move\", handleCursorMove);\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId\n    ]);\n    // Render cursors\n    const renderCursors = ()=>{\n        return Object.entries(cursorPositions).map((param)=>{\n            let [userId, position] = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    left: position.x,\n                    top: position.y,\n                    backgroundColor: \"red\",\n                    width: 10,\n                    height: 10,\n                    borderRadius: \"50%\",\n                    pointerEvents: \"none\"\n                }\n            }, userId, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 827,\n                columnNumber: 7\n            }, this);\n        });\n    };\n    // Fetch runtimes when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            fetchRuntimes();\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    // Handle request for initial code from new users\n    const handleGetInitialCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleGetInitialCode]\": (data)=>{\n            console.log(\"Received request for initial code from \".concat(data.requestingUsername, \" (\").concat(data.requestingUserId, \")\"));\n            // Only respond if we have code and we're connected\n            if (roomId && latestCodeRef.current && latestCodeRef.current.trim() !== \"// Start coding...\") {\n                const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                console.log(\"Sending initial code to \".concat(data.requestingUsername, \", length: \").concat(latestCodeRef.current.length));\n                socketServiceInstance.sendInitialCode(roomId, latestCodeRef.current, data.requestingUserId);\n            } else {\n                console.log(\"Not sending initial code - no meaningful code to share or not in room\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleGetInitialCode]\"], [\n        roomId\n    ]);\n    // Handle receiving initial code as a new user\n    const handleInitialCodeReceived = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleInitialCodeReceived]\": (data)=>{\n            console.log(\"Received initial code, length: \".concat(data.code.length));\n            // Only apply initial code if we don't have meaningful code yet\n            // This prevents overwriting code that the user might have already started typing\n            if (latestCodeRef.current === \"// Start coding...\" || latestCodeRef.current.trim() === \"\") {\n                console.log(\"Applying received initial code\");\n                // Set the flag to indicate this is a remote update\n                isRemoteUpdate.current = true;\n                // Update the code state and ref\n                setCode(data.code);\n                latestCodeRef.current = data.code;\n                // Update the editor if it's mounted\n                if (editorRef.current) {\n                    editorRef.current.setValue(data.code);\n                }\n            } else {\n                console.log(\"Not applying initial code - user already has meaningful code\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleInitialCodeReceived]\"], []);\n    // Handle teacher selection highlighting\n    const handleTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherSelection]\": (data)=>{\n            console.log(\"Received teacher selection from \".concat(data.teacherName, \":\"), data.selection);\n            // Extra debug: check if CSS class is present\n            if (true) {\n                const styleExists = !!document.querySelector('style#teacher-highlighting-css') || !!Array.from(document.styleSheets).find({\n                    \"CodeEditor.useCallback[handleTeacherSelection]\": (sheet)=>{\n                        try {\n                            return Array.from(sheet.cssRules).some({\n                                \"CodeEditor.useCallback[handleTeacherSelection]\": (rule)=>{\n                                    var _rule_selectorText;\n                                    return (_rule_selectorText = rule.selectorText) === null || _rule_selectorText === void 0 ? void 0 : _rule_selectorText.includes('.teacher-selection-highlight');\n                                }\n                            }[\"CodeEditor.useCallback[handleTeacherSelection]\"]);\n                        } catch (e) {\n                            return false;\n                        }\n                    }\n                }[\"CodeEditor.useCallback[handleTeacherSelection]\"]);\n                if (!styleExists) {\n                    console.warn('⚠️ teacher-selection-highlight CSS missing, injecting fallback');\n                    const style = document.createElement('style');\n                    style.id = 'teacher-highlighting-css-fallback';\n                    style.innerHTML = \"\\n          .teacher-selection-highlight {\\n            background: rgba(255, 230, 80, 0.7) !important;\\n            border-bottom: 2px solid orange !important;\\n            border-radius: 2px;\\n          }\\n        \";\n                    document.head.appendChild(style);\n                }\n            }\n            if (!editorRef.current || userRole === 'teacher') {\n                console.log('Skipping teacher highlight: editor not available or user is teacher');\n                return; // Don't show teacher highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model || !data.selection) {\n                    console.log('No model or selection');\n                    return;\n                }\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.selection.startLineNumber, data.selection.startColumn, data.selection.endLineNumber, data.selection.endColumn);\n                console.log('Applying teacher selection highlight with range:', range);\n                // Clear previous teacher selection decorations and apply new ones with yellow/orange styling\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-selection-highlight',\n                            hoverMessage: {\n                                value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" selected this text\\n\\nRange: Line \").concat(data.selection.startLineNumber, \"-\").concat(data.selection.endLineNumber)\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Right\n                            }\n                        }\n                    }\n                ]);\n                console.log('New teacher selection decoration IDs:', newDecorations);\n                setTeacherSelectionDecorations(newDecorations);\n                // Debug: Check DOM for highlight\n                setTimeout({\n                    \"CodeEditor.useCallback[handleTeacherSelection]\": ()=>{\n                        const elements = document.querySelectorAll('.teacher-selection-highlight');\n                        console.log('DOM .teacher-selection-highlight count:', elements.length);\n                        elements.forEach({\n                            \"CodeEditor.useCallback[handleTeacherSelection]\": (el, i)=>{\n                                console.log(\"Highlight \".concat(i, \":\"), el, el.className, el.getAttribute('style'));\n                            }\n                        }[\"CodeEditor.useCallback[handleTeacherSelection]\"]);\n                    }\n                }[\"CodeEditor.useCallback[handleTeacherSelection]\"], 200);\n            } catch (error) {\n                console.error('Error applying teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle clearing teacher selection\n    const handleClearTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherSelection]\": (data)=>{\n            console.log(\"Teacher \".concat(data.teacherName, \" cleared selection\"));\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                // Clear all teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, []);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error clearing teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle teacher cursor position updates with blinking orange cursor decoration\n    const handleTeacherCursorPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherCursorPosition]\": (data)=>{\n            console.log(\"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" cursor at line \").concat(data.position.lineNumber, \", column \").concat(data.position.column));\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't show cursor to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.log('⏭️ Skipping teacher cursor: model not available');\n                    return;\n                }\n                // Validate cursor position bounds\n                const lineCount = model.getLineCount();\n                if (data.position.lineNumber > lineCount || data.position.lineNumber <= 0) {\n                    console.log('⏭️ Skipping teacher cursor: position out of bounds');\n                    return;\n                }\n                // Create a range for the cursor position (single character width)\n                const cursorRange = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.position.lineNumber, data.position.column, data.position.lineNumber, data.position.column + 1);\n                // Clear previous teacher cursor decorations and apply new one\n                const newCursorDecorations = editor.deltaDecorations(teacherCursorDecorations, [\n                    {\n                        range: cursorRange,\n                        options: {\n                            className: 'teacher-cursor',\n                            hoverMessage: {\n                                value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" cursor position\\n\\nLine \").concat(data.position.lineNumber, \", Column \").concat(data.position.column)\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Left\n                            }\n                        }\n                    }\n                ]);\n                setTeacherCursorDecorations(newCursorDecorations);\n                // Set the teacher cursor position for overlay rendering\n                setTeacherCursorPosition({\n                    lineNumber: data.position.lineNumber,\n                    column: data.position.column,\n                    teacherName: data.teacherName\n                });\n            } catch (error) {\n                console.error('❌ Error handling teacher cursor position:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherCursorPosition]\"], [\n        userRole,\n        teacherCursorDecorations\n    ]);\n    // Handle teacher text highlight with enhanced error handling and multiple CSS class fallbacks\n    const handleTeacherTextHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherTextHighlight]\": (data)=>{\n            console.log(\"\\uD83C\\uDFA8 Teacher \".concat(data.teacherName, \" highlighted text:\"), data.selection);\n            console.log(\"\\uD83D\\uDD0D Debug info:\", {\n                editorAvailable: !!editorRef.current,\n                userRole: userRole,\n                roomId: roomId,\n                currentDecorations: teacherSelectionDecorations.length,\n                monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined'\n            });\n            if (!editorRef.current || userRole === 'teacher') {\n                console.log('⏭️ Skipping teacher text highlight: editor not available or user is teacher');\n                return; // Don't show highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('❌ Monaco Editor model not available');\n                    return;\n                }\n                if (!data.selection) {\n                    console.error('❌ Selection data is missing');\n                    return;\n                }\n                // Validate selection data with more robust checking\n                const { startLineNumber, startColumn, endLineNumber, endColumn } = data.selection;\n                if (typeof startLineNumber !== 'number' || startLineNumber <= 0 || typeof startColumn !== 'number' || startColumn <= 0 || typeof endLineNumber !== 'number' || endLineNumber <= 0 || typeof endColumn !== 'number' || endColumn <= 0) {\n                    console.error('❌ Invalid selection data:', data.selection);\n                    return;\n                }\n                // Ensure the selection is within the model bounds\n                const lineCount = model.getLineCount();\n                if (startLineNumber > lineCount || endLineNumber > lineCount) {\n                    console.warn('⚠️ Selection extends beyond model bounds, adjusting...');\n                    const adjustedEndLine = Math.min(endLineNumber, lineCount);\n                    const adjustedEndColumn = adjustedEndLine === lineCount ? model.getLineMaxColumn(adjustedEndLine) : endColumn;\n                    console.log(\"Adjusted selection: (\".concat(startLineNumber, \", \").concat(startColumn, \", \").concat(adjustedEndLine, \", \").concat(adjustedEndColumn, \")\"));\n                }\n                console.log(\"✅ Creating Monaco Range: (\".concat(startLineNumber, \", \").concat(startColumn, \", \").concat(endLineNumber, \", \").concat(endColumn, \")\"));\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(startLineNumber, startColumn, endLineNumber, endColumn);\n                console.log('✅ Monaco Range created successfully:', range);\n                // Apply decoration with multiple CSS class options and inline styles for better compatibility\n                console.log('🎨 Applying teacher text highlight decoration...');\n                const decorationOptions = {\n                    range: range,\n                    options: {\n                        className: 'teacher-highlight teacher-text-highlight',\n                        hoverMessage: {\n                            value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" highlighted this text\\n\\nClick to focus on this selection\")\n                        },\n                        stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                        // Add inline styles as fallback\n                        inlineClassName: 'teacher-highlight-inline',\n                        // Force inline styles for maximum compatibility\n                        overviewRuler: {\n                            color: 'rgba(59, 130, 246, 0.8)',\n                            position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Right\n                        },\n                        // Add background color directly\n                        backgroundColor: 'rgba(59, 130, 246, 0.25)',\n                        // Add border styling\n                        border: '2px solid rgba(59, 130, 246, 0.7)'\n                    }\n                };\n                // Clear previous decorations and apply new ones\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [\n                    decorationOptions\n                ]);\n                console.log('✅ Teacher text highlight decorations applied:', newDecorations);\n                console.log('🔍 Decoration details:', decorationOptions);\n                setTeacherSelectionDecorations(newDecorations);\n                // Debug: Check if decorations are actually in the DOM\n                setTimeout({\n                    \"CodeEditor.useCallback[handleTeacherTextHighlight]\": ()=>{\n                        var _editor_getModel;\n                        const decorationElements = document.querySelectorAll('.teacher-text-highlight, .teacher-highlight');\n                        console.log('🔍 Found decoration elements in DOM:', decorationElements.length);\n                        decorationElements.forEach({\n                            \"CodeEditor.useCallback[handleTeacherTextHighlight]\": (el, index)=>{\n                                console.log(\"\\uD83D\\uDD0D Decoration \".concat(index + 1, \":\"), {\n                                    element: el,\n                                    className: el.className,\n                                    style: el.getAttribute('style'),\n                                    computedStyle: window.getComputedStyle(el).background\n                                });\n                            }\n                        }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"]);\n                        // Also check Monaco's internal decorations\n                        const allDecorations = (_editor_getModel = editor.getModel()) === null || _editor_getModel === void 0 ? void 0 : _editor_getModel.getAllDecorations();\n                        console.log('🔍 All Monaco decorations:', allDecorations === null || allDecorations === void 0 ? void 0 : allDecorations.length);\n                        const teacherDecorations = allDecorations === null || allDecorations === void 0 ? void 0 : allDecorations.filter({\n                            \"CodeEditor.useCallback[handleTeacherTextHighlight]\": (d)=>{\n                                var _d_options_className, _d_options_className1;\n                                return ((_d_options_className = d.options.className) === null || _d_options_className === void 0 ? void 0 : _d_options_className.includes('teacher-highlight')) || ((_d_options_className1 = d.options.className) === null || _d_options_className1 === void 0 ? void 0 : _d_options_className1.includes('teacher-text-highlight'));\n                            }\n                        }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"]);\n                        console.log('🔍 Teacher decorations in model:', teacherDecorations);\n                    }\n                }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"], 200);\n                // Force a layout update to ensure the decoration is visible\n                setTimeout({\n                    \"CodeEditor.useCallback[handleTeacherTextHighlight]\": ()=>{\n                        editor.layout();\n                        console.log('🔄 Editor layout refreshed');\n                    }\n                }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"], 100);\n            } catch (error) {\n                var _editorRef_current;\n                console.error('❌ Error applying teacher text highlight:', error);\n                console.error('🔍 Error details:', {\n                    editorAvailable: !!editorRef.current,\n                    modelAvailable: !!((_editorRef_current = editorRef.current) === null || _editorRef_current === void 0 ? void 0 : _editorRef_current.getModel()),\n                    selectionData: data.selection,\n                    userRole: userRole,\n                    monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined',\n                    currentDecorations: teacherSelectionDecorations\n                });\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle clearing teacher text highlight with enhanced logging\n    const handleClearTeacherTextHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherTextHighlight]\": (data)=>{\n            console.log(\"\\uD83E\\uDDF9 Teacher \".concat(data.teacherName, \" cleared text highlight\"));\n            console.log(\"\\uD83D\\uDD0D Clear debug info:\", {\n                editorAvailable: !!editorRef.current,\n                userRole: userRole,\n                roomId: roomId,\n                currentDecorations: teacherSelectionDecorations.length,\n                monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined'\n            });\n            if (!editorRef.current || userRole === 'teacher') {\n                console.log('⏭️ Skipping clear teacher text highlight: editor not available or user is teacher');\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('❌ Monaco Editor model not available for clearing highlights');\n                    return;\n                }\n                console.log('🧹 Clearing teacher text highlight decorations...');\n                console.log(\"\\uD83D\\uDD0D Clearing \".concat(teacherSelectionDecorations.length, \" existing decorations\"));\n                // Clear all teacher text highlight decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, []);\n                console.log('✅ Teacher text highlight decorations cleared:', newDecorations);\n                setTeacherSelectionDecorations(newDecorations);\n                // Force a layout update to ensure the decorations are removed\n                setTimeout({\n                    \"CodeEditor.useCallback[handleClearTeacherTextHighlight]\": ()=>{\n                        editor.layout();\n                        console.log('🔄 Editor layout refreshed after clearing highlights');\n                    }\n                }[\"CodeEditor.useCallback[handleClearTeacherTextHighlight]\"], 50);\n            } catch (error) {\n                var _editorRef_current;\n                console.error('❌ Error clearing teacher text highlight:', error);\n                console.error('🔍 Error details:', {\n                    editorAvailable: !!editorRef.current,\n                    modelAvailable: !!((_editorRef_current = editorRef.current) === null || _editorRef_current === void 0 ? void 0 : _editorRef_current.getModel()),\n                    userRole: userRole,\n                    currentDecorations: teacherSelectionDecorations,\n                    monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined'\n                });\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherTextHighlight]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            // Handle user typing notifications\n            const handleUserTyping = {\n                \"CodeEditor.useEffect.handleUserTyping\": (param)=>{\n                    let { username, userId } = param;\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    // Don't show typing indicator for current user\n                    if (userId && userId === currentUserId) {\n                        return;\n                    }\n                    // Use the exact username without any modifications\n                    // This ensures we display exactly what the user entered on the dashboard\n                    setTypingUser(username);\n                    console.log(\"User typing: \".concat(username));\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserTyping\": ()=>setTypingUser(null)\n                    }[\"CodeEditor.useEffect.handleUserTyping\"], 2000);\n                }\n            }[\"CodeEditor.useEffect.handleUserTyping\"];\n            // Handle user list updates from both user-joined/user-left and room-users-updated events\n            const handleUserListUpdate = {\n                \"CodeEditor.useEffect.handleUserListUpdate\": (data)=>{\n                    console.log('User list update received:', data);\n                    let users = [];\n                    // Handle different event formats\n                    if (Array.isArray(data)) {\n                        // This is from user-joined or user-left events\n                        users = data;\n                    } else if (data && Array.isArray(data.users)) {\n                        // This is from room-users-updated event\n                        users = data.users.map({\n                            \"CodeEditor.useEffect.handleUserListUpdate\": (user)=>{\n                                if (typeof user === 'string') {\n                                    // If it's just a username string, convert to user object\n                                    return {\n                                        username: user\n                                    };\n                                }\n                                return user;\n                            }\n                        }[\"CodeEditor.useEffect.handleUserListUpdate\"]);\n                    }\n                    console.log('Processed users:', users);\n                    console.log('Current username state:', username);\n                    console.log('Stored username:', localStorage.getItem('username'));\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    console.log('Current userId from localStorage:', currentUserId);\n                    // Filter out any invalid users and map to display names\n                    const usernames = users.filter({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>user && user.username\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]).map({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>{\n                            const displayName = user.username;\n                            const isCurrentUser = user.userId === currentUserId;\n                            return isCurrentUser ? \"\".concat(displayName, \" (you)\") : displayName;\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]);\n                    console.log('Setting active users:', usernames);\n                    // Only update if we have users to display\n                    if (usernames.length > 0) {\n                        setActiveUsers(usernames);\n                    } else if (activeUsers.length === 0) {\n                        // If we don't have users from the server but we're connected, add ourselves\n                        const storedUsername = localStorage.getItem('username');\n                        if (storedUsername) {\n                            setActiveUsers([\n                                \"\".concat(storedUsername, \" (you)\")\n                            ]);\n                        }\n                    }\n                    // Log the current state of the active users list\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserListUpdate\": ()=>{\n                            console.log('Active users state after update:', activeUsers);\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate\"], 100);\n                }\n            }[\"CodeEditor.useEffect.handleUserListUpdate\"];\n            // Register event listeners\n            const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n            socketServiceInstance.on('code-update', handleCodeUpdate);\n            socketServiceInstance.on('user-typing', handleUserTyping);\n            socketServiceInstance.on('user-joined', handleUserListUpdate);\n            socketServiceInstance.on('user-left', handleUserListUpdate);\n            socketServiceInstance.on('room-users-updated', handleUserListUpdate);\n            socketServiceInstance.on('get-initial-code', handleGetInitialCode);\n            socketServiceInstance.on('initial-code-received', handleInitialCodeReceived);\n            socketServiceInstance.on('teacher-selection', handleTeacherSelection);\n            socketServiceInstance.on('clear-teacher-selection', handleClearTeacherSelection);\n            socketServiceInstance.on('teacher-cursor-position', handleTeacherCursorPosition);\n            socketServiceInstance.on('teacher-text-highlight', handleTeacherTextHighlight);\n            socketServiceInstance.on('clear-teacher-text-highlight', handleClearTeacherTextHighlight);\n            // Join the room when component mounts\n            if (roomId) {\n                console.log(\"Joining room:\", roomId);\n                // Only show loading on initial join, not for code updates\n                if (!editorRef.current) {\n                    setIsLoading(true);\n                }\n                const joinRoom = {\n                    \"CodeEditor.useEffect.joinRoom\": async ()=>{\n                        try {\n                            if (socketServiceInstance.isConnected()) {\n                                console.log(\"Socket connected, joining room\");\n                                const { username: validatedUsername, users, role } = await socketServiceInstance.joinRoom(roomId, username);\n                                console.log(\"Successfully joined room:\", roomId);\n                                console.log(\"Users in room:\", users);\n                                console.log(\"User role:\", role);\n                                if (validatedUsername !== username) {\n                                    console.log(\"Server validated username from \".concat(username, \" to \").concat(validatedUsername));\n                                    setUsername(validatedUsername);\n                                    localStorage.setItem('username', validatedUsername);\n                                }\n                                // Set user role and persist to localStorage\n                                if (role) {\n                                    setUserRole(role);\n                                    localStorage.setItem('userRole', role);\n                                    console.log(\"User role set to: \".concat(role, \" and persisted to localStorage\"));\n                                }\n                                if (users && Array.isArray(users)) {\n                                    console.log('Setting active users from join response');\n                                    handleUserListUpdate(users);\n                                }\n                                setIsLoading(false);\n                            } else {\n                                console.log(\"Socket not connected, trying to connect...\");\n                                await new Promise({\n                                    \"CodeEditor.useEffect.joinRoom\": (resolve)=>socketServiceInstance.onConnect({\n                                            \"CodeEditor.useEffect.joinRoom\": ()=>resolve()\n                                        }[\"CodeEditor.useEffect.joinRoom\"])\n                                }[\"CodeEditor.useEffect.joinRoom\"]);\n                                // After connect, try join again\n                                await joinRoom();\n                                return;\n                            }\n                        } catch (error) {\n                            console.error(\"Error joining room:\", error);\n                            setIsLoading(false);\n                        }\n                    }\n                }[\"CodeEditor.useEffect.joinRoom\"];\n                // Start the join process\n                joinRoom();\n                // Set a timeout to hide the loading screen even if the join fails\n                setTimeout({\n                    \"CodeEditor.useEffect\": ()=>{\n                        setIsLoading(false);\n                    }\n                }[\"CodeEditor.useEffect\"], 3000);\n            }\n            // Clean up event listeners when component unmounts\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    // Use an instance of SocketService\n                    const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                    socketServiceInstance.off('code-update', handleCodeUpdate);\n                    socketServiceInstance.off('user-typing', handleUserTyping);\n                    socketServiceInstance.off('user-joined', handleUserListUpdate);\n                    socketServiceInstance.off('user-left', handleUserListUpdate);\n                    socketServiceInstance.off('room-users-updated', handleUserListUpdate);\n                    socketServiceInstance.off('get-initial-code', handleGetInitialCode);\n                    socketServiceInstance.off('initial-code-received', handleInitialCodeReceived);\n                    socketServiceInstance.off('teacher-selection', handleTeacherSelection);\n                    socketServiceInstance.off('clear-teacher-selection', handleClearTeacherSelection);\n                    socketServiceInstance.off('teacher-cursor-position', handleTeacherCursorPosition);\n                    socketServiceInstance.off('teacher-text-highlight', handleTeacherTextHighlight);\n                    socketServiceInstance.off('clear-teacher-text-highlight', handleClearTeacherTextHighlight);\n                    // Leave the room when component unmounts\n                    if (roomId) {\n                        socketServiceInstance.leaveRoom(roomId);\n                    }\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId,\n        initialUsername,\n        username,\n        handleCodeUpdate,\n        handleGetInitialCode,\n        handleInitialCodeReceived,\n        handleTeacherSelection,\n        handleClearTeacherSelection,\n        handleTeacherCursorPosition,\n        handleTeacherTextHighlight,\n        handleClearTeacherTextHighlight\n    ]);\n    // Download code as file\n    const handleDownload = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"code-\".concat(roomId || \"session\", \".txt\");\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    // Upload code from file\n    const handleUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            var _event_target;\n            if (typeof ((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result) === \"string\") {\n                setCode(event.target.result);\n                latestCodeRef.current = event.target.result;\n            }\n        };\n        reader.readAsText(file);\n    };\n    // Show Monaco's find/replace dialog\n    const handleFindReplace = ()=>{\n        if (editorRef.current) {\n            const action = editorRef.current.getAction(\"actions.find\");\n            if (action) action.run();\n        }\n    };\n    // Insert code snippet\n    const insertSnippet = (snippet)=>{\n        if (editorRef.current) {\n            const model = editorRef.current.getModel();\n            const position = editorRef.current.getPosition();\n            if (model && position) {\n                editorRef.current.executeEdits(\"snippet\", [\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(position.lineNumber, position.column, position.lineNumber, position.column),\n                        text: snippet,\n                        forceMoveMarkers: true\n                    }\n                ]);\n                editorRef.current.focus();\n            }\n        }\n    };\n    // Use the instance of SocketService for the connect method\n    const handleReconnect = ()=>{\n        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n        socketServiceInstance.connect();\n    };\n    // Manual test function for debugging teacher text highlighting\n    const testManualHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[testManualHighlight]\": ()=>{\n            console.log('🧪 Manual test highlight triggered');\n            if (!editorRef.current) {\n                console.error('❌ Editor not available for manual test');\n                return;\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('❌ Monaco Editor model not available for manual test');\n                    return;\n                }\n                console.log('✅ Manual test: Editor and model available');\n                console.log('✅ Manual test: Model line count:', model.getLineCount());\n                console.log('✅ Manual test: Model content preview:', model.getValue().substring(0, 100));\n                // Create a test range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(1, 1, 1, 20);\n                console.log('✅ Manual test: Range created:', range);\n                // Test multiple decoration approaches with yellow/orange theme\n                const decorationOptions = [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-selection-highlight teacher-text-highlight teacher-highlight',\n                            hoverMessage: {\n                                value: 'Manual test selection highlight - Yellow background with orange border!'\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Right\n                            }\n                        }\n                    },\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(2, 1, 2, 15),\n                        options: {\n                            className: 'teacher-cursor',\n                            hoverMessage: {\n                                value: 'Manual test cursor - Blinking orange cursor!'\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges\n                        }\n                    },\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(3, 1, 3, 25),\n                        options: {\n                            className: 'teacher-selection-highlight',\n                            hoverMessage: {\n                                value: 'Manual test selection - Line 3!'\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Left\n                            }\n                        }\n                    }\n                ];\n                // Apply decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, decorationOptions);\n                console.log('✅ Manual test: Decorations applied:', newDecorations);\n                setTeacherSelectionDecorations(newDecorations);\n                // Debug: Check DOM after decoration\n                setTimeout({\n                    \"CodeEditor.useCallback[testManualHighlight]\": ()=>{\n                        const decorationElements = document.querySelectorAll('.teacher-text-highlight, .teacher-highlight');\n                        console.log('🔍 Manual test: Found decoration elements:', decorationElements.length);\n                        // Force add a visible element for testing with yellow/orange theme\n                        const testDiv = document.createElement('div');\n                        testDiv.style.cssText = \"\\n          position: fixed;\\n          top: 10px;\\n          right: 10px;\\n          background: rgba(255, 152, 0, 0.9);\\n          color: white;\\n          padding: 12px;\\n          border-radius: 6px;\\n          border: 2px solid rgba(255, 235, 59, 0.8);\\n          box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);\\n          z-index: 9999;\\n          font-family: monospace;\\n          font-weight: bold;\\n          animation: pulse 2s ease-in-out infinite;\\n        \";\n                        testDiv.innerHTML = \"\\n          \\uD83C\\uDFAF Manual Test Results<br>\\n          \\uD83D\\uDCCA \".concat(newDecorations.length, \" decorations applied<br>\\n          \\uD83C\\uDFA8 Yellow/Orange theme active\\n        \");\n                        document.body.appendChild(testDiv);\n                        setTimeout({\n                            \"CodeEditor.useCallback[testManualHighlight]\": ()=>{\n                                if (document.body.contains(testDiv)) {\n                                    document.body.removeChild(testDiv);\n                                }\n                            }\n                        }[\"CodeEditor.useCallback[testManualHighlight]\"], 4000);\n                    }\n                }[\"CodeEditor.useCallback[testManualHighlight]\"], 100);\n            } catch (error) {\n                console.error('❌ Manual test error:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[testManualHighlight]\"], [\n        teacherSelectionDecorations\n    ]);\n    // Add manual test to window for debugging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            if (true) {\n                window.testManualHighlight = testManualHighlight;\n                console.log('🔧 Manual test function added to window.testManualHighlight()');\n            }\n        }\n    }[\"CodeEditor.useEffect\"], [\n        testManualHighlight\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-[80vh] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-800 shadow-xl flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"bg-gray-100 dark:bg-zinc-800 px-4 py-2 flex flex-wrap items-center justify-between border-b border-gray-200 dark:border-gray-700 gap-2 min-h-[48px]\",\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    style: {\n                        zIndex: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-3 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 min-w-[90px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1603,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-400 truncate\",\n                                            children: isConnected ? 'Connected' : 'Disconnected'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1604,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded ml-1 transition-colors\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1608,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1602,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: language,\n                                    onChange: (e)=>changeLanguage(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"javascript\",\n                                            children: \"JavaScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1623,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"typescript\",\n                                            children: \"TypeScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1624,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"python3\",\n                                            children: \"Python\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1625,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"java\",\n                                            children: \"Java\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1626,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"csharp\",\n                                            children: \"C#\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1627,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"c\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1628,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"cpp\",\n                                            children: \"C++\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1629,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"go\",\n                                            children: \"Go\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1630,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ruby\",\n                                            children: \"Ruby\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1631,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rust\",\n                                            children: \"Rust\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1632,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"php\",\n                                            children: \"PHP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1633,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1618,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: formatCurrentCode,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm bg-blue-600 hover:bg-blue-700 text-white transition-colors whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiAlignLeft, {\n                                            size: 14\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1643,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Format\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1644,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1637,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: executeCode,\n                                    disabled: isExecuting,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm \".concat(isExecuting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700', \" text-white transition-colors whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: isExecuting ? 1 : 1.05\n                                    },\n                                    whileTap: {\n                                        scale: isExecuting ? 1 : 0.95\n                                    },\n                                    children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                                className: \"animate-spin\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1657,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Running...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1658,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiPlay, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1662,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Run Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1663,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1648,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Room:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1670,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-white dark:bg-zinc-700 px-2 py-1 rounded font-mono text-xs truncate max-w-[120px]\",\n                                            children: roomId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1671,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                            onClick: copyCodeToClipboard,\n                                            className: \"text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            title: \"Copy code to clipboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCopy, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1679,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1672,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1669,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1600,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-2 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowOutput((prev)=>!prev),\n                                    className: \"flex items-center space-x-1 text-sm \".concat(showOutput ? 'text-blue-500 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300', \" hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCode, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1692,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Output\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1693,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1686,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: toggleUserList,\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUsers, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1703,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                activeUsers.length,\n                                                \" online\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1704,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1697,\n                                    columnNumber: 13\n                                }, this),\n                                userRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    className: \"flex items-center space-x-1 text-xs font-medium px-2 py-1 rounded-full whitespace-nowrap \".concat(userRole === 'teacher' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'),\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: userRole === 'teacher' ? '👨‍🏫' : '👨‍🎓'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1719,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: userRole === 'teacher' ? 'Teacher' : 'Student'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1720,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1709,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"flex items-center space-x-1 text-sm text-yellow-600 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSun, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1732,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiMoon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1732,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1725,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setMinimapEnabled((v)=>!v),\n                                    className: \"flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Minimap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Minimap\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1743,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1736,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleFindReplace,\n                                    className: \"flex items-center space-x-1 text-sm text-green-600 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Find/Replace\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSearch, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1754,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1747,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleDownload,\n                                    className: \"flex items-center space-x-1 text-sm text-indigo-600 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Download Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiDownload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1765,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1758,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"flex items-center space-x-1 text-sm text-pink-600 dark:text-pink-300 bg-pink-100 dark:bg-pink-900/30 hover:bg-pink-200 dark:hover:bg-pink-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Upload Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUpload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1776,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1769,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \".js,.ts,.py,.java,.c,.cpp,.go,.rb,.rs,.php,.txt\",\n                                    style: {\n                                        display: \"none\"\n                                    },\n                                    onChange: handleUpload\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1778,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    onChange: (e)=>e.target.value && insertSnippet(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[100px]\",\n                                    defaultValue: \"\",\n                                    title: \"Insert Snippet\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Snippets\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1793,\n                                            columnNumber: 15\n                                        }, this),\n                                        (SNIPPETS[language] || SNIPPETS['javascript']).map((snippet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: snippet.value,\n                                                children: snippet.label\n                                            }, snippet.label, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1795,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1787,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowShortcuts(true),\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Keyboard Shortcuts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiHelpCircle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1807,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1800,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleLeaveRoom,\n                                    className: \"flex items-center space-x-1 text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors px-2 py-1 rounded-md ml-2 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Leave Room\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1817,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1811,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1684,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1593,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showUserList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute right-0 top-12 bg-white dark:bg-zinc-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 w-48 overflow-hidden\",\n                        initial: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            height: 'auto'\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 border-b border-gray-200 dark:border-gray-700 font-medium text-sm text-gray-600 dark:text-gray-300\",\n                                children: \"Active Users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1832,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"max-h-48 overflow-y-auto\",\n                                children: activeUsers.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.li, {\n                                        className: \"px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-zinc-700 flex items-center space-x-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full bg-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1844,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: user\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1845,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1837,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1835,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1825,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1823,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"h-[calc(100%-40px)] \".concat(showOutput ? 'pb-[33%]' : ''),\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.4,\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    height: \"100%\",\n                                    defaultLanguage: language,\n                                    defaultValue: code,\n                                    onChange: handleEditorChange,\n                                    onMount: handleEditorDidMount,\n                                    theme: theme === \"dark\" ? \"vs-dark\" : \"light\",\n                                    options: {\n                                        minimap: {\n                                            enabled: minimapEnabled\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1863,\n                                    columnNumber: 15\n                                }, this),\n                                teacherCursorPosition && editorRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TeacherCursor, {\n                                    editor: editorRef.current,\n                                    position: teacherCursorPosition\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1875,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1862,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1860,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1854,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: typingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 left-4 text-sm text-gray-400 bg-gray-800/80 px-3 py-1 rounded-full backdrop-blur-sm\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            typingUser,\n                            \" is typing...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1887,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1885,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gray-900 text-white border-t border-gray-700 h-1/3 overflow-hidden flex flex-col\",\n                        initial: {\n                            height: 0\n                        },\n                        animate: {\n                            height: '33%'\n                        },\n                        exit: {\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 bg-gray-800 border-b border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1910,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearOutput,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1912,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: testPistonAPI,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-blue-400 hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Test API\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1919,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowOutput(false),\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1926,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1911,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1909,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-auto font-mono text-sm whitespace-pre-wrap\",\n                                children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1937,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Executing code...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1938,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1936,\n                                    columnNumber: 19\n                                }, this) : executionError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400\",\n                                    children: executionError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1941,\n                                    columnNumber: 19\n                                }, this) : executionOutput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: executionOutput\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1943,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: 'Click \"Run Code\" to execute your code.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1945,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1934,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1902,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1900,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: copySuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 right-4 text-sm text-white bg-green-500 px-3 py-1 rounded-full shadow-lg\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: \"Copied to clipboard!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1955,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1953,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showShortcuts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 max-w-md w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-700 dark:hover:text-white\",\n                                    onClick: ()=>setShowShortcuts(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1977,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    children: \"Keyboard Shortcuts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1983,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Run Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1985,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+Enter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1985,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Format Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1986,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Shift+Alt+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1986,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Find/Replace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1987,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1987,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Download Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1988,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+D\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1988,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Upload Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1989,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+U\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1989,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Minimap:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1990,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+M\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1990,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Theme:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1991,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+T\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1991,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Show Shortcuts:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1992,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+H\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1992,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1984,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1976,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1970,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1968,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 1591,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 1589,\n        columnNumber: 5\n    }, this);\n}\n_s(CodeEditor, \"RvU97k1oHYhg/3LrOrvNHSBVWe0=\", false, function() {\n    return [\n        _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme\n    ];\n});\n_c = CodeEditor;\nconst TeacherCursor = (param)=>{\n    let { editor, position } = param;\n    _s1();\n    const [cursorStyle, setCursorStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherCursor.useEffect\": ()=>{\n            const updateCursorPosition = {\n                \"TeacherCursor.useEffect.updateCursorPosition\": ()=>{\n                    try {\n                        // Get the pixel position of the cursor\n                        const pixelPosition = editor.getScrolledVisiblePosition({\n                            lineNumber: position.lineNumber,\n                            column: position.column\n                        });\n                        if (pixelPosition) {\n                            // Get the editor container position\n                            const editorContainer = editor.getDomNode();\n                            if (editorContainer) {\n                                const containerRect = editorContainer.getBoundingClientRect();\n                                setCursorStyle({\n                                    position: 'absolute',\n                                    left: \"\".concat(pixelPosition.left, \"px\"),\n                                    top: \"\".concat(pixelPosition.top, \"px\"),\n                                    zIndex: 1000,\n                                    pointerEvents: 'none'\n                                });\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error updating teacher cursor position:', error);\n                    }\n                }\n            }[\"TeacherCursor.useEffect.updateCursorPosition\"];\n            // Update position immediately\n            updateCursorPosition();\n            // Update position when editor scrolls or layout changes\n            const scrollDisposable = editor.onDidScrollChange(updateCursorPosition);\n            const layoutDisposable = editor.onDidLayoutChange(updateCursorPosition);\n            return ({\n                \"TeacherCursor.useEffect\": ()=>{\n                    scrollDisposable.dispose();\n                    layoutDisposable.dispose();\n                }\n            })[\"TeacherCursor.useEffect\"];\n        }\n    }[\"TeacherCursor.useEffect\"], [\n        editor,\n        position\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: cursorStyle,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"teacher-cursor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"teacher-cursor-label\",\n                children: position.teacherName\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 2057,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 2056,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 2055,\n        columnNumber: 5\n    }, undefined);\n}; // Keyboard shortcuts global handler\n // useEffect(() => {\n //   const handler = (e: KeyboardEvent) => {\n //     if (e.altKey && e.key.toLowerCase() === \"d\") {\n //       e.preventDefault();\n //       handleDownload();\n //     } else if (e.altKey && e.key.toLowerCase() === \"u\") {\n //       e.preventDefault();\n //       fileInputRef.current?.click();\n //     } else if (e.altKey && e.key.toLowerCase() === \"m\") {\n //       e.preventDefault();\n //       setMinimapEnabled((v: boolean) => !v);\n //     } else if (e.altKey && e.key.toLowerCase() === \"t\") {\n //       e.preventDefault();\n //       setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n //     } else if (e.altKey && e.key.toLowerCase() === \"h\") {\n //       e.preventDefault();\n //       setShowShortcuts(true);\n //     }\n //   };\n //   window.addEventListener(\"keydown\", handler);\n //   return () => window.removeEventListener(\"keydown\", handler);\n // }, [theme, setTheme]);\n_s1(TeacherCursor, \"NrZZSKlPdtAtpq9NKiew7IOqw6k=\");\n_c1 = TeacherCursor;\nvar _c, _c1;\n$RefreshReg$(_c, \"CodeEditor\");\n$RefreshReg$(_c1, \"TeacherCursor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CodeEditor.tsx\n"));

/***/ })

});