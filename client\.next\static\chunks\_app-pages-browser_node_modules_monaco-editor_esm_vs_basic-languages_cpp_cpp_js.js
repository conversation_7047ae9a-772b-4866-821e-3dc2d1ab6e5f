"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_cpp_cpp_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/cpp/cpp.js":
/*!***********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/cpp/cpp.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/cpp/cpp.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#pragma\\\\s+region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#pragma\\\\s+endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".cpp\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.angle\", open: \"<\", close: \">\" }\n  ],\n  keywords: [\n    \"abstract\",\n    \"amp\",\n    \"array\",\n    \"auto\",\n    \"bool\",\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"char\",\n    \"class\",\n    \"const\",\n    \"constexpr\",\n    \"const_cast\",\n    \"continue\",\n    \"cpu\",\n    \"decltype\",\n    \"default\",\n    \"delegate\",\n    \"delete\",\n    \"do\",\n    \"double\",\n    \"dynamic_cast\",\n    \"each\",\n    \"else\",\n    \"enum\",\n    \"event\",\n    \"explicit\",\n    \"export\",\n    \"extern\",\n    \"false\",\n    \"final\",\n    \"finally\",\n    \"float\",\n    \"for\",\n    \"friend\",\n    \"gcnew\",\n    \"generic\",\n    \"goto\",\n    \"if\",\n    \"in\",\n    \"initonly\",\n    \"inline\",\n    \"int\",\n    \"interface\",\n    \"interior_ptr\",\n    \"internal\",\n    \"literal\",\n    \"long\",\n    \"mutable\",\n    \"namespace\",\n    \"new\",\n    \"noexcept\",\n    \"nullptr\",\n    \"__nullptr\",\n    \"operator\",\n    \"override\",\n    \"partial\",\n    \"pascal\",\n    \"pin_ptr\",\n    \"private\",\n    \"property\",\n    \"protected\",\n    \"public\",\n    \"ref\",\n    \"register\",\n    \"reinterpret_cast\",\n    \"restrict\",\n    \"return\",\n    \"safe_cast\",\n    \"sealed\",\n    \"short\",\n    \"signed\",\n    \"sizeof\",\n    \"static\",\n    \"static_assert\",\n    \"static_cast\",\n    \"struct\",\n    \"switch\",\n    \"template\",\n    \"this\",\n    \"thread_local\",\n    \"throw\",\n    \"tile_static\",\n    \"true\",\n    \"try\",\n    \"typedef\",\n    \"typeid\",\n    \"typename\",\n    \"union\",\n    \"unsigned\",\n    \"using\",\n    \"virtual\",\n    \"void\",\n    \"volatile\",\n    \"wchar_t\",\n    \"where\",\n    \"while\",\n    \"_asm\",\n    // reserved word with one underscores\n    \"_based\",\n    \"_cdecl\",\n    \"_declspec\",\n    \"_fastcall\",\n    \"_if_exists\",\n    \"_if_not_exists\",\n    \"_inline\",\n    \"_multiple_inheritance\",\n    \"_pascal\",\n    \"_single_inheritance\",\n    \"_stdcall\",\n    \"_virtual_inheritance\",\n    \"_w64\",\n    \"__abstract\",\n    // reserved word with two underscores\n    \"__alignof\",\n    \"__asm\",\n    \"__assume\",\n    \"__based\",\n    \"__box\",\n    \"__builtin_alignof\",\n    \"__cdecl\",\n    \"__clrcall\",\n    \"__declspec\",\n    \"__delegate\",\n    \"__event\",\n    \"__except\",\n    \"__fastcall\",\n    \"__finally\",\n    \"__forceinline\",\n    \"__gc\",\n    \"__hook\",\n    \"__identifier\",\n    \"__if_exists\",\n    \"__if_not_exists\",\n    \"__inline\",\n    \"__int128\",\n    \"__int16\",\n    \"__int32\",\n    \"__int64\",\n    \"__int8\",\n    \"__interface\",\n    \"__leave\",\n    \"__m128\",\n    \"__m128d\",\n    \"__m128i\",\n    \"__m256\",\n    \"__m256d\",\n    \"__m256i\",\n    \"__m512\",\n    \"__m512d\",\n    \"__m512i\",\n    \"__m64\",\n    \"__multiple_inheritance\",\n    \"__newslot\",\n    \"__nogc\",\n    \"__noop\",\n    \"__nounwind\",\n    \"__novtordisp\",\n    \"__pascal\",\n    \"__pin\",\n    \"__pragma\",\n    \"__property\",\n    \"__ptr32\",\n    \"__ptr64\",\n    \"__raise\",\n    \"__restrict\",\n    \"__resume\",\n    \"__sealed\",\n    \"__single_inheritance\",\n    \"__stdcall\",\n    \"__super\",\n    \"__thiscall\",\n    \"__try\",\n    \"__try_cast\",\n    \"__typeof\",\n    \"__unaligned\",\n    \"__unhook\",\n    \"__uuidof\",\n    \"__value\",\n    \"__virtual_inheritance\",\n    \"__w64\",\n    \"__wchar_t\"\n  ],\n  operators: [\n    \"=\",\n    \">\",\n    \"<\",\n    \"!\",\n    \"~\",\n    \"?\",\n    \":\",\n    \"==\",\n    \"<=\",\n    \">=\",\n    \"!=\",\n    \"&&\",\n    \"||\",\n    \"++\",\n    \"--\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"%\",\n    \"<<\",\n    \">>\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[0abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  integersuffix: /([uU](ll|LL|l|L)|(ll|LL|l|L)?[uU]?)/,\n  floatsuffix: /[fFlL]?/,\n  encoding: /u|u8|U|L/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // C++ 11 Raw String\n      [/@encoding?R\\\"(?:([^ ()\\\\\\t]*))\\(/, { token: \"string.raw.begin\", next: \"@raw.$1\" }],\n      // identifiers and keywords\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // The preprocessor checks must be before whitespace as they check /^\\s*#/ which\n      // otherwise fails to match later after other whitespace has been removed.\n      // Inclusion\n      [/^\\s*#\\s*include/, { token: \"keyword.directive.include\", next: \"@include\" }],\n      // Preprocessor directive\n      [/^\\s*#\\s*\\w+/, \"keyword.directive\"],\n      // whitespace\n      { include: \"@whitespace\" },\n      // [[ attributes ]].\n      [/\\[\\s*\\[/, { token: \"annotation\", next: \"@annotation\" }],\n      // delimiters and operators\n      [/[{}()<>\\[\\]]/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?(@floatsuffix)/, \"number.float\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?(@floatsuffix)/, \"number.float\"],\n      [/0[xX][0-9a-fA-F']*[0-9a-fA-F](@integersuffix)/, \"number.hex\"],\n      [/0[0-7']*[0-7](@integersuffix)/, \"number.octal\"],\n      [/0[bB][0-1']*[0-1](@integersuffix)/, \"number.binary\"],\n      [/\\d[\\d']*\\d(@integersuffix)/, \"number\"],\n      [/\\d(@integersuffix)/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string\"],\n      // characters\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@doccomment\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*\\\\$/, \"comment\", \"@linecomment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    //For use with continuous line comments\n    linecomment: [\n      [/.*[^\\\\]$/, \"comment\", \"@pop\"],\n      [/[^]+/, \"comment\"]\n    ],\n    //Identical copy of comment above, except for the addition of .doc\n    doccomment: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    raw: [\n      [/[^)]+/, \"string.raw\"],\n      [/\\)$S2\\\"/, { token: \"string.raw.end\", next: \"@pop\" }],\n      [/\\)/, \"string.raw\"]\n    ],\n    annotation: [\n      { include: \"@whitespace\" },\n      [/using|alignas/, \"keyword\"],\n      [/[a-zA-Z0-9_]+/, \"annotation\"],\n      [/[,:]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [/\\]\\s*\\]/, { token: \"annotation\", next: \"@pop\" }]\n    ],\n    include: [\n      [\n        /(\\s*)(<)([^<>]*)(>)/,\n        [\n          \"\",\n          \"keyword.directive.include.begin\",\n          \"string.include.identifier\",\n          { token: \"keyword.directive.include.end\", next: \"@pop\" }\n        ]\n      ],\n      [\n        /(\\s*)(\")([^\"]*)(\")/,\n        [\n          \"\",\n          \"keyword.directive.include.begin\",\n          \"string.include.identifier\",\n          { token: \"keyword.directive.include.end\", next: \"@pop\" }\n        ]\n      ]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/cpp/cpp.js\n"));

/***/ })

}]);