'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useSocketService } from '@/hooks/useSocketService';

interface Student {
  socketId: string;
  username: string;
  userId: string;
  email?: string;
  canEdit: boolean;
  joinedAt: string;
  lastActivity: string;
}

interface User {
  username: string;
  role: 'teacher' | 'student';
  socketId: string;
  userId: string;
  canEdit: boolean;
}

interface EditPermissionContextType {
  canEdit: boolean;
  isTeacher: boolean;
  users: User[];
  students: Student[];
  permissionBadge: 'teacher' | 'edit-access' | 'view-only';
  grantEditPermission: (targetSocketId: string) => void;
  revokeEditPermission: (targetSocketId: string) => void;
  setEditPermission: (targetSocketId: string, canEdit: boolean) => void;
  updateUserPermission: (socketId: string, canEdit: boolean) => void;
  setUsers: (users: User[]) => void;
  setStudents: (students: Student[]) => void;
  setCanEdit: (canEdit: boolean) => void;
  setIsTeacher: (isTeacher: boolean) => void;
}

const EditPermissionContext = createContext<EditPermissionContextType | undefined>(undefined);

interface EditPermissionProviderProps {
  children: ReactNode;
}

export function EditPermissionProvider({ children }: EditPermissionProviderProps) {
  const [canEdit, setCanEdit] = useState<boolean>(false);
  const [isTeacher, setIsTeacher] = useState<boolean>(false);
  const [users, setUsers] = useState<User[]>([]);
  const [students, setStudents] = useState<Student[]>([]);

  // Use the socket service hook
  const { socketService, isReady: socketReady, isConnected } = useSocketService();

  // Compute permission badge based on current state
  const permissionBadge: 'teacher' | 'edit-access' | 'view-only' =
    isTeacher ? 'teacher' : (canEdit ? 'edit-access' : 'view-only');

  // Grant edit permission to a student
  const grantEditPermission = (targetSocketId: string) => {
    if (!isTeacher) {
      console.warn('Only teachers can grant edit permissions');
      return;
    }

    if (!socketService || !socketReady) {
      console.warn('Socket service not ready, cannot grant edit permission');
      return;
    }

    const roomId = typeof window !== 'undefined' ?
      window.localStorage.getItem('currentRoomId') : null;

    if (!roomId) {
      console.error('No room ID found');
      return;
    }

    console.log(`Granting edit permission to ${targetSocketId}`);

    try {
      // FIX: Use the public method, not .emit
      socketService.setEditPermission(roomId, targetSocketId, true);
    } catch (error) {
      console.error('Error granting edit permission:', error);
    }
  };

  // Revoke edit permission from a student
  const revokeEditPermission = (targetSocketId: string) => {
    if (!isTeacher) {
      console.warn('Only teachers can revoke edit permissions');
      return;
    }

    if (!socketService || !socketReady) {
      console.warn('Socket service not ready, cannot revoke edit permission');
      return;
    }

    const roomId = typeof window !== 'undefined' ?
      window.localStorage.getItem('currentRoomId') : null;

    if (!roomId) {
      console.error('No room ID found');
      return;
    }

    console.log(`Revoking edit permission from ${targetSocketId}`);

    try {
      // FIX: Use the public method, not .emit
      socketService.setEditPermission(roomId, targetSocketId, false);
    } catch (error) {
      console.error('Error revoking edit permission:', error);
    }
  };

  // Legacy method for backward compatibility
  const setEditPermission = (targetSocketId: string, canEdit: boolean) => {
    if (canEdit) {
      grantEditPermission(targetSocketId);
    } else {
      revokeEditPermission(targetSocketId);
    }
  };

  const updateUserPermission = (socketId: string, canEdit: boolean) => {
    setUsers(prevUsers => 
      prevUsers.map(user => 
        user.socketId === socketId 
          ? { ...user, canEdit }
          : user
      )
    );
  };

  useEffect(() => {
    // Only set up listeners when socket is ready
    if (!socketReady || !socketService) {
      console.log('Waiting for socket to be ready for edit permissions...');
      return;
    }

    // Listen for edit permission changes
    const handleEditPermission = (data: { canEdit: boolean }) => {
      console.log('Received edit permission update:', data);
      setCanEdit(data.canEdit);
    };

    // Listen for permission updates (new event)
    const handlePermissionUpdated = (data: { canEdit: boolean }) => {
      console.log('Received permission-updated event:', data);
      setCanEdit(data.canEdit);
    };

    // Listen for student list updates (for teachers)
    const handleUpdateStudentList = (data: { students: Student[] | any[] }) => {
      console.log('Received update-student-list event:', data);
      if (!data || !Array.isArray(data.students)) {
        console.warn('update-student-list event received with invalid or missing students array:', data);
        setStudents([]);
        return;
      }
      // Log the raw students array for debugging
      console.log('Raw students array from update-student-list:', data.students);
      // Defensive: filter out any object with role==='teacher', otherwise accept as student
      const filtered = data.students.filter(s => {
        const isStudent = typeof s === 'object' && s !== null && (!('role' in s) || s.role !== 'teacher');
        if (!isStudent) {
          console.warn('Filtered out non-student from update-student-list:', s);
        }
        return isStudent;
      });
      console.log('Filtered students array for setStudents:', filtered);
      setStudents(filtered);
    };

    // Listen for room users updates (includes permission info)
    const handleRoomUsersUpdated = (data: { users: User[]; count: number }) => {
      console.log('Room users updated with permissions:', data);
      setUsers(data.users);
    };

    // Listen for user-joined events to trigger student list refresh for teachers
    const handleUserJoined = (data: any) => {
      console.log('User joined event received:', data);
      // If this is a teacher, request updated student list
      if (isTeacher) {
        const currentRoomId = window.location.pathname.split('/').pop();
        if (currentRoomId) {
          console.log('Teacher detected user joined, requesting updated student list');
          const sock = socketService.getSocket && socketService.getSocket();
          if (sock && typeof sock.emit === 'function') {
            // Use type assertion to bypass TS error for custom event
            (sock.emit as any)('request-student-list', { roomId: currentRoomId });
          } else {
            console.warn('Socket instance not available for request-student-list emit');
          }
        }
      }
    };

    // Set up socket listeners with null checks
    try {
      socketService.on('edit-permission', handleEditPermission);
      socketService.on('permission-updated', handlePermissionUpdated);
      socketService.on('update-student-list', handleUpdateStudentList);
      socketService.on('room-users-updated', handleRoomUsersUpdated);
      socketService.on('user-joined', handleUserJoined);
      console.log('Enhanced permission socket listeners set up successfully');
    } catch (error) {
      console.error('Error setting up socket listeners:', error);
    }

    // Cleanup listeners
    return () => {
      try {
        if (socketService) {
          socketService.off('edit-permission', handleEditPermission);
          socketService.off('permission-updated', handlePermissionUpdated);
          socketService.off('update-student-list', handleUpdateStudentList);
          socketService.off('room-users-updated', handleRoomUsersUpdated);
          socketService.off('user-joined', handleUserJoined);
          console.log('Enhanced permission socket listeners cleaned up');
        }
      } catch (error) {
        console.error('Error cleaning up socket listeners:', error);
      }
    };
  }, [socketReady]);

  // In the effect that requests the student list, use the correct public method
  useEffect(() => {
    if (!socketReady || !socketService || !isTeacher) {
      return;
    }

    // If this is a teacher, request the initial student list
    const currentRoomId = window.location.pathname.split('/').pop();
    if (currentRoomId) {
      console.log('Teacher role detected, requesting initial student list for room:', currentRoomId);
      // FIX: Use the underlying socket directly for custom events
      const sock = socketService.getSocket && socketService.getSocket();
      if (sock && typeof sock.emit === 'function') {
        sock.emit('request-student-list', { roomId: currentRoomId });
      } else {
        console.warn('Socket instance not available for request-student-list emit');
      }
    }
  }, [socketReady, isTeacher, socketService]);

  // Log permission changes for debugging
  useEffect(() => {
    console.log(`Edit permission state: canEdit=${canEdit}, isTeacher=${isTeacher}, socketReady=${socketReady}, isConnected=${isConnected}`);
  }, [canEdit, isTeacher, socketReady, isConnected]);

  const value: EditPermissionContextType = {
    canEdit,
    isTeacher,
    users,
    students,
    permissionBadge,
    grantEditPermission,
    revokeEditPermission,
    setEditPermission,
    updateUserPermission,
    setUsers,
    setStudents,
    setCanEdit,
    setIsTeacher
  };

  return (
    <EditPermissionContext.Provider value={value}>
      {children}
    </EditPermissionContext.Provider>
  );
}

export function useEditPermission() {
  const context = useContext(EditPermissionContext);
  if (context === undefined) {
    throw new Error('useEditPermission must be used within an EditPermissionProvider');
  }
  return context;
}
