'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { socketService } from '@/services/socketService';

interface User {
  username: string;
  role: 'teacher' | 'student';
  socketId: string;
  userId: string;
  canEdit: boolean;
}

interface EditPermissionContextType {
  canEdit: boolean;
  isTeacher: boolean;
  users: User[];
  setEditPermission: (targetSocketId: string, canEdit: boolean) => void;
  updateUserPermission: (socketId: string, canEdit: boolean) => void;
  setUsers: (users: User[]) => void;
  setCanEdit: (canEdit: boolean) => void;
  setIsTeacher: (isTeacher: boolean) => void;
}

const EditPermissionContext = createContext<EditPermissionContextType | undefined>(undefined);

interface EditPermissionProviderProps {
  children: ReactNode;
}

export function EditPermissionProvider({ children }: EditPermissionProviderProps) {
  const [canEdit, setCanEdit] = useState<boolean>(false);
  const [isTeacher, setIsTeacher] = useState<boolean>(false);
  const [users, setUsers] = useState<User[]>([]);

  const setEditPermission = (targetSocketId: string, canEdit: boolean) => {
    if (!isTeacher) {
      console.warn('Only teachers can set edit permissions');
      return;
    }

    // Get current room ID from localStorage or context
    const roomId = typeof window !== 'undefined' ? 
      window.localStorage.getItem('currentRoomId') : null;

    if (!roomId) {
      console.error('No room ID found');
      return;
    }

    console.log(`Setting edit permission for ${targetSocketId} to ${canEdit}`);

    // Use the socket service method to set edit permission
    socketService.setEditPermission(roomId, targetSocketId, canEdit);
  };

  const updateUserPermission = (socketId: string, canEdit: boolean) => {
    setUsers(prevUsers => 
      prevUsers.map(user => 
        user.socketId === socketId 
          ? { ...user, canEdit }
          : user
      )
    );
  };

  useEffect(() => {
    // Listen for edit permission changes
    const handleEditPermission = (data: { canEdit: boolean }) => {
      console.log('Received edit permission update:', data);
      setCanEdit(data.canEdit);
    };

    // Listen for room users updates (includes permission info)
    const handleRoomUsersUpdated = (data: { users: User[]; count: number }) => {
      console.log('Room users updated with permissions:', data);
      setUsers(data.users);
    };

    // Set up socket listeners
    socketService.on('edit-permission', handleEditPermission);
    socketService.on('room-users-updated', handleRoomUsersUpdated);

    // Cleanup listeners
    return () => {
      socketService.off('edit-permission', handleEditPermission);
      socketService.off('room-users-updated', handleRoomUsersUpdated);
    };
  }, []);

  // Log permission changes for debugging
  useEffect(() => {
    console.log(`Edit permission changed: canEdit=${canEdit}, isTeacher=${isTeacher}`);
  }, [canEdit, isTeacher]);

  const value: EditPermissionContextType = {
    canEdit,
    isTeacher,
    users,
    setEditPermission,
    updateUserPermission,
    setUsers,
    setCanEdit,
    setIsTeacher
  };

  return (
    <EditPermissionContext.Provider value={value}>
      {children}
    </EditPermissionContext.Provider>
  );
}

export function useEditPermission() {
  const context = useContext(EditPermissionContext);
  if (context === undefined) {
    throw new Error('useEditPermission must be used within an EditPermissionProvider');
  }
  return context;
}
