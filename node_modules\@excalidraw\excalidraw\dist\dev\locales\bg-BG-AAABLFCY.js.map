{"version": 3, "sources": ["../../../locales/bg-BG.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Постави\",\n    \"pasteAsPlaintext\": \"Постави като обикновен текст\",\n    \"pasteCharts\": \"Постави графики\",\n    \"selectAll\": \"Марк<PERSON><PERSON><PERSON><PERSON> всичко\",\n    \"multiSelect\": \"Добави елемент към селекция\",\n    \"moveCanvas\": \"Премести платно\",\n    \"cut\": \"Изрежи\",\n    \"copy\": \"Копирай\",\n    \"copyAsPng\": \"Копиране в клипборда\",\n    \"copyAsSvg\": \"Копирано в клипборда като SVG\",\n    \"copyText\": \"\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Преместване напред\",\n    \"sendToBack\": \"Изнасяне назад\",\n    \"bringToFront\": \"Изнасяне отпред\",\n    \"sendBackward\": \"Изпрати отзад\",\n    \"delete\": \"Изтрий\",\n    \"copyStyles\": \"Копирайте стилове\",\n    \"pasteStyles\": \"Постави стилове\",\n    \"stroke\": \"Щрих\",\n    \"background\": \"Фон\",\n    \"fill\": \"Наситеност\",\n    \"strokeWidth\": \"Ширина на щриха\",\n    \"strokeStyle\": \"Стил на линия\",\n    \"strokeStyle_solid\": \"Плътен\",\n    \"strokeStyle_dashed\": \"Пунктир\",\n    \"strokeStyle_dotted\": \"Пунктирано\",\n    \"sloppiness\": \"Небрежност\",\n    \"opacity\": \"Прозрачност\",\n    \"textAlign\": \"Подравняване на текста\",\n    \"edges\": \"Крайща\",\n    \"sharp\": \"Остър\",\n    \"round\": \"Закръглено\",\n    \"arrowheads\": \"Стрелки\",\n    \"arrowhead_none\": \"Без\",\n    \"arrowhead_arrow\": \"Стрелка\",\n    \"arrowhead_bar\": \"Връх на стрелката\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Триъгълник\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Размер на шрифта\",\n    \"fontFamily\": \"Семейство шрифтове\",\n    \"addWatermark\": \"Добави \\\"Направено с Excalidraw\\\"\",\n    \"handDrawn\": \"Нарисувано на ръка\",\n    \"normal\": \"Нормален\",\n    \"code\": \"Код\",\n    \"small\": \"Малък\",\n    \"medium\": \"Среден\",\n    \"large\": \"Голям\",\n    \"veryLarge\": \"Много голям\",\n    \"solid\": \"Солиден\",\n    \"hachure\": \"Хералдика\",\n    \"zigzag\": \"Зигзаг\",\n    \"crossHatch\": \"Двойно-пресечено\",\n    \"thin\": \"Тънък\",\n    \"bold\": \"Ясно очертан\",\n    \"left\": \"Ляво\",\n    \"center\": \"Център\",\n    \"right\": \"Дясно\",\n    \"extraBold\": \"Много ясно очертан\",\n    \"architect\": \"Архитект\",\n    \"artist\": \"Художник\",\n    \"cartoonist\": \"Карикатурист\",\n    \"fileTitle\": \"Име на файл\",\n    \"colorPicker\": \"Избор на цвят\",\n    \"canvasColors\": \"Използван на платно\",\n    \"canvasBackground\": \"Фон на платно\",\n    \"drawingCanvas\": \"Платно за рисуване\",\n    \"layers\": \"Слоеве\",\n    \"actions\": \"Действия\",\n    \"language\": \"Език\",\n    \"liveCollaboration\": \"\",\n    \"duplicateSelection\": \"Дублирай\",\n    \"untitled\": \"Неозаглавено\",\n    \"name\": \"Име\",\n    \"yourName\": \"Вашето име\",\n    \"madeWithExcalidraw\": \"Направено с Excalidraw\",\n    \"group\": \"Групирай селекцията\",\n    \"ungroup\": \"Спри групирането на селекцията\",\n    \"collaborators\": \"Сътрудници\",\n    \"showGrid\": \"Показване на мрежа\",\n    \"addToLibrary\": \"Добавяне към библиотеката\",\n    \"removeFromLibrary\": \"Премахване от библиотеката\",\n    \"libraryLoadingMessage\": \"Зареждане на библиотеката…\",\n    \"libraries\": \"Разглеждане на библиотеките\",\n    \"loadingScene\": \"Зареждане на сцена…\",\n    \"align\": \"Подравняване\",\n    \"alignTop\": \"Подравняване отгоре\",\n    \"alignBottom\": \"Подравняване отдолу\",\n    \"alignLeft\": \"Подравняване отляво\",\n    \"alignRight\": \"Подравняване отдясно\",\n    \"centerVertically\": \"Центрирай вертикално\",\n    \"centerHorizontally\": \"Центрирай хоризонтално\",\n    \"distributeHorizontally\": \"Разпредели хоризонтално\",\n    \"distributeVertically\": \"Разпредели вертикално\",\n    \"flipHorizontal\": \"Хоризонтално обръщане\",\n    \"flipVertical\": \"Вертикално обръщане\",\n    \"viewMode\": \"Изглед\",\n    \"share\": \"Сподели\",\n    \"showStroke\": \"\",\n    \"showBackground\": \"\",\n    \"toggleTheme\": \"Включи тема\",\n    \"personalLib\": \"Лична Библиотека\",\n    \"excalidrawLib\": \"Excalidraw Библиотека\",\n    \"decreaseFontSize\": \"Намали размера на шрифта\",\n    \"increaseFontSize\": \"Увеличи размера на шрифта\",\n    \"unbindText\": \"\",\n    \"bindText\": \"\",\n    \"createContainerFromText\": \"\",\n    \"link\": {\n      \"edit\": \"Редактирай линк\",\n      \"editEmbed\": \"\",\n      \"create\": \"\",\n      \"createEmbed\": \"\",\n      \"label\": \"Линк\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"\",\n      \"exit\": \"\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Заключи\",\n      \"unlock\": \"Отключи\",\n      \"lockAll\": \"Заключи всички\",\n      \"unlockAll\": \"Отключи всички\"\n    },\n    \"statusPublished\": \"Публикувани\",\n    \"sidebarLock\": \"\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"Избери цвят от платното\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Няма добавени неща все още...\",\n    \"hint_emptyLibrary\": \"\",\n    \"hint_emptyPrivateLibrary\": \"\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Нулиране на платно\",\n    \"exportJSON\": \"\",\n    \"exportImage\": \"\",\n    \"export\": \"Запази на...\",\n    \"copyToClipboard\": \"Копиране в клипборда\",\n    \"save\": \"Запази към текущ файл\",\n    \"saveAs\": \"Запиши като\",\n    \"load\": \"Отвори\",\n    \"getShareableLink\": \"Получаване на връзка за споделяне\",\n    \"close\": \"Затвори\",\n    \"selectLanguage\": \"Избор на език\",\n    \"scrollBackToContent\": \"Превъртете обратно към съдържанието\",\n    \"zoomIn\": \"Приближаване\",\n    \"zoomOut\": \"Отдалечаване\",\n    \"resetZoom\": \"Стандартен мащаб\",\n    \"menu\": \"Меню\",\n    \"done\": \"Завършено\",\n    \"edit\": \"Редактиране\",\n    \"undo\": \"Отмяна\",\n    \"redo\": \"Повтори\",\n    \"resetLibrary\": \"Нулиране на библиотеката\",\n    \"createNewRoom\": \"Създай нова стая\",\n    \"fullScreen\": \"На цял екран\",\n    \"darkMode\": \"Тъмен режим\",\n    \"lightMode\": \"Светъл режим\",\n    \"zenMode\": \"Режим Zen\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Спиране на Zen режим\",\n    \"cancel\": \"Отмени\",\n    \"clear\": \"Изчисти\",\n    \"remove\": \"Премахване\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Публикувай\",\n    \"submit\": \"Изпрати\",\n    \"confirm\": \"Потвърждаване\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Това ще изчисти цялото платно. Сигурни ли сте?\",\n    \"couldNotCreateShareableLink\": \"Връзката не може да бъде създадена.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Не може да се създаде връзка за споделяне: сцената е твърде голяма\",\n    \"couldNotLoadInvalidFile\": \"Невалиден файл не може да се зареди\",\n    \"importBackendFailed\": \"Импортирането от бекенд не беше успешно.\",\n    \"cannotExportEmptyCanvas\": \"Не може да се експортира празно платно.\",\n    \"couldNotCopyToClipboard\": \"Не можем да копираме в клипбоарда.\",\n    \"decryptFailed\": \"Данните не можаха да се дешифрират.\",\n    \"uploadedSecurly\": \"Качването е защитено с криптиране от край до край, което означава, че сървърът Excalidraw и трети страни не могат да четат съдържанието.\",\n    \"loadSceneOverridePrompt\": \"Зареждането на външна рисунка ще презапише настоящото ви съдържание. Желаете ли да продължите?\",\n    \"collabStopOverridePrompt\": \"Прекратяването на сесията ще презапише предишната, локално запазена, рисунка. Сигурни ли сте?\\n\\n(Ако искате да продължите с локалната рисунка, просто затворете таба на браузъра.)\",\n    \"errorAddingToLibrary\": \"Не можем да заредим от библиотеката\",\n    \"errorRemovingFromLibrary\": \"Не можем да премахнем елемент от библиотеката\",\n    \"confirmAddLibrary\": \"Ще се добавят {{numShapes}} фигура(и) във вашата библиотека. Сигурни ли сте?\",\n    \"imageDoesNotContainScene\": \"\",\n    \"cannotRestoreFromImage\": \"Не може да бъде възстановена сцена от този файл\",\n    \"invalidSceneUrl\": \"\",\n    \"resetLibrary\": \"\",\n    \"removeItemsFromsLibrary\": \"Изтрий {{count}} елемент(а) от библиотеката?\",\n    \"invalidEncryptionKey\": \"\",\n    \"collabOfflineWarning\": \"\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Този файлов формат не се поддържа.\",\n    \"imageInsertError\": \"\",\n    \"fileTooBig\": \"Файлът е твърде голям. Максималния допустим размер е {{maxSize}}.\",\n    \"svgImageInsertError\": \"\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"Невалиден SVG.\",\n    \"cannotResolveCollabServer\": \"\",\n    \"importLibraryError\": \"Не можем да заредим библиотеката\",\n    \"collabSaveFailed\": \"\",\n    \"collabSaveFailed_sizeExceeded\": \"\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"Силно препоръчваме да изключите тази настройка. Можете да следвате <link>тези стъпки</link> за това как да го направите.\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Селекция\",\n    \"image\": \"Вмъкване на изображение\",\n    \"rectangle\": \"Правоъгълник\",\n    \"diamond\": \"Диамант\",\n    \"ellipse\": \"Елипс\",\n    \"arrow\": \"Стрелка\",\n    \"line\": \"Линия\",\n    \"freedraw\": \"Рисуване\",\n    \"text\": \"Текст\",\n    \"library\": \"Библиотека\",\n    \"lock\": \"Поддържайте избрания инструмент активен след рисуване\",\n    \"penMode\": \"\",\n    \"link\": \"\",\n    \"eraser\": \"Гума\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"\",\n    \"extraTools\": \"Още инструменти\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Действия по платното\",\n    \"selectedShapeActions\": \"Избрани действия\",\n    \"shapes\": \"Фигури\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"\",\n    \"linearElement\": \"Кликнете, за да стартирате няколко точки, плъзнете за една линия\",\n    \"freeDraw\": \"Натиснете и влачете, пуснете като сте готови\",\n    \"text\": \"Подсказка: Можете също да добавите текст като натиснете някъде два път с инструмента за селекция\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"\",\n    \"text_editing\": \"\",\n    \"linearElementMulti\": \"Кликнете върху последната точка или натиснете Escape или Enter, за да завършите\",\n    \"lockAngle\": \"Можете да ограничите ъгъла, като задържите SHIFT\",\n    \"resize\": \"Може да ограничите при преоразмеряване като задържите SHIFT,\\nзадръжте ALT за преоразмерите през центъра\",\n    \"resizeImage\": \"\",\n    \"rotate\": \"Можете да ограничите ъглите, като държите SHIFT, докато се въртите\",\n    \"lineEditor_info\": \"\",\n    \"lineEditor_pointSelected\": \"Натиснете Delete за да изтриете точка(и), CtrlOrCmd+D за дуплициране, или извлачете за да преместите\",\n    \"lineEditor_nothingSelected\": \"\",\n    \"placeImage\": \"\",\n    \"publishLibrary\": \"\",\n    \"bindTextToElement\": \"Натиснете Enter, за да добавите\",\n    \"deepBoxSelect\": \"\",\n    \"eraserRevert\": \"\",\n    \"firefox_clipboard_write\": \"\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Невъзможност за показване на preview\",\n    \"canvasTooBig\": \"Платното е твърде голямо.\",\n    \"canvasTooBigTip\": \"Подсказка: пробвайте да приближите далечните елементи по-близко.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Среща грешка. Опитайте <button>презареждане на страницата.</button>\",\n    \"clearCanvasMessage\": \"Ако презареждането не работи, опитайте <button>изчистване на платното.</button>\",\n    \"clearCanvasCaveat\": \" Това ще доведе до загуба на работа \",\n    \"trackedToSentry\": \"Грешката с идентификатор {{eventId}} беше проследен в нашата система.\",\n    \"openIssueMessage\": \"Бяхме много предпазливи да не включите информацията за вашата сцена при грешката. Ако сцената ви не е частна, моля, помислете за последващи действия на нашата <button>тракер за грешки.</button> Моля, включете информация по-долу, като я копирате и добавите в GitHub.\",\n    \"sceneContent\": \"Съдържание на сцената:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Можете да поканите хора на текущата си сцена да си сътрудничат с вас.\",\n    \"desc_privacy\": \"Не се притеснявайте, сесията използва криптиране от край до край, така че каквото нарисувате ще остане частно. Дори нашият сървър няма да може да види какво предлагате.\",\n    \"button_startSession\": \"Стартирайте сесията\",\n    \"button_stopSession\": \"Стоп на сесията\",\n    \"desc_inProgressIntro\": \"Сесията за сътрудничество на живо е в ход.\",\n    \"desc_shareLink\": \"Споделете тази връзка с всеки, с когото искате да си сътрудничите:\",\n    \"desc_exitSession\": \"Спирането на сесията ще ви изключи от стаята, но ще можете да продължите да работите със сцената, локално. Имайте предвид, че това няма да засегне други хора и те все още ще могат да си сътрудничат с тяхната версия.\",\n    \"shareTitle\": \"\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Грешка\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"\",\n    \"disk_details\": \"\",\n    \"disk_button\": \"\",\n    \"link_title\": \"\",\n    \"link_details\": \"\",\n    \"link_button\": \"\",\n    \"excalidrawplus_description\": \"\",\n    \"excalidrawplus_button\": \"Експорт\",\n    \"excalidrawplus_exportError\": \"\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Прочетете нашия блог\",\n    \"click\": \"клик\",\n    \"deepSelect\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"curvedArrow\": \"Извита стрелка\",\n    \"curvedLine\": \"Извита линия\",\n    \"documentation\": \"Документация\",\n    \"doubleClick\": \"двойно-щракване\",\n    \"drag\": \"плъзнете\",\n    \"editor\": \"Редактор\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"Намерихте проблем? Изпратете\",\n    \"howto\": \"Следвайте нашите ръководства\",\n    \"or\": \"или\",\n    \"preventBinding\": \"Спри прилепяне на стрелките\",\n    \"tools\": \"Инструменти\",\n    \"shortcuts\": \"Клавиши за бърз достъп\",\n    \"textFinish\": \"Завърши редактиране (текстов редактор)\",\n    \"textNewLine\": \"Добави нова линия (текстов редактор)\",\n    \"title\": \"Помощ\",\n    \"view\": \"Преглед\",\n    \"zoomToFit\": \"Приближи докато се виждат всички елементи\",\n    \"zoomToSelection\": \"Приближи селекцията\",\n    \"toggleElementLock\": \"Заключи/Отключи селекция\",\n    \"movePageUpDown\": \"Премести страница нагоре/надолу\",\n    \"movePageLeftRight\": \"Премести страница наляво/надясно\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Изчисти платното\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Публикувай библиотека\",\n    \"itemName\": \"Име\",\n    \"authorName\": \"Авторско име\",\n    \"githubUsername\": \"GitHub потребителско име\",\n    \"twitterUsername\": \"Twitter потребителско име\",\n    \"libraryName\": \"Име на библиотеката\",\n    \"libraryDesc\": \"Описание на библиотеката\",\n    \"website\": \"Уебсайт\",\n    \"placeholder\": {\n      \"authorName\": \"Името или потребителското Ви име\",\n      \"libraryName\": \"Име на библиотеката Ви\",\n      \"libraryDesc\": \"Описание на библиотеката ви, за да помогнете на хората да разберат приложенията ѝ\",\n      \"githubHandle\": \"\",\n      \"twitterHandle\": \"\",\n      \"website\": \"\"\n    },\n    \"errors\": {\n      \"required\": \"Задължително\",\n      \"website\": \"Въведете валиден URL адрес\"\n    },\n    \"noteDescription\": \"\",\n    \"noteGuidelines\": \"\",\n    \"noteLicense\": \"\",\n    \"noteItems\": \"\",\n    \"atleastOneLibItem\": \"\",\n    \"republishWarning\": \"\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"\",\n    \"content\": \"\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Нулирай библиотека\",\n    \"removeItemsFromLib\": \"\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"Фон\",\n      \"onlySelected\": \"Само избраното\",\n      \"darkMode\": \"Тъмен режим\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Изнасяне в PNG\",\n      \"exportToSvg\": \"Изнасяне в SVG\",\n      \"copyPngToClipboard\": \"Копирай PNG в клипборда\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Копиране в клипборда\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Вашите рисунки са криптирани от край до край, така че сървърите на Excalidraw няма да могат да ги виждат.\",\n    \"link\": \"\"\n  },\n  \"stats\": {\n    \"angle\": \"Ъгъл\",\n    \"element\": \"Елемент\",\n    \"elements\": \"Елементи\",\n    \"height\": \"Височина\",\n    \"scene\": \"Сцена\",\n    \"selected\": \"Селектирано\",\n    \"storage\": \"Съхранение на данни\",\n    \"title\": \"Статистика за хакери\",\n    \"total\": \"Общо\",\n    \"version\": \"Версия\",\n    \"versionCopy\": \"Настисни за да копираш\",\n    \"versionNotAvailable\": \"Версията не е налична\",\n    \"width\": \"Широчина\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Добавена към библиотеката\",\n    \"copyStyles\": \"Копирани стилове.\",\n    \"copyToClipboard\": \"Копирано в клипборда.\",\n    \"copyToClipboardAsPng\": \"Копира {{exportSelection}} в клипборда като PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Файлът е запазен.\",\n    \"fileSavedToFilename\": \"Запазен към {filename}\",\n    \"canvas\": \"платно\",\n    \"selection\": \"селекция\",\n    \"pasteAsSingleElement\": \"\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Прозрачен\",\n    \"black\": \"Черен\",\n    \"white\": \"Бял\",\n    \"red\": \"Червен\",\n    \"pink\": \"Розов\",\n    \"grape\": \"Грозде\",\n    \"violet\": \"Виолетово\",\n    \"gray\": \"Сив\",\n    \"blue\": \"Син\",\n    \"cyan\": \"Синьозелено\",\n    \"teal\": \"Тъмно синьо-зелено\",\n    \"green\": \"Зелено\",\n    \"yellow\": \"Жълто\",\n    \"orange\": \"Оранжево\",\n    \"bronze\": \"Бронзово\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Всичките Ви данни са запазени локално в браузъра Ви.\",\n      \"center_heading_plus\": \"\",\n      \"menuHint\": \"Експорт, предпочитания, езици, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Експорт, предпочитания, и още...\",\n      \"center_heading\": \"Диаграми. Направени. Просто.\",\n      \"toolbarHint\": \"Изберете инструмент & Започнете да рисувате!\",\n      \"helpHint\": \"Преки пътища & помощ\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Най-често използвани цветове\",\n    \"colors\": \"Цветове\",\n    \"shades\": \"Нюанси\",\n    \"hexCode\": \"Шестнадесетичен код\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Изнеси като изображение\",\n        \"button\": \"Изнеси като изображение\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Запази към диск\",\n        \"button\": \"Запази към диск\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Експортирай към Excalidraw+\",\n        \"description\": \"Запази сцената към Excalidraw+ работното място.\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Зареди от файл\",\n        \"button\": \"Зареди от файл\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Зареди от линк\",\n        \"button\": \"Замени моето съдържание\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}