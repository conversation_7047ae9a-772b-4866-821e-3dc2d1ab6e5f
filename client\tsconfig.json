{
  "compilerOptions": {
    "target": "ES2017", // Specifies the ECMAScript target version. ES2017 includes async/await support.
    "lib": [
      "dom", // Includes DOM APIs for browser environments.
      "dom.iterable", // Adds support for iterable DOM collections like NodeList.
      "esnext" // Includes the latest ECMAScript features.
    ],
    "allowJs": true, // Allows JavaScript files to be compiled.
    "skipLibCheck": true, // Skips type checking of declaration files for faster builds.
    "strict": true, // Enables all strict type-checking options.
    "noEmit": true, // Prevents emitting compiled files. Useful for type-checking only.
    "esModuleInterop": true, // Enables compatibility with CommonJS and ES Modules.
    "module": "esnext", // Specifies the module code generation method. "esnext" uses the latest module standard.
    "moduleResolution": "bundler", // Resolves modules in a way optimized for bundlers like Webpack.
    "resolveJsonModule": true, // Allows importing JSON files as modules.
    "isolatedModules": true, // Ensures each file can be safely transpiled without relying on other files.
    "jsx": "preserve", // Keeps JSX as-is for further processing by a bundler or framework.
    "incremental": true, // Enables incremental compilation for faster subsequent builds.
    "plugins": [
      {
        "name": "next" // Adds Next.js-specific TypeScript support.
      }
    ],
    "paths": {
      "@/*": [
        "./src/*" // Maps "@/" to the "src" directory for cleaner imports.
      ]
    }
  },
  "include": [
    "next-env.d.ts", // Includes Next.js environment type definitions.
    "**/*.ts", // Includes all TypeScript files in the project.
    "**/*.tsx", // Includes all TypeScript files with JSX syntax.
    ".next/types/**/*.ts" // Includes type definitions generated by Next.js.
  ],
  "exclude": [
    "node_modules" // Excludes the "node_modules" directory from type-checking.
  ]
}
