"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_wgsl_wgsl_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/wgsl/wgsl.js":
/*!*************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/wgsl/wgsl.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/wgsl/wgsl.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" }\n  ]\n};\nfunction qw(str) {\n  let result = [];\n  const words = str.split(/\\t+|\\r+|\\n+| +/);\n  for (let i = 0; i < words.length; ++i) {\n    if (words[i].length > 0) {\n      result.push(words[i]);\n    }\n  }\n  return result;\n}\nvar atoms = qw(\"true false\");\nvar keywords = qw(`\n\t\t\t  alias\n\t\t\t  break\n\t\t\t  case\n\t\t\t  const\n\t\t\t  const_assert\n\t\t\t  continue\n\t\t\t  continuing\n\t\t\t  default\n\t\t\t  diagnostic\n\t\t\t  discard\n\t\t\t  else\n\t\t\t  enable\n\t\t\t  fn\n\t\t\t  for\n\t\t\t  if\n\t\t\t  let\n\t\t\t  loop\n\t\t\t  override\n\t\t\t  requires\n\t\t\t  return\n\t\t\t  struct\n\t\t\t  switch\n\t\t\t  var\n\t\t\t  while\n\t\t\t  `);\nvar reserved = qw(`\n\t\t\t  NULL\n\t\t\t  Self\n\t\t\t  abstract\n\t\t\t  active\n\t\t\t  alignas\n\t\t\t  alignof\n\t\t\t  as\n\t\t\t  asm\n\t\t\t  asm_fragment\n\t\t\t  async\n\t\t\t  attribute\n\t\t\t  auto\n\t\t\t  await\n\t\t\t  become\n\t\t\t  binding_array\n\t\t\t  cast\n\t\t\t  catch\n\t\t\t  class\n\t\t\t  co_await\n\t\t\t  co_return\n\t\t\t  co_yield\n\t\t\t  coherent\n\t\t\t  column_major\n\t\t\t  common\n\t\t\t  compile\n\t\t\t  compile_fragment\n\t\t\t  concept\n\t\t\t  const_cast\n\t\t\t  consteval\n\t\t\t  constexpr\n\t\t\t  constinit\n\t\t\t  crate\n\t\t\t  debugger\n\t\t\t  decltype\n\t\t\t  delete\n\t\t\t  demote\n\t\t\t  demote_to_helper\n\t\t\t  do\n\t\t\t  dynamic_cast\n\t\t\t  enum\n\t\t\t  explicit\n\t\t\t  export\n\t\t\t  extends\n\t\t\t  extern\n\t\t\t  external\n\t\t\t  fallthrough\n\t\t\t  filter\n\t\t\t  final\n\t\t\t  finally\n\t\t\t  friend\n\t\t\t  from\n\t\t\t  fxgroup\n\t\t\t  get\n\t\t\t  goto\n\t\t\t  groupshared\n\t\t\t  highp\n\t\t\t  impl\n\t\t\t  implements\n\t\t\t  import\n\t\t\t  inline\n\t\t\t  instanceof\n\t\t\t  interface\n\t\t\t  layout\n\t\t\t  lowp\n\t\t\t  macro\n\t\t\t  macro_rules\n\t\t\t  match\n\t\t\t  mediump\n\t\t\t  meta\n\t\t\t  mod\n\t\t\t  module\n\t\t\t  move\n\t\t\t  mut\n\t\t\t  mutable\n\t\t\t  namespace\n\t\t\t  new\n\t\t\t  nil\n\t\t\t  noexcept\n\t\t\t  noinline\n\t\t\t  nointerpolation\n\t\t\t  noperspective\n\t\t\t  null\n\t\t\t  nullptr\n\t\t\t  of\n\t\t\t  operator\n\t\t\t  package\n\t\t\t  packoffset\n\t\t\t  partition\n\t\t\t  pass\n\t\t\t  patch\n\t\t\t  pixelfragment\n\t\t\t  precise\n\t\t\t  precision\n\t\t\t  premerge\n\t\t\t  priv\n\t\t\t  protected\n\t\t\t  pub\n\t\t\t  public\n\t\t\t  readonly\n\t\t\t  ref\n\t\t\t  regardless\n\t\t\t  register\n\t\t\t  reinterpret_cast\n\t\t\t  require\n\t\t\t  resource\n\t\t\t  restrict\n\t\t\t  self\n\t\t\t  set\n\t\t\t  shared\n\t\t\t  sizeof\n\t\t\t  smooth\n\t\t\t  snorm\n\t\t\t  static\n\t\t\t  static_assert\n\t\t\t  static_cast\n\t\t\t  std\n\t\t\t  subroutine\n\t\t\t  super\n\t\t\t  target\n\t\t\t  template\n\t\t\t  this\n\t\t\t  thread_local\n\t\t\t  throw\n\t\t\t  trait\n\t\t\t  try\n\t\t\t  type\n\t\t\t  typedef\n\t\t\t  typeid\n\t\t\t  typename\n\t\t\t  typeof\n\t\t\t  union\n\t\t\t  unless\n\t\t\t  unorm\n\t\t\t  unsafe\n\t\t\t  unsized\n\t\t\t  use\n\t\t\t  using\n\t\t\t  varying\n\t\t\t  virtual\n\t\t\t  volatile\n\t\t\t  wgsl\n\t\t\t  where\n\t\t\t  with\n\t\t\t  writeonly\n\t\t\t  yield\n\t\t\t  `);\nvar predeclared_enums = qw(`\n\t\tread write read_write\n\t\tfunction private workgroup uniform storage\n\t\tperspective linear flat\n\t\tcenter centroid sample\n\t\tvertex_index instance_index position front_facing frag_depth\n\t\t\tlocal_invocation_id local_invocation_index\n\t\t\tglobal_invocation_id workgroup_id num_workgroups\n\t\t\tsample_index sample_mask\n\t\trgba8unorm\n\t\trgba8snorm\n\t\trgba8uint\n\t\trgba8sint\n\t\trgba16uint\n\t\trgba16sint\n\t\trgba16float\n\t\tr32uint\n\t\tr32sint\n\t\tr32float\n\t\trg32uint\n\t\trg32sint\n\t\trg32float\n\t\trgba32uint\n\t\trgba32sint\n\t\trgba32float\n\t\tbgra8unorm\n`);\nvar predeclared_types = qw(`\n\t\tbool\n\t\tf16\n\t\tf32\n\t\ti32\n\t\tsampler sampler_comparison\n\t\ttexture_depth_2d\n\t\ttexture_depth_2d_array\n\t\ttexture_depth_cube\n\t\ttexture_depth_cube_array\n\t\ttexture_depth_multisampled_2d\n\t\ttexture_external\n\t\ttexture_external\n\t\tu32\n\t\t`);\nvar predeclared_type_generators = qw(`\n\t\tarray\n\t\tatomic\n\t\tmat2x2\n\t\tmat2x3\n\t\tmat2x4\n\t\tmat3x2\n\t\tmat3x3\n\t\tmat3x4\n\t\tmat4x2\n\t\tmat4x3\n\t\tmat4x4\n\t\tptr\n\t\ttexture_1d\n\t\ttexture_2d\n\t\ttexture_2d_array\n\t\ttexture_3d\n\t\ttexture_cube\n\t\ttexture_cube_array\n\t\ttexture_multisampled_2d\n\t\ttexture_storage_1d\n\t\ttexture_storage_2d\n\t\ttexture_storage_2d_array\n\t\ttexture_storage_3d\n\t\tvec2\n\t\tvec3\n\t\tvec4\n\t\t`);\nvar predeclared_type_aliases = qw(`\n\t\tvec2i vec3i vec4i\n\t\tvec2u vec3u vec4u\n\t\tvec2f vec3f vec4f\n\t\tvec2h vec3h vec4h\n\t\tmat2x2f mat2x3f mat2x4f\n\t\tmat3x2f mat3x3f mat3x4f\n\t\tmat4x2f mat4x3f mat4x4f\n\t\tmat2x2h mat2x3h mat2x4h\n\t\tmat3x2h mat3x3h mat3x4h\n\t\tmat4x2h mat4x3h mat4x4h\n\t\t`);\nvar predeclared_intrinsics = qw(`\n  bitcast all any select arrayLength abs acos acosh asin asinh atan atanh atan2\n  ceil clamp cos cosh countLeadingZeros countOneBits countTrailingZeros cross\n  degrees determinant distance dot exp exp2 extractBits faceForward firstLeadingBit\n  firstTrailingBit floor fma fract frexp inverseBits inverseSqrt ldexp length\n  log log2 max min mix modf normalize pow quantizeToF16 radians reflect refract\n  reverseBits round saturate sign sin sinh smoothstep sqrt step tan tanh transpose\n  trunc dpdx dpdxCoarse dpdxFine dpdy dpdyCoarse dpdyFine fwidth fwidthCoarse fwidthFine\n  textureDimensions textureGather textureGatherCompare textureLoad textureNumLayers\n  textureNumLevels textureNumSamples textureSample textureSampleBias textureSampleCompare\n  textureSampleCompareLevel textureSampleGrad textureSampleLevel textureSampleBaseClampToEdge\n  textureStore atomicLoad atomicStore atomicAdd atomicSub atomicMax atomicMin\n  atomicAnd atomicOr atomicXor atomicExchange atomicCompareExchangeWeak pack4x8snorm\n  pack4x8unorm pack2x16snorm pack2x16unorm pack2x16float unpack4x8snorm unpack4x8unorm\n  unpack2x16snorm unpack2x16unorm unpack2x16float storageBarrier workgroupBarrier\n  workgroupUniformLoad\n`);\nvar operators = qw(`\n\t\t\t\t\t &\n\t\t\t\t\t &&\n\t\t\t\t\t ->\n\t\t\t\t\t /\n\t\t\t\t\t =\n\t\t\t\t\t ==\n\t\t\t\t\t !=\n\t\t\t\t\t >\n\t\t\t\t\t >=\n\t\t\t\t\t <\n\t\t\t\t\t <=\n\t\t\t\t\t %\n\t\t\t\t\t -\n\t\t\t\t\t --\n\t\t\t\t\t +\n\t\t\t\t\t ++\n\t\t\t\t\t |\n\t\t\t\t\t ||\n\t\t\t\t\t *\n\t\t\t\t\t <<\n\t\t\t\t\t >>\n\t\t\t\t\t +=\n\t\t\t\t\t -=\n\t\t\t\t\t *=\n\t\t\t\t\t /=\n\t\t\t\t\t %=\n\t\t\t\t\t &=\n\t\t\t\t\t |=\n\t\t\t\t\t ^=\n\t\t\t\t\t >>=\n\t\t\t\t\t <<=\n\t\t\t\t\t `);\nvar directive_re = /enable|requires|diagnostic/;\nvar ident_re = /[_\\p{XID_Start}]\\p{XID_Continue}*/u;\nvar predefined_token = \"variable.predefined\";\nvar language = {\n  tokenPostfix: \".wgsl\",\n  defaultToken: \"invalid\",\n  unicode: true,\n  atoms,\n  keywords,\n  reserved,\n  predeclared_enums,\n  predeclared_types,\n  predeclared_type_generators,\n  predeclared_type_aliases,\n  predeclared_intrinsics,\n  operators,\n  symbols: /[!%&*+\\-\\.\\/:;<=>^|_~,]+/,\n  tokenizer: {\n    root: [\n      [directive_re, \"keyword\", \"@directive\"],\n      [\n        // Identifier-like things, but also include '_'\n        ident_re,\n        {\n          cases: {\n            \"@atoms\": predefined_token,\n            \"@keywords\": \"keyword\",\n            \"@reserved\": \"invalid\",\n            \"@predeclared_enums\": predefined_token,\n            \"@predeclared_types\": predefined_token,\n            \"@predeclared_type_generators\": predefined_token,\n            \"@predeclared_type_aliases\": predefined_token,\n            \"@predeclared_intrinsics\": predefined_token,\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@commentOrSpace\" },\n      { include: \"@numbers\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [\"@\", \"annotation\", \"@attribute\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@default\": \"delimiter\"\n          }\n        }\n      ],\n      [/./, \"invalid\"]\n    ],\n    commentOrSpace: [\n      [/\\s+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@blockComment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    blockComment: [\n      // Soak up uninteresting text: anything except * or /\n      [/[^\\/*]+/, \"comment\"],\n      // Recognize the start of a nested block comment.\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // Recognize the end of a nested block comment.\n      [/\\*\\//, \"comment\", \"@pop\"],\n      // Recognize insignificant * and /\n      [/[\\/*]/, \"comment\"]\n    ],\n    attribute: [\n      // For things like '@fragment' both '@' and 'fragment'\n      // are marked as annotations.  This should work even if\n      // there are spaces or comments between the two tokens.\n      { include: \"@commentOrSpace\" },\n      [/\\w+/, \"annotation\", \"@pop\"]\n    ],\n    directive: [\n      // For things like 'enable f16;', 'enable' maps to 'meta'\n      // and 'f16' maps to 'meta.tag'.\n      { include: \"@commentOrSpace\" },\n      [/[()]/, \"@brackets\"],\n      [/,/, \"delimiter\"],\n      [ident_re, \"meta.content\"],\n      [/;/, \"delimiter\", \"@pop\"]\n    ],\n    numbers: [\n      // Decimal float literals\n      // https://www.w3.org/TR/WGSL/#syntax-decimal_float_literal\n      // 0, with type-specifying suffix.\n      [/0[fh]/, \"number.float\"],\n      // Other decimal integer, with type-specifying suffix.\n      [/[1-9][0-9]*[fh]/, \"number.float\"],\n      // Has decimal point, at least one digit after decimal.\n      [/[0-9]*\\.[0-9]+([eE][+-]?[0-9]+)?[fh]?/, \"number.float\"],\n      // Has decimal point, at least one digit before decimal.\n      [/[0-9]+\\.[0-9]*([eE][+-]?[0-9]+)?[fh]?/, \"number.float\"],\n      // Has at least one digit, and has an exponent.\n      [/[0-9]+[eE][+-]?[0-9]+[fh]?/, \"number.float\"],\n      // Hex float literals\n      // https://www.w3.org/TR/WGSL/#syntax-hex_float_literal\n      [/0[xX][0-9a-fA-F]*\\.[0-9a-fA-F]+(?:[pP][+-]?[0-9]+[fh]?)?/, \"number.hex\"],\n      [/0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*(?:[pP][+-]?[0-9]+[fh]?)?/, \"number.hex\"],\n      [/0[xX][0-9a-fA-F]+[pP][+-]?[0-9]+[fh]?/, \"number.hex\"],\n      // Hexadecimal integer literals\n      // https://www.w3.org/TR/WGSL/#syntax-hex_int_literal\n      [/0[xX][0-9a-fA-F]+[iu]?/, \"number.hex\"],\n      // Decimal integer literals\n      // https://www.w3.org/TR/WGSL/#syntax-decimal_int_literal\n      // We need two rules here because 01 is not valid.\n      [/[1-9][0-9]*[iu]?/, \"number\"],\n      [/0[iu]?/, \"number\"]\n      // Must match last\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/wgsl/wgsl.js\n"));

/***/ })

}]);