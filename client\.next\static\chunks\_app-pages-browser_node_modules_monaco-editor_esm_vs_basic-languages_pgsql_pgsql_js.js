"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_pgsql_pgsql_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/pgsql/pgsql.js":
/*!***************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/pgsql/pgsql.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/pgsql/pgsql.ts\nvar conf = {\n  comments: {\n    lineComment: \"--\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sql\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    // This list is generated using `keywords.js`\n    \"ALL\",\n    \"ANALYSE\",\n    \"ANALYZE\",\n    \"AND\",\n    \"ANY\",\n    \"ARRAY\",\n    \"AS\",\n    \"ASC\",\n    \"ASYMMETRIC\",\n    \"AUTHORIZATION\",\n    \"BINARY\",\n    \"BOTH\",\n    \"CASE\",\n    \"CAST\",\n    \"CHECK\",\n    \"COLLATE\",\n    \"COLLATION\",\n    \"COLUMN\",\n    \"CONCURRENTLY\",\n    \"CONSTRAINT\",\n    \"CREATE\",\n    \"CROSS\",\n    \"CURRENT_CATALOG\",\n    \"CURRENT_DATE\",\n    \"CURRENT_ROLE\",\n    \"CURRENT_SCHEMA\",\n    \"CURRENT_TIME\",\n    \"CURRENT_TIMESTAMP\",\n    \"CURRENT_USER\",\n    \"DEFAULT\",\n    \"DEFERRABLE\",\n    \"DESC\",\n    \"DISTINCT\",\n    \"DO\",\n    \"ELSE\",\n    \"END\",\n    \"EXCEPT\",\n    \"FALSE\",\n    \"FETCH\",\n    \"FOR\",\n    \"FOREIGN\",\n    \"FREEZE\",\n    \"FROM\",\n    \"FULL\",\n    \"GRANT\",\n    \"GROUP\",\n    \"HAVING\",\n    \"ILIKE\",\n    \"IN\",\n    \"INITIALLY\",\n    \"INNER\",\n    \"INTERSECT\",\n    \"INTO\",\n    \"IS\",\n    \"ISNULL\",\n    \"JOIN\",\n    \"LATERAL\",\n    \"LEADING\",\n    \"LEFT\",\n    \"LIKE\",\n    \"LIMIT\",\n    \"LOCALTIME\",\n    \"LOCALTIMESTAMP\",\n    \"NATURAL\",\n    \"NOT\",\n    \"NOTNULL\",\n    \"NULL\",\n    \"OFFSET\",\n    \"ON\",\n    \"ONLY\",\n    \"OR\",\n    \"ORDER\",\n    \"OUTER\",\n    \"OVERLAPS\",\n    \"PLACING\",\n    \"PRIMARY\",\n    \"REFERENCES\",\n    \"RETURNING\",\n    \"RIGHT\",\n    \"SELECT\",\n    \"SESSION_USER\",\n    \"SIMILAR\",\n    \"SOME\",\n    \"SYMMETRIC\",\n    \"TABLE\",\n    \"TABLESAMPLE\",\n    \"THEN\",\n    \"TO\",\n    \"TRAILING\",\n    \"TRUE\",\n    \"UNION\",\n    \"UNIQUE\",\n    \"USER\",\n    \"USING\",\n    \"VARIADIC\",\n    \"VERBOSE\",\n    \"WHEN\",\n    \"WHERE\",\n    \"WINDOW\",\n    \"WITH\"\n  ],\n  operators: [\n    \"AND\",\n    \"BETWEEN\",\n    \"IN\",\n    \"LIKE\",\n    \"NOT\",\n    \"OR\",\n    \"IS\",\n    \"NULL\",\n    \"INTERSECT\",\n    \"UNION\",\n    \"INNER\",\n    \"JOIN\",\n    \"LEFT\",\n    \"OUTER\",\n    \"RIGHT\"\n  ],\n  builtinFunctions: [\n    \"abbrev\",\n    \"abs\",\n    \"acldefault\",\n    \"aclexplode\",\n    \"acos\",\n    \"acosd\",\n    \"acosh\",\n    \"age\",\n    \"any\",\n    \"area\",\n    \"array_agg\",\n    \"array_append\",\n    \"array_cat\",\n    \"array_dims\",\n    \"array_fill\",\n    \"array_length\",\n    \"array_lower\",\n    \"array_ndims\",\n    \"array_position\",\n    \"array_positions\",\n    \"array_prepend\",\n    \"array_remove\",\n    \"array_replace\",\n    \"array_to_json\",\n    \"array_to_string\",\n    \"array_to_tsvector\",\n    \"array_upper\",\n    \"ascii\",\n    \"asin\",\n    \"asind\",\n    \"asinh\",\n    \"atan\",\n    \"atan2\",\n    \"atan2d\",\n    \"atand\",\n    \"atanh\",\n    \"avg\",\n    \"bit\",\n    \"bit_and\",\n    \"bit_count\",\n    \"bit_length\",\n    \"bit_or\",\n    \"bit_xor\",\n    \"bool_and\",\n    \"bool_or\",\n    \"bound_box\",\n    \"box\",\n    \"brin_desummarize_range\",\n    \"brin_summarize_new_values\",\n    \"brin_summarize_range\",\n    \"broadcast\",\n    \"btrim\",\n    \"cardinality\",\n    \"cbrt\",\n    \"ceil\",\n    \"ceiling\",\n    \"center\",\n    \"char_length\",\n    \"character_length\",\n    \"chr\",\n    \"circle\",\n    \"clock_timestamp\",\n    \"coalesce\",\n    \"col_description\",\n    \"concat\",\n    \"concat_ws\",\n    \"convert\",\n    \"convert_from\",\n    \"convert_to\",\n    \"corr\",\n    \"cos\",\n    \"cosd\",\n    \"cosh\",\n    \"cot\",\n    \"cotd\",\n    \"count\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"cume_dist\",\n    \"current_catalog\",\n    \"current_database\",\n    \"current_date\",\n    \"current_query\",\n    \"current_role\",\n    \"current_schema\",\n    \"current_schemas\",\n    \"current_setting\",\n    \"current_time\",\n    \"current_timestamp\",\n    \"current_user\",\n    \"currval\",\n    \"cursor_to_xml\",\n    \"cursor_to_xmlschema\",\n    \"date_bin\",\n    \"date_part\",\n    \"date_trunc\",\n    \"database_to_xml\",\n    \"database_to_xml_and_xmlschema\",\n    \"database_to_xmlschema\",\n    \"decode\",\n    \"degrees\",\n    \"dense_rank\",\n    \"diagonal\",\n    \"diameter\",\n    \"div\",\n    \"encode\",\n    \"enum_first\",\n    \"enum_last\",\n    \"enum_range\",\n    \"every\",\n    \"exp\",\n    \"extract\",\n    \"factorial\",\n    \"family\",\n    \"first_value\",\n    \"floor\",\n    \"format\",\n    \"format_type\",\n    \"gcd\",\n    \"gen_random_uuid\",\n    \"generate_series\",\n    \"generate_subscripts\",\n    \"get_bit\",\n    \"get_byte\",\n    \"get_current_ts_config\",\n    \"gin_clean_pending_list\",\n    \"greatest\",\n    \"grouping\",\n    \"has_any_column_privilege\",\n    \"has_column_privilege\",\n    \"has_database_privilege\",\n    \"has_foreign_data_wrapper_privilege\",\n    \"has_function_privilege\",\n    \"has_language_privilege\",\n    \"has_schema_privilege\",\n    \"has_sequence_privilege\",\n    \"has_server_privilege\",\n    \"has_table_privilege\",\n    \"has_tablespace_privilege\",\n    \"has_type_privilege\",\n    \"height\",\n    \"host\",\n    \"hostmask\",\n    \"inet_client_addr\",\n    \"inet_client_port\",\n    \"inet_merge\",\n    \"inet_same_family\",\n    \"inet_server_addr\",\n    \"inet_server_port\",\n    \"initcap\",\n    \"isclosed\",\n    \"isempty\",\n    \"isfinite\",\n    \"isopen\",\n    \"json_agg\",\n    \"json_array_elements\",\n    \"json_array_elements_text\",\n    \"json_array_length\",\n    \"json_build_array\",\n    \"json_build_object\",\n    \"json_each\",\n    \"json_each_text\",\n    \"json_extract_path\",\n    \"json_extract_path_text\",\n    \"json_object\",\n    \"json_object_agg\",\n    \"json_object_keys\",\n    \"json_populate_record\",\n    \"json_populate_recordset\",\n    \"json_strip_nulls\",\n    \"json_to_record\",\n    \"json_to_recordset\",\n    \"json_to_tsvector\",\n    \"json_typeof\",\n    \"jsonb_agg\",\n    \"jsonb_array_elements\",\n    \"jsonb_array_elements_text\",\n    \"jsonb_array_length\",\n    \"jsonb_build_array\",\n    \"jsonb_build_object\",\n    \"jsonb_each\",\n    \"jsonb_each_text\",\n    \"jsonb_extract_path\",\n    \"jsonb_extract_path_text\",\n    \"jsonb_insert\",\n    \"jsonb_object\",\n    \"jsonb_object_agg\",\n    \"jsonb_object_keys\",\n    \"jsonb_path_exists\",\n    \"jsonb_path_match\",\n    \"jsonb_path_query\",\n    \"jsonb_path_query_array\",\n    \"jsonb_path_exists_tz\",\n    \"jsonb_path_query_first\",\n    \"jsonb_path_query_array_tz\",\n    \"jsonb_path_query_first_tz\",\n    \"jsonb_path_query_tz\",\n    \"jsonb_path_match_tz\",\n    \"jsonb_populate_record\",\n    \"jsonb_populate_recordset\",\n    \"jsonb_pretty\",\n    \"jsonb_set\",\n    \"jsonb_set_lax\",\n    \"jsonb_strip_nulls\",\n    \"jsonb_to_record\",\n    \"jsonb_to_recordset\",\n    \"jsonb_to_tsvector\",\n    \"jsonb_typeof\",\n    \"justify_days\",\n    \"justify_hours\",\n    \"justify_interval\",\n    \"lag\",\n    \"last_value\",\n    \"lastval\",\n    \"lcm\",\n    \"lead\",\n    \"least\",\n    \"left\",\n    \"length\",\n    \"line\",\n    \"ln\",\n    \"localtime\",\n    \"localtimestamp\",\n    \"log\",\n    \"log10\",\n    \"lower\",\n    \"lower_inc\",\n    \"lower_inf\",\n    \"lpad\",\n    \"lseg\",\n    \"ltrim\",\n    \"macaddr8_set7bit\",\n    \"make_date\",\n    \"make_interval\",\n    \"make_time\",\n    \"make_timestamp\",\n    \"make_timestamptz\",\n    \"makeaclitem\",\n    \"masklen\",\n    \"max\",\n    \"md5\",\n    \"min\",\n    \"min_scale\",\n    \"mod\",\n    \"mode\",\n    \"multirange\",\n    \"netmask\",\n    \"network\",\n    \"nextval\",\n    \"normalize\",\n    \"now\",\n    \"npoints\",\n    \"nth_value\",\n    \"ntile\",\n    \"nullif\",\n    \"num_nonnulls\",\n    \"num_nulls\",\n    \"numnode\",\n    \"obj_description\",\n    \"octet_length\",\n    \"overlay\",\n    \"parse_ident\",\n    \"path\",\n    \"pclose\",\n    \"percent_rank\",\n    \"percentile_cont\",\n    \"percentile_disc\",\n    \"pg_advisory_lock\",\n    \"pg_advisory_lock_shared\",\n    \"pg_advisory_unlock\",\n    \"pg_advisory_unlock_all\",\n    \"pg_advisory_unlock_shared\",\n    \"pg_advisory_xact_lock\",\n    \"pg_advisory_xact_lock_shared\",\n    \"pg_backend_pid\",\n    \"pg_backup_start_time\",\n    \"pg_blocking_pids\",\n    \"pg_cancel_backend\",\n    \"pg_client_encoding\",\n    \"pg_collation_actual_version\",\n    \"pg_collation_is_visible\",\n    \"pg_column_compression\",\n    \"pg_column_size\",\n    \"pg_conf_load_time\",\n    \"pg_control_checkpoint\",\n    \"pg_control_init\",\n    \"pg_control_recovery\",\n    \"pg_control_system\",\n    \"pg_conversion_is_visible\",\n    \"pg_copy_logical_replication_slot\",\n    \"pg_copy_physical_replication_slot\",\n    \"pg_create_logical_replication_slot\",\n    \"pg_create_physical_replication_slot\",\n    \"pg_create_restore_point\",\n    \"pg_current_logfile\",\n    \"pg_current_snapshot\",\n    \"pg_current_wal_flush_lsn\",\n    \"pg_current_wal_insert_lsn\",\n    \"pg_current_wal_lsn\",\n    \"pg_current_xact_id\",\n    \"pg_current_xact_id_if_assigned\",\n    \"pg_current_xlog_flush_location\",\n    \"pg_current_xlog_insert_location\",\n    \"pg_current_xlog_location\",\n    \"pg_database_size\",\n    \"pg_describe_object\",\n    \"pg_drop_replication_slot\",\n    \"pg_event_trigger_ddl_commands\",\n    \"pg_event_trigger_dropped_objects\",\n    \"pg_event_trigger_table_rewrite_oid\",\n    \"pg_event_trigger_table_rewrite_reason\",\n    \"pg_export_snapshot\",\n    \"pg_filenode_relation\",\n    \"pg_function_is_visible\",\n    \"pg_get_catalog_foreign_keys\",\n    \"pg_get_constraintdef\",\n    \"pg_get_expr\",\n    \"pg_get_function_arguments\",\n    \"pg_get_function_identity_arguments\",\n    \"pg_get_function_result\",\n    \"pg_get_functiondef\",\n    \"pg_get_indexdef\",\n    \"pg_get_keywords\",\n    \"pg_get_object_address\",\n    \"pg_get_owned_sequence\",\n    \"pg_get_ruledef\",\n    \"pg_get_serial_sequence\",\n    \"pg_get_statisticsobjdef\",\n    \"pg_get_triggerdef\",\n    \"pg_get_userbyid\",\n    \"pg_get_viewdef\",\n    \"pg_get_wal_replay_pause_state\",\n    \"pg_has_role\",\n    \"pg_identify_object\",\n    \"pg_identify_object_as_address\",\n    \"pg_import_system_collations\",\n    \"pg_index_column_has_property\",\n    \"pg_index_has_property\",\n    \"pg_indexam_has_property\",\n    \"pg_indexes_size\",\n    \"pg_is_in_backup\",\n    \"pg_is_in_recovery\",\n    \"pg_is_other_temp_schema\",\n    \"pg_is_wal_replay_paused\",\n    \"pg_is_xlog_replay_paused\",\n    \"pg_jit_available\",\n    \"pg_last_committed_xact\",\n    \"pg_last_wal_receive_lsn\",\n    \"pg_last_wal_replay_lsn\",\n    \"pg_last_xact_replay_timestamp\",\n    \"pg_last_xlog_receive_location\",\n    \"pg_last_xlog_replay_location\",\n    \"pg_listening_channels\",\n    \"pg_log_backend_memory_contexts\",\n    \"pg_logical_emit_message\",\n    \"pg_logical_slot_get_binary_changes\",\n    \"pg_logical_slot_get_changes\",\n    \"pg_logical_slot_peek_binary_changes\",\n    \"pg_logical_slot_peek_changes\",\n    \"pg_ls_archive_statusdir\",\n    \"pg_ls_dir\",\n    \"pg_ls_logdir\",\n    \"pg_ls_tmpdir\",\n    \"pg_ls_waldir\",\n    \"pg_mcv_list_items\",\n    \"pg_my_temp_schema\",\n    \"pg_notification_queue_usage\",\n    \"pg_opclass_is_visible\",\n    \"pg_operator_is_visible\",\n    \"pg_opfamily_is_visible\",\n    \"pg_options_to_table\",\n    \"pg_partition_ancestors\",\n    \"pg_partition_root\",\n    \"pg_partition_tree\",\n    \"pg_postmaster_start_time\",\n    \"pg_promote\",\n    \"pg_read_binary_file\",\n    \"pg_read_file\",\n    \"pg_relation_filenode\",\n    \"pg_relation_filepath\",\n    \"pg_relation_size\",\n    \"pg_reload_conf\",\n    \"pg_replication_origin_advance\",\n    \"pg_replication_origin_create\",\n    \"pg_replication_origin_drop\",\n    \"pg_replication_origin_oid\",\n    \"pg_replication_origin_progress\",\n    \"pg_replication_origin_session_is_setup\",\n    \"pg_replication_origin_session_progress\",\n    \"pg_replication_origin_session_reset\",\n    \"pg_replication_origin_session_setup\",\n    \"pg_replication_origin_xact_reset\",\n    \"pg_replication_origin_xact_setup\",\n    \"pg_replication_slot_advance\",\n    \"pg_rotate_logfile\",\n    \"pg_safe_snapshot_blocking_pids\",\n    \"pg_size_bytes\",\n    \"pg_size_pretty\",\n    \"pg_sleep\",\n    \"pg_sleep_for\",\n    \"pg_sleep_until\",\n    \"pg_snapshot_xip\",\n    \"pg_snapshot_xmax\",\n    \"pg_snapshot_xmin\",\n    \"pg_start_backup\",\n    \"pg_stat_file\",\n    \"pg_statistics_obj_is_visible\",\n    \"pg_stop_backup\",\n    \"pg_switch_wal\",\n    \"pg_switch_xlog\",\n    \"pg_table_is_visible\",\n    \"pg_table_size\",\n    \"pg_tablespace_databases\",\n    \"pg_tablespace_location\",\n    \"pg_tablespace_size\",\n    \"pg_terminate_backend\",\n    \"pg_total_relation_size\",\n    \"pg_trigger_depth\",\n    \"pg_try_advisory_lock\",\n    \"pg_try_advisory_lock_shared\",\n    \"pg_try_advisory_xact_lock\",\n    \"pg_try_advisory_xact_lock_shared\",\n    \"pg_ts_config_is_visible\",\n    \"pg_ts_dict_is_visible\",\n    \"pg_ts_parser_is_visible\",\n    \"pg_ts_template_is_visible\",\n    \"pg_type_is_visible\",\n    \"pg_typeof\",\n    \"pg_visible_in_snapshot\",\n    \"pg_wal_lsn_diff\",\n    \"pg_wal_replay_pause\",\n    \"pg_wal_replay_resume\",\n    \"pg_walfile_name\",\n    \"pg_walfile_name_offset\",\n    \"pg_xact_commit_timestamp\",\n    \"pg_xact_commit_timestamp_origin\",\n    \"pg_xact_status\",\n    \"pg_xlog_location_diff\",\n    \"pg_xlog_replay_pause\",\n    \"pg_xlog_replay_resume\",\n    \"pg_xlogfile_name\",\n    \"pg_xlogfile_name_offset\",\n    \"phraseto_tsquery\",\n    \"pi\",\n    \"plainto_tsquery\",\n    \"point\",\n    \"polygon\",\n    \"popen\",\n    \"position\",\n    \"power\",\n    \"pqserverversion\",\n    \"query_to_xml\",\n    \"query_to_xml_and_xmlschema\",\n    \"query_to_xmlschema\",\n    \"querytree\",\n    \"quote_ident\",\n    \"quote_literal\",\n    \"quote_nullable\",\n    \"radians\",\n    \"radius\",\n    \"random\",\n    \"range_agg\",\n    \"range_intersect_agg\",\n    \"range_merge\",\n    \"rank\",\n    \"regexp_count\",\n    \"regexp_instr\",\n    \"regexp_like\",\n    \"regexp_match\",\n    \"regexp_matches\",\n    \"regexp_replace\",\n    \"regexp_split_to_array\",\n    \"regexp_split_to_table\",\n    \"regexp_substr\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"repeat\",\n    \"replace\",\n    \"reverse\",\n    \"right\",\n    \"round\",\n    \"row_number\",\n    \"row_security_active\",\n    \"row_to_json\",\n    \"rpad\",\n    \"rtrim\",\n    \"scale\",\n    \"schema_to_xml\",\n    \"schema_to_xml_and_xmlschema\",\n    \"schema_to_xmlschema\",\n    \"session_user\",\n    \"set_bit\",\n    \"set_byte\",\n    \"set_config\",\n    \"set_masklen\",\n    \"setseed\",\n    \"setval\",\n    \"setweight\",\n    \"sha224\",\n    \"sha256\",\n    \"sha384\",\n    \"sha512\",\n    \"shobj_description\",\n    \"sign\",\n    \"sin\",\n    \"sind\",\n    \"sinh\",\n    \"slope\",\n    \"split_part\",\n    \"sprintf\",\n    \"sqrt\",\n    \"starts_with\",\n    \"statement_timestamp\",\n    \"stddev\",\n    \"stddev_pop\",\n    \"stddev_samp\",\n    \"string_agg\",\n    \"string_to_array\",\n    \"string_to_table\",\n    \"strip\",\n    \"strpos\",\n    \"substr\",\n    \"substring\",\n    \"sum\",\n    \"suppress_redundant_updates_trigger\",\n    \"table_to_xml\",\n    \"table_to_xml_and_xmlschema\",\n    \"table_to_xmlschema\",\n    \"tan\",\n    \"tand\",\n    \"tanh\",\n    \"text\",\n    \"timeofday\",\n    \"timezone\",\n    \"to_ascii\",\n    \"to_char\",\n    \"to_date\",\n    \"to_hex\",\n    \"to_json\",\n    \"to_number\",\n    \"to_regclass\",\n    \"to_regcollation\",\n    \"to_regnamespace\",\n    \"to_regoper\",\n    \"to_regoperator\",\n    \"to_regproc\",\n    \"to_regprocedure\",\n    \"to_regrole\",\n    \"to_regtype\",\n    \"to_timestamp\",\n    \"to_tsquery\",\n    \"to_tsvector\",\n    \"transaction_timestamp\",\n    \"translate\",\n    \"trim\",\n    \"trim_array\",\n    \"trim_scale\",\n    \"trunc\",\n    \"ts_debug\",\n    \"ts_delete\",\n    \"ts_filter\",\n    \"ts_headline\",\n    \"ts_lexize\",\n    \"ts_parse\",\n    \"ts_rank\",\n    \"ts_rank_cd\",\n    \"ts_rewrite\",\n    \"ts_stat\",\n    \"ts_token_type\",\n    \"tsquery_phrase\",\n    \"tsvector_to_array\",\n    \"tsvector_update_trigger\",\n    \"tsvector_update_trigger_column\",\n    \"txid_current\",\n    \"txid_current_if_assigned\",\n    \"txid_current_snapshot\",\n    \"txid_snapshot_xip\",\n    \"txid_snapshot_xmax\",\n    \"txid_snapshot_xmin\",\n    \"txid_status\",\n    \"txid_visible_in_snapshot\",\n    \"unistr\",\n    \"unnest\",\n    \"upper\",\n    \"upper_inc\",\n    \"upper_inf\",\n    \"user\",\n    \"var_pop\",\n    \"var_samp\",\n    \"variance\",\n    \"version\",\n    \"websearch_to_tsquery\",\n    \"width\",\n    \"width_bucket\",\n    \"xml_is_well_formed\",\n    \"xml_is_well_formed_content\",\n    \"xml_is_well_formed_document\",\n    \"xmlagg\",\n    \"xmlcomment\",\n    \"xmlconcat\",\n    \"xmlelement\",\n    \"xmlexists\",\n    \"xmlforest\",\n    \"xmlparse\",\n    \"xmlpi\",\n    \"xmlroot\",\n    \"xmlserialize\",\n    \"xpath\",\n    \"xpath_exists\"\n  ],\n  builtinVariables: [\n    // NOT SUPPORTED\n  ],\n  pseudoColumns: [\n    // NOT SUPPORTED\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@pseudoColumns\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@complexIdentifiers\" },\n      { include: \"@scopes\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [\n        /[\\w@#$]+/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [/--+.*/, \"comment\"],\n      [/\\/\\*/, { token: \"comment.quote\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^*/]+/, \"comment\"],\n      // Not supporting nested comments, as nested comments seem to not be standard?\n      // i.e. http://stackoverflow.com/questions/728172/are-there-multiline-comment-delimiters-in-sql-that-are-vendor-agnostic\n      // [/\\/\\*/, { token: 'comment.quote', next: '@push' }],    // nested comment not allowed :-(\n      [/\\*\\//, { token: \"comment.quote\", next: \"@pop\" }],\n      [/./, \"comment\"]\n    ],\n    pseudoColumns: [\n      [\n        /[$][A-Za-z_][\\w@#$]*/,\n        {\n          cases: {\n            \"@pseudoColumns\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [[/'/, { token: \"string\", next: \"@string\" }]],\n    string: [\n      [/[^']+/, \"string\"],\n      [/''/, \"string\"],\n      [/'/, { token: \"string\", next: \"@pop\" }]\n    ],\n    complexIdentifiers: [[/\"/, { token: \"identifier.quote\", next: \"@quotedIdentifier\" }]],\n    quotedIdentifier: [\n      [/[^\"]+/, \"identifier\"],\n      [/\"\"/, \"identifier\"],\n      [/\"/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    scopes: [\n      // NOT SUPPORTED\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/pgsql/pgsql.js\n"));

/***/ })

}]);