"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-intersection-observer";
exports.ids = ["vendor-chunks/react-intersection-observer"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-intersection-observer/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/react-intersection-observer/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InView: () => (/* binding */ InView),\n/* harmony export */   defaultFallbackInView: () => (/* binding */ defaultFallbackInView),\n/* harmony export */   observe: () => (/* binding */ observe),\n/* harmony export */   useInView: () => (/* binding */ useInView)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ InView,defaultFallbackInView,observe,useInView auto */ var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        value\n    }) : obj[key] = value;\nvar __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n// src/InView.tsx\n\n// src/observe.ts\nvar observerMap = /* @__PURE__ */ new Map();\nvar RootIds = /* @__PURE__ */ new WeakMap();\nvar rootId = 0;\nvar unsupportedValue = void 0;\nfunction defaultFallbackInView(inView) {\n    unsupportedValue = inView;\n}\nfunction getRootId(root) {\n    if (!root) return \"0\";\n    if (RootIds.has(root)) return RootIds.get(root);\n    rootId += 1;\n    RootIds.set(root, rootId.toString());\n    return RootIds.get(root);\n}\nfunction optionsToId(options) {\n    return Object.keys(options).sort().filter((key)=>options[key] !== void 0).map((key)=>{\n        return `${key}_${key === \"root\" ? getRootId(options.root) : options[key]}`;\n    }).toString();\n}\nfunction createObserver(options) {\n    const id = optionsToId(options);\n    let instance = observerMap.get(id);\n    if (!instance) {\n        const elements = /* @__PURE__ */ new Map();\n        let thresholds;\n        const observer = new IntersectionObserver((entries)=>{\n            entries.forEach((entry)=>{\n                var _a;\n                const inView = entry.isIntersecting && thresholds.some((threshold)=>entry.intersectionRatio >= threshold);\n                if (options.trackVisibility && typeof entry.isVisible === \"undefined\") {\n                    entry.isVisible = inView;\n                }\n                (_a = elements.get(entry.target)) == null ? void 0 : _a.forEach((callback)=>{\n                    callback(inView, entry);\n                });\n            });\n        }, options);\n        thresholds = observer.thresholds || (Array.isArray(options.threshold) ? options.threshold : [\n            options.threshold || 0\n        ]);\n        instance = {\n            id,\n            observer,\n            elements\n        };\n        observerMap.set(id, instance);\n    }\n    return instance;\n}\nfunction observe(element, callback, options = {}, fallbackInView = unsupportedValue) {\n    if (typeof window.IntersectionObserver === \"undefined\" && fallbackInView !== void 0) {\n        const bounds = element.getBoundingClientRect();\n        callback(fallbackInView, {\n            isIntersecting: fallbackInView,\n            target: element,\n            intersectionRatio: typeof options.threshold === \"number\" ? options.threshold : 0,\n            time: 0,\n            boundingClientRect: bounds,\n            intersectionRect: bounds,\n            rootBounds: bounds\n        });\n        return ()=>{};\n    }\n    const { id, observer, elements } = createObserver(options);\n    const callbacks = elements.get(element) || [];\n    if (!elements.has(element)) {\n        elements.set(element, callbacks);\n    }\n    callbacks.push(callback);\n    observer.observe(element);\n    return function unobserve() {\n        callbacks.splice(callbacks.indexOf(callback), 1);\n        if (callbacks.length === 0) {\n            elements.delete(element);\n            observer.unobserve(element);\n        }\n        if (elements.size === 0) {\n            observer.disconnect();\n            observerMap.delete(id);\n        }\n    };\n}\n// src/InView.tsx\nfunction isPlainChildren(props) {\n    return typeof props.children !== \"function\";\n}\nvar InView = class extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    constructor(props){\n        super(props);\n        __publicField(this, \"node\", null);\n        __publicField(this, \"_unobserveCb\", null);\n        __publicField(this, \"handleNode\", (node)=>{\n            if (this.node) {\n                this.unobserve();\n                if (!node && !this.props.triggerOnce && !this.props.skip) {\n                    this.setState({\n                        inView: !!this.props.initialInView,\n                        entry: void 0\n                    });\n                }\n            }\n            this.node = node ? node : null;\n            this.observeNode();\n        });\n        __publicField(this, \"handleChange\", (inView, entry)=>{\n            if (inView && this.props.triggerOnce) {\n                this.unobserve();\n            }\n            if (!isPlainChildren(this.props)) {\n                this.setState({\n                    inView,\n                    entry\n                });\n            }\n            if (this.props.onChange) {\n                this.props.onChange(inView, entry);\n            }\n        });\n        this.state = {\n            inView: !!props.initialInView,\n            entry: void 0\n        };\n    }\n    componentDidMount() {\n        this.unobserve();\n        this.observeNode();\n    }\n    componentDidUpdate(prevProps) {\n        if (prevProps.rootMargin !== this.props.rootMargin || prevProps.root !== this.props.root || prevProps.threshold !== this.props.threshold || prevProps.skip !== this.props.skip || prevProps.trackVisibility !== this.props.trackVisibility || prevProps.delay !== this.props.delay) {\n            this.unobserve();\n            this.observeNode();\n        }\n    }\n    componentWillUnmount() {\n        this.unobserve();\n    }\n    observeNode() {\n        if (!this.node || this.props.skip) return;\n        const { threshold, root, rootMargin, trackVisibility, delay, fallbackInView } = this.props;\n        this._unobserveCb = observe(this.node, this.handleChange, {\n            threshold,\n            root,\n            rootMargin,\n            // @ts-ignore\n            trackVisibility,\n            // @ts-ignore\n            delay\n        }, fallbackInView);\n    }\n    unobserve() {\n        if (this._unobserveCb) {\n            this._unobserveCb();\n            this._unobserveCb = null;\n        }\n    }\n    render() {\n        const { children } = this.props;\n        if (typeof children === \"function\") {\n            const { inView, entry } = this.state;\n            return children({\n                inView,\n                entry,\n                ref: this.handleNode\n            });\n        }\n        const { as, triggerOnce, threshold, root, rootMargin, onChange, skip, trackVisibility, delay, initialInView, fallbackInView, ...props } = this.props;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(as || \"div\", {\n            ref: this.handleNode,\n            ...props\n        }, children);\n    }\n};\n// src/useInView.tsx\n\nfunction useInView({ threshold, delay, trackVisibility, rootMargin, root, triggerOnce, skip, initialInView, fallbackInView, onChange } = {}) {\n    var _a;\n    const [ref, setRef] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const callback = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        inView: !!initialInView,\n        entry: void 0\n    });\n    callback.current = onChange;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useInView.useEffect\": ()=>{\n            if (skip || !ref) return;\n            let unobserve;\n            unobserve = observe(ref, {\n                \"useInView.useEffect\": (inView, entry)=>{\n                    setState({\n                        inView,\n                        entry\n                    });\n                    if (callback.current) callback.current(inView, entry);\n                    if (entry.isIntersecting && triggerOnce && unobserve) {\n                        unobserve();\n                        unobserve = void 0;\n                    }\n                }\n            }[\"useInView.useEffect\"], {\n                root,\n                rootMargin,\n                threshold,\n                // @ts-ignore\n                trackVisibility,\n                // @ts-ignore\n                delay\n            }, fallbackInView);\n            return ({\n                \"useInView.useEffect\": ()=>{\n                    if (unobserve) {\n                        unobserve();\n                    }\n                }\n            })[\"useInView.useEffect\"];\n        }\n    }[\"useInView.useEffect\"], // We break the rule here, because we aren't including the actual `threshold` variable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        // If the threshold is an array, convert it to a string, so it won't change between renders.\n        Array.isArray(threshold) ? threshold.toString() : threshold,\n        ref,\n        root,\n        rootMargin,\n        triggerOnce,\n        skip,\n        trackVisibility,\n        fallbackInView,\n        delay\n    ]);\n    const entryTarget = (_a = state.entry) == null ? void 0 : _a.target;\n    const previousEntryTarget = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    if (!ref && entryTarget && !triggerOnce && !skip && previousEntryTarget.current !== entryTarget) {\n        previousEntryTarget.current = entryTarget;\n        setState({\n            inView: !!initialInView,\n            entry: void 0\n        });\n    }\n    const result = [\n        setRef,\n        state.inView,\n        state.entry\n    ];\n    result.ref = result[0];\n    result.inView = result[1];\n    result.entry = result[2];\n    return result;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-intersection-observer/dist/index.mjs\n");

/***/ })

};
;