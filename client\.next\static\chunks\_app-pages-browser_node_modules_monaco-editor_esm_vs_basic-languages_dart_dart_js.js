"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_dart_dart_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/dart/dart.js":
/*!*************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/dart/dart.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/dart/dart.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"`\", close: \"`\", notIn: [\"string\", \"comment\"] },\n    { open: \"/**\", close: \" */\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: /^\\s*\\s*#?region\\b/,\n      end: /^\\s*\\s*#?endregion\\b/\n    }\n  }\n};\nvar language = {\n  defaultToken: \"invalid\",\n  tokenPostfix: \".dart\",\n  keywords: [\n    \"abstract\",\n    \"dynamic\",\n    \"implements\",\n    \"show\",\n    \"as\",\n    \"else\",\n    \"import\",\n    \"static\",\n    \"assert\",\n    \"enum\",\n    \"in\",\n    \"super\",\n    \"async\",\n    \"export\",\n    \"interface\",\n    \"switch\",\n    \"await\",\n    \"extends\",\n    \"is\",\n    \"sync\",\n    \"break\",\n    \"external\",\n    \"library\",\n    \"this\",\n    \"case\",\n    \"factory\",\n    \"mixin\",\n    \"throw\",\n    \"catch\",\n    \"false\",\n    \"new\",\n    \"true\",\n    \"class\",\n    \"final\",\n    \"null\",\n    \"try\",\n    \"const\",\n    \"finally\",\n    \"on\",\n    \"typedef\",\n    \"continue\",\n    \"for\",\n    \"operator\",\n    \"var\",\n    \"covariant\",\n    \"Function\",\n    \"part\",\n    \"void\",\n    \"default\",\n    \"get\",\n    \"rethrow\",\n    \"while\",\n    \"deferred\",\n    \"hide\",\n    \"return\",\n    \"with\",\n    \"do\",\n    \"if\",\n    \"set\",\n    \"yield\"\n  ],\n  typeKeywords: [\"int\", \"double\", \"String\", \"bool\"],\n  operators: [\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"~/\",\n    \"%\",\n    \"++\",\n    \"--\",\n    \"==\",\n    \"!=\",\n    \">\",\n    \"<\",\n    \">=\",\n    \"<=\",\n    \"=\",\n    \"-=\",\n    \"/=\",\n    \"%=\",\n    \">>=\",\n    \"^=\",\n    \"+=\",\n    \"*=\",\n    \"~/=\",\n    \"<<=\",\n    \"&=\",\n    \"!=\",\n    \"||\",\n    \"&&\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"~\",\n    \"<<\",\n    \">>\",\n    \"!\",\n    \">>>\",\n    \"??\",\n    \"?\",\n    \":\",\n    \"|=\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+(_+\\d+)*/,\n  octaldigits: /[0-7]+(_+[0-7]+)*/,\n  binarydigits: /[0-1]+(_+[0-1]+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n  regexpesc: /\\\\(?:[bBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [[/[{}]/, \"delimiter.bracket\"], { include: \"common\" }],\n    common: [\n      // identifiers and keywords\n      [\n        /[a-z_$][\\w$]*/,\n        {\n          cases: {\n            \"@typeKeywords\": \"type.identifier\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[A-Z_$][\\w\\$]*/, \"type.identifier\"],\n      // show class names\n      // [/[A-Z][\\w\\$]*/, 'identifier'],\n      // whitespace\n      { include: \"@whitespace\" },\n      // regular expression: ensure it is terminated before beginning (otherwise it is an opeator)\n      [\n        /\\/(?=([^\\\\\\/]|\\\\.)+\\/([gimsuy]*)(\\s*)(\\.|;|,|\\)|\\]|\\}|$))/,\n        { token: \"regexp\", bracket: \"@open\", next: \"@regexp\" }\n      ],\n      // @ annotations.\n      [/@[a-zA-Z]+/, \"annotation\"],\n      // variable\n      // delimiters and operators\n      [/[()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/!(?=([^=]|$))/, \"delimiter\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?/, \"number.float\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?/, \"number.float\"],\n      [/0[xX](@hexdigits)n?/, \"number.hex\"],\n      [/0[oO]?(@octaldigits)n?/, \"number.octal\"],\n      [/0[bB](@binarydigits)n?/, \"number.binary\"],\n      [/(@digits)n?/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string_double\"],\n      [/'/, \"string\", \"@string_single\"]\n      //   [/[a-zA-Z]+/, \"variable\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@jsdoc\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/\\/.*$/, \"comment.doc\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    jsdoc: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    // We match regular expression quite precisely\n    regexp: [\n      [\n        /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n        [\"regexp.escape.control\", \"regexp.escape.control\", \"regexp.escape.control\"]\n      ],\n      [\n        /(\\[)(\\^?)(?=(?:[^\\]\\\\\\/]|\\\\.)+)/,\n        [\"regexp.escape.control\", { token: \"regexp.escape.control\", next: \"@regexrange\" }]\n      ],\n      [/(\\()(\\?:|\\?=|\\?!)/, [\"regexp.escape.control\", \"regexp.escape.control\"]],\n      [/[()]/, \"regexp.escape.control\"],\n      [/@regexpctl/, \"regexp.escape.control\"],\n      [/[^\\\\\\/]/, \"regexp\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/\\\\\\./, \"regexp.invalid\"],\n      [/(\\/)([gimsuy]*)/, [{ token: \"regexp\", bracket: \"@close\", next: \"@pop\" }, \"keyword.other\"]]\n    ],\n    regexrange: [\n      [/-/, \"regexp.escape.control\"],\n      [/\\^/, \"regexp.invalid\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/[^\\]]/, \"regexp\"],\n      [\n        /\\]/,\n        {\n          token: \"regexp.escape.control\",\n          next: \"@pop\",\n          bracket: \"@close\"\n        }\n      ]\n    ],\n    string_double: [\n      [/[^\\\\\"\\$]+/, \"string\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"],\n      [/\\$\\w+/, \"identifier\"]\n    ],\n    string_single: [\n      [/[^\\\\'\\$]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, \"string\", \"@pop\"],\n      [/\\$\\w+/, \"identifier\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/dart/dart.js\n"));

/***/ })

}]);