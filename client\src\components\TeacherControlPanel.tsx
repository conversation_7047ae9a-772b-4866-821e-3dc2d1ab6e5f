'use client';

import React, { useState, useEffect } from 'react';
import { FiEdit3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Lock, FiUnlock } from 'react-icons/fi';
import { useEditPermission } from '@/context/EditPermissionContext';
import { useSocketService } from '@/hooks/useSocketService';

interface TeacherControlPanelProps {
  className?: string;
}

export default function TeacherControlPanel({ className = '' }: TeacherControlPanelProps) {
  const { isTeacher, students, grantEditPermission, revokeEditPermission } = useEditPermission();
  const { isReady, isConnected } = useSocketService();
  const [pending, setPending] = useState<string | null>(null);

  // Debug logging for student list updates
  useEffect(() => {
    console.log('🎯 [TEACHER_PANEL] State update:', {
      isTeacher,
      studentCount: students.length,
      isReady,
      isConnected,
      students: students.map(s => ({
        username: s.username,
        socketId: s.socketId,
        canEdit: s.canEdit
      }))
    });
  }, [students, isTeacher, isReady, isConnected]);

  // Only show panel for teachers
  if (!isTeacher) {
    console.log('🎯 [TEACHER_PANEL] Not showing panel (not teacher)');
    return null;
  }

  const handleToggle = (student: any) => {
    setPending(student.socketId);
    if (student.canEdit) {
      revokeEditPermission(student.socketId);
    } else {
      grantEditPermission(student.socketId);
    }
    setTimeout(() => setPending(null), 1000);
  };

  return (
    <aside className={`w-full max-w-md bg-white border rounded-lg shadow-lg p-4 ${className}`}>
      <div className="flex items-center mb-4">
        <FiUsers className="text-blue-500 mr-2" />
        <h2 className="text-lg font-bold">Student Access Panel</h2>
      </div>
      {!isReady || !isConnected ? (
        <div className="text-yellow-600 text-sm mb-2">Waiting for connection...</div>
      ) : null}
      <div className="space-y-2">
        {students.length === 0 && (
          <div className="text-gray-400 flex flex-col items-center py-8">
            <FiUsers className="w-8 h-8 mb-2" />
            <span>No students in the room yet</span>
          </div>
        )}
        {students.map(student => (
          <div key={student.socketId} className="flex items-center justify-between bg-gray-50 rounded px-3 py-2">
            <div className="flex items-center space-x-2">
              <span className="font-medium">{student.username}</span>
              {student.canEdit ? (
                <span className="inline-flex items-center px-2 py-0.5 rounded bg-green-100 text-green-700 text-xs ml-2">
                  <FiEdit3 className="w-3 h-3 mr-1" /> Edit
                </span>
              ) : (
                <span className="inline-flex items-center px-2 py-0.5 rounded bg-gray-100 text-gray-600 text-xs ml-2">
                  <FiEye className="w-3 h-3 mr-1" /> View
                </span>
              )}
            </div>
            <button
              className={`flex items-center px-3 py-1 rounded text-xs font-medium transition-all ${student.canEdit ? 'bg-green-100 text-green-700 hover:bg-green-200' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'} ${pending === student.socketId || !isReady || !isConnected ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
              onClick={() => handleToggle(student)}
              disabled={pending === student.socketId || !isReady || !isConnected}
              title={student.canEdit ? 'Revoke edit access' : 'Grant edit access'}
            >
              {student.canEdit ? <><FiUnlock className="w-3 h-3 mr-1" /> Revoke</> : <><FiLock className="w-3 h-3 mr-1" /> Grant</>}
            </button>
          </div>
        ))}
      </div>
    </aside>
  );
}
