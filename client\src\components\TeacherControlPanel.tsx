'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiU<PERSON>s, 
  <PERSON>Eye, 
  FiEdit3, 
  FiShield, 
  FiToggleLeft, 
  FiToggleRight,
  FiChevronDown,
  FiChevronUp,
  FiClock,
  FiMail,
  FiUser
} from 'react-icons/fi';
import { useEditPermission } from '@/context/EditPermissionContext';

interface TeacherControlPanelProps {
  className?: string;
}

export default function TeacherControlPanel({ className = '' }: TeacherControlPanelProps) {
  const { 
    isTeacher, 
    students, 
    grantEditPermission, 
    revokeEditPermission 
  } = useEditPermission();
  
  const [isExpanded, setIsExpanded] = useState(true);
  const [pendingActions, setPendingActions] = useState<Set<string>>(new Set());

  // Only show panel for teachers
  if (!isTeacher) {
    return null;
  }

  const handlePermissionToggle = async (student: any) => {
    const { socketId, canEdit } = student;
    
    // Add to pending actions
    setPendingActions(prev => new Set(prev).add(socketId));

    try {
      if (canEdit) {
        revokeEditPermission(socketId);
      } else {
        grantEditPermission(socketId);
      }
      
      // Remove from pending after a delay
      setTimeout(() => {
        setPendingActions(prev => {
          const newSet = new Set(prev);
          newSet.delete(socketId);
          return newSet;
        });
      }, 1000);
    } catch (error) {
      console.error('Error toggling permission:', error);
      // Remove from pending on error
      setPendingActions(prev => {
        const newSet = new Set(prev);
        newSet.delete(socketId);
        return newSet;
      });
    }
  };

  const handleBulkAction = (action: 'grant' | 'revoke') => {
    students.forEach(student => {
      if (action === 'grant' && !student.canEdit) {
        grantEditPermission(student.socketId);
      } else if (action === 'revoke' && student.canEdit) {
        revokeEditPermission(student.socketId);
      }
    });
  };

  const getPermissionBadge = (student: any) => {
    const isPending = pendingActions.has(student.socketId);
    
    if (student.canEdit) {
      return (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          isPending ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
        }`}>
          <FiEdit3 className="w-3 h-3 mr-1" />
          {isPending ? 'Updating...' : 'Edit Access'}
        </span>
      );
    } else {
      return (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          isPending ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-600'
        }`}>
          <FiEye className="w-3 h-3 mr-1" />
          {isPending ? 'Updating...' : 'View-only'}
        </span>
      );
    }
  };

  const formatTime = (timeString: string) => {
    try {
      return new Date(timeString).toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } catch {
      return 'Unknown';
    }
  };

  const editableStudents = students.filter(s => s.canEdit).length;
  const totalStudents = students.length;

  return (
    <div className={`bg-white rounded-lg shadow-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div 
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors border-b border-gray-100"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
            <FiShield className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Teacher Control Panel</h3>
            <p className="text-sm text-gray-500">
              {editableStudents}/{totalStudents} students can edit
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">{totalStudents} students</span>
          {isExpanded ? (
            <FiChevronUp className="w-5 h-5 text-gray-400" />
          ) : (
            <FiChevronDown className="w-5 h-5 text-gray-400" />
          )}
        </div>
      </div>

      {/* Expanded Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="p-4 space-y-4">
              {/* Bulk Actions */}
              {totalStudents > 0 && (
                <div className="flex space-x-2 pb-3 border-b border-gray-100">
                  <button
                    onClick={() => handleBulkAction('grant')}
                    className="flex-1 px-3 py-2 text-sm font-medium text-green-700 bg-green-100 rounded-lg hover:bg-green-200 transition-colors"
                  >
                    Grant All Edit Access
                  </button>
                  <button
                    onClick={() => handleBulkAction('revoke')}
                    className="flex-1 px-3 py-2 text-sm font-medium text-red-700 bg-red-100 rounded-lg hover:bg-red-200 transition-colors"
                  >
                    Revoke All Access
                  </button>
                </div>
              )}

              {/* Student List */}
              {totalStudents > 0 ? (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-700 uppercase tracking-wide">
                    Students ({totalStudents})
                  </h4>
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {students.map((student) => {
                      const isPending = pendingActions.has(student.socketId);
                      
                      return (
                        <motion.div
                          key={student.socketId}
                          layout
                          className={`p-3 rounded-lg border transition-all ${
                            isPending 
                              ? 'bg-yellow-50 border-yellow-200' 
                              : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3 flex-1">
                              <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                                <FiUser className="w-4 h-4 text-blue-600" />
                              </div>
                              
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2">
                                  <p className="text-sm font-medium text-gray-900 truncate">
                                    {student.username}
                                  </p>
                                  {getPermissionBadge(student)}
                                </div>
                                
                                <div className="flex items-center space-x-4 mt-1">
                                  {student.email && (
                                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                                      <FiMail className="w-3 h-3" />
                                      <span className="truncate max-w-32">{student.email}</span>
                                    </div>
                                  )}
                                  
                                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                                    <FiClock className="w-3 h-3" />
                                    <span>Joined {formatTime(student.joinedAt)}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            {/* Permission Toggle */}
                            <button
                              onClick={() => handlePermissionToggle(student)}
                              disabled={isPending}
                              className={`ml-3 p-2 rounded-lg transition-all ${
                                student.canEdit
                                  ? 'bg-green-100 text-green-700 hover:bg-green-200'
                                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                              } ${isPending ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                              title={student.canEdit ? 'Revoke edit access' : 'Grant edit access'}
                            >
                              {student.canEdit ? (
                                <FiToggleRight className="w-5 h-5" />
                              ) : (
                                <FiToggleLeft className="w-5 h-5" />
                              )}
                            </button>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <FiUsers className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                  <p className="text-sm text-gray-500 font-medium">No students in the room yet</p>
                  <p className="text-xs text-gray-400 mt-1">
                    Students will appear here when they join the room
                  </p>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
