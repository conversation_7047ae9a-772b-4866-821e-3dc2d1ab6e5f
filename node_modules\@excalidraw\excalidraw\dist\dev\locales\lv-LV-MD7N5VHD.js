import "../chunk-XDFCUUT6.js";

// locales/lv-LV.json
var labels = {
  paste: "Iel\u012Bm\u0113t",
  pasteAsPlaintext: "Iel\u012Bm\u0113t k\u0101 vienk\u0101r\u0161u tekstu",
  pasteCharts: "Iel\u012Bm\u0113t grafikus",
  selectAll: "Atlas\u012Bt visu",
  multiSelect: "Pievienot elementu atlasei",
  moveCanvas: "P\u0101rvietot t\u0101feli",
  cut: "Izgriezt",
  copy: "Kop\u0113t",
  copyAsPng: "Kop\u0113t starpliktuv\u0113 k\u0101 PNG",
  copyAsSvg: "Kop\u0113t starpliktuv\u0113 k\u0101 SVG",
  copyText: "Kop\u0113t starpliktuv\u0113 k\u0101 tekstu",
  copySource: "",
  convertToCode: "",
  bringForward: "P\u0101rvietot vienu sl\u0101ni augst\u0101k",
  sendToBack: "P\u0101rvietot uz zem\u0101ko sl\u0101ni",
  bringToFront: "P\u0101rvietot uz virs\u0113jo sl\u0101ni",
  sendBackward: "P\u0101rvietot par vienu sl\u0101ni zem\u0101k",
  delete: "Dz\u0113st",
  copyStyles: "Kop\u0113t stilus",
  pasteStyles: "Iel\u012Bm\u0113t stilus",
  stroke: "Sv\u012Btras kr\u0101sa",
  background: "Fona kr\u0101sa",
  fill: "Aizpild\u012Bjums",
  strokeWidth: "Sv\u012Btras platums",
  strokeStyle: "Sv\u012Btras stils",
  strokeStyle_solid: "Vienlaidu",
  strokeStyle_dashed: "Raust\u012Bta l\u012Bnija",
  strokeStyle_dotted: "Punktota l\u012Bnija",
  sloppiness: "Precizit\u0101te",
  opacity: "Necaursp\u012Bd\u012Bgums",
  textAlign: "Teksta l\u012Bdzin\u0101\u0161ana",
  edges: "Malas",
  sharp: "Asas",
  round: "Apa\u013Cas",
  arrowheads: "Bultas",
  arrowhead_none: "Nek\u0101das",
  arrowhead_arrow: "Bulta",
  arrowhead_bar: "Sv\u012Btra",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "Trijst\u016Bris",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "Teksta lielums",
  fontFamily: "Fontu saime",
  addWatermark: 'Pievienot "Rad\u012Bts ar Excalidraw"',
  handDrawn: "Rokraksts",
  normal: "Parasts",
  code: "Kods",
  small: "Mazs",
  medium: "Vid\u0113js",
  large: "Liels",
  veryLarge: "\u013Boti liels",
  solid: "Pilns",
  hachure: "Sv\u012Btrots",
  zigzag: "Zigzagl\u012Bnija",
  crossHatch: "\u0160\u0137\u0113rssv\u012Btrots",
  thin: "\u0160aurs",
  bold: "Trekns",
  left: "Pa kreisi",
  center: "Vid\u016B",
  right: "Pa labi",
  extraBold: "\u012Apa\u0161i trekns",
  architect: "Arhitekts",
  artist: "M\u0101kslinieks",
  cartoonist: "Karikat\u016Brists",
  fileTitle: "Datnes nosaukums",
  colorPicker: "Kr\u0101su atlas\u012Bt\u0101js",
  canvasColors: "Izmantots t\u0101felei",
  canvasBackground: "Ainas fons",
  drawingCanvas: "T\u0101fele",
  layers: "Sl\u0101\u0146i",
  actions: "Darb\u012Bbas",
  language: "Valoda",
  liveCollaboration: "Sadarb\u012Bba tie\u0161saist\u0113...",
  duplicateSelection: "Izveidot kopiju",
  untitled: "Bez nosaukuma",
  name: "V\u0101rds",
  yourName: "J\u016Bsu v\u0101rds",
  madeWithExcalidraw: "Rad\u012Bts ar Excalidraw",
  group: "Grup\u0113t atlas\u012Bto",
  ungroup: "Atgrup\u0113t atlas\u012Bto",
  collaborators: "Dal\u012Bbnieki",
  showGrid: "R\u0101d\u012Bt re\u017E\u0123i",
  addToLibrary: "Pievienot bibliot\u0113kai",
  removeFromLibrary: "Iz\u0146emt no bibliot\u0113kas",
  libraryLoadingMessage: "Iel\u0101d\u0113 bibliot\u0113ku\u2026",
  libraries: "Apskat\u012Bt bibliot\u0113kas",
  loadingScene: "Iel\u0101d\u0113 ainu\u2026",
  align: "L\u012Bdzin\u0101t",
  alignTop: "L\u012Bdzin\u0101t aug\u0161pus\u0113",
  alignBottom: "L\u012Bdzin\u0101t lej\u0101",
  alignLeft: "L\u012Bdzin\u0101t pa kreisi",
  alignRight: "L\u012Bdzin\u0101t pa labi",
  centerVertically: "Centr\u0113t vertik\u0101li",
  centerHorizontally: "Centr\u0113t horizont\u0101li",
  distributeHorizontally: "Izdal\u012Bt horizont\u0101li",
  distributeVertically: "Izdal\u012Bt vertik\u0101li",
  flipHorizontal: "Apmest horizont\u0101li",
  flipVertical: "Apmest vertik\u0101li",
  viewMode: "Skata re\u017E\u012Bms",
  share: "Kop\u012Bgot",
  showStroke: "R\u0101d\u012Bt sv\u012Btras kr\u0101sas atlas\u012Bt\u0101ju",
  showBackground: "R\u0101d\u012Bt fona kr\u0101sas atlas\u012Bt\u0101ju",
  toggleTheme: "P\u0101rsl\u0113gt kr\u0101su t\u0113mu",
  personalLib: "Person\u012Bg\u0101 bibliot\u0113ka",
  excalidrawLib: "Excalidraw bibliot\u0113ka",
  decreaseFontSize: "Samazin\u0101t fonta izm\u0113ru",
  increaseFontSize: "Palielin\u0101t fonta izm\u0113ru",
  unbindText: "Atdal\u012Bt tekstu",
  bindText: "Piesaist\u012Bt tekstu fig\u016Brai",
  createContainerFromText: "Ietilpin\u0101t tekstu figur\u0101",
  link: {
    edit: "Redi\u0123\u0113t saiti",
    editEmbed: "",
    create: "Izveidot saiti",
    createEmbed: "",
    label: "Saite",
    labelEmbed: "",
    empty: ""
  },
  lineEditor: {
    edit: "Redi\u0123\u0113t l\u012Bniju",
    exit: "Aizv\u0113rt l\u012Bnijas redaktoru"
  },
  elementLock: {
    lock: "Fiks\u0113t",
    unlock: "Atbr\u012Bvot",
    lockAll: "Fiks\u0113t visu",
    unlockAll: "Atbr\u012Bvot visu"
  },
  statusPublished: "Public\u0113ts",
  sidebarLock: "Patur\u0113t atv\u0113rtu s\u0101njoslu",
  selectAllElementsInFrame: "",
  removeAllElementsFromFrame: "",
  eyeDropper: "",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "Neviena vien\u012Bba v\u0113l nav pievienota...",
  hint_emptyLibrary: "Atlasiet objektu t\u0101fel\u0113, lai to \u0161eit pievienotu, vai pievienojiet publisku bibliot\u0113ku zem\u0101k.",
  hint_emptyPrivateLibrary: "Atlasiet objektu t\u0101fel\u0113, lai to \u0161eit pievienotu."
};
var buttons = {
  clearReset: "Atiestat\u012Bt t\u0101feli",
  exportJSON: "Eksport\u0113t k\u0101 failu",
  exportImage: "Eksport\u0113t att\u0113lu...",
  export: "Saglab\u0101t uz...",
  copyToClipboard: "Kop\u0113t starpliktuv\u0113",
  save: "Saglab\u0101t pa\u0161reiz\u0113jo datni",
  saveAs: "Saglab\u0101t k\u0101",
  load: "Atv\u0113rt",
  getShareableLink: "Ieg\u016Bt kop\u012Bgo\u0161anas saiti",
  close: "Aizv\u0113rt",
  selectLanguage: "Izv\u0113lieties valodu",
  scrollBackToContent: "Atgriezties pie satura",
  zoomIn: "Tuvin\u0101t",
  zoomOut: "T\u0101lin\u0101t",
  resetZoom: "Atiestat\u012Bt tuvin\u0101jumu",
  menu: "Izv\u0113lne",
  done: "Gatavs",
  edit: "Redi\u0123\u0113t",
  undo: "Atsaukt",
  redo: "Atcelt atsauk\u0161anu",
  resetLibrary: "Atiestat\u012Bt bibliot\u0113ku",
  createNewRoom: "Izveidot jaunu telpu",
  fullScreen: "Pilnekr\u0101na re\u017E\u012Bms",
  darkMode: "Tum\u0161ais re\u017E\u012Bms",
  lightMode: "Gai\u0161ais re\u017E\u012Bms",
  zenMode: "Zen re\u017E\u012Bms",
  objectsSnapMode: "",
  exitZenMode: "Pamest Zen re\u017E\u012Bmu",
  cancel: "Atcelt",
  clear: "Not\u012Br\u012Bt",
  remove: "No\u0146emt",
  embed: "",
  publishLibrary: "Public\u0113t",
  submit: "Iesniegt",
  confirm: "Apstiprin\u0101t",
  embeddableInteractionButton: ""
};
var alerts = {
  clearReset: "\u0160\u012B funkcija not\u012Br\u012Bs visu t\u0101feli. Vai turpin\u0101t?",
  couldNotCreateShareableLink: "Nevar\u0113ja izveidot kop\u012Bgojamo saiti.",
  couldNotCreateShareableLinkTooBig: "Nevar\u0113ja izveidot kop\u012Bgojamo saiti \u2013 aina ir par lielu",
  couldNotLoadInvalidFile: "Nevar\u0113ja iel\u0101d\u0113t neder\u012Bgu datni",
  importBackendFailed: "Iel\u0101de no kr\u0101tuves neizdev\u0101s.",
  cannotExportEmptyCanvas: "Nevar eksport\u0113t tuk\u0161u t\u0101feli.",
  couldNotCopyToClipboard: "Nevar\u0113ja nokop\u0113t starpliktuv\u0113.",
  decryptFailed: "Nevar\u0113ja at\u0161ifr\u0113t datus.",
  uploadedSecurly: "Aug\u0161upl\u0101de nodro\u0161in\u0101ta ar \u0161ifr\u0113\u0161anu no gala l\u012Bdz galam, kas noz\u012Bm\u0113, ka Excalidraw serveri un tre\u0161\u0101s puses nevar las\u012Bt saturu.",
  loadSceneOverridePrompt: "\u0100r\u0113ja satura iel\u0101de aizst\u0101s j\u016Bsu pa\u0161reiz\u0113jo saturu. Vai v\u0113laties turpin\u0101t?",
  collabStopOverridePrompt: "Sesijas p\u0101rtrauk\u0161ana p\u0101rrakst\u012Bs j\u016Bsu iepriek\u0161\u0113jo z\u012Bm\u0113jumu, kas saglab\u0101ts j\u016Bsu p\u0101rl\u016Bk\u0101. Vai turpin\u0101t?\n\n(Ja v\u0113laties patur\u0113t z\u012Bm\u0113jumu, kas saglab\u0101ts j\u016Bsu p\u0101rl\u016Bk\u0101, vienk\u0101r\u0161i aizveriet p\u0101rl\u016Bka cilni.)",
  errorAddingToLibrary: "Nevar\u0113ja pievienot vienumu bibliot\u0113kai",
  errorRemovingFromLibrary: "Nevar\u0113ja iz\u0146emt vienumu no bibliot\u0113kas",
  confirmAddLibrary: "\u0160\u012B funkcija pievienos {{numShapes}} formu(-as) j\u016Bsu bibliot\u0113kai. Vai turpin\u0101t?",
  imageDoesNotContainScene: "\u0160\u0137iet, ka att\u0113ls nesatur ainas datus. Vai iesp\u0113joj\u0101t ainas iegul\u0161anu, kad eksport\u0113j\u0101t?",
  cannotRestoreFromImage: "Ainu nevar\u0113ja atg\u016Bt no att\u0113la datnes",
  invalidSceneUrl: "Nevar\u0113ja import\u0113t ainu no nor\u0101d\u012Bt\u0101 URL. Vai nu tas ir neder\u012Bgs, vai nesatur der\u012Bgus Excalidraw JSON datus.",
  resetLibrary: "\u0160\u012B funkcija iztuk\u0161os bibliot\u0113ku. Vai turpin\u0101t?",
  removeItemsFromsLibrary: "Vai iz\u0146emt {{count}} vienumu(s) no bibliot\u0113kas?",
  invalidEncryptionKey: "\u0160ifr\u0113\u0161anas atsl\u0113gai j\u0101b\u016Bt 22 simbolus garai. Tie\u0161saistes sadarb\u012Bba ir izsl\u0113gta.",
  collabOfflineWarning: "Nav pieejams interneta piesl\u0113gums.\nJ\u016Bsu izmai\u0146as netiks saglab\u0101tas!"
};
var errors = {
  unsupportedFileType: "Neatbalst\u012Bts datnes veids.",
  imageInsertError: "Nevar\u0113ja ievietot att\u0113lu. M\u0113\u0123iniet v\u0113l\u0101k...",
  fileTooBig: "Datne ir par lielu. Liel\u0101kais at\u013Cautais izm\u0113rs ir {{maxSize}}.",
  svgImageInsertError: "Nevar\u0113ja ievietot SVG att\u0113lu. \u0160\u0137iet, ka SVG mar\u0137\u0113jums nav der\u012Bgs.",
  failedToFetchImage: "",
  invalidSVGString: "Neder\u012Bgs SVG.",
  cannotResolveCollabServer: "Nevar\u0113ja savienoties ar sadarbo\u0161an\u0101s serveri. L\u016Bdzu, p\u0101rl\u0101d\u0113jiet lapu un m\u0113\u0123iniet v\u0113lreiz.",
  importLibraryError: "Nevar\u0113ja iel\u0101d\u0113t bibliot\u0113ku",
  collabSaveFailed: "Darbs nav saglab\u0101ts datub\u0101z\u0113. Ja probl\u0113ma turpin\u0101s, saglab\u0101jiet datni lok\u0101laj\u0101 kr\u0101tuv\u0113, lai nodro\u0161in\u0101tos pret darba pazaud\u0113\u0161anu.",
  collabSaveFailed_sizeExceeded: "Darbs nav saglab\u0101ts datub\u0101z\u0113, \u0161\u0137iet, ka t\u0101fele ir p\u0101r\u0101k liela. Saglab\u0101jiet datni lok\u0101laj\u0101 kr\u0101tuv\u0113, lai nodro\u0161in\u0101tos pret darba pazaud\u0113\u0161anu.",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "",
    line2: "",
    line3: "",
    line4: ""
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "",
    image: ""
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "Atlase",
  image: "Ievietot att\u0113lu",
  rectangle: "Taisnst\u016Bris",
  diamond: "Rombs",
  ellipse: "Elipse",
  arrow: "Bulta",
  line: "L\u012Bnija",
  freedraw: "Z\u012Bm\u0113t",
  text: "Teksts",
  library: "Bibliot\u0113ka",
  lock: "Patur\u0113t izv\u0113l\u0113to r\u012Bku p\u0113c darb\u012Bbas",
  penMode: "Pildspalvas re\u017E\u012Bms \u2013 nov\u0113rst pieskar\u0161anos",
  link: "Pievienot/redi\u0123\u0113t atlas\u012Bt\u0101s fig\u016Bras saiti",
  eraser: "Dz\u0113\u0161gumija",
  frame: "",
  magicframe: "",
  embeddable: "",
  laser: "",
  hand: "Roka (panoram\u0113\u0161anas r\u012Bks)",
  extraTools: "",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "T\u0101feles darb\u012Bbas",
  selectedShapeActions: "Izv\u0113l\u0113t\u0101s formas darb\u012Bbas",
  shapes: "Formas"
};
var hints = {
  canvasPanning: "Lai b\u012Bd\u012Btu t\u0101feli, turiet nospiestu ritin\u0101\u0161anas vai atstarpes tausti\u0146u, vai izmanto rokas r\u012Bku",
  linearElement: "Klik\u0161\u0137iniet, lai s\u0101ktu z\u012Bm\u0113t vair\u0101kus punktus; velciet, lai z\u012Bm\u0113tu l\u012Bniju",
  freeDraw: "Spiediet un velciet; atlaidiet, kad pabeidzat",
  text: "Ieteikums: lai pievienotu tekstu, varat ar\u012B jebkur dubultklik\u0161\u0137in\u0101t ar atlases r\u012Bku",
  embeddable: "",
  text_selected: "Dubultklik\u0161\u0137iniet vai spiediet ievades tausti\u0146u, lai redi\u0123\u0113tu tekstu",
  text_editing: "Spiediet izie\u0161anas tausti\u0146u vai CtrlOrCmd+ENTER, lai beigtu redi\u0123\u0113t",
  linearElementMulti: "Klik\u0161\u0137iniet uz p\u0113d\u0113j\u0101 punkta vai spiediet izejas vai ievades tausti\u0146u, lai pabeigtu",
  lockAngle: "Varat ierobe\u017Eot le\u0146\u0137i, turot nospiestu SHIFT",
  resize: "Kad main\u0101t izm\u0113ru, varat ierobe\u017Eot proporcijas, turot nospiestu SHIFT,\nvai ar\u012B ALT, lai main\u012Btu izm\u0113ru ap centru",
  resizeImage: "Varat br\u012Bvi main\u012Bt izm\u0113ru, turot nospiestu SHIFT;\nturiet nospiestu ALT, lai main\u012Btu izm\u0113ru ap centru",
  rotate: "Rot\u0113jot varat ierobe\u017Eot le\u0146\u0137i, turot nospiestu SHIFT",
  lineEditor_info: "Turiet CtrlOrCmd un dubultklik\u0161\u0137iniet, vai spiediet CtrlOrCmd + Enter, lai redi\u0123\u0113tu punktus",
  lineEditor_pointSelected: "Spiediet dz\u0113\u0161anas tausti\u0146u, lai no\u0146emtu punktus, \u2013 CtrlOrCmd+D, lai to kop\u0113tu, vai velciet, lai p\u0101rvietotu",
  lineEditor_nothingSelected: "Atlasiet punktu, lai labotu (turiet nospiestu SHIFT, lai atlas\u012Btu vair\u0101kus),\nvai turiet Alt un clik\u0161\u0137iniet, lai pievienotu jaunus punktus",
  placeImage: "Klik\u0161\u0137iniet, lai novietotu att\u0113lu, vai spiediet un velciet, lai iestat\u012Btu t\u0101 izm\u0113ru",
  publishLibrary: "Public\u0113t savu bibliot\u0113ku",
  bindTextToElement: "Spiediet ievades tausti\u0146u, lai pievienotu tekstu",
  deepBoxSelect: "Turient nospiestu Ctrl vai Cmd, lai atlas\u012Btu dzi\u013Cum\u0101 un lai nepie\u013Cautu objektu pavilk\u0161anu",
  eraserRevert: "Turiet Alt, lai no\u0146emtu elementus no dz\u0113s\u0161anas atlases",
  firefox_clipboard_write: '\u0160is iestat\u012Bjums var tikt iesl\u0113gts ar "dom.events.asyncClipboard.clipboardItem" mar\u0137ieri p\u0101rsl\u0113gtu uz "true". Lai main\u012Btu p\u0101rl\u016Bka mar\u0137ierus Firefox, apmekl\u0113 "about:config" lapu.',
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "Nevar r\u0101d\u012Bt priek\u0161skat\u012Bjumu",
  canvasTooBig: "Iesp\u0113jams, t\u0101fele ir par lielu.",
  canvasTooBigTip: "Ieteikums: m\u0113\u0123iniet satuvin\u0101t pa\u0161us t\u0101l\u0101kos elementus."
};
var errorSplash = {
  headingMain: "Notikusi k\u013C\u016Bda. M\u0113\u0123iniet <button>p\u0101rl\u0101d\u0113t lapu.</button>",
  clearCanvasMessage: "Ja p\u0101rl\u0101d\u0113\u0161ana nestr\u0101d\u0101, m\u0113\u0123iniet <button>not\u012Brot t\u0101feli.</button>",
  clearCanvasCaveat: " Tas noved\u012Bs pie darba zaud\u0113\u0161anas ",
  trackedToSentry: "K\u013C\u016Bda ar kodu {{eventId}} tika noteikta m\u016Bsu sist\u0113m\u0101.",
  openIssueMessage: "M\u0113s uzman\u012Bj\u0101mies, lai neiek\u013Cautu j\u016Bsu ainas inform\u0101ciju \u0161aj\u0101 k\u013C\u016Bd\u0101. Ja j\u016Bsu aina nav priv\u0101ta, l\u016Bdzu zi\u0146ojiet par \u0161o k\u013C\u016Bdu m\u016Bsu <button>k\u013C\u016Bdu uzskait\u0113.</button> L\u016Bdzu, miniet sekojo\u0161o inform\u0101ciju to kop\u0113jot un iel\u012Bm\u0113jot j\u016Bsu zi\u0146ojum\u0101 platform\u0101 GitHub.",
  sceneContent: "Ainas saturs:"
};
var roomDialog = {
  desc_intro: "Varat iel\u016Bgt cilv\u0113kus pa\u0161reiz\u0113jaj\u0101 ain\u0101, lai sadarbotos ar tiem.",
  desc_privacy: "Neuztraucieties, sesija izmanto \u0161ifr\u0113\u0161anu no gala l\u012Bdz galam, t\u0101tad j\u016Bsu z\u012Bm\u0113jums paliks priv\u0101ts. Pat m\u016Bsu serveri nevar\u0113s redz\u0113t, ar ko esat n\u0101cis klaj\u0101.",
  button_startSession: "S\u0101kt sesiju",
  button_stopSession: "Beigt sesiju",
  desc_inProgressIntro: "Notiek tie\u0161saistes sadarb\u012Bbas sesija.",
  desc_shareLink: "Dalieties ar \u0161o saiti ar jebkuru, ar ko v\u0113laties sadarboties:",
  desc_exitSession: "Sesijas beig\u0161ana j\u016Bs atvienos no sadarbo\u0161an\u0101s, bet j\u016Bs v\u0113l joproj\u0101m var\u0113siet str\u0101d\u0101t ar ainu sav\u0101 dator\u0101. Iev\u0113rojiet, ka \u0161is neietekm\u0113s citus dal\u012Bbniekus, un vi\u0146i v\u0113l joproj\u0101m var\u0113s sadarboties sav\u0101 ainas versij\u0101.",
  shareTitle: "Pievienoties tie\u0161saistes sadarb\u012Bbai programm\u0101 Excalidraw"
};
var errorDialog = {
  title: "K\u013C\u016Bda"
};
var exportDialog = {
  disk_title: "Saglab\u0101t disk\u0101",
  disk_details: "Eksport\u0113t ainas datus datn\u0113, ko v\u0113l\u0101k var\u0113siet import\u0113t.",
  disk_button: "Saglab\u0101t datn\u0113",
  link_title: "Kop\u012Bgo\u0161anas saite",
  link_details: "Eksport\u0113t k\u0101 tikai las\u0101mu saiti.",
  link_button: "Eksport\u0113t k\u0101 saiti",
  excalidrawplus_description: "Saglab\u0101t ainu sav\u0101 Excalidraw+ darbviet\u0101.",
  excalidrawplus_button: "Eksport\u0113t",
  excalidrawplus_exportError: "Pa\u0161reiz nevar\u0113ja eksport\u0113t uz Excalidraw+..."
};
var helpDialog = {
  blog: "Las\u012Bt m\u016Bsu blogu",
  click: "klik\u0161\u0137is",
  deepSelect: "Atlas\u012Bt dzi\u013Cum\u0101",
  deepBoxSelect: "Atlas\u012Bt dzi\u013Cum\u0101 kastes ietvaros, un nepie\u013Caut pavilk\u0161anu",
  curvedArrow: "Liekta bulta",
  curvedLine: "Liekta l\u012Bnija",
  documentation: "Dokument\u0101cija",
  doubleClick: "dubultklik\u0161\u0137is",
  drag: "vilkt",
  editor: "Redaktors",
  editLineArrowPoints: "Redi\u0123\u0113t l\u012Bniju/bultu punktus",
  editText: "Redi\u0123\u0113t tekstu/pievienot birku",
  github: "Sastap\u0101t k\u013C\u016Bdu? Zi\u0146ot",
  howto: "Sekojiet m\u016Bsu instrukcij\u0101m",
  or: "vai",
  preventBinding: "Nov\u0113rst bultu piesaist\u012B\u0161anos",
  tools: "R\u012Bki",
  shortcuts: "Tastat\u016Bras sa\u012Bsnes",
  textFinish: "Pabeigt redi\u0123\u0113\u0161anu (teksta redaktor\u0101)",
  textNewLine: "N\u0101kam\u0101 rindi\u0146a (teksta redaktor\u0101)",
  title: "Pal\u012Bdz\u012Bba",
  view: "Skat\u012Bt",
  zoomToFit: "Iestat\u012Bt m\u0113rogu, kas iek\u013Cauj visus elementus",
  zoomToSelection: "Iestat\u012Bt m\u0113rogu, lai r\u0101d\u012Btu atlasi",
  toggleElementLock: "Fiks\u0113t/atbr\u012Bvot atlas\u012Bto",
  movePageUpDown: "P\u0101rvietot lapu aug\u0161up/lejup",
  movePageLeftRight: "P\u0101rvietot lapu pa labi/kreisi"
};
var clearCanvasDialog = {
  title: "Not\u012Br\u012Bt t\u0101feli"
};
var publishDialog = {
  title: "Public\u0113t bibliot\u0113ku",
  itemName: "Vienuma nosaukums",
  authorName: "Autora v\u0101rds",
  githubUsername: "GitHub lietot\u0101jv\u0101rds",
  twitterUsername: "Twitter lietot\u0101jv\u0101rds",
  libraryName: "Bibliot\u0113kas nosaukums",
  libraryDesc: "Bibliot\u0113kas apraksts",
  website: "M\u0101jaslapa",
  placeholder: {
    authorName: "J\u016Bsu v\u0101rds vai lietot\u0101jv\u0101rds",
    libraryName: "J\u016Bsu bibliot\u0113kas nosaukums",
    libraryDesc: "Bibliot\u0113kas apraksts, kas pal\u012Bdz\u0113s citiem saprast t\u0101s pielietojumu",
    githubHandle: "GitHub lietot\u0101jv\u0101rds (neoblig\u0101ts), lai j\u016Bs var\u0113tu redi\u0123\u0113t bibliot\u0113ku p\u0113c t\u0101s iesnieg\u0161anas izskat\u012B\u0161anai",
    twitterHandle: "Twitter lietot\u0101jv\u0101rds (neoblig\u0101ts), lai m\u0113s var\u0113tu j\u016Bs piemin\u0113t k\u0101 autoru, kad reklam\u0113sim bibliot\u0113ku platform\u0101 Twitter",
    website: "Saikne uz j\u016Bsu person\u012Bgo m\u0101jaslapu vai k\u0101du citu lapu (neoblig\u0101ta)"
  },
  errors: {
    required: "Oblig\u0101ts",
    website: "Ievadiet der\u012Bgu URL"
  },
  noteDescription: "Iesniegt savu bibliot\u0113ku iek\u013Cau\u0161anai <link>publiskaj\u0101 bibliot\u0113ku datub\u0101z\u0113</link>, lai citi to var\u0113tu izmantot savos z\u012Bm\u0113jumos.",
  noteGuidelines: "\u0160ai bibliot\u0113kai vispirms j\u0101tiek manu\u0101li apstiprin\u0101tai. L\u016Bdzu, izlasiet <link>nor\u0101d\u012Bjumus</link> pirms iesnieg\u0161anas. Jums vajadz\u0113s GitHub kontu, lai sazin\u0101tos un veiktu izmai\u0146as, ja t\u0101das b\u016Bs piepras\u012Btas, bet tas nav absol\u016Bti nepiecie\u0161ams.",
  noteLicense: "Iesniedzot bibliot\u0113ku, j\u016Bs piekr\u012Btat t\u0101s public\u0113\u0161anai saska\u0146\u0101 ar <link>MIT Licenci, </link>kas \u012Bsum\u0101 noz\u012Bm\u0113, ka jebkur\u0161 to var\u0113s izmantot bez ierobe\u017Eojumiem.",
  noteItems: "Katram bibliot\u0113kas vienumam j\u0101b\u016Bt savam nosaukumam, lai to var\u0113tu atrast filtr\u0113jot. Tiks iek\u013Cauti sekojo\u0161ie bibliot\u0113kas vienumi:",
  atleastOneLibItem: "L\u016Bdzu, atlasiet vismaz vienu bibliot\u0113kas vienumu, lai s\u0101ktu darbu",
  republishWarning: "Iev\u0113ro: da\u017Ei no atz\u012Bm\u0113tajiem objektiem jau atz\u012Bm\u0113ti k\u0101 public\u0113ti vai iesniegti public\u0113\u0161anai. Tos vajadz\u0113tu atk\u0101rtoti iesniegt tikai tad, ja v\u0113lies labot eso\u0161o bibliot\u0113ku."
};
var publishSuccessDialog = {
  title: "Bibliot\u0113ka iesniegta",
  content: "Paldies, {{authorName}}! J\u016Bsu bibliot\u0113ka iesniegta izskat\u012B\u0161anai. J\u016Bs varat izsekot iesnieguma statusam<link>\u0161eit</link>"
};
var confirmDialog = {
  resetLibrary: "Atiestat\u012Bt bibliot\u0113ku",
  removeItemsFromLib: "No\u0146emt atlas\u012Btos vienumus no bibliot\u0113kas"
};
var imageExportDialog = {
  header: "",
  label: {
    withBackground: "",
    onlySelected: "",
    darkMode: "",
    embedScene: "",
    scale: "",
    padding: ""
  },
  tooltip: {
    embedScene: ""
  },
  title: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  },
  button: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  }
};
var encrypted = {
  tooltip: "J\u016Bsu z\u012Bm\u0113jumi ir \u0161ifr\u0113ti no gala l\u012Bdz galam; l\u012Bdz ar to Excalidraw serveri tos nekad neredz\u0113s.",
  link: "Ieraksts par \u0161ifr\u0113\u0161anu no gala l\u012Bdz galam Excalidraw blog\u0101"
};
var stats = {
  angle: "Le\u0146\u0137is",
  element: "Elements",
  elements: "Elementi",
  height: "Augstums",
  scene: "Aina",
  selected: "Atlas\u012Bti",
  storage: "Kr\u0101tuve",
  title: "Statistika entuziastiem",
  total: "Kop\u0101",
  version: "Versija",
  versionCopy: "Klik\u0161\u0137iniet, lai nokop\u0113tu",
  versionNotAvailable: "Versija nav pieejama",
  width: "Platums"
};
var toast = {
  addedToLibrary: "Pievienots bibliot\u0113kai",
  copyStyles: "Nokop\u0113ja stilus.",
  copyToClipboard: "Nokop\u0113ja starpliktuv\u0113.",
  copyToClipboardAsPng: "Nokop\u0113ja {{exportSelection}} starpliktuv\u0113 k\u0101 PNG ({{exportColorScheme}})",
  fileSaved: "Datne saglab\u0101ta.",
  fileSavedToFilename: "Saglab\u0101ts k\u0101 {filename}",
  canvas: "t\u0101feli",
  selection: "atlasi",
  pasteAsSingleElement: "Izmantojiet {{shortcut}}, lai iel\u012Bm\u0113tu k\u0101 jaunu elementu, vai iel\u012Bm\u0113tu eso\u0161\u0101 teksta lauci\u0146\u0101",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "Caursp\u012Bd\u012Bgs",
  black: "",
  white: "",
  red: "",
  pink: "",
  grape: "",
  violet: "",
  gray: "",
  blue: "",
  cyan: "",
  teal: "",
  green: "",
  yellow: "",
  orange: "",
  bronze: ""
};
var welcomeScreen = {
  app: {
    center_heading: "Visi j\u016Bsu dati tiek glab\u0101ti uz vietas j\u016Bsu p\u0101rl\u016Bk\u0101.",
    center_heading_plus: "Vai t\u0101 viet\u0101 v\u0113lies doties uz Excalidraw+?",
    menuHint: "Eksport\u0113\u0161ana, iestat\u012Bjumi, valodas..."
  },
  defaults: {
    menuHint: "Eksport\u0113\u0161ana, iestat\u012Bjumi un v\u0113l...",
    center_heading: "Diagrammas. Izveidotas. Vienk\u0101r\u0161i.",
    toolbarHint: "Izv\u0113lies r\u012Bku un s\u0101c z\u012Bm\u0113t!",
    helpHint: "\u012Asce\u013Ci un pal\u012Bdz\u012Bba"
  }
};
var colorPicker = {
  mostUsedCustomColors: "",
  colors: "",
  shades: "",
  hexCode: "",
  noShades: ""
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "",
      button: "",
      description: ""
    },
    saveToDisk: {
      title: "",
      button: "",
      description: ""
    },
    excalidrawPlus: {
      title: "",
      button: "",
      description: ""
    }
  },
  modal: {
    loadFromFile: {
      title: "",
      button: "",
      description: ""
    },
    shareableLink: {
      title: "",
      button: "",
      description: ""
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var lv_LV_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  lv_LV_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=lv-LV-MD7N5VHD.js.map
