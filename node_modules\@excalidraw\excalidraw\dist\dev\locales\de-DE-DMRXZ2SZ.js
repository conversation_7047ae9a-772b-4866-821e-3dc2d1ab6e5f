import "../chunk-XDFCUUT6.js";

// locales/de-DE.json
var labels = {
  paste: "Einf\xFCgen",
  pasteAsPlaintext: "Als reinen Text einf\xFCgen",
  pasteCharts: "Diagramme einf\xFCgen",
  selectAll: "Alle ausw\xE4hlen",
  multiSelect: "Element zur Auswahl hinzuf\xFCgen",
  moveCanvas: "Leinwand verschieben",
  cut: "Ausschneiden",
  copy: "Kopie<PERSON>",
  copyAsPng: "In Zwischenablage kopieren (PNG)",
  copyAsSvg: "In Zwischenablage kopieren (SVG)",
  copyText: "In die Zwischenablage als Text kopieren",
  copySource: "Quelle in Zwischenablage kopieren",
  convertToCode: "In Code konvertieren",
  bringForward: "Nach vorne",
  sendToBack: "In den Hintergrund",
  bringToFront: "In den Vordergrund",
  sendBackward: "Nach hinten",
  delete: "L\xF6schen",
  copyStyles: "Formatierung kopieren",
  pasteStyles: "Formatierung \xFCbernehmen",
  stroke: "<PERSON>rich",
  background: "Hintergrund",
  fill: "F\xFCllung",
  strokeWidth: "Strichst\xE4rke",
  strokeStyle: "Konturstil",
  strokeStyle_solid: "Durchgezogen",
  strokeStyle_dashed: "Gestrichelt",
  strokeStyle_dotted: "Gepunktet",
  sloppiness: "Sauberkeit",
  opacity: "Deckkraft",
  textAlign: "Textausrichtung",
  edges: "Kanten",
  sharp: "Scharf",
  round: "Rund",
  arrowheads: "Pfeilspitzen",
  arrowhead_none: "Keine",
  arrowhead_arrow: "Pfeil",
  arrowhead_bar: "Balken",
  arrowhead_circle: "Kreis",
  arrowhead_circle_outline: "Kreis (Umrandung)",
  arrowhead_triangle: "Dreieck",
  arrowhead_triangle_outline: "Dreieck (Umrandung)",
  arrowhead_diamond: "Raute",
  arrowhead_diamond_outline: "Raute (Umrandung)",
  fontSize: "Schriftgr\xF6\xDFe",
  fontFamily: "Schriftfamilie",
  addWatermark: '"Made with Excalidraw" hinzuf\xFCgen',
  handDrawn: "Handgezeichnet",
  normal: "Normal",
  code: "Code",
  small: "Klein",
  medium: "Mittel",
  large: "Gro\xDF",
  veryLarge: "Sehr gro\xDF",
  solid: "Deckend",
  hachure: "Schraffiert",
  zigzag: "Zickzack",
  crossHatch: "Kreuzschraffiert",
  thin: "D\xFCnn",
  bold: "Fett",
  left: "Links",
  center: "Zentriert",
  right: "Rechts",
  extraBold: "Extra Fett",
  architect: "Architekt",
  artist: "K\xFCnstler",
  cartoonist: "Karikaturist",
  fileTitle: "Dateiname",
  colorPicker: "Farbausw\xE4hler",
  canvasColors: "Auf Leinwand verwendet",
  canvasBackground: "Zeichenfl\xE4chenhintergrund",
  drawingCanvas: "Leinwand",
  layers: "Ebenen",
  actions: "Aktionen",
  language: "Sprache",
  liveCollaboration: "Live-Zusammenarbeit...",
  duplicateSelection: "Duplizieren",
  untitled: "Unbenannt",
  name: "Name",
  yourName: "Dein Name",
  madeWithExcalidraw: "Made with Excalidraw",
  group: "Auswahl gruppieren",
  ungroup: "Gruppierung aufheben",
  collaborators: "Mitarbeitende",
  showGrid: "Raster anzeigen",
  addToLibrary: "Zur Bibliothek hinzuf\xFCgen",
  removeFromLibrary: "Aus Bibliothek entfernen",
  libraryLoadingMessage: "Lade Bibliothek\u2026",
  libraries: "Bibliotheken durchsuchen",
  loadingScene: "Lade Zeichnung\u2026",
  align: "Ausrichten",
  alignTop: "Obere Kanten",
  alignBottom: "Untere Kanten",
  alignLeft: "Linke Kanten",
  alignRight: "Rechte Kanten",
  centerVertically: "Vertikal zentrieren",
  centerHorizontally: "Horizontal zentrieren",
  distributeHorizontally: "Horizontal verteilen",
  distributeVertically: "Vertikal verteilen",
  flipHorizontal: "Horizontal spiegeln",
  flipVertical: "Vertikal spiegeln",
  viewMode: "Ansichtsmodus",
  share: "Teilen",
  showStroke: "Auswahl f\xFCr Strichfarbe anzeigen",
  showBackground: "Hintergrundfarbe ausw\xE4hlen",
  toggleTheme: "Design umschalten",
  personalLib: "Pers\xF6nliche Bibliothek",
  excalidrawLib: "Excalidraw Bibliothek",
  decreaseFontSize: "Schriftgr\xF6\xDFe verkleinern",
  increaseFontSize: "Schrift vergr\xF6\xDFern",
  unbindText: "Text l\xF6sen",
  bindText: "Text an Container binden",
  createContainerFromText: "Text in Container einbetten",
  link: {
    edit: "Link bearbeiten",
    editEmbed: "Link bearbeiten & einbetten",
    create: "Link erstellen",
    createEmbed: "Link erstellen & einbetten",
    label: "Link",
    labelEmbed: "Verlinken & einbetten",
    empty: "Kein Link festgelegt"
  },
  lineEditor: {
    edit: "Linie bearbeiten",
    exit: "Linieneditor verlassen"
  },
  elementLock: {
    lock: "Sperren",
    unlock: "Entsperren",
    lockAll: "Alle sperren",
    unlockAll: "Alle entsperren"
  },
  statusPublished: "Ver\xF6ffentlicht",
  sidebarLock: "Seitenleiste offen lassen",
  selectAllElementsInFrame: "Alle Elemente im Rahmen ausw\xE4hlen",
  removeAllElementsFromFrame: "Alle Elemente aus dem Rahmen entfernen",
  eyeDropper: "Farbe von der Zeichenfl\xE4che ausw\xE4hlen",
  textToDiagram: "Text zu Diagramm",
  prompt: "Eingabe"
};
var library = {
  noItems: "Noch keine Elemente hinzugef\xFCgt...",
  hint_emptyLibrary: "W\xE4hle ein Element auf der Zeichenfl\xE4che, um es hier hinzuzuf\xFCgen. Oder installiere eine Bibliothek aus dem \xF6ffentlichen Verzeichnis.",
  hint_emptyPrivateLibrary: "W\xE4hle ein Element von der Zeichenfl\xE4che, um es hier hinzuzuf\xFCgen."
};
var buttons = {
  clearReset: "Zeichenfl\xE4che l\xF6schen & Hintergrundfarbe zur\xFCcksetzen",
  exportJSON: "In Datei exportieren",
  exportImage: "Exportiere Bild...",
  export: "Speichern als...",
  copyToClipboard: "In Zwischenablage kopieren",
  save: "In aktueller Datei speichern",
  saveAs: "Speichern unter",
  load: "\xD6ffnen",
  getShareableLink: "Teilbaren Link erhalten",
  close: "Schlie\xDFen",
  selectLanguage: "Sprache ausw\xE4hlen",
  scrollBackToContent: "Zur\xFCck zum Inhalt",
  zoomIn: "Vergr\xF6\xDFern",
  zoomOut: "Verkleinern",
  resetZoom: "Zoom zur\xFCcksetzen",
  menu: "Men\xFC",
  done: "Fertig",
  edit: "Bearbeiten",
  undo: "R\xFCckg\xE4ngig machen",
  redo: "Wiederholen",
  resetLibrary: "Bibliothek zur\xFCcksetzen",
  createNewRoom: "Neuen Raum erstellen",
  fullScreen: "Vollbildanzeige",
  darkMode: "Dunkles Design",
  lightMode: "Helles Design",
  zenMode: "Zen-Modus",
  objectsSnapMode: "Einrasten an Objekten",
  exitZenMode: "Zen-Modus verlassen",
  cancel: "Abbrechen",
  clear: "L\xF6schen",
  remove: "Entfernen",
  embed: "Einbettung umschalten",
  publishLibrary: "Ver\xF6ffentlichen",
  submit: "Absenden",
  confirm: "Best\xE4tigen",
  embeddableInteractionButton: "Klicken, um zu interagieren"
};
var alerts = {
  clearReset: "Dies wird die ganze Zeichenfl\xE4che l\xF6schen. Bist du dir sicher?",
  couldNotCreateShareableLink: "Konnte keinen teilbaren Link erstellen.",
  couldNotCreateShareableLinkTooBig: "Konnte keinen teilbaren Link erstellen: Die Zeichnung ist zu gro\xDF",
  couldNotLoadInvalidFile: "Ung\xFCltige Datei konnte nicht geladen werden",
  importBackendFailed: "Import vom Server ist fehlgeschlagen.",
  cannotExportEmptyCanvas: "Leere Zeichenfl\xE4che kann nicht exportiert werden.",
  couldNotCopyToClipboard: "Kopieren in die Zwischenablage fehlgeschlagen.",
  decryptFailed: "Daten konnten nicht entschl\xFCsselt werden.",
  uploadedSecurly: "Der Upload wurde mit Ende-zu-Ende-Verschl\xFCsselung gespeichert. Weder Excalidraw noch Dritte k\xF6nnen den Inhalt einsehen.",
  loadSceneOverridePrompt: "Das Laden einer externen Zeichnung ersetzt den vorhandenen Inhalt. M\xF6chtest du fortfahren?",
  collabStopOverridePrompt: "Das Stoppen der Sitzung wird deine vorherige, lokal gespeicherte Zeichnung \xFCberschreiben. Bist du dir sicher?\n\n(Wenn du deine lokale Zeichnung behalten m\xF6chtest, schlie\xDFe stattdessen den Browser-Tab.)",
  errorAddingToLibrary: "Das Element konnte nicht zur Bibliothek hinzugef\xFCgt werden",
  errorRemovingFromLibrary: "Das Element konnte nicht aus der Bibliothek entfernt werden",
  confirmAddLibrary: "Dies f\xFCgt {{numShapes}} Form(en) zu deiner Bibliothek hinzu. Bist du dir sicher?",
  imageDoesNotContainScene: "Dieses Bild scheint keine Szenendaten zu enthalten. Hast Du das Einbetten der Szene w\xE4hrend des Exports aktiviert?",
  cannotRestoreFromImage: "Die Zeichnung konnte aus dieser Bilddatei nicht wiederhergestellt werden",
  invalidSceneUrl: "Die Szene konnte nicht von der angegebenen URL importiert werden. Sie ist entweder fehlerhaft oder enth\xE4lt keine g\xFCltigen Excalidraw JSON-Daten.",
  resetLibrary: "Dieses l\xF6scht deine Bibliothek. Bist du sicher?",
  removeItemsFromsLibrary: "{{count}} Element(e) aus der Bibliothek l\xF6schen?",
  invalidEncryptionKey: "Verschl\xFCsselungsschl\xFCssel muss 22 Zeichen lang sein. Die Live-Zusammenarbeit ist deaktiviert.",
  collabOfflineWarning: "Keine Internetverbindung verf\xFCgbar.\nDeine \xC4nderungen werden nicht gespeichert!"
};
var errors = {
  unsupportedFileType: "Nicht unterst\xFCtzter Dateityp.",
  imageInsertError: "Das Bild konnte nicht eingef\xFCgt werden. Versuche es sp\xE4ter erneut...",
  fileTooBig: "Die Datei ist zu gro\xDF. Die maximal zul\xE4ssige Gr\xF6\xDFe ist {{maxSize}}.",
  svgImageInsertError: "SVG-Bild konnte nicht eingef\xFCgt werden. Das SVG-Markup sieht ung\xFCltig aus.",
  failedToFetchImage: "Bild konnte nicht abgerufen werden.",
  invalidSVGString: "Ung\xFCltige SVG.",
  cannotResolveCollabServer: "Konnte keine Verbindung zum Collab-Server herstellen. Bitte lade die Seite neu und versuche es erneut.",
  importLibraryError: "Bibliothek konnte nicht geladen werden",
  collabSaveFailed: "Keine Speicherung in der Backend-Datenbank m\xF6glich. Wenn die Probleme weiterhin bestehen, solltest Du Deine Datei lokal speichern, um sicherzustellen, dass Du Deine Arbeit nicht verlierst.",
  collabSaveFailed_sizeExceeded: "Keine Speicherung in der Backend-Datenbank m\xF6glich, die Zeichenfl\xE4che scheint zu gro\xDF zu sein. Du solltest Deine Datei lokal speichern, um sicherzustellen, dass Du Deine Arbeit nicht verlierst.",
  imageToolNotSupported: "Bilder sind deaktiviert.",
  brave_measure_text_error: {
    line1: "Sieht so aus, als ob Du den Brave-Browser verwendest und die <bold>aggressive Blockierung von Fingerabdr\xFCcken</bold> aktiviert hast.",
    line2: "Dies k\xF6nnte dazu f\xFChren, dass die <bold>Textelemente</bold> in Ihren Zeichnungen zerst\xF6rt werden.",
    line3: "Wir empfehlen dringend, diese Einstellung zu deaktivieren. Dazu kannst Du <link>diesen Schritten</link> folgen.",
    line4: "Wenn die Deaktivierung dieser Einstellung die fehlerhafte Anzeige von Textelementen nicht behebt, \xF6ffne bitte ein <issueLink>Ticket</issueLink> auf unserem GitHub oder schreibe uns auf <discordLink>Discord</discordLink>"
  },
  libraryElementTypeError: {
    embeddable: "Einbettbare Elemente k\xF6nnen der Bibliothek nicht hinzugef\xFCgt werden.",
    iframe: "IFrame-Elemente k\xF6nnen nicht zur Bibliothek hinzugef\xFCgt werden.",
    image: "Unterst\xFCtzung f\xFCr das Hinzuf\xFCgen von Bildern in die Bibliothek kommt bald!"
  },
  asyncPasteFailedOnRead: "Einf\xFCgen fehlgeschlagen (konnte aus der Zwischenablage des Systems nicht gelesen werden).",
  asyncPasteFailedOnParse: "Einf\xFCgen fehlgeschlagen.",
  copyToSystemClipboardFailed: "Kopieren in die Zwischenablage fehlgeschlagen."
};
var toolBar = {
  selection: "Auswahl",
  image: "Bild einf\xFCgen",
  rectangle: "Rechteck",
  diamond: "Raute",
  ellipse: "Ellipse",
  arrow: "Pfeil",
  line: "Linie",
  freedraw: "Zeichnen",
  text: "Text",
  library: "Bibliothek",
  lock: "Ausgew\xE4hltes Werkzeug nach Zeichnen aktiv lassen",
  penMode: "Stift-Modus - Ber\xFChrung verhindern",
  link: "Link f\xFCr ausgew\xE4hlte Form hinzuf\xFCgen / aktualisieren",
  eraser: "Radierer",
  frame: "Rahmenwerkzeug",
  magicframe: "Wireframe zu Code",
  embeddable: "Web-Einbettung",
  laser: "Laserpointer",
  hand: "Hand (Schwenkwerkzeug)",
  extraTools: "Weitere Werkzeuge",
  mermaidToExcalidraw: "Mermaid zu Excalidraw",
  magicSettings: "KI-Einstellungen"
};
var headings = {
  canvasActions: "Aktionen f\xFCr Zeichenfl\xE4che",
  selectedShapeActions: "Aktionen f\xFCr Auswahl",
  shapes: "Formen"
};
var hints = {
  canvasPanning: "Um die Zeichenfl\xE4che zu verschieben, halte das Mausrad oder die Leertaste w\xE4hrend des Ziehens, oder verwende das Hand-Werkzeug",
  linearElement: "Klicken f\xFCr Linie mit mehreren Punkten, Ziehen f\xFCr einzelne Linie",
  freeDraw: "Klicke und ziehe. Lass los, wenn du fertig bist",
  text: "Tipp: Du kannst auch Text hinzuf\xFCgen, indem du mit dem Auswahlwerkzeug auf eine beliebige Stelle doppelklickst",
  embeddable: "Klicken und ziehen, um eine Webseiten-Einbettung zu erstellen",
  text_selected: "Doppelklicken oder Eingabetaste dr\xFCcken, um Text zu bearbeiten",
  text_editing: "Dr\xFCcke Escape oder CtrlOrCmd+Eingabetaste, um die Bearbeitung abzuschlie\xDFen",
  linearElementMulti: "Zum Beenden auf den letzten Punkt klicken oder Escape oder Eingabe dr\xFCcken",
  lockAngle: "Du kannst Winkel einschr\xE4nken, indem du SHIFT gedr\xFCckt h\xE4ltst",
  resize: "Du kannst die Proportionen einschr\xE4nken, indem du SHIFT w\xE4hrend der Gr\xF6\xDFen\xE4nderung gedr\xFCckt h\xE4ltst. Halte ALT gedr\xFCckt, um die Gr\xF6\xDFe vom Zentrum aus zu \xE4ndern",
  resizeImage: "Du kannst die Gr\xF6\xDFe frei \xE4ndern, indem du SHIFT gedr\xFCckt h\xE4ltst; halte ALT, um die Gr\xF6\xDFe vom Zentrum aus zu \xE4ndern",
  rotate: "Du kannst Winkel einschr\xE4nken, indem du SHIFT w\xE4hrend der Drehung gedr\xFCckt h\xE4ltst",
  lineEditor_info: "CtrlOrCmd halten und Doppelklick oder CtrlOrCmd + Eingabe dr\xFCcken, um Punkte zu bearbeiten",
  lineEditor_pointSelected: "Dr\xFCcke L\xF6schen, um Punkt(e) zu entfernen, CtrlOrCmd+D zum Duplizieren oder ziehe zum Verschieben",
  lineEditor_nothingSelected: "W\xE4hle einen zu bearbeitenden Punkt (halte SHIFT gedr\xFCckt um mehrere Punkte auszuw\xE4hlen),\noder halte Alt gedr\xFCckt und klicke um neue Punkte hinzuzuf\xFCgen",
  placeImage: "Klicken, um das Bild zu platzieren oder klicken und ziehen um seine Gr\xF6\xDFe manuell zu setzen",
  publishLibrary: "Ver\xF6ffentliche deine eigene Bibliothek",
  bindTextToElement: "Zum Hinzuf\xFCgen Eingabetaste dr\xFCcken",
  deepBoxSelect: "Halte CtrlOrCmd gedr\xFCckt, um innerhalb der Gruppe auszuw\xE4hlen, und um Ziehen zu vermeiden",
  eraserRevert: "Halte Alt gedr\xFCckt, um die zum L\xF6schen markierten Elemente zur\xFCckzusetzen",
  firefox_clipboard_write: 'Diese Funktion kann wahrscheinlich aktiviert werden, indem die Einstellung "dom.events.asyncClipboard.clipboardItem" auf "true" gesetzt wird. Um die Browsereinstellungen in Firefox zu \xE4ndern, besuche die Seite "about:config".',
  disableSnapping: "Halte CtrlOrCmd gedr\xFCckt, um das Einrasten zu deaktivieren"
};
var canvasError = {
  cannotShowPreview: "Vorschau kann nicht angezeigt werden",
  canvasTooBig: "Die Leinwand ist m\xF6glicherweise zu gro\xDF.",
  canvasTooBigTip: "Tipp: Schiebe die am weitesten entfernten Elemente ein wenig n\xE4her zusammen."
};
var errorSplash = {
  headingMain: "Es ist ein Fehler aufgetreten. Versuche <button>die Seite neu zu laden.</button>",
  clearCanvasMessage: "Wenn das Neuladen nicht funktioniert, versuche <button>die Zeichenfl\xE4che zu l\xF6schen.</button>",
  clearCanvasCaveat: " Dies wird zum Verlust von Daten f\xFChren ",
  trackedToSentry: "Der Fehler mit der Kennung {{eventId}} wurde in unserem System registriert.",
  openIssueMessage: "Wir waren sehr vorsichtig und haben deine Zeichnungsinformationen nicht in die Fehlerinformationen aufgenommen. Wenn deine Zeichnung nicht privat ist, unterst\xFCtze uns bitte \xFCber unseren <button>Bug-Tracker</button>. Bitte teile die unten stehenden Informationen mit uns im GitHub Issue (Kopieren und Einf\xFCgen).",
  sceneContent: "Zeichnungsinhalt:"
};
var roomDialog = {
  desc_intro: "Du kannst Leute zu deiner aktuellen Zeichnung einladen um mit ihnen zusammenzuarbeiten.",
  desc_privacy: "Keine Sorge, die Sitzung nutzt eine Ende-zu-Ende-Verschl\xFCsselung. Alles was du zeichnest, bleibt privat. Auch unser Server sieht nicht, was du dir einfallen l\xE4sst.",
  button_startSession: "Sitzung starten",
  button_stopSession: "Sitzung beenden",
  desc_inProgressIntro: "Die Live-Sitzung wird nun ausgef\xFChrt.",
  desc_shareLink: "Teile diesen Link mit allen, mit denen du zusammenarbeiten m\xF6chtest:",
  desc_exitSession: "Wenn du die Sitzung beendest, wird deine Verbindung zum Raum getrennt. Du kannst jedoch lokal weiter an der Zeichnung arbeiten. Beachte, dass dies keine Auswirkungen auf andere hat und diese weiterhin gemeinsam an ihrer Version arbeiten k\xF6nnen.",
  shareTitle: "An einer Live-Kollaborationssitzung auf Excalidraw teilnehmen"
};
var errorDialog = {
  title: "Fehler"
};
var exportDialog = {
  disk_title: "Auf Festplatte speichern",
  disk_details: "Exportiere die Zeichnungsdaten in eine Datei, die Du sp\xE4ter importieren kannst.",
  disk_button: "Als Datei speichern",
  link_title: "Teilbarer Link",
  link_details: "Als schreibgesch\xFCtzten Link exportieren.",
  link_button: "Als Link exportieren",
  excalidrawplus_description: "Speichere die Szene in deinem Excalidraw+ Arbeitsbereich.",
  excalidrawplus_button: "Exportieren",
  excalidrawplus_exportError: "Konnte nicht nach Excalidraw+ exportieren..."
};
var helpDialog = {
  blog: "Lies unseren Blog",
  click: "klicken",
  deepSelect: "Auswahl innerhalb der Gruppe",
  deepBoxSelect: "Auswahl innerhalb der Gruppe, und Ziehen vermeiden",
  curvedArrow: "Gebogener Pfeil",
  curvedLine: "Gebogene Linie",
  documentation: "Dokumentation",
  doubleClick: "doppelklicken",
  drag: "ziehen",
  editor: "Editor",
  editLineArrowPoints: "Linien-/Pfeil-Punkte bearbeiten",
  editText: "Text bearbeiten / Label hinzuf\xFCgen",
  github: "Ein Problem gefunden? Informiere uns",
  howto: "Folge unseren Anleitungen",
  or: "oder",
  preventBinding: "Pfeil-Bindung verhindern",
  tools: "Werkzeuge",
  shortcuts: "Tastaturk\xFCrzel",
  textFinish: "Bearbeitung beenden (Texteditor)",
  textNewLine: "Neue Zeile hinzuf\xFCgen (Texteditor)",
  title: "Hilfe",
  view: "Ansicht",
  zoomToFit: "Zoomen um alle Elemente einzupassen",
  zoomToSelection: "Auf Auswahl zoomen",
  toggleElementLock: "Auswahl sperren/entsperren",
  movePageUpDown: "Seite nach oben/unten verschieben",
  movePageLeftRight: "Seite nach links/rechts verschieben"
};
var clearCanvasDialog = {
  title: "Zeichenfl\xE4che l\xF6schen"
};
var publishDialog = {
  title: "Bibliothek ver\xF6ffentlichen",
  itemName: "Elementname",
  authorName: "Name des Autors",
  githubUsername: "GitHub-Benutzername",
  twitterUsername: "Twitter-Benutzername",
  libraryName: "Name der Bibliothek",
  libraryDesc: "Beschreibung der Bibliothek",
  website: "Webseite",
  placeholder: {
    authorName: "Dein Name oder Benutzername",
    libraryName: "Name deiner Bibliothek",
    libraryDesc: "Beschreibung deiner Bibliothek, um anderen Nutzern bei der Verwendung zu helfen",
    githubHandle: "GitHub-Handle (optional), damit du die Bibliothek bearbeiten kannst, wenn sie zur \xDCberpr\xFCfung eingereicht wurde",
    twitterHandle: "Twitter-Benutzername (optional), damit wir wissen, wen wir bei Werbung \xFCber Twitter nennen k\xF6nnen",
    website: "Link zu deiner pers\xF6nlichen Webseite oder zu anderer Seite (optional)"
  },
  errors: {
    required: "Erforderlich",
    website: "G\xFCltige URL eingeben"
  },
  noteDescription: "Sende deine Bibliothek ein, um in die <link>\xF6ffentliche Bibliotheks-Repository aufgenommen zu werden</link>damit andere Nutzer sie in ihren Zeichnungen verwenden k\xF6nnen.",
  noteGuidelines: "Die Bibliothek muss zuerst manuell freigegeben werden. Bitte lies die <link>Richtlinien</link> vor dem Absenden. Du ben\xF6tigst ein GitHub-Konto, um zu kommunizieren und \xC4nderungen vorzunehmen, falls erforderlich, aber es ist nicht unbedingt erforderlich.",
  noteLicense: "Mit dem Absenden stimmst du zu, dass die Bibliothek unter der <link>MIT-Lizenz, </link>die zusammengefasst beinhaltet, dass jeder sie ohne Einschr\xE4nkungen nutzen kann.",
  noteItems: "Jedes Bibliothekselement muss einen eigenen Namen haben, damit es gefiltert werden kann. Die folgenden Bibliothekselemente werden hinzugef\xFCgt:",
  atleastOneLibItem: "Bitte w\xE4hle mindestens ein Bibliothekselement aus, um zu beginnen",
  republishWarning: "Hinweis: Einige der ausgew\xE4hlten Elemente sind bereits als ver\xF6ffentlicht/eingereicht markiert. Du solltest Elemente nur erneut einreichen, wenn Du eine existierende Bibliothek oder Einreichung aktualisierst."
};
var publishSuccessDialog = {
  title: "Bibliothek \xFCbermittelt",
  content: "Vielen Dank {{authorName}}. Deine Bibliothek wurde zur \xDCberpr\xFCfung eingereicht. Du kannst den Status verfolgen<link>hier</link>"
};
var confirmDialog = {
  resetLibrary: "Bibliothek zur\xFCcksetzen",
  removeItemsFromLib: "Ausgew\xE4hlte Elemente aus der Bibliothek entfernen"
};
var imageExportDialog = {
  header: "Bild exportieren",
  label: {
    withBackground: "Hintergrund",
    onlySelected: "Nur ausgew\xE4hlte",
    darkMode: "Dunkler Modus",
    embedScene: "Szene einbetten",
    scale: "Skalierung",
    padding: "Abstand"
  },
  tooltip: {
    embedScene: "Die Zeichnungsdaten werden in der exportierten PNG/SVG-Datei gespeichert, sodass das Dokument sp\xE4ter weiter bearbeitet werden kann. \nDieses wird die exportierte Datei vergr\xF6\xDFern."
  },
  title: {
    exportToPng: "Als PNG exportieren",
    exportToSvg: "Als SVG exportieren",
    copyPngToClipboard: "PNG in die Zwischenablage kopieren"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "In Zwischenablage kopieren"
  }
};
var encrypted = {
  tooltip: "Da deine Zeichnungen Ende-zu-Ende verschl\xFCsselt werden, sehen auch unsere Excalidraw-Server sie niemals.",
  link: "Blogbeitrag \xFCber Ende-zu-Ende-Verschl\xFCsselung in Excalidraw"
};
var stats = {
  angle: "Winkel",
  element: "Element",
  elements: "Elemente",
  height: "H\xF6he",
  scene: "Zeichnung",
  selected: "Ausgew\xE4hlt",
  storage: "Speicher",
  title: "Statistiken f\xFCr Nerds",
  total: "Gesamt",
  version: "Version",
  versionCopy: "Zum Kopieren klicken",
  versionNotAvailable: "Version nicht verf\xFCgbar",
  width: "Breite"
};
var toast = {
  addedToLibrary: "Zur Bibliothek hinzugef\xFCgt",
  copyStyles: "Formatierungen kopiert.",
  copyToClipboard: "In die Zwischenablage kopiert.",
  copyToClipboardAsPng: "{{exportSelection}} als PNG in die Zwischenablage kopiert\n({{exportColorScheme}})",
  fileSaved: "Datei gespeichert.",
  fileSavedToFilename: "Als {filename} gespeichert",
  canvas: "Zeichenfl\xE4che",
  selection: "Auswahl",
  pasteAsSingleElement: "Verwende {{shortcut}} , um als einzelnes Element\neinzuf\xFCgen oder in einen existierenden Texteditor einzuf\xFCgen",
  unableToEmbed: "Einbetten dieser URL ist derzeit nicht zul\xE4ssig. Erstelle einen Issue auf GitHub, um die URL freigeben zu lassen",
  unrecognizedLinkFormat: "Der Link, den Du eingebettet hast, stimmt nicht mit dem erwarteten Format \xFCberein. Bitte versuche den 'embed' String einzuf\xFCgen, der von der Quellseite zur Verf\xFCgung gestellt wird"
};
var colors = {
  transparent: "Transparent",
  black: "Schwarz",
  white: "Wei\xDF",
  red: "Rot",
  pink: "Pink",
  grape: "Traube",
  violet: "Violett",
  gray: "Grau",
  blue: "Blau",
  cyan: "Cyan",
  teal: "Blaugr\xFCn",
  green: "Gr\xFCn",
  yellow: "Gelb",
  orange: "Orange",
  bronze: "Bronze"
};
var welcomeScreen = {
  app: {
    center_heading: "Alle Daten werden lokal in Deinem Browser gespeichert.",
    center_heading_plus: "M\xF6chtest du stattdessen zu Excalidraw+ gehen?",
    menuHint: "Exportieren, Einstellungen, Sprachen, ..."
  },
  defaults: {
    menuHint: "Exportieren, Einstellungen und mehr...",
    center_heading: "Diagramme. Einfach. Gemacht.",
    toolbarHint: "W\xE4hle ein Werkzeug & beginne zu zeichnen!",
    helpHint: "Kurzbefehle & Hilfe"
  }
};
var colorPicker = {
  mostUsedCustomColors: "Beliebteste benutzerdefinierte Farben",
  colors: "Farben",
  shades: "Schattierungen",
  hexCode: "Hex-Code",
  noShades: "Keine Schattierungen f\xFCr diese Farbe verf\xFCgbar"
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "Als Bild exportieren",
      button: "Als Bild exportieren",
      description: "Exportiere die Zeichnungsdaten als ein Bild, von dem Du sp\xE4ter importieren kannst."
    },
    saveToDisk: {
      title: "Auf Festplatte speichern",
      button: "Auf Festplatte speichern",
      description: "Exportiere die Zeichnungsdaten in eine Datei, von der Du sp\xE4ter importieren kannst."
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "Export nach Excalidraw+",
      description: "Speichere die Szene in deinem Excalidraw+-Arbeitsbereich."
    }
  },
  modal: {
    loadFromFile: {
      title: "Aus Datei laden",
      button: "Aus Datei laden",
      description: "Das Laden aus einer Datei wird <bold>Deinen vorhandenen Inhalt ersetzen</bold>.<br></br>Du kannst Deine Zeichnung zuerst mit einer der folgenden Optionen sichern."
    },
    shareableLink: {
      title: "Aus Link laden",
      button: "Meinen Inhalt ersetzen",
      description: "Das Laden einer externen Zeichnung wird <bold>Deinen vorhandenen Inhalt ersetzen</bold>.<br></br>Du kannst Deine Zeichnung zuerst mit einer der folgenden Optionen sichern."
    }
  }
};
var mermaid = {
  title: "Mermaid zu Excalidraw",
  button: "Einf\xFCgen",
  description: "Derzeit werden nur <flowchartLink>Flussdiagramme</flowchartLink>, <sequenceLink>Sequenzdiagramme</sequenceLink> und <classLink>Klassendiagramme</classLink> unterst\xFCtzt. Die anderen Typen werden als Bild in Excalidraw dargestellt.",
  syntax: "Mermaid-Syntax",
  preview: "Vorschau"
};
var de_DE_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  de_DE_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=de-DE-DMRXZ2SZ.js.map
