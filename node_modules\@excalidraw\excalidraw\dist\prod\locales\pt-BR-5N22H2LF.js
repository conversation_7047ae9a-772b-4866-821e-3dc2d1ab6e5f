import"../chunk-SRAX5OIU.js";var o={paste:"Colar",pasteAsPlaintext:"Colar como texto sem formata\xE7\xE3o",pasteCharts:"Colar gr\xE1ficos",selectAll:"Selecionar tudo",multiSelect:"Adicionar elemento \xE0 sele\xE7\xE3o",moveCanvas:"Mover tela",cut:"Recortar",copy:"Copiar",copyAsPng:"Copiar para a \xE1rea de transfer\xEAncia como PNG",copyAsSvg:"Copiar para a \xE1rea de transfer\xEAncia como SVG",copyText:"Copiar para \xE1rea de transfer\xEAncia como texto",copySource:"",convertToCode:"",bringForward:"Trazer para a frente",sendToBack:"Enviar para o fundo",bringToFront:"Trazer para o primeiro plano",sendBackward:"Enviar para tr\xE1s",delete:"Apagar",copyStyles:"Copiar os estilos",pasteStyles:"Colar os estilos",stroke:"Contorno",background:"Fundo",fill:"Preenchimento",strokeWidth:"Espessura do tra\xE7o",strokeStyle:"Estilo de tra\xE7o",strokeStyle_solid:"S\xF3lido",strokeStyle_dashed:"Tracejado",strokeStyle_dotted:"Pontilhado",sloppiness:"Precis\xE3o do tra\xE7o",opacity:"Opacidade",textAlign:"Alinhamento do texto",edges:"Arestas",sharp:"Pontudo",round:"Arredondado",arrowheads:"Pontas",arrowhead_none:"Nenhuma",arrowhead_arrow:"Flecha",arrowhead_bar:"Barra",arrowhead_circle:"",arrowhead_circle_outline:"",arrowhead_triangle:"Tri\xE2ngulo",arrowhead_triangle_outline:"",arrowhead_diamond:"",arrowhead_diamond_outline:"",fontSize:"Tamanho da fonte",fontFamily:"Fam\xEDlia da fonte",addWatermark:'Adicionar "Feito com Excalidraw"',handDrawn:"Manuscrito",normal:"Normal",code:"C\xF3digo",small:"Pequeno",medium:"M\xE9dio",large:"Grande",veryLarge:"Muito grande",solid:"S\xF3lido",hachure:"Hachura",zigzag:"Zigue-zague",crossHatch:"Hachura cruzada",thin:"Fino",bold:"Espesso",left:"Esquerda",center:"Centralizar",right:"Direita",extraBold:"Muito espesso",architect:"Arquiteto",artist:"Artista",cartoonist:"Cartunista",fileTitle:"Nome do arquivo",colorPicker:"Seletor de cores",canvasColors:"Usado na tela",canvasBackground:"Fundo da tela",drawingCanvas:"Tela de desenho",layers:"Camadas",actions:"A\xE7\xF5es",language:"Idioma",liveCollaboration:"Colabora\xE7\xE3o ao vivo...",duplicateSelection:"Duplicar",untitled:"Sem t\xEDtulo",name:"Nome",yourName:"Seu nome",madeWithExcalidraw:"Feito com Excalidraw",group:"Agrupar sele\xE7\xE3o",ungroup:"Desagrupar sele\xE7\xE3o",collaborators:"Colaboradores",showGrid:"Mostrar grade",addToLibrary:"Adicionar \xE0 biblioteca",removeFromLibrary:"Remover da biblioteca",libraryLoadingMessage:"Carregando biblioteca\u2026",libraries:"Procurar bibliotecas",loadingScene:"Carregando cena\u2026",align:"Alinhamento",alignTop:"Alinhar ao topo",alignBottom:"Alinhar embaixo",alignLeft:"Alinhar \xE0 esquerda",alignRight:"Alinhar \xE0 direita",centerVertically:"Centralizar verticalmente",centerHorizontally:"Centralizar horizontalmente",distributeHorizontally:"Distribuir horizontalmente",distributeVertically:"Distribuir verticalmente",flipHorizontal:"Inverter horizontalmente",flipVertical:"Inverter verticalmente",viewMode:"Modo de visualiza\xE7\xE3o",share:"Compartilhar",showStroke:"Exibir seletor de cores do tra\xE7o",showBackground:"Exibir seletor de cores do fundo",toggleTheme:"Alternar tema",personalLib:"Biblioteca Pessoal",excalidrawLib:"Biblioteca do Excalidraw",decreaseFontSize:"Diminuir o tamanho da fonte",increaseFontSize:"Aumentar o tamanho da fonte",unbindText:"Desvincular texto",bindText:"Vincular texto ao cont\xEAiner",createContainerFromText:"Envolver texto em um cont\xEAiner",link:{edit:"Editar link",editEmbed:"",create:"Criar link",createEmbed:"",label:"Link",labelEmbed:"",empty:""},lineEditor:{edit:"Editar linha",exit:"Sair do editor de linha"},elementLock:{lock:"Bloquear",unlock:"Desbloquear",lockAll:"Bloquear tudo",unlockAll:"Desbloquear tudo"},statusPublished:"Publicado",sidebarLock:"Manter barra lateral aberta",selectAllElementsInFrame:"Selecionar todos os elementos no quadro",removeAllElementsFromFrame:"Remover todos os elementos do quadro",eyeDropper:"Escolher cor da tela",textToDiagram:"",prompt:""},r={noItems:"Nenhum item adicionado ainda...",hint_emptyLibrary:"Selecione um item na tela para adicion\xE1-lo aqui, ou instale uma biblioteca do reposit\xF3rio p\xFAblico, abaixo.",hint_emptyPrivateLibrary:"Selecione um item na tela para adicion\xE1-lo aqui."},i={clearReset:"Limpar o canvas e redefinir a cor de fundo",exportJSON:"Exportar arquivo",exportImage:"Exportar imagem...",export:"Salvar como...",copyToClipboard:"Copiar para o clipboard",save:"Salvar para o arquivo atual",saveAs:"Salvar como",load:"Abrir",getShareableLink:"Obter um link de compartilhamento",close:"Fechar",selectLanguage:"Selecionar idioma",scrollBackToContent:"Voltar para o conte\xFAdo",zoomIn:"Aumentar zoom",zoomOut:"Diminuir zoom",resetZoom:"Redefinir zoom",menu:"Menu",done:"Conclu\xEDdo",edit:"Editar",undo:"Desfazer",redo:"Refazer",resetLibrary:"Redefinir biblioteca",createNewRoom:"Criar nova sala",fullScreen:"Tela cheia",darkMode:"Modo escuro",lightMode:"Modo claro",zenMode:"Modo Zen",objectsSnapMode:"",exitZenMode:"Sair do modo zen",cancel:"Cancelar",clear:"Limpar",remove:"Remover",embed:"",publishLibrary:"Publicar",submit:"Enviar",confirm:"Confirmar",embeddableInteractionButton:""},t={clearReset:"Isto ir\xE1 limpar toda a tela. Voc\xEA tem certeza?",couldNotCreateShareableLink:"N\xE3o foi poss\xEDvel criar um link de compartilhamento.",couldNotCreateShareableLinkTooBig:"N\xE3o foi poss\xEDvel criar um link compartilh\xE1vel: a cena \xE9 muito grande",couldNotLoadInvalidFile:"N\xE3o foi poss\xEDvel carregar o arquivo inv\xE1lido",importBackendFailed:"A importa\xE7\xE3o do servidor falhou.",cannotExportEmptyCanvas:"N\xE3o \xE9 poss\xEDvel exportar um canvas vazio.",couldNotCopyToClipboard:"N\xE3o foi poss\xEDvel copiar para a \xE1rea de transfer\xEAncia.",decryptFailed:"N\xE3o foi poss\xEDvel descriptografar os dados.",uploadedSecurly:"O upload foi protegido com criptografia de ponta a ponta, o que significa que o servidor do Excalidraw e terceiros n\xE3o podem ler o conte\xFAdo.",loadSceneOverridePrompt:"Carregar um desenho externo substituir\xE1 o seu conte\xFAdo existente. Deseja continuar?",collabStopOverridePrompt:`Ao interromper a sess\xE3o, voc\xEA substituir\xE1 seu desenho anterior, armazenado localmente. Voc\xEA tem certeza?

(Se voc\xEA deseja manter seu desenho local, simplesmente feche a aba do navegador.)`,errorAddingToLibrary:"N\xE3o foi poss\xEDvel adicionar o item \xE0 biblioteca",errorRemovingFromLibrary:"N\xE3o foi poss\xEDvel remover o item da biblioteca",confirmAddLibrary:"Isso adicionar\xE1 {{numShapes}} forma(s) \xE0 sua biblioteca. Tem certeza?",imageDoesNotContainScene:"Esta imagem parece n\xE3o conter dados de cenas. Voc\xEA ativou a incorpora\xE7\xE3o da cena durante a exporta\xE7\xE3o?",cannotRestoreFromImage:"N\xE3o foi poss\xEDvel restaurar a cena deste arquivo de imagem",invalidSceneUrl:"N\xE3o foi poss\xEDvel importar a cena da URL fornecida. Ela est\xE1 incompleta ou n\xE3o cont\xE9m dados JSON v\xE1lidos do Excalidraw.",resetLibrary:"Isto limpar\xE1 a sua biblioteca. Voc\xEA tem certeza?",removeItemsFromsLibrary:"Excluir {{count}} item(ns) da biblioteca?",invalidEncryptionKey:"A chave de encripta\xE7\xE3o deve ter 22 caracteres. A colabora\xE7\xE3o ao vivo est\xE1 desabilitada.",collabOfflineWarning:`Sem conex\xE3o com a internet dispon\xEDvel.
Suas altera\xE7\xF5es n\xE3o ser\xE3o salvas!`},n={unsupportedFileType:"Tipo de arquivo n\xE3o suportado.",imageInsertError:"N\xE3o foi poss\xEDvel inserir imagem. Tente novamente mais tarde...",fileTooBig:"O arquivo \xE9 muito grande. O tamanho m\xE1ximo permitido \xE9 {{maxSize}}.",svgImageInsertError:"N\xE3o foi poss\xEDvel inserir a imagem SVG. A marca\xE7\xE3o SVG parece inv\xE1lida.",failedToFetchImage:"",invalidSVGString:"SVG Inv\xE1lido.",cannotResolveCollabServer:"N\xE3o foi poss\xEDvel conectar-se ao servidor colaborativo. Por favor, recarregue a p\xE1gina e tente novamente.",importLibraryError:"N\xE3o foi poss\xEDvel carregar a biblioteca",collabSaveFailed:"N\xE3o foi poss\xEDvel salvar no banco de dados do servidor. Se os problemas persistirem, salve o arquivo localmente para garantir que n\xE3o perca o seu trabalho.",collabSaveFailed_sizeExceeded:"N\xE3o foi poss\xEDvel salvar no banco de dados do servidor, a tela parece ser muito grande. Se os problemas persistirem, salve o arquivo localmente para garantir que n\xE3o perca o seu trabalho.",imageToolNotSupported:"",brave_measure_text_error:{line1:"Parece que voc\xEA est\xE1 usando o navegador Brave com a configura\xE7\xE3o <bold>Bloquear Impress\xF5es Digitais</bold> no modo agressivo.",line2:"Isso pode acabar quebrando <bold>Elementos de Texto</bold> em seus desenhos.",line3:"Recomendamos fortemente desativar essa configura\xE7\xE3o. Voc\xEA pode acessar o <link>passo a passo</link> sobre como fazer isso.",line4:"Se desativar essa configura\xE7\xE3o n\xE3o corrigir a exibi\xE7\xE3o de elementos de texto, por favor abra uma <issueLink>issue</issueLink> em nosso GitHub, ou mande uma mensagem em nosso <discordLink>Discord</discordLink>"},libraryElementTypeError:{embeddable:"",iframe:"",image:""},asyncPasteFailedOnRead:"",asyncPasteFailedOnParse:"",copyToSystemClipboardFailed:""},s={selection:"Sele\xE7\xE3o",image:"Inserir imagem",rectangle:"Ret\xE2ngulo",diamond:"Losango",ellipse:"Elipse",arrow:"Flecha",line:"Linha",freedraw:"Desenhar",text:"Texto",library:"Biblioteca",lock:"Manter ativa a ferramenta selecionada ap\xF3s desenhar",penMode:"Modo caneta \u2014 impede o toque",link:"Adicionar/Atualizar link para uma forma selecionada",eraser:"Borracha",frame:"Ferramenta de quadro",magicframe:"",embeddable:"",laser:"",hand:"M\xE3o (ferramenta de rolagem)",extraTools:"Mais ferramentas",mermaidToExcalidraw:"",magicSettings:""},l={canvasActions:"A\xE7\xF5es da tela",selectedShapeActions:"A\xE7\xF5es das formas selecionadas",shapes:"Formas"},d={canvasPanning:"Para mover a tela, segure a roda do mouse ou a barra de espa\xE7o enquanto arrasta ou use a ferramenta de m\xE3o",linearElement:"Clique para iniciar v\xE1rios pontos, arraste para uma \xFAnica linha",freeDraw:"Toque e arraste, solte quando terminar",text:"Dica: voc\xEA tamb\xE9m pode adicionar texto clicando duas vezes em qualquer lugar com a ferramenta de sele\xE7\xE3o",embeddable:"",text_selected:"Clique duplo ou tecle ENTER para editar o texto",text_editing:"Pressione Esc ou Ctrl/Cmd+ENTER para encerrar a edi\xE7\xE3o",linearElementMulti:"Clique no \xFAltimo ponto ou pressione Escape ou Enter para terminar",lockAngle:"Voc\xEA pode restringir o \xE2ngulo segurando o SHIFT",resize:`Voc\xEA pode restringir propor\xE7\xF5es segurando SHIFT enquanto redimensiona,
segure ALT para redimensionar do centro`,resizeImage:`Voc\xEA pode redimensionar livremente segurando SHIFT,
segure ALT para redimensionar a partir do centro`,rotate:"Voc\xEA pode restringir os \xE2ngulos segurando SHIFT enquanto gira",lineEditor_info:"Pressione CtrlOuCmd e duplo-clique ou pressione CtrlOuCmd + Enter para editar pontos",lineEditor_pointSelected:`Pressione Delete para remover o(s) ponto(s),
Ctrl/Cmd+D para duplicar ou arraste para mover`,lineEditor_nothingSelected:"Selecione um ponto para editar (segure SHIFT para selecionar v\xE1rios) ou segure Alt e clique para adicionar novos pontos",placeImage:"Clique para colocar a imagem, ou clique e arraste para definir manualmente o seu tamanho",publishLibrary:"Publicar sua pr\xF3pria biblioteca",bindTextToElement:"Pressione Enter para adicionar o texto",deepBoxSelect:"Segure Ctrl/Cmd para sele\xE7\xE3o profunda e para evitar arrastar",eraserRevert:"Segure a tecla Alt para inverter os elementos marcados para exclus\xE3o",firefox_clipboard_write:'Esse recurso pode ser ativado configurando a op\xE7\xE3o "dom.events.asyncClipboard.clipboardItem" como "true". Para alterar os sinalizadores do navegador no Firefox, visite a p\xE1gina "about:config".',disableSnapping:""},c={cannotShowPreview:"N\xE3o \xE9 poss\xEDvel mostrar pr\xE9-visualiza\xE7\xE3o",canvasTooBig:"A tela pode ser muito grande.",canvasTooBigTip:"Dica: tente aproximar um pouco os elementos mais distantes."},m={headingMain:"Foi encontrado um erro. Tente <button>recarregar a p\xE1gina.</button>",clearCanvasMessage:"Se recarregar a p\xE1gina n\xE3o funcionar, tente <button>limpando a tela.</button>",clearCanvasCaveat:" Isso resultar\xE1 em perda de trabalho ",trackedToSentry:"O erro com o identificador {{eventId}} foi rastreado no nosso sistema.",openIssueMessage:"Fomos muito cautelosos para n\xE3o incluir suas informa\xE7\xF5es de cena no erro. Se sua cena n\xE3o for privada, por favor, considere seguir nosso <button>rastreador de bugs.</button> Por favor, inclua informa\xE7\xF5es abaixo, copiando e colando para a issue do GitHub.",sceneContent:"Conte\xFAdo da cena:"},u={desc_intro:"Voc\xEA pode convidar pessoas para sua cena atual para colaborar com voc\xEA.",desc_privacy:"N\xE3o se preocupe, a sess\xE3o usa criptografia de ponta a ponta; portanto, o que voc\xEA desenhar permanecer\xE1 privado. Nem mesmo nosso servidor poder\xE1 ver o que voc\xEA cria.",button_startSession:"Iniciar sess\xE3o",button_stopSession:"Parar sess\xE3o",desc_inProgressIntro:"A sess\xE3o de colabora\xE7\xE3o ao vivo est\xE1 agora em andamento.",desc_shareLink:"Compartilhe este link com qualquer pessoa com quem voc\xEA queira colaborar:",desc_exitSession:"Interrompendo a sess\xE3o voc\xEA ir\xE1 se desconectar da sala, mas voc\xEA poder\xE1 continuar trabalhando com a cena localmente. Observe que isso n\xE3o afetar\xE1 outras pessoas, e elas ainda poder\xE3o colaborar em suas vers\xF5es.",shareTitle:"Participe de uma sess\xE3o ao vivo de colabora\xE7\xE3o no Excalidraw"},p={title:"Erro"},b={disk_title:"Salvar no computador",disk_details:"Exportar os dados da cena para um arquivo que voc\xEA poder\xE1 importar mais tarde.",disk_button:"Salvar em um arquivo",link_title:"Link compartilh\xE1vel",link_details:"Exportar como link de apenas leitura.",link_button:"Exportar link",excalidrawplus_description:"Salvar a cena na sua \xE1rea de trabalho Excalidraw+.",excalidrawplus_button:"Exportar",excalidrawplus_exportError:"N\xE3o \xE9 poss\xEDvel exportar para o Excalidraw+ neste momento..."},v={blog:"Leia o nosso blog",click:"clicar",deepSelect:"Sele\xE7\xE3o profunda",deepBoxSelect:"Use a sele\xE7\xE3o profunda dentro da caixa para previnir arrastar",curvedArrow:"Seta curva",curvedLine:"Linha curva",documentation:"Documenta\xE7\xE3o",doubleClick:"clique duplo",drag:"arrastar",editor:"Editor",editLineArrowPoints:"Editar linha/ponta da seta",editText:"Editar texto / adicionar etiqueta",github:"Encontrou algum problema? Nos informe",howto:"Siga nossos guias",or:"ou",preventBinding:"Evitar fixa\xE7\xE3o de seta",tools:"Ferramentas",shortcuts:"Atalhos de teclado",textFinish:"Encerrar edi\xE7\xE3o (editor de texto)",textNewLine:"Adicionar nova linha (editor de texto)",title:"Ajudar",view:"Visualizar",zoomToFit:"Ampliar para encaixar todos os elementos",zoomToSelection:"Ampliar a sele\xE7\xE3o",toggleElementLock:"Bloquear/desbloquear sele\xE7\xE3o",movePageUpDown:"Mover a p\xE1gina para cima/baixo",movePageLeftRight:"Mover a p\xE1gina para esquerda/direita"},g={title:"Limpar a tela"},h={title:"Publicar biblioteca",itemName:"Nome do item",authorName:"Nome do autor",githubUsername:"Nome de usu\xE1rio do GitHub",twitterUsername:"Nome de usu\xE1rio do Twitter",libraryName:"Nome da Biblioteca",libraryDesc:"Descri\xE7\xE3o da biblioteca",website:"Site",placeholder:{authorName:"Seu nome ou nome de usu\xE1rio",libraryName:"Nome da sua biblioteca",libraryDesc:"Descri\xE7\xE3o para ajudar as pessoas a entenderem o uso da sua da sua biblioteca",githubHandle:"Identificador do GitHub (opcional), para que voc\xEA possa editar a biblioteca depois de enviar para revis\xE3o",twitterHandle:"Nome de usu\xE1rio do Twitter (opcional), para que saibamos quem deve ser creditado se promovermos no Twitter",website:"Link para o seu site pessoal ou outro lugar (opcional)"},errors:{required:"Obrigat\xF3rio",website:"Informe uma URL v\xE1lida"},noteDescription:"Envie sua biblioteca para ser inclu\xEDda no <link>reposit\xF3rio de biblioteca p\xFAblica</link>para outras pessoas usarem em seus desenhos.",noteGuidelines:"A biblioteca precisa ser aprovada manualmente primeiro. Por favor leia o <link>orienta\xE7\xF5es</link> antes de enviar. Voc\xEA precisar\xE1 de uma conta do GitHub para se comunicar e fazer altera\xE7\xF5es quando solicitado, mas n\xE3o \xE9 estritamente necess\xE1rio.",noteLicense:"Ao enviar, voc\xEA concorda que a biblioteca ser\xE1 publicada sob a <link>Licen\xE7a MIT, </link>o que, em suma, significa que qualquer pessoa pode utiliz\xE1-los sem restri\xE7\xF5es.",noteItems:"Cada item da biblioteca deve ter seu pr\xF3prio nome para que seja filtr\xE1vel. Os seguintes itens da biblioteca ser\xE3o inclu\xEDdos:",atleastOneLibItem:"Por favor, selecione pelo menos um item da biblioteca para come\xE7ar",republishWarning:"Nota: alguns dos itens selecionados est\xE3o marcados como j\xE1 publicado/enviado. Voc\xEA s\xF3 deve reenviar itens ao atualizar uma biblioteca existente ou submiss\xE3o."},x={title:"Biblioteca enviada",content:"Obrigado {{authorName}}. Sua biblioteca foi enviada para an\xE1lise. Voc\xEA pode acompanhar o status<link>aqui</link>"},f={resetLibrary:"Redefinir biblioteca",removeItemsFromLib:"Remover itens selecionados da biblioteca"},S={header:"Exportar imagem",label:{withBackground:"Fundo",onlySelected:"Somente selecionados",darkMode:"Modo escuro",embedScene:"Incorporar cena",scale:"Escala",padding:"Margem interna"},tooltip:{embedScene:`Os dados da cena ser\xE3o salvos no arquivo PNG/SVG exportado para que a cena possa ser restaurada a partir dele.
Isso aumentar\xE1 o tamanho do arquivo exportado.`},title:{exportToPng:"Exportar como PNG",exportToSvg:"Exportar como SVG",copyPngToClipboard:"Copiar PNG para \xE1rea de transfer\xEAncia"},button:{exportToPng:"PNG",exportToSvg:"SVG",copyPngToClipboard:"Copiar para a \xE1rea de transfer\xEAncia"}},E={tooltip:"Seus desenhos s\xE3o criptografados de ponta a ponta, ent\xE3o os servidores do Excalidraw nunca os ver\xE3o.",link:"Publica\xE7\xE3o de blog com criptografia de ponta a ponta no Excalidraw"},C={angle:"\xC2ngulo",element:"Elemento",elements:"Elementos",height:"Altura",scene:"Cena",selected:"Selecionado",storage:"Armazenamento",title:"Estat\xEDsticas para nerds",total:"Total",version:"Vers\xE3o",versionCopy:"Clique para copiar",versionNotAvailable:"Vers\xE3o n\xE3o dispon\xEDvel",width:"Largura"},q={addedToLibrary:"Adicionado \xE0 biblioteca",copyStyles:"Estilos copiados.",copyToClipboard:"Copiado para \xE1rea de transfer\xEAncia.",copyToClipboardAsPng:"{{exportSelection}} copiado para a \xE1rea de transfer\xEAncia como PNG ({{exportColorScheme}})",fileSaved:"Arquivo salvo.",fileSavedToFilename:"Salvo em {filename}",canvas:"tela",selection:"sele\xE7\xE3o",pasteAsSingleElement:`Use {{shortcut}} para colar como um \xFAnico elemento,
ou cole em um editor de texto j\xE1 existente`,unableToEmbed:"",unrecognizedLinkFormat:""},T={transparent:"Transparente",black:"Preto",white:"Branco",red:"Vermelho",pink:"Rosa",grape:"Uva",violet:"Violeta",gray:"Cinza",blue:"Azul",cyan:"Ciano",teal:"Verde-azulado",green:"Verde",yellow:"Amarelo",orange:"Laranja",bronze:"Bronze"},k={app:{center_heading:"Todos os dados s\xE3o salvos localmente no seu navegador.",center_heading_plus:"Voc\xEA queria ir para o Excalidraw+ em vez disso?",menuHint:"Exportar, prefer\xEAncias, idiomas..."},defaults:{menuHint:"Exportar, prefer\xEAncias e mais...",center_heading:"Diagramas, Feito. Simples.",toolbarHint:"Escolha uma ferramenta e comece a desenhar!",helpHint:"Atalhos e ajuda"}},y={mostUsedCustomColors:"Cores personalizadas mais usadas",colors:"Cores",shades:"Tons",hexCode:"C\xF3digo hexadecimal",noShades:"Sem tons dispon\xEDveis para essa cor"},A={action:{exportToImage:{title:"Exportar como imagem",button:"Exportar como imagem",description:"Exportar os dados da cena para um arquivo que voc\xEA poder\xE1 importar mais tarde."},saveToDisk:{title:"Salvar no computador",button:"Salvar no computador",description:"Exportar os dados da cena para um arquivo que voc\xEA poder\xE1 importar mais tarde."},excalidrawPlus:{title:"Excalidraw+",button:"Exportar para Excalidraw+",description:"Salvar a cena na sua \xE1rea de trabalho Excalidraw+."}},modal:{loadFromFile:{title:"Carregar de arquivo",button:"Carregar de arquivo",description:"Carregar de um arquivo ir\xE1 <bold> substituir o conte\xFAdo existente</bold>.<br></br>Voc\xEA pode salvar seu desenho primeiro usando uma das op\xE7\xF5es abaixo."},shareableLink:{title:"Carregar de um link",button:"Substituir meu conte\xFAdo",description:"Carregar um desenho externo ir\xE1 <bold> substituir seu conte\xFAdo existente</bold>.<br></br>Voc\xEA pode salvar seu desenho antes utilizando uma das op\xE7\xF5es abaixo."}}},w={title:"",button:"",description:"",syntax:"",preview:""},N={labels:o,library:r,buttons:i,alerts:t,errors:n,toolBar:s,headings:l,hints:d,canvasError:c,errorSplash:m,roomDialog:u,errorDialog:p,exportDialog:b,helpDialog:v,clearCanvasDialog:g,publishDialog:h,publishSuccessDialog:x,confirmDialog:f,imageExportDialog:S,encrypted:E,stats:C,toast:q,colors:T,welcomeScreen:k,colorPicker:y,overwriteConfirm:A,mermaid:w};export{t as alerts,i as buttons,c as canvasError,g as clearCanvasDialog,y as colorPicker,T as colors,f as confirmDialog,N as default,E as encrypted,p as errorDialog,m as errorSplash,n as errors,b as exportDialog,l as headings,v as helpDialog,d as hints,S as imageExportDialog,o as labels,r as library,w as mermaid,A as overwriteConfirm,h as publishDialog,x as publishSuccessDialog,u as roomDialog,C as stats,q as toast,s as toolBar,k as welcomeScreen};
