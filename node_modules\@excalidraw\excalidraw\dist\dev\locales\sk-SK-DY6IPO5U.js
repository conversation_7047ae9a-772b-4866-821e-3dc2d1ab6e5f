import "../chunk-XDFCUUT6.js";

// locales/sk-SK.json
var labels = {
  paste: "Vlo\u017Ei\u0165",
  pasteAsPlaintext: "Vlo\u017Ei\u0165 ako oby\u010Dajn\xFD text",
  pasteCharts: "Vlo\u017Ei\u0165 grafy",
  selectAll: "Vybra\u0165 v\u0161etko",
  multiSelect: "Prida\u0165 prvok do v\xFDberu",
  moveCanvas: "Pohyb pl\xE1tna",
  cut: "Vystrihn\xFA\u0165",
  copy: "Kop\xEDrova\u0165",
  copyAsPng: "Kop\xEDrova\u0165 do schr\xE1nky ako PNG",
  copyAsSvg: "Kop\xEDrova\u0165 do schr\xE1nky ako SVG",
  copyText: "Kop\xEDrova\u0165 do schr\xE1nky ako text",
  copySource: "Kop\xEDrova\u0165 k\xF3d do schr\xE1nky",
  convertToCode: "Konvertova\u0165 na k\xF3d",
  bringForward: "Presun\xFA\u0165 o \xFArove\u0148 dopredu",
  sendToBack: "Presun\xFA\u0165 dozadu",
  bringToFront: "Presun\xFA\u0165 dopredu",
  sendBackward: "Presun\xFA\u0165 o \xFArove\u0148 dozadu",
  delete: "Vymaza\u0165",
  copyStyles: "Kop\xEDrova\u0165 \u0161t\xFDly",
  pasteStyles: "Vlo\u017Ei\u0165 \u0161t\xFDly",
  stroke: "Obrys",
  background: "Pozadie",
  fill: "V\xFDpl\u0148",
  strokeWidth: "Hr\xFAbka obrysu",
  strokeStyle: "\u0160t\xFDl obrysu",
  strokeStyle_solid: "Pln\xFD",
  strokeStyle_dashed: "\u010Ciarkovan\xFD",
  strokeStyle_dotted: "Bodkovan\xFD",
  sloppiness: "\u0160tyliz\xE1cia",
  opacity: "Prieh\u013Eadnos\u0165",
  textAlign: "Zarovnanie textu",
  edges: "Okraje",
  sharp: "Ostr\xE9",
  round: "Zaokr\xFAhlen\xE9",
  arrowheads: "Zakon\u010Denie \u0161\xEDpky",
  arrowhead_none: "\u017Diadne",
  arrowhead_arrow: "\u0160\xEDpka",
  arrowhead_bar: "\u010Ciara",
  arrowhead_circle: "Kruh",
  arrowhead_circle_outline: "Kruh (obrys)",
  arrowhead_triangle: "Trojuholn\xEDk",
  arrowhead_triangle_outline: "Trojuholn\xEDk (obrys)",
  arrowhead_diamond: "Diamant",
  arrowhead_diamond_outline: "Diamant (obrys)",
  fontSize: "Ve\u013Ekos\u0165 p\xEDsma",
  fontFamily: "P\xEDsmo",
  addWatermark: 'Prida\u0165 "Vytvoren\xE9 s Excalidraw"',
  handDrawn: "Ru\u010Dne p\xEDsan\xE9",
  normal: "Norm\xE1lne",
  code: "K\xF3d",
  small: "Mal\xE9",
  medium: "Stredn\xE9",
  large: "Ve\u013Ek\xE9",
  veryLarge: "Ve\u013Emi ve\u013Ek\xE9",
  solid: "Pln\xE1",
  hachure: "\u0160rafovan\xE1",
  zigzag: "Cik-cak",
  crossHatch: "Mrie\u017Ekovan\xE1",
  thin: "Tenk\xE1",
  bold: "Hrub\xE1",
  left: "Do\u013Eava",
  center: "Na stred",
  right: "Doprava",
  extraBold: "Ve\u013Emi hrub\xE1",
  architect: "Architekt",
  artist: "Umelec",
  cartoonist: "Ilustr\xE1tor",
  fileTitle: "N\xE1zov s\xFAboru",
  colorPicker: "V\xFDber farby",
  canvasColors: "Pou\u017Eit\xE9 na pl\xE1tne",
  canvasBackground: "Pozadie pl\xE1tna",
  drawingCanvas: "Kresliace pl\xE1tno",
  layers: "Vrstvy",
  actions: "Akcie",
  language: "Jazyk",
  liveCollaboration: "\u017Div\xE1 spolupr\xE1ca...",
  duplicateSelection: "Duplikova\u0165",
  untitled: "Bez n\xE1zvu",
  name: "Meno",
  yourName: "Va\u0161e meno",
  madeWithExcalidraw: "Vytvoren\xE9 s Excalidraw",
  group: "Zoskupi\u0165",
  ungroup: "Zru\u0161i\u0165 zoskupenie",
  collaborators: "Spolupracovn\xEDci",
  showGrid: "Zobrazi\u0165 mrie\u017Eku",
  addToLibrary: "Prida\u0165 do kni\u017Enice",
  removeFromLibrary: "Odstr\xE1ni\u0165 z kni\u017Enice",
  libraryLoadingMessage: "Na\u010D\xEDtavanie kni\u017Enice\u2026",
  libraries: "Prehliada\u0165 kni\u017Enice",
  loadingScene: "Na\u010D\xEDtavanie sc\xE9ny\u2026",
  align: "Zarovnanie",
  alignTop: "Zarovna\u0165 nahor",
  alignBottom: "Zarovna\u0165 nadol",
  alignLeft: "Zarovna\u0165 do\u013Eava",
  alignRight: "Zarovna\u0165 doprava",
  centerVertically: "Zarovna\u0165 zvislo na stred",
  centerHorizontally: "Zarovna\u0165 vodorovne na stred",
  distributeHorizontally: "Rozmiestni\u0165 vodorovne",
  distributeVertically: "Rozmiestni\u0165 zvisle",
  flipHorizontal: "Prevr\xE1ti\u0165 vodorovne",
  flipVertical: "Prevr\xE1ti\u0165 zvislo",
  viewMode: "Re\u017Eim zobrazenia",
  share: "Zdie\u013Ea\u0165",
  showStroke: "Zobrazi\u0165 v\xFDber farby pre obrys",
  showBackground: "Zobrazi\u0165 v\xFDber farby pre pozadie",
  toggleTheme: "Prepn\xFA\u0165 t\xE9mu",
  personalLib: "Moja kni\u017Enica",
  excalidrawLib: "Excalidraw kni\u017Enica",
  decreaseFontSize: "Zmen\u0161i\u0165 ve\u013Ekos\u0165 p\xEDsma",
  increaseFontSize: "Zv\xE4\u010D\u0161i\u0165 ve\u013Ekos\u0165 p\xEDsma",
  unbindText: "Zru\u0161i\u0165 previazanie textu",
  bindText: "Previaza\u0165 text s kontajnerom",
  createContainerFromText: "Zabali\u0165 text do kontajneru",
  link: {
    edit: "Upravi\u0165 odkaz",
    editEmbed: "Editova\u0165 a zapusti\u0165 odkaz",
    create: "Vytvori\u0165 odkaz",
    createEmbed: "Vytvori\u0165 a zapusti\u0165 odkaz",
    label: "Odkaz",
    labelEmbed: "Zapusti\u0165 odkaz",
    empty: "Nie je nastaven\xFD \u017Eiaden odkaz"
  },
  lineEditor: {
    edit: "Upravi\u0165 \u010Diaru",
    exit: "Ukon\u010Di\u0165 editovanie \u010Diary"
  },
  elementLock: {
    lock: "Zamkn\xFA\u0165",
    unlock: "Odomkn\xFA\u0165",
    lockAll: "Zamkn\xFA\u0165 v\u0161etko",
    unlockAll: "Odomkn\xFA\u0165 v\u0161etko"
  },
  statusPublished: "Zverejnen\xE9",
  sidebarLock: "Necha\u0165 bo\u010Dn\xFD panel otvoren\xFD",
  selectAllElementsInFrame: "Vybra\u0165 v\u0161etky prvky v r\xE1me",
  removeAllElementsFromFrame: "Odstr\xE1ni\u0165 v\u0161etky prvky z r\xE1mu",
  eyeDropper: "Vybra\u0165 farbu z pl\xE1tna",
  textToDiagram: "Text na diagram",
  prompt: "In\u0161trukcia"
};
var library = {
  noItems: "Zatia\u013E neboli pridan\xE9 \u017Eiadne polo\u017Eky...",
  hint_emptyLibrary: "Vyberte polo\u017Eku z pl\xE1tna pre jej pridanie do kni\u017Enice alebo pou\u017Eite kni\u017Enicu z verejn\xE9ho zoznamu kni\u017En\xEDc ni\u017E\u0161ie.",
  hint_emptyPrivateLibrary: "Vyberte polo\u017Eku z pl\xE1tna pre jej pridanie do kni\u017Enice."
};
var buttons = {
  clearReset: "Obnovi\u0165 pl\xE1tno",
  exportJSON: "Exportova\u0165 do s\xFAboru",
  exportImage: "Exportova\u0165 obr\xE1zok...",
  export: "Ulo\u017Ei\u0165 do...",
  copyToClipboard: "Kop\xEDrova\u0165 do schr\xE1nky",
  save: "Ulo\u017Ei\u0165 do aktu\xE1lneho s\xFAboru",
  saveAs: "Ulo\u017Ei\u0165 ako",
  load: "Otvori\u0165",
  getShareableLink: "Z\xEDska\u0165 odkaz na zdie\u013Eanie",
  close: "Zavrie\u0165",
  selectLanguage: "Zvoli\u0165 jazyk",
  scrollBackToContent: "Vr\xE1ti\u0165 sa sp\xE4\u0165 na obsah",
  zoomIn: "Pribl\xED\u017Ei\u0165",
  zoomOut: "Oddiali\u0165",
  resetZoom: "Obnovi\u0165 pribl\xED\u017Eenie",
  menu: "Ponuka",
  done: "Hotovo",
  edit: "Upravi\u0165",
  undo: "Sp\xE4\u0165",
  redo: "Znova",
  resetLibrary: "Obnovi\u0165 kni\u017Enicu",
  createNewRoom: "Vytvori\u0165 nov\xFA miestnos\u0165",
  fullScreen: "Cel\xE1 obrazovka",
  darkMode: "Tmav\xFD re\u017Eim",
  lightMode: "Svetl\xFD re\u017Eim",
  zenMode: "Re\u017Eim zen",
  objectsSnapMode: "Prichyti\u0165 k objektom",
  exitZenMode: "Zru\u0161i\u0165 re\u017Eim zen",
  cancel: "Zru\u0161i\u0165",
  clear: "Vymaza\u0165",
  remove: "Odstr\xE1ni\u0165",
  embed: "Prepn\xFA\u0165 zapustenie",
  publishLibrary: "Uverejni\u0165",
  submit: "Potvrdi\u0165",
  confirm: "Potvrdi\u0165",
  embeddableInteractionButton: "Kliknite pre interakciu"
};
var alerts = {
  clearReset: "T\xFDmto sa vy\u010Dist\xED cel\xE9 pl\xE1tno. Ste si ist\xED?",
  couldNotCreateShareableLink: "Nepodarilo sa vytvori\u0165 odkaz na zdie\u013Eanie.",
  couldNotCreateShareableLinkTooBig: "Nepodarilo sa vytvori\u0165 odkaz na zdie\u013Eanie: sc\xE9na je pr\xEDli\u0161 ve\u013Ek\xE1",
  couldNotLoadInvalidFile: "Nepodarilo sa na\u010D\xEDta\u0165 nevalidn\xFD s\xFAbor",
  importBackendFailed: "Nepdarilo sa importovanie zo serveru.",
  cannotExportEmptyCanvas: "Nie je mo\u017En\xE9 exportova\u0165 pr\xE1zdne pl\xE1tno.",
  couldNotCopyToClipboard: "Kop\xEDrovanie do schr\xE1nky sa nepodarilo.",
  decryptFailed: "Nepodarilo sa roz\u0161ifrova\u0165 \xFAdaje.",
  uploadedSecurly: "Nahratie je zabezpe\u010Den\xE9 end-to-end \u0161ifrovan\xEDm, tak\u017Ee Excalidraw server a tretie strany nedok\xE1\u017Eu pre\u010D\xEDta\u0165 jeho obsah.",
  loadSceneOverridePrompt: "Nahratie externej kresby nahrad\xED existuj\xFAci obsah. Prajete si pokra\u010Dova\u0165?",
  collabStopOverridePrompt: "Ukon\u010Denie sch\xF4dze nahrad\xED va\u0161u predch\xE1dzaj\xFAcu lok\xE1lne ulo\u017Een\xFA sc\xE9nu. Ste si ist\xFD?\n\n(Ak si chcete ponecha\u0165 lok\xE1lnu sc\xE9nu, jednoducho iba zavrite kartu prehliada\u010Da.)",
  errorAddingToLibrary: "Nepodarilo sa prida\u0165 polo\u017Eku do kni\u017Enice",
  errorRemovingFromLibrary: "Nepodarilo sa odstr\xE1ni\u0165 polo\u017Eku z kni\u017Enice",
  confirmAddLibrary: "T\xFDmto sa prid\xE1 {{numShapes}} tvar(ov) do va\u0161ej kni\u017Enice. Ste si ist\xED?",
  imageDoesNotContainScene: "Tento obr\xE1zok neobsahuje \u017Eiadne \xFAdaje sc\xE9ny. Zvolili ste mo\u017Enos\u0165 zahrn\xFA\u0165 sc\xE9nu po\u010Das exportu?",
  cannotRestoreFromImage: "Nepodarilo sa obnovi\u0165 sc\xE9nu z tohto obr\xE1zkov\xE9ho s\xFAboru",
  invalidSceneUrl: "Nepodarilo sa na\u010D\xEDta\u0165 sc\xE9nu z poskytnutej URL. Je nevalidn\xE1 alebo neobsahuje \u017Eiadne validn\xE9 Excalidraw JSON d\xE1ta.",
  resetLibrary: "T\xFDmto vypr\xE1zdnite va\u0161u kni\u017Enicu. Ste si ist\xFD?",
  removeItemsFromsLibrary: "Odstr\xE1ni\u0165 {{count}} polo\u017Eiek z kni\u017Enice?",
  invalidEncryptionKey: "\u0160ifrovac\xED k\u013E\xFA\u010D mus\xED ma\u0165 22 znakov. \u017Div\xE1 spolupr\xE1ca je vypnut\xE1.",
  collabOfflineWarning: "Internetov\xE9 pripojenie nie je dostupn\xE9.\nVa\u0161e zmeny nebud\xFA ulo\u017Een\xE9!"
};
var errors = {
  unsupportedFileType: "Nepodporovan\xFD typ s\xFAboru.",
  imageInsertError: "Nepodarilo sa vlo\u017Ei\u0165 obr\xE1zok. Sk\xFAste to znova nesk\xF4r...",
  fileTooBig: "S\xFAbor je pr\xEDli\u0161 ve\u013Ek\xFD. Maxim\xE1lna povolen\xE1 ve\u013Ekos\u0165 je {{maxSize}}.",
  svgImageInsertError: "Nepodarilo sa vlo\u017Ei\u0165 SVG obr\xE1zok. SVG form\xE1t je pravdepodobne nevalidn\xFD.",
  failedToFetchImage: "Na\u010D\xEDtanie obr\xE1zka zlyhalo.",
  invalidSVGString: "Nevalidn\xE9 SVG.",
  cannotResolveCollabServer: "Nepodarilo sa pripoji\u0165 ku kolabora\u010Dn\xE9mu serveru. Pros\xEDm obnovte str\xE1nku a sk\xFAste to znovu.",
  importLibraryError: "Nepodarilo sa na\u010D\xEDta\u0165 kni\u017Enicu",
  collabSaveFailed: "Ulo\u017Eenie do datab\xE1zy sa nepodarilo. Ak tento probl\xE9m pretrv\xE1va ulo\u017Ete si v\xE1\u0161 s\xFAbor lok\xE1lne aby ste nestratili va\u0161u pr\xE1cu.",
  collabSaveFailed_sizeExceeded: "Ulo\u017Eenie do datab\xE1zy sa nepodarilo, preto\u017Ee ve\u013Ekos\u0165 pl\xE1tna je pr\xEDli\u0161 ve\u013Ek\xE1. Ulo\u017Ete si v\xE1\u0161 s\xFAbor lok\xE1lne aby ste nestratili va\u0161u pr\xE1cu.",
  imageToolNotSupported: "Obr\xE1zky s\xFA vypnut\xE9.",
  brave_measure_text_error: {
    line1: "Vyzer\xE1 to, \u017Ee pou\u017E\xEDvate prehliada\u010D Brave so zapnut\xFDm nastaven\xEDm pre <bold>agres\xEDvne blokovanie</bold>.",
    line2: "To m\xF4\u017Ee sp\xF4sobi\u0165 nespr\xE1vne zobrazenie <bold>textov\xFDch prvkov</bold> vo va\u0161ej kresbe.",
    line3: "D\xF4razne odpor\xFA\u010Dame vypnutie toho nastavenia. M\xF4\u017Eete tak spravi\u0165 vykonan\xEDm <link>t\xFDchto krokov</link>.",
    line4: "Ak vypnutie toho nastavenia nevyrie\u0161i probl\xE9m so zobrazen\xEDm textov\xFDch prvkov, pros\xEDm ohl\xE1ste <issueLink>probl\xE9m</issueLink> na na\u0161om GitHub-e alebo n\xE1m nap\xED\u0161te na n\xE1\u0161 <discordLink>Discord</discordLink>"
  },
  libraryElementTypeError: {
    embeddable: "Zapusten\xE9 prvky nie je mo\u017En\xE9 prida\u0165 do kni\u017Enice.",
    iframe: "Vlo\u017Een\xE9 r\xE1mce IFrame nie je mo\u017En\xE9 prida\u0165 do kni\u017Enice.",
    image: "Podpora pre prid\xE1vanie obr\xE1zkov do kni\u017Enice bude dostupn\xE1 u\u017E \u010Doskoro!"
  },
  asyncPasteFailedOnRead: "Vlo\u017Eenie sa nepodarilo (nebolo mo\u017En\xE9 pre\u010D\xEDta\u0165 obsah schr\xE1nky).",
  asyncPasteFailedOnParse: "Vlo\u017Eenie sa nepodarilo.",
  copyToSystemClipboardFailed: "Kop\xEDrovanie do schr\xE1nky sa nepodarilo."
};
var toolBar = {
  selection: "V\xFDber",
  image: "Vlo\u017Ei\u0165 obr\xE1zok",
  rectangle: "Obd\u013A\u017Enik",
  diamond: "Diamant",
  ellipse: "Elipsa",
  arrow: "\u0160\xEDpka",
  line: "\u010Ciara",
  freedraw: "Kresli\u0165",
  text: "Text",
  library: "Kni\u017Enica",
  lock: "Necha\u0165 zvolen\xFD n\xE1stroj akt\xEDvny po skon\u010Den\xED kreslenia",
  penMode: "Re\u017Eim pera \u2013 zabr\xE1ni\u0165 dotyku",
  link: "Prida\u0165/ Upravi\u0165 odkaz pre vybran\xFD tvar",
  eraser: "Guma",
  frame: "N\xE1stroj r\xE1m",
  magicframe: "Dr\xF4ten\xFD model na k\xF3d",
  embeddable: "Web Embed",
  laser: "Laserov\xFD ukazovate\u013E",
  hand: "Ruka (n\xE1stroj pre pohyb pl\xE1tna)",
  extraTools: "\u010Eal\u0161ie n\xE1stroje",
  mermaidToExcalidraw: "Mermaid do Excalidraw",
  magicSettings: "AI nastavenia"
};
var headings = {
  canvasActions: "Akcie pl\xE1tna",
  selectedShapeActions: "Akcie tvarov z v\xFDberu",
  shapes: "Tvary"
};
var hints = {
  canvasPanning: "Pre pohyb pl\xE1tna podr\u017Ete koliesko my\u0161i alebo medzern\xEDk po\u010Das \u0165ahania, alebo pou\u017Eite n\xE1stroj ruka",
  linearElement: "Kliknite na vlo\u017Eenie viacer\xFDch bodov, potiahnite na vytvorenie jednej priamky",
  freeDraw: "Kliknite a \u0165ahajte, pustite na ukon\u010Denie",
  text: "Tip: text m\xF4\u017Eete prida\u0165 aj dvojklikom kdeko\u013Evek, ak je zvolen\xFD n\xE1stroj v\xFDber",
  embeddable: "Kliknite a \u0165ahajte pre zapustenie webovej str\xE1nky",
  text_selected: "Pou\u017Eite dvojklik alebo stla\u010Dte Enter na edit\xE1ciu textu",
  text_editing: "Stla\u010Dte Escape alebo CtrlOrCmd+ENTER na ukon\u010Denie editovania",
  linearElementMulti: "Kliknite na po\u010Diato\u010Dn\xFD bod alebo stla\u010Dte Escape alebo Enter na ukon\u010Denie",
  lockAngle: "Po\u010Das rot\xE1cie obmedz\xEDte uhol podr\u017Ean\xEDm SHIFT",
  resize: "Po\u010Das zmeny ve\u013Ekosti zachov\xE1te proporcie podr\u017Ean\xEDm SHIFT,\\npodr\u017Ean\xEDm ALT men\xEDte ve\u013Ekos\u0165 so zachovan\xEDm stredu",
  resizeImage: "Podr\u017Ete SHIFT pre vo\u013En\xFA zmenu ve\u013Ekosti, podr\u017Ete ALT pre zmenu ve\u013Ekosti od stredu",
  rotate: "Po\u010Das rot\xE1cie obmedz\xEDte uhol podr\u017Ean\xEDm SHIFT",
  lineEditor_info: "Podr\u017Ete CtrlOrCmd a kliknite dva kr\xE1t alebo stla\u010Dte CtrlOrCmd + Enter pre edit\xE1ciu bodov",
  lineEditor_pointSelected: "Sta\u010Dte Delete na vymazanie bodu (bodov), CtrlOrCmd+D na duplikovanie, alebo potiahnite na presunutie",
  lineEditor_nothingSelected: "Zvo\u013Ete bod na upravovanie (podr\u017Ete SHIFT pre zvolenie viacer\xFDch bodov) alebo podr\u017Ete Alt a kliknite na pridanie nov\xE9ho bodu",
  placeImage: "Kliknite pre umiestnenie obr\xE1zka alebo kliknite a \u0165ahajte pre zmenu jeho ve\u013Ekosti",
  publishLibrary: "Uverejni\u0165 va\u0161u kni\u017Enicu",
  bindTextToElement: "Stla\u010Dte enter na pridanie textu",
  deepBoxSelect: "Podr\u017Ete CtrlOrCmd na v\xFDber v skupine alebo zamedzeniu po\u0165iahnutia",
  eraserRevert: "Podr\u017Ete Alt pre prehodenie polo\u017Eiek ur\u010Den\xFDch na vymazanie",
  firefox_clipboard_write: 'T\xE1to sa funkcionalita sa d\xE1 zapn\xFA\u0165 nastaven\xEDm "dom.events.asyncClipboard.clipboardItem" na "true". Pre zmenu nastaven\xED vo Firefox-e otvorte str\xE1nku "about:config".',
  disableSnapping: "Podr\u017Ete CtrlOrCmd pre vypnutie prichyt\xE1vania"
};
var canvasError = {
  cannotShowPreview: "Nie je mo\u017En\xE9 zobrazi\u0165 n\xE1h\u013Ead pl\xE1tna",
  canvasTooBig: "Pl\xE1tno je mo\u017Eno pr\xEDli\u0161 ve\u013Ek\xE9.",
  canvasTooBigTip: "Tip: sk\xFAste presun\xFA\u0165 najvzdialenej\u0161ie prvky bli\u017E\u0161ie k sebe."
};
var errorSplash = {
  headingMain: "Nastala chyba. Vysk\xFA\u0161ajte <button>obnovi\u0165 str\xE1nku.</button>",
  clearCanvasMessage: "Ak obnovenie str\xE1nky nepom\xE1ha, vysk\xFA\u0161ajte <button>vy\u010Disti\u0165 pl\xE1tno.</button>",
  clearCanvasCaveat: " To bude ma\u0165 za n\xE1sledok stratu pr\xE1ce ",
  trackedToSentry: "Chyba s identifik\xE1torom {{eventId}} bola zaznamenan\xE1 v na\u0161om syst\xE9me.",
  openIssueMessage: "Boli sme ve\u013Emi opatrn\xED, aby inform\xE1cie va\u0161ej sc\xE9ny neboli v chybe zaznamenan\xE9. Ak va\u0161a sc\xE9na nie je s\xFAkromn\xE1, pros\xEDm zv\xE1\u017Ete pokra\u010Dovanie na na\u0161e <button>hl\xE1senie ch\xFDb.</button> Pros\xEDm zahr\u0148te inform\xE1cie ni\u017E\u0161ie pomocou kop\xEDrovania a prilepenia do GitHub issue.",
  sceneContent: "Obsah sc\xE9ny:"
};
var roomDialog = {
  desc_intro: "Pozvite niekoho do svojej aktu\xE1lnej sc\xE9ny a pracujte spolo\u010Dne.",
  desc_privacy: "Nemajte obavy, sch\xF4dza pou\u017E\xEDva end-to-end \u0161ifrovanie, tak\u017Ee v\u0161etko \u010Do nakresl\xEDte je s\xFAkromn\xE9. Dokonca, ani n\xE1\u0161 server dedok\xE1\u017Ee pre\u010D\xEDta\u0165, \u010Do ste vytvorili.",
  button_startSession: "Za\u010Da\u0165 sch\xF4dzu",
  button_stopSession: "Ukon\u010Di\u0165 sch\xF4dzu",
  desc_inProgressIntro: "Pr\xE1ve prebieha \u017Eiv\xE1 sch\xF4dza.",
  desc_shareLink: "Zdie\u013Eajte tento odkaz s osobou, s ktorou chcete spolupracova\u0165:",
  desc_exitSession: "Ukon\u010Denie sch\xF4dze v\xE1s odpoj\xED z miestnosti, av\u0161ak na\u010Falej budete m\xF4c\u0165 pokra\u010Dova\u0165 v pr\xE1ci na sc\xE9ne lok\xE1lne. Toto neovplyvn\xED ostatn\xFDch spolupracovn\xEDkov a st\xE1le bud\xFA m\xF4c\u0165 spolupracova\u0165 na ich verzii.",
  shareTitle: "Pripoji\u0165 sa k \u017Eivej sch\xF4dzi na Excalidraw"
};
var errorDialog = {
  title: "Chyba"
};
var exportDialog = {
  disk_title: "Ulo\u017Ei\u0165 na disk",
  disk_details: "Exportova\u0165 \xFAdaje sc\xE9ny do s\xFAboru, z ktor\xE9ho m\xF4\u017Eu by\u0165 nesk\xF4r importovan\xE9.",
  disk_button: "Ulo\u017Ei\u0165 do s\xFAboru",
  link_title: "Odkaz na zdie\u013Eanie",
  link_details: "Exportova\u0165 ako odkaz iba na \u010D\xEDtanie.",
  link_button: "Exportova\u0165 ako odkaz",
  excalidrawplus_description: "Ulo\u017Ei\u0165 sc\xE9nu do v\xE1\u0161ho Excalidraw+ pracovn\xE9ho priestoru.",
  excalidrawplus_button: "Exportova\u0165",
  excalidrawplus_exportError: "Nepodarilo sa vykona\u0165 export do Excalidraw+..."
};
var helpDialog = {
  blog: "Pre\u010D\xEDtajte si n\xE1\u0161 blog",
  click: "kliknutie",
  deepSelect: "V\xFDber v skupine",
  deepBoxSelect: "V\xFDber v skupine alebo zamedzenie po\u0165iahnutia",
  curvedArrow: "Zakriven\xE1 \u0161\xEDpka",
  curvedLine: "Zakriven\xE1 \u010Diara",
  documentation: "Dokument\xE1cia",
  doubleClick: "dvojklik",
  drag: "potiahnutie",
  editor: "Editovanie",
  editLineArrowPoints: "Edit\xE1cia bodov \u010Diary/\u0161\xEDpky",
  editText: "Edit\xE1cia textu / pridanie \u0161t\xEDtku",
  github: "Objavili ste probl\xE9m? Nahl\xE1ste ho",
  howto: "Postupujte pod\u013Ea na\u0161\xEDch n\xE1vodov",
  or: "alebo",
  preventBinding: "Zak\xE1za\u0165 prip\xE1janie \u0161\xEDpky",
  tools: "N\xE1stroje",
  shortcuts: "Kl\xE1vesov\xE9 skratky",
  textFinish: "Ukon\u010Denie editovania (text editor)",
  textNewLine: "Vlo\u017Ei\u0165 nov\xFD riadok (text editor)",
  title: "Pomocn\xEDk",
  view: "Zobrazenie",
  zoomToFit: "Pribl\xED\u017Ei\u0165 aby boli zahrnut\xE9 v\u0161etky prvky",
  zoomToSelection: "Pribl\xED\u017Ei\u0165 na v\xFDber",
  toggleElementLock: "Zamkn\xFA\u0165/odomkn\xFA\u0165 vybran\xE9",
  movePageUpDown: "Posun\xFA\u0165 stranu hore/dole",
  movePageLeftRight: "Posun\xFA\u0165 stranu do\u013Eava/doprava"
};
var clearCanvasDialog = {
  title: "Vy\u010Disti\u0165 pl\xE1tno"
};
var publishDialog = {
  title: "Uverejni\u0165 kni\u017Enicu",
  itemName: "N\xE1zov polo\u017Eky",
  authorName: "Meno autora",
  githubUsername: "Github u\u017E\xEDvate\u013Esk\xE9 meno",
  twitterUsername: "Twitter u\u017E\xEDvate\u013Esk\xE9 meno",
  libraryName: "N\xE1zov kni\u017Enice",
  libraryDesc: "Popis kni\u017Enice",
  website: "Webov\xE1 str\xE1nka",
  placeholder: {
    authorName: "Va\u0161e meno alebo u\u017E\xEDvate\u013Esk\xE9 meno",
    libraryName: "N\xE1zov va\u0161ej kni\u017Enice",
    libraryDesc: "Popis va\u0161ej kni\u017Enice, ktor\xFD ostatn\xFDm pom\xF4\u017Ee porozumie\u0165 jej vhodn\xE9mu pou\u017Eitiu",
    githubHandle: "GitHub u\u017E\xEDvate\u013Esk\xE9 meno (nepovinn\xE9), aby ste mohli robi\u0165 \xFApravy po tom, \u010Do bude kni\u017Enica uverejnen\xE1 na schv\xE1lenie",
    twitterHandle: "Twitter u\u017E\xEDvate\u013Esk\xE9 meno (nepovinn\xE9), aby sme vedeli komu prip\xEDsa\u0165 z\xE1sluhu pri propagovan\xED cez Twitter",
    website: "Odkaz na va\u0161u osobn\xFA webov\xFA str\xE1nku alebo niekam inam (nepovinn\xE9)"
  },
  errors: {
    required: "Povinn\xE9",
    website: "Zadajte platn\xFA adresu URL"
  },
  noteDescription: "Uverejnite va\u0161u kni\u017Enicu vo <link>verejnom zozname kni\u017En\xEDc</link>aby ju aj ostatn\xED mohli pou\u017Ei\u0165 v ich n\xE1\u010Drtoch.",
  noteGuidelines: "Kni\u017Enica mus\xED by\u0165 najprv manu\xE1lne schv\xE1len\xE1. Pros\xEDm pre\u010D\xEDtajte si <link>pokyny</link> pred uverejnen\xEDm. Budete potrebova\u0165 Github \xFA\u010Det na komunik\xE1ciu a vykonanie zmien, ak bud\xFA potrebn\xE9, av\u0161ak nie je to \xFAplne povinn\xE9.",
  noteLicense: "Potvrden\xEDm s\xFAhlas\xEDte, \u017Ee kni\u017Enica bude zverejnen\xE1 s <link>MIT licenciou, </link>\u010Do v skratke znamen\xE1, \u017Ee ju m\xF4\u017Ee pou\u017Ei\u0165 hocikto bez obmedzen\xED.",
  noteItems: "Ka\u017Ed\xE1 polo\u017Eka v kni\u017Enici mus\xED ma\u0165 svoje vlastn\xE9 meno, aby sa dala vyh\u013Eada\u0165. S\xFA\u010Das\u0165ou kni\u017Enice bud\xFA nasleduj\xFAce polo\u017Eky:",
  atleastOneLibItem: "Za\u010Dnite pros\xEDm zvolen\xEDm aspo\u0148 jednej polo\u017Eky z kni\u017Enice",
  republishWarning: "Pozn\xE1mka: Niektor\xE9 z vybran\xFDch polo\u017Eiek s\xFA u\u017E ozna\u010Den\xE9 ako zverejnen\xE9. Ich znovu uverejnenie by ste mali vykova\u0165 iba vtedy ak aktualizujete u\u017E existuj\xFAcu kni\u017Enicu alebo po\u017Eiadavku na uverejnenie."
};
var publishSuccessDialog = {
  title: "Kni\u017Enica uverejnen\xE1",
  content: "\u010Eakujeme v\xE1m {{authorName}}. Va\u0161a kni\u017Enica bola uverejnen\xE1 na pos\xFAdenie. Stav m\xF4\u017Eete skontrolova\u0165<link>tu</link>"
};
var confirmDialog = {
  resetLibrary: "Obnovi\u0165 kni\u017Enicu",
  removeItemsFromLib: "Odstr\xE1ni\u0165 zvolen\xE9 polo\u017Eky z kni\u017Enice"
};
var imageExportDialog = {
  header: "Exportova\u0165 obr\xE1zok",
  label: {
    withBackground: "Pozadie",
    onlySelected: "Iba vybran\xE9",
    darkMode: "Tmav\xFD re\u017Eim",
    embedScene: "Zahrn\xFA\u0165 sc\xE9nu",
    scale: "Mierka",
    padding: "Odsadenie"
  },
  tooltip: {
    embedScene: "\xDAdaje sc\xE9ny bud\xFA ulo\u017Een\xE9 do exportovan\xE9ho PNG/SVG s\xFAboru, tak\u017Ee sc\xE9na z neho m\xF4\u017Ee by\u0165 op\xE4\u0165 obnoven\xE1.\nBude to ma\u0165 za n\xE1sledok zv\xFD\u0161enie ve\u013Ekosti s\xFAboru."
  },
  title: {
    exportToPng: "Exportova\u0165 do PNG",
    exportToSvg: "Exportova\u0165 do SVG",
    copyPngToClipboard: "Kop\xEDrova\u0165 PNG do schr\xE1nky"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "Kop\xEDrova\u0165 do schr\xE1nky"
  }
};
var encrypted = {
  tooltip: "Va\u0161e kresby pou\u017E\xEDvaj\xFA end-to-end \u0161ifrovanie, tak\u017Ee ich Excalidraw server nedok\xE1\u017Ee pre\u010D\xEDta\u0165.",
  link: "Blog o end-to-end \u0161ifrovan\xED v Excalidraw"
};
var stats = {
  angle: "Uhol",
  element: "Prvok",
  elements: "Prvky",
  height: "V\xFD\u0161ka",
  scene: "Sc\xE9na",
  selected: "Vybran\xE9",
  storage: "\xDAlo\u017Eisko",
  title: "\u0160tatistiky",
  total: "Celkom",
  version: "Verzia",
  versionCopy: "Kliknut\xEDm skop\xEDrujete",
  versionNotAvailable: "Verzia nie je k dispoz\xEDcii",
  width: "\u0160\xEDrka"
};
var toast = {
  addedToLibrary: "Pridan\xE9 do kni\u017Enice",
  copyStyles: "\u0160t\xFDly skop\xEDrovan\xE9.",
  copyToClipboard: "Skop\xEDrovan\xE9 do schr\xE1nky.",
  copyToClipboardAsPng: "Kop\xEDrovanie {{exportSelection}} do schr\xE1nky ako PNG prebehlo \xFAspe\u0161ne\n({{exportColorScheme}})",
  fileSaved: "S\xFAbor ulo\u017Een\xFD.",
  fileSavedToFilename: "Ulo\u017Een\xFD ako {filename}",
  canvas: "pl\xE1tna",
  selection: "v\xFDberu",
  pasteAsSingleElement: "Pou\u017Eit\xEDm {{shortcut}} vlo\u017Ete ako samostatn\xFD prvok alebo vlo\u017Ete do existuj\xFAceho editovan\xE9ho textu",
  unableToEmbed: "Zapustenie tejto URL nie je povolen\xE9. Vytvorte issue na GitHub-e a po\u017Eiadajte povolenie tejto URL",
  unrecognizedLinkFormat: "Odkaz, ktor\xFD sa sna\u017E\xEDte zapusti\u0165 nie je v o\u010Dak\xE1vanom form\xE1te. Pros\xEDm sk\xFAste vlo\u017Ei\u0165 'odkaz na zdie\u013Eanie' poskytnut\xFD zdrojovou webovou str\xE1nkou"
};
var colors = {
  transparent: "Prieh\u013Eadn\xE1",
  black: "\u010Cierna",
  white: "Biela",
  red: "\u010Cerven\xE1",
  pink: "Ru\u017Eov\xE1",
  grape: "Hroznov\xE1 fialov\xE1",
  violet: "Fialov\xE1",
  gray: "Siv\xE1",
  blue: "Modr\xE1",
  cyan: "Az\xFArov\xE1",
  teal: "Modrozelen\xE1",
  green: "Zelen\xE1",
  yellow: "\u017Dlt\xE1",
  orange: "Oran\u017Eov\xE1",
  bronze: "Bronzov\xE1"
};
var welcomeScreen = {
  app: {
    center_heading: "V\u0161etky va\u0161e d\xE1ta s\xFA ulo\u017Een\xE9 lok\xE1lne vo va\u0161om prehliada\u010Di.",
    center_heading_plus: "Chceli ste namiesto toho prejs\u0165 do Excalidraw+?",
    menuHint: "Exportovanie, nastavenia, jazyky, ..."
  },
  defaults: {
    menuHint: "Exportovanie, nastavenia a \u010Fal\u0161ie...",
    center_heading: "Diagramy. Jednoducho.",
    toolbarHint: "Zvo\u013Ete n\xE1stroj a za\u010Dnite kresli\u0165!",
    helpHint: "Kl\xE1vesov\xE9 skratky a pomocn\xEDk"
  }
};
var colorPicker = {
  mostUsedCustomColors: "Najpou\u017E\xEDvanej\u0161ie vlastn\xE9 farby",
  colors: "Farby",
  shades: "Odtiene",
  hexCode: "Hex k\xF3d",
  noShades: "Pre t\xFAto farbu nie s\xFA dostupn\xE9 \u017Eiadne odtiene"
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "Exportova\u0165 ako obr\xE1zok",
      button: "Exportova\u0165 ako obr\xE1zok",
      description: "Exportova\u0165 \xFAdaje sc\xE9ny ako obr\xE1zok, z ktor\xE9ho m\xF4\u017Eu by\u0165 nesk\xF4r importovan\xE9."
    },
    saveToDisk: {
      title: "Ulo\u017Ei\u0165 na disk",
      button: "Ulo\u017Ei\u0165 na disk",
      description: "Exportova\u0165 \xFAdaje sc\xE9ny do s\xFAboru, z ktor\xE9ho m\xF4\u017Eu by\u0165 nesk\xF4r importovan\xE9."
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "Exportova\u0165 ako Excalidraw+",
      description: "Ulo\u017Ei\u0165 sc\xE9nu do v\xE1\u0161ho Excalidraw+ pracovn\xE9ho priestoru."
    }
  },
  modal: {
    loadFromFile: {
      title: "Na\u010D\xEDta\u0165 zo s\xFAboru",
      button: "Na\u010D\xEDta\u0165 zo s\xFAboru",
      description: "Na\u010D\xEDtanie zo s\xFAboru <bold>nahrad\xED v\xE1\u0161 existuj\xFAci obsah</bold>.<br></br>Va\u0161u kresbu m\xF4\u017Eete z\xE1lohova\u0165 jednou z ni\u017E\u0161ie uveden\xFDch mo\u017Enost\xED."
    },
    shareableLink: {
      title: "Na\u010D\xEDta\u0165 z odkazu",
      button: "Nahradi\u0165 m\xF4j obsah",
      description: "Na\u010D\xEDtanie externej kresby <bold>nahrad\xED v\xE1\u0161 existuj\xFAci obsah</bold>.<br></br>Va\u0161u kresbu m\xF4\u017Eete z\xE1lohova\u0165 jednou z ni\u017E\u0161ie uveden\xFDch mo\u017Enost\xED."
    }
  }
};
var mermaid = {
  title: "Mermaid do Excalidraw",
  button: "Vlo\u017Ei\u0165",
  description: "Aktu\xE1lne s\xFA podporovan\xE9 iba <flowchartLink>v\xFDvojov\xE9 diagramy</flowchartLink>, <sequenceLink>sekven\u010Dn\xE9 diagramy</sequenceLink> a <classLink>diagramy tried</classLink>. Ostatn\xE9 typy bud\xFA v Excalidraw vykreslen\xE9 ako obr\xE1zky.",
  syntax: "Mermaid syntax",
  preview: "Uk\xE1\u017Eka"
};
var sk_SK_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  sk_SK_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=sk-SK-DY6IPO5U.js.map
