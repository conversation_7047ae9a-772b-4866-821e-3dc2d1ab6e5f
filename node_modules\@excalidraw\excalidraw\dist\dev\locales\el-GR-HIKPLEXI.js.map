{"version": 3, "sources": ["../../../locales/el-GR.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Επικόλληση\",\n    \"pasteAsPlaintext\": \"Επικόλληση ως απλό κείμενο\",\n    \"pasteCharts\": \"Επικόλληση γραφημάτων\",\n    \"selectAll\": \"Επιλογή όλων\",\n    \"multiSelect\": \"Προσθέστε το στοιχείο στην επιλογή\",\n    \"moveCanvas\": \"Μετακίνηση καμβά\",\n    \"cut\": \"Αποκοπή\",\n    \"copy\": \"Αντιγραφή\",\n    \"copyAsPng\": \"Αντιγραφή στο πρόχειρο ως PNG\",\n    \"copyAsSvg\": \"Αντιγραφή στο πρόχειρο ως SVG\",\n    \"copyText\": \"Αντιγραφή στο πρόχειρο ως κείμενο\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Στο προσκήνιο\",\n    \"sendToBack\": \"Ένα επίπεδο πίσω\",\n    \"bringToFront\": \"Ένα επίπεδο μπροστά\",\n    \"sendBackward\": \"Στο παρασκήνιο\",\n    \"delete\": \"Διαγραφή\",\n    \"copyStyles\": \"Αντιγραφή εμφάνισης\",\n    \"pasteStyles\": \"Επικόλληση εμφάνισης\",\n    \"stroke\": \"Μολυβιά\",\n    \"background\": \"Φόντο\",\n    \"fill\": \"Γέμισμα\",\n    \"strokeWidth\": \"Πάχος μολυβιάς\",\n    \"strokeStyle\": \"Στυλ περιγράμματος\",\n    \"strokeStyle_solid\": \"Συμπαγής\",\n    \"strokeStyle_dashed\": \"Διακεκομμένη με παύλες\",\n    \"strokeStyle_dotted\": \"Διακεκομμένη με τελείες\",\n    \"sloppiness\": \"Ακαταστασία\",\n    \"opacity\": \"Διαφάνεια\",\n    \"textAlign\": \"Στοίχιση κειμένου\",\n    \"edges\": \"Άκρες\",\n    \"sharp\": \"Οξύ\",\n    \"round\": \"Στρογγυλό\",\n    \"arrowheads\": \"Σύμβολα βελών\",\n    \"arrowhead_none\": \"Κανένα\",\n    \"arrowhead_arrow\": \"Βέλος\",\n    \"arrowhead_bar\": \"Μπάρα\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Τρίγωνο\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Μέγεθος γραμματοσειράς\",\n    \"fontFamily\": \"Γραμματοσειρά\",\n    \"addWatermark\": \"Προσθήκη \\\"Φτιαγμένο με Excalidraw\\\"\",\n    \"handDrawn\": \"Σχεδιασμένο στο χέρι\",\n    \"normal\": \"Κανονική\",\n    \"code\": \"Κώδικας\",\n    \"small\": \"Μικρό\",\n    \"medium\": \"Μεσαίο\",\n    \"large\": \"Μεγάλο\",\n    \"veryLarge\": \"Πολύ μεγάλο\",\n    \"solid\": \"Συμπαγής\",\n    \"hachure\": \"Εκκόλαψη\",\n    \"zigzag\": \"\",\n    \"crossHatch\": \"Διασταυρούμενη εκκόλαψη\",\n    \"thin\": \"Λεπτή\",\n    \"bold\": \"Έντονη\",\n    \"left\": \"Αριστερά\",\n    \"center\": \"Κέντρο\",\n    \"right\": \"Δεξιά\",\n    \"extraBold\": \"Πολύ έντονη\",\n    \"architect\": \"Αρχιτέκτονας\",\n    \"artist\": \"Καλλιτέχνης\",\n    \"cartoonist\": \"Σκιτσογράφος\",\n    \"fileTitle\": \"Όνομα αρχείου\",\n    \"colorPicker\": \"Επιλογή Χρώματος\",\n    \"canvasColors\": \"Χρησιμοποείται στον καμβά\",\n    \"canvasBackground\": \"Φόντο καμβά\",\n    \"drawingCanvas\": \"Σχεδίαση καμβά\",\n    \"layers\": \"Στρώματα\",\n    \"actions\": \"Ενέργειες\",\n    \"language\": \"Γλώσσα\",\n    \"liveCollaboration\": \"Live συνεργασία...\",\n    \"duplicateSelection\": \"Δημιουργία αντιγράφου\",\n    \"untitled\": \"Χωρίς τίτλο\",\n    \"name\": \"Όνομα\",\n    \"yourName\": \"Το όνομά σου\",\n    \"madeWithExcalidraw\": \"Φτιαγμένο με Excalidraw\",\n    \"group\": \"Δημιουργία ομάδας από επιλογή\",\n    \"ungroup\": \"Κατάργηση ομάδας από επιλογή\",\n    \"collaborators\": \"Συνεργάτες\",\n    \"showGrid\": \"Προβολή πλέγματος\",\n    \"addToLibrary\": \"Προσθήκη στη βιβλιοθήκη\",\n    \"removeFromLibrary\": \"Αφαίρεση από τη βιβλιοθήκη\",\n    \"libraryLoadingMessage\": \"Φόρτωση βιβλιοθήκης…\",\n    \"libraries\": \"Άλλες βιβλιοθήκες\",\n    \"loadingScene\": \"Φόρτωση σκηνής…\",\n    \"align\": \"Στοίχιση\",\n    \"alignTop\": \"Στοίχιση πάνω\",\n    \"alignBottom\": \"Στοίχιση κάτω\",\n    \"alignLeft\": \"Στοίχιση αριστερά\",\n    \"alignRight\": \"Στοίχιση δεξιά\",\n    \"centerVertically\": \"Κέντρο κάθετα\",\n    \"centerHorizontally\": \"Κέντρο οριζόντια\",\n    \"distributeHorizontally\": \"Οριζόντια κατανομή\",\n    \"distributeVertically\": \"Κατακόρυφη κατανομή\",\n    \"flipHorizontal\": \"Οριζόντια αναστροφή\",\n    \"flipVertical\": \"Κατακόρυφη αναστροφή\",\n    \"viewMode\": \"Λειτουργία προβολής\",\n    \"share\": \"Κοινοποίηση\",\n    \"showStroke\": \"Εμφάνιση επιλογέα χρωμάτων πινελιάς\",\n    \"showBackground\": \"Εμφάνιση επιλογέα χρώματος φόντου\",\n    \"toggleTheme\": \"Εναλλαγή θέματος\",\n    \"personalLib\": \"Προσωπική Βιβλιοθήκη\",\n    \"excalidrawLib\": \"Βιβλιοθήκη Excalidraw\",\n    \"decreaseFontSize\": \"Μείωση μεγέθους γραμματοσειράς\",\n    \"increaseFontSize\": \"Αύξηση μεγέθους γραμματοσειράς\",\n    \"unbindText\": \"Αποσύνδεση κειμένου\",\n    \"bindText\": \"Δέσμευση κειμένου στο δοχείο\",\n    \"createContainerFromText\": \"\",\n    \"link\": {\n      \"edit\": \"Επεξεργασία συνδέσμου\",\n      \"editEmbed\": \"\",\n      \"create\": \"Δημιουργία συνδέσμου\",\n      \"createEmbed\": \"\",\n      \"label\": \"Σύνδεσμος\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Επεξεργασία γραμμής\",\n      \"exit\": \"Έξοδος επεξεργαστή κειμένου\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Κλείδωμα\",\n      \"unlock\": \"Ξεκλείδωμα\",\n      \"lockAll\": \"Κλείδωμα όλων\",\n      \"unlockAll\": \"Ξεκλείδωμα όλων\"\n    },\n    \"statusPublished\": \"Δημοσιευμένο\",\n    \"sidebarLock\": \"Κρατήστε την πλαϊνή μπάρα ανοιχτή\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Δεν έχουν προστεθεί αντικείμενα ακόμη...\",\n    \"hint_emptyLibrary\": \"Επιλέξτε ένα στοιχείο στον καμβά για να το προσθέσετε εδώ, ή εγκαταστήστε μια βιβλιοθήκη από το δημόσιο αποθετήριο, παρακάτω.\",\n    \"hint_emptyPrivateLibrary\": \"Επιλέξτε ένα στοιχείο στον καμβά για να το προσθέσετε εδώ.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Επαναφορά του καμβά\",\n    \"exportJSON\": \"Εξαγωγή σε αρχείο\",\n    \"exportImage\": \"Εξαγωγή εικόνας...\",\n    \"export\": \"Αποθήκευση ως...\",\n    \"copyToClipboard\": \"Αντιγραφή στο πρόχειρο\",\n    \"save\": \"Αποθήκευση στο τρέχον αρχείο\",\n    \"saveAs\": \"Αποθήκευση ως\",\n    \"load\": \"Άνοιγμα\",\n    \"getShareableLink\": \"Δημόσιος σύνδεσμος\",\n    \"close\": \"Κλείσιμο\",\n    \"selectLanguage\": \"Επιλογή γλώσσας\",\n    \"scrollBackToContent\": \"Μετακινηθείτε πίσω στο περιεχόμενο\",\n    \"zoomIn\": \"Μεγέθυνση\",\n    \"zoomOut\": \"Σμίκρυνση\",\n    \"resetZoom\": \"Επαναφορά μεγέθυνσης\",\n    \"menu\": \"Μενού\",\n    \"done\": \"Τέλος\",\n    \"edit\": \"Επεξεργασία\",\n    \"undo\": \"Αναίρεση\",\n    \"redo\": \"Επαναφορά\",\n    \"resetLibrary\": \"Καθαρισμός βιβλιοθήκης\",\n    \"createNewRoom\": \"Δημιουργία νέου χώρου\",\n    \"fullScreen\": \"Πλήρης οθόνη\",\n    \"darkMode\": \"Σκοτεινή λειτουργία\",\n    \"lightMode\": \"Φωτεινή λειτουργία\",\n    \"zenMode\": \"Λειτουργία Zεν\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Έξοδος από την λειτουργία Zen\",\n    \"cancel\": \"Ακύρωση\",\n    \"clear\": \"Καθαρισμός\",\n    \"remove\": \"Κατάργηση\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Δημοσίευση\",\n    \"submit\": \"Υποβολή\",\n    \"confirm\": \"Επιβεβαίωση\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Αυτό θα σβήσει ολόκληρο τον καμβά. Είσαι σίγουρος;\",\n    \"couldNotCreateShareableLink\": \"Δεν ήταν δυνατή η δημιουργία συνδέσμου κοινής χρήσης.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Δεν ήταν δυνατή η δημιουργία κοινόχρηστου συνδέσμου: η σκηνή είναι πολύ μεγάλη\",\n    \"couldNotLoadInvalidFile\": \"Δεν μπόρεσε να ανοίξει εσφαλμένο αρχείο\",\n    \"importBackendFailed\": \"Η εισαγωγή από το backend απέτυχε.\",\n    \"cannotExportEmptyCanvas\": \"Δεν είναι δυνατή η εξαγωγή κενού καμβά.\",\n    \"couldNotCopyToClipboard\": \"Αδυναμία αντιγραφής στο πρόχειρο.\",\n    \"decryptFailed\": \"Δεν ήταν δυνατή η αποκρυπτογράφηση δεδομένων.\",\n    \"uploadedSecurly\": \"Η μεταφόρτωση έχει εξασφαλιστεί με κρυπτογράφηση από άκρο σε άκρο, πράγμα που σημαίνει ότι ο διακομιστής Excalidraw και τρίτα μέρη δεν μπορούν να διαβάσουν το περιεχόμενο.\",\n    \"loadSceneOverridePrompt\": \"Η φόρτωση εξωτερικού σχεδίου θα αντικαταστήσει το υπάρχον περιεχόμενο. Επιθυμείτε να συνεχίσετε;\",\n    \"collabStopOverridePrompt\": \"Η διακοπή της συνεδρίας θα αντικαταστήσει το προηγούμενο, τοπικά αποθηκευμένο σχέδιο. Είστε σίγουροι?\\n\\n(Αν θέλετε να διατηρήσετε το τοπικό σας σχέδιο, απλά κλείστε την καρτέλα του προγράμματος περιήγησης.)\",\n    \"errorAddingToLibrary\": \"Αδυναμία προσθήκης αντικειμένου στη βιβλιοθήκη\",\n    \"errorRemovingFromLibrary\": \"Αδυναμία αφαίρεσης αντικειμένου από τη βιβλιοθήκη\",\n    \"confirmAddLibrary\": \"Αυτό θα προσθέσει {{numShapes}} σχήμα(τα) στη βιβλιοθήκη σας. Είστε σίγουροι;\",\n    \"imageDoesNotContainScene\": \"Αυτή η εικόνα δεν φαίνεται να περιέχει δεδομένα σκηνής. Έχετε ενεργοποιήσει την ενσωμάτωση σκηνής κατά την εξαγωγή;\",\n    \"cannotRestoreFromImage\": \"Η σκηνή δεν ήταν δυνατό να αποκατασταθεί από αυτό το αρχείο εικόνας\",\n    \"invalidSceneUrl\": \"Δεν ήταν δυνατή η εισαγωγή σκηνής από το URL που δώσατε. Είτε έχει λάθος μορφή, είτε δεν περιέχει έγκυρα δεδομένα JSON Excalidraw.\",\n    \"resetLibrary\": \"Αυτό θα καθαρίσει τη βιβλιοθήκη σας. Είστε σίγουροι;\",\n    \"removeItemsFromsLibrary\": \"Διαγραφή {{count}} αντικειμένου(ων) από τη βιβλιοθήκη;\",\n    \"invalidEncryptionKey\": \"Το κλειδί κρυπτογράφησης πρέπει να είναι 22 χαρακτήρες. Η ζωντανή συνεργασία είναι απενεργοποιημένη.\",\n    \"collabOfflineWarning\": \"Δεν υπάρχει διαθέσιμη σύνδεση στο internet.\\nΟι αλλαγές σας δεν θα αποθηκευτούν!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Μη υποστηριζόμενος τύπος αρχείου.\",\n    \"imageInsertError\": \"Αδυναμία εισαγωγής εικόνας. Προσπαθήστε ξανά αργότερα...\",\n    \"fileTooBig\": \"Το αρχείο είναι πολύ μεγάλο. Το μέγιστο επιτρεπόμενο μέγεθος είναι {{maxSize}}.\",\n    \"svgImageInsertError\": \"Αδυναμία εισαγωγής εικόνας SVG. Η σήμανση της SVG δεν φαίνεται έγκυρη.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"Μη έγκυρο SVG.\",\n    \"cannotResolveCollabServer\": \"Αδυναμία σύνδεσης με τον διακομιστή συνεργασίας. Παρακαλώ ανανεώστε τη σελίδα και προσπαθήστε ξανά.\",\n    \"importLibraryError\": \"Αδυναμία φόρτωσης βιβλιοθήκης\",\n    \"collabSaveFailed\": \"Η αποθήκευση στη βάση δεδομένων δεν ήταν δυνατή. Αν το προβλήματα παραμείνει, θα πρέπει να αποθηκεύσετε το αρχείο σας τοπικά για να βεβαιωθείτε ότι δεν χάνετε την εργασία σας.\",\n    \"collabSaveFailed_sizeExceeded\": \"Η αποθήκευση στη βάση δεδομένων δεν ήταν δυνατή, ο καμβάς φαίνεται να είναι πολύ μεγάλος. Θα πρέπει να αποθηκεύσετε το αρχείο τοπικά για να βεβαιωθείτε ότι δεν θα χάσετε την εργασία σας.\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Επιλογή\",\n    \"image\": \"Εισαγωγή εικόνας\",\n    \"rectangle\": \"Ορθογώνιο\",\n    \"diamond\": \"Ρόμβος\",\n    \"ellipse\": \"Έλλειψη\",\n    \"arrow\": \"Βέλος\",\n    \"line\": \"Γραμμή\",\n    \"freedraw\": \"Σχεδίαση\",\n    \"text\": \"Κείμενο\",\n    \"library\": \"Βιβλιοθήκη\",\n    \"lock\": \"Κράτησε επιλεγμένο το εργαλείο μετά το σχέδιο\",\n    \"penMode\": \"Λειτουργία μολυβιού - αποτροπή αφής\",\n    \"link\": \"Προσθήκη/ Ενημέρωση συνδέσμου για ένα επιλεγμένο σχήμα\",\n    \"eraser\": \"Γόμα\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Ενέργειες καμβά\",\n    \"selectedShapeActions\": \"Επιλεγμένες ενέργειες σχήματος\",\n    \"shapes\": \"Σχήματα\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"\",\n    \"linearElement\": \"Κάνε κλικ για να ξεκινήσεις πολλαπλά σημεία, σύρε για μια γραμμή\",\n    \"freeDraw\": \"Κάντε κλικ και σύρτε, απελευθερώσατε όταν έχετε τελειώσει\",\n    \"text\": \"Tip: μπορείτε επίσης να προσθέστε κείμενο με διπλό-κλικ οπουδήποτε με το εργαλείο επιλογών\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"Κάντε διπλό κλικ ή πατήστε ENTER για να επεξεργαστείτε το κείμενο\",\n    \"text_editing\": \"Πατήστε Escape ή CtrlOrCmd+ENTER για να ολοκληρώσετε την επεξεργασία\",\n    \"linearElementMulti\": \"Κάνε κλικ στο τελευταίο σημείο ή πάτησε Escape ή Enter για να τελειώσεις\",\n    \"lockAngle\": \"Μπορείτε να περιορίσετε τη γωνία κρατώντας πατημένο το SHIFT\",\n    \"resize\": \"Μπορείς να περιορίσεις τις αναλογίες κρατώντας το SHIFT ενώ αλλάζεις μέγεθος,\\nκράτησε πατημένο το ALT για αλλαγή μεγέθους από το κέντρο\",\n    \"resizeImage\": \"Μπορείτε να αλλάξετε το μέγεθος ελεύθερα κρατώντας πατημένο το SHIFT,\\nκρατήστε πατημένο το ALT για να αλλάξετε το μέγεθος από το κέντρο\",\n    \"rotate\": \"Μπορείς να περιορίσεις τις γωνίες κρατώντας πατημένο το πλήκτρο SHIFT κατά την περιστροφή\",\n    \"lineEditor_info\": \"Κρατήστε πατημένο Ctrl ή Cmd και πατήστε το πλήκτρο Ctrl ή Cmd + Enter για επεξεργασία σημείων\",\n    \"lineEditor_pointSelected\": \"Πατήστε Διαγραφή για αφαίρεση σημείου(ων),\\nCtrlOrCmd+D για αντιγραφή, ή σύρετε για μετακίνηση\",\n    \"lineEditor_nothingSelected\": \"Επιλέξτε ένα σημείο για να επεξεργαστείτε (κρατήστε πατημένο το SHIFT για να επιλέξετε πολλαπλά),\\nή κρατήστε πατημένο το Alt και κάντε κλικ για να προσθέσετε νέα σημεία\",\n    \"placeImage\": \"Κάντε κλικ για να τοποθετήσετε την εικόνα ή κάντε κλικ και σύρετε για να ορίσετε το μέγεθός της χειροκίνητα\",\n    \"publishLibrary\": \"Δημοσιεύστε τη δική σας βιβλιοθήκη\",\n    \"bindTextToElement\": \"Πατήστε Enter για προσθήκη κειμένου\",\n    \"deepBoxSelect\": \"Κρατήστε πατημένο το CtrlOrCmd για να επιλέξετε βαθιά, και να αποτρέψετε τη μεταφορά\",\n    \"eraserRevert\": \"Κρατήστε πατημένο το Alt για να επαναφέρετε τα στοιχεία που σημειώθηκαν για διαγραφή\",\n    \"firefox_clipboard_write\": \"Αυτή η επιλογή μπορεί πιθανώς να ενεργοποιηθεί αλλάζοντας την ρύθμιση \\\"dom.events.asyncClipboard.clipboardItem\\\" σε \\\"true\\\". Για να αλλάξετε τις ρυθμίσεις του προγράμματος περιήγησης στο Firefox, επισκεφθείτε τη σελίδα \\\"about:config\\\".\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Αδυναμία εμφάνισης προεπισκόπησης\",\n    \"canvasTooBig\": \"Ο καμβάς μπορεί να είναι μεγάλος.\",\n    \"canvasTooBigTip\": \"Συμβουλή: προσπαθήστε να μετακινήσετε τα πιο απομακρυσμένα στοιχεία λίγο πιο κοντά μαζί.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Συνέβη κάποιο σφάλμα. Προσπάθησε <button>φόρτωσε ξανά την σελίδα.</button>\",\n    \"clearCanvasMessage\": \"Εάν το παραπάνω δεν δουλέψει, προσπάθησε <button>καθαρίσετε τον κανβά.</button>\",\n    \"clearCanvasCaveat\": \" Αυτό θα προκαλέσει απώλεια της δουλειάς σου \",\n    \"trackedToSentry\": \"Το σφάλμα με αναγνωριστικό {{eventId}} παρακολουθήθηκε στο σύστημά μας.\",\n    \"openIssueMessage\": \"Ήμασταν πολύ προσεκτικοί για να μην συμπεριλάβουμε τις πληροφορίες της σκηνής σου στο σφάλμα. Αν η σκηνή σου δεν είναι ιδιωτική, παρακαλώ σκέψου να ακολουθήσεις το δικό μας <button>ανιχνευτής σφαλμάτων.</button> Παρακαλώ να συμπεριλάβετε τις παρακάτω πληροφορίες, αντιγράφοντας και επικολλώντας το ζήτημα στο GitHub.\",\n    \"sceneContent\": \"Περιεχόμενο σκηνής:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Μπορείς να προσκαλέσεις άλλους να δουλέψουν μαζί σου.\",\n    \"desc_privacy\": \"Μην ανησυχείς, η συνεδρία χρησιμοποιεί κρυπτογράφηση από σημείο σε σημείο, άρα οτιδήποτε κάνεις θα παραμείνει ανοιχτό μόνο σε εσένα. Ούτε οι μηχανές μας μπορούν να δουν τι κάνεις.\",\n    \"button_startSession\": \"Έναρξη Συνεδρίας\",\n    \"button_stopSession\": \"Τερματισμός Συνεδρίας\",\n    \"desc_inProgressIntro\": \"Η ζωντανή συνεργασία με άλλους είναι σε ενεργή.\",\n    \"desc_shareLink\": \"Μοιραστείτε τον σύνδεσμο με όποιον θέλετε να δουλέψετε μαζί:\",\n    \"desc_exitSession\": \"Η διακοπή θα σας αποσυνδέσει από το δωμάτιο, αλλά θα μπορείτε να συνεχίσετε να δουλεύετε στον πίνακα, τοπικά. Σημειώσατε ότι αυτό δεν θα επηρεάσει τον πίνακα άλλων, και θα μπορούν ακόμα να συνεισφέρουν στην δική τους έκδοση.\",\n    \"shareTitle\": \"Συμμετάσχετε σε μια ζωντανή συνεδρία συνεργασίας για το Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Σφάλμα\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Αποθήκευση στο δίσκο\",\n    \"disk_details\": \"Εξαγωγή δεδομένων σκηνής σε ένα αρχείο από το οποίο μπορείτε να εισάγετε αργότερα.\",\n    \"disk_button\": \"Αποθήκευση σε αρχείο\",\n    \"link_title\": \"Κοινόχρηστος σύνδεσμος\",\n    \"link_details\": \"Εξαγωγή ως σύνδεσμο μόνο για ανάγνωση.\",\n    \"link_button\": \"Εξαγωγή σε Σύνδεση\",\n    \"excalidrawplus_description\": \"Αποθηκεύστε τη σκηνή στο χώρο εργασίας σας Excalidraw+.\",\n    \"excalidrawplus_button\": \"Εξαγωγή\",\n    \"excalidrawplus_exportError\": \"Δεν ήταν δυνατή η εξαγωγή στο Excalidraw+ αυτή τη στιγμή...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Διαβάστε το Blog μας\",\n    \"click\": \"κλικ\",\n    \"deepSelect\": \"Βαθιά επιλογή\",\n    \"deepBoxSelect\": \"Βαθιά επιλογή μέσα στο πλαίσιο και αποτροπή συρσίματος\",\n    \"curvedArrow\": \"Κυρτό βέλος\",\n    \"curvedLine\": \"Κυρτή γραμμή\",\n    \"documentation\": \"Εγχειρίδιο\",\n    \"doubleClick\": \"διπλό κλικ\",\n    \"drag\": \"σύρε\",\n    \"editor\": \"Επεξεργαστής\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"Βρήκατε πρόβλημα; Υποβάλετε το\",\n    \"howto\": \"Ακολουθήστε τους οδηγούς μας\",\n    \"or\": \"ή\",\n    \"preventBinding\": \"Αποτροπή δέσμευσης βέλων\",\n    \"tools\": \"Εργαλεία\",\n    \"shortcuts\": \"Συντομεύσεις πληκτρολογίου\",\n    \"textFinish\": \"Ολοκλήρωση επεξεργασίας (επεξεργαστής κειμένου)\",\n    \"textNewLine\": \"Προσθήκη νέας γραμμής (επεξεργαστής κειμένου)\",\n    \"title\": \"Βοήθεια\",\n    \"view\": \"Προβολή\",\n    \"zoomToFit\": \"Zoom ώστε να χωρέσουν όλα τα στοιχεία\",\n    \"zoomToSelection\": \"Ζουμ στην επιλογή\",\n    \"toggleElementLock\": \"Κλείδωμα/Ξεκλείδωμα επιλογής\",\n    \"movePageUpDown\": \"Μετακίνηση σελίδας πάνω/κάτω\",\n    \"movePageLeftRight\": \"Μετακίνηση σελίδας αριστερά/δεξιά\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Καθαρισμός καμβά\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Δημοσίευση βιβλιοθήκης\",\n    \"itemName\": \"Όνομα αντικειμένου\",\n    \"authorName\": \"Όνομα δημιουργού\",\n    \"githubUsername\": \"GitHub username\",\n    \"twitterUsername\": \"Twitter username\",\n    \"libraryName\": \"Όνομα βιβλιοθήκης\",\n    \"libraryDesc\": \"Περιγραφή βιβλιοθήκης\",\n    \"website\": \"Ιστοσελίδα\",\n    \"placeholder\": {\n      \"authorName\": \"Όνομα ή όνομα χρήστη\",\n      \"libraryName\": \"Όνομα της βιβλιοθήκης σας\",\n      \"libraryDesc\": \"Περιγραφή της βιβλιοθήκης σας ώστε να βοηθήσει το κοινό να κατανοήσει τη χρήση της\",\n      \"githubHandle\": \"Όνομα χρήστη στο GitHub (προαιρετικό), ώστε να μπορείτε να επεξεργαστείτε τη βιβλιοθήκη αφού υποβληθεί για αξιολόγηση\",\n      \"twitterHandle\": \"Όνομα χρήστη Twitter (προαιρετικό), ώστε να γνωρίζουμε σε ποιον/η να δώσουμε εύσημα κατά την προώθηση μέσω Twitter\",\n      \"website\": \"Σύνδεσμος για την προσωπική σας ιστοσελίδα ή αλλού (προαιρετικό)\"\n    },\n    \"errors\": {\n      \"required\": \"Απαιτείται\",\n      \"website\": \"Εισάγετε μια έγκυρη διεύθυνση URL\"\n    },\n    \"noteDescription\": \"Υποβάλετε τη βιβλιοθήκη σας για να συμπεριληφθεί στο <link>δημόσιο αποθετήριο βιβλιοθήκης</link>ώστε να χρησιμοποιηθεί από άλλα άτομα στα σχέδιά τους.\",\n    \"noteGuidelines\": \"Η βιβλιοθήκη πρέπει πρώτα να εγκριθεί χειροκίνητα. Παρακαλώ διαβάστε τους <link>οδηγίες</link> πριν την υποβολή. Θα χρειαστείτε έναν λογαριασμό GitHub για την επικοινωνία και για να προβείτε σε αλλαγές εφ' όσον χρειαστεί, αλλά δεν είναι αυστηρή απαίτηση.\",\n    \"noteLicense\": \"Με την υποβολή, συμφωνείτε ότι η βιβλιοθήκη θα δημοσιευθεί υπό την <link>Άδεια MIT, </link>που εν συντομία σημαίνει ότι ο καθένας μπορεί να τα χρησιμοποιήσει χωρίς περιορισμούς.\",\n    \"noteItems\": \"Κάθε αντικείμενο της βιβλιοθήκης πρέπει να έχει το δικό του όνομα ώστε να μπορεί να φιλτραριστεί. Θα συμπεριληφθούν τα ακόλουθα αντικείμενα βιβλιοθήκης:\",\n    \"atleastOneLibItem\": \"Παρακαλώ επιλέξτε τουλάχιστον ένα αντικείμενο βιβλιοθήκης για να ξεκινήσετε\",\n    \"republishWarning\": \"Σημείωση: μερικά από τα επιλεγμένα αντικέιμενα έχουν ήδη επισημανθεί ως δημοσιευμένα/υποβεβλημένα. Θα πρέπει να υποβάλετε αντικείμενα εκ νέου μόνο για να ενημερώσετε μία ήδη υπάρχουσα βιβλιοθήκη ή υποβολή.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Η βιβλιοθήκη υποβλήθηκε\",\n    \"content\": \"Ευχαριστούμε {{authorName}}. Η βιβλιοθήκη σας έχει υποβληθεί για αξιολόγηση. Μπορείτε να παρακολουθείτε τη διαδικασία<link>εδώ</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Καθαρισμός βιβλιοθήκης\",\n    \"removeItemsFromLib\": \"Αφαίρεση επιλεγμένων αντικειμένων από τη βιβλιοθήκη\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Τα σχέδιά σου είναι κρυπτογραφημένα από άκρο σε άκρο, έτσι δεν θα είναι ποτέ ορατά μέσα από τους διακομιστές του Excalidraw.\",\n    \"link\": \"Blog post στην κρυπτογράφηση end-to-end στο Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Γωνία\",\n    \"element\": \"Στοιχείο\",\n    \"elements\": \"Στοιχεία\",\n    \"height\": \"Ύψος\",\n    \"scene\": \"Σκηνή\",\n    \"selected\": \"Επιλεγμένα\",\n    \"storage\": \"Χώρος\",\n    \"title\": \"Στατιστικά για σπασίκλες\",\n    \"total\": \"Σύνολο \",\n    \"version\": \"Έκδοση\",\n    \"versionCopy\": \"Κάνε κλικ για αντιγραφή\",\n    \"versionNotAvailable\": \"Έκδοση μη διαθέσιμη\",\n    \"width\": \"Πλάτος\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Προστέθηκε στη βιβλιοθήκη\",\n    \"copyStyles\": \"Αντιγράφηκαν στυλ.\",\n    \"copyToClipboard\": \"Αντιγράφηκε στο πρόχειρο.\",\n    \"copyToClipboardAsPng\": \"Αντιγράφηκε {{exportSelection}} στο πρόχειρο ως PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Το αρχείο αποθηκεύτηκε.\",\n    \"fileSavedToFilename\": \"Αποθηκεύτηκε στο {filename}\",\n    \"canvas\": \"καμβάς\",\n    \"selection\": \"επιλογή\",\n    \"pasteAsSingleElement\": \"Χρησιμοποίησε το {{shortcut}} για να επικολλήσεις ως ένα μόνο στοιχείο,\\nή να επικολλήσεις σε έναν υπάρχοντα επεξεργαστή κειμένου\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Διαφανές\",\n    \"black\": \"Μαύρο\",\n    \"white\": \"Λευκό\",\n    \"red\": \"Κόκκινο\",\n    \"pink\": \"Ροζ\",\n    \"grape\": \"Σταφυλί\",\n    \"violet\": \"Βιολετί\",\n    \"gray\": \"Γκρι\",\n    \"blue\": \"Μπλε\",\n    \"cyan\": \"Κυανό\",\n    \"teal\": \"Τιρκουάζ\",\n    \"green\": \"Πράσινο\",\n    \"yellow\": \"Κίτρινο\",\n    \"orange\": \"Πορτοκαλί\",\n    \"bronze\": \"Χαλκινο\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Όλα τα δεδομένα σας αποθηκεύονται τοπικά στο πρόγραμμα περιήγησης.\",\n      \"center_heading_plus\": \"Μήπως θέλατε να πάτε στο Excalidraw+;\",\n      \"menuHint\": \"Εξαγωγή, προτιμήσεις, γλώσσες, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Εξαγωγή, προτιμήσεις και άλλες επιλογές...\",\n      \"center_heading\": \"Διαγράμματα. Εύκολα. Γρήγορα.\",\n      \"toolbarHint\": \"Επιλέξτε ένα εργαλείο και ξεκινήστε να σχεδιάζεται!\",\n      \"helpHint\": \"Συντομεύσεις και βοήθεια\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Πιο χρησιμοποιούμενα χρώματα\",\n    \"colors\": \"Χρώματα\",\n    \"shades\": \"Αποχρώσεις\",\n    \"hexCode\": \"Κωδικός Hex\",\n    \"noShades\": \"Δεν υπάρχουν διαθέσιμες αποχρώσεις για αυτό το χρώμα\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}