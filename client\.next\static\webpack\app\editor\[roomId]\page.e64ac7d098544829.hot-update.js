"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var monaco_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! monaco-editor */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.main.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlignLeft,FiCode,FiCopy,FiDownload,FiHelpCircle,FiLoader,FiMoon,FiPlay,FiSearch,FiSun,FiUpload,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_socketService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/socketService */ \"(app-pages-browser)/./src/services/socketService.ts\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/throttle */ \"(app-pages-browser)/./node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/SocketContext */ \"(app-pages-browser)/./src/context/SocketContext.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_formatCode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/formatCode */ \"(app-pages-browser)/./src/utils/formatCode.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/ThemeContext */ \"(app-pages-browser)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CodeEditor(param) {\n    let { roomId, username: initialUsername } = param;\n    _s();\n    const { isConnected } = (0,_context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket)();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"// Start coding...\");\n    // Use a ref to always track the latest code value\n    const latestCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"// Start coding...\");\n    // Get username from localStorage if available, otherwise use initialUsername\n    const storedUsername =  true ? localStorage.getItem(\"username\") : 0;\n    console.log(\"Initial username: \".concat(initialUsername, \", Stored username: \").concat(storedUsername));\n    // Track the username internally to handle server validation\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUsername || initialUsername);\n    const [typingUser, setTypingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeUsers, setActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUserList, setShowUserList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"javascript\");\n    const [copySuccess, setCopySuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [teacherSelectionDecorations, setTeacherSelectionDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Code execution states\n    const [isExecuting, setIsExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionOutput, setExecutionOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [executionError, setExecutionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOutput, setShowOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Piston API runtimes\n    const [runtimes, setRuntimes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [runtimesLoaded, setRuntimesLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    // Theme and minimap state (ensure these are always defined at the top)\n    const { theme, setTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme)();\n    const [showShortcuts, setShowShortcuts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [minimapEnabled, setMinimapEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Language-specific code snippets\n    const SNIPPETS = {\n        javascript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            }\n        ],\n        typescript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction(): void {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            },\n            {\n                label: \"Type Declaration\",\n                value: \"type MyType = {\\n  key: string;\\n};\"\n            }\n        ],\n        python3: [\n            {\n                label: \"Function\",\n                value: \"def my_function():\\n    # code\\n    pass\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in range(10):\\n    # code\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition:\\n    # code\"\n            },\n            {\n                label: \"Print\",\n                value: \"print('Hello, World!')\"\n            }\n        ],\n        java: [\n            {\n                label: \"Main Method\",\n                value: \"public static void main(String[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"System.out.println(\\\"Hello, World!\\\");\"\n            }\n        ],\n        csharp: [\n            {\n                label: \"Main Method\",\n                value: \"static void Main(string[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"Console.WriteLine(\\\"Hello, World!\\\");\"\n            }\n        ],\n        c: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"printf(\\\"Hello, World!\\\\n\\\");\"\n            }\n        ],\n        cpp: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"std::cout << \\\"Hello, World!\\\" << std::endl;\"\n            }\n        ],\n        go: [\n            {\n                label: \"Main Function\",\n                value: \"func main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i := 0; i < 10; i++ {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"fmt.Println(\\\"Hello, World!\\\")\"\n            }\n        ],\n        ruby: [\n            {\n                label: \"Method\",\n                value: \"def my_method\\n  # code\\nend\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..9\\n  # code\\nend\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition\\n  # code\\nend\"\n            },\n            {\n                label: \"Print\",\n                value: \"puts 'Hello, World!'\"\n            }\n        ],\n        rust: [\n            {\n                label: \"Main Function\",\n                value: \"fn main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..10 {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"println!(\\\"Hello, World!\\\");\"\n            }\n        ],\n        php: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for ($i = 0; $i < 10; $i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if ($condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"echo 'Hello, World!';\"\n            }\n        ]\n    };\n    // Declare the `socketService` instance at the top of the file\n    const socketService = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n    // Leave room handler\n    const handleLeaveRoom = ()=>{\n        socketService.leaveRoom(roomId);\n        router.push(\"/dashboard\");\n    };\n    // Handle editor mounting\n    const handleEditorDidMount = (editor)=>{\n        editorRef.current = editor;\n        setIsLoading(false);\n        // Auto-focus the editor after mounting\n        setTimeout(()=>{\n            if (editor) {\n                editor.focus();\n                // Add keyboard shortcut (Ctrl+Enter) to run code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.CtrlCmd | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.Enter, ()=>{\n                    executeCode();\n                });\n                // Add keyboard shortcut (Shift+Alt+F) to format code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Shift | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Alt | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.KeyF, ()=>{\n                    formatCurrentCode();\n                });\n                // Add teacher selection change listener\n                if (userRole === 'teacher') {\n                    editor.onDidChangeCursorSelection((e)=>{\n                        const selection = e.selection;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        // Only broadcast if there's an actual selection (not just cursor position)\n                        if (!selection.isEmpty() && roomId) {\n                            console.log('Teacher made selection:', selection);\n                            socketServiceInstance.sendTeacherSelection(roomId, {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            });\n                        } else if (roomId) {\n                            // Clear selection when teacher deselects\n                            socketServiceInstance.clearTeacherSelection(roomId);\n                        }\n                    });\n                }\n            }\n        }, 500) // Small delay to ensure the editor is fully ready\n        ;\n    };\n    // Track if the change is from a remote update\n    const isRemoteUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Create a throttled version of the code change handler\n    // This prevents sending too many updates when typing quickly\n    // but ensures updates are sent at regular intervals\n    const throttledCodeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default()({\n        \"CodeEditor.useRef\": (code)=>{\n            if (roomId && isConnected) {\n                console.log(\"Sending throttled code update to room \".concat(roomId, \", length: \").concat(code.length));\n                socketService.sendCodeChange(roomId, code);\n            }\n        }\n    }[\"CodeEditor.useRef\"], 50) // Use throttle with a short interval for more responsive updates\n    ).current;\n    // Create a debounced version of the typing notification\n    const debouncedTypingNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default()({\n        \"CodeEditor.useCallback[debouncedTypingNotification]\": ()=>{\n            if (roomId && isConnected) {\n                // Always send the current username with typing notifications\n                console.log(\"Sending typing notification with username: \".concat(username));\n                socketService.sendTyping(roomId, username);\n            }\n        }\n    }[\"CodeEditor.useCallback[debouncedTypingNotification]\"], 1000), [\n        roomId,\n        isConnected,\n        username\n    ]);\n    // Handle editor change\n    const handleEditorChange = (value)=>{\n        if (typeof value !== \"string\") return;\n        // Make sure loading is off during code changes\n        setIsLoading(false);\n        // If this change is from a remote update, just update the state and return\n        if (isRemoteUpdate.current) {\n            console.log(\"Handling remote update, not emitting\");\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            isRemoteUpdate.current = false;\n            return;\n        }\n        // Only process if the value actually changed\n        if (value !== latestCodeRef.current) {\n            // Update local state immediately\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            // Send typing notification (debounced)\n            debouncedTypingNotification();\n            // Send code update (throttled)\n            throttledCodeChange(value);\n            // If not connected, try to reconnect\n            if (!isConnected) {\n                console.log(\"Socket not connected, attempting to reconnect\");\n                socketService.connect();\n            }\n        }\n    };\n    // Copy code to clipboard\n    const copyCodeToClipboard = ()=>{\n        if (navigator.clipboard && code) {\n            navigator.clipboard.writeText(code).then(()=>{\n                setCopySuccess(true);\n                setTimeout(()=>setCopySuccess(false), 2000);\n            }).catch((err)=>{\n                console.error('Failed to copy code: ', err);\n            });\n        }\n    };\n    // Toggle user list\n    const toggleUserList = ()=>{\n        setShowUserList((prev)=>!prev);\n    };\n    // Change language\n    const changeLanguage = (lang)=>{\n        setLanguage(lang);\n        // Log available runtimes for this language\n        if (runtimesLoaded && runtimes.length > 0) {\n            const availableRuntimes = runtimes.filter((runtime)=>runtime.language === lang || runtime.aliases && runtime.aliases.includes(lang));\n            if (availableRuntimes.length > 0) {\n                console.log(\"Available runtimes for \".concat(lang, \":\"), availableRuntimes);\n            } else {\n                console.log(\"No runtimes available for \".concat(lang));\n            }\n        }\n    };\n    // Format code\n    const formatCurrentCode = ()=>{\n        if (!code || !editorRef.current) return;\n        try {\n            // Format the code based on the current language\n            const formattedCode = (0,_utils_formatCode__WEBPACK_IMPORTED_MODULE_8__.formatCode)(code, language);\n            // Only update if the code actually changed\n            if (formattedCode !== code) {\n                // Update the editor\n                const model = editorRef.current.getModel();\n                if (model) {\n                    // Store current cursor position/selection\n                    const currentPosition = editorRef.current.getPosition();\n                    const currentSelection = editorRef.current.getSelection();\n                    // Update the editor content\n                    editorRef.current.executeEdits('format', [\n                        {\n                            range: model.getFullModelRange(),\n                            text: formattedCode,\n                            forceMoveMarkers: true\n                        }\n                    ]);\n                    // Restore cursor position if possible\n                    if (currentPosition) {\n                        editorRef.current.setPosition(currentPosition);\n                    }\n                    if (currentSelection) {\n                        editorRef.current.setSelection(currentSelection);\n                    }\n                    // Update state and ref\n                    setCode(formattedCode);\n                    latestCodeRef.current = formattedCode;\n                    // Send the formatted code to other users\n                    if (roomId && isConnected) {\n                        console.log(\"Sending formatted code to room \".concat(roomId, \", length: \").concat(formattedCode.length));\n                        socketService.sendCodeChange(roomId, formattedCode);\n                    }\n                    // Show a success message\n                    console.log('Code formatted successfully');\n                }\n            } else {\n                console.log('Code is already formatted');\n            }\n        } catch (error) {\n            console.error('Error formatting code:', error);\n        }\n    };\n    // Clear execution output\n    const clearOutput = ()=>{\n        setExecutionOutput(null);\n        setExecutionError(null);\n    };\n    // Fetch and store available runtimes from Piston API\n    const fetchRuntimes = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get('https://emkc.org/api/v2/piston/runtimes');\n            console.log('Piston API status:', response.status);\n            console.log('Available runtimes:', response.data);\n            // Store the runtimes in state\n            setRuntimes(response.data);\n            setRuntimesLoaded(true);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error fetching Piston API runtimes:', error);\n            return {\n                success: false,\n                error: axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) ? \"\".concat(error.message, \" - \").concat(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) || 'Unknown', \" \").concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText) || '') : String(error)\n            };\n        }\n    };\n    // Check if the Piston API is available\n    const checkPistonAPI = async ()=>{\n        // If we haven't loaded runtimes yet, fetch them\n        if (!runtimesLoaded) {\n            return await fetchRuntimes();\n        }\n        // If we already have runtimes, just return success\n        if (runtimes.length > 0) {\n            return {\n                success: true,\n                data: runtimes\n            };\n        }\n        // If we've tried to load runtimes but have none, try again\n        return await fetchRuntimes();\n    };\n    // Test the Piston API with a hardcoded sample payload\n    const testPistonAPI = async ()=>{\n        setIsExecuting(true);\n        setExecutionError(null);\n        setExecutionOutput(null);\n        setShowOutput(true);\n        try {\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Find JavaScript runtime\n            const jsRuntimes = apiStatus.data.filter((runtime)=>runtime.language === \"javascript\" || runtime.aliases && runtime.aliases.includes(\"javascript\"));\n            if (jsRuntimes.length === 0) {\n                setExecutionError('No JavaScript runtime found. Please try a different language.');\n                return;\n            }\n            const jsRuntime = jsRuntimes[0];\n            console.log(\"Using JavaScript runtime: \".concat(jsRuntime.language, \" \").concat(jsRuntime.version));\n            // Sample JavaScript code that should work\n            const samplePayload = {\n                language: jsRuntime.language,\n                version: jsRuntime.version,\n                files: [\n                    {\n                        name: \"main.js\",\n                        content: \"console.log('Hello, World!');\"\n                    }\n                ],\n                stdin: \"\",\n                args: []\n            };\n            console.log('Testing Piston API with sample payload:', JSON.stringify(samplePayload, null, 2));\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', samplePayload);\n            console.log('Sample execution response:', response.data);\n            if (response.data.run) {\n                setExecutionOutput(\"API Test Successful!\\n\\n\" + \"Output: \" + response.data.run.stdout + \"\\n\\n\" + \"The API is working correctly. You can now run your own code.\");\n            } else {\n                setExecutionError('API test failed. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error testing Piston API:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                setExecutionError(\"API Test Failed: \".concat(error.response.status, \" \").concat(error.response.statusText, \"\\n\\n\") + JSON.stringify(error.response.data, null, 2));\n            } else {\n                setExecutionError(error instanceof Error ? \"API Test Error: \".concat(error.message) : 'An unknown error occurred while testing the API.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // Execute code using Piston API\n    const executeCode = async ()=>{\n        if (!code || isExecuting) return;\n        // Map Monaco editor language to Piston API language\n        const languageMap = {\n            javascript: \"javascript\",\n            typescript: \"typescript\",\n            python: \"python3\",\n            python3: \"python3\",\n            java: \"java\",\n            csharp: \"csharp\",\n            c: \"c\",\n            cpp: \"cpp\",\n            go: \"go\",\n            ruby: \"ruby\",\n            rust: \"rust\",\n            php: \"php\"\n        };\n        // Get the mapped language\n        const pistonLanguage = languageMap[language] || language;\n        // Check if we have runtimes loaded\n        if (!runtimesLoaded || runtimes.length === 0) {\n            setExecutionError('Runtimes not loaded yet. Please try again in a moment.');\n            setShowOutput(true);\n            return;\n        }\n        // Find available runtimes for the selected language\n        const availableRuntimes = runtimes.filter((runtime)=>runtime.language === pistonLanguage || runtime.aliases && runtime.aliases.includes(pistonLanguage));\n        // Check if the language is supported\n        if (availableRuntimes.length === 0) {\n            // Get a list of supported languages from the runtimes\n            const supportedLanguages = [\n                ...new Set(runtimes.flatMap((runtime)=>[\n                        runtime.language,\n                        ...runtime.aliases || []\n                    ]))\n            ].sort();\n            setExecutionError(\"The language '\".concat(language, \"' (mapped to '\").concat(pistonLanguage, \"') is not supported by the Piston API.\\n\\n\") + \"Supported languages: \".concat(supportedLanguages.join(', ')));\n            setShowOutput(true);\n            return;\n        }\n        // Get the latest version of the runtime\n        const selectedRuntime = availableRuntimes[0];\n        try {\n            setIsExecuting(true);\n            setExecutionError(null);\n            setExecutionOutput(null);\n            setShowOutput(true);\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Determine file extension based on language\n            let fileExtension = '';\n            if (selectedRuntime.language === 'python3' || selectedRuntime.language === 'python') fileExtension = '.py';\n            else if (selectedRuntime.language === 'javascript') fileExtension = '.js';\n            else if (selectedRuntime.language === 'typescript') fileExtension = '.ts';\n            else if (selectedRuntime.language === 'java') fileExtension = '.java';\n            else if (selectedRuntime.language === 'csharp') fileExtension = '.cs';\n            else if (selectedRuntime.language === 'c') fileExtension = '.c';\n            else if (selectedRuntime.language === 'cpp') fileExtension = '.cpp';\n            else if (selectedRuntime.language === 'go') fileExtension = '.go';\n            else if (selectedRuntime.language === 'rust') fileExtension = '.rs';\n            else if (selectedRuntime.language === 'ruby') fileExtension = '.rb';\n            else if (selectedRuntime.language === 'php') fileExtension = '.php';\n            else fileExtension = \".\".concat(selectedRuntime.language);\n            console.log(\"Selected runtime: \".concat(selectedRuntime.language, \" \").concat(selectedRuntime.version));\n            // Prepare the payload according to Piston API documentation\n            const payload = {\n                language: selectedRuntime.language,\n                version: selectedRuntime.version,\n                files: [\n                    {\n                        name: \"main\".concat(fileExtension),\n                        content: code\n                    }\n                ],\n                stdin: '',\n                args: [],\n                compile_timeout: 10000,\n                run_timeout: 5000\n            };\n            // Log the payload for debugging\n            console.log(\"Executing \".concat(pistonLanguage, \" code with payload:\"), JSON.stringify(payload, null, 2));\n            // Make the API request\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', payload);\n            console.log('Execution response:', response.data);\n            const result = response.data;\n            if (result.run) {\n                // Format the output\n                let output = '';\n                let hasOutput = false;\n                // Add compile output if available (for compiled languages)\n                if (result.compile && result.compile.stderr) {\n                    output += \"Compilation output:\\n\".concat(result.compile.stderr, \"\\n\\n\");\n                    hasOutput = true;\n                }\n                // Add standard output\n                if (result.run.stdout) {\n                    output += result.run.stdout;\n                    hasOutput = true;\n                }\n                // Add error output\n                if (result.run.stderr) {\n                    if (hasOutput) output += '\\n';\n                    output += \"Error output:\\n\".concat(result.run.stderr);\n                    hasOutput = true;\n                }\n                // Add exit code if non-zero\n                if (result.run.code !== 0) {\n                    if (hasOutput) output += '\\n';\n                    output += \"\\nProcess exited with code \".concat(result.run.code);\n                    hasOutput = true;\n                }\n                if (!hasOutput) {\n                    output = 'Program executed successfully with no output.';\n                }\n                setExecutionOutput(output);\n            } else {\n                setExecutionError('Failed to execute code. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error executing code:', error);\n            // Handle Axios errors with more detailed information\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                const statusCode = error.response.status;\n                const responseData = error.response.data;\n                console.error('API Error Details:', {\n                    status: statusCode,\n                    data: responseData\n                });\n                // Format a more helpful error message\n                if (statusCode === 400) {\n                    setExecutionError(\"API Error (400 Bad Request): \".concat(JSON.stringify(responseData), \"\\n\\n\") + 'This usually means the API request format is incorrect. ' + 'Please check the console for more details.');\n                } else if (statusCode === 429) {\n                    setExecutionError('Rate limit exceeded. Please try again later.');\n                } else {\n                    setExecutionError(\"API Error (\".concat(statusCode, \"): \").concat(JSON.stringify(responseData), \"\\n\\n\") + 'Please check the console for more details.');\n                }\n            } else {\n                // Handle non-Axios errors\n                setExecutionError(error instanceof Error ? \"Error: \".concat(error.message) : 'An unknown error occurred while executing the code.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // This function will be called when we receive a code update from another user\n    const handleCodeUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleCodeUpdate]\": (incomingCode)=>{\n            console.log(\"Remote code update received, length:\", incomingCode.length);\n            // Make sure loading is off during code updates\n            setIsLoading(false);\n            // Only update if the code is different from our latest code\n            if (incomingCode !== latestCodeRef.current) {\n                try {\n                    // Set the flag to indicate this is a remote update\n                    isRemoteUpdate.current = true;\n                    // Update the editor if it's mounted\n                    if (editorRef.current) {\n                        console.log(\"Updating editor with remote code\");\n                        // Use the editor's model to update the value to preserve cursor position\n                        const model = editorRef.current.getModel();\n                        if (model) {\n                            // Store current cursor position/selection\n                            const currentPosition = editorRef.current.getPosition();\n                            const currentSelection = editorRef.current.getSelection();\n                            // Use executeEdits with better handling of cursor position\n                            editorRef.current.executeEdits('remote', [\n                                {\n                                    range: model.getFullModelRange(),\n                                    text: incomingCode,\n                                    forceMoveMarkers: true\n                                }\n                            ]);\n                            // Restore cursor position if possible\n                            if (currentPosition) {\n                                editorRef.current.setPosition(currentPosition);\n                            }\n                            if (currentSelection) {\n                                editorRef.current.setSelection(currentSelection);\n                            }\n                            // Also update our state and ref\n                            setCode(incomingCode);\n                            latestCodeRef.current = incomingCode;\n                        }\n                    } else {\n                        // If editor isn't mounted yet, just update the state and ref\n                        console.log(\"Editor not mounted, updating state only\");\n                        setCode(incomingCode);\n                        latestCodeRef.current = incomingCode;\n                        isRemoteUpdate.current = false;\n                    }\n                } catch (error) {\n                    console.error(\"Error updating editor with remote code:\", error);\n                    isRemoteUpdate.current = false;\n                }\n            } else {\n                console.log(\"Remote code matches current code, ignoring\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleCodeUpdate]\"], []);\n    // State to track cursor positions\n    const [cursorPositions, setCursorPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Emit cursor movement\n    const handleMouseMove = (e)=>{\n        const position = {\n            x: e.clientX,\n            y: e.clientY\n        };\n        _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().sendCursorMove(roomId, username, position);\n    };\n    // Listen for cursor movement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            const handleCursorMove = {\n                \"CodeEditor.useEffect.handleCursorMove\": (param)=>{\n                    let { userId, position } = param;\n                    setCursorPositions({\n                        \"CodeEditor.useEffect.handleCursorMove\": (prev)=>({\n                                ...prev,\n                                [userId]: position\n                            })\n                    }[\"CodeEditor.useEffect.handleCursorMove\"]);\n                }\n            }[\"CodeEditor.useEffect.handleCursorMove\"];\n            _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().on(\"cursor-move\", handleCursorMove);\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().off(\"cursor-move\", handleCursorMove);\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId\n    ]);\n    // Render cursors\n    const renderCursors = ()=>{\n        return Object.entries(cursorPositions).map((param)=>{\n            let [userId, position] = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    left: position.x,\n                    top: position.y,\n                    backgroundColor: \"red\",\n                    width: 10,\n                    height: 10,\n                    borderRadius: \"50%\",\n                    pointerEvents: \"none\"\n                }\n            }, userId, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 742,\n                columnNumber: 7\n            }, this);\n        });\n    };\n    // Fetch runtimes when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            fetchRuntimes();\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    // Handle request for initial code from new users\n    const handleGetInitialCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleGetInitialCode]\": (data)=>{\n            console.log(\"Received request for initial code from \".concat(data.requestingUsername, \" (\").concat(data.requestingUserId, \")\"));\n            // Only respond if we have code and we're connected\n            if (roomId && latestCodeRef.current && latestCodeRef.current.trim() !== \"// Start coding...\") {\n                const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                console.log(\"Sending initial code to \".concat(data.requestingUsername, \", length: \").concat(latestCodeRef.current.length));\n                socketServiceInstance.sendInitialCode(roomId, latestCodeRef.current, data.requestingUserId);\n            } else {\n                console.log(\"Not sending initial code - no meaningful code to share or not in room\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleGetInitialCode]\"], [\n        roomId\n    ]);\n    // Handle receiving initial code as a new user\n    const handleInitialCodeReceived = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleInitialCodeReceived]\": (data)=>{\n            console.log(\"Received initial code, length: \".concat(data.code.length));\n            // Only apply initial code if we don't have meaningful code yet\n            // This prevents overwriting code that the user might have already started typing\n            if (latestCodeRef.current === \"// Start coding...\" || latestCodeRef.current.trim() === \"\") {\n                console.log(\"Applying received initial code\");\n                // Set the flag to indicate this is a remote update\n                isRemoteUpdate.current = true;\n                // Update the code state and ref\n                setCode(data.code);\n                latestCodeRef.current = data.code;\n                // Update the editor if it's mounted\n                if (editorRef.current) {\n                    editorRef.current.setValue(data.code);\n                }\n            } else {\n                console.log(\"Not applying initial code - user already has meaningful code\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleInitialCodeReceived]\"], []);\n    // Handle teacher selection highlighting\n    const handleTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherSelection]\": (data)=>{\n            console.log(\"Received teacher selection from \".concat(data.teacherName, \":\"), data.selection);\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't show teacher highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model || !data.selection) {\n                    return;\n                }\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.selection.startLineNumber, data.selection.startColumn, data.selection.endLineNumber, data.selection.endColumn);\n                // Clear previous teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-selection-highlight',\n                            hoverMessage: {\n                                value: \"Teacher \".concat(data.teacherName, \" highlighted this text\")\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges\n                        }\n                    }\n                ]);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error applying teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle clearing teacher selection\n    const handleClearTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherSelection]\": (data)=>{\n            console.log(\"Teacher \".concat(data.teacherName, \" cleared selection\"));\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                // Clear all teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, []);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error clearing teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            // Handle user typing notifications\n            const handleUserTyping = {\n                \"CodeEditor.useEffect.handleUserTyping\": (param)=>{\n                    let { username, userId } = param;\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    // Don't show typing indicator for current user\n                    if (userId && userId === currentUserId) {\n                        return;\n                    }\n                    // Use the exact username without any modifications\n                    // This ensures we display exactly what the user entered on the dashboard\n                    setTypingUser(username);\n                    console.log(\"User typing: \".concat(username));\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserTyping\": ()=>setTypingUser(null)\n                    }[\"CodeEditor.useEffect.handleUserTyping\"], 2000);\n                }\n            }[\"CodeEditor.useEffect.handleUserTyping\"];\n            // Handle user list updates from both user-joined/user-left and room-users-updated events\n            const handleUserListUpdate = {\n                \"CodeEditor.useEffect.handleUserListUpdate\": (data)=>{\n                    console.log('User list update received:', data);\n                    let users = [];\n                    // Handle different event formats\n                    if (Array.isArray(data)) {\n                        // This is from user-joined or user-left events\n                        users = data;\n                    } else if (data && Array.isArray(data.users)) {\n                        // This is from room-users-updated event\n                        users = data.users.map({\n                            \"CodeEditor.useEffect.handleUserListUpdate\": (user)=>{\n                                if (typeof user === 'string') {\n                                    // If it's just a username string, convert to user object\n                                    return {\n                                        username: user\n                                    };\n                                }\n                                return user;\n                            }\n                        }[\"CodeEditor.useEffect.handleUserListUpdate\"]);\n                    }\n                    console.log('Processed users:', users);\n                    console.log('Current username state:', username);\n                    console.log('Stored username:', localStorage.getItem('username'));\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    console.log('Current userId from localStorage:', currentUserId);\n                    // Filter out any invalid users and map to display names\n                    const usernames = users.filter({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>user && user.username\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]).map({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>{\n                            const displayName = user.username;\n                            const isCurrentUser = user.userId === currentUserId;\n                            return isCurrentUser ? \"\".concat(displayName, \" (you)\") : displayName;\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]);\n                    console.log('Setting active users:', usernames);\n                    // Only update if we have users to display\n                    if (usernames.length > 0) {\n                        setActiveUsers(usernames);\n                    } else if (activeUsers.length === 0) {\n                        // If we don't have users from the server but we're connected, add ourselves\n                        const storedUsername = localStorage.getItem('username');\n                        if (storedUsername) {\n                            setActiveUsers([\n                                \"\".concat(storedUsername, \" (you)\")\n                            ]);\n                        }\n                    }\n                    // Log the current state of the active users list\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserListUpdate\": ()=>{\n                            console.log('Active users state after update:', activeUsers);\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate\"], 100);\n                }\n            }[\"CodeEditor.useEffect.handleUserListUpdate\"];\n            // Register event listeners\n            const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n            socketServiceInstance.on('code-update', handleCodeUpdate);\n            socketServiceInstance.on('user-typing', handleUserTyping);\n            socketServiceInstance.on('user-joined', handleUserListUpdate);\n            socketServiceInstance.on('user-left', handleUserListUpdate);\n            socketServiceInstance.on('room-users-updated', handleUserListUpdate);\n            socketServiceInstance.on('get-initial-code', handleGetInitialCode);\n            socketServiceInstance.on('initial-code-received', handleInitialCodeReceived);\n            socketServiceInstance.on('teacher-selection', handleTeacherSelection);\n            socketServiceInstance.on('clear-teacher-selection', handleClearTeacherSelection);\n            // Join the room when component mounts\n            if (roomId) {\n                console.log(\"Joining room:\", roomId);\n                // Only show loading on initial join, not for code updates\n                if (!editorRef.current) {\n                    setIsLoading(true);\n                }\n                const joinRoom = {\n                    \"CodeEditor.useEffect.joinRoom\": async ()=>{\n                        try {\n                            if (socketServiceInstance.isConnected()) {\n                                console.log(\"Socket connected, joining room\");\n                                const { username: validatedUsername, users } = await socketServiceInstance.joinRoom(roomId, username);\n                                console.log(\"Successfully joined room:\", roomId);\n                                console.log(\"Users in room:\", users);\n                                if (validatedUsername !== username) {\n                                    console.log(\"Server validated username from \".concat(username, \" to \").concat(validatedUsername));\n                                    setUsername(validatedUsername);\n                                    localStorage.setItem('username', validatedUsername);\n                                }\n                                if (users && Array.isArray(users)) {\n                                    console.log('Setting active users from join response');\n                                    handleUserListUpdate(users);\n                                }\n                                setIsLoading(false);\n                            } else {\n                                console.log(\"Socket not connected, trying to connect...\");\n                                await new Promise({\n                                    \"CodeEditor.useEffect.joinRoom\": (resolve)=>socketServiceInstance.onConnect({\n                                            \"CodeEditor.useEffect.joinRoom\": ()=>resolve()\n                                        }[\"CodeEditor.useEffect.joinRoom\"])\n                                }[\"CodeEditor.useEffect.joinRoom\"]);\n                                // After connect, try join again\n                                await joinRoom();\n                                return;\n                            }\n                        } catch (error) {\n                            console.error(\"Error joining room:\", error);\n                            setIsLoading(false);\n                        }\n                    }\n                }[\"CodeEditor.useEffect.joinRoom\"];\n                // Start the join process\n                joinRoom();\n                // Set a timeout to hide the loading screen even if the join fails\n                setTimeout({\n                    \"CodeEditor.useEffect\": ()=>{\n                        setIsLoading(false);\n                    }\n                }[\"CodeEditor.useEffect\"], 3000);\n            }\n            // Clean up event listeners when component unmounts\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    // Use an instance of SocketService\n                    const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                    socketServiceInstance.off('code-update', handleCodeUpdate);\n                    socketServiceInstance.off('user-typing', handleUserTyping);\n                    socketServiceInstance.off('user-joined', handleUserListUpdate);\n                    socketServiceInstance.off('user-left', handleUserListUpdate);\n                    socketServiceInstance.off('room-users-updated', handleUserListUpdate);\n                    socketServiceInstance.off('get-initial-code', handleGetInitialCode);\n                    socketServiceInstance.off('initial-code-received', handleInitialCodeReceived);\n                    socketServiceInstance.off('teacher-selection', handleTeacherSelection);\n                    socketServiceInstance.off('clear-teacher-selection', handleClearTeacherSelection);\n                    // Leave the room when component unmounts\n                    if (roomId) {\n                        socketServiceInstance.leaveRoom(roomId);\n                    }\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId,\n        initialUsername,\n        username,\n        handleCodeUpdate,\n        handleGetInitialCode,\n        handleInitialCodeReceived,\n        handleTeacherSelection,\n        handleClearTeacherSelection\n    ]);\n    // Download code as file\n    const handleDownload = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"code-\".concat(roomId || \"session\", \".txt\");\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    // Upload code from file\n    const handleUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            var _event_target;\n            if (typeof ((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result) === \"string\") {\n                setCode(event.target.result);\n                latestCodeRef.current = event.target.result;\n            }\n        };\n        reader.readAsText(file);\n    };\n    // Show Monaco's find/replace dialog\n    const handleFindReplace = ()=>{\n        if (editorRef.current) {\n            const action = editorRef.current.getAction(\"actions.find\");\n            if (action) action.run();\n        }\n    };\n    // Insert code snippet\n    const insertSnippet = (snippet)=>{\n        if (editorRef.current) {\n            const model = editorRef.current.getModel();\n            const position = editorRef.current.getPosition();\n            if (model && position) {\n                editorRef.current.executeEdits(\"snippet\", [\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(position.lineNumber, position.column, position.lineNumber, position.column),\n                        text: snippet,\n                        forceMoveMarkers: true\n                    }\n                ]);\n                editorRef.current.focus();\n            }\n        }\n    };\n    // Use the instance of SocketService for the connect method\n    const handleReconnect = ()=>{\n        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n        socketServiceInstance.connect();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-[80vh] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-800 shadow-xl flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"bg-gray-100 dark:bg-zinc-800 px-4 py-2 flex flex-wrap items-center justify-between border-b border-gray-200 dark:border-gray-700 gap-2 min-h-[48px]\",\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    style: {\n                        zIndex: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-3 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 min-w-[90px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1092,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-400 truncate\",\n                                            children: isConnected ? 'Connected' : 'Disconnected'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1093,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded ml-1 transition-colors\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1097,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1091,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: language,\n                                    onChange: (e)=>changeLanguage(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"javascript\",\n                                            children: \"JavaScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"typescript\",\n                                            children: \"TypeScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1113,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"python3\",\n                                            children: \"Python\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"java\",\n                                            children: \"Java\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"csharp\",\n                                            children: \"C#\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1116,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"c\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"cpp\",\n                                            children: \"C++\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"go\",\n                                            children: \"Go\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ruby\",\n                                            children: \"Ruby\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rust\",\n                                            children: \"Rust\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"php\",\n                                            children: \"PHP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: formatCurrentCode,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm bg-blue-600 hover:bg-blue-700 text-white transition-colors whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiAlignLeft, {\n                                            size: 14\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Format\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1133,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: executeCode,\n                                    disabled: isExecuting,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm \".concat(isExecuting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700', \" text-white transition-colors whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: isExecuting ? 1 : 1.05\n                                    },\n                                    whileTap: {\n                                        scale: isExecuting ? 1 : 0.95\n                                    },\n                                    children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                                className: \"animate-spin\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1146,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Running...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1147,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiPlay, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1151,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Run Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1152,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Room:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-white dark:bg-zinc-700 px-2 py-1 rounded font-mono text-xs truncate max-w-[120px]\",\n                                            children: roomId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                            onClick: copyCodeToClipboard,\n                                            className: \"text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            title: \"Copy code to clipboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCopy, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1168,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1161,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1158,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1089,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-2 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowOutput((prev)=>!prev),\n                                    className: \"flex items-center space-x-1 text-sm \".concat(showOutput ? 'text-blue-500 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300', \" hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCode, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Output\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: toggleUserList,\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUsers, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                activeUsers.length,\n                                                \" online\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"flex items-center space-x-1 text-sm text-yellow-600 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSun, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1204,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiMoon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1204,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1197,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setMinimapEnabled((v)=>!v),\n                                    className: \"flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Minimap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Minimap\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1215,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleFindReplace,\n                                    className: \"flex items-center space-x-1 text-sm text-green-600 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Find/Replace\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSearch, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1226,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1219,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleDownload,\n                                    className: \"flex items-center space-x-1 text-sm text-indigo-600 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Download Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiDownload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1237,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"flex items-center space-x-1 text-sm text-pink-600 dark:text-pink-300 bg-pink-100 dark:bg-pink-900/30 hover:bg-pink-200 dark:hover:bg-pink-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Upload Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUpload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1248,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \".js,.ts,.py,.java,.c,.cpp,.go,.rb,.rs,.php,.txt\",\n                                    style: {\n                                        display: \"none\"\n                                    },\n                                    onChange: handleUpload\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    onChange: (e)=>e.target.value && insertSnippet(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[100px]\",\n                                    defaultValue: \"\",\n                                    title: \"Insert Snippet\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Snippets\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1265,\n                                            columnNumber: 15\n                                        }, this),\n                                        (SNIPPETS[language] || SNIPPETS['javascript']).map((snippet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: snippet.value,\n                                                children: snippet.label\n                                            }, snippet.label, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1267,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowShortcuts(true),\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Keyboard Shortcuts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiHelpCircle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1279,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleLeaveRoom,\n                                    className: \"flex items-center space-x-1 text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors px-2 py-1 rounded-md ml-2 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Leave Room\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1289,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1283,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1173,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1082,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showUserList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute right-0 top-12 bg-white dark:bg-zinc-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 w-48 overflow-hidden\",\n                        initial: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            height: 'auto'\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 border-b border-gray-200 dark:border-gray-700 font-medium text-sm text-gray-600 dark:text-gray-300\",\n                                children: \"Active Users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1304,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"max-h-48 overflow-y-auto\",\n                                children: activeUsers.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.li, {\n                                        className: \"px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-zinc-700 flex items-center space-x-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full bg-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1316,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: user\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1317,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1309,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1307,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1297,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1295,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"h-[calc(100%-40px)] \".concat(showOutput ? 'pb-[33%]' : ''),\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.4,\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                height: \"100%\",\n                                defaultLanguage: language,\n                                defaultValue: code,\n                                onChange: handleEditorChange,\n                                onMount: handleEditorDidMount,\n                                theme: theme === \"dark\" ? \"vs-dark\" : \"light\",\n                                options: {\n                                    minimap: {\n                                        enabled: minimapEnabled\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1335,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1334,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1332,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1326,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: typingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 left-4 text-sm text-gray-400 bg-gray-800/80 px-3 py-1 rounded-full backdrop-blur-sm\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            typingUser,\n                            \" is typing...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1351,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1349,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gray-900 text-white border-t border-gray-700 h-1/3 overflow-hidden flex flex-col\",\n                        initial: {\n                            height: 0\n                        },\n                        animate: {\n                            height: '33%'\n                        },\n                        exit: {\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 bg-gray-800 border-b border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1374,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearOutput,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1376,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: testPistonAPI,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-blue-400 hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Test API\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1383,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowOutput(false),\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1390,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1375,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1373,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-auto font-mono text-sm whitespace-pre-wrap\",\n                                children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1401,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Executing code...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1402,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1400,\n                                    columnNumber: 19\n                                }, this) : executionError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400\",\n                                    children: executionError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1405,\n                                    columnNumber: 19\n                                }, this) : executionOutput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: executionOutput\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1407,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: 'Click \"Run Code\" to execute your code.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1409,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1398,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1366,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1364,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: copySuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 right-4 text-sm text-white bg-green-500 px-3 py-1 rounded-full shadow-lg\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: \"Copied to clipboard!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1419,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1417,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showShortcuts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 max-w-md w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-700 dark:hover:text-white\",\n                                    onClick: ()=>setShowShortcuts(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1441,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    children: \"Keyboard Shortcuts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1447,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Run Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1449,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+Enter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1449,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Format Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1450,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Shift+Alt+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1450,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Find/Replace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1451,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1451,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Download Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1452,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+D\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1452,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Upload Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1453,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+U\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1453,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Minimap:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1454,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+M\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1454,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Theme:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1455,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+T\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1455,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Show Shortcuts:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1456,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+H\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1456,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1448,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1440,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1434,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1432,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 1080,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 1078,\n        columnNumber: 5\n    }, this);\n} // Keyboard shortcuts global handler\n // useEffect(() => {\n //   const handler = (e: KeyboardEvent) => {\n //     if (e.altKey && e.key.toLowerCase() === \"d\") {\n //       e.preventDefault();\n //       handleDownload();\n //     } else if (e.altKey && e.key.toLowerCase() === \"u\") {\n //       e.preventDefault();\n //       fileInputRef.current?.click();\n //     } else if (e.altKey && e.key.toLowerCase() === \"m\") {\n //       e.preventDefault();\n //       setMinimapEnabled((v: boolean) => !v);\n //     } else if (e.altKey && e.key.toLowerCase() === \"t\") {\n //       e.preventDefault();\n //       setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n //     } else if (e.altKey && e.key.toLowerCase() === \"h\") {\n //       e.preventDefault();\n //       setShowShortcuts(true);\n //     }\n //   };\n //   window.addEventListener(\"keydown\", handler);\n //   return () => window.removeEventListener(\"keydown\", handler);\n // }, [theme, setTheme]);\n_s(CodeEditor, \"Cf9tT16S7JToKFFdKJngpzRFyDc=\", false, function() {\n    return [\n        _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme\n    ];\n});\n_c = CodeEditor;\nvar _c;\n$RefreshReg$(_c, \"CodeEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CodeEditor.tsx\n"));

/***/ })

});