"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var monaco_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! monaco-editor */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.main.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlignLeft,FiCode,FiCopy,FiDownload,FiHelpCircle,FiLoader,FiMoon,FiPlay,FiSearch,FiSun,FiUpload,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_socketService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/socketService */ \"(app-pages-browser)/./src/services/socketService.ts\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/throttle */ \"(app-pages-browser)/./node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/SocketContext */ \"(app-pages-browser)/./src/context/SocketContext.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_formatCode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/formatCode */ \"(app-pages-browser)/./src/utils/formatCode.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/ThemeContext */ \"(app-pages-browser)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CodeEditor(param) {\n    let { roomId, username: initialUsername } = param;\n    _s();\n    const { isConnected } = (0,_context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket)();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"// Start coding...\");\n    // Use a ref to always track the latest code value\n    const latestCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"// Start coding...\");\n    // Get username from localStorage if available, otherwise use initialUsername\n    const storedUsername =  true ? localStorage.getItem(\"username\") : 0;\n    console.log(\"Initial username: \".concat(initialUsername, \", Stored username: \").concat(storedUsername));\n    // Track the username internally to handle server validation\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUsername || initialUsername);\n    const [typingUser, setTypingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeUsers, setActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUserList, setShowUserList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"javascript\");\n    const [copySuccess, setCopySuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [teacherSelectionDecorations, setTeacherSelectionDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [teacherTextHighlightDecorations, setTeacherTextHighlightDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [teacherCursorPosition, setTeacherCursorPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Code execution states\n    const [isExecuting, setIsExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionOutput, setExecutionOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [executionError, setExecutionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOutput, setShowOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Piston API runtimes\n    const [runtimes, setRuntimes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [runtimesLoaded, setRuntimesLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    // Theme and minimap state (ensure these are always defined at the top)\n    const { theme, setTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme)();\n    const [showShortcuts, setShowShortcuts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [minimapEnabled, setMinimapEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Language-specific code snippets\n    const SNIPPETS = {\n        javascript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            }\n        ],\n        typescript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction(): void {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            },\n            {\n                label: \"Type Declaration\",\n                value: \"type MyType = {\\n  key: string;\\n};\"\n            }\n        ],\n        python3: [\n            {\n                label: \"Function\",\n                value: \"def my_function():\\n    # code\\n    pass\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in range(10):\\n    # code\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition:\\n    # code\"\n            },\n            {\n                label: \"Print\",\n                value: \"print('Hello, World!')\"\n            }\n        ],\n        java: [\n            {\n                label: \"Main Method\",\n                value: \"public static void main(String[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"System.out.println(\\\"Hello, World!\\\");\"\n            }\n        ],\n        csharp: [\n            {\n                label: \"Main Method\",\n                value: \"static void Main(string[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"Console.WriteLine(\\\"Hello, World!\\\");\"\n            }\n        ],\n        c: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"printf(\\\"Hello, World!\\\\n\\\");\"\n            }\n        ],\n        cpp: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"std::cout << \\\"Hello, World!\\\" << std::endl;\"\n            }\n        ],\n        go: [\n            {\n                label: \"Main Function\",\n                value: \"func main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i := 0; i < 10; i++ {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"fmt.Println(\\\"Hello, World!\\\")\"\n            }\n        ],\n        ruby: [\n            {\n                label: \"Method\",\n                value: \"def my_method\\n  # code\\nend\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..9\\n  # code\\nend\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition\\n  # code\\nend\"\n            },\n            {\n                label: \"Print\",\n                value: \"puts 'Hello, World!'\"\n            }\n        ],\n        rust: [\n            {\n                label: \"Main Function\",\n                value: \"fn main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..10 {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"println!(\\\"Hello, World!\\\");\"\n            }\n        ],\n        php: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for ($i = 0; $i < 10; $i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if ($condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"echo 'Hello, World!';\"\n            }\n        ]\n    };\n    // Declare the `socketService` instance at the top of the file\n    const socketService = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n    // Leave room handler\n    const handleLeaveRoom = ()=>{\n        socketService.leaveRoom(roomId);\n        router.push(\"/dashboard\");\n    };\n    // Handle editor mounting\n    const handleEditorDidMount = (editor)=>{\n        editorRef.current = editor;\n        setIsLoading(false);\n        // Auto-focus the editor after mounting\n        setTimeout(()=>{\n            if (editor) {\n                editor.focus();\n                // Add keyboard shortcut (Ctrl+Enter) to run code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.CtrlCmd | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.Enter, ()=>{\n                    executeCode();\n                });\n                // Add keyboard shortcut (Shift+Alt+F) to format code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Shift | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Alt | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.KeyF, ()=>{\n                    formatCurrentCode();\n                });\n                // Add teacher cursor and selection listeners\n                if (userRole === 'teacher') {\n                    // Track cursor position changes\n                    editor.onDidChangeCursorPosition((e)=>{\n                        const position = e.position;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        if (roomId) {\n                            // Send cursor position to students\n                            socketServiceInstance.sendTeacherCursorPosition(roomId, {\n                                lineNumber: position.lineNumber,\n                                column: position.column\n                            });\n                        }\n                    });\n                    // Track selection changes for text highlighting\n                    editor.onDidChangeCursorSelection((e)=>{\n                        console.log('🎯 Teacher cursor selection changed:', {\n                            selection: e.selection,\n                            isEmpty: e.selection.isEmpty(),\n                            userRole: userRole,\n                            roomId: roomId\n                        });\n                        const selection = e.selection;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        if (!selection.isEmpty() && roomId) {\n                            // Send text highlight to students\n                            console.log('📤 Teacher sending text selection to students:', {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            });\n                            socketServiceInstance.sendTeacherTextHighlight(roomId, {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            });\n                        } else if (roomId) {\n                            // Clear text highlight when teacher deselects\n                            console.log('🧹 Teacher clearing text selection');\n                            socketServiceInstance.clearTeacherTextHighlight(roomId);\n                        }\n                    });\n                }\n            }\n        }, 500) // Small delay to ensure the editor is fully ready\n        ;\n    };\n    // Track if the change is from a remote update\n    const isRemoteUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Create a throttled version of the code change handler\n    // This prevents sending too many updates when typing quickly\n    // but ensures updates are sent at regular intervals\n    const throttledCodeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default()({\n        \"CodeEditor.useRef\": (code)=>{\n            if (roomId && isConnected) {\n                console.log(\"Sending throttled code update to room \".concat(roomId, \", length: \").concat(code.length));\n                socketService.sendCodeChange(roomId, code);\n            }\n        }\n    }[\"CodeEditor.useRef\"], 50) // Use throttle with a short interval for more responsive updates\n    ).current;\n    // Create a debounced version of the typing notification\n    const debouncedTypingNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default()({\n        \"CodeEditor.useCallback[debouncedTypingNotification]\": ()=>{\n            if (roomId && isConnected) {\n                // Always send the current username with typing notifications\n                console.log(\"Sending typing notification with username: \".concat(username));\n                socketService.sendTyping(roomId, username);\n            }\n        }\n    }[\"CodeEditor.useCallback[debouncedTypingNotification]\"], 1000), [\n        roomId,\n        isConnected,\n        username\n    ]);\n    // Handle editor change\n    const handleEditorChange = (value)=>{\n        if (typeof value !== \"string\") return;\n        // Make sure loading is off during code changes\n        setIsLoading(false);\n        // If this change is from a remote update, just update the state and return\n        if (isRemoteUpdate.current) {\n            console.log(\"Handling remote update, not emitting\");\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            isRemoteUpdate.current = false;\n            return;\n        }\n        // Only process if the value actually changed\n        if (value !== latestCodeRef.current) {\n            // Update local state immediately\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            // Send typing notification (debounced)\n            debouncedTypingNotification();\n            // Send code update (throttled)\n            throttledCodeChange(value);\n            // If not connected, try to reconnect\n            if (!isConnected) {\n                console.log(\"Socket not connected, attempting to reconnect\");\n                socketService.connect();\n            }\n        }\n    };\n    // Copy code to clipboard\n    const copyCodeToClipboard = ()=>{\n        if (navigator.clipboard && code) {\n            navigator.clipboard.writeText(code).then(()=>{\n                setCopySuccess(true);\n                setTimeout(()=>setCopySuccess(false), 2000);\n            }).catch((err)=>{\n                console.error('Failed to copy code: ', err);\n            });\n        }\n    };\n    // Toggle user list\n    const toggleUserList = ()=>{\n        setShowUserList((prev)=>!prev);\n    };\n    // Change language\n    const changeLanguage = (lang)=>{\n        setLanguage(lang);\n        // Log available runtimes for this language\n        if (runtimesLoaded && runtimes.length > 0) {\n            const availableRuntimes = runtimes.filter((runtime)=>runtime.language === lang || runtime.aliases && runtime.aliases.includes(lang));\n            if (availableRuntimes.length > 0) {\n                console.log(\"Available runtimes for \".concat(lang, \":\"), availableRuntimes);\n            } else {\n                console.log(\"No runtimes available for \".concat(lang));\n            }\n        }\n    };\n    // Format code\n    const formatCurrentCode = ()=>{\n        if (!code || !editorRef.current) return;\n        try {\n            // Format the code based on the current language\n            const formattedCode = (0,_utils_formatCode__WEBPACK_IMPORTED_MODULE_8__.formatCode)(code, language);\n            // Only update if the code actually changed\n            if (formattedCode !== code) {\n                // Update the editor\n                const model = editorRef.current.getModel();\n                if (model) {\n                    // Store current cursor position/selection\n                    const currentPosition = editorRef.current.getPosition();\n                    const currentSelection = editorRef.current.getSelection();\n                    // Update the editor content\n                    editorRef.current.executeEdits('format', [\n                        {\n                            range: model.getFullModelRange(),\n                            text: formattedCode,\n                            forceMoveMarkers: true\n                        }\n                    ]);\n                    // Restore cursor position if possible\n                    if (currentPosition) {\n                        editorRef.current.setPosition(currentPosition);\n                    }\n                    if (currentSelection) {\n                        editorRef.current.setSelection(currentSelection);\n                    }\n                    // Update state and ref\n                    setCode(formattedCode);\n                    latestCodeRef.current = formattedCode;\n                    // Send the formatted code to other users\n                    if (roomId && isConnected) {\n                        console.log(\"Sending formatted code to room \".concat(roomId, \", length: \").concat(formattedCode.length));\n                        socketService.sendCodeChange(roomId, formattedCode);\n                    }\n                    // Show a success message\n                    console.log('Code formatted successfully');\n                }\n            } else {\n                console.log('Code is already formatted');\n            }\n        } catch (error) {\n            console.error('Error formatting code:', error);\n        }\n    };\n    // Clear execution output\n    const clearOutput = ()=>{\n        setExecutionOutput(null);\n        setExecutionError(null);\n    };\n    // Fetch and store available runtimes from Piston API\n    const fetchRuntimes = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get('https://emkc.org/api/v2/piston/runtimes');\n            console.log('Piston API status:', response.status);\n            console.log('Available runtimes:', response.data);\n            // Store the runtimes in state\n            setRuntimes(response.data);\n            setRuntimesLoaded(true);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error fetching Piston API runtimes:', error);\n            return {\n                success: false,\n                error: axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) ? \"\".concat(error.message, \" - \").concat(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) || 'Unknown', \" \").concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText) || '') : String(error)\n            };\n        }\n    };\n    // Check if the Piston API is available\n    const checkPistonAPI = async ()=>{\n        // If we haven't loaded runtimes yet, fetch them\n        if (!runtimesLoaded) {\n            return await fetchRuntimes();\n        }\n        // If we already have runtimes, just return success\n        if (runtimes.length > 0) {\n            return {\n                success: true,\n                data: runtimes\n            };\n        }\n        // If we've tried to load runtimes but have none, try again\n        return await fetchRuntimes();\n    };\n    // Test the Piston API with a hardcoded sample payload\n    const testPistonAPI = async ()=>{\n        setIsExecuting(true);\n        setExecutionError(null);\n        setExecutionOutput(null);\n        setShowOutput(true);\n        try {\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Find JavaScript runtime\n            const jsRuntimes = apiStatus.data.filter((runtime)=>runtime.language === \"javascript\" || runtime.aliases && runtime.aliases.includes(\"javascript\"));\n            if (jsRuntimes.length === 0) {\n                setExecutionError('No JavaScript runtime found. Please try a different language.');\n                return;\n            }\n            const jsRuntime = jsRuntimes[0];\n            console.log(\"Using JavaScript runtime: \".concat(jsRuntime.language, \" \").concat(jsRuntime.version));\n            // Sample JavaScript code that should work\n            const samplePayload = {\n                language: jsRuntime.language,\n                version: jsRuntime.version,\n                files: [\n                    {\n                        name: \"main.js\",\n                        content: \"console.log('Hello, World!');\"\n                    }\n                ],\n                stdin: \"\",\n                args: []\n            };\n            console.log('Testing Piston API with sample payload:', JSON.stringify(samplePayload, null, 2));\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', samplePayload);\n            console.log('Sample execution response:', response.data);\n            if (response.data.run) {\n                setExecutionOutput(\"API Test Successful!\\n\\n\" + \"Output: \" + response.data.run.stdout + \"\\n\\n\" + \"The API is working correctly. You can now run your own code.\");\n            } else {\n                setExecutionError('API test failed. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error testing Piston API:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                setExecutionError(\"API Test Failed: \".concat(error.response.status, \" \").concat(error.response.statusText, \"\\n\\n\") + JSON.stringify(error.response.data, null, 2));\n            } else {\n                setExecutionError(error instanceof Error ? \"API Test Error: \".concat(error.message) : 'An unknown error occurred while testing the API.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // Execute code using Piston API\n    const executeCode = async ()=>{\n        if (!code || isExecuting) return;\n        // Map Monaco editor language to Piston API language\n        const languageMap = {\n            javascript: \"javascript\",\n            typescript: \"typescript\",\n            python: \"python3\",\n            python3: \"python3\",\n            java: \"java\",\n            csharp: \"csharp\",\n            c: \"c\",\n            cpp: \"cpp\",\n            go: \"go\",\n            ruby: \"ruby\",\n            rust: \"rust\",\n            php: \"php\"\n        };\n        // Get the mapped language\n        const pistonLanguage = languageMap[language] || language;\n        // Check if we have runtimes loaded\n        if (!runtimesLoaded || runtimes.length === 0) {\n            setExecutionError('Runtimes not loaded yet. Please try again in a moment.');\n            setShowOutput(true);\n            return;\n        }\n        // Find available runtimes for the selected language\n        const availableRuntimes = runtimes.filter((runtime)=>runtime.language === pistonLanguage || runtime.aliases && runtime.aliases.includes(pistonLanguage));\n        // Check if the language is supported\n        if (availableRuntimes.length === 0) {\n            // Get a list of supported languages from the runtimes\n            const supportedLanguages = [\n                ...new Set(runtimes.flatMap((runtime)=>[\n                        runtime.language,\n                        ...runtime.aliases || []\n                    ]))\n            ].sort();\n            setExecutionError(\"The language '\".concat(language, \"' (mapped to '\").concat(pistonLanguage, \"') is not supported by the Piston API.\\n\\n\") + \"Supported languages: \".concat(supportedLanguages.join(', ')));\n            setShowOutput(true);\n            return;\n        }\n        // Get the latest version of the runtime\n        const selectedRuntime = availableRuntimes[0];\n        try {\n            setIsExecuting(true);\n            setExecutionError(null);\n            setExecutionOutput(null);\n            setShowOutput(true);\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Determine file extension based on language\n            let fileExtension = '';\n            if (selectedRuntime.language === 'python3' || selectedRuntime.language === 'python') fileExtension = '.py';\n            else if (selectedRuntime.language === 'javascript') fileExtension = '.js';\n            else if (selectedRuntime.language === 'typescript') fileExtension = '.ts';\n            else if (selectedRuntime.language === 'java') fileExtension = '.java';\n            else if (selectedRuntime.language === 'csharp') fileExtension = '.cs';\n            else if (selectedRuntime.language === 'c') fileExtension = '.c';\n            else if (selectedRuntime.language === 'cpp') fileExtension = '.cpp';\n            else if (selectedRuntime.language === 'go') fileExtension = '.go';\n            else if (selectedRuntime.language === 'rust') fileExtension = '.rs';\n            else if (selectedRuntime.language === 'ruby') fileExtension = '.rb';\n            else if (selectedRuntime.language === 'php') fileExtension = '.php';\n            else fileExtension = \".\".concat(selectedRuntime.language);\n            console.log(\"Selected runtime: \".concat(selectedRuntime.language, \" \").concat(selectedRuntime.version));\n            // Prepare the payload according to Piston API documentation\n            const payload = {\n                language: selectedRuntime.language,\n                version: selectedRuntime.version,\n                files: [\n                    {\n                        name: \"main\".concat(fileExtension),\n                        content: code\n                    }\n                ],\n                stdin: '',\n                args: [],\n                compile_timeout: 10000,\n                run_timeout: 5000\n            };\n            // Log the payload for debugging\n            console.log(\"Executing \".concat(pistonLanguage, \" code with payload:\"), JSON.stringify(payload, null, 2));\n            // Make the API request\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', payload);\n            console.log('Execution response:', response.data);\n            const result = response.data;\n            if (result.run) {\n                // Format the output\n                let output = '';\n                let hasOutput = false;\n                // Add compile output if available (for compiled languages)\n                if (result.compile && result.compile.stderr) {\n                    output += \"Compilation output:\\n\".concat(result.compile.stderr, \"\\n\\n\");\n                    hasOutput = true;\n                }\n                // Add standard output\n                if (result.run.stdout) {\n                    output += result.run.stdout;\n                    hasOutput = true;\n                }\n                // Add error output\n                if (result.run.stderr) {\n                    if (hasOutput) output += '\\n';\n                    output += \"Error output:\\n\".concat(result.run.stderr);\n                    hasOutput = true;\n                }\n                // Add exit code if non-zero\n                if (result.run.code !== 0) {\n                    if (hasOutput) output += '\\n';\n                    output += \"\\nProcess exited with code \".concat(result.run.code);\n                    hasOutput = true;\n                }\n                if (!hasOutput) {\n                    output = 'Program executed successfully with no output.';\n                }\n                setExecutionOutput(output);\n            } else {\n                setExecutionError('Failed to execute code. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error executing code:', error);\n            // Handle Axios errors with more detailed information\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                const statusCode = error.response.status;\n                const responseData = error.response.data;\n                console.error('API Error Details:', {\n                    status: statusCode,\n                    data: responseData\n                });\n                // Format a more helpful error message\n                if (statusCode === 400) {\n                    setExecutionError(\"API Error (400 Bad Request): \".concat(JSON.stringify(responseData), \"\\n\\n\") + 'This usually means the API request format is incorrect. ' + 'Please check the console for more details.');\n                } else if (statusCode === 429) {\n                    setExecutionError('Rate limit exceeded. Please try again later.');\n                } else {\n                    setExecutionError(\"API Error (\".concat(statusCode, \"): \").concat(JSON.stringify(responseData), \"\\n\\n\") + 'Please check the console for more details.');\n                }\n            } else {\n                // Handle non-Axios errors\n                setExecutionError(error instanceof Error ? \"Error: \".concat(error.message) : 'An unknown error occurred while executing the code.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // This function will be called when we receive a code update from another user\n    const handleCodeUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleCodeUpdate]\": (incomingCode)=>{\n            console.log(\"Remote code update received, length:\", incomingCode.length);\n            // Make sure loading is off during code updates\n            setIsLoading(false);\n            // Only update if the code is different from our latest code\n            if (incomingCode !== latestCodeRef.current) {\n                try {\n                    // Set the flag to indicate this is a remote update\n                    isRemoteUpdate.current = true;\n                    // Update the editor if it's mounted\n                    if (editorRef.current) {\n                        console.log(\"Updating editor with remote code\");\n                        // Use the editor's model to update the value to preserve cursor position\n                        const model = editorRef.current.getModel();\n                        if (model) {\n                            // Store current cursor position/selection\n                            const currentPosition = editorRef.current.getPosition();\n                            const currentSelection = editorRef.current.getSelection();\n                            // Use executeEdits with better handling of cursor position\n                            editorRef.current.executeEdits('remote', [\n                                {\n                                    range: model.getFullModelRange(),\n                                    text: incomingCode,\n                                    forceMoveMarkers: true\n                                }\n                            ]);\n                            // Restore cursor position if possible\n                            if (currentPosition) {\n                                editorRef.current.setPosition(currentPosition);\n                            }\n                            if (currentSelection) {\n                                editorRef.current.setSelection(currentSelection);\n                            }\n                            // Also update our state and ref\n                            setCode(incomingCode);\n                            latestCodeRef.current = incomingCode;\n                        }\n                    } else {\n                        // If editor isn't mounted yet, just update the state and ref\n                        console.log(\"Editor not mounted, updating state only\");\n                        setCode(incomingCode);\n                        latestCodeRef.current = incomingCode;\n                        isRemoteUpdate.current = false;\n                    }\n                } catch (error) {\n                    console.error(\"Error updating editor with remote code:\", error);\n                    isRemoteUpdate.current = false;\n                }\n            } else {\n                console.log(\"Remote code matches current code, ignoring\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleCodeUpdate]\"], []);\n    // State to track cursor positions\n    const [cursorPositions, setCursorPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Emit cursor movement\n    const handleMouseMove = (e)=>{\n        const position = {\n            x: e.clientX,\n            y: e.clientY\n        };\n        _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().sendCursorMove(roomId, username, position);\n    };\n    // Listen for cursor movement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            const handleCursorMove = {\n                \"CodeEditor.useEffect.handleCursorMove\": (param)=>{\n                    let { userId, position } = param;\n                    setCursorPositions({\n                        \"CodeEditor.useEffect.handleCursorMove\": (prev)=>({\n                                ...prev,\n                                [userId]: position\n                            })\n                    }[\"CodeEditor.useEffect.handleCursorMove\"]);\n                }\n            }[\"CodeEditor.useEffect.handleCursorMove\"];\n            _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().on(\"cursor-move\", handleCursorMove);\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().off(\"cursor-move\", handleCursorMove);\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId\n    ]);\n    // Render cursors\n    const renderCursors = ()=>{\n        return Object.entries(cursorPositions).map((param)=>{\n            let [userId, position] = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    left: position.x,\n                    top: position.y,\n                    backgroundColor: \"red\",\n                    width: 10,\n                    height: 10,\n                    borderRadius: \"50%\",\n                    pointerEvents: \"none\"\n                }\n            }, userId, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 773,\n                columnNumber: 7\n            }, this);\n        });\n    };\n    // Fetch runtimes when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            fetchRuntimes();\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    // Handle request for initial code from new users\n    const handleGetInitialCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleGetInitialCode]\": (data)=>{\n            console.log(\"Received request for initial code from \".concat(data.requestingUsername, \" (\").concat(data.requestingUserId, \")\"));\n            // Only respond if we have code and we're connected\n            if (roomId && latestCodeRef.current && latestCodeRef.current.trim() !== \"// Start coding...\") {\n                const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                console.log(\"Sending initial code to \".concat(data.requestingUsername, \", length: \").concat(latestCodeRef.current.length));\n                socketServiceInstance.sendInitialCode(roomId, latestCodeRef.current, data.requestingUserId);\n            } else {\n                console.log(\"Not sending initial code - no meaningful code to share or not in room\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleGetInitialCode]\"], [\n        roomId\n    ]);\n    // Handle receiving initial code as a new user\n    const handleInitialCodeReceived = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleInitialCodeReceived]\": (data)=>{\n            console.log(\"Received initial code, length: \".concat(data.code.length));\n            // Only apply initial code if we don't have meaningful code yet\n            // This prevents overwriting code that the user might have already started typing\n            if (latestCodeRef.current === \"// Start coding...\" || latestCodeRef.current.trim() === \"\") {\n                console.log(\"Applying received initial code\");\n                // Set the flag to indicate this is a remote update\n                isRemoteUpdate.current = true;\n                // Update the code state and ref\n                setCode(data.code);\n                latestCodeRef.current = data.code;\n                // Update the editor if it's mounted\n                if (editorRef.current) {\n                    editorRef.current.setValue(data.code);\n                }\n            } else {\n                console.log(\"Not applying initial code - user already has meaningful code\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleInitialCodeReceived]\"], []);\n    // Handle teacher selection highlighting\n    const handleTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherSelection]\": (data)=>{\n            console.log(\"Received teacher selection from \".concat(data.teacherName, \":\"), data.selection);\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't show teacher highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model || !data.selection) {\n                    return;\n                }\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.selection.startLineNumber, data.selection.startColumn, data.selection.endLineNumber, data.selection.endColumn);\n                // Clear previous teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-selection-highlight',\n                            hoverMessage: {\n                                value: \"Teacher \".concat(data.teacherName, \" highlighted this text\")\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges\n                        }\n                    }\n                ]);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error applying teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle clearing teacher selection\n    const handleClearTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherSelection]\": (data)=>{\n            console.log(\"Teacher \".concat(data.teacherName, \" cleared selection\"));\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                // Clear all teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, []);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error clearing teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle teacher cursor position updates\n    const handleTeacherCursorPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherCursorPosition]\": (data)=>{\n            console.log(\"Teacher \".concat(data.teacherName, \" cursor at line \").concat(data.position.lineNumber, \", column \").concat(data.position.column));\n            if (userRole === 'teacher') {\n                return; // Don't show cursor to the teacher themselves\n            }\n            // Update teacher cursor position state\n            setTeacherCursorPosition({\n                lineNumber: data.position.lineNumber,\n                column: data.position.column,\n                teacherName: data.teacherName\n            });\n        }\n    }[\"CodeEditor.useCallback[handleTeacherCursorPosition]\"], [\n        userRole\n    ]);\n    // Handle teacher text highlight with enhanced error handling and multiple CSS class fallbacks\n    const handleTeacherTextHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherTextHighlight]\": (data)=>{\n            console.log(\"\\uD83C\\uDFA8 Teacher \".concat(data.teacherName, \" highlighted text:\"), data.selection);\n            console.log(\"\\uD83D\\uDD0D Debug info:\", {\n                editorAvailable: !!editorRef.current,\n                userRole: userRole,\n                roomId: roomId,\n                currentDecorations: teacherTextHighlightDecorations.length,\n                monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined'\n            });\n            if (!editorRef.current || userRole === 'teacher') {\n                console.log('⏭️ Skipping teacher text highlight: editor not available or user is teacher');\n                return; // Don't show highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('❌ Monaco Editor model not available');\n                    return;\n                }\n                if (!data.selection) {\n                    console.error('❌ Selection data is missing');\n                    return;\n                }\n                // Validate selection data with more robust checking\n                const { startLineNumber, startColumn, endLineNumber, endColumn } = data.selection;\n                if (typeof startLineNumber !== 'number' || startLineNumber <= 0 || typeof startColumn !== 'number' || startColumn <= 0 || typeof endLineNumber !== 'number' || endLineNumber <= 0 || typeof endColumn !== 'number' || endColumn <= 0) {\n                    console.error('❌ Invalid selection data:', data.selection);\n                    return;\n                }\n                // Ensure the selection is within the model bounds\n                const lineCount = model.getLineCount();\n                if (startLineNumber > lineCount || endLineNumber > lineCount) {\n                    console.warn('⚠️ Selection extends beyond model bounds, adjusting...');\n                    const adjustedEndLine = Math.min(endLineNumber, lineCount);\n                    const adjustedEndColumn = adjustedEndLine === lineCount ? model.getLineMaxColumn(adjustedEndLine) : endColumn;\n                    console.log(\"Adjusted selection: (\".concat(startLineNumber, \", \").concat(startColumn, \", \").concat(adjustedEndLine, \", \").concat(adjustedEndColumn, \")\"));\n                }\n                console.log(\"✅ Creating Monaco Range: (\".concat(startLineNumber, \", \").concat(startColumn, \", \").concat(endLineNumber, \", \").concat(endColumn, \")\"));\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(startLineNumber, startColumn, endLineNumber, endColumn);\n                console.log('✅ Monaco Range created successfully:', range);\n                // Apply decoration with multiple CSS class options for better compatibility\n                console.log('🎨 Applying teacher text highlight decoration...');\n                const decorationOptions = {\n                    range: range,\n                    options: {\n                        className: 'teacher-highlight teacher-text-highlight',\n                        hoverMessage: {\n                            value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" highlighted this text\\n\\nClick to focus on this selection\")\n                        },\n                        stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                        // Add inline styles as fallback\n                        inlineClassName: 'teacher-highlight-inline'\n                    }\n                };\n                // Clear previous decorations and apply new ones\n                const newDecorations = editor.deltaDecorations(teacherTextHighlightDecorations, [\n                    decorationOptions\n                ]);\n                console.log('✅ Teacher text highlight decorations applied:', newDecorations);\n                console.log('🔍 Decoration details:', decorationOptions);\n                setTeacherTextHighlightDecorations(newDecorations);\n                // Force a layout update to ensure the decoration is visible\n                setTimeout({\n                    \"CodeEditor.useCallback[handleTeacherTextHighlight]\": ()=>{\n                        editor.layout();\n                        console.log('🔄 Editor layout refreshed');\n                    }\n                }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"], 100);\n            } catch (error) {\n                var _editorRef_current;\n                console.error('❌ Error applying teacher text highlight:', error);\n                console.error('🔍 Error details:', {\n                    editorAvailable: !!editorRef.current,\n                    modelAvailable: !!((_editorRef_current = editorRef.current) === null || _editorRef_current === void 0 ? void 0 : _editorRef_current.getModel()),\n                    selectionData: data.selection,\n                    userRole: userRole,\n                    monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined',\n                    currentDecorations: teacherTextHighlightDecorations\n                });\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"], [\n        userRole,\n        teacherTextHighlightDecorations\n    ]);\n    // Handle clearing teacher text highlight\n    const handleClearTeacherTextHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherTextHighlight]\": (data)=>{\n            console.log(\"\\uD83E\\uDDF9 Teacher \".concat(data.teacherName, \" cleared text highlight\"));\n            console.log(\"\\uD83D\\uDD0D Clear debug info:\", {\n                editorAvailable: !!editorRef.current,\n                userRole: userRole,\n                roomId: roomId,\n                currentDecorations: teacherTextHighlightDecorations.length\n            });\n            if (!editorRef.current || userRole === 'teacher') {\n                console.log('⏭️ Skipping clear teacher text highlight: editor not available or user is teacher');\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('Monaco Editor model not available for clearing highlights');\n                    return;\n                }\n                console.log('Clearing teacher text highlight decorations...');\n                // Clear all teacher text highlight decorations\n                const newDecorations = editor.deltaDecorations(teacherTextHighlightDecorations, []);\n                console.log('Teacher text highlight decorations cleared:', newDecorations);\n                setTeacherTextHighlightDecorations(newDecorations);\n            } catch (error) {\n                var _editorRef_current;\n                console.error('Error clearing teacher text highlight:', error);\n                console.error('Error details:', {\n                    editorAvailable: !!editorRef.current,\n                    modelAvailable: !!((_editorRef_current = editorRef.current) === null || _editorRef_current === void 0 ? void 0 : _editorRef_current.getModel()),\n                    userRole: userRole,\n                    currentDecorations: teacherTextHighlightDecorations\n                });\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherTextHighlight]\"], [\n        userRole,\n        teacherTextHighlightDecorations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            // Handle user typing notifications\n            const handleUserTyping = {\n                \"CodeEditor.useEffect.handleUserTyping\": (param)=>{\n                    let { username, userId } = param;\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    // Don't show typing indicator for current user\n                    if (userId && userId === currentUserId) {\n                        return;\n                    }\n                    // Use the exact username without any modifications\n                    // This ensures we display exactly what the user entered on the dashboard\n                    setTypingUser(username);\n                    console.log(\"User typing: \".concat(username));\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserTyping\": ()=>setTypingUser(null)\n                    }[\"CodeEditor.useEffect.handleUserTyping\"], 2000);\n                }\n            }[\"CodeEditor.useEffect.handleUserTyping\"];\n            // Handle user list updates from both user-joined/user-left and room-users-updated events\n            const handleUserListUpdate = {\n                \"CodeEditor.useEffect.handleUserListUpdate\": (data)=>{\n                    console.log('User list update received:', data);\n                    let users = [];\n                    // Handle different event formats\n                    if (Array.isArray(data)) {\n                        // This is from user-joined or user-left events\n                        users = data;\n                    } else if (data && Array.isArray(data.users)) {\n                        // This is from room-users-updated event\n                        users = data.users.map({\n                            \"CodeEditor.useEffect.handleUserListUpdate\": (user)=>{\n                                if (typeof user === 'string') {\n                                    // If it's just a username string, convert to user object\n                                    return {\n                                        username: user\n                                    };\n                                }\n                                return user;\n                            }\n                        }[\"CodeEditor.useEffect.handleUserListUpdate\"]);\n                    }\n                    console.log('Processed users:', users);\n                    console.log('Current username state:', username);\n                    console.log('Stored username:', localStorage.getItem('username'));\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    console.log('Current userId from localStorage:', currentUserId);\n                    // Filter out any invalid users and map to display names\n                    const usernames = users.filter({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>user && user.username\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]).map({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>{\n                            const displayName = user.username;\n                            const isCurrentUser = user.userId === currentUserId;\n                            return isCurrentUser ? \"\".concat(displayName, \" (you)\") : displayName;\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]);\n                    console.log('Setting active users:', usernames);\n                    // Only update if we have users to display\n                    if (usernames.length > 0) {\n                        setActiveUsers(usernames);\n                    } else if (activeUsers.length === 0) {\n                        // If we don't have users from the server but we're connected, add ourselves\n                        const storedUsername = localStorage.getItem('username');\n                        if (storedUsername) {\n                            setActiveUsers([\n                                \"\".concat(storedUsername, \" (you)\")\n                            ]);\n                        }\n                    }\n                    // Log the current state of the active users list\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserListUpdate\": ()=>{\n                            console.log('Active users state after update:', activeUsers);\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate\"], 100);\n                }\n            }[\"CodeEditor.useEffect.handleUserListUpdate\"];\n            // Register event listeners\n            const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n            socketServiceInstance.on('code-update', handleCodeUpdate);\n            socketServiceInstance.on('user-typing', handleUserTyping);\n            socketServiceInstance.on('user-joined', handleUserListUpdate);\n            socketServiceInstance.on('user-left', handleUserListUpdate);\n            socketServiceInstance.on('room-users-updated', handleUserListUpdate);\n            socketServiceInstance.on('get-initial-code', handleGetInitialCode);\n            socketServiceInstance.on('initial-code-received', handleInitialCodeReceived);\n            socketServiceInstance.on('teacher-selection', handleTeacherSelection);\n            socketServiceInstance.on('clear-teacher-selection', handleClearTeacherSelection);\n            socketServiceInstance.on('teacher-cursor-position', handleTeacherCursorPosition);\n            socketServiceInstance.on('teacher-text-highlight', handleTeacherTextHighlight);\n            socketServiceInstance.on('clear-teacher-text-highlight', handleClearTeacherTextHighlight);\n            // Join the room when component mounts\n            if (roomId) {\n                console.log(\"Joining room:\", roomId);\n                // Only show loading on initial join, not for code updates\n                if (!editorRef.current) {\n                    setIsLoading(true);\n                }\n                const joinRoom = {\n                    \"CodeEditor.useEffect.joinRoom\": async ()=>{\n                        try {\n                            if (socketServiceInstance.isConnected()) {\n                                console.log(\"Socket connected, joining room\");\n                                const { username: validatedUsername, users, role } = await socketServiceInstance.joinRoom(roomId, username);\n                                console.log(\"Successfully joined room:\", roomId);\n                                console.log(\"Users in room:\", users);\n                                console.log(\"User role:\", role);\n                                if (validatedUsername !== username) {\n                                    console.log(\"Server validated username from \".concat(username, \" to \").concat(validatedUsername));\n                                    setUsername(validatedUsername);\n                                    localStorage.setItem('username', validatedUsername);\n                                }\n                                // Set user role\n                                if (role) {\n                                    setUserRole(role);\n                                    console.log(\"User role set to: \".concat(role));\n                                }\n                                if (users && Array.isArray(users)) {\n                                    console.log('Setting active users from join response');\n                                    handleUserListUpdate(users);\n                                }\n                                setIsLoading(false);\n                            } else {\n                                console.log(\"Socket not connected, trying to connect...\");\n                                await new Promise({\n                                    \"CodeEditor.useEffect.joinRoom\": (resolve)=>socketServiceInstance.onConnect({\n                                            \"CodeEditor.useEffect.joinRoom\": ()=>resolve()\n                                        }[\"CodeEditor.useEffect.joinRoom\"])\n                                }[\"CodeEditor.useEffect.joinRoom\"]);\n                                // After connect, try join again\n                                await joinRoom();\n                                return;\n                            }\n                        } catch (error) {\n                            console.error(\"Error joining room:\", error);\n                            setIsLoading(false);\n                        }\n                    }\n                }[\"CodeEditor.useEffect.joinRoom\"];\n                // Start the join process\n                joinRoom();\n                // Set a timeout to hide the loading screen even if the join fails\n                setTimeout({\n                    \"CodeEditor.useEffect\": ()=>{\n                        setIsLoading(false);\n                    }\n                }[\"CodeEditor.useEffect\"], 3000);\n            }\n            // Clean up event listeners when component unmounts\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    // Use an instance of SocketService\n                    const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                    socketServiceInstance.off('code-update', handleCodeUpdate);\n                    socketServiceInstance.off('user-typing', handleUserTyping);\n                    socketServiceInstance.off('user-joined', handleUserListUpdate);\n                    socketServiceInstance.off('user-left', handleUserListUpdate);\n                    socketServiceInstance.off('room-users-updated', handleUserListUpdate);\n                    socketServiceInstance.off('get-initial-code', handleGetInitialCode);\n                    socketServiceInstance.off('initial-code-received', handleInitialCodeReceived);\n                    socketServiceInstance.off('teacher-selection', handleTeacherSelection);\n                    socketServiceInstance.off('clear-teacher-selection', handleClearTeacherSelection);\n                    socketServiceInstance.off('teacher-cursor-position', handleTeacherCursorPosition);\n                    socketServiceInstance.off('teacher-text-highlight', handleTeacherTextHighlight);\n                    socketServiceInstance.off('clear-teacher-text-highlight', handleClearTeacherTextHighlight);\n                    // Leave the room when component unmounts\n                    if (roomId) {\n                        socketServiceInstance.leaveRoom(roomId);\n                    }\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId,\n        initialUsername,\n        username,\n        handleCodeUpdate,\n        handleGetInitialCode,\n        handleInitialCodeReceived,\n        handleTeacherSelection,\n        handleClearTeacherSelection,\n        handleTeacherCursorPosition,\n        handleTeacherTextHighlight,\n        handleClearTeacherTextHighlight\n    ]);\n    // Download code as file\n    const handleDownload = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"code-\".concat(roomId || \"session\", \".txt\");\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    // Upload code from file\n    const handleUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            var _event_target;\n            if (typeof ((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result) === \"string\") {\n                setCode(event.target.result);\n                latestCodeRef.current = event.target.result;\n            }\n        };\n        reader.readAsText(file);\n    };\n    // Show Monaco's find/replace dialog\n    const handleFindReplace = ()=>{\n        if (editorRef.current) {\n            const action = editorRef.current.getAction(\"actions.find\");\n            if (action) action.run();\n        }\n    };\n    // Insert code snippet\n    const insertSnippet = (snippet)=>{\n        if (editorRef.current) {\n            const model = editorRef.current.getModel();\n            const position = editorRef.current.getPosition();\n            if (model && position) {\n                editorRef.current.executeEdits(\"snippet\", [\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(position.lineNumber, position.column, position.lineNumber, position.column),\n                        text: snippet,\n                        forceMoveMarkers: true\n                    }\n                ]);\n                editorRef.current.focus();\n            }\n        }\n    };\n    // Use the instance of SocketService for the connect method\n    const handleReconnect = ()=>{\n        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n        socketServiceInstance.connect();\n    };\n    // Manual test function for debugging teacher text highlighting\n    const testManualHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[testManualHighlight]\": ()=>{\n            console.log('🧪 Manual test highlight triggered');\n            if (!editorRef.current) {\n                console.error('❌ Editor not available for manual test');\n                return;\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('❌ Monaco Editor model not available for manual test');\n                    return;\n                }\n                console.log('✅ Manual test: Editor and model available');\n                // Create a test range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(1, 1, 1, 20);\n                console.log('✅ Manual test: Range created:', range);\n                // Apply decoration\n                const newDecorations = editor.deltaDecorations(teacherTextHighlightDecorations, [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-text-highlight',\n                            hoverMessage: {\n                                value: 'Manual test highlight - this should be blue!'\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges\n                        }\n                    }\n                ]);\n                console.log('✅ Manual test: Decorations applied:', newDecorations);\n                setTeacherTextHighlightDecorations(newDecorations);\n            } catch (error) {\n                console.error('❌ Manual test error:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[testManualHighlight]\"], [\n        teacherTextHighlightDecorations\n    ]);\n    // Add manual test to window for debugging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            if (true) {\n                window.testManualHighlight = testManualHighlight;\n                console.log('🔧 Manual test function added to window.testManualHighlight()');\n            }\n        }\n    }[\"CodeEditor.useEffect\"], [\n        testManualHighlight\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-[80vh] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-800 shadow-xl flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"bg-gray-100 dark:bg-zinc-800 px-4 py-2 flex flex-wrap items-center justify-between border-b border-gray-200 dark:border-gray-700 gap-2 min-h-[48px]\",\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    style: {\n                        zIndex: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-3 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 min-w-[90px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1353,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-400 truncate\",\n                                            children: isConnected ? 'Connected' : 'Disconnected'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1354,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded ml-1 transition-colors\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1358,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: language,\n                                    onChange: (e)=>changeLanguage(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"javascript\",\n                                            children: \"JavaScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1373,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"typescript\",\n                                            children: \"TypeScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1374,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"python3\",\n                                            children: \"Python\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1375,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"java\",\n                                            children: \"Java\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1376,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"csharp\",\n                                            children: \"C#\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1377,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"c\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1378,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"cpp\",\n                                            children: \"C++\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1379,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"go\",\n                                            children: \"Go\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1380,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ruby\",\n                                            children: \"Ruby\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1381,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rust\",\n                                            children: \"Rust\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1382,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"php\",\n                                            children: \"PHP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1383,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1368,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: formatCurrentCode,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm bg-blue-600 hover:bg-blue-700 text-white transition-colors whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiAlignLeft, {\n                                            size: 14\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1393,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Format\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1394,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: executeCode,\n                                    disabled: isExecuting,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm \".concat(isExecuting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700', \" text-white transition-colors whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: isExecuting ? 1 : 1.05\n                                    },\n                                    whileTap: {\n                                        scale: isExecuting ? 1 : 0.95\n                                    },\n                                    children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                                className: \"animate-spin\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1407,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Running...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1408,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiPlay, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1412,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Run Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1413,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1398,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Room:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1420,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-white dark:bg-zinc-700 px-2 py-1 rounded font-mono text-xs truncate max-w-[120px]\",\n                                            children: roomId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1421,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                            onClick: copyCodeToClipboard,\n                                            className: \"text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            title: \"Copy code to clipboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCopy, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1429,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1422,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1419,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1350,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-2 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowOutput((prev)=>!prev),\n                                    className: \"flex items-center space-x-1 text-sm \".concat(showOutput ? 'text-blue-500 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300', \" hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCode, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1442,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Output\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1443,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: toggleUserList,\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUsers, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1453,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                activeUsers.length,\n                                                \" online\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1454,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1447,\n                                    columnNumber: 13\n                                }, this),\n                                userRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    className: \"flex items-center space-x-1 text-xs font-medium px-2 py-1 rounded-full whitespace-nowrap \".concat(userRole === 'teacher' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'),\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: userRole === 'teacher' ? '👨‍🏫' : '👨‍🎓'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1469,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: userRole === 'teacher' ? 'Teacher' : 'Student'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1470,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1459,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"flex items-center space-x-1 text-sm text-yellow-600 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSun, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1482,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiMoon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1482,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1475,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setMinimapEnabled((v)=>!v),\n                                    className: \"flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Minimap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Minimap\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1493,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1486,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleFindReplace,\n                                    className: \"flex items-center space-x-1 text-sm text-green-600 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Find/Replace\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSearch, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1504,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1497,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleDownload,\n                                    className: \"flex items-center space-x-1 text-sm text-indigo-600 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Download Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiDownload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1515,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"flex items-center space-x-1 text-sm text-pink-600 dark:text-pink-300 bg-pink-100 dark:bg-pink-900/30 hover:bg-pink-200 dark:hover:bg-pink-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Upload Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUpload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1526,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1519,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \".js,.ts,.py,.java,.c,.cpp,.go,.rb,.rs,.php,.txt\",\n                                    style: {\n                                        display: \"none\"\n                                    },\n                                    onChange: handleUpload\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1528,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    onChange: (e)=>e.target.value && insertSnippet(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[100px]\",\n                                    defaultValue: \"\",\n                                    title: \"Insert Snippet\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Snippets\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1543,\n                                            columnNumber: 15\n                                        }, this),\n                                        (SNIPPETS[language] || SNIPPETS['javascript']).map((snippet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: snippet.value,\n                                                children: snippet.label\n                                            }, snippet.label, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1545,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1537,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowShortcuts(true),\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Keyboard Shortcuts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiHelpCircle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1557,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1550,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleLeaveRoom,\n                                    className: \"flex items-center space-x-1 text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors px-2 py-1 rounded-md ml-2 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Leave Room\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1567,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1561,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1434,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1343,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showUserList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute right-0 top-12 bg-white dark:bg-zinc-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 w-48 overflow-hidden\",\n                        initial: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            height: 'auto'\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 border-b border-gray-200 dark:border-gray-700 font-medium text-sm text-gray-600 dark:text-gray-300\",\n                                children: \"Active Users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1582,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"max-h-48 overflow-y-auto\",\n                                children: activeUsers.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.li, {\n                                        className: \"px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-zinc-700 flex items-center space-x-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full bg-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1594,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: user\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1595,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1587,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1585,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1575,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1573,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"h-[calc(100%-40px)] \".concat(showOutput ? 'pb-[33%]' : ''),\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.4,\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    height: \"100%\",\n                                    defaultLanguage: language,\n                                    defaultValue: code,\n                                    onChange: handleEditorChange,\n                                    onMount: handleEditorDidMount,\n                                    theme: theme === \"dark\" ? \"vs-dark\" : \"light\",\n                                    options: {\n                                        minimap: {\n                                            enabled: minimapEnabled\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1613,\n                                    columnNumber: 15\n                                }, this),\n                                teacherCursorPosition && editorRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TeacherCursor, {\n                                    editor: editorRef.current,\n                                    position: teacherCursorPosition\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1625,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1612,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1610,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1604,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: typingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 left-4 text-sm text-gray-400 bg-gray-800/80 px-3 py-1 rounded-full backdrop-blur-sm\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            typingUser,\n                            \" is typing...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1637,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1635,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gray-900 text-white border-t border-gray-700 h-1/3 overflow-hidden flex flex-col\",\n                        initial: {\n                            height: 0\n                        },\n                        animate: {\n                            height: '33%'\n                        },\n                        exit: {\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 bg-gray-800 border-b border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1660,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearOutput,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1662,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: testPistonAPI,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-blue-400 hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Test API\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1669,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowOutput(false),\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1676,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1661,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1659,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-auto font-mono text-sm whitespace-pre-wrap\",\n                                children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1687,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Executing code...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1688,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1686,\n                                    columnNumber: 19\n                                }, this) : executionError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400\",\n                                    children: executionError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1691,\n                                    columnNumber: 19\n                                }, this) : executionOutput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: executionOutput\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1693,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: 'Click \"Run Code\" to execute your code.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1695,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1684,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1652,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1650,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: copySuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 right-4 text-sm text-white bg-green-500 px-3 py-1 rounded-full shadow-lg\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: \"Copied to clipboard!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1705,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1703,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showShortcuts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 max-w-md w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-700 dark:hover:text-white\",\n                                    onClick: ()=>setShowShortcuts(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1727,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    children: \"Keyboard Shortcuts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1733,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Run Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1735,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+Enter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1735,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Format Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1736,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Shift+Alt+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1736,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Find/Replace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1737,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1737,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Download Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1738,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+D\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1738,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Upload Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1739,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+U\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1739,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Minimap:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1740,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+M\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1740,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Theme:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1741,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+T\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1741,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Show Shortcuts:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1742,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+H\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1742,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1734,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1726,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1720,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1718,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 1341,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 1339,\n        columnNumber: 5\n    }, this);\n}\n_s(CodeEditor, \"mDpLeWTc7bpvH9zKdUtrWsk6ecA=\", false, function() {\n    return [\n        _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme\n    ];\n});\n_c = CodeEditor;\nconst TeacherCursor = (param)=>{\n    let { editor, position } = param;\n    _s1();\n    const [cursorStyle, setCursorStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherCursor.useEffect\": ()=>{\n            const updateCursorPosition = {\n                \"TeacherCursor.useEffect.updateCursorPosition\": ()=>{\n                    try {\n                        // Get the pixel position of the cursor\n                        const pixelPosition = editor.getScrolledVisiblePosition({\n                            lineNumber: position.lineNumber,\n                            column: position.column\n                        });\n                        if (pixelPosition) {\n                            // Get the editor container position\n                            const editorContainer = editor.getDomNode();\n                            if (editorContainer) {\n                                const containerRect = editorContainer.getBoundingClientRect();\n                                setCursorStyle({\n                                    position: 'absolute',\n                                    left: \"\".concat(pixelPosition.left, \"px\"),\n                                    top: \"\".concat(pixelPosition.top, \"px\"),\n                                    zIndex: 1000,\n                                    pointerEvents: 'none'\n                                });\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error updating teacher cursor position:', error);\n                    }\n                }\n            }[\"TeacherCursor.useEffect.updateCursorPosition\"];\n            // Update position immediately\n            updateCursorPosition();\n            // Update position when editor scrolls or layout changes\n            const scrollDisposable = editor.onDidScrollChange(updateCursorPosition);\n            const layoutDisposable = editor.onDidLayoutChange(updateCursorPosition);\n            return ({\n                \"TeacherCursor.useEffect\": ()=>{\n                    scrollDisposable.dispose();\n                    layoutDisposable.dispose();\n                }\n            })[\"TeacherCursor.useEffect\"];\n        }\n    }[\"TeacherCursor.useEffect\"], [\n        editor,\n        position\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: cursorStyle,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"teacher-cursor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"teacher-cursor-label\",\n                children: position.teacherName\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 1807,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 1806,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 1805,\n        columnNumber: 5\n    }, undefined);\n}; // Keyboard shortcuts global handler\n // useEffect(() => {\n //   const handler = (e: KeyboardEvent) => {\n //     if (e.altKey && e.key.toLowerCase() === \"d\") {\n //       e.preventDefault();\n //       handleDownload();\n //     } else if (e.altKey && e.key.toLowerCase() === \"u\") {\n //       e.preventDefault();\n //       fileInputRef.current?.click();\n //     } else if (e.altKey && e.key.toLowerCase() === \"m\") {\n //       e.preventDefault();\n //       setMinimapEnabled((v: boolean) => !v);\n //     } else if (e.altKey && e.key.toLowerCase() === \"t\") {\n //       e.preventDefault();\n //       setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n //     } else if (e.altKey && e.key.toLowerCase() === \"h\") {\n //       e.preventDefault();\n //       setShowShortcuts(true);\n //     }\n //   };\n //   window.addEventListener(\"keydown\", handler);\n //   return () => window.removeEventListener(\"keydown\", handler);\n // }, [theme, setTheme]);\n_s1(TeacherCursor, \"NrZZSKlPdtAtpq9NKiew7IOqw6k=\");\n_c1 = TeacherCursor;\nvar _c, _c1;\n$RefreshReg$(_c, \"CodeEditor\");\n$RefreshReg$(_c1, \"TeacherCursor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CodeEditor.tsx\n"));

/***/ })

});