"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1414f007864c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUHJvamVjdFxccmVhbGNvZGVcXGNsaWVudFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTQxNGYwMDc4NjRjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/socketService.ts":
/*!***************************************!*\
  !*** ./src/services/socketService.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n// client/src/services/socketService.ts\n\n\n// Fix: Ensure SOCKET_URL is defined before use (move it above all uses)\nconst SOCKET_URL = \"http://localhost:5001\" || 0;\n// Create a singleton socket instance\nclass SocketService {\n    static getInstance() {\n        if (!SocketService.instance) {\n            SocketService.instance = new SocketService();\n        }\n        return SocketService.instance;\n    }\n    initSocket() {\n        try {\n            console.log('Initializing socket connection to', SOCKET_URL);\n            // Use polling first for better compatibility, then upgrade to websocket\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                reconnectionAttempts: 10,\n                reconnectionDelay: 1000,\n                timeout: 10000,\n                transports: [\n                    'polling',\n                    'websocket'\n                ],\n                autoConnect: true,\n                forceNew: true,\n                upgrade: true\n            });\n            console.log('Socket instance created successfully');\n            this.setupEventListeners();\n        } catch (error) {\n            console.error('Error initializing socket:', error);\n        }\n    }\n    setupEventListeners() {\n        if (!this.socket) return;\n        // Clear any existing listeners to prevent duplicates\n        this.socket.removeAllListeners();\n        // Connection events\n        this.socket.on('connect', ()=>{\n            var _this_socket;\n            console.log('Socket connected successfully:', (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.id);\n            this.connected = true;\n            this.emitEvent('connect', null);\n        });\n        this.socket.on('disconnect', (reason)=>{\n            console.log(\"Socket disconnected. Reason: \".concat(reason));\n            this.connected = false;\n            this.emitEvent('disconnect', reason);\n            if (reason !== 'io client disconnect') {\n                console.log('Reconnection attempt will start in 2 seconds...');\n                setTimeout(()=>{\n                    console.log('Attempting to reconnect...');\n                    this.connect();\n                }, 2000);\n            }\n        });\n        this.socket.on('connect_error', (error)=>{\n            var _error_message, _error_message1;\n            console.error('Socket connection error:', (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error');\n            this.connected = false;\n            // Handle specific error types\n            if (error === null || error === void 0 ? void 0 : (_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('xhr poll error')) {\n                console.log('XHR polling error detected - will try alternative connection method');\n                // Set a flag to track this specific error\n                if (true) {\n                    window.localStorage.setItem('socket_xhr_error', 'true');\n                }\n                // Emit the error event\n                this.emitEvent('error', 'XHR polling error - trying alternative connection');\n                // Try the alternative transport after a short delay\n                setTimeout(()=>{\n                    this.tryAlternativeTransport();\n                }, 1000);\n            } else if (error === null || error === void 0 ? void 0 : (_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('websocket error')) {\n                console.log('WebSocket error detected - will try polling transport');\n                // Set a flag to track this specific error\n                if (true) {\n                    window.localStorage.setItem('socket_websocket_error', 'true');\n                }\n                // Try polling immediately\n                setTimeout(()=>{\n                    try {\n                        var _this_socket;\n                        console.log('Switching to polling transport...');\n                        (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.disconnect();\n                        this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                            reconnectionAttempts: 5,\n                            reconnectionDelay: 1000,\n                            timeout: 10000,\n                            transports: [\n                                'polling'\n                            ],\n                            autoConnect: true,\n                            forceNew: true,\n                            upgrade: false\n                        });\n                        this.setupEventListeners();\n                    } catch (e) {\n                        console.error('Error switching to polling transport:', e);\n                    }\n                }, 1000);\n                this.emitEvent('error', 'WebSocket error - trying polling transport');\n            } else {\n                // For other errors, just emit the error event\n                this.emitEvent('error', (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error');\n                // Try to reconnect after a delay\n                setTimeout(()=>{\n                    this.connect();\n                }, 3000);\n            }\n        });\n        // Transport and reconnection events\n        if (this.socket.io) {\n            this.socket.io.on('error', (error)=>{\n                console.error('Transport error:', error);\n            });\n            this.socket.io.on('reconnect_attempt', (attempt)=>{\n                console.log(\"Reconnection attempt \".concat(attempt));\n            });\n            this.socket.io.on('reconnect', (attemptNumber)=>{\n                this.connected = true;\n                this.emitEvent('connect', null);\n            });\n            this.socket.io.on('reconnect_error', (error)=>{\n                console.error('Reconnection error:', error);\n            });\n            this.socket.io.on('reconnect_failed', ()=>{\n                this.emitEvent('error', 'Failed to reconnect after multiple attempts');\n                // Try a different approach after all reconnection attempts fail\n                setTimeout(()=>{\n                    this.initSocket();\n                }, 3000);\n            });\n        }\n        // Application-specific events\n        this.socket.on('code-update', (code)=>{\n            this.emitEvent('code-update', code);\n        });\n        this.socket.on('user-typing', (data)=>{\n            this.emitEvent('user-typing', data);\n        });\n        this.socket.on('user-joined', (users)=>{\n            this.emitEvent('user-joined', users);\n        });\n        this.socket.on('user-left', (users)=>{\n            this.emitEvent('user-left', users);\n        });\n        this.socket.on('highlight-line', (data)=>{\n            this.emitEvent('highlight-line', data);\n        });\n        this.socket.on('cursor-move', (param)=>{\n            let { userId, position } = param;\n            var _this_listeners_get;\n            console.log(\"Cursor move received from user \".concat(userId, \":\"), position);\n            (_this_listeners_get = this.listeners.get('cursor-move')) === null || _this_listeners_get === void 0 ? void 0 : _this_listeners_get.forEach((callback)=>callback({\n                    userId,\n                    position\n                }));\n        });\n        this.socket.on('room-users-updated', (data)=>{\n            this.emitEvent('room-users-updated', data);\n        });\n    }\n    // Add event listener\n    on(event, callback) {\n        var _this_listeners_get;\n        if (!this.listeners.has(event)) {\n            this.listeners.set(event, []);\n        }\n        (_this_listeners_get = this.listeners.get(event)) === null || _this_listeners_get === void 0 ? void 0 : _this_listeners_get.push(callback);\n    }\n    // Remove event listener\n    off(event, callback) {\n        if (!this.listeners.has(event)) return;\n        const callbacks = this.listeners.get(event) || [];\n        const index = callbacks.indexOf(callback);\n        if (index !== -1) {\n            callbacks.splice(index, 1);\n        }\n    }\n    // Emit event to listeners\n    emitEvent(event, data) {\n        if (!this.listeners.has(event)) return;\n        const callbacks = this.listeners.get(event) || [];\n        callbacks.forEach((callback)=>{\n            try {\n                callback(data);\n            } catch (error) {\n                console.error(\"Error in \".concat(event, \" listener:\"), error);\n            }\n        });\n    }\n    // Check if socket is connected\n    isConnected() {\n        var _this_socket;\n        return this.connected && !!((_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.connected);\n    }\n    // Connect to socket server with fallback mechanisms\n    connect() {\n        if (!this.socket) {\n            this.initSocket();\n        } else if (!this.socket.connected) {\n            console.log('Socket exists but not connected, attempting to reconnect...');\n            // Try to reconnect with existing socket\n            try {\n                this.socket.connect();\n            } catch (error) {\n                console.error('Error reconnecting with existing socket:', error);\n                // If reconnection fails, create a new socket\n                this.initSocket();\n            }\n        }\n        // Set a timeout to check if connection was successful\n        setTimeout(()=>{\n            if (!this.isConnected()) {\n                this.tryAlternativeTransport();\n            }\n        }, 5000);\n    }\n    // Try alternative transport method if WebSocket fails\n    tryAlternativeTransport() {\n        try {\n            // Disconnect existing socket if any\n            if (this.socket) {\n                this.socket.disconnect();\n            }\n            // Create new socket with both transports but prioritize polling\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                reconnectionAttempts: 5,\n                reconnectionDelay: 1000,\n                timeout: 10000,\n                transports: [\n                    'polling',\n                    'websocket'\n                ],\n                autoConnect: true,\n                forceNew: true,\n                upgrade: true\n            });\n            this.setupEventListeners();\n        } catch (error) {\n            console.error('Error setting up alternative transport:', error);\n        }\n    }\n    // Disconnect from socket server\n    disconnect() {\n        if (this.socket) {\n            this.socket.disconnect();\n        }\n    }\n    // Create a new room\n    createRoom(username, roomId) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket || !this.isConnected()) {\n                return reject(new Error('Socket not connected'));\n            }\n            // Generate a unique userId if not already stored\n            let userId =  true ? window.localStorage.getItem('userId') : 0;\n            if (!userId) {\n                userId = \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9));\n                if (true) {\n                    window.localStorage.setItem('userId', userId);\n                }\n            }\n            this.socket.emit('create-room', {\n                username,\n                roomId,\n                userId\n            }, (response)=>{\n                if (response.error) {\n                    reject(new Error(response.error));\n                } else {\n                    // If the server validated and possibly changed the username, update it locally\n                    if (response.username && response.username !== username) {\n                        if (true) {\n                            window.localStorage.setItem('username', response.username);\n                        }\n                    }\n                    resolve({\n                        roomId: response.roomId,\n                        username: response.username || username\n                    });\n                }\n            });\n        });\n    }\n    // Join an existing room with fallback to HTTP if socket fails\n    joinRoom(roomId, username) {\n        return new Promise(async (resolve, reject)=>{\n            // Generate a unique userId if not already stored\n            let userId =  true ? window.localStorage.getItem('userId') : 0;\n            if (!userId) {\n                userId = \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9));\n                if (true) {\n                    window.localStorage.setItem('userId', userId);\n                }\n            }\n            // Check if we have a socket connection\n            if (!this.socket || !this.isConnected()) {\n                // Try to connect\n                this.connect();\n                // Wait a bit to see if connection succeeds\n                await new Promise((r)=>setTimeout(r, 2000));\n                // If still not connected, use HTTP fallback\n                if (!this.isConnected()) {\n                    return this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                }\n            }\n            // If we reach here, we have a socket connection, so use it\n            try {\n                if (!this.socket) {\n                    throw new Error('Socket is null');\n                }\n                // Emit join-room with userId, username, and roomId\n                this.socket.emit('join-room', {\n                    roomId,\n                    username,\n                    userId\n                }, (response)=>{\n                    if (response.error) {\n                        // If socket join fails, try HTTP fallback\n                        this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                    } else if (response.success) {\n                        // If the server validated and possibly changed the username, update it locally\n                        if (response.username && response.username !== username) {\n                            if (true) {\n                                window.localStorage.setItem('username', response.username);\n                            }\n                        }\n                        // Mark the current user in the users list\n                        const usersWithCurrentFlag = (response.users || []).map((user)=>({\n                                ...user,\n                                userId: user.userId || '',\n                                isCurrentUser: (user.userId || '') === userId\n                            }));\n                        resolve({\n                            users: usersWithCurrentFlag || [],\n                            username: response.username || username\n                        });\n                    } else {\n                        // If socket join fails, try HTTP fallback\n                        this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                    }\n                });\n                // Set a timeout in case the callback never fires\n                setTimeout(()=>{\n                    // If we haven't resolved or rejected yet, try HTTP fallback\n                    this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                }, 5000);\n            } catch (error) {\n                // If socket join throws an exception, try HTTP fallback\n                this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n            }\n        });\n    }\n    // HTTP fallback for joining a room when socket fails\n    joinRoomViaHttp(roomId, username, userId, resolve, reject) {\n        // Use axios to make a direct HTTP request to the server\n        const apiUrl = \"\".concat(SOCKET_URL, \"/api/join-room\");\n        axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(apiUrl, {\n            roomId,\n            username,\n            userId\n        }).then((response)=>{\n            if (response.data.error) {\n                reject(new Error(response.data.error));\n            } else {\n                // If the server validated and possibly changed the username, update it locally\n                if (response.data.username && response.data.username !== username) {\n                    if (true) {\n                        window.localStorage.setItem('username', response.data.username);\n                    }\n                }\n                // Create a default user list with at least the current user\n                const users = response.data.users || [\n                    {\n                        userId,\n                        username: response.data.username || username,\n                        socketId: 'http-fallback'\n                    }\n                ];\n                // Fix: When mapping users, always provide a fallback for userId (empty string if undefined)\n                const usersWithCurrentFlag = users.map((user)=>({\n                        ...user,\n                        userId: user.userId || '',\n                        isCurrentUser: (user.userId || '') === userId\n                    }));\n                resolve({\n                    users: usersWithCurrentFlag,\n                    username: response.data.username || username\n                });\n                // Try to reconnect socket after successful HTTP fallback\n                setTimeout(()=>this.connect(), 1000);\n            }\n        }).catch((error)=>{\n            // If HTTP fallback also fails, create a minimal response with just the current user\n            const fallbackUser = {\n                userId,\n                username,\n                isCurrentUser: true\n            };\n            // Resolve with just the current user to allow the UI to function\n            resolve({\n                users: [\n                    fallbackUser\n                ],\n                username\n            });\n            // Try to reconnect socket after error\n            setTimeout(()=>this.connect(), 2000);\n        });\n    }\n    // Send code changes to the server with HTTP fallback\n    sendCodeChange(roomId, code) {\n        // If socket is connected, use it\n        if (this.socket && this.isConnected()) {\n            try {\n                // Use volatile for code changes to prevent queueing\n                // This helps prevent outdated updates from being sent\n                this.socket.volatile.emit('code-change', {\n                    roomId,\n                    code\n                });\n                return true;\n            } catch (error) {\n            // Fall through to HTTP fallback\n            }\n        } else {}\n        // HTTP fallback for code changes\n        this.sendCodeChangeViaHttp(roomId, code);\n        return true;\n    }\n    // HTTP fallback for sending code changes\n    sendCodeChangeViaHttp(roomId, code) {\n        // Only send HTTP fallback for significant changes to reduce traffic\n        // Store the last sent code to avoid sending duplicates\n        const lastSentCode =  true ? window.localStorage.getItem(\"last_http_code_\".concat(roomId)) : 0;\n        if (lastSentCode === code) {\n            return; // Don't send duplicate code\n        }\n        // Use axios to make a direct HTTP request to the server\n        const apiUrl = \"\".concat(SOCKET_URL, \"/api/code-change\");\n        const userId =  true ? window.localStorage.getItem('userId') || '' : 0;\n        axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(apiUrl, {\n            roomId,\n            code,\n            userId\n        }).then((response)=>{\n            // Store the sent code to avoid duplicates\n            if (true) {\n                window.localStorage.setItem(\"last_http_code_\".concat(roomId), code);\n            }\n        }).catch((error)=>{\n            console.error('Error sending code change via HTTP:', error);\n        });\n        // Try to reconnect socket\n        if (!this.isConnected()) {\n            setTimeout(()=>this.connect(), 1000);\n        }\n    }\n    // Send typing notification\n    sendTyping(roomId, username) {\n        if (!this.socket || !this.isConnected()) {\n            return;\n        }\n        // Use the exact username provided by the user without any modifications\n        // This ensures we use exactly what the user entered on the dashboard\n        const validUsername = username;\n        // Get userId from localStorage\n        const userId =  true ? window.localStorage.getItem('userId') || \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9)) : 0;\n        try {\n            this.socket.emit('typing', {\n                roomId,\n                username: validUsername,\n                userId\n            });\n        } catch (error) {}\n    }\n    // Emit a highlight-line event to the server\n    sendHighlightLine(roomId, startLine, endLine, comment) {\n        if (!this.socket || !this.isConnected()) return;\n        try {\n            this.socket.emit('highlight-line', {\n                roomId,\n                startLine,\n                endLine,\n                comment\n            });\n        } catch (error) {\n        // Optionally handle error\n        }\n    }\n    // Leave a room\n    leaveRoom(roomId) {\n        if (!this.socket || !this.isConnected()) return;\n        try {\n            this.socket.emit('leave-room', roomId);\n        } catch (error) {}\n    }\n    // Add a public method to listen for the next connect event\n    onConnect(callback) {\n        if (this.isConnected()) {\n            callback();\n        } else if (this.socket) {\n            this.socket.once('connect', callback);\n        } else {\n            // If socket is not initialized, initialize and then listen\n            this.initSocket();\n            // Wait for the socket to be created, then attach the listener\n            setTimeout(()=>{\n                var _this_socket;\n                (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.once('connect', callback);\n            }, 100);\n        }\n    }\n    getSocket() {\n        return this.socket;\n    }\n    // Emit cursor movement\n    sendCursorMove(roomId, userId, position) {\n        if (this.socket && this.connected) {\n            this.socket.emit(\"cursor-move\", {\n                roomId,\n                userId,\n                position\n            });\n        }\n    }\n    // Add a method to validate if a room exists on the server\n    async validateRoom(roomId) {\n        if (!this.socket || !this.isConnected()) {\n            throw new Error(\"Socket not connected\");\n        }\n        return new Promise((resolve, reject)=>{\n            if (!this.socket) {\n                throw new Error(\"Socket is not initialized\");\n            }\n            this.socket.emit(\"validate-room\", {\n                roomId\n            }, (response)=>{\n                if (response) {\n                    resolve(response);\n                } else {\n                    reject(new Error(\"Failed to validate room\"));\n                }\n            });\n        });\n    }\n    constructor(){\n        this.socket = null;\n        this.listeners = new Map();\n        this.connected = false;\n        this.initSocket();\n    }\n}\n// Export the class itself instead of calling getInstance during export\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SocketService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/socketService.ts\n"));

/***/ })

});