'use client';

import { useState, useEffect } from 'react';
import { socketService } from '@/services/socketService';

export function useSocketService() {
  const [isReady, setIsReady] = useState(false);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const checkSocketService = () => {
      try {
        // Check if socketService exists and has required methods
        if (socketService && 
            typeof socketService.on === 'function' && 
            typeof socketService.off === 'function' && 
            typeof socketService.emit === 'function') {
          setIsReady(true);
          
          // Check connection status
          if (typeof socketService.isConnected === 'function') {
            setIsConnected(socketService.isConnected());
          }
          
          console.log('Socket service is ready and available');
          return true;
        } else {
          setIsReady(false);
          setIsConnected(false);
          console.log('Socket service not ready yet...');
          return false;
        }
      } catch (error) {
        console.error('Error checking socket service:', error);
        setIsReady(false);
        setIsConnected(false);
        return false;
      }
    };

    // Check immediately
    const isInitiallyReady = checkSocketService();

    // If not ready, set up polling
    let interval: NodeJS.Timeout | null = null;
    if (!isInitiallyReady) {
      interval = setInterval(() => {
        const ready = checkSocketService();
        if (ready && interval) {
          clearInterval(interval);
          interval = null;
        }
      }, 1000);
    }

    // Set up connection status listeners if socket is ready
    if (isInitiallyReady && socketService) {
      const handleConnect = () => {
        console.log('Socket connected');
        setIsConnected(true);
      };

      const handleDisconnect = () => {
        console.log('Socket disconnected');
        setIsConnected(false);
      };

      try {
        socketService.on('connect', handleConnect);
        socketService.on('disconnect', handleDisconnect);
      } catch (error) {
        console.error('Error setting up connection listeners:', error);
      }

      // Cleanup function
      return () => {
        if (interval) {
          clearInterval(interval);
        }
        try {
          if (socketService) {
            socketService.off('connect', handleConnect);
            socketService.off('disconnect', handleDisconnect);
          }
        } catch (error) {
          console.error('Error cleaning up connection listeners:', error);
        }
      };
    }

    // Cleanup function for polling case
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, []);

  return {
    socketService: isReady ? socketService : null,
    isReady,
    isConnected
  };
}
