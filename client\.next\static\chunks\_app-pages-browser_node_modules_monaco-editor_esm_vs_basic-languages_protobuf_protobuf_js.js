"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_protobuf_protobuf_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/protobuf/protobuf.ts\nvar namedLiterals = [\"true\", \"false\"];\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"<\", \">\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\"] }\n  ],\n  autoCloseBefore: \".,=}])>' \\n\t\",\n  indentationRules: {\n    increaseIndentPattern: new RegExp(\"^((?!\\\\/\\\\/).)*(\\\\{[^}\\\"'`]*|\\\\([^)\\\"'`]*|\\\\[[^\\\\]\\\"'`]*)$\"),\n    decreaseIndentPattern: new RegExp(\"^((?!.*?\\\\/\\\\*).*\\\\*/)?\\\\s*[\\\\}\\\\]].*$\")\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".proto\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  symbols: /[=><!~?:&|+\\-*/^%]+/,\n  keywords: [\n    \"syntax\",\n    \"import\",\n    \"weak\",\n    \"public\",\n    \"package\",\n    \"option\",\n    \"repeated\",\n    \"oneof\",\n    \"map\",\n    \"reserved\",\n    \"to\",\n    \"max\",\n    \"enum\",\n    \"message\",\n    \"service\",\n    \"rpc\",\n    \"stream\",\n    \"returns\",\n    \"package\",\n    \"optional\",\n    \"true\",\n    \"false\"\n  ],\n  builtinTypes: [\n    \"double\",\n    \"float\",\n    \"int32\",\n    \"int64\",\n    \"uint32\",\n    \"uint64\",\n    \"sint32\",\n    \"sint64\",\n    \"fixed32\",\n    \"fixed64\",\n    \"sfixed32\",\n    \"sfixed64\",\n    \"bool\",\n    \"string\",\n    \"bytes\"\n  ],\n  operators: [\"=\", \"+\", \"-\"],\n  namedLiterals,\n  escapes: `\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|'|\\\\\\${)`,\n  identifier: /[a-zA-Z]\\w*/,\n  fullIdentifier: /@identifier(?:\\s*\\.\\s*@identifier)*/,\n  optionName: /(?:@identifier|\\(\\s*@fullIdentifier\\s*\\))(?:\\s*\\.\\s*@identifier)*/,\n  messageName: /@identifier/,\n  enumName: /@identifier/,\n  messageType: /\\.?\\s*(?:@identifier\\s*\\.\\s*)*@messageName/,\n  enumType: /\\.?\\s*(?:@identifier\\s*\\.\\s*)*@enumName/,\n  floatLit: /[0-9]+\\s*\\.\\s*[0-9]*(?:@exponent)?|[0-9]+@exponent|\\.[0-9]+(?:@exponent)?/,\n  exponent: /[eE]\\s*[+-]?\\s*[0-9]+/,\n  boolLit: /true\\b|false\\b/,\n  decimalLit: /[1-9][0-9]*/,\n  octalLit: /0[0-7]*/,\n  hexLit: /0[xX][0-9a-fA-F]+/,\n  type: /double|float|int32|int64|uint32|uint64|sint32|sint64|fixed32|fixed64|sfixed32|sfixed64|bool|string|bytes|@messageType|@enumType/,\n  keyType: /int32|int64|uint32|uint64|sint32|sint64|fixed32|fixed64|sfixed32|sfixed64|bool|string/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      [/syntax/, \"keyword\"],\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [\n        /(\")(proto3)(\")/,\n        [\"string.quote\", \"string\", { token: \"string.quote\", switchTo: \"@topLevel.proto3\" }]\n      ],\n      [\n        /(\")(proto2)(\")/,\n        [\"string.quote\", \"string\", { token: \"string.quote\", switchTo: \"@topLevel.proto2\" }]\n      ],\n      [\n        // If no `syntax` provided, regarded as proto2\n        /.*?/,\n        { token: \"\", switchTo: \"@topLevel.proto2\" }\n      ]\n    ],\n    topLevel: [\n      // whitespace\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/[;.]/, \"delimiter\"],\n      [\n        /@fullIdentifier/,\n        {\n          cases: {\n            option: { token: \"keyword\", next: \"@option.$S2\" },\n            enum: { token: \"keyword\", next: \"@enumDecl.$S2\" },\n            message: { token: \"keyword\", next: \"@messageDecl.$S2\" },\n            service: { token: \"keyword\", next: \"@serviceDecl.$S2\" },\n            extend: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@extendDecl.$S2\" }\n              }\n            },\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    enumDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@enumBody.$S2\" }]\n    ],\n    enumBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [/option\\b/, \"keyword\", \"@option.$S2\"],\n      [/@identifier/, \"identifier\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    messageDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@messageBody.$S2\" }]\n    ],\n    messageBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [\n        \"(map)(s*)(<)\",\n        [\"keyword\", \"white\", { token: \"@brackets\", bracket: \"@open\", next: \"@map.$S2\" }]\n      ],\n      [\n        /@identifier/,\n        {\n          cases: {\n            option: { token: \"keyword\", next: \"@option.$S2\" },\n            enum: { token: \"keyword\", next: \"@enumDecl.$S2\" },\n            message: { token: \"keyword\", next: \"@messageDecl.$S2\" },\n            oneof: { token: \"keyword\", next: \"@oneofDecl.$S2\" },\n            extensions: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@reserved.$S2\" }\n              }\n            },\n            reserved: { token: \"keyword\", next: \"@reserved.$S2\" },\n            \"(?:repeated|optional)\": { token: \"keyword\", next: \"@field.$S2\" },\n            required: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@field.$S2\" }\n              }\n            },\n            \"$S2==proto3\": { token: \"@rematch\", next: \"@field.$S2\" }\n          }\n        }\n      ],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    extendDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@extendBody.$S2\" }]\n    ],\n    extendBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/(?:repeated|optional|required)/, \"keyword\", \"@field.$S2\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    options: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\"],\n      [/\\]/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    option: [\n      { include: \"@whitespace\" },\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\", \"@pop\"]\n    ],\n    oneofDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@oneofBody.$S2\" }]\n    ],\n    oneofBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/(@identifier)(\\s*)(=)/, [\"identifier\", \"white\", \"delimiter\"]],\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    reserved: [\n      { include: \"@whitespace\" },\n      [/,/, \"delimiter\"],\n      [/;/, \"delimiter\", \"@pop\"],\n      { include: \"@constant\" },\n      [/to\\b|max\\b/, \"keyword\"]\n    ],\n    map: [\n      { include: \"@whitespace\" },\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/,/, \"delimiter\"],\n      [/>/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"identifier\" }]\n    ],\n    field: [\n      { include: \"@whitespace\" },\n      [\n        \"group\",\n        {\n          cases: {\n            \"$S2==proto2\": { token: \"keyword\", switchTo: \"@groupDecl.$S2\" }\n          }\n        }\n      ],\n      [/(@identifier)(\\s*)(=)/, [\"identifier\", \"white\", { token: \"delimiter\", next: \"@pop\" }]],\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ]\n    ],\n    groupDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [\"=\", \"operator\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@messageBody.$S2\" }],\n      { include: \"@constant\" }\n    ],\n    type: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\", \"@pop\"],\n      [/./, \"delimiter\"]\n    ],\n    identifier: [{ include: \"@whitespace\" }, [/@identifier/, \"identifier\", \"@pop\"]],\n    serviceDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@serviceBody.$S2\" }]\n    ],\n    serviceBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/option\\b/, \"keyword\", \"@option.$S2\"],\n      [/rpc\\b/, \"keyword\", \"@rpc.$S2\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    rpc: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/\\(/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@request.$S2\" }],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", next: \"@methodOptions.$S2\" }],\n      [/;/, \"delimiter\", \"@pop\"]\n    ],\n    request: [\n      { include: \"@whitespace\" },\n      [\n        /@messageType/,\n        {\n          cases: {\n            stream: { token: \"keyword\", next: \"@type.$S2\" },\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\)/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"@returns.$S2\" }]\n    ],\n    returns: [\n      { include: \"@whitespace\" },\n      [/returns\\b/, \"keyword\"],\n      [/\\(/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@response.$S2\" }]\n    ],\n    response: [\n      { include: \"@whitespace\" },\n      [\n        /@messageType/,\n        {\n          cases: {\n            stream: { token: \"keyword\", next: \"@type.$S2\" },\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\)/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"@rpc.$S2\" }]\n    ],\n    methodOptions: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [\"option\", \"keyword\"],\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\"],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [\"\\\\*/\", \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    stringSingle: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    constant: [\n      [\"@boolLit\", \"keyword.constant\"],\n      [\"@hexLit\", \"number.hex\"],\n      [\"@octalLit\", \"number.octal\"],\n      [\"@decimalLit\", \"number\"],\n      [\"@floatLit\", \"number.float\"],\n      [/(\"([^\"\\\\]|\\\\.)*|'([^'\\\\]|\\\\.)*)$/, \"string.invalid\"],\n      // non-terminated string\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }],\n      [/'/, { token: \"string.quote\", bracket: \"@open\", next: \"@stringSingle\" }],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", next: \"@prototext\" }],\n      [/identifier/, \"identifier\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    prototext: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/@identifier/, \"identifier\"],\n      [/[:;]/, \"delimiter\"],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.js\n"));

/***/ })

}]);