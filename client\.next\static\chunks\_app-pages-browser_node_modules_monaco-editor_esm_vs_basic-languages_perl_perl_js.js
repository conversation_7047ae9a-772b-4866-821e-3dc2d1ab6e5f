"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_perl_perl_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/perl/perl.js":
/*!*************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/perl/perl.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/perl/perl.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".perl\",\n  brackets: [\n    { token: \"delimiter.bracket\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  // https://learn.perl.org/docs/keywords.html\n  // Perl syntax\n  keywords: [\n    \"__DATA__\",\n    \"else\",\n    \"lock\",\n    \"__END__\",\n    \"elsif\",\n    \"lt\",\n    \"__FILE__\",\n    \"eq\",\n    \"__LINE__\",\n    \"exp\",\n    \"ne\",\n    \"sub\",\n    \"__PACKAGE__\",\n    \"for\",\n    \"no\",\n    \"and\",\n    \"foreach\",\n    \"or\",\n    \"unless\",\n    \"cmp\",\n    \"ge\",\n    \"package\",\n    \"until\",\n    \"continue\",\n    \"gt\",\n    \"while\",\n    \"CORE\",\n    \"if\",\n    \"xor\",\n    \"do\",\n    \"le\",\n    \"__DIE__\",\n    \"__WARN__\"\n  ],\n  // Perl functions\n  builtinFunctions: [\n    \"-A\",\n    \"END\",\n    \"length\",\n    \"setpgrp\",\n    \"-B\",\n    \"endgrent\",\n    \"link\",\n    \"setpriority\",\n    \"-b\",\n    \"endhostent\",\n    \"listen\",\n    \"setprotoent\",\n    \"-C\",\n    \"endnetent\",\n    \"local\",\n    \"setpwent\",\n    \"-c\",\n    \"endprotoent\",\n    \"localtime\",\n    \"setservent\",\n    \"-d\",\n    \"endpwent\",\n    \"log\",\n    \"setsockopt\",\n    \"-e\",\n    \"endservent\",\n    \"lstat\",\n    \"shift\",\n    \"-f\",\n    \"eof\",\n    \"map\",\n    \"shmctl\",\n    \"-g\",\n    \"eval\",\n    \"mkdir\",\n    \"shmget\",\n    \"-k\",\n    \"exec\",\n    \"msgctl\",\n    \"shmread\",\n    \"-l\",\n    \"exists\",\n    \"msgget\",\n    \"shmwrite\",\n    \"-M\",\n    \"exit\",\n    \"msgrcv\",\n    \"shutdown\",\n    \"-O\",\n    \"fcntl\",\n    \"msgsnd\",\n    \"sin\",\n    \"-o\",\n    \"fileno\",\n    \"my\",\n    \"sleep\",\n    \"-p\",\n    \"flock\",\n    \"next\",\n    \"socket\",\n    \"-r\",\n    \"fork\",\n    \"not\",\n    \"socketpair\",\n    \"-R\",\n    \"format\",\n    \"oct\",\n    \"sort\",\n    \"-S\",\n    \"formline\",\n    \"open\",\n    \"splice\",\n    \"-s\",\n    \"getc\",\n    \"opendir\",\n    \"split\",\n    \"-T\",\n    \"getgrent\",\n    \"ord\",\n    \"sprintf\",\n    \"-t\",\n    \"getgrgid\",\n    \"our\",\n    \"sqrt\",\n    \"-u\",\n    \"getgrnam\",\n    \"pack\",\n    \"srand\",\n    \"-w\",\n    \"gethostbyaddr\",\n    \"pipe\",\n    \"stat\",\n    \"-W\",\n    \"gethostbyname\",\n    \"pop\",\n    \"state\",\n    \"-X\",\n    \"gethostent\",\n    \"pos\",\n    \"study\",\n    \"-x\",\n    \"getlogin\",\n    \"print\",\n    \"substr\",\n    \"-z\",\n    \"getnetbyaddr\",\n    \"printf\",\n    \"symlink\",\n    \"abs\",\n    \"getnetbyname\",\n    \"prototype\",\n    \"syscall\",\n    \"accept\",\n    \"getnetent\",\n    \"push\",\n    \"sysopen\",\n    \"alarm\",\n    \"getpeername\",\n    \"quotemeta\",\n    \"sysread\",\n    \"atan2\",\n    \"getpgrp\",\n    \"rand\",\n    \"sysseek\",\n    \"AUTOLOAD\",\n    \"getppid\",\n    \"read\",\n    \"system\",\n    \"BEGIN\",\n    \"getpriority\",\n    \"readdir\",\n    \"syswrite\",\n    \"bind\",\n    \"getprotobyname\",\n    \"readline\",\n    \"tell\",\n    \"binmode\",\n    \"getprotobynumber\",\n    \"readlink\",\n    \"telldir\",\n    \"bless\",\n    \"getprotoent\",\n    \"readpipe\",\n    \"tie\",\n    \"break\",\n    \"getpwent\",\n    \"recv\",\n    \"tied\",\n    \"caller\",\n    \"getpwnam\",\n    \"redo\",\n    \"time\",\n    \"chdir\",\n    \"getpwuid\",\n    \"ref\",\n    \"times\",\n    \"CHECK\",\n    \"getservbyname\",\n    \"rename\",\n    \"truncate\",\n    \"chmod\",\n    \"getservbyport\",\n    \"require\",\n    \"uc\",\n    \"chomp\",\n    \"getservent\",\n    \"reset\",\n    \"ucfirst\",\n    \"chop\",\n    \"getsockname\",\n    \"return\",\n    \"umask\",\n    \"chown\",\n    \"getsockopt\",\n    \"reverse\",\n    \"undef\",\n    \"chr\",\n    \"glob\",\n    \"rewinddir\",\n    \"UNITCHECK\",\n    \"chroot\",\n    \"gmtime\",\n    \"rindex\",\n    \"unlink\",\n    \"close\",\n    \"goto\",\n    \"rmdir\",\n    \"unpack\",\n    \"closedir\",\n    \"grep\",\n    \"say\",\n    \"unshift\",\n    \"connect\",\n    \"hex\",\n    \"scalar\",\n    \"untie\",\n    \"cos\",\n    \"index\",\n    \"seek\",\n    \"use\",\n    \"crypt\",\n    \"INIT\",\n    \"seekdir\",\n    \"utime\",\n    \"dbmclose\",\n    \"int\",\n    \"select\",\n    \"values\",\n    \"dbmopen\",\n    \"ioctl\",\n    \"semctl\",\n    \"vec\",\n    \"defined\",\n    \"join\",\n    \"semget\",\n    \"wait\",\n    \"delete\",\n    \"keys\",\n    \"semop\",\n    \"waitpid\",\n    \"DESTROY\",\n    \"kill\",\n    \"send\",\n    \"wantarray\",\n    \"die\",\n    \"last\",\n    \"setgrent\",\n    \"warn\",\n    \"dump\",\n    \"lc\",\n    \"sethostent\",\n    \"write\",\n    \"each\",\n    \"lcfirst\",\n    \"setnetent\"\n  ],\n  // File handlers\n  builtinFileHandlers: [\"ARGV\", \"STDERR\", \"STDOUT\", \"ARGVOUT\", \"STDIN\", \"ENV\"],\n  // Perl variables\n  builtinVariables: [\n    \"$!\",\n    \"$^RE_TRIE_MAXBUF\",\n    \"$LAST_REGEXP_CODE_RESULT\",\n    '$\"',\n    \"$^S\",\n    \"$LIST_SEPARATOR\",\n    \"$#\",\n    \"$^T\",\n    \"$MATCH\",\n    \"$$\",\n    \"$^TAINT\",\n    \"$MULTILINE_MATCHING\",\n    \"$%\",\n    \"$^UNICODE\",\n    \"$NR\",\n    \"$&\",\n    \"$^UTF8LOCALE\",\n    \"$OFMT\",\n    \"$'\",\n    \"$^V\",\n    \"$OFS\",\n    \"$(\",\n    \"$^W\",\n    \"$ORS\",\n    \"$)\",\n    \"$^WARNING_BITS\",\n    \"$OS_ERROR\",\n    \"$*\",\n    \"$^WIDE_SYSTEM_CALLS\",\n    \"$OSNAME\",\n    \"$+\",\n    \"$^X\",\n    \"$OUTPUT_AUTO_FLUSH\",\n    \"$,\",\n    \"$_\",\n    \"$OUTPUT_FIELD_SEPARATOR\",\n    \"$-\",\n    \"$`\",\n    \"$OUTPUT_RECORD_SEPARATOR\",\n    \"$.\",\n    \"$a\",\n    \"$PERL_VERSION\",\n    \"$/\",\n    \"$ACCUMULATOR\",\n    \"$PERLDB\",\n    \"$0\",\n    \"$ARG\",\n    \"$PID\",\n    \"$:\",\n    \"$ARGV\",\n    \"$POSTMATCH\",\n    \"$;\",\n    \"$b\",\n    \"$PREMATCH\",\n    \"$<\",\n    \"$BASETIME\",\n    \"$PROCESS_ID\",\n    \"$=\",\n    \"$CHILD_ERROR\",\n    \"$PROGRAM_NAME\",\n    \"$>\",\n    \"$COMPILING\",\n    \"$REAL_GROUP_ID\",\n    \"$?\",\n    \"$DEBUGGING\",\n    \"$REAL_USER_ID\",\n    \"$@\",\n    \"$EFFECTIVE_GROUP_ID\",\n    \"$RS\",\n    \"$[\",\n    \"$EFFECTIVE_USER_ID\",\n    \"$SUBSCRIPT_SEPARATOR\",\n    \"$\\\\\",\n    \"$EGID\",\n    \"$SUBSEP\",\n    \"$]\",\n    \"$ERRNO\",\n    \"$SYSTEM_FD_MAX\",\n    \"$^\",\n    \"$EUID\",\n    \"$UID\",\n    \"$^A\",\n    \"$EVAL_ERROR\",\n    \"$WARNING\",\n    \"$^C\",\n    \"$EXCEPTIONS_BEING_CAUGHT\",\n    \"$|\",\n    \"$^CHILD_ERROR_NATIVE\",\n    \"$EXECUTABLE_NAME\",\n    \"$~\",\n    \"$^D\",\n    \"$EXTENDED_OS_ERROR\",\n    \"%!\",\n    \"$^E\",\n    \"$FORMAT_FORMFEED\",\n    \"%^H\",\n    \"$^ENCODING\",\n    \"$FORMAT_LINE_BREAK_CHARACTERS\",\n    \"%ENV\",\n    \"$^F\",\n    \"$FORMAT_LINES_LEFT\",\n    \"%INC\",\n    \"$^H\",\n    \"$FORMAT_LINES_PER_PAGE\",\n    \"%OVERLOAD\",\n    \"$^I\",\n    \"$FORMAT_NAME\",\n    \"%SIG\",\n    \"$^L\",\n    \"$FORMAT_PAGE_NUMBER\",\n    \"@+\",\n    \"$^M\",\n    \"$FORMAT_TOP_NAME\",\n    \"@-\",\n    \"$^N\",\n    \"$GID\",\n    \"@_\",\n    \"$^O\",\n    \"$INPLACE_EDIT\",\n    \"@ARGV\",\n    \"$^OPEN\",\n    \"$INPUT_LINE_NUMBER\",\n    \"@INC\",\n    \"$^P\",\n    \"$INPUT_RECORD_SEPARATOR\",\n    \"@LAST_MATCH_START\",\n    \"$^R\",\n    \"$LAST_MATCH_END\",\n    \"$^RE_DEBUG_FLAGS\",\n    \"$LAST_PAREN_MATCH\"\n  ],\n  // operators\n  symbols: /[:+\\-\\^*$&%@=<>!?|\\/~\\.]/,\n  quoteLikeOps: [\"qr\", \"m\", \"s\", \"q\", \"qq\", \"qx\", \"qw\", \"tr\", \"y\"],\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      [\n        /[a-zA-Z\\-_][\\w\\-_]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@builtinFunctions\": \"type.identifier\",\n            \"@builtinFileHandlers\": \"variable.predefined\",\n            \"@quoteLikeOps\": {\n              token: \"@rematch\",\n              next: \"quotedConstructs\"\n            },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // Perl variables\n      [\n        /[\\$@%][*@#?\\+\\-\\$!\\w\\\\\\^><~:;\\.]+/,\n        {\n          cases: {\n            \"@builtinVariables\": \"variable.predefined\",\n            \"@default\": \"variable\"\n          }\n        }\n      ],\n      { include: \"@strings\" },\n      { include: \"@dblStrings\" },\n      // Perl Doc\n      { include: \"@perldoc\" },\n      // Here Doc\n      { include: \"@heredoc\" },\n      [/[{}\\[\\]()]/, \"@brackets\"],\n      // RegExp\n      [/[\\/](?:(?:\\[(?:\\\\]|[^\\]])+\\])|(?:\\\\\\/|[^\\]\\/]))*[\\/]\\w*\\s*(?=[).,;]|$)/, \"regexp\"],\n      [/@symbols/, \"operators\"],\n      { include: \"@numbers\" },\n      [/[,;]/, \"delimiter\"]\n    ],\n    whitespace: [\n      [/\\s+/, \"white\"],\n      [/(^#!.*$)/, \"metatag\"],\n      [/(^#.*$)/, \"comment\"]\n    ],\n    numbers: [\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F_]*[0-9a-fA-F]/, \"number.hex\"],\n      [/\\d+/, \"number\"]\n    ],\n    // Single quote string\n    strings: [[/'/, \"string\", \"@stringBody\"]],\n    stringBody: [\n      [/'/, \"string\", \"@popall\"],\n      [/\\\\'/, \"string.escape\"],\n      [/./, \"string\"]\n    ],\n    // Double quote string\n    dblStrings: [[/\"/, \"string\", \"@dblStringBody\"]],\n    dblStringBody: [\n      [/\"/, \"string\", \"@popall\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      { include: \"@variables\" },\n      [/./, \"string\"]\n    ],\n    // Quoted constructs\n    // Percent strings in Ruby are similar to quote-like operators in Perl.\n    // This is adapted from pstrings in ../ruby/ruby.ts.\n    quotedConstructs: [\n      [/(q|qw|tr|y)\\s*\\(/, { token: \"string.delim\", switchTo: \"@qstring.(.)\" }],\n      [/(q|qw|tr|y)\\s*\\[/, { token: \"string.delim\", switchTo: \"@qstring.[.]\" }],\n      [/(q|qw|tr|y)\\s*\\{/, { token: \"string.delim\", switchTo: \"@qstring.{.}\" }],\n      [/(q|qw|tr|y)\\s*</, { token: \"string.delim\", switchTo: \"@qstring.<.>\" }],\n      [/(q|qw|tr|y)#/, { token: \"string.delim\", switchTo: \"@qstring.#.#\" }],\n      [/(q|qw|tr|y)\\s*([^A-Za-z0-9#\\s])/, { token: \"string.delim\", switchTo: \"@qstring.$2.$2\" }],\n      [/(q|qw|tr|y)\\s+(\\w)/, { token: \"string.delim\", switchTo: \"@qstring.$2.$2\" }],\n      [/(qr|m|s)\\s*\\(/, { token: \"regexp.delim\", switchTo: \"@qregexp.(.)\" }],\n      [/(qr|m|s)\\s*\\[/, { token: \"regexp.delim\", switchTo: \"@qregexp.[.]\" }],\n      [/(qr|m|s)\\s*\\{/, { token: \"regexp.delim\", switchTo: \"@qregexp.{.}\" }],\n      [/(qr|m|s)\\s*</, { token: \"regexp.delim\", switchTo: \"@qregexp.<.>\" }],\n      [/(qr|m|s)#/, { token: \"regexp.delim\", switchTo: \"@qregexp.#.#\" }],\n      [/(qr|m|s)\\s*([^A-Za-z0-9_#\\s])/, { token: \"regexp.delim\", switchTo: \"@qregexp.$2.$2\" }],\n      [/(qr|m|s)\\s+(\\w)/, { token: \"regexp.delim\", switchTo: \"@qregexp.$2.$2\" }],\n      [/(qq|qx)\\s*\\(/, { token: \"string.delim\", switchTo: \"@qqstring.(.)\" }],\n      [/(qq|qx)\\s*\\[/, { token: \"string.delim\", switchTo: \"@qqstring.[.]\" }],\n      [/(qq|qx)\\s*\\{/, { token: \"string.delim\", switchTo: \"@qqstring.{.}\" }],\n      [/(qq|qx)\\s*</, { token: \"string.delim\", switchTo: \"@qqstring.<.>\" }],\n      [/(qq|qx)#/, { token: \"string.delim\", switchTo: \"@qqstring.#.#\" }],\n      [/(qq|qx)\\s*([^A-Za-z0-9#\\s])/, { token: \"string.delim\", switchTo: \"@qqstring.$2.$2\" }],\n      [/(qq|qx)\\s+(\\w)/, { token: \"string.delim\", switchTo: \"@qqstring.$2.$2\" }]\n    ],\n    // Non-expanded quoted string\n    // qstring<open>.<close>\n    //  open = open delimiter\n    //  close = close delimiter\n    qstring: [\n      [/\\\\./, \"string.escape\"],\n      [\n        /./,\n        {\n          cases: {\n            \"$#==$S3\": { token: \"string.delim\", next: \"@pop\" },\n            \"$#==$S2\": { token: \"string.delim\", next: \"@push\" },\n            // nested delimiters\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    // Quoted regexp\n    // qregexp.<open>.<close>\n    //  open = open delimiter\n    //  close = close delimiter\n    qregexp: [\n      { include: \"@variables\" },\n      [/\\\\./, \"regexp.escape\"],\n      [\n        /./,\n        {\n          cases: {\n            \"$#==$S3\": {\n              token: \"regexp.delim\",\n              next: \"@regexpModifiers\"\n            },\n            \"$#==$S2\": { token: \"regexp.delim\", next: \"@push\" },\n            // nested delimiters\n            \"@default\": \"regexp\"\n          }\n        }\n      ]\n    ],\n    regexpModifiers: [[/[msixpodualngcer]+/, { token: \"regexp.modifier\", next: \"@popall\" }]],\n    // Expanded quoted string\n    // qqstring.<open>.<close>\n    //  open = open delimiter\n    //  close = close delimiter\n    qqstring: [{ include: \"@variables\" }, { include: \"@qstring\" }],\n    heredoc: [\n      [/<<\\s*['\"`]?([\\w\\-]+)['\"`]?/, { token: \"string.heredoc.delimiter\", next: \"@heredocBody.$1\" }]\n    ],\n    heredocBody: [\n      [\n        /^([\\w\\-]+)$/,\n        {\n          cases: {\n            \"$1==$S2\": [\n              {\n                token: \"string.heredoc.delimiter\",\n                next: \"@popall\"\n              }\n            ],\n            \"@default\": \"string.heredoc\"\n          }\n        }\n      ],\n      [/./, \"string.heredoc\"]\n    ],\n    perldoc: [[/^=\\w/, \"comment.doc\", \"@perldocBody\"]],\n    perldocBody: [\n      [/^=cut\\b/, \"type.identifier\", \"@popall\"],\n      [/./, \"comment.doc\"]\n    ],\n    variables: [\n      [/\\$\\w+/, \"variable\"],\n      // scalar\n      [/@\\w+/, \"variable\"],\n      // array\n      [/%\\w+/, \"variable\"]\n      // key/value\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/perl/perl.js\n"));

/***/ })

}]);