"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_rust_rust_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/rust/rust.js":
/*!*************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/rust/rust.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/rust/rust.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#pragma\\\\s+region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#pragma\\\\s+endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  tokenPostfix: \".rust\",\n  defaultToken: \"invalid\",\n  keywords: [\n    \"as\",\n    \"async\",\n    \"await\",\n    \"box\",\n    \"break\",\n    \"const\",\n    \"continue\",\n    \"crate\",\n    \"dyn\",\n    \"else\",\n    \"enum\",\n    \"extern\",\n    \"false\",\n    \"fn\",\n    \"for\",\n    \"if\",\n    \"impl\",\n    \"in\",\n    \"let\",\n    \"loop\",\n    \"match\",\n    \"mod\",\n    \"move\",\n    \"mut\",\n    \"pub\",\n    \"ref\",\n    \"return\",\n    \"self\",\n    \"static\",\n    \"struct\",\n    \"super\",\n    \"trait\",\n    \"true\",\n    \"try\",\n    \"type\",\n    \"unsafe\",\n    \"use\",\n    \"where\",\n    \"while\",\n    \"catch\",\n    \"default\",\n    \"union\",\n    \"static\",\n    \"abstract\",\n    \"alignof\",\n    \"become\",\n    \"do\",\n    \"final\",\n    \"macro\",\n    \"offsetof\",\n    \"override\",\n    \"priv\",\n    \"proc\",\n    \"pure\",\n    \"sizeof\",\n    \"typeof\",\n    \"unsized\",\n    \"virtual\",\n    \"yield\"\n  ],\n  typeKeywords: [\n    \"Self\",\n    \"m32\",\n    \"m64\",\n    \"m128\",\n    \"f80\",\n    \"f16\",\n    \"f128\",\n    \"int\",\n    \"uint\",\n    \"float\",\n    \"char\",\n    \"bool\",\n    \"u8\",\n    \"u16\",\n    \"u32\",\n    \"u64\",\n    \"f32\",\n    \"f64\",\n    \"i8\",\n    \"i16\",\n    \"i32\",\n    \"i64\",\n    \"str\",\n    \"Option\",\n    \"Either\",\n    \"c_float\",\n    \"c_double\",\n    \"c_void\",\n    \"FILE\",\n    \"fpos_t\",\n    \"DIR\",\n    \"dirent\",\n    \"c_char\",\n    \"c_schar\",\n    \"c_uchar\",\n    \"c_short\",\n    \"c_ushort\",\n    \"c_int\",\n    \"c_uint\",\n    \"c_long\",\n    \"c_ulong\",\n    \"size_t\",\n    \"ptrdiff_t\",\n    \"clock_t\",\n    \"time_t\",\n    \"c_longlong\",\n    \"c_ulonglong\",\n    \"intptr_t\",\n    \"uintptr_t\",\n    \"off_t\",\n    \"dev_t\",\n    \"ino_t\",\n    \"pid_t\",\n    \"mode_t\",\n    \"ssize_t\"\n  ],\n  constants: [\"true\", \"false\", \"Some\", \"None\", \"Left\", \"Right\", \"Ok\", \"Err\"],\n  supportConstants: [\n    \"EXIT_FAILURE\",\n    \"EXIT_SUCCESS\",\n    \"RAND_MAX\",\n    \"EOF\",\n    \"SEEK_SET\",\n    \"SEEK_CUR\",\n    \"SEEK_END\",\n    \"_IOFBF\",\n    \"_IONBF\",\n    \"_IOLBF\",\n    \"BUFSIZ\",\n    \"FOPEN_MAX\",\n    \"FILENAME_MAX\",\n    \"L_tmpnam\",\n    \"TMP_MAX\",\n    \"O_RDONLY\",\n    \"O_WRONLY\",\n    \"O_RDWR\",\n    \"O_APPEND\",\n    \"O_CREAT\",\n    \"O_EXCL\",\n    \"O_TRUNC\",\n    \"S_IFIFO\",\n    \"S_IFCHR\",\n    \"S_IFBLK\",\n    \"S_IFDIR\",\n    \"S_IFREG\",\n    \"S_IFMT\",\n    \"S_IEXEC\",\n    \"S_IWRITE\",\n    \"S_IREAD\",\n    \"S_IRWXU\",\n    \"S_IXUSR\",\n    \"S_IWUSR\",\n    \"S_IRUSR\",\n    \"F_OK\",\n    \"R_OK\",\n    \"W_OK\",\n    \"X_OK\",\n    \"STDIN_FILENO\",\n    \"STDOUT_FILENO\",\n    \"STDERR_FILENO\"\n  ],\n  supportMacros: [\n    \"format!\",\n    \"print!\",\n    \"println!\",\n    \"panic!\",\n    \"format_args!\",\n    \"unreachable!\",\n    \"write!\",\n    \"writeln!\"\n  ],\n  operators: [\n    \"!\",\n    \"!=\",\n    \"%\",\n    \"%=\",\n    \"&\",\n    \"&=\",\n    \"&&\",\n    \"*\",\n    \"*=\",\n    \"+\",\n    \"+=\",\n    \"-\",\n    \"-=\",\n    \"->\",\n    \".\",\n    \"..\",\n    \"...\",\n    \"/\",\n    \"/=\",\n    \":\",\n    \";\",\n    \"<<\",\n    \"<<=\",\n    \"<\",\n    \"<=\",\n    \"=\",\n    \"==\",\n    \"=>\",\n    \">\",\n    \">=\",\n    \">>\",\n    \">>=\",\n    \"@\",\n    \"^\",\n    \"^=\",\n    \"|\",\n    \"|=\",\n    \"||\",\n    \"_\",\n    \"?\",\n    \"#\"\n  ],\n  escapes: /\\\\([nrt0\\\"''\\\\]|x\\h{2}|u\\{\\h{1,6}\\})/,\n  delimiters: /[,]/,\n  symbols: /[\\#\\!\\%\\&\\*\\+\\-\\.\\/\\:\\;\\<\\=\\>\\@\\^\\|_\\?]+/,\n  intSuffixes: /[iu](8|16|32|64|128|size)/,\n  floatSuffixes: /f(32|64)/,\n  tokenizer: {\n    root: [\n      // Raw string literals\n      [/r(#*)\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@stringraw.$1\" }],\n      [\n        /[a-zA-Z][a-zA-Z0-9_]*!?|_[a-zA-Z0-9_]+/,\n        {\n          cases: {\n            \"@typeKeywords\": \"keyword.type\",\n            \"@keywords\": \"keyword\",\n            \"@supportConstants\": \"keyword\",\n            \"@supportMacros\": \"keyword\",\n            \"@constants\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // Designator\n      [/\\$/, \"identifier\"],\n      // Lifetime annotations\n      [/'[a-zA-Z_][a-zA-Z0-9_]*(?=[^\\'])/, \"identifier\"],\n      // Byte literal\n      [/'(\\S|@escapes)'/, \"string.byteliteral\"],\n      // Strings\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }],\n      { include: \"@numbers\" },\n      // Whitespace + comments\n      { include: \"@whitespace\" },\n      [\n        /@delimiters/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"delimiter\"\n          }\n        }\n      ],\n      [/[{}()\\[\\]<>]/, \"@brackets\"],\n      [/@symbols/, { cases: { \"@operators\": \"operator\", \"@default\": \"\" } }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      [\"\\\\*/\", \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    stringraw: [\n      [/[^\"#]+/, { token: \"string\" }],\n      [\n        /\"(#*)/,\n        {\n          cases: {\n            \"$1==$S2\": { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" },\n            \"@default\": { token: \"string\" }\n          }\n        }\n      ],\n      [/[\"#]/, { token: \"string\" }]\n    ],\n    numbers: [\n      //Octal\n      [/(0o[0-7_]+)(@intSuffixes)?/, { token: \"number\" }],\n      //Binary\n      [/(0b[0-1_]+)(@intSuffixes)?/, { token: \"number\" }],\n      //Exponent\n      [/[\\d][\\d_]*(\\.[\\d][\\d_]*)?[eE][+-][\\d_]+(@floatSuffixes)?/, { token: \"number\" }],\n      //Float\n      [/\\b(\\d\\.?[\\d_]*)(@floatSuffixes)?\\b/, { token: \"number\" }],\n      //Hexadecimal\n      [/(0x[\\da-fA-F]+)_?(@intSuffixes)?/, { token: \"number\" }],\n      //Integer\n      [/[\\d][\\d_]*(@intSuffixes?)?/, { token: \"number\" }]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/rust/rust.js\n"));

/***/ })

}]);