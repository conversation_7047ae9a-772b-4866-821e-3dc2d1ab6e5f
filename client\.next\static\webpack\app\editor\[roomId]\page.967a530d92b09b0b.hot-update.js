"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var monaco_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! monaco-editor */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.main.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlignLeft,FiCode,FiCopy,FiDownload,FiHelpCircle,FiLoader,FiMoon,FiPlay,FiSearch,FiSun,FiUpload,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_socketService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/socketService */ \"(app-pages-browser)/./src/services/socketService.ts\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/throttle */ \"(app-pages-browser)/./node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/SocketContext */ \"(app-pages-browser)/./src/context/SocketContext.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_formatCode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/formatCode */ \"(app-pages-browser)/./src/utils/formatCode.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/ThemeContext */ \"(app-pages-browser)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CodeEditor(param) {\n    let { roomId, username: initialUsername } = param;\n    _s();\n    const { isConnected } = (0,_context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket)();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"// Start coding...\");\n    // Use a ref to always track the latest code value\n    const latestCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"// Start coding...\");\n    // Get username from localStorage if available, otherwise use initialUsername\n    const storedUsername =  true ? localStorage.getItem(\"username\") : 0;\n    console.log(\"Initial username: \".concat(initialUsername, \", Stored username: \").concat(storedUsername));\n    // Track the username internally to handle server validation\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUsername || initialUsername);\n    const [typingUser, setTypingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeUsers, setActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUserList, setShowUserList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"javascript\");\n    const [copySuccess, setCopySuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Code execution states\n    const [isExecuting, setIsExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionOutput, setExecutionOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [executionError, setExecutionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOutput, setShowOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Piston API runtimes\n    const [runtimes, setRuntimes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [runtimesLoaded, setRuntimesLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    // Theme and minimap state (ensure these are always defined at the top)\n    const { theme, setTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme)();\n    const [showShortcuts, setShowShortcuts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [minimapEnabled, setMinimapEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Language-specific code snippets\n    const SNIPPETS = {\n        javascript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            }\n        ],\n        typescript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction(): void {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            },\n            {\n                label: \"Type Declaration\",\n                value: \"type MyType = {\\n  key: string;\\n};\"\n            }\n        ],\n        python3: [\n            {\n                label: \"Function\",\n                value: \"def my_function():\\n    # code\\n    pass\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in range(10):\\n    # code\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition:\\n    # code\"\n            },\n            {\n                label: \"Print\",\n                value: \"print('Hello, World!')\"\n            }\n        ],\n        java: [\n            {\n                label: \"Main Method\",\n                value: \"public static void main(String[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"System.out.println(\\\"Hello, World!\\\");\"\n            }\n        ],\n        csharp: [\n            {\n                label: \"Main Method\",\n                value: \"static void Main(string[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"Console.WriteLine(\\\"Hello, World!\\\");\"\n            }\n        ],\n        c: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"printf(\\\"Hello, World!\\\\n\\\");\"\n            }\n        ],\n        cpp: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"std::cout << \\\"Hello, World!\\\" << std::endl;\"\n            }\n        ],\n        go: [\n            {\n                label: \"Main Function\",\n                value: \"func main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i := 0; i < 10; i++ {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"fmt.Println(\\\"Hello, World!\\\")\"\n            }\n        ],\n        ruby: [\n            {\n                label: \"Method\",\n                value: \"def my_method\\n  # code\\nend\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..9\\n  # code\\nend\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition\\n  # code\\nend\"\n            },\n            {\n                label: \"Print\",\n                value: \"puts 'Hello, World!'\"\n            }\n        ],\n        rust: [\n            {\n                label: \"Main Function\",\n                value: \"fn main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..10 {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"println!(\\\"Hello, World!\\\");\"\n            }\n        ],\n        php: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for ($i = 0; $i < 10; $i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if ($condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"echo 'Hello, World!';\"\n            }\n        ]\n    };\n    // Declare the `socketService` instance at the top of the file\n    const socketService = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n    // Leave room handler\n    const handleLeaveRoom = ()=>{\n        socketService.leaveRoom(roomId);\n        router.push(\"/dashboard\");\n    };\n    // Handle editor mounting\n    const handleEditorDidMount = (editor)=>{\n        editorRef.current = editor;\n        setIsLoading(false);\n        // Auto-focus the editor after mounting\n        setTimeout(()=>{\n            if (editor) {\n                editor.focus();\n                // Add keyboard shortcut (Ctrl+Enter) to run code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.CtrlCmd | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.Enter, ()=>{\n                    executeCode();\n                });\n                // Add keyboard shortcut (Shift+Alt+F) to format code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Shift | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Alt | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.KeyF, ()=>{\n                    formatCurrentCode();\n                });\n            }\n        }, 500) // Small delay to ensure the editor is fully ready\n        ;\n    };\n    // Track if the change is from a remote update\n    const isRemoteUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Create a throttled version of the code change handler\n    // This prevents sending too many updates when typing quickly\n    // but ensures updates are sent at regular intervals\n    const throttledCodeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default()({\n        \"CodeEditor.useRef\": (code)=>{\n            if (roomId && isConnected) {\n                console.log(\"Sending throttled code update to room \".concat(roomId, \", length: \").concat(code.length));\n                socketService.sendCodeChange(roomId, code);\n            }\n        }\n    }[\"CodeEditor.useRef\"], 50) // Use throttle with a short interval for more responsive updates\n    ).current;\n    // Create a debounced version of the typing notification\n    const debouncedTypingNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default()({\n        \"CodeEditor.useCallback[debouncedTypingNotification]\": ()=>{\n            if (roomId && isConnected) {\n                // Always send the current username with typing notifications\n                console.log(\"Sending typing notification with username: \".concat(username));\n                socketService.sendTyping(roomId, username);\n            }\n        }\n    }[\"CodeEditor.useCallback[debouncedTypingNotification]\"], 1000), [\n        roomId,\n        isConnected,\n        username\n    ]);\n    // Handle editor change\n    const handleEditorChange = (value)=>{\n        if (typeof value !== \"string\") return;\n        // Make sure loading is off during code changes\n        setIsLoading(false);\n        // If this change is from a remote update, just update the state and return\n        if (isRemoteUpdate.current) {\n            console.log(\"Handling remote update, not emitting\");\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            isRemoteUpdate.current = false;\n            return;\n        }\n        // Only process if the value actually changed\n        if (value !== latestCodeRef.current) {\n            // Update local state immediately\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            // Send typing notification (debounced)\n            debouncedTypingNotification();\n            // Send code update (throttled)\n            throttledCodeChange(value);\n            // If not connected, try to reconnect\n            if (!isConnected) {\n                console.log(\"Socket not connected, attempting to reconnect\");\n                socketService.connect();\n            }\n        }\n    };\n    // Copy code to clipboard\n    const copyCodeToClipboard = ()=>{\n        if (navigator.clipboard && code) {\n            navigator.clipboard.writeText(code).then(()=>{\n                setCopySuccess(true);\n                setTimeout(()=>setCopySuccess(false), 2000);\n            }).catch((err)=>{\n                console.error('Failed to copy code: ', err);\n            });\n        }\n    };\n    // Toggle user list\n    const toggleUserList = ()=>{\n        setShowUserList((prev)=>!prev);\n    };\n    // Change language\n    const changeLanguage = (lang)=>{\n        setLanguage(lang);\n        // Log available runtimes for this language\n        if (runtimesLoaded && runtimes.length > 0) {\n            const availableRuntimes = runtimes.filter((runtime)=>runtime.language === lang || runtime.aliases && runtime.aliases.includes(lang));\n            if (availableRuntimes.length > 0) {\n                console.log(\"Available runtimes for \".concat(lang, \":\"), availableRuntimes);\n            } else {\n                console.log(\"No runtimes available for \".concat(lang));\n            }\n        }\n    };\n    // Format code\n    const formatCurrentCode = ()=>{\n        if (!code || !editorRef.current) return;\n        try {\n            // Format the code based on the current language\n            const formattedCode = (0,_utils_formatCode__WEBPACK_IMPORTED_MODULE_8__.formatCode)(code, language);\n            // Only update if the code actually changed\n            if (formattedCode !== code) {\n                // Update the editor\n                const model = editorRef.current.getModel();\n                if (model) {\n                    // Store current cursor position/selection\n                    const currentPosition = editorRef.current.getPosition();\n                    const currentSelection = editorRef.current.getSelection();\n                    // Update the editor content\n                    editorRef.current.executeEdits('format', [\n                        {\n                            range: model.getFullModelRange(),\n                            text: formattedCode,\n                            forceMoveMarkers: true\n                        }\n                    ]);\n                    // Restore cursor position if possible\n                    if (currentPosition) {\n                        editorRef.current.setPosition(currentPosition);\n                    }\n                    if (currentSelection) {\n                        editorRef.current.setSelection(currentSelection);\n                    }\n                    // Update state and ref\n                    setCode(formattedCode);\n                    latestCodeRef.current = formattedCode;\n                    // Send the formatted code to other users\n                    if (roomId && isConnected) {\n                        console.log(\"Sending formatted code to room \".concat(roomId, \", length: \").concat(formattedCode.length));\n                        socketService.sendCodeChange(roomId, formattedCode);\n                    }\n                    // Show a success message\n                    console.log('Code formatted successfully');\n                }\n            } else {\n                console.log('Code is already formatted');\n            }\n        } catch (error) {\n            console.error('Error formatting code:', error);\n        }\n    };\n    // Clear execution output\n    const clearOutput = ()=>{\n        setExecutionOutput(null);\n        setExecutionError(null);\n    };\n    // Fetch and store available runtimes from Piston API\n    const fetchRuntimes = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get('https://emkc.org/api/v2/piston/runtimes');\n            console.log('Piston API status:', response.status);\n            console.log('Available runtimes:', response.data);\n            // Store the runtimes in state\n            setRuntimes(response.data);\n            setRuntimesLoaded(true);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error fetching Piston API runtimes:', error);\n            return {\n                success: false,\n                error: axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) ? \"\".concat(error.message, \" - \").concat(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) || 'Unknown', \" \").concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText) || '') : String(error)\n            };\n        }\n    };\n    // Check if the Piston API is available\n    const checkPistonAPI = async ()=>{\n        // If we haven't loaded runtimes yet, fetch them\n        if (!runtimesLoaded) {\n            return await fetchRuntimes();\n        }\n        // If we already have runtimes, just return success\n        if (runtimes.length > 0) {\n            return {\n                success: true,\n                data: runtimes\n            };\n        }\n        // If we've tried to load runtimes but have none, try again\n        return await fetchRuntimes();\n    };\n    // Test the Piston API with a hardcoded sample payload\n    const testPistonAPI = async ()=>{\n        setIsExecuting(true);\n        setExecutionError(null);\n        setExecutionOutput(null);\n        setShowOutput(true);\n        try {\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Find JavaScript runtime\n            const jsRuntimes = apiStatus.data.filter((runtime)=>runtime.language === \"javascript\" || runtime.aliases && runtime.aliases.includes(\"javascript\"));\n            if (jsRuntimes.length === 0) {\n                setExecutionError('No JavaScript runtime found. Please try a different language.');\n                return;\n            }\n            const jsRuntime = jsRuntimes[0];\n            console.log(\"Using JavaScript runtime: \".concat(jsRuntime.language, \" \").concat(jsRuntime.version));\n            // Sample JavaScript code that should work\n            const samplePayload = {\n                language: jsRuntime.language,\n                version: jsRuntime.version,\n                files: [\n                    {\n                        name: \"main.js\",\n                        content: \"console.log('Hello, World!');\"\n                    }\n                ],\n                stdin: \"\",\n                args: []\n            };\n            console.log('Testing Piston API with sample payload:', JSON.stringify(samplePayload, null, 2));\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', samplePayload);\n            console.log('Sample execution response:', response.data);\n            if (response.data.run) {\n                setExecutionOutput(\"API Test Successful!\\n\\n\" + \"Output: \" + response.data.run.stdout + \"\\n\\n\" + \"The API is working correctly. You can now run your own code.\");\n            } else {\n                setExecutionError('API test failed. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error testing Piston API:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                setExecutionError(\"API Test Failed: \".concat(error.response.status, \" \").concat(error.response.statusText, \"\\n\\n\") + JSON.stringify(error.response.data, null, 2));\n            } else {\n                setExecutionError(error instanceof Error ? \"API Test Error: \".concat(error.message) : 'An unknown error occurred while testing the API.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // Execute code using Piston API\n    const executeCode = async ()=>{\n        if (!code || isExecuting) return;\n        // Map Monaco editor language to Piston API language\n        const languageMap = {\n            javascript: \"javascript\",\n            typescript: \"typescript\",\n            python: \"python3\",\n            python3: \"python3\",\n            java: \"java\",\n            csharp: \"csharp\",\n            c: \"c\",\n            cpp: \"cpp\",\n            go: \"go\",\n            ruby: \"ruby\",\n            rust: \"rust\",\n            php: \"php\"\n        };\n        // Get the mapped language\n        const pistonLanguage = languageMap[language] || language;\n        // Check if we have runtimes loaded\n        if (!runtimesLoaded || runtimes.length === 0) {\n            setExecutionError('Runtimes not loaded yet. Please try again in a moment.');\n            setShowOutput(true);\n            return;\n        }\n        // Find available runtimes for the selected language\n        const availableRuntimes = runtimes.filter((runtime)=>runtime.language === pistonLanguage || runtime.aliases && runtime.aliases.includes(pistonLanguage));\n        // Check if the language is supported\n        if (availableRuntimes.length === 0) {\n            // Get a list of supported languages from the runtimes\n            const supportedLanguages = [\n                ...new Set(runtimes.flatMap((runtime)=>[\n                        runtime.language,\n                        ...runtime.aliases || []\n                    ]))\n            ].sort();\n            setExecutionError(\"The language '\".concat(language, \"' (mapped to '\").concat(pistonLanguage, \"') is not supported by the Piston API.\\n\\n\") + \"Supported languages: \".concat(supportedLanguages.join(', ')));\n            setShowOutput(true);\n            return;\n        }\n        // Get the latest version of the runtime\n        const selectedRuntime = availableRuntimes[0];\n        try {\n            setIsExecuting(true);\n            setExecutionError(null);\n            setExecutionOutput(null);\n            setShowOutput(true);\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Determine file extension based on language\n            let fileExtension = '';\n            if (selectedRuntime.language === 'python3' || selectedRuntime.language === 'python') fileExtension = '.py';\n            else if (selectedRuntime.language === 'javascript') fileExtension = '.js';\n            else if (selectedRuntime.language === 'typescript') fileExtension = '.ts';\n            else if (selectedRuntime.language === 'java') fileExtension = '.java';\n            else if (selectedRuntime.language === 'csharp') fileExtension = '.cs';\n            else if (selectedRuntime.language === 'c') fileExtension = '.c';\n            else if (selectedRuntime.language === 'cpp') fileExtension = '.cpp';\n            else if (selectedRuntime.language === 'go') fileExtension = '.go';\n            else if (selectedRuntime.language === 'rust') fileExtension = '.rs';\n            else if (selectedRuntime.language === 'ruby') fileExtension = '.rb';\n            else if (selectedRuntime.language === 'php') fileExtension = '.php';\n            else fileExtension = \".\".concat(selectedRuntime.language);\n            console.log(\"Selected runtime: \".concat(selectedRuntime.language, \" \").concat(selectedRuntime.version));\n            // Prepare the payload according to Piston API documentation\n            const payload = {\n                language: selectedRuntime.language,\n                version: selectedRuntime.version,\n                files: [\n                    {\n                        name: \"main\".concat(fileExtension),\n                        content: code\n                    }\n                ],\n                stdin: '',\n                args: [],\n                compile_timeout: 10000,\n                run_timeout: 5000\n            };\n            // Log the payload for debugging\n            console.log(\"Executing \".concat(pistonLanguage, \" code with payload:\"), JSON.stringify(payload, null, 2));\n            // Make the API request\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', payload);\n            console.log('Execution response:', response.data);\n            const result = response.data;\n            if (result.run) {\n                // Format the output\n                let output = '';\n                let hasOutput = false;\n                // Add compile output if available (for compiled languages)\n                if (result.compile && result.compile.stderr) {\n                    output += \"Compilation output:\\n\".concat(result.compile.stderr, \"\\n\\n\");\n                    hasOutput = true;\n                }\n                // Add standard output\n                if (result.run.stdout) {\n                    output += result.run.stdout;\n                    hasOutput = true;\n                }\n                // Add error output\n                if (result.run.stderr) {\n                    if (hasOutput) output += '\\n';\n                    output += \"Error output:\\n\".concat(result.run.stderr);\n                    hasOutput = true;\n                }\n                // Add exit code if non-zero\n                if (result.run.code !== 0) {\n                    if (hasOutput) output += '\\n';\n                    output += \"\\nProcess exited with code \".concat(result.run.code);\n                    hasOutput = true;\n                }\n                if (!hasOutput) {\n                    output = 'Program executed successfully with no output.';\n                }\n                setExecutionOutput(output);\n            } else {\n                setExecutionError('Failed to execute code. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error executing code:', error);\n            // Handle Axios errors with more detailed information\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                const statusCode = error.response.status;\n                const responseData = error.response.data;\n                console.error('API Error Details:', {\n                    status: statusCode,\n                    data: responseData\n                });\n                // Format a more helpful error message\n                if (statusCode === 400) {\n                    setExecutionError(\"API Error (400 Bad Request): \".concat(JSON.stringify(responseData), \"\\n\\n\") + 'This usually means the API request format is incorrect. ' + 'Please check the console for more details.');\n                } else if (statusCode === 429) {\n                    setExecutionError('Rate limit exceeded. Please try again later.');\n                } else {\n                    setExecutionError(\"API Error (\".concat(statusCode, \"): \").concat(JSON.stringify(responseData), \"\\n\\n\") + 'Please check the console for more details.');\n                }\n            } else {\n                // Handle non-Axios errors\n                setExecutionError(error instanceof Error ? \"Error: \".concat(error.message) : 'An unknown error occurred while executing the code.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // This function will be called when we receive a code update from another user\n    const handleCodeUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleCodeUpdate]\": (incomingCode)=>{\n            console.log(\"Remote code update received, length:\", incomingCode.length);\n            // Make sure loading is off during code updates\n            setIsLoading(false);\n            // Only update if the code is different from our latest code\n            if (incomingCode !== latestCodeRef.current) {\n                try {\n                    // Set the flag to indicate this is a remote update\n                    isRemoteUpdate.current = true;\n                    // Update the editor if it's mounted\n                    if (editorRef.current) {\n                        console.log(\"Updating editor with remote code\");\n                        // Use the editor's model to update the value to preserve cursor position\n                        const model = editorRef.current.getModel();\n                        if (model) {\n                            // Store current cursor position/selection\n                            const currentPosition = editorRef.current.getPosition();\n                            const currentSelection = editorRef.current.getSelection();\n                            // Use executeEdits with better handling of cursor position\n                            editorRef.current.executeEdits('remote', [\n                                {\n                                    range: model.getFullModelRange(),\n                                    text: incomingCode,\n                                    forceMoveMarkers: true\n                                }\n                            ]);\n                            // Restore cursor position if possible\n                            if (currentPosition) {\n                                editorRef.current.setPosition(currentPosition);\n                            }\n                            if (currentSelection) {\n                                editorRef.current.setSelection(currentSelection);\n                            }\n                            // Also update our state and ref\n                            setCode(incomingCode);\n                            latestCodeRef.current = incomingCode;\n                        }\n                    } else {\n                        // If editor isn't mounted yet, just update the state and ref\n                        console.log(\"Editor not mounted, updating state only\");\n                        setCode(incomingCode);\n                        latestCodeRef.current = incomingCode;\n                        isRemoteUpdate.current = false;\n                    }\n                } catch (error) {\n                    console.error(\"Error updating editor with remote code:\", error);\n                    isRemoteUpdate.current = false;\n                }\n            } else {\n                console.log(\"Remote code matches current code, ignoring\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleCodeUpdate]\"], []);\n    // State to track cursor positions\n    const [cursorPositions, setCursorPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Emit cursor movement\n    const handleMouseMove = (e)=>{\n        const position = {\n            x: e.clientX,\n            y: e.clientY\n        };\n        _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().sendCursorMove(roomId, username, position);\n    };\n    // Listen for cursor movement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            const handleCursorMove = {\n                \"CodeEditor.useEffect.handleCursorMove\": (param)=>{\n                    let { userId, position } = param;\n                    setCursorPositions({\n                        \"CodeEditor.useEffect.handleCursorMove\": (prev)=>({\n                                ...prev,\n                                [userId]: position\n                            })\n                    }[\"CodeEditor.useEffect.handleCursorMove\"]);\n                }\n            }[\"CodeEditor.useEffect.handleCursorMove\"];\n            _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().on(\"cursor-move\", handleCursorMove);\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().off(\"cursor-move\", handleCursorMove);\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId\n    ]);\n    // Render cursors\n    const renderCursors = ()=>{\n        return Object.entries(cursorPositions).map((param)=>{\n            let [userId, position] = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    left: position.x,\n                    top: position.y,\n                    backgroundColor: \"red\",\n                    width: 10,\n                    height: 10,\n                    borderRadius: \"50%\",\n                    pointerEvents: \"none\"\n                }\n            }, userId, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 718,\n                columnNumber: 7\n            }, this);\n        });\n    };\n    // Fetch runtimes when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            fetchRuntimes();\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            // Handle user typing notifications\n            const handleUserTyping = {\n                \"CodeEditor.useEffect.handleUserTyping\": (param)=>{\n                    let { username, userId } = param;\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    // Don't show typing indicator for current user\n                    if (userId && userId === currentUserId) {\n                        return;\n                    }\n                    // Use the exact username without any modifications\n                    // This ensures we display exactly what the user entered on the dashboard\n                    setTypingUser(username);\n                    console.log(\"User typing: \".concat(username));\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserTyping\": ()=>setTypingUser(null)\n                    }[\"CodeEditor.useEffect.handleUserTyping\"], 2000);\n                }\n            }[\"CodeEditor.useEffect.handleUserTyping\"];\n            // Handle user list updates from both user-joined/user-left and room-users-updated events\n            const handleUserListUpdate = {\n                \"CodeEditor.useEffect.handleUserListUpdate\": (data)=>{\n                    console.log('User list update received:', data);\n                    let users = [];\n                    // Handle different event formats\n                    if (Array.isArray(data)) {\n                        // This is from user-joined or user-left events\n                        users = data;\n                    } else if (data && Array.isArray(data.users)) {\n                        // This is from room-users-updated event\n                        users = data.users.map({\n                            \"CodeEditor.useEffect.handleUserListUpdate\": (user)=>{\n                                if (typeof user === 'string') {\n                                    // If it's just a username string, convert to user object\n                                    return {\n                                        username: user\n                                    };\n                                }\n                                return user;\n                            }\n                        }[\"CodeEditor.useEffect.handleUserListUpdate\"]);\n                    }\n                    console.log('Processed users:', users);\n                    console.log('Current username state:', username);\n                    console.log('Stored username:', localStorage.getItem('username'));\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    console.log('Current userId from localStorage:', currentUserId);\n                    // Filter out any invalid users and map to display names\n                    const usernames = users.filter({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>user && user.username\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]).map({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>{\n                            const displayName = user.username;\n                            const isCurrentUser = user.userId === currentUserId;\n                            return isCurrentUser ? \"\".concat(displayName, \" (you)\") : displayName;\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]);\n                    console.log('Setting active users:', usernames);\n                    // Only update if we have users to display\n                    if (usernames.length > 0) {\n                        setActiveUsers(usernames);\n                    } else if (activeUsers.length === 0) {\n                        // If we don't have users from the server but we're connected, add ourselves\n                        const storedUsername = localStorage.getItem('username');\n                        if (storedUsername) {\n                            setActiveUsers([\n                                \"\".concat(storedUsername, \" (you)\")\n                            ]);\n                        }\n                    }\n                    // Log the current state of the active users list\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserListUpdate\": ()=>{\n                            console.log('Active users state after update:', activeUsers);\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate\"], 100);\n                }\n            }[\"CodeEditor.useEffect.handleUserListUpdate\"];\n            // Register event listeners\n            const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n            socketServiceInstance.on('code-update', handleCodeUpdate);\n            socketServiceInstance.on('user-typing', handleUserTyping);\n            socketServiceInstance.on('user-joined', handleUserListUpdate);\n            socketServiceInstance.on('user-left', handleUserListUpdate);\n            socketServiceInstance.on('room-users-updated', handleUserListUpdate);\n            socketServiceInstance.on('get-initial-code', handleGetInitialCode);\n            socketServiceInstance.on('initial-code-received', handleInitialCodeReceived);\n            // Join the room when component mounts\n            if (roomId) {\n                console.log(\"Joining room:\", roomId);\n                // Only show loading on initial join, not for code updates\n                if (!editorRef.current) {\n                    setIsLoading(true);\n                }\n                const joinRoom = {\n                    \"CodeEditor.useEffect.joinRoom\": async ()=>{\n                        try {\n                            if (socketServiceInstance.isConnected()) {\n                                console.log(\"Socket connected, joining room\");\n                                const { username: validatedUsername, users } = await socketServiceInstance.joinRoom(roomId, username);\n                                console.log(\"Successfully joined room:\", roomId);\n                                console.log(\"Users in room:\", users);\n                                if (validatedUsername !== username) {\n                                    console.log(\"Server validated username from \".concat(username, \" to \").concat(validatedUsername));\n                                    setUsername(validatedUsername);\n                                    localStorage.setItem('username', validatedUsername);\n                                }\n                                if (users && Array.isArray(users)) {\n                                    console.log('Setting active users from join response');\n                                    handleUserListUpdate(users);\n                                }\n                                setIsLoading(false);\n                            } else {\n                                console.log(\"Socket not connected, trying to connect...\");\n                                await new Promise({\n                                    \"CodeEditor.useEffect.joinRoom\": (resolve)=>socketServiceInstance.onConnect({\n                                            \"CodeEditor.useEffect.joinRoom\": ()=>resolve()\n                                        }[\"CodeEditor.useEffect.joinRoom\"])\n                                }[\"CodeEditor.useEffect.joinRoom\"]);\n                                // After connect, try join again\n                                await joinRoom();\n                                return;\n                            }\n                        } catch (error) {\n                            console.error(\"Error joining room:\", error);\n                            setIsLoading(false);\n                        }\n                    }\n                }[\"CodeEditor.useEffect.joinRoom\"];\n                // Start the join process\n                joinRoom();\n                // Set a timeout to hide the loading screen even if the join fails\n                setTimeout({\n                    \"CodeEditor.useEffect\": ()=>{\n                        setIsLoading(false);\n                    }\n                }[\"CodeEditor.useEffect\"], 3000);\n            }\n            // Clean up event listeners when component unmounts\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    // Use an instance of SocketService\n                    const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                    socketServiceInstance.off('code-update', handleCodeUpdate);\n                    socketServiceInstance.off('user-typing', handleUserTyping);\n                    socketServiceInstance.off('user-joined', handleUserListUpdate);\n                    socketServiceInstance.off('user-left', handleUserListUpdate);\n                    socketServiceInstance.off('room-users-updated', handleUserListUpdate);\n                    // Leave the room when component unmounts\n                    if (roomId) {\n                        socketServiceInstance.leaveRoom(roomId);\n                    }\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId,\n        initialUsername,\n        username,\n        handleCodeUpdate\n    ]);\n    // Download code as file\n    const handleDownload = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"code-\".concat(roomId || \"session\", \".txt\");\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    // Upload code from file\n    const handleUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            var _event_target;\n            if (typeof ((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result) === \"string\") {\n                setCode(event.target.result);\n                latestCodeRef.current = event.target.result;\n            }\n        };\n        reader.readAsText(file);\n    };\n    // Show Monaco's find/replace dialog\n    const handleFindReplace = ()=>{\n        if (editorRef.current) {\n            const action = editorRef.current.getAction(\"actions.find\");\n            if (action) action.run();\n        }\n    };\n    // Insert code snippet\n    const insertSnippet = (snippet)=>{\n        if (editorRef.current) {\n            const model = editorRef.current.getModel();\n            const position = editorRef.current.getPosition();\n            if (model && position) {\n                editorRef.current.executeEdits(\"snippet\", [\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(position.lineNumber, position.column, position.lineNumber, position.column),\n                        text: snippet,\n                        forceMoveMarkers: true\n                    }\n                ]);\n                editorRef.current.focus();\n            }\n        }\n    };\n    // Use the instance of SocketService for the connect method\n    const handleReconnect = ()=>{\n        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n        socketServiceInstance.connect();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-[80vh] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-800 shadow-xl flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"bg-gray-100 dark:bg-zinc-800 px-4 py-2 flex flex-wrap items-center justify-between border-b border-gray-200 dark:border-gray-700 gap-2 min-h-[48px]\",\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    style: {\n                        zIndex: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-3 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 min-w-[90px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 961,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-400 truncate\",\n                                            children: isConnected ? 'Connected' : 'Disconnected'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 962,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded ml-1 transition-colors\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 966,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 960,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: language,\n                                    onChange: (e)=>changeLanguage(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"javascript\",\n                                            children: \"JavaScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 981,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"typescript\",\n                                            children: \"TypeScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 982,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"python3\",\n                                            children: \"Python\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 983,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"java\",\n                                            children: \"Java\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 984,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"csharp\",\n                                            children: \"C#\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"c\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 986,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"cpp\",\n                                            children: \"C++\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 987,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"go\",\n                                            children: \"Go\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 988,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ruby\",\n                                            children: \"Ruby\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 989,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rust\",\n                                            children: \"Rust\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 990,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"php\",\n                                            children: \"PHP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 991,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 976,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: formatCurrentCode,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm bg-blue-600 hover:bg-blue-700 text-white transition-colors whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiAlignLeft, {\n                                            size: 14\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1001,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Format\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1002,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 995,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: executeCode,\n                                    disabled: isExecuting,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm \".concat(isExecuting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700', \" text-white transition-colors whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: isExecuting ? 1 : 1.05\n                                    },\n                                    whileTap: {\n                                        scale: isExecuting ? 1 : 0.95\n                                    },\n                                    children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                                className: \"animate-spin\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1015,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Running...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1016,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiPlay, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1020,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Run Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1021,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1006,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Room:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1028,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-white dark:bg-zinc-700 px-2 py-1 rounded font-mono text-xs truncate max-w-[120px]\",\n                                            children: roomId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1029,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                            onClick: copyCodeToClipboard,\n                                            className: \"text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            title: \"Copy code to clipboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCopy, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1030,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1027,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 958,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-2 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowOutput((prev)=>!prev),\n                                    className: \"flex items-center space-x-1 text-sm \".concat(showOutput ? 'text-blue-500 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300', \" hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCode, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1050,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Output\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1051,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1044,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: toggleUserList,\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUsers, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1061,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                activeUsers.length,\n                                                \" online\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1062,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1055,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"flex items-center space-x-1 text-sm text-yellow-600 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSun, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1073,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiMoon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1073,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1066,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setMinimapEnabled((v)=>!v),\n                                    className: \"flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Minimap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Minimap\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1084,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1077,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleFindReplace,\n                                    className: \"flex items-center space-x-1 text-sm text-green-600 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Find/Replace\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSearch, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1095,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1088,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleDownload,\n                                    className: \"flex items-center space-x-1 text-sm text-indigo-600 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Download Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiDownload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1106,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1099,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"flex items-center space-x-1 text-sm text-pink-600 dark:text-pink-300 bg-pink-100 dark:bg-pink-900/30 hover:bg-pink-200 dark:hover:bg-pink-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Upload Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUpload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1117,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \".js,.ts,.py,.java,.c,.cpp,.go,.rb,.rs,.php,.txt\",\n                                    style: {\n                                        display: \"none\"\n                                    },\n                                    onChange: handleUpload\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    onChange: (e)=>e.target.value && insertSnippet(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[100px]\",\n                                    defaultValue: \"\",\n                                    title: \"Insert Snippet\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Snippets\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1134,\n                                            columnNumber: 15\n                                        }, this),\n                                        (SNIPPETS[language] || SNIPPETS['javascript']).map((snippet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: snippet.value,\n                                                children: snippet.label\n                                            }, snippet.label, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1136,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowShortcuts(true),\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Keyboard Shortcuts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiHelpCircle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1148,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleLeaveRoom,\n                                    className: \"flex items-center space-x-1 text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors px-2 py-1 rounded-md ml-2 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Leave Room\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1158,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1042,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 951,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showUserList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute right-0 top-12 bg-white dark:bg-zinc-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 w-48 overflow-hidden\",\n                        initial: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            height: 'auto'\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 border-b border-gray-200 dark:border-gray-700 font-medium text-sm text-gray-600 dark:text-gray-300\",\n                                children: \"Active Users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1173,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"max-h-48 overflow-y-auto\",\n                                children: activeUsers.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.li, {\n                                        className: \"px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-zinc-700 flex items-center space-x-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full bg-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1185,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: user\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1186,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1178,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1176,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1166,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1164,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"h-[calc(100%-40px)] \".concat(showOutput ? 'pb-[33%]' : ''),\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.4,\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                height: \"100%\",\n                                defaultLanguage: language,\n                                defaultValue: code,\n                                onChange: handleEditorChange,\n                                onMount: handleEditorDidMount,\n                                theme: theme === \"dark\" ? \"vs-dark\" : \"light\",\n                                options: {\n                                    minimap: {\n                                        enabled: minimapEnabled\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1204,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1203,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1201,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1195,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: typingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 left-4 text-sm text-gray-400 bg-gray-800/80 px-3 py-1 rounded-full backdrop-blur-sm\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            typingUser,\n                            \" is typing...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1220,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1218,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gray-900 text-white border-t border-gray-700 h-1/3 overflow-hidden flex flex-col\",\n                        initial: {\n                            height: 0\n                        },\n                        animate: {\n                            height: '33%'\n                        },\n                        exit: {\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 bg-gray-800 border-b border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1243,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearOutput,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1245,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: testPistonAPI,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-blue-400 hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Test API\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1252,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowOutput(false),\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1259,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1244,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1242,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-auto font-mono text-sm whitespace-pre-wrap\",\n                                children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1270,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Executing code...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1271,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1269,\n                                    columnNumber: 19\n                                }, this) : executionError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400\",\n                                    children: executionError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1274,\n                                    columnNumber: 19\n                                }, this) : executionOutput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: executionOutput\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1276,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: 'Click \"Run Code\" to execute your code.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1278,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1267,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1235,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1233,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: copySuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 right-4 text-sm text-white bg-green-500 px-3 py-1 rounded-full shadow-lg\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: \"Copied to clipboard!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1288,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1286,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showShortcuts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 max-w-md w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-700 dark:hover:text-white\",\n                                    onClick: ()=>setShowShortcuts(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1310,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    children: \"Keyboard Shortcuts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1316,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Run Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1318,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+Enter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1318,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Format Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1319,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Shift+Alt+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1319,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Find/Replace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1320,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1320,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Download Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1321,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+D\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1321,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Upload Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1322,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+U\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1322,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Minimap:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1323,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+M\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1323,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Theme:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1324,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+T\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1324,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Show Shortcuts:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1325,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+H\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1325,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1317,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1309,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1303,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1301,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 949,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 947,\n        columnNumber: 5\n    }, this);\n} // Keyboard shortcuts global handler\n // useEffect(() => {\n //   const handler = (e: KeyboardEvent) => {\n //     if (e.altKey && e.key.toLowerCase() === \"d\") {\n //       e.preventDefault();\n //       handleDownload();\n //     } else if (e.altKey && e.key.toLowerCase() === \"u\") {\n //       e.preventDefault();\n //       fileInputRef.current?.click();\n //     } else if (e.altKey && e.key.toLowerCase() === \"m\") {\n //       e.preventDefault();\n //       setMinimapEnabled((v: boolean) => !v);\n //     } else if (e.altKey && e.key.toLowerCase() === \"t\") {\n //       e.preventDefault();\n //       setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n //     } else if (e.altKey && e.key.toLowerCase() === \"h\") {\n //       e.preventDefault();\n //       setShowShortcuts(true);\n //     }\n //   };\n //   window.addEventListener(\"keydown\", handler);\n //   return () => window.removeEventListener(\"keydown\", handler);\n // }, [theme, setTheme]);\n_s(CodeEditor, \"2WwyeeZiqxytaE++jGkyfL2MSN0=\", false, function() {\n    return [\n        _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme\n    ];\n});\n_c = CodeEditor;\nvar _c;\n$RefreshReg$(_c, \"CodeEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CodeEditor.tsx\n"));

/***/ })

});