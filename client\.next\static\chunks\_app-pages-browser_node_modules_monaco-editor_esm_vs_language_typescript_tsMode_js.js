"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_language_typescript_tsMode_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/language/typescript/tsMode.js":
/*!**************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/language/typescript/tsMode.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Adapter: () => (/* binding */ Adapter),\n/* harmony export */   CodeActionAdaptor: () => (/* binding */ CodeActionAdaptor),\n/* harmony export */   DefinitionAdapter: () => (/* binding */ DefinitionAdapter),\n/* harmony export */   DiagnosticsAdapter: () => (/* binding */ DiagnosticsAdapter),\n/* harmony export */   DocumentHighlightAdapter: () => (/* binding */ DocumentHighlightAdapter),\n/* harmony export */   FormatAdapter: () => (/* binding */ FormatAdapter),\n/* harmony export */   FormatHelper: () => (/* binding */ FormatHelper),\n/* harmony export */   FormatOnTypeAdapter: () => (/* binding */ FormatOnTypeAdapter),\n/* harmony export */   InlayHintsAdapter: () => (/* binding */ InlayHintsAdapter),\n/* harmony export */   Kind: () => (/* binding */ Kind),\n/* harmony export */   LibFiles: () => (/* binding */ LibFiles),\n/* harmony export */   OutlineAdapter: () => (/* binding */ OutlineAdapter),\n/* harmony export */   QuickInfoAdapter: () => (/* binding */ QuickInfoAdapter),\n/* harmony export */   ReferenceAdapter: () => (/* binding */ ReferenceAdapter),\n/* harmony export */   RenameAdapter: () => (/* binding */ RenameAdapter),\n/* harmony export */   SignatureHelpAdapter: () => (/* binding */ SignatureHelpAdapter),\n/* harmony export */   SuggestAdapter: () => (/* binding */ SuggestAdapter),\n/* harmony export */   WorkerManager: () => (/* binding */ WorkerManager),\n/* harmony export */   flattenDiagnosticMessageText: () => (/* binding */ flattenDiagnosticMessageText),\n/* harmony export */   getJavaScriptWorker: () => (/* binding */ getJavaScriptWorker),\n/* harmony export */   getTypeScriptWorker: () => (/* binding */ getTypeScriptWorker),\n/* harmony export */   setupJavaScript: () => (/* binding */ setupJavaScript),\n/* harmony export */   setupTypeScript: () => (/* binding */ setupTypeScript)\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.api.js\");\n/* harmony import */ var _monaco_contribution_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./monaco.contribution.js */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/language/typescript/monaco.contribution.js\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/language/typescript/workerManager.ts\nvar WorkerManager = class {\n  constructor(_modeId, _defaults) {\n    this._modeId = _modeId;\n    this._defaults = _defaults;\n    this._worker = null;\n    this._client = null;\n    this._configChangeListener = this._defaults.onDidChange(() => this._stopWorker());\n    this._updateExtraLibsToken = 0;\n    this._extraLibsChangeListener = this._defaults.onDidExtraLibsChange(\n      () => this._updateExtraLibs()\n    );\n  }\n  dispose() {\n    this._configChangeListener.dispose();\n    this._extraLibsChangeListener.dispose();\n    this._stopWorker();\n  }\n  _stopWorker() {\n    if (this._worker) {\n      this._worker.dispose();\n      this._worker = null;\n    }\n    this._client = null;\n  }\n  async _updateExtraLibs() {\n    if (!this._worker) {\n      return;\n    }\n    const myToken = ++this._updateExtraLibsToken;\n    const proxy = await this._worker.getProxy();\n    if (this._updateExtraLibsToken !== myToken) {\n      return;\n    }\n    proxy.updateExtraLibs(this._defaults.getExtraLibs());\n  }\n  _getClient() {\n    if (!this._client) {\n      this._client = (async () => {\n        this._worker = monaco_editor_core_exports.editor.createWebWorker({\n          // module that exports the create() method and returns a `TypeScriptWorker` instance\n          moduleId: \"vs/language/typescript/tsWorker\",\n          label: this._modeId,\n          keepIdleModels: true,\n          // passed in to the create() method\n          createData: {\n            compilerOptions: this._defaults.getCompilerOptions(),\n            extraLibs: this._defaults.getExtraLibs(),\n            customWorkerPath: this._defaults.workerOptions.customWorkerPath,\n            inlayHintsOptions: this._defaults.inlayHintsOptions\n          }\n        });\n        if (this._defaults.getEagerModelSync()) {\n          return await this._worker.withSyncedResources(\n            monaco_editor_core_exports.editor.getModels().filter((model) => model.getLanguageId() === this._modeId).map((model) => model.uri)\n          );\n        }\n        return await this._worker.getProxy();\n      })();\n    }\n    return this._client;\n  }\n  async getLanguageServiceWorker(...resources) {\n    const client = await this._getClient();\n    if (this._worker) {\n      await this._worker.withSyncedResources(resources);\n    }\n    return client;\n  }\n};\n\n// src/language/typescript/languageFeatures.ts\n\n\n// src/language/typescript/lib/lib.index.ts\nvar libFileSet = {};\nlibFileSet[\"lib.d.ts\"] = true;\nlibFileSet[\"lib.decorators.d.ts\"] = true;\nlibFileSet[\"lib.decorators.legacy.d.ts\"] = true;\nlibFileSet[\"lib.dom.asynciterable.d.ts\"] = true;\nlibFileSet[\"lib.dom.d.ts\"] = true;\nlibFileSet[\"lib.dom.iterable.d.ts\"] = true;\nlibFileSet[\"lib.es2015.collection.d.ts\"] = true;\nlibFileSet[\"lib.es2015.core.d.ts\"] = true;\nlibFileSet[\"lib.es2015.d.ts\"] = true;\nlibFileSet[\"lib.es2015.generator.d.ts\"] = true;\nlibFileSet[\"lib.es2015.iterable.d.ts\"] = true;\nlibFileSet[\"lib.es2015.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2015.proxy.d.ts\"] = true;\nlibFileSet[\"lib.es2015.reflect.d.ts\"] = true;\nlibFileSet[\"lib.es2015.symbol.d.ts\"] = true;\nlibFileSet[\"lib.es2015.symbol.wellknown.d.ts\"] = true;\nlibFileSet[\"lib.es2016.array.include.d.ts\"] = true;\nlibFileSet[\"lib.es2016.d.ts\"] = true;\nlibFileSet[\"lib.es2016.full.d.ts\"] = true;\nlibFileSet[\"lib.es2016.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2017.d.ts\"] = true;\nlibFileSet[\"lib.es2017.date.d.ts\"] = true;\nlibFileSet[\"lib.es2017.full.d.ts\"] = true;\nlibFileSet[\"lib.es2017.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2017.object.d.ts\"] = true;\nlibFileSet[\"lib.es2017.sharedmemory.d.ts\"] = true;\nlibFileSet[\"lib.es2017.string.d.ts\"] = true;\nlibFileSet[\"lib.es2017.typedarrays.d.ts\"] = true;\nlibFileSet[\"lib.es2018.asyncgenerator.d.ts\"] = true;\nlibFileSet[\"lib.es2018.asynciterable.d.ts\"] = true;\nlibFileSet[\"lib.es2018.d.ts\"] = true;\nlibFileSet[\"lib.es2018.full.d.ts\"] = true;\nlibFileSet[\"lib.es2018.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2018.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2018.regexp.d.ts\"] = true;\nlibFileSet[\"lib.es2019.array.d.ts\"] = true;\nlibFileSet[\"lib.es2019.d.ts\"] = true;\nlibFileSet[\"lib.es2019.full.d.ts\"] = true;\nlibFileSet[\"lib.es2019.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2019.object.d.ts\"] = true;\nlibFileSet[\"lib.es2019.string.d.ts\"] = true;\nlibFileSet[\"lib.es2019.symbol.d.ts\"] = true;\nlibFileSet[\"lib.es2020.bigint.d.ts\"] = true;\nlibFileSet[\"lib.es2020.d.ts\"] = true;\nlibFileSet[\"lib.es2020.date.d.ts\"] = true;\nlibFileSet[\"lib.es2020.full.d.ts\"] = true;\nlibFileSet[\"lib.es2020.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2020.number.d.ts\"] = true;\nlibFileSet[\"lib.es2020.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2020.sharedmemory.d.ts\"] = true;\nlibFileSet[\"lib.es2020.string.d.ts\"] = true;\nlibFileSet[\"lib.es2020.symbol.wellknown.d.ts\"] = true;\nlibFileSet[\"lib.es2021.d.ts\"] = true;\nlibFileSet[\"lib.es2021.full.d.ts\"] = true;\nlibFileSet[\"lib.es2021.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2021.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2021.string.d.ts\"] = true;\nlibFileSet[\"lib.es2021.weakref.d.ts\"] = true;\nlibFileSet[\"lib.es2022.array.d.ts\"] = true;\nlibFileSet[\"lib.es2022.d.ts\"] = true;\nlibFileSet[\"lib.es2022.error.d.ts\"] = true;\nlibFileSet[\"lib.es2022.full.d.ts\"] = true;\nlibFileSet[\"lib.es2022.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2022.object.d.ts\"] = true;\nlibFileSet[\"lib.es2022.regexp.d.ts\"] = true;\nlibFileSet[\"lib.es2022.sharedmemory.d.ts\"] = true;\nlibFileSet[\"lib.es2022.string.d.ts\"] = true;\nlibFileSet[\"lib.es2023.array.d.ts\"] = true;\nlibFileSet[\"lib.es2023.collection.d.ts\"] = true;\nlibFileSet[\"lib.es2023.d.ts\"] = true;\nlibFileSet[\"lib.es2023.full.d.ts\"] = true;\nlibFileSet[\"lib.es5.d.ts\"] = true;\nlibFileSet[\"lib.es6.d.ts\"] = true;\nlibFileSet[\"lib.esnext.collection.d.ts\"] = true;\nlibFileSet[\"lib.esnext.d.ts\"] = true;\nlibFileSet[\"lib.esnext.decorators.d.ts\"] = true;\nlibFileSet[\"lib.esnext.disposable.d.ts\"] = true;\nlibFileSet[\"lib.esnext.full.d.ts\"] = true;\nlibFileSet[\"lib.esnext.intl.d.ts\"] = true;\nlibFileSet[\"lib.esnext.object.d.ts\"] = true;\nlibFileSet[\"lib.esnext.promise.d.ts\"] = true;\nlibFileSet[\"lib.scripthost.d.ts\"] = true;\nlibFileSet[\"lib.webworker.asynciterable.d.ts\"] = true;\nlibFileSet[\"lib.webworker.d.ts\"] = true;\nlibFileSet[\"lib.webworker.importscripts.d.ts\"] = true;\nlibFileSet[\"lib.webworker.iterable.d.ts\"] = true;\n\n// src/language/typescript/languageFeatures.ts\nfunction flattenDiagnosticMessageText(diag, newLine, indent = 0) {\n  if (typeof diag === \"string\") {\n    return diag;\n  } else if (diag === void 0) {\n    return \"\";\n  }\n  let result = \"\";\n  if (indent) {\n    result += newLine;\n    for (let i = 0; i < indent; i++) {\n      result += \"  \";\n    }\n  }\n  result += diag.messageText;\n  indent++;\n  if (diag.next) {\n    for (const kid of diag.next) {\n      result += flattenDiagnosticMessageText(kid, newLine, indent);\n    }\n  }\n  return result;\n}\nfunction displayPartsToString(displayParts) {\n  if (displayParts) {\n    return displayParts.map((displayPart) => displayPart.text).join(\"\");\n  }\n  return \"\";\n}\nvar Adapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  // protected _positionToOffset(model: editor.ITextModel, position: monaco.IPosition): number {\n  // \treturn model.getOffsetAt(position);\n  // }\n  // protected _offsetToPosition(model: editor.ITextModel, offset: number): monaco.IPosition {\n  // \treturn model.getPositionAt(offset);\n  // }\n  _textSpanToRange(model, span) {\n    let p1 = model.getPositionAt(span.start);\n    let p2 = model.getPositionAt(span.start + span.length);\n    let { lineNumber: startLineNumber, column: startColumn } = p1;\n    let { lineNumber: endLineNumber, column: endColumn } = p2;\n    return { startLineNumber, startColumn, endLineNumber, endColumn };\n  }\n};\nvar LibFiles = class {\n  constructor(_worker) {\n    this._worker = _worker;\n    this._libFiles = {};\n    this._hasFetchedLibFiles = false;\n    this._fetchLibFilesPromise = null;\n  }\n  isLibFile(uri) {\n    if (!uri) {\n      return false;\n    }\n    if (uri.path.indexOf(\"/lib.\") === 0) {\n      return !!libFileSet[uri.path.slice(1)];\n    }\n    return false;\n  }\n  getOrCreateModel(fileName) {\n    const uri = monaco_editor_core_exports.Uri.parse(fileName);\n    const model = monaco_editor_core_exports.editor.getModel(uri);\n    if (model) {\n      return model;\n    }\n    if (this.isLibFile(uri) && this._hasFetchedLibFiles) {\n      return monaco_editor_core_exports.editor.createModel(this._libFiles[uri.path.slice(1)], \"typescript\", uri);\n    }\n    const matchedLibFile = _monaco_contribution_js__WEBPACK_IMPORTED_MODULE_1__.typescriptDefaults.getExtraLibs()[fileName];\n    if (matchedLibFile) {\n      return monaco_editor_core_exports.editor.createModel(matchedLibFile.content, \"typescript\", uri);\n    }\n    return null;\n  }\n  _containsLibFile(uris) {\n    for (let uri of uris) {\n      if (this.isLibFile(uri)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  async fetchLibFilesIfNecessary(uris) {\n    if (!this._containsLibFile(uris)) {\n      return;\n    }\n    await this._fetchLibFiles();\n  }\n  _fetchLibFiles() {\n    if (!this._fetchLibFilesPromise) {\n      this._fetchLibFilesPromise = this._worker().then((w) => w.getLibFiles()).then((libFiles) => {\n        this._hasFetchedLibFiles = true;\n        this._libFiles = libFiles;\n      });\n    }\n    return this._fetchLibFilesPromise;\n  }\n};\nvar DiagnosticsAdapter = class extends Adapter {\n  constructor(_libFiles, _defaults, _selector, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n    this._defaults = _defaults;\n    this._selector = _selector;\n    this._disposables = [];\n    this._listener = /* @__PURE__ */ Object.create(null);\n    const onModelAdd = (model) => {\n      if (model.getLanguageId() !== _selector) {\n        return;\n      }\n      const maybeValidate = () => {\n        const { onlyVisible } = this._defaults.getDiagnosticsOptions();\n        if (onlyVisible) {\n          if (model.isAttachedToEditor()) {\n            this._doValidate(model);\n          }\n        } else {\n          this._doValidate(model);\n        }\n      };\n      let handle;\n      const changeSubscription = model.onDidChangeContent(() => {\n        clearTimeout(handle);\n        handle = window.setTimeout(maybeValidate, 500);\n      });\n      const visibleSubscription = model.onDidChangeAttached(() => {\n        const { onlyVisible } = this._defaults.getDiagnosticsOptions();\n        if (onlyVisible) {\n          if (model.isAttachedToEditor()) {\n            maybeValidate();\n          } else {\n            monaco_editor_core_exports.editor.setModelMarkers(model, this._selector, []);\n          }\n        }\n      });\n      this._listener[model.uri.toString()] = {\n        dispose() {\n          changeSubscription.dispose();\n          visibleSubscription.dispose();\n          clearTimeout(handle);\n        }\n      };\n      maybeValidate();\n    };\n    const onModelRemoved = (model) => {\n      monaco_editor_core_exports.editor.setModelMarkers(model, this._selector, []);\n      const key = model.uri.toString();\n      if (this._listener[key]) {\n        this._listener[key].dispose();\n        delete this._listener[key];\n      }\n    };\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidCreateModel((model) => onModelAdd(model))\n    );\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel(onModelRemoved));\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        onModelRemoved(event.model);\n        onModelAdd(event.model);\n      })\n    );\n    this._disposables.push({\n      dispose() {\n        for (const model of monaco_editor_core_exports.editor.getModels()) {\n          onModelRemoved(model);\n        }\n      }\n    });\n    const recomputeDiagostics = () => {\n      for (const model of monaco_editor_core_exports.editor.getModels()) {\n        onModelRemoved(model);\n        onModelAdd(model);\n      }\n    };\n    this._disposables.push(this._defaults.onDidChange(recomputeDiagostics));\n    this._disposables.push(this._defaults.onDidExtraLibsChange(recomputeDiagostics));\n    monaco_editor_core_exports.editor.getModels().forEach((model) => onModelAdd(model));\n  }\n  dispose() {\n    this._disposables.forEach((d) => d && d.dispose());\n    this._disposables = [];\n  }\n  async _doValidate(model) {\n    const worker = await this._worker(model.uri);\n    if (model.isDisposed()) {\n      return;\n    }\n    const promises = [];\n    const { noSyntaxValidation, noSemanticValidation, noSuggestionDiagnostics } = this._defaults.getDiagnosticsOptions();\n    if (!noSyntaxValidation) {\n      promises.push(worker.getSyntacticDiagnostics(model.uri.toString()));\n    }\n    if (!noSemanticValidation) {\n      promises.push(worker.getSemanticDiagnostics(model.uri.toString()));\n    }\n    if (!noSuggestionDiagnostics) {\n      promises.push(worker.getSuggestionDiagnostics(model.uri.toString()));\n    }\n    const allDiagnostics = await Promise.all(promises);\n    if (!allDiagnostics || model.isDisposed()) {\n      return;\n    }\n    const diagnostics = allDiagnostics.reduce((p, c) => c.concat(p), []).filter(\n      (d) => (this._defaults.getDiagnosticsOptions().diagnosticCodesToIgnore || []).indexOf(d.code) === -1\n    );\n    const relatedUris = diagnostics.map((d) => d.relatedInformation || []).reduce((p, c) => c.concat(p), []).map(\n      (relatedInformation) => relatedInformation.file ? monaco_editor_core_exports.Uri.parse(relatedInformation.file.fileName) : null\n    );\n    await this._libFiles.fetchLibFilesIfNecessary(relatedUris);\n    if (model.isDisposed()) {\n      return;\n    }\n    monaco_editor_core_exports.editor.setModelMarkers(\n      model,\n      this._selector,\n      diagnostics.map((d) => this._convertDiagnostics(model, d))\n    );\n  }\n  _convertDiagnostics(model, diag) {\n    const diagStart = diag.start || 0;\n    const diagLength = diag.length || 1;\n    const { lineNumber: startLineNumber, column: startColumn } = model.getPositionAt(diagStart);\n    const { lineNumber: endLineNumber, column: endColumn } = model.getPositionAt(\n      diagStart + diagLength\n    );\n    const tags = [];\n    if (diag.reportsUnnecessary) {\n      tags.push(monaco_editor_core_exports.MarkerTag.Unnecessary);\n    }\n    if (diag.reportsDeprecated) {\n      tags.push(monaco_editor_core_exports.MarkerTag.Deprecated);\n    }\n    return {\n      severity: this._tsDiagnosticCategoryToMarkerSeverity(diag.category),\n      startLineNumber,\n      startColumn,\n      endLineNumber,\n      endColumn,\n      message: flattenDiagnosticMessageText(diag.messageText, \"\\n\"),\n      code: diag.code.toString(),\n      tags,\n      relatedInformation: this._convertRelatedInformation(model, diag.relatedInformation)\n    };\n  }\n  _convertRelatedInformation(model, relatedInformation) {\n    if (!relatedInformation) {\n      return [];\n    }\n    const result = [];\n    relatedInformation.forEach((info) => {\n      let relatedResource = model;\n      if (info.file) {\n        relatedResource = this._libFiles.getOrCreateModel(info.file.fileName);\n      }\n      if (!relatedResource) {\n        return;\n      }\n      const infoStart = info.start || 0;\n      const infoLength = info.length || 1;\n      const { lineNumber: startLineNumber, column: startColumn } = relatedResource.getPositionAt(infoStart);\n      const { lineNumber: endLineNumber, column: endColumn } = relatedResource.getPositionAt(\n        infoStart + infoLength\n      );\n      result.push({\n        resource: relatedResource.uri,\n        startLineNumber,\n        startColumn,\n        endLineNumber,\n        endColumn,\n        message: flattenDiagnosticMessageText(info.messageText, \"\\n\")\n      });\n    });\n    return result;\n  }\n  _tsDiagnosticCategoryToMarkerSeverity(category) {\n    switch (category) {\n      case 1 /* Error */:\n        return monaco_editor_core_exports.MarkerSeverity.Error;\n      case 3 /* Message */:\n        return monaco_editor_core_exports.MarkerSeverity.Info;\n      case 0 /* Warning */:\n        return monaco_editor_core_exports.MarkerSeverity.Warning;\n      case 2 /* Suggestion */:\n        return monaco_editor_core_exports.MarkerSeverity.Hint;\n    }\n    return monaco_editor_core_exports.MarkerSeverity.Info;\n  }\n};\nvar SuggestAdapter = class _SuggestAdapter extends Adapter {\n  get triggerCharacters() {\n    return [\".\"];\n  }\n  async provideCompletionItems(model, position, _context, token) {\n    const wordInfo = model.getWordUntilPosition(position);\n    const wordRange = new monaco_editor_core_exports.Range(\n      position.lineNumber,\n      wordInfo.startColumn,\n      position.lineNumber,\n      wordInfo.endColumn\n    );\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const info = await worker.getCompletionsAtPosition(resource.toString(), offset);\n    if (!info || model.isDisposed()) {\n      return;\n    }\n    const suggestions = info.entries.map((entry) => {\n      let range = wordRange;\n      if (entry.replacementSpan) {\n        const p1 = model.getPositionAt(entry.replacementSpan.start);\n        const p2 = model.getPositionAt(entry.replacementSpan.start + entry.replacementSpan.length);\n        range = new monaco_editor_core_exports.Range(p1.lineNumber, p1.column, p2.lineNumber, p2.column);\n      }\n      const tags = [];\n      if (entry.kindModifiers !== void 0 && entry.kindModifiers.indexOf(\"deprecated\") !== -1) {\n        tags.push(monaco_editor_core_exports.languages.CompletionItemTag.Deprecated);\n      }\n      return {\n        uri: resource,\n        position,\n        offset,\n        range,\n        label: entry.name,\n        insertText: entry.name,\n        sortText: entry.sortText,\n        kind: _SuggestAdapter.convertKind(entry.kind),\n        tags\n      };\n    });\n    return {\n      suggestions\n    };\n  }\n  async resolveCompletionItem(item, token) {\n    const myItem = item;\n    const resource = myItem.uri;\n    const position = myItem.position;\n    const offset = myItem.offset;\n    const worker = await this._worker(resource);\n    const details = await worker.getCompletionEntryDetails(\n      resource.toString(),\n      offset,\n      myItem.label\n    );\n    if (!details) {\n      return myItem;\n    }\n    return {\n      uri: resource,\n      position,\n      label: details.name,\n      kind: _SuggestAdapter.convertKind(details.kind),\n      detail: displayPartsToString(details.displayParts),\n      documentation: {\n        value: _SuggestAdapter.createDocumentationString(details)\n      }\n    };\n  }\n  static convertKind(kind) {\n    switch (kind) {\n      case Kind.primitiveType:\n      case Kind.keyword:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Keyword;\n      case Kind.variable:\n      case Kind.localVariable:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Variable;\n      case Kind.memberVariable:\n      case Kind.memberGetAccessor:\n      case Kind.memberSetAccessor:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Field;\n      case Kind.function:\n      case Kind.memberFunction:\n      case Kind.constructSignature:\n      case Kind.callSignature:\n      case Kind.indexSignature:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Function;\n      case Kind.enum:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Enum;\n      case Kind.module:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Module;\n      case Kind.class:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Class;\n      case Kind.interface:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Interface;\n      case Kind.warning:\n        return monaco_editor_core_exports.languages.CompletionItemKind.File;\n    }\n    return monaco_editor_core_exports.languages.CompletionItemKind.Property;\n  }\n  static createDocumentationString(details) {\n    let documentationString = displayPartsToString(details.documentation);\n    if (details.tags) {\n      for (const tag of details.tags) {\n        documentationString += `\n\n${tagToString(tag)}`;\n      }\n    }\n    return documentationString;\n  }\n};\nfunction tagToString(tag) {\n  let tagLabel = `*@${tag.name}*`;\n  if (tag.name === \"param\" && tag.text) {\n    const [paramName, ...rest] = tag.text;\n    tagLabel += `\\`${paramName.text}\\``;\n    if (rest.length > 0)\n      tagLabel += ` \\u2014 ${rest.map((r) => r.text).join(\" \")}`;\n  } else if (Array.isArray(tag.text)) {\n    tagLabel += ` \\u2014 ${tag.text.map((r) => r.text).join(\" \")}`;\n  } else if (tag.text) {\n    tagLabel += ` \\u2014 ${tag.text}`;\n  }\n  return tagLabel;\n}\nvar SignatureHelpAdapter = class _SignatureHelpAdapter extends Adapter {\n  constructor() {\n    super(...arguments);\n    this.signatureHelpTriggerCharacters = [\"(\", \",\"];\n  }\n  static _toSignatureHelpTriggerReason(context) {\n    switch (context.triggerKind) {\n      case monaco_editor_core_exports.languages.SignatureHelpTriggerKind.TriggerCharacter:\n        if (context.triggerCharacter) {\n          if (context.isRetrigger) {\n            return { kind: \"retrigger\", triggerCharacter: context.triggerCharacter };\n          } else {\n            return { kind: \"characterTyped\", triggerCharacter: context.triggerCharacter };\n          }\n        } else {\n          return { kind: \"invoked\" };\n        }\n      case monaco_editor_core_exports.languages.SignatureHelpTriggerKind.ContentChange:\n        return context.isRetrigger ? { kind: \"retrigger\" } : { kind: \"invoked\" };\n      case monaco_editor_core_exports.languages.SignatureHelpTriggerKind.Invoke:\n      default:\n        return { kind: \"invoked\" };\n    }\n  }\n  async provideSignatureHelp(model, position, token, context) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const info = await worker.getSignatureHelpItems(resource.toString(), offset, {\n      triggerReason: _SignatureHelpAdapter._toSignatureHelpTriggerReason(context)\n    });\n    if (!info || model.isDisposed()) {\n      return;\n    }\n    const ret = {\n      activeSignature: info.selectedItemIndex,\n      activeParameter: info.argumentIndex,\n      signatures: []\n    };\n    info.items.forEach((item) => {\n      const signature = {\n        label: \"\",\n        parameters: []\n      };\n      signature.documentation = {\n        value: displayPartsToString(item.documentation)\n      };\n      signature.label += displayPartsToString(item.prefixDisplayParts);\n      item.parameters.forEach((p, i, a) => {\n        const label = displayPartsToString(p.displayParts);\n        const parameter = {\n          label,\n          documentation: {\n            value: displayPartsToString(p.documentation)\n          }\n        };\n        signature.label += label;\n        signature.parameters.push(parameter);\n        if (i < a.length - 1) {\n          signature.label += displayPartsToString(item.separatorDisplayParts);\n        }\n      });\n      signature.label += displayPartsToString(item.suffixDisplayParts);\n      ret.signatures.push(signature);\n    });\n    return {\n      value: ret,\n      dispose() {\n      }\n    };\n  }\n};\nvar QuickInfoAdapter = class extends Adapter {\n  async provideHover(model, position, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const info = await worker.getQuickInfoAtPosition(resource.toString(), offset);\n    if (!info || model.isDisposed()) {\n      return;\n    }\n    const documentation = displayPartsToString(info.documentation);\n    const tags = info.tags ? info.tags.map((tag) => tagToString(tag)).join(\"  \\n\\n\") : \"\";\n    const contents = displayPartsToString(info.displayParts);\n    return {\n      range: this._textSpanToRange(model, info.textSpan),\n      contents: [\n        {\n          value: \"```typescript\\n\" + contents + \"\\n```\\n\"\n        },\n        {\n          value: documentation + (tags ? \"\\n\\n\" + tags : \"\")\n        }\n      ]\n    };\n  }\n};\nvar DocumentHighlightAdapter = class extends Adapter {\n  async provideDocumentHighlights(model, position, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const entries = await worker.getDocumentHighlights(resource.toString(), offset, [\n      resource.toString()\n    ]);\n    if (!entries || model.isDisposed()) {\n      return;\n    }\n    return entries.flatMap((entry) => {\n      return entry.highlightSpans.map((highlightSpans) => {\n        return {\n          range: this._textSpanToRange(model, highlightSpans.textSpan),\n          kind: highlightSpans.kind === \"writtenReference\" ? monaco_editor_core_exports.languages.DocumentHighlightKind.Write : monaco_editor_core_exports.languages.DocumentHighlightKind.Text\n        };\n      });\n    });\n  }\n};\nvar DefinitionAdapter = class extends Adapter {\n  constructor(_libFiles, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n  }\n  async provideDefinition(model, position, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const entries = await worker.getDefinitionAtPosition(resource.toString(), offset);\n    if (!entries || model.isDisposed()) {\n      return;\n    }\n    await this._libFiles.fetchLibFilesIfNecessary(\n      entries.map((entry) => monaco_editor_core_exports.Uri.parse(entry.fileName))\n    );\n    if (model.isDisposed()) {\n      return;\n    }\n    const result = [];\n    for (let entry of entries) {\n      const refModel = this._libFiles.getOrCreateModel(entry.fileName);\n      if (refModel) {\n        result.push({\n          uri: refModel.uri,\n          range: this._textSpanToRange(refModel, entry.textSpan)\n        });\n      }\n    }\n    return result;\n  }\n};\nvar ReferenceAdapter = class extends Adapter {\n  constructor(_libFiles, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n  }\n  async provideReferences(model, position, context, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const entries = await worker.getReferencesAtPosition(resource.toString(), offset);\n    if (!entries || model.isDisposed()) {\n      return;\n    }\n    await this._libFiles.fetchLibFilesIfNecessary(\n      entries.map((entry) => monaco_editor_core_exports.Uri.parse(entry.fileName))\n    );\n    if (model.isDisposed()) {\n      return;\n    }\n    const result = [];\n    for (let entry of entries) {\n      const refModel = this._libFiles.getOrCreateModel(entry.fileName);\n      if (refModel) {\n        result.push({\n          uri: refModel.uri,\n          range: this._textSpanToRange(refModel, entry.textSpan)\n        });\n      }\n    }\n    return result;\n  }\n};\nvar OutlineAdapter = class extends Adapter {\n  async provideDocumentSymbols(model, token) {\n    const resource = model.uri;\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const root = await worker.getNavigationTree(resource.toString());\n    if (!root || model.isDisposed()) {\n      return;\n    }\n    const convert = (item, containerLabel) => {\n      const result2 = {\n        name: item.text,\n        detail: \"\",\n        kind: outlineTypeTable[item.kind] || monaco_editor_core_exports.languages.SymbolKind.Variable,\n        range: this._textSpanToRange(model, item.spans[0]),\n        selectionRange: this._textSpanToRange(model, item.spans[0]),\n        tags: [],\n        children: item.childItems?.map((child) => convert(child, item.text)),\n        containerName: containerLabel\n      };\n      return result2;\n    };\n    const result = root.childItems ? root.childItems.map((item) => convert(item)) : [];\n    return result;\n  }\n};\nvar Kind = class {\n  static {\n    this.unknown = \"\";\n  }\n  static {\n    this.keyword = \"keyword\";\n  }\n  static {\n    this.script = \"script\";\n  }\n  static {\n    this.module = \"module\";\n  }\n  static {\n    this.class = \"class\";\n  }\n  static {\n    this.interface = \"interface\";\n  }\n  static {\n    this.type = \"type\";\n  }\n  static {\n    this.enum = \"enum\";\n  }\n  static {\n    this.variable = \"var\";\n  }\n  static {\n    this.localVariable = \"local var\";\n  }\n  static {\n    this.function = \"function\";\n  }\n  static {\n    this.localFunction = \"local function\";\n  }\n  static {\n    this.memberFunction = \"method\";\n  }\n  static {\n    this.memberGetAccessor = \"getter\";\n  }\n  static {\n    this.memberSetAccessor = \"setter\";\n  }\n  static {\n    this.memberVariable = \"property\";\n  }\n  static {\n    this.constructorImplementation = \"constructor\";\n  }\n  static {\n    this.callSignature = \"call\";\n  }\n  static {\n    this.indexSignature = \"index\";\n  }\n  static {\n    this.constructSignature = \"construct\";\n  }\n  static {\n    this.parameter = \"parameter\";\n  }\n  static {\n    this.typeParameter = \"type parameter\";\n  }\n  static {\n    this.primitiveType = \"primitive type\";\n  }\n  static {\n    this.label = \"label\";\n  }\n  static {\n    this.alias = \"alias\";\n  }\n  static {\n    this.const = \"const\";\n  }\n  static {\n    this.let = \"let\";\n  }\n  static {\n    this.warning = \"warning\";\n  }\n};\nvar outlineTypeTable = /* @__PURE__ */ Object.create(null);\noutlineTypeTable[Kind.module] = monaco_editor_core_exports.languages.SymbolKind.Module;\noutlineTypeTable[Kind.class] = monaco_editor_core_exports.languages.SymbolKind.Class;\noutlineTypeTable[Kind.enum] = monaco_editor_core_exports.languages.SymbolKind.Enum;\noutlineTypeTable[Kind.interface] = monaco_editor_core_exports.languages.SymbolKind.Interface;\noutlineTypeTable[Kind.memberFunction] = monaco_editor_core_exports.languages.SymbolKind.Method;\noutlineTypeTable[Kind.memberVariable] = monaco_editor_core_exports.languages.SymbolKind.Property;\noutlineTypeTable[Kind.memberGetAccessor] = monaco_editor_core_exports.languages.SymbolKind.Property;\noutlineTypeTable[Kind.memberSetAccessor] = monaco_editor_core_exports.languages.SymbolKind.Property;\noutlineTypeTable[Kind.variable] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.const] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.localVariable] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.variable] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.function] = monaco_editor_core_exports.languages.SymbolKind.Function;\noutlineTypeTable[Kind.localFunction] = monaco_editor_core_exports.languages.SymbolKind.Function;\nvar FormatHelper = class extends Adapter {\n  static _convertOptions(options) {\n    return {\n      ConvertTabsToSpaces: options.insertSpaces,\n      TabSize: options.tabSize,\n      IndentSize: options.tabSize,\n      IndentStyle: 2 /* Smart */,\n      NewLineCharacter: \"\\n\",\n      InsertSpaceAfterCommaDelimiter: true,\n      InsertSpaceAfterSemicolonInForStatements: true,\n      InsertSpaceBeforeAndAfterBinaryOperators: true,\n      InsertSpaceAfterKeywordsInControlFlowStatements: true,\n      InsertSpaceAfterFunctionKeywordForAnonymousFunctions: true,\n      InsertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis: false,\n      InsertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets: false,\n      InsertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces: false,\n      PlaceOpenBraceOnNewLineForControlBlocks: false,\n      PlaceOpenBraceOnNewLineForFunctions: false\n    };\n  }\n  _convertTextChanges(model, change) {\n    return {\n      text: change.newText,\n      range: this._textSpanToRange(model, change.span)\n    };\n  }\n};\nvar FormatAdapter = class extends FormatHelper {\n  constructor() {\n    super(...arguments);\n    this.canFormatMultipleRanges = false;\n  }\n  async provideDocumentRangeFormattingEdits(model, range, options, token) {\n    const resource = model.uri;\n    const startOffset = model.getOffsetAt({\n      lineNumber: range.startLineNumber,\n      column: range.startColumn\n    });\n    const endOffset = model.getOffsetAt({\n      lineNumber: range.endLineNumber,\n      column: range.endColumn\n    });\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const edits = await worker.getFormattingEditsForRange(\n      resource.toString(),\n      startOffset,\n      endOffset,\n      FormatHelper._convertOptions(options)\n    );\n    if (!edits || model.isDisposed()) {\n      return;\n    }\n    return edits.map((edit) => this._convertTextChanges(model, edit));\n  }\n};\nvar FormatOnTypeAdapter = class extends FormatHelper {\n  get autoFormatTriggerCharacters() {\n    return [\";\", \"}\", \"\\n\"];\n  }\n  async provideOnTypeFormattingEdits(model, position, ch, options, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const edits = await worker.getFormattingEditsAfterKeystroke(\n      resource.toString(),\n      offset,\n      ch,\n      FormatHelper._convertOptions(options)\n    );\n    if (!edits || model.isDisposed()) {\n      return;\n    }\n    return edits.map((edit) => this._convertTextChanges(model, edit));\n  }\n};\nvar CodeActionAdaptor = class extends FormatHelper {\n  async provideCodeActions(model, range, context, token) {\n    const resource = model.uri;\n    const start = model.getOffsetAt({\n      lineNumber: range.startLineNumber,\n      column: range.startColumn\n    });\n    const end = model.getOffsetAt({\n      lineNumber: range.endLineNumber,\n      column: range.endColumn\n    });\n    const formatOptions = FormatHelper._convertOptions(model.getOptions());\n    const errorCodes = context.markers.filter((m) => m.code).map((m) => m.code).map(Number);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const codeFixes = await worker.getCodeFixesAtPosition(\n      resource.toString(),\n      start,\n      end,\n      errorCodes,\n      formatOptions\n    );\n    if (!codeFixes || model.isDisposed()) {\n      return { actions: [], dispose: () => {\n      } };\n    }\n    const actions = codeFixes.filter((fix) => {\n      return fix.changes.filter((change) => change.isNewFile).length === 0;\n    }).map((fix) => {\n      return this._tsCodeFixActionToMonacoCodeAction(model, context, fix);\n    });\n    return {\n      actions,\n      dispose: () => {\n      }\n    };\n  }\n  _tsCodeFixActionToMonacoCodeAction(model, context, codeFix) {\n    const edits = [];\n    for (const change of codeFix.changes) {\n      for (const textChange of change.textChanges) {\n        edits.push({\n          resource: model.uri,\n          versionId: void 0,\n          textEdit: {\n            range: this._textSpanToRange(model, textChange.span),\n            text: textChange.newText\n          }\n        });\n      }\n    }\n    const action = {\n      title: codeFix.description,\n      edit: { edits },\n      diagnostics: context.markers,\n      kind: \"quickfix\"\n    };\n    return action;\n  }\n};\nvar RenameAdapter = class extends Adapter {\n  constructor(_libFiles, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n  }\n  async provideRenameEdits(model, position, newName, token) {\n    const resource = model.uri;\n    const fileName = resource.toString();\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const renameInfo = await worker.getRenameInfo(fileName, offset, {\n      allowRenameOfImportPath: false\n    });\n    if (renameInfo.canRename === false) {\n      return {\n        edits: [],\n        rejectReason: renameInfo.localizedErrorMessage\n      };\n    }\n    if (renameInfo.fileToRename !== void 0) {\n      throw new Error(\"Renaming files is not supported.\");\n    }\n    const renameLocations = await worker.findRenameLocations(\n      fileName,\n      offset,\n      /*strings*/\n      false,\n      /*comments*/\n      false,\n      /*prefixAndSuffix*/\n      false\n    );\n    if (!renameLocations || model.isDisposed()) {\n      return;\n    }\n    const edits = [];\n    for (const renameLocation of renameLocations) {\n      const model2 = this._libFiles.getOrCreateModel(renameLocation.fileName);\n      if (model2) {\n        edits.push({\n          resource: model2.uri,\n          versionId: void 0,\n          textEdit: {\n            range: this._textSpanToRange(model2, renameLocation.textSpan),\n            text: newName\n          }\n        });\n      } else {\n        throw new Error(`Unknown file ${renameLocation.fileName}.`);\n      }\n    }\n    return { edits };\n  }\n};\nvar InlayHintsAdapter = class extends Adapter {\n  async provideInlayHints(model, range, token) {\n    const resource = model.uri;\n    const fileName = resource.toString();\n    const start = model.getOffsetAt({\n      lineNumber: range.startLineNumber,\n      column: range.startColumn\n    });\n    const end = model.getOffsetAt({\n      lineNumber: range.endLineNumber,\n      column: range.endColumn\n    });\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return null;\n    }\n    const tsHints = await worker.provideInlayHints(fileName, start, end);\n    const hints = tsHints.map((hint) => {\n      return {\n        ...hint,\n        label: hint.text,\n        position: model.getPositionAt(hint.position),\n        kind: this._convertHintKind(hint.kind)\n      };\n    });\n    return { hints, dispose: () => {\n    } };\n  }\n  _convertHintKind(kind) {\n    switch (kind) {\n      case \"Parameter\":\n        return monaco_editor_core_exports.languages.InlayHintKind.Parameter;\n      case \"Type\":\n        return monaco_editor_core_exports.languages.InlayHintKind.Type;\n      default:\n        return monaco_editor_core_exports.languages.InlayHintKind.Type;\n    }\n  }\n};\n\n// src/language/typescript/tsMode.ts\nvar javaScriptWorker;\nvar typeScriptWorker;\nfunction setupTypeScript(defaults) {\n  typeScriptWorker = setupMode(defaults, \"typescript\");\n}\nfunction setupJavaScript(defaults) {\n  javaScriptWorker = setupMode(defaults, \"javascript\");\n}\nfunction getJavaScriptWorker() {\n  return new Promise((resolve, reject) => {\n    if (!javaScriptWorker) {\n      return reject(\"JavaScript not registered!\");\n    }\n    resolve(javaScriptWorker);\n  });\n}\nfunction getTypeScriptWorker() {\n  return new Promise((resolve, reject) => {\n    if (!typeScriptWorker) {\n      return reject(\"TypeScript not registered!\");\n    }\n    resolve(typeScriptWorker);\n  });\n}\nfunction setupMode(defaults, modeId) {\n  const disposables = [];\n  const providers = [];\n  const client = new WorkerManager(modeId, defaults);\n  disposables.push(client);\n  const worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  const libFiles = new LibFiles(worker);\n  function registerProviders() {\n    const { modeConfiguration } = defaults;\n    disposeAll(providers);\n    if (modeConfiguration.completionItems) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCompletionItemProvider(\n          modeId,\n          new SuggestAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.signatureHelp) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerSignatureHelpProvider(\n          modeId,\n          new SignatureHelpAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.hovers) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerHoverProvider(modeId, new QuickInfoAdapter(worker))\n      );\n    }\n    if (modeConfiguration.documentHighlights) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentHighlightProvider(\n          modeId,\n          new DocumentHighlightAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.definitions) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDefinitionProvider(\n          modeId,\n          new DefinitionAdapter(libFiles, worker)\n        )\n      );\n    }\n    if (modeConfiguration.references) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerReferenceProvider(\n          modeId,\n          new ReferenceAdapter(libFiles, worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentSymbols) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentSymbolProvider(\n          modeId,\n          new OutlineAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.rename) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerRenameProvider(\n          modeId,\n          new RenameAdapter(libFiles, worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentRangeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(\n          modeId,\n          new FormatAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.onTypeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerOnTypeFormattingEditProvider(\n          modeId,\n          new FormatOnTypeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.codeActions) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCodeActionProvider(modeId, new CodeActionAdaptor(worker))\n      );\n    }\n    if (modeConfiguration.inlayHints) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerInlayHintsProvider(modeId, new InlayHintsAdapter(worker))\n      );\n    }\n    if (modeConfiguration.diagnostics) {\n      providers.push(new DiagnosticsAdapter(libFiles, defaults, modeId, worker));\n    }\n  }\n  registerProviders();\n  disposables.push(asDisposable(providers));\n  return worker;\n}\nfunction asDisposable(disposables) {\n  return { dispose: () => disposeAll(disposables) };\n}\nfunction disposeAll(disposables) {\n  while (disposables.length) {\n    disposables.pop().dispose();\n  }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/language/typescript/tsMode.js\n"));

/***/ })

}]);