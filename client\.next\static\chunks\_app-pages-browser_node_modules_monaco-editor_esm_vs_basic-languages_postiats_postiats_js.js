"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_postiats_postiats_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/postiats/postiats.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/postiats/postiats.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/postiats/postiats.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"(*\", \"*)\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"<\", \">\"]\n  ],\n  autoClosingPairs: [\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] }\n  ]\n};\nvar language = {\n  tokenPostfix: \".pats\",\n  // TODO: staload and dynload are followed by a special kind of string literals\n  // with {$IDENTIFER} variables, and it also may make sense to highlight\n  // the punctuation (. and / and \\) differently.\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  defaultToken: \"invalid\",\n  // keyword reference: https://github.com/githwxi/ATS-Postiats/blob/master/src/pats_lexing_token.dats\n  keywords: [\n    //\n    \"abstype\",\n    // ABSTYPE\n    \"abst0ype\",\n    // ABST0YPE\n    \"absprop\",\n    // ABSPROP\n    \"absview\",\n    // ABSVIEW\n    \"absvtype\",\n    // ABSVIEWTYPE\n    \"absviewtype\",\n    // ABSVIEWTYPE\n    \"absvt0ype\",\n    // ABSVIEWT0YPE\n    \"absviewt0ype\",\n    // ABSVIEWT0YPE\n    //\n    \"as\",\n    // T_AS\n    //\n    \"and\",\n    // T_AND\n    //\n    \"assume\",\n    // T_ASSUME\n    //\n    \"begin\",\n    // T_BEGIN\n    //\n    /*\n    \t\t\"case\", // CASE\n    */\n    //\n    \"classdec\",\n    // T_CLASSDEC\n    //\n    \"datasort\",\n    // T_DATASORT\n    //\n    \"datatype\",\n    // DATATYPE\n    \"dataprop\",\n    // DATAPROP\n    \"dataview\",\n    // DATAVIEW\n    \"datavtype\",\n    // DATAVIEWTYPE\n    \"dataviewtype\",\n    // DATAVIEWTYPE\n    //\n    \"do\",\n    // T_DO\n    //\n    \"end\",\n    // T_END\n    //\n    \"extern\",\n    // T_EXTERN\n    \"extype\",\n    // T_EXTYPE\n    \"extvar\",\n    // T_EXTVAR\n    //\n    \"exception\",\n    // T_EXCEPTION\n    //\n    \"fn\",\n    // FN // non-recursive\n    \"fnx\",\n    // FNX // mutual tail-rec.\n    \"fun\",\n    // FUN // general-recursive\n    //\n    \"prfn\",\n    // PRFN\n    \"prfun\",\n    // PRFUN\n    //\n    \"praxi\",\n    // PRAXI\n    \"castfn\",\n    // CASTFN\n    //\n    \"if\",\n    // T_IF\n    \"then\",\n    // T_THEN\n    \"else\",\n    // T_ELSE\n    //\n    \"ifcase\",\n    // T_IFCASE\n    //\n    \"in\",\n    // T_IN\n    //\n    \"infix\",\n    // INFIX\n    \"infixl\",\n    // INFIXL\n    \"infixr\",\n    // INFIXR\n    \"prefix\",\n    // PREFIX\n    \"postfix\",\n    // POSTFIX\n    //\n    \"implmnt\",\n    // IMPLMNT // 0\n    \"implement\",\n    // IMPLEMENT // 1\n    //\n    \"primplmnt\",\n    // PRIMPLMNT // ~1\n    \"primplement\",\n    // PRIMPLMNT // ~1\n    //\n    \"import\",\n    // T_IMPORT // for importing packages\n    //\n    /*\n    \t\t\"lam\", // LAM\n    \t\t\"llam\", // LLAM\n    \t\t\"fix\", // FIX\n    */\n    //\n    \"let\",\n    // T_LET\n    //\n    \"local\",\n    // T_LOCAL\n    //\n    \"macdef\",\n    // MACDEF\n    \"macrodef\",\n    // MACRODEF\n    //\n    \"nonfix\",\n    // T_NONFIX\n    //\n    \"symelim\",\n    // T_SYMELIM\n    \"symintr\",\n    // T_SYMINTR\n    \"overload\",\n    // T_OVERLOAD\n    //\n    \"of\",\n    // T_OF\n    \"op\",\n    // T_OP\n    //\n    \"rec\",\n    // T_REC\n    //\n    \"sif\",\n    // T_SIF\n    \"scase\",\n    // T_SCASE\n    //\n    \"sortdef\",\n    // T_SORTDEF\n    /*\n    // HX: [sta] is now deprecated\n    */\n    \"sta\",\n    // T_STACST\n    \"stacst\",\n    // T_STACST\n    \"stadef\",\n    // T_STADEF\n    \"static\",\n    // T_STATIC\n    /*\n    \t\t\"stavar\", // T_STAVAR\n    */\n    //\n    \"staload\",\n    // T_STALOAD\n    \"dynload\",\n    // T_DYNLOAD\n    //\n    \"try\",\n    // T_TRY\n    //\n    \"tkindef\",\n    // T_TKINDEF // HX-2012-05-23\n    //\n    /*\n    \t\t\"type\", // TYPE\n    */\n    \"typedef\",\n    // TYPEDEF\n    \"propdef\",\n    // PROPDEF\n    \"viewdef\",\n    // VIEWDEF\n    \"vtypedef\",\n    // VIEWTYPEDEF\n    \"viewtypedef\",\n    // VIEWTYPEDEF\n    //\n    /*\n    \t\t\"val\", // VAL\n    */\n    \"prval\",\n    // PRVAL\n    //\n    \"var\",\n    // VAR\n    \"prvar\",\n    // PRVAR\n    //\n    \"when\",\n    // T_WHEN\n    \"where\",\n    // T_WHERE\n    //\n    /*\n    \t\t\"for\", // T_FOR\n    \t\t\"while\", // T_WHILE\n    */\n    //\n    \"with\",\n    // T_WITH\n    //\n    \"withtype\",\n    // WITHTYPE\n    \"withprop\",\n    // WITHPROP\n    \"withview\",\n    // WITHVIEW\n    \"withvtype\",\n    // WITHVIEWTYPE\n    \"withviewtype\"\n    // WITHVIEWTYPE\n    //\n  ],\n  keywords_dlr: [\n    \"$delay\",\n    // DLRDELAY\n    \"$ldelay\",\n    // DLRLDELAY\n    //\n    \"$arrpsz\",\n    // T_DLRARRPSZ\n    \"$arrptrsize\",\n    // T_DLRARRPSZ\n    //\n    \"$d2ctype\",\n    // T_DLRD2CTYPE\n    //\n    \"$effmask\",\n    // DLREFFMASK\n    \"$effmask_ntm\",\n    // DLREFFMASK_NTM\n    \"$effmask_exn\",\n    // DLREFFMASK_EXN\n    \"$effmask_ref\",\n    // DLREFFMASK_REF\n    \"$effmask_wrt\",\n    // DLREFFMASK_WRT\n    \"$effmask_all\",\n    // DLREFFMASK_ALL\n    //\n    \"$extern\",\n    // T_DLREXTERN\n    \"$extkind\",\n    // T_DLREXTKIND\n    \"$extype\",\n    // T_DLREXTYPE\n    \"$extype_struct\",\n    // T_DLREXTYPE_STRUCT\n    //\n    \"$extval\",\n    // T_DLREXTVAL\n    \"$extfcall\",\n    // T_DLREXTFCALL\n    \"$extmcall\",\n    // T_DLREXTMCALL\n    //\n    \"$literal\",\n    // T_DLRLITERAL\n    //\n    \"$myfilename\",\n    // T_DLRMYFILENAME\n    \"$mylocation\",\n    // T_DLRMYLOCATION\n    \"$myfunction\",\n    // T_DLRMYFUNCTION\n    //\n    \"$lst\",\n    // DLRLST\n    \"$lst_t\",\n    // DLRLST_T\n    \"$lst_vt\",\n    // DLRLST_VT\n    \"$list\",\n    // DLRLST\n    \"$list_t\",\n    // DLRLST_T\n    \"$list_vt\",\n    // DLRLST_VT\n    //\n    \"$rec\",\n    // DLRREC\n    \"$rec_t\",\n    // DLRREC_T\n    \"$rec_vt\",\n    // DLRREC_VT\n    \"$record\",\n    // DLRREC\n    \"$record_t\",\n    // DLRREC_T\n    \"$record_vt\",\n    // DLRREC_VT\n    //\n    \"$tup\",\n    // DLRTUP\n    \"$tup_t\",\n    // DLRTUP_T\n    \"$tup_vt\",\n    // DLRTUP_VT\n    \"$tuple\",\n    // DLRTUP\n    \"$tuple_t\",\n    // DLRTUP_T\n    \"$tuple_vt\",\n    // DLRTUP_VT\n    //\n    \"$break\",\n    // T_DLRBREAK\n    \"$continue\",\n    // T_DLRCONTINUE\n    //\n    \"$raise\",\n    // T_DLRRAISE\n    //\n    \"$showtype\",\n    // T_DLRSHOWTYPE\n    //\n    \"$vcopyenv_v\",\n    // DLRVCOPYENV_V\n    \"$vcopyenv_vt\",\n    // DLRVCOPYENV_VT\n    //\n    \"$tempenver\",\n    // T_DLRTEMPENVER\n    //\n    \"$solver_assert\",\n    // T_DLRSOLASSERT\n    \"$solver_verify\"\n    // T_DLRSOLVERIFY\n  ],\n  keywords_srp: [\n    //\n    \"#if\",\n    // T_SRPIF\n    \"#ifdef\",\n    // T_SRPIFDEF\n    \"#ifndef\",\n    // T_SRPIFNDEF\n    //\n    \"#then\",\n    // T_SRPTHEN\n    //\n    \"#elif\",\n    // T_SRPELIF\n    \"#elifdef\",\n    // T_SRPELIFDEF\n    \"#elifndef\",\n    // T_SRPELIFNDEF\n    //\n    \"#else\",\n    // T_SRPELSE\n    \"#endif\",\n    // T_SRPENDIF\n    //\n    \"#error\",\n    // T_SRPERROR\n    //\n    \"#prerr\",\n    // T_SRPPRERR // outpui to stderr\n    \"#print\",\n    // T_SRPPRINT // output to stdout\n    //\n    \"#assert\",\n    // T_SRPASSERT\n    //\n    \"#undef\",\n    // T_SRPUNDEF\n    \"#define\",\n    // T_SRPDEFINE\n    //\n    \"#include\",\n    // T_SRPINCLUDE\n    \"#require\",\n    // T_SRPREQUIRE\n    //\n    \"#pragma\",\n    // T_SRPPRAGMA // HX: general pragma\n    \"#codegen2\",\n    // T_SRPCODEGEN2 // for level-2 codegen\n    \"#codegen3\"\n    // T_SRPCODEGEN3 // for level-3 codegen\n    //\n    // HX: end of special tokens\n    //\n  ],\n  irregular_keyword_list: [\n    \"val+\",\n    \"val-\",\n    \"val\",\n    \"case+\",\n    \"case-\",\n    \"case\",\n    \"addr@\",\n    \"addr\",\n    \"fold@\",\n    \"free@\",\n    \"fix@\",\n    \"fix\",\n    \"lam@\",\n    \"lam\",\n    \"llam@\",\n    \"llam\",\n    \"viewt@ype+\",\n    \"viewt@ype-\",\n    \"viewt@ype\",\n    \"viewtype+\",\n    \"viewtype-\",\n    \"viewtype\",\n    \"view+\",\n    \"view-\",\n    \"view@\",\n    \"view\",\n    \"type+\",\n    \"type-\",\n    \"type\",\n    \"vtype+\",\n    \"vtype-\",\n    \"vtype\",\n    \"vt@ype+\",\n    \"vt@ype-\",\n    \"vt@ype\",\n    \"viewt@ype+\",\n    \"viewt@ype-\",\n    \"viewt@ype\",\n    \"viewtype+\",\n    \"viewtype-\",\n    \"viewtype\",\n    \"prop+\",\n    \"prop-\",\n    \"prop\",\n    \"type+\",\n    \"type-\",\n    \"type\",\n    \"t@ype\",\n    \"t@ype+\",\n    \"t@ype-\",\n    \"abst@ype\",\n    \"abstype\",\n    \"absviewt@ype\",\n    \"absvt@ype\",\n    \"for*\",\n    \"for\",\n    \"while*\",\n    \"while\"\n  ],\n  keywords_types: [\n    \"bool\",\n    \"double\",\n    \"byte\",\n    \"int\",\n    \"short\",\n    \"char\",\n    \"void\",\n    \"unit\",\n    \"long\",\n    \"float\",\n    \"string\",\n    \"strptr\"\n  ],\n  // TODO: reference for this?\n  keywords_effects: [\n    \"0\",\n    // no effects\n    \"fun\",\n    \"clo\",\n    \"prf\",\n    \"funclo\",\n    \"cloptr\",\n    \"cloref\",\n    \"ref\",\n    \"ntm\",\n    \"1\"\n    // all effects\n  ],\n  operators: [\n    \"@\",\n    // T_AT\n    \"!\",\n    // T_BANG\n    \"|\",\n    // T_BAR\n    \"`\",\n    // T_BQUOTE\n    \":\",\n    // T_COLON\n    \"$\",\n    // T_DOLLAR\n    \".\",\n    // T_DOT\n    \"=\",\n    // T_EQ\n    \"#\",\n    // T_HASH\n    \"~\",\n    // T_TILDE\n    //\n    \"..\",\n    // T_DOTDOT\n    \"...\",\n    // T_DOTDOTDOT\n    //\n    \"=>\",\n    // T_EQGT\n    // \"=<\", // T_EQLT\n    \"=<>\",\n    // T_EQLTGT\n    \"=/=>\",\n    // T_EQSLASHEQGT\n    \"=>>\",\n    // T_EQGTGT\n    \"=/=>>\",\n    // T_EQSLASHEQGTGT\n    //\n    \"<\",\n    // T_LT // opening a tmparg\n    \">\",\n    // T_GT // closing a tmparg\n    //\n    \"><\",\n    // T_GTLT\n    //\n    \".<\",\n    // T_DOTLT\n    \">.\",\n    // T_GTDOT\n    //\n    \".<>.\",\n    // T_DOTLTGTDOT\n    //\n    \"->\",\n    // T_MINUSGT\n    //\"-<\", // T_MINUSLT\n    \"-<>\"\n    // T_MINUSLTGT\n    //\n    /*\n    \t\t\":<\", // T_COLONLT\n    */\n  ],\n  brackets: [\n    { open: \",(\", close: \")\", token: \"delimiter.parenthesis\" },\n    // meta-programming syntax\n    { open: \"`(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"%(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"'(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"'{\", close: \"}\", token: \"delimiter.parenthesis\" },\n    { open: \"@(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"@{\", close: \"}\", token: \"delimiter.brace\" },\n    { open: \"@[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"#[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  IDENTFST: /[a-zA-Z_]/,\n  IDENTRST: /[a-zA-Z0-9_'$]/,\n  symbolic: /[%&+-./:=@~`^|*!$#?<>]/,\n  digit: /[0-9]/,\n  digitseq0: /@digit*/,\n  xdigit: /[0-9A-Za-z]/,\n  xdigitseq0: /@xdigit*/,\n  INTSP: /[lLuU]/,\n  FLOATSP: /[fFlL]/,\n  fexponent: /[eE][+-]?[0-9]+/,\n  fexponent_bin: /[pP][+-]?[0-9]+/,\n  deciexp: /\\.[0-9]*@fexponent?/,\n  hexiexp: /\\.[0-9a-zA-Z]*@fexponent_bin?/,\n  irregular_keywords: /val[+-]?|case[+-]?|addr\\@?|fold\\@|free\\@|fix\\@?|lam\\@?|llam\\@?|prop[+-]?|type[+-]?|view[+-@]?|viewt@?ype[+-]?|t@?ype[+-]?|v(iew)?t@?ype[+-]?|abst@?ype|absv(iew)?t@?ype|for\\*?|while\\*?/,\n  ESCHAR: /[ntvbrfa\\\\\\?'\"\\(\\[\\{]/,\n  start: \"root\",\n  // The main tokenizer for ATS/Postiats\n  // reference: https://github.com/githwxi/ATS-Postiats/blob/master/src/pats_lexing.dats\n  tokenizer: {\n    root: [\n      // lexing_blankseq0\n      { regex: /[ \\t\\r\\n]+/, action: { token: \"\" } },\n      // NOTE: (*) is an invalid ML-like comment!\n      { regex: /\\(\\*\\)/, action: { token: \"invalid\" } },\n      {\n        regex: /\\(\\*/,\n        action: { token: \"comment\", next: \"lexing_COMMENT_block_ml\" }\n      },\n      {\n        regex: /\\(/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.parenthesis' }*/\n      },\n      {\n        regex: /\\)/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.parenthesis' }*/\n      },\n      {\n        regex: /\\[/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.bracket' }*/\n      },\n      {\n        regex: /\\]/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.bracket' }*/\n      },\n      {\n        regex: /\\{/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.brace' }*/\n      },\n      {\n        regex: /\\}/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.brace' }*/\n      },\n      // lexing_COMMA\n      {\n        regex: /,\\(/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.parenthesis' }*/\n      },\n      // meta-programming syntax\n      { regex: /,/, action: { token: \"delimiter.comma\" } },\n      { regex: /;/, action: { token: \"delimiter.semicolon\" } },\n      // lexing_AT\n      {\n        regex: /@\\(/,\n        action: \"@brackets\"\n        /* { token: 'delimiter.parenthesis' }*/\n      },\n      {\n        regex: /@\\[/,\n        action: \"@brackets\"\n        /* { token: 'delimiter.bracket' }*/\n      },\n      {\n        regex: /@\\{/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.brace' }*/\n      },\n      // lexing_COLON\n      {\n        regex: /:</,\n        action: { token: \"keyword\", next: \"@lexing_EFFECT_commaseq0\" }\n      },\n      // T_COLONLT\n      /*\n      \t\t\tlexing_DOT:\n      \n      \t\t\t. // SYMBOLIC => lexing_IDENT_sym\n      \t\t\t. FLOATDOT => lexing_FLOAT_deciexp\n      \t\t\t. DIGIT => T_DOTINT\n      \t\t\t*/\n      { regex: /\\.@symbolic+/, action: { token: \"identifier.sym\" } },\n      // FLOATDOT case\n      {\n        regex: /\\.@digit*@fexponent@FLOATSP*/,\n        action: { token: \"number.float\" }\n      },\n      { regex: /\\.@digit+/, action: { token: \"number.float\" } },\n      // T_DOTINT\n      // lexing_DOLLAR:\n      // '$' IDENTFST IDENTRST* => lexing_IDENT_dlr, _ => lexing_IDENT_sym\n      {\n        regex: /\\$@IDENTFST@IDENTRST*/,\n        action: {\n          cases: {\n            \"@keywords_dlr\": { token: \"keyword.dlr\" },\n            \"@default\": { token: \"namespace\" }\n            // most likely a module qualifier\n          }\n        }\n      },\n      // lexing_SHARP:\n      // '#' IDENTFST IDENTRST* => lexing_ident_srp, _ => lexing_IDENT_sym\n      {\n        regex: /\\#@IDENTFST@IDENTRST*/,\n        action: {\n          cases: {\n            \"@keywords_srp\": { token: \"keyword.srp\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      },\n      // lexing_PERCENT:\n      { regex: /%\\(/, action: { token: \"delimiter.parenthesis\" } },\n      {\n        regex: /^%{(#|\\^|\\$)?/,\n        action: {\n          token: \"keyword\",\n          next: \"@lexing_EXTCODE\",\n          nextEmbedded: \"text/javascript\"\n        }\n      },\n      { regex: /^%}/, action: { token: \"keyword\" } },\n      // lexing_QUOTE\n      { regex: /'\\(/, action: { token: \"delimiter.parenthesis\" } },\n      { regex: /'\\[/, action: { token: \"delimiter.bracket\" } },\n      { regex: /'\\{/, action: { token: \"delimiter.brace\" } },\n      [/(')(\\\\@ESCHAR|\\\\[xX]@xdigit+|\\\\@digit+)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'[^\\\\']'/, \"string\"],\n      // lexing_DQUOTE\n      [/\"/, \"string.quote\", \"@lexing_DQUOTE\"],\n      // lexing_BQUOTE\n      {\n        regex: /`\\(/,\n        action: \"@brackets\"\n        /* { token: 'delimiter.parenthesis' }*/\n      },\n      // TODO: otherwise, try lexing_IDENT_sym\n      { regex: /\\\\/, action: { token: \"punctuation\" } },\n      // just T_BACKSLASH\n      // lexing_IDENT_alp:\n      // NOTE: (?!regex) is syntax for \"not-followed-by\" regex\n      // to resolve ambiguity such as foreach$fwork being incorrectly lexed as [for] [each$fwork]!\n      {\n        regex: /@irregular_keywords(?!@IDENTRST)/,\n        action: { token: \"keyword\" }\n      },\n      {\n        regex: /@IDENTFST@IDENTRST*[<!\\[]?/,\n        action: {\n          cases: {\n            // TODO: dynload and staload should be specially parsed\n            // dynload whitespace+ \"special_string\"\n            // this special string is really:\n            //  '/' '\\\\' '.' => punctuation\n            // ({\\$)([a-zA-Z_][a-zA-Z_0-9]*)(}) => punctuation,keyword,punctuation\n            // [^\"] => identifier/literal\n            \"@keywords\": { token: \"keyword\" },\n            \"@keywords_types\": { token: \"type\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      },\n      // lexing_IDENT_sym:\n      {\n        regex: /\\/\\/\\/\\//,\n        action: { token: \"comment\", next: \"@lexing_COMMENT_rest\" }\n      },\n      { regex: /\\/\\/.*$/, action: { token: \"comment\" } },\n      {\n        regex: /\\/\\*/,\n        action: { token: \"comment\", next: \"@lexing_COMMENT_block_c\" }\n      },\n      // AS-20160627: specifically for effect annotations\n      {\n        regex: /-<|=</,\n        action: { token: \"keyword\", next: \"@lexing_EFFECT_commaseq0\" }\n      },\n      {\n        regex: /@symbolic+/,\n        action: {\n          cases: {\n            \"@operators\": \"keyword\",\n            \"@default\": \"operator\"\n          }\n        }\n      },\n      // lexing_ZERO:\n      // FIXME: this one is quite messy/unfinished yet\n      // TODO: lexing_INT_hex\n      // - testing_hexiexp => lexing_FLOAT_hexiexp\n      // - testing_fexponent_bin => lexing_FLOAT_hexiexp\n      // - testing_intspseq0 => T_INT_hex\n      // lexing_INT_hex:\n      {\n        regex: /0[xX]@xdigit+(@hexiexp|@fexponent_bin)@FLOATSP*/,\n        action: { token: \"number.float\" }\n      },\n      { regex: /0[xX]@xdigit+@INTSP*/, action: { token: \"number.hex\" } },\n      {\n        regex: /0[0-7]+(?![0-9])@INTSP*/,\n        action: { token: \"number.octal\" }\n      },\n      // lexing_INT_oct\n      //{regex: /0/, action: { token: 'number' } }, // INTZERO\n      // lexing_INT_dec:\n      // - testing_deciexp => lexing_FLOAT_deciexp\n      // - testing_fexponent => lexing_FLOAT_deciexp\n      // - otherwise => intspseq0 ([0-9]*[lLuU]?)\n      {\n        regex: /@digit+(@fexponent|@deciexp)@FLOATSP*/,\n        action: { token: \"number.float\" }\n      },\n      {\n        regex: /@digit@digitseq0@INTSP*/,\n        action: { token: \"number.decimal\" }\n      },\n      // DIGIT, if followed by digitseq0, is lexing_INT_dec\n      { regex: /@digit+@INTSP*/, action: { token: \"number\" } }\n    ],\n    lexing_COMMENT_block_ml: [\n      [/[^\\(\\*]+/, \"comment\"],\n      [/\\(\\*/, \"comment\", \"@push\"],\n      [/\\(\\*/, \"comment.invalid\"],\n      [/\\*\\)/, \"comment\", \"@pop\"],\n      [/\\*/, \"comment\"]\n    ],\n    lexing_COMMENT_block_c: [\n      [/[^\\/*]+/, \"comment\"],\n      // [/\\/\\*/, 'comment', '@push' ],    // nested C-style block comments not allowed\n      // [/\\/\\*/,    'comment.invalid' ],\t// NOTE: this breaks block comments in the shape of /* //*/\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    lexing_COMMENT_rest: [\n      [/$/, \"comment\", \"@pop\"],\n      // FIXME: does it match? docs say 'no'\n      [/.*/, \"comment\"]\n    ],\n    // NOTE: added by AS, specifically for highlighting\n    lexing_EFFECT_commaseq0: [\n      {\n        regex: /@IDENTFST@IDENTRST+|@digit+/,\n        action: {\n          cases: {\n            \"@keywords_effects\": { token: \"type.effect\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      },\n      { regex: /,/, action: { token: \"punctuation\" } },\n      { regex: />/, action: { token: \"@rematch\", next: \"@pop\" } }\n    ],\n    lexing_EXTCODE: [\n      {\n        regex: /^%}/,\n        action: {\n          token: \"@rematch\",\n          next: \"@pop\",\n          nextEmbedded: \"@pop\"\n        }\n      },\n      { regex: /[^%]+/, action: \"\" }\n    ],\n    lexing_DQUOTE: [\n      { regex: /\"/, action: { token: \"string.quote\", next: \"@pop\" } },\n      // AS-20160628: additional hi-lighting for variables in staload/dynload strings\n      {\n        regex: /(\\{\\$)(@IDENTFST@IDENTRST*)(\\})/,\n        action: [{ token: \"string.escape\" }, { token: \"identifier\" }, { token: \"string.escape\" }]\n      },\n      { regex: /\\\\$/, action: { token: \"string.escape\" } },\n      {\n        regex: /\\\\(@ESCHAR|[xX]@xdigit+|@digit+)/,\n        action: { token: \"string.escape\" }\n      },\n      { regex: /[^\\\\\"]+/, action: { token: \"string\" } }\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/postiats/postiats.js\n"));

/***/ })

}]);