{"version": 3, "sources": ["../../../locales/fr-FR.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Coller\",\n    \"pasteAsPlaintext\": \"Coller comme texte brut\",\n    \"pasteCharts\": \"Coller les graphiques\",\n    \"selectAll\": \"Tout sélectionner\",\n    \"multiSelect\": \"Ajouter l'élément à la sélection\",\n    \"moveCanvas\": \"<PERSON><PERSON><PERSON><PERSON> le canevas\",\n    \"cut\": \"Couper\",\n    \"copy\": \"Copier\",\n    \"copyAsPng\": \"Copier dans le presse-papier en PNG\",\n    \"copyAsSvg\": \"Copier dans le presse-papier en SVG\",\n    \"copyText\": \"Copier dans le presse-papier en tant que texte\",\n    \"copySource\": \"Copier la source dans le presse-papiers\",\n    \"convertToCode\": \"Convertir en code\",\n    \"bringForward\": \"Envoyer vers l'avant\",\n    \"sendToBack\": \"Déplacer à l'arrière-plan\",\n    \"bringToFront\": \"Mettre au premier plan\",\n    \"sendBackward\": \"Reculer d'un plan\",\n    \"delete\": \"Supprimer\",\n    \"copyStyles\": \"Copier les styles\",\n    \"pasteStyles\": \"Coller les styles\",\n    \"stroke\": \"Trait\",\n    \"background\": \"Arrière-plan\",\n    \"fill\": \"Remplissage\",\n    \"strokeWidth\": \"Largeur du contour\",\n    \"strokeStyle\": \"Style du trait\",\n    \"strokeStyle_solid\": \"Continu\",\n    \"strokeStyle_dashed\": \"Tirets\",\n    \"strokeStyle_dotted\": \"Pointillés\",\n    \"sloppiness\": \"Style de tracé\",\n    \"opacity\": \"Transparence\",\n    \"textAlign\": \"Alignement du texte\",\n    \"edges\": \"Angles\",\n    \"sharp\": \"Pointus\",\n    \"round\": \"Arrondis\",\n    \"arrowheads\": \"Extrémités\",\n    \"arrowhead_none\": \"Sans\",\n    \"arrowhead_arrow\": \"Flèche\",\n    \"arrowhead_bar\": \"Barre\",\n    \"arrowhead_circle\": \"Cercle\",\n    \"arrowhead_circle_outline\": \"Contour du cercle\",\n    \"arrowhead_triangle\": \"Triangle\",\n    \"arrowhead_triangle_outline\": \"Triangle (contour)\",\n    \"arrowhead_diamond\": \"Losange\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Taille de la police\",\n    \"fontFamily\": \"Police\",\n    \"addWatermark\": \"Ajouter \\\"Réalisé avec Excalidraw\\\"\",\n    \"handDrawn\": \"À main levée\",\n    \"normal\": \"Normale\",\n    \"code\": \"Code\",\n    \"small\": \"Petite\",\n    \"medium\": \"Moyenne\",\n    \"large\": \"Grande\",\n    \"veryLarge\": \"Très grande\",\n    \"solid\": \"Solide\",\n    \"hachure\": \"Hachures\",\n    \"zigzag\": \"Zigzag\",\n    \"crossHatch\": \"Hachures croisées\",\n    \"thin\": \"Fine\",\n    \"bold\": \"Épaisse\",\n    \"left\": \"À gauche\",\n    \"center\": \"Au centre\",\n    \"right\": \"À droite\",\n    \"extraBold\": \"Très épaisse\",\n    \"architect\": \"Architecte\",\n    \"artist\": \"Artiste\",\n    \"cartoonist\": \"Caricaturiste\",\n    \"fileTitle\": \"Nom du fichier\",\n    \"colorPicker\": \"Sélecteur de couleur\",\n    \"canvasColors\": \"Utilisé sur la zone de dessin\",\n    \"canvasBackground\": \"Arrière-plan du canevas\",\n    \"drawingCanvas\": \"Zone de dessin\",\n    \"layers\": \"Disposition\",\n    \"actions\": \"Actions\",\n    \"language\": \"Langue\",\n    \"liveCollaboration\": \"Collaboration en direct...\",\n    \"duplicateSelection\": \"Dupliquer\",\n    \"untitled\": \"Sans-titre\",\n    \"name\": \"Nom\",\n    \"yourName\": \"Votre nom\",\n    \"madeWithExcalidraw\": \"Fait avec Excalidraw\",\n    \"group\": \"Grouper la sélection\",\n    \"ungroup\": \"Dégrouper la sélection\",\n    \"collaborators\": \"Collaborateurs\",\n    \"showGrid\": \"Afficher la grille\",\n    \"addToLibrary\": \"Ajouter à la bibliothèque\",\n    \"removeFromLibrary\": \"Supprimer de la bibliothèque\",\n    \"libraryLoadingMessage\": \"Chargement de la bibliothèque…\",\n    \"libraries\": \"Parcourir les bibliothèques\",\n    \"loadingScene\": \"Chargement de la scène…\",\n    \"align\": \"Alignement\",\n    \"alignTop\": \"Aligner en haut\",\n    \"alignBottom\": \"Aligner en bas\",\n    \"alignLeft\": \"Aligner à gauche\",\n    \"alignRight\": \"Aligner à droite\",\n    \"centerVertically\": \"Centrer verticalement\",\n    \"centerHorizontally\": \"Centrer horizontalement\",\n    \"distributeHorizontally\": \"Répartir horizontalement\",\n    \"distributeVertically\": \"Répartir verticalement\",\n    \"flipHorizontal\": \"Retourner horizontalement\",\n    \"flipVertical\": \"Retourner verticalement\",\n    \"viewMode\": \"Mode présentation\",\n    \"share\": \"Partager\",\n    \"showStroke\": \"Afficher le sélecteur de couleur de trait\",\n    \"showBackground\": \"Afficher le sélecteur de couleur de fond\",\n    \"toggleTheme\": \"Changer le thème\",\n    \"personalLib\": \"Bibliothèque personnelle\",\n    \"excalidrawLib\": \"Bibliothèque Excalidraw\",\n    \"decreaseFontSize\": \"Diminuer la taille de police\",\n    \"increaseFontSize\": \"Augmenter la taille de la police\",\n    \"unbindText\": \"Dissocier le texte\",\n    \"bindText\": \"Associer le texte au conteneur\",\n    \"createContainerFromText\": \"Encadrer le texte dans un conteneur\",\n    \"link\": {\n      \"edit\": \"Modifier le lien\",\n      \"editEmbed\": \"Éditer le lien & intégrer\",\n      \"create\": \"Ajouter un lien\",\n      \"createEmbed\": \"Créer un lien & intégrer\",\n      \"label\": \"Lien\",\n      \"labelEmbed\": \"Lier & intégrer\",\n      \"empty\": \"Aucun lien défini\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Modifier la ligne\",\n      \"exit\": \"Quitter l'éditeur de ligne\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Verrouiller\",\n      \"unlock\": \"Déverrouiller\",\n      \"lockAll\": \"Tout verrouiller\",\n      \"unlockAll\": \"Tout déverrouiller\"\n    },\n    \"statusPublished\": \"Publié\",\n    \"sidebarLock\": \"Maintenir la barre latérale ouverte\",\n    \"selectAllElementsInFrame\": \"Sélectionner tous les éléments du cadre\",\n    \"removeAllElementsFromFrame\": \"Supprimer tous les éléments du cadre\",\n    \"eyeDropper\": \"Choisir la couleur depuis la toile\",\n    \"textToDiagram\": \"Texte vers Diagramme\",\n    \"prompt\": \"Consignes\"\n  },\n  \"library\": {\n    \"noItems\": \"Aucun élément n'a encore été ajouté ...\",\n    \"hint_emptyLibrary\": \"Sélectionnez un élément sur le canevas pour l'ajouter ici ou installez une bibliothèque depuis le dépôt public, ci-dessous.\",\n    \"hint_emptyPrivateLibrary\": \"Sélectionnez un élément sur le canevas pour l'ajouter ici.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Réinitialiser le canevas\",\n    \"exportJSON\": \"Exporter comme fichier\",\n    \"exportImage\": \"Exporter l'image...\",\n    \"export\": \"Enregistrer sous...\",\n    \"copyToClipboard\": \"Copier dans le presse-papier\",\n    \"save\": \"Enregistrer dans le fichier actuel\",\n    \"saveAs\": \"Enregistrer sous\",\n    \"load\": \"Ouvrir\",\n    \"getShareableLink\": \"Obtenir un lien de partage\",\n    \"close\": \"Fermer\",\n    \"selectLanguage\": \"Choisir une langue\",\n    \"scrollBackToContent\": \"Revenir au contenu\",\n    \"zoomIn\": \"Zoomer\",\n    \"zoomOut\": \"Dézoomer\",\n    \"resetZoom\": \"Réinitialiser le zoom\",\n    \"menu\": \"Menu\",\n    \"done\": \"Terminé\",\n    \"edit\": \"Modifier\",\n    \"undo\": \"Annuler\",\n    \"redo\": \"Rétablir\",\n    \"resetLibrary\": \"Réinitialiser la bibliothèque\",\n    \"createNewRoom\": \"Créer une nouvelle salle\",\n    \"fullScreen\": \"Plein écran\",\n    \"darkMode\": \"Mode sombre\",\n    \"lightMode\": \"Mode clair\",\n    \"zenMode\": \"Mode zen\",\n    \"objectsSnapMode\": \"Aimanter aux objets\",\n    \"exitZenMode\": \"Quitter le mode zen\",\n    \"cancel\": \"Annuler\",\n    \"clear\": \"Effacer\",\n    \"remove\": \"Supprimer\",\n    \"embed\": \"Activer/Désactiver l'intégration\",\n    \"publishLibrary\": \"Publier\",\n    \"submit\": \"Envoyer\",\n    \"confirm\": \"Confirmer\",\n    \"embeddableInteractionButton\": \"Cliquez pour interagir\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"L'intégralité du canevas va être effacée. Êtes-vous sûr ?\",\n    \"couldNotCreateShareableLink\": \"Impossible de créer un lien de partage.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Impossible de créer un lien de partage : la scène est trop volumineuse\",\n    \"couldNotLoadInvalidFile\": \"Impossible de charger un fichier invalide\",\n    \"importBackendFailed\": \"L'importation depuis le serveur a échoué.\",\n    \"cannotExportEmptyCanvas\": \"Impossible d'exporter un canevas vide.\",\n    \"couldNotCopyToClipboard\": \"Impossible de copier dans le presse-papiers.\",\n    \"decryptFailed\": \"Les données n'ont pas pu être déchiffrées.\",\n    \"uploadedSecurly\": \"Le téléchargement a été sécurisé avec un chiffrement de bout en bout, ce qui signifie que ni Excalidraw ni personne d'autre ne peut en lire le contenu.\",\n    \"loadSceneOverridePrompt\": \"Le chargement d'un dessin externe remplacera votre contenu actuel. Souhaitez-vous continuer ?\",\n    \"collabStopOverridePrompt\": \"Arrêter la session écrasera votre précédent dessin stocké localement. Êtes-vous sûr·e ?\\n\\n(Si vous voulez garder votre dessin local, fermez simplement l'onglet du navigateur à la place.)\",\n    \"errorAddingToLibrary\": \"Impossible d'ajouter l'élément à la bibliothèque\",\n    \"errorRemovingFromLibrary\": \"Impossible de retirer l'élément de la bibliothèque\",\n    \"confirmAddLibrary\": \"Cela va ajouter {{numShapes}} forme(s) à votre bibliothèque. Êtes-vous sûr·e ?\",\n    \"imageDoesNotContainScene\": \"Cette image ne semble pas contenir de données de scène. Avez-vous activé l'intégration de scène lors de l'exportation ?\",\n    \"cannotRestoreFromImage\": \"Impossible de restaurer la scène depuis ce fichier image\",\n    \"invalidSceneUrl\": \"Impossible d'importer la scène depuis l'URL fournie. Elle est soit incorrecte, soit ne contient pas de données JSON Excalidraw valides.\",\n    \"resetLibrary\": \"Cela va effacer votre bibliothèque. Êtes-vous sûr·e ?\",\n    \"removeItemsFromsLibrary\": \"Supprimer {{count}} élément(s) de la bibliothèque ?\",\n    \"invalidEncryptionKey\": \"La clé de chiffrement doit comporter 22 caractères. La collaboration en direct est désactivée.\",\n    \"collabOfflineWarning\": \"Aucune connexion internet disponible.\\nVos modifications ne seront pas enregistrées !\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Type de fichier non supporté.\",\n    \"imageInsertError\": \"Impossible d'insérer l'image. Réessayez plus tard...\",\n    \"fileTooBig\": \"Le fichier est trop volumineux. La taille maximale autorisée est de {{maxSize}}.\",\n    \"svgImageInsertError\": \"Impossible d'insérer l'image SVG. Le balisage SVG semble invalide.\",\n    \"failedToFetchImage\": \"Échec de récupération de l'image.\",\n    \"invalidSVGString\": \"SVG invalide.\",\n    \"cannotResolveCollabServer\": \"Impossible de se connecter au serveur collaboratif. Veuillez recharger la page et réessayer.\",\n    \"importLibraryError\": \"Impossible de charger la bibliothèque\",\n    \"collabSaveFailed\": \"Impossible d'enregistrer dans la base de données en arrière-plan. Si des problèmes persistent, vous devriez enregistrer votre fichier localement pour vous assurer de ne pas perdre votre travail.\",\n    \"collabSaveFailed_sizeExceeded\": \"Impossible d'enregistrer dans la base de données en arrière-plan, le tableau semble trop grand. Vous devriez enregistrer le fichier localement pour vous assurer de ne pas perdre votre travail.\",\n    \"imageToolNotSupported\": \"Les images sont désactivées.\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"On dirait que vous utilisez le navigateur Brave avec l'option <bold>Bloquer agressivement le fichage</bold> activée.\",\n      \"line2\": \"Cela pourrait entraîner des problèmes avec les <bold>Éléments Textuels</bold> dans vos dessins.\",\n      \"line3\": \"Nous recommandons fortement de désactiver cette option. Vous pouvez suivre <link>ces instructions</link> pour savoir comment faire.\",\n      \"line4\": \"Si désactiver cette option de résout pas le problème d'affichage des éléments textuels, veuillez ouvrir un <issueLink>ticket</issueLink> sur notre GitHub, ou écrivez-nous sur notre <discordLink>Discord</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"Les éléments intégrés ne peuvent pas être ajoutés à la librairie.\",\n      \"iframe\": \"\",\n      \"image\": \"Le support pour l'ajout d'images à la librairie arrive bientôt !\"\n    },\n    \"asyncPasteFailedOnRead\": \"Impossible de coller (impossible de lire le presse-papiers système).\",\n    \"asyncPasteFailedOnParse\": \"Impossible de coller.\",\n    \"copyToSystemClipboardFailed\": \"Échec de la copie dans le presse-papiers.\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Sélection\",\n    \"image\": \"Insérer une image\",\n    \"rectangle\": \"Rectangle\",\n    \"diamond\": \"Losange\",\n    \"ellipse\": \"Ellipse\",\n    \"arrow\": \"Flèche\",\n    \"line\": \"Ligne\",\n    \"freedraw\": \"Dessiner\",\n    \"text\": \"Texte\",\n    \"library\": \"Bibliothèque\",\n    \"lock\": \"Garder l'outil sélectionné actif après le dessin\",\n    \"penMode\": \"Mode stylo - évite le toucher\",\n    \"link\": \"Ajouter/mettre à jour le lien pour une forme sélectionnée\",\n    \"eraser\": \"Gomme\",\n    \"frame\": \"Outil de cadre\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"Intégration Web\",\n    \"laser\": \"Pointeur laser\",\n    \"hand\": \"Mains (outil de déplacement de la vue)\",\n    \"extraTools\": \"Plus d'outils\",\n    \"mermaidToExcalidraw\": \"De Mermaid à Excalidraw\",\n    \"magicSettings\": \"Paramètres IA\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Actions du canevas\",\n    \"selectedShapeActions\": \"Actions pour la forme sélectionnée\",\n    \"shapes\": \"Formes\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Pour déplacer la zone de dessin, maintenez la molette de la souris enfoncée ou la barre d'espace tout en faisant glisser, ou utiliser l'outil main.\",\n    \"linearElement\": \"Cliquez pour démarrer plusieurs points, faites glisser pour une seule ligne\",\n    \"freeDraw\": \"Cliquez et faites glissez, relâchez quand vous avez terminé\",\n    \"text\": \"Astuce : vous pouvez aussi ajouter du texte en double-cliquant n'importe où avec l'outil de sélection\",\n    \"embeddable\": \"Cliquez et glissez pour créer une intégration de site web\",\n    \"text_selected\": \"Double-cliquez ou appuyez sur ENTRÉE pour modifier le texte\",\n    \"text_editing\": \"Appuyez sur ÉCHAP ou Ctrl/Cmd+ENTRÉE pour terminer l'édition\",\n    \"linearElementMulti\": \"Cliquez sur le dernier point ou appuyez sur Échap ou Entrée pour terminer\",\n    \"lockAngle\": \"Vous pouvez restreindre l'angle en maintenant MAJ\",\n    \"resize\": \"Vous pouvez conserver les proportions en maintenant la touche MAJ pendant le redimensionnement, maintenez la touche ALT pour redimensionner par rapport au centre\",\n    \"resizeImage\": \"Vous pouvez redimensionner librement en maintenant SHIFT,\\nmaintenez ALT pour redimensionner depuis le centre\",\n    \"rotate\": \"Vous pouvez restreindre les angles en maintenant MAJ pendant la rotation\",\n    \"lineEditor_info\": \"Maintenez CtrlOrCmd et Double-cliquez ou appuyez sur CtrlOrCmd + Entrée pour modifier les points\",\n    \"lineEditor_pointSelected\": \"Appuyer sur Suppr. pour supprimer des points, Ctrl ou Cmd+D pour dupliquer, ou faire glisser pour déplacer\",\n    \"lineEditor_nothingSelected\": \"Sélectionner un point pour éditer (maintenir la touche MAJ pour en sélectionner plusieurs),\\nou maintenir la touche Alt enfoncée et cliquer pour ajouter de nouveaux points\",\n    \"placeImage\": \"Cliquez pour placer l'image, ou cliquez et faites glisser pour définir sa taille manuellement\",\n    \"publishLibrary\": \"Publier votre propre bibliothèque\",\n    \"bindTextToElement\": \"Appuyer sur Entrée pour ajouter du texte\",\n    \"deepBoxSelect\": \"Maintenir Ctrl ou Cmd pour sélectionner dans les groupes et empêcher le déplacement\",\n    \"eraserRevert\": \"Maintenez Alt enfoncé pour annuler les éléments marqués pour suppression\",\n    \"firefox_clipboard_write\": \"Cette fonctionnalité devrait pouvoir être activée en définissant l'option \\\"dom.events.asyncClipboard.clipboard.clipboardItem\\\" à \\\"true\\\". Pour modifier les paramètres du navigateur dans Firefox, visitez la page \\\"about:config\\\".\",\n    \"disableSnapping\": \"Maintenez CtrlOuCmd pour désactiver l'aimantation\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Impossible d’afficher l’aperçu\",\n    \"canvasTooBig\": \"Le canevas est peut-être trop grand.\",\n    \"canvasTooBigTip\": \"Astuce : essayez de rapprocher un peu les éléments les plus éloignés.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Une erreur est survenue. Essayez <button>de recharger la page.</button>\",\n    \"clearCanvasMessage\": \"Si le rechargement ne résout pas l'erreur, essayez <button>effacement du canevas.</button>\",\n    \"clearCanvasCaveat\": \" Cela entraînera une perte du travail \",\n    \"trackedToSentry\": \"L'erreur avec l'identifiant {{eventId}} a été enregistrée dans notre système.\",\n    \"openIssueMessage\": \"Nous avons fait très attention à ne pas inclure les informations de votre scène dans l'erreur. Si votre scène n'est pas privée, veuillez envisager de poursuivre sur notre <button>outil de suivi des bugs.</button> Veuillez inclure les informations ci-dessous en les copiant-collant dans le ticket GitHub.\",\n    \"sceneContent\": \"Contenu de la scène :\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Vous pouvez inviter des personnes à collaborer avec vous sur votre scène actuelle.\",\n    \"desc_privacy\": \"Pas d'inquiétude, la session utilise le chiffrement de bout en bout, donc tout ce que vous dessinez restera privé. Même notre serveur ne pourra voir ce que vous faites.\",\n    \"button_startSession\": \"Démarrer la session\",\n    \"button_stopSession\": \"Arrêter la session\",\n    \"desc_inProgressIntro\": \"La session de collaboration en direct est maintenant en cours.\",\n    \"desc_shareLink\": \"Partagez ce lien avec les personnes avec lesquelles vous souhaitez collaborer :\",\n    \"desc_exitSession\": \"Arrêter la session vous déconnectera de la salle, mais vous pourrez continuer à travailler avec la scène, localement. Notez que cela n'affectera pas les autres personnes, et ils pourront toujours collaborer sur leur version.\",\n    \"shareTitle\": \"Rejoindre une session de collaboration en direct sur Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Erreur\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Enregistrer sur le disque\",\n    \"disk_details\": \"Exporter les données de la scène comme un fichier que vous pourrez importer ultérieurement.\",\n    \"disk_button\": \"Enregistrer comme fichier\",\n    \"link_title\": \"Lien partageable\",\n    \"link_details\": \"Exporter comme un lien en lecture seule.\",\n    \"link_button\": \"Exporter comme lien\",\n    \"excalidrawplus_description\": \"Enregistrer la scène dans votre espace de travail Excalidraw+.\",\n    \"excalidrawplus_button\": \"Exporter\",\n    \"excalidrawplus_exportError\": \"Impossible d'exporter vers Excalidraw+ pour le moment...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Lire notre blog\",\n    \"click\": \"clic\",\n    \"deepSelect\": \"Sélection dans les groupes\",\n    \"deepBoxSelect\": \"Sélectionner dans les groupes, et empêcher le déplacement\",\n    \"curvedArrow\": \"Flèche courbée\",\n    \"curvedLine\": \"Ligne courbée\",\n    \"documentation\": \"Documentation\",\n    \"doubleClick\": \"double-clic\",\n    \"drag\": \"glisser\",\n    \"editor\": \"Éditeur\",\n    \"editLineArrowPoints\": \"Modifier les points de ligne/flèche\",\n    \"editText\": \"Modifier le texte / ajouter un libellé\",\n    \"github\": \"Problème trouvé ? Soumettre\",\n    \"howto\": \"Suivez nos guides\",\n    \"or\": \"ou\",\n    \"preventBinding\": \"Empêcher la liaison de flèche\",\n    \"tools\": \"Outils\",\n    \"shortcuts\": \"Raccourcis clavier\",\n    \"textFinish\": \"Terminer l'édition (éditeur de texte)\",\n    \"textNewLine\": \"Ajouter une nouvelle ligne (éditeur de texte)\",\n    \"title\": \"Aide\",\n    \"view\": \"Affichage\",\n    \"zoomToFit\": \"Zoomer pour voir tous les éléments\",\n    \"zoomToSelection\": \"Zoomer sur la sélection\",\n    \"toggleElementLock\": \"Verrouiller/déverrouiller la sélection\",\n    \"movePageUpDown\": \"Déplacer la page vers le haut/bas\",\n    \"movePageLeftRight\": \"Déplacer la page vers la gauche/droite\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Effacer la zone de dessin\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Publier la bibliothèque\",\n    \"itemName\": \"Nom de l’élément\",\n    \"authorName\": \"Nom de l'auteur\",\n    \"githubUsername\": \"Nom d'utilisateur GitHub\",\n    \"twitterUsername\": \"Nom d'utilisateur Twitter\",\n    \"libraryName\": \"Nom de la bibliothèque\",\n    \"libraryDesc\": \"Description de la bibliothèque\",\n    \"website\": \"Site web\",\n    \"placeholder\": {\n      \"authorName\": \"Votre nom ou nom d'utilisateur\",\n      \"libraryName\": \"Nom de votre bibliothèque\",\n      \"libraryDesc\": \"Description de votre bibliothèque pour aider les gens à comprendre son usage\",\n      \"githubHandle\": \"Nom d'utilisateur GitHub (optionnel), pour que tu puisses modifier la bibliothèque une fois soumise pour vérification\",\n      \"twitterHandle\": \"Nom d'utilisateur Twitter (optionnel), pour savoir qui créditer lors de la promotion sur Twitter\",\n      \"website\": \"Lien vers votre site web personnel ou autre (optionnel)\"\n    },\n    \"errors\": {\n      \"required\": \"Requis\",\n      \"website\": \"Entrer une URL valide\"\n    },\n    \"noteDescription\": \"Soumets ta bibliothèque pour l'inclure au <link>dépôt de bibliothèque publique</link>pour permettre son utilisation par autrui dans leurs dessins.\",\n    \"noteGuidelines\": \"La bibliothèque doit d'abord être approuvée manuellement. Veuillez lire les <link>lignes directrices</link> avant de la soumettre. Vous aurez besoin d'un compte GitHub pour communiquer et apporter des modifications si demandé, mais ce n'est pas obligatoire.\",\n    \"noteLicense\": \"En soumettant, vous acceptez que la bibliothèque soit publiée sous la <link>Licence MIT, </link>ce qui en gros signifie que tout le monde peut l'utiliser sans restrictions.\",\n    \"noteItems\": \"Chaque élément de la bibliothèque doit avoir son propre nom afin qu'il soit filtrable. Les éléments de bibliothèque suivants seront inclus :\",\n    \"atleastOneLibItem\": \"Veuillez sélectionner au moins un élément de bibliothèque pour commencer\",\n    \"republishWarning\": \"Remarque : certains des éléments sélectionnés sont marqués comme étant déjà publiés/soumis. Vous devez uniquement resoumettre des éléments lors de la mise à jour d'une bibliothèque ou d'une soumission existante.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Bibliothèque soumise\",\n    \"content\": \"Merci {{authorName}}. Votre bibliothèque a été soumise pour examen. Vous pouvez suivre le statut<link>ici</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Réinitialiser la bibliothèque\",\n    \"removeItemsFromLib\": \"Enlever les éléments sélectionnés de la bibliothèque\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Exporter l'image\",\n    \"label\": {\n      \"withBackground\": \"Fond\",\n      \"onlySelected\": \"Uniquement la sélection\",\n      \"darkMode\": \"Mode sombre\",\n      \"embedScene\": \"Intégrer la scène\",\n      \"scale\": \"Échelle\",\n      \"padding\": \"Marge interne\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"Les données de la scène seront sauvegardées dans le fichier PNG/SVG exporté afin que la scène puisse être restaurée depuis celui-ci.\\nCela augmentera la taille du fichier exporté.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Exporter en PNG\",\n      \"exportToSvg\": \"Exporter en SVG\",\n      \"copyPngToClipboard\": \"Copier le PNG dans le presse-papier\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Copier dans le presse-papier\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Vos dessins sont chiffrés de bout en bout, les serveurs d'Excalidraw ne les verront jamais.\",\n    \"link\": \"Article de blog sur le chiffrement de bout en bout dans Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Angle\",\n    \"element\": \"Élément\",\n    \"elements\": \"Éléments\",\n    \"height\": \"Hauteur\",\n    \"scene\": \"Scène\",\n    \"selected\": \"Sélection\",\n    \"storage\": \"Stockage\",\n    \"title\": \"Stats pour les nerds\",\n    \"total\": \"Total\",\n    \"version\": \"Version\",\n    \"versionCopy\": \"Cliquer pour copier\",\n    \"versionNotAvailable\": \"Version non disponible\",\n    \"width\": \"Largeur\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Ajouté à la bibliothèque\",\n    \"copyStyles\": \"Styles copiés.\",\n    \"copyToClipboard\": \"Copié dans le presse-papier.\",\n    \"copyToClipboardAsPng\": \"{{exportSelection}} copié dans le presse-papier en PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Fichier enregistré.\",\n    \"fileSavedToFilename\": \"Enregistré sous {filename}\",\n    \"canvas\": \"canevas\",\n    \"selection\": \"sélection\",\n    \"pasteAsSingleElement\": \"Utiliser {{shortcut}} pour coller comme un seul élément,\\nou coller dans un éditeur de texte existant\",\n    \"unableToEmbed\": \"Intégrer cet URL n'est actuellement pas autorisé. Ouvrez un ticket sur GitHub pour demander son ajout à la liste blanche\",\n    \"unrecognizedLinkFormat\": \"Le lien que vous avez intégré ne correspond pas au format attendu. Veuillez essayer de coller la chaîne d'intégration fournie par le site source\"\n  },\n  \"colors\": {\n    \"transparent\": \"Transparent\",\n    \"black\": \"Noir\",\n    \"white\": \"Blanc\",\n    \"red\": \"Rouge\",\n    \"pink\": \"Rose\",\n    \"grape\": \"Mauve\",\n    \"violet\": \"Violet\",\n    \"gray\": \"Gris\",\n    \"blue\": \"Bleu\",\n    \"cyan\": \"Cyan\",\n    \"teal\": \"Turquoise\",\n    \"green\": \"Vert\",\n    \"yellow\": \"Jaune\",\n    \"orange\": \"Orange\",\n    \"bronze\": \"Bronze\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Toutes vos données sont sauvegardées en local dans votre navigateur.\",\n      \"center_heading_plus\": \"Vouliez-vous plutôt aller à Excalidraw+ à la place ?\",\n      \"menuHint\": \"Exportation, préférences, langues, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Exportation, préférences et plus...\",\n      \"center_heading\": \"Diagrammes. Rendus. Simples.\",\n      \"toolbarHint\": \"Choisissez un outil et commencez à dessiner !\",\n      \"helpHint\": \"Raccourcis et aide\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Couleurs personnalisées les plus fréquemment utilisées\",\n    \"colors\": \"Couleurs\",\n    \"shades\": \"Nuances\",\n    \"hexCode\": \"Code hex\",\n    \"noShades\": \"Aucune nuance disponible pour cette couleur\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Exporter en image\",\n        \"button\": \"Exporter en image\",\n        \"description\": \"Exporter les données de la scène comme une image que vous pourrez importer ultérieurement.\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Sauvegarder sur le disque\",\n        \"button\": \"Sauvegarder sur le disque\",\n        \"description\": \"Exporter les données de la scène comme un fichier que vous pourrez importer ultérieurement.\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Exporter vers Excalidraw+\",\n        \"description\": \"Enregistrer la scène dans votre espace de travail Excalidraw+.\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Charger depuis un fichier\",\n        \"button\": \"Charger depuis un fichier\",\n        \"description\": \"Charger depuis un fichier va <bold>remplacer votre contenu existant</bold>.<br></br>Vous pouvez d'abord sauvegarder votre dessin en utilisant l'une des options ci-dessous.\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Charger depuis un lien\",\n        \"button\": \"Remplacer mon contenu\",\n        \"description\": \"Charger un dessin externe va <bold>remplacer votre contenu existant</bold>.<br></br>Vous pouvez d'abord sauvegarder votre dessin en utilisant l'une des options ci-dessous.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"De Mermaid à Excalidraw\",\n    \"button\": \"Insérer\",\n    \"description\": \"\",\n    \"syntax\": \"Syntaxe Mermaid\",\n    \"preview\": \"Prévisualisation\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}