"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_cypher_cypher_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/cypher/cypher.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/cypher/cypher.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/cypher/cypher.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: `.cypher`,\n  ignoreCase: true,\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \"ALL\",\n    \"AND\",\n    \"AS\",\n    \"ASC\",\n    \"ASCENDING\",\n    \"BY\",\n    \"CALL\",\n    \"CASE\",\n    \"CONTAINS\",\n    \"CREATE\",\n    \"DELETE\",\n    \"DESC\",\n    \"DESCENDING\",\n    \"DETACH\",\n    \"DISTINCT\",\n    \"ELSE\",\n    \"END\",\n    \"ENDS\",\n    \"EXISTS\",\n    \"IN\",\n    \"IS\",\n    \"LIMIT\",\n    \"MANDATORY\",\n    \"MATCH\",\n    \"MERGE\",\n    \"NOT\",\n    \"ON\",\n    \"ON\",\n    \"OPTIONAL\",\n    \"OR\",\n    \"ORDER\",\n    \"REMOVE\",\n    \"RETURN\",\n    \"SET\",\n    \"SKIP\",\n    \"STARTS\",\n    \"THEN\",\n    \"UNION\",\n    \"UNWIND\",\n    \"WHEN\",\n    \"WHERE\",\n    \"WITH\",\n    \"XOR\",\n    \"YIELD\"\n  ],\n  builtinLiterals: [\"true\", \"TRUE\", \"false\", \"FALSE\", \"null\", \"NULL\"],\n  builtinFunctions: [\n    \"abs\",\n    \"acos\",\n    \"asin\",\n    \"atan\",\n    \"atan2\",\n    \"avg\",\n    \"ceil\",\n    \"coalesce\",\n    \"collect\",\n    \"cos\",\n    \"cot\",\n    \"count\",\n    \"degrees\",\n    \"e\",\n    \"endNode\",\n    \"exists\",\n    \"exp\",\n    \"floor\",\n    \"head\",\n    \"id\",\n    \"keys\",\n    \"labels\",\n    \"last\",\n    \"left\",\n    \"length\",\n    \"log\",\n    \"log10\",\n    \"lTrim\",\n    \"max\",\n    \"min\",\n    \"nodes\",\n    \"percentileCont\",\n    \"percentileDisc\",\n    \"pi\",\n    \"properties\",\n    \"radians\",\n    \"rand\",\n    \"range\",\n    \"relationships\",\n    \"replace\",\n    \"reverse\",\n    \"right\",\n    \"round\",\n    \"rTrim\",\n    \"sign\",\n    \"sin\",\n    \"size\",\n    \"split\",\n    \"sqrt\",\n    \"startNode\",\n    \"stDev\",\n    \"stDevP\",\n    \"substring\",\n    \"sum\",\n    \"tail\",\n    \"tan\",\n    \"timestamp\",\n    \"toBoolean\",\n    \"toFloat\",\n    \"toInteger\",\n    \"toLower\",\n    \"toString\",\n    \"toUpper\",\n    \"trim\",\n    \"type\"\n  ],\n  operators: [\n    // Math operators\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"^\",\n    // Comparison operators\n    \"=\",\n    \"<>\",\n    \"<\",\n    \">\",\n    \"<=\",\n    \">=\",\n    // Pattern operators\n    \"->\",\n    \"<-\",\n    \"-->\",\n    \"<--\"\n  ],\n  escapes: /\\\\(?:[tbnrf\\\\\"'`]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+/,\n  octaldigits: /[0-7]+/,\n  hexdigits: /[0-9a-fA-F]+/,\n  tokenizer: {\n    root: [[/[{}[\\]()]/, \"@brackets\"], { include: \"common\" }],\n    common: [\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      // Cypher labels on nodes/relationships, e.g. (n:NodeLabel)-[e:RelationshipLabel]\n      [/:[a-zA-Z_][\\w]*/, \"type.identifier\"],\n      [\n        /[a-zA-Z_][\\w]*(?=\\()/,\n        {\n          cases: {\n            \"@builtinFunctions\": \"predefined.function\"\n          }\n        }\n      ],\n      [\n        /[a-zA-Z_$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@builtinLiterals\": \"predefined.literal\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/`/, \"identifier.escape\", \"@identifierBacktick\"],\n      // delimiter and operator after number because of `.\\d` floats and `:` in labels\n      [/[;,.:|]/, \"delimiter\"],\n      [\n        /[<>=%+\\-*/^]+/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/-?(@digits)[eE](-?(@digits))?/, \"number.float\"],\n      [/-?(@digits)?\\.(@digits)([eE]-?(@digits))?/, \"number.float\"],\n      [/-?0x(@hexdigits)/, \"number.hex\"],\n      [/-?0(@octaldigits)/, \"number.octal\"],\n      [/-?(@digits)/, \"number\"]\n    ],\n    strings: [\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@stringDouble\"],\n      [/'/, \"string\", \"@stringSingle\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/\\/\\/.*/, \"comment\"],\n      [/[^/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[/*]/, \"comment\"]\n    ],\n    stringDouble: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string\"],\n      [/\\\\./, \"string.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    stringSingle: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string\"],\n      [/\\\\./, \"string.invalid\"],\n      [/'/, \"string\", \"@pop\"]\n    ],\n    identifierBacktick: [\n      [/[^\\\\`]+/, \"identifier.escape\"],\n      [/@escapes/, \"identifier.escape\"],\n      [/\\\\./, \"identifier.escape.invalid\"],\n      [/`/, \"identifier.escape\", \"@pop\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/cypher/cypher.js\n"));

/***/ })

}]);