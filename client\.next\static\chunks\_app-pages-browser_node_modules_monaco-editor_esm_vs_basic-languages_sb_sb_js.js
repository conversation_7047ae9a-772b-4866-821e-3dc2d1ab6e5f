"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_sb_sb_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/sb/sb.js":
/*!*********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/sb/sb.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/sb/sb.ts\nvar conf = {\n  comments: {\n    lineComment: \"'\"\n  },\n  brackets: [\n    [\"(\", \")\"],\n    [\"[\", \"]\"],\n    [\"If\", \"EndIf\"],\n    [\"While\", \"EndWhile\"],\n    [\"For\", \"EndFor\"],\n    [\"Sub\", \"EndSub\"]\n  ],\n  autoClosingPairs: [\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sb\",\n  ignoreCase: true,\n  brackets: [\n    { token: \"delimiter.array\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    // Special bracket statement pairs\n    { token: \"keyword.tag-if\", open: \"If\", close: \"EndIf\" },\n    { token: \"keyword.tag-while\", open: \"While\", close: \"EndWhile\" },\n    { token: \"keyword.tag-for\", open: \"For\", close: \"EndFor\" },\n    { token: \"keyword.tag-sub\", open: \"Sub\", close: \"EndSub\" }\n  ],\n  keywords: [\n    \"Else\",\n    \"ElseIf\",\n    \"EndFor\",\n    \"EndIf\",\n    \"EndSub\",\n    \"EndWhile\",\n    \"For\",\n    \"Goto\",\n    \"If\",\n    \"Step\",\n    \"Sub\",\n    \"Then\",\n    \"To\",\n    \"While\"\n  ],\n  tagwords: [\"If\", \"Sub\", \"While\", \"For\"],\n  operators: [\">\", \"<\", \"<>\", \"<=\", \">=\", \"And\", \"Or\", \"+\", \"-\", \"*\", \"/\", \"=\"],\n  // we include these common regular expressions\n  identifier: /[a-zA-Z_][\\w]*/,\n  symbols: /[=><:+\\-*\\/%\\.,]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // whitespace\n      { include: \"@whitespace\" },\n      // classes\n      [/(@identifier)(?=[.])/, \"type\"],\n      // identifiers, tagwords, and keywords\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@operators\": \"operator\",\n            \"@default\": \"variable.name\"\n          }\n        }\n      ],\n      // methods, properties, and events\n      [\n        /([.])(@identifier)/,\n        {\n          cases: {\n            $2: [\"delimiter\", \"type.member\"],\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d*\\.\\d+/, \"number.float\"],\n      [/\\d+/, \"number\"],\n      // delimiters and operators\n      [/[()\\[\\]]/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@default\": \"delimiter\"\n          }\n        }\n      ],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/(\\').*$/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"C?/, \"string\", \"@pop\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/sb/sb.js\n"));

/***/ })

}]);