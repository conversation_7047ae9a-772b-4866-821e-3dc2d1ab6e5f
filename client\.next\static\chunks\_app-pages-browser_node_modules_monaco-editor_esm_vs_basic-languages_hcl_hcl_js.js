"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_hcl_hcl_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/hcl/hcl.js":
/*!***********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/hcl/hcl.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/hcl/hcl.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".hcl\",\n  keywords: [\n    \"var\",\n    \"local\",\n    \"path\",\n    \"for_each\",\n    \"any\",\n    \"string\",\n    \"number\",\n    \"bool\",\n    \"true\",\n    \"false\",\n    \"null\",\n    \"if \",\n    \"else \",\n    \"endif \",\n    \"for \",\n    \"in\",\n    \"endfor\"\n  ],\n  operators: [\n    \"=\",\n    \">=\",\n    \"<=\",\n    \"==\",\n    \"!=\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"&&\",\n    \"||\",\n    \"!\",\n    \"<\",\n    \">\",\n    \"?\",\n    \"...\",\n    \":\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  terraformFunctions: /(abs|ceil|floor|log|max|min|pow|signum|chomp|format|formatlist|indent|join|lower|regex|regexall|replace|split|strrev|substr|title|trimspace|upper|chunklist|coalesce|coalescelist|compact|concat|contains|distinct|element|flatten|index|keys|length|list|lookup|map|matchkeys|merge|range|reverse|setintersection|setproduct|setunion|slice|sort|transpose|values|zipmap|base64decode|base64encode|base64gzip|csvdecode|jsondecode|jsonencode|urlencode|yamldecode|yamlencode|abspath|dirname|pathexpand|basename|file|fileexists|fileset|filebase64|templatefile|formatdate|timeadd|timestamp|base64sha256|base64sha512|bcrypt|filebase64sha256|filebase64sha512|filemd5|filemd1|filesha256|filesha512|md5|rsadecrypt|sha1|sha256|sha512|uuid|uuidv5|cidrhost|cidrnetmask|cidrsubnet|tobool|tolist|tomap|tonumber|toset|tostring)/,\n  terraformMainBlocks: /(module|data|terraform|resource|provider|variable|output|locals)/,\n  tokenizer: {\n    root: [\n      // highlight main blocks\n      [\n        /^@terraformMainBlocks([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)(\\{)/,\n        [\"type\", \"\", \"string\", \"\", \"string\", \"\", \"@brackets\"]\n      ],\n      // highlight all the remaining blocks\n      [\n        /(\\w+[ \\t]+)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)(\\{)/,\n        [\"identifier\", \"\", \"string\", \"\", \"string\", \"\", \"@brackets\"]\n      ],\n      // highlight block\n      [\n        /(\\w+[ \\t]+)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)(=)(\\{)/,\n        [\"identifier\", \"\", \"string\", \"\", \"operator\", \"\", \"@brackets\"]\n      ],\n      // terraform general highlight - shared with expressions\n      { include: \"@terraform\" }\n    ],\n    terraform: [\n      // highlight terraform functions\n      [/@terraformFunctions(\\()/, [\"type\", \"@brackets\"]],\n      // all other words are variables or keywords\n      [\n        /[a-zA-Z_]\\w*-*/,\n        // must work with variables such as foo-bar and also with negative numbers\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"variable\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      { include: \"@heredoc\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d[\\d']*/, \"number\"],\n      [/\\d/, \"number\"],\n      [/[;,.]/, \"delimiter\"],\n      // delimiter: after number because of .\\d floats\n      // strings\n      [/\"/, \"string\", \"@string\"],\n      // this will include expressions\n      [/'/, \"invalid\"]\n    ],\n    heredoc: [\n      [/<<[-]*\\s*[\"]?([\\w\\-]+)[\"]?/, { token: \"string.heredoc.delimiter\", next: \"@heredocBody.$1\" }]\n    ],\n    heredocBody: [\n      [\n        /([\\w\\-]+)$/,\n        {\n          cases: {\n            \"$1==$S2\": [\n              {\n                token: \"string.heredoc.delimiter\",\n                next: \"@popall\"\n              }\n            ],\n            \"@default\": \"string.heredoc\"\n          }\n        }\n      ],\n      [/./, \"string.heredoc\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"],\n      [/#.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/\\$\\{/, { token: \"delimiter\", next: \"@stringExpression\" }],\n      [/[^\\\\\"\\$]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@popall\"]\n    ],\n    stringInsideExpression: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    stringExpression: [\n      [/\\}/, { token: \"delimiter\", next: \"@pop\" }],\n      [/\"/, \"string\", \"@stringInsideExpression\"],\n      { include: \"@terraform\" }\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbW9uYWNvLWVkaXRvci9lc20vdnMvYmFzaWMtbGFuZ3VhZ2VzL2hjbC9oY2wuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUdBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxPQUFPLEtBQUs7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sUUFBUSxZQUFZLEdBQUc7QUFDN0IsTUFBTSx1QkFBdUI7QUFDN0IsTUFBTSx1QkFBdUI7QUFDN0IsTUFBTTtBQUNOO0FBQ0E7QUFDQSxNQUFNLFFBQVEsWUFBWSxHQUFHO0FBQzdCLE1BQU0sdUJBQXVCO0FBQzdCLE1BQU0sdUJBQXVCO0FBQzdCLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsSUFBSSxjQUFjLEVBQUUsY0FBYyxFQUFFO0FBQ2hGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZGQUE2RjtBQUM3RjtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1GQUFtRjtBQUNuRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhFQUE4RTtBQUM5RTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIscUJBQXFCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSx3QkFBd0I7QUFDaEMsUUFBUSxxQkFBcUI7QUFDN0I7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLDREQUE0RDtBQUNuRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLEtBQUssK0NBQStDO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsS0FBSyxrQ0FBa0M7QUFDakQ7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBSUUiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0XFxyZWFsY29kZVxcbm9kZV9tb2R1bGVzXFxtb25hY28tZWRpdG9yXFxlc21cXHZzXFxiYXNpYy1sYW5ndWFnZXNcXGhjbFxcaGNsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi4gQWxsIHJpZ2h0cyByZXNlcnZlZC5cbiAqIFZlcnNpb246IDAuNTIuMig0MDQ1NDViZGVkMWRmNmZmYTQxZWEwYWY0ZThkZGIyMTkwMThjNmMxKVxuICogUmVsZWFzZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlXG4gKiBodHRwczovL2dpdGh1Yi5jb20vbWljcm9zb2Z0L21vbmFjby1lZGl0b3IvYmxvYi9tYWluL0xJQ0VOU0UudHh0XG4gKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuXG4vLyBzcmMvYmFzaWMtbGFuZ3VhZ2VzL2hjbC9oY2wudHNcbnZhciBjb25mID0ge1xuICBjb21tZW50czoge1xuICAgIGxpbmVDb21tZW50OiBcIiNcIixcbiAgICBibG9ja0NvbW1lbnQ6IFtcIi8qXCIsIFwiKi9cIl1cbiAgfSxcbiAgYnJhY2tldHM6IFtcbiAgICBbXCJ7XCIsIFwifVwiXSxcbiAgICBbXCJbXCIsIFwiXVwiXSxcbiAgICBbXCIoXCIsIFwiKVwiXVxuICBdLFxuICBhdXRvQ2xvc2luZ1BhaXJzOiBbXG4gICAgeyBvcGVuOiBcIntcIiwgY2xvc2U6IFwifVwiIH0sXG4gICAgeyBvcGVuOiBcIltcIiwgY2xvc2U6IFwiXVwiIH0sXG4gICAgeyBvcGVuOiBcIihcIiwgY2xvc2U6IFwiKVwiIH0sXG4gICAgeyBvcGVuOiAnXCInLCBjbG9zZTogJ1wiJywgbm90SW46IFtcInN0cmluZ1wiXSB9XG4gIF0sXG4gIHN1cnJvdW5kaW5nUGFpcnM6IFtcbiAgICB7IG9wZW46IFwie1wiLCBjbG9zZTogXCJ9XCIgfSxcbiAgICB7IG9wZW46IFwiW1wiLCBjbG9zZTogXCJdXCIgfSxcbiAgICB7IG9wZW46IFwiKFwiLCBjbG9zZTogXCIpXCIgfSxcbiAgICB7IG9wZW46ICdcIicsIGNsb3NlOiAnXCInIH1cbiAgXVxufTtcbnZhciBsYW5ndWFnZSA9IHtcbiAgZGVmYXVsdFRva2VuOiBcIlwiLFxuICB0b2tlblBvc3RmaXg6IFwiLmhjbFwiLFxuICBrZXl3b3JkczogW1xuICAgIFwidmFyXCIsXG4gICAgXCJsb2NhbFwiLFxuICAgIFwicGF0aFwiLFxuICAgIFwiZm9yX2VhY2hcIixcbiAgICBcImFueVwiLFxuICAgIFwic3RyaW5nXCIsXG4gICAgXCJudW1iZXJcIixcbiAgICBcImJvb2xcIixcbiAgICBcInRydWVcIixcbiAgICBcImZhbHNlXCIsXG4gICAgXCJudWxsXCIsXG4gICAgXCJpZiBcIixcbiAgICBcImVsc2UgXCIsXG4gICAgXCJlbmRpZiBcIixcbiAgICBcImZvciBcIixcbiAgICBcImluXCIsXG4gICAgXCJlbmRmb3JcIlxuICBdLFxuICBvcGVyYXRvcnM6IFtcbiAgICBcIj1cIixcbiAgICBcIj49XCIsXG4gICAgXCI8PVwiLFxuICAgIFwiPT1cIixcbiAgICBcIiE9XCIsXG4gICAgXCIrXCIsXG4gICAgXCItXCIsXG4gICAgXCIqXCIsXG4gICAgXCIvXCIsXG4gICAgXCIlXCIsXG4gICAgXCImJlwiLFxuICAgIFwifHxcIixcbiAgICBcIiFcIixcbiAgICBcIjxcIixcbiAgICBcIj5cIixcbiAgICBcIj9cIixcbiAgICBcIi4uLlwiLFxuICAgIFwiOlwiXG4gIF0sXG4gIHN5bWJvbHM6IC9bPT48IX4/OiZ8K1xcLSpcXC9cXF4lXSsvLFxuICBlc2NhcGVzOiAvXFxcXCg/OlthYmZucnR2XFxcXFwiJ118eFswLTlBLUZhLWZdezEsNH18dVswLTlBLUZhLWZdezR9fFVbMC05QS1GYS1mXXs4fSkvLFxuICB0ZXJyYWZvcm1GdW5jdGlvbnM6IC8oYWJzfGNlaWx8Zmxvb3J8bG9nfG1heHxtaW58cG93fHNpZ251bXxjaG9tcHxmb3JtYXR8Zm9ybWF0bGlzdHxpbmRlbnR8am9pbnxsb3dlcnxyZWdleHxyZWdleGFsbHxyZXBsYWNlfHNwbGl0fHN0cnJldnxzdWJzdHJ8dGl0bGV8dHJpbXNwYWNlfHVwcGVyfGNodW5rbGlzdHxjb2FsZXNjZXxjb2FsZXNjZWxpc3R8Y29tcGFjdHxjb25jYXR8Y29udGFpbnN8ZGlzdGluY3R8ZWxlbWVudHxmbGF0dGVufGluZGV4fGtleXN8bGVuZ3RofGxpc3R8bG9va3VwfG1hcHxtYXRjaGtleXN8bWVyZ2V8cmFuZ2V8cmV2ZXJzZXxzZXRpbnRlcnNlY3Rpb258c2V0cHJvZHVjdHxzZXR1bmlvbnxzbGljZXxzb3J0fHRyYW5zcG9zZXx2YWx1ZXN8emlwbWFwfGJhc2U2NGRlY29kZXxiYXNlNjRlbmNvZGV8YmFzZTY0Z3ppcHxjc3ZkZWNvZGV8anNvbmRlY29kZXxqc29uZW5jb2RlfHVybGVuY29kZXx5YW1sZGVjb2RlfHlhbWxlbmNvZGV8YWJzcGF0aHxkaXJuYW1lfHBhdGhleHBhbmR8YmFzZW5hbWV8ZmlsZXxmaWxlZXhpc3RzfGZpbGVzZXR8ZmlsZWJhc2U2NHx0ZW1wbGF0ZWZpbGV8Zm9ybWF0ZGF0ZXx0aW1lYWRkfHRpbWVzdGFtcHxiYXNlNjRzaGEyNTZ8YmFzZTY0c2hhNTEyfGJjcnlwdHxmaWxlYmFzZTY0c2hhMjU2fGZpbGViYXNlNjRzaGE1MTJ8ZmlsZW1kNXxmaWxlbWQxfGZpbGVzaGEyNTZ8ZmlsZXNoYTUxMnxtZDV8cnNhZGVjcnlwdHxzaGExfHNoYTI1NnxzaGE1MTJ8dXVpZHx1dWlkdjV8Y2lkcmhvc3R8Y2lkcm5ldG1hc2t8Y2lkcnN1Ym5ldHx0b2Jvb2x8dG9saXN0fHRvbWFwfHRvbnVtYmVyfHRvc2V0fHRvc3RyaW5nKS8sXG4gIHRlcnJhZm9ybU1haW5CbG9ja3M6IC8obW9kdWxlfGRhdGF8dGVycmFmb3JtfHJlc291cmNlfHByb3ZpZGVyfHZhcmlhYmxlfG91dHB1dHxsb2NhbHMpLyxcbiAgdG9rZW5pemVyOiB7XG4gICAgcm9vdDogW1xuICAgICAgLy8gaGlnaGxpZ2h0IG1haW4gYmxvY2tzXG4gICAgICBbXG4gICAgICAgIC9eQHRlcnJhZm9ybU1haW5CbG9ja3MoWyBcXHRdKikoW1xcdy1dK3xcIltcXHctXStcInwpKFsgXFx0XSopKFtcXHctXSt8XCJbXFx3LV0rXCJ8KShbIFxcdF0qKShcXHspLyxcbiAgICAgICAgW1widHlwZVwiLCBcIlwiLCBcInN0cmluZ1wiLCBcIlwiLCBcInN0cmluZ1wiLCBcIlwiLCBcIkBicmFja2V0c1wiXVxuICAgICAgXSxcbiAgICAgIC8vIGhpZ2hsaWdodCBhbGwgdGhlIHJlbWFpbmluZyBibG9ja3NcbiAgICAgIFtcbiAgICAgICAgLyhcXHcrWyBcXHRdKykoWyBcXHRdKikoW1xcdy1dK3xcIltcXHctXStcInwpKFsgXFx0XSopKFtcXHctXSt8XCJbXFx3LV0rXCJ8KShbIFxcdF0qKShcXHspLyxcbiAgICAgICAgW1wiaWRlbnRpZmllclwiLCBcIlwiLCBcInN0cmluZ1wiLCBcIlwiLCBcInN0cmluZ1wiLCBcIlwiLCBcIkBicmFja2V0c1wiXVxuICAgICAgXSxcbiAgICAgIC8vIGhpZ2hsaWdodCBibG9ja1xuICAgICAgW1xuICAgICAgICAvKFxcdytbIFxcdF0rKShbIFxcdF0qKShbXFx3LV0rfFwiW1xcdy1dK1wifCkoWyBcXHRdKikoW1xcdy1dK3xcIltcXHctXStcInwpKD0pKFxceykvLFxuICAgICAgICBbXCJpZGVudGlmaWVyXCIsIFwiXCIsIFwic3RyaW5nXCIsIFwiXCIsIFwib3BlcmF0b3JcIiwgXCJcIiwgXCJAYnJhY2tldHNcIl1cbiAgICAgIF0sXG4gICAgICAvLyB0ZXJyYWZvcm0gZ2VuZXJhbCBoaWdobGlnaHQgLSBzaGFyZWQgd2l0aCBleHByZXNzaW9uc1xuICAgICAgeyBpbmNsdWRlOiBcIkB0ZXJyYWZvcm1cIiB9XG4gICAgXSxcbiAgICB0ZXJyYWZvcm06IFtcbiAgICAgIC8vIGhpZ2hsaWdodCB0ZXJyYWZvcm0gZnVuY3Rpb25zXG4gICAgICBbL0B0ZXJyYWZvcm1GdW5jdGlvbnMoXFwoKS8sIFtcInR5cGVcIiwgXCJAYnJhY2tldHNcIl1dLFxuICAgICAgLy8gYWxsIG90aGVyIHdvcmRzIGFyZSB2YXJpYWJsZXMgb3Iga2V5d29yZHNcbiAgICAgIFtcbiAgICAgICAgL1thLXpBLVpfXVxcdyotKi8sXG4gICAgICAgIC8vIG11c3Qgd29yayB3aXRoIHZhcmlhYmxlcyBzdWNoIGFzIGZvby1iYXIgYW5kIGFsc28gd2l0aCBuZWdhdGl2ZSBudW1iZXJzXG4gICAgICAgIHtcbiAgICAgICAgICBjYXNlczoge1xuICAgICAgICAgICAgXCJAa2V5d29yZHNcIjogeyB0b2tlbjogXCJrZXl3b3JkLiQwXCIgfSxcbiAgICAgICAgICAgIFwiQGRlZmF1bHRcIjogXCJ2YXJpYWJsZVwiXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgeyBpbmNsdWRlOiBcIkB3aGl0ZXNwYWNlXCIgfSxcbiAgICAgIHsgaW5jbHVkZTogXCJAaGVyZWRvY1wiIH0sXG4gICAgICAvLyBkZWxpbWl0ZXJzIGFuZCBvcGVyYXRvcnNcbiAgICAgIFsvW3t9KClcXFtcXF1dLywgXCJAYnJhY2tldHNcIl0sXG4gICAgICBbL1s8Pl0oPyFAc3ltYm9scykvLCBcIkBicmFja2V0c1wiXSxcbiAgICAgIFtcbiAgICAgICAgL0BzeW1ib2xzLyxcbiAgICAgICAge1xuICAgICAgICAgIGNhc2VzOiB7XG4gICAgICAgICAgICBcIkBvcGVyYXRvcnNcIjogXCJvcGVyYXRvclwiLFxuICAgICAgICAgICAgXCJAZGVmYXVsdFwiOiBcIlwiXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgLy8gbnVtYmVyc1xuICAgICAgWy9cXGQqXFxkK1tlRV0oW1xcLStdP1xcZCspPy8sIFwibnVtYmVyLmZsb2F0XCJdLFxuICAgICAgWy9cXGQqXFwuXFxkKyhbZUVdW1xcLStdP1xcZCspPy8sIFwibnVtYmVyLmZsb2F0XCJdLFxuICAgICAgWy9cXGRbXFxkJ10qLywgXCJudW1iZXJcIl0sXG4gICAgICBbL1xcZC8sIFwibnVtYmVyXCJdLFxuICAgICAgWy9bOywuXS8sIFwiZGVsaW1pdGVyXCJdLFxuICAgICAgLy8gZGVsaW1pdGVyOiBhZnRlciBudW1iZXIgYmVjYXVzZSBvZiAuXFxkIGZsb2F0c1xuICAgICAgLy8gc3RyaW5nc1xuICAgICAgWy9cIi8sIFwic3RyaW5nXCIsIFwiQHN0cmluZ1wiXSxcbiAgICAgIC8vIHRoaXMgd2lsbCBpbmNsdWRlIGV4cHJlc3Npb25zXG4gICAgICBbLycvLCBcImludmFsaWRcIl1cbiAgICBdLFxuICAgIGhlcmVkb2M6IFtcbiAgICAgIFsvPDxbLV0qXFxzKltcIl0/KFtcXHdcXC1dKylbXCJdPy8sIHsgdG9rZW46IFwic3RyaW5nLmhlcmVkb2MuZGVsaW1pdGVyXCIsIG5leHQ6IFwiQGhlcmVkb2NCb2R5LiQxXCIgfV1cbiAgICBdLFxuICAgIGhlcmVkb2NCb2R5OiBbXG4gICAgICBbXG4gICAgICAgIC8oW1xcd1xcLV0rKSQvLFxuICAgICAgICB7XG4gICAgICAgICAgY2FzZXM6IHtcbiAgICAgICAgICAgIFwiJDE9PSRTMlwiOiBbXG4gICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICB0b2tlbjogXCJzdHJpbmcuaGVyZWRvYy5kZWxpbWl0ZXJcIixcbiAgICAgICAgICAgICAgICBuZXh0OiBcIkBwb3BhbGxcIlxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICBdLFxuICAgICAgICAgICAgXCJAZGVmYXVsdFwiOiBcInN0cmluZy5oZXJlZG9jXCJcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIF0sXG4gICAgICBbLy4vLCBcInN0cmluZy5oZXJlZG9jXCJdXG4gICAgXSxcbiAgICB3aGl0ZXNwYWNlOiBbXG4gICAgICBbL1sgXFx0XFxyXFxuXSsvLCBcIlwiXSxcbiAgICAgIFsvXFwvXFwqLywgXCJjb21tZW50XCIsIFwiQGNvbW1lbnRcIl0sXG4gICAgICBbL1xcL1xcLy4qJC8sIFwiY29tbWVudFwiXSxcbiAgICAgIFsvIy4qJC8sIFwiY29tbWVudFwiXVxuICAgIF0sXG4gICAgY29tbWVudDogW1xuICAgICAgWy9bXlxcLypdKy8sIFwiY29tbWVudFwiXSxcbiAgICAgIFsvXFwqXFwvLywgXCJjb21tZW50XCIsIFwiQHBvcFwiXSxcbiAgICAgIFsvW1xcLypdLywgXCJjb21tZW50XCJdXG4gICAgXSxcbiAgICBzdHJpbmc6IFtcbiAgICAgIFsvXFwkXFx7LywgeyB0b2tlbjogXCJkZWxpbWl0ZXJcIiwgbmV4dDogXCJAc3RyaW5nRXhwcmVzc2lvblwiIH1dLFxuICAgICAgWy9bXlxcXFxcIlxcJF0rLywgXCJzdHJpbmdcIl0sXG4gICAgICBbL0Blc2NhcGVzLywgXCJzdHJpbmcuZXNjYXBlXCJdLFxuICAgICAgWy9cXFxcLi8sIFwic3RyaW5nLmVzY2FwZS5pbnZhbGlkXCJdLFxuICAgICAgWy9cIi8sIFwic3RyaW5nXCIsIFwiQHBvcGFsbFwiXVxuICAgIF0sXG4gICAgc3RyaW5nSW5zaWRlRXhwcmVzc2lvbjogW1xuICAgICAgWy9bXlxcXFxcIl0rLywgXCJzdHJpbmdcIl0sXG4gICAgICBbL0Blc2NhcGVzLywgXCJzdHJpbmcuZXNjYXBlXCJdLFxuICAgICAgWy9cXFxcLi8sIFwic3RyaW5nLmVzY2FwZS5pbnZhbGlkXCJdLFxuICAgICAgWy9cIi8sIFwic3RyaW5nXCIsIFwiQHBvcFwiXVxuICAgIF0sXG4gICAgc3RyaW5nRXhwcmVzc2lvbjogW1xuICAgICAgWy9cXH0vLCB7IHRva2VuOiBcImRlbGltaXRlclwiLCBuZXh0OiBcIkBwb3BcIiB9XSxcbiAgICAgIFsvXCIvLCBcInN0cmluZ1wiLCBcIkBzdHJpbmdJbnNpZGVFeHByZXNzaW9uXCJdLFxuICAgICAgeyBpbmNsdWRlOiBcIkB0ZXJyYWZvcm1cIiB9XG4gICAgXVxuICB9XG59O1xuZXhwb3J0IHtcbiAgY29uZixcbiAgbGFuZ3VhZ2Vcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/hcl/hcl.js\n"));

/***/ })

}]);