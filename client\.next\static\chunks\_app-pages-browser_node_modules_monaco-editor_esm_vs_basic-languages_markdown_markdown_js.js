"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_markdown_markdown_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/markdown/markdown.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/markdown/markdown.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/markdown/markdown.ts\nvar conf = {\n  comments: {\n    blockComment: [\"<!--\", \"-->\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"(\", close: \")\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*<!--\\\\s*#?region\\\\b.*-->\"),\n      end: new RegExp(\"^\\\\s*<!--\\\\s*#?endregion\\\\b.*-->\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".md\",\n  // escape codes\n  control: /[\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  noncontrol: /[^\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  escapes: /\\\\(?:@control)/,\n  // escape codes for javascript/CSS strings\n  jsescapes: /\\\\(?:[btnfr\\\\\"']|[0-7][0-7]?|[0-3][0-7]{2})/,\n  // non matched elements\n  empty: [\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"link\",\n    \"meta\",\n    \"param\"\n  ],\n  tokenizer: {\n    root: [\n      // markdown tables\n      [/^\\s*\\|/, \"@rematch\", \"@table_header\"],\n      // headers (with #)\n      [/^(\\s{0,3})(#+)((?:[^\\\\#]|@escapes)+)((?:#+)?)/, [\"white\", \"keyword\", \"keyword\", \"keyword\"]],\n      // headers (with =)\n      [/^\\s*(=+|\\-+)\\s*$/, \"keyword\"],\n      // headers (with ***)\n      [/^\\s*((\\*[ ]?)+)\\s*$/, \"meta.separator\"],\n      // quote\n      [/^\\s*>+/, \"comment\"],\n      // list (starting with * or number)\n      [/^\\s*([\\*\\-+:]|\\d+\\.)\\s/, \"keyword\"],\n      // code block (4 spaces indent)\n      [/^(\\t|[ ]{4})[^ ].*$/, \"string\"],\n      // code block (3 tilde)\n      [/^\\s*~~~\\s*((?:\\w|[\\/\\-#])+)?\\s*$/, { token: \"string\", next: \"@codeblock\" }],\n      // github style code blocks (with backticks and language)\n      [\n        /^\\s*```\\s*((?:\\w|[\\/\\-#])+).*$/,\n        { token: \"string\", next: \"@codeblockgh\", nextEmbedded: \"$1\" }\n      ],\n      // github style code blocks (with backticks but no language)\n      [/^\\s*```\\s*$/, { token: \"string\", next: \"@codeblock\" }],\n      // markup within lines\n      { include: \"@linecontent\" }\n    ],\n    table_header: [\n      { include: \"@table_common\" },\n      [/[^\\|]+/, \"keyword.table.header\"]\n      // table header\n    ],\n    table_body: [{ include: \"@table_common\" }, { include: \"@linecontent\" }],\n    table_common: [\n      [/\\s*[\\-:]+\\s*/, { token: \"keyword\", switchTo: \"table_body\" }],\n      // header-divider\n      [/^\\s*\\|/, \"keyword.table.left\"],\n      // opening |\n      [/^\\s*[^\\|]/, \"@rematch\", \"@pop\"],\n      // exiting\n      [/^\\s*$/, \"@rematch\", \"@pop\"],\n      // exiting\n      [\n        /\\|/,\n        {\n          cases: {\n            \"@eos\": \"keyword.table.right\",\n            // closing |\n            \"@default\": \"keyword.table.middle\"\n            // inner |\n          }\n        }\n      ]\n    ],\n    codeblock: [\n      [/^\\s*~~~\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/^\\s*```\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    // github style code blocks\n    codeblockgh: [\n      [/```\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^`]+/, \"variable.source\"]\n    ],\n    linecontent: [\n      // escapes\n      [/&\\w+;/, \"string.escape\"],\n      [/@escapes/, \"escape\"],\n      // various markup\n      [/\\b__([^\\\\_]|@escapes|_(?!_))+__\\b/, \"strong\"],\n      [/\\*\\*([^\\\\*]|@escapes|\\*(?!\\*))+\\*\\*/, \"strong\"],\n      [/\\b_[^_]+_\\b/, \"emphasis\"],\n      [/\\*([^\\\\*]|@escapes)+\\*/, \"emphasis\"],\n      [/`([^\\\\`]|@escapes)+`/, \"variable\"],\n      // links\n      [/\\{+[^}]+\\}+/, \"string.target\"],\n      [/(!?\\[)((?:[^\\]\\\\]|@escapes)*)(\\]\\([^\\)]+\\))/, [\"string.link\", \"\", \"string.link\"]],\n      [/(!?\\[)((?:[^\\]\\\\]|@escapes)*)(\\])/, \"string.link\"],\n      // or html\n      { include: \"html\" }\n    ],\n    // Note: it is tempting to rather switch to the real HTML mode instead of building our own here\n    // but currently there is a limitation in Monarch that prevents us from doing it: The opening\n    // '<' would start the HTML mode, however there is no way to jump 1 character back to let the\n    // HTML mode also tokenize the opening angle bracket. Thus, even though we could jump to HTML,\n    // we cannot correctly tokenize it in that mode yet.\n    html: [\n      // html tags\n      [/<(\\w+)\\/>/, \"tag\"],\n      [\n        /<(\\w+)(\\-|\\w)*/,\n        {\n          cases: {\n            \"@empty\": { token: \"tag\", next: \"@tag.$1\" },\n            \"@default\": { token: \"tag\", next: \"@tag.$1\" }\n          }\n        }\n      ],\n      [/<\\/(\\w+)(\\-|\\w)*\\s*>/, { token: \"tag\" }],\n      [/<!--/, \"comment\", \"@comment\"]\n    ],\n    comment: [\n      [/[^<\\-]+/, \"comment.content\"],\n      [/-->/, \"comment\", \"@pop\"],\n      [/<!--/, \"comment.content.invalid\"],\n      [/[<\\-]/, \"comment.content\"]\n    ],\n    // Almost full HTML tag matching, complete with embedded scripts & styles\n    tag: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [\n        /(type)(\\s*=\\s*)(\")([^\"]+)(\")/,\n        [\n          \"attribute.name.html\",\n          \"delimiter.html\",\n          \"string.html\",\n          { token: \"string.html\", switchTo: \"@tag.$S2.$4\" },\n          \"string.html\"\n        ]\n      ],\n      [\n        /(type)(\\s*=\\s*)(')([^']+)(')/,\n        [\n          \"attribute.name.html\",\n          \"delimiter.html\",\n          \"string.html\",\n          { token: \"string.html\", switchTo: \"@tag.$S2.$4\" },\n          \"string.html\"\n        ]\n      ],\n      [/(\\w+)(\\s*=\\s*)(\"[^\"]*\"|'[^']*')/, [\"attribute.name.html\", \"delimiter.html\", \"string.html\"]],\n      [/\\w+/, \"attribute.name.html\"],\n      [/\\/>/, \"tag\", \"@pop\"],\n      [\n        />/,\n        {\n          cases: {\n            \"$S2==style\": {\n              token: \"tag\",\n              switchTo: \"embeddedStyle\",\n              nextEmbedded: \"text/css\"\n            },\n            \"$S2==script\": {\n              cases: {\n                $S3: {\n                  token: \"tag\",\n                  switchTo: \"embeddedScript\",\n                  nextEmbedded: \"$S3\"\n                },\n                \"@default\": {\n                  token: \"tag\",\n                  switchTo: \"embeddedScript\",\n                  nextEmbedded: \"text/javascript\"\n                }\n              }\n            },\n            \"@default\": { token: \"tag\", next: \"@pop\" }\n          }\n        }\n      ]\n    ],\n    embeddedStyle: [\n      [/[^<]+/, \"\"],\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/</, \"\"]\n    ],\n    embeddedScript: [\n      [/[^<]+/, \"\"],\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/</, \"\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/markdown/markdown.js\n"));

/***/ })

}]);