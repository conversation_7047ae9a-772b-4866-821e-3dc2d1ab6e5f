"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_elixir_elixir_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/elixir/elixir.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/elixir/elixir.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/elixir/elixir.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  autoClosingPairs: [\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"comment\"] },\n    { open: '\"\"\"', close: '\"\"\"' },\n    { open: \"`\", close: \"`\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"<<\", close: \">>\" }\n  ],\n  indentationRules: {\n    increaseIndentPattern: /^\\s*(after|else|catch|rescue|fn|[^#]*(do|<\\-|\\->|\\{|\\[|\\=))\\s*$/,\n    decreaseIndentPattern: /^\\s*((\\}|\\])\\s*$|(after|else|catch|rescue|end)\\b)/\n  }\n};\nvar language = {\n  defaultToken: \"source\",\n  tokenPostfix: \".elixir\",\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"<<\", close: \">>\", token: \"delimiter.angle.special\" }\n  ],\n  // Below are lists/regexps to which we reference later.\n  declarationKeywords: [\n    \"def\",\n    \"defp\",\n    \"defn\",\n    \"defnp\",\n    \"defguard\",\n    \"defguardp\",\n    \"defmacro\",\n    \"defmacrop\",\n    \"defdelegate\",\n    \"defcallback\",\n    \"defmacrocallback\",\n    \"defmodule\",\n    \"defprotocol\",\n    \"defexception\",\n    \"defimpl\",\n    \"defstruct\"\n  ],\n  operatorKeywords: [\"and\", \"in\", \"not\", \"or\", \"when\"],\n  namespaceKeywords: [\"alias\", \"import\", \"require\", \"use\"],\n  otherKeywords: [\n    \"after\",\n    \"case\",\n    \"catch\",\n    \"cond\",\n    \"do\",\n    \"else\",\n    \"end\",\n    \"fn\",\n    \"for\",\n    \"if\",\n    \"quote\",\n    \"raise\",\n    \"receive\",\n    \"rescue\",\n    \"super\",\n    \"throw\",\n    \"try\",\n    \"unless\",\n    \"unquote_splicing\",\n    \"unquote\",\n    \"with\"\n  ],\n  constants: [\"true\", \"false\", \"nil\"],\n  nameBuiltin: [\"__MODULE__\", \"__DIR__\", \"__ENV__\", \"__CALLER__\", \"__STACKTRACE__\"],\n  // Matches any of the operator names:\n  // <<< >>> ||| &&& ^^^ ~~~ === !== ~>> <~> |~> <|> == != <= >= && || \\\\ <> ++ -- |> =~ -> <- ~> <~ :: .. = < > + - * / | . ^ & !\n  operator: /-[->]?|!={0,2}|\\*{1,2}|\\/|\\\\\\\\|&{1,3}|\\.\\.?|\\^(?:\\^\\^)?|\\+\\+?|<(?:-|<<|=|>|\\|>|~>?)?|=~|={1,3}|>(?:=|>>)?|\\|~>|\\|>|\\|{1,3}|~>>?|~~~|::/,\n  // See https://hexdocs.pm/elixir/syntax-reference.html#variables\n  variableName: /[a-z_][a-zA-Z0-9_]*[?!]?/,\n  // See https://hexdocs.pm/elixir/syntax-reference.html#atoms\n  atomName: /[a-zA-Z_][a-zA-Z0-9_@]*[?!]?|@specialAtomName|@operator/,\n  specialAtomName: /\\.\\.\\.|<<>>|%\\{\\}|%|\\{\\}/,\n  aliasPart: /[A-Z][a-zA-Z0-9_]*/,\n  moduleName: /@aliasPart(?:\\.@aliasPart)*/,\n  // Sigil pairs are: \"\"\" \"\"\", ''' ''', \" \", ' ', / /, | |, < >, { }, [ ], ( )\n  sigilSymmetricDelimiter: /\"\"\"|'''|\"|'|\\/|\\|/,\n  sigilStartDelimiter: /@sigilSymmetricDelimiter|<|\\{|\\[|\\(/,\n  sigilEndDelimiter: /@sigilSymmetricDelimiter|>|\\}|\\]|\\)/,\n  sigilModifiers: /[a-zA-Z0-9]*/,\n  decimal: /\\d(?:_?\\d)*/,\n  hex: /[0-9a-fA-F](_?[0-9a-fA-F])*/,\n  octal: /[0-7](_?[0-7])*/,\n  binary: /[01](_?[01])*/,\n  // See https://hexdocs.pm/elixir/master/String.html#module-escape-characters\n  escape: /\\\\u[0-9a-fA-F]{4}|\\\\x[0-9a-fA-F]{2}|\\\\./,\n  // The keys below correspond to tokenizer states.\n  // We start from the root state and match against its rules\n  // until we explicitly transition into another state.\n  // The `include` simply brings in all operations from the given state\n  // and is useful for improving readability.\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comments\" },\n      // Keywords start as either an identifier or a string,\n      // but end with a : so it's important to match this first.\n      { include: \"@keywordsShorthand\" },\n      { include: \"@numbers\" },\n      { include: \"@identifiers\" },\n      { include: \"@strings\" },\n      { include: \"@atoms\" },\n      { include: \"@sigils\" },\n      { include: \"@attributes\" },\n      { include: \"@symbols\" }\n    ],\n    // Whitespace\n    whitespace: [[/\\s+/, \"white\"]],\n    // Comments\n    comments: [[/(#)(.*)/, [\"comment.punctuation\", \"comment\"]]],\n    // Keyword list shorthand\n    keywordsShorthand: [\n      [/(@atomName)(:)(\\s+)/, [\"constant\", \"constant.punctuation\", \"white\"]],\n      // Use positive look-ahead to ensure the string is followed by :\n      // and should be considered a keyword.\n      [\n        /\"(?=([^\"]|#\\{.*?\\}|\\\\\")*\":)/,\n        { token: \"constant.delimiter\", next: \"@doubleQuotedStringKeyword\" }\n      ],\n      [\n        /'(?=([^']|#\\{.*?\\}|\\\\')*':)/,\n        { token: \"constant.delimiter\", next: \"@singleQuotedStringKeyword\" }\n      ]\n    ],\n    doubleQuotedStringKeyword: [\n      [/\":/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    singleQuotedStringKeyword: [\n      [/':/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    // Numbers\n    numbers: [\n      [/0b@binary/, \"number.binary\"],\n      [/0o@octal/, \"number.octal\"],\n      [/0x@hex/, \"number.hex\"],\n      [/@decimal\\.@decimal([eE]-?@decimal)?/, \"number.float\"],\n      [/@decimal/, \"number\"]\n    ],\n    // Identifiers\n    identifiers: [\n      // Tokenize identifier name in function-like definitions.\n      // Note: given `def a + b, do: nil`, `a` is not a function name,\n      // so we use negative look-ahead to ensure there's no operator.\n      [\n        /\\b(defp?|defnp?|defmacrop?|defguardp?|defdelegate)(\\s+)(@variableName)(?!\\s+@operator)/,\n        [\n          \"keyword.declaration\",\n          \"white\",\n          {\n            cases: {\n              unquote: \"keyword\",\n              \"@default\": \"function\"\n            }\n          }\n        ]\n      ],\n      // Tokenize function calls\n      [\n        // In-scope call - an identifier followed by ( or .(\n        /(@variableName)(?=\\s*\\.?\\s*\\()/,\n        {\n          cases: {\n            // Tokenize as keyword in cases like `if(..., do: ..., else: ...)`\n            \"@declarationKeywords\": \"keyword.declaration\",\n            \"@namespaceKeywords\": \"keyword\",\n            \"@otherKeywords\": \"keyword\",\n            \"@default\": \"function.call\"\n          }\n        }\n      ],\n      [\n        // Referencing function in a module\n        /(@moduleName)(\\s*)(\\.)(\\s*)(@variableName)/,\n        [\"type.identifier\", \"white\", \"operator\", \"white\", \"function.call\"]\n      ],\n      [\n        // Referencing function in an Erlang module\n        /(:)(@atomName)(\\s*)(\\.)(\\s*)(@variableName)/,\n        [\"constant.punctuation\", \"constant\", \"white\", \"operator\", \"white\", \"function.call\"]\n      ],\n      [\n        // Piping into a function (tokenized separately as it may not have parentheses)\n        /(\\|>)(\\s*)(@variableName)/,\n        [\n          \"operator\",\n          \"white\",\n          {\n            cases: {\n              \"@otherKeywords\": \"keyword\",\n              \"@default\": \"function.call\"\n            }\n          }\n        ]\n      ],\n      [\n        // Function reference passed to another function\n        /(&)(\\s*)(@variableName)/,\n        [\"operator\", \"white\", \"function.call\"]\n      ],\n      // Language keywords, builtins, constants and variables\n      [\n        /@variableName/,\n        {\n          cases: {\n            \"@declarationKeywords\": \"keyword.declaration\",\n            \"@operatorKeywords\": \"keyword.operator\",\n            \"@namespaceKeywords\": \"keyword\",\n            \"@otherKeywords\": \"keyword\",\n            \"@constants\": \"constant.language\",\n            \"@nameBuiltin\": \"variable.language\",\n            \"_.*\": \"comment.unused\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // Module names\n      [/@moduleName/, \"type.identifier\"]\n    ],\n    // Strings\n    strings: [\n      [/\"\"\"/, { token: \"string.delimiter\", next: \"@doubleQuotedHeredoc\" }],\n      [/'''/, { token: \"string.delimiter\", next: \"@singleQuotedHeredoc\" }],\n      [/\"/, { token: \"string.delimiter\", next: \"@doubleQuotedString\" }],\n      [/'/, { token: \"string.delimiter\", next: \"@singleQuotedString\" }]\n    ],\n    doubleQuotedHeredoc: [\n      [/\"\"\"/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    singleQuotedHeredoc: [\n      [/'''/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    doubleQuotedString: [\n      [/\"/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    singleQuotedString: [\n      [/'/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    // Atoms\n    atoms: [\n      [/(:)(@atomName)/, [\"constant.punctuation\", \"constant\"]],\n      [/:\"/, { token: \"constant.delimiter\", next: \"@doubleQuotedStringAtom\" }],\n      [/:'/, { token: \"constant.delimiter\", next: \"@singleQuotedStringAtom\" }]\n    ],\n    doubleQuotedStringAtom: [\n      [/\"/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    singleQuotedStringAtom: [\n      [/'/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    // Sigils\n    // See https://elixir-lang.org/getting-started/sigils.html\n    // Sigils allow for typing values using their textual representation.\n    // All sigils start with ~ followed by a letter or\n    // multi-letter uppercase starting at Elixir v1.15.0, indicating sigil type\n    // and then a delimiter pair enclosing the textual representation.\n    // Optional modifiers are allowed after the closing delimiter.\n    // For instance a regular expressions can be written as:\n    // ~r/foo|bar/ ~r{foo|bar} ~r/foo|bar/g\n    //\n    // In general lowercase sigils allow for interpolation\n    // and escaped characters, whereas uppercase sigils don't\n    //\n    // During tokenization we want to distinguish some\n    // specific sigil types, namely string and regexp,\n    // so that they cen be themed separately.\n    //\n    // To reasonably handle all those combinations we leverage\n    // dot-separated states, so if we transition to @sigilStart.interpol.s.{.}\n    // then \"sigilStart.interpol.s\" state will match and also all\n    // the individual dot-separated parameters can be accessed.\n    sigils: [\n      [/~[a-z]@sigilStartDelimiter/, { token: \"@rematch\", next: \"@sigil.interpol\" }],\n      [/~([A-Z]+)@sigilStartDelimiter/, { token: \"@rematch\", next: \"@sigil.noInterpol\" }]\n    ],\n    sigil: [\n      [/~([a-z]|[A-Z]+)\\{/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.{.}\" }],\n      [/~([a-z]|[A-Z]+)\\[/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.[.]\" }],\n      [/~([a-z]|[A-Z]+)\\(/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.(.)\" }],\n      [/~([a-z]|[A-Z]+)\\</, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.<.>\" }],\n      [\n        /~([a-z]|[A-Z]+)(@sigilSymmetricDelimiter)/,\n        { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.$2.$2\" }\n      ]\n    ],\n    // The definitions below expect states to be of the form:\n    //\n    // sigilStart.<interpol-or-noInterpol>.<sigil-letter>.<start-delimiter>.<end-delimiter>\n    // sigilContinue.<interpol-or-noInterpol>.<sigil-letter>.<start-delimiter>.<end-delimiter>\n    //\n    // The sigilStart state is used only to properly classify the token (as string/regex/sigil)\n    // and immediately switches to the sigilContinue sate, which handles the actual content\n    // and waits for the corresponding end delimiter.\n    \"sigilStart.interpol.s\": [\n      [\n        /~s@sigilStartDelimiter/,\n        {\n          token: \"string.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol.s\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"string.delimiter\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      { include: \"@stringContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol.S\": [\n      [\n        /~S@sigilStartDelimiter/,\n        {\n          token: \"string.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol.S\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"string\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"string.delimiter\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      { include: \"@stringContent\" }\n    ],\n    \"sigilStart.interpol.r\": [\n      [\n        /~r@sigilStartDelimiter/,\n        {\n          token: \"regexp.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol.r\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"regexp.delimiter\", next: \"@pop\" },\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexpContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol.R\": [\n      [\n        /~R@sigilStartDelimiter/,\n        {\n          token: \"regexp.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol.R\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"regexp\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"regexp.delimiter\", next: \"@pop\" },\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexpContent\" }\n    ],\n    // Fallback to the generic sigil by default\n    \"sigilStart.interpol\": [\n      [\n        /~([a-z]|[A-Z]+)@sigilStartDelimiter/,\n        {\n          token: \"sigil.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"sigil.delimiter\", next: \"@pop\" },\n            \"@default\": \"sigil\"\n          }\n        }\n      ],\n      { include: \"@sigilContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol\": [\n      [\n        /~([a-z]|[A-Z]+)@sigilStartDelimiter/,\n        {\n          token: \"sigil.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"sigil\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"sigil.delimiter\", next: \"@pop\" },\n            \"@default\": \"sigil\"\n          }\n        }\n      ],\n      { include: \"@sigilContent\" }\n    ],\n    // Attributes\n    attributes: [\n      // Module @doc* attributes - tokenized as comments\n      [\n        /\\@(module|type)?doc (~[sS])?\"\"\"/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@doubleQuotedHeredocDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?'''/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@singleQuotedHeredocDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?\"/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@doubleQuotedStringDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?'/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@singleQuotedStringDocstring\"\n        }\n      ],\n      [/\\@(module|type)?doc false/, \"comment.block.documentation\"],\n      // Module attributes\n      [/\\@(@variableName)/, \"variable\"]\n    ],\n    doubleQuotedHeredocDocstring: [\n      [/\"\"\"/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    singleQuotedHeredocDocstring: [\n      [/'''/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    doubleQuotedStringDocstring: [\n      [/\"/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    singleQuotedStringDocstring: [\n      [/'/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    // Operators, punctuation, brackets\n    symbols: [\n      // Code point operator (either with regular character ?a or an escaped one ?\\n)\n      [/\\?(\\\\.|[^\\\\\\s])/, \"number.constant\"],\n      // Anonymous function arguments\n      [/&\\d+/, \"operator\"],\n      // Bitshift operators (must go before delimiters, so that << >> don't match first)\n      [/<<<|>>>/, \"operator\"],\n      // Delimiter pairs\n      [/[()\\[\\]\\{\\}]|<<|>>/, \"@brackets\"],\n      // Triple dot is a valid name (must go before operators, so that .. doesn't match instead)\n      [/\\.\\.\\./, \"identifier\"],\n      // Punctuation => (must go before operators, so it's not tokenized as = then >)\n      [/=>/, \"punctuation\"],\n      // Operators\n      [/@operator/, \"operator\"],\n      // Punctuation\n      [/[:;,.%]/, \"punctuation\"]\n    ],\n    // Generic helpers\n    stringContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@stringContent\" }\n    ],\n    stringContent: [[/./, \"string\"]],\n    stringConstantContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@stringConstantContent\" }\n    ],\n    stringConstantContent: [[/./, \"constant\"]],\n    regexpContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@regexpContent\" }\n    ],\n    regexpContent: [\n      // # may be a regular regexp char, so we use a heuristic\n      // assuming a # surrounded by whitespace is actually a comment.\n      [/(\\s)(#)(\\s.*)$/, [\"white\", \"comment.punctuation\", \"comment\"]],\n      [/./, \"regexp\"]\n    ],\n    sigilContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@sigilContent\" }\n    ],\n    sigilContent: [[/./, \"sigil\"]],\n    docstringContent: [[/./, \"comment.block.documentation\"]],\n    escapeChar: [[/@escape/, \"constant.character.escape\"]],\n    interpolation: [[/#{/, { token: \"delimiter.bracket.embed\", next: \"@interpolationContinue\" }]],\n    interpolationContinue: [\n      [/}/, { token: \"delimiter.bracket.embed\", next: \"@pop\" }],\n      // Interpolation brackets may contain arbitrary code,\n      // so we simply match against all the root rules,\n      // until we reach interpolation end (the above matches).\n      { include: \"@root\" }\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbW9uYWNvLWVkaXRvci9lc20vdnMvYmFzaWMtbGFuZ3VhZ2VzL2VsaXhpci9lbGl4aXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUdBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsT0FBTyxLQUFLO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLFFBQVEsWUFBWSxHQUFHO0FBQzdCLE1BQU0sdUJBQXVCO0FBQzdCLE1BQU0sdUJBQXVCO0FBQzdCLE1BQU0sdUJBQXVCO0FBQzdCLE1BQU07QUFDTjtBQUNBO0FBQ0EsTUFBTSxxREFBcUQ7QUFDM0QsTUFBTSwyQ0FBMkM7QUFDakQsTUFBTSwyQkFBMkI7QUFDakMsTUFBTSxxREFBcUQ7QUFDM0QsTUFBTSx1QkFBdUI7QUFDN0IsTUFBTSxRQUFRLFlBQVksR0FBRztBQUM3QixNQUFNLHVCQUF1QjtBQUM3QixNQUFNO0FBQ047QUFDQTtBQUNBLCtFQUErRTtBQUMvRSxvQ0FBb0M7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSxrREFBa0Q7QUFDeEQsTUFBTSx1REFBdUQ7QUFDN0QsTUFBTSxRQUFRLFlBQVksNkJBQTZCO0FBQ3ZELE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixJQUFJLElBQUksSUFBSSxXQUFXLElBQUkscURBQXFELElBQUksd0JBQXdCLElBQUk7QUFDdkk7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsRUFBRSxLQUFLLEVBQUU7QUFDNUM7QUFDQTtBQUNBLG9FQUFvRTtBQUNwRTtBQUNBLHFEQUFxRDtBQUNyRCxtREFBbUQ7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLEVBQUUsZ0JBQWdCLEVBQUU7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLHdCQUF3QjtBQUNoQyxRQUFRLHNCQUFzQjtBQUM5QjtBQUNBO0FBQ0EsUUFBUSwrQkFBK0I7QUFDdkMsUUFBUSxxQkFBcUI7QUFDN0IsUUFBUSx5QkFBeUI7QUFDakMsUUFBUSxxQkFBcUI7QUFDN0IsUUFBUSxtQkFBbUI7QUFDM0IsUUFBUSxvQkFBb0I7QUFDNUIsUUFBUSx3QkFBd0I7QUFDaEMsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsS0FBSztBQUMzQixVQUFVO0FBQ1Y7QUFDQTtBQUNBLHNCQUFzQixLQUFLO0FBQzNCLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSxlQUFlLDJDQUEyQztBQUMxRCxRQUFRO0FBQ1I7QUFDQTtBQUNBLGVBQWUsMkNBQTJDO0FBQzFELFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQix5REFBeUQ7QUFDekUsZ0JBQWdCLHlEQUF5RDtBQUN6RSxjQUFjLHdEQUF3RDtBQUN0RSxjQUFjLHdEQUF3RDtBQUN0RTtBQUNBO0FBQ0EsZ0JBQWdCLHlDQUF5QztBQUN6RCxRQUFRO0FBQ1I7QUFDQTtBQUNBLGdCQUFnQix5Q0FBeUM7QUFDekQsUUFBUTtBQUNSO0FBQ0E7QUFDQSxjQUFjLHlDQUF5QztBQUN2RCxRQUFRO0FBQ1I7QUFDQTtBQUNBLGNBQWMseUNBQXlDO0FBQ3ZELFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsOERBQThEO0FBQzdFLGVBQWUsOERBQThEO0FBQzdFO0FBQ0E7QUFDQSxjQUFjLDJDQUEyQztBQUN6RCxRQUFRO0FBQ1I7QUFDQTtBQUNBLGNBQWMsMkNBQTJDO0FBQ3pELFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsU0FBUztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0RUFBNEU7QUFDNUU7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLDRDQUE0QztBQUNuRiwwQ0FBMEMsOENBQThDO0FBQ3hGO0FBQ0E7QUFDQSx5QkFBeUIsS0FBSyxrREFBa0QsRUFBRSxHQUFHO0FBQ3JGLDhCQUE4Qix1REFBdUQ7QUFDckYsOEJBQThCLHVEQUF1RDtBQUNyRiw4QkFBOEIsdURBQXVEO0FBQ3JGO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHlDQUF5QztBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHlDQUF5QztBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIseUNBQXlDO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIseUNBQXlDO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHdDQUF3QztBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHdDQUF3QztBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixvREFBb0Q7QUFDcEUsUUFBUTtBQUNSO0FBQ0E7QUFDQSxnQkFBZ0Isb0RBQW9EO0FBQ3BFLFFBQVE7QUFDUjtBQUNBO0FBQ0EsY0FBYyxvREFBb0Q7QUFDbEUsUUFBUTtBQUNSO0FBQ0E7QUFDQSxjQUFjLG9EQUFvRDtBQUNsRSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsRUFBRTtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQSxRQUFRLDJCQUEyQjtBQUNuQyxRQUFRLHdCQUF3QjtBQUNoQyxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsUUFBUSwyQkFBMkI7QUFDbkMsUUFBUSx3QkFBd0I7QUFDaEMsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLFFBQVEsMkJBQTJCO0FBQ25DLFFBQVEsd0JBQXdCO0FBQ2hDLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSwyQkFBMkI7QUFDbkMsUUFBUSx3QkFBd0I7QUFDaEMsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLEtBQUssa0VBQWtFO0FBQy9GO0FBQ0EsU0FBUyxLQUFLLGdEQUFnRDtBQUM5RDtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBSUUiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0XFxyZWFsY29kZVxcbm9kZV9tb2R1bGVzXFxtb25hY28tZWRpdG9yXFxlc21cXHZzXFxiYXNpYy1sYW5ndWFnZXNcXGVsaXhpclxcZWxpeGlyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi4gQWxsIHJpZ2h0cyByZXNlcnZlZC5cbiAqIFZlcnNpb246IDAuNTIuMig0MDQ1NDViZGVkMWRmNmZmYTQxZWEwYWY0ZThkZGIyMTkwMThjNmMxKVxuICogUmVsZWFzZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlXG4gKiBodHRwczovL2dpdGh1Yi5jb20vbWljcm9zb2Z0L21vbmFjby1lZGl0b3IvYmxvYi9tYWluL0xJQ0VOU0UudHh0XG4gKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuXG4vLyBzcmMvYmFzaWMtbGFuZ3VhZ2VzL2VsaXhpci9lbGl4aXIudHNcbnZhciBjb25mID0ge1xuICBjb21tZW50czoge1xuICAgIGxpbmVDb21tZW50OiBcIiNcIlxuICB9LFxuICBicmFja2V0czogW1xuICAgIFtcIntcIiwgXCJ9XCJdLFxuICAgIFtcIltcIiwgXCJdXCJdLFxuICAgIFtcIihcIiwgXCIpXCJdXG4gIF0sXG4gIHN1cnJvdW5kaW5nUGFpcnM6IFtcbiAgICB7IG9wZW46IFwie1wiLCBjbG9zZTogXCJ9XCIgfSxcbiAgICB7IG9wZW46IFwiW1wiLCBjbG9zZTogXCJdXCIgfSxcbiAgICB7IG9wZW46IFwiKFwiLCBjbG9zZTogXCIpXCIgfSxcbiAgICB7IG9wZW46IFwiJ1wiLCBjbG9zZTogXCInXCIgfSxcbiAgICB7IG9wZW46ICdcIicsIGNsb3NlOiAnXCInIH1cbiAgXSxcbiAgYXV0b0Nsb3NpbmdQYWlyczogW1xuICAgIHsgb3BlbjogXCInXCIsIGNsb3NlOiBcIidcIiwgbm90SW46IFtcInN0cmluZ1wiLCBcImNvbW1lbnRcIl0gfSxcbiAgICB7IG9wZW46ICdcIicsIGNsb3NlOiAnXCInLCBub3RJbjogW1wiY29tbWVudFwiXSB9LFxuICAgIHsgb3BlbjogJ1wiXCJcIicsIGNsb3NlOiAnXCJcIlwiJyB9LFxuICAgIHsgb3BlbjogXCJgXCIsIGNsb3NlOiBcImBcIiwgbm90SW46IFtcInN0cmluZ1wiLCBcImNvbW1lbnRcIl0gfSxcbiAgICB7IG9wZW46IFwiKFwiLCBjbG9zZTogXCIpXCIgfSxcbiAgICB7IG9wZW46IFwie1wiLCBjbG9zZTogXCJ9XCIgfSxcbiAgICB7IG9wZW46IFwiW1wiLCBjbG9zZTogXCJdXCIgfSxcbiAgICB7IG9wZW46IFwiPDxcIiwgY2xvc2U6IFwiPj5cIiB9XG4gIF0sXG4gIGluZGVudGF0aW9uUnVsZXM6IHtcbiAgICBpbmNyZWFzZUluZGVudFBhdHRlcm46IC9eXFxzKihhZnRlcnxlbHNlfGNhdGNofHJlc2N1ZXxmbnxbXiNdKihkb3w8XFwtfFxcLT58XFx7fFxcW3xcXD0pKVxccyokLyxcbiAgICBkZWNyZWFzZUluZGVudFBhdHRlcm46IC9eXFxzKigoXFx9fFxcXSlcXHMqJHwoYWZ0ZXJ8ZWxzZXxjYXRjaHxyZXNjdWV8ZW5kKVxcYikvXG4gIH1cbn07XG52YXIgbGFuZ3VhZ2UgPSB7XG4gIGRlZmF1bHRUb2tlbjogXCJzb3VyY2VcIixcbiAgdG9rZW5Qb3N0Zml4OiBcIi5lbGl4aXJcIixcbiAgYnJhY2tldHM6IFtcbiAgICB7IG9wZW46IFwiW1wiLCBjbG9zZTogXCJdXCIsIHRva2VuOiBcImRlbGltaXRlci5zcXVhcmVcIiB9LFxuICAgIHsgb3BlbjogXCIoXCIsIGNsb3NlOiBcIilcIiwgdG9rZW46IFwiZGVsaW1pdGVyLnBhcmVudGhlc2lzXCIgfSxcbiAgICB7IG9wZW46IFwie1wiLCBjbG9zZTogXCJ9XCIsIHRva2VuOiBcImRlbGltaXRlci5jdXJseVwiIH0sXG4gICAgeyBvcGVuOiBcIjw8XCIsIGNsb3NlOiBcIj4+XCIsIHRva2VuOiBcImRlbGltaXRlci5hbmdsZS5zcGVjaWFsXCIgfVxuICBdLFxuICAvLyBCZWxvdyBhcmUgbGlzdHMvcmVnZXhwcyB0byB3aGljaCB3ZSByZWZlcmVuY2UgbGF0ZXIuXG4gIGRlY2xhcmF0aW9uS2V5d29yZHM6IFtcbiAgICBcImRlZlwiLFxuICAgIFwiZGVmcFwiLFxuICAgIFwiZGVmblwiLFxuICAgIFwiZGVmbnBcIixcbiAgICBcImRlZmd1YXJkXCIsXG4gICAgXCJkZWZndWFyZHBcIixcbiAgICBcImRlZm1hY3JvXCIsXG4gICAgXCJkZWZtYWNyb3BcIixcbiAgICBcImRlZmRlbGVnYXRlXCIsXG4gICAgXCJkZWZjYWxsYmFja1wiLFxuICAgIFwiZGVmbWFjcm9jYWxsYmFja1wiLFxuICAgIFwiZGVmbW9kdWxlXCIsXG4gICAgXCJkZWZwcm90b2NvbFwiLFxuICAgIFwiZGVmZXhjZXB0aW9uXCIsXG4gICAgXCJkZWZpbXBsXCIsXG4gICAgXCJkZWZzdHJ1Y3RcIlxuICBdLFxuICBvcGVyYXRvcktleXdvcmRzOiBbXCJhbmRcIiwgXCJpblwiLCBcIm5vdFwiLCBcIm9yXCIsIFwid2hlblwiXSxcbiAgbmFtZXNwYWNlS2V5d29yZHM6IFtcImFsaWFzXCIsIFwiaW1wb3J0XCIsIFwicmVxdWlyZVwiLCBcInVzZVwiXSxcbiAgb3RoZXJLZXl3b3JkczogW1xuICAgIFwiYWZ0ZXJcIixcbiAgICBcImNhc2VcIixcbiAgICBcImNhdGNoXCIsXG4gICAgXCJjb25kXCIsXG4gICAgXCJkb1wiLFxuICAgIFwiZWxzZVwiLFxuICAgIFwiZW5kXCIsXG4gICAgXCJmblwiLFxuICAgIFwiZm9yXCIsXG4gICAgXCJpZlwiLFxuICAgIFwicXVvdGVcIixcbiAgICBcInJhaXNlXCIsXG4gICAgXCJyZWNlaXZlXCIsXG4gICAgXCJyZXNjdWVcIixcbiAgICBcInN1cGVyXCIsXG4gICAgXCJ0aHJvd1wiLFxuICAgIFwidHJ5XCIsXG4gICAgXCJ1bmxlc3NcIixcbiAgICBcInVucXVvdGVfc3BsaWNpbmdcIixcbiAgICBcInVucXVvdGVcIixcbiAgICBcIndpdGhcIlxuICBdLFxuICBjb25zdGFudHM6IFtcInRydWVcIiwgXCJmYWxzZVwiLCBcIm5pbFwiXSxcbiAgbmFtZUJ1aWx0aW46IFtcIl9fTU9EVUxFX19cIiwgXCJfX0RJUl9fXCIsIFwiX19FTlZfX1wiLCBcIl9fQ0FMTEVSX19cIiwgXCJfX1NUQUNLVFJBQ0VfX1wiXSxcbiAgLy8gTWF0Y2hlcyBhbnkgb2YgdGhlIG9wZXJhdG9yIG5hbWVzOlxuICAvLyA8PDwgPj4+IHx8fCAmJiYgXl5eIH5+fiA9PT0gIT09IH4+PiA8fj4gfH4+IDx8PiA9PSAhPSA8PSA+PSAmJiB8fCBcXFxcIDw+ICsrIC0tIHw+ID1+IC0+IDwtIH4+IDx+IDo6IC4uID0gPCA+ICsgLSAqIC8gfCAuIF4gJiAhXG4gIG9wZXJhdG9yOiAvLVstPl0/fCE9ezAsMn18XFwqezEsMn18XFwvfFxcXFxcXFxcfCZ7MSwzfXxcXC5cXC4/fFxcXig/OlxcXlxcXik/fFxcK1xcKz98PCg/Oi18PDx8PXw+fFxcfD58fj4/KT98PX58PXsxLDN9fD4oPzo9fD4+KT98XFx8fj58XFx8PnxcXHx7MSwzfXx+Pj4/fH5+fnw6Oi8sXG4gIC8vIFNlZSBodHRwczovL2hleGRvY3MucG0vZWxpeGlyL3N5bnRheC1yZWZlcmVuY2UuaHRtbCN2YXJpYWJsZXNcbiAgdmFyaWFibGVOYW1lOiAvW2Etel9dW2EtekEtWjAtOV9dKls/IV0/LyxcbiAgLy8gU2VlIGh0dHBzOi8vaGV4ZG9jcy5wbS9lbGl4aXIvc3ludGF4LXJlZmVyZW5jZS5odG1sI2F0b21zXG4gIGF0b21OYW1lOiAvW2EtekEtWl9dW2EtekEtWjAtOV9AXSpbPyFdP3xAc3BlY2lhbEF0b21OYW1lfEBvcGVyYXRvci8sXG4gIHNwZWNpYWxBdG9tTmFtZTogL1xcLlxcLlxcLnw8PD4+fCVcXHtcXH18JXxcXHtcXH0vLFxuICBhbGlhc1BhcnQ6IC9bQS1aXVthLXpBLVowLTlfXSovLFxuICBtb2R1bGVOYW1lOiAvQGFsaWFzUGFydCg/OlxcLkBhbGlhc1BhcnQpKi8sXG4gIC8vIFNpZ2lsIHBhaXJzIGFyZTogXCJcIlwiIFwiXCJcIiwgJycnICcnJywgXCIgXCIsICcgJywgLyAvLCB8IHwsIDwgPiwgeyB9LCBbIF0sICggKVxuICBzaWdpbFN5bW1ldHJpY0RlbGltaXRlcjogL1wiXCJcInwnJyd8XCJ8J3xcXC98XFx8LyxcbiAgc2lnaWxTdGFydERlbGltaXRlcjogL0BzaWdpbFN5bW1ldHJpY0RlbGltaXRlcnw8fFxce3xcXFt8XFwoLyxcbiAgc2lnaWxFbmREZWxpbWl0ZXI6IC9Ac2lnaWxTeW1tZXRyaWNEZWxpbWl0ZXJ8PnxcXH18XFxdfFxcKS8sXG4gIHNpZ2lsTW9kaWZpZXJzOiAvW2EtekEtWjAtOV0qLyxcbiAgZGVjaW1hbDogL1xcZCg/Ol8/XFxkKSovLFxuICBoZXg6IC9bMC05YS1mQS1GXShfP1swLTlhLWZBLUZdKSovLFxuICBvY3RhbDogL1swLTddKF8/WzAtN10pKi8sXG4gIGJpbmFyeTogL1swMV0oXz9bMDFdKSovLFxuICAvLyBTZWUgaHR0cHM6Ly9oZXhkb2NzLnBtL2VsaXhpci9tYXN0ZXIvU3RyaW5nLmh0bWwjbW9kdWxlLWVzY2FwZS1jaGFyYWN0ZXJzXG4gIGVzY2FwZTogL1xcXFx1WzAtOWEtZkEtRl17NH18XFxcXHhbMC05YS1mQS1GXXsyfXxcXFxcLi8sXG4gIC8vIFRoZSBrZXlzIGJlbG93IGNvcnJlc3BvbmQgdG8gdG9rZW5pemVyIHN0YXRlcy5cbiAgLy8gV2Ugc3RhcnQgZnJvbSB0aGUgcm9vdCBzdGF0ZSBhbmQgbWF0Y2ggYWdhaW5zdCBpdHMgcnVsZXNcbiAgLy8gdW50aWwgd2UgZXhwbGljaXRseSB0cmFuc2l0aW9uIGludG8gYW5vdGhlciBzdGF0ZS5cbiAgLy8gVGhlIGBpbmNsdWRlYCBzaW1wbHkgYnJpbmdzIGluIGFsbCBvcGVyYXRpb25zIGZyb20gdGhlIGdpdmVuIHN0YXRlXG4gIC8vIGFuZCBpcyB1c2VmdWwgZm9yIGltcHJvdmluZyByZWFkYWJpbGl0eS5cbiAgdG9rZW5pemVyOiB7XG4gICAgcm9vdDogW1xuICAgICAgeyBpbmNsdWRlOiBcIkB3aGl0ZXNwYWNlXCIgfSxcbiAgICAgIHsgaW5jbHVkZTogXCJAY29tbWVudHNcIiB9LFxuICAgICAgLy8gS2V5d29yZHMgc3RhcnQgYXMgZWl0aGVyIGFuIGlkZW50aWZpZXIgb3IgYSBzdHJpbmcsXG4gICAgICAvLyBidXQgZW5kIHdpdGggYSA6IHNvIGl0J3MgaW1wb3J0YW50IHRvIG1hdGNoIHRoaXMgZmlyc3QuXG4gICAgICB7IGluY2x1ZGU6IFwiQGtleXdvcmRzU2hvcnRoYW5kXCIgfSxcbiAgICAgIHsgaW5jbHVkZTogXCJAbnVtYmVyc1wiIH0sXG4gICAgICB7IGluY2x1ZGU6IFwiQGlkZW50aWZpZXJzXCIgfSxcbiAgICAgIHsgaW5jbHVkZTogXCJAc3RyaW5nc1wiIH0sXG4gICAgICB7IGluY2x1ZGU6IFwiQGF0b21zXCIgfSxcbiAgICAgIHsgaW5jbHVkZTogXCJAc2lnaWxzXCIgfSxcbiAgICAgIHsgaW5jbHVkZTogXCJAYXR0cmlidXRlc1wiIH0sXG4gICAgICB7IGluY2x1ZGU6IFwiQHN5bWJvbHNcIiB9XG4gICAgXSxcbiAgICAvLyBXaGl0ZXNwYWNlXG4gICAgd2hpdGVzcGFjZTogW1svXFxzKy8sIFwid2hpdGVcIl1dLFxuICAgIC8vIENvbW1lbnRzXG4gICAgY29tbWVudHM6IFtbLygjKSguKikvLCBbXCJjb21tZW50LnB1bmN0dWF0aW9uXCIsIFwiY29tbWVudFwiXV1dLFxuICAgIC8vIEtleXdvcmQgbGlzdCBzaG9ydGhhbmRcbiAgICBrZXl3b3Jkc1Nob3J0aGFuZDogW1xuICAgICAgWy8oQGF0b21OYW1lKSg6KShcXHMrKS8sIFtcImNvbnN0YW50XCIsIFwiY29uc3RhbnQucHVuY3R1YXRpb25cIiwgXCJ3aGl0ZVwiXV0sXG4gICAgICAvLyBVc2UgcG9zaXRpdmUgbG9vay1haGVhZCB0byBlbnN1cmUgdGhlIHN0cmluZyBpcyBmb2xsb3dlZCBieSA6XG4gICAgICAvLyBhbmQgc2hvdWxkIGJlIGNvbnNpZGVyZWQgYSBrZXl3b3JkLlxuICAgICAgW1xuICAgICAgICAvXCIoPz0oW15cIl18I1xcey4qP1xcfXxcXFxcXCIpKlwiOikvLFxuICAgICAgICB7IHRva2VuOiBcImNvbnN0YW50LmRlbGltaXRlclwiLCBuZXh0OiBcIkBkb3VibGVRdW90ZWRTdHJpbmdLZXl3b3JkXCIgfVxuICAgICAgXSxcbiAgICAgIFtcbiAgICAgICAgLycoPz0oW14nXXwjXFx7Lio/XFx9fFxcXFwnKSonOikvLFxuICAgICAgICB7IHRva2VuOiBcImNvbnN0YW50LmRlbGltaXRlclwiLCBuZXh0OiBcIkBzaW5nbGVRdW90ZWRTdHJpbmdLZXl3b3JkXCIgfVxuICAgICAgXVxuICAgIF0sXG4gICAgZG91YmxlUXVvdGVkU3RyaW5nS2V5d29yZDogW1xuICAgICAgWy9cIjovLCB7IHRva2VuOiBcImNvbnN0YW50LmRlbGltaXRlclwiLCBuZXh0OiBcIkBwb3BcIiB9XSxcbiAgICAgIHsgaW5jbHVkZTogXCJAc3RyaW5nQ29uc3RhbnRDb250ZW50SW50ZXJwb2xcIiB9XG4gICAgXSxcbiAgICBzaW5nbGVRdW90ZWRTdHJpbmdLZXl3b3JkOiBbXG4gICAgICBbLyc6LywgeyB0b2tlbjogXCJjb25zdGFudC5kZWxpbWl0ZXJcIiwgbmV4dDogXCJAcG9wXCIgfV0sXG4gICAgICB7IGluY2x1ZGU6IFwiQHN0cmluZ0NvbnN0YW50Q29udGVudEludGVycG9sXCIgfVxuICAgIF0sXG4gICAgLy8gTnVtYmVyc1xuICAgIG51bWJlcnM6IFtcbiAgICAgIFsvMGJAYmluYXJ5LywgXCJudW1iZXIuYmluYXJ5XCJdLFxuICAgICAgWy8wb0BvY3RhbC8sIFwibnVtYmVyLm9jdGFsXCJdLFxuICAgICAgWy8weEBoZXgvLCBcIm51bWJlci5oZXhcIl0sXG4gICAgICBbL0BkZWNpbWFsXFwuQGRlY2ltYWwoW2VFXS0/QGRlY2ltYWwpPy8sIFwibnVtYmVyLmZsb2F0XCJdLFxuICAgICAgWy9AZGVjaW1hbC8sIFwibnVtYmVyXCJdXG4gICAgXSxcbiAgICAvLyBJZGVudGlmaWVyc1xuICAgIGlkZW50aWZpZXJzOiBbXG4gICAgICAvLyBUb2tlbml6ZSBpZGVudGlmaWVyIG5hbWUgaW4gZnVuY3Rpb24tbGlrZSBkZWZpbml0aW9ucy5cbiAgICAgIC8vIE5vdGU6IGdpdmVuIGBkZWYgYSArIGIsIGRvOiBuaWxgLCBgYWAgaXMgbm90IGEgZnVuY3Rpb24gbmFtZSxcbiAgICAgIC8vIHNvIHdlIHVzZSBuZWdhdGl2ZSBsb29rLWFoZWFkIHRvIGVuc3VyZSB0aGVyZSdzIG5vIG9wZXJhdG9yLlxuICAgICAgW1xuICAgICAgICAvXFxiKGRlZnA/fGRlZm5wP3xkZWZtYWNyb3A/fGRlZmd1YXJkcD98ZGVmZGVsZWdhdGUpKFxccyspKEB2YXJpYWJsZU5hbWUpKD8hXFxzK0BvcGVyYXRvcikvLFxuICAgICAgICBbXG4gICAgICAgICAgXCJrZXl3b3JkLmRlY2xhcmF0aW9uXCIsXG4gICAgICAgICAgXCJ3aGl0ZVwiLFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIGNhc2VzOiB7XG4gICAgICAgICAgICAgIHVucXVvdGU6IFwia2V5d29yZFwiLFxuICAgICAgICAgICAgICBcIkBkZWZhdWx0XCI6IFwiZnVuY3Rpb25cIlxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgXVxuICAgICAgXSxcbiAgICAgIC8vIFRva2VuaXplIGZ1bmN0aW9uIGNhbGxzXG4gICAgICBbXG4gICAgICAgIC8vIEluLXNjb3BlIGNhbGwgLSBhbiBpZGVudGlmaWVyIGZvbGxvd2VkIGJ5ICggb3IgLihcbiAgICAgICAgLyhAdmFyaWFibGVOYW1lKSg/PVxccypcXC4/XFxzKlxcKCkvLFxuICAgICAgICB7XG4gICAgICAgICAgY2FzZXM6IHtcbiAgICAgICAgICAgIC8vIFRva2VuaXplIGFzIGtleXdvcmQgaW4gY2FzZXMgbGlrZSBgaWYoLi4uLCBkbzogLi4uLCBlbHNlOiAuLi4pYFxuICAgICAgICAgICAgXCJAZGVjbGFyYXRpb25LZXl3b3Jkc1wiOiBcImtleXdvcmQuZGVjbGFyYXRpb25cIixcbiAgICAgICAgICAgIFwiQG5hbWVzcGFjZUtleXdvcmRzXCI6IFwia2V5d29yZFwiLFxuICAgICAgICAgICAgXCJAb3RoZXJLZXl3b3Jkc1wiOiBcImtleXdvcmRcIixcbiAgICAgICAgICAgIFwiQGRlZmF1bHRcIjogXCJmdW5jdGlvbi5jYWxsXCJcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIF0sXG4gICAgICBbXG4gICAgICAgIC8vIFJlZmVyZW5jaW5nIGZ1bmN0aW9uIGluIGEgbW9kdWxlXG4gICAgICAgIC8oQG1vZHVsZU5hbWUpKFxccyopKFxcLikoXFxzKikoQHZhcmlhYmxlTmFtZSkvLFxuICAgICAgICBbXCJ0eXBlLmlkZW50aWZpZXJcIiwgXCJ3aGl0ZVwiLCBcIm9wZXJhdG9yXCIsIFwid2hpdGVcIiwgXCJmdW5jdGlvbi5jYWxsXCJdXG4gICAgICBdLFxuICAgICAgW1xuICAgICAgICAvLyBSZWZlcmVuY2luZyBmdW5jdGlvbiBpbiBhbiBFcmxhbmcgbW9kdWxlXG4gICAgICAgIC8oOikoQGF0b21OYW1lKShcXHMqKShcXC4pKFxccyopKEB2YXJpYWJsZU5hbWUpLyxcbiAgICAgICAgW1wiY29uc3RhbnQucHVuY3R1YXRpb25cIiwgXCJjb25zdGFudFwiLCBcIndoaXRlXCIsIFwib3BlcmF0b3JcIiwgXCJ3aGl0ZVwiLCBcImZ1bmN0aW9uLmNhbGxcIl1cbiAgICAgIF0sXG4gICAgICBbXG4gICAgICAgIC8vIFBpcGluZyBpbnRvIGEgZnVuY3Rpb24gKHRva2VuaXplZCBzZXBhcmF0ZWx5IGFzIGl0IG1heSBub3QgaGF2ZSBwYXJlbnRoZXNlcylcbiAgICAgICAgLyhcXHw+KShcXHMqKShAdmFyaWFibGVOYW1lKS8sXG4gICAgICAgIFtcbiAgICAgICAgICBcIm9wZXJhdG9yXCIsXG4gICAgICAgICAgXCJ3aGl0ZVwiLFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIGNhc2VzOiB7XG4gICAgICAgICAgICAgIFwiQG90aGVyS2V5d29yZHNcIjogXCJrZXl3b3JkXCIsXG4gICAgICAgICAgICAgIFwiQGRlZmF1bHRcIjogXCJmdW5jdGlvbi5jYWxsXCJcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIF1cbiAgICAgIF0sXG4gICAgICBbXG4gICAgICAgIC8vIEZ1bmN0aW9uIHJlZmVyZW5jZSBwYXNzZWQgdG8gYW5vdGhlciBmdW5jdGlvblxuICAgICAgICAvKCYpKFxccyopKEB2YXJpYWJsZU5hbWUpLyxcbiAgICAgICAgW1wib3BlcmF0b3JcIiwgXCJ3aGl0ZVwiLCBcImZ1bmN0aW9uLmNhbGxcIl1cbiAgICAgIF0sXG4gICAgICAvLyBMYW5ndWFnZSBrZXl3b3JkcywgYnVpbHRpbnMsIGNvbnN0YW50cyBhbmQgdmFyaWFibGVzXG4gICAgICBbXG4gICAgICAgIC9AdmFyaWFibGVOYW1lLyxcbiAgICAgICAge1xuICAgICAgICAgIGNhc2VzOiB7XG4gICAgICAgICAgICBcIkBkZWNsYXJhdGlvbktleXdvcmRzXCI6IFwia2V5d29yZC5kZWNsYXJhdGlvblwiLFxuICAgICAgICAgICAgXCJAb3BlcmF0b3JLZXl3b3Jkc1wiOiBcImtleXdvcmQub3BlcmF0b3JcIixcbiAgICAgICAgICAgIFwiQG5hbWVzcGFjZUtleXdvcmRzXCI6IFwia2V5d29yZFwiLFxuICAgICAgICAgICAgXCJAb3RoZXJLZXl3b3Jkc1wiOiBcImtleXdvcmRcIixcbiAgICAgICAgICAgIFwiQGNvbnN0YW50c1wiOiBcImNvbnN0YW50Lmxhbmd1YWdlXCIsXG4gICAgICAgICAgICBcIkBuYW1lQnVpbHRpblwiOiBcInZhcmlhYmxlLmxhbmd1YWdlXCIsXG4gICAgICAgICAgICBcIl8uKlwiOiBcImNvbW1lbnQudW51c2VkXCIsXG4gICAgICAgICAgICBcIkBkZWZhdWx0XCI6IFwiaWRlbnRpZmllclwiXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgLy8gTW9kdWxlIG5hbWVzXG4gICAgICBbL0Btb2R1bGVOYW1lLywgXCJ0eXBlLmlkZW50aWZpZXJcIl1cbiAgICBdLFxuICAgIC8vIFN0cmluZ3NcbiAgICBzdHJpbmdzOiBbXG4gICAgICBbL1wiXCJcIi8sIHsgdG9rZW46IFwic3RyaW5nLmRlbGltaXRlclwiLCBuZXh0OiBcIkBkb3VibGVRdW90ZWRIZXJlZG9jXCIgfV0sXG4gICAgICBbLycnJy8sIHsgdG9rZW46IFwic3RyaW5nLmRlbGltaXRlclwiLCBuZXh0OiBcIkBzaW5nbGVRdW90ZWRIZXJlZG9jXCIgfV0sXG4gICAgICBbL1wiLywgeyB0b2tlbjogXCJzdHJpbmcuZGVsaW1pdGVyXCIsIG5leHQ6IFwiQGRvdWJsZVF1b3RlZFN0cmluZ1wiIH1dLFxuICAgICAgWy8nLywgeyB0b2tlbjogXCJzdHJpbmcuZGVsaW1pdGVyXCIsIG5leHQ6IFwiQHNpbmdsZVF1b3RlZFN0cmluZ1wiIH1dXG4gICAgXSxcbiAgICBkb3VibGVRdW90ZWRIZXJlZG9jOiBbXG4gICAgICBbL1wiXCJcIi8sIHsgdG9rZW46IFwic3RyaW5nLmRlbGltaXRlclwiLCBuZXh0OiBcIkBwb3BcIiB9XSxcbiAgICAgIHsgaW5jbHVkZTogXCJAc3RyaW5nQ29udGVudEludGVycG9sXCIgfVxuICAgIF0sXG4gICAgc2luZ2xlUXVvdGVkSGVyZWRvYzogW1xuICAgICAgWy8nJycvLCB7IHRva2VuOiBcInN0cmluZy5kZWxpbWl0ZXJcIiwgbmV4dDogXCJAcG9wXCIgfV0sXG4gICAgICB7IGluY2x1ZGU6IFwiQHN0cmluZ0NvbnRlbnRJbnRlcnBvbFwiIH1cbiAgICBdLFxuICAgIGRvdWJsZVF1b3RlZFN0cmluZzogW1xuICAgICAgWy9cIi8sIHsgdG9rZW46IFwic3RyaW5nLmRlbGltaXRlclwiLCBuZXh0OiBcIkBwb3BcIiB9XSxcbiAgICAgIHsgaW5jbHVkZTogXCJAc3RyaW5nQ29udGVudEludGVycG9sXCIgfVxuICAgIF0sXG4gICAgc2luZ2xlUXVvdGVkU3RyaW5nOiBbXG4gICAgICBbLycvLCB7IHRva2VuOiBcInN0cmluZy5kZWxpbWl0ZXJcIiwgbmV4dDogXCJAcG9wXCIgfV0sXG4gICAgICB7IGluY2x1ZGU6IFwiQHN0cmluZ0NvbnRlbnRJbnRlcnBvbFwiIH1cbiAgICBdLFxuICAgIC8vIEF0b21zXG4gICAgYXRvbXM6IFtcbiAgICAgIFsvKDopKEBhdG9tTmFtZSkvLCBbXCJjb25zdGFudC5wdW5jdHVhdGlvblwiLCBcImNvbnN0YW50XCJdXSxcbiAgICAgIFsvOlwiLywgeyB0b2tlbjogXCJjb25zdGFudC5kZWxpbWl0ZXJcIiwgbmV4dDogXCJAZG91YmxlUXVvdGVkU3RyaW5nQXRvbVwiIH1dLFxuICAgICAgWy86Jy8sIHsgdG9rZW46IFwiY29uc3RhbnQuZGVsaW1pdGVyXCIsIG5leHQ6IFwiQHNpbmdsZVF1b3RlZFN0cmluZ0F0b21cIiB9XVxuICAgIF0sXG4gICAgZG91YmxlUXVvdGVkU3RyaW5nQXRvbTogW1xuICAgICAgWy9cIi8sIHsgdG9rZW46IFwiY29uc3RhbnQuZGVsaW1pdGVyXCIsIG5leHQ6IFwiQHBvcFwiIH1dLFxuICAgICAgeyBpbmNsdWRlOiBcIkBzdHJpbmdDb25zdGFudENvbnRlbnRJbnRlcnBvbFwiIH1cbiAgICBdLFxuICAgIHNpbmdsZVF1b3RlZFN0cmluZ0F0b206IFtcbiAgICAgIFsvJy8sIHsgdG9rZW46IFwiY29uc3RhbnQuZGVsaW1pdGVyXCIsIG5leHQ6IFwiQHBvcFwiIH1dLFxuICAgICAgeyBpbmNsdWRlOiBcIkBzdHJpbmdDb25zdGFudENvbnRlbnRJbnRlcnBvbFwiIH1cbiAgICBdLFxuICAgIC8vIFNpZ2lsc1xuICAgIC8vIFNlZSBodHRwczovL2VsaXhpci1sYW5nLm9yZy9nZXR0aW5nLXN0YXJ0ZWQvc2lnaWxzLmh0bWxcbiAgICAvLyBTaWdpbHMgYWxsb3cgZm9yIHR5cGluZyB2YWx1ZXMgdXNpbmcgdGhlaXIgdGV4dHVhbCByZXByZXNlbnRhdGlvbi5cbiAgICAvLyBBbGwgc2lnaWxzIHN0YXJ0IHdpdGggfiBmb2xsb3dlZCBieSBhIGxldHRlciBvclxuICAgIC8vIG11bHRpLWxldHRlciB1cHBlcmNhc2Ugc3RhcnRpbmcgYXQgRWxpeGlyIHYxLjE1LjAsIGluZGljYXRpbmcgc2lnaWwgdHlwZVxuICAgIC8vIGFuZCB0aGVuIGEgZGVsaW1pdGVyIHBhaXIgZW5jbG9zaW5nIHRoZSB0ZXh0dWFsIHJlcHJlc2VudGF0aW9uLlxuICAgIC8vIE9wdGlvbmFsIG1vZGlmaWVycyBhcmUgYWxsb3dlZCBhZnRlciB0aGUgY2xvc2luZyBkZWxpbWl0ZXIuXG4gICAgLy8gRm9yIGluc3RhbmNlIGEgcmVndWxhciBleHByZXNzaW9ucyBjYW4gYmUgd3JpdHRlbiBhczpcbiAgICAvLyB+ci9mb298YmFyLyB+cntmb298YmFyfSB+ci9mb298YmFyL2dcbiAgICAvL1xuICAgIC8vIEluIGdlbmVyYWwgbG93ZXJjYXNlIHNpZ2lscyBhbGxvdyBmb3IgaW50ZXJwb2xhdGlvblxuICAgIC8vIGFuZCBlc2NhcGVkIGNoYXJhY3RlcnMsIHdoZXJlYXMgdXBwZXJjYXNlIHNpZ2lscyBkb24ndFxuICAgIC8vXG4gICAgLy8gRHVyaW5nIHRva2VuaXphdGlvbiB3ZSB3YW50IHRvIGRpc3Rpbmd1aXNoIHNvbWVcbiAgICAvLyBzcGVjaWZpYyBzaWdpbCB0eXBlcywgbmFtZWx5IHN0cmluZyBhbmQgcmVnZXhwLFxuICAgIC8vIHNvIHRoYXQgdGhleSBjZW4gYmUgdGhlbWVkIHNlcGFyYXRlbHkuXG4gICAgLy9cbiAgICAvLyBUbyByZWFzb25hYmx5IGhhbmRsZSBhbGwgdGhvc2UgY29tYmluYXRpb25zIHdlIGxldmVyYWdlXG4gICAgLy8gZG90LXNlcGFyYXRlZCBzdGF0ZXMsIHNvIGlmIHdlIHRyYW5zaXRpb24gdG8gQHNpZ2lsU3RhcnQuaW50ZXJwb2wucy57Ln1cbiAgICAvLyB0aGVuIFwic2lnaWxTdGFydC5pbnRlcnBvbC5zXCIgc3RhdGUgd2lsbCBtYXRjaCBhbmQgYWxzbyBhbGxcbiAgICAvLyB0aGUgaW5kaXZpZHVhbCBkb3Qtc2VwYXJhdGVkIHBhcmFtZXRlcnMgY2FuIGJlIGFjY2Vzc2VkLlxuICAgIHNpZ2lsczogW1xuICAgICAgWy9+W2Etel1Ac2lnaWxTdGFydERlbGltaXRlci8sIHsgdG9rZW46IFwiQHJlbWF0Y2hcIiwgbmV4dDogXCJAc2lnaWwuaW50ZXJwb2xcIiB9XSxcbiAgICAgIFsvfihbQS1aXSspQHNpZ2lsU3RhcnREZWxpbWl0ZXIvLCB7IHRva2VuOiBcIkByZW1hdGNoXCIsIG5leHQ6IFwiQHNpZ2lsLm5vSW50ZXJwb2xcIiB9XVxuICAgIF0sXG4gICAgc2lnaWw6IFtcbiAgICAgIFsvfihbYS16XXxbQS1aXSspXFx7LywgeyB0b2tlbjogXCJAcmVtYXRjaFwiLCBzd2l0Y2hUbzogXCJAc2lnaWxTdGFydC4kUzIuJDEuey59XCIgfV0sXG4gICAgICBbL34oW2Etel18W0EtWl0rKVxcWy8sIHsgdG9rZW46IFwiQHJlbWF0Y2hcIiwgc3dpdGNoVG86IFwiQHNpZ2lsU3RhcnQuJFMyLiQxLlsuXVwiIH1dLFxuICAgICAgWy9+KFthLXpdfFtBLVpdKylcXCgvLCB7IHRva2VuOiBcIkByZW1hdGNoXCIsIHN3aXRjaFRvOiBcIkBzaWdpbFN0YXJ0LiRTMi4kMS4oLilcIiB9XSxcbiAgICAgIFsvfihbYS16XXxbQS1aXSspXFw8LywgeyB0b2tlbjogXCJAcmVtYXRjaFwiLCBzd2l0Y2hUbzogXCJAc2lnaWxTdGFydC4kUzIuJDEuPC4+XCIgfV0sXG4gICAgICBbXG4gICAgICAgIC9+KFthLXpdfFtBLVpdKykoQHNpZ2lsU3ltbWV0cmljRGVsaW1pdGVyKS8sXG4gICAgICAgIHsgdG9rZW46IFwiQHJlbWF0Y2hcIiwgc3dpdGNoVG86IFwiQHNpZ2lsU3RhcnQuJFMyLiQxLiQyLiQyXCIgfVxuICAgICAgXVxuICAgIF0sXG4gICAgLy8gVGhlIGRlZmluaXRpb25zIGJlbG93IGV4cGVjdCBzdGF0ZXMgdG8gYmUgb2YgdGhlIGZvcm06XG4gICAgLy9cbiAgICAvLyBzaWdpbFN0YXJ0LjxpbnRlcnBvbC1vci1ub0ludGVycG9sPi48c2lnaWwtbGV0dGVyPi48c3RhcnQtZGVsaW1pdGVyPi48ZW5kLWRlbGltaXRlcj5cbiAgICAvLyBzaWdpbENvbnRpbnVlLjxpbnRlcnBvbC1vci1ub0ludGVycG9sPi48c2lnaWwtbGV0dGVyPi48c3RhcnQtZGVsaW1pdGVyPi48ZW5kLWRlbGltaXRlcj5cbiAgICAvL1xuICAgIC8vIFRoZSBzaWdpbFN0YXJ0IHN0YXRlIGlzIHVzZWQgb25seSB0byBwcm9wZXJseSBjbGFzc2lmeSB0aGUgdG9rZW4gKGFzIHN0cmluZy9yZWdleC9zaWdpbClcbiAgICAvLyBhbmQgaW1tZWRpYXRlbHkgc3dpdGNoZXMgdG8gdGhlIHNpZ2lsQ29udGludWUgc2F0ZSwgd2hpY2ggaGFuZGxlcyB0aGUgYWN0dWFsIGNvbnRlbnRcbiAgICAvLyBhbmQgd2FpdHMgZm9yIHRoZSBjb3JyZXNwb25kaW5nIGVuZCBkZWxpbWl0ZXIuXG4gICAgXCJzaWdpbFN0YXJ0LmludGVycG9sLnNcIjogW1xuICAgICAgW1xuICAgICAgICAvfnNAc2lnaWxTdGFydERlbGltaXRlci8sXG4gICAgICAgIHtcbiAgICAgICAgICB0b2tlbjogXCJzdHJpbmcuZGVsaW1pdGVyXCIsXG4gICAgICAgICAgc3dpdGNoVG86IFwiQHNpZ2lsQ29udGludWUuJFMyLiRTMy4kUzQuJFM1XCJcbiAgICAgICAgfVxuICAgICAgXVxuICAgIF0sXG4gICAgXCJzaWdpbENvbnRpbnVlLmludGVycG9sLnNcIjogW1xuICAgICAgW1xuICAgICAgICAvKEBzaWdpbEVuZERlbGltaXRlcilAc2lnaWxNb2RpZmllcnMvLFxuICAgICAgICB7XG4gICAgICAgICAgY2FzZXM6IHtcbiAgICAgICAgICAgIFwiJDE9PSRTNVwiOiB7IHRva2VuOiBcInN0cmluZy5kZWxpbWl0ZXJcIiwgbmV4dDogXCJAcG9wXCIgfSxcbiAgICAgICAgICAgIFwiQGRlZmF1bHRcIjogXCJzdHJpbmdcIlxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIHsgaW5jbHVkZTogXCJAc3RyaW5nQ29udGVudEludGVycG9sXCIgfVxuICAgIF0sXG4gICAgXCJzaWdpbFN0YXJ0Lm5vSW50ZXJwb2wuU1wiOiBbXG4gICAgICBbXG4gICAgICAgIC9+U0BzaWdpbFN0YXJ0RGVsaW1pdGVyLyxcbiAgICAgICAge1xuICAgICAgICAgIHRva2VuOiBcInN0cmluZy5kZWxpbWl0ZXJcIixcbiAgICAgICAgICBzd2l0Y2hUbzogXCJAc2lnaWxDb250aW51ZS4kUzIuJFMzLiRTNC4kUzVcIlxuICAgICAgICB9XG4gICAgICBdXG4gICAgXSxcbiAgICBcInNpZ2lsQ29udGludWUubm9JbnRlcnBvbC5TXCI6IFtcbiAgICAgIC8vIElnbm9yZSBlc2NhcGVkIHNpZ2lsIGVuZFxuICAgICAgWy8oXnxbXlxcXFxdKVxcXFxAc2lnaWxFbmREZWxpbWl0ZXIvLCBcInN0cmluZ1wiXSxcbiAgICAgIFtcbiAgICAgICAgLyhAc2lnaWxFbmREZWxpbWl0ZXIpQHNpZ2lsTW9kaWZpZXJzLyxcbiAgICAgICAge1xuICAgICAgICAgIGNhc2VzOiB7XG4gICAgICAgICAgICBcIiQxPT0kUzVcIjogeyB0b2tlbjogXCJzdHJpbmcuZGVsaW1pdGVyXCIsIG5leHQ6IFwiQHBvcFwiIH0sXG4gICAgICAgICAgICBcIkBkZWZhdWx0XCI6IFwic3RyaW5nXCJcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIF0sXG4gICAgICB7IGluY2x1ZGU6IFwiQHN0cmluZ0NvbnRlbnRcIiB9XG4gICAgXSxcbiAgICBcInNpZ2lsU3RhcnQuaW50ZXJwb2wuclwiOiBbXG4gICAgICBbXG4gICAgICAgIC9+ckBzaWdpbFN0YXJ0RGVsaW1pdGVyLyxcbiAgICAgICAge1xuICAgICAgICAgIHRva2VuOiBcInJlZ2V4cC5kZWxpbWl0ZXJcIixcbiAgICAgICAgICBzd2l0Y2hUbzogXCJAc2lnaWxDb250aW51ZS4kUzIuJFMzLiRTNC4kUzVcIlxuICAgICAgICB9XG4gICAgICBdXG4gICAgXSxcbiAgICBcInNpZ2lsQ29udGludWUuaW50ZXJwb2wuclwiOiBbXG4gICAgICBbXG4gICAgICAgIC8oQHNpZ2lsRW5kRGVsaW1pdGVyKUBzaWdpbE1vZGlmaWVycy8sXG4gICAgICAgIHtcbiAgICAgICAgICBjYXNlczoge1xuICAgICAgICAgICAgXCIkMT09JFM1XCI6IHsgdG9rZW46IFwicmVnZXhwLmRlbGltaXRlclwiLCBuZXh0OiBcIkBwb3BcIiB9LFxuICAgICAgICAgICAgXCJAZGVmYXVsdFwiOiBcInJlZ2V4cFwiXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgeyBpbmNsdWRlOiBcIkByZWdleHBDb250ZW50SW50ZXJwb2xcIiB9XG4gICAgXSxcbiAgICBcInNpZ2lsU3RhcnQubm9JbnRlcnBvbC5SXCI6IFtcbiAgICAgIFtcbiAgICAgICAgL35SQHNpZ2lsU3RhcnREZWxpbWl0ZXIvLFxuICAgICAgICB7XG4gICAgICAgICAgdG9rZW46IFwicmVnZXhwLmRlbGltaXRlclwiLFxuICAgICAgICAgIHN3aXRjaFRvOiBcIkBzaWdpbENvbnRpbnVlLiRTMi4kUzMuJFM0LiRTNVwiXG4gICAgICAgIH1cbiAgICAgIF1cbiAgICBdLFxuICAgIFwic2lnaWxDb250aW51ZS5ub0ludGVycG9sLlJcIjogW1xuICAgICAgLy8gSWdub3JlIGVzY2FwZWQgc2lnaWwgZW5kXG4gICAgICBbLyhefFteXFxcXF0pXFxcXEBzaWdpbEVuZERlbGltaXRlci8sIFwicmVnZXhwXCJdLFxuICAgICAgW1xuICAgICAgICAvKEBzaWdpbEVuZERlbGltaXRlcilAc2lnaWxNb2RpZmllcnMvLFxuICAgICAgICB7XG4gICAgICAgICAgY2FzZXM6IHtcbiAgICAgICAgICAgIFwiJDE9PSRTNVwiOiB7IHRva2VuOiBcInJlZ2V4cC5kZWxpbWl0ZXJcIiwgbmV4dDogXCJAcG9wXCIgfSxcbiAgICAgICAgICAgIFwiQGRlZmF1bHRcIjogXCJyZWdleHBcIlxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIHsgaW5jbHVkZTogXCJAcmVnZXhwQ29udGVudFwiIH1cbiAgICBdLFxuICAgIC8vIEZhbGxiYWNrIHRvIHRoZSBnZW5lcmljIHNpZ2lsIGJ5IGRlZmF1bHRcbiAgICBcInNpZ2lsU3RhcnQuaW50ZXJwb2xcIjogW1xuICAgICAgW1xuICAgICAgICAvfihbYS16XXxbQS1aXSspQHNpZ2lsU3RhcnREZWxpbWl0ZXIvLFxuICAgICAgICB7XG4gICAgICAgICAgdG9rZW46IFwic2lnaWwuZGVsaW1pdGVyXCIsXG4gICAgICAgICAgc3dpdGNoVG86IFwiQHNpZ2lsQ29udGludWUuJFMyLiRTMy4kUzQuJFM1XCJcbiAgICAgICAgfVxuICAgICAgXVxuICAgIF0sXG4gICAgXCJzaWdpbENvbnRpbnVlLmludGVycG9sXCI6IFtcbiAgICAgIFtcbiAgICAgICAgLyhAc2lnaWxFbmREZWxpbWl0ZXIpQHNpZ2lsTW9kaWZpZXJzLyxcbiAgICAgICAge1xuICAgICAgICAgIGNhc2VzOiB7XG4gICAgICAgICAgICBcIiQxPT0kUzVcIjogeyB0b2tlbjogXCJzaWdpbC5kZWxpbWl0ZXJcIiwgbmV4dDogXCJAcG9wXCIgfSxcbiAgICAgICAgICAgIFwiQGRlZmF1bHRcIjogXCJzaWdpbFwiXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgeyBpbmNsdWRlOiBcIkBzaWdpbENvbnRlbnRJbnRlcnBvbFwiIH1cbiAgICBdLFxuICAgIFwic2lnaWxTdGFydC5ub0ludGVycG9sXCI6IFtcbiAgICAgIFtcbiAgICAgICAgL34oW2Etel18W0EtWl0rKUBzaWdpbFN0YXJ0RGVsaW1pdGVyLyxcbiAgICAgICAge1xuICAgICAgICAgIHRva2VuOiBcInNpZ2lsLmRlbGltaXRlclwiLFxuICAgICAgICAgIHN3aXRjaFRvOiBcIkBzaWdpbENvbnRpbnVlLiRTMi4kUzMuJFM0LiRTNVwiXG4gICAgICAgIH1cbiAgICAgIF1cbiAgICBdLFxuICAgIFwic2lnaWxDb250aW51ZS5ub0ludGVycG9sXCI6IFtcbiAgICAgIC8vIElnbm9yZSBlc2NhcGVkIHNpZ2lsIGVuZFxuICAgICAgWy8oXnxbXlxcXFxdKVxcXFxAc2lnaWxFbmREZWxpbWl0ZXIvLCBcInNpZ2lsXCJdLFxuICAgICAgW1xuICAgICAgICAvKEBzaWdpbEVuZERlbGltaXRlcilAc2lnaWxNb2RpZmllcnMvLFxuICAgICAgICB7XG4gICAgICAgICAgY2FzZXM6IHtcbiAgICAgICAgICAgIFwiJDE9PSRTNVwiOiB7IHRva2VuOiBcInNpZ2lsLmRlbGltaXRlclwiLCBuZXh0OiBcIkBwb3BcIiB9LFxuICAgICAgICAgICAgXCJAZGVmYXVsdFwiOiBcInNpZ2lsXCJcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIF0sXG4gICAgICB7IGluY2x1ZGU6IFwiQHNpZ2lsQ29udGVudFwiIH1cbiAgICBdLFxuICAgIC8vIEF0dHJpYnV0ZXNcbiAgICBhdHRyaWJ1dGVzOiBbXG4gICAgICAvLyBNb2R1bGUgQGRvYyogYXR0cmlidXRlcyAtIHRva2VuaXplZCBhcyBjb21tZW50c1xuICAgICAgW1xuICAgICAgICAvXFxAKG1vZHVsZXx0eXBlKT9kb2MgKH5bc1NdKT9cIlwiXCIvLFxuICAgICAgICB7XG4gICAgICAgICAgdG9rZW46IFwiY29tbWVudC5ibG9jay5kb2N1bWVudGF0aW9uXCIsXG4gICAgICAgICAgbmV4dDogXCJAZG91YmxlUXVvdGVkSGVyZWRvY0RvY3N0cmluZ1wiXG4gICAgICAgIH1cbiAgICAgIF0sXG4gICAgICBbXG4gICAgICAgIC9cXEAobW9kdWxlfHR5cGUpP2RvYyAofltzU10pPycnJy8sXG4gICAgICAgIHtcbiAgICAgICAgICB0b2tlbjogXCJjb21tZW50LmJsb2NrLmRvY3VtZW50YXRpb25cIixcbiAgICAgICAgICBuZXh0OiBcIkBzaW5nbGVRdW90ZWRIZXJlZG9jRG9jc3RyaW5nXCJcbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIFtcbiAgICAgICAgL1xcQChtb2R1bGV8dHlwZSk/ZG9jICh+W3NTXSk/XCIvLFxuICAgICAgICB7XG4gICAgICAgICAgdG9rZW46IFwiY29tbWVudC5ibG9jay5kb2N1bWVudGF0aW9uXCIsXG4gICAgICAgICAgbmV4dDogXCJAZG91YmxlUXVvdGVkU3RyaW5nRG9jc3RyaW5nXCJcbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIFtcbiAgICAgICAgL1xcQChtb2R1bGV8dHlwZSk/ZG9jICh+W3NTXSk/Jy8sXG4gICAgICAgIHtcbiAgICAgICAgICB0b2tlbjogXCJjb21tZW50LmJsb2NrLmRvY3VtZW50YXRpb25cIixcbiAgICAgICAgICBuZXh0OiBcIkBzaW5nbGVRdW90ZWRTdHJpbmdEb2NzdHJpbmdcIlxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgWy9cXEAobW9kdWxlfHR5cGUpP2RvYyBmYWxzZS8sIFwiY29tbWVudC5ibG9jay5kb2N1bWVudGF0aW9uXCJdLFxuICAgICAgLy8gTW9kdWxlIGF0dHJpYnV0ZXNcbiAgICAgIFsvXFxAKEB2YXJpYWJsZU5hbWUpLywgXCJ2YXJpYWJsZVwiXVxuICAgIF0sXG4gICAgZG91YmxlUXVvdGVkSGVyZWRvY0RvY3N0cmluZzogW1xuICAgICAgWy9cIlwiXCIvLCB7IHRva2VuOiBcImNvbW1lbnQuYmxvY2suZG9jdW1lbnRhdGlvblwiLCBuZXh0OiBcIkBwb3BcIiB9XSxcbiAgICAgIHsgaW5jbHVkZTogXCJAZG9jc3RyaW5nQ29udGVudFwiIH1cbiAgICBdLFxuICAgIHNpbmdsZVF1b3RlZEhlcmVkb2NEb2NzdHJpbmc6IFtcbiAgICAgIFsvJycnLywgeyB0b2tlbjogXCJjb21tZW50LmJsb2NrLmRvY3VtZW50YXRpb25cIiwgbmV4dDogXCJAcG9wXCIgfV0sXG4gICAgICB7IGluY2x1ZGU6IFwiQGRvY3N0cmluZ0NvbnRlbnRcIiB9XG4gICAgXSxcbiAgICBkb3VibGVRdW90ZWRTdHJpbmdEb2NzdHJpbmc6IFtcbiAgICAgIFsvXCIvLCB7IHRva2VuOiBcImNvbW1lbnQuYmxvY2suZG9jdW1lbnRhdGlvblwiLCBuZXh0OiBcIkBwb3BcIiB9XSxcbiAgICAgIHsgaW5jbHVkZTogXCJAZG9jc3RyaW5nQ29udGVudFwiIH1cbiAgICBdLFxuICAgIHNpbmdsZVF1b3RlZFN0cmluZ0RvY3N0cmluZzogW1xuICAgICAgWy8nLywgeyB0b2tlbjogXCJjb21tZW50LmJsb2NrLmRvY3VtZW50YXRpb25cIiwgbmV4dDogXCJAcG9wXCIgfV0sXG4gICAgICB7IGluY2x1ZGU6IFwiQGRvY3N0cmluZ0NvbnRlbnRcIiB9XG4gICAgXSxcbiAgICAvLyBPcGVyYXRvcnMsIHB1bmN0dWF0aW9uLCBicmFja2V0c1xuICAgIHN5bWJvbHM6IFtcbiAgICAgIC8vIENvZGUgcG9pbnQgb3BlcmF0b3IgKGVpdGhlciB3aXRoIHJlZ3VsYXIgY2hhcmFjdGVyID9hIG9yIGFuIGVzY2FwZWQgb25lID9cXG4pXG4gICAgICBbL1xcPyhcXFxcLnxbXlxcXFxcXHNdKS8sIFwibnVtYmVyLmNvbnN0YW50XCJdLFxuICAgICAgLy8gQW5vbnltb3VzIGZ1bmN0aW9uIGFyZ3VtZW50c1xuICAgICAgWy8mXFxkKy8sIFwib3BlcmF0b3JcIl0sXG4gICAgICAvLyBCaXRzaGlmdCBvcGVyYXRvcnMgKG11c3QgZ28gYmVmb3JlIGRlbGltaXRlcnMsIHNvIHRoYXQgPDwgPj4gZG9uJ3QgbWF0Y2ggZmlyc3QpXG4gICAgICBbLzw8PHw+Pj4vLCBcIm9wZXJhdG9yXCJdLFxuICAgICAgLy8gRGVsaW1pdGVyIHBhaXJzXG4gICAgICBbL1soKVxcW1xcXVxce1xcfV18PDx8Pj4vLCBcIkBicmFja2V0c1wiXSxcbiAgICAgIC8vIFRyaXBsZSBkb3QgaXMgYSB2YWxpZCBuYW1lIChtdXN0IGdvIGJlZm9yZSBvcGVyYXRvcnMsIHNvIHRoYXQgLi4gZG9lc24ndCBtYXRjaCBpbnN0ZWFkKVxuICAgICAgWy9cXC5cXC5cXC4vLCBcImlkZW50aWZpZXJcIl0sXG4gICAgICAvLyBQdW5jdHVhdGlvbiA9PiAobXVzdCBnbyBiZWZvcmUgb3BlcmF0b3JzLCBzbyBpdCdzIG5vdCB0b2tlbml6ZWQgYXMgPSB0aGVuID4pXG4gICAgICBbLz0+LywgXCJwdW5jdHVhdGlvblwiXSxcbiAgICAgIC8vIE9wZXJhdG9yc1xuICAgICAgWy9Ab3BlcmF0b3IvLCBcIm9wZXJhdG9yXCJdLFxuICAgICAgLy8gUHVuY3R1YXRpb25cbiAgICAgIFsvWzo7LC4lXS8sIFwicHVuY3R1YXRpb25cIl1cbiAgICBdLFxuICAgIC8vIEdlbmVyaWMgaGVscGVyc1xuICAgIHN0cmluZ0NvbnRlbnRJbnRlcnBvbDogW1xuICAgICAgeyBpbmNsdWRlOiBcIkBpbnRlcnBvbGF0aW9uXCIgfSxcbiAgICAgIHsgaW5jbHVkZTogXCJAZXNjYXBlQ2hhclwiIH0sXG4gICAgICB7IGluY2x1ZGU6IFwiQHN0cmluZ0NvbnRlbnRcIiB9XG4gICAgXSxcbiAgICBzdHJpbmdDb250ZW50OiBbWy8uLywgXCJzdHJpbmdcIl1dLFxuICAgIHN0cmluZ0NvbnN0YW50Q29udGVudEludGVycG9sOiBbXG4gICAgICB7IGluY2x1ZGU6IFwiQGludGVycG9sYXRpb25cIiB9LFxuICAgICAgeyBpbmNsdWRlOiBcIkBlc2NhcGVDaGFyXCIgfSxcbiAgICAgIHsgaW5jbHVkZTogXCJAc3RyaW5nQ29uc3RhbnRDb250ZW50XCIgfVxuICAgIF0sXG4gICAgc3RyaW5nQ29uc3RhbnRDb250ZW50OiBbWy8uLywgXCJjb25zdGFudFwiXV0sXG4gICAgcmVnZXhwQ29udGVudEludGVycG9sOiBbXG4gICAgICB7IGluY2x1ZGU6IFwiQGludGVycG9sYXRpb25cIiB9LFxuICAgICAgeyBpbmNsdWRlOiBcIkBlc2NhcGVDaGFyXCIgfSxcbiAgICAgIHsgaW5jbHVkZTogXCJAcmVnZXhwQ29udGVudFwiIH1cbiAgICBdLFxuICAgIHJlZ2V4cENvbnRlbnQ6IFtcbiAgICAgIC8vICMgbWF5IGJlIGEgcmVndWxhciByZWdleHAgY2hhciwgc28gd2UgdXNlIGEgaGV1cmlzdGljXG4gICAgICAvLyBhc3N1bWluZyBhICMgc3Vycm91bmRlZCBieSB3aGl0ZXNwYWNlIGlzIGFjdHVhbGx5IGEgY29tbWVudC5cbiAgICAgIFsvKFxccykoIykoXFxzLiopJC8sIFtcIndoaXRlXCIsIFwiY29tbWVudC5wdW5jdHVhdGlvblwiLCBcImNvbW1lbnRcIl1dLFxuICAgICAgWy8uLywgXCJyZWdleHBcIl1cbiAgICBdLFxuICAgIHNpZ2lsQ29udGVudEludGVycG9sOiBbXG4gICAgICB7IGluY2x1ZGU6IFwiQGludGVycG9sYXRpb25cIiB9LFxuICAgICAgeyBpbmNsdWRlOiBcIkBlc2NhcGVDaGFyXCIgfSxcbiAgICAgIHsgaW5jbHVkZTogXCJAc2lnaWxDb250ZW50XCIgfVxuICAgIF0sXG4gICAgc2lnaWxDb250ZW50OiBbWy8uLywgXCJzaWdpbFwiXV0sXG4gICAgZG9jc3RyaW5nQ29udGVudDogW1svLi8sIFwiY29tbWVudC5ibG9jay5kb2N1bWVudGF0aW9uXCJdXSxcbiAgICBlc2NhcGVDaGFyOiBbWy9AZXNjYXBlLywgXCJjb25zdGFudC5jaGFyYWN0ZXIuZXNjYXBlXCJdXSxcbiAgICBpbnRlcnBvbGF0aW9uOiBbWy8jey8sIHsgdG9rZW46IFwiZGVsaW1pdGVyLmJyYWNrZXQuZW1iZWRcIiwgbmV4dDogXCJAaW50ZXJwb2xhdGlvbkNvbnRpbnVlXCIgfV1dLFxuICAgIGludGVycG9sYXRpb25Db250aW51ZTogW1xuICAgICAgWy99LywgeyB0b2tlbjogXCJkZWxpbWl0ZXIuYnJhY2tldC5lbWJlZFwiLCBuZXh0OiBcIkBwb3BcIiB9XSxcbiAgICAgIC8vIEludGVycG9sYXRpb24gYnJhY2tldHMgbWF5IGNvbnRhaW4gYXJiaXRyYXJ5IGNvZGUsXG4gICAgICAvLyBzbyB3ZSBzaW1wbHkgbWF0Y2ggYWdhaW5zdCBhbGwgdGhlIHJvb3QgcnVsZXMsXG4gICAgICAvLyB1bnRpbCB3ZSByZWFjaCBpbnRlcnBvbGF0aW9uIGVuZCAodGhlIGFib3ZlIG1hdGNoZXMpLlxuICAgICAgeyBpbmNsdWRlOiBcIkByb290XCIgfVxuICAgIF1cbiAgfVxufTtcbmV4cG9ydCB7XG4gIGNvbmYsXG4gIGxhbmd1YWdlXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/elixir/elixir.js\n"));

/***/ })

}]);