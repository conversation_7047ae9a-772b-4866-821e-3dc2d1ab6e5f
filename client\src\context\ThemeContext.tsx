"use client"

import { createContext, useContext, useEffect, useState } from "react"

type Theme = "light" | "dark" | "system"

interface ThemeContextType {
  theme: Theme
  setTheme: (theme: Theme) => void
}

const ThemeContext = createContext<ThemeContextType>({
  theme: "system",
  setTheme: () => null,
})

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const [theme, setTheme] = useState<Theme>("system")
  const [mounted, setMounted] = useState(false)

  // Update theme
  const updateTheme = (newTheme: Theme) => {
    const root = window.document.documentElement
    const isDark =
      newTheme === "dark" ||
      (newTheme === "system" &&
        window.matchMedia("(prefers-color-scheme: dark)").matches)

    root.classList.remove("light", "dark")
    root.classList.add(isDark ? "dark" : "light")
    
    // Store the preference
    if (newTheme !== "system") {
      localStorage.setItem("theme", newTheme)
    } else {
      localStorage.removeItem("theme")
    }
  }

  // Set theme
  const handleSetTheme = (newTheme: Theme) => {
    setTheme(newTheme)
    updateTheme(newTheme)
  }

  // Initialize theme
  useEffect(() => {
    setMounted(true)
    const storedTheme = localStorage.getItem("theme") as Theme | null
    if (storedTheme) {
      setTheme(storedTheme)
      updateTheme(storedTheme)
    } else {
      updateTheme("system")
    }
  }, [])

  // Handle system theme changes
  useEffect(() => {
    if (theme === "system") {
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)")
      const handleChange = () => {
        updateTheme("system")
      }
      mediaQuery.addEventListener("change", handleChange)
      return () => mediaQuery.removeEventListener("change", handleChange)
    }
  }, [theme])

  return (
    <ThemeContext.Provider value={{ theme, setTheme: handleSetTheme }}>
      {mounted && children}
    </ThemeContext.Provider>
  )
}

export const useTheme = () => useContext(ThemeContext)
