{"version": 3, "sources": ["../../../locales/cs-CZ.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"V<PERSON><PERSON>it\",\n    \"pasteAsPlaintext\": \"Vložit jako prostý text\",\n    \"pasteCharts\": \"Vložit grafy\",\n    \"selectAll\": \"Vybrat vše\",\n    \"multiSelect\": \"Přidat prvek do výběru\",\n    \"moveCanvas\": \"Posunout plátno\",\n    \"cut\": \"Vyjmout\",\n    \"copy\": \"Kopírovat\",\n    \"copyAsPng\": \"Zkopírovat do schránky jako PNG\",\n    \"copyAsSvg\": \"Zkopírovat do schránky jako SVG\",\n    \"copyText\": \"Zkopírovat do schránky jako text\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Přenést blíž\",\n    \"sendToBack\": \"Přenést do pozadí\",\n    \"bringToFront\": \"Přenést do popředí\",\n    \"sendBackward\": \"<PERSON>ř<PERSON><PERSON>t dál\",\n    \"delete\": \"<PERSON><PERSON><PERSON>t\",\n    \"copyStyles\": \"Kop<PERSON>rovat styly\",\n    \"pasteStyles\": \"Vlo<PERSON>it styly\",\n    \"stroke\": \"<PERSON><PERSON>ry<PERSON>\",\n    \"background\": \"<PERSON>zadí\",\n    \"fill\": \"Výplň\",\n    \"strokeWidth\": \"Tloušťka tahu\",\n    \"strokeStyle\": \"Styl tahu\",\n    \"strokeStyle_solid\": \"Plný\",\n    \"strokeStyle_dashed\": \"Čárkovaný\",\n    \"strokeStyle_dotted\": \"Tečkovaný\",\n    \"sloppiness\": \"Stylizace\",\n    \"opacity\": \"Průhlednost\",\n    \"textAlign\": \"Zarovnání textu\",\n    \"edges\": \"Hrany\",\n    \"sharp\": \"Ostré\",\n    \"round\": \"Zaoblené\",\n    \"arrowheads\": \"Styl šipky\",\n    \"arrowhead_none\": \"Žádný\",\n    \"arrowhead_arrow\": \"Šipka\",\n    \"arrowhead_bar\": \"Kóta\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Trojúhelník\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Velikost písma\",\n    \"fontFamily\": \"Písmo\",\n    \"addWatermark\": \"Přidat \\\"Vyrobeno s Excalidraw\\\"\",\n    \"handDrawn\": \"Od ruky\",\n    \"normal\": \"Normální\",\n    \"code\": \"Kód\",\n    \"small\": \"Malé\",\n    \"medium\": \"Střední\",\n    \"large\": \"Velké\",\n    \"veryLarge\": \"Velmi velké\",\n    \"solid\": \"Plný\",\n    \"hachure\": \"Hachure\",\n    \"zigzag\": \"Klikatě\",\n    \"crossHatch\": \"Křížový šrafování\",\n    \"thin\": \"Tenký\",\n    \"bold\": \"Tlustý\",\n    \"left\": \"Vlevo\",\n    \"center\": \"Na střed\",\n    \"right\": \"Vpravo\",\n    \"extraBold\": \"Extra tlustý\",\n    \"architect\": \"Architekt\",\n    \"artist\": \"Umělec\",\n    \"cartoonist\": \"Kartoonista\",\n    \"fileTitle\": \"Název souboru\",\n    \"colorPicker\": \"Výběr barvy\",\n    \"canvasColors\": \"Použito na plátně\",\n    \"canvasBackground\": \"Pozadí plátna\",\n    \"drawingCanvas\": \"Kreslicí plátno\",\n    \"layers\": \"Vrstvy\",\n    \"actions\": \"Akce\",\n    \"language\": \"Jazyk\",\n    \"liveCollaboration\": \"Živá spolupráce...\",\n    \"duplicateSelection\": \"Duplikovat\",\n    \"untitled\": \"Bez názvu\",\n    \"name\": \"Název\",\n    \"yourName\": \"Vaše jméno\",\n    \"madeWithExcalidraw\": \"Vytvořeno v Excalidraw\",\n    \"group\": \"Sloučit výběr do skupiny\",\n    \"ungroup\": \"Zrušit sloučení skupiny\",\n    \"collaborators\": \"Spolupracovníci\",\n    \"showGrid\": \"Zobrazit mřížku\",\n    \"addToLibrary\": \"Přidat do knihovny\",\n    \"removeFromLibrary\": \"Odebrat z knihovny\",\n    \"libraryLoadingMessage\": \"Načítání knihovny…\",\n    \"libraries\": \"Procházet knihovny\",\n    \"loadingScene\": \"Načítání scény…\",\n    \"align\": \"Zarovnání\",\n    \"alignTop\": \"Zarovnat nahoru\",\n    \"alignBottom\": \"Zarovnat dolů\",\n    \"alignLeft\": \"Zarovnat vlevo\",\n    \"alignRight\": \"Zarovnejte vpravo\",\n    \"centerVertically\": \"Vycentrovat svisle\",\n    \"centerHorizontally\": \"Vycentrovat vodorovně\",\n    \"distributeHorizontally\": \"Rozložit horizontálně\",\n    \"distributeVertically\": \"Rozložit svisle\",\n    \"flipHorizontal\": \"Převrátit vodorovně\",\n    \"flipVertical\": \"Převrátit svisle\",\n    \"viewMode\": \"Náhled\",\n    \"share\": \"Sdílet\",\n    \"showStroke\": \"Zobrazit výběr barvy\",\n    \"showBackground\": \"Zobrazit výběr barev pozadí\",\n    \"toggleTheme\": \"Přepnout tmavý řežim\",\n    \"personalLib\": \"Osobní knihovna\",\n    \"excalidrawLib\": \"Exkalidraw knihovna\",\n    \"decreaseFontSize\": \"Zmenšit písmo\",\n    \"increaseFontSize\": \"Zvětšit písmo\",\n    \"unbindText\": \"Zrušit vazbu textu\",\n    \"bindText\": \"Vázat text s kontejnerem\",\n    \"createContainerFromText\": \"Zabalit text do kontejneru\",\n    \"link\": {\n      \"edit\": \"Upravit odkaz\",\n      \"editEmbed\": \"\",\n      \"create\": \"Vytvořit odkaz\",\n      \"createEmbed\": \"\",\n      \"label\": \"Odkaz\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Upravit čáru\",\n      \"exit\": \"Ukončit editor řádků\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Uzamknout\",\n      \"unlock\": \"Odemknout\",\n      \"lockAll\": \"Uzamknout vše\",\n      \"unlockAll\": \"Odemknout vše\"\n    },\n    \"statusPublished\": \"Zveřejněno\",\n    \"sidebarLock\": \"Ponechat postranní panel otevřený\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"Vyberte barvu z plátna\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Dosud neexistují žádné položky...\",\n    \"hint_emptyLibrary\": \"Vyberte položku na plátně a přidejte ji sem nebo nainstalujte knihovnu z veřejného úložiště níže.\",\n    \"hint_emptyPrivateLibrary\": \"Vyberte položku na plátně a přidejte ji sem.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Resetovat plátno\",\n    \"exportJSON\": \"Exportovat do souboru\",\n    \"exportImage\": \"Exportovat obrázek...\",\n    \"export\": \"Uložit jako...\",\n    \"copyToClipboard\": \"Kopírovat do schránky\",\n    \"save\": \"Uložit do aktuálního souboru\",\n    \"saveAs\": \"Uložit jako\",\n    \"load\": \"Otevřít\",\n    \"getShareableLink\": \"Získat odkaz pro sdílení\",\n    \"close\": \"Zavřít\",\n    \"selectLanguage\": \"Zvolit jazyk\",\n    \"scrollBackToContent\": \"Přejít zpět na obsah\",\n    \"zoomIn\": \"Přiblížit\",\n    \"zoomOut\": \"Oddálit\",\n    \"resetZoom\": \"Resetovat přiblížení\",\n    \"menu\": \"Menu\",\n    \"done\": \"Hotovo\",\n    \"edit\": \"Upravit\",\n    \"undo\": \"Zpět\",\n    \"redo\": \"Znovu\",\n    \"resetLibrary\": \"Obnovit knihovnu\",\n    \"createNewRoom\": \"Vytvořit novou místnost\",\n    \"fullScreen\": \"Celá obrazovka\",\n    \"darkMode\": \"Tmavý režim\",\n    \"lightMode\": \"Světlý režim\",\n    \"zenMode\": \"Zen mód\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Opustit zen mód\",\n    \"cancel\": \"Zrušit\",\n    \"clear\": \"Vyčistit\",\n    \"remove\": \"Odstranit\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Zveřejnit\",\n    \"submit\": \"Odeslat\",\n    \"confirm\": \"Potvrdit\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Toto vymaže celé plátno. Jste si jisti?\",\n    \"couldNotCreateShareableLink\": \"Nepodařilo se vytvořit sdílitelný odkaz.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Nepodařilo se vytvořit sdílený odkaz: scéna je příliš velká\",\n    \"couldNotLoadInvalidFile\": \"Nepodařilo se načíst neplatný soubor\",\n    \"importBackendFailed\": \"Import z backendu se nezdařil.\",\n    \"cannotExportEmptyCanvas\": \"Nelze exportovat prázdné plátno.\",\n    \"couldNotCopyToClipboard\": \"Nelze zkopírovat do schránky.\",\n    \"decryptFailed\": \"Nelze dešifrovat data.\",\n    \"uploadedSecurly\": \"Nahrávání je zabezpečeno koncovým šifrováním, což znamená, že server Excalidraw ani třetí strany nemohou obsah přečíst.\",\n    \"loadSceneOverridePrompt\": \"Načítání externího výkresu nahradí váš existující obsah. Přejete si pokračovat?\",\n    \"collabStopOverridePrompt\": \"Zastavení relace přepíše vaše předchozí, lokálně uložené kresby. Jste si jisti?\\n\\n(Pokud chcete zachovat místní kresbu, jednoduše zavřete kartu prohlížeče)\",\n    \"errorAddingToLibrary\": \"Položku nelze přidat do knihovny\",\n    \"errorRemovingFromLibrary\": \"Položku nelze odstranit z knihovny\",\n    \"confirmAddLibrary\": \"Tímto přidáte {{numShapes}} tvarů do tvé knihovny. Jste si jisti?\",\n    \"imageDoesNotContainScene\": \"Zdá se, že tento obrázek neobsahuje žádná data o scéně. Zapnuli jste při exportu vkládání scény?\",\n    \"cannotRestoreFromImage\": \"Scénu nelze obnovit z tohoto souboru obrázku\",\n    \"invalidSceneUrl\": \"Nelze importovat scénu z zadané URL. Je buď poškozená, nebo neobsahuje platná JSON data Excalidraw.\",\n    \"resetLibrary\": \"Tímto vymažete vaši knihovnu. Jste si jisti?\",\n    \"removeItemsFromsLibrary\": \"Smazat {{count}} položek z knihovny?\",\n    \"invalidEncryptionKey\": \"Šifrovací klíč musí mít 22 znaků. Live spolupráce je zakázána.\",\n    \"collabOfflineWarning\": \"Není k dispozici žádné internetové připojení.\\nVaše změny nebudou uloženy!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Nepodporovaný typ souboru.\",\n    \"imageInsertError\": \"Nelze vložit obrázek. Zkuste to později...\",\n    \"fileTooBig\": \"Soubor je příliš velký. Maximální povolená velikost je {{maxSize}}.\",\n    \"svgImageInsertError\": \"Nelze vložit SVG obrázek. Značení SVG je neplatné.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"Neplatný SVG.\",\n    \"cannotResolveCollabServer\": \"Nelze se připojit ke sdílenému serveru. Prosím obnovte stránku a zkuste to znovu.\",\n    \"importLibraryError\": \"Nelze načíst knihovnu\",\n    \"collabSaveFailed\": \"Nelze uložit do databáze na serveru. Pokud problémy přetrvávají, měli byste uložit soubor lokálně, abyste se ujistili, že neztratíte svou práci.\",\n    \"collabSaveFailed_sizeExceeded\": \"Nelze uložit do databáze na serveru, plátno se zdá být příliš velké. Měli byste uložit soubor lokálně, abyste se ujistili, že neztratíte svou práci.\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Vypadá to, že používáte Brave prohlížeč s povoleným nastavením <bold>Aggressively Block Fingerprinting</bold>.\",\n      \"line2\": \"To by mohlo vést k narušení <bold>Textových elementů</bold> ve vašich výkresech.\",\n      \"line3\": \"Důrazně doporučujeme zakázat toto nastavení. Můžete sledovat <link>tyto kroky</link> jak to udělat.\",\n      \"line4\": \"Pokud vypnutí tohoto nastavení neopravuje zobrazení textových prvků, prosím, otevřete <issueLink>problém</issueLink> na našem GitHubu, nebo nám napište na <discordLink>Discord</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Výběr\",\n    \"image\": \"Vložit obrázek\",\n    \"rectangle\": \"Obdélník\",\n    \"diamond\": \"Diamant\",\n    \"ellipse\": \"Elipsa\",\n    \"arrow\": \"Šipka\",\n    \"line\": \"Čára\",\n    \"freedraw\": \"Kreslení\",\n    \"text\": \"Text\",\n    \"library\": \"Knihovna\",\n    \"lock\": \"Po kreslení ponechat vybraný nástroj aktivní\",\n    \"penMode\": \"Režim Pera - zabránit dotyku\",\n    \"link\": \"Přidat/aktualizovat odkaz pro vybraný tvar\",\n    \"eraser\": \"Guma\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"Ruka (nástroj pro posouvání)\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Akce plátna\",\n    \"selectedShapeActions\": \"Akce vybraného tvaru\",\n    \"shapes\": \"Tvary\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Chcete-li přesunout plátno, podržte kolečko nebo mezerník při tažení nebo použijte nástroj Ruka\",\n    \"linearElement\": \"Kliknutím pro více bodů, táhnutím pro jednu čáru\",\n    \"freeDraw\": \"Klikněte a táhněte, pro ukončení pusťte\",\n    \"text\": \"Tip: Text můžete také přidat dvojitým kliknutím kdekoli pomocí nástroje pro výběr\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"Dvojklikem nebo stisknutím klávesy ENTER upravíte text\",\n    \"text_editing\": \"Stiskněte Escape nebo Ctrl/Cmd+ENTER pro dokončení úprav\",\n    \"linearElementMulti\": \"Klikněte na poslední bod nebo stiskněte Escape anebo Enter pro dokončení\",\n    \"lockAngle\": \"Úhel můžete omezit podržením SHIFT\",\n    \"resize\": \"Můžete omezit proporce podržením SHIFT při změně velikosti,\\npodržte ALT pro změnu velikosti od středu\",\n    \"resizeImage\": \"Můžete volně změnit velikost podržením SHIFT,\\npodržením klávesy ALT změníte velikosti od středu\",\n    \"rotate\": \"Úhly můžete omezit podržením SHIFT při otáčení\",\n    \"lineEditor_info\": \"Podržte Ctrl/Cmd a dvakrát klikněte nebo stiskněte Ctrl/Cmd + Enter pro úpravu bodů\",\n    \"lineEditor_pointSelected\": \"Stisknutím tlačítka Delete odstraňte bod(y),\\nCtrl/Cmd+D pro duplicitu nebo táhnutím pro přesun\",\n    \"lineEditor_nothingSelected\": \"Vyberte bod, který chcete upravit (podržením klávesy SHIFT vyberete více položek),\\nnebo podržením klávesy Alt a kliknutím přidáte nové body\",\n    \"placeImage\": \"Kliknutím umístěte obrázek, nebo klepnutím a přetažením ručně nastavíte jeho velikost\",\n    \"publishLibrary\": \"Publikovat vlastní knihovnu\",\n    \"bindTextToElement\": \"Stiskněte Enter pro přidání textu\",\n    \"deepBoxSelect\": \"Podržte Ctrl/Cmd pro hluboký výběr a pro zabránění táhnutí\",\n    \"eraserRevert\": \"Podržením klávesy Alt vrátíte prvky označené pro smazání\",\n    \"firefox_clipboard_write\": \"Tato funkce může být povolena nastavením vlajky \\\"dom.events.asyncClipboard.clipboardItem\\\" na \\\"true\\\". Chcete-li změnit vlajky prohlížeče ve Firefoxu, navštivte stránku \\\"about:config\\\".\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Náhled nelze zobrazit\",\n    \"canvasTooBig\": \"Plátno je možná příliš velké.\",\n    \"canvasTooBigTip\": \"Tip: zkus posunout nejvzdálenější prvky trochu blíže k sobě.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Chyba. Zkuste <button>znovu načíst stránku</button>.\",\n    \"clearCanvasMessage\": \"Pokud opětovné načtení nefunguje, zkuste <button>vymazat plátno</button>.\",\n    \"clearCanvasCaveat\": \" To povede ke ztrátě dat \",\n    \"trackedToSentry\": \"Chyba identifikátoru {{eventId}} byl zaznamenán v našem systému.\",\n    \"openIssueMessage\": \"Byli jsme velmi opatrní, abychom neuváděli informace o Vaší scéně. Pokud vaše scéna není soukromá, zvažte prosím sledování na našem <button>bug trackeru</button>. Uveďte prosím níže uvedené informace kopírováním a vložením do problému na GitHubu.\",\n    \"sceneContent\": \"Obsah scény:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Můžete pozvat lidi na vaši aktuální scénu ke spolupráci s vámi.\",\n    \"desc_privacy\": \"Nebojte se, relace používá end-to-end šifrování, takže cokoliv nakreslíte zůstane soukromé. Ani náš server nebude schopen vidět, s čím budete pracovat.\",\n    \"button_startSession\": \"Zahájit relaci\",\n    \"button_stopSession\": \"Ukončit relaci\",\n    \"desc_inProgressIntro\": \"Živá spolupráce právě probíhá.\",\n    \"desc_shareLink\": \"Sdílejte tento odkaz s každým, s kým chcete spolupracovat:\",\n    \"desc_exitSession\": \"Zastavením relace se odpojíte od místnosti, ale budete moci pokračovat v práci s touto scénou lokálně. Všimněte si, že to nebude mít vliv na ostatní lidi a budou stále moci spolupracovat na jejich verzi.\",\n    \"shareTitle\": \"Připojte se k aktivní spolupráci na Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Chyba\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Uložit na disk\",\n    \"disk_details\": \"Exportovat data scény do souboru, ze kterého můžete importovat později.\",\n    \"disk_button\": \"Uložit do souboru\",\n    \"link_title\": \"Odkaz pro sdílení\",\n    \"link_details\": \"Exportovat jako odkaz pouze pro čtení.\",\n    \"link_button\": \"Exportovat do odkazu\",\n    \"excalidrawplus_description\": \"Uložit scénu do vašeho pracovního prostoru Excalidraw+.\",\n    \"excalidrawplus_button\": \"Exportovat\",\n    \"excalidrawplus_exportError\": \"Export do Excalidraw+ se v tuto chvíli nezdařil...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Přečtěte si náš blog\",\n    \"click\": \"kliknutí\",\n    \"deepSelect\": \"Hluboký výběr\",\n    \"deepBoxSelect\": \"Hluboký výběr uvnitř boxu a zabránění táhnnutí\",\n    \"curvedArrow\": \"Zakřivená šipka\",\n    \"curvedLine\": \"Zakřivená čára\",\n    \"documentation\": \"Dokumentace\",\n    \"doubleClick\": \"dvojklik\",\n    \"drag\": \"tažení\",\n    \"editor\": \"Editor\",\n    \"editLineArrowPoints\": \"Upravit body linií/šipek\",\n    \"editText\": \"Upravit text / přidat popis\",\n    \"github\": \"Našel jsi problém? Nahlaš ho\",\n    \"howto\": \"Sledujte naše návody\",\n    \"or\": \"nebo\",\n    \"preventBinding\": \"Zabránit vázání šipky\",\n    \"tools\": \"Nástroje\",\n    \"shortcuts\": \"Klávesové zkratky\",\n    \"textFinish\": \"Dokončit úpravy (textový editor)\",\n    \"textNewLine\": \"Přidat nový řádek (textový editor)\",\n    \"title\": \"Nápověda\",\n    \"view\": \"Zobrazení\",\n    \"zoomToFit\": \"Přiblížit na zobrazení všech prvků\",\n    \"zoomToSelection\": \"Přiblížit na výběr\",\n    \"toggleElementLock\": \"Zamknout/odemknout výběr\",\n    \"movePageUpDown\": \"Posunout stránku nahoru/dolů\",\n    \"movePageLeftRight\": \"Přesunout stránku doleva/doprava\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Vymazat plátno\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Publikovat knihovnu\",\n    \"itemName\": \"Název položky\",\n    \"authorName\": \"Jméno autora\",\n    \"githubUsername\": \"GitHub uživatelské jméno\",\n    \"twitterUsername\": \"Twitter uživatelské jméno\",\n    \"libraryName\": \"Název knihovny\",\n    \"libraryDesc\": \"Popis knihovny\",\n    \"website\": \"Webová stránka\",\n    \"placeholder\": {\n      \"authorName\": \"Jméno nebo uživatelské jméno\",\n      \"libraryName\": \"Název vaší knihovny\",\n      \"libraryDesc\": \"Popis Vaší knihovny, který pomůže lidem pochopit její využití\",\n      \"githubHandle\": \"Github uživatelské jméno (nepovinné), abyste mohli upravovat knihovnu poté co je odeslána ke kontrole\",\n      \"twitterHandle\": \"Twitter uživatelské jméno (nepovinné), abychom věděli koho označit při propagaci na Twitteru\",\n      \"website\": \"Odkaz na Vaši osobní webovou stránku nebo jinam (nepovinné)\"\n    },\n    \"errors\": {\n      \"required\": \"Povinné\",\n      \"website\": \"Zadejte platnou URL adresu\"\n    },\n    \"noteDescription\": \"Odešlete svou knihovnu, pro zařazení do <link>veřejného úložiště knihoven</link>, odkud ji budou moci při kreslení využít i ostatní uživatelé.\",\n    \"noteGuidelines\": \"Knihovna musí být nejdříve ručně schválena. Přečtěte si prosím <link>pokyny</link>\",\n    \"noteLicense\": \"Odesláním souhlasíte s tím, že knihovna bude zveřejněna pod <link>MIT licencí</link>, stručně řečeno, kdokoli ji může používat bez omezení.\",\n    \"noteItems\": \"Každá položka knihovny musí mít svůj vlastní název, aby byla filtrovatelná. Následující položky knihovny budou zahrnuty:\",\n    \"atleastOneLibItem\": \"Vyberte alespoň jednu položku knihovny, kterou chcete začít\",\n    \"republishWarning\": \"Poznámka: některé z vybraných položek jsou označeny jako již zveřejněné/odeslané. Položky byste měli znovu odeslat pouze při aktualizaci existující knihovny nebo podání.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Knihovna byla odeslána\",\n    \"content\": \"Děkujeme vám {{authorName}}. Vaše knihovna byla odeslána k posouzení. Stav můžete sledovat <link>zde</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Resetovat knihovnu\",\n    \"removeItemsFromLib\": \"Odstranit vybrané položky z knihovny\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Exportovat obrázek\",\n    \"label\": {\n      \"withBackground\": \"Pozadí\",\n      \"onlySelected\": \"Pouze vybrané\",\n      \"darkMode\": \"Tmavý režim\",\n      \"embedScene\": \"Vložit scénu\",\n      \"scale\": \"Měřítko\",\n      \"padding\": \"Odsazení\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"Data scény budou uložena do exportovaného souboru PNG/SVG tak, aby z něj mohla být scéna obnovena.\\nZvýší se velikost exportovaného souboru.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Exportovat do PNG\",\n      \"exportToSvg\": \"Exportovat do SVG\",\n      \"copyPngToClipboard\": \"Kopírovat PNG do schránky\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Kopírovat do schránky\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Vaše kresby jsou end-to-end šifrované, takže servery Excalidraw je nikdy neuvidí.\",\n    \"link\": \"Blog příspěvek na end-to-end šifrování v Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Úhel\",\n    \"element\": \"Prvek\",\n    \"elements\": \"Prvky\",\n    \"height\": \"Výška\",\n    \"scene\": \"Scéna\",\n    \"selected\": \"Vybráno\",\n    \"storage\": \"Úložiště\",\n    \"title\": \"Statistika pro nerdy\",\n    \"total\": \"Celkem\",\n    \"version\": \"Verze\",\n    \"versionCopy\": \"Kliknutím zkopírujete\",\n    \"versionNotAvailable\": \"Verze není k dispozici\",\n    \"width\": \"Šířka\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Přidáno do knihovny\",\n    \"copyStyles\": \"Styly byly zkopírovány.\",\n    \"copyToClipboard\": \"Zkopírováno do schránky.\",\n    \"copyToClipboardAsPng\": \"{{exportSelection}} zkopírován do schránky jako PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Soubor byl uložen.\",\n    \"fileSavedToFilename\": \"Uloženo do {filename}\",\n    \"canvas\": \"plátno\",\n    \"selection\": \"výběr\",\n    \"pasteAsSingleElement\": \"Pomocí {{shortcut}} vložte jako jeden prvek,\\nnebo vložte do existujícího textového editoru\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Průhledná\",\n    \"black\": \"Černá\",\n    \"white\": \"Bílá\",\n    \"red\": \"Červená\",\n    \"pink\": \"Růžová\",\n    \"grape\": \"Vínová\",\n    \"violet\": \"Fialová\",\n    \"gray\": \"Šedá\",\n    \"blue\": \"Modrá\",\n    \"cyan\": \"Azurová\",\n    \"teal\": \"Modrozelená\",\n    \"green\": \"Zelená\",\n    \"yellow\": \"Žlutá\",\n    \"orange\": \"Oranžová\",\n    \"bronze\": \"Bronzová\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Všechna vaše data jsou uložena lokálně ve vašem prohlížeči.\",\n      \"center_heading_plus\": \"Chcete místo toho přejít na Excalidraw+?\",\n      \"menuHint\": \"Export, nastavení, jazyky, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Export, nastavení a další...\",\n      \"center_heading\": \"Diagramy. Vytvořeny. Jednoduše.\",\n      \"toolbarHint\": \"Vyberte nástroj a začněte kreslit!\",\n      \"helpHint\": \"Zkratky a pomoc\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Nejpoužívanější vlastní barvy\",\n    \"colors\": \"Barvy\",\n    \"shades\": \"Stíny\",\n    \"hexCode\": \"Hex kód\",\n    \"noShades\": \"Pro tuto barvu nejsou k dispozici žádné odstíny\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}