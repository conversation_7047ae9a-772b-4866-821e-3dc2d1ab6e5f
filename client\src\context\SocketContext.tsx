"use client"

import React from "react";
import { createContext, useContext, useEffect, useState } from "react"
import socketService from "../services/socketService"
import { Socket } from "socket.io-client"

type SocketContextType = {
    isConnected: boolean;
    socket: Socket | null;
}

const SocketContext = createContext<SocketContextType | null>(null)

export const useSocket = () => {
    const context = useContext(SocketContext)
    if (!context) {
        throw new Error("useSocket must be used within SocketProvider")
    }
    return context
}

// Use an instance of SocketService
const socketServiceInstance = socketService.getInstance();

export const SocketProvider = ({ children }: { children: React.ReactNode }) => {
    const [isConnected, setIsConnected] = useState(socketServiceInstance.isConnected());
    const socket = socketServiceInstance.getSocket();

    useEffect(() => {
        // Handle connection status changes
        const handleConnect = () => {
            console.log("Socket connected");
            setIsConnected(true);
        };

        const handleDisconnect = () => {
            console.log("Socket disconnected");
            setIsConnected(false);

            // Try to reconnect after a short delay
            setTimeout(() => {
                console.log("Attempting to reconnect...");
                socketServiceInstance.connect();
            }, 3000);
        };

        if (socket) {
            socket.on("connect", handleConnect);
            socket.on("disconnect", handleDisconnect);
        }

        return () => {
            if (socket) {
                socket.off("connect", handleConnect);
                socket.off("disconnect", handleDisconnect);
            }
        };
    }, [socket]);

    return (
        <SocketContext.Provider value={{ isConnected, socket }}>
            {children}
        </SocketContext.Provider>
    )
}
