"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/context/EditPermissionContext.tsx":
/*!***********************************************!*\
  !*** ./src/context/EditPermissionContext.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditPermissionProvider: () => (/* binding */ EditPermissionProvider),\n/* harmony export */   useEditPermission: () => (/* binding */ useEditPermission)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSocketService */ \"(app-pages-browser)/./src/hooks/useSocketService.ts\");\n/* __next_internal_client_entry_do_not_use__ EditPermissionProvider,useEditPermission auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst EditPermissionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction EditPermissionProvider(param) {\n    let { children } = param;\n    _s();\n    const [canEdit, setCanEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTeacher, setIsTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Use the socket service hook\n    const { socketService, isReady: socketReady, isConnected } = (0,_hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__.useSocketService)();\n    // Compute permission badge based on current state\n    const permissionBadge = isTeacher ? 'teacher' : canEdit ? 'edit-access' : 'view-only';\n    // Grant edit permission to a student\n    const grantEditPermission = (targetSocketId)=>{\n        if (!isTeacher) {\n            console.warn('Only teachers can grant edit permissions');\n            return;\n        }\n        if (!socketService || !socketReady) {\n            console.warn('Socket service not ready, cannot grant edit permission');\n            return;\n        }\n        const roomId =  true ? window.localStorage.getItem('currentRoomId') : 0;\n        if (!roomId) {\n            console.error('No room ID found');\n            return;\n        }\n        console.log(\"Granting edit permission to \".concat(targetSocketId));\n        try {\n            socketService.emit('grant-edit-permission', {\n                roomId,\n                targetSocketId\n            });\n        } catch (error) {\n            console.error('Error granting edit permission:', error);\n        }\n    };\n    // Revoke edit permission from a student\n    const revokeEditPermission = (targetSocketId)=>{\n        if (!isTeacher) {\n            console.warn('Only teachers can revoke edit permissions');\n            return;\n        }\n        if (!socketService || !socketReady) {\n            console.warn('Socket service not ready, cannot revoke edit permission');\n            return;\n        }\n        const roomId =  true ? window.localStorage.getItem('currentRoomId') : 0;\n        if (!roomId) {\n            console.error('No room ID found');\n            return;\n        }\n        console.log(\"Revoking edit permission from \".concat(targetSocketId));\n        try {\n            socketService.emit('revoke-edit-permission', {\n                roomId,\n                targetSocketId\n            });\n        } catch (error) {\n            console.error('Error revoking edit permission:', error);\n        }\n    };\n    // Legacy method for backward compatibility\n    const setEditPermission = (targetSocketId, canEdit)=>{\n        if (canEdit) {\n            grantEditPermission(targetSocketId);\n        } else {\n            revokeEditPermission(targetSocketId);\n        }\n    };\n    const updateUserPermission = (socketId, canEdit)=>{\n        setUsers((prevUsers)=>prevUsers.map((user)=>user.socketId === socketId ? {\n                    ...user,\n                    canEdit\n                } : user));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPermissionProvider.useEffect\": ()=>{\n            // Only set up listeners when socket is ready\n            if (!socketReady || !socketService) {\n                console.log('Waiting for socket to be ready for edit permissions...');\n                return;\n            }\n            // Listen for edit permission changes\n            const handleEditPermission = {\n                \"EditPermissionProvider.useEffect.handleEditPermission\": (data)=>{\n                    console.log('Received edit permission update:', data);\n                    setCanEdit(data.canEdit);\n                }\n            }[\"EditPermissionProvider.useEffect.handleEditPermission\"];\n            // Listen for permission updates (new event)\n            const handlePermissionUpdated = {\n                \"EditPermissionProvider.useEffect.handlePermissionUpdated\": (data)=>{\n                    console.log('Received permission-updated event:', data);\n                    setCanEdit(data.canEdit);\n                }\n            }[\"EditPermissionProvider.useEffect.handlePermissionUpdated\"];\n            // Listen for student list updates (for teachers)\n            const handleUpdateStudentList = {\n                \"EditPermissionProvider.useEffect.handleUpdateStudentList\": (data)=>{\n                    console.log('Received update-student-list event:', data);\n                    setStudents(data.students);\n                }\n            }[\"EditPermissionProvider.useEffect.handleUpdateStudentList\"];\n            // Listen for room users updates (includes permission info)\n            const handleRoomUsersUpdated = {\n                \"EditPermissionProvider.useEffect.handleRoomUsersUpdated\": (data)=>{\n                    console.log('Room users updated with permissions:', data);\n                    setUsers(data.users);\n                }\n            }[\"EditPermissionProvider.useEffect.handleRoomUsersUpdated\"];\n            // Set up socket listeners with null checks\n            try {\n                socketService.on('edit-permission', handleEditPermission);\n                socketService.on('permission-updated', handlePermissionUpdated);\n                socketService.on('update-student-list', handleUpdateStudentList);\n                socketService.on('room-users-updated', handleRoomUsersUpdated);\n                console.log('Enhanced permission socket listeners set up successfully');\n                // If this is a teacher, request the initial student list\n                const currentRoomId = window.location.pathname.split('/').pop();\n                if (isTeacher && currentRoomId) {\n                    console.log('Teacher detected, requesting initial student list for room:', currentRoomId);\n                    socketService.emit('request-student-list', {\n                        roomId: currentRoomId\n                    });\n                }\n            } catch (error) {\n                console.error('Error setting up socket listeners:', error);\n            }\n            // Cleanup listeners\n            return ({\n                \"EditPermissionProvider.useEffect\": ()=>{\n                    try {\n                        if (socketService) {\n                            socketService.off('edit-permission', handleEditPermission);\n                            socketService.off('permission-updated', handlePermissionUpdated);\n                            socketService.off('update-student-list', handleUpdateStudentList);\n                            socketService.off('room-users-updated', handleRoomUsersUpdated);\n                            console.log('Enhanced permission socket listeners cleaned up');\n                        }\n                    } catch (error) {\n                        console.error('Error cleaning up socket listeners:', error);\n                    }\n                }\n            })[\"EditPermissionProvider.useEffect\"];\n        }\n    }[\"EditPermissionProvider.useEffect\"], [\n        socketReady\n    ]);\n    // Log permission changes for debugging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPermissionProvider.useEffect\": ()=>{\n            console.log(\"Edit permission state: canEdit=\".concat(canEdit, \", isTeacher=\").concat(isTeacher, \", socketReady=\").concat(socketReady, \", isConnected=\").concat(isConnected));\n        }\n    }[\"EditPermissionProvider.useEffect\"], [\n        canEdit,\n        isTeacher,\n        socketReady,\n        isConnected\n    ]);\n    const value = {\n        canEdit,\n        isTeacher,\n        users,\n        students,\n        permissionBadge,\n        grantEditPermission,\n        revokeEditPermission,\n        setEditPermission,\n        updateUserPermission,\n        setUsers,\n        setStudents,\n        setCanEdit,\n        setIsTeacher\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditPermissionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\context\\\\EditPermissionContext.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n_s(EditPermissionProvider, \"W8VZUkPAjYm7fgg7tAjRyWJmsnI=\", false, function() {\n    return [\n        _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__.useSocketService\n    ];\n});\n_c = EditPermissionProvider;\nfunction useEditPermission() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(EditPermissionContext);\n    if (context === undefined) {\n        throw new Error('useEditPermission must be used within an EditPermissionProvider');\n    }\n    return context;\n}\n_s1(useEditPermission, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"EditPermissionProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/context/EditPermissionContext.tsx\n"));

/***/ })

});