"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var monaco_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! monaco-editor */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.main.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlignLeft,FiCode,FiCopy,FiDownload,FiHelpCircle,FiLoader,FiMoon,FiPlay,FiSearch,FiSun,FiUpload,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_socketService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/socketService */ \"(app-pages-browser)/./src/services/socketService.ts\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/throttle */ \"(app-pages-browser)/./node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/SocketContext */ \"(app-pages-browser)/./src/context/SocketContext.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_formatCode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/formatCode */ \"(app-pages-browser)/./src/utils/formatCode.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/ThemeContext */ \"(app-pages-browser)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CodeEditor(param) {\n    let { roomId, username: initialUsername } = param;\n    _s();\n    const { isConnected } = (0,_context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket)();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"// Start coding...\");\n    // Use a ref to always track the latest code value\n    const latestCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"// Start coding...\");\n    // Get username from localStorage if available, otherwise use initialUsername\n    const storedUsername =  true ? localStorage.getItem(\"username\") : 0;\n    console.log(\"Initial username: \".concat(initialUsername, \", Stored username: \").concat(storedUsername));\n    // Track the username internally to handle server validation\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUsername || initialUsername);\n    const [typingUser, setTypingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeUsers, setActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUserList, setShowUserList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"javascript\");\n    const [copySuccess, setCopySuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [teacherSelectionDecorations, setTeacherSelectionDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Code execution states\n    const [isExecuting, setIsExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionOutput, setExecutionOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [executionError, setExecutionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOutput, setShowOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Piston API runtimes\n    const [runtimes, setRuntimes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [runtimesLoaded, setRuntimesLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    // Theme and minimap state (ensure these are always defined at the top)\n    const { theme, setTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme)();\n    const [showShortcuts, setShowShortcuts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [minimapEnabled, setMinimapEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Language-specific code snippets\n    const SNIPPETS = {\n        javascript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            }\n        ],\n        typescript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction(): void {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            },\n            {\n                label: \"Type Declaration\",\n                value: \"type MyType = {\\n  key: string;\\n};\"\n            }\n        ],\n        python3: [\n            {\n                label: \"Function\",\n                value: \"def my_function():\\n    # code\\n    pass\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in range(10):\\n    # code\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition:\\n    # code\"\n            },\n            {\n                label: \"Print\",\n                value: \"print('Hello, World!')\"\n            }\n        ],\n        java: [\n            {\n                label: \"Main Method\",\n                value: \"public static void main(String[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"System.out.println(\\\"Hello, World!\\\");\"\n            }\n        ],\n        csharp: [\n            {\n                label: \"Main Method\",\n                value: \"static void Main(string[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"Console.WriteLine(\\\"Hello, World!\\\");\"\n            }\n        ],\n        c: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"printf(\\\"Hello, World!\\\\n\\\");\"\n            }\n        ],\n        cpp: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"std::cout << \\\"Hello, World!\\\" << std::endl;\"\n            }\n        ],\n        go: [\n            {\n                label: \"Main Function\",\n                value: \"func main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i := 0; i < 10; i++ {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"fmt.Println(\\\"Hello, World!\\\")\"\n            }\n        ],\n        ruby: [\n            {\n                label: \"Method\",\n                value: \"def my_method\\n  # code\\nend\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..9\\n  # code\\nend\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition\\n  # code\\nend\"\n            },\n            {\n                label: \"Print\",\n                value: \"puts 'Hello, World!'\"\n            }\n        ],\n        rust: [\n            {\n                label: \"Main Function\",\n                value: \"fn main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..10 {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"println!(\\\"Hello, World!\\\");\"\n            }\n        ],\n        php: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for ($i = 0; $i < 10; $i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if ($condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"echo 'Hello, World!';\"\n            }\n        ]\n    };\n    // Declare the `socketService` instance at the top of the file\n    const socketService = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n    // Leave room handler\n    const handleLeaveRoom = ()=>{\n        socketService.leaveRoom(roomId);\n        router.push(\"/dashboard\");\n    };\n    // Handle editor mounting\n    const handleEditorDidMount = (editor)=>{\n        editorRef.current = editor;\n        setIsLoading(false);\n        // Auto-focus the editor after mounting\n        setTimeout(()=>{\n            if (editor) {\n                editor.focus();\n                // Add keyboard shortcut (Ctrl+Enter) to run code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.CtrlCmd | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.Enter, ()=>{\n                    executeCode();\n                });\n                // Add keyboard shortcut (Shift+Alt+F) to format code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Shift | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Alt | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.KeyF, ()=>{\n                    formatCurrentCode();\n                });\n                // Add teacher selection change listener\n                if (userRole === 'teacher') {\n                    editor.onDidChangeCursorSelection((e)=>{\n                        const selection = e.selection;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        // Only broadcast if there's an actual selection (not just cursor position)\n                        if (!selection.isEmpty() && roomId) {\n                            console.log('Teacher made selection:', selection);\n                            socketServiceInstance.sendTeacherSelection(roomId, {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            });\n                        } else if (roomId) {\n                            // Clear selection when teacher deselects\n                            socketServiceInstance.clearTeacherSelection(roomId);\n                        }\n                    });\n                }\n            }\n        }, 500) // Small delay to ensure the editor is fully ready\n        ;\n    };\n    // Track if the change is from a remote update\n    const isRemoteUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Create a throttled version of the code change handler\n    // This prevents sending too many updates when typing quickly\n    // but ensures updates are sent at regular intervals\n    const throttledCodeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default()({\n        \"CodeEditor.useRef\": (code)=>{\n            if (roomId && isConnected) {\n                console.log(\"Sending throttled code update to room \".concat(roomId, \", length: \").concat(code.length));\n                socketService.sendCodeChange(roomId, code);\n            }\n        }\n    }[\"CodeEditor.useRef\"], 50) // Use throttle with a short interval for more responsive updates\n    ).current;\n    // Create a debounced version of the typing notification\n    const debouncedTypingNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default()({\n        \"CodeEditor.useCallback[debouncedTypingNotification]\": ()=>{\n            if (roomId && isConnected) {\n                // Always send the current username with typing notifications\n                console.log(\"Sending typing notification with username: \".concat(username));\n                socketService.sendTyping(roomId, username);\n            }\n        }\n    }[\"CodeEditor.useCallback[debouncedTypingNotification]\"], 1000), [\n        roomId,\n        isConnected,\n        username\n    ]);\n    // Handle editor change\n    const handleEditorChange = (value)=>{\n        if (typeof value !== \"string\") return;\n        // Make sure loading is off during code changes\n        setIsLoading(false);\n        // If this change is from a remote update, just update the state and return\n        if (isRemoteUpdate.current) {\n            console.log(\"Handling remote update, not emitting\");\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            isRemoteUpdate.current = false;\n            return;\n        }\n        // Only process if the value actually changed\n        if (value !== latestCodeRef.current) {\n            // Update local state immediately\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            // Send typing notification (debounced)\n            debouncedTypingNotification();\n            // Send code update (throttled)\n            throttledCodeChange(value);\n            // If not connected, try to reconnect\n            if (!isConnected) {\n                console.log(\"Socket not connected, attempting to reconnect\");\n                socketService.connect();\n            }\n        }\n    };\n    // Copy code to clipboard\n    const copyCodeToClipboard = ()=>{\n        if (navigator.clipboard && code) {\n            navigator.clipboard.writeText(code).then(()=>{\n                setCopySuccess(true);\n                setTimeout(()=>setCopySuccess(false), 2000);\n            }).catch((err)=>{\n                console.error('Failed to copy code: ', err);\n            });\n        }\n    };\n    // Toggle user list\n    const toggleUserList = ()=>{\n        setShowUserList((prev)=>!prev);\n    };\n    // Change language\n    const changeLanguage = (lang)=>{\n        setLanguage(lang);\n        // Log available runtimes for this language\n        if (runtimesLoaded && runtimes.length > 0) {\n            const availableRuntimes = runtimes.filter((runtime)=>runtime.language === lang || runtime.aliases && runtime.aliases.includes(lang));\n            if (availableRuntimes.length > 0) {\n                console.log(\"Available runtimes for \".concat(lang, \":\"), availableRuntimes);\n            } else {\n                console.log(\"No runtimes available for \".concat(lang));\n            }\n        }\n    };\n    // Format code\n    const formatCurrentCode = ()=>{\n        if (!code || !editorRef.current) return;\n        try {\n            // Format the code based on the current language\n            const formattedCode = (0,_utils_formatCode__WEBPACK_IMPORTED_MODULE_8__.formatCode)(code, language);\n            // Only update if the code actually changed\n            if (formattedCode !== code) {\n                // Update the editor\n                const model = editorRef.current.getModel();\n                if (model) {\n                    // Store current cursor position/selection\n                    const currentPosition = editorRef.current.getPosition();\n                    const currentSelection = editorRef.current.getSelection();\n                    // Update the editor content\n                    editorRef.current.executeEdits('format', [\n                        {\n                            range: model.getFullModelRange(),\n                            text: formattedCode,\n                            forceMoveMarkers: true\n                        }\n                    ]);\n                    // Restore cursor position if possible\n                    if (currentPosition) {\n                        editorRef.current.setPosition(currentPosition);\n                    }\n                    if (currentSelection) {\n                        editorRef.current.setSelection(currentSelection);\n                    }\n                    // Update state and ref\n                    setCode(formattedCode);\n                    latestCodeRef.current = formattedCode;\n                    // Send the formatted code to other users\n                    if (roomId && isConnected) {\n                        console.log(\"Sending formatted code to room \".concat(roomId, \", length: \").concat(formattedCode.length));\n                        socketService.sendCodeChange(roomId, formattedCode);\n                    }\n                    // Show a success message\n                    console.log('Code formatted successfully');\n                }\n            } else {\n                console.log('Code is already formatted');\n            }\n        } catch (error) {\n            console.error('Error formatting code:', error);\n        }\n    };\n    // Clear execution output\n    const clearOutput = ()=>{\n        setExecutionOutput(null);\n        setExecutionError(null);\n    };\n    // Fetch and store available runtimes from Piston API\n    const fetchRuntimes = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get('https://emkc.org/api/v2/piston/runtimes');\n            console.log('Piston API status:', response.status);\n            console.log('Available runtimes:', response.data);\n            // Store the runtimes in state\n            setRuntimes(response.data);\n            setRuntimesLoaded(true);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error fetching Piston API runtimes:', error);\n            return {\n                success: false,\n                error: axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) ? \"\".concat(error.message, \" - \").concat(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) || 'Unknown', \" \").concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText) || '') : String(error)\n            };\n        }\n    };\n    // Check if the Piston API is available\n    const checkPistonAPI = async ()=>{\n        // If we haven't loaded runtimes yet, fetch them\n        if (!runtimesLoaded) {\n            return await fetchRuntimes();\n        }\n        // If we already have runtimes, just return success\n        if (runtimes.length > 0) {\n            return {\n                success: true,\n                data: runtimes\n            };\n        }\n        // If we've tried to load runtimes but have none, try again\n        return await fetchRuntimes();\n    };\n    // Test the Piston API with a hardcoded sample payload\n    const testPistonAPI = async ()=>{\n        setIsExecuting(true);\n        setExecutionError(null);\n        setExecutionOutput(null);\n        setShowOutput(true);\n        try {\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Find JavaScript runtime\n            const jsRuntimes = apiStatus.data.filter((runtime)=>runtime.language === \"javascript\" || runtime.aliases && runtime.aliases.includes(\"javascript\"));\n            if (jsRuntimes.length === 0) {\n                setExecutionError('No JavaScript runtime found. Please try a different language.');\n                return;\n            }\n            const jsRuntime = jsRuntimes[0];\n            console.log(\"Using JavaScript runtime: \".concat(jsRuntime.language, \" \").concat(jsRuntime.version));\n            // Sample JavaScript code that should work\n            const samplePayload = {\n                language: jsRuntime.language,\n                version: jsRuntime.version,\n                files: [\n                    {\n                        name: \"main.js\",\n                        content: \"console.log('Hello, World!');\"\n                    }\n                ],\n                stdin: \"\",\n                args: []\n            };\n            console.log('Testing Piston API with sample payload:', JSON.stringify(samplePayload, null, 2));\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', samplePayload);\n            console.log('Sample execution response:', response.data);\n            if (response.data.run) {\n                setExecutionOutput(\"API Test Successful!\\n\\n\" + \"Output: \" + response.data.run.stdout + \"\\n\\n\" + \"The API is working correctly. You can now run your own code.\");\n            } else {\n                setExecutionError('API test failed. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error testing Piston API:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                setExecutionError(\"API Test Failed: \".concat(error.response.status, \" \").concat(error.response.statusText, \"\\n\\n\") + JSON.stringify(error.response.data, null, 2));\n            } else {\n                setExecutionError(error instanceof Error ? \"API Test Error: \".concat(error.message) : 'An unknown error occurred while testing the API.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // Execute code using Piston API\n    const executeCode = async ()=>{\n        if (!code || isExecuting) return;\n        // Map Monaco editor language to Piston API language\n        const languageMap = {\n            javascript: \"javascript\",\n            typescript: \"typescript\",\n            python: \"python3\",\n            python3: \"python3\",\n            java: \"java\",\n            csharp: \"csharp\",\n            c: \"c\",\n            cpp: \"cpp\",\n            go: \"go\",\n            ruby: \"ruby\",\n            rust: \"rust\",\n            php: \"php\"\n        };\n        // Get the mapped language\n        const pistonLanguage = languageMap[language] || language;\n        // Check if we have runtimes loaded\n        if (!runtimesLoaded || runtimes.length === 0) {\n            setExecutionError('Runtimes not loaded yet. Please try again in a moment.');\n            setShowOutput(true);\n            return;\n        }\n        // Find available runtimes for the selected language\n        const availableRuntimes = runtimes.filter((runtime)=>runtime.language === pistonLanguage || runtime.aliases && runtime.aliases.includes(pistonLanguage));\n        // Check if the language is supported\n        if (availableRuntimes.length === 0) {\n            // Get a list of supported languages from the runtimes\n            const supportedLanguages = [\n                ...new Set(runtimes.flatMap((runtime)=>[\n                        runtime.language,\n                        ...runtime.aliases || []\n                    ]))\n            ].sort();\n            setExecutionError(\"The language '\".concat(language, \"' (mapped to '\").concat(pistonLanguage, \"') is not supported by the Piston API.\\n\\n\") + \"Supported languages: \".concat(supportedLanguages.join(', ')));\n            setShowOutput(true);\n            return;\n        }\n        // Get the latest version of the runtime\n        const selectedRuntime = availableRuntimes[0];\n        try {\n            setIsExecuting(true);\n            setExecutionError(null);\n            setExecutionOutput(null);\n            setShowOutput(true);\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Determine file extension based on language\n            let fileExtension = '';\n            if (selectedRuntime.language === 'python3' || selectedRuntime.language === 'python') fileExtension = '.py';\n            else if (selectedRuntime.language === 'javascript') fileExtension = '.js';\n            else if (selectedRuntime.language === 'typescript') fileExtension = '.ts';\n            else if (selectedRuntime.language === 'java') fileExtension = '.java';\n            else if (selectedRuntime.language === 'csharp') fileExtension = '.cs';\n            else if (selectedRuntime.language === 'c') fileExtension = '.c';\n            else if (selectedRuntime.language === 'cpp') fileExtension = '.cpp';\n            else if (selectedRuntime.language === 'go') fileExtension = '.go';\n            else if (selectedRuntime.language === 'rust') fileExtension = '.rs';\n            else if (selectedRuntime.language === 'ruby') fileExtension = '.rb';\n            else if (selectedRuntime.language === 'php') fileExtension = '.php';\n            else fileExtension = \".\".concat(selectedRuntime.language);\n            console.log(\"Selected runtime: \".concat(selectedRuntime.language, \" \").concat(selectedRuntime.version));\n            // Prepare the payload according to Piston API documentation\n            const payload = {\n                language: selectedRuntime.language,\n                version: selectedRuntime.version,\n                files: [\n                    {\n                        name: \"main\".concat(fileExtension),\n                        content: code\n                    }\n                ],\n                stdin: '',\n                args: [],\n                compile_timeout: 10000,\n                run_timeout: 5000\n            };\n            // Log the payload for debugging\n            console.log(\"Executing \".concat(pistonLanguage, \" code with payload:\"), JSON.stringify(payload, null, 2));\n            // Make the API request\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', payload);\n            console.log('Execution response:', response.data);\n            const result = response.data;\n            if (result.run) {\n                // Format the output\n                let output = '';\n                let hasOutput = false;\n                // Add compile output if available (for compiled languages)\n                if (result.compile && result.compile.stderr) {\n                    output += \"Compilation output:\\n\".concat(result.compile.stderr, \"\\n\\n\");\n                    hasOutput = true;\n                }\n                // Add standard output\n                if (result.run.stdout) {\n                    output += result.run.stdout;\n                    hasOutput = true;\n                }\n                // Add error output\n                if (result.run.stderr) {\n                    if (hasOutput) output += '\\n';\n                    output += \"Error output:\\n\".concat(result.run.stderr);\n                    hasOutput = true;\n                }\n                // Add exit code if non-zero\n                if (result.run.code !== 0) {\n                    if (hasOutput) output += '\\n';\n                    output += \"\\nProcess exited with code \".concat(result.run.code);\n                    hasOutput = true;\n                }\n                if (!hasOutput) {\n                    output = 'Program executed successfully with no output.';\n                }\n                setExecutionOutput(output);\n            } else {\n                setExecutionError('Failed to execute code. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error executing code:', error);\n            // Handle Axios errors with more detailed information\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                const statusCode = error.response.status;\n                const responseData = error.response.data;\n                console.error('API Error Details:', {\n                    status: statusCode,\n                    data: responseData\n                });\n                // Format a more helpful error message\n                if (statusCode === 400) {\n                    setExecutionError(\"API Error (400 Bad Request): \".concat(JSON.stringify(responseData), \"\\n\\n\") + 'This usually means the API request format is incorrect. ' + 'Please check the console for more details.');\n                } else if (statusCode === 429) {\n                    setExecutionError('Rate limit exceeded. Please try again later.');\n                } else {\n                    setExecutionError(\"API Error (\".concat(statusCode, \"): \").concat(JSON.stringify(responseData), \"\\n\\n\") + 'Please check the console for more details.');\n                }\n            } else {\n                // Handle non-Axios errors\n                setExecutionError(error instanceof Error ? \"Error: \".concat(error.message) : 'An unknown error occurred while executing the code.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // This function will be called when we receive a code update from another user\n    const handleCodeUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleCodeUpdate]\": (incomingCode)=>{\n            console.log(\"Remote code update received, length:\", incomingCode.length);\n            // Make sure loading is off during code updates\n            setIsLoading(false);\n            // Only update if the code is different from our latest code\n            if (incomingCode !== latestCodeRef.current) {\n                try {\n                    // Set the flag to indicate this is a remote update\n                    isRemoteUpdate.current = true;\n                    // Update the editor if it's mounted\n                    if (editorRef.current) {\n                        console.log(\"Updating editor with remote code\");\n                        // Use the editor's model to update the value to preserve cursor position\n                        const model = editorRef.current.getModel();\n                        if (model) {\n                            // Store current cursor position/selection\n                            const currentPosition = editorRef.current.getPosition();\n                            const currentSelection = editorRef.current.getSelection();\n                            // Use executeEdits with better handling of cursor position\n                            editorRef.current.executeEdits('remote', [\n                                {\n                                    range: model.getFullModelRange(),\n                                    text: incomingCode,\n                                    forceMoveMarkers: true\n                                }\n                            ]);\n                            // Restore cursor position if possible\n                            if (currentPosition) {\n                                editorRef.current.setPosition(currentPosition);\n                            }\n                            if (currentSelection) {\n                                editorRef.current.setSelection(currentSelection);\n                            }\n                            // Also update our state and ref\n                            setCode(incomingCode);\n                            latestCodeRef.current = incomingCode;\n                        }\n                    } else {\n                        // If editor isn't mounted yet, just update the state and ref\n                        console.log(\"Editor not mounted, updating state only\");\n                        setCode(incomingCode);\n                        latestCodeRef.current = incomingCode;\n                        isRemoteUpdate.current = false;\n                    }\n                } catch (error) {\n                    console.error(\"Error updating editor with remote code:\", error);\n                    isRemoteUpdate.current = false;\n                }\n            } else {\n                console.log(\"Remote code matches current code, ignoring\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleCodeUpdate]\"], []);\n    // State to track cursor positions\n    const [cursorPositions, setCursorPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Emit cursor movement\n    const handleMouseMove = (e)=>{\n        const position = {\n            x: e.clientX,\n            y: e.clientY\n        };\n        _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().sendCursorMove(roomId, username, position);\n    };\n    // Listen for cursor movement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            const handleCursorMove = {\n                \"CodeEditor.useEffect.handleCursorMove\": (param)=>{\n                    let { userId, position } = param;\n                    setCursorPositions({\n                        \"CodeEditor.useEffect.handleCursorMove\": (prev)=>({\n                                ...prev,\n                                [userId]: position\n                            })\n                    }[\"CodeEditor.useEffect.handleCursorMove\"]);\n                }\n            }[\"CodeEditor.useEffect.handleCursorMove\"];\n            _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().on(\"cursor-move\", handleCursorMove);\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().off(\"cursor-move\", handleCursorMove);\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId\n    ]);\n    // Render cursors\n    const renderCursors = ()=>{\n        return Object.entries(cursorPositions).map((param)=>{\n            let [userId, position] = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    left: position.x,\n                    top: position.y,\n                    backgroundColor: \"red\",\n                    width: 10,\n                    height: 10,\n                    borderRadius: \"50%\",\n                    pointerEvents: \"none\"\n                }\n            }, userId, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 742,\n                columnNumber: 7\n            }, this);\n        });\n    };\n    // Fetch runtimes when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            fetchRuntimes();\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    // Handle request for initial code from new users\n    const handleGetInitialCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleGetInitialCode]\": (data)=>{\n            console.log(\"Received request for initial code from \".concat(data.requestingUsername, \" (\").concat(data.requestingUserId, \")\"));\n            // Only respond if we have code and we're connected\n            if (roomId && latestCodeRef.current && latestCodeRef.current.trim() !== \"// Start coding...\") {\n                const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                console.log(\"Sending initial code to \".concat(data.requestingUsername, \", length: \").concat(latestCodeRef.current.length));\n                socketServiceInstance.sendInitialCode(roomId, latestCodeRef.current, data.requestingUserId);\n            } else {\n                console.log(\"Not sending initial code - no meaningful code to share or not in room\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleGetInitialCode]\"], [\n        roomId\n    ]);\n    // Handle receiving initial code as a new user\n    const handleInitialCodeReceived = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleInitialCodeReceived]\": (data)=>{\n            console.log(\"Received initial code, length: \".concat(data.code.length));\n            // Only apply initial code if we don't have meaningful code yet\n            // This prevents overwriting code that the user might have already started typing\n            if (latestCodeRef.current === \"// Start coding...\" || latestCodeRef.current.trim() === \"\") {\n                console.log(\"Applying received initial code\");\n                // Set the flag to indicate this is a remote update\n                isRemoteUpdate.current = true;\n                // Update the code state and ref\n                setCode(data.code);\n                latestCodeRef.current = data.code;\n                // Update the editor if it's mounted\n                if (editorRef.current) {\n                    editorRef.current.setValue(data.code);\n                }\n            } else {\n                console.log(\"Not applying initial code - user already has meaningful code\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleInitialCodeReceived]\"], []);\n    // Handle teacher selection highlighting\n    const handleTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherSelection]\": (data)=>{\n            console.log(\"Received teacher selection from \".concat(data.teacherName, \":\"), data.selection);\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't show teacher highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model || !data.selection) {\n                    return;\n                }\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.selection.startLineNumber, data.selection.startColumn, data.selection.endLineNumber, data.selection.endColumn);\n                // Clear previous teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-selection-highlight',\n                            hoverMessage: {\n                                value: \"Teacher \".concat(data.teacherName, \" highlighted this text\")\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges\n                        }\n                    }\n                ]);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error applying teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle clearing teacher selection\n    const handleClearTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherSelection]\": (data)=>{\n            console.log(\"Teacher \".concat(data.teacherName, \" cleared selection\"));\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                // Clear all teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, []);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error clearing teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            // Handle user typing notifications\n            const handleUserTyping = {\n                \"CodeEditor.useEffect.handleUserTyping\": (param)=>{\n                    let { username, userId } = param;\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    // Don't show typing indicator for current user\n                    if (userId && userId === currentUserId) {\n                        return;\n                    }\n                    // Use the exact username without any modifications\n                    // This ensures we display exactly what the user entered on the dashboard\n                    setTypingUser(username);\n                    console.log(\"User typing: \".concat(username));\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserTyping\": ()=>setTypingUser(null)\n                    }[\"CodeEditor.useEffect.handleUserTyping\"], 2000);\n                }\n            }[\"CodeEditor.useEffect.handleUserTyping\"];\n            // Handle user list updates from both user-joined/user-left and room-users-updated events\n            const handleUserListUpdate = {\n                \"CodeEditor.useEffect.handleUserListUpdate\": (data)=>{\n                    console.log('User list update received:', data);\n                    let users = [];\n                    // Handle different event formats\n                    if (Array.isArray(data)) {\n                        // This is from user-joined or user-left events\n                        users = data;\n                    } else if (data && Array.isArray(data.users)) {\n                        // This is from room-users-updated event\n                        users = data.users.map({\n                            \"CodeEditor.useEffect.handleUserListUpdate\": (user)=>{\n                                if (typeof user === 'string') {\n                                    // If it's just a username string, convert to user object\n                                    return {\n                                        username: user\n                                    };\n                                }\n                                return user;\n                            }\n                        }[\"CodeEditor.useEffect.handleUserListUpdate\"]);\n                    }\n                    console.log('Processed users:', users);\n                    console.log('Current username state:', username);\n                    console.log('Stored username:', localStorage.getItem('username'));\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    console.log('Current userId from localStorage:', currentUserId);\n                    // Filter out any invalid users and map to display names\n                    const usernames = users.filter({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>user && user.username\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]).map({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>{\n                            const displayName = user.username;\n                            const isCurrentUser = user.userId === currentUserId;\n                            return isCurrentUser ? \"\".concat(displayName, \" (you)\") : displayName;\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]);\n                    console.log('Setting active users:', usernames);\n                    // Only update if we have users to display\n                    if (usernames.length > 0) {\n                        setActiveUsers(usernames);\n                    } else if (activeUsers.length === 0) {\n                        // If we don't have users from the server but we're connected, add ourselves\n                        const storedUsername = localStorage.getItem('username');\n                        if (storedUsername) {\n                            setActiveUsers([\n                                \"\".concat(storedUsername, \" (you)\")\n                            ]);\n                        }\n                    }\n                    // Log the current state of the active users list\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserListUpdate\": ()=>{\n                            console.log('Active users state after update:', activeUsers);\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate\"], 100);\n                }\n            }[\"CodeEditor.useEffect.handleUserListUpdate\"];\n            // Register event listeners\n            const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n            socketServiceInstance.on('code-update', handleCodeUpdate);\n            socketServiceInstance.on('user-typing', handleUserTyping);\n            socketServiceInstance.on('user-joined', handleUserListUpdate);\n            socketServiceInstance.on('user-left', handleUserListUpdate);\n            socketServiceInstance.on('room-users-updated', handleUserListUpdate);\n            socketServiceInstance.on('get-initial-code', handleGetInitialCode);\n            socketServiceInstance.on('initial-code-received', handleInitialCodeReceived);\n            socketServiceInstance.on('teacher-selection', handleTeacherSelection);\n            socketServiceInstance.on('clear-teacher-selection', handleClearTeacherSelection);\n            // Join the room when component mounts\n            if (roomId) {\n                console.log(\"Joining room:\", roomId);\n                // Only show loading on initial join, not for code updates\n                if (!editorRef.current) {\n                    setIsLoading(true);\n                }\n                const joinRoom = {\n                    \"CodeEditor.useEffect.joinRoom\": async ()=>{\n                        try {\n                            if (socketServiceInstance.isConnected()) {\n                                console.log(\"Socket connected, joining room\");\n                                const { username: validatedUsername, users, role } = await socketServiceInstance.joinRoom(roomId, username);\n                                console.log(\"Successfully joined room:\", roomId);\n                                console.log(\"Users in room:\", users);\n                                console.log(\"User role:\", role);\n                                if (validatedUsername !== username) {\n                                    console.log(\"Server validated username from \".concat(username, \" to \").concat(validatedUsername));\n                                    setUsername(validatedUsername);\n                                    localStorage.setItem('username', validatedUsername);\n                                }\n                                // Set user role\n                                if (role) {\n                                    setUserRole(role);\n                                    console.log(\"User role set to: \".concat(role));\n                                }\n                                if (users && Array.isArray(users)) {\n                                    console.log('Setting active users from join response');\n                                    handleUserListUpdate(users);\n                                }\n                                setIsLoading(false);\n                            } else {\n                                console.log(\"Socket not connected, trying to connect...\");\n                                await new Promise({\n                                    \"CodeEditor.useEffect.joinRoom\": (resolve)=>socketServiceInstance.onConnect({\n                                            \"CodeEditor.useEffect.joinRoom\": ()=>resolve()\n                                        }[\"CodeEditor.useEffect.joinRoom\"])\n                                }[\"CodeEditor.useEffect.joinRoom\"]);\n                                // After connect, try join again\n                                await joinRoom();\n                                return;\n                            }\n                        } catch (error) {\n                            console.error(\"Error joining room:\", error);\n                            setIsLoading(false);\n                        }\n                    }\n                }[\"CodeEditor.useEffect.joinRoom\"];\n                // Start the join process\n                joinRoom();\n                // Set a timeout to hide the loading screen even if the join fails\n                setTimeout({\n                    \"CodeEditor.useEffect\": ()=>{\n                        setIsLoading(false);\n                    }\n                }[\"CodeEditor.useEffect\"], 3000);\n            }\n            // Clean up event listeners when component unmounts\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    // Use an instance of SocketService\n                    const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                    socketServiceInstance.off('code-update', handleCodeUpdate);\n                    socketServiceInstance.off('user-typing', handleUserTyping);\n                    socketServiceInstance.off('user-joined', handleUserListUpdate);\n                    socketServiceInstance.off('user-left', handleUserListUpdate);\n                    socketServiceInstance.off('room-users-updated', handleUserListUpdate);\n                    socketServiceInstance.off('get-initial-code', handleGetInitialCode);\n                    socketServiceInstance.off('initial-code-received', handleInitialCodeReceived);\n                    socketServiceInstance.off('teacher-selection', handleTeacherSelection);\n                    socketServiceInstance.off('clear-teacher-selection', handleClearTeacherSelection);\n                    // Leave the room when component unmounts\n                    if (roomId) {\n                        socketServiceInstance.leaveRoom(roomId);\n                    }\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId,\n        initialUsername,\n        username,\n        handleCodeUpdate,\n        handleGetInitialCode,\n        handleInitialCodeReceived,\n        handleTeacherSelection,\n        handleClearTeacherSelection\n    ]);\n    // Download code as file\n    const handleDownload = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"code-\".concat(roomId || \"session\", \".txt\");\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    // Upload code from file\n    const handleUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            var _event_target;\n            if (typeof ((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result) === \"string\") {\n                setCode(event.target.result);\n                latestCodeRef.current = event.target.result;\n            }\n        };\n        reader.readAsText(file);\n    };\n    // Show Monaco's find/replace dialog\n    const handleFindReplace = ()=>{\n        if (editorRef.current) {\n            const action = editorRef.current.getAction(\"actions.find\");\n            if (action) action.run();\n        }\n    };\n    // Insert code snippet\n    const insertSnippet = (snippet)=>{\n        if (editorRef.current) {\n            const model = editorRef.current.getModel();\n            const position = editorRef.current.getPosition();\n            if (model && position) {\n                editorRef.current.executeEdits(\"snippet\", [\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(position.lineNumber, position.column, position.lineNumber, position.column),\n                        text: snippet,\n                        forceMoveMarkers: true\n                    }\n                ]);\n                editorRef.current.focus();\n            }\n        }\n    };\n    // Use the instance of SocketService for the connect method\n    const handleReconnect = ()=>{\n        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n        socketServiceInstance.connect();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-[80vh] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-800 shadow-xl flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"bg-gray-100 dark:bg-zinc-800 px-4 py-2 flex flex-wrap items-center justify-between border-b border-gray-200 dark:border-gray-700 gap-2 min-h-[48px]\",\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    style: {\n                        zIndex: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-3 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 min-w-[90px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-400 truncate\",\n                                            children: isConnected ? 'Connected' : 'Disconnected'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1102,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded ml-1 transition-colors\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1106,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: language,\n                                    onChange: (e)=>changeLanguage(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"javascript\",\n                                            children: \"JavaScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"typescript\",\n                                            children: \"TypeScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"python3\",\n                                            children: \"Python\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1123,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"java\",\n                                            children: \"Java\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1124,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"csharp\",\n                                            children: \"C#\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"c\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"cpp\",\n                                            children: \"C++\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"go\",\n                                            children: \"Go\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ruby\",\n                                            children: \"Ruby\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1129,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rust\",\n                                            children: \"Rust\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"php\",\n                                            children: \"PHP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1131,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: formatCurrentCode,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm bg-blue-600 hover:bg-blue-700 text-white transition-colors whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiAlignLeft, {\n                                            size: 14\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Format\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: executeCode,\n                                    disabled: isExecuting,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm \".concat(isExecuting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700', \" text-white transition-colors whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: isExecuting ? 1 : 1.05\n                                    },\n                                    whileTap: {\n                                        scale: isExecuting ? 1 : 0.95\n                                    },\n                                    children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                                className: \"animate-spin\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1155,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Running...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1156,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiPlay, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1160,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Run Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1161,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Room:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-white dark:bg-zinc-700 px-2 py-1 rounded font-mono text-xs truncate max-w-[120px]\",\n                                            children: roomId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                            onClick: copyCodeToClipboard,\n                                            className: \"text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            title: \"Copy code to clipboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCopy, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1177,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1098,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-2 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowOutput((prev)=>!prev),\n                                    className: \"flex items-center space-x-1 text-sm \".concat(showOutput ? 'text-blue-500 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300', \" hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCode, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Output\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: toggleUserList,\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUsers, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                activeUsers.length,\n                                                \" online\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1202,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"flex items-center space-x-1 text-sm text-yellow-600 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSun, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1213,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiMoon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1213,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setMinimapEnabled((v)=>!v),\n                                    className: \"flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Minimap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Minimap\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1224,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleFindReplace,\n                                    className: \"flex items-center space-x-1 text-sm text-green-600 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Find/Replace\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSearch, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1235,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1228,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleDownload,\n                                    className: \"flex items-center space-x-1 text-sm text-indigo-600 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Download Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiDownload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1246,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"flex items-center space-x-1 text-sm text-pink-600 dark:text-pink-300 bg-pink-100 dark:bg-pink-900/30 hover:bg-pink-200 dark:hover:bg-pink-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Upload Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUpload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1257,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \".js,.ts,.py,.java,.c,.cpp,.go,.rb,.rs,.php,.txt\",\n                                    style: {\n                                        display: \"none\"\n                                    },\n                                    onChange: handleUpload\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    onChange: (e)=>e.target.value && insertSnippet(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[100px]\",\n                                    defaultValue: \"\",\n                                    title: \"Insert Snippet\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Snippets\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1274,\n                                            columnNumber: 15\n                                        }, this),\n                                        (SNIPPETS[language] || SNIPPETS['javascript']).map((snippet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: snippet.value,\n                                                children: snippet.label\n                                            }, snippet.label, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1276,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1268,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowShortcuts(true),\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Keyboard Shortcuts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiHelpCircle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1288,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleLeaveRoom,\n                                    className: \"flex items-center space-x-1 text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors px-2 py-1 rounded-md ml-2 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Leave Room\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1298,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1292,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1091,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showUserList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute right-0 top-12 bg-white dark:bg-zinc-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 w-48 overflow-hidden\",\n                        initial: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            height: 'auto'\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 border-b border-gray-200 dark:border-gray-700 font-medium text-sm text-gray-600 dark:text-gray-300\",\n                                children: \"Active Users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1313,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"max-h-48 overflow-y-auto\",\n                                children: activeUsers.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.li, {\n                                        className: \"px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-zinc-700 flex items-center space-x-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full bg-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1325,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: user\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1326,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1318,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1316,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1306,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"h-[calc(100%-40px)] \".concat(showOutput ? 'pb-[33%]' : ''),\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.4,\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                height: \"100%\",\n                                defaultLanguage: language,\n                                defaultValue: code,\n                                onChange: handleEditorChange,\n                                onMount: handleEditorDidMount,\n                                theme: theme === \"dark\" ? \"vs-dark\" : \"light\",\n                                options: {\n                                    minimap: {\n                                        enabled: minimapEnabled\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1344,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1343,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1341,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1335,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: typingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 left-4 text-sm text-gray-400 bg-gray-800/80 px-3 py-1 rounded-full backdrop-blur-sm\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            typingUser,\n                            \" is typing...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1360,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1358,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gray-900 text-white border-t border-gray-700 h-1/3 overflow-hidden flex flex-col\",\n                        initial: {\n                            height: 0\n                        },\n                        animate: {\n                            height: '33%'\n                        },\n                        exit: {\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 bg-gray-800 border-b border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1383,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearOutput,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1385,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: testPistonAPI,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-blue-400 hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Test API\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1392,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowOutput(false),\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1399,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1384,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1382,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-auto font-mono text-sm whitespace-pre-wrap\",\n                                children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1410,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Executing code...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1411,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1409,\n                                    columnNumber: 19\n                                }, this) : executionError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400\",\n                                    children: executionError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1414,\n                                    columnNumber: 19\n                                }, this) : executionOutput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: executionOutput\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1416,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: 'Click \"Run Code\" to execute your code.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1418,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1407,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1375,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1373,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: copySuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 right-4 text-sm text-white bg-green-500 px-3 py-1 rounded-full shadow-lg\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: \"Copied to clipboard!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1428,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1426,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showShortcuts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 max-w-md w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-700 dark:hover:text-white\",\n                                    onClick: ()=>setShowShortcuts(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1450,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    children: \"Keyboard Shortcuts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1456,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Run Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1458,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+Enter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1458,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Format Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1459,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Shift+Alt+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1459,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Find/Replace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1460,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1460,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Download Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1461,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+D\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1461,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Upload Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1462,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+U\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1462,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Minimap:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1463,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+M\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1463,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Theme:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1464,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+T\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1464,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Show Shortcuts:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1465,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+H\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1465,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1457,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1449,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1443,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1441,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 1089,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 1087,\n        columnNumber: 5\n    }, this);\n} // Keyboard shortcuts global handler\n // useEffect(() => {\n //   const handler = (e: KeyboardEvent) => {\n //     if (e.altKey && e.key.toLowerCase() === \"d\") {\n //       e.preventDefault();\n //       handleDownload();\n //     } else if (e.altKey && e.key.toLowerCase() === \"u\") {\n //       e.preventDefault();\n //       fileInputRef.current?.click();\n //     } else if (e.altKey && e.key.toLowerCase() === \"m\") {\n //       e.preventDefault();\n //       setMinimapEnabled((v: boolean) => !v);\n //     } else if (e.altKey && e.key.toLowerCase() === \"t\") {\n //       e.preventDefault();\n //       setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n //     } else if (e.altKey && e.key.toLowerCase() === \"h\") {\n //       e.preventDefault();\n //       setShowShortcuts(true);\n //     }\n //   };\n //   window.addEventListener(\"keydown\", handler);\n //   return () => window.removeEventListener(\"keydown\", handler);\n // }, [theme, setTheme]);\n_s(CodeEditor, \"Cf9tT16S7JToKFFdKJngpzRFyDc=\", false, function() {\n    return [\n        _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme\n    ];\n});\n_c = CodeEditor;\nvar _c;\n$RefreshReg$(_c, \"CodeEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CodeEditor.tsx\n"));

/***/ })

});