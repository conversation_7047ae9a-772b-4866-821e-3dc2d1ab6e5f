{"version": 3, "sources": ["../../../locales/nn-NO.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Lim inn\",\n    \"pasteAsPlaintext\": \"\",\n    \"pasteCharts\": \"Lim inn diagram\",\n    \"selectAll\": \"Vel alt\",\n    \"multiSelect\": \"Legg til element i utval\",\n    \"moveCanvas\": \"Flytt lerretet\",\n    \"cut\": \"<PERSON>lip<PERSON> ut\",\n    \"copy\": \"Kopier\",\n    \"copyAsPng\": \"Kopier til utklippstavla som PNG\",\n    \"copyAsSvg\": \"Kopier til utklippstavla som SVG\",\n    \"copyText\": \"\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Flytt framover\",\n    \"sendToBack\": \"Send heilt bak\",\n    \"bringToFront\": \"Flytt heilt fram\",\n    \"sendBackward\": \"Send bakover\",\n    \"delete\": \"Slett\",\n    \"copyStyles\": \"Kopier stilar\",\n    \"pasteStyles\": \"Lim inn stilar\",\n    \"stroke\": \"Strek\",\n    \"background\": \"Bakgrunn\",\n    \"fill\": \"Fyll\",\n    \"strokeWidth\": \"Strekbreidd\",\n    \"strokeStyle\": \"Strekstil\",\n    \"strokeStyle_solid\": \"Solid\",\n    \"strokeStyle_dashed\": \"Stipla\",\n    \"strokeStyle_dotted\": \"Prikka\",\n    \"sloppiness\": \"Ujamnheit\",\n    \"opacity\": \"Synlegheit\",\n    \"textAlign\": \"Tekstjustering\",\n    \"edges\": \"Kanter\",\n    \"sharp\": \"Skarp\",\n    \"round\": \"Rund\",\n    \"arrowheads\": \"Pilhovud\",\n    \"arrowhead_none\": \"Ingen\",\n    \"arrowhead_arrow\": \"Pil\",\n    \"arrowhead_bar\": \"Stolpe\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Trekant\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Skriftstorleik\",\n    \"fontFamily\": \"Skrifttype\",\n    \"addWatermark\": \"Legg til «Laga med Excalidraw»\",\n    \"handDrawn\": \"Handteikna\",\n    \"normal\": \"Normal\",\n    \"code\": \"Kode\",\n    \"small\": \"Liten\",\n    \"medium\": \"Medium\",\n    \"large\": \"Stor\",\n    \"veryLarge\": \"Svært stor\",\n    \"solid\": \"Solid\",\n    \"hachure\": \"Skravert\",\n    \"zigzag\": \"\",\n    \"crossHatch\": \"Krysskravert\",\n    \"thin\": \"Tynn\",\n    \"bold\": \"Tjukk\",\n    \"left\": \"Venstre\",\n    \"center\": \"Midstill\",\n    \"right\": \"Høgre\",\n    \"extraBold\": \"Ekstra tjukk\",\n    \"architect\": \"Arkitekt\",\n    \"artist\": \"Kunstnar\",\n    \"cartoonist\": \"Teiknar\",\n    \"fileTitle\": \"Filnamn\",\n    \"colorPicker\": \"Fargeveljar\",\n    \"canvasColors\": \"Brukt på lerretet\",\n    \"canvasBackground\": \"Lerretsbakgrunn\",\n    \"drawingCanvas\": \"Lerret\",\n    \"layers\": \"Lag\",\n    \"actions\": \"Handlingar\",\n    \"language\": \"Språk\",\n    \"liveCollaboration\": \"\",\n    \"duplicateSelection\": \"Dupliser\",\n    \"untitled\": \"Utan namn\",\n    \"name\": \"Namn\",\n    \"yourName\": \"Namnet ditt\",\n    \"madeWithExcalidraw\": \"Laga med Excalidraw\",\n    \"group\": \"Grupper utval\",\n    \"ungroup\": \"Avgrupper utval\",\n    \"collaborators\": \"Samarbeidarar\",\n    \"showGrid\": \"Vis rutenett\",\n    \"addToLibrary\": \"Legg til i bibliotek\",\n    \"removeFromLibrary\": \"Fjern frå bibliotek\",\n    \"libraryLoadingMessage\": \"Laster bibliotek…\",\n    \"libraries\": \"Blad gjennom bibliotek\",\n    \"loadingScene\": \"Laster scene…\",\n    \"align\": \"Juster\",\n    \"alignTop\": \"Juster til topp\",\n    \"alignBottom\": \"Juster til botn\",\n    \"alignLeft\": \"Juster til venstre\",\n    \"alignRight\": \"Juster til høgre\",\n    \"centerVertically\": \"Midtstill vertikalt\",\n    \"centerHorizontally\": \"Midtstill horisontalt\",\n    \"distributeHorizontally\": \"Sprei horisontalt\",\n    \"distributeVertically\": \"Sprei vertikalt\",\n    \"flipHorizontal\": \"Vipp vassrett\",\n    \"flipVertical\": \"Vipp loddrett\",\n    \"viewMode\": \"Visningsmodus\",\n    \"share\": \"Del\",\n    \"showStroke\": \"Vis fargeveljar for linjer\",\n    \"showBackground\": \"Vis fargeveljar for bakgrunn\",\n    \"toggleTheme\": \"Veksle tema\",\n    \"personalLib\": \"Personleg bibliotek\",\n    \"excalidrawLib\": \"Excalidraw-bibliotek\",\n    \"decreaseFontSize\": \"Gjer skriftstorleik mindre\",\n    \"increaseFontSize\": \"Gjer skriftstorleik større\",\n    \"unbindText\": \"Avbind tekst\",\n    \"bindText\": \"\",\n    \"createContainerFromText\": \"\",\n    \"link\": {\n      \"edit\": \"Rediger lenke\",\n      \"editEmbed\": \"\",\n      \"create\": \"Lag lenke\",\n      \"createEmbed\": \"\",\n      \"label\": \"Lenke\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"\",\n      \"exit\": \"\"\n    },\n    \"elementLock\": {\n      \"lock\": \"\",\n      \"unlock\": \"\",\n      \"lockAll\": \"\",\n      \"unlockAll\": \"\"\n    },\n    \"statusPublished\": \"\",\n    \"sidebarLock\": \"\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"\",\n    \"hint_emptyLibrary\": \"\",\n    \"hint_emptyPrivateLibrary\": \"\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Tilbakestill lerretet\",\n    \"exportJSON\": \"Eksporter til fil\",\n    \"exportImage\": \"\",\n    \"export\": \"\",\n    \"copyToClipboard\": \"Kopier til utklippstavla\",\n    \"save\": \"Lagre til noverande fil\",\n    \"saveAs\": \"Lagre som\",\n    \"load\": \"\",\n    \"getShareableLink\": \"Hent delingslenke\",\n    \"close\": \"Lukk\",\n    \"selectLanguage\": \"Vel språk\",\n    \"scrollBackToContent\": \"Skroll tilbake til innhald\",\n    \"zoomIn\": \"Zoom inn\",\n    \"zoomOut\": \"Zoom ut\",\n    \"resetZoom\": \"Nullstill zoom\",\n    \"menu\": \"Meny\",\n    \"done\": \"Ferdig\",\n    \"edit\": \"Rediger\",\n    \"undo\": \"Angre\",\n    \"redo\": \"Gjer om\",\n    \"resetLibrary\": \"Nullstill bibliotek\",\n    \"createNewRoom\": \"Lag nytt rom\",\n    \"fullScreen\": \"Fullskjerm\",\n    \"darkMode\": \"Mørk modus\",\n    \"lightMode\": \"Lys modus\",\n    \"zenMode\": \"Zen-modus\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Avslutt zen-modus\",\n    \"cancel\": \"Avbryt\",\n    \"clear\": \"Tøm\",\n    \"remove\": \"Fjern\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Publiser\",\n    \"submit\": \"Send inn\",\n    \"confirm\": \"Stadfest\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Dette vil tømme lerretet. Er du sikker?\",\n    \"couldNotCreateShareableLink\": \"Kunne ikkje lage delingslenke.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Kunne ikkje opprette deleleg lenke: scena er for stor\",\n    \"couldNotLoadInvalidFile\": \"Kunne ikkje laste inn ugyldig fil\",\n    \"importBackendFailed\": \"Importering av backend feila.\",\n    \"cannotExportEmptyCanvas\": \"Kan ikkje eksportere eit tomt lerret.\",\n    \"couldNotCopyToClipboard\": \"\",\n    \"decryptFailed\": \"Kunne ikkje dekryptere data.\",\n    \"uploadedSecurly\": \"Opplastinga er kryptert og er ikkje mogleg å lese av Excalidraw-serveren eller tredjepartar.\",\n    \"loadSceneOverridePrompt\": \"Innlasting av ekstern teikning erstattar ditt eksisterande innhald. Ynskjer du å fortsette?\",\n    \"collabStopOverridePrompt\": \"Viss du avsluttar økta overskriv du den førre, lokalt lagra teikninga di. Er du sikker?\\n\\n(Ønsker du å halde fram med denne? Då er det berre å lukke denne fana.)\",\n    \"errorAddingToLibrary\": \"Kunne ikkje legge elementet i biblioteket\",\n    \"errorRemovingFromLibrary\": \"Kunne ikkje fjerne elementet frå biblioteket\",\n    \"confirmAddLibrary\": \"Dette vil legge til {{numShapes}} form(er) i biblioteket ditt. Er du sikker?\",\n    \"imageDoesNotContainScene\": \"Dette biletet ser ikkje ut til å ha noko scenedata. Har du skrutt på innbygging av scene medan eksporteringa heldt på?\",\n    \"cannotRestoreFromImage\": \"Scena kunne ikkje gjenopprettast frå denne biletfila\",\n    \"invalidSceneUrl\": \"Kunne ikkje hente noko scene frå den URL-en. Ho er anten øydelagd eller inneheld ikkje gyldig Excalidraw JSON-data.\",\n    \"resetLibrary\": \"Dette vil fjerne alt innhald frå biblioteket. Er du sikker?\",\n    \"removeItemsFromsLibrary\": \"Slette {{count}} element frå biblioteket?\",\n    \"invalidEncryptionKey\": \"Krypteringsnøkkelen må ha 22 teikn. Sanntidssamarbeid er deaktivert.\",\n    \"collabOfflineWarning\": \"\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Filtypen er ikkje støtta.\",\n    \"imageInsertError\": \"Kunne ikkje sette inn biletet. Prøv igjen seinare...\",\n    \"fileTooBig\": \"Fila er for stor. Maksimal tillate storleik er {{maxSize}}.\",\n    \"svgImageInsertError\": \"Kunne ikkje sette inn SVG-biletet. SVG-koden ser ugyldig ut.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"Ugyldig SVG.\",\n    \"cannotResolveCollabServer\": \"Kunne ikkje kople til samarbeidsserveren. Ver vennleg å oppdatere inn sida og prøv på nytt.\",\n    \"importLibraryError\": \"\",\n    \"collabSaveFailed\": \"\",\n    \"collabSaveFailed_sizeExceeded\": \"\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Vel\",\n    \"image\": \"Sett in bilete\",\n    \"rectangle\": \"Rektangel\",\n    \"diamond\": \"Diamant\",\n    \"ellipse\": \"Ellipse\",\n    \"arrow\": \"Pil\",\n    \"line\": \"Linje\",\n    \"freedraw\": \"Teikn\",\n    \"text\": \"Tekst\",\n    \"library\": \"Bibliotek\",\n    \"lock\": \"Hald fram med valt verktøy\",\n    \"penMode\": \"\",\n    \"link\": \"Legg til/ oppdater lenke til valt figur\",\n    \"eraser\": \"Viskelêr\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Handlingar: lerret\",\n    \"selectedShapeActions\": \"Handlingar: valt objekt\",\n    \"shapes\": \"Formar\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"\",\n    \"linearElement\": \"Klikk for å starte linje med fleire punkt, eller drag for ei enkel linje\",\n    \"freeDraw\": \"Klikk og drag, slepp når du er ferdig\",\n    \"text\": \"Tips: du kan òg leggje til tekst ved å dobbeltklikke kor som helst med utvalgsverktyet\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"Dobbelklikk eller trykk ENTER for å redigere teksta\",\n    \"text_editing\": \"Trykk Escape eller CtrlOrCmd+ENTER for å fullføre redigeringa\",\n    \"linearElementMulti\": \"Klikk på siste punkt eller trykk Escape eller Enter for å fullføre\",\n    \"lockAngle\": \"Du kan begrense vinkelen ved å holde nede SKIFT\",\n    \"resize\": \"Du kan halde fram med forholdet ved å trykke SHIFT medan du endrar storleik,\\ntrykk ALT for å endre storleiken frå midten\",\n    \"resizeImage\": \"Du kan endre storleiken fritt ved å halde inne SHIFT,\\nhald ALT for å endre storleik frå sentrum\",\n    \"rotate\": \"Du kan låse vinklane ved å halde SHIFT medan du roterer\",\n    \"lineEditor_info\": \"\",\n    \"lineEditor_pointSelected\": \"Trykk på Slett for å fjerne punkt(a),\\nCtrl / Cmd+D for å duplisere, eller drag for å flytte\",\n    \"lineEditor_nothingSelected\": \"Vel eit punkt å redigere (hald inne SHIFT for å velje fleire),\\neller hald inne Alt og klikk for å legge til nye punkt\",\n    \"placeImage\": \"Klikk for å plassere biletet, eller klikk og drag for å velje storleik manuelt\",\n    \"publishLibrary\": \"Publiser ditt eige bibliotek\",\n    \"bindTextToElement\": \"Trykk på enter for å legge til tekst\",\n    \"deepBoxSelect\": \"Hald inne Ctrl / Cmd for å velje djupt, og forhindre flytting\",\n    \"eraserRevert\": \"Hald inne Alt for å reversere markering av element for sletting\",\n    \"firefox_clipboard_write\": \"\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Kan ikkje vise førehandsvising\",\n    \"canvasTooBig\": \"Lerretet er mogleg for stort.\",\n    \"canvasTooBigTip\": \"Tips: prøv å flytte elementa som er lengst frå kvarandre, litt nærare kvarandre.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Ein feil oppstod. Prøv <button>å laste sida på nytt.</button>\",\n    \"clearCanvasMessage\": \"Om ny sidelasting ikkje fungerer, prøv <button>å tømme lerretet.</button>\",\n    \"clearCanvasCaveat\": \" Dette vil føre til tap av arbeid \",\n    \"trackedToSentry\": \"Feilen med identifikator {{eventId}} vart logga i systemet vårt.\",\n    \"openIssueMessage\": \"Vi er veldig nøye med å ikkje inkludere scene-opplysingane dine i feilmeldinga. Viss scena di ikkje er privat kan du vurdere å følge opp i <button>feilrapporteringssystemet vårt.</button> Ta med opplysingane nedanfor ved å kopiere og lime inn i GitHub-saka.\",\n    \"sceneContent\": \"Scene-innhald:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Du kan invitere personar til scena di for å samarbeide med deg.\",\n    \"desc_privacy\": \"Ta det med ro; økta brukar ende-til-ende-kryptering, så alt du teiknar held fram med å vere privat. Ikkje ein gong serveren vår kan sjå kva du lagar.\",\n    \"button_startSession\": \"Start økt\",\n    \"button_stopSession\": \"Stopp økt\",\n    \"desc_inProgressIntro\": \"Sanntids-samarbeidsøkt er no i gang.\",\n    \"desc_shareLink\": \"Del denne lenka med dei du vil samarbeide med:\",\n    \"desc_exitSession\": \"Dersom du avsluttar økta blir du kopla frå rommet, men du kan halde fram med å arbeide med scena lokalt. Ver merksam på at dette ikkje vil påverke andre personar, og desse vil framleis ha moglegheit til å samarbeide på deira eigen versjon.\",\n    \"shareTitle\": \"Bli med på eit sanntidssamarbeid på Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Feil\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Lagre til disk\",\n    \"disk_details\": \"Eksporter scenedataa til ei fil du kan importere seinare.\",\n    \"disk_button\": \"Lagre til fil\",\n    \"link_title\": \"Deleleg lenke\",\n    \"link_details\": \"Eksporter som skrivebeskytta lenke.\",\n    \"link_button\": \"Eksporter til lenke\",\n    \"excalidrawplus_description\": \"Lagre scena til Excalidraw+-arbeidsområdet ditt.\",\n    \"excalidrawplus_button\": \"Eksporter\",\n    \"excalidrawplus_exportError\": \"Kunne ikkje eksportere til Excalidraw+ akkurat no...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Les bloggen vår\",\n    \"click\": \"klikk\",\n    \"deepSelect\": \"Marker djupt\",\n    \"deepBoxSelect\": \"Marker djupt inni boksen og forhindr flytting\",\n    \"curvedArrow\": \"Boga pil\",\n    \"curvedLine\": \"Boga linje\",\n    \"documentation\": \"Dokumentasjon\",\n    \"doubleClick\": \"dobbelklikk\",\n    \"drag\": \"drag\",\n    \"editor\": \"Redigering\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"Funne eit problem? Send inn\",\n    \"howto\": \"Følg vegleiinga vår\",\n    \"or\": \"eller\",\n    \"preventBinding\": \"Hindre pilkopling\",\n    \"tools\": \"\",\n    \"shortcuts\": \"Tastatursnarvegar\",\n    \"textFinish\": \"Fullfør redigering (teksthandsamar)\",\n    \"textNewLine\": \"Legg til ny linje (teksthandsamar)\",\n    \"title\": \"Hjelp\",\n    \"view\": \"Vising\",\n    \"zoomToFit\": \"Zoom for å sjå alle elementa\",\n    \"zoomToSelection\": \"Zoom til utval\",\n    \"toggleElementLock\": \"\",\n    \"movePageUpDown\": \"\",\n    \"movePageLeftRight\": \"\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Tøm lerretet\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Publiser bibliotek\",\n    \"itemName\": \"Elementnamn\",\n    \"authorName\": \"Eigaren sitt namn\",\n    \"githubUsername\": \"GitHub-brukarnamn\",\n    \"twitterUsername\": \"Twitter-brukarnamn\",\n    \"libraryName\": \"Biblioteknamn\",\n    \"libraryDesc\": \"Bibliotekskildring\",\n    \"website\": \"Nettstad\",\n    \"placeholder\": {\n      \"authorName\": \"Namnet eller brukarnamnet ditt\",\n      \"libraryName\": \"Namnet på biblioteket ditt\",\n      \"libraryDesc\": \"Skildring av biblioteket ditt sånn at andre forstår bruken av det\",\n      \"githubHandle\": \"GitHub-brukarnamn (valfritt), slik at du kan redigere bibiloteket når det er sendt inn til vurdering\",\n      \"twitterHandle\": \"Twitter-brukarnamn (valfritt), så vi veit kven vi skal kreditere på Twitter\",\n      \"website\": \"Lenke til den personlege nettstaden din eller ein anna stad (valfritt)\"\n    },\n    \"errors\": {\n      \"required\": \"Kravt\",\n      \"website\": \"Fyll inn ein gyldig URL\"\n    },\n    \"noteDescription\": \"Send inn biblioteket ditt til inkludering i <link>den offentlege bibliotek-kjeldekoda</link>slik at andre kan bruke det i teikningane deira.\",\n    \"noteGuidelines\": \"Biblioteket må godkjennast manuelt fyrst. Ver vennleg å lese <link>retningslinjene</link> før du sender inn. Du kjem til å trenge ein GitHub-konto for å kommunisere og gjere endringar dersom kravt, men det er ikkje strengt naudsynt.\",\n    \"noteLicense\": \"Ved å sende inn godkjenner du at biblioteket vert publisert under <link>MIT-lisensen, </link>som kort sagt betyr at kven som helst kan bruke det utan avgrensingar.\",\n    \"noteItems\": \"Kvart bibliotekselement må ha eit eige namn, slik at det er mogleg å filtrere. Dei følgande bibliotekselementa blir inkludert:\",\n    \"atleastOneLibItem\": \"Ver vennleg å markere minst eitt bibliotekselement for å starte\",\n    \"republishWarning\": \"\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Bibliotek innsendt\",\n    \"content\": \"Tusen takk {{authorName}}! Biblioteket ditt har blitt sendt inn til gjennomgang. Du kan halde styr på status<link>her</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Tilbakestill bibliotek\",\n    \"removeItemsFromLib\": \"Fjern valde element frå biblioteket\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Teikningane dine er ende-til-ende-krypterte slik at Excalidraw sine serverar aldri får sjå dei.\",\n    \"link\": \"Blogginnlegg om ende-til-ende-kryptering i Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Vinkel\",\n    \"element\": \"Element\",\n    \"elements\": \"Element\",\n    \"height\": \"Høgde\",\n    \"scene\": \"Scene\",\n    \"selected\": \"Valde\",\n    \"storage\": \"Lagring\",\n    \"title\": \"Statistikk for nerdar\",\n    \"total\": \"Totalt\",\n    \"version\": \"Versjon\",\n    \"versionCopy\": \"Klikk for å kopiere\",\n    \"versionNotAvailable\": \"Versjonen er ikkje tilgjengeleg\",\n    \"width\": \"Breidde\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Lagt til i bibliotek\",\n    \"copyStyles\": \"Kopierte stilane.\",\n    \"copyToClipboard\": \"Kopiert til utklippstavla.\",\n    \"copyToClipboardAsPng\": \"Kopierte {{exportSelection}} til utklippstavla som PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Fila er lagra.\",\n    \"fileSavedToFilename\": \"Lagra som {filename}\",\n    \"canvas\": \"lerret\",\n    \"selection\": \"val\",\n    \"pasteAsSingleElement\": \"\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Gjennomsiktig\",\n    \"black\": \"\",\n    \"white\": \"\",\n    \"red\": \"\",\n    \"pink\": \"\",\n    \"grape\": \"\",\n    \"violet\": \"\",\n    \"gray\": \"\",\n    \"blue\": \"\",\n    \"cyan\": \"\",\n    \"teal\": \"\",\n    \"green\": \"\",\n    \"yellow\": \"\",\n    \"orange\": \"\",\n    \"bronze\": \"\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"\",\n      \"center_heading_plus\": \"\",\n      \"menuHint\": \"\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"\",\n      \"center_heading\": \"\",\n      \"toolbarHint\": \"\",\n      \"helpHint\": \"\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"\",\n    \"shades\": \"\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}