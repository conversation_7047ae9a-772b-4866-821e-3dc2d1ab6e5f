import "../chunk-XDFCUUT6.js";

// locales/vi-VN.json
var labels = {
  paste: "D\xE1n",
  pasteAsPlaintext: "D\xE1n ki\u1EC3u v\u0103n b\u1EA3n thu\u1EA7n",
  pasteCharts: "D\xE1n bi\u1EC3u \u0111\u1ED3",
  selectAll: "Ch\u1ECDn t\u1EA5t c\u1EA3",
  multiSelect: "Th\xEAm m\u1EDBi v\xE0o Select",
  moveCanvas: "Di chuy\u1EC3n canvas",
  cut: "C\u1EAFt",
  copy: "Sao ch\xE9p",
  copyAsPng: "Sao ch\xE9p v\xE0o b\u1ED9 nh\u1EDB t\u1EA1m d\u01B0\u1EDBi d\u1EA1ng PNG",
  copyAsSvg: "Sao ch\xE9p v\xE0o b\u1ED9 nh\u1EDB t\u1EA1m d\u01B0\u1EDBi d\u1EA1ng SVG",
  copyText: "Sao ch\xE9p v\xE0o b\u1ED9 nh\u1EDB t\u1EA1m d\u01B0\u1EDBi d\u1EA1ng ch\u1EEF",
  copySource: "",
  convertToCode: "",
  bringForward: "\u0110\u01B0a ra tr\u01B0\u1EDBc",
  sendToBack: "H\u1EA1 xu\u1ED1ng d\u01B0\u1EDBi",
  bringToFront: "\u0110\u01B0a ra \u0111\u1EA7u ti\xEAn",
  sendBackward: "H\u1EA1 xu\u1ED1ng cu\u1ED1i",
  delete: "X\xF3a",
  copyStyles: "Sao ch\xE9p \u0111\u1ECBnh d\u1EA1ng",
  pasteStyles: "D\xE1n \u0111\u1ECBnh d\u1EA1ng",
  stroke: "N\xE9t",
  background: "N\u1EC1n",
  fill: "Fill",
  strokeWidth: "\u0110\u1ED9 d\xE0y n\xE9t",
  strokeStyle: "Ki\u1EC3u n\xE9t",
  strokeStyle_solid: "Kh\u1ED1i",
  strokeStyle_dashed: "G\u1EA1ch ngang",
  strokeStyle_dotted: "Nhi\u1EC1u ch\u1EA5m",
  sloppiness: "Hoa v\u0103n n\xE9t",
  opacity: "\u0110\u1ED9 trong su\u1ED1t",
  textAlign: "C\u0103n ch\u1EC9nh v\u0103n b\u1EA3n",
  edges: "C\u1EA1nh",
  sharp: "Nh\u1ECDn",
  round: "Tr\xF2n",
  arrowheads: "\u0110\u1EA7u m\u0169i t\xEAn",
  arrowhead_none: "Kh\xF4ng",
  arrowhead_arrow: "M\u0169i t\xEAn",
  arrowhead_bar: "Thanh",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "Tam gi\xE1c",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "C\u1EE1 ch\u1EEF",
  fontFamily: "Ph\xF4ng ch\u1EEF",
  addWatermark: 'L\xE0m v\u1EDBi Excalidraw"',
  handDrawn: "V\u1EBD tay",
  normal: "B\xECnh th\u01B0\u1EDDng",
  code: "M\xE3",
  small: "Nh\u1ECF",
  medium: "V\u1EEBa",
  large: "L\u1EDBn",
  veryLarge: "R\u1EA5t l\u1EDBn",
  solid: "\u0110\u1EB7c",
  hachure: "N\xE9t g\u1EA1ch g\u1EA1ch",
  zigzag: "Zigzag",
  crossHatch: "N\xE9t g\u1EA1ch ch\xE9o",
  thin: "M\u1ECFng",
  bold: "In \u0111\u1EADm",
  left: "Tr\xE1i",
  center: "Gi\u1EEFa",
  right: "Ph\u1EA3i",
  extraBold: "N\xE9t si\xEAu \u0111\u1EADm",
  architect: "Ki\u1EBFn tr\xFAc s\u01B0",
  artist: "Ngh\u1EC7 sy\u0303",
  cartoonist: "Ho\u1EA1t h\xECnh",
  fileTitle: "T\xEAn t\u1EADp tin",
  colorPicker: "Ch\u1ECDn m\xE0u",
  canvasColors: "\u0110\xE3 d\xF9ng tr\xEAn canvas",
  canvasBackground: "N\u1EC1n canvas",
  drawingCanvas: "Canvas v\u1EBD",
  layers: "L\u1EDBp",
  actions: "Ch\u1EE9c n\u0103ng",
  language: "Ng\xF4n ng\u1EEF",
  liveCollaboration: "H\u1EE3p t\xE1c tr\u1EF1c ti\u1EBFp...",
  duplicateSelection: "T\u1EA1o b\u1EA3n sao",
  untitled: "Kh\xF4ng c\xF3 ti\xEAu \u0111\u1EC1",
  name: "T\xEAn",
  yourName: "T\xEAn c\u1EE7a b\u1EA1n",
  madeWithExcalidraw: "L\xE0m v\u1EDBi Excalidraw",
  group: "G\u1ED9p nh\xF3m l\u1EA1i l\u1EF1a ch\u1ECDn",
  ungroup: "T\xE1ch nh\xF3m l\u1EF1a ch\u1ECDn",
  collaborators: "C\u1ED9ng t\xE1c vi\xEAn",
  showGrid: "Hi\u1EC3n th\u1ECB l\u01B0\u1EDBi",
  addToLibrary: "Th\xEAm v\xE0o th\u01B0 vi\u1EC7n",
  removeFromLibrary: "X\xF3a kh\u1ECFi th\u01B0 vi\u1EC7n",
  libraryLoadingMessage: "\u0110ang t\u1EA3i th\u01B0 vi\u1EC7n\u2026",
  libraries: "Xem th\u01B0 vi\u1EC7n",
  loadingScene: "\u0110ang t\u1EA3i v\u1EC1\u2026",
  align: "C\u0103n ch\u1EC9nh",
  alignTop: "C\u0103n tr\xEAn",
  alignBottom: "C\u0103n d\u01B0\u1EDBi",
  alignLeft: "Canh tr\xE1i",
  alignRight: "Canh ph\u1EA3i",
  centerVertically: "Gi\u01B0\u0303a theo chi\u1EC1u d\u1ECDc",
  centerHorizontally: "Gi\u01B0\u0303a theo chi\u1EC1u ngang",
  distributeHorizontally: "Ph\xE2n b\xF4\u0301 theo chi\xEA\u0300u ngang",
  distributeVertically: "Ph\xE2n b\xF4\u0301 theo chi\xEA\u0300u do\u0323c",
  flipHorizontal: "L\u1EADt ngang",
  flipVertical: "L\u1EADt d\u1ECDc",
  viewMode: "Ch\u1EBF \u0111\u1ED9 xem",
  share: "Chia s\u1EBB",
  showStroke: "Hi\u1EC3n th\u1ECB ch\u1ECDn m\xE0u",
  showBackground: "Hi\u1EC7n th\u1ECB ch\u1ECDn m\xE0u n\u1EC1n",
  toggleTheme: "",
  personalLib: "Th\u01B0 vi\u1EC7n c\xE1 nh\xE2n",
  excalidrawLib: "Th\u01B0 vi\u1EC7n Excalidraw",
  decreaseFontSize: "Gi\u1EA3m c\u1EE1 ch\u1EEF",
  increaseFontSize: "T\u0103ng c\u1EE1 ch\u1EEF",
  unbindText: "",
  bindText: "",
  createContainerFromText: "",
  link: {
    edit: "S\u1EEDa li\xEAn k\u1EBFt",
    editEmbed: "",
    create: "T\u1EA1o li\xEAn k\u1EBFt",
    createEmbed: "",
    label: "Li\xEAn k\u1EBFt",
    labelEmbed: "",
    empty: ""
  },
  lineEditor: {
    edit: "\u0110i\u1EC1u ch\u1EC9nh n\xE9t",
    exit: "Tho\xE1t ch\u1EC9nh n\xE9t"
  },
  elementLock: {
    lock: "Kho\xE1",
    unlock: "M\u1EDF kho\xE1",
    lockAll: "Kh\xF3a t\u1EA5t c\u1EA3",
    unlockAll: "M\u1EDF kh\xF3a t\u1EA5t c\u1EA3"
  },
  statusPublished: "\u0110\xE3 \u0111\u0103ng t\u1EA3i",
  sidebarLock: "Gi\u1EEF thanh b\xEAn lu\xF4n m\u1EDF",
  selectAllElementsInFrame: "",
  removeAllElementsFromFrame: "",
  eyeDropper: "",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "Ch\u01B0a c\xF3 m\xF3n n\xE0o...",
  hint_emptyLibrary: "Ch\u1ECDn m\u1ED9t m\xF3n tr\xEAn canvas \u0111\u1EC3 th\xEAm n\xF3 v\xE0o \u0111\xE2y, ho\u1EB7c c\xE0i \u0111\u1EB7t th\u01B0 vi\u1EC7n t\u1EEB kho l\u01B0u tr\u1EEF c\xF4ng c\u1ED9ng, \u1EDF b\xEAn d\u01B0\u1EDBi.",
  hint_emptyPrivateLibrary: "Ch\u1ECDn m\u1ED9t m\xF3n tr\xEAn canvas \u0111\u1EC3 th\xEAm n\xF3 v\xE0o \u0111\xE2y."
};
var buttons = {
  clearReset: "Reset canvas",
  exportJSON: "Xu\u1EA5t ra t\u1EADp tin",
  exportImage: "Xu\u1EA5t file \u1EA3nh...",
  export: "L\u01B0u v\xE0o...",
  copyToClipboard: "Sao ch\xE9p v\xE0o b\u1ED9 nh\u1EDB t\u1EA1m",
  save: "L\u01B0u v\xE0o t\u1EADp tin hi\u1EC7n t\u1EA1i",
  saveAs: "L\u01B0u th\xE0nh",
  load: "M\u1EDF",
  getShareableLink: "T\u1EA1o li\xEAn k\u1EBFt \u0111\u1EC3 chia s\u1EBB",
  close: "\u0110\xF3ng",
  selectLanguage: "Ch\u1ECDn ng\xF4n ng\u1EEF",
  scrollBackToContent: "Cu\u1ED9n v\u1EC1 n\u1ED9i dung ch\xEDnh",
  zoomIn: "Ph\xF3ng to",
  zoomOut: "Thu nh\u1ECF",
  resetZoom: "\u0110\u1EB7t l\u1EA1i thu ph\xF3ng",
  menu: "B\u1EA3ng ch\u1ECDn",
  done: "Xong",
  edit: "Ch\u1EC9nh s\u1EEDa",
  undo: "Ho\xE0n t\xE1c",
  redo: "L\xE0m l\u1EA1i",
  resetLibrary: "",
  createNewRoom: "T\u1EA1o ph\xF2ng m\u1EDBi",
  fullScreen: "To\xE0n m\xE0n h\xECnh",
  darkMode: "Ch\u1EBF \u0111\u1ED9 t\u1ED1i",
  lightMode: "Ch\u1EBF \u0111\u1ED9 s\xE1ng",
  zenMode: "Ch\u1EBF \u0111\u1ED9 zen",
  objectsSnapMode: "",
  exitZenMode: "Tho\xE1t ch\u1EC3 \u0111\u1ED9 zen",
  cancel: "Hu\u0309y",
  clear: "L\xE0m s\u1EA1ch",
  remove: "X\xF3a",
  embed: "",
  publishLibrary: "\u0110\u0103ng t\u1EA3i",
  submit: "G\u1EEDi",
  confirm: "X\xE1c nh\u1EADn",
  embeddableInteractionButton: ""
};
var alerts = {
  clearReset: "\u0110i\u1EC1u n\xE0y s\u1EBD d\u1ECDn h\u1EBFt canvas. B\u1EA1n c\xF3 ch\u1EAFc kh\xF4ng?",
  couldNotCreateShareableLink: "Kh\xF4ng th\u1EC3 t\u1EA1o \u0111\u01B0\u1EDDng d\u1EABn chia s\u1EBB.",
  couldNotCreateShareableLinkTooBig: "Kh\xF4ng th\u1EC3 t\u1EA1o \u0111\u01B0\u1EDDng d\u1EABn chia s\u1EBB: b\u1EA3n v\u1EBD qu\xE1 l\u1EDBn",
  couldNotLoadInvalidFile: "Kh\xF4ng th\u1EC3 load t\u1EADp tin kh\xF4ng h\u1EE3p l\u1EC7",
  importBackendFailed: "",
  cannotExportEmptyCanvas: "Kh\xF4ng th\u1EC3 xu\u1EA5t canvas tr\u1ED1ng.",
  couldNotCopyToClipboard: "",
  decryptFailed: "Kh\xF4ng th\u1EC3 gi\u1EA3i m\xE3 d\u1EEF li\u1EC7u.",
  uploadedSecurly: "",
  loadSceneOverridePrompt: "",
  collabStopOverridePrompt: "D\u1EEBng phi\xEAn s\u1EBD ghi \u0111\xE8 l\xEAn b\u1EA3n v\u1EBD \u0111\u01B0\u1EE3c l\u01B0u tr\u1EEF c\u1EE5c b\u1ED9 tr\u01B0\u1EDBc \u0111\xF3 c\u1EE7a b\u1EA1n. B\u1EA1n c\xF3 ch\u1EAFc kh\xF4ng?\n\n(N\u1EBFu b\u1EA1n mu\u1ED1n gi\u1EEF b\u1EA3n v\u1EBD c\u1EE5c b\u1ED9 c\u1EE7a m\xECnh, ch\u1EC9 c\u1EA7n \u0111\xF3ng tab tr\xECnh duy\u1EC7t.)",
  errorAddingToLibrary: "Kh\xF4ng th\u1EC3 th\xEAm m\xF3n v\xE0o th\u01B0 vi\u1EC7n",
  errorRemovingFromLibrary: "Kh\xF4ng th\u1EC3 xo\xE1 m\xF3n kh\u1ECFi th\u01B0 vi\u1EC7n",
  confirmAddLibrary: "H\xECnh {{numShapes}} s\u1EBD \u0111\u01B0\u1EE3c th\xEAm v\xE0o th\u01B0 vi\u1EC7n. B\u1EA1n ch\u1EAFc ch\u1EE9?",
  imageDoesNotContainScene: "H\xECnh \u1EA3nh n\xE0y d\u01B0\u1EDDng nh\u01B0 kh\xF4ng ch\u1EE9a b\u1EA5t k\u1EF3 d\u1EEF li\u1EC7u c\u1EA3nh n\xE0o. B\u1EA1n \u0111\xE3 b\u1EADt t\xEDnh n\u0103ng nh\xFAng c\u1EA3nh khi xu\u1EA5t ch\u01B0a?",
  cannotRestoreFromImage: "",
  invalidSceneUrl: "",
  resetLibrary: "",
  removeItemsFromsLibrary: "Xo\xE1 {{count}} m\xF3n t\u1EEB th\u01B0 vi\u1EC7n?",
  invalidEncryptionKey: "Kh\xF3a m\xE3 h\xF3a ph\u1EA3i c\xF3 22 k\xFD t\u1EF1. H\u1EE3p t\xE1c tr\u1EF1c ti\u1EBFp b\u1ECB v\xF4 hi\u1EC7u h\xF3a.",
  collabOfflineWarning: "Kh\xF4ng c\xF3 k\u1EBFt n\u1ED1i internet.\nThay \u0111\u1ED5i c\u1EE7a b\u1EA1n s\u1EBD kh\xF4ng \u0111\u01B0\u1EE3c l\u01B0u!"
};
var errors = {
  unsupportedFileType: "Lo\u1EA1i t\u1EADp tin kh\xF4ng \u0111\u01B0\u1EE3c h\u1ED7 tr\u1EE3.",
  imageInsertError: "Kh\xF4ng th\u1EC3 th\xEAm \u1EA3nh. H\xE3y th\u1EED l\u1EA1i sau...",
  fileTooBig: "T\u1EC7p tin qu\xE1 l\u1EDBn. Dung l\u01B0\u1EE3ng t\u1ED1i \u0111a cho ph\xE9p l\xE0 {{maxSize}}.",
  svgImageInsertError: "Kh\xF4ng th\u1EC3 th\xEAm \u1EA3nh SVG. M\xE3 SVG c\xF3 v\u1EBB sai.",
  failedToFetchImage: "",
  invalidSVGString: "SVG kh\xF4ng h\u1EE3p l\u1EC7.",
  cannotResolveCollabServer: "Kh\xF4ng th\u1EC3 k\u1EBFt n\u1ED1i v\u1EDBi m\xE1y ch\u1EE7 h\u1EE3p t\xE1c. H\xE3y t\u1EA3i l\u1EA1i trang v\xE0 th\u1EED l\u1EA1i.",
  importLibraryError: "Kh\xF4ng th\u1EC3 t\u1EA3i th\u01B0 vi\u1EC7n",
  collabSaveFailed: "Kh\xF4ng th\u1EC3 l\u01B0u v\xE0o c\u01A1 s\u1EDF d\u1EEF li\u1EC7u. N\u1EBFu v\u1EA5n \u0111\u1EC1 ti\u1EBFp t\u1EE5c x\u1EA3y ra, b\u1EA1n n\xEAn l\u01B0u t\u1EC7p v\xE0o m\xE1y \u0111\u1EC3 \u0111\u1EA3m b\u1EA3o b\u1EA1n kh\xF4ng b\u1ECB m\u1EA5t c\xF4ng vi\u1EC7c.",
  collabSaveFailed_sizeExceeded: "Kh\xF4ng th\u1EC3 l\u01B0u v\xE0o c\u01A1 s\u1EDF d\u1EEF li\u1EC7u, canvas c\xF3 v\u1EBB qu\xE1 l\u1EDBn. B\u1EA1n n\xEAn l\u01B0u t\u1EC7p c\u1EE5c b\u1ED9 \u0111\u1EC3 \u0111\u1EA3m b\u1EA3o b\u1EA1n kh\xF4ng b\u1ECB m\u1EA5t c\xF4ng vi\u1EC7c.",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "",
    line2: "",
    line3: "",
    line4: ""
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "",
    image: ""
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "L\u1EF1a ch\u1ECDn",
  image: "Ch\xE8n \u1EA3nh",
  rectangle: "H\xECnh ch\u1EEF nh\u1EADt",
  diamond: "Kim c\u01B0\u01A1ng",
  ellipse: "H\xECnh el\xEDp",
  arrow: "M\u0169i t\xEAn",
  line: "\u0110\u01B0\u1EDDng k\u1EBB",
  freedraw: "V\u1EBD",
  text: "V\u0103n b\u1EA3n",
  library: "Th\u01B0 vi\u1EC7n",
  lock: "Gi\u1EEF d\u1EE5ng c\u0169 hi\u1EC7n t\u1EA1i sau khi v\u1EBD",
  penMode: "Ch\u1EBF \u0111\u1ED9 b\xFAt v\u1EBD - ng\u0103n ng\u1EEBa ch\u1EA1m nh\u1EA7m",
  link: "Th\xEAm/ Ch\u1EC9nh s\u1EEDa li\xEAn k\u1EBFt cho h\xECnh \u0111\u01B0\u1EE3c ch\u1ECDn",
  eraser: "X\xF3a",
  frame: "",
  magicframe: "",
  embeddable: "",
  laser: "",
  hand: "Tay k\xE9o",
  extraTools: "",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "H\xE0nh \u0111\u1ED9ng canvas",
  selectedShapeActions: "C\xE1c h\xE0nh \u0111\u1ED9ng cho h\xECnh d\u1EA1ng \u0111\xE3 ch\u1ECDn",
  shapes: "C\xE1c h\xECnh kh\u1ED1i"
};
var hints = {
  canvasPanning: "\u0110\u1EC3 di chuy\u1EC3n canvas, gi\u1EEF con l\u0103n chu\u1ED9t ho\u1EB7c ph\xEDm c\xE1ch trong khi k\xE9o, ho\u1EB7c s\u1EED d\u1EE5ng c\xF4ng c\u1EE5 c\u1EA7m tay",
  linearElement: "\u1EA4n \u0111\u1EC3 b\u1EAFt \u0111\u1EA7u nhi\u1EC3m \u0111i\u1EC3m v\u1EBD, k\xE9o \u0111\u1EC3 v\u1EBD m\u1ED9t \u0111\u01B0\u1EDDng th\u1EB3ng",
  freeDraw: "\u1EA4n b\xE0 k\xE9o, th\u1EA3 khi b\u1EA1n xong",
  text: "M\u1EB9o: b\u1EA1n c\xF3 th\u1EC3 th\xEAm v\u0103n b\u1EA3n t\u1EA1i b\u1EA5t c\u1EE9 \u0111\xE2u b\u1EB1ng c\xE1ch \u1EA5n hai l\u1EA7n b\u1EB1ng tool l\u1EF1a ch\u1ECDn",
  embeddable: "",
  text_selected: "\u1EA4n 2 l\u1EA7n ho\u1EB7c nh\u1EA5n ENTER \u0111\u1EC3 ch\u1EC9nh v\u0103n b\u1EA3n",
  text_editing: "Nh\u1EA5n Escape ho\u1EB7c Ctrl/Cmd+ENTER \u0111\u1EC3 ho\xE0n th\xE0nh ch\u1EC9nh s\u1EEDa",
  linearElementMulti: "Nh\u1EA5n v\xE0o \u0111i\u1EC3m cu\u1ED1i ho\u1EB7c nh\u1EA5n Escape ho\u1EB7c Enter \u0111\u1EC3 k\u1EBFt th\xFAc",
  lockAngle: "B\u1EA1n c\xF3 th\u1EC3 ch\u1EC9nh l\u1EA1i g\xF3c b\u1EB1ng c\xE1ch gi\u1EEF ph\xEDm SHIFT",
  resize: "B\u1EA1n c\xF3 th\u1EC3 ch\u1EC9nh t\u1EF7 l\u1EC7 b\u1EB1ng c\xE1ch gi\u1EEF SHIFT khi ch\u1EC9nh k\xEDch c\u1EE1,\ngi\u1EEF ALT \u0111\u1EC3 ch\u1EC9nh k\xEDch c\u1EE1 t\u1EEB trung t\xE2m",
  resizeImage: "",
  rotate: "",
  lineEditor_info: "",
  lineEditor_pointSelected: "",
  lineEditor_nothingSelected: "",
  placeImage: "",
  publishLibrary: "",
  bindTextToElement: "",
  deepBoxSelect: "",
  eraserRevert: "",
  firefox_clipboard_write: 'T\xEDnh n\u0103ng n\xE0y c\xF3 th\u1EC3 \u0111\u01B0\u1EE3c b\u1EADt b\u1EB1ng c\xE1ch \u0111\u1EB7t c\u1EDD "dom.events.asyncClipboard.clipboardItem" th\xE0nh "true". \u0110\u1EC3 thay \u0111\u1ED5i c\u1EDD tr\xECnh duy\u1EC7t trong Firefox, h\xE3y truy c\u1EADp trang "about:config".',
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "Kh\xF4ng th\u1EC3 xem tr\u01B0\u1EDBc",
  canvasTooBig: "Canvas n\xE0y c\xF3 th\u1EC3 h\u01A1i l\u1EDBn.",
  canvasTooBigTip: "M\u1EB9o: h\xE3y th\u1EED di chuy\u1EC3n c\xE1c elements nh\u1EA5t l\u1EA1i g\u1EA7n nhau h\u01A1n m\u1ED9t ch\xFAt."
};
var errorSplash = {
  headingMain: "",
  clearCanvasMessage: "N\u1EBFu kh\xF4ng t\u1EA3i l\u1EA1i \u0111\u01B0\u1EE3c, h\xE3y th\u1EED <button>d\u1ECDn canvas.</button>",
  clearCanvasCaveat: " \u0110i\u1EC1u n\xE0y s\u1EBD d\u1EABn \u0111\u1EBFn m\u1EA5t d\u1EEF li\u1EC7u b\u1EA1n \u0111\xE3 l\xE0m ",
  trackedToSentry: "",
  openIssueMessage: "",
  sceneContent: ""
};
var roomDialog = {
  desc_intro: "",
  desc_privacy: "",
  button_startSession: "",
  button_stopSession: "",
  desc_inProgressIntro: "",
  desc_shareLink: "",
  desc_exitSession: "",
  shareTitle: ""
};
var errorDialog = {
  title: ""
};
var exportDialog = {
  disk_title: "",
  disk_details: "",
  disk_button: "",
  link_title: "",
  link_details: "",
  link_button: "",
  excalidrawplus_description: "",
  excalidrawplus_button: "",
  excalidrawplus_exportError: ""
};
var helpDialog = {
  blog: "",
  click: "",
  deepSelect: "",
  deepBoxSelect: "",
  curvedArrow: "",
  curvedLine: "",
  documentation: "",
  doubleClick: "",
  drag: "",
  editor: "",
  editLineArrowPoints: "",
  editText: "",
  github: "",
  howto: "",
  or: "",
  preventBinding: "",
  tools: "",
  shortcuts: "",
  textFinish: "",
  textNewLine: "",
  title: "",
  view: "",
  zoomToFit: "",
  zoomToSelection: "",
  toggleElementLock: "",
  movePageUpDown: "",
  movePageLeftRight: ""
};
var clearCanvasDialog = {
  title: "D\u1ECDn canvas"
};
var publishDialog = {
  title: "",
  itemName: "T\xEAn m\xF3n",
  authorName: "",
  githubUsername: "",
  twitterUsername: "",
  libraryName: "",
  libraryDesc: "",
  website: "",
  placeholder: {
    authorName: "",
    libraryName: "",
    libraryDesc: "",
    githubHandle: "",
    twitterHandle: "",
    website: ""
  },
  errors: {
    required: "",
    website: ""
  },
  noteDescription: "",
  noteGuidelines: "",
  noteLicense: "",
  noteItems: "T\u1EEBng m\xF3n trong th\u01B0 vi\u1EC7n ph\u1EA3i c\xF3 t\xEAn ri\xEAng \u0111\u1EC3 c\xF3 th\u1EC3 l\u1ECDc. C\xE1c m\xF3n th\u01B0 vi\u1EC7n sau \u0111\xE2y s\u1EBD th\xEAm:",
  atleastOneLibItem: "Vui l\xF2ng ch\u1ECDn \xEDt nh\u1EA5t m\u1ED9t m\xF3n th\u01B0 vi\u1EC7n \u0111\u1EC3 b\u1EAFt \u0111\u1EA7u",
  republishWarning: "L\u01B0u \xFD: m\u1ED9t s\u1ED1 m\xF3n \u0111\xE3 ch\u1ECDn \u0111\u01B0\u1EE3c \u0111\xE1nh d\u1EA5u l\xE0 \u0111\xE3 xu\u1EA5t b\u1EA3n/\u0111\xE3 g\u1EEDi. B\u1EA1n ch\u1EC9 n\xEAn g\u1EEDi l\u1EA1i c\xE1c m\xF3n khi c\u1EADp nh\u1EADt th\u01B0 vi\u1EC7n hi\u1EC7n c\xF3 ho\u1EB7c g\u1EEDi."
};
var publishSuccessDialog = {
  title: "",
  content: ""
};
var confirmDialog = {
  resetLibrary: "",
  removeItemsFromLib: "X\xF3a m\xF3n \u0111\xE3 ch\u1ECDn kh\u1ECFi th\u01B0 vi\u1EC7n"
};
var imageExportDialog = {
  header: "",
  label: {
    withBackground: "",
    onlySelected: "",
    darkMode: "",
    embedScene: "",
    scale: "",
    padding: ""
  },
  tooltip: {
    embedScene: ""
  },
  title: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  },
  button: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  }
};
var encrypted = {
  tooltip: "",
  link: ""
};
var stats = {
  angle: "",
  element: "",
  elements: "",
  height: "",
  scene: "",
  selected: "",
  storage: "",
  title: "",
  total: "",
  version: "",
  versionCopy: "",
  versionNotAvailable: "",
  width: ""
};
var toast = {
  addedToLibrary: "",
  copyStyles: "",
  copyToClipboard: "",
  copyToClipboardAsPng: "",
  fileSaved: "",
  fileSavedToFilename: "",
  canvas: "canvas",
  selection: "",
  pasteAsSingleElement: "",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "",
  black: "",
  white: "",
  red: "",
  pink: "",
  grape: "",
  violet: "",
  gray: "",
  blue: "",
  cyan: "",
  teal: "",
  green: "",
  yellow: "",
  orange: "",
  bronze: ""
};
var welcomeScreen = {
  app: {
    center_heading: "",
    center_heading_plus: "",
    menuHint: ""
  },
  defaults: {
    menuHint: "",
    center_heading: "",
    toolbarHint: "",
    helpHint: ""
  }
};
var colorPicker = {
  mostUsedCustomColors: "",
  colors: "",
  shades: "",
  hexCode: "",
  noShades: ""
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "",
      button: "",
      description: ""
    },
    saveToDisk: {
      title: "",
      button: "",
      description: ""
    },
    excalidrawPlus: {
      title: "",
      button: "",
      description: ""
    }
  },
  modal: {
    loadFromFile: {
      title: "",
      button: "",
      description: ""
    },
    shareableLink: {
      title: "",
      button: "",
      description: ""
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var vi_VN_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  vi_VN_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=vi-VN-Y5CQ2EKQ.js.map
