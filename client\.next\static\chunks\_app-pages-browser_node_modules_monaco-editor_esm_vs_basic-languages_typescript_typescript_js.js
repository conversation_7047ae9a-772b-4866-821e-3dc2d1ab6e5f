"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_typescript_typescript_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/typescript/typescript.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/typescript/typescript.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.api.js\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/basic-languages/typescript/typescript.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  onEnterRules: [\n    {\n      // e.g. /** | */\n      beforeText: /^\\s*\\/\\*\\*(?!\\/)([^\\*]|\\*(?!\\/))*$/,\n      afterText: /^\\s*\\*\\/$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent,\n        appendText: \" * \"\n      }\n    },\n    {\n      // e.g. /** ...|\n      beforeText: /^\\s*\\/\\*\\*(?!\\/)([^\\*]|\\*(?!\\/))*$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.None,\n        appendText: \" * \"\n      }\n    },\n    {\n      // e.g.  * ...|\n      beforeText: /^(\\t|(\\ \\ ))*\\ \\*(\\ ([^\\*]|\\*(?!\\/))*)?$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.None,\n        appendText: \"* \"\n      }\n    },\n    {\n      // e.g.  */|\n      beforeText: /^(\\t|(\\ \\ ))*\\ \\*\\/\\s*$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.None,\n        removeText: 1\n      }\n    }\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: \"`\", close: \"`\", notIn: [\"string\", \"comment\"] },\n    { open: \"/**\", close: \" */\", notIn: [\"string\"] }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*//\\\\s*#?region\\\\b\"),\n      end: new RegExp(\"^\\\\s*//\\\\s*#?endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  defaultToken: \"invalid\",\n  tokenPostfix: \".ts\",\n  keywords: [\n    // Should match the keys of textToKeywordObj in\n    // https://github.com/microsoft/TypeScript/blob/master/src/compiler/scanner.ts\n    \"abstract\",\n    \"any\",\n    \"as\",\n    \"asserts\",\n    \"bigint\",\n    \"boolean\",\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"class\",\n    \"continue\",\n    \"const\",\n    \"constructor\",\n    \"debugger\",\n    \"declare\",\n    \"default\",\n    \"delete\",\n    \"do\",\n    \"else\",\n    \"enum\",\n    \"export\",\n    \"extends\",\n    \"false\",\n    \"finally\",\n    \"for\",\n    \"from\",\n    \"function\",\n    \"get\",\n    \"if\",\n    \"implements\",\n    \"import\",\n    \"in\",\n    \"infer\",\n    \"instanceof\",\n    \"interface\",\n    \"is\",\n    \"keyof\",\n    \"let\",\n    \"module\",\n    \"namespace\",\n    \"never\",\n    \"new\",\n    \"null\",\n    \"number\",\n    \"object\",\n    \"out\",\n    \"package\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"override\",\n    \"readonly\",\n    \"require\",\n    \"global\",\n    \"return\",\n    \"satisfies\",\n    \"set\",\n    \"static\",\n    \"string\",\n    \"super\",\n    \"switch\",\n    \"symbol\",\n    \"this\",\n    \"throw\",\n    \"true\",\n    \"try\",\n    \"type\",\n    \"typeof\",\n    \"undefined\",\n    \"unique\",\n    \"unknown\",\n    \"var\",\n    \"void\",\n    \"while\",\n    \"with\",\n    \"yield\",\n    \"async\",\n    \"await\",\n    \"of\"\n  ],\n  operators: [\n    \"<=\",\n    \">=\",\n    \"==\",\n    \"!=\",\n    \"===\",\n    \"!==\",\n    \"=>\",\n    \"+\",\n    \"-\",\n    \"**\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"++\",\n    \"--\",\n    \"<<\",\n    \"</\",\n    \">>\",\n    \">>>\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"!\",\n    \"~\",\n    \"&&\",\n    \"||\",\n    \"??\",\n    \"?\",\n    \":\",\n    \"=\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"**=\",\n    \"/=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\",\n    \">>>=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"@\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+(_+\\d+)*/,\n  octaldigits: /[0-7]+(_+[0-7]+)*/,\n  binarydigits: /[0-1]+(_+[0-1]+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n  regexpesc: /\\\\(?:[bBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [[/[{}]/, \"delimiter.bracket\"], { include: \"common\" }],\n    common: [\n      // identifiers and keywords\n      [\n        /#?[a-z_$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[A-Z][\\w\\$]*/, \"type.identifier\"],\n      // to show class names nicely\n      // [/[A-Z][\\w\\$]*/, 'identifier'],\n      // whitespace\n      { include: \"@whitespace\" },\n      // regular expression: ensure it is terminated before beginning (otherwise it is an opeator)\n      [\n        /\\/(?=([^\\\\\\/]|\\\\.)+\\/([dgimsuy]*)(\\s*)(\\.|;|,|\\)|\\]|\\}|$))/,\n        { token: \"regexp\", bracket: \"@open\", next: \"@regexp\" }\n      ],\n      // delimiters and operators\n      [/[()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/!(?=([^=]|$))/, \"delimiter\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?/, \"number.float\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?/, \"number.float\"],\n      [/0[xX](@hexdigits)n?/, \"number.hex\"],\n      [/0[oO]?(@octaldigits)n?/, \"number.octal\"],\n      [/0[bB](@binarydigits)n?/, \"number.binary\"],\n      [/(@digits)n?/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string_double\"],\n      [/'/, \"string\", \"@string_single\"],\n      [/`/, \"string\", \"@string_backtick\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@jsdoc\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    jsdoc: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    // We match regular expression quite precisely\n    regexp: [\n      [\n        /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n        [\"regexp.escape.control\", \"regexp.escape.control\", \"regexp.escape.control\"]\n      ],\n      [\n        /(\\[)(\\^?)(?=(?:[^\\]\\\\\\/]|\\\\.)+)/,\n        [\"regexp.escape.control\", { token: \"regexp.escape.control\", next: \"@regexrange\" }]\n      ],\n      [/(\\()(\\?:|\\?=|\\?!)/, [\"regexp.escape.control\", \"regexp.escape.control\"]],\n      [/[()]/, \"regexp.escape.control\"],\n      [/@regexpctl/, \"regexp.escape.control\"],\n      [/[^\\\\\\/]/, \"regexp\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/\\\\\\./, \"regexp.invalid\"],\n      [/(\\/)([dgimsuy]*)/, [{ token: \"regexp\", bracket: \"@close\", next: \"@pop\" }, \"keyword.other\"]]\n    ],\n    regexrange: [\n      [/-/, \"regexp.escape.control\"],\n      [/\\^/, \"regexp.invalid\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/[^\\]]/, \"regexp\"],\n      [\n        /\\]/,\n        {\n          token: \"regexp.escape.control\",\n          next: \"@pop\",\n          bracket: \"@close\"\n        }\n      ]\n    ],\n    string_double: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    string_single: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, \"string\", \"@pop\"]\n    ],\n    string_backtick: [\n      [/\\$\\{/, { token: \"delimiter.bracket\", next: \"@bracketCounting\" }],\n      [/[^\\\\`$]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/`/, \"string\", \"@pop\"]\n    ],\n    bracketCounting: [\n      [/\\{/, \"delimiter.bracket\", \"@bracketCounting\"],\n      [/\\}/, \"delimiter.bracket\", \"@pop\"],\n      { include: \"common\" }\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/typescript/typescript.js\n"));

/***/ })

}]);