"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var monaco_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! monaco-editor */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.main.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlignLeft,FiCode,FiCopy,FiDownload,FiHelpCircle,FiLoader,FiMoon,FiPlay,FiSearch,FiSun,FiUpload,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_socketService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/socketService */ \"(app-pages-browser)/./src/services/socketService.ts\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/throttle */ \"(app-pages-browser)/./node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/SocketContext */ \"(app-pages-browser)/./src/context/SocketContext.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_formatCode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/formatCode */ \"(app-pages-browser)/./src/utils/formatCode.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/ThemeContext */ \"(app-pages-browser)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CodeEditor(param) {\n    let { roomId, username: initialUsername } = param;\n    _s();\n    const { isConnected } = (0,_context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket)();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"// Start coding...\");\n    // Use a ref to always track the latest code value\n    const latestCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"// Start coding...\");\n    // Get username from localStorage if available, otherwise use initialUsername\n    const storedUsername =  true ? localStorage.getItem(\"username\") : 0;\n    console.log(\"Initial username: \".concat(initialUsername, \", Stored username: \").concat(storedUsername));\n    // Track the username internally to handle server validation\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUsername || initialUsername);\n    const [typingUser, setTypingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeUsers, setActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUserList, setShowUserList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"javascript\");\n    const [copySuccess, setCopySuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [teacherSelectionDecorations, setTeacherSelectionDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [teacherTextHighlightDecorations, setTeacherTextHighlightDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [teacherCursorPosition, setTeacherCursorPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Code execution states\n    const [isExecuting, setIsExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionOutput, setExecutionOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [executionError, setExecutionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOutput, setShowOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Piston API runtimes\n    const [runtimes, setRuntimes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [runtimesLoaded, setRuntimesLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    // Theme and minimap state (ensure these are always defined at the top)\n    const { theme, setTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme)();\n    const [showShortcuts, setShowShortcuts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [minimapEnabled, setMinimapEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Language-specific code snippets\n    const SNIPPETS = {\n        javascript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            }\n        ],\n        typescript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction(): void {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            },\n            {\n                label: \"Type Declaration\",\n                value: \"type MyType = {\\n  key: string;\\n};\"\n            }\n        ],\n        python3: [\n            {\n                label: \"Function\",\n                value: \"def my_function():\\n    # code\\n    pass\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in range(10):\\n    # code\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition:\\n    # code\"\n            },\n            {\n                label: \"Print\",\n                value: \"print('Hello, World!')\"\n            }\n        ],\n        java: [\n            {\n                label: \"Main Method\",\n                value: \"public static void main(String[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"System.out.println(\\\"Hello, World!\\\");\"\n            }\n        ],\n        csharp: [\n            {\n                label: \"Main Method\",\n                value: \"static void Main(string[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"Console.WriteLine(\\\"Hello, World!\\\");\"\n            }\n        ],\n        c: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"printf(\\\"Hello, World!\\\\n\\\");\"\n            }\n        ],\n        cpp: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"std::cout << \\\"Hello, World!\\\" << std::endl;\"\n            }\n        ],\n        go: [\n            {\n                label: \"Main Function\",\n                value: \"func main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i := 0; i < 10; i++ {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"fmt.Println(\\\"Hello, World!\\\")\"\n            }\n        ],\n        ruby: [\n            {\n                label: \"Method\",\n                value: \"def my_method\\n  # code\\nend\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..9\\n  # code\\nend\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition\\n  # code\\nend\"\n            },\n            {\n                label: \"Print\",\n                value: \"puts 'Hello, World!'\"\n            }\n        ],\n        rust: [\n            {\n                label: \"Main Function\",\n                value: \"fn main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..10 {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"println!(\\\"Hello, World!\\\");\"\n            }\n        ],\n        php: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for ($i = 0; $i < 10; $i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if ($condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"echo 'Hello, World!';\"\n            }\n        ]\n    };\n    // Declare the `socketService` instance at the top of the file\n    const socketService = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n    // Leave room handler\n    const handleLeaveRoom = ()=>{\n        socketService.leaveRoom(roomId);\n        router.push(\"/dashboard\");\n    };\n    // Handle editor mounting\n    const handleEditorDidMount = (editor)=>{\n        editorRef.current = editor;\n        setIsLoading(false);\n        // Auto-focus the editor after mounting\n        setTimeout(()=>{\n            if (editor) {\n                editor.focus();\n                // Add keyboard shortcut (Ctrl+Enter) to run code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.CtrlCmd | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.Enter, ()=>{\n                    executeCode();\n                });\n                // Add keyboard shortcut (Shift+Alt+F) to format code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Shift | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Alt | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.KeyF, ()=>{\n                    formatCurrentCode();\n                });\n                // Add teacher cursor and selection listeners\n                if (userRole === 'teacher') {\n                    // Track cursor position changes\n                    editor.onDidChangeCursorPosition((e)=>{\n                        const position = e.position;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        if (roomId) {\n                            // Send cursor position to students\n                            socketServiceInstance.sendTeacherCursorPosition(roomId, {\n                                lineNumber: position.lineNumber,\n                                column: position.column\n                            });\n                        }\n                    });\n                    // Track selection changes for text highlighting\n                    editor.onDidChangeCursorSelection((e)=>{\n                        const selection = e.selection;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        if (!selection.isEmpty() && roomId) {\n                            // Send text highlight to students\n                            console.log('Teacher made text selection:', selection);\n                            socketServiceInstance.sendTeacherTextHighlight(roomId, {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            });\n                        } else if (roomId) {\n                            // Clear text highlight when teacher deselects\n                            socketServiceInstance.clearTeacherTextHighlight(roomId);\n                        }\n                    });\n                }\n            }\n        }, 500) // Small delay to ensure the editor is fully ready\n        ;\n    };\n    // Track if the change is from a remote update\n    const isRemoteUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Create a throttled version of the code change handler\n    // This prevents sending too many updates when typing quickly\n    // but ensures updates are sent at regular intervals\n    const throttledCodeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default()({\n        \"CodeEditor.useRef\": (code)=>{\n            if (roomId && isConnected) {\n                console.log(\"Sending throttled code update to room \".concat(roomId, \", length: \").concat(code.length));\n                socketService.sendCodeChange(roomId, code);\n            }\n        }\n    }[\"CodeEditor.useRef\"], 50) // Use throttle with a short interval for more responsive updates\n    ).current;\n    // Create a debounced version of the typing notification\n    const debouncedTypingNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default()({\n        \"CodeEditor.useCallback[debouncedTypingNotification]\": ()=>{\n            if (roomId && isConnected) {\n                // Always send the current username with typing notifications\n                console.log(\"Sending typing notification with username: \".concat(username));\n                socketService.sendTyping(roomId, username);\n            }\n        }\n    }[\"CodeEditor.useCallback[debouncedTypingNotification]\"], 1000), [\n        roomId,\n        isConnected,\n        username\n    ]);\n    // Handle editor change\n    const handleEditorChange = (value)=>{\n        if (typeof value !== \"string\") return;\n        // Make sure loading is off during code changes\n        setIsLoading(false);\n        // If this change is from a remote update, just update the state and return\n        if (isRemoteUpdate.current) {\n            console.log(\"Handling remote update, not emitting\");\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            isRemoteUpdate.current = false;\n            return;\n        }\n        // Only process if the value actually changed\n        if (value !== latestCodeRef.current) {\n            // Update local state immediately\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            // Send typing notification (debounced)\n            debouncedTypingNotification();\n            // Send code update (throttled)\n            throttledCodeChange(value);\n            // If not connected, try to reconnect\n            if (!isConnected) {\n                console.log(\"Socket not connected, attempting to reconnect\");\n                socketService.connect();\n            }\n        }\n    };\n    // Copy code to clipboard\n    const copyCodeToClipboard = ()=>{\n        if (navigator.clipboard && code) {\n            navigator.clipboard.writeText(code).then(()=>{\n                setCopySuccess(true);\n                setTimeout(()=>setCopySuccess(false), 2000);\n            }).catch((err)=>{\n                console.error('Failed to copy code: ', err);\n            });\n        }\n    };\n    // Toggle user list\n    const toggleUserList = ()=>{\n        setShowUserList((prev)=>!prev);\n    };\n    // Change language\n    const changeLanguage = (lang)=>{\n        setLanguage(lang);\n        // Log available runtimes for this language\n        if (runtimesLoaded && runtimes.length > 0) {\n            const availableRuntimes = runtimes.filter((runtime)=>runtime.language === lang || runtime.aliases && runtime.aliases.includes(lang));\n            if (availableRuntimes.length > 0) {\n                console.log(\"Available runtimes for \".concat(lang, \":\"), availableRuntimes);\n            } else {\n                console.log(\"No runtimes available for \".concat(lang));\n            }\n        }\n    };\n    // Format code\n    const formatCurrentCode = ()=>{\n        if (!code || !editorRef.current) return;\n        try {\n            // Format the code based on the current language\n            const formattedCode = (0,_utils_formatCode__WEBPACK_IMPORTED_MODULE_8__.formatCode)(code, language);\n            // Only update if the code actually changed\n            if (formattedCode !== code) {\n                // Update the editor\n                const model = editorRef.current.getModel();\n                if (model) {\n                    // Store current cursor position/selection\n                    const currentPosition = editorRef.current.getPosition();\n                    const currentSelection = editorRef.current.getSelection();\n                    // Update the editor content\n                    editorRef.current.executeEdits('format', [\n                        {\n                            range: model.getFullModelRange(),\n                            text: formattedCode,\n                            forceMoveMarkers: true\n                        }\n                    ]);\n                    // Restore cursor position if possible\n                    if (currentPosition) {\n                        editorRef.current.setPosition(currentPosition);\n                    }\n                    if (currentSelection) {\n                        editorRef.current.setSelection(currentSelection);\n                    }\n                    // Update state and ref\n                    setCode(formattedCode);\n                    latestCodeRef.current = formattedCode;\n                    // Send the formatted code to other users\n                    if (roomId && isConnected) {\n                        console.log(\"Sending formatted code to room \".concat(roomId, \", length: \").concat(formattedCode.length));\n                        socketService.sendCodeChange(roomId, formattedCode);\n                    }\n                    // Show a success message\n                    console.log('Code formatted successfully');\n                }\n            } else {\n                console.log('Code is already formatted');\n            }\n        } catch (error) {\n            console.error('Error formatting code:', error);\n        }\n    };\n    // Clear execution output\n    const clearOutput = ()=>{\n        setExecutionOutput(null);\n        setExecutionError(null);\n    };\n    // Fetch and store available runtimes from Piston API\n    const fetchRuntimes = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get('https://emkc.org/api/v2/piston/runtimes');\n            console.log('Piston API status:', response.status);\n            console.log('Available runtimes:', response.data);\n            // Store the runtimes in state\n            setRuntimes(response.data);\n            setRuntimesLoaded(true);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error fetching Piston API runtimes:', error);\n            return {\n                success: false,\n                error: axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) ? \"\".concat(error.message, \" - \").concat(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) || 'Unknown', \" \").concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText) || '') : String(error)\n            };\n        }\n    };\n    // Check if the Piston API is available\n    const checkPistonAPI = async ()=>{\n        // If we haven't loaded runtimes yet, fetch them\n        if (!runtimesLoaded) {\n            return await fetchRuntimes();\n        }\n        // If we already have runtimes, just return success\n        if (runtimes.length > 0) {\n            return {\n                success: true,\n                data: runtimes\n            };\n        }\n        // If we've tried to load runtimes but have none, try again\n        return await fetchRuntimes();\n    };\n    // Test the Piston API with a hardcoded sample payload\n    const testPistonAPI = async ()=>{\n        setIsExecuting(true);\n        setExecutionError(null);\n        setExecutionOutput(null);\n        setShowOutput(true);\n        try {\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Find JavaScript runtime\n            const jsRuntimes = apiStatus.data.filter((runtime)=>runtime.language === \"javascript\" || runtime.aliases && runtime.aliases.includes(\"javascript\"));\n            if (jsRuntimes.length === 0) {\n                setExecutionError('No JavaScript runtime found. Please try a different language.');\n                return;\n            }\n            const jsRuntime = jsRuntimes[0];\n            console.log(\"Using JavaScript runtime: \".concat(jsRuntime.language, \" \").concat(jsRuntime.version));\n            // Sample JavaScript code that should work\n            const samplePayload = {\n                language: jsRuntime.language,\n                version: jsRuntime.version,\n                files: [\n                    {\n                        name: \"main.js\",\n                        content: \"console.log('Hello, World!');\"\n                    }\n                ],\n                stdin: \"\",\n                args: []\n            };\n            console.log('Testing Piston API with sample payload:', JSON.stringify(samplePayload, null, 2));\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', samplePayload);\n            console.log('Sample execution response:', response.data);\n            if (response.data.run) {\n                setExecutionOutput(\"API Test Successful!\\n\\n\" + \"Output: \" + response.data.run.stdout + \"\\n\\n\" + \"The API is working correctly. You can now run your own code.\");\n            } else {\n                setExecutionError('API test failed. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error testing Piston API:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                setExecutionError(\"API Test Failed: \".concat(error.response.status, \" \").concat(error.response.statusText, \"\\n\\n\") + JSON.stringify(error.response.data, null, 2));\n            } else {\n                setExecutionError(error instanceof Error ? \"API Test Error: \".concat(error.message) : 'An unknown error occurred while testing the API.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // Execute code using Piston API\n    const executeCode = async ()=>{\n        if (!code || isExecuting) return;\n        // Map Monaco editor language to Piston API language\n        const languageMap = {\n            javascript: \"javascript\",\n            typescript: \"typescript\",\n            python: \"python3\",\n            python3: \"python3\",\n            java: \"java\",\n            csharp: \"csharp\",\n            c: \"c\",\n            cpp: \"cpp\",\n            go: \"go\",\n            ruby: \"ruby\",\n            rust: \"rust\",\n            php: \"php\"\n        };\n        // Get the mapped language\n        const pistonLanguage = languageMap[language] || language;\n        // Check if we have runtimes loaded\n        if (!runtimesLoaded || runtimes.length === 0) {\n            setExecutionError('Runtimes not loaded yet. Please try again in a moment.');\n            setShowOutput(true);\n            return;\n        }\n        // Find available runtimes for the selected language\n        const availableRuntimes = runtimes.filter((runtime)=>runtime.language === pistonLanguage || runtime.aliases && runtime.aliases.includes(pistonLanguage));\n        // Check if the language is supported\n        if (availableRuntimes.length === 0) {\n            // Get a list of supported languages from the runtimes\n            const supportedLanguages = [\n                ...new Set(runtimes.flatMap((runtime)=>[\n                        runtime.language,\n                        ...runtime.aliases || []\n                    ]))\n            ].sort();\n            setExecutionError(\"The language '\".concat(language, \"' (mapped to '\").concat(pistonLanguage, \"') is not supported by the Piston API.\\n\\n\") + \"Supported languages: \".concat(supportedLanguages.join(', ')));\n            setShowOutput(true);\n            return;\n        }\n        // Get the latest version of the runtime\n        const selectedRuntime = availableRuntimes[0];\n        try {\n            setIsExecuting(true);\n            setExecutionError(null);\n            setExecutionOutput(null);\n            setShowOutput(true);\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Determine file extension based on language\n            let fileExtension = '';\n            if (selectedRuntime.language === 'python3' || selectedRuntime.language === 'python') fileExtension = '.py';\n            else if (selectedRuntime.language === 'javascript') fileExtension = '.js';\n            else if (selectedRuntime.language === 'typescript') fileExtension = '.ts';\n            else if (selectedRuntime.language === 'java') fileExtension = '.java';\n            else if (selectedRuntime.language === 'csharp') fileExtension = '.cs';\n            else if (selectedRuntime.language === 'c') fileExtension = '.c';\n            else if (selectedRuntime.language === 'cpp') fileExtension = '.cpp';\n            else if (selectedRuntime.language === 'go') fileExtension = '.go';\n            else if (selectedRuntime.language === 'rust') fileExtension = '.rs';\n            else if (selectedRuntime.language === 'ruby') fileExtension = '.rb';\n            else if (selectedRuntime.language === 'php') fileExtension = '.php';\n            else fileExtension = \".\".concat(selectedRuntime.language);\n            console.log(\"Selected runtime: \".concat(selectedRuntime.language, \" \").concat(selectedRuntime.version));\n            // Prepare the payload according to Piston API documentation\n            const payload = {\n                language: selectedRuntime.language,\n                version: selectedRuntime.version,\n                files: [\n                    {\n                        name: \"main\".concat(fileExtension),\n                        content: code\n                    }\n                ],\n                stdin: '',\n                args: [],\n                compile_timeout: 10000,\n                run_timeout: 5000\n            };\n            // Log the payload for debugging\n            console.log(\"Executing \".concat(pistonLanguage, \" code with payload:\"), JSON.stringify(payload, null, 2));\n            // Make the API request\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', payload);\n            console.log('Execution response:', response.data);\n            const result = response.data;\n            if (result.run) {\n                // Format the output\n                let output = '';\n                let hasOutput = false;\n                // Add compile output if available (for compiled languages)\n                if (result.compile && result.compile.stderr) {\n                    output += \"Compilation output:\\n\".concat(result.compile.stderr, \"\\n\\n\");\n                    hasOutput = true;\n                }\n                // Add standard output\n                if (result.run.stdout) {\n                    output += result.run.stdout;\n                    hasOutput = true;\n                }\n                // Add error output\n                if (result.run.stderr) {\n                    if (hasOutput) output += '\\n';\n                    output += \"Error output:\\n\".concat(result.run.stderr);\n                    hasOutput = true;\n                }\n                // Add exit code if non-zero\n                if (result.run.code !== 0) {\n                    if (hasOutput) output += '\\n';\n                    output += \"\\nProcess exited with code \".concat(result.run.code);\n                    hasOutput = true;\n                }\n                if (!hasOutput) {\n                    output = 'Program executed successfully with no output.';\n                }\n                setExecutionOutput(output);\n            } else {\n                setExecutionError('Failed to execute code. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error executing code:', error);\n            // Handle Axios errors with more detailed information\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                const statusCode = error.response.status;\n                const responseData = error.response.data;\n                console.error('API Error Details:', {\n                    status: statusCode,\n                    data: responseData\n                });\n                // Format a more helpful error message\n                if (statusCode === 400) {\n                    setExecutionError(\"API Error (400 Bad Request): \".concat(JSON.stringify(responseData), \"\\n\\n\") + 'This usually means the API request format is incorrect. ' + 'Please check the console for more details.');\n                } else if (statusCode === 429) {\n                    setExecutionError('Rate limit exceeded. Please try again later.');\n                } else {\n                    setExecutionError(\"API Error (\".concat(statusCode, \"): \").concat(JSON.stringify(responseData), \"\\n\\n\") + 'Please check the console for more details.');\n                }\n            } else {\n                // Handle non-Axios errors\n                setExecutionError(error instanceof Error ? \"Error: \".concat(error.message) : 'An unknown error occurred while executing the code.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // This function will be called when we receive a code update from another user\n    const handleCodeUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleCodeUpdate]\": (incomingCode)=>{\n            console.log(\"Remote code update received, length:\", incomingCode.length);\n            // Make sure loading is off during code updates\n            setIsLoading(false);\n            // Only update if the code is different from our latest code\n            if (incomingCode !== latestCodeRef.current) {\n                try {\n                    // Set the flag to indicate this is a remote update\n                    isRemoteUpdate.current = true;\n                    // Update the editor if it's mounted\n                    if (editorRef.current) {\n                        console.log(\"Updating editor with remote code\");\n                        // Use the editor's model to update the value to preserve cursor position\n                        const model = editorRef.current.getModel();\n                        if (model) {\n                            // Store current cursor position/selection\n                            const currentPosition = editorRef.current.getPosition();\n                            const currentSelection = editorRef.current.getSelection();\n                            // Use executeEdits with better handling of cursor position\n                            editorRef.current.executeEdits('remote', [\n                                {\n                                    range: model.getFullModelRange(),\n                                    text: incomingCode,\n                                    forceMoveMarkers: true\n                                }\n                            ]);\n                            // Restore cursor position if possible\n                            if (currentPosition) {\n                                editorRef.current.setPosition(currentPosition);\n                            }\n                            if (currentSelection) {\n                                editorRef.current.setSelection(currentSelection);\n                            }\n                            // Also update our state and ref\n                            setCode(incomingCode);\n                            latestCodeRef.current = incomingCode;\n                        }\n                    } else {\n                        // If editor isn't mounted yet, just update the state and ref\n                        console.log(\"Editor not mounted, updating state only\");\n                        setCode(incomingCode);\n                        latestCodeRef.current = incomingCode;\n                        isRemoteUpdate.current = false;\n                    }\n                } catch (error) {\n                    console.error(\"Error updating editor with remote code:\", error);\n                    isRemoteUpdate.current = false;\n                }\n            } else {\n                console.log(\"Remote code matches current code, ignoring\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleCodeUpdate]\"], []);\n    // State to track cursor positions\n    const [cursorPositions, setCursorPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Emit cursor movement\n    const handleMouseMove = (e)=>{\n        const position = {\n            x: e.clientX,\n            y: e.clientY\n        };\n        _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().sendCursorMove(roomId, username, position);\n    };\n    // Listen for cursor movement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            const handleCursorMove = {\n                \"CodeEditor.useEffect.handleCursorMove\": (param)=>{\n                    let { userId, position } = param;\n                    setCursorPositions({\n                        \"CodeEditor.useEffect.handleCursorMove\": (prev)=>({\n                                ...prev,\n                                [userId]: position\n                            })\n                    }[\"CodeEditor.useEffect.handleCursorMove\"]);\n                }\n            }[\"CodeEditor.useEffect.handleCursorMove\"];\n            _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().on(\"cursor-move\", handleCursorMove);\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().off(\"cursor-move\", handleCursorMove);\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId\n    ]);\n    // Render cursors\n    const renderCursors = ()=>{\n        return Object.entries(cursorPositions).map((param)=>{\n            let [userId, position] = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    left: position.x,\n                    top: position.y,\n                    backgroundColor: \"red\",\n                    width: 10,\n                    height: 10,\n                    borderRadius: \"50%\",\n                    pointerEvents: \"none\"\n                }\n            }, userId, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 759,\n                columnNumber: 7\n            }, this);\n        });\n    };\n    // Fetch runtimes when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            fetchRuntimes();\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    // Handle request for initial code from new users\n    const handleGetInitialCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleGetInitialCode]\": (data)=>{\n            console.log(\"Received request for initial code from \".concat(data.requestingUsername, \" (\").concat(data.requestingUserId, \")\"));\n            // Only respond if we have code and we're connected\n            if (roomId && latestCodeRef.current && latestCodeRef.current.trim() !== \"// Start coding...\") {\n                const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                console.log(\"Sending initial code to \".concat(data.requestingUsername, \", length: \").concat(latestCodeRef.current.length));\n                socketServiceInstance.sendInitialCode(roomId, latestCodeRef.current, data.requestingUserId);\n            } else {\n                console.log(\"Not sending initial code - no meaningful code to share or not in room\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleGetInitialCode]\"], [\n        roomId\n    ]);\n    // Handle receiving initial code as a new user\n    const handleInitialCodeReceived = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleInitialCodeReceived]\": (data)=>{\n            console.log(\"Received initial code, length: \".concat(data.code.length));\n            // Only apply initial code if we don't have meaningful code yet\n            // This prevents overwriting code that the user might have already started typing\n            if (latestCodeRef.current === \"// Start coding...\" || latestCodeRef.current.trim() === \"\") {\n                console.log(\"Applying received initial code\");\n                // Set the flag to indicate this is a remote update\n                isRemoteUpdate.current = true;\n                // Update the code state and ref\n                setCode(data.code);\n                latestCodeRef.current = data.code;\n                // Update the editor if it's mounted\n                if (editorRef.current) {\n                    editorRef.current.setValue(data.code);\n                }\n            } else {\n                console.log(\"Not applying initial code - user already has meaningful code\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleInitialCodeReceived]\"], []);\n    // Handle teacher selection highlighting\n    const handleTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherSelection]\": (data)=>{\n            console.log(\"Received teacher selection from \".concat(data.teacherName, \":\"), data.selection);\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't show teacher highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model || !data.selection) {\n                    return;\n                }\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.selection.startLineNumber, data.selection.startColumn, data.selection.endLineNumber, data.selection.endColumn);\n                // Clear previous teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-selection-highlight',\n                            hoverMessage: {\n                                value: \"Teacher \".concat(data.teacherName, \" highlighted this text\")\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges\n                        }\n                    }\n                ]);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error applying teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle clearing teacher selection\n    const handleClearTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherSelection]\": (data)=>{\n            console.log(\"Teacher \".concat(data.teacherName, \" cleared selection\"));\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                // Clear all teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, []);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error clearing teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle teacher cursor position updates\n    const handleTeacherCursorPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherCursorPosition]\": (data)=>{\n            console.log(\"Teacher \".concat(data.teacherName, \" cursor at line \").concat(data.position.lineNumber, \", column \").concat(data.position.column));\n            if (userRole === 'teacher') {\n                return; // Don't show cursor to the teacher themselves\n            }\n            // Update teacher cursor position state\n            setTeacherCursorPosition({\n                lineNumber: data.position.lineNumber,\n                column: data.position.column,\n                teacherName: data.teacherName\n            });\n        }\n    }[\"CodeEditor.useCallback[handleTeacherCursorPosition]\"], [\n        userRole\n    ]);\n    // Handle teacher text highlight\n    const handleTeacherTextHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherTextHighlight]\": (data)=>{\n            console.log(\"Teacher \".concat(data.teacherName, \" highlighted text:\"), data.selection);\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't show highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model || !data.selection) {\n                    return;\n                }\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.selection.startLineNumber, data.selection.startColumn, data.selection.endLineNumber, data.selection.endColumn);\n                // Clear previous teacher text highlight decorations\n                const newDecorations = editor.deltaDecorations(teacherTextHighlightDecorations, [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-text-highlight',\n                            hoverMessage: {\n                                value: \"Teacher \".concat(data.teacherName, \" highlighted this text\")\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges\n                        }\n                    }\n                ]);\n                setTeacherTextHighlightDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error applying teacher text highlight:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"], [\n        userRole,\n        teacherTextHighlightDecorations\n    ]);\n    // Handle clearing teacher text highlight\n    const handleClearTeacherTextHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherTextHighlight]\": (data)=>{\n            console.log(\"Teacher \".concat(data.teacherName, \" cleared text highlight\"));\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                // Clear all teacher text highlight decorations\n                const newDecorations = editor.deltaDecorations(teacherTextHighlightDecorations, []);\n                setTeacherTextHighlightDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error clearing teacher text highlight:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherTextHighlight]\"], [\n        userRole,\n        teacherTextHighlightDecorations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            // Handle user typing notifications\n            const handleUserTyping = {\n                \"CodeEditor.useEffect.handleUserTyping\": (param)=>{\n                    let { username, userId } = param;\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    // Don't show typing indicator for current user\n                    if (userId && userId === currentUserId) {\n                        return;\n                    }\n                    // Use the exact username without any modifications\n                    // This ensures we display exactly what the user entered on the dashboard\n                    setTypingUser(username);\n                    console.log(\"User typing: \".concat(username));\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserTyping\": ()=>setTypingUser(null)\n                    }[\"CodeEditor.useEffect.handleUserTyping\"], 2000);\n                }\n            }[\"CodeEditor.useEffect.handleUserTyping\"];\n            // Handle user list updates from both user-joined/user-left and room-users-updated events\n            const handleUserListUpdate = {\n                \"CodeEditor.useEffect.handleUserListUpdate\": (data)=>{\n                    console.log('User list update received:', data);\n                    let users = [];\n                    // Handle different event formats\n                    if (Array.isArray(data)) {\n                        // This is from user-joined or user-left events\n                        users = data;\n                    } else if (data && Array.isArray(data.users)) {\n                        // This is from room-users-updated event\n                        users = data.users.map({\n                            \"CodeEditor.useEffect.handleUserListUpdate\": (user)=>{\n                                if (typeof user === 'string') {\n                                    // If it's just a username string, convert to user object\n                                    return {\n                                        username: user\n                                    };\n                                }\n                                return user;\n                            }\n                        }[\"CodeEditor.useEffect.handleUserListUpdate\"]);\n                    }\n                    console.log('Processed users:', users);\n                    console.log('Current username state:', username);\n                    console.log('Stored username:', localStorage.getItem('username'));\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    console.log('Current userId from localStorage:', currentUserId);\n                    // Filter out any invalid users and map to display names\n                    const usernames = users.filter({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>user && user.username\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]).map({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>{\n                            const displayName = user.username;\n                            const isCurrentUser = user.userId === currentUserId;\n                            return isCurrentUser ? \"\".concat(displayName, \" (you)\") : displayName;\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]);\n                    console.log('Setting active users:', usernames);\n                    // Only update if we have users to display\n                    if (usernames.length > 0) {\n                        setActiveUsers(usernames);\n                    } else if (activeUsers.length === 0) {\n                        // If we don't have users from the server but we're connected, add ourselves\n                        const storedUsername = localStorage.getItem('username');\n                        if (storedUsername) {\n                            setActiveUsers([\n                                \"\".concat(storedUsername, \" (you)\")\n                            ]);\n                        }\n                    }\n                    // Log the current state of the active users list\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserListUpdate\": ()=>{\n                            console.log('Active users state after update:', activeUsers);\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate\"], 100);\n                }\n            }[\"CodeEditor.useEffect.handleUserListUpdate\"];\n            // Register event listeners\n            const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n            socketServiceInstance.on('code-update', handleCodeUpdate);\n            socketServiceInstance.on('user-typing', handleUserTyping);\n            socketServiceInstance.on('user-joined', handleUserListUpdate);\n            socketServiceInstance.on('user-left', handleUserListUpdate);\n            socketServiceInstance.on('room-users-updated', handleUserListUpdate);\n            socketServiceInstance.on('get-initial-code', handleGetInitialCode);\n            socketServiceInstance.on('initial-code-received', handleInitialCodeReceived);\n            socketServiceInstance.on('teacher-selection', handleTeacherSelection);\n            socketServiceInstance.on('clear-teacher-selection', handleClearTeacherSelection);\n            socketServiceInstance.on('teacher-cursor-position', handleTeacherCursorPosition);\n            socketServiceInstance.on('teacher-text-highlight', handleTeacherTextHighlight);\n            socketServiceInstance.on('clear-teacher-text-highlight', handleClearTeacherTextHighlight);\n            // Join the room when component mounts\n            if (roomId) {\n                console.log(\"Joining room:\", roomId);\n                // Only show loading on initial join, not for code updates\n                if (!editorRef.current) {\n                    setIsLoading(true);\n                }\n                const joinRoom = {\n                    \"CodeEditor.useEffect.joinRoom\": async ()=>{\n                        try {\n                            if (socketServiceInstance.isConnected()) {\n                                console.log(\"Socket connected, joining room\");\n                                const { username: validatedUsername, users, role } = await socketServiceInstance.joinRoom(roomId, username);\n                                console.log(\"Successfully joined room:\", roomId);\n                                console.log(\"Users in room:\", users);\n                                console.log(\"User role:\", role);\n                                if (validatedUsername !== username) {\n                                    console.log(\"Server validated username from \".concat(username, \" to \").concat(validatedUsername));\n                                    setUsername(validatedUsername);\n                                    localStorage.setItem('username', validatedUsername);\n                                }\n                                // Set user role\n                                if (role) {\n                                    setUserRole(role);\n                                    console.log(\"User role set to: \".concat(role));\n                                }\n                                if (users && Array.isArray(users)) {\n                                    console.log('Setting active users from join response');\n                                    handleUserListUpdate(users);\n                                }\n                                setIsLoading(false);\n                            } else {\n                                console.log(\"Socket not connected, trying to connect...\");\n                                await new Promise({\n                                    \"CodeEditor.useEffect.joinRoom\": (resolve)=>socketServiceInstance.onConnect({\n                                            \"CodeEditor.useEffect.joinRoom\": ()=>resolve()\n                                        }[\"CodeEditor.useEffect.joinRoom\"])\n                                }[\"CodeEditor.useEffect.joinRoom\"]);\n                                // After connect, try join again\n                                await joinRoom();\n                                return;\n                            }\n                        } catch (error) {\n                            console.error(\"Error joining room:\", error);\n                            setIsLoading(false);\n                        }\n                    }\n                }[\"CodeEditor.useEffect.joinRoom\"];\n                // Start the join process\n                joinRoom();\n                // Set a timeout to hide the loading screen even if the join fails\n                setTimeout({\n                    \"CodeEditor.useEffect\": ()=>{\n                        setIsLoading(false);\n                    }\n                }[\"CodeEditor.useEffect\"], 3000);\n            }\n            // Clean up event listeners when component unmounts\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    // Use an instance of SocketService\n                    const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                    socketServiceInstance.off('code-update', handleCodeUpdate);\n                    socketServiceInstance.off('user-typing', handleUserTyping);\n                    socketServiceInstance.off('user-joined', handleUserListUpdate);\n                    socketServiceInstance.off('user-left', handleUserListUpdate);\n                    socketServiceInstance.off('room-users-updated', handleUserListUpdate);\n                    socketServiceInstance.off('get-initial-code', handleGetInitialCode);\n                    socketServiceInstance.off('initial-code-received', handleInitialCodeReceived);\n                    socketServiceInstance.off('teacher-selection', handleTeacherSelection);\n                    socketServiceInstance.off('clear-teacher-selection', handleClearTeacherSelection);\n                    socketServiceInstance.off('teacher-cursor-position', handleTeacherCursorPosition);\n                    socketServiceInstance.off('teacher-text-highlight', handleTeacherTextHighlight);\n                    socketServiceInstance.off('clear-teacher-text-highlight', handleClearTeacherTextHighlight);\n                    // Leave the room when component unmounts\n                    if (roomId) {\n                        socketServiceInstance.leaveRoom(roomId);\n                    }\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId,\n        initialUsername,\n        username,\n        handleCodeUpdate,\n        handleGetInitialCode,\n        handleInitialCodeReceived,\n        handleTeacherSelection,\n        handleClearTeacherSelection,\n        handleTeacherCursorPosition,\n        handleTeacherTextHighlight,\n        handleClearTeacherTextHighlight\n    ]);\n    // Download code as file\n    const handleDownload = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"code-\".concat(roomId || \"session\", \".txt\");\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    // Upload code from file\n    const handleUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            var _event_target;\n            if (typeof ((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result) === \"string\") {\n                setCode(event.target.result);\n                latestCodeRef.current = event.target.result;\n            }\n        };\n        reader.readAsText(file);\n    };\n    // Show Monaco's find/replace dialog\n    const handleFindReplace = ()=>{\n        if (editorRef.current) {\n            const action = editorRef.current.getAction(\"actions.find\");\n            if (action) action.run();\n        }\n    };\n    // Insert code snippet\n    const insertSnippet = (snippet)=>{\n        if (editorRef.current) {\n            const model = editorRef.current.getModel();\n            const position = editorRef.current.getPosition();\n            if (model && position) {\n                editorRef.current.executeEdits(\"snippet\", [\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(position.lineNumber, position.column, position.lineNumber, position.column),\n                        text: snippet,\n                        forceMoveMarkers: true\n                    }\n                ]);\n                editorRef.current.focus();\n            }\n        }\n    };\n    // Use the instance of SocketService for the connect method\n    const handleReconnect = ()=>{\n        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n        socketServiceInstance.connect();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-[80vh] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-800 shadow-xl flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"bg-gray-100 dark:bg-zinc-800 px-4 py-2 flex flex-wrap items-center justify-between border-b border-gray-200 dark:border-gray-700 gap-2 min-h-[48px]\",\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    style: {\n                        zIndex: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-3 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 min-w-[90px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1200,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-400 truncate\",\n                                            children: isConnected ? 'Connected' : 'Disconnected'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1201,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded ml-1 transition-colors\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: language,\n                                    onChange: (e)=>changeLanguage(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"javascript\",\n                                            children: \"JavaScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1220,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"typescript\",\n                                            children: \"TypeScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1221,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"python3\",\n                                            children: \"Python\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1222,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"java\",\n                                            children: \"Java\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"csharp\",\n                                            children: \"C#\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"c\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"cpp\",\n                                            children: \"C++\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1226,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"go\",\n                                            children: \"Go\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ruby\",\n                                            children: \"Ruby\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rust\",\n                                            children: \"Rust\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1229,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"php\",\n                                            children: \"PHP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1230,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: formatCurrentCode,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm bg-blue-600 hover:bg-blue-700 text-white transition-colors whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiAlignLeft, {\n                                            size: 14\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1240,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Format\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1241,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: executeCode,\n                                    disabled: isExecuting,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm \".concat(isExecuting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700', \" text-white transition-colors whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: isExecuting ? 1 : 1.05\n                                    },\n                                    whileTap: {\n                                        scale: isExecuting ? 1 : 0.95\n                                    },\n                                    children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                                className: \"animate-spin\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1254,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Running...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1255,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiPlay, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1259,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Run Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1260,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Room:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-white dark:bg-zinc-700 px-2 py-1 rounded font-mono text-xs truncate max-w-[120px]\",\n                                            children: roomId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                            onClick: copyCodeToClipboard,\n                                            className: \"text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            title: \"Copy code to clipboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCopy, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1276,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1266,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-2 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowOutput((prev)=>!prev),\n                                    className: \"flex items-center space-x-1 text-sm \".concat(showOutput ? 'text-blue-500 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300', \" hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCode, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1289,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Output\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1290,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: toggleUserList,\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUsers, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1300,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                activeUsers.length,\n                                                \" online\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1301,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1294,\n                                    columnNumber: 13\n                                }, this),\n                                userRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    className: \"flex items-center space-x-1 text-xs font-medium px-2 py-1 rounded-full whitespace-nowrap \".concat(userRole === 'teacher' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'),\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: userRole === 'teacher' ? '👨‍🏫' : '👨‍🎓'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1316,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: userRole === 'teacher' ? 'Teacher' : 'Student'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1317,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1306,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"flex items-center space-x-1 text-sm text-yellow-600 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSun, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1329,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiMoon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1329,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setMinimapEnabled((v)=>!v),\n                                    className: \"flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Minimap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Minimap\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1340,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleFindReplace,\n                                    className: \"flex items-center space-x-1 text-sm text-green-600 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Find/Replace\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSearch, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1351,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1344,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleDownload,\n                                    className: \"flex items-center space-x-1 text-sm text-indigo-600 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Download Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiDownload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1362,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1355,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"flex items-center space-x-1 text-sm text-pink-600 dark:text-pink-300 bg-pink-100 dark:bg-pink-900/30 hover:bg-pink-200 dark:hover:bg-pink-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Upload Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUpload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1373,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1366,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \".js,.ts,.py,.java,.c,.cpp,.go,.rb,.rs,.php,.txt\",\n                                    style: {\n                                        display: \"none\"\n                                    },\n                                    onChange: handleUpload\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1375,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    onChange: (e)=>e.target.value && insertSnippet(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[100px]\",\n                                    defaultValue: \"\",\n                                    title: \"Insert Snippet\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Snippets\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1390,\n                                            columnNumber: 15\n                                        }, this),\n                                        (SNIPPETS[language] || SNIPPETS['javascript']).map((snippet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: snippet.value,\n                                                children: snippet.label\n                                            }, snippet.label, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1392,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1384,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowShortcuts(true),\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Keyboard Shortcuts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiHelpCircle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1404,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1397,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleLeaveRoom,\n                                    className: \"flex items-center space-x-1 text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors px-2 py-1 rounded-md ml-2 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Leave Room\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1414,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1408,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1281,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1190,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showUserList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute right-0 top-12 bg-white dark:bg-zinc-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 w-48 overflow-hidden\",\n                        initial: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            height: 'auto'\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 border-b border-gray-200 dark:border-gray-700 font-medium text-sm text-gray-600 dark:text-gray-300\",\n                                children: \"Active Users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1429,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"max-h-48 overflow-y-auto\",\n                                children: activeUsers.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.li, {\n                                        className: \"px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-zinc-700 flex items-center space-x-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full bg-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1441,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: user\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1442,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1434,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1432,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1422,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1420,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"h-[calc(100%-40px)] \".concat(showOutput ? 'pb-[33%]' : ''),\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.4,\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    height: \"100%\",\n                                    defaultLanguage: language,\n                                    defaultValue: code,\n                                    onChange: handleEditorChange,\n                                    onMount: handleEditorDidMount,\n                                    theme: theme === \"dark\" ? \"vs-dark\" : \"light\",\n                                    options: {\n                                        minimap: {\n                                            enabled: minimapEnabled\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1460,\n                                    columnNumber: 15\n                                }, this),\n                                teacherCursorPosition && editorRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TeacherCursor, {\n                                    editor: editorRef.current,\n                                    position: teacherCursorPosition\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1472,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1459,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1457,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1451,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: typingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 left-4 text-sm text-gray-400 bg-gray-800/80 px-3 py-1 rounded-full backdrop-blur-sm\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            typingUser,\n                            \" is typing...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1484,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1482,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gray-900 text-white border-t border-gray-700 h-1/3 overflow-hidden flex flex-col\",\n                        initial: {\n                            height: 0\n                        },\n                        animate: {\n                            height: '33%'\n                        },\n                        exit: {\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 bg-gray-800 border-b border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1507,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearOutput,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1509,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: testPistonAPI,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-blue-400 hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Test API\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1516,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowOutput(false),\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1523,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1508,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1506,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-auto font-mono text-sm whitespace-pre-wrap\",\n                                children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1534,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Executing code...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1535,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1533,\n                                    columnNumber: 19\n                                }, this) : executionError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400\",\n                                    children: executionError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1538,\n                                    columnNumber: 19\n                                }, this) : executionOutput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: executionOutput\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1540,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: 'Click \"Run Code\" to execute your code.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1542,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1531,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1499,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1497,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: copySuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 right-4 text-sm text-white bg-green-500 px-3 py-1 rounded-full shadow-lg\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: \"Copied to clipboard!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1552,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1550,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showShortcuts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 max-w-md w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-700 dark:hover:text-white\",\n                                    onClick: ()=>setShowShortcuts(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1574,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    children: \"Keyboard Shortcuts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1580,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Run Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1582,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+Enter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1582,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Format Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1583,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Shift+Alt+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1583,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Find/Replace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1584,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1584,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Download Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1585,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+D\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1585,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Upload Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1586,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+U\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1586,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Minimap:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1587,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+M\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1587,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Theme:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1588,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+T\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1588,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Show Shortcuts:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1589,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+H\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1589,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1581,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1573,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1567,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1565,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 1188,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 1186,\n        columnNumber: 5\n    }, this);\n} // Keyboard shortcuts global handler\n // useEffect(() => {\n //   const handler = (e: KeyboardEvent) => {\n //     if (e.altKey && e.key.toLowerCase() === \"d\") {\n //       e.preventDefault();\n //       handleDownload();\n //     } else if (e.altKey && e.key.toLowerCase() === \"u\") {\n //       e.preventDefault();\n //       fileInputRef.current?.click();\n //     } else if (e.altKey && e.key.toLowerCase() === \"m\") {\n //       e.preventDefault();\n //       setMinimapEnabled((v: boolean) => !v);\n //     } else if (e.altKey && e.key.toLowerCase() === \"t\") {\n //       e.preventDefault();\n //       setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n //     } else if (e.altKey && e.key.toLowerCase() === \"h\") {\n //       e.preventDefault();\n //       setShowShortcuts(true);\n //     }\n //   };\n //   window.addEventListener(\"keydown\", handler);\n //   return () => window.removeEventListener(\"keydown\", handler);\n // }, [theme, setTheme]);\n_s(CodeEditor, \"1Kkoq6M2vuH+ioEQKVrEVO8mGRo=\", false, function() {\n    return [\n        _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme\n    ];\n});\n_c = CodeEditor;\nvar _c;\n$RefreshReg$(_c, \"CodeEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CodeEditor.tsx\n"));

/***/ })

});