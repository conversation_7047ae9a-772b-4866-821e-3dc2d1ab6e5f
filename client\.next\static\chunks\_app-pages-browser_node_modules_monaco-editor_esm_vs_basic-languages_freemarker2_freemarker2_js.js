"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_freemarker2_freemarker2_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/freemarker2/freemarker2.js":
/*!***************************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/freemarker2/freemarker2.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TagAngleInterpolationBracket: () => (/* binding */ TagAngleInterpolationBracket),\n/* harmony export */   TagAngleInterpolationDollar: () => (/* binding */ TagAngleInterpolationDollar),\n/* harmony export */   TagAutoInterpolationBracket: () => (/* binding */ TagAutoInterpolationBracket),\n/* harmony export */   TagAutoInterpolationDollar: () => (/* binding */ TagAutoInterpolationDollar),\n/* harmony export */   TagBracketInterpolationBracket: () => (/* binding */ TagBracketInterpolationBracket),\n/* harmony export */   TagBracketInterpolationDollar: () => (/* binding */ TagBracketInterpolationDollar)\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.api.js\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/basic-languages/freemarker2/freemarker2.ts\nvar EMPTY_ELEMENTS = [\n  \"assign\",\n  \"flush\",\n  \"ftl\",\n  \"return\",\n  \"global\",\n  \"import\",\n  \"include\",\n  \"break\",\n  \"continue\",\n  \"local\",\n  \"nested\",\n  \"nt\",\n  \"setting\",\n  \"stop\",\n  \"t\",\n  \"lt\",\n  \"rt\",\n  \"fallback\"\n];\nvar BLOCK_ELEMENTS = [\n  \"attempt\",\n  \"autoesc\",\n  \"autoEsc\",\n  \"compress\",\n  \"comment\",\n  \"escape\",\n  \"noescape\",\n  \"function\",\n  \"if\",\n  \"list\",\n  \"items\",\n  \"sep\",\n  \"macro\",\n  \"noparse\",\n  \"noParse\",\n  \"noautoesc\",\n  \"noAutoEsc\",\n  \"outputformat\",\n  \"switch\",\n  \"visit\",\n  \"recurse\"\n];\nvar TagSyntaxAngle = {\n  close: \">\",\n  id: \"angle\",\n  open: \"<\"\n};\nvar TagSyntaxBracket = {\n  close: \"\\\\]\",\n  id: \"bracket\",\n  open: \"\\\\[\"\n};\nvar TagSyntaxAuto = {\n  close: \"[>\\\\]]\",\n  id: \"auto\",\n  open: \"[<\\\\[]\"\n};\nvar InterpolationSyntaxDollar = {\n  close: \"\\\\}\",\n  id: \"dollar\",\n  open1: \"\\\\$\",\n  open2: \"\\\\{\"\n};\nvar InterpolationSyntaxBracket = {\n  close: \"\\\\]\",\n  id: \"bracket\",\n  open1: \"\\\\[\",\n  open2: \"=\"\n};\nfunction createLangConfiguration(ts) {\n  return {\n    brackets: [\n      [\"<\", \">\"],\n      [\"[\", \"]\"],\n      [\"(\", \")\"],\n      [\"{\", \"}\"]\n    ],\n    comments: {\n      blockComment: [`${ts.open}--`, `--${ts.close}`]\n    },\n    autoCloseBefore: \"\\n\\r\t }]),.:;=\",\n    autoClosingPairs: [\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: '\"', close: '\"', notIn: [\"string\"] },\n      { open: \"'\", close: \"'\", notIn: [\"string\"] }\n    ],\n    surroundingPairs: [\n      { open: '\"', close: '\"' },\n      { open: \"'\", close: \"'\" },\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: \"<\", close: \">\" }\n    ],\n    folding: {\n      markers: {\n        start: new RegExp(\n          `${ts.open}#(?:${BLOCK_ELEMENTS.join(\"|\")})([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`\n        ),\n        end: new RegExp(`${ts.open}/#(?:${BLOCK_ELEMENTS.join(\"|\")})[\\\\r\\\\n\\\\t ]*>`)\n      }\n    },\n    onEnterRules: [\n      {\n        beforeText: new RegExp(\n          `${ts.open}#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`\n        ),\n        afterText: new RegExp(`^${ts.open}/#([a-zA-Z_]+)[\\\\r\\\\n\\\\t ]*${ts.close}$`),\n        action: {\n          indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n        }\n      },\n      {\n        beforeText: new RegExp(\n          `${ts.open}#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`\n        ),\n        action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n      }\n    ]\n  };\n}\nfunction createLangConfigurationAuto() {\n  return {\n    // Cannot set block comment delimiter in auto mode...\n    // It depends on the content and the cursor position of the file...\n    brackets: [\n      [\"<\", \">\"],\n      [\"[\", \"]\"],\n      [\"(\", \")\"],\n      [\"{\", \"}\"]\n    ],\n    autoCloseBefore: \"\\n\\r\t }]),.:;=\",\n    autoClosingPairs: [\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: '\"', close: '\"', notIn: [\"string\"] },\n      { open: \"'\", close: \"'\", notIn: [\"string\"] }\n    ],\n    surroundingPairs: [\n      { open: '\"', close: '\"' },\n      { open: \"'\", close: \"'\" },\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: \"<\", close: \">\" }\n    ],\n    folding: {\n      markers: {\n        start: new RegExp(`[<\\\\[]#(?:${BLOCK_ELEMENTS.join(\"|\")})([^/>\\\\]]*(?!/)[>\\\\]])[^<\\\\[]*$`),\n        end: new RegExp(`[<\\\\[]/#(?:${BLOCK_ELEMENTS.join(\"|\")})[\\\\r\\\\n\\\\t ]*>`)\n      }\n    },\n    onEnterRules: [\n      {\n        beforeText: new RegExp(\n          `[<\\\\[]#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/>\\\\]]*(?!/)[>\\\\]])[^[<\\\\[]]*$`\n        ),\n        afterText: new RegExp(`^[<\\\\[]/#([a-zA-Z_]+)[\\\\r\\\\n\\\\t ]*[>\\\\]]$`),\n        action: {\n          indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n        }\n      },\n      {\n        beforeText: new RegExp(\n          `[<\\\\[]#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/>\\\\]]*(?!/)[>\\\\]])[^[<\\\\[]]*$`\n        ),\n        action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n      }\n    ]\n  };\n}\nfunction createMonarchLanguage(ts, is) {\n  const id = `_${ts.id}_${is.id}`;\n  const s = (name) => name.replace(/__id__/g, id);\n  const r = (regexp) => {\n    const source = regexp.source.replace(/__id__/g, id);\n    return new RegExp(source, regexp.flags);\n  };\n  return {\n    // Settings\n    unicode: true,\n    includeLF: false,\n    start: s(\"default__id__\"),\n    ignoreCase: false,\n    defaultToken: \"invalid\",\n    tokenPostfix: `.freemarker2`,\n    brackets: [\n      { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n      { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n      { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n      { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n    ],\n    // Dynamic RegExp\n    [s(\"open__id__\")]: new RegExp(ts.open),\n    [s(\"close__id__\")]: new RegExp(ts.close),\n    [s(\"iOpen1__id__\")]: new RegExp(is.open1),\n    [s(\"iOpen2__id__\")]: new RegExp(is.open2),\n    [s(\"iClose__id__\")]: new RegExp(is.close),\n    // <#START_TAG : \"<\" | \"<#\" | \"[#\">\n    // <#END_TAG : \"</\" | \"</#\" | \"[/#\">\n    [s(\"startTag__id__\")]: r(/(@open__id__)(#)/),\n    [s(\"endTag__id__\")]: r(/(@open__id__)(\\/#)/),\n    [s(\"startOrEndTag__id__\")]: r(/(@open__id__)(\\/?#)/),\n    // <#CLOSE_TAG1 : (<BLANK>)* (\">\" | \"]\")>\n    [s(\"closeTag1__id__\")]: r(/((?:@blank)*)(@close__id__)/),\n    // <#CLOSE_TAG2 : (<BLANK>)* (\"/\")? (\">\" | \"]\")>\n    [s(\"closeTag2__id__\")]: r(/((?:@blank)*\\/?)(@close__id__)/),\n    // Static RegExp\n    // <#BLANK : \" \" | \"\\t\" | \"\\n\" | \"\\r\">\n    blank: /[ \\t\\n\\r]/,\n    // <FALSE : \"false\">\n    // <TRUE : \"true\">\n    // <IN : \"in\">\n    // <AS : \"as\">\n    // <USING : \"using\">\n    keywords: [\"false\", \"true\", \"in\", \"as\", \"using\"],\n    // Directive names that cannot have an expression parameters and cannot be self-closing\n    // E.g. <#if id==2> ... </#if>\n    directiveStartCloseTag1: /attempt|recover|sep|auto[eE]sc|no(?:autoe|AutoE)sc|compress|default|no[eE]scape|comment|no[pP]arse/,\n    // Directive names that cannot have an expression parameter and can be self-closing\n    // E.g. <#if> ... <#else>  ... </#if>\n    // E.g. <#if> ... <#else /></#if>\n    directiveStartCloseTag2: /else|break|continue|return|stop|flush|t|lt|rt|nt|nested|recurse|fallback|ftl/,\n    // Directive names that can have an expression parameter and cannot be self-closing\n    // E.g. <#if id==2> ... </#if>\n    directiveStartBlank: /if|else[iI]f|list|for[eE]ach|switch|case|assign|global|local|include|import|function|macro|transform|visit|stop|return|call|setting|output[fF]ormat|nested|recurse|escape|ftl|items/,\n    // Directive names that can have an end tag\n    // E.g. </#if>\n    directiveEndCloseTag1: /if|list|items|sep|recover|attempt|for[eE]ach|local|global|assign|function|macro|output[fF]ormat|auto[eE]sc|no(?:autoe|AutoE)sc|compress|transform|switch|escape|no[eE]scape/,\n    // <#ESCAPED_CHAR :\n    //     \"\\\\\"\n    //     (\n    //         (\"n\" | \"t\" | \"r\" | \"f\" | \"b\" | \"g\" | \"l\" | \"a\" | \"\\\\\" | \"'\" | \"\\\"\" | \"{\" | \"=\")\n    //         |\n    //         (\"x\" [\"0\"-\"9\", \"A\"-\"F\", \"a\"-\"f\"])\n    //     )\n    // >\n    // Note: While the JavaCC tokenizer rule only specifies one hex digit,\n    // FreeMarker actually interprets up to 4 hex digits.\n    escapedChar: /\\\\(?:[ntrfbgla\\\\'\"\\{=]|(?:x[0-9A-Fa-f]{1,4}))/,\n    // <#ASCII_DIGIT: [\"0\" - \"9\"]>\n    asciiDigit: /[0-9]/,\n    // <INTEGER : ([\"0\"-\"9\"])+>\n    integer: /[0-9]+/,\n    // <#NON_ESCAPED_ID_START_CHAR:\n    // [\n    // \t  // This was generated on JDK 1.8.0_20 Win64 with src/main/misc/identifierChars/IdentifierCharGenerator.java\n    //    ...\n    // ]\n    nonEscapedIdStartChar: /[\\$@-Z_a-z\\u00AA\\u00B5\\u00BA\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u1FFF\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183-\\u2184\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3006\\u3031-\\u3035\\u303B-\\u303C\\u3040-\\u318F\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3300-\\u337F\\u3400-\\u4DB5\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66E\\uA67F-\\uA697\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA793\\uA7A0-\\uA7AA\\uA7F8-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8D0-\\uA8D9\\uA8F2-\\uA8F7\\uA8FB\\uA900-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF-\\uA9D9\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A\\uAA80-\\uAAAF\\uAAB1\\uAAB5-\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uABC0-\\uABE2\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40-\\uFB41\\uFB43-\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]/,\n    // <#ESCAPED_ID_CHAR: \"\\\\\" (\"-\" | \".\" | \":\" | \"#\")>\n    escapedIdChar: /\\\\[\\-\\.:#]/,\n    // <#ID_START_CHAR: <NON_ESCAPED_ID_START_CHAR>|<ESCAPED_ID_CHAR>>\n    idStartChar: /(?:@nonEscapedIdStartChar)|(?:@escapedIdChar)/,\n    // <ID: <ID_START_CHAR> (<ID_START_CHAR>|<ASCII_DIGIT>)*>\n    id: /(?:@idStartChar)(?:(?:@idStartChar)|(?:@asciiDigit))*/,\n    // Certain keywords / operators are allowed to index hashes\n    //\n    // Expression DotVariable(Expression exp) :\n    // {\n    // \tToken t;\n    // }\n    // {\n    // \t\t<DOT>\n    // \t\t(\n    // \t\t\tt = <ID> | t = <TIMES> | t = <DOUBLE_STAR>\n    // \t\t\t|\n    // \t\t\t(\n    // \t\t\t\tt = <LESS_THAN>\n    // \t\t\t\t|\n    // \t\t\t\tt = <LESS_THAN_EQUALS>\n    // \t\t\t\t|\n    // \t\t\t\tt = <ESCAPED_GT>\n    // \t\t\t\t|\n    // \t\t\t\tt = <ESCAPED_GTE>\n    // \t\t\t\t|\n    // \t\t\t\tt = <FALSE>\n    // \t\t\t\t|\n    // \t\t\t\tt = <TRUE>\n    // \t\t\t\t|\n    // \t\t\t\tt = <IN>\n    // \t\t\t\t|\n    // \t\t\t\tt = <AS>\n    // \t\t\t\t|\n    // \t\t\t\tt = <USING>\n    // \t\t\t)\n    // \t\t\t{\n    // \t\t\t\tif (!Character.isLetter(t.image.charAt(0))) {\n    // \t\t\t\t\tthrow new ParseException(t.image + \" is not a valid identifier.\", template, t);\n    // \t\t\t\t}\n    // \t\t\t}\n    // \t\t)\n    // \t\t{\n    // \t\t\tnotListLiteral(exp, \"hash\");\n    // \t\t\tnotStringLiteral(exp, \"hash\");\n    // \t\t\tnotBooleanLiteral(exp, \"hash\");\n    // \t\t\tDot dot = new Dot(exp, t.image);\n    // \t\t\tdot.setLocation(template, exp, t);\n    // \t\t\treturn dot;\n    // \t\t}\n    // }\n    specialHashKeys: /\\*\\*|\\*|false|true|in|as|using/,\n    // <DOUBLE_EQUALS : \"==\">\n    // <EQUALS : \"=\">\n    // <NOT_EQUALS : \"!=\">\n    // <PLUS_EQUALS : \"+=\">\n    // <MINUS_EQUALS : \"-=\">\n    // <TIMES_EQUALS : \"*=\">\n    // <DIV_EQUALS : \"/=\">\n    // <MOD_EQUALS : \"%=\">\n    // <PLUS_PLUS : \"++\">\n    // <MINUS_MINUS : \"--\">\n    // <LESS_THAN_EQUALS : \"lte\" | \"\\\\lte\" | \"<=\" | \"&lt;=\">\n    // <LESS_THAN : \"lt\" | \"\\\\lt\" | \"<\" | \"&lt;\">\n    // <ESCAPED_GTE : \"gte\" | \"\\\\gte\" | \"&gt;=\">\n    // <ESCAPED_GT: \"gt\" | \"\\\\gt\" |  \"&gt;\">\n    // <DOUBLE_STAR : \"**\">\n    // <PLUS : \"+\">\n    // <MINUS : \"-\">\n    // <TIMES : \"*\">\n    // <PERCENT : \"%\">\n    // <AND : \"&\" | \"&&\" | \"&amp;&amp;\" | \"\\\\and\" >\n    // <OR : \"|\" | \"||\">\n    // <EXCLAM : \"!\">\n    // <COMMA : \",\">\n    // <SEMICOLON : \";\">\n    // <COLON : \":\">\n    // <ELLIPSIS : \"...\">\n    // <DOT_DOT_ASTERISK : \"..*\" >\n    // <DOT_DOT_LESS : \"..<\" | \"..!\" >\n    // <DOT_DOT : \"..\">\n    // <EXISTS : \"??\">\n    // <BUILT_IN : \"?\">\n    // <LAMBDA_ARROW : \"->\" | \"-&gt;\">\n    namedSymbols: /&lt;=|&gt;=|\\\\lte|\\\\lt|&lt;|\\\\gte|\\\\gt|&gt;|&amp;&amp;|\\\\and|-&gt;|->|==|!=|\\+=|-=|\\*=|\\/=|%=|\\+\\+|--|<=|&&|\\|\\||:|\\.\\.\\.|\\.\\.\\*|\\.\\.<|\\.\\.!|\\?\\?|=|<|\\+|-|\\*|\\/|%|\\||\\.\\.|\\?|!|&|\\.|,|;/,\n    arrows: [\"->\", \"-&gt;\"],\n    delimiters: [\";\", \":\", \",\", \".\"],\n    stringOperators: [\"lte\", \"lt\", \"gte\", \"gt\"],\n    noParseTags: [\"noparse\", \"noParse\", \"comment\"],\n    tokenizer: {\n      // Parser states\n      // Plain text\n      [s(\"default__id__\")]: [\n        { include: s(\"@directive_token__id__\") },\n        { include: s(\"@interpolation_and_text_token__id__\") }\n      ],\n      // A FreeMarker expression inside a directive, e.g. <#if 2<3>\n      [s(\"fmExpression__id__.directive\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      // A FreeMarker expression inside an interpolation, e.g. ${2+3}\n      [s(\"fmExpression__id__.interpolation\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@expression_token__id__\") },\n        { include: s(\"@greater_operators_token__id__\") }\n      ],\n      // In an expression and inside a not-yet closed parenthesis / bracket\n      [s(\"inParen__id__.plain\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      [s(\"inParen__id__.gt\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@expression_token__id__\") },\n        { include: s(\"@greater_operators_token__id__\") }\n      ],\n      // Expression for the unified call, e.g. <@createMacro() ... >\n      [s(\"noSpaceExpression__id__\")]: [\n        { include: s(\"@no_space_expression_end_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      // For the function of a unified call. Special case for when the\n      // expression is a simple identifier.\n      // <@join [1,2] \",\">\n      // <@null!join [1,2] \",\">\n      [s(\"unifiedCall__id__\")]: [{ include: s(\"@unified_call_token__id__\") }],\n      // For singly and doubly quoted string (that may contain interpolations)\n      [s(\"singleString__id__\")]: [{ include: s(\"@string_single_token__id__\") }],\n      [s(\"doubleString__id__\")]: [{ include: s(\"@string_double_token__id__\") }],\n      // For singly and doubly quoted string (that may not contain interpolations)\n      [s(\"rawSingleString__id__\")]: [{ include: s(\"@string_single_raw_token__id__\") }],\n      [s(\"rawDoubleString__id__\")]: [{ include: s(\"@string_double_raw_token__id__\") }],\n      // For a comment in an expression\n      // ${ 1 + <#-- comment --> 2}\n      [s(\"expressionComment__id__\")]: [{ include: s(\"@expression_comment_token__id__\") }],\n      // For <#noparse> ... </#noparse>\n      // For <#noParse> ... </#noParse>\n      // For <#comment> ... </#comment>\n      [s(\"noParse__id__\")]: [{ include: s(\"@no_parse_token__id__\") }],\n      // For <#-- ... -->\n      [s(\"terseComment__id__\")]: [{ include: s(\"@terse_comment_token__id__\") }],\n      // Common rules\n      [s(\"directive_token__id__\")]: [\n        // <ATTEMPT : <START_TAG> \"attempt\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <RECOVER : <START_TAG> \"recover\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SEP : <START_TAG> \"sep\" <CLOSE_TAG1>>\n        // <AUTOESC : <START_TAG> \"auto\" (\"e\"|\"E\") \"sc\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 4), DEFAULT);\n        // }\n        // <NOAUTOESC : <START_TAG> \"no\" (\"autoe\"|\"AutoE\") \"sc\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        // <COMPRESS : <START_TAG> \"compress\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <DEFAUL : <START_TAG> \"default\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <NOESCAPE : <START_TAG> \"no\" (\"e\" | \"E\") \"scape\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        //\n        // <COMMENT : <START_TAG> \"comment\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, NO_PARSE); noparseTag = \"comment\";\n        // }\n        // <NOPARSE: <START_TAG> \"no\" (\"p\" | \"P\") \"arse\" <CLOSE_TAG1>> {\n        //     int tagNamingConvention = getTagNamingConvention(matchedToken, 2);\n        //     handleTagSyntaxAndSwitch(matchedToken, tagNamingConvention, NO_PARSE);\n        //     noparseTag = tagNamingConvention == Configuration.CAMEL_CASE_NAMING_CONVENTION ? \"noParse\" : \"noparse\";\n        // }\n        [\n          r(/(?:@startTag__id__)(@directiveStartCloseTag1)(?:@closeTag1__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            {\n              cases: {\n                \"@noParseTags\": { token: \"tag\", next: s(\"@noParse__id__.$3\") },\n                \"@default\": { token: \"tag\" }\n              }\n            },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <ELSE : <START_TAG> \"else\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <BREAK : <START_TAG> \"break\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <CONTINUE : <START_TAG> \"continue\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SIMPLE_RETURN : <START_TAG> \"return\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <HALT : <START_TAG> \"stop\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <FLUSH : <START_TAG> \"flush\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <TRIM : <START_TAG> \"t\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <LTRIM : <START_TAG> \"lt\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <RTRIM : <START_TAG> \"rt\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <NOTRIM : <START_TAG> \"nt\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SIMPLE_NESTED : <START_TAG> \"nested\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SIMPLE_RECURSE : <START_TAG> \"recurse\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <FALLBACK : <START_TAG> \"fallback\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <TRIVIAL_FTL_HEADER : (\"<#ftl\" | \"[#ftl\") (\"/\")? (\">\" | \"]\")> { ftlHeader(matchedToken); }\n        [\n          r(/(?:@startTag__id__)(@directiveStartCloseTag2)(?:@closeTag2__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <IF : <START_TAG> \"if\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <ELSE_IF : <START_TAG> \"else\" (\"i\" | \"I\") \"f\" <BLANK>> {\n        // \thandleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 4), FM_EXPRESSION);\n        // }\n        // <LIST : <START_TAG> \"list\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <FOREACH : <START_TAG> \"for\" (\"e\" | \"E\") \"ach\" <BLANK>> {\n        //    handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 3), FM_EXPRESSION);\n        // }\n        // <SWITCH : <START_TAG> \"switch\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <CASE : <START_TAG> \"case\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <ASSIGN : <START_TAG> \"assign\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <GLOBALASSIGN : <START_TAG> \"global\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <LOCALASSIGN : <START_TAG> \"local\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <_INCLUDE : <START_TAG> \"include\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <IMPORT : <START_TAG> \"import\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <FUNCTION : <START_TAG> \"function\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <MACRO : <START_TAG> \"macro\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <TRANSFORM : <START_TAG> \"transform\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <VISIT : <START_TAG> \"visit\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <STOP : <START_TAG> \"stop\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <RETURN : <START_TAG> \"return\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <CALL : <START_TAG> \"call\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <SETTING : <START_TAG> \"setting\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <OUTPUTFORMAT : <START_TAG> \"output\" (\"f\"|\"F\") \"ormat\" <BLANK>> {\n        //    handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 6), FM_EXPRESSION);\n        // }\n        // <NESTED : <START_TAG> \"nested\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <RECURSE : <START_TAG> \"recurse\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <ESCAPE : <START_TAG> \"escape\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        //\n        // Note: FreeMarker grammar appears to treat the FTL header as a special case,\n        // in order to remove new lines after the header (?), but since we only need\n        // to tokenize for highlighting, we can include this directive here.\n        // <FTL_HEADER : (\"<#ftl\" | \"[#ftl\") <BLANK>> { ftlHeader(matchedToken); }\n        //\n        // Note: FreeMarker grammar appears to treat the items directive as a special case for\n        // the AST parsing process, but since we only need to tokenize, we can include this\n        // directive here.\n        // <ITEMS : <START_TAG> \"items\" (<BLANK>)+ <AS> <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        [\n          r(/(?:@startTag__id__)(@directiveStartBlank)(@blank)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"\", next: s(\"@fmExpression__id__.directive\") }\n          ]\n        ],\n        // <END_IF : <END_TAG> \"if\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_LIST : <END_TAG> \"list\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_SEP : <END_TAG> \"sep\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_RECOVER : <END_TAG> \"recover\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_ATTEMPT : <END_TAG> \"attempt\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_FOREACH : <END_TAG> \"for\" (\"e\" | \"E\") \"ach\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 3), DEFAULT);\n        // }\n        // <END_LOCAL : <END_TAG> \"local\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_GLOBAL : <END_TAG> \"global\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_ASSIGN : <END_TAG> \"assign\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_FUNCTION : <END_TAG> \"function\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_MACRO : <END_TAG> \"macro\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_OUTPUTFORMAT : <END_TAG> \"output\" (\"f\" | \"F\") \"ormat\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 6), DEFAULT);\n        // }\n        // <END_AUTOESC : <END_TAG> \"auto\" (\"e\" | \"E\") \"sc\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 4), DEFAULT);\n        // }\n        // <END_NOAUTOESC : <END_TAG> \"no\" (\"autoe\"|\"AutoE\") \"sc\" <CLOSE_TAG1>> {\n        //   handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        // <END_COMPRESS : <END_TAG> \"compress\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_TRANSFORM : <END_TAG> \"transform\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_SWITCH : <END_TAG> \"switch\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_ESCAPE : <END_TAG> \"escape\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_NOESCAPE : <END_TAG> \"no\" (\"e\" | \"E\") \"scape\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        [\n          r(/(?:@endTag__id__)(@directiveEndCloseTag1)(?:@closeTag1__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <UNIFIED_CALL : \"<@\" | \"[@\" > { unifiedCall(matchedToken); }\n        [\n          r(/(@open__id__)(@)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\", next: s(\"@unifiedCall__id__\") }\n          ]\n        ],\n        // <UNIFIED_CALL_END : (\"<\" | \"[\") \"/@\" ((<ID>) (\".\"<ID>)*)? <CLOSE_TAG1>> { unifiedCallEnd(matchedToken); }\n        [\n          r(/(@open__id__)(\\/@)((?:(?:@id)(?:\\.(?:@id))*)?)(?:@closeTag1__id__)/),\n          [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <TERSE_COMMENT : (\"<\" | \"[\") \"#--\" > { noparseTag = \"-->\"; handleTagSyntaxAndSwitch(matchedToken, NO_PARSE); }\n        [\n          r(/(@open__id__)#--/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : { token: \"comment\", next: s(\"@terseComment__id__\") }\n        ],\n        // <UNKNOWN_DIRECTIVE : (\"[#\" | \"[/#\" | \"<#\" | \"</#\") ([\"a\"-\"z\", \"A\"-\"Z\", \"_\"])+>\n        [\n          r(/(?:@startOrEndTag__id__)([a-zA-Z_]+)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag.invalid\", next: s(\"@fmExpression__id__.directive\") }\n          ]\n        ]\n      ],\n      // <DEFAULT, NO_DIRECTIVE> TOKEN :\n      [s(\"interpolation_and_text_token__id__\")]: [\n        // <DOLLAR_INTERPOLATION_OPENING : \"${\"> { startInterpolation(matchedToken); }\n        // <SQUARE_BRACKET_INTERPOLATION_OPENING : \"[=\"> { startInterpolation(matchedToken); }\n        [\n          r(/(@iOpen1__id__)(@iOpen2__id__)/),\n          [\n            { token: is.id === \"bracket\" ? \"@brackets.interpolation\" : \"delimiter.interpolation\" },\n            {\n              token: is.id === \"bracket\" ? \"delimiter.interpolation\" : \"@brackets.interpolation\",\n              next: s(\"@fmExpression__id__.interpolation\")\n            }\n          ]\n        ],\n        // <STATIC_TEXT_FALSE_ALARM : \"$\" | \"#\" | \"<\" | \"[\" | \"{\"> // to handle a lone dollar sign or \"<\" or \"# or <@ with whitespace after\"\n        // <STATIC_TEXT_WS : (\"\\n\" | \"\\r\" | \"\\t\" | \" \")+>\n        // <STATIC_TEXT_NON_WS : (~[\"$\", \"<\", \"#\", \"[\", \"{\", \"\\n\", \"\\r\", \"\\t\", \" \"])+>\n        [/[\\$#<\\[\\{]|(?:@blank)+|[^\\$<#\\[\\{\\n\\r\\t ]+/, { token: \"source\" }]\n      ],\n      // <STRING_LITERAL :\n      // \t(\n      // \t\t\"\\\"\"\n      // \t\t((~[\"\\\"\", \"\\\\\"]) | <ESCAPED_CHAR>)*\n      // \t\t\"\\\"\"\n      // \t)\n      // \t|\n      // \t(\n      // \t\t\"'\"\n      // \t\t((~[\"'\", \"\\\\\"]) | <ESCAPED_CHAR>)*\n      // \t\t\"'\"\n      // \t)\n      // >\n      [s(\"string_single_token__id__\")]: [\n        [/[^'\\\\]/, { token: \"string\" }],\n        [/@escapedChar/, { token: \"string.escape\" }],\n        [/'/, { token: \"string\", next: \"@pop\" }]\n      ],\n      [s(\"string_double_token__id__\")]: [\n        [/[^\"\\\\]/, { token: \"string\" }],\n        [/@escapedChar/, { token: \"string.escape\" }],\n        [/\"/, { token: \"string\", next: \"@pop\" }]\n      ],\n      // <RAW_STRING : \"r\" ((\"\\\"\" (~[\"\\\"\"])* \"\\\"\") | (\"'\" (~[\"'\"])* \"'\"))>\n      [s(\"string_single_raw_token__id__\")]: [\n        [/[^']+/, { token: \"string.raw\" }],\n        [/'/, { token: \"string.raw\", next: \"@pop\" }]\n      ],\n      [s(\"string_double_raw_token__id__\")]: [\n        [/[^\"]+/, { token: \"string.raw\" }],\n        [/\"/, { token: \"string.raw\", next: \"@pop\" }]\n      ],\n      // <FM_EXPRESSION, IN_PAREN, NO_SPACE_EXPRESSION, NAMED_PARAMETER_EXPRESSION> TOKEN :\n      [s(\"expression_token__id__\")]: [\n        // Strings\n        [\n          /(r?)(['\"])/,\n          {\n            cases: {\n              \"r'\": [\n                { token: \"keyword\" },\n                { token: \"string.raw\", next: s(\"@rawSingleString__id__\") }\n              ],\n              'r\"': [\n                { token: \"keyword\" },\n                { token: \"string.raw\", next: s(\"@rawDoubleString__id__\") }\n              ],\n              \"'\": [{ token: \"source\" }, { token: \"string\", next: s(\"@singleString__id__\") }],\n              '\"': [{ token: \"source\" }, { token: \"string\", next: s(\"@doubleString__id__\") }]\n            }\n          }\n        ],\n        // Numbers\n        // <INTEGER : ([\"0\"-\"9\"])+>\n        // <DECIMAL : <INTEGER> \".\" <INTEGER>>\n        [\n          /(?:@integer)(?:\\.(?:@integer))?/,\n          {\n            cases: {\n              \"(?:@integer)\": { token: \"number\" },\n              \"@default\": { token: \"number.float\" }\n            }\n          }\n        ],\n        // Special hash keys that must not be treated as identifiers\n        // after a period, e.g. a.** is accessing the key \"**\" of a\n        [\n          /(\\.)(@blank*)(@specialHashKeys)/,\n          [{ token: \"delimiter\" }, { token: \"\" }, { token: \"identifier\" }]\n        ],\n        // Symbols / operators\n        [\n          /(?:@namedSymbols)/,\n          {\n            cases: {\n              \"@arrows\": { token: \"meta.arrow\" },\n              \"@delimiters\": { token: \"delimiter\" },\n              \"@default\": { token: \"operators\" }\n            }\n          }\n        ],\n        // Identifiers\n        [\n          /@id/,\n          {\n            cases: {\n              \"@keywords\": { token: \"keyword.$0\" },\n              \"@stringOperators\": { token: \"operators\" },\n              \"@default\": { token: \"identifier\" }\n            }\n          }\n        ],\n        // <OPEN_BRACKET : \"[\">\n        // <CLOSE_BRACKET : \"]\">\n        // <OPEN_PAREN : \"(\">\n        // <CLOSE_PAREN : \")\">\n        // <OPENING_CURLY_BRACKET : \"{\">\n        // <CLOSING_CURLY_BRACKET : \"}\">\n        [\n          /[\\[\\]\\(\\)\\{\\}]/,\n          {\n            cases: {\n              \"\\\\[\": {\n                cases: {\n                  \"$S2==gt\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n                  \"@default\": { token: \"@brackets\", next: s(\"@inParen__id__.plain\") }\n                }\n              },\n              \"\\\\]\": {\n                cases: {\n                  ...is.id === \"bracket\" ? {\n                    \"$S2==interpolation\": { token: \"@brackets.interpolation\", next: \"@popall\" }\n                  } : {},\n                  // This cannot happen while in auto mode, since this applies only to an\n                  // fmExpression inside a directive. But once we encounter the start of a\n                  // directive, we can establish the tag syntax mode.\n                  ...ts.id === \"bracket\" ? {\n                    \"$S2==directive\": { token: \"@brackets.directive\", next: \"@popall\" }\n                  } : {},\n                  // Ignore mismatched paren\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              },\n              \"\\\\(\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n              \"\\\\)\": {\n                cases: {\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              },\n              \"\\\\{\": {\n                cases: {\n                  \"$S2==gt\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n                  \"@default\": { token: \"@brackets\", next: s(\"@inParen__id__.plain\") }\n                }\n              },\n              \"\\\\}\": {\n                cases: {\n                  ...is.id === \"bracket\" ? {} : {\n                    \"$S2==interpolation\": { token: \"@brackets.interpolation\", next: \"@popall\" }\n                  },\n                  // Ignore mismatched paren\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              }\n            }\n          }\n        ],\n        // <OPEN_MISPLACED_INTERPOLATION : \"${\" | \"#{\" | \"[=\">\n        [/\\$\\{/, { token: \"delimiter.invalid\" }]\n      ],\n      // <FM_EXPRESSION, IN_PAREN, NAMED_PARAMETER_EXPRESSION> SKIP :\n      [s(\"blank_and_expression_comment_token__id__\")]: [\n        // < ( \" \" | \"\\t\" | \"\\n\" | \"\\r\" )+ >\n        [/(?:@blank)+/, { token: \"\" }],\n        // < (\"<\" | \"[\") (\"#\" | \"!\") \"--\"> : EXPRESSION_COMMENT\n        [/[<\\[][#!]--/, { token: \"comment\", next: s(\"@expressionComment__id__\") }]\n      ],\n      // <FM_EXPRESSION, NO_SPACE_EXPRESSION, NAMED_PARAMETER_EXPRESSION> TOKEN :\n      [s(\"directive_end_token__id__\")]: [\n        // <DIRECTIVE_END : \">\">\n        // {\n        //     if (inFTLHeader) {\n        //         eatNewline();\n        //         inFTLHeader = false;\n        //     }\n        //     if (squBracTagSyntax || postInterpolationLexState != -1 /* We are in an interpolation */) {\n        //         matchedToken.kind = NATURAL_GT;\n        //     } else {\n        //         SwitchTo(DEFAULT);\n        //     }\n        // }\n        // This cannot happen while in auto mode, since this applies only to an\n        // fmExpression inside a directive. But once we encounter the start of a\n        // directive, we can establish the tag syntax mode.\n        [\n          />/,\n          ts.id === \"bracket\" ? { token: \"operators\" } : { token: \"@brackets.directive\", next: \"@popall\" }\n        ],\n        // <EMPTY_DIRECTIVE_END : \"/>\" | \"/]\">\n        // It is a syntax error to end a tag with the wrong close token\n        // Let's indicate that to the user by not closing the tag\n        [\n          r(/(\\/)(@close__id__)/),\n          [{ token: \"delimiter.directive\" }, { token: \"@brackets.directive\", next: \"@popall\" }]\n        ]\n      ],\n      // <IN_PAREN> TOKEN :\n      [s(\"greater_operators_token__id__\")]: [\n        // <NATURAL_GT : \">\">\n        [/>/, { token: \"operators\" }],\n        // <NATURAL_GTE : \">=\">\n        [/>=/, { token: \"operators\" }]\n      ],\n      // <NO_SPACE_EXPRESSION> TOKEN :\n      [s(\"no_space_expression_end_token__id__\")]: [\n        // <TERMINATING_WHITESPACE :  ([\"\\n\", \"\\r\", \"\\t\", \" \"])+> : FM_EXPRESSION\n        [/(?:@blank)+/, { token: \"\", switchTo: s(\"@fmExpression__id__.directive\") }]\n      ],\n      [s(\"unified_call_token__id__\")]: [\n        // Special case for a call where the expression is just an ID\n        // <UNIFIED_CALL> <ID> <BLANK>+\n        [\n          /(@id)((?:@blank)+)/,\n          [{ token: \"tag\" }, { token: \"\", next: s(\"@fmExpression__id__.directive\") }]\n        ],\n        [\n          r(/(@id)(\\/?)(@close__id__)/),\n          [\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\", next: \"@popall\" }\n          ]\n        ],\n        [/./, { token: \"@rematch\", next: s(\"@noSpaceExpression__id__\") }]\n      ],\n      // <NO_PARSE> TOKEN :\n      [s(\"no_parse_token__id__\")]: [\n        // <MAYBE_END :\n        // \t (\"<\" | \"[\")\n        // \t \"/\"\n        // \t (\"#\")?\n        // \t ([\"a\"-\"z\", \"A\"-\"Z\"])+\n        // \t ( \" \" | \"\\t\" | \"\\n\" | \"\\r\" )*\n        // \t (\">\" | \"]\")\n        // >\n        [\n          r(/(@open__id__)(\\/#?)([a-zA-Z]+)((?:@blank)*)(@close__id__)/),\n          {\n            cases: {\n              \"$S2==$3\": [\n                { token: \"@brackets.directive\" },\n                { token: \"delimiter.directive\" },\n                { token: \"tag\" },\n                { token: \"\" },\n                { token: \"@brackets.directive\", next: \"@popall\" }\n              ],\n              \"$S2==comment\": [\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" }\n              ],\n              \"@default\": [\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" }\n              ]\n            }\n          }\n        ],\n        // <KEEP_GOING : (~[\"<\", \"[\", \"-\"])+>\n        // <LONE_LESS_THAN_OR_DASH : [\"<\", \"[\", \"-\"]>\n        [\n          /[^<\\[\\-]+|[<\\[\\-]/,\n          {\n            cases: {\n              \"$S2==comment\": { token: \"comment\" },\n              \"@default\": { token: \"source\" }\n            }\n          }\n        ]\n      ],\n      // <EXPRESSION_COMMENT> SKIP:\n      [s(\"expression_comment_token__id__\")]: [\n        // < \"-->\" | \"--]\">\n        [\n          /--[>\\]]/,\n          {\n            token: \"comment\",\n            next: \"@pop\"\n          }\n        ],\n        // < (~[\"-\", \">\", \"]\"])+ >\n        // < \">\">\n        // < \"]\">\n        // < \"-\">\n        [/[^\\->\\]]+|[>\\]\\-]/, { token: \"comment\" }]\n      ],\n      [s(\"terse_comment_token__id__\")]: [\n        //  <TERSE_COMMENT_END : \"-->\" | \"--]\">\n        [r(/--(?:@close__id__)/), { token: \"comment\", next: \"@popall\" }],\n        // <KEEP_GOING : (~[\"<\", \"[\", \"-\"])+>\n        // <LONE_LESS_THAN_OR_DASH : [\"<\", \"[\", \"-\"]>\n        [/[^<\\[\\-]+|[<\\[\\-]/, { token: \"comment\" }]\n      ]\n    }\n  };\n}\nfunction createMonarchLanguageAuto(is) {\n  const angle = createMonarchLanguage(TagSyntaxAngle, is);\n  const bracket = createMonarchLanguage(TagSyntaxBracket, is);\n  const auto = createMonarchLanguage(TagSyntaxAuto, is);\n  return {\n    // Angle and bracket syntax mode\n    // We switch to one of these once we have determined the mode\n    ...angle,\n    ...bracket,\n    ...auto,\n    // Settings\n    unicode: true,\n    includeLF: false,\n    start: `default_auto_${is.id}`,\n    ignoreCase: false,\n    defaultToken: \"invalid\",\n    tokenPostfix: `.freemarker2`,\n    brackets: [\n      { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n      { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n      { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n      { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n    ],\n    tokenizer: {\n      ...angle.tokenizer,\n      ...bracket.tokenizer,\n      ...auto.tokenizer\n    }\n  };\n}\nvar TagAngleInterpolationDollar = {\n  conf: createLangConfiguration(TagSyntaxAngle),\n  language: createMonarchLanguage(TagSyntaxAngle, InterpolationSyntaxDollar)\n};\nvar TagBracketInterpolationDollar = {\n  conf: createLangConfiguration(TagSyntaxBracket),\n  language: createMonarchLanguage(TagSyntaxBracket, InterpolationSyntaxDollar)\n};\nvar TagAngleInterpolationBracket = {\n  conf: createLangConfiguration(TagSyntaxAngle),\n  language: createMonarchLanguage(TagSyntaxAngle, InterpolationSyntaxBracket)\n};\nvar TagBracketInterpolationBracket = {\n  conf: createLangConfiguration(TagSyntaxBracket),\n  language: createMonarchLanguage(TagSyntaxBracket, InterpolationSyntaxBracket)\n};\nvar TagAutoInterpolationDollar = {\n  conf: createLangConfigurationAuto(),\n  language: createMonarchLanguageAuto(InterpolationSyntaxDollar)\n};\nvar TagAutoInterpolationBracket = {\n  conf: createLangConfigurationAuto(),\n  language: createMonarchLanguageAuto(InterpolationSyntaxBracket)\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/freemarker2/freemarker2.js\n"));

/***/ })

}]);