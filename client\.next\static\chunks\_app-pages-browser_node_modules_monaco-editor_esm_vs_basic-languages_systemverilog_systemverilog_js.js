"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_systemverilog_systemverilog_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.js":
/*!*******************************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/systemverilog/systemverilog.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"begin\", \"end\"],\n    [\"case\", \"endcase\"],\n    [\"casex\", \"endcase\"],\n    [\"casez\", \"endcase\"],\n    [\"checker\", \"endchecker\"],\n    [\"class\", \"endclass\"],\n    [\"clocking\", \"endclocking\"],\n    [\"config\", \"endconfig\"],\n    [\"function\", \"endfunction\"],\n    [\"generate\", \"endgenerate\"],\n    [\"group\", \"endgroup\"],\n    [\"interface\", \"endinterface\"],\n    [\"module\", \"endmodule\"],\n    [\"package\", \"endpackage\"],\n    [\"primitive\", \"endprimitive\"],\n    [\"program\", \"endprogram\"],\n    [\"property\", \"endproperty\"],\n    [\"specify\", \"endspecify\"],\n    [\"sequence\", \"endsequence\"],\n    [\"table\", \"endtable\"],\n    [\"task\", \"endtask\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    offSide: false,\n    markers: {\n      start: new RegExp(\n        \"^(?:\\\\s*|.*(?!\\\\/[\\\\/\\\\*])[^\\\\w])(?:begin|case(x|z)?|class|clocking|config|covergroup|function|generate|interface|module|package|primitive|property|program|sequence|specify|table|task)\\\\b\"\n      ),\n      end: new RegExp(\n        \"^(?:\\\\s*|.*(?!\\\\/[\\\\/\\\\*])[^\\\\w])(?:end|endcase|endclass|endclocking|endconfig|endgroup|endfunction|endgenerate|endinterface|endmodule|endpackage|endprimitive|endproperty|endprogram|endsequence|endspecify|endtable|endtask)\\\\b\"\n      )\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sv\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.angle\", open: \"<\", close: \">\" }\n  ],\n  keywords: [\n    \"accept_on\",\n    \"alias\",\n    \"always\",\n    \"always_comb\",\n    \"always_ff\",\n    \"always_latch\",\n    \"and\",\n    \"assert\",\n    \"assign\",\n    \"assume\",\n    \"automatic\",\n    \"before\",\n    \"begin\",\n    \"bind\",\n    \"bins\",\n    \"binsof\",\n    \"bit\",\n    \"break\",\n    \"buf\",\n    \"bufif0\",\n    \"bufif1\",\n    \"byte\",\n    \"case\",\n    \"casex\",\n    \"casez\",\n    \"cell\",\n    \"chandle\",\n    \"checker\",\n    \"class\",\n    \"clocking\",\n    \"cmos\",\n    \"config\",\n    \"const\",\n    \"constraint\",\n    \"context\",\n    \"continue\",\n    \"cover\",\n    \"covergroup\",\n    \"coverpoint\",\n    \"cross\",\n    \"deassign\",\n    \"default\",\n    \"defparam\",\n    \"design\",\n    \"disable\",\n    \"dist\",\n    \"do\",\n    \"edge\",\n    \"else\",\n    \"end\",\n    \"endcase\",\n    \"endchecker\",\n    \"endclass\",\n    \"endclocking\",\n    \"endconfig\",\n    \"endfunction\",\n    \"endgenerate\",\n    \"endgroup\",\n    \"endinterface\",\n    \"endmodule\",\n    \"endpackage\",\n    \"endprimitive\",\n    \"endprogram\",\n    \"endproperty\",\n    \"endspecify\",\n    \"endsequence\",\n    \"endtable\",\n    \"endtask\",\n    \"enum\",\n    \"event\",\n    \"eventually\",\n    \"expect\",\n    \"export\",\n    \"extends\",\n    \"extern\",\n    \"final\",\n    \"first_match\",\n    \"for\",\n    \"force\",\n    \"foreach\",\n    \"forever\",\n    \"fork\",\n    \"forkjoin\",\n    \"function\",\n    \"generate\",\n    \"genvar\",\n    \"global\",\n    \"highz0\",\n    \"highz1\",\n    \"if\",\n    \"iff\",\n    \"ifnone\",\n    \"ignore_bins\",\n    \"illegal_bins\",\n    \"implements\",\n    \"implies\",\n    \"import\",\n    \"incdir\",\n    \"include\",\n    \"initial\",\n    \"inout\",\n    \"input\",\n    \"inside\",\n    \"instance\",\n    \"int\",\n    \"integer\",\n    \"interconnect\",\n    \"interface\",\n    \"intersect\",\n    \"join\",\n    \"join_any\",\n    \"join_none\",\n    \"large\",\n    \"let\",\n    \"liblist\",\n    \"library\",\n    \"local\",\n    \"localparam\",\n    \"logic\",\n    \"longint\",\n    \"macromodule\",\n    \"matches\",\n    \"medium\",\n    \"modport\",\n    \"module\",\n    \"nand\",\n    \"negedge\",\n    \"nettype\",\n    \"new\",\n    \"nexttime\",\n    \"nmos\",\n    \"nor\",\n    \"noshowcancelled\",\n    \"not\",\n    \"notif0\",\n    \"notif1\",\n    \"null\",\n    \"or\",\n    \"output\",\n    \"package\",\n    \"packed\",\n    \"parameter\",\n    \"pmos\",\n    \"posedge\",\n    \"primitive\",\n    \"priority\",\n    \"program\",\n    \"property\",\n    \"protected\",\n    \"pull0\",\n    \"pull1\",\n    \"pulldown\",\n    \"pullup\",\n    \"pulsestyle_ondetect\",\n    \"pulsestyle_onevent\",\n    \"pure\",\n    \"rand\",\n    \"randc\",\n    \"randcase\",\n    \"randsequence\",\n    \"rcmos\",\n    \"real\",\n    \"realtime\",\n    \"ref\",\n    \"reg\",\n    \"reject_on\",\n    \"release\",\n    \"repeat\",\n    \"restrict\",\n    \"return\",\n    \"rnmos\",\n    \"rpmos\",\n    \"rtran\",\n    \"rtranif0\",\n    \"rtranif1\",\n    \"s_always\",\n    \"s_eventually\",\n    \"s_nexttime\",\n    \"s_until\",\n    \"s_until_with\",\n    \"scalared\",\n    \"sequence\",\n    \"shortint\",\n    \"shortreal\",\n    \"showcancelled\",\n    \"signed\",\n    \"small\",\n    \"soft\",\n    \"solve\",\n    \"specify\",\n    \"specparam\",\n    \"static\",\n    \"string\",\n    \"strong\",\n    \"strong0\",\n    \"strong1\",\n    \"struct\",\n    \"super\",\n    \"supply0\",\n    \"supply1\",\n    \"sync_accept_on\",\n    \"sync_reject_on\",\n    \"table\",\n    \"tagged\",\n    \"task\",\n    \"this\",\n    \"throughout\",\n    \"time\",\n    \"timeprecision\",\n    \"timeunit\",\n    \"tran\",\n    \"tranif0\",\n    \"tranif1\",\n    \"tri\",\n    \"tri0\",\n    \"tri1\",\n    \"triand\",\n    \"trior\",\n    \"trireg\",\n    \"type\",\n    \"typedef\",\n    \"union\",\n    \"unique\",\n    \"unique0\",\n    \"unsigned\",\n    \"until\",\n    \"until_with\",\n    \"untyped\",\n    \"use\",\n    \"uwire\",\n    \"var\",\n    \"vectored\",\n    \"virtual\",\n    \"void\",\n    \"wait\",\n    \"wait_order\",\n    \"wand\",\n    \"weak\",\n    \"weak0\",\n    \"weak1\",\n    \"while\",\n    \"wildcard\",\n    \"wire\",\n    \"with\",\n    \"within\",\n    \"wor\",\n    \"xnor\",\n    \"xor\"\n  ],\n  builtin_gates: [\n    \"and\",\n    \"nand\",\n    \"nor\",\n    \"or\",\n    \"xor\",\n    \"xnor\",\n    \"buf\",\n    \"not\",\n    \"bufif0\",\n    \"bufif1\",\n    \"notif1\",\n    \"notif0\",\n    \"cmos\",\n    \"nmos\",\n    \"pmos\",\n    \"rcmos\",\n    \"rnmos\",\n    \"rpmos\",\n    \"tran\",\n    \"tranif1\",\n    \"tranif0\",\n    \"rtran\",\n    \"rtranif1\",\n    \"rtranif0\"\n  ],\n  operators: [\n    // assignment operators\n    \"=\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"%=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"<<=\",\n    \">>+\",\n    \"<<<=\",\n    \">>>=\",\n    // conditional expression\n    \"?\",\n    \":\",\n    // Unary operators\n    \"+\",\n    \"-\",\n    \"!\",\n    \"~\",\n    \"&\",\n    \"~&\",\n    \"|\",\n    \"~|\",\n    \"^\",\n    \"~^\",\n    \"^~\",\n    //binary operators\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"==\",\n    \"!=\",\n    \"===\",\n    \"!==\",\n    \"==?\",\n    \"!=?\",\n    \"&&\",\n    \"||\",\n    \"**\",\n    \"<\",\n    \"<=\",\n    \">\",\n    \">=\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \">>\",\n    \"<<\",\n    \">>>\",\n    \"<<<\",\n    // increment or decrement operator\n    \"++\",\n    \"--\",\n    //binary logical operator\n    \"->\",\n    \"<->\",\n    // binary set membership operator\n    \"inside\",\n    // binary distrubution operator\n    \"dist\",\n    \"::\",\n    \"+:\",\n    \"-:\",\n    \"*>\",\n    \"&&&\",\n    \"|->\",\n    \"|=>\",\n    \"#=#\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%#]+/,\n  escapes: /%%|\\\\(?:[antvf\\\\\"']|x[0-9A-Fa-f]{1,2}|[0-7]{1,3})/,\n  identifier: /(?:[a-zA-Z_][a-zA-Z0-9_$\\.]*|\\\\\\S+ )/,\n  systemcall: /[$][a-zA-Z0-9_]+/,\n  timeunits: /s|ms|us|ns|ps|fs/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // module instances\n      [\n        /^(\\s*)(@identifier)/,\n        [\n          \"\",\n          {\n            cases: {\n              \"@builtin_gates\": {\n                token: \"keyword.$2\",\n                next: \"@module_instance\"\n              },\n              table: {\n                token: \"keyword.$2\",\n                next: \"@table\"\n              },\n              \"@keywords\": { token: \"keyword.$2\" },\n              \"@default\": {\n                token: \"identifier\",\n                next: \"@module_instance\"\n              }\n            }\n          }\n        ]\n      ],\n      // include statements\n      [/^\\s*`include/, { token: \"keyword.directive.include\", next: \"@include\" }],\n      // Preprocessor directives\n      [/^\\s*`\\s*\\w+/, \"keyword\"],\n      // identifiers and keywords\n      { include: \"@identifier_or_keyword\" },\n      // whitespace and comments\n      { include: \"@whitespace\" },\n      // (* attributes *).\n      [/\\(\\*.*\\*\\)/, \"annotation\"],\n      // Systemcall\n      [/@systemcall/, \"variable.predefined\"],\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      { include: \"@numbers\" },\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      { include: \"@strings\" }\n    ],\n    identifier_or_keyword: [\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/\\d+?[\\d_]*(?:\\.[\\d_]+)?[eE][\\-+]?\\d+/, \"number.float\"],\n      [/\\d+?[\\d_]*\\.[\\d_]+(?:\\s*@timeunits)?/, \"number.float\"],\n      [/(?:\\d+?[\\d_]*\\s*)?'[sS]?[dD]\\s*[0-9xXzZ?]+?[0-9xXzZ?_]*/, \"number\"],\n      [/(?:\\d+?[\\d_]*\\s*)?'[sS]?[bB]\\s*[0-1xXzZ?]+?[0-1xXzZ?_]*/, \"number.binary\"],\n      [/(?:\\d+?[\\d_]*\\s*)?'[sS]?[oO]\\s*[0-7xXzZ?]+?[0-7xXzZ?_]*/, \"number.octal\"],\n      [/(?:\\d+?[\\d_]*\\s*)?'[sS]?[hH]\\s*[0-9a-fA-FxXzZ?]+?[0-9a-fA-FxXzZ?_]*/, \"number.hex\"],\n      [/1step/, \"number\"],\n      [/[\\dxXzZ]+?[\\dxXzZ_]*(?:\\s*@timeunits)?/, \"number\"],\n      [/'[01xXzZ]+/, \"number\"]\n    ],\n    module_instance: [\n      { include: \"@whitespace\" },\n      [/(#?)(\\()/, [\"\", { token: \"@brackets\", next: \"@port_connection\" }]],\n      [/@identifier\\s*[;={}\\[\\],]/, { token: \"@rematch\", next: \"@pop\" }],\n      [/@symbols|[;={}\\[\\],]/, { token: \"@rematch\", next: \"@pop\" }],\n      [/@identifier/, \"type\"],\n      [/;/, \"delimiter\", \"@pop\"]\n    ],\n    port_connection: [\n      { include: \"@identifier_or_keyword\" },\n      { include: \"@whitespace\" },\n      [/@systemcall/, \"variable.predefined\"],\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[,]/, \"delimiter\"],\n      [/\\(/, \"@brackets\", \"@port_connection\"],\n      [/\\)/, \"@brackets\", \"@pop\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    strings: [\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    include: [\n      [\n        /(\\s*)(\")([\\w*\\/*]*)(.\\w*)(\")/,\n        [\n          \"\",\n          \"string.include.identifier\",\n          \"string.include.identifier\",\n          \"string.include.identifier\",\n          { token: \"string.include.identifier\", next: \"@pop\" }\n        ]\n      ],\n      [\n        /(\\s*)(<)([\\w*\\/*]*)(.\\w*)(>)/,\n        [\n          \"\",\n          \"string.include.identifier\",\n          \"string.include.identifier\",\n          \"string.include.identifier\",\n          { token: \"string.include.identifier\", next: \"@pop\" }\n        ]\n      ]\n    ],\n    table: [\n      { include: \"@whitespace\" },\n      [/[()]/, \"@brackets\"],\n      [/[:;]/, \"delimiter\"],\n      [/[01\\-*?xXbBrRfFpPnN]/, \"variable.predefined\"],\n      [\"endtable\", \"keyword.endtable\", \"@pop\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.js\n"));

/***/ })

}]);