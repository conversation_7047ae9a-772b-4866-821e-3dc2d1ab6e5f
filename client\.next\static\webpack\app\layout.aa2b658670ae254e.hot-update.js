"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e66eb4bb8070\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUHJvamVjdFxccmVhbGNvZGVcXGNsaWVudFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZTY2ZWI0YmI4MDcwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/socketService.ts":
/*!***************************************!*\
  !*** ./src/services/socketService.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n// client/src/services/socketService.ts\n\n\n// Fix: Ensure SOCKET_URL is defined before use (move it above all uses)\nconst SOCKET_URL = \"http://localhost:5001\" || 0;\n// Create a singleton socket instance\nclass SocketService {\n    static getInstance() {\n        if (!SocketService.instance) {\n            SocketService.instance = new SocketService();\n        }\n        return SocketService.instance;\n    }\n    initSocket() {\n        try {\n            console.log('Initializing socket connection to', SOCKET_URL);\n            // Remove listeners from old socket if it exists\n            if (this.socket) {\n                this.socket.removeAllListeners();\n                this.socket.disconnect();\n            }\n            // Use polling first for better compatibility, then upgrade to websocket\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                reconnectionAttempts: 10,\n                reconnectionDelay: 1000,\n                timeout: 10000,\n                transports: [\n                    'polling',\n                    'websocket'\n                ],\n                autoConnect: true,\n                forceNew: true,\n                upgrade: true\n            });\n            console.log('Socket instance created successfully');\n            this.setupEventListeners();\n        } catch (error) {\n            console.error('Error initializing socket:', error);\n        }\n    }\n    setupEventListeners() {\n        if (!this.socket) return;\n        // Clear any existing listeners to prevent duplicates\n        this.socket.removeAllListeners();\n        // Connection events\n        this.socket.on('connect', ()=>{\n            var _this_socket;\n            console.log('Socket connected successfully:', (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.id);\n            this.connected = true;\n            this.emitEvent('connect', null);\n        });\n        this.socket.on('disconnect', (reason)=>{\n            console.log(\"Socket disconnected. Reason: \".concat(reason));\n            this.connected = false;\n            this.emitEvent('disconnect', reason);\n            if (reason !== 'io client disconnect') {\n                console.log('Reconnection attempt will start in 2 seconds...');\n                setTimeout(()=>{\n                    console.log('Attempting to reconnect...');\n                    this.connect();\n                }, 2000);\n            }\n        });\n        // Transport and reconnection events\n        if (this.socket.io) {\n            this.socket.io.on('error', (error)=>{\n                console.error('Transport error:', error);\n            });\n            this.socket.io.on('reconnect_attempt', (attempt)=>{\n                console.log(\"Reconnection attempt \".concat(attempt));\n            });\n            this.socket.io.on('reconnect', (attemptNumber)=>{\n                this.connected = true;\n                this.emitEvent('connect', null);\n            });\n            this.socket.io.on('reconnect_error', (error)=>{\n                console.error('Reconnection error:', error);\n            });\n            this.socket.io.on('reconnect_failed', ()=>{\n                this.emitEvent('error', 'Failed to reconnect after multiple attempts');\n                // Try a different approach after all reconnection attempts fail\n                setTimeout(()=>{\n                    this.initSocket();\n                }, 3000);\n            });\n        }\n        this.socket.on('connect_error', (error)=>{\n            var _error_message, _error_message1, _error_message2;\n            console.error('Socket connection error:', (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error');\n            this.connected = false;\n            // Handle specific error types\n            if (error === null || error === void 0 ? void 0 : (_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('xhr poll error')) {\n                console.log('XHR polling error detected - will try alternative connection method');\n                // Set a flag to track this specific error\n                if (true) {\n                    window.localStorage.setItem('socket_xhr_error', 'true');\n                }\n                // Emit the error event\n                this.emitEvent('error', 'XHR polling error - trying alternative connection');\n                // Try the alternative transport after a short delay\n                setTimeout(()=>{\n                    this.tryAlternativeTransport();\n                }, 1000);\n            } else if ((error === null || error === void 0 ? void 0 : (_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('websocket')) || (error === null || error === void 0 ? void 0 : (_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('WebSocket')) || typeof error === 'object' && 'type' in error && error.type === 'TransportError') {\n                console.log('WebSocket error detected - will try polling transport');\n                // Set a flag to track this specific error\n                if (true) {\n                    window.localStorage.setItem('socket_websocket_error', 'true');\n                }\n                // Try polling immediately\n                setTimeout(()=>{\n                    try {\n                        var _this_socket;\n                        console.log('Switching to polling transport...');\n                        (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.disconnect();\n                        this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                            reconnectionAttempts: 5,\n                            reconnectionDelay: 1000,\n                            timeout: 10000,\n                            transports: [\n                                'polling'\n                            ],\n                            autoConnect: true,\n                            forceNew: true,\n                            upgrade: false\n                        });\n                        this.setupEventListeners();\n                    } catch (e) {\n                        console.error('Error switching to polling transport:', e);\n                    }\n                }, 1000);\n                this.emitEvent('error', 'WebSocket error - trying polling transport');\n            } else {\n                // For other errors, just emit the error event\n                this.emitEvent('error', (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error');\n                // Try to reconnect after a delay\n                setTimeout(()=>{\n                    this.connect();\n                }, 3000);\n            }\n        });\n        // Application-specific events\n        this.socket.on('code-update', (code)=>{\n            this.emitEvent('code-update', code);\n        });\n        this.socket.on('user-typing', (data)=>{\n            this.emitEvent('user-typing', data);\n        });\n        this.socket.on('user-joined', (users)=>{\n            this.emitEvent('user-joined', users);\n        });\n        this.socket.on('user-left', (users)=>{\n            this.emitEvent('user-left', users);\n        });\n        this.socket.on('highlight-line', (data)=>{\n            this.emitEvent('highlight-line', data);\n        });\n        this.socket.on('cursor-move', (param)=>{\n            let { userId, position } = param;\n            var _this_listeners_get;\n            console.log(\"Cursor move received from user \".concat(userId, \":\"), position);\n            (_this_listeners_get = this.listeners.get('cursor-move')) === null || _this_listeners_get === void 0 ? void 0 : _this_listeners_get.forEach((callback)=>callback({\n                    userId,\n                    position\n                }));\n        });\n        this.socket.on('room-users-updated', (data)=>{\n            this.emitEvent('room-users-updated', data);\n        });\n        // Handle request for initial code from new users\n        this.socket.on('get-initial-code', (data)=>{\n            this.emitEvent('get-initial-code', data);\n        });\n        // Handle receiving initial code\n        this.socket.on('initial-code-received', (data)=>{\n            this.emitEvent('initial-code-received', data);\n        });\n        // Handle teacher selection events\n        this.socket.on('teacher-selection', (data)=>{\n            this.emitEvent('teacher-selection', data);\n        });\n        this.socket.on('clear-teacher-selection', (data)=>{\n            this.emitEvent('clear-teacher-selection', data);\n        });\n    }\n    // Add event listener\n    on(event, callback) {\n        var _this_listeners_get;\n        if (!this.listeners.has(event)) {\n            this.listeners.set(event, []);\n        }\n        (_this_listeners_get = this.listeners.get(event)) === null || _this_listeners_get === void 0 ? void 0 : _this_listeners_get.push(callback);\n    }\n    // Remove event listener\n    off(event, callback) {\n        if (!this.listeners.has(event)) return;\n        const callbacks = this.listeners.get(event) || [];\n        const index = callbacks.indexOf(callback);\n        if (index !== -1) {\n            callbacks.splice(index, 1);\n        }\n    }\n    // Emit event to listeners\n    emitEvent(event, data) {\n        if (!this.listeners.has(event)) return;\n        const callbacks = this.listeners.get(event) || [];\n        callbacks.forEach((callback)=>{\n            if (typeof callback === 'function') {\n                try {\n                    callback(data);\n                } catch (error) {\n                    console.error(\"Error in \".concat(event, \" listener:\"), error);\n                }\n            }\n        });\n    }\n    // Check if socket is connected\n    isConnected() {\n        var _this_socket;\n        return this.connected && !!((_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.connected);\n    }\n    // Connect to socket server with fallback mechanisms\n    connect() {\n        if (!this.socket) {\n            this.initSocket();\n        } else if (!this.socket.connected) {\n            console.log('Socket exists but not connected, attempting to reconnect...');\n            // Try to reconnect with existing socket\n            try {\n                this.socket.connect();\n            } catch (error) {\n                console.error('Error reconnecting with existing socket:', error);\n                // If reconnection fails, create a new socket\n                this.initSocket();\n            }\n        }\n        // Set a timeout to check if connection was successful\n        setTimeout(()=>{\n            if (!this.isConnected()) {\n                this.tryAlternativeTransport();\n            }\n        }, 5000);\n    }\n    // Try alternative transport method if WebSocket fails\n    tryAlternativeTransport() {\n        try {\n            // Disconnect existing socket if any\n            if (this.socket) {\n                this.socket.disconnect();\n            }\n            // Create new socket with both transports but prioritize polling\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                reconnectionAttempts: 5,\n                reconnectionDelay: 1000,\n                timeout: 10000,\n                transports: [\n                    'polling',\n                    'websocket'\n                ],\n                autoConnect: true,\n                forceNew: true,\n                upgrade: true\n            });\n            this.setupEventListeners();\n        } catch (error) {\n            console.error('Error setting up alternative transport:', error);\n        }\n    }\n    // Disconnect from socket server\n    disconnect() {\n        if (this.socket) {\n            this.socket.disconnect();\n        }\n    }\n    // Create a new room\n    createRoom(username, roomId) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket || !this.isConnected()) {\n                return reject(new Error('Socket not connected'));\n            }\n            // Generate a unique userId if not already stored\n            let userId =  true ? window.localStorage.getItem('userId') : 0;\n            if (!userId) {\n                userId = \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9));\n                if (true) {\n                    window.localStorage.setItem('userId', userId);\n                }\n            }\n            this.socket.emit('create-room', {\n                username,\n                roomId,\n                userId\n            }, (response)=>{\n                if (response.error) {\n                    reject(new Error(response.error));\n                } else {\n                    // If the server validated and possibly changed the username, update it locally\n                    if (response.username && response.username !== username) {\n                        if (true) {\n                            window.localStorage.setItem('username', response.username);\n                        }\n                    }\n                    resolve({\n                        roomId: response.roomId,\n                        username: response.username || username\n                    });\n                }\n            });\n        });\n    }\n    // Join an existing room with fallback to HTTP if socket fails\n    joinRoom(roomId, username) {\n        return new Promise(async (resolve, reject)=>{\n            // Generate a unique userId if not already stored\n            let userId =  true ? window.localStorage.getItem('userId') : 0;\n            if (!userId) {\n                userId = \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9));\n                if (true) {\n                    window.localStorage.setItem('userId', userId);\n                }\n            }\n            // Check if we have a socket connection\n            if (!this.socket || !this.isConnected()) {\n                // Try to connect\n                this.connect();\n                // Wait a bit to see if connection succeeds\n                await new Promise((r)=>setTimeout(r, 2000));\n                // If still not connected, use HTTP fallback\n                if (!this.isConnected()) {\n                    return this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                }\n            }\n            // If we reach here, we have a socket connection, so use it\n            try {\n                if (!this.socket) {\n                    throw new Error('Socket is null');\n                }\n                // Emit join-room with userId, username, and roomId\n                this.socket.emit('join-room', {\n                    roomId,\n                    username,\n                    userId\n                }, (response)=>{\n                    if (response.error) {\n                        // If socket join fails, try HTTP fallback\n                        this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                    } else if (response.success) {\n                        // If the server validated and possibly changed the username, update it locally\n                        if (response.username && response.username !== username) {\n                            if (true) {\n                                window.localStorage.setItem('username', response.username);\n                            }\n                        }\n                        // Mark the current user in the users list\n                        const usersWithCurrentFlag = (response.users || []).map((user)=>({\n                                ...user,\n                                userId: user.userId || '',\n                                isCurrentUser: (user.userId || '') === userId\n                            }));\n                        resolve({\n                            users: usersWithCurrentFlag || [],\n                            username: response.username || username,\n                            role: response.role\n                        });\n                    } else {\n                        // If socket join fails, try HTTP fallback\n                        this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                    }\n                });\n                // Set a timeout in case the callback never fires\n                setTimeout(()=>{\n                    // If we haven't resolved or rejected yet, try HTTP fallback\n                    this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                }, 5000);\n            } catch (error) {\n                // If socket join throws an exception, try HTTP fallback\n                this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n            }\n        });\n    }\n    // HTTP fallback for joining a room when socket fails\n    joinRoomViaHttp(roomId, username, userId, resolve, reject) {\n        // Use axios to make a direct HTTP request to the server\n        const apiUrl = \"\".concat(SOCKET_URL, \"/api/join-room\");\n        axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(apiUrl, {\n            roomId,\n            username,\n            userId\n        }).then((response)=>{\n            if (response.data.error) {\n                reject(new Error(response.data.error));\n            } else {\n                // If the server validated and possibly changed the username, update it locally\n                if (response.data.username && response.data.username !== username) {\n                    if (true) {\n                        window.localStorage.setItem('username', response.data.username);\n                    }\n                }\n                // Create a default user list with at least the current user\n                const users = response.data.users || [\n                    {\n                        userId,\n                        username: response.data.username || username,\n                        socketId: 'http-fallback'\n                    }\n                ];\n                // Fix: When mapping users, always provide a fallback for userId (empty string if undefined)\n                const usersWithCurrentFlag = users.map((user)=>({\n                        ...user,\n                        userId: user.userId || '',\n                        isCurrentUser: (user.userId || '') === userId\n                    }));\n                resolve({\n                    users: usersWithCurrentFlag,\n                    username: response.data.username || username,\n                    role: response.data.role\n                });\n                // Try to reconnect socket after successful HTTP fallback\n                setTimeout(()=>this.connect(), 1000);\n            }\n        }).catch((error)=>{\n            // If HTTP fallback also fails, create a minimal response with just the current user\n            const fallbackUser = {\n                userId,\n                username,\n                isCurrentUser: true\n            };\n            // Resolve with just the current user to allow the UI to function\n            resolve({\n                users: [\n                    fallbackUser\n                ],\n                username,\n                role: 'student' // Default role for fallback\n            });\n            // Try to reconnect socket after error\n            setTimeout(()=>this.connect(), 2000);\n        });\n    }\n    // Send code changes to the server with HTTP fallback\n    sendCodeChange(roomId, code) {\n        // If socket is connected, use it\n        if (this.socket && this.isConnected()) {\n            try {\n                // Use volatile for code changes to prevent queueing\n                // This helps prevent outdated updates from being sent\n                this.socket.volatile.emit('code-change', {\n                    roomId,\n                    code\n                });\n                return true;\n            } catch (error) {\n            // Fall through to HTTP fallback\n            }\n        } else {\n            console.warn('Socket not connected, falling back to HTTP for code change.');\n        }\n        // HTTP fallback for code changes\n        this.sendCodeChangeViaHttp(roomId, code);\n        return true;\n    }\n    // Send initial code to a requesting user\n    sendInitialCode(roomId, code, requestingUserId) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot send initial code');\n            return false;\n        }\n        try {\n            this.socket.emit('send-initial-code', {\n                roomId,\n                code,\n                requestingUserId\n            });\n            console.log(\"Sent initial code to user \".concat(requestingUserId, \" in room \").concat(roomId));\n            return true;\n        } catch (error) {\n            console.error('Error sending initial code:', error);\n            return false;\n        }\n    }\n    // Send teacher text selection to students\n    sendTeacherSelection(roomId, selection) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot send teacher selection');\n            return false;\n        }\n        try {\n            this.socket.emit('teacher-selection', {\n                roomId,\n                selection\n            });\n            console.log(\"Sent teacher selection to room \".concat(roomId, \":\"), selection);\n            return true;\n        } catch (error) {\n            console.error('Error sending teacher selection:', error);\n            return false;\n        }\n    }\n    // Clear teacher text selection\n    clearTeacherSelection(roomId) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot clear teacher selection');\n            return false;\n        }\n        try {\n            this.socket.emit('clear-teacher-selection', {\n                roomId\n            });\n            console.log(\"Cleared teacher selection in room \".concat(roomId));\n            return true;\n        } catch (error) {\n            console.error('Error clearing teacher selection:', error);\n            return false;\n        }\n    }\n    // Send teacher cursor position to students\n    sendTeacherCursorPosition(roomId, position) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot send teacher cursor position');\n            return false;\n        }\n        try {\n            this.socket.emit('teacher-cursor-position', {\n                roomId,\n                position\n            });\n            return true;\n        } catch (error) {\n            console.error('Error sending teacher cursor position:', error);\n            return false;\n        }\n    }\n    // Send teacher text highlight to students\n    sendTeacherTextHighlight(roomId, selection) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot send teacher text highlight');\n            return false;\n        }\n        try {\n            this.socket.emit('teacher-text-highlight', {\n                roomId,\n                selection\n            });\n            return true;\n        } catch (error) {\n            console.error('Error sending teacher text highlight:', error);\n            return false;\n        }\n    }\n    // Clear teacher text highlight\n    clearTeacherTextHighlight(roomId) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot clear teacher text highlight');\n            return false;\n        }\n        try {\n            this.socket.emit('clear-teacher-text-highlight', {\n                roomId\n            });\n            return true;\n        } catch (error) {\n            console.error('Error clearing teacher text highlight:', error);\n            return false;\n        }\n    }\n    // HTTP fallback for sending code changes\n    sendCodeChangeViaHttp(roomId, code) {\n        // Only send HTTP fallback for significant changes to reduce traffic\n        // Store the last sent code to avoid sending duplicates\n        const lastSentCode =  true ? window.localStorage.getItem(\"last_http_code_\".concat(roomId)) : 0;\n        if (lastSentCode === code) {\n            return; // Don't send duplicate code\n        }\n        // Use axios to make a direct HTTP request to the server\n        const apiUrl = \"\".concat(SOCKET_URL, \"/api/code-change\");\n        const userId =  true ? window.localStorage.getItem('userId') || '' : 0;\n        axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(apiUrl, {\n            roomId,\n            code,\n            userId\n        }).then((response)=>{\n            // Store the sent code to avoid duplicates\n            if (true) {\n                window.localStorage.setItem(\"last_http_code_\".concat(roomId), code);\n            }\n        }).catch((error)=>{\n            console.error('Error sending code change via HTTP:', error);\n        });\n        // Try to reconnect socket\n        if (!this.isConnected()) {\n            setTimeout(()=>this.connect(), 1000);\n        }\n    }\n    // Send typing notification\n    sendTyping(roomId, username) {\n        if (!this.socket || !this.isConnected()) {\n            return;\n        }\n        // Use the exact username provided by the user without any modifications\n        // This ensures we use exactly what the user entered on the dashboard\n        const validUsername = username;\n        // Get userId from localStorage\n        const userId =  true ? window.localStorage.getItem('userId') || \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9)) : 0;\n        try {\n            this.socket.emit('typing', {\n                roomId,\n                username: validUsername,\n                userId\n            });\n        } catch (error) {}\n    }\n    // Emit a highlight-line event to the server\n    sendHighlightLine(roomId, startLine, endLine, comment) {\n        if (!this.socket || !this.isConnected()) return;\n        try {\n            this.socket.emit('highlight-line', {\n                roomId,\n                startLine,\n                endLine,\n                comment\n            });\n        } catch (error) {\n        // Optionally handle error\n        }\n    }\n    // Leave a room\n    leaveRoom(roomId) {\n        if (!this.socket || !this.isConnected()) return;\n        try {\n            this.socket.emit('leave-room', roomId);\n        } catch (error) {}\n    }\n    // Add a public method to listen for the next connect event\n    onConnect(callback) {\n        if (this.isConnected()) {\n            callback();\n        } else if (this.socket) {\n            this.socket.once('connect', callback);\n        } else {\n            // If socket is not initialized, initialize and then listen\n            this.initSocket();\n            // Wait for the socket to be created, then attach the listener\n            setTimeout(()=>{\n                var _this_socket;\n                (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.once('connect', callback);\n            }, 100);\n        }\n    }\n    getSocket() {\n        return this.socket;\n    }\n    // Emit cursor movement\n    sendCursorMove(roomId, userId, position) {\n        if (this.socket && this.connected) {\n            this.socket.emit(\"cursor-move\", {\n                roomId,\n                userId,\n                position\n            });\n        }\n    }\n    // Add a method to validate if a room exists on the server\n    async validateRoom(roomId) {\n        if (!this.socket || !this.isConnected()) {\n            throw new Error(\"Socket not connected\");\n        }\n        return new Promise((resolve, reject)=>{\n            if (!this.socket) {\n                throw new Error(\"Socket is not initialized\");\n            }\n            this.socket.emit(\"validate-room\", {\n                roomId\n            }, (response)=>{\n                if (response) {\n                    resolve(response);\n                } else {\n                    reject(new Error(\"Failed to validate room\"));\n                }\n            });\n        });\n    }\n    constructor(){\n        this.socket = null;\n        this.listeners = new Map();\n        this.connected = false;\n        this.initSocket();\n    }\n}\n// Export the class itself instead of calling getInstance during export\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SocketService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/socketService.ts\n"));

/***/ })

});