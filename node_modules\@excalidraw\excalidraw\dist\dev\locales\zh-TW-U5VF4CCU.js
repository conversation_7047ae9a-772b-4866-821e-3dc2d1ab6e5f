import "../chunk-XDFCUUT6.js";

// locales/zh-TW.json
var labels = {
  paste: "\u8CBC\u4E0A",
  pasteAsPlaintext: "\u4EE5\u7D14\u6587\u5B57\u8CBC\u4E0A",
  pasteCharts: "\u8CBC\u4E0A\u5716\u8868",
  selectAll: "\u5168\u9078",
  multiSelect: "\u5C07\u7269\u4EF6\u52A0\u5165\u9078\u53D6\u7BC4\u570D",
  moveCanvas: "\u79FB\u52D5\u756B\u5E03",
  cut: "\u526A\u4E0B",
  copy: "\u8907\u88FD",
  copyAsPng: "\u4EE5PNG\u683C\u5F0F\u5132\u5B58\u5230\u526A\u8CBC\u677F",
  copyAsSvg: "\u4EE5SVG\u683C\u5F0F\u8907\u88FD\u5230\u526A\u8CBC\u677F",
  copyText: "\u4EE5\u6587\u5B57\u683C\u5F0F\u8907\u88FD\u81F3\u526A\u8CBC\u7C3F",
  copySource: "\u8907\u88FD\u4F86\u6E90\u81F3\u526A\u8CBC\u7C3F",
  convertToCode: "\u8F49\u63DB\u70BA\u7A0B\u5F0F\u78BC",
  bringForward: "\u4E0A\u79FB\u4E00\u5C64",
  sendToBack: "\u79FB\u5230\u6700\u5E95\u5C64",
  bringToFront: "\u7F6E\u65BC\u6700\u9802\u5C64",
  sendBackward: "\u5F80\u5F8C\u79FB\u4E00\u5C64",
  delete: "\u522A\u9664",
  copyStyles: "\u8907\u88FD\u6A23\u5F0F",
  pasteStyles: "\u8CBC\u4E0A\u6A23\u5F0F",
  stroke: "\u7B46\u756B",
  background: "\u80CC\u666F",
  fill: "\u586B\u6EFF",
  strokeWidth: "\u7B46\u8DE1\u5BEC\u5EA6",
  strokeStyle: "\u7B46\u756B\u6A23\u5F0F",
  strokeStyle_solid: "\u5BE6\u7DDA",
  strokeStyle_dashed: "\u865B\u7DDA",
  strokeStyle_dotted: "\u9EDE\u7DDA",
  sloppiness: "\u7DDA\u689D\u98A8\u683C",
  opacity: "\u900F\u660E\u5EA6",
  textAlign: "\u6587\u5B57\u5C0D\u9F4A",
  edges: "\u908A\u7DE3",
  sharp: "\u5C16\u92B3",
  round: "\u5E73\u6ED1",
  arrowheads: "\u7BAD\u982D",
  arrowhead_none: "\u7121",
  arrowhead_arrow: "\u7BAD\u982D",
  arrowhead_bar: "\u689D\u72C0\u7BAD\u982D",
  arrowhead_circle: "\u5713\u5F62",
  arrowhead_circle_outline: "\u5713\u5F62\uFF08\u5916\u6846\uFF09",
  arrowhead_triangle: "\u4E09\u89D2\u5F62",
  arrowhead_triangle_outline: "\u4E09\u89D2\u5F62\uFF08\u5916\u6846\uFF09",
  arrowhead_diamond: "\u83F1\u5F62",
  arrowhead_diamond_outline: "\u83F1\u5F62\uFF08\u5916\u6846\uFF09",
  fontSize: "\u5B57\u578B\u5927\u5C0F",
  fontFamily: "\u5B57\u9AD4\u96C6",
  addWatermark: '\u52A0\u4E0A "Made with Excalidraw" \u6D6E\u6C34\u5370',
  handDrawn: "\u624B\u5BEB",
  normal: "\u4E00\u822C",
  code: "\u4EE3\u78BC",
  small: "\u5C0F",
  medium: "\u4E2D",
  large: "\u5927",
  veryLarge: "\u7279\u5927",
  solid: "\u5BE6\u5FC3",
  hachure: "\u659C\u7DDA\u7B46\u89F8",
  zigzag: "\uFF3A\u5B57\u5F62",
  crossHatch: "\u4EA4\u53C9\u7B46\u89F8",
  thin: "\u7D30",
  bold: "\u7C97",
  left: "\u5DE6\u5074",
  center: "\u7F6E\u4E2D",
  right: "\u53F3\u5074",
  extraBold: "\u6975\u7C97",
  architect: "\u7CBE\u78BA",
  artist: "\u85DD\u8853",
  cartoonist: "\u5361\u901A",
  fileTitle: "\u6A94\u6848\u540D\u7A31",
  colorPicker: "\u8272\u5F69\u9078\u64C7\u5DE5\u5177",
  canvasColors: "\u4F7F\u7528\u65BC\u756B\u5E03",
  canvasBackground: "Canvas \u80CC\u666F",
  drawingCanvas: "\u7E6A\u5716 canvas",
  layers: "\u5716\u5C64",
  actions: "\u52D5\u4F5C",
  language: "\u8A9E\u8A00",
  liveCollaboration: "\u5373\u6642\u5354\u4F5C...",
  duplicateSelection: "\u8907\u88FD",
  untitled: "\u7121\u6A19\u984C",
  name: "\u540D\u7A31",
  yourName: "\u4F60\u7684\u540D\u7A31",
  madeWithExcalidraw: "\u4EE5 Excalidraw \u88FD\u4F5C",
  group: "\u5EFA\u7ACB\u7FA4\u7D44",
  ungroup: "\u53D6\u6D88\u7FA4\u7D44",
  collaborators: "\u5354\u4F5C\u8005",
  showGrid: "\u986F\u793A\u683C\u7DDA",
  addToLibrary: "\u52A0\u5165\u8CC7\u6599\u5EAB",
  removeFromLibrary: "\u5F9E\u8CC7\u6599\u5EAB\u4E2D\u79FB\u9664",
  libraryLoadingMessage: "\u8CC7\u6599\u5EAB\u8B80\u53D6\u4E2D\u2026",
  libraries: "\u700F\u89BD\u8CC7\u6599\u5EAB",
  loadingScene: "\u5834\u666F\u8B80\u53D6\u4E2D\u2026",
  align: "\u5C0D\u9F4A",
  alignTop: "\u5C0D\u9F4A\u9802\u90E8",
  alignBottom: "\u5C0D\u9F4A\u5E95\u90E8",
  alignLeft: "\u5C0D\u9F4A\u5DE6\u5074",
  alignRight: "\u5C0D\u9F4A\u53F3\u5074",
  centerVertically: "\u5782\u76F4\u7F6E\u4E2D",
  centerHorizontally: "\u6C34\u5E73\u7F6E\u4E2D",
  distributeHorizontally: "\u6C34\u5E73\u5206\u5E03",
  distributeVertically: "\u5782\u76F4\u5206\u5E03",
  flipHorizontal: "\u6C34\u5E73\u7FFB\u8F49",
  flipVertical: "\u5782\u76F4\u7FFB\u8F49",
  viewMode: "\u6AA2\u8996\u6A21\u5F0F",
  share: "\u5171\u4EAB",
  showStroke: "\u986F\u793A\u7DDA\u689D\u6AA2\u8272\u5668",
  showBackground: "\u986F\u793A\u80CC\u666F\u6AA2\u8272\u5668",
  toggleTheme: "\u5207\u63DB\u4E3B\u984C",
  personalLib: "\u500B\u4EBA\u8CC7\u6599\u5EAB",
  excalidrawLib: "Excalidraw \u8CC7\u6599\u5EAB",
  decreaseFontSize: "\u7E2E\u5C0F\u6587\u5B57",
  increaseFontSize: "\u653E\u5927\u6587\u5B57",
  unbindText: "\u53D6\u6D88\u7D81\u5B9A\u6587\u5B57",
  bindText: "\u7D50\u5408\u6587\u5B57\u81F3\u5BB9\u5668",
  createContainerFromText: "\u5C07\u6587\u5B57\u5305\u65BC\u5BB9\u5668\u4E2D",
  link: {
    edit: "\u7DE8\u8F2F\u9023\u7D50",
    editEmbed: "\u7DE8\u8F2F\u9023\u7D50&\u5D4C\u5165",
    create: "\u5EFA\u7ACB\u9023\u7D50",
    createEmbed: "\u5EFA\u7ACB\u9023\u7D50&\u5D4C\u5165",
    label: "\u9023\u7D50",
    labelEmbed: "\u9023\u7D50&\u5D4C\u5165",
    empty: "\u672A\u8A2D\u5B9A\u9023\u7D50"
  },
  lineEditor: {
    edit: "\u7DE8\u8F2F\u7DDA\u689D",
    exit: "\u7D50\u675F\u7DDA\u689D\u7DE8\u8F2F"
  },
  elementLock: {
    lock: "\u9396\u5B9A",
    unlock: "\u89E3\u9396",
    lockAll: "\u5168\u90E8\u9396\u5B9A",
    unlockAll: "\u5168\u90E8\u89E3\u9396"
  },
  statusPublished: "\u5DF2\u767C\u5E03",
  sidebarLock: "\u5074\u6B04\u7DAD\u6301\u958B\u555F",
  selectAllElementsInFrame: "\u9078\u53D6\u6846\u67B6\u5167\u7684\u6240\u6709\u5143\u7D20",
  removeAllElementsFromFrame: "\u5F9E\u6846\u67B6\u5167\u79FB\u9664\u6240\u6709\u5143\u7D20",
  eyeDropper: "\u5F9E\u756B\u5E03\u4E2D\u9078\u53D6\u984F\u8272",
  textToDiagram: "\u6587\u5B57\u8F49\u5716\u8868",
  prompt: "\u63D0\u793A\u8A5E"
};
var library = {
  noItems: "\u5C1A\u672A\u52A0\u5165\u4EFB\u4F55\u7269\u4EF6...",
  hint_emptyLibrary: "\u9078\u53D6\u756B\u5E03\u4E0A\u7684\u7269\u4EF6\u4EE5\u52A0\u5165\uFF0C\u6216\u5F9E\u4E0B\u65B9\u7684\u516C\u958B repository \u4E2D\u5B89\u88DD\u8CC7\u6599\u5EAB",
  hint_emptyPrivateLibrary: "\u9078\u64C7\u756B\u5E03\u4E0A\u7684\u7269\u4EF6\u4EE5\u5728\u6B64\u52A0\u5165"
};
var buttons = {
  clearReset: "\u91CD\u7F6E canvas",
  exportJSON: "\u532F\u51FA\u81F3\u6A94\u6848",
  exportImage: "\u532F\u51FA\u5716\u7247",
  export: "\u5132\u5B58\u81F3...",
  copyToClipboard: "\u8907\u88FD\u81F3\u526A\u8CBC\u7C3F",
  save: "\u5132\u5B58\u76EE\u524D\u6A94\u6848",
  saveAs: "\u5132\u5B58\u70BA",
  load: "\u958B\u555F",
  getShareableLink: "\u53D6\u5F97\u5171\u4EAB\u9023\u7D50",
  close: "\u95DC\u9589",
  selectLanguage: "\u9078\u64C7\u8A9E\u8A00",
  scrollBackToContent: "\u6372\u52D5\u56DE\u5230\u5167\u5BB9",
  zoomIn: "\u653E\u5927",
  zoomOut: "\u7E2E\u5C0F",
  resetZoom: "\u91CD\u8A2D\u7E2E\u653E",
  menu: "\u9078\u55AE",
  done: "\u5B8C\u6210",
  edit: "\u7DE8\u8F2F",
  undo: "\u5FA9\u539F",
  redo: "\u91CD\u505A",
  resetLibrary: "\u91CD\u8A2D\u8CC7\u6599\u5EAB",
  createNewRoom: "\u5EFA\u7ACB\u65B0\u5354\u4F5C\u6703\u8B70\u5BA4",
  fullScreen: "\u5168\u87A2\u5E55",
  darkMode: "\u6DF1\u8272\u6A21\u5F0F",
  lightMode: "\u6DFA\u8272\u6A21\u5F0F",
  zenMode: "\u5C08\u6CE8\u6A21\u5F0F",
  objectsSnapMode: "\u5438\u9644\u81F3\u7269\u4EF6",
  exitZenMode: "\u96E2\u958B\u5C08\u6CE8\u6A21\u5F0F",
  cancel: "\u53D6\u6D88",
  clear: "\u6E05\u9664",
  remove: "\u522A\u9664",
  embed: "\u5207\u63DB\u5D4C\u5165",
  publishLibrary: "\u767C\u5E03",
  submit: "\u9001\u51FA",
  confirm: "\u78BA\u8A8D",
  embeddableInteractionButton: "\u9EDE\u64CA\u4EE5\u4E92\u52D5"
};
var alerts = {
  clearReset: "\u9019\u5C07\u6703\u6E05\u9664\u6574\u500B canvas\u3002\u4F60\u78BA\u5B9A\u55CE\uFF1F",
  couldNotCreateShareableLink: "\u7121\u6CD5\u5EFA\u7ACB\u5171\u4EAB\u9023\u7D50\u3002",
  couldNotCreateShareableLinkTooBig: "\u7121\u6CD5\u5EFA\u7ACB\u5171\u4EAB\u9023\u7D50\uFF1A\u5834\u666F\u592A\u5927",
  couldNotLoadInvalidFile: "\u7121\u6CD5\u8B80\u53D6\u5931\u6548\u7684\u6A94\u6848\u3002",
  importBackendFailed: "\u5F8C\u7AEF\u8B80\u53D6\u5931\u6557\u3002",
  cannotExportEmptyCanvas: "\u7121\u6CD5\u8F38\u51FA\u7A7A\u767D\u7684 canvas\u3002",
  couldNotCopyToClipboard: "\u7121\u6CD5\u8907\u88FD\u5230\u526A\u8CBC\u7C3F",
  decryptFailed: "\u7121\u6CD5\u89E3\u5BC6\u8CC7\u6599\u3002",
  uploadedSecurly: "\u4E0A\u50B3\u5DF2\u901A\u904E end-to-end \u52A0\u5BC6\uFF0CExcalidraw \u4F3A\u670D\u5668\u548C\u7B2C\u4E09\u65B9\u7121\u6CD5\u7686\u8B80\u53D6\u5176\u5167\u5BB9\u3002",
  loadSceneOverridePrompt: "\u8B80\u53D6\u5916\u90E8\u5716\u6A23\u5C07\u53D6\u4EE3\u76EE\u524D\u7684\u5167\u5BB9\u3002\u662F\u5426\u8981\u7E7C\u7E8C\uFF1F",
  collabStopOverridePrompt: "\u505C\u6B62\u9023\u7DDA\u5C07\u8986\u84CB\u60A8\u5148\u524D\u65BC\u672C\u6A5F\u5132\u5B58\u7684\u7E6A\u5716\u9032\u5EA6\uFF0C\u662F\u5426\u78BA\u8A8D\uFF1F\n\n\uFF08\u5982\u8981\u4FDD\u7559\u539F\u6709\u7684\u672C\u6A5F\u7E6A\u5716\u9032\u5EA6\uFF0C\u76F4\u63A5\u95DC\u9589\u700F\u89BD\u5668\u5206\u9801\u5373\u53EF\u3002\uFF09",
  errorAddingToLibrary: "\u7121\u6CD5\u65BC\u6B64\u8CC7\u6599\u5EAB\u52A0\u5165\u9805\u76EE",
  errorRemovingFromLibrary: "\u7121\u6CD5\u7531\u6B64\u8CC7\u6599\u5EAB\u79FB\u9664\u9805\u76EE",
  confirmAddLibrary: "\u9019\u5C07\u6703\u5C07 {{numShapes}} \u500B\u5716\u5F62\u52A0\u5165\u4F60\u7684\u8CC7\u6599\u5EAB\uFF0C\u4F60\u78BA\u5B9A\u55CE\uFF1F",
  imageDoesNotContainScene: "\u6B64\u5716\u6A94\u4E2D\u672A\u5305\u542B\u5834\u666F\u8CC7\u6599\u3002\u8F38\u51FA\u6A94\u6848\u6642\u662F\u5426\u6709\u5305\u542B\u5834\u666F\u8CC7\u6599\uFF1F",
  cannotRestoreFromImage: "\u7121\u6CD5\u7531\u6B64\u6A94\u6848\u56DE\u5FA9\u5834\u666F\u3002",
  invalidSceneUrl: "\u7121\u6CD5\u7531\u63D0\u4F9B\u7684 URL \u532F\u5165\u5834\u666F\u3002\u53EF\u80FD\u662F\u767C\u751F\u7570\u5E38\uFF0C\u6216\u672A\u5305\u542B\u6709\u6548\u7684 Excalidraw JSON \u8CC7\u6599\u3002",
  resetLibrary: "\u9019\u6703\u6E05\u9664\u60A8\u7684\u8CC7\u6599\u5EAB\uFF0C\u662F\u5426\u78BA\u5B9A\uFF1F",
  removeItemsFromsLibrary: "\u5F9E\u8CC7\u6599\u5EAB\u522A\u9664 {{count}} \u9805\uFF1F",
  invalidEncryptionKey: "\u52A0\u5BC6\u9375\u5FC5\u9808\u70BA22\u5B57\u5143\u3002\u5373\u6642\u5354\u4F5C\u5DF2\u505C\u7528\u3002",
  collabOfflineWarning: "\u6C92\u6709\u53EF\u7528\u7684\u7DB2\u8DEF\u9023\u7DDA\u3002\n\u8B8A\u66F4\u7121\u6CD5\u5132\u5B58\uFF01"
};
var errors = {
  unsupportedFileType: "\u4E0D\u652F\u63F4\u7684\u6A94\u6848\u985E\u578B\u3002",
  imageInsertError: "\u7121\u6CD5\u63D2\u5165\u5716\u7247\u3002\u8ACB\u7A0D\u5F8C\u518D\u8A66\u2026",
  fileTooBig: "\u6A94\u6848\u904E\u5927\u3002\u53EF\u63A5\u53D7\u7684\u6700\u5927\u5C3A\u5BF8\u70BA {{maxSize}} \u3002",
  svgImageInsertError: "\u7121\u6CD5\u63D2\u5165 SVG \u5716\u7247\u3002\u6B64 SVG \u6A94\u6848\u6709\u554F\u984C\u3002",
  failedToFetchImage: "\u7121\u6CD5\u7372\u53D6\u5716\u7247\u3002",
  invalidSVGString: "\u7121\u6548\u7684 SVG\u3002",
  cannotResolveCollabServer: "\u7121\u6CD5\u9023\u7D50\u81F3 collab \u4F3A\u670D\u5668\u3002\u8ACB\u91CD\u65B0\u6574\u7406\u5F8C\u518D\u8A66\u4E00\u6B21\u3002",
  importLibraryError: "\u7121\u6CD5\u8F09\u5165\u8CC7\u6599\u5EAB",
  collabSaveFailed: "\u7121\u6CD5\u5132\u5B58\u81F3\u5F8C\u7AEF\u8CC7\u6599\u5EAB\u3002\u82E5\u6B64\u554F\u984C\u6301\u7E8C\u767C\u751F\uFF0C\u8ACB\u5C07\u6A94\u6848\u5132\u5B58\u65BC\u672C\u6A5F\u4EE5\u78BA\u4FDD\u8CC7\u6599\u4E0D\u6703\u907A\u5931\u3002",
  collabSaveFailed_sizeExceeded: "\u7121\u6CD5\u5132\u5B58\u81F3\u5F8C\u7AEF\u8CC7\u6599\u5EAB\uFF0C\u53EF\u80FD\u7684\u539F\u56E0\u70BA\u756B\u5E03\u5C3A\u5BF8\u904E\u5927\u3002\u8ACB\u5C07\u6A94\u6848\u5132\u5B58\u65BC\u672C\u6A5F\u4EE5\u78BA\u4FDD\u8CC7\u6599\u4E0D\u6703\u907A\u5931\u3002",
  imageToolNotSupported: "\u5716\u7247\u5DF2\u505C\u7528",
  brave_measure_text_error: {
    line1: "\u770B\u8D77\u4F86\u60A8\u958B\u555F\u4E86 Brave \u700F\u89BD\u5668\u7684 <bold>Aggressively Block Fingerprinting</bold> \u8A2D\u5B9A\u3002",
    line2: "\u9019\u53EF\u80FD\u9020\u6210\u60A8\u756B\u5E03\u4E2D <bold>\u6587\u5B57\u5143\u7D20</bold> \u7684\u7570\u5E38\u3002",
    line3: "\u6211\u5011\u5F37\u70C8\u5EFA\u8B70\u60A8\u95DC\u9589\u6B64\u8A2D\u5B9A\u3002\u60A8\u53EF\u4EE5\u4F9D\u7167 <link>\u9019\u4E9B\u6B65\u9A5F</link> \u4F86\u9032\u884C\u3002",
    line4: "\u82E5\u95DC\u9589\u6B64\u8A2D\u5B9A\u4E26\u672A\u4FEE\u5FA9\u6587\u5B57\u5143\u7D20\u7684\u986F\u793A\u554F\u984C\uFF0C\u8ACB\u56DE\u5831\u65BC\u6211\u5011 GitHub \u4E0A\u7684 <issueLink>issue</issueLink>\uFF0C\u6216\u5728 <discordLink>Discord</discordLink> \u4E0A\u544A\u8A34\u6211\u5011\u3002"
  },
  libraryElementTypeError: {
    embeddable: "\u53EF\u5D4C\u5165\u5143\u7D20\u7121\u6CD5\u52A0\u5165\u8CC7\u6599\u5EAB",
    iframe: "IFrame \u5143\u7D20\u7121\u6CD5\u52A0\u5165\u8CC7\u6599\u5EAB",
    image: "\u5373\u5C07\u652F\u63F4\u52A0\u5165\u5716\u7247\u81F3\u8CC7\u6599\u5EAB\uFF01"
  },
  asyncPasteFailedOnRead: "\u7121\u6CD5\u8CBC\u4E0A\uFF08\u7121\u6CD5\u7531\u7CFB\u7D71\u526A\u8CBC\u7C3F\u8B80\u5165\uFF09",
  asyncPasteFailedOnParse: "\u7121\u6CD5\u8CBC\u4E0A",
  copyToSystemClipboardFailed: "\u7121\u6CD5\u8907\u88FD\u81F3\u526A\u8CBC\u7C3F"
};
var toolBar = {
  selection: "\u9078\u53D6",
  image: "\u63D2\u5165\u5716\u7247",
  rectangle: "\u9577\u65B9\u5F62",
  diamond: "\u83F1\u5F62",
  ellipse: "\u6A62\u5713",
  arrow: "\u7BAD\u982D",
  line: "\u7DDA\u689D",
  freedraw: "\u7E6A\u5716",
  text: "\u6587\u5B57",
  library: "\u8CC7\u6599\u5EAB",
  lock: "\u53EF\u9023\u7E8C\u4F7F\u7528\u9078\u53D6\u7684\u5DE5\u5177",
  penMode: "\u7B46\u6A21\u5F0F - \u907F\u514D\u89F8\u6478",
  link: "\u70BA\u6240\u9078\u7684\u5F62\u72C0\u589E\u52A0\b/\u66F4\u65B0\u9023\u7D50",
  eraser: "\u6A61\u76AE\u64E6",
  frame: "\u6846\u67B6\u5DE5\u5177",
  magicframe: "\u7DDA\u6846\u7A3F\u8F49\u70BA\u7A0B\u5F0F\u78BC",
  embeddable: "\u5D4C\u5165\u7DB2\u7AD9",
  laser: "\u96F7\u5C04\u7B46",
  hand: "\u624B\u5F62\uFF08\u5E73\u79FB\u5DE5\u5177\uFF09",
  extraTools: "\u66F4\u591A\u5DE5\u5177",
  mermaidToExcalidraw: "Mermaid \u81F3 Excalidraw",
  magicSettings: "AI \u8A2D\u5B9A"
};
var headings = {
  canvasActions: "canvas \u52D5\u4F5C",
  selectedShapeActions: "\u9078\u53D6\u5716\u5F62\u52D5\u4F5C",
  shapes: "\u5F62\u72C0"
};
var hints = {
  canvasPanning: "\u82E5\u8981\u79FB\u52D5\u756B\u5E03\uFF0C\u8ACB\u5728\u62D6\u66F3\u6642\u6309\u4F4F\u6ED1\u9F20\u6EFE\u8F2A\u6216\u7A7A\u767D\u9375\uFF0C\u6216\u4F7F\u7528\u624B\u5F62\u5DE5\u5177",
  linearElement: "\u9EDE\u64CA\u4EE5\u7E6A\u88FD\u591A\u9EDE\u66F2\u7DDA\uFF1B\u6216\u62D6\u66F3\u4EE5\u7E6A\u88FD\u76F4\u7DDA",
  freeDraw: "\u9EDE\u64CA\u4E26\u62D6\u66F3\u4F86\u7E6A\u5716\uFF0C\u653E\u958B\u5373\u7D50\u675F",
  text: "\u63D0\u793A\uFF1A\u4EA6\u53EF\u4F7F\u7528\u9078\u53D6\u5DE5\u5177\u5728\u4EFB\u4F55\u5730\u65B9\u96D9\u64CA\u4F86\u52A0\u5165\u6587\u5B57",
  embeddable: "\u9EDE\u64CA\u4E26\u62D6\u79FB\u4EE5\u5EFA\u7ACB\u5D4C\u5165\u7DB2\u7AD9",
  text_selected: "\u96D9\u64CA\u6ED1\u9F20\u6216\u6309 Enter \u4EE5\u7DE8\u8F2F\u6587\u5B57",
  text_editing: "\u6309\u8DF3\u812B\u9375\u6216 Ctrl \u6216 Cmd + Enter \u4EE5\u7D50\u675F\u7DE8\u8F2F",
  linearElementMulti: "\u6309\u4E0B Escape \u6216 Enter \u4EE5\u7D50\u675F\u7E6A\u88FD",
  lockAngle: "\u6309\u4F4F SHIFT \u53EF\u9650\u5236\u65CB\u8F49\u89D2\u5EA6",
  resize: "\u7E2E\u653E\u6642\u6309\u4F4F Shift \u53EF\u4FDD\u6301\u539F\u6BD4\u4F8B\u7E2E\u653E\uFF1B\\n\u6309\u4F4F Alt \u53EF\u7531\u4E2D\u5FC3\u9EDE\u9032\u884C\u7E2E\u653E",
  resizeImage: "\u6309\u4F4F SHIFT \u53EF\u4EFB\u610F\u7E2E\u653E\uFF0C\u6309\u4F4F ALT \u53EF\u7531\u4E2D\u592E\u7E2E\u653E\u3002",
  rotate: "\u65CB\u8F49\u6642\u6309\u4F4F Shift \u53EF\u9650\u5236\u65CB\u8F49\u89D2\u5EA6",
  lineEditor_info: "\u6309\u4F4F Ctrl \u6216 Cmd \u4E26\u96D9\u64CA\u6216\u6309\u4F4F Ctrl \u6216 Cmd + Enter \u4F86\u7DE8\u8F2F\u63A7\u5236\u9EDE",
  lineEditor_pointSelected: "\u6309\u4E0B Delete \u53EF\u79FB\u9664\u9328\u9EDE\uFF1BCtrl \u6216 Cmd + D \u53EF\u8907\u88FD\uFF1B\u6216\u53EF\u62D6\u66F3\u4F86\u79FB\u52D5",
  lineEditor_nothingSelected: "\u9078\u64C7\u8981\u7DE8\u8F2F\u7684\u9328\u9EDE\uFF08\u6309\u4F4F SHIFT \u53EF\u591A\u9078\uFF09\uFF0C\n\u6216\u6309\u4F4F Alt \u4E26\u9EDE\u64CA\u4EE5\u589E\u52A0\u65B0\u9328\u9EDE\u3002",
  placeImage: "\u9EDE\u64CA\u4EE5\u653E\u7F6E\u5716\u7247\uFF0C\u6216\u9EDE\u64CA\u4E26\u62D6\u66F3\u4EE5\u624B\u52D5\u8ABF\u6574\u5176\u5C3A\u5BF8\u3002",
  publishLibrary: "\u767C\u5E03\u500B\u4EBA\u8CC7\u6599\u5EAB",
  bindTextToElement: "\u6309\u4E0B Enter \u4EE5\u52A0\u5165\u6587\u5B57\u3002",
  deepBoxSelect: "\u6309\u4F4F Ctrl \u6216 Cmd \u4EE5\u6DF1\u5EA6\u9078\u53D6\u4E26\u907F\u514D\u62D6\u66F3",
  eraserRevert: "\u6309\u4F4F Alt \u4EE5\u53CD\u9078\u53D6\u5DF2\u6A19\u8A18\u5F85\u522A\u9664\u7684\u5143\u7D20",
  firefox_clipboard_write: '\u6B64\u529F\u80FD\u6709\u6A5F\u6703\u900F\u904E\u5C07 "dom.events.asyncClipboard.clipboardItem" \u8A2D\u5B9A\u70BA "true" \u4F86\u958B\u555F\u3002\n\u82E5\u8981\u8B8A\u66F4 Firefox \u700F\u89BD\u5668\u7684\u6B64\u8A2D\u5B9A\u503C\uFF0C\u8ACB\u81F3 "about:config" \u9801\u9762\u3002',
  disableSnapping: "\u6309\u4F4F Ctrl \u6216 Cmd \u4EE5\u7981\u7528\u5438\u9644"
};
var canvasError = {
  cannotShowPreview: "\u7121\u6CD5\u986F\u793A\u9810\u89BD",
  canvasTooBig: "\u756B\u5E03\u53EF\u80FD\u904E\u5927",
  canvasTooBigTip: "\u63D0\u793A\uFF1A\u53EF\u5617\u8A66\u5C07\u6700\u9060\u7684\u5143\u7D20\u79FB\u52D5\u81F3\u8F03\u96C6\u4E2D\u7684\u4F4D\u7F6E"
};
var errorSplash = {
  headingMain: "\u767C\u751F\u932F\u8AA4\uFF0C\u5617\u8A66<button>\u91CD\u65B0\u8F09\u5165\u9801\u9762\u3002</button>",
  clearCanvasMessage: "\u82E5\u91CD\u65B0\u8F09\u5165\u4ECD\u7121\u6CD5\u89E3\u6C7A\u554F\u984C\uFF0C\u5617\u8A66<button>\u6E05\u9664 canvas\u3002</button>",
  clearCanvasCaveat: "\u6B64\u52D5\u4F5C\u5C07\u9020\u6210\u76EE\u524D\u7684\u4F5C\u54C1\u88AB\u79FB\u9664\u3002",
  trackedToSentry: "\u6B64\u932F\u8AA4\u8207\u5176\u8B58\u5225\u78BC{{eventId}}\u5C07\u7531\u7CFB\u7D71\u8A18\u9304\u3002",
  openIssueMessage: "\u6211\u5011\u5C07\u8B39\u614E\u8655\u7406\uFF0C\u4F60\u7684\u4F5C\u54C1\u5167\u5BB9\u4E0D\u6703\u88AB\u5305\u542B\u5728\u932F\u8AA4\u5831\u544A\u4E2D\u3002\u82E5\u4F60\u7684\u4F5C\u54C1\u4E0D\u9700\u4FDD\u6301\u79C1\u5BC6\uFF0C\u8ACB\u8003\u616E\u4F7F\u7528\u6211\u5011\u7684<button>bug tracker\u3002</button>\u8ACB\u5C07\u4E0B\u5217\u8CC7\u8A0A\u8907\u88FD\u8CBC\u4E0A\u81F3 GitHub issue \u4E2D\u3002",
  sceneContent: "\u4F5C\u54C1\u5167\u5BB9\uFF1A"
};
var roomDialog = {
  desc_intro: "\u4F60\u53EF\u4EE5\u9080\u8ACB\u5176\u4ED6\u4EBA\u4E00\u8D77\u5354\u4F5C\u76EE\u524D\u7684\u4F5C\u54C1\u3002",
  desc_privacy: "\u9023\u7DDA\u4F7F\u7528 end-to-end \u52A0\u5BC6\u6545\u7121\u9808\u64D4\u5FC3\u4F5C\u54C1\u7684\u5B89\u5168\u6027\u3002\u5373\u4F7F\u662F\u6211\u5011\u7684\u4F3A\u670D\u5668\u4E5F\u7121\u6CD5\u53D6\u5F97\u5176\u5167\u5BB9\u3002",
  button_startSession: "\u958B\u59CB\u9023\u7DDA",
  button_stopSession: "\u505C\u6B62\u9023\u7DDA",
  desc_inProgressIntro: "\u5373\u6642\u5354\u4F5C\u9023\u7DDA\u6B63\u5728\u9032\u884C\u4E2D\u3002",
  desc_shareLink: "\u5C07\u6B64\u9023\u7D50\u5206\u4EAB\u7D66\u6B32\u5354\u4F5C\u7684\u5C0D\u8C61\uFF1A",
  desc_exitSession: "\u505C\u6B62\u9023\u7DDA\u5C07\u4E2D\u65B7\u4F60\u8207\u5354\u4F5C\u6703\u8B70\u5BA4\u7684\u9023\u7D50\uFF0C\u4F46\u4F60\u4ECD\u53EF\u65BC\u672C\u6A5F\u7DE8\u8F2F\u6B64\u4F5C\u54C1\u3002\u610F\u6307\u505C\u6B62\u9023\u7DDA\u5F8C\u4F60\u7684\u7DE8\u8F2F\u4E0D\u6703\u88AB\u5148\u524D\u5171\u540C\u5354\u4F5C\u7684\u4EBA\u770B\u898B\uFF0C\u4E14\u4ED6\u5011\u53EF\u7E7C\u7E8C\u5171\u540C\u5354\u4F5C\u53E6\u4E00\u500B\u7248\u672C\u3002",
  shareTitle: "\u52A0\u5165 Excalidraw \u4E0A\u7684\u5373\u6642\u5354\u4F5C\u6703\u8B70\u5BA4"
};
var errorDialog = {
  title: "\u932F\u8AA4"
};
var exportDialog = {
  disk_title: "\u5132\u5B58\u81F3\u786C\u789F",
  disk_details: "\u5C07\u5834\u666F\u532F\u51FA\u70BA\u53EF\u4F9B\u532F\u5165\u4E4B\u6A94\u6848",
  disk_button: "\u5132\u5B58\u81F3\u6A94\u6848",
  link_title: "\u53EF\u5171\u4EAB\u9023\u7D50",
  link_details: "\u532F\u51FA\u70BA\u552F\u8B80\u9023\u7D50",
  link_button: "\u532F\u51FA\u70BA\u9023\u7D50",
  excalidrawplus_description: "\u5C07\u6B64\u5834\u666F\u5132\u5B58\u81F3\u4F60\u7684 Excalidraw+ \u5DE5\u4F5C\u5340",
  excalidrawplus_button: "\u8F38\u51FA",
  excalidrawplus_exportError: "\u76EE\u524D\u7121\u6CD5\u8F38\u51FA\u81F3 Excalidraw+"
};
var helpDialog = {
  blog: "\u95B1\u8B80\u90E8\u843D\u683C",
  click: "\u9EDE\u64CA",
  deepSelect: "\u6DF1\u5EA6\u9078\u53D6",
  deepBoxSelect: "\u5728\u5BB9\u5668\u5167\u6DF1\u5EA6\u9078\u53D6\u4E26\u907F\u514D\u62D6\u66F3",
  curvedArrow: "\u66F2\u7BAD\u982D",
  curvedLine: "\u66F2\u7DDA",
  documentation: "\u6587\u4EF6",
  doubleClick: "\u96D9\u64CA",
  drag: "\u62D6\u66F3",
  editor: "\u7DE8\u8F2F\u5668",
  editLineArrowPoints: "\u7DE8\u8F2F\u7DDA/\u7BAD\u982D\u63A7\u5236\u9EDE",
  editText: "\u7DE8\u8F2F\u6587\u5B57/\u589E\u52A0\u6A19\u7C64",
  github: "\u767C\u73FE\u7570\u5E38\uFF1F\u56DE\u5831\u554F\u984C",
  howto: "\u53C3\u7167\u6211\u5011\u7684\u8AAA\u660E",
  or: "\u6216",
  preventBinding: "\u907F\u514D\u7BAD\u865F\u9023\u7D50",
  tools: "\u5DE5\u5177",
  shortcuts: "\u9375\u76E4\u5FEB\u901F\u9375",
  textFinish: "\u5B8C\u6210\u7DE8\u8F2F\uFF08\u6587\u5B57\u7DE8\u8F2F\u5668\uFF09",
  textNewLine: "\u63DB\u884C\uFF08\u6587\u5B57\u7DE8\u8F2F\u5668\uFF09",
  title: "\u8AAA\u660E",
  view: "\u6AA2\u8996",
  zoomToFit: "\u653E\u5927\u81F3\u586B\u6EFF\u756B\u9762",
  zoomToSelection: "\u7E2E\u653E\u81F3\u9078\u53D6\u5340",
  toggleElementLock: "\u9396\u5B9A/\u89E3\u9396\u5DF2\u9078\u7684\u9805\u76EE",
  movePageUpDown: "\u5411\u4E0A/\u4E0B\u79FB\u52D5\u9801\u9762",
  movePageLeftRight: "\u5411\u5DE6/\u53F3\u79FB\u52D5\u9801\u9762"
};
var clearCanvasDialog = {
  title: "\u6E05\u9664\u756B\u5E03"
};
var publishDialog = {
  title: "\u767C\u5E03\u8CC7\u6599\u5EAB",
  itemName: "\u9805\u76EE\u540D\u7A31",
  authorName: "\u4F5C\u8005\u540D\u7A31",
  githubUsername: "GitHub \u5E33\u865F",
  twitterUsername: "Twitter \u5E33\u865F",
  libraryName: "\u8CC7\u6599\u5EAB\u540D\u7A31",
  libraryDesc: "\u8CC7\u6599\u5EAB\u8AAA\u660E",
  website: "\u7DB2\u7AD9",
  placeholder: {
    authorName: "\u60A8\u7684\u540D\u7A31\u6216\u5E33\u865F",
    libraryName: "\u60A8\u7684\u8CC7\u6599\u5EAB\u540D\u7A31",
    libraryDesc: "\u63D0\u4F9B\u60A8\u7684\u8CC7\u6599\u5EAB\u8AAA\u660E\u4EE5\u5229\u4ED6\u4EBA\u7406\u89E3\u5176\u7528\u9014",
    githubHandle: "Github handle\uFF08\u9078\u586B\uFF09\uFF0C\u586B\u5BEB\u5F8C\u60A8\u53EF\u7DE8\u8F2F\u5DF2\u9001\u51FA\u5F85\u5BE9\u67E5\u7684\u8CC7\u6599\u5EAB",
    twitterHandle: "Twitter \u5E33\u865F\uFF08\u9078\u586B\uFF09\uFF0C\u586B\u5BEB\u5F8C\u82E5\u6211\u5011\u5728 Twitter \u63A8\u5EE3\u6642\u53EF\u63D0\u53CA\u60A8",
    website: "\u60A8\u500B\u4EBA\u7DB2\u7AD9\u6216\u5176\u4ED6\u7DB2\u7AD9\u7684\u9023\u7D50\uFF08\u9078\u586B\uFF09"
  },
  errors: {
    required: "\u5FC5\u586B",
    website: "\u8ACB\u8F38\u5165\u6709\u6548\u7684 URL"
  },
  noteDescription: "\u9001\u51FA\u60A8\u7684\u8CC7\u6599\u5EAB\u5F8C\u5C07\u88AB\u5305\u542B\u65BC<link>\u516C\u958B\u8CC7\u6599\u5EAB repository</link>\u4EE5\u5229\u4ED6\u4EBA\u5728\u5176\u7E6A\u5716\u4E2D\u4F7F\u7528\u3002",
  noteGuidelines: "\u8CC7\u6599\u5EAB\u9700\u5148\u7D93\u4EBA\u5DE5\u5BE9\u67E5\u3002\u8ACB\u95B1\u8B80<link>\u8AAA\u660E\u6587\u4EF6</link>\u518D\u9001\u51FA\u3002\u82E5\u9700\u6E9D\u901A\u8207\u4FEE\u6539\u6642\u8981\u900F\u904E GitHub \u5E33\u865F\u4F86\u9032\u884C\uFF0C\u4F46\u4E26\u975E\u5F37\u5236\u9700\u6C42\u3002",
  noteLicense: "\u9001\u51FA\u5373\u4EE3\u8868\u60A8\u540C\u610F\u6B64\u8CC7\u6599\u5EAB\u5C07\u767C\u5E03\u6642\u4F7F\u7528 <link>MIT \u6388\u6B0A\uFF0C</link>\u7C21\u55AE\u4F86\u8AAA\u662F\u6307\u4EFB\u4F55\u4EBA\u90FD\u80FD\u4E0D\u53D7\u9650\u5236\u7684\u4F7F\u7528\u3002",
  noteItems: "\u6BCF\u500B\u8CC7\u6599\u5EAB\u9805\u76EE\u90FD\u6709\u7368\u7ACB\u7684\u540D\u7A31\u6545\u53EF\u7BE9\u9078\u3002\u6703\u5305\u542B\u4E0B\u5217\u8CC7\u6599\u5EAB\u9805\u76EE\uFF1A",
  atleastOneLibItem: "\u8ACB\u9078\u64C7\u81F3\u5C11\u4E00\u9805\u8CC7\u6599\u5EAB\u9805\u76EE",
  republishWarning: "\u6CE8\u610F\uFF1A\u90E8\u5206\u9078\u53D6\u4E2D\u7684\u7269\u4EF6\u5148\u524D\u5DF2\u767C\u5E03/\u9001\u51FA\u904E\u3002\u5EFA\u8B70\u50C5\u5728\u8981\u66F4\u65B0\u73FE\u5B58\u8CC7\u6599\u5EAB\u6216\u5DF2\u9001\u51FA\u7684\u7269\u4EF6\u6642\u624D\u91CD\u65B0\u9001\u51FA\u9019\u4E9B\u7269\u4EF6\u3002"
};
var publishSuccessDialog = {
  title: "\u8CC7\u6599\u5EAB\u5DF2\u9001\u51FA",
  content: "\u611F\u8B1D {{authorName}} \u3002\u60A8\u7684\u8CC7\u6599\u5EAB\u5DF2\u9001\u51FA\u5F85\u5BE9\u67E5\u3002\u60A8\u53EF\u67E5\u770B\u76EE\u524D\u72C0\u614B<link>\u5728\u6B64</link>"
};
var confirmDialog = {
  resetLibrary: "\u91CD\u8A2D\u8CC7\u6599\u5EAB",
  removeItemsFromLib: "\u5F9E\u8CC7\u6599\u5EAB\u79FB\u9664\u6240\u9078\u7684\u9805\u76EE"
};
var imageExportDialog = {
  header: "\u532F\u51FA\u5716\u7247",
  label: {
    withBackground: "\u80CC\u666F",
    onlySelected: "\u50C5\u9078\u53D6\u7269\u4EF6",
    darkMode: "\u6DF1\u8272\u6A21\u5F0F",
    embedScene: "\u5D4C\u5165\u5834\u666F",
    scale: "\u7E2E\u653E\u6BD4\u4F8B",
    padding: "\u5167\u9593\u8DDD"
  },
  tooltip: {
    embedScene: "\u7528\u65BC\u56DE\u5FA9\u5834\u666F\u7684\u5834\u666F\u8CC7\u6599\u6703\u88AB\u5305\u542B\u5728\u8F38\u51FA\u7684 PNG/SVG \u6A94\u6848\u4E2D\u3002\n\u6703\u589E\u52A0\u8F38\u51FA\u7684\u6A94\u6848\u5927\u5C0F\u3002"
  },
  title: {
    exportToPng: "\u8F38\u51FA\u6210 PNG",
    exportToSvg: "\u8F38\u51FA\u6210 SVG",
    copyPngToClipboard: "\u8907\u88FD PNG \u81F3\u526A\u8CBC\u7C3F"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "\u8907\u88FD\u81F3\u526A\u8CBC\u7C3F"
  }
};
var encrypted = {
  tooltip: "\u4F60\u7684\u4F5C\u54C1\u5DF2\u4F7F\u7528 end-to-end \u65B9\u5F0F\u52A0\u5BC6\uFF0CExcalidraw \u7684\u4F3A\u670D\u5668\u4E5F\u7121\u6CD5\u53D6\u5F97\u5176\u5167\u5BB9\u3002",
  link: "Excalidraw \u7AEF\u5230\u7AEF\u52A0\u5BC6\u7684\u76F8\u95DC\u90E8\u843D\u683C\u6587\u7AE0"
};
var stats = {
  angle: "\u89D2\u5EA6",
  element: "\u5143\u7D20",
  elements: "\u5143\u7D20",
  height: "\u9AD8\u5EA6",
  scene: "\u5834\u666F",
  selected: "\u5DF2\u9078",
  storage: "\u5132\u5B58",
  title: "\u8A73\u7D30\u7D71\u8A08",
  total: "\u5408\u8A08",
  version: "\u7248\u672C",
  versionCopy: "\u9EDE\u64CA\u8907\u88FD",
  versionNotAvailable: "\u7121\u6CD5\u53D6\u5F97\u7248\u672C",
  width: "\u5BEC\u5EA6"
};
var toast = {
  addedToLibrary: "\u52A0\u5165\u8CC7\u6599\u5EAB",
  copyStyles: "\u5DF2\u8907\u88FD\u6A23\u5F0F",
  copyToClipboard: "\u8907\u88FD\u81F3\u526A\u8CBC\u7C3F\u3002",
  copyToClipboardAsPng: "\u4EE5 PNG \u683C\u5F0F\u5C07 {{exportSelection}} \u8907\u88FD\u81F3\u526A\u8CBC\u7C3F\n({{exportColorScheme}})",
  fileSaved: "\u5DF2\u5132\u5B58\u6A94\u6848\u3002",
  fileSavedToFilename: "\u5132\u5B58\u70BA {filename}",
  canvas: "\u756B\u5E03",
  selection: "\u5DF2\u9078\u9805\u76EE",
  pasteAsSingleElement: "\u4F7F\u7528 {{shortcut}} \u4EE5\u505A\u70BA\u55AE\u4E00\u7269\u4EF6\u8CBC\u4E0A\uFF0C\n\u6216\u8CBC\u4E0A\u81F3\u73FE\u6709\u7684\u6587\u5B57\u7DE8\u8F2F\u5668",
  unableToEmbed: "\u76EE\u524D\u4E0D\u5141\u8A31\u5D4C\u5165\u6B64\u7DB2\u5740\u3002\u60A8\u53EF\u81F3 GitHub \u63D0\u51FA issue \u4EE5\u8981\u6C42\u5C07\u6B64\u7DB2\u5740\u52A0\u5165\u5408\u683C\u540D\u55AE\u3002",
  unrecognizedLinkFormat: "\u60A8\u5D4C\u5165\u7684\u9023\u7D50\u683C\u5F0F\u4E0D\u7B26\u3002\u8ACB\u5617\u8A66\u8CBC\u5165\u539F\u7DB2\u7AD9\u6240\u63D0\u4F9B\u7684\u300C\u5D4C\u5165\u300D\u5B57\u4E32\u3002"
};
var colors = {
  transparent: "\u900F\u660E",
  black: "\u9ED1",
  white: "\u767D",
  red: "\u7D05",
  pink: "\u7C89\u7D05",
  grape: "\u6DF1\u7D2B",
  violet: "\u85CD\u7D2B",
  gray: "\u7070",
  blue: "\u85CD",
  cyan: "\u9752",
  teal: "\u85CD\u7DA0",
  green: "\u7DA0",
  yellow: "\u9EC3",
  orange: "\u6A58",
  bronze: "\u9285"
};
var welcomeScreen = {
  app: {
    center_heading: "\u6240\u6709\u8CC7\u6599\u7686\u5DF2\u5728\u700F\u89BD\u5668\u4E2D\u5132\u5B58\u65BC\u672C\u6A5F",
    center_heading_plus: "\u60A8\u662F\u5426\u662F\u8981\u524D\u5F80 Excalidraw+ \uFF1F",
    menuHint: "\u8F38\u51FA\u3001\u504F\u597D\u8A2D\u5B9A\u3001\u8A9E\u8A00..."
  },
  defaults: {
    menuHint: "\u8F38\u51FA\u3001\u504F\u597D\u8A2D\u5B9A\u53CA\u5176\u4ED6...",
    center_heading: "\u5716\u8868\u3002\u88FD\u4F5C\u3002\u8D85\u7C21\u55AE\u3002",
    toolbarHint: "\u9078\u500B\u5DE5\u5177\u958B\u59CB\u756B\u5716\u5427\uFF01",
    helpHint: "\u5FEB\u901F\u9375\u8207\u8AAA\u660E"
  }
};
var colorPicker = {
  mostUsedCustomColors: "\u6700\u5E38\u4F7F\u7528\u7684\u81EA\u8A02\u984F\u8272",
  colors: "\u984F\u8272",
  shades: "\u6F38\u8B8A\u8272",
  hexCode: "Hex \u78BC",
  noShades: "\u6C92\u6709\u6B64\u984F\u8272\u7684\u6F38\u8B8A\u8272"
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "\u532F\u51FA\u70BA\u5716\u7247",
      button: "\u532F\u51FA\u70BA\u5716\u7247",
      description: "\u5C07\u5834\u666F\u532F\u51FA\u70BA\u53EF\u4F9B\u532F\u5165\u7684\u5716\u7247\u6A94\u6848"
    },
    saveToDisk: {
      title: "\u5132\u5B58\u81F3\u786C\u789F",
      button: "\u5132\u5B58\u81F3\u786C\u789F",
      description: "\u5C07\u5834\u666F\u532F\u51FA\u70BA\u53EF\u4F9B\u532F\u5165\u7684\u6A94\u6848"
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "\u532F\u51FA\u81F3 Excalidraw+",
      description: "\u5C07\u6B64\u5834\u666F\u5132\u5B58\u81F3\u60A8\u7684 Excalidraw+ \u5DE5\u4F5C\u5340"
    }
  },
  modal: {
    loadFromFile: {
      title: "\u5F9E\u6A94\u6848\u8F09\u5165",
      button: "\u5F9E\u6A94\u6848\u8F09\u5165",
      description: "\u5F9E\u6A94\u6848\u8F09\u5165\u5C07<bold>\u53D6\u4EE3\u60A8\u76EE\u524D\u7684\u5167\u5BB9</bold>\u3002<br></br>\u53EF\u5148\u4F7F\u7528\u4E0B\u65B9\u7684\u9078\u9805\u5099\u4EFD\u60A8\u7684\u7E6A\u5716\u3002"
    },
    shareableLink: {
      title: "\u5F9E\u9023\u7D50\u8F09\u5165",
      button: "\u53D6\u4EE3\u6211\u7684\u5167\u5BB9",
      description: "\u8F09\u5165\u5916\u90E8\u7E6A\u5716\u5C07<bold>\u53D6\u4EE3\u60A8\u76EE\u524D\u7684\u5167\u5BB9</bold>\u3002<br></br>\u53EF\u5148\u4F7F\u7528\u4E0B\u65B9\u7684\u9078\u9805\u5099\u4EFD\u60A8\u7684\u7E6A\u5716\u3002"
    }
  }
};
var mermaid = {
  title: "Mermaid \u81F3 Excalidraw",
  button: "\u63D2\u5165",
  description: "\u76EE\u524D\u50C5\u652F\u63F4 <flowchartLink>Flowchart</flowchartLink> \u3001 <sequenceLink>Sequence</sequenceLink> \u53CA <classLink>Class </classLink> \u5716\u8868\u3002\u5176\u9918\u6A94\u6848\u985E\u578B\u5728 Excalidraw \u5C07\u6703\u4EE5\u5716\u50CF\u5448\u73FE\u3002",
  syntax: "Mermaid \u8A9E\u6CD5",
  preview: "\u9810\u89BD"
};
var zh_TW_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  zh_TW_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=zh-TW-U5VF4CCU.js.map
