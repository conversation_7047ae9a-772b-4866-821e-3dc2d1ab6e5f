{"version": 3, "sources": ["../../../locales/gl-ES.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"<PERSON>egar\",\n    \"pasteAsPlaintext\": \"Pegar coma texto sen formato\",\n    \"pasteCharts\": \"<PERSON>egar gráficos\",\n    \"selectAll\": \"Seleccionar todo\",\n    \"multiSelect\": \"Engadir elemento á selección\",\n    \"moveCanvas\": \"Mover o lenzo\",\n    \"cut\": \"Cortar\",\n    \"copy\": \"Copiar\",\n    \"copyAsPng\": \"Copiar no portapapeis como PNG\",\n    \"copyAsSvg\": \"Copiar no portapapeis como SVG\",\n    \"copyText\": \"Copia no portapapeis como texto\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Traer cara adiante\",\n    \"sendToBack\": \"Enviar cara atrás\",\n    \"bringToFront\": \"Traer á fronte\",\n    \"sendBackward\": \"Enviar ao fondo\",\n    \"delete\": \"Borrar\",\n    \"copyStyles\": \"Copiar estilo\",\n    \"pasteStyles\": \"Pegar estilo\",\n    \"stroke\": \"Trazo\",\n    \"background\": \"Fondo\",\n    \"fill\": \"Recheo\",\n    \"strokeWidth\": \"Largo do trazo\",\n    \"strokeStyle\": \"Estilo do trazo\",\n    \"strokeStyle_solid\": \"Sólido\",\n    \"strokeStyle_dashed\": \"Liña de trazos\",\n    \"strokeStyle_dotted\": \"Liña de puntos\",\n    \"sloppiness\": \"Estilo de trazo\",\n    \"opacity\": \"Opacidade\",\n    \"textAlign\": \"Aliñar texto\",\n    \"edges\": \"Bordos\",\n    \"sharp\": \"Agudo\",\n    \"round\": \"Redondo\",\n    \"arrowheads\": \"Puntas de frecha\",\n    \"arrowhead_none\": \"Ningunha\",\n    \"arrowhead_arrow\": \"Frecha\",\n    \"arrowhead_bar\": \"Barra\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Triángulo\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Tamaño da fonte\",\n    \"fontFamily\": \"Tipo de fonte\",\n    \"addWatermark\": \"Engadir \\\"Feito con Excalidraw\\\"\",\n    \"handDrawn\": \"Debuxado a man\",\n    \"normal\": \"Normal\",\n    \"code\": \"Código\",\n    \"small\": \"Pequeno\",\n    \"medium\": \"Mediano\",\n    \"large\": \"Grande\",\n    \"veryLarge\": \"Moi grande\",\n    \"solid\": \"Sólido\",\n    \"hachure\": \"Folleto\",\n    \"zigzag\": \"Zigzag\",\n    \"crossHatch\": \"Raiado transversal\",\n    \"thin\": \"Estreito\",\n    \"bold\": \"Groso\",\n    \"left\": \"Esquerda\",\n    \"center\": \"Centrado\",\n    \"right\": \"Dereita\",\n    \"extraBold\": \"Moi groso\",\n    \"architect\": \"Arquitecto\",\n    \"artist\": \"Artista\",\n    \"cartoonist\": \"Caricatura\",\n    \"fileTitle\": \"Nome do arquivo\",\n    \"colorPicker\": \"Selector de cor\",\n    \"canvasColors\": \"Usado en lenzo\",\n    \"canvasBackground\": \"Fondo do lenzo\",\n    \"drawingCanvas\": \"Lenzo de debuxo\",\n    \"layers\": \"Capas\",\n    \"actions\": \"Accións\",\n    \"language\": \"Idioma\",\n    \"liveCollaboration\": \"Colaboración en directo...\",\n    \"duplicateSelection\": \"Duplicar\",\n    \"untitled\": \"Sen título\",\n    \"name\": \"Nome\",\n    \"yourName\": \"O teu nome\",\n    \"madeWithExcalidraw\": \"Feito con Excalidraw\",\n    \"group\": \"Agrupar selección\",\n    \"ungroup\": \"Desagrupar selección\",\n    \"collaborators\": \"Colaboradores\",\n    \"showGrid\": \"Mostrar cuadrícula\",\n    \"addToLibrary\": \"Engadir á biblioteca\",\n    \"removeFromLibrary\": \"Eliminar da biblioteca\",\n    \"libraryLoadingMessage\": \"Cargando biblioteca…\",\n    \"libraries\": \"Explorar bibliotecas\",\n    \"loadingScene\": \"Cargando escena…\",\n    \"align\": \"Aliñamento\",\n    \"alignTop\": \"Aliñamento superior\",\n    \"alignBottom\": \"Aliñamento inferior\",\n    \"alignLeft\": \"Aliñar a esquerda\",\n    \"alignRight\": \"Aliñar a dereita\",\n    \"centerVertically\": \"Centrar verticalmente\",\n    \"centerHorizontally\": \"Centrar horizontalmente\",\n    \"distributeHorizontally\": \"Distribuír horizontalmente\",\n    \"distributeVertically\": \"Distribuír verticalmente\",\n    \"flipHorizontal\": \"Virar horizontalmente\",\n    \"flipVertical\": \"Virar verticalmente\",\n    \"viewMode\": \"Modo de visualización\",\n    \"share\": \"Compartir\",\n    \"showStroke\": \"Mostrar selector de cores do trazo\",\n    \"showBackground\": \"Mostrar selector de cores do fondo\",\n    \"toggleTheme\": \"Alternar tema\",\n    \"personalLib\": \"Biblioteca Persoal\",\n    \"excalidrawLib\": \"Biblioteca Excalidraw\",\n    \"decreaseFontSize\": \"Diminuír tamaño da fonte\",\n    \"increaseFontSize\": \"Aumentar o tamaño da fonte\",\n    \"unbindText\": \"Desvincular texto\",\n    \"bindText\": \"Ligar o texto ao contedor\",\n    \"createContainerFromText\": \"Envolver o texto nun contedor\",\n    \"link\": {\n      \"edit\": \"Editar ligazón\",\n      \"editEmbed\": \"\",\n      \"create\": \"Crear ligazón\",\n      \"createEmbed\": \"\",\n      \"label\": \"Ligazón\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Editar liña\",\n      \"exit\": \"Saír do editor de liñas\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Bloquear\",\n      \"unlock\": \"Desbloquear\",\n      \"lockAll\": \"Bloquear todo\",\n      \"unlockAll\": \"Desbloquear todo\"\n    },\n    \"statusPublished\": \"Publicado\",\n    \"sidebarLock\": \"Manter a barra lateral aberta\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Aínda non hai elementos engadidos...\",\n    \"hint_emptyLibrary\": \"Seleccione un elemento no lenzo para engadilo aquí, ou instale unha biblioteca dende o repositorio público, como se detalla a continuación.\",\n    \"hint_emptyPrivateLibrary\": \"Seleccione un elemento do lenzo para engadilo aquí.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Limpar o lenzo\",\n    \"exportJSON\": \"Exportar a arquivo\",\n    \"exportImage\": \"Exportar imaxe...\",\n    \"export\": \"Gardar en...\",\n    \"copyToClipboard\": \"Copiar ao portapapeis\",\n    \"save\": \"Gardar no ficheiro actual\",\n    \"saveAs\": \"Gardar como\",\n    \"load\": \"Abrir\",\n    \"getShareableLink\": \"Obter unha ligazón que se poida compartir\",\n    \"close\": \"Pechar\",\n    \"selectLanguage\": \"Seleccionar idioma\",\n    \"scrollBackToContent\": \"Volver ao contido\",\n    \"zoomIn\": \"Ampliar\",\n    \"zoomOut\": \"Reducir\",\n    \"resetZoom\": \"Reiniciar zoom\",\n    \"menu\": \"Menú\",\n    \"done\": \"Feito\",\n    \"edit\": \"Editar\",\n    \"undo\": \"Desfacer\",\n    \"redo\": \"Refacer\",\n    \"resetLibrary\": \"Reiniciar biblioteca\",\n    \"createNewRoom\": \"Crear nova sala\",\n    \"fullScreen\": \"Pantalla completa\",\n    \"darkMode\": \"Modo escuro\",\n    \"lightMode\": \"Modo claro\",\n    \"zenMode\": \"Modo zen\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Saír do modo zen\",\n    \"cancel\": \"Cancelar\",\n    \"clear\": \"Limpar\",\n    \"remove\": \"Eliminar\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Publicar\",\n    \"submit\": \"Enviar\",\n    \"confirm\": \"Confirmar\",\n    \"embeddableInteractionButton\": \"Faga clic para interactuar\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Isto limpará todo o lenzo. Estás seguro?\",\n    \"couldNotCreateShareableLink\": \"Non se puido crear unha ligazón para compartir.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Non se puido crear a ligazón para compartir: a escena é demasiado grande\",\n    \"couldNotLoadInvalidFile\": \"Non se puido cargar o ficheiro non válido\",\n    \"importBackendFailed\": \"A importación dende o backend fallou.\",\n    \"cannotExportEmptyCanvas\": \"Non se pode exportar un lenzo baleiro.\",\n    \"couldNotCopyToClipboard\": \"Non se puido copiar ao portapapeis.\",\n    \"decryptFailed\": \"Non se poideron descifrar os datos.\",\n    \"uploadedSecurly\": \"A carga foi asegurada con cifrado de extremo a extremo, o que significa que o servidor de Excalidraw e terceiros non poden ler o contido.\",\n    \"loadSceneOverridePrompt\": \"A carga dun debuxo externo substituirá o contido existente. Desexa continuar?\",\n    \"collabStopOverridePrompt\": \"Deter a sesión, sobrescribirá o seu debuxo local previamente almacenado. Está seguro?\\n\\n(Se quere manter o seu debuxo local, simplemente peche a lapela do navegador.)\",\n    \"errorAddingToLibrary\": \"Non se puido engadir o elemento á biblioteca\",\n    \"errorRemovingFromLibrary\": \"Non se puido eliminar o elemento da biblioteca\",\n    \"confirmAddLibrary\": \"Isto engadirá {{numShapes}} forma(s) a túa biblioteca. Estás seguro?\",\n    \"imageDoesNotContainScene\": \"Esta imaxe non parece conter ningún dato da escena. Activou a inserción de escenas durante a exportación?\",\n    \"cannotRestoreFromImage\": \"Non se puido restaurar a escena dende este arquivo de imaxe\",\n    \"invalidSceneUrl\": \"Non se puido importar a escena dende a URL proporcionada. Ou ben está malformada ou non contén un JSON con información válida para Excalidraw.\",\n    \"resetLibrary\": \"Isto limpará a súa biblioteca. Está seguro?\",\n    \"removeItemsFromsLibrary\": \"Eliminar {{count}} elemento(s) da biblioteca?\",\n    \"invalidEncryptionKey\": \"A clave de cifrado debe ter 22 caracteres. A colaboración en directo está desactivada.\",\n    \"collabOfflineWarning\": \"Non hai conexión a Internet dispoñible.\\nOs teus cambios non serán gardados!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Tipo de ficheiro non soportado.\",\n    \"imageInsertError\": \"Non se puido inserir a imaxe. Probe de novo máis tarde...\",\n    \"fileTooBig\": \"O ficheiro é demasiado grande. O tamaño máximo permitido é {{maxSize}}.\",\n    \"svgImageInsertError\": \"Non se puido inserir como imaxe SVG. O marcado SVG semella inválido.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"SVG inválido.\",\n    \"cannotResolveCollabServer\": \"Non se puido conectar ao servidor de colaboración. Por favor recargue a páxina e probe de novo.\",\n    \"importLibraryError\": \"Non se puido cargar a biblioteca\",\n    \"collabSaveFailed\": \"Non se puido gardar na base de datos. Se o problema persiste, deberías gardar o teu arquivo de maneira local para asegurarte de non perdelo teu traballo.\",\n    \"collabSaveFailed_sizeExceeded\": \"Non se puido gardar na base de datos, o lenzo semella demasiado grande. Deberías gardar o teu arquivo de maneira local para asegurarte de non perdelo teu traballo.\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Selección\",\n    \"image\": \"Inserir imaxe\",\n    \"rectangle\": \"Rectángulo\",\n    \"diamond\": \"Diamante\",\n    \"ellipse\": \"Elipse\",\n    \"arrow\": \"Frecha\",\n    \"line\": \"Liña\",\n    \"freedraw\": \"Debuxar\",\n    \"text\": \"Texto\",\n    \"library\": \"Biblioteca\",\n    \"lock\": \"Manter a ferramenta seleccionada activa despois de debuxar\",\n    \"penMode\": \"Modo lapis - evitar o contacto\",\n    \"link\": \"Engadir/ Actualizar ligazón para a forma seleccionada\",\n    \"eraser\": \"Goma de borrar\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"Inserir na web\",\n    \"laser\": \"Punteiro láser\",\n    \"hand\": \"Man (ferramenta de desprazamento)\",\n    \"extraTools\": \"Máis ferramentas\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Accións do lenzo\",\n    \"selectedShapeActions\": \"Accións da forma seleccionada\",\n    \"shapes\": \"Formas\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Para mover o lenzo, manteña pulsada a roda do rato ou a barra de espazo mentres arrastra, ou utilice a ferramenta da man\",\n    \"linearElement\": \"Faga clic para iniciar varios puntos, arrastre para unha sola liña\",\n    \"freeDraw\": \"Fai clic e arrastra, solta cando acabes\",\n    \"text\": \"Consello: tamén podes engadir texto facendo dobre-clic en calquera lugar coa ferramenta de selección\",\n    \"embeddable\": \"Faga clic e arrastre para crear un sitio web embebido\",\n    \"text_selected\": \"Dobre-clic ou prema ENTER para editar o texto\",\n    \"text_editing\": \"Prema Escape ou CtrlOrCmd+ENTER para finalizar a edición\",\n    \"linearElementMulti\": \"Faga clic no último punto ou prema Escape ou Enter para rematar\",\n    \"lockAngle\": \"Pode reducir o ángulo mantendo SHIFT\",\n    \"resize\": \"Pode reducir as proporcións mantendo SHIFT mentres axusta o tamaño,\\nmanteña ALT para axustalo dende o centro\",\n    \"resizeImage\": \"Pode axustar o tamaño libremente mantendo SHIFT,\\nmanteña ALT para axustalo dende o centro\",\n    \"rotate\": \"Podes reducir os ángulos mantendo SHIFT mentres os rotas\",\n    \"lineEditor_info\": \"Manteña pulsado CtrlOrCmd e faga dobre clic ou prema CtrlOrCmd + Enter para editar puntos\",\n    \"lineEditor_pointSelected\": \"Prema Suprimir para eliminar o(s) punto(s)\\nCtrlOrCmd+D para duplicalos, ou arrastre para movelos\",\n    \"lineEditor_nothingSelected\": \"Seleccione un punto para editar (manteña pulsado SHIFT para selección múltiple),\\nou manteña pulsado Alt e faga clic para engadir novos puntos\",\n    \"placeImage\": \"Faga clic para colocar a imaxe, ou faga clic e arrastre para establecer o seu tamaño manualmente\",\n    \"publishLibrary\": \"Publica a túa propia biblioteca\",\n    \"bindTextToElement\": \"Prema a tecla enter para engadir texto\",\n    \"deepBoxSelect\": \"Manteña pulsado CtrlOrCmd para seleccionar en profundidade e evitar o arrastre\",\n    \"eraserRevert\": \"Manteña pulsado Alt para reverter os elementos marcados para a súa eliminación\",\n    \"firefox_clipboard_write\": \"Esta función pódese activar establecendo a opción \\\"dom.events.asyncClipboard.clipboardItem\\\" a \\\"true\\\". Para cambiar as opcións do navegador en Firefox, visita a páxina \\\"about:config\\\".\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Non se pode mostrar a vista previa\",\n    \"canvasTooBig\": \"Pode que o lenzo sexa demasiado grande.\",\n    \"canvasTooBigTip\": \"Consello: Probe a acercar un pouco os elementos máis afastados.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Atopouse un erro. Probe <button>recargando a páxina.</button>\",\n    \"clearCanvasMessage\": \"Se recargar non funcionou, probe <button>limpando o lenzo.</button>\",\n    \"clearCanvasCaveat\": \" Isto resultará nunha perda do seu traballo \",\n    \"trackedToSentry\": \"O erro con identificador {{eventId}} foi rastrexado no noso sistema.\",\n    \"openIssueMessage\": \"Fomos moi cautelosos de non incluír a información da súa escena no erro. Se a súa escena non é privada, por favor, considere o seguimento do noso <button>rastrexador de erros.</button> Por favor inclúa a seguinte información copiándoa e pegándoa na issue de Github.\",\n    \"sceneContent\": \"Contido da escena:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Podes invitar xente a colaborar contigo na túa escena actual.\",\n    \"desc_privacy\": \"Non te preocupes, a sesión usa cifrado de punto a punto, polo que calquera cousa que debuxes mantense privada. Nin tan sequera o noso servidor será capaz de ver o que fas.\",\n    \"button_startSession\": \"Comezar sesión\",\n    \"button_stopSession\": \"Rematar sesión\",\n    \"desc_inProgressIntro\": \"A sesión de colaboración en directo está agora en progreso.\",\n    \"desc_shareLink\": \"Comparte esta ligazón con calquera que queiras colaborar:\",\n    \"desc_exitSession\": \"Deter a sesión desconectarao da sala, pero poderá seguir traballando coa escena de maneira local. Teña en conta que isto non afectará a outras persoas, que poderán seguir colaborando na súa versión.\",\n    \"shareTitle\": \"Únase a unha sesión de colaboración en directo en Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Erro\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Gardar no disco\",\n    \"disk_details\": \"Exporte os datos da escena a un ficheiro que poderás importar máis tarde.\",\n    \"disk_button\": \"Gardar nun ficheiro\",\n    \"link_title\": \"Ligazón para compartir\",\n    \"link_details\": \"Exportar como unha ligazón de só lectura.\",\n    \"link_button\": \"Exportar a unha ligazón\",\n    \"excalidrawplus_description\": \"Garde a escena no seu espazo de traballo en Excalidraw+.\",\n    \"excalidrawplus_button\": \"Exportar\",\n    \"excalidrawplus_exportError\": \"Non se puido exportar a Excalidraw+ neste momento...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Le o noso blog\",\n    \"click\": \"clic\",\n    \"deepSelect\": \"Selección en profundidade\",\n    \"deepBoxSelect\": \"Selección en profundidade dentro da caixa, evitando o arrastre\",\n    \"curvedArrow\": \"Frecha curva\",\n    \"curvedLine\": \"Liña curva\",\n    \"documentation\": \"Documentación\",\n    \"doubleClick\": \"dobre-clic\",\n    \"drag\": \"arrastrar\",\n    \"editor\": \"Editor\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"Encontrou un problema? Envíeo\",\n    \"howto\": \"Sigue as nosas normas\",\n    \"or\": \"ou\",\n    \"preventBinding\": \"Evitar a unión de frechas\",\n    \"tools\": \"Ferramentas\",\n    \"shortcuts\": \"Atallos de teclado\",\n    \"textFinish\": \"Rematar de editar (editor de texto)\",\n    \"textNewLine\": \"Engadir unha nova liña (editor de texto)\",\n    \"title\": \"Axuda\",\n    \"view\": \"Vista\",\n    \"zoomToFit\": \"Zoom que se axuste a todos os elementos\",\n    \"zoomToSelection\": \"Zoom á selección\",\n    \"toggleElementLock\": \"Bloquear/desbloquear selección\",\n    \"movePageUpDown\": \"Mover páxina cara enriba/abaixo\",\n    \"movePageLeftRight\": \"Mover páxina cara a esquerda/dereita\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Limpar lenzo\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Publicar biblioteca\",\n    \"itemName\": \"Nome do elemento\",\n    \"authorName\": \"Nome do autor\",\n    \"githubUsername\": \"Nome de usuario en Github\",\n    \"twitterUsername\": \"Nome de usuario en Twitter\",\n    \"libraryName\": \"Nome da biblioteca\",\n    \"libraryDesc\": \"Descrición da biblioteca\",\n    \"website\": \"Páxina web\",\n    \"placeholder\": {\n      \"authorName\": \"O seu nome ou nome de usuario\",\n      \"libraryName\": \"Nome da súa biblioteca\",\n      \"libraryDesc\": \"Descrición da súa biblioteca para axudar a xente a entender o seu uso\",\n      \"githubHandle\": \"Nome de usuario de GitHub (opcional), así poderás editar a biblioteca unha vez enviada para a súa revisión\",\n      \"twitterHandle\": \"Nome de usuario en Twitter(opcional), así sabemos a quen darlle crédito cando se lle de promoción a través de Twitter\",\n      \"website\": \"Ligazón ao teu sitio web persoal ou a outro sitio (opcional)\"\n    },\n    \"errors\": {\n      \"required\": \"Obrigatorio\",\n      \"website\": \"Introduza unha URL válida\"\n    },\n    \"noteDescription\": \"Envíe a súa biblioteca para que sexa incluída no <link>repositorio público de bibliotecas</link>para que outra xente a poida usar nos seus debuxos.\",\n    \"noteGuidelines\": \"A biblioteca necesita ser aprobada manualmente primeiro. Por favor, lea as <link>normas</link> antes de ser enviado. Necesitarás unha conta de GitHub para comunicarte ou facer cambios se se solicitan, pero non é estritamente necesario.\",\n    \"noteLicense\": \"Ao enviar, estás de acordo con que a biblioteca sexa publicada baixo a <link>Licenza MIT, </link>o cal significa que, en resumo, calquera pode usalo sen restricións.\",\n    \"noteItems\": \"Cada elemento da biblioteca debe ter o seu nome propio para que se poida filtrar. Os seguintes elementos da biblioteca serán incluídos:\",\n    \"atleastOneLibItem\": \"Por favor seleccione polo menos un elemento da biblioteca para comezar\",\n    \"republishWarning\": \"Nota: algúns dos elementos seleccionados están marcados como xa publicados/enviados. Só deberías reenviar elementos cando se actualice unha biblioteca ou envío.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Biblioteca enviada\",\n    \"content\": \"Grazas {{authorName}}. A súa biblioteca foi enviada para ser revisada. Pode seguir o estado<link>aquí</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Restablecer biblioteca\",\n    \"removeItemsFromLib\": \"Eliminar os elementos seleccionados da biblioteca\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Exportar imaxe\",\n    \"label\": {\n      \"withBackground\": \"Fondo\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"Modo escuro\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Exportar a PNG\",\n      \"exportToSvg\": \"Exportar a SVG\",\n      \"copyPngToClipboard\": \"Copiar PNG ao portapapeis\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Copiar ao portapapeis\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Os teus debuxos están cifrados de punto a punto, polo que os servidores de Excalidraw nunca os verán.\",\n    \"link\": \"Entrada do blog acerca do cifrado de punto a punto en Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Ángulo\",\n    \"element\": \"Elemento\",\n    \"elements\": \"Elementos\",\n    \"height\": \"Alto\",\n    \"scene\": \"Escena\",\n    \"selected\": \"Seleccionado\",\n    \"storage\": \"Almacenamento\",\n    \"title\": \"Estadísticas para nerds\",\n    \"total\": \"Total\",\n    \"version\": \"Versión\",\n    \"versionCopy\": \"Faga clic para copiar\",\n    \"versionNotAvailable\": \"Versión non dispoñible\",\n    \"width\": \"Ancho\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Engadido á biblioteca\",\n    \"copyStyles\": \"Estilos copiados.\",\n    \"copyToClipboard\": \"Copiado ao portapapeis.\",\n    \"copyToClipboardAsPng\": \"Copiar {{exportSelection}} ao portapapeis como PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Ficheiro gardado.\",\n    \"fileSavedToFilename\": \"Gardado en {filename}\",\n    \"canvas\": \"lenzo\",\n    \"selection\": \"selección\",\n    \"pasteAsSingleElement\": \"Usa {{shortcut}} para pegar como un único elemento\\nou pega nun editor de texto existente\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Transparente\",\n    \"black\": \"Negro\",\n    \"white\": \"Branco\",\n    \"red\": \"Vermello\",\n    \"pink\": \"Rosa\",\n    \"grape\": \"Uva\",\n    \"violet\": \"Violeta\",\n    \"gray\": \"Gris\",\n    \"blue\": \"Azul\",\n    \"cyan\": \"\",\n    \"teal\": \"\",\n    \"green\": \"Verde\",\n    \"yellow\": \"Marelo\",\n    \"orange\": \"Laranxa\",\n    \"bronze\": \"Bronce\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Toda a información é gardada de maneira local no seu navegador.\",\n      \"center_heading_plus\": \"Queres ir a Excalidraw+ no seu lugar?\",\n      \"menuHint\": \"Exportar, preferencias, idiomas, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Exportar, preferencias, e máis...\",\n      \"center_heading\": \"Diagramas. Feito. Sinxelo.\",\n      \"toolbarHint\": \"Escolle unha ferramenta & Comeza a debuxar!\",\n      \"helpHint\": \"Atallos & axuda\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"Cores\",\n    \"shades\": \"\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Exportar como imaxe\",\n        \"button\": \"Exportar como imaxe\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Gardar no disco\",\n        \"button\": \"Gardar no disco\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Exportar a Excalidraw+\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Cargar dende arquivo\",\n        \"button\": \"Cargar dende arquivo\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Cargar dende un enlace\",\n        \"button\": \"Substituír o meu contido\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}