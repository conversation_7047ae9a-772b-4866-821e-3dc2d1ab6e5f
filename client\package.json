{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@babel/parser": "^7.27.2", "@clerk/nextjs": "^6.19.4", "@monaco-editor/react": "^4.7.0", "@types/lodash": "^4.17.16", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "axios": "^1.8.4", "cookies-next": "^5.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "firebase": "^11.8.0", "framer-motion": "^12.7.3", "google-auth-library": "^9.15.1", "gsap": "^3.12.7", "lodash": "^4.17.21", "next": "15.3.0", "prettier": "^3.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-config-next": "15.3.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "5.8.3"}}