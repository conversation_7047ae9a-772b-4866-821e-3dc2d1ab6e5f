"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_lexon_lexon_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/lexon/lexon.js":
/*!***************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/lexon/lexon.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/lexon/lexon.ts\nvar conf = {\n  comments: {\n    lineComment: \"COMMENT\"\n    // blockComment: ['COMMENT', '.'],\n  },\n  brackets: [[\"(\", \")\"]],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \":\", close: \".\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"`\", close: \"`\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \":\", close: \".\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*(::\\\\s*|COMMENT\\\\s+)#region\"),\n      end: new RegExp(\"^\\\\s*(::\\\\s*|COMMENT\\\\s+)#endregion\")\n    }\n  }\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  // defaultToken: 'invalid',\n  tokenPostfix: \".lexon\",\n  ignoreCase: true,\n  keywords: [\n    \"lexon\",\n    \"lex\",\n    \"clause\",\n    \"terms\",\n    \"contracts\",\n    \"may\",\n    \"pay\",\n    \"pays\",\n    \"appoints\",\n    \"into\",\n    \"to\"\n  ],\n  typeKeywords: [\"amount\", \"person\", \"key\", \"time\", \"date\", \"asset\", \"text\"],\n  operators: [\n    \"less\",\n    \"greater\",\n    \"equal\",\n    \"le\",\n    \"gt\",\n    \"or\",\n    \"and\",\n    \"add\",\n    \"added\",\n    \"subtract\",\n    \"subtracted\",\n    \"multiply\",\n    \"multiplied\",\n    \"times\",\n    \"divide\",\n    \"divided\",\n    \"is\",\n    \"be\",\n    \"certified\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // comment\n      [/^(\\s*)(comment:?(?:\\s.*|))$/, [\"\", \"comment\"]],\n      // special identifier cases\n      [\n        /\"/,\n        {\n          token: \"identifier.quote\",\n          bracket: \"@open\",\n          next: \"@quoted_identifier\"\n        }\n      ],\n      [\n        \"LEX$\",\n        {\n          token: \"keyword\",\n          bracket: \"@open\",\n          next: \"@identifier_until_period\"\n        }\n      ],\n      [\"LEXON\", { token: \"keyword\", bracket: \"@open\", next: \"@semver\" }],\n      [\n        \":\",\n        {\n          token: \"delimiter\",\n          bracket: \"@open\",\n          next: \"@identifier_until_period\"\n        }\n      ],\n      // identifiers and keywords\n      [\n        /[a-z_$][\\w$]*/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@typeKeywords\": \"keyword.type\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d*\\.\\d*\\.\\d*/, \"number.semver\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"]\n    ],\n    quoted_identifier: [\n      [/[^\\\\\"]+/, \"identifier\"],\n      [/\"/, { token: \"identifier.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    space_identifier_until_period: [\n      [\":\", \"delimiter\"],\n      [\" \", { token: \"white\", next: \"@identifier_rest\" }]\n    ],\n    identifier_until_period: [\n      { include: \"@whitespace\" },\n      [\":\", { token: \"delimiter\", next: \"@identifier_rest\" }],\n      [/[^\\\\.]+/, \"identifier\"],\n      [/\\./, { token: \"delimiter\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    identifier_rest: [\n      [/[^\\\\.]+/, \"identifier\"],\n      [/\\./, { token: \"delimiter\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    semver: [\n      { include: \"@whitespace\" },\n      [\":\", \"delimiter\"],\n      [/\\d*\\.\\d*\\.\\d*/, { token: \"number.semver\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [[/[ \\t\\r\\n]+/, \"white\"]]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/lexon/lexon.js\n"));

/***/ })

}]);