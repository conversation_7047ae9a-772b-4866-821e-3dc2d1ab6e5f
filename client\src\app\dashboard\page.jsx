"use client"

import RoomForm from "@/components/RoomForm"
import { motion } from "framer-motion"
import { <PERSON>C<PERSON>, FiUsers, FiClock, FiActivity } from "react-icons/fi"

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6, ease: "easeOut" }
  }
}

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
}

const StatCard = ({ icon, title, value, color }) => {
  return (
    <motion.div
      className="card p-6 flex flex-col items-center text-center"
      variants={fadeIn}
    >
      <div className={`text-4xl mb-4 ${color}`}>{icon}</div>
      <h3 className="text-xl font-bold mb-2">{title}</h3>
      <p className="text-3xl font-bold text-gray-800 dark:text-gray-200">{value}</p>
    </motion.div>
  )
}

export default function DashboardPage() {
  return (
    <main className="flex flex-col items-center justify-center min-h-screen p-4 relative overflow-hidden">
      {/* Background elements */}
      <motion.div
        className="absolute inset-0 -z-10"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-blue-900/10 to-purple-900/10 dark:from-blue-900/20 dark:to-purple-900/20" />
        <div className="absolute inset-0 bg-grid-pattern opacity-10" />
      </motion.div>

      <div className="w-full max-w-7xl mx-auto">
        <motion.div
          className="text-center mb-12"
          initial="hidden"
          animate="visible"
          variants={staggerContainer}
        >
          <motion.h1
            className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent"
            variants={fadeIn}
          >
            Dashboard
          </motion.h1>

          <motion.p
            className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto"
            variants={fadeIn}
          >
            Create or join a collaborative coding session
          </motion.p>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
          initial="hidden"
          animate="visible"
          variants={staggerContainer}
        >
          <StatCard
            icon={<FiCode />}
            title="Active Sessions"
            value="12"
            color="text-blue-500"
          />

          <StatCard
            icon={<FiUsers />}
            title="Online Users"
            value="28"
            color="text-green-500"
          />

          <StatCard
            icon={<FiClock />}
            title="Avg. Session Time"
            value="42m"
            color="text-purple-500"
          />

          <StatCard
            icon={<FiActivity />}
            title="Code Changes"
            value="1.4k"
            color="text-orange-500"
          />
        </motion.div>

        {/* Room Form */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="bg-white/10 dark:bg-zinc-900/80 p-8 rounded-2xl shadow-2xl backdrop-blur-lg border border-white/10"
        >
          <RoomForm />
        </motion.div>
      </div>
    </main>
  )
}