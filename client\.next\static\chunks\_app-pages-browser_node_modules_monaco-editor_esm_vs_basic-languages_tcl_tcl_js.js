"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_tcl_tcl_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/tcl/tcl.js":
/*!***********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/tcl/tcl.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/tcl/tcl.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  tokenPostfix: \".tcl\",\n  specialFunctions: [\n    \"set\",\n    \"unset\",\n    \"rename\",\n    \"variable\",\n    \"proc\",\n    \"coroutine\",\n    \"foreach\",\n    \"incr\",\n    \"append\",\n    \"lappend\",\n    \"linsert\",\n    \"lreplace\"\n  ],\n  mainFunctions: [\n    \"if\",\n    \"then\",\n    \"elseif\",\n    \"else\",\n    \"case\",\n    \"switch\",\n    \"while\",\n    \"for\",\n    \"break\",\n    \"continue\",\n    \"return\",\n    \"package\",\n    \"namespace\",\n    \"catch\",\n    \"exit\",\n    \"eval\",\n    \"expr\",\n    \"uplevel\",\n    \"upvar\"\n  ],\n  builtinFunctions: [\n    \"file\",\n    \"info\",\n    \"concat\",\n    \"join\",\n    \"lindex\",\n    \"list\",\n    \"llength\",\n    \"lrange\",\n    \"lsearch\",\n    \"lsort\",\n    \"split\",\n    \"array\",\n    \"parray\",\n    \"binary\",\n    \"format\",\n    \"regexp\",\n    \"regsub\",\n    \"scan\",\n    \"string\",\n    \"subst\",\n    \"dict\",\n    \"cd\",\n    \"clock\",\n    \"exec\",\n    \"glob\",\n    \"pid\",\n    \"pwd\",\n    \"close\",\n    \"eof\",\n    \"fblocked\",\n    \"fconfigure\",\n    \"fcopy\",\n    \"fileevent\",\n    \"flush\",\n    \"gets\",\n    \"open\",\n    \"puts\",\n    \"read\",\n    \"seek\",\n    \"socket\",\n    \"tell\",\n    \"interp\",\n    \"after\",\n    \"auto_execok\",\n    \"auto_load\",\n    \"auto_mkindex\",\n    \"auto_reset\",\n    \"bgerror\",\n    \"error\",\n    \"global\",\n    \"history\",\n    \"load\",\n    \"source\",\n    \"time\",\n    \"trace\",\n    \"unknown\",\n    \"unset\",\n    \"update\",\n    \"vwait\",\n    \"winfo\",\n    \"wm\",\n    \"bind\",\n    \"event\",\n    \"pack\",\n    \"place\",\n    \"grid\",\n    \"font\",\n    \"bell\",\n    \"clipboard\",\n    \"destroy\",\n    \"focus\",\n    \"grab\",\n    \"lower\",\n    \"option\",\n    \"raise\",\n    \"selection\",\n    \"send\",\n    \"tk\",\n    \"tkwait\",\n    \"tk_bisque\",\n    \"tk_focusNext\",\n    \"tk_focusPrev\",\n    \"tk_focusFollowsMouse\",\n    \"tk_popup\",\n    \"tk_setPalette\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  brackets: [\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" }\n  ],\n  escapes: /\\\\(?:[abfnrtv\\\\\"'\\[\\]\\{\\};\\$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  variables: /(?:\\$+(?:(?:\\:\\:?)?[a-zA-Z_]\\w*)+)/,\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@specialFunctions\": {\n              token: \"keyword.flow\",\n              next: \"@specialFunc\"\n            },\n            \"@mainFunctions\": \"keyword\",\n            \"@builtinFunctions\": \"variable\",\n            \"@default\": \"operator.scss\"\n          }\n        }\n      ],\n      [/\\s+\\-+(?!\\d|\\.)\\w*|{\\*}/, \"metatag\"],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"operator\"],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/\\.(?!\\d|\\.)[\\w\\-]*/, \"operator.sql\"],\n      // numbers\n      [/\\d+(\\.\\d+)?/, \"number\"],\n      [/\\d+/, \"number\"],\n      // delimiter\n      [/;/, \"delimiter\"],\n      // strings\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@dstring\" }],\n      [/'/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstring\" }]\n    ],\n    dstring: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/[^\\\\$\\[\\]\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    sstring: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/[^\\\\$\\[\\]']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/#.*\\\\$/, { token: \"comment\", next: \"@newlineComment\" }],\n      [/#.*(?!\\\\)$/, \"comment\"]\n    ],\n    newlineComment: [\n      [/.*\\\\$/, \"comment\"],\n      [/.*(?!\\\\)$/, { token: \"comment\", next: \"@pop\" }]\n    ],\n    nestedVariable: [\n      [/[^\\{\\}\\$]+/, \"type.identifier\"],\n      [/\\}/, { token: \"identifier\", next: \"@pop\" }]\n    ],\n    nestedCall: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\]/, { token: \"@brackets\", next: \"@pop\" }],\n      { include: \"root\" }\n    ],\n    specialFunc: [\n      [/\"/, { token: \"string\", next: \"@dstring\" }],\n      [/'/, { token: \"string\", next: \"@sstring\" }],\n      [/\\S+/, { token: \"type\", next: \"@pop\" }]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/tcl/tcl.js\n"));

/***/ })

}]);