{"name": "@braintree/sanitize-url", "version": "6.0.2", "description": "A url sanitizer", "main": "dist/index.js", "types": "dist/index.d.ts", "author": "", "scripts": {"prepublishOnly": "npm run build", "prebuild": "prettier --write .", "build": "tsc --declaration", "lint": "eslint --ext js,ts .", "posttest": "npm run lint", "test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/braintree/sanitize-url.git"}, "keywords": [], "license": "MIT", "bugs": {"url": "https://github.com/braintree/sanitize-url/issues"}, "homepage": "https://github.com/braintree/sanitize-url#readme", "devDependencies": {"@types/jest": "^27.4.1", "@typescript-eslint/eslint-plugin": "^5.19.0", "chai": "^4.3.6", "eslint": "^8.13.0", "eslint-config-braintree": "^6.0.0-typescript-prep-rc.2", "eslint-plugin-prettier": "^4.0.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "jest": {"testEnvironment": "jsdom", "preset": "ts-jest", "globals": {"ts-jest": {"tsconfig": "src/__tests__/tsconfig.json"}}}}