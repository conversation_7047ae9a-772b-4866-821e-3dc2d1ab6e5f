"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_redshift_redshift_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/redshift/redshift.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/redshift/redshift.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/redshift/redshift.ts\nvar conf = {\n  comments: {\n    lineComment: \"--\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sql\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \"AES128\",\n    \"AES256\",\n    \"ALL\",\n    \"ALLOWOVERWRITE\",\n    \"ANALYSE\",\n    \"ANALYZE\",\n    \"AND\",\n    \"ANY\",\n    \"ARRAY\",\n    \"AS\",\n    \"ASC\",\n    \"AUTHORIZATION\",\n    \"AZ64\",\n    \"BACKUP\",\n    \"BETWEEN\",\n    \"BINARY\",\n    \"BLANKSASNULL\",\n    \"BOTH\",\n    \"BYTEDICT\",\n    \"BZIP2\",\n    \"CASE\",\n    \"CAST\",\n    \"CHECK\",\n    \"COLLATE\",\n    \"COLUMN\",\n    \"CONSTRAINT\",\n    \"CREATE\",\n    \"CREDENTIALS\",\n    \"CROSS\",\n    \"CURRENT_DATE\",\n    \"CURRENT_TIME\",\n    \"CURRENT_TIMESTAMP\",\n    \"CURRENT_USER\",\n    \"CURRENT_USER_ID\",\n    \"DEFAULT\",\n    \"DEFERRABLE\",\n    \"DEFLATE\",\n    \"DEFRAG\",\n    \"DELTA\",\n    \"DELTA32K\",\n    \"DESC\",\n    \"DISABLE\",\n    \"DISTINCT\",\n    \"DO\",\n    \"ELSE\",\n    \"EMPTYASNULL\",\n    \"ENABLE\",\n    \"ENCODE\",\n    \"ENCRYPT\",\n    \"ENCRYPTION\",\n    \"END\",\n    \"EXCEPT\",\n    \"EXPLICIT\",\n    \"FALSE\",\n    \"FOR\",\n    \"FOREIGN\",\n    \"FREEZE\",\n    \"FROM\",\n    \"FULL\",\n    \"GLOBALDICT256\",\n    \"GLOBALDICT64K\",\n    \"GRANT\",\n    \"GROUP\",\n    \"GZIP\",\n    \"HAVING\",\n    \"IDENTITY\",\n    \"IGNORE\",\n    \"ILIKE\",\n    \"IN\",\n    \"INITIALLY\",\n    \"INNER\",\n    \"INTERSECT\",\n    \"INTO\",\n    \"IS\",\n    \"ISNULL\",\n    \"JOIN\",\n    \"LANGUAGE\",\n    \"LEADING\",\n    \"LEFT\",\n    \"LIKE\",\n    \"LIMIT\",\n    \"LOCALTIME\",\n    \"LOCALTIMESTAMP\",\n    \"LUN\",\n    \"LUNS\",\n    \"LZO\",\n    \"LZOP\",\n    \"MINUS\",\n    \"MOSTLY16\",\n    \"MOSTLY32\",\n    \"MOSTLY8\",\n    \"NATURAL\",\n    \"NEW\",\n    \"NOT\",\n    \"NOTNULL\",\n    \"NULL\",\n    \"NULLS\",\n    \"OFF\",\n    \"OFFLINE\",\n    \"OFFSET\",\n    \"OID\",\n    \"OLD\",\n    \"ON\",\n    \"ONLY\",\n    \"OPEN\",\n    \"OR\",\n    \"ORDER\",\n    \"OUTER\",\n    \"OVERLAPS\",\n    \"PARALLEL\",\n    \"PARTITION\",\n    \"PERCENT\",\n    \"PERMISSIONS\",\n    \"PLACING\",\n    \"PRIMARY\",\n    \"RAW\",\n    \"READRATIO\",\n    \"RECOVER\",\n    \"REFERENCES\",\n    \"RESPECT\",\n    \"REJECTLOG\",\n    \"RESORT\",\n    \"RESTORE\",\n    \"RIGHT\",\n    \"SELECT\",\n    \"SESSION_USER\",\n    \"SIMILAR\",\n    \"SNAPSHOT\",\n    \"SOME\",\n    \"SYSDATE\",\n    \"SYSTEM\",\n    \"TABLE\",\n    \"TAG\",\n    \"TDES\",\n    \"TEXT255\",\n    \"TEXT32K\",\n    \"THEN\",\n    \"TIMESTAMP\",\n    \"TO\",\n    \"TOP\",\n    \"TRAILING\",\n    \"TRUE\",\n    \"TRUNCATECOLUMNS\",\n    \"UNION\",\n    \"UNIQUE\",\n    \"USER\",\n    \"USING\",\n    \"VERBOSE\",\n    \"WALLET\",\n    \"WHEN\",\n    \"WHERE\",\n    \"WITH\",\n    \"WITHOUT\"\n  ],\n  operators: [\n    \"AND\",\n    \"BETWEEN\",\n    \"IN\",\n    \"LIKE\",\n    \"NOT\",\n    \"OR\",\n    \"IS\",\n    \"NULL\",\n    \"INTERSECT\",\n    \"UNION\",\n    \"INNER\",\n    \"JOIN\",\n    \"LEFT\",\n    \"OUTER\",\n    \"RIGHT\"\n  ],\n  builtinFunctions: [\n    \"current_schema\",\n    \"current_schemas\",\n    \"has_database_privilege\",\n    \"has_schema_privilege\",\n    \"has_table_privilege\",\n    \"age\",\n    \"current_time\",\n    \"current_timestamp\",\n    \"localtime\",\n    \"isfinite\",\n    \"now\",\n    \"ascii\",\n    \"get_bit\",\n    \"get_byte\",\n    \"set_bit\",\n    \"set_byte\",\n    \"to_ascii\",\n    \"approximate percentile_disc\",\n    \"avg\",\n    \"count\",\n    \"listagg\",\n    \"max\",\n    \"median\",\n    \"min\",\n    \"percentile_cont\",\n    \"stddev_samp\",\n    \"stddev_pop\",\n    \"sum\",\n    \"var_samp\",\n    \"var_pop\",\n    \"bit_and\",\n    \"bit_or\",\n    \"bool_and\",\n    \"bool_or\",\n    \"cume_dist\",\n    \"first_value\",\n    \"lag\",\n    \"last_value\",\n    \"lead\",\n    \"nth_value\",\n    \"ratio_to_report\",\n    \"dense_rank\",\n    \"ntile\",\n    \"percent_rank\",\n    \"rank\",\n    \"row_number\",\n    \"case\",\n    \"coalesce\",\n    \"decode\",\n    \"greatest\",\n    \"least\",\n    \"nvl\",\n    \"nvl2\",\n    \"nullif\",\n    \"add_months\",\n    \"at time zone\",\n    \"convert_timezone\",\n    \"current_date\",\n    \"date_cmp\",\n    \"date_cmp_timestamp\",\n    \"date_cmp_timestamptz\",\n    \"date_part_year\",\n    \"dateadd\",\n    \"datediff\",\n    \"date_part\",\n    \"date_trunc\",\n    \"extract\",\n    \"getdate\",\n    \"interval_cmp\",\n    \"last_day\",\n    \"months_between\",\n    \"next_day\",\n    \"sysdate\",\n    \"timeofday\",\n    \"timestamp_cmp\",\n    \"timestamp_cmp_date\",\n    \"timestamp_cmp_timestamptz\",\n    \"timestamptz_cmp\",\n    \"timestamptz_cmp_date\",\n    \"timestamptz_cmp_timestamp\",\n    \"timezone\",\n    \"to_timestamp\",\n    \"trunc\",\n    \"abs\",\n    \"acos\",\n    \"asin\",\n    \"atan\",\n    \"atan2\",\n    \"cbrt\",\n    \"ceil\",\n    \"ceiling\",\n    \"checksum\",\n    \"cos\",\n    \"cot\",\n    \"degrees\",\n    \"dexp\",\n    \"dlog1\",\n    \"dlog10\",\n    \"exp\",\n    \"floor\",\n    \"ln\",\n    \"log\",\n    \"mod\",\n    \"pi\",\n    \"power\",\n    \"radians\",\n    \"random\",\n    \"round\",\n    \"sin\",\n    \"sign\",\n    \"sqrt\",\n    \"tan\",\n    \"to_hex\",\n    \"bpcharcmp\",\n    \"btrim\",\n    \"bttext_pattern_cmp\",\n    \"char_length\",\n    \"character_length\",\n    \"charindex\",\n    \"chr\",\n    \"concat\",\n    \"crc32\",\n    \"func_sha1\",\n    \"initcap\",\n    \"left and rights\",\n    \"len\",\n    \"length\",\n    \"lower\",\n    \"lpad and rpads\",\n    \"ltrim\",\n    \"md5\",\n    \"octet_length\",\n    \"position\",\n    \"quote_ident\",\n    \"quote_literal\",\n    \"regexp_count\",\n    \"regexp_instr\",\n    \"regexp_replace\",\n    \"regexp_substr\",\n    \"repeat\",\n    \"replace\",\n    \"replicate\",\n    \"reverse\",\n    \"rtrim\",\n    \"split_part\",\n    \"strpos\",\n    \"strtol\",\n    \"substring\",\n    \"textlen\",\n    \"translate\",\n    \"trim\",\n    \"upper\",\n    \"cast\",\n    \"convert\",\n    \"to_char\",\n    \"to_date\",\n    \"to_number\",\n    \"json_array_length\",\n    \"json_extract_array_element_text\",\n    \"json_extract_path_text\",\n    \"current_setting\",\n    \"pg_cancel_backend\",\n    \"pg_terminate_backend\",\n    \"set_config\",\n    \"current_database\",\n    \"current_user\",\n    \"current_user_id\",\n    \"pg_backend_pid\",\n    \"pg_last_copy_count\",\n    \"pg_last_copy_id\",\n    \"pg_last_query_id\",\n    \"pg_last_unload_count\",\n    \"session_user\",\n    \"slice_num\",\n    \"user\",\n    \"version\",\n    \"abbrev\",\n    \"acosd\",\n    \"any\",\n    \"area\",\n    \"array_agg\",\n    \"array_append\",\n    \"array_cat\",\n    \"array_dims\",\n    \"array_fill\",\n    \"array_length\",\n    \"array_lower\",\n    \"array_ndims\",\n    \"array_position\",\n    \"array_positions\",\n    \"array_prepend\",\n    \"array_remove\",\n    \"array_replace\",\n    \"array_to_json\",\n    \"array_to_string\",\n    \"array_to_tsvector\",\n    \"array_upper\",\n    \"asind\",\n    \"atan2d\",\n    \"atand\",\n    \"bit\",\n    \"bit_length\",\n    \"bound_box\",\n    \"box\",\n    \"brin_summarize_new_values\",\n    \"broadcast\",\n    \"cardinality\",\n    \"center\",\n    \"circle\",\n    \"clock_timestamp\",\n    \"col_description\",\n    \"concat_ws\",\n    \"convert_from\",\n    \"convert_to\",\n    \"corr\",\n    \"cosd\",\n    \"cotd\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"current_catalog\",\n    \"current_query\",\n    \"current_role\",\n    \"currval\",\n    \"cursor_to_xml\",\n    \"diameter\",\n    \"div\",\n    \"encode\",\n    \"enum_first\",\n    \"enum_last\",\n    \"enum_range\",\n    \"every\",\n    \"family\",\n    \"format\",\n    \"format_type\",\n    \"generate_series\",\n    \"generate_subscripts\",\n    \"get_current_ts_config\",\n    \"gin_clean_pending_list\",\n    \"grouping\",\n    \"has_any_column_privilege\",\n    \"has_column_privilege\",\n    \"has_foreign_data_wrapper_privilege\",\n    \"has_function_privilege\",\n    \"has_language_privilege\",\n    \"has_sequence_privilege\",\n    \"has_server_privilege\",\n    \"has_tablespace_privilege\",\n    \"has_type_privilege\",\n    \"height\",\n    \"host\",\n    \"hostmask\",\n    \"inet_client_addr\",\n    \"inet_client_port\",\n    \"inet_merge\",\n    \"inet_same_family\",\n    \"inet_server_addr\",\n    \"inet_server_port\",\n    \"isclosed\",\n    \"isempty\",\n    \"isopen\",\n    \"json_agg\",\n    \"json_object\",\n    \"json_object_agg\",\n    \"json_populate_record\",\n    \"json_populate_recordset\",\n    \"json_to_record\",\n    \"json_to_recordset\",\n    \"jsonb_agg\",\n    \"jsonb_object_agg\",\n    \"justify_days\",\n    \"justify_hours\",\n    \"justify_interval\",\n    \"lastval\",\n    \"left\",\n    \"line\",\n    \"localtimestamp\",\n    \"lower_inc\",\n    \"lower_inf\",\n    \"lpad\",\n    \"lseg\",\n    \"make_date\",\n    \"make_interval\",\n    \"make_time\",\n    \"make_timestamp\",\n    \"make_timestamptz\",\n    \"masklen\",\n    \"mode\",\n    \"netmask\",\n    \"network\",\n    \"nextval\",\n    \"npoints\",\n    \"num_nonnulls\",\n    \"num_nulls\",\n    \"numnode\",\n    \"obj_description\",\n    \"overlay\",\n    \"parse_ident\",\n    \"path\",\n    \"pclose\",\n    \"percentile_disc\",\n    \"pg_advisory_lock\",\n    \"pg_advisory_lock_shared\",\n    \"pg_advisory_unlock\",\n    \"pg_advisory_unlock_all\",\n    \"pg_advisory_unlock_shared\",\n    \"pg_advisory_xact_lock\",\n    \"pg_advisory_xact_lock_shared\",\n    \"pg_backup_start_time\",\n    \"pg_blocking_pids\",\n    \"pg_client_encoding\",\n    \"pg_collation_is_visible\",\n    \"pg_column_size\",\n    \"pg_conf_load_time\",\n    \"pg_control_checkpoint\",\n    \"pg_control_init\",\n    \"pg_control_recovery\",\n    \"pg_control_system\",\n    \"pg_conversion_is_visible\",\n    \"pg_create_logical_replication_slot\",\n    \"pg_create_physical_replication_slot\",\n    \"pg_create_restore_point\",\n    \"pg_current_xlog_flush_location\",\n    \"pg_current_xlog_insert_location\",\n    \"pg_current_xlog_location\",\n    \"pg_database_size\",\n    \"pg_describe_object\",\n    \"pg_drop_replication_slot\",\n    \"pg_export_snapshot\",\n    \"pg_filenode_relation\",\n    \"pg_function_is_visible\",\n    \"pg_get_constraintdef\",\n    \"pg_get_expr\",\n    \"pg_get_function_arguments\",\n    \"pg_get_function_identity_arguments\",\n    \"pg_get_function_result\",\n    \"pg_get_functiondef\",\n    \"pg_get_indexdef\",\n    \"pg_get_keywords\",\n    \"pg_get_object_address\",\n    \"pg_get_owned_sequence\",\n    \"pg_get_ruledef\",\n    \"pg_get_serial_sequence\",\n    \"pg_get_triggerdef\",\n    \"pg_get_userbyid\",\n    \"pg_get_viewdef\",\n    \"pg_has_role\",\n    \"pg_identify_object\",\n    \"pg_identify_object_as_address\",\n    \"pg_index_column_has_property\",\n    \"pg_index_has_property\",\n    \"pg_indexam_has_property\",\n    \"pg_indexes_size\",\n    \"pg_is_in_backup\",\n    \"pg_is_in_recovery\",\n    \"pg_is_other_temp_schema\",\n    \"pg_is_xlog_replay_paused\",\n    \"pg_last_committed_xact\",\n    \"pg_last_xact_replay_timestamp\",\n    \"pg_last_xlog_receive_location\",\n    \"pg_last_xlog_replay_location\",\n    \"pg_listening_channels\",\n    \"pg_logical_emit_message\",\n    \"pg_logical_slot_get_binary_changes\",\n    \"pg_logical_slot_get_changes\",\n    \"pg_logical_slot_peek_binary_changes\",\n    \"pg_logical_slot_peek_changes\",\n    \"pg_ls_dir\",\n    \"pg_my_temp_schema\",\n    \"pg_notification_queue_usage\",\n    \"pg_opclass_is_visible\",\n    \"pg_operator_is_visible\",\n    \"pg_opfamily_is_visible\",\n    \"pg_options_to_table\",\n    \"pg_postmaster_start_time\",\n    \"pg_read_binary_file\",\n    \"pg_read_file\",\n    \"pg_relation_filenode\",\n    \"pg_relation_filepath\",\n    \"pg_relation_size\",\n    \"pg_reload_conf\",\n    \"pg_replication_origin_create\",\n    \"pg_replication_origin_drop\",\n    \"pg_replication_origin_oid\",\n    \"pg_replication_origin_progress\",\n    \"pg_replication_origin_session_is_setup\",\n    \"pg_replication_origin_session_progress\",\n    \"pg_replication_origin_session_reset\",\n    \"pg_replication_origin_session_setup\",\n    \"pg_replication_origin_xact_reset\",\n    \"pg_replication_origin_xact_setup\",\n    \"pg_rotate_logfile\",\n    \"pg_size_bytes\",\n    \"pg_size_pretty\",\n    \"pg_sleep\",\n    \"pg_sleep_for\",\n    \"pg_sleep_until\",\n    \"pg_start_backup\",\n    \"pg_stat_file\",\n    \"pg_stop_backup\",\n    \"pg_switch_xlog\",\n    \"pg_table_is_visible\",\n    \"pg_table_size\",\n    \"pg_tablespace_databases\",\n    \"pg_tablespace_location\",\n    \"pg_tablespace_size\",\n    \"pg_total_relation_size\",\n    \"pg_trigger_depth\",\n    \"pg_try_advisory_lock\",\n    \"pg_try_advisory_lock_shared\",\n    \"pg_try_advisory_xact_lock\",\n    \"pg_try_advisory_xact_lock_shared\",\n    \"pg_ts_config_is_visible\",\n    \"pg_ts_dict_is_visible\",\n    \"pg_ts_parser_is_visible\",\n    \"pg_ts_template_is_visible\",\n    \"pg_type_is_visible\",\n    \"pg_typeof\",\n    \"pg_xact_commit_timestamp\",\n    \"pg_xlog_location_diff\",\n    \"pg_xlog_replay_pause\",\n    \"pg_xlog_replay_resume\",\n    \"pg_xlogfile_name\",\n    \"pg_xlogfile_name_offset\",\n    \"phraseto_tsquery\",\n    \"plainto_tsquery\",\n    \"point\",\n    \"polygon\",\n    \"popen\",\n    \"pqserverversion\",\n    \"query_to_xml\",\n    \"querytree\",\n    \"quote_nullable\",\n    \"radius\",\n    \"range_merge\",\n    \"regexp_matches\",\n    \"regexp_split_to_array\",\n    \"regexp_split_to_table\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"right\",\n    \"row_security_active\",\n    \"row_to_json\",\n    \"rpad\",\n    \"scale\",\n    \"set_masklen\",\n    \"setseed\",\n    \"setval\",\n    \"setweight\",\n    \"shobj_description\",\n    \"sind\",\n    \"sprintf\",\n    \"statement_timestamp\",\n    \"stddev\",\n    \"string_agg\",\n    \"string_to_array\",\n    \"strip\",\n    \"substr\",\n    \"table_to_xml\",\n    \"table_to_xml_and_xmlschema\",\n    \"tand\",\n    \"text\",\n    \"to_json\",\n    \"to_regclass\",\n    \"to_regnamespace\",\n    \"to_regoper\",\n    \"to_regoperator\",\n    \"to_regproc\",\n    \"to_regprocedure\",\n    \"to_regrole\",\n    \"to_regtype\",\n    \"to_tsquery\",\n    \"to_tsvector\",\n    \"transaction_timestamp\",\n    \"ts_debug\",\n    \"ts_delete\",\n    \"ts_filter\",\n    \"ts_headline\",\n    \"ts_lexize\",\n    \"ts_parse\",\n    \"ts_rank\",\n    \"ts_rank_cd\",\n    \"ts_rewrite\",\n    \"ts_stat\",\n    \"ts_token_type\",\n    \"tsquery_phrase\",\n    \"tsvector_to_array\",\n    \"tsvector_update_trigger\",\n    \"tsvector_update_trigger_column\",\n    \"txid_current\",\n    \"txid_current_snapshot\",\n    \"txid_snapshot_xip\",\n    \"txid_snapshot_xmax\",\n    \"txid_snapshot_xmin\",\n    \"txid_visible_in_snapshot\",\n    \"unnest\",\n    \"upper_inc\",\n    \"upper_inf\",\n    \"variance\",\n    \"width\",\n    \"width_bucket\",\n    \"xml_is_well_formed\",\n    \"xml_is_well_formed_content\",\n    \"xml_is_well_formed_document\",\n    \"xmlagg\",\n    \"xmlcomment\",\n    \"xmlconcat\",\n    \"xmlelement\",\n    \"xmlexists\",\n    \"xmlforest\",\n    \"xmlparse\",\n    \"xmlpi\",\n    \"xmlroot\",\n    \"xmlserialize\",\n    \"xpath\",\n    \"xpath_exists\"\n  ],\n  builtinVariables: [\n    // NOT SUPPORTED\n  ],\n  pseudoColumns: [\n    // NOT SUPPORTED\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@pseudoColumns\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@complexIdentifiers\" },\n      { include: \"@scopes\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [\n        /[\\w@#$]+/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@operators\": \"operator\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [/--+.*/, \"comment\"],\n      [/\\/\\*/, { token: \"comment.quote\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^*/]+/, \"comment\"],\n      // Not supporting nested comments, as nested comments seem to not be standard?\n      // i.e. http://stackoverflow.com/questions/728172/are-there-multiline-comment-delimiters-in-sql-that-are-vendor-agnostic\n      // [/\\/\\*/, { token: 'comment.quote', next: '@push' }],    // nested comment not allowed :-(\n      [/\\*\\//, { token: \"comment.quote\", next: \"@pop\" }],\n      [/./, \"comment\"]\n    ],\n    pseudoColumns: [\n      [\n        /[$][A-Za-z_][\\w@#$]*/,\n        {\n          cases: {\n            \"@pseudoColumns\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [[/'/, { token: \"string\", next: \"@string\" }]],\n    string: [\n      [/[^']+/, \"string\"],\n      [/''/, \"string\"],\n      [/'/, { token: \"string\", next: \"@pop\" }]\n    ],\n    complexIdentifiers: [[/\"/, { token: \"identifier.quote\", next: \"@quotedIdentifier\" }]],\n    quotedIdentifier: [\n      [/[^\"]+/, \"identifier\"],\n      [/\"\"/, \"identifier\"],\n      [/\"/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    scopes: [\n      // NOT SUPPORTED\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbW9uYWNvLWVkaXRvci9lc20vdnMvYmFzaWMtbGFuZ3VhZ2VzL3JlZHNoaWZ0L3JlZHNoaWZ0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsT0FBTyxLQUFLO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLFFBQVEsWUFBWSxHQUFHO0FBQzdCLE1BQU0sdUJBQXVCO0FBQzdCLE1BQU0sdUJBQXVCO0FBQzdCLE1BQU0sdUJBQXVCO0FBQzdCLE1BQU07QUFDTjtBQUNBO0FBQ0EsTUFBTSxRQUFRLFlBQVksR0FBRztBQUM3QixNQUFNLHVCQUF1QjtBQUM3QixNQUFNLHVCQUF1QjtBQUM3QixNQUFNLHVCQUF1QjtBQUM3QixNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLGtEQUFrRDtBQUN4RCxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzQkFBc0I7QUFDOUIsUUFBUSx3QkFBd0I7QUFDaEMsUUFBUSwyQkFBMkI7QUFDbkMsUUFBUSxxQkFBcUI7QUFDN0IsUUFBUSxxQkFBcUI7QUFDN0IsUUFBUSxnQ0FBZ0M7QUFDeEMsUUFBUSxvQkFBb0I7QUFDNUIsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQiwwQ0FBMEM7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQix1Q0FBdUM7QUFDM0QsaUJBQWlCLHNDQUFzQztBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0Isa0NBQWtDO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBLGNBQWMsK0JBQStCO0FBQzdDO0FBQ0EsaUNBQWlDLHNEQUFzRDtBQUN2RjtBQUNBO0FBQ0E7QUFDQSxjQUFjLHlDQUF5QztBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFJRSIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RcXHJlYWxjb2RlXFxub2RlX21vZHVsZXNcXG1vbmFjby1lZGl0b3JcXGVzbVxcdnNcXGJhc2ljLWxhbmd1YWdlc1xccmVkc2hpZnRcXHJlZHNoaWZ0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi4gQWxsIHJpZ2h0cyByZXNlcnZlZC5cbiAqIFZlcnNpb246IDAuNTIuMig0MDQ1NDViZGVkMWRmNmZmYTQxZWEwYWY0ZThkZGIyMTkwMThjNmMxKVxuICogUmVsZWFzZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlXG4gKiBodHRwczovL2dpdGh1Yi5jb20vbWljcm9zb2Z0L21vbmFjby1lZGl0b3IvYmxvYi9tYWluL0xJQ0VOU0UudHh0XG4gKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuXG4vLyBzcmMvYmFzaWMtbGFuZ3VhZ2VzL3JlZHNoaWZ0L3JlZHNoaWZ0LnRzXG52YXIgY29uZiA9IHtcbiAgY29tbWVudHM6IHtcbiAgICBsaW5lQ29tbWVudDogXCItLVwiLFxuICAgIGJsb2NrQ29tbWVudDogW1wiLypcIiwgXCIqL1wiXVxuICB9LFxuICBicmFja2V0czogW1xuICAgIFtcIntcIiwgXCJ9XCJdLFxuICAgIFtcIltcIiwgXCJdXCJdLFxuICAgIFtcIihcIiwgXCIpXCJdXG4gIF0sXG4gIGF1dG9DbG9zaW5nUGFpcnM6IFtcbiAgICB7IG9wZW46IFwie1wiLCBjbG9zZTogXCJ9XCIgfSxcbiAgICB7IG9wZW46IFwiW1wiLCBjbG9zZTogXCJdXCIgfSxcbiAgICB7IG9wZW46IFwiKFwiLCBjbG9zZTogXCIpXCIgfSxcbiAgICB7IG9wZW46ICdcIicsIGNsb3NlOiAnXCInIH0sXG4gICAgeyBvcGVuOiBcIidcIiwgY2xvc2U6IFwiJ1wiIH1cbiAgXSxcbiAgc3Vycm91bmRpbmdQYWlyczogW1xuICAgIHsgb3BlbjogXCJ7XCIsIGNsb3NlOiBcIn1cIiB9LFxuICAgIHsgb3BlbjogXCJbXCIsIGNsb3NlOiBcIl1cIiB9LFxuICAgIHsgb3BlbjogXCIoXCIsIGNsb3NlOiBcIilcIiB9LFxuICAgIHsgb3BlbjogJ1wiJywgY2xvc2U6ICdcIicgfSxcbiAgICB7IG9wZW46IFwiJ1wiLCBjbG9zZTogXCInXCIgfVxuICBdXG59O1xudmFyIGxhbmd1YWdlID0ge1xuICBkZWZhdWx0VG9rZW46IFwiXCIsXG4gIHRva2VuUG9zdGZpeDogXCIuc3FsXCIsXG4gIGlnbm9yZUNhc2U6IHRydWUsXG4gIGJyYWNrZXRzOiBbXG4gICAgeyBvcGVuOiBcIltcIiwgY2xvc2U6IFwiXVwiLCB0b2tlbjogXCJkZWxpbWl0ZXIuc3F1YXJlXCIgfSxcbiAgICB7IG9wZW46IFwiKFwiLCBjbG9zZTogXCIpXCIsIHRva2VuOiBcImRlbGltaXRlci5wYXJlbnRoZXNpc1wiIH1cbiAgXSxcbiAga2V5d29yZHM6IFtcbiAgICBcIkFFUzEyOFwiLFxuICAgIFwiQUVTMjU2XCIsXG4gICAgXCJBTExcIixcbiAgICBcIkFMTE9XT1ZFUldSSVRFXCIsXG4gICAgXCJBTkFMWVNFXCIsXG4gICAgXCJBTkFMWVpFXCIsXG4gICAgXCJBTkRcIixcbiAgICBcIkFOWVwiLFxuICAgIFwiQVJSQVlcIixcbiAgICBcIkFTXCIsXG4gICAgXCJBU0NcIixcbiAgICBcIkFVVEhPUklaQVRJT05cIixcbiAgICBcIkFaNjRcIixcbiAgICBcIkJBQ0tVUFwiLFxuICAgIFwiQkVUV0VFTlwiLFxuICAgIFwiQklOQVJZXCIsXG4gICAgXCJCTEFOS1NBU05VTExcIixcbiAgICBcIkJPVEhcIixcbiAgICBcIkJZVEVESUNUXCIsXG4gICAgXCJCWklQMlwiLFxuICAgIFwiQ0FTRVwiLFxuICAgIFwiQ0FTVFwiLFxuICAgIFwiQ0hFQ0tcIixcbiAgICBcIkNPTExBVEVcIixcbiAgICBcIkNPTFVNTlwiLFxuICAgIFwiQ09OU1RSQUlOVFwiLFxuICAgIFwiQ1JFQVRFXCIsXG4gICAgXCJDUkVERU5USUFMU1wiLFxuICAgIFwiQ1JPU1NcIixcbiAgICBcIkNVUlJFTlRfREFURVwiLFxuICAgIFwiQ1VSUkVOVF9USU1FXCIsXG4gICAgXCJDVVJSRU5UX1RJTUVTVEFNUFwiLFxuICAgIFwiQ1VSUkVOVF9VU0VSXCIsXG4gICAgXCJDVVJSRU5UX1VTRVJfSURcIixcbiAgICBcIkRFRkFVTFRcIixcbiAgICBcIkRFRkVSUkFCTEVcIixcbiAgICBcIkRFRkxBVEVcIixcbiAgICBcIkRFRlJBR1wiLFxuICAgIFwiREVMVEFcIixcbiAgICBcIkRFTFRBMzJLXCIsXG4gICAgXCJERVNDXCIsXG4gICAgXCJESVNBQkxFXCIsXG4gICAgXCJESVNUSU5DVFwiLFxuICAgIFwiRE9cIixcbiAgICBcIkVMU0VcIixcbiAgICBcIkVNUFRZQVNOVUxMXCIsXG4gICAgXCJFTkFCTEVcIixcbiAgICBcIkVOQ09ERVwiLFxuICAgIFwiRU5DUllQVFwiLFxuICAgIFwiRU5DUllQVElPTlwiLFxuICAgIFwiRU5EXCIsXG4gICAgXCJFWENFUFRcIixcbiAgICBcIkVYUExJQ0lUXCIsXG4gICAgXCJGQUxTRVwiLFxuICAgIFwiRk9SXCIsXG4gICAgXCJGT1JFSUdOXCIsXG4gICAgXCJGUkVFWkVcIixcbiAgICBcIkZST01cIixcbiAgICBcIkZVTExcIixcbiAgICBcIkdMT0JBTERJQ1QyNTZcIixcbiAgICBcIkdMT0JBTERJQ1Q2NEtcIixcbiAgICBcIkdSQU5UXCIsXG4gICAgXCJHUk9VUFwiLFxuICAgIFwiR1pJUFwiLFxuICAgIFwiSEFWSU5HXCIsXG4gICAgXCJJREVOVElUWVwiLFxuICAgIFwiSUdOT1JFXCIsXG4gICAgXCJJTElLRVwiLFxuICAgIFwiSU5cIixcbiAgICBcIklOSVRJQUxMWVwiLFxuICAgIFwiSU5ORVJcIixcbiAgICBcIklOVEVSU0VDVFwiLFxuICAgIFwiSU5UT1wiLFxuICAgIFwiSVNcIixcbiAgICBcIklTTlVMTFwiLFxuICAgIFwiSk9JTlwiLFxuICAgIFwiTEFOR1VBR0VcIixcbiAgICBcIkxFQURJTkdcIixcbiAgICBcIkxFRlRcIixcbiAgICBcIkxJS0VcIixcbiAgICBcIkxJTUlUXCIsXG4gICAgXCJMT0NBTFRJTUVcIixcbiAgICBcIkxPQ0FMVElNRVNUQU1QXCIsXG4gICAgXCJMVU5cIixcbiAgICBcIkxVTlNcIixcbiAgICBcIkxaT1wiLFxuICAgIFwiTFpPUFwiLFxuICAgIFwiTUlOVVNcIixcbiAgICBcIk1PU1RMWTE2XCIsXG4gICAgXCJNT1NUTFkzMlwiLFxuICAgIFwiTU9TVExZOFwiLFxuICAgIFwiTkFUVVJBTFwiLFxuICAgIFwiTkVXXCIsXG4gICAgXCJOT1RcIixcbiAgICBcIk5PVE5VTExcIixcbiAgICBcIk5VTExcIixcbiAgICBcIk5VTExTXCIsXG4gICAgXCJPRkZcIixcbiAgICBcIk9GRkxJTkVcIixcbiAgICBcIk9GRlNFVFwiLFxuICAgIFwiT0lEXCIsXG4gICAgXCJPTERcIixcbiAgICBcIk9OXCIsXG4gICAgXCJPTkxZXCIsXG4gICAgXCJPUEVOXCIsXG4gICAgXCJPUlwiLFxuICAgIFwiT1JERVJcIixcbiAgICBcIk9VVEVSXCIsXG4gICAgXCJPVkVSTEFQU1wiLFxuICAgIFwiUEFSQUxMRUxcIixcbiAgICBcIlBBUlRJVElPTlwiLFxuICAgIFwiUEVSQ0VOVFwiLFxuICAgIFwiUEVSTUlTU0lPTlNcIixcbiAgICBcIlBMQUNJTkdcIixcbiAgICBcIlBSSU1BUllcIixcbiAgICBcIlJBV1wiLFxuICAgIFwiUkVBRFJBVElPXCIsXG4gICAgXCJSRUNPVkVSXCIsXG4gICAgXCJSRUZFUkVOQ0VTXCIsXG4gICAgXCJSRVNQRUNUXCIsXG4gICAgXCJSRUpFQ1RMT0dcIixcbiAgICBcIlJFU09SVFwiLFxuICAgIFwiUkVTVE9SRVwiLFxuICAgIFwiUklHSFRcIixcbiAgICBcIlNFTEVDVFwiLFxuICAgIFwiU0VTU0lPTl9VU0VSXCIsXG4gICAgXCJTSU1JTEFSXCIsXG4gICAgXCJTTkFQU0hPVFwiLFxuICAgIFwiU09NRVwiLFxuICAgIFwiU1lTREFURVwiLFxuICAgIFwiU1lTVEVNXCIsXG4gICAgXCJUQUJMRVwiLFxuICAgIFwiVEFHXCIsXG4gICAgXCJUREVTXCIsXG4gICAgXCJURVhUMjU1XCIsXG4gICAgXCJURVhUMzJLXCIsXG4gICAgXCJUSEVOXCIsXG4gICAgXCJUSU1FU1RBTVBcIixcbiAgICBcIlRPXCIsXG4gICAgXCJUT1BcIixcbiAgICBcIlRSQUlMSU5HXCIsXG4gICAgXCJUUlVFXCIsXG4gICAgXCJUUlVOQ0FURUNPTFVNTlNcIixcbiAgICBcIlVOSU9OXCIsXG4gICAgXCJVTklRVUVcIixcbiAgICBcIlVTRVJcIixcbiAgICBcIlVTSU5HXCIsXG4gICAgXCJWRVJCT1NFXCIsXG4gICAgXCJXQUxMRVRcIixcbiAgICBcIldIRU5cIixcbiAgICBcIldIRVJFXCIsXG4gICAgXCJXSVRIXCIsXG4gICAgXCJXSVRIT1VUXCJcbiAgXSxcbiAgb3BlcmF0b3JzOiBbXG4gICAgXCJBTkRcIixcbiAgICBcIkJFVFdFRU5cIixcbiAgICBcIklOXCIsXG4gICAgXCJMSUtFXCIsXG4gICAgXCJOT1RcIixcbiAgICBcIk9SXCIsXG4gICAgXCJJU1wiLFxuICAgIFwiTlVMTFwiLFxuICAgIFwiSU5URVJTRUNUXCIsXG4gICAgXCJVTklPTlwiLFxuICAgIFwiSU5ORVJcIixcbiAgICBcIkpPSU5cIixcbiAgICBcIkxFRlRcIixcbiAgICBcIk9VVEVSXCIsXG4gICAgXCJSSUdIVFwiXG4gIF0sXG4gIGJ1aWx0aW5GdW5jdGlvbnM6IFtcbiAgICBcImN1cnJlbnRfc2NoZW1hXCIsXG4gICAgXCJjdXJyZW50X3NjaGVtYXNcIixcbiAgICBcImhhc19kYXRhYmFzZV9wcml2aWxlZ2VcIixcbiAgICBcImhhc19zY2hlbWFfcHJpdmlsZWdlXCIsXG4gICAgXCJoYXNfdGFibGVfcHJpdmlsZWdlXCIsXG4gICAgXCJhZ2VcIixcbiAgICBcImN1cnJlbnRfdGltZVwiLFxuICAgIFwiY3VycmVudF90aW1lc3RhbXBcIixcbiAgICBcImxvY2FsdGltZVwiLFxuICAgIFwiaXNmaW5pdGVcIixcbiAgICBcIm5vd1wiLFxuICAgIFwiYXNjaWlcIixcbiAgICBcImdldF9iaXRcIixcbiAgICBcImdldF9ieXRlXCIsXG4gICAgXCJzZXRfYml0XCIsXG4gICAgXCJzZXRfYnl0ZVwiLFxuICAgIFwidG9fYXNjaWlcIixcbiAgICBcImFwcHJveGltYXRlIHBlcmNlbnRpbGVfZGlzY1wiLFxuICAgIFwiYXZnXCIsXG4gICAgXCJjb3VudFwiLFxuICAgIFwibGlzdGFnZ1wiLFxuICAgIFwibWF4XCIsXG4gICAgXCJtZWRpYW5cIixcbiAgICBcIm1pblwiLFxuICAgIFwicGVyY2VudGlsZV9jb250XCIsXG4gICAgXCJzdGRkZXZfc2FtcFwiLFxuICAgIFwic3RkZGV2X3BvcFwiLFxuICAgIFwic3VtXCIsXG4gICAgXCJ2YXJfc2FtcFwiLFxuICAgIFwidmFyX3BvcFwiLFxuICAgIFwiYml0X2FuZFwiLFxuICAgIFwiYml0X29yXCIsXG4gICAgXCJib29sX2FuZFwiLFxuICAgIFwiYm9vbF9vclwiLFxuICAgIFwiY3VtZV9kaXN0XCIsXG4gICAgXCJmaXJzdF92YWx1ZVwiLFxuICAgIFwibGFnXCIsXG4gICAgXCJsYXN0X3ZhbHVlXCIsXG4gICAgXCJsZWFkXCIsXG4gICAgXCJudGhfdmFsdWVcIixcbiAgICBcInJhdGlvX3RvX3JlcG9ydFwiLFxuICAgIFwiZGVuc2VfcmFua1wiLFxuICAgIFwibnRpbGVcIixcbiAgICBcInBlcmNlbnRfcmFua1wiLFxuICAgIFwicmFua1wiLFxuICAgIFwicm93X251bWJlclwiLFxuICAgIFwiY2FzZVwiLFxuICAgIFwiY29hbGVzY2VcIixcbiAgICBcImRlY29kZVwiLFxuICAgIFwiZ3JlYXRlc3RcIixcbiAgICBcImxlYXN0XCIsXG4gICAgXCJudmxcIixcbiAgICBcIm52bDJcIixcbiAgICBcIm51bGxpZlwiLFxuICAgIFwiYWRkX21vbnRoc1wiLFxuICAgIFwiYXQgdGltZSB6b25lXCIsXG4gICAgXCJjb252ZXJ0X3RpbWV6b25lXCIsXG4gICAgXCJjdXJyZW50X2RhdGVcIixcbiAgICBcImRhdGVfY21wXCIsXG4gICAgXCJkYXRlX2NtcF90aW1lc3RhbXBcIixcbiAgICBcImRhdGVfY21wX3RpbWVzdGFtcHR6XCIsXG4gICAgXCJkYXRlX3BhcnRfeWVhclwiLFxuICAgIFwiZGF0ZWFkZFwiLFxuICAgIFwiZGF0ZWRpZmZcIixcbiAgICBcImRhdGVfcGFydFwiLFxuICAgIFwiZGF0ZV90cnVuY1wiLFxuICAgIFwiZXh0cmFjdFwiLFxuICAgIFwiZ2V0ZGF0ZVwiLFxuICAgIFwiaW50ZXJ2YWxfY21wXCIsXG4gICAgXCJsYXN0X2RheVwiLFxuICAgIFwibW9udGhzX2JldHdlZW5cIixcbiAgICBcIm5leHRfZGF5XCIsXG4gICAgXCJzeXNkYXRlXCIsXG4gICAgXCJ0aW1lb2ZkYXlcIixcbiAgICBcInRpbWVzdGFtcF9jbXBcIixcbiAgICBcInRpbWVzdGFtcF9jbXBfZGF0ZVwiLFxuICAgIFwidGltZXN0YW1wX2NtcF90aW1lc3RhbXB0elwiLFxuICAgIFwidGltZXN0YW1wdHpfY21wXCIsXG4gICAgXCJ0aW1lc3RhbXB0el9jbXBfZGF0ZVwiLFxuICAgIFwidGltZXN0YW1wdHpfY21wX3RpbWVzdGFtcFwiLFxuICAgIFwidGltZXpvbmVcIixcbiAgICBcInRvX3RpbWVzdGFtcFwiLFxuICAgIFwidHJ1bmNcIixcbiAgICBcImFic1wiLFxuICAgIFwiYWNvc1wiLFxuICAgIFwiYXNpblwiLFxuICAgIFwiYXRhblwiLFxuICAgIFwiYXRhbjJcIixcbiAgICBcImNicnRcIixcbiAgICBcImNlaWxcIixcbiAgICBcImNlaWxpbmdcIixcbiAgICBcImNoZWNrc3VtXCIsXG4gICAgXCJjb3NcIixcbiAgICBcImNvdFwiLFxuICAgIFwiZGVncmVlc1wiLFxuICAgIFwiZGV4cFwiLFxuICAgIFwiZGxvZzFcIixcbiAgICBcImRsb2cxMFwiLFxuICAgIFwiZXhwXCIsXG4gICAgXCJmbG9vclwiLFxuICAgIFwibG5cIixcbiAgICBcImxvZ1wiLFxuICAgIFwibW9kXCIsXG4gICAgXCJwaVwiLFxuICAgIFwicG93ZXJcIixcbiAgICBcInJhZGlhbnNcIixcbiAgICBcInJhbmRvbVwiLFxuICAgIFwicm91bmRcIixcbiAgICBcInNpblwiLFxuICAgIFwic2lnblwiLFxuICAgIFwic3FydFwiLFxuICAgIFwidGFuXCIsXG4gICAgXCJ0b19oZXhcIixcbiAgICBcImJwY2hhcmNtcFwiLFxuICAgIFwiYnRyaW1cIixcbiAgICBcImJ0dGV4dF9wYXR0ZXJuX2NtcFwiLFxuICAgIFwiY2hhcl9sZW5ndGhcIixcbiAgICBcImNoYXJhY3Rlcl9sZW5ndGhcIixcbiAgICBcImNoYXJpbmRleFwiLFxuICAgIFwiY2hyXCIsXG4gICAgXCJjb25jYXRcIixcbiAgICBcImNyYzMyXCIsXG4gICAgXCJmdW5jX3NoYTFcIixcbiAgICBcImluaXRjYXBcIixcbiAgICBcImxlZnQgYW5kIHJpZ2h0c1wiLFxuICAgIFwibGVuXCIsXG4gICAgXCJsZW5ndGhcIixcbiAgICBcImxvd2VyXCIsXG4gICAgXCJscGFkIGFuZCBycGFkc1wiLFxuICAgIFwibHRyaW1cIixcbiAgICBcIm1kNVwiLFxuICAgIFwib2N0ZXRfbGVuZ3RoXCIsXG4gICAgXCJwb3NpdGlvblwiLFxuICAgIFwicXVvdGVfaWRlbnRcIixcbiAgICBcInF1b3RlX2xpdGVyYWxcIixcbiAgICBcInJlZ2V4cF9jb3VudFwiLFxuICAgIFwicmVnZXhwX2luc3RyXCIsXG4gICAgXCJyZWdleHBfcmVwbGFjZVwiLFxuICAgIFwicmVnZXhwX3N1YnN0clwiLFxuICAgIFwicmVwZWF0XCIsXG4gICAgXCJyZXBsYWNlXCIsXG4gICAgXCJyZXBsaWNhdGVcIixcbiAgICBcInJldmVyc2VcIixcbiAgICBcInJ0cmltXCIsXG4gICAgXCJzcGxpdF9wYXJ0XCIsXG4gICAgXCJzdHJwb3NcIixcbiAgICBcInN0cnRvbFwiLFxuICAgIFwic3Vic3RyaW5nXCIsXG4gICAgXCJ0ZXh0bGVuXCIsXG4gICAgXCJ0cmFuc2xhdGVcIixcbiAgICBcInRyaW1cIixcbiAgICBcInVwcGVyXCIsXG4gICAgXCJjYXN0XCIsXG4gICAgXCJjb252ZXJ0XCIsXG4gICAgXCJ0b19jaGFyXCIsXG4gICAgXCJ0b19kYXRlXCIsXG4gICAgXCJ0b19udW1iZXJcIixcbiAgICBcImpzb25fYXJyYXlfbGVuZ3RoXCIsXG4gICAgXCJqc29uX2V4dHJhY3RfYXJyYXlfZWxlbWVudF90ZXh0XCIsXG4gICAgXCJqc29uX2V4dHJhY3RfcGF0aF90ZXh0XCIsXG4gICAgXCJjdXJyZW50X3NldHRpbmdcIixcbiAgICBcInBnX2NhbmNlbF9iYWNrZW5kXCIsXG4gICAgXCJwZ190ZXJtaW5hdGVfYmFja2VuZFwiLFxuICAgIFwic2V0X2NvbmZpZ1wiLFxuICAgIFwiY3VycmVudF9kYXRhYmFzZVwiLFxuICAgIFwiY3VycmVudF91c2VyXCIsXG4gICAgXCJjdXJyZW50X3VzZXJfaWRcIixcbiAgICBcInBnX2JhY2tlbmRfcGlkXCIsXG4gICAgXCJwZ19sYXN0X2NvcHlfY291bnRcIixcbiAgICBcInBnX2xhc3RfY29weV9pZFwiLFxuICAgIFwicGdfbGFzdF9xdWVyeV9pZFwiLFxuICAgIFwicGdfbGFzdF91bmxvYWRfY291bnRcIixcbiAgICBcInNlc3Npb25fdXNlclwiLFxuICAgIFwic2xpY2VfbnVtXCIsXG4gICAgXCJ1c2VyXCIsXG4gICAgXCJ2ZXJzaW9uXCIsXG4gICAgXCJhYmJyZXZcIixcbiAgICBcImFjb3NkXCIsXG4gICAgXCJhbnlcIixcbiAgICBcImFyZWFcIixcbiAgICBcImFycmF5X2FnZ1wiLFxuICAgIFwiYXJyYXlfYXBwZW5kXCIsXG4gICAgXCJhcnJheV9jYXRcIixcbiAgICBcImFycmF5X2RpbXNcIixcbiAgICBcImFycmF5X2ZpbGxcIixcbiAgICBcImFycmF5X2xlbmd0aFwiLFxuICAgIFwiYXJyYXlfbG93ZXJcIixcbiAgICBcImFycmF5X25kaW1zXCIsXG4gICAgXCJhcnJheV9wb3NpdGlvblwiLFxuICAgIFwiYXJyYXlfcG9zaXRpb25zXCIsXG4gICAgXCJhcnJheV9wcmVwZW5kXCIsXG4gICAgXCJhcnJheV9yZW1vdmVcIixcbiAgICBcImFycmF5X3JlcGxhY2VcIixcbiAgICBcImFycmF5X3RvX2pzb25cIixcbiAgICBcImFycmF5X3RvX3N0cmluZ1wiLFxuICAgIFwiYXJyYXlfdG9fdHN2ZWN0b3JcIixcbiAgICBcImFycmF5X3VwcGVyXCIsXG4gICAgXCJhc2luZFwiLFxuICAgIFwiYXRhbjJkXCIsXG4gICAgXCJhdGFuZFwiLFxuICAgIFwiYml0XCIsXG4gICAgXCJiaXRfbGVuZ3RoXCIsXG4gICAgXCJib3VuZF9ib3hcIixcbiAgICBcImJveFwiLFxuICAgIFwiYnJpbl9zdW1tYXJpemVfbmV3X3ZhbHVlc1wiLFxuICAgIFwiYnJvYWRjYXN0XCIsXG4gICAgXCJjYXJkaW5hbGl0eVwiLFxuICAgIFwiY2VudGVyXCIsXG4gICAgXCJjaXJjbGVcIixcbiAgICBcImNsb2NrX3RpbWVzdGFtcFwiLFxuICAgIFwiY29sX2Rlc2NyaXB0aW9uXCIsXG4gICAgXCJjb25jYXRfd3NcIixcbiAgICBcImNvbnZlcnRfZnJvbVwiLFxuICAgIFwiY29udmVydF90b1wiLFxuICAgIFwiY29yclwiLFxuICAgIFwiY29zZFwiLFxuICAgIFwiY290ZFwiLFxuICAgIFwiY292YXJfcG9wXCIsXG4gICAgXCJjb3Zhcl9zYW1wXCIsXG4gICAgXCJjdXJyZW50X2NhdGFsb2dcIixcbiAgICBcImN1cnJlbnRfcXVlcnlcIixcbiAgICBcImN1cnJlbnRfcm9sZVwiLFxuICAgIFwiY3VycnZhbFwiLFxuICAgIFwiY3Vyc29yX3RvX3htbFwiLFxuICAgIFwiZGlhbWV0ZXJcIixcbiAgICBcImRpdlwiLFxuICAgIFwiZW5jb2RlXCIsXG4gICAgXCJlbnVtX2ZpcnN0XCIsXG4gICAgXCJlbnVtX2xhc3RcIixcbiAgICBcImVudW1fcmFuZ2VcIixcbiAgICBcImV2ZXJ5XCIsXG4gICAgXCJmYW1pbHlcIixcbiAgICBcImZvcm1hdFwiLFxuICAgIFwiZm9ybWF0X3R5cGVcIixcbiAgICBcImdlbmVyYXRlX3Nlcmllc1wiLFxuICAgIFwiZ2VuZXJhdGVfc3Vic2NyaXB0c1wiLFxuICAgIFwiZ2V0X2N1cnJlbnRfdHNfY29uZmlnXCIsXG4gICAgXCJnaW5fY2xlYW5fcGVuZGluZ19saXN0XCIsXG4gICAgXCJncm91cGluZ1wiLFxuICAgIFwiaGFzX2FueV9jb2x1bW5fcHJpdmlsZWdlXCIsXG4gICAgXCJoYXNfY29sdW1uX3ByaXZpbGVnZVwiLFxuICAgIFwiaGFzX2ZvcmVpZ25fZGF0YV93cmFwcGVyX3ByaXZpbGVnZVwiLFxuICAgIFwiaGFzX2Z1bmN0aW9uX3ByaXZpbGVnZVwiLFxuICAgIFwiaGFzX2xhbmd1YWdlX3ByaXZpbGVnZVwiLFxuICAgIFwiaGFzX3NlcXVlbmNlX3ByaXZpbGVnZVwiLFxuICAgIFwiaGFzX3NlcnZlcl9wcml2aWxlZ2VcIixcbiAgICBcImhhc190YWJsZXNwYWNlX3ByaXZpbGVnZVwiLFxuICAgIFwiaGFzX3R5cGVfcHJpdmlsZWdlXCIsXG4gICAgXCJoZWlnaHRcIixcbiAgICBcImhvc3RcIixcbiAgICBcImhvc3RtYXNrXCIsXG4gICAgXCJpbmV0X2NsaWVudF9hZGRyXCIsXG4gICAgXCJpbmV0X2NsaWVudF9wb3J0XCIsXG4gICAgXCJpbmV0X21lcmdlXCIsXG4gICAgXCJpbmV0X3NhbWVfZmFtaWx5XCIsXG4gICAgXCJpbmV0X3NlcnZlcl9hZGRyXCIsXG4gICAgXCJpbmV0X3NlcnZlcl9wb3J0XCIsXG4gICAgXCJpc2Nsb3NlZFwiLFxuICAgIFwiaXNlbXB0eVwiLFxuICAgIFwiaXNvcGVuXCIsXG4gICAgXCJqc29uX2FnZ1wiLFxuICAgIFwianNvbl9vYmplY3RcIixcbiAgICBcImpzb25fb2JqZWN0X2FnZ1wiLFxuICAgIFwianNvbl9wb3B1bGF0ZV9yZWNvcmRcIixcbiAgICBcImpzb25fcG9wdWxhdGVfcmVjb3Jkc2V0XCIsXG4gICAgXCJqc29uX3RvX3JlY29yZFwiLFxuICAgIFwianNvbl90b19yZWNvcmRzZXRcIixcbiAgICBcImpzb25iX2FnZ1wiLFxuICAgIFwianNvbmJfb2JqZWN0X2FnZ1wiLFxuICAgIFwianVzdGlmeV9kYXlzXCIsXG4gICAgXCJqdXN0aWZ5X2hvdXJzXCIsXG4gICAgXCJqdXN0aWZ5X2ludGVydmFsXCIsXG4gICAgXCJsYXN0dmFsXCIsXG4gICAgXCJsZWZ0XCIsXG4gICAgXCJsaW5lXCIsXG4gICAgXCJsb2NhbHRpbWVzdGFtcFwiLFxuICAgIFwibG93ZXJfaW5jXCIsXG4gICAgXCJsb3dlcl9pbmZcIixcbiAgICBcImxwYWRcIixcbiAgICBcImxzZWdcIixcbiAgICBcIm1ha2VfZGF0ZVwiLFxuICAgIFwibWFrZV9pbnRlcnZhbFwiLFxuICAgIFwibWFrZV90aW1lXCIsXG4gICAgXCJtYWtlX3RpbWVzdGFtcFwiLFxuICAgIFwibWFrZV90aW1lc3RhbXB0elwiLFxuICAgIFwibWFza2xlblwiLFxuICAgIFwibW9kZVwiLFxuICAgIFwibmV0bWFza1wiLFxuICAgIFwibmV0d29ya1wiLFxuICAgIFwibmV4dHZhbFwiLFxuICAgIFwibnBvaW50c1wiLFxuICAgIFwibnVtX25vbm51bGxzXCIsXG4gICAgXCJudW1fbnVsbHNcIixcbiAgICBcIm51bW5vZGVcIixcbiAgICBcIm9ial9kZXNjcmlwdGlvblwiLFxuICAgIFwib3ZlcmxheVwiLFxuICAgIFwicGFyc2VfaWRlbnRcIixcbiAgICBcInBhdGhcIixcbiAgICBcInBjbG9zZVwiLFxuICAgIFwicGVyY2VudGlsZV9kaXNjXCIsXG4gICAgXCJwZ19hZHZpc29yeV9sb2NrXCIsXG4gICAgXCJwZ19hZHZpc29yeV9sb2NrX3NoYXJlZFwiLFxuICAgIFwicGdfYWR2aXNvcnlfdW5sb2NrXCIsXG4gICAgXCJwZ19hZHZpc29yeV91bmxvY2tfYWxsXCIsXG4gICAgXCJwZ19hZHZpc29yeV91bmxvY2tfc2hhcmVkXCIsXG4gICAgXCJwZ19hZHZpc29yeV94YWN0X2xvY2tcIixcbiAgICBcInBnX2Fkdmlzb3J5X3hhY3RfbG9ja19zaGFyZWRcIixcbiAgICBcInBnX2JhY2t1cF9zdGFydF90aW1lXCIsXG4gICAgXCJwZ19ibG9ja2luZ19waWRzXCIsXG4gICAgXCJwZ19jbGllbnRfZW5jb2RpbmdcIixcbiAgICBcInBnX2NvbGxhdGlvbl9pc192aXNpYmxlXCIsXG4gICAgXCJwZ19jb2x1bW5fc2l6ZVwiLFxuICAgIFwicGdfY29uZl9sb2FkX3RpbWVcIixcbiAgICBcInBnX2NvbnRyb2xfY2hlY2twb2ludFwiLFxuICAgIFwicGdfY29udHJvbF9pbml0XCIsXG4gICAgXCJwZ19jb250cm9sX3JlY292ZXJ5XCIsXG4gICAgXCJwZ19jb250cm9sX3N5c3RlbVwiLFxuICAgIFwicGdfY29udmVyc2lvbl9pc192aXNpYmxlXCIsXG4gICAgXCJwZ19jcmVhdGVfbG9naWNhbF9yZXBsaWNhdGlvbl9zbG90XCIsXG4gICAgXCJwZ19jcmVhdGVfcGh5c2ljYWxfcmVwbGljYXRpb25fc2xvdFwiLFxuICAgIFwicGdfY3JlYXRlX3Jlc3RvcmVfcG9pbnRcIixcbiAgICBcInBnX2N1cnJlbnRfeGxvZ19mbHVzaF9sb2NhdGlvblwiLFxuICAgIFwicGdfY3VycmVudF94bG9nX2luc2VydF9sb2NhdGlvblwiLFxuICAgIFwicGdfY3VycmVudF94bG9nX2xvY2F0aW9uXCIsXG4gICAgXCJwZ19kYXRhYmFzZV9zaXplXCIsXG4gICAgXCJwZ19kZXNjcmliZV9vYmplY3RcIixcbiAgICBcInBnX2Ryb3BfcmVwbGljYXRpb25fc2xvdFwiLFxuICAgIFwicGdfZXhwb3J0X3NuYXBzaG90XCIsXG4gICAgXCJwZ19maWxlbm9kZV9yZWxhdGlvblwiLFxuICAgIFwicGdfZnVuY3Rpb25faXNfdmlzaWJsZVwiLFxuICAgIFwicGdfZ2V0X2NvbnN0cmFpbnRkZWZcIixcbiAgICBcInBnX2dldF9leHByXCIsXG4gICAgXCJwZ19nZXRfZnVuY3Rpb25fYXJndW1lbnRzXCIsXG4gICAgXCJwZ19nZXRfZnVuY3Rpb25faWRlbnRpdHlfYXJndW1lbnRzXCIsXG4gICAgXCJwZ19nZXRfZnVuY3Rpb25fcmVzdWx0XCIsXG4gICAgXCJwZ19nZXRfZnVuY3Rpb25kZWZcIixcbiAgICBcInBnX2dldF9pbmRleGRlZlwiLFxuICAgIFwicGdfZ2V0X2tleXdvcmRzXCIsXG4gICAgXCJwZ19nZXRfb2JqZWN0X2FkZHJlc3NcIixcbiAgICBcInBnX2dldF9vd25lZF9zZXF1ZW5jZVwiLFxuICAgIFwicGdfZ2V0X3J1bGVkZWZcIixcbiAgICBcInBnX2dldF9zZXJpYWxfc2VxdWVuY2VcIixcbiAgICBcInBnX2dldF90cmlnZ2VyZGVmXCIsXG4gICAgXCJwZ19nZXRfdXNlcmJ5aWRcIixcbiAgICBcInBnX2dldF92aWV3ZGVmXCIsXG4gICAgXCJwZ19oYXNfcm9sZVwiLFxuICAgIFwicGdfaWRlbnRpZnlfb2JqZWN0XCIsXG4gICAgXCJwZ19pZGVudGlmeV9vYmplY3RfYXNfYWRkcmVzc1wiLFxuICAgIFwicGdfaW5kZXhfY29sdW1uX2hhc19wcm9wZXJ0eVwiLFxuICAgIFwicGdfaW5kZXhfaGFzX3Byb3BlcnR5XCIsXG4gICAgXCJwZ19pbmRleGFtX2hhc19wcm9wZXJ0eVwiLFxuICAgIFwicGdfaW5kZXhlc19zaXplXCIsXG4gICAgXCJwZ19pc19pbl9iYWNrdXBcIixcbiAgICBcInBnX2lzX2luX3JlY292ZXJ5XCIsXG4gICAgXCJwZ19pc19vdGhlcl90ZW1wX3NjaGVtYVwiLFxuICAgIFwicGdfaXNfeGxvZ19yZXBsYXlfcGF1c2VkXCIsXG4gICAgXCJwZ19sYXN0X2NvbW1pdHRlZF94YWN0XCIsXG4gICAgXCJwZ19sYXN0X3hhY3RfcmVwbGF5X3RpbWVzdGFtcFwiLFxuICAgIFwicGdfbGFzdF94bG9nX3JlY2VpdmVfbG9jYXRpb25cIixcbiAgICBcInBnX2xhc3RfeGxvZ19yZXBsYXlfbG9jYXRpb25cIixcbiAgICBcInBnX2xpc3RlbmluZ19jaGFubmVsc1wiLFxuICAgIFwicGdfbG9naWNhbF9lbWl0X21lc3NhZ2VcIixcbiAgICBcInBnX2xvZ2ljYWxfc2xvdF9nZXRfYmluYXJ5X2NoYW5nZXNcIixcbiAgICBcInBnX2xvZ2ljYWxfc2xvdF9nZXRfY2hhbmdlc1wiLFxuICAgIFwicGdfbG9naWNhbF9zbG90X3BlZWtfYmluYXJ5X2NoYW5nZXNcIixcbiAgICBcInBnX2xvZ2ljYWxfc2xvdF9wZWVrX2NoYW5nZXNcIixcbiAgICBcInBnX2xzX2RpclwiLFxuICAgIFwicGdfbXlfdGVtcF9zY2hlbWFcIixcbiAgICBcInBnX25vdGlmaWNhdGlvbl9xdWV1ZV91c2FnZVwiLFxuICAgIFwicGdfb3BjbGFzc19pc192aXNpYmxlXCIsXG4gICAgXCJwZ19vcGVyYXRvcl9pc192aXNpYmxlXCIsXG4gICAgXCJwZ19vcGZhbWlseV9pc192aXNpYmxlXCIsXG4gICAgXCJwZ19vcHRpb25zX3RvX3RhYmxlXCIsXG4gICAgXCJwZ19wb3N0bWFzdGVyX3N0YXJ0X3RpbWVcIixcbiAgICBcInBnX3JlYWRfYmluYXJ5X2ZpbGVcIixcbiAgICBcInBnX3JlYWRfZmlsZVwiLFxuICAgIFwicGdfcmVsYXRpb25fZmlsZW5vZGVcIixcbiAgICBcInBnX3JlbGF0aW9uX2ZpbGVwYXRoXCIsXG4gICAgXCJwZ19yZWxhdGlvbl9zaXplXCIsXG4gICAgXCJwZ19yZWxvYWRfY29uZlwiLFxuICAgIFwicGdfcmVwbGljYXRpb25fb3JpZ2luX2NyZWF0ZVwiLFxuICAgIFwicGdfcmVwbGljYXRpb25fb3JpZ2luX2Ryb3BcIixcbiAgICBcInBnX3JlcGxpY2F0aW9uX29yaWdpbl9vaWRcIixcbiAgICBcInBnX3JlcGxpY2F0aW9uX29yaWdpbl9wcm9ncmVzc1wiLFxuICAgIFwicGdfcmVwbGljYXRpb25fb3JpZ2luX3Nlc3Npb25faXNfc2V0dXBcIixcbiAgICBcInBnX3JlcGxpY2F0aW9uX29yaWdpbl9zZXNzaW9uX3Byb2dyZXNzXCIsXG4gICAgXCJwZ19yZXBsaWNhdGlvbl9vcmlnaW5fc2Vzc2lvbl9yZXNldFwiLFxuICAgIFwicGdfcmVwbGljYXRpb25fb3JpZ2luX3Nlc3Npb25fc2V0dXBcIixcbiAgICBcInBnX3JlcGxpY2F0aW9uX29yaWdpbl94YWN0X3Jlc2V0XCIsXG4gICAgXCJwZ19yZXBsaWNhdGlvbl9vcmlnaW5feGFjdF9zZXR1cFwiLFxuICAgIFwicGdfcm90YXRlX2xvZ2ZpbGVcIixcbiAgICBcInBnX3NpemVfYnl0ZXNcIixcbiAgICBcInBnX3NpemVfcHJldHR5XCIsXG4gICAgXCJwZ19zbGVlcFwiLFxuICAgIFwicGdfc2xlZXBfZm9yXCIsXG4gICAgXCJwZ19zbGVlcF91bnRpbFwiLFxuICAgIFwicGdfc3RhcnRfYmFja3VwXCIsXG4gICAgXCJwZ19zdGF0X2ZpbGVcIixcbiAgICBcInBnX3N0b3BfYmFja3VwXCIsXG4gICAgXCJwZ19zd2l0Y2hfeGxvZ1wiLFxuICAgIFwicGdfdGFibGVfaXNfdmlzaWJsZVwiLFxuICAgIFwicGdfdGFibGVfc2l6ZVwiLFxuICAgIFwicGdfdGFibGVzcGFjZV9kYXRhYmFzZXNcIixcbiAgICBcInBnX3RhYmxlc3BhY2VfbG9jYXRpb25cIixcbiAgICBcInBnX3RhYmxlc3BhY2Vfc2l6ZVwiLFxuICAgIFwicGdfdG90YWxfcmVsYXRpb25fc2l6ZVwiLFxuICAgIFwicGdfdHJpZ2dlcl9kZXB0aFwiLFxuICAgIFwicGdfdHJ5X2Fkdmlzb3J5X2xvY2tcIixcbiAgICBcInBnX3RyeV9hZHZpc29yeV9sb2NrX3NoYXJlZFwiLFxuICAgIFwicGdfdHJ5X2Fkdmlzb3J5X3hhY3RfbG9ja1wiLFxuICAgIFwicGdfdHJ5X2Fkdmlzb3J5X3hhY3RfbG9ja19zaGFyZWRcIixcbiAgICBcInBnX3RzX2NvbmZpZ19pc192aXNpYmxlXCIsXG4gICAgXCJwZ190c19kaWN0X2lzX3Zpc2libGVcIixcbiAgICBcInBnX3RzX3BhcnNlcl9pc192aXNpYmxlXCIsXG4gICAgXCJwZ190c190ZW1wbGF0ZV9pc192aXNpYmxlXCIsXG4gICAgXCJwZ190eXBlX2lzX3Zpc2libGVcIixcbiAgICBcInBnX3R5cGVvZlwiLFxuICAgIFwicGdfeGFjdF9jb21taXRfdGltZXN0YW1wXCIsXG4gICAgXCJwZ194bG9nX2xvY2F0aW9uX2RpZmZcIixcbiAgICBcInBnX3hsb2dfcmVwbGF5X3BhdXNlXCIsXG4gICAgXCJwZ194bG9nX3JlcGxheV9yZXN1bWVcIixcbiAgICBcInBnX3hsb2dmaWxlX25hbWVcIixcbiAgICBcInBnX3hsb2dmaWxlX25hbWVfb2Zmc2V0XCIsXG4gICAgXCJwaHJhc2V0b190c3F1ZXJ5XCIsXG4gICAgXCJwbGFpbnRvX3RzcXVlcnlcIixcbiAgICBcInBvaW50XCIsXG4gICAgXCJwb2x5Z29uXCIsXG4gICAgXCJwb3BlblwiLFxuICAgIFwicHFzZXJ2ZXJ2ZXJzaW9uXCIsXG4gICAgXCJxdWVyeV90b194bWxcIixcbiAgICBcInF1ZXJ5dHJlZVwiLFxuICAgIFwicXVvdGVfbnVsbGFibGVcIixcbiAgICBcInJhZGl1c1wiLFxuICAgIFwicmFuZ2VfbWVyZ2VcIixcbiAgICBcInJlZ2V4cF9tYXRjaGVzXCIsXG4gICAgXCJyZWdleHBfc3BsaXRfdG9fYXJyYXlcIixcbiAgICBcInJlZ2V4cF9zcGxpdF90b190YWJsZVwiLFxuICAgIFwicmVncl9hdmd4XCIsXG4gICAgXCJyZWdyX2F2Z3lcIixcbiAgICBcInJlZ3JfY291bnRcIixcbiAgICBcInJlZ3JfaW50ZXJjZXB0XCIsXG4gICAgXCJyZWdyX3IyXCIsXG4gICAgXCJyZWdyX3Nsb3BlXCIsXG4gICAgXCJyZWdyX3N4eFwiLFxuICAgIFwicmVncl9zeHlcIixcbiAgICBcInJlZ3Jfc3l5XCIsXG4gICAgXCJyaWdodFwiLFxuICAgIFwicm93X3NlY3VyaXR5X2FjdGl2ZVwiLFxuICAgIFwicm93X3RvX2pzb25cIixcbiAgICBcInJwYWRcIixcbiAgICBcInNjYWxlXCIsXG4gICAgXCJzZXRfbWFza2xlblwiLFxuICAgIFwic2V0c2VlZFwiLFxuICAgIFwic2V0dmFsXCIsXG4gICAgXCJzZXR3ZWlnaHRcIixcbiAgICBcInNob2JqX2Rlc2NyaXB0aW9uXCIsXG4gICAgXCJzaW5kXCIsXG4gICAgXCJzcHJpbnRmXCIsXG4gICAgXCJzdGF0ZW1lbnRfdGltZXN0YW1wXCIsXG4gICAgXCJzdGRkZXZcIixcbiAgICBcInN0cmluZ19hZ2dcIixcbiAgICBcInN0cmluZ190b19hcnJheVwiLFxuICAgIFwic3RyaXBcIixcbiAgICBcInN1YnN0clwiLFxuICAgIFwidGFibGVfdG9feG1sXCIsXG4gICAgXCJ0YWJsZV90b194bWxfYW5kX3htbHNjaGVtYVwiLFxuICAgIFwidGFuZFwiLFxuICAgIFwidGV4dFwiLFxuICAgIFwidG9fanNvblwiLFxuICAgIFwidG9fcmVnY2xhc3NcIixcbiAgICBcInRvX3JlZ25hbWVzcGFjZVwiLFxuICAgIFwidG9fcmVnb3BlclwiLFxuICAgIFwidG9fcmVnb3BlcmF0b3JcIixcbiAgICBcInRvX3JlZ3Byb2NcIixcbiAgICBcInRvX3JlZ3Byb2NlZHVyZVwiLFxuICAgIFwidG9fcmVncm9sZVwiLFxuICAgIFwidG9fcmVndHlwZVwiLFxuICAgIFwidG9fdHNxdWVyeVwiLFxuICAgIFwidG9fdHN2ZWN0b3JcIixcbiAgICBcInRyYW5zYWN0aW9uX3RpbWVzdGFtcFwiLFxuICAgIFwidHNfZGVidWdcIixcbiAgICBcInRzX2RlbGV0ZVwiLFxuICAgIFwidHNfZmlsdGVyXCIsXG4gICAgXCJ0c19oZWFkbGluZVwiLFxuICAgIFwidHNfbGV4aXplXCIsXG4gICAgXCJ0c19wYXJzZVwiLFxuICAgIFwidHNfcmFua1wiLFxuICAgIFwidHNfcmFua19jZFwiLFxuICAgIFwidHNfcmV3cml0ZVwiLFxuICAgIFwidHNfc3RhdFwiLFxuICAgIFwidHNfdG9rZW5fdHlwZVwiLFxuICAgIFwidHNxdWVyeV9waHJhc2VcIixcbiAgICBcInRzdmVjdG9yX3RvX2FycmF5XCIsXG4gICAgXCJ0c3ZlY3Rvcl91cGRhdGVfdHJpZ2dlclwiLFxuICAgIFwidHN2ZWN0b3JfdXBkYXRlX3RyaWdnZXJfY29sdW1uXCIsXG4gICAgXCJ0eGlkX2N1cnJlbnRcIixcbiAgICBcInR4aWRfY3VycmVudF9zbmFwc2hvdFwiLFxuICAgIFwidHhpZF9zbmFwc2hvdF94aXBcIixcbiAgICBcInR4aWRfc25hcHNob3RfeG1heFwiLFxuICAgIFwidHhpZF9zbmFwc2hvdF94bWluXCIsXG4gICAgXCJ0eGlkX3Zpc2libGVfaW5fc25hcHNob3RcIixcbiAgICBcInVubmVzdFwiLFxuICAgIFwidXBwZXJfaW5jXCIsXG4gICAgXCJ1cHBlcl9pbmZcIixcbiAgICBcInZhcmlhbmNlXCIsXG4gICAgXCJ3aWR0aFwiLFxuICAgIFwid2lkdGhfYnVja2V0XCIsXG4gICAgXCJ4bWxfaXNfd2VsbF9mb3JtZWRcIixcbiAgICBcInhtbF9pc193ZWxsX2Zvcm1lZF9jb250ZW50XCIsXG4gICAgXCJ4bWxfaXNfd2VsbF9mb3JtZWRfZG9jdW1lbnRcIixcbiAgICBcInhtbGFnZ1wiLFxuICAgIFwieG1sY29tbWVudFwiLFxuICAgIFwieG1sY29uY2F0XCIsXG4gICAgXCJ4bWxlbGVtZW50XCIsXG4gICAgXCJ4bWxleGlzdHNcIixcbiAgICBcInhtbGZvcmVzdFwiLFxuICAgIFwieG1scGFyc2VcIixcbiAgICBcInhtbHBpXCIsXG4gICAgXCJ4bWxyb290XCIsXG4gICAgXCJ4bWxzZXJpYWxpemVcIixcbiAgICBcInhwYXRoXCIsXG4gICAgXCJ4cGF0aF9leGlzdHNcIlxuICBdLFxuICBidWlsdGluVmFyaWFibGVzOiBbXG4gICAgLy8gTk9UIFNVUFBPUlRFRFxuICBdLFxuICBwc2V1ZG9Db2x1bW5zOiBbXG4gICAgLy8gTk9UIFNVUFBPUlRFRFxuICBdLFxuICB0b2tlbml6ZXI6IHtcbiAgICByb290OiBbXG4gICAgICB7IGluY2x1ZGU6IFwiQGNvbW1lbnRzXCIgfSxcbiAgICAgIHsgaW5jbHVkZTogXCJAd2hpdGVzcGFjZVwiIH0sXG4gICAgICB7IGluY2x1ZGU6IFwiQHBzZXVkb0NvbHVtbnNcIiB9LFxuICAgICAgeyBpbmNsdWRlOiBcIkBudW1iZXJzXCIgfSxcbiAgICAgIHsgaW5jbHVkZTogXCJAc3RyaW5nc1wiIH0sXG4gICAgICB7IGluY2x1ZGU6IFwiQGNvbXBsZXhJZGVudGlmaWVyc1wiIH0sXG4gICAgICB7IGluY2x1ZGU6IFwiQHNjb3Blc1wiIH0sXG4gICAgICBbL1s7LC5dLywgXCJkZWxpbWl0ZXJcIl0sXG4gICAgICBbL1soKV0vLCBcIkBicmFja2V0c1wiXSxcbiAgICAgIFtcbiAgICAgICAgL1tcXHdAIyRdKy8sXG4gICAgICAgIHtcbiAgICAgICAgICBjYXNlczoge1xuICAgICAgICAgICAgXCJAa2V5d29yZHNcIjogXCJrZXl3b3JkXCIsXG4gICAgICAgICAgICBcIkBvcGVyYXRvcnNcIjogXCJvcGVyYXRvclwiLFxuICAgICAgICAgICAgXCJAYnVpbHRpblZhcmlhYmxlc1wiOiBcInByZWRlZmluZWRcIixcbiAgICAgICAgICAgIFwiQGJ1aWx0aW5GdW5jdGlvbnNcIjogXCJwcmVkZWZpbmVkXCIsXG4gICAgICAgICAgICBcIkBkZWZhdWx0XCI6IFwiaWRlbnRpZmllclwiXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgWy9bPD49ISUmK1xcLSovfH5eXS8sIFwib3BlcmF0b3JcIl1cbiAgICBdLFxuICAgIHdoaXRlc3BhY2U6IFtbL1xccysvLCBcIndoaXRlXCJdXSxcbiAgICBjb21tZW50czogW1xuICAgICAgWy8tLSsuKi8sIFwiY29tbWVudFwiXSxcbiAgICAgIFsvXFwvXFwqLywgeyB0b2tlbjogXCJjb21tZW50LnF1b3RlXCIsIG5leHQ6IFwiQGNvbW1lbnRcIiB9XVxuICAgIF0sXG4gICAgY29tbWVudDogW1xuICAgICAgWy9bXiovXSsvLCBcImNvbW1lbnRcIl0sXG4gICAgICAvLyBOb3Qgc3VwcG9ydGluZyBuZXN0ZWQgY29tbWVudHMsIGFzIG5lc3RlZCBjb21tZW50cyBzZWVtIHRvIG5vdCBiZSBzdGFuZGFyZD9cbiAgICAgIC8vIGkuZS4gaHR0cDovL3N0YWNrb3ZlcmZsb3cuY29tL3F1ZXN0aW9ucy83MjgxNzIvYXJlLXRoZXJlLW11bHRpbGluZS1jb21tZW50LWRlbGltaXRlcnMtaW4tc3FsLXRoYXQtYXJlLXZlbmRvci1hZ25vc3RpY1xuICAgICAgLy8gWy9cXC9cXCovLCB7IHRva2VuOiAnY29tbWVudC5xdW90ZScsIG5leHQ6ICdAcHVzaCcgfV0sICAgIC8vIG5lc3RlZCBjb21tZW50IG5vdCBhbGxvd2VkIDotKFxuICAgICAgWy9cXCpcXC8vLCB7IHRva2VuOiBcImNvbW1lbnQucXVvdGVcIiwgbmV4dDogXCJAcG9wXCIgfV0sXG4gICAgICBbLy4vLCBcImNvbW1lbnRcIl1cbiAgICBdLFxuICAgIHBzZXVkb0NvbHVtbnM6IFtcbiAgICAgIFtcbiAgICAgICAgL1skXVtBLVphLXpfXVtcXHdAIyRdKi8sXG4gICAgICAgIHtcbiAgICAgICAgICBjYXNlczoge1xuICAgICAgICAgICAgXCJAcHNldWRvQ29sdW1uc1wiOiBcInByZWRlZmluZWRcIixcbiAgICAgICAgICAgIFwiQGRlZmF1bHRcIjogXCJpZGVudGlmaWVyXCJcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIF1cbiAgICBdLFxuICAgIG51bWJlcnM6IFtcbiAgICAgIFsvMFt4WF1bMC05YS1mQS1GXSovLCBcIm51bWJlclwiXSxcbiAgICAgIFsvWyRdWystXSpcXGQqKFxcLlxcZCopPy8sIFwibnVtYmVyXCJdLFxuICAgICAgWy8oKFxcZCsoXFwuXFxkKik/KXwoXFwuXFxkKykpKFtlRV1bXFwtK10/XFxkKyk/LywgXCJudW1iZXJcIl1cbiAgICBdLFxuICAgIHN0cmluZ3M6IFtbLycvLCB7IHRva2VuOiBcInN0cmluZ1wiLCBuZXh0OiBcIkBzdHJpbmdcIiB9XV0sXG4gICAgc3RyaW5nOiBbXG4gICAgICBbL1teJ10rLywgXCJzdHJpbmdcIl0sXG4gICAgICBbLycnLywgXCJzdHJpbmdcIl0sXG4gICAgICBbLycvLCB7IHRva2VuOiBcInN0cmluZ1wiLCBuZXh0OiBcIkBwb3BcIiB9XVxuICAgIF0sXG4gICAgY29tcGxleElkZW50aWZpZXJzOiBbWy9cIi8sIHsgdG9rZW46IFwiaWRlbnRpZmllci5xdW90ZVwiLCBuZXh0OiBcIkBxdW90ZWRJZGVudGlmaWVyXCIgfV1dLFxuICAgIHF1b3RlZElkZW50aWZpZXI6IFtcbiAgICAgIFsvW15cIl0rLywgXCJpZGVudGlmaWVyXCJdLFxuICAgICAgWy9cIlwiLywgXCJpZGVudGlmaWVyXCJdLFxuICAgICAgWy9cIi8sIHsgdG9rZW46IFwiaWRlbnRpZmllci5xdW90ZVwiLCBuZXh0OiBcIkBwb3BcIiB9XVxuICAgIF0sXG4gICAgc2NvcGVzOiBbXG4gICAgICAvLyBOT1QgU1VQUE9SVEVEXG4gICAgXVxuICB9XG59O1xuZXhwb3J0IHtcbiAgY29uZixcbiAgbGFuZ3VhZ2Vcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/redshift/redshift.js\n"));

/***/ })

}]);