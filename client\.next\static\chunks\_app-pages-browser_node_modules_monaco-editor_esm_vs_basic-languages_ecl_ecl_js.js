"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_ecl_ecl_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/ecl/ecl.js":
/*!***********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/ecl/ecl.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/ecl/ecl.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".ecl\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  pounds: [\n    \"append\",\n    \"break\",\n    \"declare\",\n    \"demangle\",\n    \"end\",\n    \"for\",\n    \"getdatatype\",\n    \"if\",\n    \"inmodule\",\n    \"loop\",\n    \"mangle\",\n    \"onwarning\",\n    \"option\",\n    \"set\",\n    \"stored\",\n    \"uniquename\"\n  ].join(\"|\"),\n  keywords: [\n    \"__compressed__\",\n    \"after\",\n    \"all\",\n    \"and\",\n    \"any\",\n    \"as\",\n    \"atmost\",\n    \"before\",\n    \"beginc\",\n    \"best\",\n    \"between\",\n    \"case\",\n    \"cluster\",\n    \"compressed\",\n    \"compression\",\n    \"const\",\n    \"counter\",\n    \"csv\",\n    \"default\",\n    \"descend\",\n    \"embed\",\n    \"encoding\",\n    \"encrypt\",\n    \"end\",\n    \"endc\",\n    \"endembed\",\n    \"endmacro\",\n    \"enum\",\n    \"escape\",\n    \"except\",\n    \"exclusive\",\n    \"expire\",\n    \"export\",\n    \"extend\",\n    \"fail\",\n    \"few\",\n    \"fileposition\",\n    \"first\",\n    \"flat\",\n    \"forward\",\n    \"from\",\n    \"full\",\n    \"function\",\n    \"functionmacro\",\n    \"group\",\n    \"grouped\",\n    \"heading\",\n    \"hole\",\n    \"ifblock\",\n    \"import\",\n    \"in\",\n    \"inner\",\n    \"interface\",\n    \"internal\",\n    \"joined\",\n    \"keep\",\n    \"keyed\",\n    \"last\",\n    \"left\",\n    \"limit\",\n    \"linkcounted\",\n    \"literal\",\n    \"little_endian\",\n    \"load\",\n    \"local\",\n    \"locale\",\n    \"lookup\",\n    \"lzw\",\n    \"macro\",\n    \"many\",\n    \"maxcount\",\n    \"maxlength\",\n    \"min skew\",\n    \"module\",\n    \"mofn\",\n    \"multiple\",\n    \"named\",\n    \"namespace\",\n    \"nocase\",\n    \"noroot\",\n    \"noscan\",\n    \"nosort\",\n    \"not\",\n    \"noxpath\",\n    \"of\",\n    \"onfail\",\n    \"only\",\n    \"opt\",\n    \"or\",\n    \"outer\",\n    \"overwrite\",\n    \"packed\",\n    \"partition\",\n    \"penalty\",\n    \"physicallength\",\n    \"pipe\",\n    \"prefetch\",\n    \"quote\",\n    \"record\",\n    \"repeat\",\n    \"retry\",\n    \"return\",\n    \"right\",\n    \"right1\",\n    \"right2\",\n    \"rows\",\n    \"rowset\",\n    \"scan\",\n    \"scope\",\n    \"self\",\n    \"separator\",\n    \"service\",\n    \"shared\",\n    \"skew\",\n    \"skip\",\n    \"smart\",\n    \"soapaction\",\n    \"sql\",\n    \"stable\",\n    \"store\",\n    \"terminator\",\n    \"thor\",\n    \"threshold\",\n    \"timelimit\",\n    \"timeout\",\n    \"token\",\n    \"transform\",\n    \"trim\",\n    \"type\",\n    \"unicodeorder\",\n    \"unordered\",\n    \"unsorted\",\n    \"unstable\",\n    \"update\",\n    \"use\",\n    \"validate\",\n    \"virtual\",\n    \"whole\",\n    \"width\",\n    \"wild\",\n    \"within\",\n    \"wnotrim\",\n    \"xml\",\n    \"xpath\"\n  ],\n  functions: [\n    \"abs\",\n    \"acos\",\n    \"aggregate\",\n    \"allnodes\",\n    \"apply\",\n    \"ascii\",\n    \"asin\",\n    \"assert\",\n    \"asstring\",\n    \"atan\",\n    \"atan2\",\n    \"ave\",\n    \"build\",\n    \"buildindex\",\n    \"case\",\n    \"catch\",\n    \"choose\",\n    \"choosen\",\n    \"choosesets\",\n    \"clustersize\",\n    \"combine\",\n    \"correlation\",\n    \"cos\",\n    \"cosh\",\n    \"count\",\n    \"covariance\",\n    \"cron\",\n    \"dataset\",\n    \"dedup\",\n    \"define\",\n    \"denormalize\",\n    \"dictionary\",\n    \"distribute\",\n    \"distributed\",\n    \"distribution\",\n    \"ebcdic\",\n    \"enth\",\n    \"error\",\n    \"evaluate\",\n    \"event\",\n    \"eventextra\",\n    \"eventname\",\n    \"exists\",\n    \"exp\",\n    \"fail\",\n    \"failcode\",\n    \"failmessage\",\n    \"fetch\",\n    \"fromunicode\",\n    \"fromxml\",\n    \"getenv\",\n    \"getisvalid\",\n    \"global\",\n    \"graph\",\n    \"group\",\n    \"hash\",\n    \"hash32\",\n    \"hash64\",\n    \"hashcrc\",\n    \"hashmd5\",\n    \"having\",\n    \"httpcall\",\n    \"httpheader\",\n    \"if\",\n    \"iff\",\n    \"index\",\n    \"intformat\",\n    \"isvalid\",\n    \"iterate\",\n    \"join\",\n    \"keydiff\",\n    \"keypatch\",\n    \"keyunicode\",\n    \"length\",\n    \"library\",\n    \"limit\",\n    \"ln\",\n    \"loadxml\",\n    \"local\",\n    \"log\",\n    \"loop\",\n    \"map\",\n    \"matched\",\n    \"matchlength\",\n    \"matchposition\",\n    \"matchtext\",\n    \"matchunicode\",\n    \"max\",\n    \"merge\",\n    \"mergejoin\",\n    \"min\",\n    \"nofold\",\n    \"nolocal\",\n    \"nonempty\",\n    \"normalize\",\n    \"nothor\",\n    \"notify\",\n    \"output\",\n    \"parallel\",\n    \"parse\",\n    \"pipe\",\n    \"power\",\n    \"preload\",\n    \"process\",\n    \"project\",\n    \"pull\",\n    \"random\",\n    \"range\",\n    \"rank\",\n    \"ranked\",\n    \"realformat\",\n    \"recordof\",\n    \"regexfind\",\n    \"regexreplace\",\n    \"regroup\",\n    \"rejected\",\n    \"rollup\",\n    \"round\",\n    \"roundup\",\n    \"row\",\n    \"rowdiff\",\n    \"sample\",\n    \"sequential\",\n    \"set\",\n    \"sin\",\n    \"sinh\",\n    \"sizeof\",\n    \"soapcall\",\n    \"sort\",\n    \"sorted\",\n    \"sqrt\",\n    \"stepped\",\n    \"stored\",\n    \"sum\",\n    \"table\",\n    \"tan\",\n    \"tanh\",\n    \"thisnode\",\n    \"topn\",\n    \"tounicode\",\n    \"toxml\",\n    \"transfer\",\n    \"transform\",\n    \"trim\",\n    \"truncate\",\n    \"typeof\",\n    \"ungroup\",\n    \"unicodeorder\",\n    \"variance\",\n    \"wait\",\n    \"which\",\n    \"workunit\",\n    \"xmldecode\",\n    \"xmlencode\",\n    \"xmltext\",\n    \"xmlunicode\"\n  ],\n  typesint: [\"integer\", \"unsigned\"].join(\"|\"),\n  typesnum: [\"data\", \"qstring\", \"string\", \"unicode\", \"utf8\", \"varstring\", \"varunicode\"],\n  typesone: [\n    \"ascii\",\n    \"big_endian\",\n    \"boolean\",\n    \"data\",\n    \"decimal\",\n    \"ebcdic\",\n    \"grouped\",\n    \"integer\",\n    \"linkcounted\",\n    \"pattern\",\n    \"qstring\",\n    \"real\",\n    \"record\",\n    \"rule\",\n    \"set of\",\n    \"streamed\",\n    \"string\",\n    \"token\",\n    \"udecimal\",\n    \"unicode\",\n    \"unsigned\",\n    \"utf8\",\n    \"varstring\",\n    \"varunicode\"\n  ].join(\"|\"),\n  operators: [\"+\", \"-\", \"/\", \":=\", \"<\", \"<>\", \"=\", \">\", \"\\\\\", \"and\", \"in\", \"not\", \"or\"],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  // escape sequences\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/@typesint[4|8]/, \"type\"],\n      [/#(@pounds)/, \"type\"],\n      [/@typesone/, \"type\"],\n      [\n        /[a-zA-Z_$][\\w-$]*/,\n        {\n          cases: {\n            \"@functions\": \"keyword.function\",\n            \"@keywords\": \"keyword\",\n            \"@operators\": \"operator\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/[0-9_]*\\.[0-9_]+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F_]+/, \"number.hex\"],\n      [/0[bB][01]+/, \"number.hex\"],\n      // binary: use same theme style as hex\n      [/[0-9_]+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/\"/, \"string\", \"@string\"],\n      // characters\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    whitespace: [\n      [/[ \\t\\v\\f\\r\\n]+/, \"\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, \"string\", \"@pop\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/ecl/ecl.js\n"));

/***/ })

}]);