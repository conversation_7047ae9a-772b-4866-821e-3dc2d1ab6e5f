"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_handlebars_handlebars_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/handlebars/handlebars.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/handlebars/handlebars.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.api.js\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/basic-languages/handlebars/handlebars.ts\nvar EMPTY_ELEMENTS = [\n  \"area\",\n  \"base\",\n  \"br\",\n  \"col\",\n  \"embed\",\n  \"hr\",\n  \"img\",\n  \"input\",\n  \"keygen\",\n  \"link\",\n  \"menuitem\",\n  \"meta\",\n  \"param\",\n  \"source\",\n  \"track\",\n  \"wbr\"\n];\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  comments: {\n    blockComment: [\"{{!--\", \"--}}\"]\n  },\n  brackets: [\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"],\n    [\"{{\", \"}}\"],\n    [\"{\", \"}\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      afterText: /^<\\/(\\w[\\w\\d]*)\\s*>$/i,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n      }\n    },\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  // ignoreCase: true,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/\\{\\{!--/, \"comment.block.start.handlebars\", \"@commentBlock\"],\n      [/\\{\\{!/, \"comment.start.handlebars\", \"@comment\"],\n      [/\\{\\{/, { token: \"@rematch\", switchTo: \"@handlebarsInSimpleState.root\" }],\n      [/<!DOCTYPE/, \"metatag.html\", \"@doctype\"],\n      [/<!--/, \"comment.html\", \"@commentHtml\"],\n      [/(<)(\\w+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)(script)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@style\" }]],\n      [/(<)([:\\w]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)(\\w+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/\\{/, \"delimiter.html\"],\n      [/[^<{]+/]\n      // text\n    ],\n    doctype: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.comment\"\n        }\n      ],\n      [/[^>]+/, \"metatag.content.html\"],\n      [/>/, \"metatag.html\", \"@pop\"]\n    ],\n    comment: [\n      [/\\}\\}/, \"comment.end.handlebars\", \"@pop\"],\n      [/./, \"comment.content.handlebars\"]\n    ],\n    commentBlock: [\n      [/--\\}\\}/, \"comment.block.end.handlebars\", \"@pop\"],\n      [/./, \"comment.content.handlebars\"]\n    ],\n    commentHtml: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.comment\"\n        }\n      ],\n      [/-->/, \"comment.html\", \"@pop\"],\n      [/[^-]+/, \"comment.content.html\"],\n      [/./, \"comment.content.html\"]\n    ],\n    otherTag: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.otherTag\"\n        }\n      ],\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    // -- BEGIN <script> tags handling\n    // After <script\n    script: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.script\"\n        }\n      ],\n      [/type/, \"attribute.name\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(script\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <script ... type\n    scriptAfterType: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.scriptAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type =\n    scriptAfterTypeEquals: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.scriptAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type = $S2\n    scriptWithCustomType: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.scriptWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInEmbeddedState.scriptEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    // -- END <script> tags handling\n    // -- BEGIN <style> tags handling\n    // After <style\n    style: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.style\"\n        }\n      ],\n      [/type/, \"attribute.name\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(style\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <style ... type\n    styleAfterType: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.styleAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type =\n    styleAfterTypeEquals: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.styleAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type = $S2\n    styleWithCustomType: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInSimpleState.styleWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@handlebarsInEmbeddedState.styleEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    // -- END <style> tags handling\n    handlebarsInSimpleState: [\n      [/\\{\\{\\{?/, \"delimiter.handlebars\"],\n      [/\\}\\}\\}?/, { token: \"delimiter.handlebars\", switchTo: \"@$S2.$S3\" }],\n      { include: \"handlebarsRoot\" }\n    ],\n    handlebarsInEmbeddedState: [\n      [/\\{\\{\\{?/, \"delimiter.handlebars\"],\n      [\n        /\\}\\}\\}?/,\n        {\n          token: \"delimiter.handlebars\",\n          switchTo: \"@$S2.$S3\",\n          nextEmbedded: \"$S3\"\n        }\n      ],\n      { include: \"handlebarsRoot\" }\n    ],\n    handlebarsRoot: [\n      [/\"[^\"]*\"/, \"string.handlebars\"],\n      [/[#/][^\\s}]+/, \"keyword.helper.handlebars\"],\n      [/else\\b/, \"keyword.helper.handlebars\"],\n      [/[\\s]+/],\n      [/[^}]/, \"variable.parameter.handlebars\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/handlebars/handlebars.js\n"));

/***/ })

}]);