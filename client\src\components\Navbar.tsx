"use client"

import Link from "next/link"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { useTheme } from "@/context/ThemeContext"
import { useAuth } from "@/context/AuthContext"
import { <PERSON><PERSON>un, FiMoon, FiMonitor, FiCode } from "react-icons/fi"

const Navbar = () => {
  const { user, logout } = useAuth()
  const router = useRouter()
  const { theme, setTheme } = useTheme()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const handleLogout = async () => {
    logout()
    router.push("/login")
  }

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <motion.nav
      className="w-full bg-zinc-900 p-4 flex justify-between items-center text-white sticky top-0 z-50 glass"
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ type: "spring", stiffness: 100, damping: 15 }}
    >
      <motion.div
        className="flex items-center space-x-2"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <FiCode className="text-2xl text-blue-400" />
        <Link href="/" className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
          RealCode
        </Link>
      </motion.div>

      {/* Desktop Menu */}
      <div className="hidden md:flex items-center space-x-6">
        {/* Theme Switcher */}
        <div className="flex items-center space-x-2 mr-4">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setTheme("light")}
            className={`p-2 rounded-full ${theme === "light" ? "bg-yellow-400 text-yellow-900" : "text-gray-400"}`}
          >
            <FiSun />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setTheme("dark")}
            className={`p-2 rounded-full ${theme === "dark" ? "bg-indigo-600 text-white" : "text-gray-400"}`}
          >
            <FiMoon />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setTheme("system")}
            className={`p-2 rounded-full ${theme === "system" ? "bg-green-500 text-white" : "text-gray-400"}`}
          >
            <FiMonitor />
          </motion.button>
        </div>

        {user ? (
          <div className="flex items-center space-x-4">
            <motion.span
              className="text-sm bg-zinc-800 px-3 py-1 rounded-full"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              Hello, {user.email}
            </motion.span>
            <motion.button
              onClick={handleLogout}
              className="bg-gradient-to-r from-red-500 to-pink-600 px-4 py-2 rounded-md font-medium"
              whileHover={{ scale: 1.05, boxShadow: "0 10px 25px -5px rgba(239, 68, 68, 0.4)" }}
              whileTap={{ scale: 0.95 }}
            >
              Logout
            </motion.button>
          </div>
        ) : (
          <div className="flex items-center space-x-4">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link href="/login" className="px-4 py-2 rounded-md border border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white transition-colors">
                Login
              </Link>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link href="/signup" className="px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-colors">
                Signup
              </Link>
            </motion.div>
          </div>
        )}
      </div>

      {/* Mobile Menu Toggle */}
      <motion.button
        className="md:hidden text-white"
        onClick={toggleMenu}
        whileTap={{ scale: 0.9 }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={isMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"} />
        </svg>
      </motion.button>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <motion.div
          className="absolute top-16 left-0 right-0 bg-zinc-900 p-4 md:hidden z-50 glass"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.2 }}
        >
          <div className="flex flex-col space-y-4">
            {/* Theme Switcher */}
            <div className="flex justify-center space-x-4 py-2">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setTheme("light")}
                className={`p-2 rounded-full ${theme === "light" ? "bg-yellow-400 text-yellow-900" : "text-gray-400"}`}
              >
                <FiSun />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setTheme("dark")}
                className={`p-2 rounded-full ${theme === "dark" ? "bg-indigo-600 text-white" : "text-gray-400"}`}
              >
                <FiMoon />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setTheme("system")}
                className={`p-2 rounded-full ${theme === "system" ? "bg-green-500 text-white" : "text-gray-400"}`}
              >
                <FiMonitor />
              </motion.button>
            </div>

            {user ? (
              <>
                <div className="text-center py-2 border-t border-zinc-800">
                  <span className="text-sm">Hello, {user.email}</span>
                </div>
                <motion.button
                  onClick={handleLogout}
                  className="w-full bg-gradient-to-r from-red-500 to-pink-600 py-2 rounded-md font-medium"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Logout
                </motion.button>
              </>
            ) : (
              <>
                <Link href="/login" className="w-full text-center py-2 rounded-md border border-blue-500 text-blue-500">
                  Login
                </Link>
                <Link href="/signup" className="w-full text-center py-2 rounded-md bg-blue-600 text-white">
                  Signup
                </Link>
              </>
            )}
          </div>
        </motion.div>
      )}
    </motion.nav>
  )
}

export default Navbar
