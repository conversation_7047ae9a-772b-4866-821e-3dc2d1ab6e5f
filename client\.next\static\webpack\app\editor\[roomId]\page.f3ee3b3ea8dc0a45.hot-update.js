"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var monaco_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! monaco-editor */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.main.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlignLeft,FiCode,FiCopy,FiDownload,FiHelpCircle,FiLoader,FiMoon,FiPlay,FiSearch,FiSun,FiUpload,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_socketService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/socketService */ \"(app-pages-browser)/./src/services/socketService.ts\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/throttle */ \"(app-pages-browser)/./node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/SocketContext */ \"(app-pages-browser)/./src/context/SocketContext.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_formatCode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/formatCode */ \"(app-pages-browser)/./src/utils/formatCode.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/ThemeContext */ \"(app-pages-browser)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CodeEditor(param) {\n    let { roomId, username: initialUsername } = param;\n    _s();\n    const { isConnected } = (0,_context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket)();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"// Start coding...\");\n    // Use a ref to always track the latest code value\n    const latestCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"// Start coding...\");\n    // Get username from localStorage if available, otherwise use initialUsername\n    const storedUsername =  true ? localStorage.getItem(\"username\") : 0;\n    console.log(\"Initial username: \".concat(initialUsername, \", Stored username: \").concat(storedUsername));\n    // Track the username internally to handle server validation\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUsername || initialUsername);\n    const [typingUser, setTypingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeUsers, setActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUserList, setShowUserList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"javascript\");\n    const [copySuccess, setCopySuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get user role from localStorage if available, otherwise null\n    const storedUserRole =  true ? localStorage.getItem(\"userRole\") : 0;\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUserRole);\n    // Code execution states\n    const [isExecuting, setIsExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionOutput, setExecutionOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [executionError, setExecutionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOutput, setShowOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Piston API runtimes\n    const [runtimes, setRuntimes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [runtimesLoaded, setRuntimesLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    // Theme and minimap state (ensure these are always defined at the top)\n    const { theme, setTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme)();\n    const [showShortcuts, setShowShortcuts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [minimapEnabled, setMinimapEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Language-specific code snippets\n    const SNIPPETS = {\n        javascript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            }\n        ],\n        typescript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction(): void {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            },\n            {\n                label: \"Type Declaration\",\n                value: \"type MyType = {\\n  key: string;\\n};\"\n            }\n        ],\n        python3: [\n            {\n                label: \"Function\",\n                value: \"def my_function():\\n    # code\\n    pass\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in range(10):\\n    # code\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition:\\n    # code\"\n            },\n            {\n                label: \"Print\",\n                value: \"print('Hello, World!')\"\n            }\n        ],\n        java: [\n            {\n                label: \"Main Method\",\n                value: \"public static void main(String[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"System.out.println(\\\"Hello, World!\\\");\"\n            }\n        ],\n        csharp: [\n            {\n                label: \"Main Method\",\n                value: \"static void Main(string[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"Console.WriteLine(\\\"Hello, World!\\\");\"\n            }\n        ],\n        c: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"printf(\\\"Hello, World!\\\\n\\\");\"\n            }\n        ],\n        cpp: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"std::cout << \\\"Hello, World!\\\" << std::endl;\"\n            }\n        ],\n        go: [\n            {\n                label: \"Main Function\",\n                value: \"func main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i := 0; i < 10; i++ {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"fmt.Println(\\\"Hello, World!\\\")\"\n            }\n        ],\n        ruby: [\n            {\n                label: \"Method\",\n                value: \"def my_method\\n  # code\\nend\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..9\\n  # code\\nend\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition\\n  # code\\nend\"\n            },\n            {\n                label: \"Print\",\n                value: \"puts 'Hello, World!'\"\n            }\n        ],\n        rust: [\n            {\n                label: \"Main Function\",\n                value: \"fn main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..10 {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"println!(\\\"Hello, World!\\\");\"\n            }\n        ],\n        php: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for ($i = 0; $i < 10; $i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if ($condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"echo 'Hello, World!';\"\n            }\n        ]\n    };\n    // Declare the `socketService` instance at the top of the file\n    const socketService = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n    // Leave room handler\n    const handleLeaveRoom = ()=>{\n        socketService.leaveRoom(roomId);\n        router.push(\"/dashboard\");\n    };\n    // --- Teacher Selection Highlighting and Cursor Sharing ---\n    // Add custom CSS for teacher selection and cursor if not already present\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            if ( true && !document.getElementById('teacher-highlighting-css')) {\n                const style = document.createElement('style');\n                style.id = 'teacher-highlighting-css';\n                style.innerHTML = \"\\n        .teacher-selection-highlight {\\n          background: rgba(255, 230, 80, 0.5) !important;\\n          border-bottom: 2px solid orange !important;\\n          border-radius: 2px;\\n        }\\n        .teacher-cursor-highlight {\\n          border-left: 2px solid orange !important;\\n          margin-left: -1px;\\n          animation: teacher-cursor-blink 1s steps(2, start) infinite;\\n        }\\n        @keyframes teacher-cursor-blink {\\n          0%, 100% { opacity: 1; }\\n          50% { opacity: 0; }\\n        }\\n      \";\n                document.head.appendChild(style);\n            }\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    // Store decoration ids for cleanup\n    const [teacherSelectionDecorations, setTeacherSelectionDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [teacherCursorDecorations, setTeacherCursorDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Add state for teacher cursor position (for custom blinking cursor overlay)\n    const [teacherCursorPosition, setTeacherCursorPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State to track current teacher selection and cursor for syncing to new users\n    const [currentTeacherSelection, setCurrentTeacherSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentTeacherCursor, setCurrentTeacherCursor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Handle editor mounting\n    const handleEditorDidMount = (editor)=>{\n        editorRef.current = editor;\n        setIsLoading(false);\n        // Auto-focus the editor after mounting\n        setTimeout(()=>{\n            if (editor) {\n                editor.focus();\n                // Add keyboard shortcut (Ctrl+Enter) to run code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.CtrlCmd | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.Enter, ()=>{\n                    executeCode();\n                });\n                // Add keyboard shortcut (Shift+Alt+F) to format code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Shift | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Alt | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.KeyF, ()=>{\n                    formatCurrentCode();\n                });\n                // Add teacher cursor and selection listeners\n                if (userRole === 'teacher') {\n                    // Track cursor position changes - emit 'teacher-cursor' event\n                    editor.onDidChangeCursorPosition((e)=>{\n                        const position = e.position;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        if (roomId) {\n                            console.log('🎯 Teacher cursor position changed:', {\n                                lineNumber: position.lineNumber,\n                                column: position.column\n                            });\n                            // Update current teacher cursor state for syncing to new users\n                            setCurrentTeacherCursor({\n                                lineNumber: position.lineNumber,\n                                column: position.column\n                            });\n                            // Send cursor position to students using teacher-cursor-position event\n                            socketServiceInstance.sendTeacherCursorPosition(roomId, {\n                                lineNumber: position.lineNumber,\n                                column: position.column\n                            });\n                        }\n                    });\n                    // Track selection changes - emit 'teacher-selection' event\n                    editor.onDidChangeCursorSelection((e)=>{\n                        console.log('🎯 Teacher cursor selection changed:', {\n                            selection: e.selection,\n                            isEmpty: e.selection.isEmpty(),\n                            userRole: userRole,\n                            roomId: roomId\n                        });\n                        const selection = e.selection;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        if (!selection.isEmpty() && roomId) {\n                            // Send selection range to students using teacher-selection event\n                            console.log('📤 Teacher sending selection range to students:', {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            });\n                            const selectionData = {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            };\n                            // Update current teacher selection state for syncing to new users\n                            setCurrentTeacherSelection(selectionData);\n                            // Use both teacher-selection (for range highlighting) and teacher-text-highlight (for compatibility)\n                            socketServiceInstance.sendTeacherSelection(roomId, selectionData);\n                            socketServiceInstance.sendTeacherTextHighlight(roomId, selectionData);\n                        } else if (roomId) {\n                            // Clear selection when teacher deselects\n                            console.log('🧹 Teacher clearing selection');\n                            setCurrentTeacherSelection(null);\n                            socketServiceInstance.clearTeacherSelection(roomId);\n                            socketServiceInstance.clearTeacherTextHighlight(roomId);\n                        }\n                    });\n                }\n            }\n        }, 500) // Small delay to ensure the editor is fully ready\n        ;\n    };\n    // Track if the change is from a remote update\n    const isRemoteUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Create a throttled version of the code change handler\n    // This prevents sending too many updates when typing quickly\n    // but ensures updates are sent at regular intervals\n    const throttledCodeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default()({\n        \"CodeEditor.useRef\": (code)=>{\n            if (roomId && isConnected) {\n                console.log(\"Sending throttled code update to room \".concat(roomId, \", length: \").concat(code.length));\n                socketService.sendCodeChange(roomId, code);\n            }\n        }\n    }[\"CodeEditor.useRef\"], 50) // Use throttle with a short interval for more responsive updates\n    ).current;\n    // Create a debounced version of the typing notification\n    const debouncedTypingNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default()({\n        \"CodeEditor.useCallback[debouncedTypingNotification]\": ()=>{\n            if (roomId && isConnected) {\n                // Always send the current username with typing notifications\n                console.log(\"Sending typing notification with username: \".concat(username));\n                socketService.sendTyping(roomId, username);\n            }\n        }\n    }[\"CodeEditor.useCallback[debouncedTypingNotification]\"], 1000), [\n        roomId,\n        isConnected,\n        username\n    ]);\n    // Handle editor change\n    const handleEditorChange = (value)=>{\n        if (typeof value !== \"string\") return;\n        // Make sure loading is off during code changes\n        setIsLoading(false);\n        // If this change is from a remote update, just update the state and return\n        if (isRemoteUpdate.current) {\n            console.log(\"Handling remote update, not emitting\");\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            isRemoteUpdate.current = false;\n            return;\n        }\n        // Only process if the value actually changed\n        if (value !== latestCodeRef.current) {\n            // Update local state immediately\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            // Send typing notification (debounced)\n            debouncedTypingNotification();\n            // Send code update (throttled)\n            throttledCodeChange(value);\n            // If not connected, try to reconnect\n            if (!isConnected) {\n                console.log(\"Socket not connected, attempting to reconnect\");\n                socketService.connect();\n            }\n        }\n    };\n    // Copy code to clipboard\n    const copyCodeToClipboard = ()=>{\n        if (navigator.clipboard && code) {\n            navigator.clipboard.writeText(code).then(()=>{\n                setCopySuccess(true);\n                setTimeout(()=>setCopySuccess(false), 2000);\n            }).catch((err)=>{\n                console.error('Failed to copy code: ', err);\n            });\n        }\n    };\n    // Toggle user list\n    const toggleUserList = ()=>{\n        setShowUserList((prev)=>!prev);\n    };\n    // Change language\n    const changeLanguage = (lang)=>{\n        setLanguage(lang);\n        // Log available runtimes for this language\n        if (runtimesLoaded && runtimes.length > 0) {\n            const availableRuntimes = runtimes.filter((runtime)=>runtime.language === lang || runtime.aliases && runtime.aliases.includes(lang));\n            if (availableRuntimes.length > 0) {\n                console.log(\"Available runtimes for \".concat(lang, \":\"), availableRuntimes);\n            } else {\n                console.log(\"No runtimes available for \".concat(lang));\n            }\n        }\n    };\n    // Format code\n    const formatCurrentCode = ()=>{\n        if (!code || !editorRef.current) return;\n        try {\n            // Format the code based on the current language\n            const formattedCode = (0,_utils_formatCode__WEBPACK_IMPORTED_MODULE_8__.formatCode)(code, language);\n            // Only update if the code actually changed\n            if (formattedCode !== code) {\n                // Update the editor\n                const model = editorRef.current.getModel();\n                if (model) {\n                    // Store current cursor position/selection\n                    const currentPosition = editorRef.current.getPosition();\n                    const currentSelection = editorRef.current.getSelection();\n                    // Update the editor content\n                    editorRef.current.executeEdits('format', [\n                        {\n                            range: model.getFullModelRange(),\n                            text: formattedCode,\n                            forceMoveMarkers: true\n                        }\n                    ]);\n                    // Restore cursor position if possible\n                    if (currentPosition) {\n                        editorRef.current.setPosition(currentPosition);\n                    }\n                    if (currentSelection) {\n                        editorRef.current.setSelection(currentSelection);\n                    }\n                    // Update state and ref\n                    setCode(formattedCode);\n                    latestCodeRef.current = formattedCode;\n                    // Send the formatted code to other users\n                    if (roomId && isConnected) {\n                        console.log(\"Sending formatted code to room \".concat(roomId, \", length: \").concat(formattedCode.length));\n                        socketService.sendCodeChange(roomId, formattedCode);\n                    }\n                    // Show a success message\n                    console.log('Code formatted successfully');\n                }\n            } else {\n                console.log('Code is already formatted');\n            }\n        } catch (error) {\n            console.error('Error formatting code:', error);\n        }\n    };\n    // Clear execution output\n    const clearOutput = ()=>{\n        setExecutionOutput(null);\n        setExecutionError(null);\n    };\n    // Fetch and store available runtimes from Piston API\n    const fetchRuntimes = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get('https://emkc.org/api/v2/piston/runtimes');\n            console.log('Piston API status:', response.status);\n            console.log('Available runtimes:', response.data);\n            // Store the runtimes in state\n            setRuntimes(response.data);\n            setRuntimesLoaded(true);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error fetching Piston API runtimes:', error);\n            return {\n                success: false,\n                error: axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) ? \"\".concat(error.message, \" - \").concat(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) || 'Unknown', \" \").concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText) || '') : String(error)\n            };\n        }\n    };\n    // Check if the Piston API is available\n    const checkPistonAPI = async ()=>{\n        // If we haven't loaded runtimes yet, fetch them\n        if (!runtimesLoaded) {\n            return await fetchRuntimes();\n        }\n        // If we already have runtimes, just return success\n        if (runtimes.length > 0) {\n            return {\n                success: true,\n                data: runtimes\n            };\n        }\n        // If we've tried to load runtimes but have none, try again\n        return await fetchRuntimes();\n    };\n    // Test the Piston API with a hardcoded sample payload\n    const testPistonAPI = async ()=>{\n        setIsExecuting(true);\n        setExecutionError(null);\n        setExecutionOutput(null);\n        setShowOutput(true);\n        try {\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Find JavaScript runtime\n            const jsRuntimes = apiStatus.data.filter((runtime)=>runtime.language === \"javascript\" || runtime.aliases && runtime.aliases.includes(\"javascript\"));\n            if (jsRuntimes.length === 0) {\n                setExecutionError('No JavaScript runtime found. Please try a different language.');\n                return;\n            }\n            const jsRuntime = jsRuntimes[0];\n            console.log(\"Using JavaScript runtime: \".concat(jsRuntime.language, \" \").concat(jsRuntime.version));\n            // Sample JavaScript code that should work\n            const samplePayload = {\n                language: jsRuntime.language,\n                version: jsRuntime.version,\n                files: [\n                    {\n                        name: \"main.js\",\n                        content: \"console.log('Hello, World!');\"\n                    }\n                ],\n                stdin: \"\",\n                args: []\n            };\n            console.log('Testing Piston API with sample payload:', JSON.stringify(samplePayload, null, 2));\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', samplePayload);\n            console.log('Sample execution response:', response.data);\n            if (response.data.run) {\n                setExecutionOutput(\"API Test Successful!\\n\\n\" + \"Output: \" + response.data.run.stdout + \"\\n\\n\" + \"The API is working correctly. You can now run your own code.\");\n            } else {\n                setExecutionError('API test failed. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error testing Piston API:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                setExecutionError(\"API Test Failed: \".concat(error.response.status, \" \").concat(error.response.statusText, \"\\n\\n\") + JSON.stringify(error.response.data, null, 2));\n            } else {\n                setExecutionError(error instanceof Error ? \"API Test Error: \".concat(error.message) : 'An unknown error occurred while testing the API.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // Execute code using Piston API\n    const executeCode = async ()=>{\n        if (!code || isExecuting) return;\n        // Map Monaco editor language to Piston API language\n        const languageMap = {\n            javascript: \"javascript\",\n            typescript: \"typescript\",\n            python: \"python3\",\n            python3: \"python3\",\n            java: \"java\",\n            csharp: \"csharp\",\n            c: \"c\",\n            cpp: \"cpp\",\n            go: \"go\",\n            ruby: \"ruby\",\n            rust: \"rust\",\n            php: \"php\"\n        };\n        // Get the mapped language\n        const pistonLanguage = languageMap[language] || language;\n        // Check if we have runtimes loaded\n        if (!runtimesLoaded || runtimes.length === 0) {\n            setExecutionError('Runtimes not loaded yet. Please try again in a moment.');\n            setShowOutput(true);\n            return;\n        }\n        // Find available runtimes for the selected language\n        const availableRuntimes = runtimes.filter((runtime)=>runtime.language === pistonLanguage || runtime.aliases && runtime.aliases.includes(pistonLanguage));\n        // Check if the language is supported\n        if (availableRuntimes.length === 0) {\n            // Get a list of supported languages from the runtimes\n            const supportedLanguages = [\n                ...new Set(runtimes.flatMap((runtime)=>[\n                        runtime.language,\n                        ...runtime.aliases || []\n                    ]))\n            ].sort();\n            setExecutionError(\"The language '\".concat(language, \"' (mapped to '\").concat(pistonLanguage, \"') is not supported by the Piston API.\\n\\n\") + \"Supported languages: \".concat(supportedLanguages.join(', ')));\n            setShowOutput(true);\n            return;\n        }\n        // Get the latest version of the runtime\n        const selectedRuntime = availableRuntimes[0];\n        try {\n            setIsExecuting(true);\n            setExecutionError(null);\n            setExecutionOutput(null);\n            setShowOutput(true);\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Determine file extension based on language\n            let fileExtension = '';\n            if (selectedRuntime.language === 'python3' || selectedRuntime.language === 'python') fileExtension = '.py';\n            else if (selectedRuntime.language === 'javascript') fileExtension = '.js';\n            else if (selectedRuntime.language === 'typescript') fileExtension = '.ts';\n            else if (selectedRuntime.language === 'java') fileExtension = '.java';\n            else if (selectedRuntime.language === 'csharp') fileExtension = '.cs';\n            else if (selectedRuntime.language === 'c') fileExtension = '.c';\n            else if (selectedRuntime.language === 'cpp') fileExtension = '.cpp';\n            else if (selectedRuntime.language === 'go') fileExtension = '.go';\n            else if (selectedRuntime.language === 'rust') fileExtension = '.rs';\n            else if (selectedRuntime.language === 'ruby') fileExtension = '.rb';\n            else if (selectedRuntime.language === 'php') fileExtension = '.php';\n            else fileExtension = \".\".concat(selectedRuntime.language);\n            console.log(\"Selected runtime: \".concat(selectedRuntime.language, \" \").concat(selectedRuntime.version));\n            // Prepare the payload according to Piston API documentation\n            const payload = {\n                language: selectedRuntime.language,\n                version: selectedRuntime.version,\n                files: [\n                    {\n                        name: \"main\".concat(fileExtension),\n                        content: code\n                    }\n                ],\n                stdin: '',\n                args: [],\n                compile_timeout: 10000,\n                run_timeout: 5000\n            };\n            // Log the payload for debugging\n            console.log(\"Executing \".concat(pistonLanguage, \" code with payload:\"), JSON.stringify(payload, null, 2));\n            // Make the API request\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', payload);\n            console.log('Execution response:', response.data);\n            const result = response.data;\n            if (result.run) {\n                // Format the output\n                let output = '';\n                let hasOutput = false;\n                // Add compile output if available (for compiled languages)\n                if (result.compile && result.compile.stderr) {\n                    output += \"Compilation output:\\n\".concat(result.compile.stderr, \"\\n\\n\");\n                    hasOutput = true;\n                }\n                // Add standard output\n                if (result.run.stdout) {\n                    output += result.run.stdout;\n                    hasOutput = true;\n                }\n                // Add error output\n                if (result.run.stderr) {\n                    if (hasOutput) output += '\\n';\n                    output += \"Error output:\\n\".concat(result.run.stderr);\n                    hasOutput = true;\n                }\n                // Add exit code if non-zero\n                if (result.run.code !== 0) {\n                    if (hasOutput) output += '\\n';\n                    output += \"\\nProcess exited with code \".concat(result.run.code);\n                    hasOutput = true;\n                }\n                if (!hasOutput) {\n                    output = 'Program executed successfully with no output.';\n                }\n                setExecutionOutput(output);\n            } else {\n                setExecutionError('Failed to execute code. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error executing code:', error);\n            // Handle Axios errors with more detailed information\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                const statusCode = error.response.status;\n                const responseData = error.response.data;\n                console.error('API Error Details:', {\n                    status: statusCode,\n                    data: responseData\n                });\n                // Format a more helpful error message\n                if (statusCode === 400) {\n                    setExecutionError(\"API Error (400 Bad Request): \".concat(JSON.stringify(responseData), \"\\n\\n\") + 'This usually means the API request format is incorrect. ' + 'Please check the console for more details.');\n                } else if (statusCode === 429) {\n                    setExecutionError('Rate limit exceeded. Please try again later.');\n                } else {\n                    setExecutionError(\"API Error (\".concat(statusCode, \"): \").concat(JSON.stringify(responseData), \"\\n\\n\") + 'Please check the console for more details.');\n                }\n            } else {\n                // Handle non-Axios errors\n                setExecutionError(error instanceof Error ? \"Error: \".concat(error.message) : 'An unknown error occurred while executing the code.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // This function will be called when we receive a code update from another user\n    const handleCodeUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleCodeUpdate]\": (incomingCode)=>{\n            console.log(\"Remote code update received, length:\", incomingCode.length);\n            // Make sure loading is off during code updates\n            setIsLoading(false);\n            // Only update if the code is different from our latest code\n            if (incomingCode !== latestCodeRef.current) {\n                try {\n                    // Set the flag to indicate this is a remote update\n                    isRemoteUpdate.current = true;\n                    // Update the editor if it's mounted\n                    if (editorRef.current) {\n                        console.log(\"Updating editor with remote code\");\n                        // Use the editor's model to update the value to preserve cursor position\n                        const model = editorRef.current.getModel();\n                        if (model) {\n                            // Store current cursor position/selection\n                            const currentPosition = editorRef.current.getPosition();\n                            const currentSelection = editorRef.current.getSelection();\n                            // Use executeEdits with better handling of cursor position\n                            editorRef.current.executeEdits('remote', [\n                                {\n                                    range: model.getFullModelRange(),\n                                    text: incomingCode,\n                                    forceMoveMarkers: true\n                                }\n                            ]);\n                            // Restore cursor position if possible\n                            if (currentPosition) {\n                                editorRef.current.setPosition(currentPosition);\n                            }\n                            if (currentSelection) {\n                                editorRef.current.setSelection(currentSelection);\n                            }\n                            // Also update our state and ref\n                            setCode(incomingCode);\n                            latestCodeRef.current = incomingCode;\n                        }\n                    } else {\n                        // If editor isn't mounted yet, just update the state and ref\n                        console.log(\"Editor not mounted, updating state only\");\n                        setCode(incomingCode);\n                        latestCodeRef.current = incomingCode;\n                        isRemoteUpdate.current = false;\n                    }\n                } catch (error) {\n                    console.error(\"Error updating editor with remote code:\", error);\n                    isRemoteUpdate.current = false;\n                }\n            } else {\n                console.log(\"Remote code matches current code, ignoring\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleCodeUpdate]\"], []);\n    // State to track cursor positions\n    const [cursorPositions, setCursorPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Emit cursor movement\n    const handleMouseMove = (e)=>{\n        const position = {\n            x: e.clientX,\n            y: e.clientY\n        };\n        _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().sendCursorMove(roomId, username, position);\n    };\n    // Listen for cursor movement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            const handleCursorMove = {\n                \"CodeEditor.useEffect.handleCursorMove\": (param)=>{\n                    let { userId, position } = param;\n                    setCursorPositions({\n                        \"CodeEditor.useEffect.handleCursorMove\": (prev)=>({\n                                ...prev,\n                                [userId]: position\n                            })\n                    }[\"CodeEditor.useEffect.handleCursorMove\"]);\n                }\n            }[\"CodeEditor.useEffect.handleCursorMove\"];\n            _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().on(\"cursor-move\", handleCursorMove);\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().off(\"cursor-move\", handleCursorMove);\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId\n    ]);\n    // Render cursors\n    const renderCursors = ()=>{\n        return Object.entries(cursorPositions).map((param)=>{\n            let [userId, position] = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    left: position.x,\n                    top: position.y,\n                    backgroundColor: \"red\",\n                    width: 10,\n                    height: 10,\n                    borderRadius: \"50%\",\n                    pointerEvents: \"none\"\n                }\n            }, userId, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 833,\n                columnNumber: 7\n            }, this);\n        });\n    };\n    // Fetch runtimes when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            fetchRuntimes();\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    // Handle request for initial code from new users\n    const handleGetInitialCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleGetInitialCode]\": (data)=>{\n            console.log(\"Received request for initial code from \".concat(data.requestingUsername, \" (\").concat(data.requestingUserId, \")\"));\n            // Only respond if we have code and we're connected\n            if (roomId && latestCodeRef.current && latestCodeRef.current.trim() !== \"// Start coding...\") {\n                const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                console.log(\"Sending initial code to \".concat(data.requestingUsername, \", length: \").concat(latestCodeRef.current.length));\n                socketServiceInstance.sendInitialCode(roomId, latestCodeRef.current, data.requestingUserId);\n            } else {\n                console.log(\"Not sending initial code - no meaningful code to share or not in room\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleGetInitialCode]\"], [\n        roomId\n    ]);\n    // Handle receiving initial code as a new user\n    const handleInitialCodeReceived = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleInitialCodeReceived]\": (data)=>{\n            console.log(\"Received initial code, length: \".concat(data.code.length));\n            // Only apply initial code if we don't have meaningful code yet\n            // This prevents overwriting code that the user might have already started typing\n            if (latestCodeRef.current === \"// Start coding...\" || latestCodeRef.current.trim() === \"\") {\n                console.log(\"Applying received initial code\");\n                // Set the flag to indicate this is a remote update\n                isRemoteUpdate.current = true;\n                // Update the code state and ref\n                setCode(data.code);\n                latestCodeRef.current = data.code;\n                // Update the editor if it's mounted\n                if (editorRef.current) {\n                    editorRef.current.setValue(data.code);\n                }\n            } else {\n                console.log(\"Not applying initial code - user already has meaningful code\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleInitialCodeReceived]\"], []);\n    // Handle teacher selection highlighting\n    const handleTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherSelection]\": (data)=>{\n            console.log(\"Received teacher selection from \".concat(data.teacherName, \":\"), data.selection);\n            // Extra debug: check if CSS class is present\n            if (true) {\n                const styleExists = !!document.querySelector('style#teacher-highlighting-css') || !!Array.from(document.styleSheets).find({\n                    \"CodeEditor.useCallback[handleTeacherSelection]\": (sheet)=>{\n                        try {\n                            return Array.from(sheet.cssRules).some({\n                                \"CodeEditor.useCallback[handleTeacherSelection]\": (rule)=>{\n                                    var _rule_selectorText;\n                                    return (_rule_selectorText = rule.selectorText) === null || _rule_selectorText === void 0 ? void 0 : _rule_selectorText.includes('.teacher-selection-highlight');\n                                }\n                            }[\"CodeEditor.useCallback[handleTeacherSelection]\"]);\n                        } catch (e) {\n                            return false;\n                        }\n                    }\n                }[\"CodeEditor.useCallback[handleTeacherSelection]\"]);\n                if (!styleExists) {\n                    console.warn('⚠️ teacher-selection-highlight CSS missing, injecting fallback');\n                    const style = document.createElement('style');\n                    style.id = 'teacher-highlighting-css-fallback';\n                    style.innerHTML = \"\\n          .teacher-selection-highlight {\\n            background: rgba(255, 230, 80, 0.7) !important;\\n            border-bottom: 2px solid orange !important;\\n            border-radius: 2px;\\n          }\\n        \";\n                    document.head.appendChild(style);\n                }\n            }\n            if (!editorRef.current || userRole === 'teacher') {\n                console.log('Skipping teacher highlight: editor not available or user is teacher');\n                return; // Don't show teacher highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model || !data.selection) {\n                    console.log('No model or selection');\n                    return;\n                }\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.selection.startLineNumber, data.selection.startColumn, data.selection.endLineNumber, data.selection.endColumn);\n                console.log('Applying teacher selection highlight with range:', range);\n                // Clear previous teacher selection decorations and apply new ones with yellow/orange styling\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-selection-highlight',\n                            hoverMessage: {\n                                value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" selected this text\\n\\nRange: Line \").concat(data.selection.startLineNumber, \"-\").concat(data.selection.endLineNumber)\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Right\n                            }\n                        }\n                    }\n                ]);\n                console.log('New teacher selection decoration IDs:', newDecorations);\n                setTeacherSelectionDecorations(newDecorations);\n                // Debug: Check DOM for highlight\n                setTimeout({\n                    \"CodeEditor.useCallback[handleTeacherSelection]\": ()=>{\n                        const elements = document.querySelectorAll('.teacher-selection-highlight');\n                        console.log('DOM .teacher-selection-highlight count:', elements.length);\n                        elements.forEach({\n                            \"CodeEditor.useCallback[handleTeacherSelection]\": (el, i)=>{\n                                console.log(\"Highlight \".concat(i, \":\"), el, el.className, el.getAttribute('style'));\n                            }\n                        }[\"CodeEditor.useCallback[handleTeacherSelection]\"]);\n                    }\n                }[\"CodeEditor.useCallback[handleTeacherSelection]\"], 200);\n            } catch (error) {\n                console.error('Error applying teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle clearing teacher selection\n    const handleClearTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherSelection]\": (data)=>{\n            console.log(\"Teacher \".concat(data.teacherName, \" cleared selection\"));\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                // Clear all teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, []);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error clearing teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle teacher cursor position updates with blinking orange cursor decoration\n    const handleTeacherCursorPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherCursorPosition]\": (data)=>{\n            console.log(\"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" cursor at line \").concat(data.position.lineNumber, \", column \").concat(data.position.column));\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't show cursor to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.log('⏭️ Skipping teacher cursor: model not available');\n                    return;\n                }\n                // Validate cursor position bounds\n                const lineCount = model.getLineCount();\n                if (data.position.lineNumber > lineCount || data.position.lineNumber <= 0) {\n                    console.log('⏭️ Skipping teacher cursor: position out of bounds');\n                    return;\n                }\n                // Create a range for the cursor position (single character width)\n                const cursorRange = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.position.lineNumber, data.position.column, data.position.lineNumber, data.position.column + 1);\n                // Clear previous teacher cursor decorations and apply new one\n                const newCursorDecorations = editor.deltaDecorations(teacherCursorDecorations, [\n                    {\n                        range: cursorRange,\n                        options: {\n                            className: 'teacher-cursor',\n                            hoverMessage: {\n                                value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" cursor position\\n\\nLine \").concat(data.position.lineNumber, \", Column \").concat(data.position.column)\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Left\n                            }\n                        }\n                    }\n                ]);\n                setTeacherCursorDecorations(newCursorDecorations);\n                // Set the teacher cursor position for overlay rendering\n                setTeacherCursorPosition({\n                    lineNumber: data.position.lineNumber,\n                    column: data.position.column,\n                    teacherName: data.teacherName\n                });\n            } catch (error) {\n                console.error('❌ Error handling teacher cursor position:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherCursorPosition]\"], [\n        userRole,\n        teacherCursorDecorations\n    ]);\n    // Handle teacher text highlight with enhanced error handling and multiple CSS class fallbacks\n    const handleTeacherTextHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherTextHighlight]\": (data)=>{\n            console.log(\"\\uD83C\\uDFA8 Teacher \".concat(data.teacherName, \" highlighted text:\"), data.selection);\n            console.log(\"\\uD83D\\uDD0D Debug info:\", {\n                editorAvailable: !!editorRef.current,\n                userRole: userRole,\n                roomId: roomId,\n                currentDecorations: teacherSelectionDecorations.length,\n                monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined'\n            });\n            if (!editorRef.current || userRole === 'teacher') {\n                console.log('⏭️ Skipping teacher text highlight: editor not available or user is teacher');\n                return; // Don't show highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('❌ Monaco Editor model not available');\n                    return;\n                }\n                if (!data.selection) {\n                    console.error('❌ Selection data is missing');\n                    return;\n                }\n                // Validate selection data with more robust checking\n                const { startLineNumber, startColumn, endLineNumber, endColumn } = data.selection;\n                if (typeof startLineNumber !== 'number' || startLineNumber <= 0 || typeof startColumn !== 'number' || startColumn <= 0 || typeof endLineNumber !== 'number' || endLineNumber <= 0 || typeof endColumn !== 'number' || endColumn <= 0) {\n                    console.error('❌ Invalid selection data:', data.selection);\n                    return;\n                }\n                // Ensure the selection is within the model bounds\n                const lineCount = model.getLineCount();\n                if (startLineNumber > lineCount || endLineNumber > lineCount) {\n                    console.warn('⚠️ Selection extends beyond model bounds, adjusting...');\n                    const adjustedEndLine = Math.min(endLineNumber, lineCount);\n                    const adjustedEndColumn = adjustedEndLine === lineCount ? model.getLineMaxColumn(adjustedEndLine) : endColumn;\n                    console.log(\"Adjusted selection: (\".concat(startLineNumber, \", \").concat(startColumn, \", \").concat(adjustedEndLine, \", \").concat(adjustedEndColumn, \")\"));\n                }\n                console.log(\"✅ Creating Monaco Range: (\".concat(startLineNumber, \", \").concat(startColumn, \", \").concat(endLineNumber, \", \").concat(endColumn, \")\"));\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(startLineNumber, startColumn, endLineNumber, endColumn);\n                console.log('✅ Monaco Range created successfully:', range);\n                // Apply decoration with multiple CSS class options and inline styles for better compatibility\n                console.log('🎨 Applying teacher text highlight decoration...');\n                const decorationOptions = {\n                    range: range,\n                    options: {\n                        className: 'teacher-highlight teacher-text-highlight',\n                        hoverMessage: {\n                            value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" highlighted this text\\n\\nClick to focus on this selection\")\n                        },\n                        stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                        // Add inline styles as fallback\n                        inlineClassName: 'teacher-highlight-inline',\n                        // Force inline styles for maximum compatibility\n                        overviewRuler: {\n                            color: 'rgba(59, 130, 246, 0.8)',\n                            position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Right\n                        },\n                        // Add background color directly\n                        backgroundColor: 'rgba(59, 130, 246, 0.25)',\n                        // Add border styling\n                        border: '2px solid rgba(59, 130, 246, 0.7)'\n                    }\n                };\n                // Clear previous decorations and apply new ones\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [\n                    decorationOptions\n                ]);\n                console.log('✅ Teacher text highlight decorations applied:', newDecorations);\n                console.log('🔍 Decoration details:', decorationOptions);\n                setTeacherSelectionDecorations(newDecorations);\n                // Debug: Check if decorations are actually in the DOM\n                setTimeout({\n                    \"CodeEditor.useCallback[handleTeacherTextHighlight]\": ()=>{\n                        var _editor_getModel;\n                        const decorationElements = document.querySelectorAll('.teacher-text-highlight, .teacher-highlight');\n                        console.log('🔍 Found decoration elements in DOM:', decorationElements.length);\n                        decorationElements.forEach({\n                            \"CodeEditor.useCallback[handleTeacherTextHighlight]\": (el, index)=>{\n                                console.log(\"\\uD83D\\uDD0D Decoration \".concat(index + 1, \":\"), {\n                                    element: el,\n                                    className: el.className,\n                                    style: el.getAttribute('style'),\n                                    computedStyle: window.getComputedStyle(el).background\n                                });\n                            }\n                        }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"]);\n                        // Also check Monaco's internal decorations\n                        const allDecorations = (_editor_getModel = editor.getModel()) === null || _editor_getModel === void 0 ? void 0 : _editor_getModel.getAllDecorations();\n                        console.log('🔍 All Monaco decorations:', allDecorations === null || allDecorations === void 0 ? void 0 : allDecorations.length);\n                        const teacherDecorations = allDecorations === null || allDecorations === void 0 ? void 0 : allDecorations.filter({\n                            \"CodeEditor.useCallback[handleTeacherTextHighlight]\": (d)=>{\n                                var _d_options_className, _d_options_className1;\n                                return ((_d_options_className = d.options.className) === null || _d_options_className === void 0 ? void 0 : _d_options_className.includes('teacher-highlight')) || ((_d_options_className1 = d.options.className) === null || _d_options_className1 === void 0 ? void 0 : _d_options_className1.includes('teacher-text-highlight'));\n                            }\n                        }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"]);\n                        console.log('🔍 Teacher decorations in model:', teacherDecorations);\n                    }\n                }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"], 200);\n                // Force a layout update to ensure the decoration is visible\n                setTimeout({\n                    \"CodeEditor.useCallback[handleTeacherTextHighlight]\": ()=>{\n                        editor.layout();\n                        console.log('🔄 Editor layout refreshed');\n                    }\n                }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"], 100);\n            } catch (error) {\n                var _editorRef_current;\n                console.error('❌ Error applying teacher text highlight:', error);\n                console.error('🔍 Error details:', {\n                    editorAvailable: !!editorRef.current,\n                    modelAvailable: !!((_editorRef_current = editorRef.current) === null || _editorRef_current === void 0 ? void 0 : _editorRef_current.getModel()),\n                    selectionData: data.selection,\n                    userRole: userRole,\n                    monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined',\n                    currentDecorations: teacherSelectionDecorations\n                });\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle clearing teacher text highlight with enhanced logging\n    const handleClearTeacherTextHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherTextHighlight]\": (data)=>{\n            console.log(\"\\uD83E\\uDDF9 Teacher \".concat(data.teacherName, \" cleared text highlight\"));\n            console.log(\"\\uD83D\\uDD0D Clear debug info:\", {\n                editorAvailable: !!editorRef.current,\n                userRole: userRole,\n                roomId: roomId,\n                currentDecorations: teacherSelectionDecorations.length,\n                monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined'\n            });\n            if (!editorRef.current || userRole === 'teacher') {\n                console.log('⏭️ Skipping clear teacher text highlight: editor not available or user is teacher');\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('❌ Monaco Editor model not available for clearing highlights');\n                    return;\n                }\n                console.log('🧹 Clearing teacher text highlight decorations...');\n                console.log(\"\\uD83D\\uDD0D Clearing \".concat(teacherSelectionDecorations.length, \" existing decorations\"));\n                // Clear all teacher text highlight decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, []);\n                console.log('✅ Teacher text highlight decorations cleared:', newDecorations);\n                setTeacherSelectionDecorations(newDecorations);\n                // Force a layout update to ensure the decorations are removed\n                setTimeout({\n                    \"CodeEditor.useCallback[handleClearTeacherTextHighlight]\": ()=>{\n                        editor.layout();\n                        console.log('🔄 Editor layout refreshed after clearing highlights');\n                    }\n                }[\"CodeEditor.useCallback[handleClearTeacherTextHighlight]\"], 50);\n            } catch (error) {\n                var _editorRef_current;\n                console.error('❌ Error clearing teacher text highlight:', error);\n                console.error('🔍 Error details:', {\n                    editorAvailable: !!editorRef.current,\n                    modelAvailable: !!((_editorRef_current = editorRef.current) === null || _editorRef_current === void 0 ? void 0 : _editorRef_current.getModel()),\n                    userRole: userRole,\n                    currentDecorations: teacherSelectionDecorations,\n                    monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined'\n                });\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherTextHighlight]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle sync events for newly joined users\n    const handleSyncCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleSyncCode]\": (data)=>{\n            console.log('🔄 Received sync-code event:', data);\n            if (!editorRef.current) {\n                console.log('⏭️ Skipping sync-code: editor not available');\n                return;\n            }\n            try {\n                // Set the editor value directly\n                editorRef.current.setValue(data.code);\n                setCode(data.code);\n                latestCodeRef.current = data.code;\n                console.log('✅ Code synced successfully');\n            } catch (error) {\n                console.error('❌ Error syncing code:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleSyncCode]\"], []);\n    const handleSyncTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleSyncTeacherSelection]\": (data)=>{\n            console.log('🔄 Received sync-teacher-selection event:', data);\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't show highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model || !data.selection) {\n                    return;\n                }\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.selection.startLineNumber, data.selection.startColumn, data.selection.endLineNumber, data.selection.endColumn);\n                // Apply teacher selection decoration\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-selection-highlight',\n                            hoverMessage: {\n                                value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" selected this text (synced)\\n\\nRange: Line \").concat(data.selection.startLineNumber, \"-\").concat(data.selection.endLineNumber)\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Right\n                            }\n                        }\n                    }\n                ]);\n                setTeacherSelectionDecorations(newDecorations);\n                console.log('✅ Teacher selection synced successfully');\n            } catch (error) {\n                console.error('❌ Error syncing teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleSyncTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    const handleSyncTeacherCursor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleSyncTeacherCursor]\": (data)=>{\n            console.log('🔄 Received sync-teacher-cursor event:', data);\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't show cursor to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    return;\n                }\n                // Create a range for the cursor position\n                const cursorRange = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.position.lineNumber, data.position.column, data.position.lineNumber, data.position.column + 1);\n                // Apply teacher cursor decoration\n                const newCursorDecorations = editor.deltaDecorations(teacherCursorDecorations, [\n                    {\n                        range: cursorRange,\n                        options: {\n                            className: 'teacher-cursor',\n                            hoverMessage: {\n                                value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" cursor position (synced)\\n\\nLine \").concat(data.position.lineNumber, \", Column \").concat(data.position.column)\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Left\n                            }\n                        }\n                    }\n                ]);\n                setTeacherCursorDecorations(newCursorDecorations);\n                // Update teacher cursor position state\n                setTeacherCursorPosition({\n                    lineNumber: data.position.lineNumber,\n                    column: data.position.column,\n                    teacherName: data.teacherName\n                });\n                console.log('✅ Teacher cursor synced successfully');\n            } catch (error) {\n                console.error('❌ Error syncing teacher cursor:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleSyncTeacherCursor]\"], [\n        userRole,\n        teacherCursorDecorations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            // Handle user typing notifications\n            const handleUserTyping = {\n                \"CodeEditor.useEffect.handleUserTyping\": (param)=>{\n                    let { username, userId } = param;\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    // Don't show typing indicator for current user\n                    if (userId && userId === currentUserId) {\n                        return;\n                    }\n                    // Use the exact username without any modifications\n                    // This ensures we display exactly what the user entered on the dashboard\n                    setTypingUser(username);\n                    console.log(\"User typing: \".concat(username));\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserTyping\": ()=>setTypingUser(null)\n                    }[\"CodeEditor.useEffect.handleUserTyping\"], 2000);\n                }\n            }[\"CodeEditor.useEffect.handleUserTyping\"];\n            // Handle user list updates from both user-joined/user-left and room-users-updated events\n            const handleUserListUpdate = {\n                \"CodeEditor.useEffect.handleUserListUpdate\": (data)=>{\n                    console.log('User list update received:', data);\n                    let users = [];\n                    // Handle different event formats\n                    if (Array.isArray(data)) {\n                        // This is from user-joined or user-left events\n                        users = data;\n                    } else if (data && Array.isArray(data.users)) {\n                        // This is from room-users-updated event\n                        users = data.users.map({\n                            \"CodeEditor.useEffect.handleUserListUpdate\": (user)=>{\n                                if (typeof user === 'string') {\n                                    // If it's just a username string, convert to user object\n                                    return {\n                                        username: user\n                                    };\n                                }\n                                return user;\n                            }\n                        }[\"CodeEditor.useEffect.handleUserListUpdate\"]);\n                    }\n                    console.log('Processed users:', users);\n                    console.log('Current username state:', username);\n                    console.log('Stored username:', localStorage.getItem('username'));\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    console.log('Current userId from localStorage:', currentUserId);\n                    // Filter out any invalid users and map to display names\n                    const usernames = users.filter({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>user && user.username\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]).map({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>{\n                            const displayName = user.username;\n                            const isCurrentUser = user.userId === currentUserId;\n                            return isCurrentUser ? \"\".concat(displayName, \" (you)\") : displayName;\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]);\n                    console.log('Setting active users:', usernames);\n                    // Only update if we have users to display\n                    if (usernames.length > 0) {\n                        setActiveUsers(usernames);\n                    } else if (activeUsers.length === 0) {\n                        // If we don't have users from the server but we're connected, add ourselves\n                        const storedUsername = localStorage.getItem('username');\n                        if (storedUsername) {\n                            setActiveUsers([\n                                \"\".concat(storedUsername, \" (you)\")\n                            ]);\n                        }\n                    }\n                    // Log the current state of the active users list\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserListUpdate\": ()=>{\n                            console.log('Active users state after update:', activeUsers);\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate\"], 100);\n                }\n            }[\"CodeEditor.useEffect.handleUserListUpdate\"];\n            // Register event listeners\n            const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n            socketServiceInstance.on('code-update', handleCodeUpdate);\n            socketServiceInstance.on('user-typing', handleUserTyping);\n            socketServiceInstance.on('user-joined', handleUserListUpdate);\n            socketServiceInstance.on('user-left', handleUserListUpdate);\n            socketServiceInstance.on('room-users-updated', handleUserListUpdate);\n            socketServiceInstance.on('get-initial-code', handleGetInitialCode);\n            socketServiceInstance.on('initial-code-received', handleInitialCodeReceived);\n            socketServiceInstance.on('teacher-selection', handleTeacherSelection);\n            socketServiceInstance.on('clear-teacher-selection', handleClearTeacherSelection);\n            socketServiceInstance.on('teacher-cursor-position', handleTeacherCursorPosition);\n            socketServiceInstance.on('teacher-text-highlight', handleTeacherTextHighlight);\n            socketServiceInstance.on('clear-teacher-text-highlight', handleClearTeacherTextHighlight);\n            socketServiceInstance.on('sync-code', handleSyncCode);\n            socketServiceInstance.on('sync-teacher-selection', handleSyncTeacherSelection);\n            socketServiceInstance.on('sync-teacher-cursor', handleSyncTeacherCursor);\n            // Join the room when component mounts\n            if (roomId) {\n                console.log(\"Joining room:\", roomId);\n                // Only show loading on initial join, not for code updates\n                if (!editorRef.current) {\n                    setIsLoading(true);\n                }\n                const joinRoom = {\n                    \"CodeEditor.useEffect.joinRoom\": async ()=>{\n                        try {\n                            if (socketServiceInstance.isConnected()) {\n                                console.log(\"Socket connected, joining room\");\n                                const { username: validatedUsername, users, role } = await socketServiceInstance.joinRoom(roomId, username);\n                                console.log(\"Successfully joined room:\", roomId);\n                                console.log(\"Users in room:\", users);\n                                console.log(\"User role:\", role);\n                                if (validatedUsername !== username) {\n                                    console.log(\"Server validated username from \".concat(username, \" to \").concat(validatedUsername));\n                                    setUsername(validatedUsername);\n                                    localStorage.setItem('username', validatedUsername);\n                                }\n                                // Set user role and persist to localStorage\n                                if (role) {\n                                    setUserRole(role);\n                                    localStorage.setItem('userRole', role);\n                                    console.log(\"User role set to: \".concat(role, \" and persisted to localStorage\"));\n                                }\n                                if (users && Array.isArray(users)) {\n                                    console.log('Setting active users from join response');\n                                    handleUserListUpdate(users);\n                                }\n                                setIsLoading(false);\n                            } else {\n                                console.log(\"Socket not connected, trying to connect...\");\n                                await new Promise({\n                                    \"CodeEditor.useEffect.joinRoom\": (resolve)=>socketServiceInstance.onConnect({\n                                            \"CodeEditor.useEffect.joinRoom\": ()=>resolve()\n                                        }[\"CodeEditor.useEffect.joinRoom\"])\n                                }[\"CodeEditor.useEffect.joinRoom\"]);\n                                // After connect, try join again\n                                await joinRoom();\n                                return;\n                            }\n                        } catch (error) {\n                            console.error(\"Error joining room:\", error);\n                            setIsLoading(false);\n                        }\n                    }\n                }[\"CodeEditor.useEffect.joinRoom\"];\n                // Start the join process\n                joinRoom();\n                // Set a timeout to hide the loading screen even if the join fails\n                setTimeout({\n                    \"CodeEditor.useEffect\": ()=>{\n                        setIsLoading(false);\n                    }\n                }[\"CodeEditor.useEffect\"], 3000);\n            }\n            // Clean up event listeners when component unmounts\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    // Use an instance of SocketService\n                    const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                    socketServiceInstance.off('code-update', handleCodeUpdate);\n                    socketServiceInstance.off('user-typing', handleUserTyping);\n                    socketServiceInstance.off('user-joined', handleUserListUpdate);\n                    socketServiceInstance.off('user-left', handleUserListUpdate);\n                    socketServiceInstance.off('room-users-updated', handleUserListUpdate);\n                    socketServiceInstance.off('get-initial-code', handleGetInitialCode);\n                    socketServiceInstance.off('initial-code-received', handleInitialCodeReceived);\n                    socketServiceInstance.off('teacher-selection', handleTeacherSelection);\n                    socketServiceInstance.off('clear-teacher-selection', handleClearTeacherSelection);\n                    socketServiceInstance.off('teacher-cursor-position', handleTeacherCursorPosition);\n                    socketServiceInstance.off('teacher-text-highlight', handleTeacherTextHighlight);\n                    socketServiceInstance.off('clear-teacher-text-highlight', handleClearTeacherTextHighlight);\n                    socketServiceInstance.off('sync-code', handleSyncCode);\n                    socketServiceInstance.off('sync-teacher-selection', handleSyncTeacherSelection);\n                    socketServiceInstance.off('sync-teacher-cursor', handleSyncTeacherCursor);\n                    // Leave the room when component unmounts\n                    if (roomId) {\n                        socketServiceInstance.leaveRoom(roomId);\n                    }\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId,\n        initialUsername,\n        username,\n        handleCodeUpdate,\n        handleGetInitialCode,\n        handleInitialCodeReceived,\n        handleTeacherSelection,\n        handleClearTeacherSelection,\n        handleTeacherCursorPosition,\n        handleTeacherTextHighlight,\n        handleClearTeacherTextHighlight,\n        handleSyncCode,\n        handleSyncTeacherSelection,\n        handleSyncTeacherCursor\n    ]);\n    // Download code as file\n    const handleDownload = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"code-\".concat(roomId || \"session\", \".txt\");\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    // Upload code from file\n    const handleUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            var _event_target;\n            if (typeof ((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result) === \"string\") {\n                setCode(event.target.result);\n                latestCodeRef.current = event.target.result;\n            }\n        };\n        reader.readAsText(file);\n    };\n    // Show Monaco's find/replace dialog\n    const handleFindReplace = ()=>{\n        if (editorRef.current) {\n            const action = editorRef.current.getAction(\"actions.find\");\n            if (action) action.run();\n        }\n    };\n    // Insert code snippet\n    const insertSnippet = (snippet)=>{\n        if (editorRef.current) {\n            const model = editorRef.current.getModel();\n            const position = editorRef.current.getPosition();\n            if (model && position) {\n                editorRef.current.executeEdits(\"snippet\", [\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(position.lineNumber, position.column, position.lineNumber, position.column),\n                        text: snippet,\n                        forceMoveMarkers: true\n                    }\n                ]);\n                editorRef.current.focus();\n            }\n        }\n    };\n    // Use the instance of SocketService for the connect method\n    const handleReconnect = ()=>{\n        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n        socketServiceInstance.connect();\n    };\n    // Manual test function for debugging teacher text highlighting\n    const testManualHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[testManualHighlight]\": ()=>{\n            console.log('🧪 Manual test highlight triggered');\n            if (!editorRef.current) {\n                console.error('❌ Editor not available for manual test');\n                return;\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('❌ Monaco Editor model not available for manual test');\n                    return;\n                }\n                console.log('✅ Manual test: Editor and model available');\n                console.log('✅ Manual test: Model line count:', model.getLineCount());\n                console.log('✅ Manual test: Model content preview:', model.getValue().substring(0, 100));\n                // Create a test range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(1, 1, 1, 20);\n                console.log('✅ Manual test: Range created:', range);\n                // Test multiple decoration approaches with yellow/orange theme\n                const decorationOptions = [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-selection-highlight teacher-text-highlight teacher-highlight',\n                            hoverMessage: {\n                                value: 'Manual test selection highlight - Yellow background with orange border!'\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Right\n                            }\n                        }\n                    },\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(2, 1, 2, 15),\n                        options: {\n                            className: 'teacher-cursor',\n                            hoverMessage: {\n                                value: 'Manual test cursor - Blinking orange cursor!'\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges\n                        }\n                    },\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(3, 1, 3, 25),\n                        options: {\n                            className: 'teacher-selection-highlight',\n                            hoverMessage: {\n                                value: 'Manual test selection - Line 3!'\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Left\n                            }\n                        }\n                    }\n                ];\n                // Apply decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, decorationOptions);\n                console.log('✅ Manual test: Decorations applied:', newDecorations);\n                setTeacherSelectionDecorations(newDecorations);\n                // Debug: Check DOM after decoration\n                setTimeout({\n                    \"CodeEditor.useCallback[testManualHighlight]\": ()=>{\n                        const decorationElements = document.querySelectorAll('.teacher-text-highlight, .teacher-highlight');\n                        console.log('🔍 Manual test: Found decoration elements:', decorationElements.length);\n                        // Force add a visible element for testing with yellow/orange theme\n                        const testDiv = document.createElement('div');\n                        testDiv.style.cssText = \"\\n          position: fixed;\\n          top: 10px;\\n          right: 10px;\\n          background: rgba(255, 152, 0, 0.9);\\n          color: white;\\n          padding: 12px;\\n          border-radius: 6px;\\n          border: 2px solid rgba(255, 235, 59, 0.8);\\n          box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);\\n          z-index: 9999;\\n          font-family: monospace;\\n          font-weight: bold;\\n          animation: pulse 2s ease-in-out infinite;\\n        \";\n                        testDiv.innerHTML = \"\\n          \\uD83C\\uDFAF Manual Test Results<br>\\n          \\uD83D\\uDCCA \".concat(newDecorations.length, \" decorations applied<br>\\n          \\uD83C\\uDFA8 Yellow/Orange theme active\\n        \");\n                        document.body.appendChild(testDiv);\n                        setTimeout({\n                            \"CodeEditor.useCallback[testManualHighlight]\": ()=>{\n                                if (document.body.contains(testDiv)) {\n                                    document.body.removeChild(testDiv);\n                                }\n                            }\n                        }[\"CodeEditor.useCallback[testManualHighlight]\"], 4000);\n                    }\n                }[\"CodeEditor.useCallback[testManualHighlight]\"], 100);\n            } catch (error) {\n                console.error('❌ Manual test error:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[testManualHighlight]\"], [\n        teacherSelectionDecorations\n    ]);\n    // Add manual test to window for debugging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            if (true) {\n                window.testManualHighlight = testManualHighlight;\n                console.log('🔧 Manual test function added to window.testManualHighlight()');\n            }\n        }\n    }[\"CodeEditor.useEffect\"], [\n        testManualHighlight\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-[80vh] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-800 shadow-xl flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"bg-gray-100 dark:bg-zinc-800 px-4 py-2 flex flex-wrap items-center justify-between border-b border-gray-200 dark:border-gray-700 gap-2 min-h-[48px]\",\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    style: {\n                        zIndex: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-3 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 min-w-[90px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1739,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-400 truncate\",\n                                            children: isConnected ? 'Connected' : 'Disconnected'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1740,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded ml-1 transition-colors\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1744,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1738,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: language,\n                                    onChange: (e)=>changeLanguage(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"javascript\",\n                                            children: \"JavaScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1759,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"typescript\",\n                                            children: \"TypeScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1760,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"python3\",\n                                            children: \"Python\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1761,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"java\",\n                                            children: \"Java\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1762,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"csharp\",\n                                            children: \"C#\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1763,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"c\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1764,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"cpp\",\n                                            children: \"C++\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1765,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"go\",\n                                            children: \"Go\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1766,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ruby\",\n                                            children: \"Ruby\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1767,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rust\",\n                                            children: \"Rust\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1768,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"php\",\n                                            children: \"PHP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1769,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1754,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: formatCurrentCode,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm bg-blue-600 hover:bg-blue-700 text-white transition-colors whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiAlignLeft, {\n                                            size: 14\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1779,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Format\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1780,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1773,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: executeCode,\n                                    disabled: isExecuting,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm \".concat(isExecuting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700', \" text-white transition-colors whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: isExecuting ? 1 : 1.05\n                                    },\n                                    whileTap: {\n                                        scale: isExecuting ? 1 : 0.95\n                                    },\n                                    children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                                className: \"animate-spin\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1793,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Running...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1794,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiPlay, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1798,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Run Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1799,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1784,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Room:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1806,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-white dark:bg-zinc-700 px-2 py-1 rounded font-mono text-xs truncate max-w-[120px]\",\n                                            children: roomId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1807,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                            onClick: copyCodeToClipboard,\n                                            className: \"text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            title: \"Copy code to clipboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCopy, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1815,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1808,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1805,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1736,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-2 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowOutput((prev)=>!prev),\n                                    className: \"flex items-center space-x-1 text-sm \".concat(showOutput ? 'text-blue-500 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300', \" hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCode, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1828,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Output\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1829,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1822,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: toggleUserList,\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUsers, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1839,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                activeUsers.length,\n                                                \" online\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1840,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1833,\n                                    columnNumber: 13\n                                }, this),\n                                userRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    className: \"flex items-center space-x-1 text-xs font-medium px-2 py-1 rounded-full whitespace-nowrap \".concat(userRole === 'teacher' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'),\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: userRole === 'teacher' ? '👨‍🏫' : '👨‍🎓'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1855,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: userRole === 'teacher' ? 'Teacher' : 'Student'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1856,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1845,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"flex items-center space-x-1 text-sm text-yellow-600 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSun, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1868,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiMoon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1868,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1861,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setMinimapEnabled((v)=>!v),\n                                    className: \"flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Minimap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Minimap\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1879,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1872,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleFindReplace,\n                                    className: \"flex items-center space-x-1 text-sm text-green-600 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Find/Replace\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSearch, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1890,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1883,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleDownload,\n                                    className: \"flex items-center space-x-1 text-sm text-indigo-600 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Download Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiDownload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1901,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1894,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"flex items-center space-x-1 text-sm text-pink-600 dark:text-pink-300 bg-pink-100 dark:bg-pink-900/30 hover:bg-pink-200 dark:hover:bg-pink-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Upload Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUpload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1912,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1905,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \".js,.ts,.py,.java,.c,.cpp,.go,.rb,.rs,.php,.txt\",\n                                    style: {\n                                        display: \"none\"\n                                    },\n                                    onChange: handleUpload\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1914,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    onChange: (e)=>e.target.value && insertSnippet(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[100px]\",\n                                    defaultValue: \"\",\n                                    title: \"Insert Snippet\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Snippets\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1929,\n                                            columnNumber: 15\n                                        }, this),\n                                        (SNIPPETS[language] || SNIPPETS['javascript']).map((snippet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: snippet.value,\n                                                children: snippet.label\n                                            }, snippet.label, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1931,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1923,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowShortcuts(true),\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Keyboard Shortcuts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiHelpCircle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1943,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1936,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleLeaveRoom,\n                                    className: \"flex items-center space-x-1 text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors px-2 py-1 rounded-md ml-2 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Leave Room\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1953,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1947,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1820,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1729,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showUserList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute right-0 top-12 bg-white dark:bg-zinc-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 w-48 overflow-hidden\",\n                        initial: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            height: 'auto'\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 border-b border-gray-200 dark:border-gray-700 font-medium text-sm text-gray-600 dark:text-gray-300\",\n                                children: \"Active Users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1968,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"max-h-48 overflow-y-auto\",\n                                children: activeUsers.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.li, {\n                                        className: \"px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-zinc-700 flex items-center space-x-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full bg-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1980,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: user\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1981,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1973,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1971,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1961,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1959,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"h-[calc(100%-40px)] \".concat(showOutput ? 'pb-[33%]' : ''),\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.4,\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    height: \"100%\",\n                                    defaultLanguage: language,\n                                    defaultValue: code,\n                                    onChange: handleEditorChange,\n                                    onMount: handleEditorDidMount,\n                                    theme: theme === \"dark\" ? \"vs-dark\" : \"light\",\n                                    options: {\n                                        minimap: {\n                                            enabled: minimapEnabled\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1999,\n                                    columnNumber: 15\n                                }, this),\n                                teacherCursorPosition && editorRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TeacherCursor, {\n                                    editor: editorRef.current,\n                                    position: teacherCursorPosition\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2011,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1998,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1996,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1990,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: typingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 left-4 text-sm text-gray-400 bg-gray-800/80 px-3 py-1 rounded-full backdrop-blur-sm\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            typingUser,\n                            \" is typing...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 2023,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 2021,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gray-900 text-white border-t border-gray-700 h-1/3 overflow-hidden flex flex-col\",\n                        initial: {\n                            height: 0\n                        },\n                        animate: {\n                            height: '33%'\n                        },\n                        exit: {\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 bg-gray-800 border-b border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 2046,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearOutput,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 2048,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: testPistonAPI,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-blue-400 hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Test API\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 2055,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowOutput(false),\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 2062,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 2047,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 2045,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-auto font-mono text-sm whitespace-pre-wrap\",\n                                children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2073,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Executing code...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2074,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2072,\n                                    columnNumber: 19\n                                }, this) : executionError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400\",\n                                    children: executionError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2077,\n                                    columnNumber: 19\n                                }, this) : executionOutput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: executionOutput\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2079,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: 'Click \"Run Code\" to execute your code.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2081,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 2070,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 2038,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 2036,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: copySuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 right-4 text-sm text-white bg-green-500 px-3 py-1 rounded-full shadow-lg\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: \"Copied to clipboard!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 2091,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 2089,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showShortcuts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 max-w-md w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-700 dark:hover:text-white\",\n                                    onClick: ()=>setShowShortcuts(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2113,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    children: \"Keyboard Shortcuts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2119,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Run Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2121,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+Enter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2121,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Format Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2122,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Shift+Alt+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2122,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Find/Replace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2123,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2123,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Download Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2124,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+D\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2124,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Upload Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2125,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+U\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2125,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Minimap:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2126,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+M\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2126,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Theme:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2127,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+T\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2127,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Show Shortcuts:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2128,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+H\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2128,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2120,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 2112,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 2106,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 2104,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 1727,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 1725,\n        columnNumber: 5\n    }, this);\n}\n_s(CodeEditor, \"sxNeq0t2LGrybDSMrUfzOiW785Q=\", false, function() {\n    return [\n        _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme\n    ];\n});\n_c = CodeEditor;\nconst TeacherCursor = (param)=>{\n    let { editor, position } = param;\n    _s1();\n    const [cursorStyle, setCursorStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherCursor.useEffect\": ()=>{\n            const updateCursorPosition = {\n                \"TeacherCursor.useEffect.updateCursorPosition\": ()=>{\n                    try {\n                        // Get the pixel position of the cursor\n                        const pixelPosition = editor.getScrolledVisiblePosition({\n                            lineNumber: position.lineNumber,\n                            column: position.column\n                        });\n                        if (pixelPosition) {\n                            // Get the editor container position\n                            const editorContainer = editor.getDomNode();\n                            if (editorContainer) {\n                                const containerRect = editorContainer.getBoundingClientRect();\n                                setCursorStyle({\n                                    position: 'absolute',\n                                    left: \"\".concat(pixelPosition.left, \"px\"),\n                                    top: \"\".concat(pixelPosition.top, \"px\"),\n                                    zIndex: 1000,\n                                    pointerEvents: 'none'\n                                });\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error updating teacher cursor position:', error);\n                    }\n                }\n            }[\"TeacherCursor.useEffect.updateCursorPosition\"];\n            // Update position immediately\n            updateCursorPosition();\n            // Update position when editor scrolls or layout changes\n            const scrollDisposable = editor.onDidScrollChange(updateCursorPosition);\n            const layoutDisposable = editor.onDidLayoutChange(updateCursorPosition);\n            return ({\n                \"TeacherCursor.useEffect\": ()=>{\n                    scrollDisposable.dispose();\n                    layoutDisposable.dispose();\n                }\n            })[\"TeacherCursor.useEffect\"];\n        }\n    }[\"TeacherCursor.useEffect\"], [\n        editor,\n        position\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: cursorStyle,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"teacher-cursor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"teacher-cursor-label\",\n                children: position.teacherName\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 2193,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 2192,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 2191,\n        columnNumber: 5\n    }, undefined);\n}; // Keyboard shortcuts global handler\n // useEffect(() => {\n //   const handler = (e: KeyboardEvent) => {\n //     if (e.altKey && e.key.toLowerCase() === \"d\") {\n //       e.preventDefault();\n //       handleDownload();\n //     } else if (e.altKey && e.key.toLowerCase() === \"u\") {\n //       e.preventDefault();\n //       fileInputRef.current?.click();\n //     } else if (e.altKey && e.key.toLowerCase() === \"m\") {\n //       e.preventDefault();\n //       setMinimapEnabled((v: boolean) => !v);\n //     } else if (e.altKey && e.key.toLowerCase() === \"t\") {\n //       e.preventDefault();\n //       setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n //     } else if (e.altKey && e.key.toLowerCase() === \"h\") {\n //       e.preventDefault();\n //       setShowShortcuts(true);\n //     }\n //   };\n //   window.addEventListener(\"keydown\", handler);\n //   return () => window.removeEventListener(\"keydown\", handler);\n // }, [theme, setTheme]);\n_s1(TeacherCursor, \"NrZZSKlPdtAtpq9NKiew7IOqw6k=\");\n_c1 = TeacherCursor;\nvar _c, _c1;\n$RefreshReg$(_c, \"CodeEditor\");\n$RefreshReg$(_c1, \"TeacherCursor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CodeEditor.tsx\n"));

/***/ })

});