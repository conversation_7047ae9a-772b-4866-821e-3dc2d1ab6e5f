/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_editor_common_services_sync_recursive_"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services sync recursive ^.*$":
/*!******************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/editor/common/services/ sync ^.*$ ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./editorBaseApi": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorBaseApi.js",
	"./editorBaseApi.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorBaseApi.js",
	"./editorSimpleWorker": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorSimpleWorker.js",
	"./editorSimpleWorker.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorSimpleWorker.js",
	"./editorWorker": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorWorker.js",
	"./editorWorker.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorWorker.js",
	"./editorWorkerHost": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorWorkerHost.js",
	"./editorWorkerHost.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorWorkerHost.js",
	"./findSectionHeaders": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/findSectionHeaders.js",
	"./findSectionHeaders.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/findSectionHeaders.js",
	"./getIconClasses": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/getIconClasses.js",
	"./getIconClasses.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/getIconClasses.js",
	"./languageFeatureDebounce": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatureDebounce.js",
	"./languageFeatureDebounce.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatureDebounce.js",
	"./languageFeatures": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatures.js",
	"./languageFeatures.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatures.js",
	"./languageFeaturesService": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageFeaturesService.js",
	"./languageFeaturesService.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageFeaturesService.js",
	"./languageService": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageService.js",
	"./languageService.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageService.js",
	"./languagesAssociations": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/languagesAssociations.js",
	"./languagesAssociations.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/languagesAssociations.js",
	"./languagesRegistry": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/languagesRegistry.js",
	"./languagesRegistry.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/languagesRegistry.js",
	"./markerDecorations": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorations.js",
	"./markerDecorations.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorations.js",
	"./markerDecorationsService": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorationsService.js",
	"./markerDecorationsService.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorationsService.js",
	"./model": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/model.js",
	"./model.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/model.js",
	"./modelService": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/modelService.js",
	"./modelService.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/modelService.js",
	"./resolverService": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/resolverService.js",
	"./resolverService.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/resolverService.js",
	"./semanticTokensDto": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensDto.js",
	"./semanticTokensDto.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensDto.js",
	"./semanticTokensProviderStyling": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensProviderStyling.js",
	"./semanticTokensProviderStyling.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensProviderStyling.js",
	"./semanticTokensStyling": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStyling.js",
	"./semanticTokensStyling.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStyling.js",
	"./semanticTokensStylingService": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStylingService.js",
	"./semanticTokensStylingService.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStylingService.js",
	"./textModelSync/textModelSync.impl": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.impl.js",
	"./textModelSync/textModelSync.impl.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.impl.js",
	"./textModelSync/textModelSync.protocol": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js",
	"./textModelSync/textModelSync.protocol.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js",
	"./textResourceConfiguration": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/textResourceConfiguration.js",
	"./textResourceConfiguration.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/textResourceConfiguration.js",
	"./treeSitterParserService": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/treeSitterParserService.js",
	"./treeSitterParserService.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/treeSitterParserService.js",
	"./treeViewsDnd": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDnd.js",
	"./treeViewsDnd.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDnd.js",
	"./treeViewsDndService": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDndService.js",
	"./treeViewsDndService.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDndService.js",
	"./unicodeTextModelHighlighter": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/unicodeTextModelHighlighter.js",
	"./unicodeTextModelHighlighter.js": "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/unicodeTextModelHighlighter.js"
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services sync recursive ^.*$";

/***/ }),

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js":
/*!***********************************************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbW9uYWNvLWVkaXRvci9lc20vdnMvZWRpdG9yL2NvbW1vbi9zZXJ2aWNlcy90ZXh0TW9kZWxTeW5jL3RleHRNb2RlbFN5bmMucHJvdG9jb2wuanMiLCJtYXBwaW5ncyI6IjtBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ1UiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0XFxyZWFsY29kZVxcbm9kZV9tb2R1bGVzXFxtb25hY28tZWRpdG9yXFxlc21cXHZzXFxlZGl0b3JcXGNvbW1vblxcc2VydmljZXNcXHRleHRNb2RlbFN5bmNcXHRleHRNb2RlbFN5bmMucHJvdG9jb2wuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyotLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqICBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi4gQWxsIHJpZ2h0cyByZXNlcnZlZC5cbiAqICBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIExpY2Vuc2UuIFNlZSBMaWNlbnNlLnR4dCBpbiB0aGUgcHJvamVjdCByb290IGZvciBsaWNlbnNlIGluZm9ybWF0aW9uLlxuICotLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5leHBvcnQge307XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js\n"));

/***/ })

}]);