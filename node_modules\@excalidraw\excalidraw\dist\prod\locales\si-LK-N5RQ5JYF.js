import"../chunk-SRAX5OIU.js";var t={paste:"\u0D85\u0DBD\u0DC0\u0DB1\u0DCA\u0DB1",pasteAsPlaintext:"",pasteCharts:"\u0DB4\u0DCA\u200D\u0DBB\u0DC3\u0DCA\u0DAE\u0DCF\u0DBB\u0DBA",selectAll:"\u0DC3\u0DD2\u0DBA\u0DBD\u0DCA\u0DBD\u0DB8",multiSelect:"\u0DAD\u0DDD\u0DBB\u0DCF \u0D9C\u0DD0\u0DB1\u0DD3\u0DB8\u0DA7 \u0D85\u0D82\u0D9C\u0DBA \u0D91\u0D9A\u0DAD\u0DD4 \u0D9A\u0DBB\u0DB1\u0DCA\u0DB1",moveCanvas:"\u0D9A\u0DD0\u0DB1\u0DCA\u0DC0\u0DC3\u0DBA \u0DA0\u0DBD\u0DB1\u0DBA \u0D9A\u0DBB\u0DB1\u0DCA\u0DB1",cut:"\u0D9A\u0DB4\u0DB1\u0DCA\u0DB1",copy:"\u0DB4\u0DD2\u0DA7\u0DB4\u0DAD\u0DCA \u0D9A\u0DBB\u0DB1\u0DCA\u0DB1",copyAsPng:"PNG \u0DBD\u0DD9\u0DC3 \u0DB4\u0DD2\u0DA7\u0DB4\u0DAD\u0DCA \u0D9A\u0DBB\u0DB1\u0DCA\u0DB1",copyAsSvg:"SVG \u0DBD\u0DD9\u0DC3 \u0DB4\u0DD2\u0DA7\u0DB4\u0DAD\u0DCA \u0D9A\u0DBB\u0DB1\u0DCA\u0DB1",copyText:"",copySource:"",convertToCode:"",bringForward:"\u0D89\u0DAF\u0DD2\u0DBB\u0DD2\u0DBA\u0DA7 \u0D9C\u0DD9\u0DB1\u0DCA\u0DB1",sendToBack:"\u0DB4\u0DC3\u0DD4\u0DB4\u0DC3\u0DA7\u0DB8 \u0D9C\u0DD9\u0DB1\u0DD2\u0DBA\u0DB1\u0DCA\u0DB1",bringToFront:"\u0D89\u0DAF\u0DD2\u0DBB\u0DD2\u0DBA\u0DA7\u0DB8 \u0D9C\u0DD9\u0DB1\u0DCA\u0DB1",sendBackward:"\u0DB4\u0DC3\u0DD4\u0DB4\u0DC3\u0DA7 \u0D9C\u0DD9\u0DB1\u0DD2\u0DBA\u0DB1\u0DCA\u0DB1",delete:"\u0DB8\u0D9A\u0DB1\u0DCA\u0DB1",copyStyles:"",pasteStyles:"",stroke:"",background:"",fill:"",strokeWidth:"",strokeStyle:"",strokeStyle_solid:"",strokeStyle_dashed:"",strokeStyle_dotted:"",sloppiness:"",opacity:"",textAlign:"",edges:"",sharp:"",round:"",arrowheads:"",arrowhead_none:"",arrowhead_arrow:"",arrowhead_bar:"",arrowhead_circle:"",arrowhead_circle_outline:"",arrowhead_triangle:"",arrowhead_triangle_outline:"",arrowhead_diamond:"",arrowhead_diamond_outline:"",fontSize:"",fontFamily:"",addWatermark:"",handDrawn:"",normal:"",code:"",small:"",medium:"",large:"",veryLarge:"\u0D89\u0DAD\u0DCF \u0DC0\u0DD2\u0DC1\u0DCF\u0DBD",solid:"\u0DC0\u0DD2\u0DC1\u0DCF\u0DBD",hachure:"\u0DB8\u0DB0\u0DCA\u200D\u0DBA\u0DB8",zigzag:"",crossHatch:"",thin:"\u0D9A\u0DD9\u0DA7\u0DCA\u0DA7\u0DD4",bold:"\u0DAD\u0DAF",left:"\u0DC0\u0DB8",center:"\u0DB8\u0DD0\u0DAF",right:"\u0DAF\u0D9A\u0DD4\u0DAB",extraBold:"\u0D89\u0DAD\u0DCF \u0DAD\u0DAF",architect:"\u0DC0\u0DCF\u0DC3\u0DCA\u0DAD\u0DD4\u0DC0\u0DDA\u0DAF\u0DD3\u0DBA\u0DCF",artist:"\u0D9A\u0DBD\u0DCF\u0D9A\u0DBB\u0DD4",cartoonist:"\u0DC3\u0DD0\u0D9A\u0DD2\u0DBD\u0DD2\u0DBB\u0DD6\u0D9A\u0DBB\u0DD4",fileTitle:"\u0D9C\u0DDC\u0DB1\u0DD4 \u0DB1\u0DCF\u0DB8\u0DBA",colorPicker:"\u0DB4\u0DCF\u0DA7 \u0DAD\u0DDD\u0DBB\u0D9A\u0DBA",canvasColors:"",canvasBackground:"\u0D9A\u0DD0\u0DB1\u0DCA\u0DC0\u0DC3 \u0DB4\u0DC3\u0DD4\u0DB6\u0DD2\u0DB8",drawingCanvas:"\u0DA0\u0DD2\u0DAD\u0DCA\u200D\u0DBB\u0D9A \u0D9A\u0DD0\u0DB1\u0DCA\u0DC0\u0DC3\u0DBA",layers:"\u0DBD\u0DDA\u0DBA\u0DBB",actions:"\u0D9A\u0DCA\u200D\u0DBB\u0DD2\u0DBA\u0DCF\u0D9A\u0DCF\u0DBB\u0D9A\u0DB8",language:"\u0DB7\u0DCF\u0DC2\u0DCF\u0DC0 ",liveCollaboration:"",duplicateSelection:"",untitled:"",name:"\u0DB1\u0DB8",yourName:"",madeWithExcalidraw:"",group:"",ungroup:"",collaborators:"",showGrid:"",addToLibrary:"",removeFromLibrary:"",libraryLoadingMessage:"",libraries:"",loadingScene:"",align:"",alignTop:"",alignBottom:"",alignLeft:"",alignRight:"",centerVertically:"",centerHorizontally:"",distributeHorizontally:"",distributeVertically:"",flipHorizontal:"",flipVertical:"",viewMode:"",share:"",showStroke:"",showBackground:"",toggleTheme:"",personalLib:"",excalidrawLib:"",decreaseFontSize:"",increaseFontSize:"",unbindText:"",bindText:"",createContainerFromText:"",link:{edit:"",editEmbed:"",create:"",createEmbed:"",label:"",labelEmbed:"",empty:""},lineEditor:{edit:"",exit:""},elementLock:{lock:"",unlock:"",lockAll:"",unlockAll:""},statusPublished:"",sidebarLock:"",selectAllElementsInFrame:"",removeAllElementsFromFrame:"",eyeDropper:"",textToDiagram:"",prompt:""},r={noItems:"",hint_emptyLibrary:"",hint_emptyPrivateLibrary:""},a={clearReset:"",exportJSON:"",exportImage:"",export:"",copyToClipboard:"",save:"",saveAs:"",load:"",getShareableLink:"",close:"",selectLanguage:"",scrollBackToContent:"",zoomIn:"",zoomOut:"",resetZoom:"",menu:"",done:"",edit:"",undo:"",redo:"",resetLibrary:"",createNewRoom:"",fullScreen:"",darkMode:"",lightMode:"",zenMode:"",objectsSnapMode:"",exitZenMode:"",cancel:"",clear:"",remove:"",embed:"",publishLibrary:"",submit:"",confirm:"",embeddableInteractionButton:""},i={clearReset:"",couldNotCreateShareableLink:"",couldNotCreateShareableLinkTooBig:"",couldNotLoadInvalidFile:"",importBackendFailed:"",cannotExportEmptyCanvas:"",couldNotCopyToClipboard:"",decryptFailed:"",uploadedSecurly:"",loadSceneOverridePrompt:"",collabStopOverridePrompt:"",errorAddingToLibrary:"",errorRemovingFromLibrary:"",confirmAddLibrary:"",imageDoesNotContainScene:"",cannotRestoreFromImage:"",invalidSceneUrl:"",resetLibrary:"",removeItemsFromsLibrary:"",invalidEncryptionKey:"",collabOfflineWarning:""},l={unsupportedFileType:"",imageInsertError:"",fileTooBig:"",svgImageInsertError:"",failedToFetchImage:"",invalidSVGString:"",cannotResolveCollabServer:"",importLibraryError:"",collabSaveFailed:"",collabSaveFailed_sizeExceeded:"",imageToolNotSupported:"",brave_measure_text_error:{line1:"",line2:"",line3:"",line4:""},libraryElementTypeError:{embeddable:"",iframe:"",image:""},asyncPasteFailedOnRead:"",asyncPasteFailedOnParse:"",copyToSystemClipboardFailed:""},n={selection:"",image:"",rectangle:"",diamond:"",ellipse:"",arrow:"",line:"",freedraw:"",text:"",library:"",lock:"",penMode:"",link:"",eraser:"",frame:"",magicframe:"",embeddable:"",laser:"",hand:"",extraTools:"",mermaidToExcalidraw:"",magicSettings:""},s={canvasActions:"",selectedShapeActions:"",shapes:""},d={canvasPanning:"",linearElement:"",freeDraw:"",text:"",embeddable:"",text_selected:"",text_editing:"",linearElementMulti:"",lockAngle:"",resize:"",resizeImage:"",rotate:"",lineEditor_info:"",lineEditor_pointSelected:"",lineEditor_nothingSelected:"",placeImage:"",publishLibrary:"",bindTextToElement:"",deepBoxSelect:"",eraserRevert:"",firefox_clipboard_write:"",disableSnapping:""},c={cannotShowPreview:"",canvasTooBig:"",canvasTooBigTip:""},m={headingMain:"",clearCanvasMessage:"",clearCanvasCaveat:"",trackedToSentry:"",openIssueMessage:"",sceneContent:""},p={desc_intro:"",desc_privacy:"",button_startSession:"",button_stopSession:"",desc_inProgressIntro:"",desc_shareLink:"",desc_exitSession:"",shareTitle:""},g={title:""},b={disk_title:"",disk_details:"",disk_button:"",link_title:"",link_details:"",link_button:"",excalidrawplus_description:"",excalidrawplus_button:"",excalidrawplus_exportError:""},u={blog:"",click:"",deepSelect:"",deepBoxSelect:"",curvedArrow:"",curvedLine:"",documentation:"",doubleClick:"",drag:"",editor:"",editLineArrowPoints:"",editText:"",github:"",howto:"",or:"",preventBinding:"",tools:"",shortcuts:"",textFinish:"",textNewLine:"",title:"",view:"",zoomToFit:"",zoomToSelection:"",toggleElementLock:"",movePageUpDown:"",movePageLeftRight:""},h={title:""},y={title:"",itemName:"",authorName:"",githubUsername:"",twitterUsername:"",libraryName:"",libraryDesc:"",website:"",placeholder:{authorName:"",libraryName:"",libraryDesc:"",githubHandle:"",twitterHandle:"",website:""},errors:{required:"",website:""},noteDescription:"",noteGuidelines:"",noteLicense:"",noteItems:"",atleastOneLibItem:"",republishWarning:""},v={title:"",content:""},S={resetLibrary:"",removeItemsFromLib:""},w={header:"",label:{withBackground:"",onlySelected:"",darkMode:"",embedScene:"",scale:"",padding:""},tooltip:{embedScene:""},title:{exportToPng:"",exportToSvg:"",copyPngToClipboard:""},button:{exportToPng:"",exportToSvg:"",copyPngToClipboard:""}},k={tooltip:"",link:""},T={angle:"",element:"",elements:"",height:"",scene:"",selected:"",storage:"",title:"",total:"",version:"",versionCopy:"",versionNotAvailable:"",width:""},_={addedToLibrary:"",copyStyles:"",copyToClipboard:"",copyToClipboardAsPng:"",fileSaved:"",fileSavedToFilename:"",canvas:"",selection:"",pasteAsSingleElement:"",unableToEmbed:"",unrecognizedLinkFormat:""},x={transparent:"",black:"",white:"",red:"",pink:"",grape:"",violet:"",gray:"",blue:"",cyan:"",teal:"",green:"",yellow:"",orange:"",bronze:""},L={app:{center_heading:"",center_heading_plus:"",menuHint:""},defaults:{menuHint:"",center_heading:"",toolbarHint:"",helpHint:""}},C={mostUsedCustomColors:"",colors:"",shades:"",hexCode:"",noShades:""},F={action:{exportToImage:{title:"",button:"",description:""},saveToDisk:{title:"",button:"",description:""},excalidrawPlus:{title:"",button:"",description:""}},modal:{loadFromFile:{title:"",button:"",description:""},shareableLink:{title:"",button:"",description:""}}},f={title:"",button:"",description:"",syntax:"",preview:""},E={labels:t,library:r,buttons:a,alerts:i,errors:l,toolBar:n,headings:s,hints:d,canvasError:c,errorSplash:m,roomDialog:p,errorDialog:g,exportDialog:b,helpDialog:u,clearCanvasDialog:h,publishDialog:y,publishSuccessDialog:v,confirmDialog:S,imageExportDialog:w,encrypted:k,stats:T,toast:_,colors:x,welcomeScreen:L,colorPicker:C,overwriteConfirm:F,mermaid:f};export{i as alerts,a as buttons,c as canvasError,h as clearCanvasDialog,C as colorPicker,x as colors,S as confirmDialog,E as default,k as encrypted,g as errorDialog,m as errorSplash,l as errors,b as exportDialog,s as headings,u as helpDialog,d as hints,w as imageExportDialog,t as labels,r as library,f as mermaid,F as overwriteConfirm,y as publishDialog,v as publishSuccessDialog,p as roomDialog,T as stats,_ as toast,n as toolBar,L as welcomeScreen};
