"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_typespec_typespec_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/typespec/typespec.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/typespec/typespec.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/typespec/typespec.ts\nvar bounded = (text) => `\\\\b${text}\\\\b`;\nvar notBefore = (regex) => `(?!${regex})`;\nvar identifierStart = \"[_a-zA-Z]\";\nvar identifierContinue = \"[_a-zA-Z0-9]\";\nvar identifier = bounded(`${identifierStart}${identifierContinue}*`);\nvar directive = bounded(`[_a-zA-Z-0-9]+`);\nvar keywords = [\n  \"import\",\n  \"model\",\n  \"scalar\",\n  \"namespace\",\n  \"op\",\n  \"interface\",\n  \"union\",\n  \"using\",\n  \"is\",\n  \"extends\",\n  \"enum\",\n  \"alias\",\n  \"return\",\n  \"void\",\n  \"if\",\n  \"else\",\n  \"projection\",\n  \"dec\",\n  \"extern\",\n  \"fn\"\n];\nvar namedLiterals = [\"true\", \"false\", \"null\", \"unknown\", \"never\"];\nvar nonCommentWs = `[ \\\\t\\\\r\\\\n]`;\nvar numericLiteral = `[0-9]+`;\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"/**\", close: \" */\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ],\n  indentationRules: {\n    decreaseIndentPattern: new RegExp(\"^((?!.*?/\\\\*).*\\\\*/)?\\\\s*[\\\\}\\\\]].*$\"),\n    increaseIndentPattern: new RegExp(\n      \"^((?!//).)*(\\\\{([^}\\\"'`/]*|(\\\\t|[ ])*//.*)|\\\\([^)\\\"'`/]*|\\\\[[^\\\\]\\\"'`/]*)$\"\n    ),\n    // e.g.  * ...| or */| or *-----*/|\n    unIndentedLinePattern: new RegExp(\n      \"^(\\\\t|[ ])*[ ]\\\\*[^/]*\\\\*/\\\\s*$|^(\\\\t|[ ])*[ ]\\\\*/\\\\s*$|^(\\\\t|[ ])*[ ]\\\\*([ ]([^\\\\*]|\\\\*(?!/))*)?$\"\n    )\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".tsp\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  symbols: /[=:;<>]+/,\n  keywords,\n  namedLiterals,\n  escapes: `\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|\"|\\\\\\${)`,\n  tokenizer: {\n    root: [{ include: \"@expression\" }, { include: \"@whitespace\" }],\n    stringVerbatim: [\n      { regex: `(|\"|\"\")[^\"]`, action: { token: \"string\" } },\n      { regex: `\"\"\"${notBefore(`\"`)}`, action: { token: \"string\", next: \"@pop\" } }\n    ],\n    stringLiteral: [\n      { regex: `\\\\\\${`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `[^\\\\\\\\\"$]+`, action: { token: \"string\" } },\n      { regex: \"@escapes\", action: { token: \"string.escape\" } },\n      { regex: `\\\\\\\\.`, action: { token: \"string.escape.invalid\" } },\n      { regex: `\"`, action: { token: \"string\", next: \"@pop\" } }\n    ],\n    bracketCounting: [\n      { regex: `{`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `}`, action: { token: \"delimiter.bracket\", next: \"@pop\" } },\n      { include: \"@expression\" }\n    ],\n    comment: [\n      { regex: `[^\\\\*]+`, action: { token: \"comment\" } },\n      { regex: `\\\\*\\\\/`, action: { token: \"comment\", next: \"@pop\" } },\n      { regex: `[\\\\/*]`, action: { token: \"comment\" } }\n    ],\n    whitespace: [\n      { regex: nonCommentWs },\n      { regex: `\\\\/\\\\*`, action: { token: \"comment\", next: \"@comment\" } },\n      { regex: `\\\\/\\\\/.*$`, action: { token: \"comment\" } }\n    ],\n    expression: [\n      { regex: `\"\"\"`, action: { token: \"string\", next: \"@stringVerbatim\" } },\n      { regex: `\"${notBefore(`\"\"`)}`, action: { token: \"string\", next: \"@stringLiteral\" } },\n      { regex: numericLiteral, action: { token: \"number\" } },\n      {\n        regex: identifier,\n        action: {\n          cases: {\n            \"@keywords\": { token: \"keyword\" },\n            \"@namedLiterals\": { token: \"keyword\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      },\n      { regex: `@${identifier}`, action: { token: \"tag\" } },\n      { regex: `#${directive}`, action: { token: \"directive\" } }\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/typespec/typespec.js\n"));

/***/ })

}]);