import "../chunk-XDFCUUT6.js";

// locales/bg-BG.json
var labels = {
  paste: "\u041F\u043E\u0441\u0442\u0430\u0432\u0438",
  pasteAsPlaintext: "\u041F\u043E\u0441\u0442\u0430\u0432\u0438 \u043A\u0430\u0442\u043E \u043E\u0431\u0438\u043A\u043D\u043E\u0432\u0435\u043D \u0442\u0435\u043A\u0441\u0442",
  pasteCharts: "\u041F\u043E\u0441\u0442\u0430\u0432\u0438 \u0433\u0440\u0430\u0444\u0438\u043A\u0438",
  selectAll: "\u041C\u0430\u0440\u043A\u0438\u0440\u0430\u0439 \u0432\u0441\u0438\u0447\u043A\u043E",
  multiSelect: "\u0414\u043E\u0431\u0430\u0432\u0438 \u0435\u043B\u0435\u043C\u0435\u043D\u0442 \u043A\u044A\u043C \u0441\u0435\u043B\u0435\u043A\u0446\u0438\u044F",
  moveCanvas: "\u041F\u0440\u0435\u043C\u0435\u0441\u0442\u0438 \u043F\u043B\u0430\u0442\u043D\u043E",
  cut: "\u0418\u0437\u0440\u0435\u0436\u0438",
  copy: "\u041A\u043E\u043F\u0438\u0440\u0430\u0439",
  copyAsPng: "\u041A\u043E\u043F\u0438\u0440\u0430\u043D\u0435 \u0432 \u043A\u043B\u0438\u043F\u0431\u043E\u0440\u0434\u0430",
  copyAsSvg: "\u041A\u043E\u043F\u0438\u0440\u0430\u043D\u043E \u0432 \u043A\u043B\u0438\u043F\u0431\u043E\u0440\u0434\u0430 \u043A\u0430\u0442\u043E SVG",
  copyText: "",
  copySource: "",
  convertToCode: "",
  bringForward: "\u041F\u0440\u0435\u043C\u0435\u0441\u0442\u0432\u0430\u043D\u0435 \u043D\u0430\u043F\u0440\u0435\u0434",
  sendToBack: "\u0418\u0437\u043D\u0430\u0441\u044F\u043D\u0435 \u043D\u0430\u0437\u0430\u0434",
  bringToFront: "\u0418\u0437\u043D\u0430\u0441\u044F\u043D\u0435 \u043E\u0442\u043F\u0440\u0435\u0434",
  sendBackward: "\u0418\u0437\u043F\u0440\u0430\u0442\u0438 \u043E\u0442\u0437\u0430\u0434",
  delete: "\u0418\u0437\u0442\u0440\u0438\u0439",
  copyStyles: "\u041A\u043E\u043F\u0438\u0440\u0430\u0439\u0442\u0435 \u0441\u0442\u0438\u043B\u043E\u0432\u0435",
  pasteStyles: "\u041F\u043E\u0441\u0442\u0430\u0432\u0438 \u0441\u0442\u0438\u043B\u043E\u0432\u0435",
  stroke: "\u0429\u0440\u0438\u0445",
  background: "\u0424\u043E\u043D",
  fill: "\u041D\u0430\u0441\u0438\u0442\u0435\u043D\u043E\u0441\u0442",
  strokeWidth: "\u0428\u0438\u0440\u0438\u043D\u0430 \u043D\u0430 \u0449\u0440\u0438\u0445\u0430",
  strokeStyle: "\u0421\u0442\u0438\u043B \u043D\u0430 \u043B\u0438\u043D\u0438\u044F",
  strokeStyle_solid: "\u041F\u043B\u044A\u0442\u0435\u043D",
  strokeStyle_dashed: "\u041F\u0443\u043D\u043A\u0442\u0438\u0440",
  strokeStyle_dotted: "\u041F\u0443\u043D\u043A\u0442\u0438\u0440\u0430\u043D\u043E",
  sloppiness: "\u041D\u0435\u0431\u0440\u0435\u0436\u043D\u043E\u0441\u0442",
  opacity: "\u041F\u0440\u043E\u0437\u0440\u0430\u0447\u043D\u043E\u0441\u0442",
  textAlign: "\u041F\u043E\u0434\u0440\u0430\u0432\u043D\u044F\u0432\u0430\u043D\u0435 \u043D\u0430 \u0442\u0435\u043A\u0441\u0442\u0430",
  edges: "\u041A\u0440\u0430\u0439\u0449\u0430",
  sharp: "\u041E\u0441\u0442\u044A\u0440",
  round: "\u0417\u0430\u043A\u0440\u044A\u0433\u043B\u0435\u043D\u043E",
  arrowheads: "\u0421\u0442\u0440\u0435\u043B\u043A\u0438",
  arrowhead_none: "\u0411\u0435\u0437",
  arrowhead_arrow: "\u0421\u0442\u0440\u0435\u043B\u043A\u0430",
  arrowhead_bar: "\u0412\u0440\u044A\u0445 \u043D\u0430 \u0441\u0442\u0440\u0435\u043B\u043A\u0430\u0442\u0430",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "\u0422\u0440\u0438\u044A\u0433\u044A\u043B\u043D\u0438\u043A",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "\u0420\u0430\u0437\u043C\u0435\u0440 \u043D\u0430 \u0448\u0440\u0438\u0444\u0442\u0430",
  fontFamily: "\u0421\u0435\u043C\u0435\u0439\u0441\u0442\u0432\u043E \u0448\u0440\u0438\u0444\u0442\u043E\u0432\u0435",
  addWatermark: '\u0414\u043E\u0431\u0430\u0432\u0438 "\u041D\u0430\u043F\u0440\u0430\u0432\u0435\u043D\u043E \u0441 Excalidraw"',
  handDrawn: "\u041D\u0430\u0440\u0438\u0441\u0443\u0432\u0430\u043D\u043E \u043D\u0430 \u0440\u044A\u043A\u0430",
  normal: "\u041D\u043E\u0440\u043C\u0430\u043B\u0435\u043D",
  code: "\u041A\u043E\u0434",
  small: "\u041C\u0430\u043B\u044A\u043A",
  medium: "\u0421\u0440\u0435\u0434\u0435\u043D",
  large: "\u0413\u043E\u043B\u044F\u043C",
  veryLarge: "\u041C\u043D\u043E\u0433\u043E \u0433\u043E\u043B\u044F\u043C",
  solid: "\u0421\u043E\u043B\u0438\u0434\u0435\u043D",
  hachure: "\u0425\u0435\u0440\u0430\u043B\u0434\u0438\u043A\u0430",
  zigzag: "\u0417\u0438\u0433\u0437\u0430\u0433",
  crossHatch: "\u0414\u0432\u043E\u0439\u043D\u043E-\u043F\u0440\u0435\u0441\u0435\u0447\u0435\u043D\u043E",
  thin: "\u0422\u044A\u043D\u044A\u043A",
  bold: "\u042F\u0441\u043D\u043E \u043E\u0447\u0435\u0440\u0442\u0430\u043D",
  left: "\u041B\u044F\u0432\u043E",
  center: "\u0426\u0435\u043D\u0442\u044A\u0440",
  right: "\u0414\u044F\u0441\u043D\u043E",
  extraBold: "\u041C\u043D\u043E\u0433\u043E \u044F\u0441\u043D\u043E \u043E\u0447\u0435\u0440\u0442\u0430\u043D",
  architect: "\u0410\u0440\u0445\u0438\u0442\u0435\u043A\u0442",
  artist: "\u0425\u0443\u0434\u043E\u0436\u043D\u0438\u043A",
  cartoonist: "\u041A\u0430\u0440\u0438\u043A\u0430\u0442\u0443\u0440\u0438\u0441\u0442",
  fileTitle: "\u0418\u043C\u0435 \u043D\u0430 \u0444\u0430\u0439\u043B",
  colorPicker: "\u0418\u0437\u0431\u043E\u0440 \u043D\u0430 \u0446\u0432\u044F\u0442",
  canvasColors: "\u0418\u0437\u043F\u043E\u043B\u0437\u0432\u0430\u043D \u043D\u0430 \u043F\u043B\u0430\u0442\u043D\u043E",
  canvasBackground: "\u0424\u043E\u043D \u043D\u0430 \u043F\u043B\u0430\u0442\u043D\u043E",
  drawingCanvas: "\u041F\u043B\u0430\u0442\u043D\u043E \u0437\u0430 \u0440\u0438\u0441\u0443\u0432\u0430\u043D\u0435",
  layers: "\u0421\u043B\u043E\u0435\u0432\u0435",
  actions: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
  language: "\u0415\u0437\u0438\u043A",
  liveCollaboration: "",
  duplicateSelection: "\u0414\u0443\u0431\u043B\u0438\u0440\u0430\u0439",
  untitled: "\u041D\u0435\u043E\u0437\u0430\u0433\u043B\u0430\u0432\u0435\u043D\u043E",
  name: "\u0418\u043C\u0435",
  yourName: "\u0412\u0430\u0448\u0435\u0442\u043E \u0438\u043C\u0435",
  madeWithExcalidraw: "\u041D\u0430\u043F\u0440\u0430\u0432\u0435\u043D\u043E \u0441 Excalidraw",
  group: "\u0413\u0440\u0443\u043F\u0438\u0440\u0430\u0439 \u0441\u0435\u043B\u0435\u043A\u0446\u0438\u044F\u0442\u0430",
  ungroup: "\u0421\u043F\u0440\u0438 \u0433\u0440\u0443\u043F\u0438\u0440\u0430\u043D\u0435\u0442\u043E \u043D\u0430 \u0441\u0435\u043B\u0435\u043A\u0446\u0438\u044F\u0442\u0430",
  collaborators: "\u0421\u044A\u0442\u0440\u0443\u0434\u043D\u0438\u0446\u0438",
  showGrid: "\u041F\u043E\u043A\u0430\u0437\u0432\u0430\u043D\u0435 \u043D\u0430 \u043C\u0440\u0435\u0436\u0430",
  addToLibrary: "\u0414\u043E\u0431\u0430\u0432\u044F\u043D\u0435 \u043A\u044A\u043C \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430\u0442\u0430",
  removeFromLibrary: "\u041F\u0440\u0435\u043C\u0430\u0445\u0432\u0430\u043D\u0435 \u043E\u0442 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430\u0442\u0430",
  libraryLoadingMessage: "\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043D\u0435 \u043D\u0430 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430\u0442\u0430\u2026",
  libraries: "\u0420\u0430\u0437\u0433\u043B\u0435\u0436\u0434\u0430\u043D\u0435 \u043D\u0430 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438\u0442\u0435",
  loadingScene: "\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043D\u0435 \u043D\u0430 \u0441\u0446\u0435\u043D\u0430\u2026",
  align: "\u041F\u043E\u0434\u0440\u0430\u0432\u043D\u044F\u0432\u0430\u043D\u0435",
  alignTop: "\u041F\u043E\u0434\u0440\u0430\u0432\u043D\u044F\u0432\u0430\u043D\u0435 \u043E\u0442\u0433\u043E\u0440\u0435",
  alignBottom: "\u041F\u043E\u0434\u0440\u0430\u0432\u043D\u044F\u0432\u0430\u043D\u0435 \u043E\u0442\u0434\u043E\u043B\u0443",
  alignLeft: "\u041F\u043E\u0434\u0440\u0430\u0432\u043D\u044F\u0432\u0430\u043D\u0435 \u043E\u0442\u043B\u044F\u0432\u043E",
  alignRight: "\u041F\u043E\u0434\u0440\u0430\u0432\u043D\u044F\u0432\u0430\u043D\u0435 \u043E\u0442\u0434\u044F\u0441\u043D\u043E",
  centerVertically: "\u0426\u0435\u043D\u0442\u0440\u0438\u0440\u0430\u0439 \u0432\u0435\u0440\u0442\u0438\u043A\u0430\u043B\u043D\u043E",
  centerHorizontally: "\u0426\u0435\u043D\u0442\u0440\u0438\u0440\u0430\u0439 \u0445\u043E\u0440\u0438\u0437\u043E\u043D\u0442\u0430\u043B\u043D\u043E",
  distributeHorizontally: "\u0420\u0430\u0437\u043F\u0440\u0435\u0434\u0435\u043B\u0438 \u0445\u043E\u0440\u0438\u0437\u043E\u043D\u0442\u0430\u043B\u043D\u043E",
  distributeVertically: "\u0420\u0430\u0437\u043F\u0440\u0435\u0434\u0435\u043B\u0438 \u0432\u0435\u0440\u0442\u0438\u043A\u0430\u043B\u043D\u043E",
  flipHorizontal: "\u0425\u043E\u0440\u0438\u0437\u043E\u043D\u0442\u0430\u043B\u043D\u043E \u043E\u0431\u0440\u044A\u0449\u0430\u043D\u0435",
  flipVertical: "\u0412\u0435\u0440\u0442\u0438\u043A\u0430\u043B\u043D\u043E \u043E\u0431\u0440\u044A\u0449\u0430\u043D\u0435",
  viewMode: "\u0418\u0437\u0433\u043B\u0435\u0434",
  share: "\u0421\u043F\u043E\u0434\u0435\u043B\u0438",
  showStroke: "",
  showBackground: "",
  toggleTheme: "\u0412\u043A\u043B\u044E\u0447\u0438 \u0442\u0435\u043C\u0430",
  personalLib: "\u041B\u0438\u0447\u043D\u0430 \u0411\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430",
  excalidrawLib: "Excalidraw \u0411\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430",
  decreaseFontSize: "\u041D\u0430\u043C\u0430\u043B\u0438 \u0440\u0430\u0437\u043C\u0435\u0440\u0430 \u043D\u0430 \u0448\u0440\u0438\u0444\u0442\u0430",
  increaseFontSize: "\u0423\u0432\u0435\u043B\u0438\u0447\u0438 \u0440\u0430\u0437\u043C\u0435\u0440\u0430 \u043D\u0430 \u0448\u0440\u0438\u0444\u0442\u0430",
  unbindText: "",
  bindText: "",
  createContainerFromText: "",
  link: {
    edit: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u0430\u0439 \u043B\u0438\u043D\u043A",
    editEmbed: "",
    create: "",
    createEmbed: "",
    label: "\u041B\u0438\u043D\u043A",
    labelEmbed: "",
    empty: ""
  },
  lineEditor: {
    edit: "",
    exit: ""
  },
  elementLock: {
    lock: "\u0417\u0430\u043A\u043B\u044E\u0447\u0438",
    unlock: "\u041E\u0442\u043A\u043B\u044E\u0447\u0438",
    lockAll: "\u0417\u0430\u043A\u043B\u044E\u0447\u0438 \u0432\u0441\u0438\u0447\u043A\u0438",
    unlockAll: "\u041E\u0442\u043A\u043B\u044E\u0447\u0438 \u0432\u0441\u0438\u0447\u043A\u0438"
  },
  statusPublished: "\u041F\u0443\u0431\u043B\u0438\u043A\u0443\u0432\u0430\u043D\u0438",
  sidebarLock: "",
  selectAllElementsInFrame: "",
  removeAllElementsFromFrame: "",
  eyeDropper: "\u0418\u0437\u0431\u0435\u0440\u0438 \u0446\u0432\u044F\u0442 \u043E\u0442 \u043F\u043B\u0430\u0442\u043D\u043E\u0442\u043E",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "\u041D\u044F\u043C\u0430 \u0434\u043E\u0431\u0430\u0432\u0435\u043D\u0438 \u043D\u0435\u0449\u0430 \u0432\u0441\u0435 \u043E\u0449\u0435...",
  hint_emptyLibrary: "",
  hint_emptyPrivateLibrary: ""
};
var buttons = {
  clearReset: "\u041D\u0443\u043B\u0438\u0440\u0430\u043D\u0435 \u043D\u0430 \u043F\u043B\u0430\u0442\u043D\u043E",
  exportJSON: "",
  exportImage: "",
  export: "\u0417\u0430\u043F\u0430\u0437\u0438 \u043D\u0430...",
  copyToClipboard: "\u041A\u043E\u043F\u0438\u0440\u0430\u043D\u0435 \u0432 \u043A\u043B\u0438\u043F\u0431\u043E\u0440\u0434\u0430",
  save: "\u0417\u0430\u043F\u0430\u0437\u0438 \u043A\u044A\u043C \u0442\u0435\u043A\u0443\u0449 \u0444\u0430\u0439\u043B",
  saveAs: "\u0417\u0430\u043F\u0438\u0448\u0438 \u043A\u0430\u0442\u043E",
  load: "\u041E\u0442\u0432\u043E\u0440\u0438",
  getShareableLink: "\u041F\u043E\u043B\u0443\u0447\u0430\u0432\u0430\u043D\u0435 \u043D\u0430 \u0432\u0440\u044A\u0437\u043A\u0430 \u0437\u0430 \u0441\u043F\u043E\u0434\u0435\u043B\u044F\u043D\u0435",
  close: "\u0417\u0430\u0442\u0432\u043E\u0440\u0438",
  selectLanguage: "\u0418\u0437\u0431\u043E\u0440 \u043D\u0430 \u0435\u0437\u0438\u043A",
  scrollBackToContent: "\u041F\u0440\u0435\u0432\u044A\u0440\u0442\u0435\u0442\u0435 \u043E\u0431\u0440\u0430\u0442\u043D\u043E \u043A\u044A\u043C \u0441\u044A\u0434\u044A\u0440\u0436\u0430\u043D\u0438\u0435\u0442\u043E",
  zoomIn: "\u041F\u0440\u0438\u0431\u043B\u0438\u0436\u0430\u0432\u0430\u043D\u0435",
  zoomOut: "\u041E\u0442\u0434\u0430\u043B\u0435\u0447\u0430\u0432\u0430\u043D\u0435",
  resetZoom: "\u0421\u0442\u0430\u043D\u0434\u0430\u0440\u0442\u0435\u043D \u043C\u0430\u0449\u0430\u0431",
  menu: "\u041C\u0435\u043D\u044E",
  done: "\u0417\u0430\u0432\u044A\u0440\u0448\u0435\u043D\u043E",
  edit: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u0430\u043D\u0435",
  undo: "\u041E\u0442\u043C\u044F\u043D\u0430",
  redo: "\u041F\u043E\u0432\u0442\u043E\u0440\u0438",
  resetLibrary: "\u041D\u0443\u043B\u0438\u0440\u0430\u043D\u0435 \u043D\u0430 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430\u0442\u0430",
  createNewRoom: "\u0421\u044A\u0437\u0434\u0430\u0439 \u043D\u043E\u0432\u0430 \u0441\u0442\u0430\u044F",
  fullScreen: "\u041D\u0430 \u0446\u044F\u043B \u0435\u043A\u0440\u0430\u043D",
  darkMode: "\u0422\u044A\u043C\u0435\u043D \u0440\u0435\u0436\u0438\u043C",
  lightMode: "\u0421\u0432\u0435\u0442\u044A\u043B \u0440\u0435\u0436\u0438\u043C",
  zenMode: "\u0420\u0435\u0436\u0438\u043C Zen",
  objectsSnapMode: "",
  exitZenMode: "\u0421\u043F\u0438\u0440\u0430\u043D\u0435 \u043D\u0430 Zen \u0440\u0435\u0436\u0438\u043C",
  cancel: "\u041E\u0442\u043C\u0435\u043D\u0438",
  clear: "\u0418\u0437\u0447\u0438\u0441\u0442\u0438",
  remove: "\u041F\u0440\u0435\u043C\u0430\u0445\u0432\u0430\u043D\u0435",
  embed: "",
  publishLibrary: "\u041F\u0443\u0431\u043B\u0438\u043A\u0443\u0432\u0430\u0439",
  submit: "\u0418\u0437\u043F\u0440\u0430\u0442\u0438",
  confirm: "\u041F\u043E\u0442\u0432\u044A\u0440\u0436\u0434\u0430\u0432\u0430\u043D\u0435",
  embeddableInteractionButton: ""
};
var alerts = {
  clearReset: "\u0422\u043E\u0432\u0430 \u0449\u0435 \u0438\u0437\u0447\u0438\u0441\u0442\u0438 \u0446\u044F\u043B\u043E\u0442\u043E \u043F\u043B\u0430\u0442\u043D\u043E. \u0421\u0438\u0433\u0443\u0440\u043D\u0438 \u043B\u0438 \u0441\u0442\u0435?",
  couldNotCreateShareableLink: "\u0412\u0440\u044A\u0437\u043A\u0430\u0442\u0430 \u043D\u0435 \u043C\u043E\u0436\u0435 \u0434\u0430 \u0431\u044A\u0434\u0435 \u0441\u044A\u0437\u0434\u0430\u0434\u0435\u043D\u0430.",
  couldNotCreateShareableLinkTooBig: "\u041D\u0435 \u043C\u043E\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0441\u044A\u0437\u0434\u0430\u0434\u0435 \u0432\u0440\u044A\u0437\u043A\u0430 \u0437\u0430 \u0441\u043F\u043E\u0434\u0435\u043B\u044F\u043D\u0435: \u0441\u0446\u0435\u043D\u0430\u0442\u0430 \u0435 \u0442\u0432\u044A\u0440\u0434\u0435 \u0433\u043E\u043B\u044F\u043C\u0430",
  couldNotLoadInvalidFile: "\u041D\u0435\u0432\u0430\u043B\u0438\u0434\u0435\u043D \u0444\u0430\u0439\u043B \u043D\u0435 \u043C\u043E\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0437\u0430\u0440\u0435\u0434\u0438",
  importBackendFailed: "\u0418\u043C\u043F\u043E\u0440\u0442\u0438\u0440\u0430\u043D\u0435\u0442\u043E \u043E\u0442 \u0431\u0435\u043A\u0435\u043D\u0434 \u043D\u0435 \u0431\u0435\u0448\u0435 \u0443\u0441\u043F\u0435\u0448\u043D\u043E.",
  cannotExportEmptyCanvas: "\u041D\u0435 \u043C\u043E\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0435\u043A\u0441\u043F\u043E\u0440\u0442\u0438\u0440\u0430 \u043F\u0440\u0430\u0437\u043D\u043E \u043F\u043B\u0430\u0442\u043D\u043E.",
  couldNotCopyToClipboard: "\u041D\u0435 \u043C\u043E\u0436\u0435\u043C \u0434\u0430 \u043A\u043E\u043F\u0438\u0440\u0430\u043C\u0435 \u0432 \u043A\u043B\u0438\u043F\u0431\u043E\u0430\u0440\u0434\u0430.",
  decryptFailed: "\u0414\u0430\u043D\u043D\u0438\u0442\u0435 \u043D\u0435 \u043C\u043E\u0436\u0430\u0445\u0430 \u0434\u0430 \u0441\u0435 \u0434\u0435\u0448\u0438\u0444\u0440\u0438\u0440\u0430\u0442.",
  uploadedSecurly: "\u041A\u0430\u0447\u0432\u0430\u043D\u0435\u0442\u043E \u0435 \u0437\u0430\u0449\u0438\u0442\u0435\u043D\u043E \u0441 \u043A\u0440\u0438\u043F\u0442\u0438\u0440\u0430\u043D\u0435 \u043E\u0442 \u043A\u0440\u0430\u0439 \u0434\u043E \u043A\u0440\u0430\u0439, \u043A\u043E\u0435\u0442\u043E \u043E\u0437\u043D\u0430\u0447\u0430\u0432\u0430, \u0447\u0435 \u0441\u044A\u0440\u0432\u044A\u0440\u044A\u0442 Excalidraw \u0438 \u0442\u0440\u0435\u0442\u0438 \u0441\u0442\u0440\u0430\u043D\u0438 \u043D\u0435 \u043C\u043E\u0433\u0430\u0442 \u0434\u0430 \u0447\u0435\u0442\u0430\u0442 \u0441\u044A\u0434\u044A\u0440\u0436\u0430\u043D\u0438\u0435\u0442\u043E.",
  loadSceneOverridePrompt: "\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043D\u0435\u0442\u043E \u043D\u0430 \u0432\u044A\u043D\u0448\u043D\u0430 \u0440\u0438\u0441\u0443\u043D\u043A\u0430 \u0449\u0435 \u043F\u0440\u0435\u0437\u0430\u043F\u0438\u0448\u0435 \u043D\u0430\u0441\u0442\u043E\u044F\u0449\u043E\u0442\u043E \u0432\u0438 \u0441\u044A\u0434\u044A\u0440\u0436\u0430\u043D\u0438\u0435. \u0416\u0435\u043B\u0430\u0435\u0442\u0435 \u043B\u0438 \u0434\u0430 \u043F\u0440\u043E\u0434\u044A\u043B\u0436\u0438\u0442\u0435?",
  collabStopOverridePrompt: "\u041F\u0440\u0435\u043A\u0440\u0430\u0442\u044F\u0432\u0430\u043D\u0435\u0442\u043E \u043D\u0430 \u0441\u0435\u0441\u0438\u044F\u0442\u0430 \u0449\u0435 \u043F\u0440\u0435\u0437\u0430\u043F\u0438\u0448\u0435 \u043F\u0440\u0435\u0434\u0438\u0448\u043D\u0430\u0442\u0430, \u043B\u043E\u043A\u0430\u043B\u043D\u043E \u0437\u0430\u043F\u0430\u0437\u0435\u043D\u0430, \u0440\u0438\u0441\u0443\u043D\u043A\u0430. \u0421\u0438\u0433\u0443\u0440\u043D\u0438 \u043B\u0438 \u0441\u0442\u0435?\n\n(\u0410\u043A\u043E \u0438\u0441\u043A\u0430\u0442\u0435 \u0434\u0430 \u043F\u0440\u043E\u0434\u044A\u043B\u0436\u0438\u0442\u0435 \u0441 \u043B\u043E\u043A\u0430\u043B\u043D\u0430\u0442\u0430 \u0440\u0438\u0441\u0443\u043D\u043A\u0430, \u043F\u0440\u043E\u0441\u0442\u043E \u0437\u0430\u0442\u0432\u043E\u0440\u0435\u0442\u0435 \u0442\u0430\u0431\u0430 \u043D\u0430 \u0431\u0440\u0430\u0443\u0437\u044A\u0440\u0430.)",
  errorAddingToLibrary: "\u041D\u0435 \u043C\u043E\u0436\u0435\u043C \u0434\u0430 \u0437\u0430\u0440\u0435\u0434\u0438\u043C \u043E\u0442 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430\u0442\u0430",
  errorRemovingFromLibrary: "\u041D\u0435 \u043C\u043E\u0436\u0435\u043C \u0434\u0430 \u043F\u0440\u0435\u043C\u0430\u0445\u043D\u0435\u043C \u0435\u043B\u0435\u043C\u0435\u043D\u0442 \u043E\u0442 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430\u0442\u0430",
  confirmAddLibrary: "\u0429\u0435 \u0441\u0435 \u0434\u043E\u0431\u0430\u0432\u044F\u0442 {{numShapes}} \u0444\u0438\u0433\u0443\u0440\u0430(\u0438) \u0432\u044A\u0432 \u0432\u0430\u0448\u0430\u0442\u0430 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430. \u0421\u0438\u0433\u0443\u0440\u043D\u0438 \u043B\u0438 \u0441\u0442\u0435?",
  imageDoesNotContainScene: "",
  cannotRestoreFromImage: "\u041D\u0435 \u043C\u043E\u0436\u0435 \u0434\u0430 \u0431\u044A\u0434\u0435 \u0432\u044A\u0437\u0441\u0442\u0430\u043D\u043E\u0432\u0435\u043D\u0430 \u0441\u0446\u0435\u043D\u0430 \u043E\u0442 \u0442\u043E\u0437\u0438 \u0444\u0430\u0439\u043B",
  invalidSceneUrl: "",
  resetLibrary: "",
  removeItemsFromsLibrary: "\u0418\u0437\u0442\u0440\u0438\u0439 {{count}} \u0435\u043B\u0435\u043C\u0435\u043D\u0442(\u0430) \u043E\u0442 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430\u0442\u0430?",
  invalidEncryptionKey: "",
  collabOfflineWarning: ""
};
var errors = {
  unsupportedFileType: "\u0422\u043E\u0437\u0438 \u0444\u0430\u0439\u043B\u043E\u0432 \u0444\u043E\u0440\u043C\u0430\u0442 \u043D\u0435 \u0441\u0435 \u043F\u043E\u0434\u0434\u044A\u0440\u0436\u0430.",
  imageInsertError: "",
  fileTooBig: "\u0424\u0430\u0439\u043B\u044A\u0442 \u0435 \u0442\u0432\u044A\u0440\u0434\u0435 \u0433\u043E\u043B\u044F\u043C. \u041C\u0430\u043A\u0441\u0438\u043C\u0430\u043B\u043D\u0438\u044F \u0434\u043E\u043F\u0443\u0441\u0442\u0438\u043C \u0440\u0430\u0437\u043C\u0435\u0440 \u0435 {{maxSize}}.",
  svgImageInsertError: "",
  failedToFetchImage: "",
  invalidSVGString: "\u041D\u0435\u0432\u0430\u043B\u0438\u0434\u0435\u043D SVG.",
  cannotResolveCollabServer: "",
  importLibraryError: "\u041D\u0435 \u043C\u043E\u0436\u0435\u043C \u0434\u0430 \u0437\u0430\u0440\u0435\u0434\u0438\u043C \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430\u0442\u0430",
  collabSaveFailed: "",
  collabSaveFailed_sizeExceeded: "",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "",
    line2: "",
    line3: "\u0421\u0438\u043B\u043D\u043E \u043F\u0440\u0435\u043F\u043E\u0440\u044A\u0447\u0432\u0430\u043C\u0435 \u0434\u0430 \u0438\u0437\u043A\u043B\u044E\u0447\u0438\u0442\u0435 \u0442\u0430\u0437\u0438 \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0430. \u041C\u043E\u0436\u0435\u0442\u0435 \u0434\u0430 \u0441\u043B\u0435\u0434\u0432\u0430\u0442\u0435 <link>\u0442\u0435\u0437\u0438 \u0441\u0442\u044A\u043F\u043A\u0438</link> \u0437\u0430 \u0442\u043E\u0432\u0430 \u043A\u0430\u043A \u0434\u0430 \u0433\u043E \u043D\u0430\u043F\u0440\u0430\u0432\u0438\u0442\u0435.",
    line4: ""
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "",
    image: ""
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "\u0421\u0435\u043B\u0435\u043A\u0446\u0438\u044F",
  image: "\u0412\u043C\u044A\u043A\u0432\u0430\u043D\u0435 \u043D\u0430 \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435",
  rectangle: "\u041F\u0440\u0430\u0432\u043E\u044A\u0433\u044A\u043B\u043D\u0438\u043A",
  diamond: "\u0414\u0438\u0430\u043C\u0430\u043D\u0442",
  ellipse: "\u0415\u043B\u0438\u043F\u0441",
  arrow: "\u0421\u0442\u0440\u0435\u043B\u043A\u0430",
  line: "\u041B\u0438\u043D\u0438\u044F",
  freedraw: "\u0420\u0438\u0441\u0443\u0432\u0430\u043D\u0435",
  text: "\u0422\u0435\u043A\u0441\u0442",
  library: "\u0411\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430",
  lock: "\u041F\u043E\u0434\u0434\u044A\u0440\u0436\u0430\u0439\u0442\u0435 \u0438\u0437\u0431\u0440\u0430\u043D\u0438\u044F \u0438\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442 \u0430\u043A\u0442\u0438\u0432\u0435\u043D \u0441\u043B\u0435\u0434 \u0440\u0438\u0441\u0443\u0432\u0430\u043D\u0435",
  penMode: "",
  link: "",
  eraser: "\u0413\u0443\u043C\u0430",
  frame: "",
  magicframe: "",
  embeddable: "",
  laser: "",
  hand: "",
  extraTools: "\u041E\u0449\u0435 \u0438\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442\u0438",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F \u043F\u043E \u043F\u043B\u0430\u0442\u043D\u043E\u0442\u043E",
  selectedShapeActions: "\u0418\u0437\u0431\u0440\u0430\u043D\u0438 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
  shapes: "\u0424\u0438\u0433\u0443\u0440\u0438"
};
var hints = {
  canvasPanning: "",
  linearElement: "\u041A\u043B\u0438\u043A\u043D\u0435\u0442\u0435, \u0437\u0430 \u0434\u0430 \u0441\u0442\u0430\u0440\u0442\u0438\u0440\u0430\u0442\u0435 \u043D\u044F\u043A\u043E\u043B\u043A\u043E \u0442\u043E\u0447\u043A\u0438, \u043F\u043B\u044A\u0437\u043D\u0435\u0442\u0435 \u0437\u0430 \u0435\u0434\u043D\u0430 \u043B\u0438\u043D\u0438\u044F",
  freeDraw: "\u041D\u0430\u0442\u0438\u0441\u043D\u0435\u0442\u0435 \u0438 \u0432\u043B\u0430\u0447\u0435\u0442\u0435, \u043F\u0443\u0441\u043D\u0435\u0442\u0435 \u043A\u0430\u0442\u043E \u0441\u0442\u0435 \u0433\u043E\u0442\u043E\u0432\u0438",
  text: "\u041F\u043E\u0434\u0441\u043A\u0430\u0437\u043A\u0430: \u041C\u043E\u0436\u0435\u0442\u0435 \u0441\u044A\u0449\u043E \u0434\u0430 \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u0435 \u0442\u0435\u043A\u0441\u0442 \u043A\u0430\u0442\u043E \u043D\u0430\u0442\u0438\u0441\u043D\u0435\u0442\u0435 \u043D\u044F\u043A\u044A\u0434\u0435 \u0434\u0432\u0430 \u043F\u044A\u0442 \u0441 \u0438\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442\u0430 \u0437\u0430 \u0441\u0435\u043B\u0435\u043A\u0446\u0438\u044F",
  embeddable: "",
  text_selected: "",
  text_editing: "",
  linearElementMulti: "\u041A\u043B\u0438\u043A\u043D\u0435\u0442\u0435 \u0432\u044A\u0440\u0445\u0443 \u043F\u043E\u0441\u043B\u0435\u0434\u043D\u0430\u0442\u0430 \u0442\u043E\u0447\u043A\u0430 \u0438\u043B\u0438 \u043D\u0430\u0442\u0438\u0441\u043D\u0435\u0442\u0435 Escape \u0438\u043B\u0438 Enter, \u0437\u0430 \u0434\u0430 \u0437\u0430\u0432\u044A\u0440\u0448\u0438\u0442\u0435",
  lockAngle: "\u041C\u043E\u0436\u0435\u0442\u0435 \u0434\u0430 \u043E\u0433\u0440\u0430\u043D\u0438\u0447\u0438\u0442\u0435 \u044A\u0433\u044A\u043B\u0430, \u043A\u0430\u0442\u043E \u0437\u0430\u0434\u044A\u0440\u0436\u0438\u0442\u0435 SHIFT",
  resize: "\u041C\u043E\u0436\u0435 \u0434\u0430 \u043E\u0433\u0440\u0430\u043D\u0438\u0447\u0438\u0442\u0435 \u043F\u0440\u0438 \u043F\u0440\u0435\u043E\u0440\u0430\u0437\u043C\u0435\u0440\u044F\u0432\u0430\u043D\u0435 \u043A\u0430\u0442\u043E \u0437\u0430\u0434\u044A\u0440\u0436\u0438\u0442\u0435 SHIFT,\n\u0437\u0430\u0434\u0440\u044A\u0436\u0442\u0435 ALT \u0437\u0430 \u043F\u0440\u0435\u043E\u0440\u0430\u0437\u043C\u0435\u0440\u0438\u0442\u0435 \u043F\u0440\u0435\u0437 \u0446\u0435\u043D\u0442\u044A\u0440\u0430",
  resizeImage: "",
  rotate: "\u041C\u043E\u0436\u0435\u0442\u0435 \u0434\u0430 \u043E\u0433\u0440\u0430\u043D\u0438\u0447\u0438\u0442\u0435 \u044A\u0433\u043B\u0438\u0442\u0435, \u043A\u0430\u0442\u043E \u0434\u044A\u0440\u0436\u0438\u0442\u0435 SHIFT, \u0434\u043E\u043A\u0430\u0442\u043E \u0441\u0435 \u0432\u044A\u0440\u0442\u0438\u0442\u0435",
  lineEditor_info: "",
  lineEditor_pointSelected: "\u041D\u0430\u0442\u0438\u0441\u043D\u0435\u0442\u0435 Delete \u0437\u0430 \u0434\u0430 \u0438\u0437\u0442\u0440\u0438\u0435\u0442\u0435 \u0442\u043E\u0447\u043A\u0430(\u0438), CtrlOrCmd+D \u0437\u0430 \u0434\u0443\u043F\u043B\u0438\u0446\u0438\u0440\u0430\u043D\u0435, \u0438\u043B\u0438 \u0438\u0437\u0432\u043B\u0430\u0447\u0435\u0442\u0435 \u0437\u0430 \u0434\u0430 \u043F\u0440\u0435\u043C\u0435\u0441\u0442\u0438\u0442\u0435",
  lineEditor_nothingSelected: "",
  placeImage: "",
  publishLibrary: "",
  bindTextToElement: "\u041D\u0430\u0442\u0438\u0441\u043D\u0435\u0442\u0435 Enter, \u0437\u0430 \u0434\u0430 \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u0435",
  deepBoxSelect: "",
  eraserRevert: "",
  firefox_clipboard_write: "",
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "\u041D\u0435\u0432\u044A\u0437\u043C\u043E\u0436\u043D\u043E\u0441\u0442 \u0437\u0430 \u043F\u043E\u043A\u0430\u0437\u0432\u0430\u043D\u0435 \u043D\u0430 preview",
  canvasTooBig: "\u041F\u043B\u0430\u0442\u043D\u043E\u0442\u043E \u0435 \u0442\u0432\u044A\u0440\u0434\u0435 \u0433\u043E\u043B\u044F\u043C\u043E.",
  canvasTooBigTip: "\u041F\u043E\u0434\u0441\u043A\u0430\u0437\u043A\u0430: \u043F\u0440\u043E\u0431\u0432\u0430\u0439\u0442\u0435 \u0434\u0430 \u043F\u0440\u0438\u0431\u043B\u0438\u0436\u0438\u0442\u0435 \u0434\u0430\u043B\u0435\u0447\u043D\u0438\u0442\u0435 \u0435\u043B\u0435\u043C\u0435\u043D\u0442\u0438 \u043F\u043E-\u0431\u043B\u0438\u0437\u043A\u043E."
};
var errorSplash = {
  headingMain: "\u0421\u0440\u0435\u0449\u0430 \u0433\u0440\u0435\u0448\u043A\u0430. \u041E\u043F\u0438\u0442\u0430\u0439\u0442\u0435 <button>\u043F\u0440\u0435\u0437\u0430\u0440\u0435\u0436\u0434\u0430\u043D\u0435 \u043D\u0430 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430\u0442\u0430.</button>",
  clearCanvasMessage: "\u0410\u043A\u043E \u043F\u0440\u0435\u0437\u0430\u0440\u0435\u0436\u0434\u0430\u043D\u0435\u0442\u043E \u043D\u0435 \u0440\u0430\u0431\u043E\u0442\u0438, \u043E\u043F\u0438\u0442\u0430\u0439\u0442\u0435 <button>\u0438\u0437\u0447\u0438\u0441\u0442\u0432\u0430\u043D\u0435 \u043D\u0430 \u043F\u043B\u0430\u0442\u043D\u043E\u0442\u043E.</button>",
  clearCanvasCaveat: " \u0422\u043E\u0432\u0430 \u0449\u0435 \u0434\u043E\u0432\u0435\u0434\u0435 \u0434\u043E \u0437\u0430\u0433\u0443\u0431\u0430 \u043D\u0430 \u0440\u0430\u0431\u043E\u0442\u0430 ",
  trackedToSentry: "\u0413\u0440\u0435\u0448\u043A\u0430\u0442\u0430 \u0441 \u0438\u0434\u0435\u043D\u0442\u0438\u0444\u0438\u043A\u0430\u0442\u043E\u0440 {{eventId}} \u0431\u0435\u0448\u0435 \u043F\u0440\u043E\u0441\u043B\u0435\u0434\u0435\u043D \u0432 \u043D\u0430\u0448\u0430\u0442\u0430 \u0441\u0438\u0441\u0442\u0435\u043C\u0430.",
  openIssueMessage: "\u0411\u044F\u0445\u043C\u0435 \u043C\u043D\u043E\u0433\u043E \u043F\u0440\u0435\u0434\u043F\u0430\u0437\u043B\u0438\u0432\u0438 \u0434\u0430 \u043D\u0435 \u0432\u043A\u043B\u044E\u0447\u0438\u0442\u0435 \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F\u0442\u0430 \u0437\u0430 \u0432\u0430\u0448\u0430\u0442\u0430 \u0441\u0446\u0435\u043D\u0430 \u043F\u0440\u0438 \u0433\u0440\u0435\u0448\u043A\u0430\u0442\u0430. \u0410\u043A\u043E \u0441\u0446\u0435\u043D\u0430\u0442\u0430 \u0432\u0438 \u043D\u0435 \u0435 \u0447\u0430\u0441\u0442\u043D\u0430, \u043C\u043E\u043B\u044F, \u043F\u043E\u043C\u0438\u0441\u043B\u0435\u0442\u0435 \u0437\u0430 \u043F\u043E\u0441\u043B\u0435\u0434\u0432\u0430\u0449\u0438 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u044F \u043D\u0430 \u043D\u0430\u0448\u0430\u0442\u0430 <button>\u0442\u0440\u0430\u043A\u0435\u0440 \u0437\u0430 \u0433\u0440\u0435\u0448\u043A\u0438.</button> \u041C\u043E\u043B\u044F, \u0432\u043A\u043B\u044E\u0447\u0435\u0442\u0435 \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043F\u043E-\u0434\u043E\u043B\u0443, \u043A\u0430\u0442\u043E \u044F \u043A\u043E\u043F\u0438\u0440\u0430\u0442\u0435 \u0438 \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u0435 \u0432 GitHub.",
  sceneContent: "\u0421\u044A\u0434\u044A\u0440\u0436\u0430\u043D\u0438\u0435 \u043D\u0430 \u0441\u0446\u0435\u043D\u0430\u0442\u0430:"
};
var roomDialog = {
  desc_intro: "\u041C\u043E\u0436\u0435\u0442\u0435 \u0434\u0430 \u043F\u043E\u043A\u0430\u043D\u0438\u0442\u0435 \u0445\u043E\u0440\u0430 \u043D\u0430 \u0442\u0435\u043A\u0443\u0449\u0430\u0442\u0430 \u0441\u0438 \u0441\u0446\u0435\u043D\u0430 \u0434\u0430 \u0441\u0438 \u0441\u044A\u0442\u0440\u0443\u0434\u043D\u0438\u0447\u0430\u0442 \u0441 \u0432\u0430\u0441.",
  desc_privacy: "\u041D\u0435 \u0441\u0435 \u043F\u0440\u0438\u0442\u0435\u0441\u043D\u044F\u0432\u0430\u0439\u0442\u0435, \u0441\u0435\u0441\u0438\u044F\u0442\u0430 \u0438\u0437\u043F\u043E\u043B\u0437\u0432\u0430 \u043A\u0440\u0438\u043F\u0442\u0438\u0440\u0430\u043D\u0435 \u043E\u0442 \u043A\u0440\u0430\u0439 \u0434\u043E \u043A\u0440\u0430\u0439, \u0442\u0430\u043A\u0430 \u0447\u0435 \u043A\u0430\u043A\u0432\u043E\u0442\u043E \u043D\u0430\u0440\u0438\u0441\u0443\u0432\u0430\u0442\u0435 \u0449\u0435 \u043E\u0441\u0442\u0430\u043D\u0435 \u0447\u0430\u0441\u0442\u043D\u043E. \u0414\u043E\u0440\u0438 \u043D\u0430\u0448\u0438\u044F\u0442 \u0441\u044A\u0440\u0432\u044A\u0440 \u043D\u044F\u043C\u0430 \u0434\u0430 \u043C\u043E\u0436\u0435 \u0434\u0430 \u0432\u0438\u0434\u0438 \u043A\u0430\u043A\u0432\u043E \u043F\u0440\u0435\u0434\u043B\u0430\u0433\u0430\u0442\u0435.",
  button_startSession: "\u0421\u0442\u0430\u0440\u0442\u0438\u0440\u0430\u0439\u0442\u0435 \u0441\u0435\u0441\u0438\u044F\u0442\u0430",
  button_stopSession: "\u0421\u0442\u043E\u043F \u043D\u0430 \u0441\u0435\u0441\u0438\u044F\u0442\u0430",
  desc_inProgressIntro: "\u0421\u0435\u0441\u0438\u044F\u0442\u0430 \u0437\u0430 \u0441\u044A\u0442\u0440\u0443\u0434\u043D\u0438\u0447\u0435\u0441\u0442\u0432\u043E \u043D\u0430 \u0436\u0438\u0432\u043E \u0435 \u0432 \u0445\u043E\u0434.",
  desc_shareLink: "\u0421\u043F\u043E\u0434\u0435\u043B\u0435\u0442\u0435 \u0442\u0430\u0437\u0438 \u0432\u0440\u044A\u0437\u043A\u0430 \u0441 \u0432\u0441\u0435\u043A\u0438, \u0441 \u043A\u043E\u0433\u043E\u0442\u043E \u0438\u0441\u043A\u0430\u0442\u0435 \u0434\u0430 \u0441\u0438 \u0441\u044A\u0442\u0440\u0443\u0434\u043D\u0438\u0447\u0438\u0442\u0435:",
  desc_exitSession: "\u0421\u043F\u0438\u0440\u0430\u043D\u0435\u0442\u043E \u043D\u0430 \u0441\u0435\u0441\u0438\u044F\u0442\u0430 \u0449\u0435 \u0432\u0438 \u0438\u0437\u043A\u043B\u044E\u0447\u0438 \u043E\u0442 \u0441\u0442\u0430\u044F\u0442\u0430, \u043D\u043E \u0449\u0435 \u043C\u043E\u0436\u0435\u0442\u0435 \u0434\u0430 \u043F\u0440\u043E\u0434\u044A\u043B\u0436\u0438\u0442\u0435 \u0434\u0430 \u0440\u0430\u0431\u043E\u0442\u0438\u0442\u0435 \u0441\u044A\u0441 \u0441\u0446\u0435\u043D\u0430\u0442\u0430, \u043B\u043E\u043A\u0430\u043B\u043D\u043E. \u0418\u043C\u0430\u0439\u0442\u0435 \u043F\u0440\u0435\u0434\u0432\u0438\u0434, \u0447\u0435 \u0442\u043E\u0432\u0430 \u043D\u044F\u043C\u0430 \u0434\u0430 \u0437\u0430\u0441\u0435\u0433\u043D\u0435 \u0434\u0440\u0443\u0433\u0438 \u0445\u043E\u0440\u0430 \u0438 \u0442\u0435 \u0432\u0441\u0435 \u043E\u0449\u0435 \u0449\u0435 \u043C\u043E\u0433\u0430\u0442 \u0434\u0430 \u0441\u0438 \u0441\u044A\u0442\u0440\u0443\u0434\u043D\u0438\u0447\u0430\u0442 \u0441 \u0442\u044F\u0445\u043D\u0430\u0442\u0430 \u0432\u0435\u0440\u0441\u0438\u044F.",
  shareTitle: ""
};
var errorDialog = {
  title: "\u0413\u0440\u0435\u0448\u043A\u0430"
};
var exportDialog = {
  disk_title: "",
  disk_details: "",
  disk_button: "",
  link_title: "",
  link_details: "",
  link_button: "",
  excalidrawplus_description: "",
  excalidrawplus_button: "\u0415\u043A\u0441\u043F\u043E\u0440\u0442",
  excalidrawplus_exportError: ""
};
var helpDialog = {
  blog: "\u041F\u0440\u043E\u0447\u0435\u0442\u0435\u0442\u0435 \u043D\u0430\u0448\u0438\u044F \u0431\u043B\u043E\u0433",
  click: "\u043A\u043B\u0438\u043A",
  deepSelect: "",
  deepBoxSelect: "",
  curvedArrow: "\u0418\u0437\u0432\u0438\u0442\u0430 \u0441\u0442\u0440\u0435\u043B\u043A\u0430",
  curvedLine: "\u0418\u0437\u0432\u0438\u0442\u0430 \u043B\u0438\u043D\u0438\u044F",
  documentation: "\u0414\u043E\u043A\u0443\u043C\u0435\u043D\u0442\u0430\u0446\u0438\u044F",
  doubleClick: "\u0434\u0432\u043E\u0439\u043D\u043E-\u0449\u0440\u0430\u043A\u0432\u0430\u043D\u0435",
  drag: "\u043F\u043B\u044A\u0437\u043D\u0435\u0442\u0435",
  editor: "\u0420\u0435\u0434\u0430\u043A\u0442\u043E\u0440",
  editLineArrowPoints: "",
  editText: "",
  github: "\u041D\u0430\u043C\u0435\u0440\u0438\u0445\u0442\u0435 \u043F\u0440\u043E\u0431\u043B\u0435\u043C? \u0418\u0437\u043F\u0440\u0430\u0442\u0435\u0442\u0435",
  howto: "\u0421\u043B\u0435\u0434\u0432\u0430\u0439\u0442\u0435 \u043D\u0430\u0448\u0438\u0442\u0435 \u0440\u044A\u043A\u043E\u0432\u043E\u0434\u0441\u0442\u0432\u0430",
  or: "\u0438\u043B\u0438",
  preventBinding: "\u0421\u043F\u0440\u0438 \u043F\u0440\u0438\u043B\u0435\u043F\u044F\u043D\u0435 \u043D\u0430 \u0441\u0442\u0440\u0435\u043B\u043A\u0438\u0442\u0435",
  tools: "\u0418\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442\u0438",
  shortcuts: "\u041A\u043B\u0430\u0432\u0438\u0448\u0438 \u0437\u0430 \u0431\u044A\u0440\u0437 \u0434\u043E\u0441\u0442\u044A\u043F",
  textFinish: "\u0417\u0430\u0432\u044A\u0440\u0448\u0438 \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u0430\u043D\u0435 (\u0442\u0435\u043A\u0441\u0442\u043E\u0432 \u0440\u0435\u0434\u0430\u043A\u0442\u043E\u0440)",
  textNewLine: "\u0414\u043E\u0431\u0430\u0432\u0438 \u043D\u043E\u0432\u0430 \u043B\u0438\u043D\u0438\u044F (\u0442\u0435\u043A\u0441\u0442\u043E\u0432 \u0440\u0435\u0434\u0430\u043A\u0442\u043E\u0440)",
  title: "\u041F\u043E\u043C\u043E\u0449",
  view: "\u041F\u0440\u0435\u0433\u043B\u0435\u0434",
  zoomToFit: "\u041F\u0440\u0438\u0431\u043B\u0438\u0436\u0438 \u0434\u043E\u043A\u0430\u0442\u043E \u0441\u0435 \u0432\u0438\u0436\u0434\u0430\u0442 \u0432\u0441\u0438\u0447\u043A\u0438 \u0435\u043B\u0435\u043C\u0435\u043D\u0442\u0438",
  zoomToSelection: "\u041F\u0440\u0438\u0431\u043B\u0438\u0436\u0438 \u0441\u0435\u043B\u0435\u043A\u0446\u0438\u044F\u0442\u0430",
  toggleElementLock: "\u0417\u0430\u043A\u043B\u044E\u0447\u0438/\u041E\u0442\u043A\u043B\u044E\u0447\u0438 \u0441\u0435\u043B\u0435\u043A\u0446\u0438\u044F",
  movePageUpDown: "\u041F\u0440\u0435\u043C\u0435\u0441\u0442\u0438 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430 \u043D\u0430\u0433\u043E\u0440\u0435/\u043D\u0430\u0434\u043E\u043B\u0443",
  movePageLeftRight: "\u041F\u0440\u0435\u043C\u0435\u0441\u0442\u0438 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430 \u043D\u0430\u043B\u044F\u0432\u043E/\u043D\u0430\u0434\u044F\u0441\u043D\u043E"
};
var clearCanvasDialog = {
  title: "\u0418\u0437\u0447\u0438\u0441\u0442\u0438 \u043F\u043B\u0430\u0442\u043D\u043E\u0442\u043E"
};
var publishDialog = {
  title: "\u041F\u0443\u0431\u043B\u0438\u043A\u0443\u0432\u0430\u0439 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430",
  itemName: "\u0418\u043C\u0435",
  authorName: "\u0410\u0432\u0442\u043E\u0440\u0441\u043A\u043E \u0438\u043C\u0435",
  githubUsername: "GitHub \u043F\u043E\u0442\u0440\u0435\u0431\u0438\u0442\u0435\u043B\u0441\u043A\u043E \u0438\u043C\u0435",
  twitterUsername: "Twitter \u043F\u043E\u0442\u0440\u0435\u0431\u0438\u0442\u0435\u043B\u0441\u043A\u043E \u0438\u043C\u0435",
  libraryName: "\u0418\u043C\u0435 \u043D\u0430 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430\u0442\u0430",
  libraryDesc: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u043D\u0430 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430\u0442\u0430",
  website: "\u0423\u0435\u0431\u0441\u0430\u0439\u0442",
  placeholder: {
    authorName: "\u0418\u043C\u0435\u0442\u043E \u0438\u043B\u0438 \u043F\u043E\u0442\u0440\u0435\u0431\u0438\u0442\u0435\u043B\u0441\u043A\u043E\u0442\u043E \u0412\u0438 \u0438\u043C\u0435",
    libraryName: "\u0418\u043C\u0435 \u043D\u0430 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430\u0442\u0430 \u0412\u0438",
    libraryDesc: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u043D\u0430 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430\u0442\u0430 \u0432\u0438, \u0437\u0430 \u0434\u0430 \u043F\u043E\u043C\u043E\u0433\u043D\u0435\u0442\u0435 \u043D\u0430 \u0445\u043E\u0440\u0430\u0442\u0430 \u0434\u0430 \u0440\u0430\u0437\u0431\u0435\u0440\u0430\u0442 \u043F\u0440\u0438\u043B\u043E\u0436\u0435\u043D\u0438\u044F\u0442\u0430 \u045D",
    githubHandle: "",
    twitterHandle: "",
    website: ""
  },
  errors: {
    required: "\u0417\u0430\u0434\u044A\u043B\u0436\u0438\u0442\u0435\u043B\u043D\u043E",
    website: "\u0412\u044A\u0432\u0435\u0434\u0435\u0442\u0435 \u0432\u0430\u043B\u0438\u0434\u0435\u043D URL \u0430\u0434\u0440\u0435\u0441"
  },
  noteDescription: "",
  noteGuidelines: "",
  noteLicense: "",
  noteItems: "",
  atleastOneLibItem: "",
  republishWarning: ""
};
var publishSuccessDialog = {
  title: "",
  content: ""
};
var confirmDialog = {
  resetLibrary: "\u041D\u0443\u043B\u0438\u0440\u0430\u0439 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430",
  removeItemsFromLib: ""
};
var imageExportDialog = {
  header: "",
  label: {
    withBackground: "\u0424\u043E\u043D",
    onlySelected: "\u0421\u0430\u043C\u043E \u0438\u0437\u0431\u0440\u0430\u043D\u043E\u0442\u043E",
    darkMode: "\u0422\u044A\u043C\u0435\u043D \u0440\u0435\u0436\u0438\u043C",
    embedScene: "",
    scale: "",
    padding: ""
  },
  tooltip: {
    embedScene: ""
  },
  title: {
    exportToPng: "\u0418\u0437\u043D\u0430\u0441\u044F\u043D\u0435 \u0432 PNG",
    exportToSvg: "\u0418\u0437\u043D\u0430\u0441\u044F\u043D\u0435 \u0432 SVG",
    copyPngToClipboard: "\u041A\u043E\u043F\u0438\u0440\u0430\u0439 PNG \u0432 \u043A\u043B\u0438\u043F\u0431\u043E\u0440\u0434\u0430"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "\u041A\u043E\u043F\u0438\u0440\u0430\u043D\u0435 \u0432 \u043A\u043B\u0438\u043F\u0431\u043E\u0440\u0434\u0430"
  }
};
var encrypted = {
  tooltip: "\u0412\u0430\u0448\u0438\u0442\u0435 \u0440\u0438\u0441\u0443\u043D\u043A\u0438 \u0441\u0430 \u043A\u0440\u0438\u043F\u0442\u0438\u0440\u0430\u043D\u0438 \u043E\u0442 \u043A\u0440\u0430\u0439 \u0434\u043E \u043A\u0440\u0430\u0439, \u0442\u0430\u043A\u0430 \u0447\u0435 \u0441\u044A\u0440\u0432\u044A\u0440\u0438\u0442\u0435 \u043D\u0430 Excalidraw \u043D\u044F\u043C\u0430 \u0434\u0430 \u043C\u043E\u0433\u0430\u0442 \u0434\u0430 \u0433\u0438 \u0432\u0438\u0436\u0434\u0430\u0442.",
  link: ""
};
var stats = {
  angle: "\u042A\u0433\u044A\u043B",
  element: "\u0415\u043B\u0435\u043C\u0435\u043D\u0442",
  elements: "\u0415\u043B\u0435\u043C\u0435\u043D\u0442\u0438",
  height: "\u0412\u0438\u0441\u043E\u0447\u0438\u043D\u0430",
  scene: "\u0421\u0446\u0435\u043D\u0430",
  selected: "\u0421\u0435\u043B\u0435\u043A\u0442\u0438\u0440\u0430\u043D\u043E",
  storage: "\u0421\u044A\u0445\u0440\u0430\u043D\u0435\u043D\u0438\u0435 \u043D\u0430 \u0434\u0430\u043D\u043D\u0438",
  title: "\u0421\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043A\u0430 \u0437\u0430 \u0445\u0430\u043A\u0435\u0440\u0438",
  total: "\u041E\u0431\u0449\u043E",
  version: "\u0412\u0435\u0440\u0441\u0438\u044F",
  versionCopy: "\u041D\u0430\u0441\u0442\u0438\u0441\u043D\u0438 \u0437\u0430 \u0434\u0430 \u043A\u043E\u043F\u0438\u0440\u0430\u0448",
  versionNotAvailable: "\u0412\u0435\u0440\u0441\u0438\u044F\u0442\u0430 \u043D\u0435 \u0435 \u043D\u0430\u043B\u0438\u0447\u043D\u0430",
  width: "\u0428\u0438\u0440\u043E\u0447\u0438\u043D\u0430"
};
var toast = {
  addedToLibrary: "\u0414\u043E\u0431\u0430\u0432\u0435\u043D\u0430 \u043A\u044A\u043C \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430\u0442\u0430",
  copyStyles: "\u041A\u043E\u043F\u0438\u0440\u0430\u043D\u0438 \u0441\u0442\u0438\u043B\u043E\u0432\u0435.",
  copyToClipboard: "\u041A\u043E\u043F\u0438\u0440\u0430\u043D\u043E \u0432 \u043A\u043B\u0438\u043F\u0431\u043E\u0440\u0434\u0430.",
  copyToClipboardAsPng: "\u041A\u043E\u043F\u0438\u0440\u0430 {{exportSelection}} \u0432 \u043A\u043B\u0438\u043F\u0431\u043E\u0440\u0434\u0430 \u043A\u0430\u0442\u043E PNG\n({{exportColorScheme}})",
  fileSaved: "\u0424\u0430\u0439\u043B\u044A\u0442 \u0435 \u0437\u0430\u043F\u0430\u0437\u0435\u043D.",
  fileSavedToFilename: "\u0417\u0430\u043F\u0430\u0437\u0435\u043D \u043A\u044A\u043C {filename}",
  canvas: "\u043F\u043B\u0430\u0442\u043D\u043E",
  selection: "\u0441\u0435\u043B\u0435\u043A\u0446\u0438\u044F",
  pasteAsSingleElement: "",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "\u041F\u0440\u043E\u0437\u0440\u0430\u0447\u0435\u043D",
  black: "\u0427\u0435\u0440\u0435\u043D",
  white: "\u0411\u044F\u043B",
  red: "\u0427\u0435\u0440\u0432\u0435\u043D",
  pink: "\u0420\u043E\u0437\u043E\u0432",
  grape: "\u0413\u0440\u043E\u0437\u0434\u0435",
  violet: "\u0412\u0438\u043E\u043B\u0435\u0442\u043E\u0432\u043E",
  gray: "\u0421\u0438\u0432",
  blue: "\u0421\u0438\u043D",
  cyan: "\u0421\u0438\u043D\u044C\u043E\u0437\u0435\u043B\u0435\u043D\u043E",
  teal: "\u0422\u044A\u043C\u043D\u043E \u0441\u0438\u043D\u044C\u043E-\u0437\u0435\u043B\u0435\u043D\u043E",
  green: "\u0417\u0435\u043B\u0435\u043D\u043E",
  yellow: "\u0416\u044A\u043B\u0442\u043E",
  orange: "\u041E\u0440\u0430\u043D\u0436\u0435\u0432\u043E",
  bronze: "\u0411\u0440\u043E\u043D\u0437\u043E\u0432\u043E"
};
var welcomeScreen = {
  app: {
    center_heading: "\u0412\u0441\u0438\u0447\u043A\u0438\u0442\u0435 \u0412\u0438 \u0434\u0430\u043D\u043D\u0438 \u0441\u0430 \u0437\u0430\u043F\u0430\u0437\u0435\u043D\u0438 \u043B\u043E\u043A\u0430\u043B\u043D\u043E \u0432 \u0431\u0440\u0430\u0443\u0437\u044A\u0440\u0430 \u0412\u0438.",
    center_heading_plus: "",
    menuHint: "\u0415\u043A\u0441\u043F\u043E\u0440\u0442, \u043F\u0440\u0435\u0434\u043F\u043E\u0447\u0438\u0442\u0430\u043D\u0438\u044F, \u0435\u0437\u0438\u0446\u0438, ..."
  },
  defaults: {
    menuHint: "\u0415\u043A\u0441\u043F\u043E\u0440\u0442, \u043F\u0440\u0435\u0434\u043F\u043E\u0447\u0438\u0442\u0430\u043D\u0438\u044F, \u0438 \u043E\u0449\u0435...",
    center_heading: "\u0414\u0438\u0430\u0433\u0440\u0430\u043C\u0438. \u041D\u0430\u043F\u0440\u0430\u0432\u0435\u043D\u0438. \u041F\u0440\u043E\u0441\u0442\u043E.",
    toolbarHint: "\u0418\u0437\u0431\u0435\u0440\u0435\u0442\u0435 \u0438\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442 & \u0417\u0430\u043F\u043E\u0447\u043D\u0435\u0442\u0435 \u0434\u0430 \u0440\u0438\u0441\u0443\u0432\u0430\u0442\u0435!",
    helpHint: "\u041F\u0440\u0435\u043A\u0438 \u043F\u044A\u0442\u0438\u0449\u0430 & \u043F\u043E\u043C\u043E\u0449"
  }
};
var colorPicker = {
  mostUsedCustomColors: "\u041D\u0430\u0439-\u0447\u0435\u0441\u0442\u043E \u0438\u0437\u043F\u043E\u043B\u0437\u0432\u0430\u043D\u0438 \u0446\u0432\u0435\u0442\u043E\u0432\u0435",
  colors: "\u0426\u0432\u0435\u0442\u043E\u0432\u0435",
  shades: "\u041D\u044E\u0430\u043D\u0441\u0438",
  hexCode: "\u0428\u0435\u0441\u0442\u043D\u0430\u0434\u0435\u0441\u0435\u0442\u0438\u0447\u0435\u043D \u043A\u043E\u0434",
  noShades: ""
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "\u0418\u0437\u043D\u0435\u0441\u0438 \u043A\u0430\u0442\u043E \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435",
      button: "\u0418\u0437\u043D\u0435\u0441\u0438 \u043A\u0430\u0442\u043E \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435",
      description: ""
    },
    saveToDisk: {
      title: "\u0417\u0430\u043F\u0430\u0437\u0438 \u043A\u044A\u043C \u0434\u0438\u0441\u043A",
      button: "\u0417\u0430\u043F\u0430\u0437\u0438 \u043A\u044A\u043C \u0434\u0438\u0441\u043A",
      description: ""
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "\u0415\u043A\u0441\u043F\u043E\u0440\u0442\u0438\u0440\u0430\u0439 \u043A\u044A\u043C Excalidraw+",
      description: "\u0417\u0430\u043F\u0430\u0437\u0438 \u0441\u0446\u0435\u043D\u0430\u0442\u0430 \u043A\u044A\u043C Excalidraw+ \u0440\u0430\u0431\u043E\u0442\u043D\u043E\u0442\u043E \u043C\u044F\u0441\u0442\u043E."
    }
  },
  modal: {
    loadFromFile: {
      title: "\u0417\u0430\u0440\u0435\u0434\u0438 \u043E\u0442 \u0444\u0430\u0439\u043B",
      button: "\u0417\u0430\u0440\u0435\u0434\u0438 \u043E\u0442 \u0444\u0430\u0439\u043B",
      description: ""
    },
    shareableLink: {
      title: "\u0417\u0430\u0440\u0435\u0434\u0438 \u043E\u0442 \u043B\u0438\u043D\u043A",
      button: "\u0417\u0430\u043C\u0435\u043D\u0438 \u043C\u043E\u0435\u0442\u043E \u0441\u044A\u0434\u044A\u0440\u0436\u0430\u043D\u0438\u0435",
      description: ""
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var bg_BG_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  bg_BG_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=bg-BG-AAABLFCY.js.map
