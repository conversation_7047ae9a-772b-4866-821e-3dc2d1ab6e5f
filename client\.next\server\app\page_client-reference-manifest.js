globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-toastify/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ClientAnimatePresence.tsx":{"*":{"id":"(ssr)/./src/components/ClientAnimatePresence.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Navbar.tsx":{"*":{"id":"(ssr)/./src/components/Navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/AuthContext.tsx":{"*":{"id":"(ssr)/./src/context/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/SocketContext.tsx":{"*":{"id":"(ssr)/./src/context/SocketContext.tsx","name":"*","chunks":[],"async":true}},"(app-pages-browser)/./src/context/ThemeContext.tsx":{"*":{"id":"(ssr)/./src/context/ThemeContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.jsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.jsx","name":"*","chunks":[],"async":true}},"(app-pages-browser)/./src/app/editor/[roomId]/page.tsx":{"*":{"id":"(ssr)/./src/app/editor/[roomId]/page.tsx","name":"*","chunks":[],"async":true}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(ssr)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Project\\realcode\\client\\node_modules\\@clerk\\nextjs\\dist\\esm\\app-router\\client\\ClerkProvider.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\@clerk\\nextjs\\dist\\esm\\app-router\\client\\keyless-cookie-sync.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\PromisifiedAuthProvider.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\react-toastify\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\src\\app\\grid-pattern.css":{"id":"(app-pages-browser)/./src/app/grid-pattern.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\react-toastify\\dist\\ReactToastify.css":{"id":"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\src\\components\\ClientAnimatePresence.tsx":{"id":"(app-pages-browser)/./src/components/ClientAnimatePresence.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\src\\components\\Navbar.tsx":{"id":"(app-pages-browser)/./src/components/Navbar.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\src\\context\\AuthContext.tsx":{"id":"(app-pages-browser)/./src/context/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\src\\context\\SocketContext.tsx":{"id":"(app-pages-browser)/./src/context/SocketContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":true},"C:\\Project\\realcode\\client\\src\\context\\ThemeContext.tsx":{"id":"(app-pages-browser)/./src/context/ThemeContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Project\\realcode\\client\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Project\\realcode\\client\\src\\app\\dashboard\\page.jsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.jsx","name":"*","chunks":[],"async":true},"C:\\Project\\realcode\\client\\src\\app\\editor\\[roomId]\\page.tsx":{"id":"(app-pages-browser)/./src/app/editor/[roomId]/page.tsx","name":"*","chunks":[],"async":true},"C:\\Project\\realcode\\client\\src\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Project\\realcode\\client\\src\\":[],"C:\\Project\\realcode\\client\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Project\\realcode\\client\\src\\app\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/react-toastify/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/grid-pattern.css":{"*":{"id":"(rsc)/./src/app/grid-pattern.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css":{"*":{"id":"(rsc)/./node_modules/react-toastify/dist/ReactToastify.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ClientAnimatePresence.tsx":{"*":{"id":"(rsc)/./src/components/ClientAnimatePresence.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Navbar.tsx":{"*":{"id":"(rsc)/./src/components/Navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/AuthContext.tsx":{"*":{"id":"(rsc)/./src/context/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/SocketContext.tsx":{"*":{"id":"(rsc)/./src/context/SocketContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/ThemeContext.tsx":{"*":{"id":"(rsc)/./src/context/ThemeContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.jsx":{"*":{"id":"(rsc)/./src/app/dashboard/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/editor/[roomId]/page.tsx":{"*":{"id":"(rsc)/./src/app/editor/[roomId]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(rsc)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}