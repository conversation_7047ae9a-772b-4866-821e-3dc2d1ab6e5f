import "../chunk-XDFCUUT6.js";

// locales/kab-KAB.json
var labels = {
  paste: "Sen\u1E6De\u1E0D",
  pasteAsPlaintext: "",
  pasteCharts: "Sen\u1E6De\u1E0D udlifen",
  selectAll: "Fren akk",
  multiSelect: "<PERSON><PERSON> aferdis \u0263er tefrayt",
  moveCanvas: "Smutti ta\u0263zut n usune\u0263",
  cut: "Gzem",
  copy: "N\u0263el",
  copyAsPng: "N\u0263el \u0263er tecfawit am PNG",
  copyAsSvg: "N\u0263el \u0263er tecfawit am SVG",
  copyText: "N\u0263el \u0263er tecfawit am u\u1E0Dris",
  copySource: "",
  convertToCode: "",
  bringForward: "Awi \u0263er sdat",
  sendToBack: "Awi s agilal",
  bringToFront: "Err \u0263er deffir",
  sendBackward: "Awi \u0263er deffir",
  delete: "Kkes",
  copyStyles: "N\u0263el i\u0263unab",
  pasteStyles: "Sen\u1E6De\u1E0D i\u0263unab",
  stroke: "Azizdew",
  background: "Agilal",
  fill: "Ta\u010D\u010Dart",
  strokeWidth: "Tehri n yizirig",
  strokeStyle: "A\u0263anib n tizirig",
  strokeStyle_solid: "A\u010D\u010Duran",
  strokeStyle_dashed: "S tjerri\u1E0Din",
  strokeStyle_dotted: "S tenqi\u1E0Din",
  sloppiness: "Astehzi",
  opacity: "Ti\u1E0Dullest",
  textAlign: "Areyyec n u\u1E0Dris",
  edges: "Leryuf",
  sharp: "Yemsed",
  round: "Imdewer",
  arrowheads: "Ixfawen n tenccabt",
  arrowhead_none: "Ulac",
  arrowhead_arrow: "Taneccabt",
  arrowhead_bar: "Afeggag",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "Akerdis",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "Tiddi n tsefsit",
  fontFamily: "Tawacult n tsefsiyin",
  addWatermark: 'Seddu "Yettwaxdem s Excalidraw"',
  handDrawn: "Asune\u0263 s ufus",
  normal: "Amagnu",
  code: "Tangalt",
  small: "Me\u1E93\u1E93i",
  medium: "Alemmas",
  large: "Ameqran",
  veryLarge: "Meqqer a\u1E6Das",
  solid: "A\u010D\u010Duran",
  hachure: "Azerreg",
  zigzag: "",
  crossHatch: "Azerreg anmidag",
  thin: "Arqaq",
  bold: "Azuran",
  left: "Azelma\u1E0D",
  center: "Talemmast",
  right: "Ayfus",
  extraBold: "Azuran a\u1E6Das",
  architect: "Amasdag",
  artist: "Ana\u1E93ur",
  cartoonist: "Amef\u0263ul",
  fileTitle: "Isem n ufaylu",
  colorPicker: "Amafran n yini",
  canvasColors: "Yettwaseqdec di te\u0263zut n usune\u0263",
  canvasBackground: "Agilal n te\u0263zut n usune\u0263",
  drawingCanvas: "Ta\u0263zut n usune\u0263",
  layers: "Tissiyin",
  actions: "Tigawin",
  language: "Tutlayt",
  liveCollaboration: "Am\u025Biwen s srid...",
  duplicateSelection: "Sisleg",
  untitled: "War azwel",
  name: "Isem",
  yourName: "Isem-ik (im)",
  madeWithExcalidraw: "Yettwaxdem s Excalidraw",
  group: "Segrew tafrayt",
  ungroup: "Kkess asegrew i tefrayt",
  collaborators: "Im\u025Biwnen",
  showGrid: "Beqqe\u1E0D aferrug",
  addToLibrary: "Rnu \u0263er temkar\u1E0Dit",
  removeFromLibrary: "Kkes si temkar\u1E0Dit",
  libraryLoadingMessage: "Asali n temkar\u1E0Dit\u2026",
  libraries: "Snirem timkar\u1E0Diyin",
  loadingScene: "Asali n usayes\u2026",
  align: "Reyyec",
  alignTop: "Areyyec uksawen",
  alignBottom: "Areyyec ukessar",
  alignLeft: "Reyyec s azelma\u1E0D",
  alignRight: "Areyyec s ayfus",
  centerVertically: "Di tlemmast s ibeddi",
  centerHorizontally: "Di tlemmast s uglawi",
  distributeHorizontally: "Freq s uglawi",
  distributeVertically: "Freq s yibeddi",
  flipHorizontal: "Tuttya taglawant",
  flipVertical: "Tuttya tubdidt",
  viewMode: "Askar n tmu\u0263li",
  share: "B\u1E0Du",
  showStroke: "Beqqe\u1E0D amelqa\u1E0D n yini n yizirig",
  showBackground: "Beqqe\u1E0D amelqa\u1E0D n yini n ugilal",
  toggleTheme: "Snifel asentel",
  personalLib: "Tamkar\u1E0Dit tudmawant",
  excalidrawLib: "Tamkar\u1E0Dit n Excalidraw",
  decreaseFontSize: "Senqes tiddi n tsefsit",
  increaseFontSize: "Sali tiddi n tsefsit",
  unbindText: "Serre\u1E25 iwe\u1E0Dris",
  bindText: "Arez a\u1E0Dris s anagbar",
  createContainerFromText: "",
  link: {
    edit: "\u1E92reg ase\u0263wen",
    editEmbed: "",
    create: "Snulfu-d ase\u0263wen",
    createEmbed: "",
    label: "Ase\u0263wen",
    labelEmbed: "",
    empty: ""
  },
  lineEditor: {
    edit: "\u1E92reg izirig",
    exit: "Ffe\u0263 seg uma\u1E93rag n yizirig"
  },
  elementLock: {
    lock: "Sekke\u1E5B",
    unlock: "Serre\u1E25",
    lockAll: "Sekke\u1E5B akk",
    unlockAll: "Serre\u1E25 akk"
  },
  statusPublished: "Yeffe\u0263-d",
  sidebarLock: "E\u01E7\u01E7 afeggag n yidis yeldi",
  selectAllElementsInFrame: "",
  removeAllElementsFromFrame: "",
  eyeDropper: "",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "Ulac iferdisen yettwarnan yakan...",
  hint_emptyLibrary: "Fren aferdis di te\u0263zut nusune\u0263 akken at-ternu\u1E0D dagi, ne\u0263 sbedd tamkar\u1E0Dit seg usarsay azayez, ukessar-agi.",
  hint_emptyPrivateLibrary: "Fren aferdis di te\u0263zut nusune\u0263 akken at-ternu\u1E0D dagi."
};
var buttons = {
  clearReset: "Ales awennez n te\u0263zut n usune\u0263",
  exportJSON: "Sife\u1E0D afaylu",
  exportImage: "Sife\u1E0D tugna...",
  export: "Sekles di...",
  copyToClipboard: "N\u0263el \u0263er tecfawit",
  save: "Sekles deg ufaylu amiran",
  saveAs: "Sekles am",
  load: "Ldi",
  getShareableLink: "Awi-d ase\u0263wen n be\u1E6D\u1E6Du",
  close: "Mdel",
  selectLanguage: "Fren tutlayt",
  scrollBackToContent: "U\u0263al s agbur",
  zoomIn: "Sim\u0263ur",
  zoomOut: "Sim\u1E93i",
  resetZoom: "Ales awennez n usem\u0263er",
  menu: "Umu\u0263",
  done: "Ifukk",
  edit: "\u1E92reg",
  undo: "Sefsex",
  redo: "Err-d",
  resetLibrary: "Ales awennez n temkar\u1E0Dit",
  createNewRoom: "Snulfu-d taxxamt tamaynutt",
  fullScreen: "Agdil a\u010D\u010Duran",
  darkMode: "Askar imsulles",
  lightMode: "Askar afaw",
  zenMode: "Askar Zen",
  objectsSnapMode: "",
  exitZenMode: "Ffe\u0263 seg uskar Zen",
  cancel: "Sefsex",
  clear: "Sfe\u1E0D",
  remove: "Kkes",
  embed: "",
  publishLibrary: "\u1E92reg",
  submit: "Azen",
  confirm: "Sentem",
  embeddableInteractionButton: ""
};
var alerts = {
  clearReset: "Ayagi ad isfe\u1E0D akk ta\u0263zut n usune\u0263. Tet\u1E25eqqe\u1E0D?",
  couldNotCreateShareableLink: "D awez\u0263i asnulfu n use\u0263wen n be\u1E6D\u1E6Du.",
  couldNotCreateShareableLinkTooBig: "D awez\u0263i asnulfu n use\u0263wen n be\u1E6D\u1E6Du. Asayes \u0263ezzif a\u1E6Das",
  couldNotLoadInvalidFile: "D awez\u0263i asali n ufaylu arme\u0263tu",
  importBackendFailed: "Takter\u1E0D seg u\u0263awas n deffir ur teddi ara.",
  cannotExportEmptyCanvas: "D awez\u0263i asife\u1E0D n te\u0263zut n usune\u0263 tilemt.",
  couldNotCopyToClipboard: "Ulamek an\u0263al \u0263er tecfawit.",
  decryptFailed: "D awez\u0263i tukksa n uwgelhen i yisefka.",
  uploadedSecurly: "Asili yettwas\u0263elles s uwgelhen ixef s ixef, ayagi yeb\u0263a ad d-yini belli aqeddac n Excalidraw akked medden ur zmiren ara ad \u0263ren agbur.",
  loadSceneOverridePrompt: "Asali n wunu\u0263 uffi\u0263 ad isemselsi agbur-inek (m) yellan. Teb\u0263i\u1E0D ad tkemmele\u1E0D?",
  collabStopOverridePrompt: "A\u1E25bas n t\u0263imit ad yesefsex unu\u0263-inek (m) yettwa\u1E25erzen yakan s wudem adigan. Tet\u1E25eqqe\u1E0D?\n(Ma teb\u0263i\u1E0D ad te\u01E7\u01E7e\u1E0D unu\u0263-inek (m) adigan, mdel iccer n yiminig, deg um\u1E0Diq.)",
  errorAddingToLibrary: "Ulamek ara yettwarnu uferdis \u0263er temkar\u1E0Dit",
  errorRemovingFromLibrary: "Ulamek ara yettwakkes uferdis si temkar\u1E0Dit",
  confirmAddLibrary: "Ayagi adirnu tal\u0263a (win) {{numShapes}} \u0263er temkar\u1E0Dit-inek (m). Tet\u1E25eqqe\u1E0D?",
  imageDoesNotContainScene: "Tugna-agi tettban-d ur tes\u025Bi ara isefka n usayes. Tesremde\u1E0D aseddu n usayes deg usife\u1E0D?",
  cannotRestoreFromImage: "Asayes ulamek ara d-yettwarr seg ufaylu-agi n tugna",
  invalidSceneUrl: "Ulamek taktert n usayes seg URL i d-ittunefken. Ahat ma\u010D\u010Di d tame\u0263tut ne\u0263 ur tegbir ara isefka JSON n Excalidraw.",
  resetLibrary: "Ayagi ad isfe\u1E0D tamkar\u1E0Dit-inek\u2022m. Tet\u1E25eqqe\u1E0D?",
  removeItemsFromsLibrary: "Ad tekkse\u1E0D {{count}} n uferdis (en) si temkar\u1E0Dit?",
  invalidEncryptionKey: "Tasarut n uwgelhen isefk ad tes\u025Bu 22 n yiekkilen. Am\u025Biwen srid yensa.",
  collabOfflineWarning: "Ulac tuqqna n internet.\nIbedilen-ik ur ttwaklasen ara!"
};
var errors = {
  unsupportedFileType: "Anaw n ufaylu ur yettwasefrak ara.",
  imageInsertError: "D awez\u0263i tugra n tugna. E\u025Bre\u1E0D tikkelt-nni\u1E0Den ardeqqal...",
  fileTooBig: "Afaylu meqqer a\u1E6Das. Tiddi tafellayt yurgen d {{maxSize}}.",
  svgImageInsertError: "D awez\u0263i tugra n tugna SVG. Acra\u1E0D SVG yettban-d d arme\u0263tu.",
  failedToFetchImage: "",
  invalidSVGString: "SVG arme\u0263tu.",
  cannotResolveCollabServer: "Ulamek tuqqna s aqeddac n umyalel. Ma ulac u\u0263ilif ales asali n usebter sakin e\u025Bre\u1E0D tikkelt-nni\u1E0Den.",
  importLibraryError: "Ur d-ssalay ara tamkar\u1E0Dit",
  collabSaveFailed: "Ulamek asekles deg uzadur n yisefka deg ugilal. Ma ikemmel wugur, isefk ad teskelse\u1E0D afaylu s wudem adigan akken ad tet\u1E25eqqe\u1E0D ur tesru\u1E25uye\u1E0D ara amahil-inek\u2022inem.",
  collabSaveFailed_sizeExceeded: "Ulamek asekles deg uzadur n yisefka deg ugilal, ta\u0263zut n usune\u0263 tettban-d temqer a\u1E6Das. Isefk ad teskelse\u1E0D afaylu s wudem adigan akken ad tet\u1E25eqqe\u1E0D ur tesru\u1E25uye\u1E0D ara amahil-inek\u2022inem.",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "",
    line2: "Ayagi yezmer ad d-iglu s tru\u1E93i n<bold>Iferdisen n u\u1E0Dris</bold>deg wunu\u0263en-inek.",
    line3: "Ad k-nsemter ad tsexsi\u1E0D a\u0263ewwar-agi. Tzemre\u1E0D ad t\u1E0Defre\u1E0D<link>isurifen-agi</link> \u0263ef wamek ara txedme\u1E0D.",
    line4: ""
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "",
    image: ""
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "Tafrayt",
  image: "Ger tugna",
  rectangle: "Asrem",
  diamond: "Ame\u0263\u1E5Bun",
  ellipse: "Taglayt",
  arrow: "Taneccabt",
  line: "Izirig",
  freedraw: "Sune\u0263",
  text: "A\u1E0Dris",
  library: "Tamkar\u1E0Dit",
  lock: "E\u01E7\u01E7 afecku n tefrayt yermed mba\u025Bd asune\u0263",
  penMode: "Askar n yimru - gdel tanalit",
  link: "Rnu/leqqem ase\u0263wen i tal\u0263a yettwafernen",
  eraser: "Sfe\u1E0D",
  frame: "",
  magicframe: "",
  embeddable: "",
  laser: "",
  hand: "Afus (afecku n usmutti n tmu\u0263li)",
  extraTools: "",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "Tigawin n te\u0263zut n usune\u0263",
  selectedShapeActions: "Tigawin n tal\u0263a yettwafernen",
  shapes: "Tal\u0263iwin"
};
var hints = {
  canvasPanning: "Akken ad tesmutti\u1E0D ta\u0263zut n usune\u0263, \u1E6D\u1E6Def \u1E5B\u1E5Buda n umumed, ne\u0263 seqdec afecku Afus",
  linearElement: "Ssit akken ad tebdu\u1E0D a\u1E6Das n tenqi\u1E0Din, zu\u0263er i yiwen n yizirig",
  freeDraw: "Ssit yerna zu\u0263er, serre\u1E25 ticki tfuke\u1E0D",
  text: "Tixidest: tzemre\u1E0D da\u0263en ad ternu\u1E0D a\u1E0Dris s usiti snat n tikkal anida teb\u0263i\u1E0D s ufecku n tefrayt",
  embeddable: "",
  text_selected: "Ssit snat n tikkal ne\u0263 ssed taqeffalt Kcem akken ad t\u1E93erge\u1E0D a\u1E0Dris",
  text_editing: "Ssit Escape ne\u0263 CtrlOrCmd+ENTER akken ad tfakke\u1E0D asi\u1E93reg",
  linearElementMulti: "Ssit \u0263ef tenqi\u1E0Dt taneggarut ne\u0263 ssed taqeffalt Escape ne\u0263 taqeffalt Kcem akken ad tfakke\u1E0D",
  lockAngle: "Tzemre\u1E0D ad t\u1E25ettme\u1E0D ti\u0263mert s tu\u1E6D\u1E6Dfa n tqeffalt SHIFT",
  resize: "Tzemre\u1E0D ad t\u1E25etteme\u1E0D assa\u0263 s tu\u1E6D\u1E6Dfa n tqeffalt SHIFT mi ara tettbeddile\u1E0D tiddi,\nma te\u1E6D\u1E6Dfe\u1E0D ALT abeddel n tiddi ad yili si tlemmast",
  resizeImage: "Tzemre\u1E0D ad talse\u1E0D tiddi s tilelli s tu\u1E6D\u1E6Dfa n SHIFT,\n\u1E6D\u1E6Def ALT akken ad talse\u1E0D tiddi si tlemmast",
  rotate: "Tzemre\u1E0D ad t\u1E25etteme\u1E0D ti\u0263emmar s tu\u1E6D\u1E6Dfa n SHIFT di tuzzya",
  lineEditor_info: "Ssed \u0263ef CtrlOrCmd yerna ssit snat n tikkal ne\u0263 ssed \u0263ef CtrlOrCmd + Kcem akken ad t\u1E93erge\u1E0D tineqqi\u1E0Din",
  lineEditor_pointSelected: "Ssed taqeffalt kkes akken ad tekkse\u1E0D tanqi\u1E0D (tinqi\u1E0Din),\nCtrlOrCmd+D akken ad tsiselge\u1E0D, ne\u0263 zu\u0263er akken ad tesmutti\u1E0D",
  lineEditor_nothingSelected: "Fren tanqi\u1E0Dt akken ad t\u1E93erge\u1E0D (\u1E6D\u1E6Def SHIFT akken ad tferne\u1E0D a\u1E6Das),\nne\u0263 \u1E6D\u1E6Def Alt akken ad ternu\u1E0D tinqi\u1E0Din timaynutin",
  placeImage: "Ssit akken ad tserse\u1E0D tugna, ne\u0263 ssit u zu\u0263er akken ad tesbadu\u1E0D tiddi-ines s ufus",
  publishLibrary: "Si\u1E93reg tamkar\u1E0Dit-inek\u2022inem",
  bindTextToElement: "Ssed \u0263ef kcem akken ad ternu\u1E0D a\u1E0Dris",
  deepBoxSelect: "\u1E6C\u1E6Def CtrlOrCmd akken ad tferne\u1E0D s telqey, yerna ad trewle\u1E0D i uzu\u0263er",
  eraserRevert: "Ssed Alt akken ad tsefsxe\u1E0D iferdisen yettwacer\u1E0Den i tukksa",
  firefox_clipboard_write: "",
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "Ulamek abeqqe\u1E0D n teskant",
  canvasTooBig: "Ta\u0263zut n usune\u0263 tezmer ad tili temeqqer a\u1E6Das.",
  canvasTooBigTip: "Tixidest: e\u025Bre\u1E0D ad tesqerbe\u1E0D ci\u1E6D iferdisen yemba\u025Baden."
};
var errorSplash = {
  headingMain: "Te\u1E0Dra-d tucc\u1E0Da. E\u025Bre\u1E0D <button>asali n usebter tikkelt-nni\u1E0Den.</button>",
  clearCanvasMessage: "Ma yella tulsa n usali ur tefri ara ugur, e\u025Bre\u1E0D <button>asfa\u1E0D n te\u0263zut n usune\u0263.</button>",
  clearCanvasCaveat: " Ayagi ad d-iglu s us\u1E5Bu\u1E25u n umahil ",
  trackedToSentry: "Tucc\u1E0Da akked umesmagi {{eventId}} tettwasekles deg unagraw-nne\u0263.",
  openIssueMessage: "N\u1E25uder a\u1E6Das akken ur nseddu ara tal\u0263ut n usayes-inek (m) di tucc\u1E0Da. Ma yella asayes-inek (m) ma\u010D\u010Di d ama\u1E93lay, ttxil-k (m) xemmem ad \u1E0Defre\u1E0D <button>afecku n we\u1E0Dfar n yibugen.</button> Ma ulac u\u0263ilif seddu tal\u0263ut ukessar-agi s wen\u0263al akked usen\u1E6De\u1E0D di GitHub issue.",
  sceneContent: "Agbur n usayes:"
};
var roomDialog = {
  desc_intro: "Tzemre\u1E0D ad d-te\u025Ber\u1E0De\u1E0D medden \u0263er usayes-inek (m) amiran akken ad ttekkin yid-k.",
  desc_privacy: "Ur tqelliq ara, ti\u0263imit tsseqdac awgelhen ixef s ixef, d\u0263a ayen ara tsun\u0263e\u1E0D ad iqqim d ama\u1E93lay. Ula d aqeddac-nne\u0263 ur yezmir ara ad iwali acu txeddeme\u1E0D.",
  button_startSession: "Bdu ti\u0263imit",
  button_stopSession: "\u1E24bes ti\u0263imit",
  desc_inProgressIntro: "Ti\u0263imit n um\u025Bawen s srid tetteddu akka tura.",
  desc_shareLink: "B\u1E0Du ase\u0263wen-agi akked medden ukud teb\u0263i\u1E0D ad tem\u025Bawane\u1E0D:",
  desc_exitSession: "A\u1E25bas n t\u0263imit ad k (m) yesenser si texxamt, maca ad tizmire\u1E0D ad tkemmele\u1E0D amahil s usayes, s wudem adigan. \u1E92er belli ayagi ur yett\u1E25az ara imdanen-nni\u1E0Den, yerna ad izmiren ad kemmelen ad m\u025Bawanen di tsuffe\u0263t-nnsen.",
  shareTitle: "Rnu \u0263er t\u0263imit n um\u025Biwen s srid n Excalidraw"
};
var errorDialog = {
  title: "Tucc\u1E0Da"
};
var exportDialog = {
  disk_title: "Sekles deg u\u1E0Debsi",
  disk_details: "Sekles isefka n usayes deg ufaylu ansi ara tizmire\u1E0D ad d-tketre\u1E0D are\u1E0Dqal.",
  disk_button: "Sekles deg ufaylu",
  link_title: "Ase\u0263wen n be\u1E6D\u1E6Du",
  link_details: "Sife\u1E0D am use\u0263wen n t\u0263uri kan.",
  link_button: "Sife\u1E0D deg use\u0263wen",
  excalidrawplus_description: "Sekles asayes-inek\u2022inem di tallunt n umahil Excalidraw+.",
  excalidrawplus_button: "Sife\u1E0D",
  excalidrawplus_exportError: "Ulamek asife\u1E0D \u0263er Excalidraw+ akka tura..."
};
var helpDialog = {
  blog: "\u0194e\u1E5B ablug-nne\u0263",
  click: "ssit",
  deepSelect: "Afran s telqey",
  deepBoxSelect: "Afran s telqey s tnaka, yerna ad tyrewle\u1E0D i uzu\u0263er",
  curvedArrow: "Taneccabt izelgen",
  curvedLine: "Izirig izelgen",
  documentation: "Tasemlit",
  doubleClick: "ssit snat n tikkal",
  drag: "zu\u0263er",
  editor: "Ama\u1E93rag",
  editLineArrowPoints: "\u1E92reg tinqi\u1E0Din n yizirig/taneccabt",
  editText: "\u1E92reg a\u1E0Dris/rnu tabzimt",
  github: "Tufi\u1E0D-d ugur? Azen-a\u0263-d",
  howto: "\u1E0Cfer imniren-nne\u0263",
  or: "ne\u0263",
  preventBinding: "Se\u1E25bes tuqqna n tneccabin",
  tools: "Ifecka",
  shortcuts: "Inegzumen n unasiw",
  textFinish: "Fak asi\u1E93reg (ama\u1E93rag n u\u1E0Dris)",
  textNewLine: "Rnu ajerri\u1E0D amaynut (ama\u1E93rag n u\u1E0Dris)",
  title: "Tallelt",
  view: "Tamu\u0263li",
  zoomToFit: "Sim\u0263ur akken ad twli\u1E0D akk iferdisen",
  zoomToSelection: "Sim\u0263ur \u0263er tefrayt",
  toggleElementLock: "Sekke\u1E5B/kkes asekker i tefrayt",
  movePageUpDown: "Smutti asebter d asawen/akessar",
  movePageLeftRight: "Smutti asebter s azelma\u1E0D/ayfus"
};
var clearCanvasDialog = {
  title: "Sfe\u1E0D ta\u0263zut n usune\u0263"
};
var publishDialog = {
  title: "Suffe\u0263-d tamkar\u1E0Dit",
  itemName: "Isem n uferdis",
  authorName: "Isem n umeskar",
  githubUsername: "Isem n useqdac n GitHub",
  twitterUsername: "Isem n useqdac n Twitter",
  libraryName: "Isem n temkar\u1E0Dit",
  libraryDesc: "Aglam n temkar\u1E0Dit",
  website: "Asmel n web",
  placeholder: {
    authorName: "Isem ne\u0263 isem n useqdac inek\u2022inem",
    libraryName: "Isem n temkar\u1E0Dit-inek\u2022inem",
    libraryDesc: "Aglam n temkar\u1E0Dit-inek\u2022inem akken ad t\u025Biwne\u1E0D medden ad fehmen aseqdec-inec",
    githubHandle: "Isem n useqdac n GitHub ( d anefrunan) akken ad tizmire\u1E0D ad tis\u1E93rige\u1E0D tamkar\u1E0Dit ticki tuzne\u1E0D-tt i uselken",
    twitterHandle: "Isem n useqdac n Twitter (d anefrunan) akken ad n\u1E93er anwa ara nsenmer deg udellel di Twitter",
    website: "Ase\u0263wen \u0263er usmel-inek\u2022inem ne\u0263 waye\u1E0D (d anefrunan)"
  },
  errors: {
    required: "Yettwasra",
    website: "Sekcem URL ame\u0263tu"
  },
  noteDescription: "Azen tamkar\u1E0Dit-inek\u2022inem akken ad teddu di <link>akaram azayez n temkar\u1E0Dit</link>i yimdanen-nni\u1E0Den ara isqedcen deg wunu\u0263en-nnsen.",
  noteGuidelines: "Tamkar\u1E0Dit te\u1E25wa\u01E7 ad tettwaqbel s ufus qbel. Ma ulac u\u0263ilif \u0263er <link>iwellihen</link> send ad tazne\u1E0D. Tesri\u1E0D ami\u1E0Dan n GitHub akken ad tmmeslaye\u1E0D yerna ad tge\u1E0D ibeddilen ma yelaq, maca ma\u010D\u010Di d ayen yettwa\u1E25etmen.",
  noteLicense: "Mi tuzne\u1E0D ad tqeble\u1E0D akken tamkar\u1E0Dit ad d-teffe\u0263 s <link>Turagt MIT, </link>ayen yeb\u0263an ad d-yini belli yal yiwen izmer ad ten-iseqdec war tilist.",
  noteItems: "Yal aferdis n temkar\u1E0Dit isefk ad is\u025Bu isem-is i yiman-is akken ad yili wamek ara yettusizdeg. Iferdisen-agi n temkar\u1E0Dit ad ddun:",
  atleastOneLibItem: "Ma ulac u\u0263ilif fern ma drus yiwen n uferdis n temkar\u1E0Dit akken ad tebdu\u1E0D",
  republishWarning: "Tamawt: kra n yiferdisen yettwafernen ttwacer\u1E0Den ffe\u0263en-d/ttwaznen. Isefk ad talse\u1E0D tuzzna n yiferdisen anagar mi ara tleqqeme\u1E0D tamkar\u1E0Dit ne\u0263 tuzzna yellan."
};
var publishSuccessDialog = {
  title: "Tamkar\u1E0Dit tettwazen",
  content: "Tanemmirt-ik\u2022im {{authorName}}. Tamkar\u1E0Dit-inek\u2022inem tettwazen i weselken. Tzemre\u1E0D ad t\u1E0Defre\u1E0D a\u1E93ayer<link>dagi</link>"
};
var confirmDialog = {
  resetLibrary: "Ales awennez n temkar\u1E0Dit",
  removeItemsFromLib: "Kkes iferdisen yettafernen si temkar\u1E0Dit"
};
var imageExportDialog = {
  header: "",
  label: {
    withBackground: "",
    onlySelected: "",
    darkMode: "",
    embedScene: "",
    scale: "",
    padding: ""
  },
  tooltip: {
    embedScene: ""
  },
  title: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  },
  button: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  }
};
var encrypted = {
  tooltip: "Unu\u0263en-inek (m) ttuwgelhnen seg yixef s ixef d\u0263a iqeddacen n Excalidraw wer\u01E7in ad ten-walin. ",
  link: "Amagrad \u0263ef uwgelhen ixef s ixef di Excalidraw"
};
var stats = {
  angle: "Ti\u0263me\u1E5Bt",
  element: "Aferdis",
  elements: "Iferdisen",
  height: "Tattayt",
  scene: "Asayes",
  selected: "Yettwafren",
  storage: "A\u1E25raz",
  title: "",
  total: "A\u0263rud",
  version: "Alqem",
  versionCopy: "Sit ad tne\u0263le\u1E0D",
  versionNotAvailable: "Ur inu\u1E25 ulqem",
  width: "Tehri"
};
var toast = {
  addedToLibrary: "Yettwarna \u0263er temkar\u1E0Dit",
  copyStyles: "I\u0263unab yettwane\u0263len.",
  copyToClipboard: "Yettwa\u0263el \u0263er tecfawit.",
  copyToClipboardAsPng: "{{exportSelection}} yettwan\u0263el \u0263er tecfawit am PNG\n({{exportColorScheme}})",
  fileSaved: "Afaylu yettwasekles.",
  fileSavedToFilename: "Yettwasekles di {filename}",
  canvas: "ta\u0263zut n usune\u0263",
  selection: "tafrayt",
  pasteAsSingleElement: "",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "Afrawan",
  black: "",
  white: "",
  red: "",
  pink: "",
  grape: "",
  violet: "",
  gray: "",
  blue: "",
  cyan: "",
  teal: "",
  green: "",
  yellow: "",
  orange: "",
  bronze: ""
};
var welcomeScreen = {
  app: {
    center_heading: "Akk isefka-inek\u2022inem ttwakelsen s wudem adigan deg yiminig-inek\u2022inem.",
    center_heading_plus: "Teb\u0263i\u1E0D ad teddu\u1E0D \u0263er Excalidraw+ deg um\u1E0Diq?",
    menuHint: "Asife\u1E0D, ismenyifen, tutlayin, ..."
  },
  defaults: {
    menuHint: "Asife\u1E0D, ismenyifen, d wayen-nni\u1E0Den...",
    center_heading: "",
    toolbarHint: "Fren afecku tebdu\u1E0D asune\u0263!",
    helpHint: "Inegzumen akked tallelt"
  }
};
var colorPicker = {
  mostUsedCustomColors: "",
  colors: "",
  shades: "",
  hexCode: "",
  noShades: ""
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "",
      button: "",
      description: ""
    },
    saveToDisk: {
      title: "",
      button: "",
      description: ""
    },
    excalidrawPlus: {
      title: "",
      button: "",
      description: ""
    }
  },
  modal: {
    loadFromFile: {
      title: "",
      button: "",
      description: ""
    },
    shareableLink: {
      title: "",
      button: "",
      description: ""
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var kab_KAB_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  kab_KAB_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=kab-KAB-2S7ZURK7.js.map
