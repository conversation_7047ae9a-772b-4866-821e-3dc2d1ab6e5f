"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_graphql_graphql_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/graphql/graphql.js":
/*!*******************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/graphql/graphql.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/graphql/graphql.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"\"\"', close: '\"\"\"', notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"\"\"', close: '\"\"\"' },\n    { open: '\"', close: '\"' }\n  ],\n  folding: {\n    offSide: true\n  }\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  defaultToken: \"invalid\",\n  tokenPostfix: \".gql\",\n  keywords: [\n    \"null\",\n    \"true\",\n    \"false\",\n    \"query\",\n    \"mutation\",\n    \"subscription\",\n    \"extend\",\n    \"schema\",\n    \"directive\",\n    \"scalar\",\n    \"type\",\n    \"interface\",\n    \"union\",\n    \"enum\",\n    \"input\",\n    \"implements\",\n    \"fragment\",\n    \"on\"\n  ],\n  typeKeywords: [\"Int\", \"Float\", \"String\", \"Boolean\", \"ID\"],\n  directiveLocations: [\n    \"SCHEMA\",\n    \"SCALAR\",\n    \"OBJECT\",\n    \"FIELD_DEFINITION\",\n    \"ARGUMENT_DEFINITION\",\n    \"INTERFACE\",\n    \"UNION\",\n    \"ENUM\",\n    \"ENUM_VALUE\",\n    \"INPUT_OBJECT\",\n    \"INPUT_FIELD_DEFINITION\",\n    \"QUERY\",\n    \"MUTATION\",\n    \"SUBSCRIPTION\",\n    \"FIELD\",\n    \"FRAGMENT_DEFINITION\",\n    \"FRAGMENT_SPREAD\",\n    \"INLINE_FRAGMENT\",\n    \"VARIABLE_DEFINITION\"\n  ],\n  operators: [\"=\", \"!\", \"?\", \":\", \"&\", \"|\"],\n  // we include these common regular expressions\n  symbols: /[=!?:&|]+/,\n  // https://facebook.github.io/graphql/draft/#sec-String-Value\n  escapes: /\\\\(?:[\"\\\\\\/bfnrt]|u[0-9A-Fa-f]{4})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // fields and argument names\n      [\n        /[a-z_][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"key.identifier\"\n          }\n        }\n      ],\n      // identify typed input variables\n      [\n        /[$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"argument.identifier\"\n          }\n        }\n      ],\n      // to show class names nicely\n      [\n        /[A-Z][\\w\\$]*/,\n        {\n          cases: {\n            \"@typeKeywords\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, { cases: { \"@operators\": \"operator\", \"@default\": \"\" } }],\n      // @ annotations.\n      // As an example, we emit a debugging log message on these tokens.\n      // Note: message are supressed during the first load -- change some lines to see them.\n      [/@\\s*[a-zA-Z_\\$][\\w\\$]*/, { token: \"annotation\", log: \"annotation token: $0\" }],\n      // numbers\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      [/\"\"\"/, { token: \"string\", next: \"@mlstring\", nextEmbedded: \"markdown\" }],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }]\n    ],\n    mlstring: [\n      [/[^\"]+/, \"string\"],\n      ['\"\"\"', { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/#.*$/, \"comment\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/graphql/graphql.js\n"));

/***/ })

}]);