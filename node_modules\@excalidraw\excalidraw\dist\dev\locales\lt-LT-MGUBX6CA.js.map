{"version": 3, "sources": ["../../../locales/lt-LT.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Įklijuoti\",\n    \"pasteAsPlaintext\": \"Įklijuoti kaip paprastą tekstą\",\n    \"pasteCharts\": \"Įklijuoti diagramas\",\n    \"selectAll\": \"Pažymėti viską\",\n    \"multiSelect\": \"Pridėkite elementą prie pasirinktų\",\n    \"moveCanvas\": \"<PERSON>dinti drobę\",\n    \"cut\": \"<PERSON>š<PERSON>rpti\",\n    \"copy\": \"<PERSON>pijuoti\",\n    \"copyAsPng\": \"Kopijuoti į iškarpinę kaip PNG\",\n    \"copyAsSvg\": \"Kopijuoti į iškarpinę kaip SVG\",\n    \"copyText\": \"Kopijuoti į iškarpinę kaip tekstą\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Kelti priekio link\",\n    \"sendToBack\": \"Nustumti į užnugarį\",\n    \"bringToFront\": \"Iškelti į priekį\",\n    \"sendBackward\": \"Nustumti link užnugario\",\n    \"delete\": \"<PERSON>š<PERSON><PERSON>\",\n    \"copyStyles\": \"<PERSON><PERSON><PERSON><PERSON>i stilius\",\n    \"pasteStyles\": \"Įklijuoti stilius\",\n    \"stroke\": \"<PERSON><PERSON>\",\n    \"background\": \"<PERSON>ona<PERSON>\",\n    \"fill\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n    \"strokeWidth\": \"Linijos storis\",\n    \"strokeStyle\": \"Linijos stilius\",\n    \"strokeStyle_solid\": \"Ištisinė\",\n    \"strokeStyle_dashed\": \"Brūkšniuota\",\n    \"strokeStyle_dotted\": \"Taškuota\",\n    \"sloppiness\": \"Netvarkingumas\",\n    \"opacity\": \"Nepermatomumas\",\n    \"textAlign\": \"Teksto lygiavimas\",\n    \"edges\": \"Kraštai\",\n    \"sharp\": \"Aštrus\",\n    \"round\": \"Užapvalintas\",\n    \"arrowheads\": \"Rodyklės viršūnės\",\n    \"arrowhead_none\": \"Jokios\",\n    \"arrowhead_arrow\": \"Rodyklė\",\n    \"arrowhead_bar\": \"Brukšnys\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Trikampis\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Šrifto dydis\",\n    \"fontFamily\": \"Šriftas\",\n    \"addWatermark\": \"Sukurta su Excalidraw\",\n    \"handDrawn\": \"Ranka rašytas\",\n    \"normal\": \"Normalus\",\n    \"code\": \"Kodas\",\n    \"small\": \"Mažas\",\n    \"medium\": \"Vidutinis\",\n    \"large\": \"Didelis\",\n    \"veryLarge\": \"Labai didelis\",\n    \"solid\": \"\",\n    \"hachure\": \"\",\n    \"zigzag\": \"\",\n    \"crossHatch\": \"\",\n    \"thin\": \"Plonas\",\n    \"bold\": \"Pastorintas\",\n    \"left\": \"Kairėje\",\n    \"center\": \"Centre\",\n    \"right\": \"Dešinėje\",\n    \"extraBold\": \"Labiau pastorintas\",\n    \"architect\": \"Architektas\",\n    \"artist\": \"Menininkas\",\n    \"cartoonist\": \"Karikatūristas\",\n    \"fileTitle\": \"Failo pavadinimas\",\n    \"colorPicker\": \"Spalvos parinkiklis\",\n    \"canvasColors\": \"\",\n    \"canvasBackground\": \"Drobės fonas\",\n    \"drawingCanvas\": \"\",\n    \"layers\": \"Sluoksniai\",\n    \"actions\": \"Veiksmai\",\n    \"language\": \"Kalba\",\n    \"liveCollaboration\": \"Bendradarbiavimas gyvai...\",\n    \"duplicateSelection\": \"\",\n    \"untitled\": \"\",\n    \"name\": \"\",\n    \"yourName\": \"Jūsų vardas\",\n    \"madeWithExcalidraw\": \"Sukurta su Excalidraw\",\n    \"group\": \"Grupuoti pasirinkimą\",\n    \"ungroup\": \"Išgrupuoti pasirinkimą\",\n    \"collaborators\": \"Bendradarbiautojai\",\n    \"showGrid\": \"Rodyti tinklelį\",\n    \"addToLibrary\": \"Pridėti į biblioteką\",\n    \"removeFromLibrary\": \"Pašalinti iš bibliotekos\",\n    \"libraryLoadingMessage\": \"\",\n    \"libraries\": \"Naršyti bibliotekas\",\n    \"loadingScene\": \"\",\n    \"align\": \"Lygiuoti\",\n    \"alignTop\": \"Lygiuoti viršuje\",\n    \"alignBottom\": \"Lygiuoti apačioje\",\n    \"alignLeft\": \"Lygiuoti kairėje\",\n    \"alignRight\": \"Lygiuoti dešinėje\",\n    \"centerVertically\": \"Centruoti vertikaliai\",\n    \"centerHorizontally\": \"Centruoti horizontaliai\",\n    \"distributeHorizontally\": \"\",\n    \"distributeVertically\": \"\",\n    \"flipHorizontal\": \"Apversti horizontaliai\",\n    \"flipVertical\": \"Apversti vertikaliai\",\n    \"viewMode\": \"\",\n    \"share\": \"Dalintis\",\n    \"showStroke\": \"\",\n    \"showBackground\": \"\",\n    \"toggleTheme\": \"\",\n    \"personalLib\": \"Asmeninė biblioteka\",\n    \"excalidrawLib\": \"Exaclidraw biblioteka\",\n    \"decreaseFontSize\": \"\",\n    \"increaseFontSize\": \"\",\n    \"unbindText\": \"\",\n    \"bindText\": \"\",\n    \"createContainerFromText\": \"\",\n    \"link\": {\n      \"edit\": \"Redeguoti nuorodą\",\n      \"editEmbed\": \"\",\n      \"create\": \"Sukurti nuorodą\",\n      \"createEmbed\": \"\",\n      \"label\": \"Nuoroda\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"\",\n      \"exit\": \"\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Užrakinti\",\n      \"unlock\": \"Atrakinti\",\n      \"lockAll\": \"\",\n      \"unlockAll\": \"\"\n    },\n    \"statusPublished\": \"\",\n    \"sidebarLock\": \"\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"\",\n    \"hint_emptyLibrary\": \"\",\n    \"hint_emptyPrivateLibrary\": \"\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"\",\n    \"exportJSON\": \"Eksportuoti į failą\",\n    \"exportImage\": \"\",\n    \"export\": \"\",\n    \"copyToClipboard\": \"Kopijuoti į iškarpinę\",\n    \"save\": \"\",\n    \"saveAs\": \"Išsaugoti kaip\",\n    \"load\": \"\",\n    \"getShareableLink\": \"Gauti nuorodą dalinimuisi\",\n    \"close\": \"Uždaryti\",\n    \"selectLanguage\": \"Pasirinkite kalbą\",\n    \"scrollBackToContent\": \"\",\n    \"zoomIn\": \"Priartinti\",\n    \"zoomOut\": \"Nutolinti\",\n    \"resetZoom\": \"\",\n    \"menu\": \"Meniu\",\n    \"done\": \"\",\n    \"edit\": \"Redaguoti\",\n    \"undo\": \"Anuliuoti\",\n    \"redo\": \"\",\n    \"resetLibrary\": \"Atstatyti biblioteką\",\n    \"createNewRoom\": \"Sukurti naują kambarį\",\n    \"fullScreen\": \"Visas ekranas\",\n    \"darkMode\": \"Tamsus režimas\",\n    \"lightMode\": \"Šviesus režimas\",\n    \"zenMode\": \"„Zen“ režimas\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Išeiti iš „Zen“ režimo\",\n    \"cancel\": \"Atšaukti\",\n    \"clear\": \"Išvalyti\",\n    \"remove\": \"Pašalinti\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Paskelbti\",\n    \"submit\": \"Pateikti\",\n    \"confirm\": \"Patvirtinti\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"\",\n    \"couldNotCreateShareableLink\": \"\",\n    \"couldNotCreateShareableLinkTooBig\": \"\",\n    \"couldNotLoadInvalidFile\": \"\",\n    \"importBackendFailed\": \"\",\n    \"cannotExportEmptyCanvas\": \"\",\n    \"couldNotCopyToClipboard\": \"\",\n    \"decryptFailed\": \"\",\n    \"uploadedSecurly\": \"\",\n    \"loadSceneOverridePrompt\": \"\",\n    \"collabStopOverridePrompt\": \"Sesijos nutraukimas perrašys ankstesnį, lokaliai išsaugotą piešinį. Ar tikrai to nori?\\n\\n(Jei nori išlaikyti lokalų piešinį, tiesiog uždaryk naršyklės skirtuką.)\",\n    \"errorAddingToLibrary\": \"Nepavyko įtraukti elemento į biblioteką\",\n    \"errorRemovingFromLibrary\": \"Nepavyko pašalinti elemento iš bibliotekos\",\n    \"confirmAddLibrary\": \"Tai įtrauks {{numShapes}} figūrą/-as į tavo biblioteką. Ar tikrai to nori?\",\n    \"imageDoesNotContainScene\": \"Panašu, jog šis paveiksliukas neturi scenos duomenų. Ar yra įjuntas scenos įtraukimas ekportavimo metu?\",\n    \"cannotRestoreFromImage\": \"Nepavyko atstatyti scenos iš šio nuotraukos failo\",\n    \"invalidSceneUrl\": \"Nepavyko suimportuoti scenos iš pateiktos nuorodos (URL). Ji arba blogai suformatuota, arba savyje neturi teisingų Excalidraw JSON duomenų.\",\n    \"resetLibrary\": \"Tai išvalys tavo biblioteką. Ar tikrai to nori?\",\n    \"removeItemsFromsLibrary\": \"Ištrinti {{count}} elementą/-us iš bibliotekos?\",\n    \"invalidEncryptionKey\": \"Šifravimo raktas turi būti iš 22 simbolių. Redagavimas gyvai yra išjungtas.\",\n    \"collabOfflineWarning\": \"\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Nepalaikomas failo tipas.\",\n    \"imageInsertError\": \"Nepyko įkelti paveiksliuko. Pabandyk vėliau...\",\n    \"fileTooBig\": \"Per didelis failas. Didžiausias leidžiamas dydis yra {{maxSize}}.\",\n    \"svgImageInsertError\": \"Nepavyko įtraukti SVG paveiksliuko. Panašu, jog SVG yra nevalidus.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"Nevalidus SVG.\",\n    \"cannotResolveCollabServer\": \"Nepavyko prisijungti prie serverio bendradarbiavimui. Perkrauk puslapį ir pabandyk prisijungti dar kartą.\",\n    \"importLibraryError\": \"Nepavyko įkelti bibliotekos\",\n    \"collabSaveFailed\": \"\",\n    \"collabSaveFailed_sizeExceeded\": \"\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Žymėjimas\",\n    \"image\": \"Įkelti paveiksliuką\",\n    \"rectangle\": \"Stačiakampis\",\n    \"diamond\": \"Deimantas\",\n    \"ellipse\": \"Elipsė\",\n    \"arrow\": \"Rodyklė\",\n    \"line\": \"Linija\",\n    \"freedraw\": \"Piešti\",\n    \"text\": \"Tekstas\",\n    \"library\": \"Biblioteka\",\n    \"lock\": \"Baigus piešti, išlaikyti pasirinktą įrankį\",\n    \"penMode\": \"Rašyklio režimas - neleisti prisilietimų\",\n    \"link\": \"Pridėti / Atnaujinti pasirinktos figūros nuorodą\",\n    \"eraser\": \"Trintukas\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Veiksmai su drobe\",\n    \"selectedShapeActions\": \"Veiksmai su pasirinkta figūra\",\n    \"shapes\": \"Figūros\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"\",\n    \"linearElement\": \"Paspaudimai sukurs papildomus taškus, nepertraukiamas tempimas sukurs liniją\",\n    \"freeDraw\": \"Spausk ir tempk, paleisk kai norėsi pabaigti\",\n    \"text\": \"Užuomina: tekstą taip pat galima pridėti bet kur su dvigubu pelės paspaudimu, kol parinkas žymėjimo įrankis\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"\",\n    \"text_editing\": \"\",\n    \"linearElementMulti\": \"\",\n    \"lockAngle\": \"\",\n    \"resize\": \"\",\n    \"resizeImage\": \"\",\n    \"rotate\": \"\",\n    \"lineEditor_info\": \"\",\n    \"lineEditor_pointSelected\": \"\",\n    \"lineEditor_nothingSelected\": \"\",\n    \"placeImage\": \"\",\n    \"publishLibrary\": \"\",\n    \"bindTextToElement\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"eraserRevert\": \"\",\n    \"firefox_clipboard_write\": \"\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"\",\n    \"canvasTooBig\": \"\",\n    \"canvasTooBigTip\": \"\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"\",\n    \"clearCanvasMessage\": \"\",\n    \"clearCanvasCaveat\": \"\",\n    \"trackedToSentry\": \"\",\n    \"openIssueMessage\": \"\",\n    \"sceneContent\": \"\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"\",\n    \"desc_privacy\": \"\",\n    \"button_startSession\": \"Pradėti seansą\",\n    \"button_stopSession\": \"Sustabdyti seansą\",\n    \"desc_inProgressIntro\": \"\",\n    \"desc_shareLink\": \"\",\n    \"desc_exitSession\": \"\",\n    \"shareTitle\": \"\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Klaida\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Įrašyti į diską\",\n    \"disk_details\": \"\",\n    \"disk_button\": \"Įrašyti į failą\",\n    \"link_title\": \"Nuoroda dalinimuisi\",\n    \"link_details\": \"\",\n    \"link_button\": \"\",\n    \"excalidrawplus_description\": \"\",\n    \"excalidrawplus_button\": \"Eksportuoti\",\n    \"excalidrawplus_exportError\": \"\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"\",\n    \"click\": \"paspaudimas\",\n    \"deepSelect\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"curvedArrow\": \"Banguota rodyklė\",\n    \"curvedLine\": \"Banguota linija\",\n    \"documentation\": \"Dokumentacija\",\n    \"doubleClick\": \"dvigubas paspaudimas\",\n    \"drag\": \"vilkti\",\n    \"editor\": \"Redaktorius\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"Radai klaidą? Pateik\",\n    \"howto\": \"Vadovaukis mūsų gidu\",\n    \"or\": \"arba\",\n    \"preventBinding\": \"\",\n    \"tools\": \"Įrankiai\",\n    \"shortcuts\": \"Spartieji klavišai\",\n    \"textFinish\": \"Baigti redagavimą (teksto redaktoriuje)\",\n    \"textNewLine\": \"Pridėti naują eilutę (tekto redaktoriuje)\",\n    \"title\": \"Pagalba\",\n    \"view\": \"\",\n    \"zoomToFit\": \"\",\n    \"zoomToSelection\": \"Priartinti iki pažymėtos vietos\",\n    \"toggleElementLock\": \"\",\n    \"movePageUpDown\": \"Pajudinti puslapį aukštyn/žemyn\",\n    \"movePageLeftRight\": \"Pajudinti puslapį kairėn/dešinėn\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Išvalyti drobę\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Paviešinti biblioteką\",\n    \"itemName\": \"Elemento pavadinimas\",\n    \"authorName\": \"Autoriaus vardas\",\n    \"githubUsername\": \"Github spalyvardis\",\n    \"twitterUsername\": \"Twitter slapyvardis\",\n    \"libraryName\": \"Bibliotekos pavadinimas\",\n    \"libraryDesc\": \"Bibliotekos aprašas\",\n    \"website\": \"Tinklalapis\",\n    \"placeholder\": {\n      \"authorName\": \"Tavo vardas arba spalyvardis\",\n      \"libraryName\": \"Tavo bibliotekos pavadinimas\",\n      \"libraryDesc\": \"Tavo bibliotekos aprašas, padėti žmonėms geriau suprasti jos paskirtį\",\n      \"githubHandle\": \"\",\n      \"twitterHandle\": \"\",\n      \"website\": \"\"\n    },\n    \"errors\": {\n      \"required\": \"Privalomas\",\n      \"website\": \"Įveskite teisingą nuorodą (URL)\"\n    },\n    \"noteDescription\": \"Pateik savo biblioteką, jog ji galėtų būti įtraukta į <link></link>jog kiti žmonės galėtų tai naudoti savo piešiniuose.\",\n    \"noteGuidelines\": \"Visų pirma, biblioteka turi būti rankiniu būdu patvirtinta. Prašome paskaityti <link>gairės</link>\",\n    \"noteLicense\": \"<link>MIT licencija, </link>\",\n    \"noteItems\": \"\",\n    \"atleastOneLibItem\": \"\",\n    \"republishWarning\": \"\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Biblioteka pateikta\",\n    \"content\": \"Ačiū {{authorName}}. Tavo biblioteka buvo pateikta peržiūrai. Gali sekti būseną<link>čia</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Atstatyti biblioteką\",\n    \"removeItemsFromLib\": \"Pašalinti pasirinktus elementus iš bibliotekos\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"\",\n    \"link\": \"\"\n  },\n  \"stats\": {\n    \"angle\": \"\",\n    \"element\": \"Elementas\",\n    \"elements\": \"Elementai\",\n    \"height\": \"Aukštis\",\n    \"scene\": \"Scena\",\n    \"selected\": \"Pasirinkta\",\n    \"storage\": \"Saugykla\",\n    \"title\": \"Informacija moksliukams\",\n    \"total\": \"\",\n    \"version\": \"\",\n    \"versionCopy\": \"\",\n    \"versionNotAvailable\": \"\",\n    \"width\": \"Plotis\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Pridėta į biblioteką\",\n    \"copyStyles\": \"\",\n    \"copyToClipboard\": \"Nukopijuota į iškarpinę.\",\n    \"copyToClipboardAsPng\": \"\",\n    \"fileSaved\": \"Failas išsaugotas.\",\n    \"fileSavedToFilename\": \"Išsaugota į {filename}\",\n    \"canvas\": \"drobė\",\n    \"selection\": \"\",\n    \"pasteAsSingleElement\": \"\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Permatoma\",\n    \"black\": \"\",\n    \"white\": \"\",\n    \"red\": \"\",\n    \"pink\": \"\",\n    \"grape\": \"\",\n    \"violet\": \"\",\n    \"gray\": \"\",\n    \"blue\": \"\",\n    \"cyan\": \"\",\n    \"teal\": \"\",\n    \"green\": \"\",\n    \"yellow\": \"\",\n    \"orange\": \"\",\n    \"bronze\": \"\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"\",\n      \"center_heading_plus\": \"\",\n      \"menuHint\": \"\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"\",\n      \"center_heading\": \"\",\n      \"toolbarHint\": \"\",\n      \"helpHint\": \"\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"\",\n    \"shades\": \"\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}