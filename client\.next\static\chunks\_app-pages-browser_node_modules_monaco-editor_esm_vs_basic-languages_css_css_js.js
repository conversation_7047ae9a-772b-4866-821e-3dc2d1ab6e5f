"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_css_css_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/css/css.js":
/*!***********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/css/css.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/css/css.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|((::|[@#.!:])?[\\w-?]+%?)|::|[@#.!:]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".css\",\n  ws: \"[ \t\\n\\r\\f]*\",\n  // whitespaces (referenced in several rules)\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.bracket\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [{ include: \"@selector\" }],\n    selector: [\n      { include: \"@comments\" },\n      { include: \"@import\" },\n      { include: \"@strings\" },\n      [\n        \"[@](keyframes|-webkit-keyframes|-moz-keyframes|-o-keyframes)\",\n        { token: \"keyword\", next: \"@keyframedeclaration\" }\n      ],\n      [\"[@](page|content|font-face|-moz-document)\", { token: \"keyword\" }],\n      [\"[@](charset|namespace)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\n        \"(url-prefix)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      [\n        \"(url)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      { include: \"@selectorname\" },\n      [\"[\\\\*]\", \"tag\"],\n      // selector symbols\n      [\"[>\\\\+,]\", \"delimiter\"],\n      // selector operators\n      [\"\\\\[\", { token: \"delimiter.bracket\", next: \"@selectorattribute\" }],\n      [\"{\", { token: \"delimiter.bracket\", next: \"@selectorbody\" }]\n    ],\n    selectorbody: [\n      { include: \"@comments\" },\n      [\"[*_]?@identifier@ws:(?=(\\\\s|\\\\d|[^{;}]*[;}]))\", \"attribute.name\", \"@rulevalue\"],\n      // rule definition: to distinguish from a nested selector check for whitespace, number or a semicolon\n      [\"}\", { token: \"delimiter.bracket\", next: \"@pop\" }]\n    ],\n    selectorname: [\n      [\"(\\\\.|#(?=[^{])|%|(@identifier)|:)+\", \"tag\"]\n      // selector (.foo, div, ...)\n    ],\n    selectorattribute: [{ include: \"@term\" }, [\"]\", { token: \"delimiter.bracket\", next: \"@pop\" }]],\n    term: [\n      { include: \"@comments\" },\n      [\n        \"(url-prefix)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      [\n        \"(url)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      { include: \"@functioninvocation\" },\n      { include: \"@numbers\" },\n      { include: \"@name\" },\n      { include: \"@strings\" },\n      [\"([<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~,])\", \"delimiter\"],\n      [\",\", \"delimiter\"]\n    ],\n    rulevalue: [\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      { include: \"@term\" },\n      [\"!important\", \"keyword\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    warndebug: [[\"[@](warn|debug)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    import: [[\"[@](import)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    parenthizedterm: [\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    declarationbody: [\n      { include: \"@term\" },\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [/[^*/]+/, \"comment\"],\n      [/./, \"comment\"]\n    ],\n    name: [[\"@identifier\", \"attribute.value\"]],\n    numbers: [\n      [\"-?(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"attribute.value.number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"attribute.value.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"attribute.value.unit\",\n        \"@pop\"\n      ]\n    ],\n    keyframedeclaration: [\n      [\"@identifier\", \"attribute.value\"],\n      [\"{\", { token: \"delimiter.bracket\", switchTo: \"@keyframebody\" }]\n    ],\n    keyframebody: [\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.bracket\", next: \"@selectorbody\" }],\n      [\"}\", { token: \"delimiter.bracket\", next: \"@pop\" }]\n    ],\n    functioninvocation: [\n      [\"@identifier\\\\(\", { token: \"attribute.value\", next: \"@functionarguments\" }]\n    ],\n    functionarguments: [\n      [\"\\\\$@identifier@ws:\", \"attribute.name\"],\n      [\"[,]\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"attribute.value\", next: \"@pop\" }]\n    ],\n    strings: [\n      ['~?\"', { token: \"string\", next: \"@stringenddoublequote\" }],\n      [\"~?'\", { token: \"string\", next: \"@stringendquote\" }]\n    ],\n    stringenddoublequote: [\n      [\"\\\\\\\\.\", \"string\"],\n      ['\"', { token: \"string\", next: \"@pop\" }],\n      [/[^\\\\\"]+/, \"string\"],\n      [\".\", \"string\"]\n    ],\n    stringendquote: [\n      [\"\\\\\\\\.\", \"string\"],\n      [\"'\", { token: \"string\", next: \"@pop\" }],\n      [/[^\\\\']+/, \"string\"],\n      [\".\", \"string\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/css/css.js\n"));

/***/ })

}]);