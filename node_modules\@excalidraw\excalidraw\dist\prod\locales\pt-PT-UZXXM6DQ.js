import"../chunk-SRAX5OIU.js";var o={paste:"Colar",pasteAsPlaintext:"Colar como texto simples",pasteCharts:"Colar gr\xE1ficos",selectAll:"Selecionar tudo",multiSelect:"Adicionar elemento \xE0 sele\xE7\xE3o",moveCanvas:"Mover tela",cut:"Cortar",copy:"Copiar",copyAsPng:"Copiar para a \xE1rea de transfer\xEAncia como PNG",copyAsSvg:"Copiar para a \xE1rea de transfer\xEAncia como SVG",copyText:"Copiar para \xC1rea de Transfer\xEAncia como texto",copySource:"",convertToCode:"",bringForward:"Trazer para o primeiro plano",sendToBack:"Enviar para o plano de fundo",bringToFront:"Trazer para o primeiro plano",sendBackward:"Enviar para tr\xE1s",delete:"Apagar",copyStyles:"Copiar os estilos",pasteStyles:"Colar os estilos",stroke:"Contor<PERSON>",background:"Fundo",fill:"Preenchimento",strokeWidth:"Espessura do tra\xE7o",strokeStyle:"Estilo de tra\xE7o",strokeStyle_solid:"S\xF3lido",strokeStyle_dashed:"Tracejado",strokeStyle_dotted:"Pontilhado",sloppiness:"Desleixo",opacity:"Opacidade",textAlign:"Alinhamento do texto",edges:"Arestas",sharp:"Agu\xE7ado",round:"Redondo",arrowheads:"Pontas",arrowhead_none:"Nenhuma",arrowhead_arrow:"Seta",arrowhead_bar:"Barra",arrowhead_circle:"",arrowhead_circle_outline:"",arrowhead_triangle:"Tri\xE2ngulo",arrowhead_triangle_outline:"",arrowhead_diamond:"",arrowhead_diamond_outline:"",fontSize:"Tamanho da fonte",fontFamily:"Fam\xEDlia da fontes",addWatermark:'Adicionar "Feito com Excalidraw"',handDrawn:"Manuscrito",normal:"Normal",code:"C\xF3digo",small:"Pequeno",medium:"M\xE9dio",large:"Grande",veryLarge:"Muito grande",solid:"S\xF3lido",hachure:"Eclos\xE3o",zigzag:"ziguezague",crossHatch:"Sombreado",thin:"Fino",bold:"Espesso",left:"Esquerda",center:"Centralizar",right:"Direita",extraBold:"Muito espesso",architect:"Arquitecto",artist:"Artista",cartoonist:"Caricaturista",fileTitle:"Nome do ficheiro",colorPicker:"Seletor de cores",canvasColors:"Usado na tela",canvasBackground:"Fundo da \xE1rea de desenho",drawingCanvas:"\xC1rea de desenho",layers:"Camadas",actions:"A\xE7\xF5es",language:"Idioma",liveCollaboration:"Colabora\xE7\xE3o ao vivo...",duplicateSelection:"Duplicar",untitled:"Sem t\xEDtulo",name:"Nome",yourName:"O seu nome",madeWithExcalidraw:"Feito com Excalidraw",group:"Agrupar sele\xE7\xE3o",ungroup:"Desagrupar sele\xE7\xE3o",collaborators:"Colaboradores",showGrid:"Mostrar grelha",addToLibrary:"Adicionar \xE0 biblioteca",removeFromLibrary:"Remover da biblioteca",libraryLoadingMessage:"A carregar a biblioteca\u2026",libraries:"Procurar bibliotecas",loadingScene:"A carregar a cena\u2026",align:"Alinhamento",alignTop:"Alinhar ao topo",alignBottom:"Alinhar ao fundo",alignLeft:"Alinhar \xE0 esquerda",alignRight:"Alinhar \xE0 direita",centerVertically:"Centrar verticalmente",centerHorizontally:"Centrar horizontalmente",distributeHorizontally:"Distribuir horizontalmente",distributeVertically:"Distribuir verticalmente",flipHorizontal:"Inverter horizontalmente",flipVertical:"Inverter verticalmente",viewMode:"Modo de visualiza\xE7\xE3o",share:"Partilhar",showStroke:"Mostrar seletor de cores do tra\xE7o",showBackground:"Mostrar seletor de cores do fundo",toggleTheme:"Alternar tema",personalLib:"Biblioteca pessoal",excalidrawLib:"Biblioteca do Excalidraw",decreaseFontSize:"Reduzir o tamanho do tipo de letra",increaseFontSize:"Aumentar o tamanho do tipo de letra",unbindText:"Desvincular texto",bindText:"Ligar texto ao recipiente",createContainerFromText:"Envolver texto num recipiente",link:{edit:"Editar liga\xE7\xE3o",editEmbed:"",create:"Criar liga\xE7\xE3o",createEmbed:"",label:"Liga\xE7\xE3o",labelEmbed:"",empty:""},lineEditor:{edit:"Editar linha",exit:"Sair do editor de linha"},elementLock:{lock:"Bloquear",unlock:"Desbloquear",lockAll:"Bloquear todos",unlockAll:"Desbloquear todos"},statusPublished:"Publicado",sidebarLock:"Manter a barra lateral aberta",selectAllElementsInFrame:"",removeAllElementsFromFrame:"",eyeDropper:"",textToDiagram:"",prompt:""},r={noItems:"Ainda n\xE3o foram adicionados nenhuns itens...",hint_emptyLibrary:"Seleccione um item na tela para adicion\xE1-lo aqui, ou ent\xE3o instale uma biblioteca do reposit\xF3rio p\xFAblico abaixo.",hint_emptyPrivateLibrary:"Seleccione um item na tela para adicion\xE1-lo aqui."},i={clearReset:"Limpar a \xE1rea de desenho e redefinir a cor de fundo",exportJSON:"Exportar para ficheiro",exportImage:"Exportar imagem...",export:"Guardar para...",copyToClipboard:"Copiar para o clipboard",save:"Guardar no ficheiro atual",saveAs:"Guardar como",load:"Abrir",getShareableLink:"Obter um link de partilha",close:"Fechar",selectLanguage:"Selecionar idioma",scrollBackToContent:"Voltar ao conte\xFAdo",zoomIn:"Aumentar zoom",zoomOut:"Diminuir zoom",resetZoom:"Redefinir zoom",menu:"Menu",done:"Conclu\xEDdo",edit:"Editar",undo:"Desfazer",redo:"Refazer",resetLibrary:"Repor a biblioteca",createNewRoom:"Criar nova sala",fullScreen:"Ecr\xE3 inteiro",darkMode:"Modo escuro",lightMode:"Modo claro",zenMode:"Modo zen",objectsSnapMode:"",exitZenMode:"Sair do modo zen",cancel:"Cancelar",clear:"Limpar",remove:"Remover",embed:"",publishLibrary:"Publicar",submit:"Enviar",confirm:"Confirmar",embeddableInteractionButton:""},t={clearReset:"Isto ir\xE1 limpar toda a \xE1rea de desenho. Tem a certeza?",couldNotCreateShareableLink:"N\xE3o foi poss\xEDvel criar um link partilh\xE1vel.",couldNotCreateShareableLinkTooBig:"N\xE3o foi poss\xEDvel criar um link partilh\xE1vel: a cena \xE9 muito grande",couldNotLoadInvalidFile:"N\xE3o foi poss\xEDvel carregar o ficheiro inv\xE1lido",importBackendFailed:"A importa\xE7\xE3o do servidor falhou.",cannotExportEmptyCanvas:"N\xE3o \xE9 poss\xEDvel exportar uma \xE1rea de desenho vazia.",couldNotCopyToClipboard:"N\xE3o foi poss\xEDvel copiar para a \xE1rea de transfer\xEAncia.",decryptFailed:"N\xE3o foi poss\xEDvel desencriptar os dados.",uploadedSecurly:"O upload foi protegido com criptografia de ponta a ponta, o que significa que o servidor do Excalidraw e terceiros n\xE3o podem ler o conte\xFAdo.",loadSceneOverridePrompt:"Se carregar um desenho externo substituir\xE1 o conte\xFAdo existente. Quer continuar?",collabStopOverridePrompt:`Ao interromper a sess\xE3o ir\xE1 substituir o \xFAltimo desenho guardado. Tem a certeza?

(Caso queira manter o \xFAltimo desenho, simplesmente feche a janela do navegador.)`,errorAddingToLibrary:"N\xE3o foi poss\xEDvel adicionar o item \xE0 biblioteca",errorRemovingFromLibrary:"N\xE3o foi poss\xEDvel remover o item da biblioteca",confirmAddLibrary:"Isso adicionar\xE1 {{numShapes}} forma(s) \xE0 sua biblioteca. Tem a certeza?",imageDoesNotContainScene:"Esta imagem parece n\xE3o conter dados de cenas. Ativou a incorpora\xE7\xE3o da cena durante a exporta\xE7\xE3o?",cannotRestoreFromImage:"N\xE3o foi poss\xEDvel restaurar a cena deste ficheiro de imagem",invalidSceneUrl:"N\xE3o foi poss\xEDvel importar a cena a partir do URL fornecido. Ou est\xE1 mal formado ou n\xE3o cont\xE9m dados JSON do Excalidraw v\xE1lidos.",resetLibrary:"Isto ir\xE1 limpar a sua biblioteca. Tem a certeza?",removeItemsFromsLibrary:"Apagar {{count}} item(ns) da biblioteca?",invalidEncryptionKey:"Chave de encripta\xE7\xE3o deve ter 22 caracteres. A colabora\xE7\xE3o ao vivo est\xE1 desativada.",collabOfflineWarning:`Sem liga\xE7\xE3o \xE0 internet dispon\xEDvel.
As suas altera\xE7\xF5es n\xE3o ser\xE3o salvas!`},n={unsupportedFileType:"Tipo de ficheiro n\xE3o suportado.",imageInsertError:"N\xE3o foi poss\xEDvel inserir a imagem, tente novamente mais tarde...",fileTooBig:"O ficheiro \xE9 muito grande. O tamanho m\xE1ximo permitido \xE9 {{maxSize}}.",svgImageInsertError:"N\xE3o foi poss\xEDvel inserir a imagem SVG. A marca\xE7\xE3o SVG parece inv\xE1lida.",failedToFetchImage:"",invalidSVGString:"SVG inv\xE1lido.",cannotResolveCollabServer:"N\xE3o foi poss\xEDvel fazer a liga\xE7\xE3o ao servidor colaborativo. Por favor, volte a carregar a p\xE1gina e tente novamente.",importLibraryError:"N\xE3o foi poss\xEDvel carregar a biblioteca",collabSaveFailed:"N\xE3o foi poss\xEDvel guardar na base de dados de backend. Se os problemas persistirem, guarde o ficheiro localmente para garantir que n\xE3o perde o seu trabalho.",collabSaveFailed_sizeExceeded:"N\xE3o foi poss\xEDvel guardar na base de dados de backend, o ecr\xE3 parece estar muito grande. Deve guardar o ficheiro localmente para garantir que n\xE3o perde o seu trabalho.",imageToolNotSupported:"",brave_measure_text_error:{line1:"",line2:"",line3:"",line4:""},libraryElementTypeError:{embeddable:"",iframe:"",image:""},asyncPasteFailedOnRead:"",asyncPasteFailedOnParse:"",copyToSystemClipboardFailed:""},s={selection:"Sele\xE7\xE3o",image:"Inserir imagem",rectangle:"Ret\xE2ngulo",diamond:"Losango",ellipse:"Elipse",arrow:"Flecha",line:"Linha",freedraw:"Desenhar",text:"Texto",library:"Biblioteca",lock:"Manter a ferramenta selecionada ativa ap\xF3s desenhar",penMode:"Modo caneta - impedir toque",link:"Acrescentar/ Adicionar liga\xE7\xE3o para uma forma seleccionada",eraser:"Borracha",frame:"",magicframe:"",embeddable:"",laser:"",hand:"M\xE3o (ferramenta de movimento da tela)",extraTools:"",mermaidToExcalidraw:"",magicSettings:""},l={canvasActions:"A\xE7\xF5es da \xE1rea de desenho",selectedShapeActions:"A\xE7\xF5es das formas selecionadas",shapes:"Formas"},d={canvasPanning:"Para mover a tela, carregue na roda do rato ou na barra de espa\xE7o enquanto arrasta, ou use a ferramenta da m\xE3o",linearElement:"Clique para iniciar v\xE1rios pontos, arraste para uma \xFAnica linha",freeDraw:"Clique e arraste, large quando terminar",text:"Dica: tamb\xE9m pode adicionar texto clicando duas vezes em qualquer lugar com a ferramenta de sele\xE7\xE3o",embeddable:"",text_selected:"Clique duas vezes ou pressione a tecla Enter para editar o texto",text_editing:"Pressione a tecla Escape ou CtrlOrCmd+ENTER para terminar a edi\xE7\xE3o",linearElementMulti:"Clique no \xFAltimo ponto ou pressione Escape ou Enter para terminar",lockAngle:"Pode restringir o \xE2ngulo mantendo premida a tecla SHIFT",resize:`Pode restringir as propor\xE7\xF5es mantendo a tecla SHIFT premida enquanto redimensiona,
mantenha a tecla ALT premida para redimensionar a partir do centro`,resizeImage:`Pode redimensionar livremente mantendo pressionada a tecla SHIFT,
mantenha pressionada a tecla ALT para redimensionar do centro`,rotate:"Pode restringir os \xE2ngulos mantendo a tecla SHIFT premida enquanto roda",lineEditor_info:"Pressione CtrlOrCmd e fa\xE7a um duplo-clique ou pressione CtrlOrCmd + Enter para editar pontos",lineEditor_pointSelected:"Carregue na tecla Delete para remover o(s) ponto(s), CtrlOuCmd+D para duplicar, ou arraste para mover",lineEditor_nothingSelected:`Seleccione um ponto para editar (carregue em SHIFT para seleccionar v\xE1rios),
ou carregue em Alt e clique para acrescentar novos pontos`,placeImage:"Clique para colocar a imagem ou clique e arraste para definir o seu tamanho manualmente",publishLibrary:"Publique a sua pr\xF3pria biblioteca",bindTextToElement:"Carregue Enter para acrescentar texto",deepBoxSelect:"Mantenha a tecla CtrlOrCmd carregada para selec\xE7\xE3o profunda, impedindo o arrastamento",eraserRevert:"Carregue tamb\xE9m em Alt para reverter os elementos marcados para serem apagados",firefox_clipboard_write:'Esta fun\xE7\xE3o pode provavelmente ser ativada definindo a op\xE7\xE3o "dom.events.asyncClipboard.clipboardItem" como "true". Para alterar os sinalizadores do navegador no Firefox, visite a p\xE1gina "about:config".',disableSnapping:""},c={cannotShowPreview:"N\xE3o \xE9 poss\xEDvel mostrar uma pr\xE9-visualiza\xE7\xE3o",canvasTooBig:"A \xE1rea de desenho pode ser muito grande.",canvasTooBigTip:"Dica: tente aproximar um pouco os elementos mais distantes."},m={headingMain:"Foi encontrado um erro. Tente <button>recarregar a p\xE1gina.</button>",clearCanvasMessage:"Se a recarga n\xE3o funcionar, tente <button>a limpar a \xE1rea de desenho.</button>",clearCanvasCaveat:" Isso resultar\xE1 em perda de trabalho ",trackedToSentry:"O erro com o identificador {{eventId}} foi rastreado no nosso sistema.",openIssueMessage:"Fomos muito cautelosos para n\xE3o incluir suas informa\xE7\xF5es de cena no erro. Se sua cena n\xE3o for privada, por favor, considere seguir nosso <button>rastreador de bugs.</button> Por favor, inclua informa\xE7\xF5es abaixo, copiando e colando no relat\xF3rio de erros no GitHub.",sceneContent:"Conte\xFAdo da cena:"},p={desc_intro:"Pode convidar pessoas para colaborarem na sua cena atual.",desc_privacy:"N\xE3o se preocupe, a sess\xE3o usa criptografia de ponta-a-ponta, por isso o que desenhar permanecer\xE1 privado. Nem mesmo o nosso servidor poder\xE1 ver o que cria.",button_startSession:"Iniciar sess\xE3o",button_stopSession:"Parar sess\xE3o",desc_inProgressIntro:"A sess\xE3o de colabora\xE7\xE3o ao vivo est\xE1 agora em andamento.",desc_shareLink:"Partilhe este link com qualquer pessoa com quem queira colaborar:",desc_exitSession:"Interrompendo a sess\xE3o ir\xE1 desconectar-se da sala, mas poder\xE1 continuar a trabalhar com a cena localmente. Note que isso n\xE3o afetar\xE1 outras pessoas e elas ainda poder\xE3o colaborar nas vers\xF5es deles.",shareTitle:"Participe numa sess\xE3o de colabora\xE7\xE3o ao vivo no Excalidraw"},u={title:"Erro"},b={disk_title:"Guardar no disco",disk_details:"Exportar os dados da cena para um ficheiro do qual poder\xE1 importar mais tarde.",disk_button:"Guardar num ficheiro",link_title:"Link partilh\xE1vel",link_details:"Exportar como um link de apenas leitura.",link_button:"Exportar para link",excalidrawplus_description:"Guardar a cena no seu espa\xE7o de trabalho Excalidraw+",excalidrawplus_button:"Exportar",excalidrawplus_exportError:"N\xE3o foi poss\xEDvel exportar para o Excalidraw+ neste momento..."},g={blog:"Leia o nosso blogue",click:"clicar",deepSelect:"Selec\xE7\xE3o profunda",deepBoxSelect:"Selec\xE7\xE3o profunda dentro da caixa, impedindo que seja arrastada",curvedArrow:"Seta curva",curvedLine:"Linha curva",documentation:"Documenta\xE7\xE3o",doubleClick:"clique duplo",drag:"arrastar",editor:"Editor",editLineArrowPoints:"Editar pontos de linha/seta",editText:"Editar texto / adicionar etiqueta",github:"Encontrou algum problema? Informe-nos",howto:"Siga os nossos guias",or:"ou",preventBinding:"Prevenir fixa\xE7\xE3o de seta",tools:"Ferramentas",shortcuts:"Atalhos de teclado",textFinish:"Finalizar edi\xE7\xE3o (editor texto)",textNewLine:"Adicionar nova linha (editor de texto)",title:"Ajuda",view:"Visualizar",zoomToFit:"Ajustar para todos os elementos caberem",zoomToSelection:"Ampliar a sele\xE7\xE3o",toggleElementLock:"Trancar/destrancar selec\xE7\xE3o",movePageUpDown:"Mover p\xE1gina para cima / baixo",movePageLeftRight:"Mover p\xE1gina para esquerda / direita"},v={title:"Apagar tela"},h={title:"Publicar biblioteca",itemName:"Nome do item",authorName:"Nome do autor",githubUsername:"Nome de utilizador do GitHub",twitterUsername:"Nome de utilizador no Twitter",libraryName:"Nome da biblioteca",libraryDesc:"Descri\xE7\xE3o da biblioteca",website:"P\xE1gina web",placeholder:{authorName:"Introduza o seu nome ou nome de utilizador",libraryName:"Nome da sua biblioteca",libraryDesc:"Descri\xE7\xE3o da sua biblioteca para ajudar as pessoas a entender a utiliza\xE7\xE3o dela",githubHandle:"Identificador do GitHub (opcional), para que possa editar a biblioteca depois desta ser enviada para revis\xE3o",twitterHandle:"Nome do Twitter (opcional), para que saibamos quem merece os cr\xE9ditos na promo\xE7\xE3o via Twitter",website:"Liga\xE7\xE3o para a sua p\xE1gina pessoal ou qualquer outra (opcional)"},errors:{required:"Obrigat\xF3rio",website:"Introduza um URL v\xE1lido"},noteDescription:"Envie a sua biblioteca para ser inclu\xEDda no <link>reposit\xF3rio de bibliotecas p\xFAblicas</link>para outras pessoas a poderem usar nos seus pr\xF3prios desenhos.",noteGuidelines:"A biblioteca precisa ser aprovada manualmente primeiro. Por favor, leia <link>orienta\xE7\xF5es</link> antes de enviar. Vai precisar de uma conta no GitHub para comunicar e fazer altera\xE7\xF5es se solicitado, mas n\xE3o \xE9 estritamente necess\xE1ria.",noteLicense:"Ao enviar, concorda que a biblioteca ser\xE1 publicada sob a <link>Licen\xE7a MIT, </link>o que significa, de forma resumida, que qualquer pessoa pode utiliz\xE1-la sem restri\xE7\xF5es.",noteItems:"Cada item da biblioteca deve ter o seu pr\xF3prio nome para que este seja pesquis\xE1vel com filtros. Os seguintes itens da biblioteca ser\xE3o inclu\xEDdos:",atleastOneLibItem:"Por favor, seleccione pelo menos um item da biblioteca para come\xE7ar",republishWarning:"Nota: alguns dos itens seleccionados est\xE3o marcados como j\xE1 publicados/enviados. S\xF3 deve reenviar itens ao actualizar uma biblioteca existente ou submiss\xE3o."},f={title:"Biblioteca enviada",content:"Obrigado {{authorName}}. A sua biblioteca foi enviada para an\xE1lise. Pode acompanhar o status<link>aqui</link>"},S={resetLibrary:"Repor a biblioteca",removeItemsFromLib:"Remover os itens seleccionados da biblioteca"},x={header:"Exportar imagem",label:{withBackground:"",onlySelected:"",darkMode:"",embedScene:"Cena embutida",scale:"",padding:"Espa\xE7amento"},tooltip:{embedScene:""},title:{exportToPng:"Exportar em PNG",exportToSvg:"Exportar em SVG",copyPngToClipboard:""},button:{exportToPng:"PNG",exportToSvg:"SVG",copyPngToClipboard:""}},C={tooltip:"Os seus desenhos s\xE3o encriptados de ponta-a-ponta, por isso os servidores do Excalidraw nunca os ver\xE3o.",link:"Publica\xE7\xE3o de blogue na encripta\xE7\xE3o ponta-a-ponta no Excalidraw"},E={angle:"\xC2ngulo",element:"Elemento",elements:"Elementos",height:"Altura",scene:"Cena",selected:"Selecionado",storage:"Armazenamento",title:"Estat\xEDsticas para nerds",total:"Total",version:"Vers\xE3o",versionCopy:"Clique para copiar",versionNotAvailable:"Vers\xE3o n\xE3o dispon\xEDvel",width:"Largura"},T={addedToLibrary:"Acrescentado \xE0 biblioteca",copyStyles:"Estilos copiados.",copyToClipboard:"Copiado para a \xE1rea de transfer\xEAncia.",copyToClipboardAsPng:`{{exportSelection}} copiado para a \xE1rea de transfer\xEAncia como PNG
({{exportColorScheme}})`,fileSaved:"Ficheiro guardado.",fileSavedToFilename:"Guardado como {filename}",canvas:"\xE1rea de desenho",selection:"sele\xE7\xE3o",pasteAsSingleElement:`Usar {{shortcut}} para colar como um \xFAnico elemento,
ou colar num editor de texto existente`,unableToEmbed:"",unrecognizedLinkFormat:""},y={transparent:"Transparente",black:"Preto",white:"Branco",red:"Vermelho",pink:"Rosa",grape:"Uva",violet:"Violeta",gray:"Cinza",blue:"Azul",cyan:"",teal:"",green:"Verde",yellow:"Amarelo",orange:"Laranja",bronze:"Bronze"},A={app:{center_heading:"Todos os dados s\xE3o guardados no seu navegador local.",center_heading_plus:"Queria antes ir para o Excalidraw+?",menuHint:"Exportar, prefer\xEAncias, idiomas..."},defaults:{menuHint:"Exportar, prefer\xEAncias e outros...",center_heading:"Diagramas. Feito. Simples.",toolbarHint:"Escolha uma ferramenta e comece a desenhar!",helpHint:"Atalhos e ajuda"}},k={mostUsedCustomColors:"",colors:"Cores",shades:"Tons",hexCode:"",noShades:""},w={action:{exportToImage:{title:"",button:"",description:""},saveToDisk:{title:"Guardar no disco",button:"Guardar no disco",description:""},excalidrawPlus:{title:"",button:"",description:""}},modal:{loadFromFile:{title:"Carregar a partir de ficheiro",button:"Carregar a partir de ficheiro",description:""},shareableLink:{title:"",button:"",description:""}}},z={title:"",button:"",description:"",syntax:"",preview:""},q={labels:o,library:r,buttons:i,alerts:t,errors:n,toolBar:s,headings:l,hints:d,canvasError:c,errorSplash:m,roomDialog:p,errorDialog:u,exportDialog:b,helpDialog:g,clearCanvasDialog:v,publishDialog:h,publishSuccessDialog:f,confirmDialog:S,imageExportDialog:x,encrypted:C,stats:E,toast:T,colors:y,welcomeScreen:A,colorPicker:k,overwriteConfirm:w,mermaid:z};export{t as alerts,i as buttons,c as canvasError,v as clearCanvasDialog,k as colorPicker,y as colors,S as confirmDialog,q as default,C as encrypted,u as errorDialog,m as errorSplash,n as errors,b as exportDialog,l as headings,g as helpDialog,d as hints,x as imageExportDialog,o as labels,r as library,z as mermaid,w as overwriteConfirm,h as publishDialog,f as publishSuccessDialog,p as roomDialog,E as stats,T as toast,s as toolBar,A as welcomeScreen};
