{"version": 3, "sources": ["../../../locales/th-TH.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"วาง\",\n    \"pasteAsPlaintext\": \"วางโดยไม่มีการจัดรูปแบบ\",\n    \"pasteCharts\": \"วางแผนภูมิ\",\n    \"selectAll\": \"เลือกทั้งหมด\",\n    \"multiSelect\": \"\",\n    \"moveCanvas\": \"\",\n    \"cut\": \"ตัด\",\n    \"copy\": \"คัดลอก\",\n    \"copyAsPng\": \"คัดลองไปยังคลิปบอร์ดเป็น PNG\",\n    \"copyAsSvg\": \"คัดลองไปยังคลิปบอร์ดเป็น SVG\",\n    \"copyText\": \"คัดลองไปยังคลิปบอร์ดเป็นข้อความ\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"นำขึ้นข้างบน\",\n    \"sendToBack\": \"ย้ายไปข้างล่าง\",\n    \"bringToFront\": \"นำขึ้นข้างหน้า\",\n    \"sendBackward\": \"ย้ายไปข้างหลัง\",\n    \"delete\": \"ลบ\",\n    \"copyStyles\": \"คัดลอกรูปแบบ\",\n    \"pasteStyles\": \"วางรูปแบบ\",\n    \"stroke\": \"เส้นขอบ\",\n    \"background\": \"พื้นหลัง\",\n    \"fill\": \"เติมสี\",\n    \"strokeWidth\": \"น้ำหนักเส้นขอบ\",\n    \"strokeStyle\": \"รูปแบบเส้น\",\n    \"strokeStyle_solid\": \"เส้นทึบ\",\n    \"strokeStyle_dashed\": \"เส้นประ\",\n    \"strokeStyle_dotted\": \"จุด\",\n    \"sloppiness\": \"ความเลอะเทอะ\",\n    \"opacity\": \"ความทึบแสง\",\n    \"textAlign\": \"จัดข้อความ\",\n    \"edges\": \"ขอบ\",\n    \"sharp\": \"\",\n    \"round\": \"\",\n    \"arrowheads\": \"หัวลูกศร\",\n    \"arrowhead_none\": \"ไม่มี\",\n    \"arrowhead_arrow\": \"ลูกศร\",\n    \"arrowhead_bar\": \"แถบ\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"สามเหลี่ยม\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"ขนาดตัวอักษร\",\n    \"fontFamily\": \"แบบตัวอักษร\",\n    \"addWatermark\": \"เพิ่มลายน้ำ \\\"สร้างด้วย Excalidraw\\\"\",\n    \"handDrawn\": \"ลายมือ\",\n    \"normal\": \"ปกติ\",\n    \"code\": \"โค้ด\",\n    \"small\": \"เล็ก\",\n    \"medium\": \"กลาง\",\n    \"large\": \"ใหญ่\",\n    \"veryLarge\": \"ใหญ่มาก\",\n    \"solid\": \"\",\n    \"hachure\": \"\",\n    \"zigzag\": \"\",\n    \"crossHatch\": \"\",\n    \"thin\": \"บาง\",\n    \"bold\": \"หนา\",\n    \"left\": \"ซ้าย\",\n    \"center\": \"กลาง\",\n    \"right\": \"ขวา\",\n    \"extraBold\": \"หนาพิเศษ\",\n    \"architect\": \"\",\n    \"artist\": \"ศิลปิน\",\n    \"cartoonist\": \"\",\n    \"fileTitle\": \"ชื่อไฟล์\",\n    \"colorPicker\": \"เลือกสีที่กำหนดเอง\",\n    \"canvasColors\": \"\",\n    \"canvasBackground\": \"\",\n    \"drawingCanvas\": \"\",\n    \"layers\": \"\",\n    \"actions\": \"การกระทำ\",\n    \"language\": \"ภาษา\",\n    \"liveCollaboration\": \"\",\n    \"duplicateSelection\": \"ทำสำเนา\",\n    \"untitled\": \"ไม่มีชื่อ\",\n    \"name\": \"ชื่อ\",\n    \"yourName\": \"ชื่อของคุณ\",\n    \"madeWithExcalidraw\": \"\",\n    \"group\": \"จัดกลุ่ม\",\n    \"ungroup\": \"ยกเลิกการจัดกลุ่ม\",\n    \"collaborators\": \"\",\n    \"showGrid\": \"แสดงเส้นตาราง\",\n    \"addToLibrary\": \"เพิ่มไปในคลัง\",\n    \"removeFromLibrary\": \"นำออกจากคลัง\",\n    \"libraryLoadingMessage\": \"กำลังโหลดคลัง...\",\n    \"libraries\": \"\",\n    \"loadingScene\": \"กำลังโหลดฉาก\",\n    \"align\": \"จัดตำแหน่ง\",\n    \"alignTop\": \"จัดชิดด้านบน\",\n    \"alignBottom\": \"จัดชิดด้านล่าง\",\n    \"alignLeft\": \"จัดชิดซ้าย\",\n    \"alignRight\": \"จัดชิดขวา\",\n    \"centerVertically\": \"กึ่งกลางแนวตั้ง\",\n    \"centerHorizontally\": \"กึ่งกลางแนวนอน\",\n    \"distributeHorizontally\": \"กระจายแนวนอน\",\n    \"distributeVertically\": \"กระจายแนวตั้ง\",\n    \"flipHorizontal\": \"พลิกแนวนอน\",\n    \"flipVertical\": \"พลิกแนวตั้ง\",\n    \"viewMode\": \"โหมดมุมมอง\",\n    \"share\": \"แชร์\",\n    \"showStroke\": \"\",\n    \"showBackground\": \"\",\n    \"toggleTheme\": \"สลับธีม\",\n    \"personalLib\": \"คลังของฉัน\",\n    \"excalidrawLib\": \"คลังของ Excalidraw\",\n    \"decreaseFontSize\": \"ลดขนาดตัวอักษร\",\n    \"increaseFontSize\": \"เพิ่มขนาดตัวอักษร\",\n    \"unbindText\": \"ยกเลิกการผูกติด\",\n    \"bindText\": \"\",\n    \"createContainerFromText\": \"\",\n    \"link\": {\n      \"edit\": \"แก้ไขลิงก์\",\n      \"editEmbed\": \"แก้ไขลิงค์และการฝัง\",\n      \"create\": \"สร้างลิงค์\",\n      \"createEmbed\": \"สร้างลิงค์และการฝัง\",\n      \"label\": \"ลิงค์\",\n      \"labelEmbed\": \"ลิงค์และการฝัง\",\n      \"empty\": \"ไม่ได้ใส่ลิงค์\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"แก้ไขเส้น\",\n      \"exit\": \"\"\n    },\n    \"elementLock\": {\n      \"lock\": \"ล็อก\",\n      \"unlock\": \"ปลดล็อก\",\n      \"lockAll\": \"ล็อกทั้งหมด\",\n      \"unlockAll\": \"ปลดล็อกทั้งหมด\"\n    },\n    \"statusPublished\": \"เผยแพร่\",\n    \"sidebarLock\": \"\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"ยังไม่มีรายการที่เพิ่มเข้าไปได้\",\n    \"hint_emptyLibrary\": \"\",\n    \"hint_emptyPrivateLibrary\": \"\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"รีเซ็ทผืนผ้าใบ\",\n    \"exportJSON\": \"ส่งออกไปยังไฟล์\",\n    \"exportImage\": \"ส่งออกเป็นรูปภาพ\",\n    \"export\": \"บันทึกไปยัง\",\n    \"copyToClipboard\": \"คัดลอกไปยังคลิปบอร์ด\",\n    \"save\": \"บันทึกเป็นไฟล์ปัจจุบัน\",\n    \"saveAs\": \"บันทึกเป็น\",\n    \"load\": \"เปิด\",\n    \"getShareableLink\": \"สร้างลิงค์ที่แชร์ได้\",\n    \"close\": \"ปิด\",\n    \"selectLanguage\": \"เลือกภาษา\",\n    \"scrollBackToContent\": \"เลื่อนกลับไปด้านบน\",\n    \"zoomIn\": \"ซูมเข้า\",\n    \"zoomOut\": \"ซูมออก\",\n    \"resetZoom\": \"รีเซ็ตการซูม\",\n    \"menu\": \"เมนู\",\n    \"done\": \"เสร็จสิ้น\",\n    \"edit\": \"แก้ไข\",\n    \"undo\": \"เลิกทำ\",\n    \"redo\": \"ทำซ้ำ\",\n    \"resetLibrary\": \"รีเซ็ตคลัง\",\n    \"createNewRoom\": \"สร้างห้องใหม่\",\n    \"fullScreen\": \"เต็มหน้าจอ\",\n    \"darkMode\": \"โหมดกลางคืน\",\n    \"lightMode\": \"โหมดกลางวัน\",\n    \"zenMode\": \"โหมด Zen\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"ออกจากโหมด Zen\",\n    \"cancel\": \"ยกเลิก\",\n    \"clear\": \"เคลียร์\",\n    \"remove\": \"ลบ\",\n    \"embed\": \"สลับการฝัง\",\n    \"publishLibrary\": \"เผยแพร่\",\n    \"submit\": \"ตกลง\",\n    \"confirm\": \"ยืนยัน\",\n    \"embeddableInteractionButton\": \"คลิกเพื่อปฏิสัมพันธ์\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"\",\n    \"couldNotCreateShareableLink\": \"ไม่สามารถสร้างลิงค์ได้\",\n    \"couldNotCreateShareableLinkTooBig\": \"\",\n    \"couldNotLoadInvalidFile\": \"ไม่สามารถโหลดไฟล์ที่ผิดพลาดได้\",\n    \"importBackendFailed\": \"เกิดข้อผิดพลาดจากการนำเข้าจากระบบหลังบ้าน\",\n    \"cannotExportEmptyCanvas\": \"ไม่สามารถนำออกจากผืนผ้าใบที่ว่างเปล่าได้\",\n    \"couldNotCopyToClipboard\": \"ไม่สามารถคัดลอกไปยังคลิปบอร์ดได้\",\n    \"decryptFailed\": \"ไม่สามารถถอดรหัสข้อมูลได้\",\n    \"uploadedSecurly\": \"การอัพโหลดได้ถูกเข้ารหัสแบบ end-to-end หมายความว่าเซิร์ฟเวอร์ของ Excalidraw และบุคคลอื่นไม่สามารถอ่านข้อมูลได้\",\n    \"loadSceneOverridePrompt\": \"\",\n    \"collabStopOverridePrompt\": \"\",\n    \"errorAddingToLibrary\": \"ไม่สามารถเพิ่มรายการเข้าไปในคลังได้\",\n    \"errorRemovingFromLibrary\": \"ไม่สามารถลบรายการนี้ออกจากคลังได้\",\n    \"confirmAddLibrary\": \"\",\n    \"imageDoesNotContainScene\": \"\",\n    \"cannotRestoreFromImage\": \"\",\n    \"invalidSceneUrl\": \"\",\n    \"resetLibrary\": \"\",\n    \"removeItemsFromsLibrary\": \"\",\n    \"invalidEncryptionKey\": \"\",\n    \"collabOfflineWarning\": \"\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"ไม่รองรับชนิดของไฟล์นี้\",\n    \"imageInsertError\": \"ไม่สามารถเพิ่มรูปภาพได้ ลองอีกครั้งในภายหลัง\",\n    \"fileTooBig\": \"\",\n    \"svgImageInsertError\": \"\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"ไฟล์ SVG ผิดพลาด\",\n    \"cannotResolveCollabServer\": \"ไม่สามารถเชื่อต่อกับ collab เซิร์ฟเวอร์ได้ โปรดลองโหลดหน้านี้ใหม่และลองอีกครั้ง\",\n    \"importLibraryError\": \"\",\n    \"collabSaveFailed\": \"\",\n    \"collabSaveFailed_sizeExceeded\": \"\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"การเพิ่มองค์ประกอบที่ฝังยังไม่สามารถเพิ่มเข้าไปในไลบลารีได้\",\n      \"iframe\": \"\",\n      \"image\": \"การสนับสนุนสำหรับเพิ่มรูปภาพลงในไลบลารีจะมาในเร็ว ๆ นี้\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"\",\n    \"image\": \"\",\n    \"rectangle\": \"สี่เหลี่ยมผืนผ้า\",\n    \"diamond\": \"\",\n    \"ellipse\": \"วงรี\",\n    \"arrow\": \"ลูกศร\",\n    \"line\": \"\",\n    \"freedraw\": \"\",\n    \"text\": \"ข้อความ\",\n    \"library\": \"คลัง\",\n    \"lock\": \"\",\n    \"penMode\": \"\",\n    \"link\": \"\",\n    \"eraser\": \"ยางลบ\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"ฝังเว็บ\",\n    \"laser\": \"\",\n    \"hand\": \"\",\n    \"extraTools\": \"เครื่องมืออื่นๆ\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"\",\n    \"selectedShapeActions\": \"\",\n    \"shapes\": \"รูปร่าง\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"\",\n    \"linearElement\": \"\",\n    \"freeDraw\": \"\",\n    \"text\": \"\",\n    \"embeddable\": \"คลิกและลากเพื่อสร้างการฝังสำหรับเว็บไซต์\",\n    \"text_selected\": \"คลิกสองครั้งหรือกด ENTER เพื่อแก้ไขข้อความ\",\n    \"text_editing\": \"กดปุ่ม Esc หรือกด Ctrl, Cmd + Enter เพื่อเสร็จการแก้ไข\",\n    \"linearElementMulti\": \"คลิกที่จุดสุดท้ายหรือกด Escape หรือ Enter เพื่อเสร็จสิ้น\",\n    \"lockAngle\": \"\",\n    \"resize\": \"\",\n    \"resizeImage\": \"\",\n    \"rotate\": \"\",\n    \"lineEditor_info\": \"\",\n    \"lineEditor_pointSelected\": \"กดปุ่ม Delete เพื่อลบจุด\\nกด Ctrl หรือ Cmd + D เพื่อทำซ้ำหรือลากเพื่อเคลื่อนย้าย\",\n    \"lineEditor_nothingSelected\": \"\",\n    \"placeImage\": \"\",\n    \"publishLibrary\": \"\",\n    \"bindTextToElement\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"eraserRevert\": \"\",\n    \"firefox_clipboard_write\": \"\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"\",\n    \"canvasTooBig\": \"\",\n    \"canvasTooBigTip\": \"\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"<button>กำลังรีโหลดหน้า</button>\",\n    \"clearCanvasMessage\": \"ถ้าโหลดไม่ได้ ให้ลอง <button>เคลียร์ผืนผ้าใบ</button>\",\n    \"clearCanvasCaveat\": \"\",\n    \"trackedToSentry\": \"\",\n    \"openIssueMessage\": \"\",\n    \"sceneContent\": \"\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"\",\n    \"desc_privacy\": \"\",\n    \"button_startSession\": \"เริ่มเซสชัน\",\n    \"button_stopSession\": \"หยุดเซสชัน\",\n    \"desc_inProgressIntro\": \"\",\n    \"desc_shareLink\": \"\",\n    \"desc_exitSession\": \"\",\n    \"shareTitle\": \"\"\n  },\n  \"errorDialog\": {\n    \"title\": \"\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"\",\n    \"disk_details\": \"\",\n    \"disk_button\": \"\",\n    \"link_title\": \"\",\n    \"link_details\": \"\",\n    \"link_button\": \"\",\n    \"excalidrawplus_description\": \"\",\n    \"excalidrawplus_button\": \"\",\n    \"excalidrawplus_exportError\": \"ไม่สามารถส่งออกไปที่ Excalidraw+ ได้ในขณะนี้\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"อ่านบล็อกของพวกเรา\",\n    \"click\": \"คลิก\",\n    \"deepSelect\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"curvedArrow\": \"\",\n    \"curvedLine\": \"\",\n    \"documentation\": \"\",\n    \"doubleClick\": \"ดับเบิลคลิก\",\n    \"drag\": \"ลาก\",\n    \"editor\": \"\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"แก้ไขข้อความ / เพิ่มข้อความ\",\n    \"github\": \"\",\n    \"howto\": \"\",\n    \"or\": \"\",\n    \"preventBinding\": \"\",\n    \"tools\": \"\",\n    \"shortcuts\": \"\",\n    \"textFinish\": \"\",\n    \"textNewLine\": \"\",\n    \"title\": \"ช่วยเหลือ\",\n    \"view\": \"ดู\",\n    \"zoomToFit\": \"\",\n    \"zoomToSelection\": \"\",\n    \"toggleElementLock\": \"\",\n    \"movePageUpDown\": \"\",\n    \"movePageLeftRight\": \"ย้ายหน้าไปด้าน ซ้าย/ขวา\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"\"\n  },\n  \"publishDialog\": {\n    \"title\": \"\",\n    \"itemName\": \"\",\n    \"authorName\": \"ชื่อเจ้าของ\",\n    \"githubUsername\": \"ชื่อผู้ใช้ GitHub\",\n    \"twitterUsername\": \"ชื่อผู้ใช้ Twitter\",\n    \"libraryName\": \"\",\n    \"libraryDesc\": \"\",\n    \"website\": \"\",\n    \"placeholder\": {\n      \"authorName\": \"\",\n      \"libraryName\": \"\",\n      \"libraryDesc\": \"\",\n      \"githubHandle\": \"\",\n      \"twitterHandle\": \"\",\n      \"website\": \"\"\n    },\n    \"errors\": {\n      \"required\": \"\",\n      \"website\": \"\"\n    },\n    \"noteDescription\": \"\",\n    \"noteGuidelines\": \"\",\n    \"noteLicense\": \"\",\n    \"noteItems\": \"\",\n    \"atleastOneLibItem\": \"\",\n    \"republishWarning\": \"\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"\",\n    \"content\": \"\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"\",\n    \"removeItemsFromLib\": \"\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"โหมดกลางคืน\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"ส่งออกไปเป็น SVG\",\n      \"exportToSvg\": \"ส่งออกไปเป็น SVG\",\n      \"copyPngToClipboard\": \"คัดลอก PNG ไปยังคลิปบอร์ด\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"คัดลอกไปยังคลิปบอร์ด\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"\",\n    \"link\": \"\"\n  },\n  \"stats\": {\n    \"angle\": \"\",\n    \"element\": \"\",\n    \"elements\": \"\",\n    \"height\": \"\",\n    \"scene\": \"\",\n    \"selected\": \"\",\n    \"storage\": \"\",\n    \"title\": \"\",\n    \"total\": \"\",\n    \"version\": \"\",\n    \"versionCopy\": \"\",\n    \"versionNotAvailable\": \"\",\n    \"width\": \"\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"\",\n    \"copyStyles\": \"\",\n    \"copyToClipboard\": \"\",\n    \"copyToClipboardAsPng\": \"\",\n    \"fileSaved\": \"\",\n    \"fileSavedToFilename\": \"\",\n    \"canvas\": \"\",\n    \"selection\": \"\",\n    \"pasteAsSingleElement\": \"\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"\",\n    \"black\": \"\",\n    \"white\": \"\",\n    \"red\": \"\",\n    \"pink\": \"\",\n    \"grape\": \"\",\n    \"violet\": \"\",\n    \"gray\": \"\",\n    \"blue\": \"\",\n    \"cyan\": \"\",\n    \"teal\": \"ฟ้าน้ำทะเล\",\n    \"green\": \"เขียว\",\n    \"yellow\": \"เหลือง\",\n    \"orange\": \"ส้ม\",\n    \"bronze\": \"ทองแดง\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"\",\n      \"center_heading_plus\": \"\",\n      \"menuHint\": \"\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"\",\n      \"center_heading\": \"\",\n      \"toolbarHint\": \"\",\n      \"helpHint\": \"\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"\",\n    \"shades\": \"\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}