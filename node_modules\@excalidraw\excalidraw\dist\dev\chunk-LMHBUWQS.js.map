{"version": 3, "sources": ["../../locales/en.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Paste\",\n    \"pasteAsPlaintext\": \"Paste as plaintext\",\n    \"pasteCharts\": \"Paste charts\",\n    \"selectAll\": \"Select all\",\n    \"multiSelect\": \"Add element to selection\",\n    \"moveCanvas\": \"Move canvas\",\n    \"cut\": \"Cut\",\n    \"copy\": \"Copy\",\n    \"copyAsPng\": \"Copy to clipboard as PNG\",\n    \"copyAsSvg\": \"Copy to clipboard as SVG\",\n    \"copyText\": \"Copy to clipboard as text\",\n    \"copySource\": \"Copy source to clipboard\",\n    \"convertToCode\": \"Convert to code\",\n    \"bringForward\": \"Bring forward\",\n    \"sendToBack\": \"Send to back\",\n    \"bringToFront\": \"Bring to front\",\n    \"sendBackward\": \"Send backward\",\n    \"delete\": \"Delete\",\n    \"copyStyles\": \"Copy styles\",\n    \"pasteStyles\": \"Paste styles\",\n    \"stroke\": \"Stroke\",\n    \"changeStroke\": \"Change stroke color\",\n    \"background\": \"Background\",\n    \"changeBackground\": \"Change background color\",\n    \"fill\": \"Fill\",\n    \"strokeWidth\": \"Stroke width\",\n    \"strokeStyle\": \"Stroke style\",\n    \"strokeStyle_solid\": \"Solid\",\n    \"strokeStyle_dashed\": \"Dashed\",\n    \"strokeStyle_dotted\": \"Dotted\",\n    \"sloppiness\": \"Sloppiness\",\n    \"opacity\": \"Opacity\",\n    \"textAlign\": \"Text align\",\n    \"edges\": \"Edges\",\n    \"sharp\": \"Sharp\",\n    \"round\": \"Round\",\n    \"arrowheads\": \"Arrowheads\",\n    \"arrowhead_none\": \"None\",\n    \"arrowhead_arrow\": \"Arrow\",\n    \"arrowhead_bar\": \"Bar\",\n    \"arrowhead_circle\": \"Circle\",\n    \"arrowhead_circle_outline\": \"Circle (outline)\",\n    \"arrowhead_triangle\": \"Triangle\",\n    \"arrowhead_triangle_outline\": \"Triangle (outline)\",\n    \"arrowhead_diamond\": \"Diamond\",\n    \"arrowhead_diamond_outline\": \"Diamond (outline)\",\n    \"arrowhead_crowfoot_many\": \"Crow's foot (many)\",\n    \"arrowhead_crowfoot_one\": \"Crow's foot (one)\",\n    \"arrowhead_crowfoot_one_or_many\": \"Crow's foot (one or many)\",\n    \"more_options\": \"More options\",\n    \"arrowtypes\": \"Arrow type\",\n    \"arrowtype_sharp\": \"Sharp arrow\",\n    \"arrowtype_round\": \"Curved arrow\",\n    \"arrowtype_elbowed\": \"Elbow arrow\",\n    \"fontSize\": \"Font size\",\n    \"fontFamily\": \"Font family\",\n    \"addWatermark\": \"Add \\\"Made with Excalidraw\\\"\",\n    \"handDrawn\": \"Hand-drawn\",\n    \"normal\": \"Normal\",\n    \"code\": \"Code\",\n    \"small\": \"Small\",\n    \"medium\": \"Medium\",\n    \"large\": \"Large\",\n    \"veryLarge\": \"Very large\",\n    \"solid\": \"Solid\",\n    \"hachure\": \"Hachure\",\n    \"zigzag\": \"Zigzag\",\n    \"crossHatch\": \"Cross-hatch\",\n    \"thin\": \"Thin\",\n    \"bold\": \"Bold\",\n    \"left\": \"Left\",\n    \"center\": \"Center\",\n    \"right\": \"Right\",\n    \"extraBold\": \"Extra bold\",\n    \"architect\": \"Architect\",\n    \"artist\": \"Artist\",\n    \"cartoonist\": \"Cartoonist\",\n    \"fileTitle\": \"File name\",\n    \"colorPicker\": \"Color picker\",\n    \"canvasColors\": \"Used on canvas\",\n    \"canvasBackground\": \"Canvas background\",\n    \"drawingCanvas\": \"Drawing canvas\",\n    \"clearCanvas\": \"Clear canvas\",\n    \"layers\": \"Layers\",\n    \"actions\": \"Actions\",\n    \"language\": \"Language\",\n    \"liveCollaboration\": \"Live collaboration...\",\n    \"duplicateSelection\": \"Duplicate\",\n    \"untitled\": \"Untitled\",\n    \"name\": \"Name\",\n    \"yourName\": \"Your name\",\n    \"madeWithExcalidraw\": \"Made with Excalidraw\",\n    \"group\": \"Group selection\",\n    \"ungroup\": \"Ungroup selection\",\n    \"collaborators\": \"Collaborators\",\n    \"toggleGrid\": \"Toggle grid\",\n    \"addToLibrary\": \"Add to library\",\n    \"removeFromLibrary\": \"Remove from library\",\n    \"libraryLoadingMessage\": \"Loading library…\",\n    \"libraries\": \"Browse libraries\",\n    \"loadingScene\": \"Loading scene…\",\n    \"loadScene\": \"Load scene from file\",\n    \"align\": \"Align\",\n    \"alignTop\": \"Align top\",\n    \"alignBottom\": \"Align bottom\",\n    \"alignLeft\": \"Align left\",\n    \"alignRight\": \"Align right\",\n    \"centerVertically\": \"Center vertically\",\n    \"centerHorizontally\": \"Center horizontally\",\n    \"distributeHorizontally\": \"Distribute horizontally\",\n    \"distributeVertically\": \"Distribute vertically\",\n    \"flipHorizontal\": \"Flip horizontal\",\n    \"flipVertical\": \"Flip vertical\",\n    \"viewMode\": \"View mode\",\n    \"share\": \"Share\",\n    \"showStroke\": \"Show stroke color picker\",\n    \"showBackground\": \"Show background color picker\",\n    \"showFonts\": \"Show font picker\",\n    \"toggleTheme\": \"Toggle light/dark theme\",\n    \"theme\": \"Theme\",\n    \"personalLib\": \"Personal Library\",\n    \"excalidrawLib\": \"Excalidraw Library\",\n    \"decreaseFontSize\": \"Decrease font size\",\n    \"increaseFontSize\": \"Increase font size\",\n    \"unbindText\": \"Unbind text\",\n    \"bindText\": \"Bind text to the container\",\n    \"createContainerFromText\": \"Wrap text in a container\",\n    \"link\": {\n      \"edit\": \"Edit link\",\n      \"editEmbed\": \"Edit embeddable link\",\n      \"create\": \"Add link\",\n      \"label\": \"Link\",\n      \"labelEmbed\": \"Link & embed\",\n      \"empty\": \"No link is set\",\n      \"hint\": \"Type or paste your link here\",\n      \"goToElement\": \"Go to target element\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Edit line\",\n      \"editArrow\": \"Edit arrow\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Lock\",\n      \"unlock\": \"Unlock\",\n      \"lockAll\": \"Lock all\",\n      \"unlockAll\": \"Unlock all\"\n    },\n    \"statusPublished\": \"Published\",\n    \"sidebarLock\": \"Keep sidebar open\",\n    \"selectAllElementsInFrame\": \"Select all elements in frame\",\n    \"removeAllElementsFromFrame\": \"Remove all elements from frame\",\n    \"eyeDropper\": \"Pick color from canvas\",\n    \"textToDiagram\": \"Text to diagram\",\n    \"prompt\": \"Prompt\",\n    \"followUs\": \"Follow us\",\n    \"discordChat\": \"Discord chat\",\n    \"zoomToFitViewport\": \"Zoom to fit in viewport\",\n    \"zoomToFitSelection\": \"Zoom to fit selection\",\n    \"zoomToFit\": \"Zoom to fit all elements\",\n    \"installPWA\": \"Install Excalidraw locally (PWA)\",\n    \"autoResize\": \"Enable text auto-resizing\",\n    \"imageCropping\": \"Image cropping\",\n    \"unCroppedDimension\": \"Uncropped dimension\",\n    \"copyElementLink\": \"Copy link to object\",\n    \"linkToElement\": \"Link to object\",\n    \"wrapSelectionInFrame\": \"Wrap selection in frame\"\n  },\n  \"elementLink\": {\n    \"title\": \"Link to object\",\n    \"desc\": \"Click on a shape on canvas or paste a link.\",\n    \"notFound\": \"Linked object wasn't found on canvas.\"\n  },\n  \"library\": {\n    \"noItems\": \"No items added yet...\",\n    \"hint_emptyLibrary\": \"Select an item on canvas to add it here, or install a library from the public repository, below.\",\n    \"hint_emptyPrivateLibrary\": \"Select an item on canvas to add it here.\"\n  },\n  \"search\": {\n    \"title\": \"Find on canvas\",\n    \"noMatch\": \"No matches found...\",\n    \"singleResult\": \"result\",\n    \"multipleResults\": \"results\",\n    \"placeholder\": \"Find text on canvas...\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Reset the canvas\",\n    \"exportJSON\": \"Export to file\",\n    \"exportImage\": \"Export image...\",\n    \"export\": \"Save to...\",\n    \"copyToClipboard\": \"Copy to clipboard\",\n    \"copyLink\": \"Copy link\",\n    \"save\": \"Save to current file\",\n    \"saveAs\": \"Save as\",\n    \"load\": \"Open\",\n    \"getShareableLink\": \"Get shareable link\",\n    \"close\": \"Close\",\n    \"selectLanguage\": \"Select language\",\n    \"scrollBackToContent\": \"Scroll back to content\",\n    \"zoomIn\": \"Zoom in\",\n    \"zoomOut\": \"Zoom out\",\n    \"resetZoom\": \"Reset zoom\",\n    \"menu\": \"Menu\",\n    \"done\": \"Done\",\n    \"edit\": \"Edit\",\n    \"undo\": \"Undo\",\n    \"redo\": \"Redo\",\n    \"resetLibrary\": \"Reset library\",\n    \"createNewRoom\": \"Create new room\",\n    \"fullScreen\": \"Full screen\",\n    \"darkMode\": \"Dark mode\",\n    \"lightMode\": \"Light mode\",\n    \"systemMode\": \"System mode\",\n    \"zenMode\": \"Zen mode\",\n    \"objectsSnapMode\": \"Snap to objects\",\n    \"exitZenMode\": \"Exit zen mode\",\n    \"cancel\": \"Cancel\",\n    \"clear\": \"Clear\",\n    \"remove\": \"Remove\",\n    \"embed\": \"Toggle embedding\",\n    \"publishLibrary\": \"Publish\",\n    \"submit\": \"Submit\",\n    \"confirm\": \"Confirm\",\n    \"embeddableInteractionButton\": \"Click to interact\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"This will clear the whole canvas. Are you sure?\",\n    \"couldNotCreateShareableLink\": \"Couldn't create shareable link.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Couldn't create shareable link: the scene is too big\",\n    \"couldNotLoadInvalidFile\": \"Couldn't load invalid file\",\n    \"importBackendFailed\": \"Importing from backend failed.\",\n    \"cannotExportEmptyCanvas\": \"Cannot export empty canvas.\",\n    \"couldNotCopyToClipboard\": \"Couldn't copy to clipboard.\",\n    \"decryptFailed\": \"Couldn't decrypt data.\",\n    \"uploadedSecurly\": \"The upload has been secured with end-to-end encryption, which means that Excalidraw server and third parties can't read the content.\",\n    \"loadSceneOverridePrompt\": \"Loading external drawing will replace your existing content. Do you wish to continue?\",\n    \"collabStopOverridePrompt\": \"Stopping the session will overwrite your previous, locally stored drawing. Are you sure?\\n\\n(If you want to keep your local drawing, simply close the browser tab instead.)\",\n    \"errorAddingToLibrary\": \"Couldn't add item to the library\",\n    \"errorRemovingFromLibrary\": \"Couldn't remove item from the library\",\n    \"confirmAddLibrary\": \"This will add {{numShapes}} shape(s) to your library. Are you sure?\",\n    \"imageDoesNotContainScene\": \"This image does not seem to contain any scene data. Have you enabled scene embedding during export?\",\n    \"cannotRestoreFromImage\": \"Scene couldn't be restored from this image file\",\n    \"invalidSceneUrl\": \"Couldn't import scene from the supplied URL. It's either malformed, or doesn't contain valid Excalidraw JSON data.\",\n    \"resetLibrary\": \"This will clear your library. Are you sure?\",\n    \"removeItemsFromsLibrary\": \"Delete {{count}} item(s) from library?\",\n    \"invalidEncryptionKey\": \"Encryption key must be of 22 characters. Live collaboration is disabled.\",\n    \"collabOfflineWarning\": \"No internet connection available.\\nYour changes will not be saved!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Unsupported file type.\",\n    \"imageInsertError\": \"Couldn't insert image. Try again later...\",\n    \"fileTooBig\": \"File is too big. Maximum allowed size is {{maxSize}}.\",\n    \"svgImageInsertError\": \"Couldn't insert SVG image. The SVG markup looks invalid.\",\n    \"failedToFetchImage\": \"Failed to fetch image.\",\n    \"cannotResolveCollabServer\": \"Couldn't connect to the collab server. Please reload the page and try again.\",\n    \"importLibraryError\": \"Couldn't load library\",\n    \"saveLibraryError\": \"Couldn't save library to storage. Please save your library to a file locally to make sure you don't lose changes.\",\n    \"collabSaveFailed\": \"Couldn't save to the backend database. If problems persist, you should save your file locally to ensure you don't lose your work.\",\n    \"collabSaveFailed_sizeExceeded\": \"Couldn't save to the backend database, the canvas seems to be too big. You should save the file locally to ensure you don't lose your work.\",\n    \"imageToolNotSupported\": \"Images are disabled.\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Looks like you are using Brave browser with the <bold>Aggressively Block Fingerprinting</bold> setting enabled.\",\n      \"line2\": \"This could result in breaking the <bold>Text Elements</bold> in your drawings.\",\n      \"line3\": \"We strongly recommend disabling this setting. You can follow <link>these steps</link> on how to do so.\",\n      \"line4\": \"If disabling this setting doesn't fix the display of text elements, please open an <issueLink>issue</issueLink> on our GitHub, or write us on <discordLink>Discord</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"Embeddable elements cannot be added to the library.\",\n      \"iframe\": \"IFrame elements cannot be added to the library.\",\n      \"image\": \"Support for adding images to the library coming soon!\"\n    },\n    \"asyncPasteFailedOnRead\": \"Couldn't paste (couldn't read from system clipboard).\",\n    \"asyncPasteFailedOnParse\": \"Couldn't paste.\",\n    \"copyToSystemClipboardFailed\": \"Couldn't copy to clipboard.\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Selection\",\n    \"image\": \"Insert image\",\n    \"rectangle\": \"Rectangle\",\n    \"diamond\": \"Diamond\",\n    \"ellipse\": \"Ellipse\",\n    \"arrow\": \"Arrow\",\n    \"line\": \"Line\",\n    \"freedraw\": \"Draw\",\n    \"text\": \"Text\",\n    \"library\": \"Library\",\n    \"lock\": \"Keep selected tool active after drawing\",\n    \"penMode\": \"Pen mode - prevent touch\",\n    \"link\": \"Add / Update link for a selected shape\",\n    \"eraser\": \"Eraser\",\n    \"frame\": \"Frame tool\",\n    \"magicframe\": \"Wireframe to code\",\n    \"embeddable\": \"Web Embed\",\n    \"laser\": \"Laser pointer\",\n    \"hand\": \"Hand (panning tool)\",\n    \"extraTools\": \"More tools\",\n    \"mermaidToExcalidraw\": \"Mermaid to Excalidraw\"\n  },\n  \"element\": {\n    \"rectangle\": \"Rectangle\",\n    \"diamond\": \"Diamond\",\n    \"ellipse\": \"Ellipse\",\n    \"arrow\": \"Arrow\",\n    \"line\": \"Line\",\n    \"freedraw\": \"Freedraw\",\n    \"text\": \"Text\",\n    \"image\": \"Image\",\n    \"group\": \"Group\",\n    \"frame\": \"Frame\",\n    \"magicframe\": \"Wireframe to code\",\n    \"embeddable\": \"Web Embed\",\n    \"selection\": \"Selection\",\n    \"iframe\": \"IFrame\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Canvas actions\",\n    \"selectedShapeActions\": \"Selected shape actions\",\n    \"shapes\": \"Shapes\"\n  },\n  \"hints\": {\n    \"dismissSearch\": \"Escape to dismiss search\",\n    \"canvasPanning\": \"To move canvas, hold mouse wheel or spacebar while dragging, or use the hand tool\",\n    \"linearElement\": \"Click to start multiple points, drag for single line\",\n    \"arrowTool\": \"Click to start multiple points, drag for single line. Press {{arrowShortcut}} again to change arrow type.\",\n    \"freeDraw\": \"Click and drag, release when you're finished\",\n    \"text\": \"Tip: you can also add text by double-clicking anywhere with the selection tool\",\n    \"embeddable\": \"Click-drag to create a website embed\",\n    \"text_selected\": \"Double-click or press ENTER to edit text\",\n    \"text_editing\": \"Press Escape or CtrlOrCmd+ENTER to finish editing\",\n    \"linearElementMulti\": \"Click on last point or press Escape or Enter to finish\",\n    \"lockAngle\": \"You can constrain angle by holding SHIFT\",\n    \"resize\": \"You can constrain proportions by holding SHIFT while resizing,\\nhold ALT to resize from the center\",\n    \"resizeImage\": \"You can resize freely by holding SHIFT,\\nhold ALT to resize from the center\",\n    \"rotate\": \"You can constrain angles by holding SHIFT while rotating\",\n    \"lineEditor_info\": \"Hold CtrlOrCmd and Double-click or press CtrlOrCmd + Enter to edit points\",\n    \"lineEditor_pointSelected\": \"Press Delete to remove point(s),\\nCtrlOrCmd+D to duplicate, or drag to move\",\n    \"lineEditor_nothingSelected\": \"Select a point to edit (hold SHIFT to select multiple),\\nor hold Alt and click to add new points\",\n    \"placeImage\": \"Click to place the image, or click and drag to set its size manually\",\n    \"publishLibrary\": \"Publish your own library\",\n    \"bindTextToElement\": \"Press enter to add text\",\n    \"createFlowchart\": \"Hold CtrlOrCmd and Arrow key to create a flowchart\",\n    \"deepBoxSelect\": \"Hold CtrlOrCmd to deep select, and to prevent dragging\",\n    \"eraserRevert\": \"Hold Alt to revert the elements marked for deletion\",\n    \"firefox_clipboard_write\": \"This feature can likely be enabled by setting the \\\"dom.events.asyncClipboard.clipboardItem\\\" flag to \\\"true\\\". To change the browser flags in Firefox, visit the \\\"about:config\\\" page.\",\n    \"disableSnapping\": \"Hold CtrlOrCmd to disable snapping\",\n    \"enterCropEditor\": \"Double click the image or press ENTER to crop the image\",\n    \"leaveCropEditor\": \"Click outside the image or press ENTER or ESCAPE to finish cropping\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Cannot show preview\",\n    \"canvasTooBig\": \"The canvas may be too big.\",\n    \"canvasTooBigTip\": \"Tip: try moving the farthest elements a bit closer together.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Encountered an error. Try <button>reloading the page</button>.\",\n    \"clearCanvasMessage\": \"If reloading doesn't work, try <button>clearing the canvas</button>.\",\n    \"clearCanvasCaveat\": \" This will result in loss of work \",\n    \"trackedToSentry\": \"The error with identifier {{eventId}} was tracked on our system.\",\n    \"openIssueMessage\": \"We were very cautious not to include your scene information on the error. If your scene is not private, please consider following up on our <button>bug tracker</button>. Please include information below by copying and pasting into the GitHub issue.\",\n    \"sceneContent\": \"Scene content:\"\n  },\n  \"shareDialog\": {\n    \"or\": \"Or\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Invite people to collaborate on your drawing.\",\n    \"desc_privacy\": \"Don't worry, the session is end-to-end encrypted, and fully private. Not even our server can see what you draw.\",\n    \"button_startSession\": \"Start session\",\n    \"button_stopSession\": \"Stop session\",\n    \"desc_inProgressIntro\": \"Live-collaboration session is now in progress.\",\n    \"desc_shareLink\": \"Share this link with anyone you want to collaborate with:\",\n    \"desc_exitSession\": \"Stopping the session will disconnect you from the room, but you'll be able to continue working with the scene, locally. Note that this won't affect other people, and they'll still be able to collaborate on their version.\",\n    \"shareTitle\": \"Join a live collaboration session on Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Error\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Save to disk\",\n    \"disk_details\": \"Export the scene data to a file from which you can import later.\",\n    \"disk_button\": \"Save to file\",\n    \"link_title\": \"Shareable link\",\n    \"link_details\": \"Export as a read-only link.\",\n    \"link_button\": \"Export to Link\",\n    \"excalidrawplus_description\": \"Save the scene to your Excalidraw+ workspace.\",\n    \"excalidrawplus_button\": \"Export\",\n    \"excalidrawplus_exportError\": \"Couldn't export to Excalidraw+ at this moment...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Read our blog\",\n    \"click\": \"click\",\n    \"deepSelect\": \"Deep select\",\n    \"deepBoxSelect\": \"Deep select within box, and prevent dragging\",\n    \"createFlowchart\": \"Create a flowchart from a generic element\",\n    \"navigateFlowchart\": \"Navigate a flowchart\",\n    \"curvedArrow\": \"Curved arrow\",\n    \"curvedLine\": \"Curved line\",\n    \"documentation\": \"Documentation\",\n    \"doubleClick\": \"double-click\",\n    \"drag\": \"drag\",\n    \"editor\": \"Editor\",\n    \"editLineArrowPoints\": \"Edit line/arrow points\",\n    \"editText\": \"Edit text / add label\",\n    \"github\": \"Found an issue? Submit\",\n    \"howto\": \"Follow our guides\",\n    \"or\": \"or\",\n    \"preventBinding\": \"Prevent arrow binding\",\n    \"tools\": \"Tools\",\n    \"shortcuts\": \"Keyboard shortcuts\",\n    \"textFinish\": \"Finish editing (text editor)\",\n    \"textNewLine\": \"Add new line (text editor)\",\n    \"title\": \"Help\",\n    \"view\": \"View\",\n    \"zoomToFit\": \"Zoom to fit all elements\",\n    \"zoomToSelection\": \"Zoom to selection\",\n    \"toggleElementLock\": \"Lock/unlock selection\",\n    \"movePageUpDown\": \"Move page up/down\",\n    \"movePageLeftRight\": \"Move page left/right\",\n    \"cropStart\": \"Crop image\",\n    \"cropFinish\": \"Finish image cropping\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Clear canvas\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Publish library\",\n    \"itemName\": \"Item name\",\n    \"authorName\": \"Author name\",\n    \"githubUsername\": \"GitHub username\",\n    \"twitterUsername\": \"Twitter username\",\n    \"libraryName\": \"Library name\",\n    \"libraryDesc\": \"Library description\",\n    \"website\": \"Website\",\n    \"placeholder\": {\n      \"authorName\": \"Your name or username\",\n      \"libraryName\": \"Name of your library\",\n      \"libraryDesc\": \"Description of your library to help people understand its usage\",\n      \"githubHandle\": \"GitHub handle (optional), so you can edit the library once submitted for review\",\n      \"twitterHandle\": \"Twitter username (optional), so we know who to credit when promoting over Twitter\",\n      \"website\": \"Link to your personal website or elsewhere (optional)\"\n    },\n    \"errors\": {\n      \"required\": \"Required\",\n      \"website\": \"Enter a valid URL\"\n    },\n    \"noteDescription\": \"Submit your library to be included in the <link>public library repository</link> for other people to use in their drawings.\",\n    \"noteGuidelines\": \"The library needs to be manually approved first. Please read the <link>guidelines</link> before submitting. You will need a GitHub account to communicate and make changes if requested, but it is not strictly required.\",\n    \"noteLicense\": \"By submitting, you agree the library will be published under the <link>MIT License</link>, which in short means anyone can use them without restrictions.\",\n    \"noteItems\": \"Each library item must have its own name so it's filterable. The following library items will be included:\",\n    \"atleastOneLibItem\": \"Please select at least one library item to get started\",\n    \"republishWarning\": \"Note: some of the selected items are marked as already published/submitted. You should only resubmit items when updating an existing library or submission.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Library submitted\",\n    \"content\": \"Thank you {{authorName}}. Your library has been submitted for review. You can track the status <link>here</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Reset library\",\n    \"removeItemsFromLib\": \"Remove selected items from library\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Export image\",\n    \"label\": {\n      \"withBackground\": \"Background\",\n      \"onlySelected\": \"Only selected\",\n      \"darkMode\": \"Dark mode\",\n      \"embedScene\": \"Embed scene\",\n      \"scale\": \"Scale\",\n      \"padding\": \"Padding\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"Scene data will be saved into the exported PNG/SVG file so that the scene can be restored from it.\\nWill increase exported file size.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Export to PNG\",\n      \"exportToSvg\": \"Export to SVG\",\n      \"copyPngToClipboard\": \"Copy PNG to clipboard\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Copy to clipboard\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Your drawings are end-to-end encrypted so Excalidraw's servers will never see them.\",\n    \"link\": \"Blog post on end-to-end encryption in Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Angle\",\n    \"shapes\": \"Shapes\",\n    \"height\": \"Height\",\n    \"scene\": \"Scene\",\n    \"selected\": \"Selected\",\n    \"storage\": \"Storage\",\n    \"fullTitle\": \"Canvas & Shape properties\",\n    \"title\": \"Properties\",\n    \"generalStats\": \"General\",\n    \"elementProperties\": \"Shape properties\",\n    \"total\": \"Total\",\n    \"version\": \"Version\",\n    \"versionCopy\": \"Click to copy\",\n    \"versionNotAvailable\": \"Version not available\",\n    \"width\": \"Width\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Added to library\",\n    \"copyStyles\": \"Copied styles.\",\n    \"copyToClipboard\": \"Copied to clipboard.\",\n    \"copyToClipboardAsPng\": \"Copied {{exportSelection}} to clipboard as PNG\\n({{exportColorScheme}})\",\n    \"copyToClipboardAsSvg\": \"Copied {{exportSelection}} to clipboard as SVG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"File saved.\",\n    \"fileSavedToFilename\": \"Saved to {filename}\",\n    \"canvas\": \"canvas\",\n    \"selection\": \"selection\",\n    \"pasteAsSingleElement\": \"Use {{shortcut}} to paste as a single element,\\nor paste into an existing text editor\",\n    \"unableToEmbed\": \"Embedding this url is currently not allowed. Raise an issue on GitHub to request the url whitelisted\",\n    \"unrecognizedLinkFormat\": \"The link you embedded does not match the expected format. Please try to paste the 'embed' string provided by the source site\",\n    \"elementLinkCopied\": \"Link copied to clipboard\"\n  },\n  \"colors\": {\n    \"transparent\": \"Transparent\",\n    \"black\": \"Black\",\n    \"white\": \"White\",\n    \"red\": \"Red\",\n    \"pink\": \"Pink\",\n    \"grape\": \"Grape\",\n    \"violet\": \"Violet\",\n    \"gray\": \"Gray\",\n    \"blue\": \"Blue\",\n    \"cyan\": \"Cyan\",\n    \"teal\": \"Teal\",\n    \"green\": \"Green\",\n    \"yellow\": \"Yellow\",\n    \"orange\": \"Orange\",\n    \"bronze\": \"Bronze\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"All your data is saved locally in your browser.\",\n      \"center_heading_plus\": \"Did you want to go to the Excalidraw+ instead?\",\n      \"menuHint\": \"Export, preferences, languages, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Export, preferences, and more...\",\n      \"center_heading\": \"Diagrams. Made. Simple.\",\n      \"toolbarHint\": \"Pick a tool & Start drawing!\",\n      \"helpHint\": \"Shortcuts & help\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Most used custom colors\",\n    \"colors\": \"Colors\",\n    \"shades\": \"Shades\",\n    \"hexCode\": \"Hex code\",\n    \"noShades\": \"No shades available for this color\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Export as image\",\n        \"button\": \"Export as image\",\n        \"description\": \"Export the scene data as an image from which you can import later.\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Save to disk\",\n        \"button\": \"Save to disk\",\n        \"description\": \"Export the scene data to a file from which you can import later.\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Export to Excalidraw+\",\n        \"description\": \"Save the scene to your Excalidraw+ workspace.\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Load from file\",\n        \"button\": \"Load from file\",\n        \"description\": \"Loading from a file will <bold>replace your existing content</bold>.<br></br>You can back up your drawing first using one of the options below.\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Load from link\",\n        \"button\": \"Replace my content\",\n        \"description\": \"Loading external drawing will <bold>replace your existing content</bold>.<br></br>You can back up your drawing first by using one of the options below.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"Mermaid to Excalidraw\",\n    \"button\": \"Insert\",\n    \"description\": \"Currently only <flowchartLink>Flowchart</flowchartLink>,<sequenceLink> Sequence, </sequenceLink> and <classLink>Class </classLink>Diagrams are supported. The other types will be rendered as image in Excalidraw.\",\n    \"syntax\": \"Mermaid Syntax\",\n    \"preview\": \"Preview\"\n  },\n  \"quickSearch\": {\n    \"placeholder\": \"Quick search\"\n  },\n  \"fontList\": {\n    \"badge\": {\n      \"old\": \"old\"\n    },\n    \"sceneFonts\": \"In this scene\",\n    \"availableFonts\": \"Available fonts\",\n    \"empty\": \"No fonts found\"\n  },\n  \"userList\": {\n    \"empty\": \"No users found\",\n    \"hint\": {\n      \"text\": \"Click on user to follow\",\n      \"followStatus\": \"You're currently following this user\",\n      \"inCall\": \"User is in a voice call\",\n      \"micMuted\": \"User's microphone is muted\",\n      \"isSpeaking\": \"User is speaking\"\n    }\n  },\n  \"commandPalette\": {\n    \"title\": \"Command palette\",\n    \"shortcuts\": {\n      \"select\": \"Select\",\n      \"confirm\": \"Confirm\",\n      \"close\": \"Close\"\n    },\n    \"recents\": \"Recently used\",\n    \"search\": {\n      \"placeholder\": \"Search menus, commands, and discover hidden gems\",\n      \"noMatch\": \"No matching commands...\"\n    },\n    \"itemNotAvailable\": \"Command is not available...\",\n    \"shortcutHint\": \"For Command palette, use {{shortcut}}\"\n  }\n}\n"], "mappings": ";AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,yBAA2B;AAAA,EAC3B,wBAA0B;AAAA,EAC1B,gCAAkC;AAAA,EAClC,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,WAAa;AAAA,EACb,aAAe;AAAA,EACf,OAAS;AAAA,EACT,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,IACT,MAAQ;AAAA,IACR,aAAe;AAAA,EACjB;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,WAAa;AAAA,EACf;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,YAAc;AAAA,EACd,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,oBAAsB;AAAA,EACtB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,sBAAwB;AAC1B;AACA,kBAAe;AAAA,EACb,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AACd;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,aAAU;AAAA,EACR,OAAS;AAAA,EACT,SAAW;AAAA,EACX,cAAgB;AAAA,EAChB,iBAAmB;AAAA,EACnB,aAAe;AACjB;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,YAAc;AAAA,EACd,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AACzB;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,WAAa;AAAA,EACb,QAAU;AACZ;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AAAA,EACnB,iBAAmB;AAAA,EACnB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,kBAAe;AAAA,EACb,IAAM;AACR;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,WAAa;AAAA,EACb,YAAc;AAChB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAAA,EAC1B,mBAAqB;AACvB;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AACA,kBAAe;AAAA,EACb,aAAe;AACjB;AACA,eAAY;AAAA,EACV,OAAS;AAAA,IACP,KAAO;AAAA,EACT;AAAA,EACA,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,OAAS;AACX;AACA,eAAY;AAAA,EACV,OAAS;AAAA,EACT,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,cAAgB;AAAA,IAChB,QAAU;AAAA,IACV,UAAY;AAAA,IACZ,YAAc;AAAA,EAChB;AACF;AACA,qBAAkB;AAAA,EAChB,OAAS;AAAA,EACT,WAAa;AAAA,IACX,QAAU;AAAA,IACV,SAAW;AAAA,IACX,OAAS;AAAA,EACX;AAAA,EACA,SAAW;AAAA,EACX,QAAU;AAAA,IACR,aAAe;AAAA,IACf,SAAW;AAAA,EACb;AAAA,EACA,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AAvnBF;AAAA,EACE;AAAA,EAwKA;AAAA,EAKA;AAAA,EAKA;AAAA,EAOA;AAAA,EAwCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAuBA;AAAA,EAgBA;AAAA,EAKA;AAAA,EA6BA;AAAA,EAKA;AAAA,EAQA;AAAA,EAGA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EAiCA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAiBA;AAAA,EAeA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAAA,EAOA;AAAA,EAGA;AAAA,EAQA;AAAA,EAUA;AAeF;", "names": []}