"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_mdx_mdx_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/mdx/mdx.js":
/*!***********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/mdx/mdx.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.api.js\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/basic-languages/mdx/mdx.ts\nvar conf = {\n  comments: {\n    blockComment: [\"{/*\", \"*/}\"]\n  },\n  brackets: [[\"{\", \"}\"]],\n  autoClosingPairs: [\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"\\u201C\", close: \"\\u201D\" },\n    { open: \"\\u2018\", close: \"\\u2019\" },\n    { open: \"`\", close: \"`\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"_\", close: \"_\" },\n    { open: \"**\", close: \"**\" },\n    { open: \"<\", close: \">\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: /^\\s*- .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"- \" }\n    },\n    {\n      beforeText: /^\\s*\\+ .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"+ \" }\n    },\n    {\n      beforeText: /^\\s*\\* .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"* \" }\n    },\n    {\n      beforeText: /^> /,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"> \" }\n    },\n    {\n      beforeText: /<\\w+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    },\n    {\n      beforeText: /\\s+>\\s*$/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    },\n    {\n      beforeText: /<\\/\\w+>/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Outdent }\n    },\n    ...Array.from({ length: 100 }, (_, index) => ({\n      beforeText: new RegExp(`^${index}\\\\. .+`),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: `${index + 1}. ` }\n    }))\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".mdx\",\n  control: /[!#()*+.[\\\\\\]_`{}\\-]/,\n  escapes: /\\\\@control/,\n  tokenizer: {\n    root: [\n      [/^---$/, { token: \"meta.content\", next: \"@frontmatter\", nextEmbedded: \"yaml\" }],\n      [/^\\s*import/, { token: \"keyword\", next: \"@import\", nextEmbedded: \"js\" }],\n      [/^\\s*export/, { token: \"keyword\", next: \"@export\", nextEmbedded: \"js\" }],\n      [/<\\w+/, { token: \"type.identifier\", next: \"@jsx\" }],\n      [/<\\/?\\w+>/, \"type.identifier\"],\n      [\n        /^(\\s*)(>*\\s*)(#{1,6}\\s)/,\n        [{ token: \"white\" }, { token: \"comment\" }, { token: \"keyword\", next: \"@header\" }]\n      ],\n      [/^(\\s*)(>*\\s*)([*+-])(\\s+)/, [\"white\", \"comment\", \"keyword\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(\\d{1,9}\\.)(\\s+)/, [\"white\", \"comment\", \"number\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(\\d{1,9}\\.)(\\s+)/, [\"white\", \"comment\", \"number\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(-{3,}|\\*{3,}|_{3,})$/, [\"white\", \"comment\", \"keyword\"]],\n      [/`{3,}(\\s.*)?$/, { token: \"string\", next: \"@codeblock_backtick\" }],\n      [/~{3,}(\\s.*)?$/, { token: \"string\", next: \"@codeblock_tilde\" }],\n      [\n        /`{3,}(\\S+).*$/,\n        { token: \"string\", next: \"@codeblock_highlight_backtick\", nextEmbedded: \"$1\" }\n      ],\n      [\n        /~{3,}(\\S+).*$/,\n        { token: \"string\", next: \"@codeblock_highlight_tilde\", nextEmbedded: \"$1\" }\n      ],\n      [/^(\\s*)(-{4,})$/, [\"white\", \"comment\"]],\n      [/^(\\s*)(>+)/, [\"white\", \"comment\"]],\n      { include: \"content\" }\n    ],\n    content: [\n      [\n        /(\\[)(.+)(]\\()(.+)(\\s+\".*\")(\\))/,\n        [\"\", \"string.link\", \"\", \"type.identifier\", \"string.link\", \"\"]\n      ],\n      [/(\\[)(.+)(]\\()(.+)(\\))/, [\"\", \"type.identifier\", \"\", \"string.link\", \"\"]],\n      [/(\\[)(.+)(]\\[)(.+)(])/, [\"\", \"type.identifier\", \"\", \"type.identifier\", \"\"]],\n      [/(\\[)(.+)(]:\\s+)(\\S*)/, [\"\", \"type.identifier\", \"\", \"string.link\"]],\n      [/(\\[)(.+)(])/, [\"\", \"type.identifier\", \"\"]],\n      [/`.*`/, \"variable.source\"],\n      [/_/, { token: \"emphasis\", next: \"@emphasis_underscore\" }],\n      [/\\*(?!\\*)/, { token: \"emphasis\", next: \"@emphasis_asterisk\" }],\n      [/\\*\\*/, { token: \"strong\", next: \"@strong\" }],\n      [/{/, { token: \"delimiter.bracket\", next: \"@expression\", nextEmbedded: \"js\" }]\n    ],\n    import: [[/'\\s*(;|$)/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }]],\n    expression: [\n      [/{/, { token: \"delimiter.bracket\", next: \"@expression\" }],\n      [/}/, { token: \"delimiter.bracket\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    export: [[/^\\s*$/, { token: \"delimiter.bracket\", next: \"@pop\", nextEmbedded: \"@pop\" }]],\n    jsx: [\n      [/\\s+/, \"\"],\n      [/(\\w+)(=)(\"(?:[^\"\\\\]|\\\\.)*\")/, [\"attribute.name\", \"operator\", \"string\"]],\n      [/(\\w+)(=)('(?:[^'\\\\]|\\\\.)*')/, [\"attribute.name\", \"operator\", \"string\"]],\n      [/(\\w+(?=\\s|>|={|$))/, [\"attribute.name\"]],\n      [/={/, { token: \"delimiter.bracket\", next: \"@expression\", nextEmbedded: \"js\" }],\n      [/>/, { token: \"type.identifier\", next: \"@pop\" }]\n    ],\n    header: [\n      [/.$/, { token: \"keyword\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"keyword\" }]\n    ],\n    strong: [\n      [/\\*\\*/, { token: \"strong\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"strong\" }]\n    ],\n    emphasis_underscore: [\n      [/_/, { token: \"emphasis\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"emphasis\" }]\n    ],\n    emphasis_asterisk: [\n      [/\\*(?!\\*)/, { token: \"emphasis\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"emphasis\" }]\n    ],\n    frontmatter: [[/^---$/, { token: \"meta.content\", nextEmbedded: \"@pop\", next: \"@pop\" }]],\n    codeblock_highlight_backtick: [\n      [/\\s*`{3,}\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_highlight_tilde: [\n      [/\\s*~{3,}\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_backtick: [\n      [/\\s*`{3,}\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_tilde: [\n      [/\\s*~{3,}\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/mdx/mdx.js\n"));

/***/ })

}]);