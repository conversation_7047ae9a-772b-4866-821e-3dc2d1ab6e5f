"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/context/EditPermissionContext.tsx":
/*!***********************************************!*\
  !*** ./src/context/EditPermissionContext.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditPermissionProvider: () => (/* binding */ EditPermissionProvider),\n/* harmony export */   useEditPermission: () => (/* binding */ useEditPermission)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSocketService */ \"(app-pages-browser)/./src/hooks/useSocketService.ts\");\n/* __next_internal_client_entry_do_not_use__ EditPermissionProvider,useEditPermission auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst EditPermissionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction EditPermissionProvider(param) {\n    let { children } = param;\n    _s();\n    const [canEdit, setCanEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTeacher, setIsTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Use the socket service hook\n    const { socketService, isReady: socketReady, isConnected } = (0,_hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__.useSocketService)();\n    // Compute permission badge based on current state\n    const permissionBadge = isTeacher ? 'teacher' : canEdit ? 'edit-access' : 'view-only';\n    // Grant edit permission to a student\n    const grantEditPermission = (targetSocketId)=>{\n        if (!isTeacher) {\n            console.warn('Only teachers can grant edit permissions');\n            return;\n        }\n        if (!socketService || !socketReady) {\n            console.warn('Socket service not ready, cannot grant edit permission');\n            return;\n        }\n        const roomId1 =  true ? window.localStorage.getItem('currentRoomId') : 0;\n        if (!roomId1) {\n            console.error('No room ID found');\n            return;\n        }\n        console.log(\"Granting edit permission to \".concat(targetSocketId));\n        try {\n            socketService.emit('grant-edit-permission', {\n                roomId: roomId1,\n                targetSocketId\n            });\n        } catch (error) {\n            console.error('Error granting edit permission:', error);\n        }\n    };\n    // Revoke edit permission from a student\n    const revokeEditPermission = (targetSocketId)=>{\n        if (!isTeacher) {\n            console.warn('Only teachers can revoke edit permissions');\n            return;\n        }\n        if (!socketService || !socketReady) {\n            console.warn('Socket service not ready, cannot revoke edit permission');\n            return;\n        }\n        const roomId1 =  true ? window.localStorage.getItem('currentRoomId') : 0;\n        if (!roomId1) {\n            console.error('No room ID found');\n            return;\n        }\n        console.log(\"Revoking edit permission from \".concat(targetSocketId));\n        try {\n            socketService.emit('revoke-edit-permission', {\n                roomId: roomId1,\n                targetSocketId\n            });\n        } catch (error) {\n            console.error('Error revoking edit permission:', error);\n        }\n    };\n    // Legacy method for backward compatibility\n    const setEditPermission = (targetSocketId, canEdit)=>{\n        if (canEdit) {\n            grantEditPermission(targetSocketId);\n        } else {\n            revokeEditPermission(targetSocketId);\n        }\n    };\n    const updateUserPermission = (socketId, canEdit)=>{\n        setUsers((prevUsers)=>prevUsers.map((user)=>user.socketId === socketId ? {\n                    ...user,\n                    canEdit\n                } : user));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPermissionProvider.useEffect\": ()=>{\n            // Only set up listeners when socket is ready\n            if (!socketReady || !socketService) {\n                console.log('Waiting for socket to be ready for edit permissions...');\n                return;\n            }\n            // Listen for edit permission changes\n            const handleEditPermission = {\n                \"EditPermissionProvider.useEffect.handleEditPermission\": (data)=>{\n                    console.log('Received edit permission update:', data);\n                    setCanEdit(data.canEdit);\n                }\n            }[\"EditPermissionProvider.useEffect.handleEditPermission\"];\n            // Listen for permission updates (new event)\n            const handlePermissionUpdated = {\n                \"EditPermissionProvider.useEffect.handlePermissionUpdated\": (data)=>{\n                    console.log('Received permission-updated event:', data);\n                    setCanEdit(data.canEdit);\n                }\n            }[\"EditPermissionProvider.useEffect.handlePermissionUpdated\"];\n            // Listen for student list updates (for teachers)\n            const handleUpdateStudentList = {\n                \"EditPermissionProvider.useEffect.handleUpdateStudentList\": (data)=>{\n                    console.log('Received update-student-list event:', data);\n                    setStudents(data.students);\n                }\n            }[\"EditPermissionProvider.useEffect.handleUpdateStudentList\"];\n            // Listen for room users updates (includes permission info)\n            const handleRoomUsersUpdated = {\n                \"EditPermissionProvider.useEffect.handleRoomUsersUpdated\": (data)=>{\n                    console.log('Room users updated with permissions:', data);\n                    setUsers(data.users);\n                }\n            }[\"EditPermissionProvider.useEffect.handleRoomUsersUpdated\"];\n            // Set up socket listeners with null checks\n            try {\n                socketService.on('edit-permission', handleEditPermission);\n                socketService.on('permission-updated', handlePermissionUpdated);\n                socketService.on('update-student-list', handleUpdateStudentList);\n                socketService.on('room-users-updated', handleRoomUsersUpdated);\n                console.log('Enhanced permission socket listeners set up successfully');\n                // If this is a teacher, request the initial student list\n                if (isTeacher && roomId) {\n                    console.log('Teacher detected, requesting initial student list...');\n                    socketService.emit('request-student-list', {\n                        roomId\n                    });\n                }\n            } catch (error) {\n                console.error('Error setting up socket listeners:', error);\n            }\n            // Cleanup listeners\n            return ({\n                \"EditPermissionProvider.useEffect\": ()=>{\n                    try {\n                        if (socketService) {\n                            socketService.off('edit-permission', handleEditPermission);\n                            socketService.off('permission-updated', handlePermissionUpdated);\n                            socketService.off('update-student-list', handleUpdateStudentList);\n                            socketService.off('room-users-updated', handleRoomUsersUpdated);\n                            console.log('Enhanced permission socket listeners cleaned up');\n                        }\n                    } catch (error) {\n                        console.error('Error cleaning up socket listeners:', error);\n                    }\n                }\n            })[\"EditPermissionProvider.useEffect\"];\n        }\n    }[\"EditPermissionProvider.useEffect\"], [\n        socketReady\n    ]);\n    // Log permission changes for debugging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPermissionProvider.useEffect\": ()=>{\n            console.log(\"Edit permission state: canEdit=\".concat(canEdit, \", isTeacher=\").concat(isTeacher, \", socketReady=\").concat(socketReady, \", isConnected=\").concat(isConnected));\n        }\n    }[\"EditPermissionProvider.useEffect\"], [\n        canEdit,\n        isTeacher,\n        socketReady,\n        isConnected\n    ]);\n    const value = {\n        canEdit,\n        isTeacher,\n        users,\n        students,\n        permissionBadge,\n        grantEditPermission,\n        revokeEditPermission,\n        setEditPermission,\n        updateUserPermission,\n        setUsers,\n        setStudents,\n        setCanEdit,\n        setIsTeacher\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditPermissionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\context\\\\EditPermissionContext.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_s(EditPermissionProvider, \"W8VZUkPAjYm7fgg7tAjRyWJmsnI=\", false, function() {\n    return [\n        _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__.useSocketService\n    ];\n});\n_c = EditPermissionProvider;\nfunction useEditPermission() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(EditPermissionContext);\n    if (context === undefined) {\n        throw new Error('useEditPermission must be used within an EditPermissionProvider');\n    }\n    return context;\n}\n_s1(useEditPermission, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"EditPermissionProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/context/EditPermissionContext.tsx\n"));

/***/ })

});