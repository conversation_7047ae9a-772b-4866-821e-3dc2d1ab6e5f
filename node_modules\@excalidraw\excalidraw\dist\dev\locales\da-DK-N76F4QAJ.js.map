{"version": 3, "sources": ["../../../locales/da-DK.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Indsæt\",\n    \"pasteAsPlaintext\": \"Indsæt som klartekst\",\n    \"pasteCharts\": \"Indsæt diagrammer\",\n    \"selectAll\": \"Marker alle\",\n    \"multiSelect\": \"Tilføj element til markering\",\n    \"moveCanvas\": \"Flyt lærred\",\n    \"cut\": \"Klip\",\n    \"copy\": \"Kopier\",\n    \"copyAsPng\": \"<PERSON>pie<PERSON> til klippebord som PNG\",\n    \"copyAsSvg\": \"Kopier til klippebord som SVG\",\n    \"copyText\": \"<PERSON><PERSON><PERSON><PERSON> til udklipsholder som tekst\",\n    \"copySource\": \"<PERSON>pi<PERSON><PERSON> kilde til udklipsholder\",\n    \"convertToCode\": \"Konvertér til kode\",\n    \"bringForward\": \"Flyt fremad\",\n    \"sendToBack\": \"Placer bagest\",\n    \"bringToFront\": \"Placer forrest\",\n    \"sendBackward\": \"Send bagud\",\n    \"delete\": \"Fjern\",\n    \"copyStyles\": \"<PERSON>pier stil\",\n    \"pasteStyles\": \"Indsæt stil\",\n    \"stroke\": \"Linje\",\n    \"background\": \"Baggrund\",\n    \"fill\": \"Udfyld\",\n    \"strokeWidth\": \"<PERSON>jebredde\",\n    \"strokeStyle\": \"Linjeform\",\n    \"strokeStyle_solid\": \"Solid\",\n    \"strokeStyle_dashed\": \"Stiplet\",\n    \"strokeStyle_dotted\": \"Prikket\",\n    \"sloppiness\": \"Sjuskethed\",\n    \"opacity\": \"Gennemsigtighed\",\n    \"textAlign\": \"Tekstjustering\",\n    \"edges\": \"Kanter\",\n    \"sharp\": \"Skarp\",\n    \"round\": \"Rund\",\n    \"arrowheads\": \"Pilehoveder\",\n    \"arrowhead_none\": \"Ingen\",\n    \"arrowhead_arrow\": \"Pil\",\n    \"arrowhead_bar\": \"Bjælke\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Trekant\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Skriftstørrelse\",\n    \"fontFamily\": \"Skrifttypefamilie\",\n    \"addWatermark\": \"Tilføj \\\"Lavet med Excalidraw\\\"\",\n    \"handDrawn\": \"Hånd-tegnet\",\n    \"normal\": \"Normal\",\n    \"code\": \"Kode\",\n    \"small\": \"Lille\",\n    \"medium\": \"Mellem\",\n    \"large\": \"Stor\",\n    \"veryLarge\": \"Meget stor\",\n    \"solid\": \"Solid\",\n    \"hachure\": \"Skravering\",\n    \"zigzag\": \"Zigzag\",\n    \"crossHatch\": \"Krydsskravering\",\n    \"thin\": \"Tynd\",\n    \"bold\": \"Fed\",\n    \"left\": \"Venstre\",\n    \"center\": \"Centrere\",\n    \"right\": \"Højre\",\n    \"extraBold\": \"Extra fed\",\n    \"architect\": \"Arkitekt\",\n    \"artist\": \"Kunstner\",\n    \"cartoonist\": \"Tegneserietegner\",\n    \"fileTitle\": \"Filnavn\",\n    \"colorPicker\": \"Farvevælger\",\n    \"canvasColors\": \"Brugt på lærred\",\n    \"canvasBackground\": \"Lærredsbaggrund\",\n    \"drawingCanvas\": \"Tegnelærred\",\n    \"layers\": \"Lag\",\n    \"actions\": \"Handlinger\",\n    \"language\": \"Sprog\",\n    \"liveCollaboration\": \"Live samarbejde...\",\n    \"duplicateSelection\": \"Duplikér\",\n    \"untitled\": \"Unavngivet\",\n    \"name\": \"Navn\",\n    \"yourName\": \"Dit navn\",\n    \"madeWithExcalidraw\": \"Fremstillet med Excalidraw\",\n    \"group\": \"Grupper valgte\",\n    \"ungroup\": \"Opløs gruppe\",\n    \"collaborators\": \"Deltagere\",\n    \"showGrid\": \"Vis gitter\",\n    \"addToLibrary\": \"Føj til Bibliotek\",\n    \"removeFromLibrary\": \"Fjern fra biblioteket\",\n    \"libraryLoadingMessage\": \"Indlæser bibliotek…\",\n    \"libraries\": \"Gennemse biblioteker\",\n    \"loadingScene\": \"Indlæser scene…\",\n    \"align\": \"Justér\",\n    \"alignTop\": \"Juster til top\",\n    \"alignBottom\": \"Juster til bund\",\n    \"alignLeft\": \"Venstrejusteret\",\n    \"alignRight\": \"Juster højre\",\n    \"centerVertically\": \"Center vertikalt\",\n    \"centerHorizontally\": \"Vandret centreret\",\n    \"distributeHorizontally\": \"Distribuer vandret\",\n    \"distributeVertically\": \"Distribuer lodret\",\n    \"flipHorizontal\": \"Spejlvend horisontalt\",\n    \"flipVertical\": \"Vend lodret\",\n    \"viewMode\": \"Visningstilstand\",\n    \"share\": \"Del\",\n    \"showStroke\": \"Vis stregfarve-vælger\",\n    \"showBackground\": \"Vis baggrundsfarve-vælger\",\n    \"toggleTheme\": \"Skift tema\",\n    \"personalLib\": \"Personligt bibliotek\",\n    \"excalidrawLib\": \"Excalidraw Bibliotek\",\n    \"decreaseFontSize\": \"Gør skriften mindre\",\n    \"increaseFontSize\": \"Gør skriften større\",\n    \"unbindText\": \"Frigør tekst\",\n    \"bindText\": \"Bind tekst til beholderen\",\n    \"createContainerFromText\": \"Ombryd tekst i en beholder\",\n    \"link\": {\n      \"edit\": \"Redigér link\",\n      \"editEmbed\": \"Redigér link & indlejret\",\n      \"create\": \"Link oprettet\",\n      \"createEmbed\": \"Opret link & indlejret\",\n      \"label\": \"Links\",\n      \"labelEmbed\": \"Link & indlejret\",\n      \"empty\": \"Intet link angivet\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Rediger Linje\",\n      \"exit\": \"Afslut linjeeditor\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Lås\",\n      \"unlock\": \"Lås op\",\n      \"lockAll\": \"Lås alle\",\n      \"unlockAll\": \"Lås alle op\"\n    },\n    \"statusPublished\": \"Udgiver\",\n    \"sidebarLock\": \"Hold sidepanel åben\",\n    \"selectAllElementsInFrame\": \"Vælg alle elementer i rammen\",\n    \"removeAllElementsFromFrame\": \"Fjern alle elementer fra ramme\",\n    \"eyeDropper\": \"Vælg farve fra lærred\",\n    \"textToDiagram\": \"Tekst til diagram\",\n    \"prompt\": \"Prompt\"\n  },\n  \"library\": {\n    \"noItems\": \"Ingen varer tilføjet endnu...\",\n    \"hint_emptyLibrary\": \"Vælg et element på lærred for at tilføje det her, eller installer et bibliotek fra det offentlige arkiv, nedenfor.\",\n    \"hint_emptyPrivateLibrary\": \"Vælg et element på lærred for at tilføje det her.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Nulstil lærredet\",\n    \"exportJSON\": \"Eksportér til fil\",\n    \"exportImage\": \"Eksporter billede...\",\n    \"export\": \"Gem til...\",\n    \"copyToClipboard\": \"Kopier til klippebord\",\n    \"save\": \"Gem til nuværende fil\",\n    \"saveAs\": \"Gem som\",\n    \"load\": \"Åbn\",\n    \"getShareableLink\": \"Lav et delbart link\",\n    \"close\": \"Luk\",\n    \"selectLanguage\": \"Vælg sprog\",\n    \"scrollBackToContent\": \"Scroll tilbage til indhold\",\n    \"zoomIn\": \"Zoom ind\",\n    \"zoomOut\": \"Zoom ud\",\n    \"resetZoom\": \"Nulstil zoom\",\n    \"menu\": \"Menu\",\n    \"done\": \"Færdig\",\n    \"edit\": \"Rediger\",\n    \"undo\": \"Fortryd\",\n    \"redo\": \"Gendan\",\n    \"resetLibrary\": \"Nulstil bibliotek\",\n    \"createNewRoom\": \"Opret nyt rum\",\n    \"fullScreen\": \"Fuld skærm\",\n    \"darkMode\": \"Mørk tilstand\",\n    \"lightMode\": \"Lys baggrund\",\n    \"zenMode\": \"Zentilstand\",\n    \"objectsSnapMode\": \"Fastgør til objekter\",\n    \"exitZenMode\": \"Stop zentilstand\",\n    \"cancel\": \"Annuller\",\n    \"clear\": \"Ryd\",\n    \"remove\": \"Fjern\",\n    \"embed\": \"Slå indlejring til/fra\",\n    \"publishLibrary\": \"Publicér\",\n    \"submit\": \"Gem\",\n    \"confirm\": \"Bekræft\",\n    \"embeddableInteractionButton\": \"Klik for at interagere\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Dette vil rydde hele lærredet. Er du sikker?\",\n    \"couldNotCreateShareableLink\": \"Kunne ikke oprette delbart link.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Kunne ikke oprette delbart link: scenen er for stor\",\n    \"couldNotLoadInvalidFile\": \"Kunne ikke indlæse ugyldig fil\",\n    \"importBackendFailed\": \"Import fra backend mislykkedes.\",\n    \"cannotExportEmptyCanvas\": \"Kan ikke eksportere tomt lærred.\",\n    \"couldNotCopyToClipboard\": \"Kunne ikke kopiere til udklipsholderen.\",\n    \"decryptFailed\": \"Kunne ikke dekryptere data.\",\n    \"uploadedSecurly\": \"Upload er blevet sikret med ende-til-ende kryptering, hvilket betyder, at Excalidraw server og tredjeparter ikke kan læse indholdet.\",\n    \"loadSceneOverridePrompt\": \"Indlæsning af ekstern tegning erstatter dit eksisterende indhold. Ønsker du at fortsætte?\",\n    \"collabStopOverridePrompt\": \"Stopper sessionen vil overskrive din tidligere, lokalt gemte tegning. Er du sikker?\\n\\n(Hvis du ønsker at beholde din lokale tegning, skal du blot lukke browserfanen i stedet.)\",\n    \"errorAddingToLibrary\": \"Kunne ikke tilføje element til biblioteket\",\n    \"errorRemovingFromLibrary\": \"Kunne ikke fjerne element fra biblioteket\",\n    \"confirmAddLibrary\": \"Dette vil tilføje {{numShapes}} form(er) til dit bibliotek. Er du sikker?\",\n    \"imageDoesNotContainScene\": \"Dette billede synes ikke at indeholde scene data. Har du aktiveret scene indlejring under eksport?\",\n    \"cannotRestoreFromImage\": \"Scene kunne ikke gendannes fra denne billedfil\",\n    \"invalidSceneUrl\": \"Kunne ikke importere scene fra den angivne URL. Det er enten misdannet eller indeholder ikke gyldige Excalidraw JSON data.\",\n    \"resetLibrary\": \"Dette vil rydde hele lærredet. Er du sikker?\",\n    \"removeItemsFromsLibrary\": \"Slet {{count}} vare(r) fra biblioteket?\",\n    \"invalidEncryptionKey\": \"Krypteringsnøglen skal være på 22 tegn. Live-samarbejde er deaktiveret.\",\n    \"collabOfflineWarning\": \"Ingen internetforbindelse tilgængelig.\\nDine ændringer vil ikke blive gemt!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Filtypen er ikke understøttet.\",\n    \"imageInsertError\": \"Billedet kunne ikke indsættes. Prøv igen senere...\",\n    \"fileTooBig\": \"Filen er for stor. Maksimal tilladt størrelse er {{maxSize}}.\",\n    \"svgImageInsertError\": \"Kunne ikke indsætte SVG-billede. SVG-markup'en ser ugyldig ud.\",\n    \"failedToFetchImage\": \"Dataene blev ikke hentet.\",\n    \"invalidSVGString\": \"Ugyldig SVG.\",\n    \"cannotResolveCollabServer\": \"Kunne ikke oprette forbindelse til samarbejdsserveren. Genindlæs siden og prøv igen.\",\n    \"importLibraryError\": \"Biblioteket kunne ikke indlæses\",\n    \"collabSaveFailed\": \"Kunne ikke gemme i databasen. Hvis problemerne fortsætter, bør du gemme din fil lokalt for at sikre, at du ikke mister dit arbejde.\",\n    \"collabSaveFailed_sizeExceeded\": \"Kunne ikke gemme i databasen, lærredet lader til at være for stort. Du bør gemme filen lokalt for at sikre, at du ikke mister dit arbejde.\",\n    \"imageToolNotSupported\": \"Billeder er deaktiveret.\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Det ser ud til, at du bruger Brave browser med indstillingen <bold>Aggressively Block Fingerprinting</bold> aktiveret.\",\n      \"line2\": \"Dette kan resultere i brud på <bold>tekstelementerne</bold> i dine tegninger.\",\n      \"line3\": \"Vi anbefaler kraftigt at deaktivere denne indstilling. Du kan følge <link>disse trin</link> om, hvordan du gør det.\",\n      \"line4\": \"Hvis deaktivering af denne indstilling ikke løser visning af tekstelementer, åbn venligst et <issueLink>issue</issueLink> på vores GitHub, eller skriv os på <discordLink>Discord</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"Indlejringselementer kan ikke tilføjes til biblioteket.\",\n      \"iframe\": \"IFrame elementer kan ikke tilføjes til biblioteket.\",\n      \"image\": \"Understøttelse af at tilføje billeder til biblioteket kommer snart!\"\n    },\n    \"asyncPasteFailedOnRead\": \"Kunne ikke indsætte (kan ikke læse fra systemets udklipsholder).\",\n    \"asyncPasteFailedOnParse\": \"Kunne ikke indsætte.\",\n    \"copyToSystemClipboardFailed\": \"Kunne ikke kopiere til udklipsholderen.\"\n  },\n  \"toolBar\": {\n    \"selection\": \"&Udvalg\",\n    \"image\": \"Indsæt billeder\",\n    \"rectangle\": \"Rektangler\",\n    \"diamond\": \"Diamanter\",\n    \"ellipse\": \"Ellipser\",\n    \"arrow\": \"Pile\",\n    \"line\": \"Linje\",\n    \"freedraw\": \"Tegn\",\n    \"text\": \"Tekster\",\n    \"library\": \"~Bibliotek\",\n    \"lock\": \"Behold valgte værktøj aktiv efter tegning\",\n    \"penMode\": \"Pen-tilstand - forhindrer berøring\",\n    \"link\": \"Tilføj/ Opdater link for en valgt form\",\n    \"eraser\": \"Slet\",\n    \"frame\": \"Rammeværktøj\",\n    \"magicframe\": \"Wireframe til kode\",\n    \"embeddable\": \"Web-indlejring\",\n    \"laser\": \"Lasermarkør\",\n    \"hand\": \"Hånd (panorering værktøj)\",\n    \"extraTools\": \"Flere værktøjer\",\n    \"mermaidToExcalidraw\": \"Mermaid til Excalidraw\",\n    \"magicSettings\": \"AI indstillinger\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Lærred handlinger\",\n    \"selectedShapeActions\": \"Valgte figurhandlinger\",\n    \"shapes\": \"Former\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"For at flytte lærred, hold musehjulet eller mellemrumstasten mens du trækker, eller brug håndværktøjet\",\n    \"linearElement\": \"Klik for at starte flere punkter, træk for enkelt linje\",\n    \"freeDraw\": \"Klik og træk, slip når du er færdig\",\n    \"text\": \"Tip: du kan også tilføje tekst ved at dobbeltklikke hvor som helst med det valgte værktøj\",\n    \"embeddable\": \"Klik på træk for at oprette en hjemmeside indlejret\",\n    \"text_selected\": \"\",\n    \"text_editing\": \"\",\n    \"linearElementMulti\": \"\",\n    \"lockAngle\": \"\",\n    \"resize\": \"\",\n    \"resizeImage\": \"\",\n    \"rotate\": \"\",\n    \"lineEditor_info\": \"\",\n    \"lineEditor_pointSelected\": \"\",\n    \"lineEditor_nothingSelected\": \"\",\n    \"placeImage\": \"\",\n    \"publishLibrary\": \"\",\n    \"bindTextToElement\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"eraserRevert\": \"\",\n    \"firefox_clipboard_write\": \"\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Kan ikke vise forhåndsvisning\",\n    \"canvasTooBig\": \"Lærredet kan være for stort.\",\n    \"canvasTooBigTip\": \"Tip: Prøv at flytte de fjerneste elementer lidt tættere sammen.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Der opstod en fejl. Prøv <button>at genindlæse siden</button>.\",\n    \"clearCanvasMessage\": \"\",\n    \"clearCanvasCaveat\": \"\",\n    \"trackedToSentry\": \"\",\n    \"openIssueMessage\": \"<button></button> Kopiere og indsæt venligst oplysningerne nedenfor i et GitHub problem.\",\n    \"sceneContent\": \"Scene indhold:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Du kan invitere folk til din nuværende scene, så de kan samarbejde med dig.\",\n    \"desc_privacy\": \"Bare rolig, sessionen bruger end-to-end kryptering, så uanset hvad du tegner vil det forblive privat. Ikke engang vores server vil kunne se, hvad du kommer op med.\",\n    \"button_startSession\": \"Start session\",\n    \"button_stopSession\": \"Stop session\",\n    \"desc_inProgressIntro\": \"Live-samarbejde session er nu begyndt.\",\n    \"desc_shareLink\": \"Del dette link med enhver, du ønsker at samarbejde med:\",\n    \"desc_exitSession\": \"\",\n    \"shareTitle\": \"\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Fejl\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Gem til disk\",\n    \"disk_details\": \"\",\n    \"disk_button\": \"\",\n    \"link_title\": \"\",\n    \"link_details\": \"\",\n    \"link_button\": \"\",\n    \"excalidrawplus_description\": \"\",\n    \"excalidrawplus_button\": \"\",\n    \"excalidrawplus_exportError\": \"\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Læs vores blog\",\n    \"click\": \"\",\n    \"deepSelect\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"curvedArrow\": \"\",\n    \"curvedLine\": \"\",\n    \"documentation\": \"\",\n    \"doubleClick\": \"\",\n    \"drag\": \"\",\n    \"editor\": \"\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"\",\n    \"howto\": \"\",\n    \"or\": \"\",\n    \"preventBinding\": \"\",\n    \"tools\": \"\",\n    \"shortcuts\": \"\",\n    \"textFinish\": \"\",\n    \"textNewLine\": \"\",\n    \"title\": \"\",\n    \"view\": \"\",\n    \"zoomToFit\": \"\",\n    \"zoomToSelection\": \"\",\n    \"toggleElementLock\": \"\",\n    \"movePageUpDown\": \"\",\n    \"movePageLeftRight\": \"\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"\"\n  },\n  \"publishDialog\": {\n    \"title\": \"\",\n    \"itemName\": \"\",\n    \"authorName\": \"\",\n    \"githubUsername\": \"\",\n    \"twitterUsername\": \"\",\n    \"libraryName\": \"\",\n    \"libraryDesc\": \"\",\n    \"website\": \"\",\n    \"placeholder\": {\n      \"authorName\": \"\",\n      \"libraryName\": \"\",\n      \"libraryDesc\": \"\",\n      \"githubHandle\": \"\",\n      \"twitterHandle\": \"\",\n      \"website\": \"\"\n    },\n    \"errors\": {\n      \"required\": \"\",\n      \"website\": \"\"\n    },\n    \"noteDescription\": \"\",\n    \"noteGuidelines\": \"\",\n    \"noteLicense\": \"\",\n    \"noteItems\": \"\",\n    \"atleastOneLibItem\": \"\",\n    \"republishWarning\": \"\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"\",\n    \"content\": \"\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"\",\n    \"removeItemsFromLib\": \"\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"\",\n    \"link\": \"\"\n  },\n  \"stats\": {\n    \"angle\": \"\",\n    \"element\": \"\",\n    \"elements\": \"\",\n    \"height\": \"\",\n    \"scene\": \"\",\n    \"selected\": \"\",\n    \"storage\": \"\",\n    \"title\": \"Statistik for nørder\",\n    \"total\": \"\",\n    \"version\": \"\",\n    \"versionCopy\": \"Klik for at kopiere\",\n    \"versionNotAvailable\": \"\",\n    \"width\": \"Bredde\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"\",\n    \"copyStyles\": \"Kopieret stilarter.\",\n    \"copyToClipboard\": \"Kopieret til klippebord.\",\n    \"copyToClipboardAsPng\": \"Kopieret {{exportSelection}} til klippebord som PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Fil gemt.\",\n    \"fileSavedToFilename\": \"Gemt som {filename}\",\n    \"canvas\": \"canvas\",\n    \"selection\": \"markering\",\n    \"pasteAsSingleElement\": \"\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"\",\n    \"black\": \"\",\n    \"white\": \"\",\n    \"red\": \"\",\n    \"pink\": \"\",\n    \"grape\": \"\",\n    \"violet\": \"\",\n    \"gray\": \"\",\n    \"blue\": \"\",\n    \"cyan\": \"\",\n    \"teal\": \"\",\n    \"green\": \"\",\n    \"yellow\": \"\",\n    \"orange\": \"\",\n    \"bronze\": \"\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"\",\n      \"center_heading_plus\": \"\",\n      \"menuHint\": \"\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"\",\n      \"center_heading\": \"\",\n      \"toolbarHint\": \"\",\n      \"helpHint\": \"\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"\",\n    \"shades\": \"\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}