"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var monaco_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! monaco-editor */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.main.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlignLeft,FiCode,FiCopy,FiDownload,FiHelpCircle,FiLoader,FiMoon,FiPlay,FiSearch,FiSun,FiUpload,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_socketService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/socketService */ \"(app-pages-browser)/./src/services/socketService.ts\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/throttle */ \"(app-pages-browser)/./node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/SocketContext */ \"(app-pages-browser)/./src/context/SocketContext.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_formatCode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/formatCode */ \"(app-pages-browser)/./src/utils/formatCode.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/ThemeContext */ \"(app-pages-browser)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CodeEditor(param) {\n    let { roomId, username: initialUsername } = param;\n    _s();\n    const { isConnected } = (0,_context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket)();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"// Start coding...\");\n    // Use a ref to always track the latest code value\n    const latestCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"// Start coding...\");\n    // Get username from localStorage if available, otherwise use initialUsername\n    const storedUsername =  true ? localStorage.getItem(\"username\") : 0;\n    console.log(\"Initial username: \".concat(initialUsername, \", Stored username: \").concat(storedUsername));\n    // Track the username internally to handle server validation\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUsername || initialUsername);\n    const [typingUser, setTypingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeUsers, setActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUserList, setShowUserList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"javascript\");\n    const [copySuccess, setCopySuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [teacherSelectionDecorations, setTeacherSelectionDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [teacherTextHighlightDecorations, setTeacherTextHighlightDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [teacherCursorPosition, setTeacherCursorPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Code execution states\n    const [isExecuting, setIsExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionOutput, setExecutionOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [executionError, setExecutionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOutput, setShowOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Piston API runtimes\n    const [runtimes, setRuntimes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [runtimesLoaded, setRuntimesLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    // Theme and minimap state (ensure these are always defined at the top)\n    const { theme, setTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme)();\n    const [showShortcuts, setShowShortcuts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [minimapEnabled, setMinimapEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Language-specific code snippets\n    const SNIPPETS = {\n        javascript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            }\n        ],\n        typescript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction(): void {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            },\n            {\n                label: \"Type Declaration\",\n                value: \"type MyType = {\\n  key: string;\\n};\"\n            }\n        ],\n        python3: [\n            {\n                label: \"Function\",\n                value: \"def my_function():\\n    # code\\n    pass\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in range(10):\\n    # code\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition:\\n    # code\"\n            },\n            {\n                label: \"Print\",\n                value: \"print('Hello, World!')\"\n            }\n        ],\n        java: [\n            {\n                label: \"Main Method\",\n                value: \"public static void main(String[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"System.out.println(\\\"Hello, World!\\\");\"\n            }\n        ],\n        csharp: [\n            {\n                label: \"Main Method\",\n                value: \"static void Main(string[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"Console.WriteLine(\\\"Hello, World!\\\");\"\n            }\n        ],\n        c: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"printf(\\\"Hello, World!\\\\n\\\");\"\n            }\n        ],\n        cpp: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"std::cout << \\\"Hello, World!\\\" << std::endl;\"\n            }\n        ],\n        go: [\n            {\n                label: \"Main Function\",\n                value: \"func main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i := 0; i < 10; i++ {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"fmt.Println(\\\"Hello, World!\\\")\"\n            }\n        ],\n        ruby: [\n            {\n                label: \"Method\",\n                value: \"def my_method\\n  # code\\nend\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..9\\n  # code\\nend\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition\\n  # code\\nend\"\n            },\n            {\n                label: \"Print\",\n                value: \"puts 'Hello, World!'\"\n            }\n        ],\n        rust: [\n            {\n                label: \"Main Function\",\n                value: \"fn main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..10 {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"println!(\\\"Hello, World!\\\");\"\n            }\n        ],\n        php: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for ($i = 0; $i < 10; $i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if ($condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"echo 'Hello, World!';\"\n            }\n        ]\n    };\n    // Declare the `socketService` instance at the top of the file\n    const socketService = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n    // Leave room handler\n    const handleLeaveRoom = ()=>{\n        socketService.leaveRoom(roomId);\n        router.push(\"/dashboard\");\n    };\n    // Handle editor mounting\n    const handleEditorDidMount = (editor)=>{\n        editorRef.current = editor;\n        setIsLoading(false);\n        // Auto-focus the editor after mounting\n        setTimeout(()=>{\n            if (editor) {\n                editor.focus();\n                // Add keyboard shortcut (Ctrl+Enter) to run code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.CtrlCmd | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.Enter, ()=>{\n                    executeCode();\n                });\n                // Add keyboard shortcut (Shift+Alt+F) to format code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Shift | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Alt | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.KeyF, ()=>{\n                    formatCurrentCode();\n                });\n                // Add teacher cursor and selection listeners\n                if (userRole === 'teacher') {\n                    // Track cursor position changes\n                    editor.onDidChangeCursorPosition((e)=>{\n                        const position = e.position;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        if (roomId) {\n                            // Send cursor position to students\n                            socketServiceInstance.sendTeacherCursorPosition(roomId, {\n                                lineNumber: position.lineNumber,\n                                column: position.column\n                            });\n                        }\n                    });\n                    // Track selection changes for text highlighting\n                    editor.onDidChangeCursorSelection((e)=>{\n                        console.log('🎯 Teacher cursor selection changed:', {\n                            selection: e.selection,\n                            isEmpty: e.selection.isEmpty(),\n                            userRole: userRole,\n                            roomId: roomId\n                        });\n                        const selection = e.selection;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        if (!selection.isEmpty() && roomId) {\n                            // Send text highlight to students\n                            console.log('📤 Teacher sending text selection to students:', {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            });\n                            socketServiceInstance.sendTeacherTextHighlight(roomId, {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            });\n                        } else if (roomId) {\n                            // Clear text highlight when teacher deselects\n                            console.log('🧹 Teacher clearing text selection');\n                            socketServiceInstance.clearTeacherTextHighlight(roomId);\n                        }\n                    });\n                }\n            }\n        }, 500) // Small delay to ensure the editor is fully ready\n        ;\n    };\n    // Track if the change is from a remote update\n    const isRemoteUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Create a throttled version of the code change handler\n    // This prevents sending too many updates when typing quickly\n    // but ensures updates are sent at regular intervals\n    const throttledCodeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default()({\n        \"CodeEditor.useRef\": (code)=>{\n            if (roomId && isConnected) {\n                console.log(\"Sending throttled code update to room \".concat(roomId, \", length: \").concat(code.length));\n                socketService.sendCodeChange(roomId, code);\n            }\n        }\n    }[\"CodeEditor.useRef\"], 50) // Use throttle with a short interval for more responsive updates\n    ).current;\n    // Create a debounced version of the typing notification\n    const debouncedTypingNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default()({\n        \"CodeEditor.useCallback[debouncedTypingNotification]\": ()=>{\n            if (roomId && isConnected) {\n                // Always send the current username with typing notifications\n                console.log(\"Sending typing notification with username: \".concat(username));\n                socketService.sendTyping(roomId, username);\n            }\n        }\n    }[\"CodeEditor.useCallback[debouncedTypingNotification]\"], 1000), [\n        roomId,\n        isConnected,\n        username\n    ]);\n    // Handle editor change\n    const handleEditorChange = (value)=>{\n        if (typeof value !== \"string\") return;\n        // Make sure loading is off during code changes\n        setIsLoading(false);\n        // If this change is from a remote update, just update the state and return\n        if (isRemoteUpdate.current) {\n            console.log(\"Handling remote update, not emitting\");\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            isRemoteUpdate.current = false;\n            return;\n        }\n        // Only process if the value actually changed\n        if (value !== latestCodeRef.current) {\n            // Update local state immediately\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            // Send typing notification (debounced)\n            debouncedTypingNotification();\n            // Send code update (throttled)\n            throttledCodeChange(value);\n            // If not connected, try to reconnect\n            if (!isConnected) {\n                console.log(\"Socket not connected, attempting to reconnect\");\n                socketService.connect();\n            }\n        }\n    };\n    // Copy code to clipboard\n    const copyCodeToClipboard = ()=>{\n        if (navigator.clipboard && code) {\n            navigator.clipboard.writeText(code).then(()=>{\n                setCopySuccess(true);\n                setTimeout(()=>setCopySuccess(false), 2000);\n            }).catch((err)=>{\n                console.error('Failed to copy code: ', err);\n            });\n        }\n    };\n    // Toggle user list\n    const toggleUserList = ()=>{\n        setShowUserList((prev)=>!prev);\n    };\n    // Change language\n    const changeLanguage = (lang)=>{\n        setLanguage(lang);\n        // Log available runtimes for this language\n        if (runtimesLoaded && runtimes.length > 0) {\n            const availableRuntimes = runtimes.filter((runtime)=>runtime.language === lang || runtime.aliases && runtime.aliases.includes(lang));\n            if (availableRuntimes.length > 0) {\n                console.log(\"Available runtimes for \".concat(lang, \":\"), availableRuntimes);\n            } else {\n                console.log(\"No runtimes available for \".concat(lang));\n            }\n        }\n    };\n    // Format code\n    const formatCurrentCode = ()=>{\n        if (!code || !editorRef.current) return;\n        try {\n            // Format the code based on the current language\n            const formattedCode = (0,_utils_formatCode__WEBPACK_IMPORTED_MODULE_8__.formatCode)(code, language);\n            // Only update if the code actually changed\n            if (formattedCode !== code) {\n                // Update the editor\n                const model = editorRef.current.getModel();\n                if (model) {\n                    // Store current cursor position/selection\n                    const currentPosition = editorRef.current.getPosition();\n                    const currentSelection = editorRef.current.getSelection();\n                    // Update the editor content\n                    editorRef.current.executeEdits('format', [\n                        {\n                            range: model.getFullModelRange(),\n                            text: formattedCode,\n                            forceMoveMarkers: true\n                        }\n                    ]);\n                    // Restore cursor position if possible\n                    if (currentPosition) {\n                        editorRef.current.setPosition(currentPosition);\n                    }\n                    if (currentSelection) {\n                        editorRef.current.setSelection(currentSelection);\n                    }\n                    // Update state and ref\n                    setCode(formattedCode);\n                    latestCodeRef.current = formattedCode;\n                    // Send the formatted code to other users\n                    if (roomId && isConnected) {\n                        console.log(\"Sending formatted code to room \".concat(roomId, \", length: \").concat(formattedCode.length));\n                        socketService.sendCodeChange(roomId, formattedCode);\n                    }\n                    // Show a success message\n                    console.log('Code formatted successfully');\n                }\n            } else {\n                console.log('Code is already formatted');\n            }\n        } catch (error) {\n            console.error('Error formatting code:', error);\n        }\n    };\n    // Clear execution output\n    const clearOutput = ()=>{\n        setExecutionOutput(null);\n        setExecutionError(null);\n    };\n    // Fetch and store available runtimes from Piston API\n    const fetchRuntimes = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get('https://emkc.org/api/v2/piston/runtimes');\n            console.log('Piston API status:', response.status);\n            console.log('Available runtimes:', response.data);\n            // Store the runtimes in state\n            setRuntimes(response.data);\n            setRuntimesLoaded(true);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error fetching Piston API runtimes:', error);\n            return {\n                success: false,\n                error: axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) ? \"\".concat(error.message, \" - \").concat(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) || 'Unknown', \" \").concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText) || '') : String(error)\n            };\n        }\n    };\n    // Check if the Piston API is available\n    const checkPistonAPI = async ()=>{\n        // If we haven't loaded runtimes yet, fetch them\n        if (!runtimesLoaded) {\n            return await fetchRuntimes();\n        }\n        // If we already have runtimes, just return success\n        if (runtimes.length > 0) {\n            return {\n                success: true,\n                data: runtimes\n            };\n        }\n        // If we've tried to load runtimes but have none, try again\n        return await fetchRuntimes();\n    };\n    // Test the Piston API with a hardcoded sample payload\n    const testPistonAPI = async ()=>{\n        setIsExecuting(true);\n        setExecutionError(null);\n        setExecutionOutput(null);\n        setShowOutput(true);\n        try {\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Find JavaScript runtime\n            const jsRuntimes = apiStatus.data.filter((runtime)=>runtime.language === \"javascript\" || runtime.aliases && runtime.aliases.includes(\"javascript\"));\n            if (jsRuntimes.length === 0) {\n                setExecutionError('No JavaScript runtime found. Please try a different language.');\n                return;\n            }\n            const jsRuntime = jsRuntimes[0];\n            console.log(\"Using JavaScript runtime: \".concat(jsRuntime.language, \" \").concat(jsRuntime.version));\n            // Sample JavaScript code that should work\n            const samplePayload = {\n                language: jsRuntime.language,\n                version: jsRuntime.version,\n                files: [\n                    {\n                        name: \"main.js\",\n                        content: \"console.log('Hello, World!');\"\n                    }\n                ],\n                stdin: \"\",\n                args: []\n            };\n            console.log('Testing Piston API with sample payload:', JSON.stringify(samplePayload, null, 2));\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', samplePayload);\n            console.log('Sample execution response:', response.data);\n            if (response.data.run) {\n                setExecutionOutput(\"API Test Successful!\\n\\n\" + \"Output: \" + response.data.run.stdout + \"\\n\\n\" + \"The API is working correctly. You can now run your own code.\");\n            } else {\n                setExecutionError('API test failed. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error testing Piston API:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                setExecutionError(\"API Test Failed: \".concat(error.response.status, \" \").concat(error.response.statusText, \"\\n\\n\") + JSON.stringify(error.response.data, null, 2));\n            } else {\n                setExecutionError(error instanceof Error ? \"API Test Error: \".concat(error.message) : 'An unknown error occurred while testing the API.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // Execute code using Piston API\n    const executeCode = async ()=>{\n        if (!code || isExecuting) return;\n        // Map Monaco editor language to Piston API language\n        const languageMap = {\n            javascript: \"javascript\",\n            typescript: \"typescript\",\n            python: \"python3\",\n            python3: \"python3\",\n            java: \"java\",\n            csharp: \"csharp\",\n            c: \"c\",\n            cpp: \"cpp\",\n            go: \"go\",\n            ruby: \"ruby\",\n            rust: \"rust\",\n            php: \"php\"\n        };\n        // Get the mapped language\n        const pistonLanguage = languageMap[language] || language;\n        // Check if we have runtimes loaded\n        if (!runtimesLoaded || runtimes.length === 0) {\n            setExecutionError('Runtimes not loaded yet. Please try again in a moment.');\n            setShowOutput(true);\n            return;\n        }\n        // Find available runtimes for the selected language\n        const availableRuntimes = runtimes.filter((runtime)=>runtime.language === pistonLanguage || runtime.aliases && runtime.aliases.includes(pistonLanguage));\n        // Check if the language is supported\n        if (availableRuntimes.length === 0) {\n            // Get a list of supported languages from the runtimes\n            const supportedLanguages = [\n                ...new Set(runtimes.flatMap((runtime)=>[\n                        runtime.language,\n                        ...runtime.aliases || []\n                    ]))\n            ].sort();\n            setExecutionError(\"The language '\".concat(language, \"' (mapped to '\").concat(pistonLanguage, \"') is not supported by the Piston API.\\n\\n\") + \"Supported languages: \".concat(supportedLanguages.join(', ')));\n            setShowOutput(true);\n            return;\n        }\n        // Get the latest version of the runtime\n        const selectedRuntime = availableRuntimes[0];\n        try {\n            setIsExecuting(true);\n            setExecutionError(null);\n            setExecutionOutput(null);\n            setShowOutput(true);\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Determine file extension based on language\n            let fileExtension = '';\n            if (selectedRuntime.language === 'python3' || selectedRuntime.language === 'python') fileExtension = '.py';\n            else if (selectedRuntime.language === 'javascript') fileExtension = '.js';\n            else if (selectedRuntime.language === 'typescript') fileExtension = '.ts';\n            else if (selectedRuntime.language === 'java') fileExtension = '.java';\n            else if (selectedRuntime.language === 'csharp') fileExtension = '.cs';\n            else if (selectedRuntime.language === 'c') fileExtension = '.c';\n            else if (selectedRuntime.language === 'cpp') fileExtension = '.cpp';\n            else if (selectedRuntime.language === 'go') fileExtension = '.go';\n            else if (selectedRuntime.language === 'rust') fileExtension = '.rs';\n            else if (selectedRuntime.language === 'ruby') fileExtension = '.rb';\n            else if (selectedRuntime.language === 'php') fileExtension = '.php';\n            else fileExtension = \".\".concat(selectedRuntime.language);\n            console.log(\"Selected runtime: \".concat(selectedRuntime.language, \" \").concat(selectedRuntime.version));\n            // Prepare the payload according to Piston API documentation\n            const payload = {\n                language: selectedRuntime.language,\n                version: selectedRuntime.version,\n                files: [\n                    {\n                        name: \"main\".concat(fileExtension),\n                        content: code\n                    }\n                ],\n                stdin: '',\n                args: [],\n                compile_timeout: 10000,\n                run_timeout: 5000\n            };\n            // Log the payload for debugging\n            console.log(\"Executing \".concat(pistonLanguage, \" code with payload:\"), JSON.stringify(payload, null, 2));\n            // Make the API request\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', payload);\n            console.log('Execution response:', response.data);\n            const result = response.data;\n            if (result.run) {\n                // Format the output\n                let output = '';\n                let hasOutput = false;\n                // Add compile output if available (for compiled languages)\n                if (result.compile && result.compile.stderr) {\n                    output += \"Compilation output:\\n\".concat(result.compile.stderr, \"\\n\\n\");\n                    hasOutput = true;\n                }\n                // Add standard output\n                if (result.run.stdout) {\n                    output += result.run.stdout;\n                    hasOutput = true;\n                }\n                // Add error output\n                if (result.run.stderr) {\n                    if (hasOutput) output += '\\n';\n                    output += \"Error output:\\n\".concat(result.run.stderr);\n                    hasOutput = true;\n                }\n                // Add exit code if non-zero\n                if (result.run.code !== 0) {\n                    if (hasOutput) output += '\\n';\n                    output += \"\\nProcess exited with code \".concat(result.run.code);\n                    hasOutput = true;\n                }\n                if (!hasOutput) {\n                    output = 'Program executed successfully with no output.';\n                }\n                setExecutionOutput(output);\n            } else {\n                setExecutionError('Failed to execute code. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error executing code:', error);\n            // Handle Axios errors with more detailed information\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                const statusCode = error.response.status;\n                const responseData = error.response.data;\n                console.error('API Error Details:', {\n                    status: statusCode,\n                    data: responseData\n                });\n                // Format a more helpful error message\n                if (statusCode === 400) {\n                    setExecutionError(\"API Error (400 Bad Request): \".concat(JSON.stringify(responseData), \"\\n\\n\") + 'This usually means the API request format is incorrect. ' + 'Please check the console for more details.');\n                } else if (statusCode === 429) {\n                    setExecutionError('Rate limit exceeded. Please try again later.');\n                } else {\n                    setExecutionError(\"API Error (\".concat(statusCode, \"): \").concat(JSON.stringify(responseData), \"\\n\\n\") + 'Please check the console for more details.');\n                }\n            } else {\n                // Handle non-Axios errors\n                setExecutionError(error instanceof Error ? \"Error: \".concat(error.message) : 'An unknown error occurred while executing the code.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // This function will be called when we receive a code update from another user\n    const handleCodeUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleCodeUpdate]\": (incomingCode)=>{\n            console.log(\"Remote code update received, length:\", incomingCode.length);\n            // Make sure loading is off during code updates\n            setIsLoading(false);\n            // Only update if the code is different from our latest code\n            if (incomingCode !== latestCodeRef.current) {\n                try {\n                    // Set the flag to indicate this is a remote update\n                    isRemoteUpdate.current = true;\n                    // Update the editor if it's mounted\n                    if (editorRef.current) {\n                        console.log(\"Updating editor with remote code\");\n                        // Use the editor's model to update the value to preserve cursor position\n                        const model = editorRef.current.getModel();\n                        if (model) {\n                            // Store current cursor position/selection\n                            const currentPosition = editorRef.current.getPosition();\n                            const currentSelection = editorRef.current.getSelection();\n                            // Use executeEdits with better handling of cursor position\n                            editorRef.current.executeEdits('remote', [\n                                {\n                                    range: model.getFullModelRange(),\n                                    text: incomingCode,\n                                    forceMoveMarkers: true\n                                }\n                            ]);\n                            // Restore cursor position if possible\n                            if (currentPosition) {\n                                editorRef.current.setPosition(currentPosition);\n                            }\n                            if (currentSelection) {\n                                editorRef.current.setSelection(currentSelection);\n                            }\n                            // Also update our state and ref\n                            setCode(incomingCode);\n                            latestCodeRef.current = incomingCode;\n                        }\n                    } else {\n                        // If editor isn't mounted yet, just update the state and ref\n                        console.log(\"Editor not mounted, updating state only\");\n                        setCode(incomingCode);\n                        latestCodeRef.current = incomingCode;\n                        isRemoteUpdate.current = false;\n                    }\n                } catch (error) {\n                    console.error(\"Error updating editor with remote code:\", error);\n                    isRemoteUpdate.current = false;\n                }\n            } else {\n                console.log(\"Remote code matches current code, ignoring\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleCodeUpdate]\"], []);\n    // State to track cursor positions\n    const [cursorPositions, setCursorPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Emit cursor movement\n    const handleMouseMove = (e)=>{\n        const position = {\n            x: e.clientX,\n            y: e.clientY\n        };\n        _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().sendCursorMove(roomId, username, position);\n    };\n    // Listen for cursor movement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            const handleCursorMove = {\n                \"CodeEditor.useEffect.handleCursorMove\": (param)=>{\n                    let { userId, position } = param;\n                    setCursorPositions({\n                        \"CodeEditor.useEffect.handleCursorMove\": (prev)=>({\n                                ...prev,\n                                [userId]: position\n                            })\n                    }[\"CodeEditor.useEffect.handleCursorMove\"]);\n                }\n            }[\"CodeEditor.useEffect.handleCursorMove\"];\n            _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().on(\"cursor-move\", handleCursorMove);\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().off(\"cursor-move\", handleCursorMove);\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId\n    ]);\n    // Render cursors\n    const renderCursors = ()=>{\n        return Object.entries(cursorPositions).map((param)=>{\n            let [userId, position] = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    left: position.x,\n                    top: position.y,\n                    backgroundColor: \"red\",\n                    width: 10,\n                    height: 10,\n                    borderRadius: \"50%\",\n                    pointerEvents: \"none\"\n                }\n            }, userId, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 773,\n                columnNumber: 7\n            }, this);\n        });\n    };\n    // Fetch runtimes when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            fetchRuntimes();\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    // Handle request for initial code from new users\n    const handleGetInitialCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleGetInitialCode]\": (data)=>{\n            console.log(\"Received request for initial code from \".concat(data.requestingUsername, \" (\").concat(data.requestingUserId, \")\"));\n            // Only respond if we have code and we're connected\n            if (roomId && latestCodeRef.current && latestCodeRef.current.trim() !== \"// Start coding...\") {\n                const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                console.log(\"Sending initial code to \".concat(data.requestingUsername, \", length: \").concat(latestCodeRef.current.length));\n                socketServiceInstance.sendInitialCode(roomId, latestCodeRef.current, data.requestingUserId);\n            } else {\n                console.log(\"Not sending initial code - no meaningful code to share or not in room\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleGetInitialCode]\"], [\n        roomId\n    ]);\n    // Handle receiving initial code as a new user\n    const handleInitialCodeReceived = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleInitialCodeReceived]\": (data)=>{\n            console.log(\"Received initial code, length: \".concat(data.code.length));\n            // Only apply initial code if we don't have meaningful code yet\n            // This prevents overwriting code that the user might have already started typing\n            if (latestCodeRef.current === \"// Start coding...\" || latestCodeRef.current.trim() === \"\") {\n                console.log(\"Applying received initial code\");\n                // Set the flag to indicate this is a remote update\n                isRemoteUpdate.current = true;\n                // Update the code state and ref\n                setCode(data.code);\n                latestCodeRef.current = data.code;\n                // Update the editor if it's mounted\n                if (editorRef.current) {\n                    editorRef.current.setValue(data.code);\n                }\n            } else {\n                console.log(\"Not applying initial code - user already has meaningful code\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleInitialCodeReceived]\"], []);\n    // Handle teacher selection highlighting\n    const handleTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherSelection]\": (data)=>{\n            console.log(\"Received teacher selection from \".concat(data.teacherName, \":\"), data.selection);\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't show teacher highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model || !data.selection) {\n                    return;\n                }\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.selection.startLineNumber, data.selection.startColumn, data.selection.endLineNumber, data.selection.endColumn);\n                // Clear previous teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-selection-highlight',\n                            hoverMessage: {\n                                value: \"Teacher \".concat(data.teacherName, \" highlighted this text\")\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges\n                        }\n                    }\n                ]);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error applying teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle clearing teacher selection\n    const handleClearTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherSelection]\": (data)=>{\n            console.log(\"Teacher \".concat(data.teacherName, \" cleared selection\"));\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                // Clear all teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, []);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error clearing teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle teacher cursor position updates\n    const handleTeacherCursorPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherCursorPosition]\": (data)=>{\n            console.log(\"Teacher \".concat(data.teacherName, \" cursor at line \").concat(data.position.lineNumber, \", column \").concat(data.position.column));\n            if (userRole === 'teacher') {\n                return; // Don't show cursor to the teacher themselves\n            }\n            // Update teacher cursor position state\n            setTeacherCursorPosition({\n                lineNumber: data.position.lineNumber,\n                column: data.position.column,\n                teacherName: data.teacherName\n            });\n        }\n    }[\"CodeEditor.useCallback[handleTeacherCursorPosition]\"], [\n        userRole\n    ]);\n    // Handle teacher text highlight with enhanced error handling and multiple CSS class fallbacks\n    const handleTeacherTextHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherTextHighlight]\": (data)=>{\n            console.log(\"\\uD83C\\uDFA8 Teacher \".concat(data.teacherName, \" highlighted text:\"), data.selection);\n            console.log(\"\\uD83D\\uDD0D Debug info:\", {\n                editorAvailable: !!editorRef.current,\n                userRole: userRole,\n                roomId: roomId,\n                currentDecorations: teacherTextHighlightDecorations.length,\n                monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined'\n            });\n            if (!editorRef.current || userRole === 'teacher') {\n                console.log('⏭️ Skipping teacher text highlight: editor not available or user is teacher');\n                return; // Don't show highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('❌ Monaco Editor model not available');\n                    return;\n                }\n                if (!data.selection) {\n                    console.error('❌ Selection data is missing');\n                    return;\n                }\n                // Validate selection data with more robust checking\n                const { startLineNumber, startColumn, endLineNumber, endColumn } = data.selection;\n                if (typeof startLineNumber !== 'number' || startLineNumber <= 0 || typeof startColumn !== 'number' || startColumn <= 0 || typeof endLineNumber !== 'number' || endLineNumber <= 0 || typeof endColumn !== 'number' || endColumn <= 0) {\n                    console.error('❌ Invalid selection data:', data.selection);\n                    return;\n                }\n                // Ensure the selection is within the model bounds\n                const lineCount = model.getLineCount();\n                if (startLineNumber > lineCount || endLineNumber > lineCount) {\n                    console.warn('⚠️ Selection extends beyond model bounds, adjusting...');\n                    const adjustedEndLine = Math.min(endLineNumber, lineCount);\n                    const adjustedEndColumn = adjustedEndLine === lineCount ? model.getLineMaxColumn(adjustedEndLine) : endColumn;\n                    console.log(\"Adjusted selection: (\".concat(startLineNumber, \", \").concat(startColumn, \", \").concat(adjustedEndLine, \", \").concat(adjustedEndColumn, \")\"));\n                }\n                console.log(\"✅ Creating Monaco Range: (\".concat(startLineNumber, \", \").concat(startColumn, \", \").concat(endLineNumber, \", \").concat(endColumn, \")\"));\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(startLineNumber, startColumn, endLineNumber, endColumn);\n                console.log('✅ Monaco Range created successfully:', range);\n                // Apply decoration with multiple CSS class options for better compatibility\n                console.log('🎨 Applying teacher text highlight decoration...');\n                const decorationOptions = {\n                    range: range,\n                    options: {\n                        className: 'teacher-highlight teacher-text-highlight',\n                        hoverMessage: {\n                            value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" highlighted this text\\n\\nClick to focus on this selection\")\n                        },\n                        stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                        // Add inline styles as fallback\n                        inlineClassName: 'teacher-highlight-inline'\n                    }\n                };\n                // Clear previous decorations and apply new ones\n                const newDecorations = editor.deltaDecorations(teacherTextHighlightDecorations, [\n                    decorationOptions\n                ]);\n                console.log('✅ Teacher text highlight decorations applied:', newDecorations);\n                console.log('🔍 Decoration details:', decorationOptions);\n                setTeacherTextHighlightDecorations(newDecorations);\n                // Force a layout update to ensure the decoration is visible\n                setTimeout({\n                    \"CodeEditor.useCallback[handleTeacherTextHighlight]\": ()=>{\n                        editor.layout();\n                        console.log('🔄 Editor layout refreshed');\n                    }\n                }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"], 100);\n            } catch (error) {\n                var _editorRef_current;\n                console.error('❌ Error applying teacher text highlight:', error);\n                console.error('🔍 Error details:', {\n                    editorAvailable: !!editorRef.current,\n                    modelAvailable: !!((_editorRef_current = editorRef.current) === null || _editorRef_current === void 0 ? void 0 : _editorRef_current.getModel()),\n                    selectionData: data.selection,\n                    userRole: userRole,\n                    monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined',\n                    currentDecorations: teacherTextHighlightDecorations\n                });\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"], [\n        userRole,\n        teacherTextHighlightDecorations\n    ]);\n    // Handle clearing teacher text highlight with enhanced logging\n    const handleClearTeacherTextHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherTextHighlight]\": (data)=>{\n            console.log(\"\\uD83E\\uDDF9 Teacher \".concat(data.teacherName, \" cleared text highlight\"));\n            console.log(\"\\uD83D\\uDD0D Clear debug info:\", {\n                editorAvailable: !!editorRef.current,\n                userRole: userRole,\n                roomId: roomId,\n                currentDecorations: teacherTextHighlightDecorations.length,\n                monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined'\n            });\n            if (!editorRef.current || userRole === 'teacher') {\n                console.log('⏭️ Skipping clear teacher text highlight: editor not available or user is teacher');\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('❌ Monaco Editor model not available for clearing highlights');\n                    return;\n                }\n                console.log('🧹 Clearing teacher text highlight decorations...');\n                console.log(\"\\uD83D\\uDD0D Clearing \".concat(teacherTextHighlightDecorations.length, \" existing decorations\"));\n                // Clear all teacher text highlight decorations\n                const newDecorations = editor.deltaDecorations(teacherTextHighlightDecorations, []);\n                console.log('✅ Teacher text highlight decorations cleared:', newDecorations);\n                setTeacherTextHighlightDecorations(newDecorations);\n                // Force a layout update to ensure the decorations are removed\n                setTimeout({\n                    \"CodeEditor.useCallback[handleClearTeacherTextHighlight]\": ()=>{\n                        editor.layout();\n                        console.log('🔄 Editor layout refreshed after clearing highlights');\n                    }\n                }[\"CodeEditor.useCallback[handleClearTeacherTextHighlight]\"], 50);\n            } catch (error) {\n                var _editorRef_current;\n                console.error('❌ Error clearing teacher text highlight:', error);\n                console.error('🔍 Error details:', {\n                    editorAvailable: !!editorRef.current,\n                    modelAvailable: !!((_editorRef_current = editorRef.current) === null || _editorRef_current === void 0 ? void 0 : _editorRef_current.getModel()),\n                    userRole: userRole,\n                    currentDecorations: teacherTextHighlightDecorations,\n                    monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined'\n                });\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherTextHighlight]\"], [\n        userRole,\n        teacherTextHighlightDecorations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            // Handle user typing notifications\n            const handleUserTyping = {\n                \"CodeEditor.useEffect.handleUserTyping\": (param)=>{\n                    let { username, userId } = param;\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    // Don't show typing indicator for current user\n                    if (userId && userId === currentUserId) {\n                        return;\n                    }\n                    // Use the exact username without any modifications\n                    // This ensures we display exactly what the user entered on the dashboard\n                    setTypingUser(username);\n                    console.log(\"User typing: \".concat(username));\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserTyping\": ()=>setTypingUser(null)\n                    }[\"CodeEditor.useEffect.handleUserTyping\"], 2000);\n                }\n            }[\"CodeEditor.useEffect.handleUserTyping\"];\n            // Handle user list updates from both user-joined/user-left and room-users-updated events\n            const handleUserListUpdate = {\n                \"CodeEditor.useEffect.handleUserListUpdate\": (data)=>{\n                    console.log('User list update received:', data);\n                    let users = [];\n                    // Handle different event formats\n                    if (Array.isArray(data)) {\n                        // This is from user-joined or user-left events\n                        users = data;\n                    } else if (data && Array.isArray(data.users)) {\n                        // This is from room-users-updated event\n                        users = data.users.map({\n                            \"CodeEditor.useEffect.handleUserListUpdate\": (user)=>{\n                                if (typeof user === 'string') {\n                                    // If it's just a username string, convert to user object\n                                    return {\n                                        username: user\n                                    };\n                                }\n                                return user;\n                            }\n                        }[\"CodeEditor.useEffect.handleUserListUpdate\"]);\n                    }\n                    console.log('Processed users:', users);\n                    console.log('Current username state:', username);\n                    console.log('Stored username:', localStorage.getItem('username'));\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    console.log('Current userId from localStorage:', currentUserId);\n                    // Filter out any invalid users and map to display names\n                    const usernames = users.filter({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>user && user.username\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]).map({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>{\n                            const displayName = user.username;\n                            const isCurrentUser = user.userId === currentUserId;\n                            return isCurrentUser ? \"\".concat(displayName, \" (you)\") : displayName;\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]);\n                    console.log('Setting active users:', usernames);\n                    // Only update if we have users to display\n                    if (usernames.length > 0) {\n                        setActiveUsers(usernames);\n                    } else if (activeUsers.length === 0) {\n                        // If we don't have users from the server but we're connected, add ourselves\n                        const storedUsername = localStorage.getItem('username');\n                        if (storedUsername) {\n                            setActiveUsers([\n                                \"\".concat(storedUsername, \" (you)\")\n                            ]);\n                        }\n                    }\n                    // Log the current state of the active users list\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserListUpdate\": ()=>{\n                            console.log('Active users state after update:', activeUsers);\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate\"], 100);\n                }\n            }[\"CodeEditor.useEffect.handleUserListUpdate\"];\n            // Register event listeners\n            const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n            socketServiceInstance.on('code-update', handleCodeUpdate);\n            socketServiceInstance.on('user-typing', handleUserTyping);\n            socketServiceInstance.on('user-joined', handleUserListUpdate);\n            socketServiceInstance.on('user-left', handleUserListUpdate);\n            socketServiceInstance.on('room-users-updated', handleUserListUpdate);\n            socketServiceInstance.on('get-initial-code', handleGetInitialCode);\n            socketServiceInstance.on('initial-code-received', handleInitialCodeReceived);\n            socketServiceInstance.on('teacher-selection', handleTeacherSelection);\n            socketServiceInstance.on('clear-teacher-selection', handleClearTeacherSelection);\n            socketServiceInstance.on('teacher-cursor-position', handleTeacherCursorPosition);\n            socketServiceInstance.on('teacher-text-highlight', handleTeacherTextHighlight);\n            socketServiceInstance.on('clear-teacher-text-highlight', handleClearTeacherTextHighlight);\n            // Join the room when component mounts\n            if (roomId) {\n                console.log(\"Joining room:\", roomId);\n                // Only show loading on initial join, not for code updates\n                if (!editorRef.current) {\n                    setIsLoading(true);\n                }\n                const joinRoom = {\n                    \"CodeEditor.useEffect.joinRoom\": async ()=>{\n                        try {\n                            if (socketServiceInstance.isConnected()) {\n                                console.log(\"Socket connected, joining room\");\n                                const { username: validatedUsername, users, role } = await socketServiceInstance.joinRoom(roomId, username);\n                                console.log(\"Successfully joined room:\", roomId);\n                                console.log(\"Users in room:\", users);\n                                console.log(\"User role:\", role);\n                                if (validatedUsername !== username) {\n                                    console.log(\"Server validated username from \".concat(username, \" to \").concat(validatedUsername));\n                                    setUsername(validatedUsername);\n                                    localStorage.setItem('username', validatedUsername);\n                                }\n                                // Set user role\n                                if (role) {\n                                    setUserRole(role);\n                                    console.log(\"User role set to: \".concat(role));\n                                }\n                                if (users && Array.isArray(users)) {\n                                    console.log('Setting active users from join response');\n                                    handleUserListUpdate(users);\n                                }\n                                setIsLoading(false);\n                            } else {\n                                console.log(\"Socket not connected, trying to connect...\");\n                                await new Promise({\n                                    \"CodeEditor.useEffect.joinRoom\": (resolve)=>socketServiceInstance.onConnect({\n                                            \"CodeEditor.useEffect.joinRoom\": ()=>resolve()\n                                        }[\"CodeEditor.useEffect.joinRoom\"])\n                                }[\"CodeEditor.useEffect.joinRoom\"]);\n                                // After connect, try join again\n                                await joinRoom();\n                                return;\n                            }\n                        } catch (error) {\n                            console.error(\"Error joining room:\", error);\n                            setIsLoading(false);\n                        }\n                    }\n                }[\"CodeEditor.useEffect.joinRoom\"];\n                // Start the join process\n                joinRoom();\n                // Set a timeout to hide the loading screen even if the join fails\n                setTimeout({\n                    \"CodeEditor.useEffect\": ()=>{\n                        setIsLoading(false);\n                    }\n                }[\"CodeEditor.useEffect\"], 3000);\n            }\n            // Clean up event listeners when component unmounts\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    // Use an instance of SocketService\n                    const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                    socketServiceInstance.off('code-update', handleCodeUpdate);\n                    socketServiceInstance.off('user-typing', handleUserTyping);\n                    socketServiceInstance.off('user-joined', handleUserListUpdate);\n                    socketServiceInstance.off('user-left', handleUserListUpdate);\n                    socketServiceInstance.off('room-users-updated', handleUserListUpdate);\n                    socketServiceInstance.off('get-initial-code', handleGetInitialCode);\n                    socketServiceInstance.off('initial-code-received', handleInitialCodeReceived);\n                    socketServiceInstance.off('teacher-selection', handleTeacherSelection);\n                    socketServiceInstance.off('clear-teacher-selection', handleClearTeacherSelection);\n                    socketServiceInstance.off('teacher-cursor-position', handleTeacherCursorPosition);\n                    socketServiceInstance.off('teacher-text-highlight', handleTeacherTextHighlight);\n                    socketServiceInstance.off('clear-teacher-text-highlight', handleClearTeacherTextHighlight);\n                    // Leave the room when component unmounts\n                    if (roomId) {\n                        socketServiceInstance.leaveRoom(roomId);\n                    }\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId,\n        initialUsername,\n        username,\n        handleCodeUpdate,\n        handleGetInitialCode,\n        handleInitialCodeReceived,\n        handleTeacherSelection,\n        handleClearTeacherSelection,\n        handleTeacherCursorPosition,\n        handleTeacherTextHighlight,\n        handleClearTeacherTextHighlight\n    ]);\n    // Download code as file\n    const handleDownload = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"code-\".concat(roomId || \"session\", \".txt\");\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    // Upload code from file\n    const handleUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            var _event_target;\n            if (typeof ((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result) === \"string\") {\n                setCode(event.target.result);\n                latestCodeRef.current = event.target.result;\n            }\n        };\n        reader.readAsText(file);\n    };\n    // Show Monaco's find/replace dialog\n    const handleFindReplace = ()=>{\n        if (editorRef.current) {\n            const action = editorRef.current.getAction(\"actions.find\");\n            if (action) action.run();\n        }\n    };\n    // Insert code snippet\n    const insertSnippet = (snippet)=>{\n        if (editorRef.current) {\n            const model = editorRef.current.getModel();\n            const position = editorRef.current.getPosition();\n            if (model && position) {\n                editorRef.current.executeEdits(\"snippet\", [\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(position.lineNumber, position.column, position.lineNumber, position.column),\n                        text: snippet,\n                        forceMoveMarkers: true\n                    }\n                ]);\n                editorRef.current.focus();\n            }\n        }\n    };\n    // Use the instance of SocketService for the connect method\n    const handleReconnect = ()=>{\n        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n        socketServiceInstance.connect();\n    };\n    // Manual test function for debugging teacher text highlighting\n    const testManualHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[testManualHighlight]\": ()=>{\n            console.log('🧪 Manual test highlight triggered');\n            if (!editorRef.current) {\n                console.error('❌ Editor not available for manual test');\n                return;\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('❌ Monaco Editor model not available for manual test');\n                    return;\n                }\n                console.log('✅ Manual test: Editor and model available');\n                // Create a test range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(1, 1, 1, 20);\n                console.log('✅ Manual test: Range created:', range);\n                // Apply decoration\n                const newDecorations = editor.deltaDecorations(teacherTextHighlightDecorations, [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-text-highlight',\n                            hoverMessage: {\n                                value: 'Manual test highlight - this should be blue!'\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges\n                        }\n                    }\n                ]);\n                console.log('✅ Manual test: Decorations applied:', newDecorations);\n                setTeacherTextHighlightDecorations(newDecorations);\n            } catch (error) {\n                console.error('❌ Manual test error:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[testManualHighlight]\"], [\n        teacherTextHighlightDecorations\n    ]);\n    // Add manual test to window for debugging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            if (true) {\n                window.testManualHighlight = testManualHighlight;\n                console.log('🔧 Manual test function added to window.testManualHighlight()');\n            }\n        }\n    }[\"CodeEditor.useEffect\"], [\n        testManualHighlight\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-[80vh] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-800 shadow-xl flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"bg-gray-100 dark:bg-zinc-800 px-4 py-2 flex flex-wrap items-center justify-between border-b border-gray-200 dark:border-gray-700 gap-2 min-h-[48px]\",\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    style: {\n                        zIndex: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-3 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 min-w-[90px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1364,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-400 truncate\",\n                                            children: isConnected ? 'Connected' : 'Disconnected'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1365,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded ml-1 transition-colors\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1369,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1363,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: language,\n                                    onChange: (e)=>changeLanguage(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"javascript\",\n                                            children: \"JavaScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1384,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"typescript\",\n                                            children: \"TypeScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1385,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"python3\",\n                                            children: \"Python\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1386,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"java\",\n                                            children: \"Java\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1387,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"csharp\",\n                                            children: \"C#\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"c\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1389,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"cpp\",\n                                            children: \"C++\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1390,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"go\",\n                                            children: \"Go\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1391,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ruby\",\n                                            children: \"Ruby\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1392,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rust\",\n                                            children: \"Rust\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1393,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"php\",\n                                            children: \"PHP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1394,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1379,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: formatCurrentCode,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm bg-blue-600 hover:bg-blue-700 text-white transition-colors whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiAlignLeft, {\n                                            size: 14\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1404,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Format\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1405,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1398,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: executeCode,\n                                    disabled: isExecuting,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm \".concat(isExecuting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700', \" text-white transition-colors whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: isExecuting ? 1 : 1.05\n                                    },\n                                    whileTap: {\n                                        scale: isExecuting ? 1 : 0.95\n                                    },\n                                    children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                                className: \"animate-spin\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1418,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Running...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1419,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiPlay, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1423,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Run Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1424,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1409,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Room:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1431,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-white dark:bg-zinc-700 px-2 py-1 rounded font-mono text-xs truncate max-w-[120px]\",\n                                            children: roomId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1432,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                            onClick: copyCodeToClipboard,\n                                            className: \"text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            title: \"Copy code to clipboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCopy, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1440,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1433,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1430,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1361,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-2 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowOutput((prev)=>!prev),\n                                    className: \"flex items-center space-x-1 text-sm \".concat(showOutput ? 'text-blue-500 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300', \" hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCode, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1453,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Output\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1454,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: toggleUserList,\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUsers, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1464,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                activeUsers.length,\n                                                \" online\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1465,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1458,\n                                    columnNumber: 13\n                                }, this),\n                                userRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    className: \"flex items-center space-x-1 text-xs font-medium px-2 py-1 rounded-full whitespace-nowrap \".concat(userRole === 'teacher' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'),\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: userRole === 'teacher' ? '👨‍🏫' : '👨‍🎓'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1480,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: userRole === 'teacher' ? 'Teacher' : 'Student'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1481,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1470,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"flex items-center space-x-1 text-sm text-yellow-600 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSun, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1493,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiMoon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1493,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1486,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setMinimapEnabled((v)=>!v),\n                                    className: \"flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Minimap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Minimap\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1504,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1497,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleFindReplace,\n                                    className: \"flex items-center space-x-1 text-sm text-green-600 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Find/Replace\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSearch, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1515,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleDownload,\n                                    className: \"flex items-center space-x-1 text-sm text-indigo-600 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Download Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiDownload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1526,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1519,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"flex items-center space-x-1 text-sm text-pink-600 dark:text-pink-300 bg-pink-100 dark:bg-pink-900/30 hover:bg-pink-200 dark:hover:bg-pink-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Upload Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUpload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1537,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1530,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \".js,.ts,.py,.java,.c,.cpp,.go,.rb,.rs,.php,.txt\",\n                                    style: {\n                                        display: \"none\"\n                                    },\n                                    onChange: handleUpload\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1539,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    onChange: (e)=>e.target.value && insertSnippet(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[100px]\",\n                                    defaultValue: \"\",\n                                    title: \"Insert Snippet\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Snippets\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1554,\n                                            columnNumber: 15\n                                        }, this),\n                                        (SNIPPETS[language] || SNIPPETS['javascript']).map((snippet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: snippet.value,\n                                                children: snippet.label\n                                            }, snippet.label, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1556,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1548,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowShortcuts(true),\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Keyboard Shortcuts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiHelpCircle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1568,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1561,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleLeaveRoom,\n                                    className: \"flex items-center space-x-1 text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors px-2 py-1 rounded-md ml-2 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Leave Room\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1578,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1572,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1445,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1354,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showUserList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute right-0 top-12 bg-white dark:bg-zinc-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 w-48 overflow-hidden\",\n                        initial: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            height: 'auto'\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 border-b border-gray-200 dark:border-gray-700 font-medium text-sm text-gray-600 dark:text-gray-300\",\n                                children: \"Active Users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1593,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"max-h-48 overflow-y-auto\",\n                                children: activeUsers.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.li, {\n                                        className: \"px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-zinc-700 flex items-center space-x-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full bg-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1605,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: user\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1606,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1598,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1596,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1586,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1584,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"h-[calc(100%-40px)] \".concat(showOutput ? 'pb-[33%]' : ''),\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.4,\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    height: \"100%\",\n                                    defaultLanguage: language,\n                                    defaultValue: code,\n                                    onChange: handleEditorChange,\n                                    onMount: handleEditorDidMount,\n                                    theme: theme === \"dark\" ? \"vs-dark\" : \"light\",\n                                    options: {\n                                        minimap: {\n                                            enabled: minimapEnabled\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1624,\n                                    columnNumber: 15\n                                }, this),\n                                teacherCursorPosition && editorRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TeacherCursor, {\n                                    editor: editorRef.current,\n                                    position: teacherCursorPosition\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1636,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1623,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1621,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1615,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: typingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 left-4 text-sm text-gray-400 bg-gray-800/80 px-3 py-1 rounded-full backdrop-blur-sm\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            typingUser,\n                            \" is typing...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1648,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1646,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gray-900 text-white border-t border-gray-700 h-1/3 overflow-hidden flex flex-col\",\n                        initial: {\n                            height: 0\n                        },\n                        animate: {\n                            height: '33%'\n                        },\n                        exit: {\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 bg-gray-800 border-b border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1671,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearOutput,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1673,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: testPistonAPI,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-blue-400 hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Test API\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1680,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowOutput(false),\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1687,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1672,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1670,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-auto font-mono text-sm whitespace-pre-wrap\",\n                                children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1698,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Executing code...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1699,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1697,\n                                    columnNumber: 19\n                                }, this) : executionError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400\",\n                                    children: executionError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1702,\n                                    columnNumber: 19\n                                }, this) : executionOutput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: executionOutput\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1704,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: 'Click \"Run Code\" to execute your code.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1706,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1695,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1663,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1661,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: copySuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 right-4 text-sm text-white bg-green-500 px-3 py-1 rounded-full shadow-lg\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: \"Copied to clipboard!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1716,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1714,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showShortcuts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 max-w-md w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-700 dark:hover:text-white\",\n                                    onClick: ()=>setShowShortcuts(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1738,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    children: \"Keyboard Shortcuts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1744,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Run Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1746,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+Enter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1746,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Format Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1747,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Shift+Alt+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1747,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Find/Replace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1748,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1748,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Download Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1749,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+D\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1749,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Upload Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1750,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+U\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1750,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Minimap:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1751,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+M\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1751,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Theme:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1752,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+T\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1752,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Show Shortcuts:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1753,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+H\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1753,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1745,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1737,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1731,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1729,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 1352,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 1350,\n        columnNumber: 5\n    }, this);\n}\n_s(CodeEditor, \"mDpLeWTc7bpvH9zKdUtrWsk6ecA=\", false, function() {\n    return [\n        _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme\n    ];\n});\n_c = CodeEditor;\nconst TeacherCursor = (param)=>{\n    let { editor, position } = param;\n    _s1();\n    const [cursorStyle, setCursorStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherCursor.useEffect\": ()=>{\n            const updateCursorPosition = {\n                \"TeacherCursor.useEffect.updateCursorPosition\": ()=>{\n                    try {\n                        // Get the pixel position of the cursor\n                        const pixelPosition = editor.getScrolledVisiblePosition({\n                            lineNumber: position.lineNumber,\n                            column: position.column\n                        });\n                        if (pixelPosition) {\n                            // Get the editor container position\n                            const editorContainer = editor.getDomNode();\n                            if (editorContainer) {\n                                const containerRect = editorContainer.getBoundingClientRect();\n                                setCursorStyle({\n                                    position: 'absolute',\n                                    left: \"\".concat(pixelPosition.left, \"px\"),\n                                    top: \"\".concat(pixelPosition.top, \"px\"),\n                                    zIndex: 1000,\n                                    pointerEvents: 'none'\n                                });\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error updating teacher cursor position:', error);\n                    }\n                }\n            }[\"TeacherCursor.useEffect.updateCursorPosition\"];\n            // Update position immediately\n            updateCursorPosition();\n            // Update position when editor scrolls or layout changes\n            const scrollDisposable = editor.onDidScrollChange(updateCursorPosition);\n            const layoutDisposable = editor.onDidLayoutChange(updateCursorPosition);\n            return ({\n                \"TeacherCursor.useEffect\": ()=>{\n                    scrollDisposable.dispose();\n                    layoutDisposable.dispose();\n                }\n            })[\"TeacherCursor.useEffect\"];\n        }\n    }[\"TeacherCursor.useEffect\"], [\n        editor,\n        position\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: cursorStyle,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"teacher-cursor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"teacher-cursor-label\",\n                children: position.teacherName\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 1818,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 1817,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 1816,\n        columnNumber: 5\n    }, undefined);\n}; // Keyboard shortcuts global handler\n // useEffect(() => {\n //   const handler = (e: KeyboardEvent) => {\n //     if (e.altKey && e.key.toLowerCase() === \"d\") {\n //       e.preventDefault();\n //       handleDownload();\n //     } else if (e.altKey && e.key.toLowerCase() === \"u\") {\n //       e.preventDefault();\n //       fileInputRef.current?.click();\n //     } else if (e.altKey && e.key.toLowerCase() === \"m\") {\n //       e.preventDefault();\n //       setMinimapEnabled((v: boolean) => !v);\n //     } else if (e.altKey && e.key.toLowerCase() === \"t\") {\n //       e.preventDefault();\n //       setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n //     } else if (e.altKey && e.key.toLowerCase() === \"h\") {\n //       e.preventDefault();\n //       setShowShortcuts(true);\n //     }\n //   };\n //   window.addEventListener(\"keydown\", handler);\n //   return () => window.removeEventListener(\"keydown\", handler);\n // }, [theme, setTheme]);\n_s1(TeacherCursor, \"NrZZSKlPdtAtpq9NKiew7IOqw6k=\");\n_c1 = TeacherCursor;\nvar _c, _c1;\n$RefreshReg$(_c, \"CodeEditor\");\n$RefreshReg$(_c1, \"TeacherCursor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CodeEditor.tsx\n"));

/***/ })

});