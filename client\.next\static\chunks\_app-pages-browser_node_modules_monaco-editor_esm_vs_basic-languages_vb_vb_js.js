"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_vb_vb_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/vb/vb.js":
/*!*********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/vb/vb.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/vb/vb.ts\nvar conf = {\n  comments: {\n    lineComment: \"'\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"<\", \">\"],\n    [\"addhandler\", \"end addhandler\"],\n    [\"class\", \"end class\"],\n    [\"enum\", \"end enum\"],\n    [\"event\", \"end event\"],\n    [\"function\", \"end function\"],\n    [\"get\", \"end get\"],\n    [\"if\", \"end if\"],\n    [\"interface\", \"end interface\"],\n    [\"module\", \"end module\"],\n    [\"namespace\", \"end namespace\"],\n    [\"operator\", \"end operator\"],\n    [\"property\", \"end property\"],\n    [\"raiseevent\", \"end raiseevent\"],\n    [\"removehandler\", \"end removehandler\"],\n    [\"select\", \"end select\"],\n    [\"set\", \"end set\"],\n    [\"structure\", \"end structure\"],\n    [\"sub\", \"end sub\"],\n    [\"synclock\", \"end synclock\"],\n    [\"try\", \"end try\"],\n    [\"while\", \"end while\"],\n    [\"with\", \"end with\"],\n    [\"using\", \"end using\"],\n    [\"do\", \"loop\"],\n    [\"for\", \"next\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"<\", close: \">\", notIn: [\"string\", \"comment\"] }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#Region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#End Region\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".vb\",\n  ignoreCase: true,\n  brackets: [\n    { token: \"delimiter.bracket\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.array\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.angle\", open: \"<\", close: \">\" },\n    // Special bracket statement pairs\n    // according to https://msdn.microsoft.com/en-us/library/tsw2a11z.aspx\n    {\n      token: \"keyword.tag-addhandler\",\n      open: \"addhandler\",\n      close: \"end addhandler\"\n    },\n    { token: \"keyword.tag-class\", open: \"class\", close: \"end class\" },\n    { token: \"keyword.tag-enum\", open: \"enum\", close: \"end enum\" },\n    { token: \"keyword.tag-event\", open: \"event\", close: \"end event\" },\n    {\n      token: \"keyword.tag-function\",\n      open: \"function\",\n      close: \"end function\"\n    },\n    { token: \"keyword.tag-get\", open: \"get\", close: \"end get\" },\n    { token: \"keyword.tag-if\", open: \"if\", close: \"end if\" },\n    {\n      token: \"keyword.tag-interface\",\n      open: \"interface\",\n      close: \"end interface\"\n    },\n    { token: \"keyword.tag-module\", open: \"module\", close: \"end module\" },\n    {\n      token: \"keyword.tag-namespace\",\n      open: \"namespace\",\n      close: \"end namespace\"\n    },\n    {\n      token: \"keyword.tag-operator\",\n      open: \"operator\",\n      close: \"end operator\"\n    },\n    {\n      token: \"keyword.tag-property\",\n      open: \"property\",\n      close: \"end property\"\n    },\n    {\n      token: \"keyword.tag-raiseevent\",\n      open: \"raiseevent\",\n      close: \"end raiseevent\"\n    },\n    {\n      token: \"keyword.tag-removehandler\",\n      open: \"removehandler\",\n      close: \"end removehandler\"\n    },\n    { token: \"keyword.tag-select\", open: \"select\", close: \"end select\" },\n    { token: \"keyword.tag-set\", open: \"set\", close: \"end set\" },\n    {\n      token: \"keyword.tag-structure\",\n      open: \"structure\",\n      close: \"end structure\"\n    },\n    { token: \"keyword.tag-sub\", open: \"sub\", close: \"end sub\" },\n    {\n      token: \"keyword.tag-synclock\",\n      open: \"synclock\",\n      close: \"end synclock\"\n    },\n    { token: \"keyword.tag-try\", open: \"try\", close: \"end try\" },\n    { token: \"keyword.tag-while\", open: \"while\", close: \"end while\" },\n    { token: \"keyword.tag-with\", open: \"with\", close: \"end with\" },\n    // Other pairs\n    { token: \"keyword.tag-using\", open: \"using\", close: \"end using\" },\n    { token: \"keyword.tag-do\", open: \"do\", close: \"loop\" },\n    { token: \"keyword.tag-for\", open: \"for\", close: \"next\" }\n  ],\n  keywords: [\n    \"AddHandler\",\n    \"AddressOf\",\n    \"Alias\",\n    \"And\",\n    \"AndAlso\",\n    \"As\",\n    \"Async\",\n    \"Boolean\",\n    \"ByRef\",\n    \"Byte\",\n    \"ByVal\",\n    \"Call\",\n    \"Case\",\n    \"Catch\",\n    \"CBool\",\n    \"CByte\",\n    \"CChar\",\n    \"CDate\",\n    \"CDbl\",\n    \"CDec\",\n    \"Char\",\n    \"CInt\",\n    \"Class\",\n    \"CLng\",\n    \"CObj\",\n    \"Const\",\n    \"Continue\",\n    \"CSByte\",\n    \"CShort\",\n    \"CSng\",\n    \"CStr\",\n    \"CType\",\n    \"CUInt\",\n    \"CULng\",\n    \"CUShort\",\n    \"Date\",\n    \"Decimal\",\n    \"Declare\",\n    \"Default\",\n    \"Delegate\",\n    \"Dim\",\n    \"DirectCast\",\n    \"Do\",\n    \"Double\",\n    \"Each\",\n    \"Else\",\n    \"ElseIf\",\n    \"End\",\n    \"EndIf\",\n    \"Enum\",\n    \"Erase\",\n    \"Error\",\n    \"Event\",\n    \"Exit\",\n    \"False\",\n    \"Finally\",\n    \"For\",\n    \"Friend\",\n    \"Function\",\n    \"Get\",\n    \"GetType\",\n    \"GetXMLNamespace\",\n    \"Global\",\n    \"GoSub\",\n    \"GoTo\",\n    \"Handles\",\n    \"If\",\n    \"Implements\",\n    \"Imports\",\n    \"In\",\n    \"Inherits\",\n    \"Integer\",\n    \"Interface\",\n    \"Is\",\n    \"IsNot\",\n    \"Let\",\n    \"Lib\",\n    \"Like\",\n    \"Long\",\n    \"Loop\",\n    \"Me\",\n    \"Mod\",\n    \"Module\",\n    \"MustInherit\",\n    \"MustOverride\",\n    \"MyBase\",\n    \"MyClass\",\n    \"NameOf\",\n    \"Namespace\",\n    \"Narrowing\",\n    \"New\",\n    \"Next\",\n    \"Not\",\n    \"Nothing\",\n    \"NotInheritable\",\n    \"NotOverridable\",\n    \"Object\",\n    \"Of\",\n    \"On\",\n    \"Operator\",\n    \"Option\",\n    \"Optional\",\n    \"Or\",\n    \"OrElse\",\n    \"Out\",\n    \"Overloads\",\n    \"Overridable\",\n    \"Overrides\",\n    \"ParamArray\",\n    \"Partial\",\n    \"Private\",\n    \"Property\",\n    \"Protected\",\n    \"Public\",\n    \"RaiseEvent\",\n    \"ReadOnly\",\n    \"ReDim\",\n    \"RemoveHandler\",\n    \"Resume\",\n    \"Return\",\n    \"SByte\",\n    \"Select\",\n    \"Set\",\n    \"Shadows\",\n    \"Shared\",\n    \"Short\",\n    \"Single\",\n    \"Static\",\n    \"Step\",\n    \"Stop\",\n    \"String\",\n    \"Structure\",\n    \"Sub\",\n    \"SyncLock\",\n    \"Then\",\n    \"Throw\",\n    \"To\",\n    \"True\",\n    \"Try\",\n    \"TryCast\",\n    \"TypeOf\",\n    \"UInteger\",\n    \"ULong\",\n    \"UShort\",\n    \"Using\",\n    \"Variant\",\n    \"Wend\",\n    \"When\",\n    \"While\",\n    \"Widening\",\n    \"With\",\n    \"WithEvents\",\n    \"WriteOnly\",\n    \"Xor\"\n  ],\n  tagwords: [\n    \"If\",\n    \"Sub\",\n    \"Select\",\n    \"Try\",\n    \"Class\",\n    \"Enum\",\n    \"Function\",\n    \"Get\",\n    \"Interface\",\n    \"Module\",\n    \"Namespace\",\n    \"Operator\",\n    \"Set\",\n    \"Structure\",\n    \"Using\",\n    \"While\",\n    \"With\",\n    \"Do\",\n    \"Loop\",\n    \"For\",\n    \"Next\",\n    \"Property\",\n    \"Continue\",\n    \"AddHandler\",\n    \"RemoveHandler\",\n    \"Event\",\n    \"RaiseEvent\",\n    \"SyncLock\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?;\\.,:&|+\\-*\\/\\^%]+/,\n  integersuffix: /U?[DI%L&S@]?/,\n  floatsuffix: /[R#F!]?/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // whitespace\n      { include: \"@whitespace\" },\n      // special ending tag-words\n      [/next(?!\\w)/, { token: \"keyword.tag-for\" }],\n      [/loop(?!\\w)/, { token: \"keyword.tag-do\" }],\n      // usual ending tags\n      [\n        /end\\s+(?!for|do)(addhandler|class|enum|event|function|get|if|interface|module|namespace|operator|property|raiseevent|removehandler|select|set|structure|sub|synclock|try|while|with|using)/,\n        { token: \"keyword.tag-$1\" }\n      ],\n      // identifiers, tagwords, and keywords\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@tagwords\": { token: \"keyword.tag-$0\" },\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // Preprocessor directive\n      [/^\\s*#\\w+/, \"keyword\"],\n      // numbers\n      [/\\d*\\d+e([\\-+]?\\d+)?(@floatsuffix)/, \"number.float\"],\n      [/\\d*\\.\\d+(e[\\-+]?\\d+)?(@floatsuffix)/, \"number.float\"],\n      [/&H[0-9a-f]+(@integersuffix)/, \"number.hex\"],\n      [/&0[0-7]+(@integersuffix)/, \"number.octal\"],\n      [/\\d+(@integersuffix)/, \"number\"],\n      // date literal\n      [/#.*#/, \"number\"],\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      // strings\n      [/[\"\\u201c\\u201d]/, { token: \"string.quote\", next: \"@string\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/(\\'|REM(?!\\w)).*$/, \"comment\"]\n    ],\n    string: [\n      [/[^\"\\u201c\\u201d]+/, \"string\"],\n      [/[\"\\u201c\\u201d]{2}/, \"string.escape\"],\n      [/[\"\\u201c\\u201d]C?/, { token: \"string.quote\", next: \"@pop\" }]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/vb/vb.js\n"));

/***/ })

}]);