"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var monaco_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! monaco-editor */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.main.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlignLeft,FiCode,FiCopy,FiDownload,FiHelpCircle,FiLoader,FiMoon,FiPlay,FiSearch,FiSun,FiUpload,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_socketService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/socketService */ \"(app-pages-browser)/./src/services/socketService.ts\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/throttle */ \"(app-pages-browser)/./node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/SocketContext */ \"(app-pages-browser)/./src/context/SocketContext.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_formatCode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/formatCode */ \"(app-pages-browser)/./src/utils/formatCode.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/ThemeContext */ \"(app-pages-browser)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CodeEditor(param) {\n    let { roomId, username: initialUsername } = param;\n    _s();\n    const { isConnected } = (0,_context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket)();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"// Start coding...\");\n    // Use a ref to always track the latest code value\n    const latestCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"// Start coding...\");\n    // Get username from localStorage if available, otherwise use initialUsername\n    const storedUsername =  true ? localStorage.getItem(\"username\") : 0;\n    console.log(\"Initial username: \".concat(initialUsername, \", Stored username: \").concat(storedUsername));\n    // Track the username internally to handle server validation\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUsername || initialUsername);\n    const [typingUser, setTypingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeUsers, setActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUserList, setShowUserList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"javascript\");\n    const [copySuccess, setCopySuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get user role from localStorage if available, otherwise null\n    const storedUserRole =  true ? localStorage.getItem(\"userRole\") : 0;\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUserRole);\n    // Code execution states\n    const [isExecuting, setIsExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionOutput, setExecutionOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [executionError, setExecutionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOutput, setShowOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Piston API runtimes\n    const [runtimes, setRuntimes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [runtimesLoaded, setRuntimesLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    // Theme and minimap state (ensure these are always defined at the top)\n    const { theme, setTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme)();\n    const [showShortcuts, setShowShortcuts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [minimapEnabled, setMinimapEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Language-specific code snippets\n    const SNIPPETS = {\n        javascript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            }\n        ],\n        typescript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction(): void {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            },\n            {\n                label: \"Type Declaration\",\n                value: \"type MyType = {\\n  key: string;\\n};\"\n            }\n        ],\n        python3: [\n            {\n                label: \"Function\",\n                value: \"def my_function():\\n    # code\\n    pass\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in range(10):\\n    # code\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition:\\n    # code\"\n            },\n            {\n                label: \"Print\",\n                value: \"print('Hello, World!')\"\n            }\n        ],\n        java: [\n            {\n                label: \"Main Method\",\n                value: \"public static void main(String[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"System.out.println(\\\"Hello, World!\\\");\"\n            }\n        ],\n        csharp: [\n            {\n                label: \"Main Method\",\n                value: \"static void Main(string[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"Console.WriteLine(\\\"Hello, World!\\\");\"\n            }\n        ],\n        c: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"printf(\\\"Hello, World!\\\\n\\\");\"\n            }\n        ],\n        cpp: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"std::cout << \\\"Hello, World!\\\" << std::endl;\"\n            }\n        ],\n        go: [\n            {\n                label: \"Main Function\",\n                value: \"func main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i := 0; i < 10; i++ {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"fmt.Println(\\\"Hello, World!\\\")\"\n            }\n        ],\n        ruby: [\n            {\n                label: \"Method\",\n                value: \"def my_method\\n  # code\\nend\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..9\\n  # code\\nend\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition\\n  # code\\nend\"\n            },\n            {\n                label: \"Print\",\n                value: \"puts 'Hello, World!'\"\n            }\n        ],\n        rust: [\n            {\n                label: \"Main Function\",\n                value: \"fn main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..10 {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"println!(\\\"Hello, World!\\\");\"\n            }\n        ],\n        php: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for ($i = 0; $i < 10; $i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if ($condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"echo 'Hello, World!';\"\n            }\n        ]\n    };\n    // Declare the `socketService` instance at the top of the file\n    const socketService = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n    // Leave room handler\n    const handleLeaveRoom = ()=>{\n        socketService.leaveRoom(roomId);\n        router.push(\"/dashboard\");\n    };\n    // --- Teacher Selection Highlighting and Cursor Sharing ---\n    // Add custom CSS for teacher selection and cursor if not already present\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            if ( true && !document.getElementById('teacher-highlighting-css')) {\n                const style = document.createElement('style');\n                style.id = 'teacher-highlighting-css';\n                style.innerHTML = \"\\n        .teacher-selection-highlight {\\n          background: rgba(255, 230, 80, 0.5) !important;\\n          border-bottom: 2px solid orange !important;\\n          border-radius: 2px;\\n        }\\n        .teacher-cursor-highlight {\\n          border-left: 2px solid orange !important;\\n          margin-left: -1px;\\n          animation: teacher-cursor-blink 1s steps(2, start) infinite;\\n        }\\n        @keyframes teacher-cursor-blink {\\n          0%, 100% { opacity: 1; }\\n          50% { opacity: 0; }\\n        }\\n      \";\n                document.head.appendChild(style);\n            }\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    // Store decoration ids for cleanup\n    const [teacherSelectionDecorations, setTeacherSelectionDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [teacherCursorDecorations, setTeacherCursorDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Add state for teacher cursor position (for custom blinking cursor overlay)\n    const [teacherCursorPosition, setTeacherCursorPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State to track current teacher selection and cursor for syncing to new users\n    const [currentTeacherSelection, setCurrentTeacherSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentTeacherCursor, setCurrentTeacherCursor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Handle editor mounting\n    const handleEditorDidMount = (editor)=>{\n        editorRef.current = editor;\n        setIsLoading(false);\n        // Auto-focus the editor after mounting\n        setTimeout(()=>{\n            if (editor) {\n                editor.focus();\n                // Add keyboard shortcut (Ctrl+Enter) to run code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.CtrlCmd | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.Enter, ()=>{\n                    executeCode();\n                });\n                // Add keyboard shortcut (Shift+Alt+F) to format code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Shift | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Alt | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.KeyF, ()=>{\n                    formatCurrentCode();\n                });\n                // Add teacher cursor and selection listeners\n                if (userRole === 'teacher') {\n                    // Track cursor position changes - emit 'teacher-cursor' event\n                    editor.onDidChangeCursorPosition((e)=>{\n                        const position = e.position;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        if (roomId) {\n                            console.log('🎯 Teacher cursor position changed:', {\n                                lineNumber: position.lineNumber,\n                                column: position.column\n                            });\n                            // Update current teacher cursor state for syncing to new users\n                            setCurrentTeacherCursor({\n                                lineNumber: position.lineNumber,\n                                column: position.column\n                            });\n                            // Send cursor position to students using teacher-cursor-position event\n                            socketServiceInstance.sendTeacherCursorPosition(roomId, {\n                                lineNumber: position.lineNumber,\n                                column: position.column\n                            });\n                        }\n                    });\n                    // Track selection changes - emit 'teacher-selection' event\n                    editor.onDidChangeCursorSelection((e)=>{\n                        console.log('🎯 Teacher cursor selection changed:', {\n                            selection: e.selection,\n                            isEmpty: e.selection.isEmpty(),\n                            userRole: userRole,\n                            roomId: roomId\n                        });\n                        const selection = e.selection;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        if (!selection.isEmpty() && roomId) {\n                            // Send selection range to students using teacher-selection event\n                            console.log('📤 Teacher sending selection range to students:', {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            });\n                            const selectionData = {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            };\n                            // Update current teacher selection state for syncing to new users\n                            setCurrentTeacherSelection(selectionData);\n                            // Use both teacher-selection (for range highlighting) and teacher-text-highlight (for compatibility)\n                            socketServiceInstance.sendTeacherSelection(roomId, selectionData);\n                            socketServiceInstance.sendTeacherTextHighlight(roomId, selectionData);\n                        } else if (roomId) {\n                            // Clear selection when teacher deselects\n                            console.log('🧹 Teacher clearing selection');\n                            setCurrentTeacherSelection(null);\n                            socketServiceInstance.clearTeacherSelection(roomId);\n                            socketServiceInstance.clearTeacherTextHighlight(roomId);\n                        }\n                    });\n                }\n            }\n        }, 500) // Small delay to ensure the editor is fully ready\n        ;\n    };\n    // Track if the change is from a remote update\n    const isRemoteUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Create a throttled version of the code change handler\n    // This prevents sending too many updates when typing quickly\n    // but ensures updates are sent at regular intervals\n    const throttledCodeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default()({\n        \"CodeEditor.useRef\": (code)=>{\n            if (roomId && isConnected) {\n                console.log(\"Sending throttled code update to room \".concat(roomId, \", length: \").concat(code.length));\n                socketService.sendCodeChange(roomId, code);\n            }\n        }\n    }[\"CodeEditor.useRef\"], 50) // Use throttle with a short interval for more responsive updates\n    ).current;\n    // Create a debounced version of the typing notification\n    const debouncedTypingNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default()({\n        \"CodeEditor.useCallback[debouncedTypingNotification]\": ()=>{\n            if (roomId && isConnected) {\n                // Always send the current username with typing notifications\n                console.log(\"Sending typing notification with username: \".concat(username));\n                socketService.sendTyping(roomId, username);\n            }\n        }\n    }[\"CodeEditor.useCallback[debouncedTypingNotification]\"], 1000), [\n        roomId,\n        isConnected,\n        username\n    ]);\n    // Handle editor change\n    const handleEditorChange = (value)=>{\n        if (typeof value !== \"string\") return;\n        // Make sure loading is off during code changes\n        setIsLoading(false);\n        // If this change is from a remote update, just update the state and return\n        if (isRemoteUpdate.current) {\n            console.log(\"Handling remote update, not emitting\");\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            isRemoteUpdate.current = false;\n            return;\n        }\n        // Only process if the value actually changed\n        if (value !== latestCodeRef.current) {\n            // Update local state immediately\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            // Send typing notification (debounced)\n            debouncedTypingNotification();\n            // Send code update (throttled)\n            throttledCodeChange(value);\n            // If not connected, try to reconnect\n            if (!isConnected) {\n                console.log(\"Socket not connected, attempting to reconnect\");\n                socketService.connect();\n            }\n        }\n    };\n    // Copy code to clipboard\n    const copyCodeToClipboard = ()=>{\n        if (navigator.clipboard && code) {\n            navigator.clipboard.writeText(code).then(()=>{\n                setCopySuccess(true);\n                setTimeout(()=>setCopySuccess(false), 2000);\n            }).catch((err)=>{\n                console.error('Failed to copy code: ', err);\n            });\n        }\n    };\n    // Toggle user list\n    const toggleUserList = ()=>{\n        setShowUserList((prev)=>!prev);\n    };\n    // Change language\n    const changeLanguage = (lang)=>{\n        setLanguage(lang);\n        // Log available runtimes for this language\n        if (runtimesLoaded && runtimes.length > 0) {\n            const availableRuntimes = runtimes.filter((runtime)=>runtime.language === lang || runtime.aliases && runtime.aliases.includes(lang));\n            if (availableRuntimes.length > 0) {\n                console.log(\"Available runtimes for \".concat(lang, \":\"), availableRuntimes);\n            } else {\n                console.log(\"No runtimes available for \".concat(lang));\n            }\n        }\n    };\n    // Format code\n    const formatCurrentCode = ()=>{\n        if (!code || !editorRef.current) return;\n        try {\n            // Format the code based on the current language\n            const formattedCode = (0,_utils_formatCode__WEBPACK_IMPORTED_MODULE_8__.formatCode)(code, language);\n            // Only update if the code actually changed\n            if (formattedCode !== code) {\n                // Update the editor\n                const model = editorRef.current.getModel();\n                if (model) {\n                    // Store current cursor position/selection\n                    const currentPosition = editorRef.current.getPosition();\n                    const currentSelection = editorRef.current.getSelection();\n                    // Update the editor content\n                    editorRef.current.executeEdits('format', [\n                        {\n                            range: model.getFullModelRange(),\n                            text: formattedCode,\n                            forceMoveMarkers: true\n                        }\n                    ]);\n                    // Restore cursor position if possible\n                    if (currentPosition) {\n                        editorRef.current.setPosition(currentPosition);\n                    }\n                    if (currentSelection) {\n                        editorRef.current.setSelection(currentSelection);\n                    }\n                    // Update state and ref\n                    setCode(formattedCode);\n                    latestCodeRef.current = formattedCode;\n                    // Send the formatted code to other users\n                    if (roomId && isConnected) {\n                        console.log(\"Sending formatted code to room \".concat(roomId, \", length: \").concat(formattedCode.length));\n                        socketService.sendCodeChange(roomId, formattedCode);\n                    }\n                    // Show a success message\n                    console.log('Code formatted successfully');\n                }\n            } else {\n                console.log('Code is already formatted');\n            }\n        } catch (error) {\n            console.error('Error formatting code:', error);\n        }\n    };\n    // Clear execution output\n    const clearOutput = ()=>{\n        setExecutionOutput(null);\n        setExecutionError(null);\n    };\n    // Fetch and store available runtimes from Piston API\n    const fetchRuntimes = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get('https://emkc.org/api/v2/piston/runtimes');\n            console.log('Piston API status:', response.status);\n            console.log('Available runtimes:', response.data);\n            // Store the runtimes in state\n            setRuntimes(response.data);\n            setRuntimesLoaded(true);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error fetching Piston API runtimes:', error);\n            return {\n                success: false,\n                error: axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) ? \"\".concat(error.message, \" - \").concat(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) || 'Unknown', \" \").concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText) || '') : String(error)\n            };\n        }\n    };\n    // Check if the Piston API is available\n    const checkPistonAPI = async ()=>{\n        // If we haven't loaded runtimes yet, fetch them\n        if (!runtimesLoaded) {\n            return await fetchRuntimes();\n        }\n        // If we already have runtimes, just return success\n        if (runtimes.length > 0) {\n            return {\n                success: true,\n                data: runtimes\n            };\n        }\n        // If we've tried to load runtimes but have none, try again\n        return await fetchRuntimes();\n    };\n    // Test the Piston API with a hardcoded sample payload\n    const testPistonAPI = async ()=>{\n        setIsExecuting(true);\n        setExecutionError(null);\n        setExecutionOutput(null);\n        setShowOutput(true);\n        try {\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Find JavaScript runtime\n            const jsRuntimes = apiStatus.data.filter((runtime)=>runtime.language === \"javascript\" || runtime.aliases && runtime.aliases.includes(\"javascript\"));\n            if (jsRuntimes.length === 0) {\n                setExecutionError('No JavaScript runtime found. Please try a different language.');\n                return;\n            }\n            const jsRuntime = jsRuntimes[0];\n            console.log(\"Using JavaScript runtime: \".concat(jsRuntime.language, \" \").concat(jsRuntime.version));\n            // Sample JavaScript code that should work\n            const samplePayload = {\n                language: jsRuntime.language,\n                version: jsRuntime.version,\n                files: [\n                    {\n                        name: \"main.js\",\n                        content: \"console.log('Hello, World!');\"\n                    }\n                ],\n                stdin: \"\",\n                args: []\n            };\n            console.log('Testing Piston API with sample payload:', JSON.stringify(samplePayload, null, 2));\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', samplePayload);\n            console.log('Sample execution response:', response.data);\n            if (response.data.run) {\n                setExecutionOutput(\"API Test Successful!\\n\\n\" + \"Output: \" + response.data.run.stdout + \"\\n\\n\" + \"The API is working correctly. You can now run your own code.\");\n            } else {\n                setExecutionError('API test failed. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error testing Piston API:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                setExecutionError(\"API Test Failed: \".concat(error.response.status, \" \").concat(error.response.statusText, \"\\n\\n\") + JSON.stringify(error.response.data, null, 2));\n            } else {\n                setExecutionError(error instanceof Error ? \"API Test Error: \".concat(error.message) : 'An unknown error occurred while testing the API.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // Execute code using Piston API\n    const executeCode = async ()=>{\n        if (!code || isExecuting) return;\n        // Map Monaco editor language to Piston API language\n        const languageMap = {\n            javascript: \"javascript\",\n            typescript: \"typescript\",\n            python: \"python3\",\n            python3: \"python3\",\n            java: \"java\",\n            csharp: \"csharp\",\n            c: \"c\",\n            cpp: \"cpp\",\n            go: \"go\",\n            ruby: \"ruby\",\n            rust: \"rust\",\n            php: \"php\"\n        };\n        // Get the mapped language\n        const pistonLanguage = languageMap[language] || language;\n        // Check if we have runtimes loaded\n        if (!runtimesLoaded || runtimes.length === 0) {\n            setExecutionError('Runtimes not loaded yet. Please try again in a moment.');\n            setShowOutput(true);\n            return;\n        }\n        // Find available runtimes for the selected language\n        const availableRuntimes = runtimes.filter((runtime)=>runtime.language === pistonLanguage || runtime.aliases && runtime.aliases.includes(pistonLanguage));\n        // Check if the language is supported\n        if (availableRuntimes.length === 0) {\n            // Get a list of supported languages from the runtimes\n            const supportedLanguages = [\n                ...new Set(runtimes.flatMap((runtime)=>[\n                        runtime.language,\n                        ...runtime.aliases || []\n                    ]))\n            ].sort();\n            setExecutionError(\"The language '\".concat(language, \"' (mapped to '\").concat(pistonLanguage, \"') is not supported by the Piston API.\\n\\n\") + \"Supported languages: \".concat(supportedLanguages.join(', ')));\n            setShowOutput(true);\n            return;\n        }\n        // Get the latest version of the runtime\n        const selectedRuntime = availableRuntimes[0];\n        try {\n            setIsExecuting(true);\n            setExecutionError(null);\n            setExecutionOutput(null);\n            setShowOutput(true);\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Determine file extension based on language\n            let fileExtension = '';\n            if (selectedRuntime.language === 'python3' || selectedRuntime.language === 'python') fileExtension = '.py';\n            else if (selectedRuntime.language === 'javascript') fileExtension = '.js';\n            else if (selectedRuntime.language === 'typescript') fileExtension = '.ts';\n            else if (selectedRuntime.language === 'java') fileExtension = '.java';\n            else if (selectedRuntime.language === 'csharp') fileExtension = '.cs';\n            else if (selectedRuntime.language === 'c') fileExtension = '.c';\n            else if (selectedRuntime.language === 'cpp') fileExtension = '.cpp';\n            else if (selectedRuntime.language === 'go') fileExtension = '.go';\n            else if (selectedRuntime.language === 'rust') fileExtension = '.rs';\n            else if (selectedRuntime.language === 'ruby') fileExtension = '.rb';\n            else if (selectedRuntime.language === 'php') fileExtension = '.php';\n            else fileExtension = \".\".concat(selectedRuntime.language);\n            console.log(\"Selected runtime: \".concat(selectedRuntime.language, \" \").concat(selectedRuntime.version));\n            // Prepare the payload according to Piston API documentation\n            const payload = {\n                language: selectedRuntime.language,\n                version: selectedRuntime.version,\n                files: [\n                    {\n                        name: \"main\".concat(fileExtension),\n                        content: code\n                    }\n                ],\n                stdin: '',\n                args: [],\n                compile_timeout: 10000,\n                run_timeout: 5000\n            };\n            // Log the payload for debugging\n            console.log(\"Executing \".concat(pistonLanguage, \" code with payload:\"), JSON.stringify(payload, null, 2));\n            // Make the API request\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', payload);\n            console.log('Execution response:', response.data);\n            const result = response.data;\n            if (result.run) {\n                // Format the output\n                let output = '';\n                let hasOutput = false;\n                // Add compile output if available (for compiled languages)\n                if (result.compile && result.compile.stderr) {\n                    output += \"Compilation output:\\n\".concat(result.compile.stderr, \"\\n\\n\");\n                    hasOutput = true;\n                }\n                // Add standard output\n                if (result.run.stdout) {\n                    output += result.run.stdout;\n                    hasOutput = true;\n                }\n                // Add error output\n                if (result.run.stderr) {\n                    if (hasOutput) output += '\\n';\n                    output += \"Error output:\\n\".concat(result.run.stderr);\n                    hasOutput = true;\n                }\n                // Add exit code if non-zero\n                if (result.run.code !== 0) {\n                    if (hasOutput) output += '\\n';\n                    output += \"\\nProcess exited with code \".concat(result.run.code);\n                    hasOutput = true;\n                }\n                if (!hasOutput) {\n                    output = 'Program executed successfully with no output.';\n                }\n                setExecutionOutput(output);\n            } else {\n                setExecutionError('Failed to execute code. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error executing code:', error);\n            // Handle Axios errors with more detailed information\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                const statusCode = error.response.status;\n                const responseData = error.response.data;\n                console.error('API Error Details:', {\n                    status: statusCode,\n                    data: responseData\n                });\n                // Format a more helpful error message\n                if (statusCode === 400) {\n                    setExecutionError(\"API Error (400 Bad Request): \".concat(JSON.stringify(responseData), \"\\n\\n\") + 'This usually means the API request format is incorrect. ' + 'Please check the console for more details.');\n                } else if (statusCode === 429) {\n                    setExecutionError('Rate limit exceeded. Please try again later.');\n                } else {\n                    setExecutionError(\"API Error (\".concat(statusCode, \"): \").concat(JSON.stringify(responseData), \"\\n\\n\") + 'Please check the console for more details.');\n                }\n            } else {\n                // Handle non-Axios errors\n                setExecutionError(error instanceof Error ? \"Error: \".concat(error.message) : 'An unknown error occurred while executing the code.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // This function will be called when we receive a code update from another user\n    const handleCodeUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleCodeUpdate]\": (incomingCode)=>{\n            console.log(\"Remote code update received, length:\", incomingCode.length);\n            // Make sure loading is off during code updates\n            setIsLoading(false);\n            // Only update if the code is different from our latest code\n            if (incomingCode !== latestCodeRef.current) {\n                try {\n                    // Set the flag to indicate this is a remote update\n                    isRemoteUpdate.current = true;\n                    // Update the editor if it's mounted\n                    if (editorRef.current) {\n                        console.log(\"Updating editor with remote code\");\n                        // Use the editor's model to update the value to preserve cursor position\n                        const model = editorRef.current.getModel();\n                        if (model) {\n                            // Store current cursor position/selection\n                            const currentPosition = editorRef.current.getPosition();\n                            const currentSelection = editorRef.current.getSelection();\n                            // Use executeEdits with better handling of cursor position\n                            editorRef.current.executeEdits('remote', [\n                                {\n                                    range: model.getFullModelRange(),\n                                    text: incomingCode,\n                                    forceMoveMarkers: true\n                                }\n                            ]);\n                            // Restore cursor position if possible\n                            if (currentPosition) {\n                                editorRef.current.setPosition(currentPosition);\n                            }\n                            if (currentSelection) {\n                                editorRef.current.setSelection(currentSelection);\n                            }\n                            // Also update our state and ref\n                            setCode(incomingCode);\n                            latestCodeRef.current = incomingCode;\n                        }\n                    } else {\n                        // If editor isn't mounted yet, just update the state and ref\n                        console.log(\"Editor not mounted, updating state only\");\n                        setCode(incomingCode);\n                        latestCodeRef.current = incomingCode;\n                        isRemoteUpdate.current = false;\n                    }\n                } catch (error) {\n                    console.error(\"Error updating editor with remote code:\", error);\n                    isRemoteUpdate.current = false;\n                }\n            } else {\n                console.log(\"Remote code matches current code, ignoring\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleCodeUpdate]\"], []);\n    // State to track cursor positions\n    const [cursorPositions, setCursorPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Emit cursor movement\n    const handleMouseMove = (e)=>{\n        const position = {\n            x: e.clientX,\n            y: e.clientY\n        };\n        _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().sendCursorMove(roomId, username, position);\n    };\n    // Listen for cursor movement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            const handleCursorMove = {\n                \"CodeEditor.useEffect.handleCursorMove\": (param)=>{\n                    let { userId, position } = param;\n                    setCursorPositions({\n                        \"CodeEditor.useEffect.handleCursorMove\": (prev)=>({\n                                ...prev,\n                                [userId]: position\n                            })\n                    }[\"CodeEditor.useEffect.handleCursorMove\"]);\n                }\n            }[\"CodeEditor.useEffect.handleCursorMove\"];\n            _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().on(\"cursor-move\", handleCursorMove);\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().off(\"cursor-move\", handleCursorMove);\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId\n    ]);\n    // Render cursors\n    const renderCursors = ()=>{\n        return Object.entries(cursorPositions).map((param)=>{\n            let [userId, position] = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    left: position.x,\n                    top: position.y,\n                    backgroundColor: \"red\",\n                    width: 10,\n                    height: 10,\n                    borderRadius: \"50%\",\n                    pointerEvents: \"none\"\n                }\n            }, userId, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 833,\n                columnNumber: 7\n            }, this);\n        });\n    };\n    // Fetch runtimes when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            fetchRuntimes();\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    // Handle request for initial code from new users\n    const handleGetInitialCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleGetInitialCode]\": (data)=>{\n            console.log(\"Received request for initial code from \".concat(data.requestingUsername, \" (\").concat(data.requestingUserId, \")\"));\n            // Only respond if we have code and we're connected\n            if (roomId && latestCodeRef.current && latestCodeRef.current.trim() !== \"// Start coding...\") {\n                const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                console.log(\"Sending initial code to \".concat(data.requestingUsername, \", length: \").concat(latestCodeRef.current.length));\n                socketServiceInstance.sendInitialCode(roomId, latestCodeRef.current, data.requestingUserId);\n            } else {\n                console.log(\"Not sending initial code - no meaningful code to share or not in room\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleGetInitialCode]\"], [\n        roomId\n    ]);\n    // Handle receiving initial code as a new user\n    const handleInitialCodeReceived = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleInitialCodeReceived]\": (data)=>{\n            console.log(\"Received initial code, length: \".concat(data.code.length));\n            // Only apply initial code if we don't have meaningful code yet\n            // This prevents overwriting code that the user might have already started typing\n            if (latestCodeRef.current === \"// Start coding...\" || latestCodeRef.current.trim() === \"\") {\n                console.log(\"Applying received initial code\");\n                // Set the flag to indicate this is a remote update\n                isRemoteUpdate.current = true;\n                // Update the code state and ref\n                setCode(data.code);\n                latestCodeRef.current = data.code;\n                // Update the editor if it's mounted\n                if (editorRef.current) {\n                    editorRef.current.setValue(data.code);\n                }\n            } else {\n                console.log(\"Not applying initial code - user already has meaningful code\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleInitialCodeReceived]\"], []);\n    // Handle teacher selection highlighting\n    const handleTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherSelection]\": (data)=>{\n            console.log(\"Received teacher selection from \".concat(data.teacherName, \":\"), data.selection);\n            // Extra debug: check if CSS class is present\n            if (true) {\n                const styleExists = !!document.querySelector('style#teacher-highlighting-css') || !!Array.from(document.styleSheets).find({\n                    \"CodeEditor.useCallback[handleTeacherSelection]\": (sheet)=>{\n                        try {\n                            return Array.from(sheet.cssRules).some({\n                                \"CodeEditor.useCallback[handleTeacherSelection]\": (rule)=>{\n                                    var _rule_selectorText;\n                                    return (_rule_selectorText = rule.selectorText) === null || _rule_selectorText === void 0 ? void 0 : _rule_selectorText.includes('.teacher-selection-highlight');\n                                }\n                            }[\"CodeEditor.useCallback[handleTeacherSelection]\"]);\n                        } catch (e) {\n                            return false;\n                        }\n                    }\n                }[\"CodeEditor.useCallback[handleTeacherSelection]\"]);\n                if (!styleExists) {\n                    console.warn('⚠️ teacher-selection-highlight CSS missing, injecting fallback');\n                    const style = document.createElement('style');\n                    style.id = 'teacher-highlighting-css-fallback';\n                    style.innerHTML = \"\\n          .teacher-selection-highlight {\\n            background: rgba(255, 230, 80, 0.7) !important;\\n            border-bottom: 2px solid orange !important;\\n            border-radius: 2px;\\n          }\\n        \";\n                    document.head.appendChild(style);\n                }\n            }\n            if (!editorRef.current || userRole === 'teacher') {\n                console.log('Skipping teacher highlight: editor not available or user is teacher');\n                return; // Don't show teacher highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model || !data.selection) {\n                    console.log('No model or selection');\n                    return;\n                }\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.selection.startLineNumber, data.selection.startColumn, data.selection.endLineNumber, data.selection.endColumn);\n                console.log('Applying teacher selection highlight with range:', range);\n                // Clear previous teacher selection decorations and apply new ones with yellow/orange styling\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-selection-highlight',\n                            hoverMessage: {\n                                value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" selected this text\\n\\nRange: Line \").concat(data.selection.startLineNumber, \"-\").concat(data.selection.endLineNumber)\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Right\n                            }\n                        }\n                    }\n                ]);\n                console.log('New teacher selection decoration IDs:', newDecorations);\n                setTeacherSelectionDecorations(newDecorations);\n                // Debug: Check DOM for highlight\n                setTimeout({\n                    \"CodeEditor.useCallback[handleTeacherSelection]\": ()=>{\n                        const elements = document.querySelectorAll('.teacher-selection-highlight');\n                        console.log('DOM .teacher-selection-highlight count:', elements.length);\n                        elements.forEach({\n                            \"CodeEditor.useCallback[handleTeacherSelection]\": (el, i)=>{\n                                console.log(\"Highlight \".concat(i, \":\"), el, el.className, el.getAttribute('style'));\n                            }\n                        }[\"CodeEditor.useCallback[handleTeacherSelection]\"]);\n                    }\n                }[\"CodeEditor.useCallback[handleTeacherSelection]\"], 200);\n            } catch (error) {\n                console.error('Error applying teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle clearing teacher selection\n    const handleClearTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherSelection]\": (data)=>{\n            console.log(\"Teacher \".concat(data.teacherName, \" cleared selection\"));\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                // Clear all teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, []);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error clearing teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle teacher cursor position updates with blinking orange cursor decoration\n    const handleTeacherCursorPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherCursorPosition]\": (data)=>{\n            console.log(\"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" cursor at line \").concat(data.position.lineNumber, \", column \").concat(data.position.column));\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't show cursor to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.log('⏭️ Skipping teacher cursor: model not available');\n                    return;\n                }\n                // Validate cursor position bounds\n                const lineCount = model.getLineCount();\n                if (data.position.lineNumber > lineCount || data.position.lineNumber <= 0) {\n                    console.log('⏭️ Skipping teacher cursor: position out of bounds');\n                    return;\n                }\n                // Create a range for the cursor position (single character width)\n                const cursorRange = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.position.lineNumber, data.position.column, data.position.lineNumber, data.position.column + 1);\n                // Clear previous teacher cursor decorations and apply new one\n                const newCursorDecorations = editor.deltaDecorations(teacherCursorDecorations, [\n                    {\n                        range: cursorRange,\n                        options: {\n                            className: 'teacher-cursor',\n                            hoverMessage: {\n                                value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" cursor position\\n\\nLine \").concat(data.position.lineNumber, \", Column \").concat(data.position.column)\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Left\n                            }\n                        }\n                    }\n                ]);\n                setTeacherCursorDecorations(newCursorDecorations);\n                // Set the teacher cursor position for overlay rendering\n                setTeacherCursorPosition({\n                    lineNumber: data.position.lineNumber,\n                    column: data.position.column,\n                    teacherName: data.teacherName\n                });\n            } catch (error) {\n                console.error('❌ Error handling teacher cursor position:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherCursorPosition]\"], [\n        userRole,\n        teacherCursorDecorations\n    ]);\n    // Handle teacher text highlight with enhanced error handling and multiple CSS class fallbacks\n    const handleTeacherTextHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherTextHighlight]\": (data)=>{\n            console.log(\"\\uD83C\\uDFA8 Teacher \".concat(data.teacherName, \" highlighted text:\"), data.selection);\n            console.log(\"\\uD83D\\uDD0D Debug info:\", {\n                editorAvailable: !!editorRef.current,\n                userRole: userRole,\n                roomId: roomId,\n                currentDecorations: teacherSelectionDecorations.length,\n                monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined'\n            });\n            if (!editorRef.current || userRole === 'teacher') {\n                console.log('⏭️ Skipping teacher text highlight: editor not available or user is teacher');\n                return; // Don't show highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('❌ Monaco Editor model not available');\n                    return;\n                }\n                if (!data.selection) {\n                    console.error('❌ Selection data is missing');\n                    return;\n                }\n                // Validate selection data with more robust checking\n                const { startLineNumber, startColumn, endLineNumber, endColumn } = data.selection;\n                if (typeof startLineNumber !== 'number' || startLineNumber <= 0 || typeof startColumn !== 'number' || startColumn <= 0 || typeof endLineNumber !== 'number' || endLineNumber <= 0 || typeof endColumn !== 'number' || endColumn <= 0) {\n                    console.error('❌ Invalid selection data:', data.selection);\n                    return;\n                }\n                // Ensure the selection is within the model bounds\n                const lineCount = model.getLineCount();\n                if (startLineNumber > lineCount || endLineNumber > lineCount) {\n                    console.warn('⚠️ Selection extends beyond model bounds, adjusting...');\n                    const adjustedEndLine = Math.min(endLineNumber, lineCount);\n                    const adjustedEndColumn = adjustedEndLine === lineCount ? model.getLineMaxColumn(adjustedEndLine) : endColumn;\n                    console.log(\"Adjusted selection: (\".concat(startLineNumber, \", \").concat(startColumn, \", \").concat(adjustedEndLine, \", \").concat(adjustedEndColumn, \")\"));\n                }\n                console.log(\"✅ Creating Monaco Range: (\".concat(startLineNumber, \", \").concat(startColumn, \", \").concat(endLineNumber, \", \").concat(endColumn, \")\"));\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(startLineNumber, startColumn, endLineNumber, endColumn);\n                console.log('✅ Monaco Range created successfully:', range);\n                // Apply decoration with multiple CSS class options and inline styles for better compatibility\n                console.log('🎨 Applying teacher text highlight decoration...');\n                const decorationOptions = {\n                    range: range,\n                    options: {\n                        className: 'teacher-highlight teacher-text-highlight',\n                        hoverMessage: {\n                            value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" highlighted this text\\n\\nClick to focus on this selection\")\n                        },\n                        stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                        // Add inline styles as fallback\n                        inlineClassName: 'teacher-highlight-inline',\n                        // Force inline styles for maximum compatibility\n                        overviewRuler: {\n                            color: 'rgba(59, 130, 246, 0.8)',\n                            position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Right\n                        },\n                        // Add background color directly\n                        backgroundColor: 'rgba(59, 130, 246, 0.25)',\n                        // Add border styling\n                        border: '2px solid rgba(59, 130, 246, 0.7)'\n                    }\n                };\n                // Clear previous decorations and apply new ones\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [\n                    decorationOptions\n                ]);\n                console.log('✅ Teacher text highlight decorations applied:', newDecorations);\n                console.log('🔍 Decoration details:', decorationOptions);\n                setTeacherSelectionDecorations(newDecorations);\n                // Debug: Check if decorations are actually in the DOM\n                setTimeout({\n                    \"CodeEditor.useCallback[handleTeacherTextHighlight]\": ()=>{\n                        var _editor_getModel;\n                        const decorationElements = document.querySelectorAll('.teacher-text-highlight, .teacher-highlight');\n                        console.log('🔍 Found decoration elements in DOM:', decorationElements.length);\n                        decorationElements.forEach({\n                            \"CodeEditor.useCallback[handleTeacherTextHighlight]\": (el, index)=>{\n                                console.log(\"\\uD83D\\uDD0D Decoration \".concat(index + 1, \":\"), {\n                                    element: el,\n                                    className: el.className,\n                                    style: el.getAttribute('style'),\n                                    computedStyle: window.getComputedStyle(el).background\n                                });\n                            }\n                        }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"]);\n                        // Also check Monaco's internal decorations\n                        const allDecorations = (_editor_getModel = editor.getModel()) === null || _editor_getModel === void 0 ? void 0 : _editor_getModel.getAllDecorations();\n                        console.log('🔍 All Monaco decorations:', allDecorations === null || allDecorations === void 0 ? void 0 : allDecorations.length);\n                        const teacherDecorations = allDecorations === null || allDecorations === void 0 ? void 0 : allDecorations.filter({\n                            \"CodeEditor.useCallback[handleTeacherTextHighlight]\": (d)=>{\n                                var _d_options_className, _d_options_className1;\n                                return ((_d_options_className = d.options.className) === null || _d_options_className === void 0 ? void 0 : _d_options_className.includes('teacher-highlight')) || ((_d_options_className1 = d.options.className) === null || _d_options_className1 === void 0 ? void 0 : _d_options_className1.includes('teacher-text-highlight'));\n                            }\n                        }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"]);\n                        console.log('🔍 Teacher decorations in model:', teacherDecorations);\n                    }\n                }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"], 200);\n                // Force a layout update to ensure the decoration is visible\n                setTimeout({\n                    \"CodeEditor.useCallback[handleTeacherTextHighlight]\": ()=>{\n                        editor.layout();\n                        console.log('🔄 Editor layout refreshed');\n                    }\n                }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"], 100);\n            } catch (error) {\n                var _editorRef_current;\n                console.error('❌ Error applying teacher text highlight:', error);\n                console.error('🔍 Error details:', {\n                    editorAvailable: !!editorRef.current,\n                    modelAvailable: !!((_editorRef_current = editorRef.current) === null || _editorRef_current === void 0 ? void 0 : _editorRef_current.getModel()),\n                    selectionData: data.selection,\n                    userRole: userRole,\n                    monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined',\n                    currentDecorations: teacherSelectionDecorations\n                });\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherTextHighlight]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle clearing teacher text highlight with enhanced logging\n    const handleClearTeacherTextHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherTextHighlight]\": (data)=>{\n            console.log(\"\\uD83E\\uDDF9 Teacher \".concat(data.teacherName, \" cleared text highlight\"));\n            console.log(\"\\uD83D\\uDD0D Clear debug info:\", {\n                editorAvailable: !!editorRef.current,\n                userRole: userRole,\n                roomId: roomId,\n                currentDecorations: teacherSelectionDecorations.length,\n                monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined'\n            });\n            if (!editorRef.current || userRole === 'teacher') {\n                console.log('⏭️ Skipping clear teacher text highlight: editor not available or user is teacher');\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('❌ Monaco Editor model not available for clearing highlights');\n                    return;\n                }\n                console.log('🧹 Clearing teacher text highlight decorations...');\n                console.log(\"\\uD83D\\uDD0D Clearing \".concat(teacherSelectionDecorations.length, \" existing decorations\"));\n                // Clear all teacher text highlight decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, []);\n                console.log('✅ Teacher text highlight decorations cleared:', newDecorations);\n                setTeacherSelectionDecorations(newDecorations);\n                // Force a layout update to ensure the decorations are removed\n                setTimeout({\n                    \"CodeEditor.useCallback[handleClearTeacherTextHighlight]\": ()=>{\n                        editor.layout();\n                        console.log('🔄 Editor layout refreshed after clearing highlights');\n                    }\n                }[\"CodeEditor.useCallback[handleClearTeacherTextHighlight]\"], 50);\n            } catch (error) {\n                var _editorRef_current;\n                console.error('❌ Error clearing teacher text highlight:', error);\n                console.error('🔍 Error details:', {\n                    editorAvailable: !!editorRef.current,\n                    modelAvailable: !!((_editorRef_current = editorRef.current) === null || _editorRef_current === void 0 ? void 0 : _editorRef_current.getModel()),\n                    userRole: userRole,\n                    currentDecorations: teacherSelectionDecorations,\n                    monacoAvailable: typeof monaco_editor__WEBPACK_IMPORTED_MODULE_3__ !== 'undefined'\n                });\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherTextHighlight]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle sync events for newly joined users\n    const handleSyncCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleSyncCode]\": (data)=>{\n            console.log('🔄 Received sync-code event:', data);\n            if (!editorRef.current) {\n                console.log('⏭️ Skipping sync-code: editor not available');\n                return;\n            }\n            try {\n                // Set the editor value directly\n                editorRef.current.setValue(data.code);\n                setCode(data.code);\n                latestCodeRef.current = data.code;\n                console.log('✅ Code synced successfully');\n            } catch (error) {\n                console.error('❌ Error syncing code:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleSyncCode]\"], []);\n    const handleSyncTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleSyncTeacherSelection]\": (data)=>{\n            console.log('🔄 Received sync-teacher-selection event:', data);\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't show highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model || !data.selection) {\n                    return;\n                }\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.selection.startLineNumber, data.selection.startColumn, data.selection.endLineNumber, data.selection.endColumn);\n                // Apply teacher selection decoration\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-selection-highlight',\n                            hoverMessage: {\n                                value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" selected this text (synced)\\n\\nRange: Line \").concat(data.selection.startLineNumber, \"-\").concat(data.selection.endLineNumber)\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Right\n                            }\n                        }\n                    }\n                ]);\n                setTeacherSelectionDecorations(newDecorations);\n                console.log('✅ Teacher selection synced successfully');\n            } catch (error) {\n                console.error('❌ Error syncing teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleSyncTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    const handleSyncTeacherCursor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleSyncTeacherCursor]\": (data)=>{\n            console.log('🔄 Received sync-teacher-cursor event:', data);\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't show cursor to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    return;\n                }\n                // Create a range for the cursor position\n                const cursorRange = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.position.lineNumber, data.position.column, data.position.lineNumber, data.position.column + 1);\n                // Apply teacher cursor decoration\n                const newCursorDecorations = editor.deltaDecorations(teacherCursorDecorations, [\n                    {\n                        range: cursorRange,\n                        options: {\n                            className: 'teacher-cursor',\n                            hoverMessage: {\n                                value: \"\\uD83C\\uDFAF Teacher \".concat(data.teacherName, \" cursor position (synced)\\n\\nLine \").concat(data.position.lineNumber, \", Column \").concat(data.position.column)\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Left\n                            }\n                        }\n                    }\n                ]);\n                setTeacherCursorDecorations(newCursorDecorations);\n                // Update teacher cursor position state\n                setTeacherCursorPosition({\n                    lineNumber: data.position.lineNumber,\n                    column: data.position.column,\n                    teacherName: data.teacherName\n                });\n                console.log('✅ Teacher cursor synced successfully');\n            } catch (error) {\n                console.error('❌ Error syncing teacher cursor:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleSyncTeacherCursor]\"], [\n        userRole,\n        teacherCursorDecorations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            // Handle user typing notifications\n            const handleUserTyping = {\n                \"CodeEditor.useEffect.handleUserTyping\": (param)=>{\n                    let { username, userId } = param;\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    // Don't show typing indicator for current user\n                    if (userId && userId === currentUserId) {\n                        return;\n                    }\n                    // Use the exact username without any modifications\n                    // This ensures we display exactly what the user entered on the dashboard\n                    setTypingUser(username);\n                    console.log(\"User typing: \".concat(username));\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserTyping\": ()=>setTypingUser(null)\n                    }[\"CodeEditor.useEffect.handleUserTyping\"], 2000);\n                }\n            }[\"CodeEditor.useEffect.handleUserTyping\"];\n            // Handle user list updates from both user-joined/user-left and room-users-updated events\n            const handleUserListUpdate = {\n                \"CodeEditor.useEffect.handleUserListUpdate\": (data)=>{\n                    console.log('User list update received:', data);\n                    let users = [];\n                    // Handle different event formats\n                    if (Array.isArray(data)) {\n                        // This is from user-joined or user-left events\n                        users = data;\n                    } else if (data && Array.isArray(data.users)) {\n                        // This is from room-users-updated event\n                        users = data.users.map({\n                            \"CodeEditor.useEffect.handleUserListUpdate\": (user)=>{\n                                if (typeof user === 'string') {\n                                    // If it's just a username string, convert to user object\n                                    return {\n                                        username: user\n                                    };\n                                }\n                                return user;\n                            }\n                        }[\"CodeEditor.useEffect.handleUserListUpdate\"]);\n                    }\n                    console.log('Processed users:', users);\n                    console.log('Current username state:', username);\n                    console.log('Stored username:', localStorage.getItem('username'));\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    console.log('Current userId from localStorage:', currentUserId);\n                    // Filter out any invalid users and map to display names\n                    const usernames = users.filter({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>user && user.username\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]).map({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>{\n                            const displayName = user.username;\n                            const isCurrentUser = user.userId === currentUserId;\n                            return isCurrentUser ? \"\".concat(displayName, \" (you)\") : displayName;\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]);\n                    console.log('Setting active users:', usernames);\n                    // Only update if we have users to display\n                    if (usernames.length > 0) {\n                        setActiveUsers(usernames);\n                    } else if (activeUsers.length === 0) {\n                        // If we don't have users from the server but we're connected, add ourselves\n                        const storedUsername = localStorage.getItem('username');\n                        if (storedUsername) {\n                            setActiveUsers([\n                                \"\".concat(storedUsername, \" (you)\")\n                            ]);\n                        }\n                    }\n                    // Log the current state of the active users list\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserListUpdate\": ()=>{\n                            console.log('Active users state after update:', activeUsers);\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate\"], 100);\n                }\n            }[\"CodeEditor.useEffect.handleUserListUpdate\"];\n            // Register event listeners\n            const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n            socketServiceInstance.on('code-update', handleCodeUpdate);\n            socketServiceInstance.on('user-typing', handleUserTyping);\n            socketServiceInstance.on('user-joined', handleUserListUpdate);\n            socketServiceInstance.on('user-left', handleUserListUpdate);\n            socketServiceInstance.on('room-users-updated', handleUserListUpdate);\n            socketServiceInstance.on('get-initial-code', handleGetInitialCode);\n            socketServiceInstance.on('initial-code-received', handleInitialCodeReceived);\n            socketServiceInstance.on('teacher-selection', handleTeacherSelection);\n            socketServiceInstance.on('clear-teacher-selection', handleClearTeacherSelection);\n            socketServiceInstance.on('teacher-cursor-position', handleTeacherCursorPosition);\n            socketServiceInstance.on('teacher-text-highlight', handleTeacherTextHighlight);\n            socketServiceInstance.on('clear-teacher-text-highlight', handleClearTeacherTextHighlight);\n            // Join the room when component mounts\n            if (roomId) {\n                console.log(\"Joining room:\", roomId);\n                // Only show loading on initial join, not for code updates\n                if (!editorRef.current) {\n                    setIsLoading(true);\n                }\n                const joinRoom = {\n                    \"CodeEditor.useEffect.joinRoom\": async ()=>{\n                        try {\n                            if (socketServiceInstance.isConnected()) {\n                                console.log(\"Socket connected, joining room\");\n                                const { username: validatedUsername, users, role } = await socketServiceInstance.joinRoom(roomId, username);\n                                console.log(\"Successfully joined room:\", roomId);\n                                console.log(\"Users in room:\", users);\n                                console.log(\"User role:\", role);\n                                if (validatedUsername !== username) {\n                                    console.log(\"Server validated username from \".concat(username, \" to \").concat(validatedUsername));\n                                    setUsername(validatedUsername);\n                                    localStorage.setItem('username', validatedUsername);\n                                }\n                                // Set user role and persist to localStorage\n                                if (role) {\n                                    setUserRole(role);\n                                    localStorage.setItem('userRole', role);\n                                    console.log(\"User role set to: \".concat(role, \" and persisted to localStorage\"));\n                                }\n                                if (users && Array.isArray(users)) {\n                                    console.log('Setting active users from join response');\n                                    handleUserListUpdate(users);\n                                }\n                                setIsLoading(false);\n                            } else {\n                                console.log(\"Socket not connected, trying to connect...\");\n                                await new Promise({\n                                    \"CodeEditor.useEffect.joinRoom\": (resolve)=>socketServiceInstance.onConnect({\n                                            \"CodeEditor.useEffect.joinRoom\": ()=>resolve()\n                                        }[\"CodeEditor.useEffect.joinRoom\"])\n                                }[\"CodeEditor.useEffect.joinRoom\"]);\n                                // After connect, try join again\n                                await joinRoom();\n                                return;\n                            }\n                        } catch (error) {\n                            console.error(\"Error joining room:\", error);\n                            setIsLoading(false);\n                        }\n                    }\n                }[\"CodeEditor.useEffect.joinRoom\"];\n                // Start the join process\n                joinRoom();\n                // Set a timeout to hide the loading screen even if the join fails\n                setTimeout({\n                    \"CodeEditor.useEffect\": ()=>{\n                        setIsLoading(false);\n                    }\n                }[\"CodeEditor.useEffect\"], 3000);\n            }\n            // Clean up event listeners when component unmounts\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    // Use an instance of SocketService\n                    const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                    socketServiceInstance.off('code-update', handleCodeUpdate);\n                    socketServiceInstance.off('user-typing', handleUserTyping);\n                    socketServiceInstance.off('user-joined', handleUserListUpdate);\n                    socketServiceInstance.off('user-left', handleUserListUpdate);\n                    socketServiceInstance.off('room-users-updated', handleUserListUpdate);\n                    socketServiceInstance.off('get-initial-code', handleGetInitialCode);\n                    socketServiceInstance.off('initial-code-received', handleInitialCodeReceived);\n                    socketServiceInstance.off('teacher-selection', handleTeacherSelection);\n                    socketServiceInstance.off('clear-teacher-selection', handleClearTeacherSelection);\n                    socketServiceInstance.off('teacher-cursor-position', handleTeacherCursorPosition);\n                    socketServiceInstance.off('teacher-text-highlight', handleTeacherTextHighlight);\n                    socketServiceInstance.off('clear-teacher-text-highlight', handleClearTeacherTextHighlight);\n                    // Leave the room when component unmounts\n                    if (roomId) {\n                        socketServiceInstance.leaveRoom(roomId);\n                    }\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId,\n        initialUsername,\n        username,\n        handleCodeUpdate,\n        handleGetInitialCode,\n        handleInitialCodeReceived,\n        handleTeacherSelection,\n        handleClearTeacherSelection,\n        handleTeacherCursorPosition,\n        handleTeacherTextHighlight,\n        handleClearTeacherTextHighlight\n    ]);\n    // Download code as file\n    const handleDownload = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"code-\".concat(roomId || \"session\", \".txt\");\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    // Upload code from file\n    const handleUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            var _event_target;\n            if (typeof ((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result) === \"string\") {\n                setCode(event.target.result);\n                latestCodeRef.current = event.target.result;\n            }\n        };\n        reader.readAsText(file);\n    };\n    // Show Monaco's find/replace dialog\n    const handleFindReplace = ()=>{\n        if (editorRef.current) {\n            const action = editorRef.current.getAction(\"actions.find\");\n            if (action) action.run();\n        }\n    };\n    // Insert code snippet\n    const insertSnippet = (snippet)=>{\n        if (editorRef.current) {\n            const model = editorRef.current.getModel();\n            const position = editorRef.current.getPosition();\n            if (model && position) {\n                editorRef.current.executeEdits(\"snippet\", [\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(position.lineNumber, position.column, position.lineNumber, position.column),\n                        text: snippet,\n                        forceMoveMarkers: true\n                    }\n                ]);\n                editorRef.current.focus();\n            }\n        }\n    };\n    // Use the instance of SocketService for the connect method\n    const handleReconnect = ()=>{\n        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n        socketServiceInstance.connect();\n    };\n    // Manual test function for debugging teacher text highlighting\n    const testManualHighlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[testManualHighlight]\": ()=>{\n            console.log('🧪 Manual test highlight triggered');\n            if (!editorRef.current) {\n                console.error('❌ Editor not available for manual test');\n                return;\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model) {\n                    console.error('❌ Monaco Editor model not available for manual test');\n                    return;\n                }\n                console.log('✅ Manual test: Editor and model available');\n                console.log('✅ Manual test: Model line count:', model.getLineCount());\n                console.log('✅ Manual test: Model content preview:', model.getValue().substring(0, 100));\n                // Create a test range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(1, 1, 1, 20);\n                console.log('✅ Manual test: Range created:', range);\n                // Test multiple decoration approaches with yellow/orange theme\n                const decorationOptions = [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-selection-highlight teacher-text-highlight teacher-highlight',\n                            hoverMessage: {\n                                value: 'Manual test selection highlight - Yellow background with orange border!'\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Right\n                            }\n                        }\n                    },\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(2, 1, 2, 15),\n                        options: {\n                            className: 'teacher-cursor',\n                            hoverMessage: {\n                                value: 'Manual test cursor - Blinking orange cursor!'\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges\n                        }\n                    },\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(3, 1, 3, 25),\n                        options: {\n                            className: 'teacher-selection-highlight',\n                            hoverMessage: {\n                                value: 'Manual test selection - Line 3!'\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,\n                            overviewRuler: {\n                                color: 'rgba(255, 152, 0, 0.8)',\n                                position: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.OverviewRulerLane.Left\n                            }\n                        }\n                    }\n                ];\n                // Apply decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, decorationOptions);\n                console.log('✅ Manual test: Decorations applied:', newDecorations);\n                setTeacherSelectionDecorations(newDecorations);\n                // Debug: Check DOM after decoration\n                setTimeout({\n                    \"CodeEditor.useCallback[testManualHighlight]\": ()=>{\n                        const decorationElements = document.querySelectorAll('.teacher-text-highlight, .teacher-highlight');\n                        console.log('🔍 Manual test: Found decoration elements:', decorationElements.length);\n                        // Force add a visible element for testing with yellow/orange theme\n                        const testDiv = document.createElement('div');\n                        testDiv.style.cssText = \"\\n          position: fixed;\\n          top: 10px;\\n          right: 10px;\\n          background: rgba(255, 152, 0, 0.9);\\n          color: white;\\n          padding: 12px;\\n          border-radius: 6px;\\n          border: 2px solid rgba(255, 235, 59, 0.8);\\n          box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);\\n          z-index: 9999;\\n          font-family: monospace;\\n          font-weight: bold;\\n          animation: pulse 2s ease-in-out infinite;\\n        \";\n                        testDiv.innerHTML = \"\\n          \\uD83C\\uDFAF Manual Test Results<br>\\n          \\uD83D\\uDCCA \".concat(newDecorations.length, \" decorations applied<br>\\n          \\uD83C\\uDFA8 Yellow/Orange theme active\\n        \");\n                        document.body.appendChild(testDiv);\n                        setTimeout({\n                            \"CodeEditor.useCallback[testManualHighlight]\": ()=>{\n                                if (document.body.contains(testDiv)) {\n                                    document.body.removeChild(testDiv);\n                                }\n                            }\n                        }[\"CodeEditor.useCallback[testManualHighlight]\"], 4000);\n                    }\n                }[\"CodeEditor.useCallback[testManualHighlight]\"], 100);\n            } catch (error) {\n                console.error('❌ Manual test error:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[testManualHighlight]\"], [\n        teacherSelectionDecorations\n    ]);\n    // Add manual test to window for debugging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            if (true) {\n                window.testManualHighlight = testManualHighlight;\n                console.log('🔧 Manual test function added to window.testManualHighlight()');\n            }\n        }\n    }[\"CodeEditor.useEffect\"], [\n        testManualHighlight\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-[80vh] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-800 shadow-xl flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"bg-gray-100 dark:bg-zinc-800 px-4 py-2 flex flex-wrap items-center justify-between border-b border-gray-200 dark:border-gray-700 gap-2 min-h-[48px]\",\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    style: {\n                        zIndex: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-3 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 min-w-[90px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1733,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-400 truncate\",\n                                            children: isConnected ? 'Connected' : 'Disconnected'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1734,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded ml-1 transition-colors\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1738,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1732,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: language,\n                                    onChange: (e)=>changeLanguage(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"javascript\",\n                                            children: \"JavaScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1753,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"typescript\",\n                                            children: \"TypeScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1754,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"python3\",\n                                            children: \"Python\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1755,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"java\",\n                                            children: \"Java\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1756,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"csharp\",\n                                            children: \"C#\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1757,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"c\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1758,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"cpp\",\n                                            children: \"C++\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1759,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"go\",\n                                            children: \"Go\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1760,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ruby\",\n                                            children: \"Ruby\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1761,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rust\",\n                                            children: \"Rust\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1762,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"php\",\n                                            children: \"PHP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1763,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1748,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: formatCurrentCode,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm bg-blue-600 hover:bg-blue-700 text-white transition-colors whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiAlignLeft, {\n                                            size: 14\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1773,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Format\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1774,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1767,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: executeCode,\n                                    disabled: isExecuting,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm \".concat(isExecuting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700', \" text-white transition-colors whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: isExecuting ? 1 : 1.05\n                                    },\n                                    whileTap: {\n                                        scale: isExecuting ? 1 : 0.95\n                                    },\n                                    children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                                className: \"animate-spin\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1787,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Running...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1788,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiPlay, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1792,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Run Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1793,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1778,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Room:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1800,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-white dark:bg-zinc-700 px-2 py-1 rounded font-mono text-xs truncate max-w-[120px]\",\n                                            children: roomId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1801,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                            onClick: copyCodeToClipboard,\n                                            className: \"text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            title: \"Copy code to clipboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCopy, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1809,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1802,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1799,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1730,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-2 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowOutput((prev)=>!prev),\n                                    className: \"flex items-center space-x-1 text-sm \".concat(showOutput ? 'text-blue-500 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300', \" hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCode, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1822,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Output\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1823,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1816,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: toggleUserList,\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUsers, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1833,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                activeUsers.length,\n                                                \" online\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1834,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1827,\n                                    columnNumber: 13\n                                }, this),\n                                userRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    className: \"flex items-center space-x-1 text-xs font-medium px-2 py-1 rounded-full whitespace-nowrap \".concat(userRole === 'teacher' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'),\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: userRole === 'teacher' ? '👨‍🏫' : '👨‍🎓'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1849,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: userRole === 'teacher' ? 'Teacher' : 'Student'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1850,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1839,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"flex items-center space-x-1 text-sm text-yellow-600 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSun, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1862,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiMoon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1862,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1855,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setMinimapEnabled((v)=>!v),\n                                    className: \"flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Minimap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Minimap\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1873,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1866,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleFindReplace,\n                                    className: \"flex items-center space-x-1 text-sm text-green-600 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Find/Replace\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSearch, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1884,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1877,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleDownload,\n                                    className: \"flex items-center space-x-1 text-sm text-indigo-600 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Download Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiDownload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1895,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1888,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"flex items-center space-x-1 text-sm text-pink-600 dark:text-pink-300 bg-pink-100 dark:bg-pink-900/30 hover:bg-pink-200 dark:hover:bg-pink-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Upload Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUpload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1906,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1899,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \".js,.ts,.py,.java,.c,.cpp,.go,.rb,.rs,.php,.txt\",\n                                    style: {\n                                        display: \"none\"\n                                    },\n                                    onChange: handleUpload\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1908,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    onChange: (e)=>e.target.value && insertSnippet(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[100px]\",\n                                    defaultValue: \"\",\n                                    title: \"Insert Snippet\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Snippets\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1923,\n                                            columnNumber: 15\n                                        }, this),\n                                        (SNIPPETS[language] || SNIPPETS['javascript']).map((snippet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: snippet.value,\n                                                children: snippet.label\n                                            }, snippet.label, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1925,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1917,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowShortcuts(true),\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Keyboard Shortcuts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiHelpCircle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1937,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1930,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleLeaveRoom,\n                                    className: \"flex items-center space-x-1 text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors px-2 py-1 rounded-md ml-2 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Leave Room\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1947,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1941,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1814,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1723,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showUserList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute right-0 top-12 bg-white dark:bg-zinc-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 w-48 overflow-hidden\",\n                        initial: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            height: 'auto'\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 border-b border-gray-200 dark:border-gray-700 font-medium text-sm text-gray-600 dark:text-gray-300\",\n                                children: \"Active Users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1962,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"max-h-48 overflow-y-auto\",\n                                children: activeUsers.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.li, {\n                                        className: \"px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-zinc-700 flex items-center space-x-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full bg-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1974,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: user\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1975,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1967,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1965,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1955,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1953,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"h-[calc(100%-40px)] \".concat(showOutput ? 'pb-[33%]' : ''),\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.4,\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    height: \"100%\",\n                                    defaultLanguage: language,\n                                    defaultValue: code,\n                                    onChange: handleEditorChange,\n                                    onMount: handleEditorDidMount,\n                                    theme: theme === \"dark\" ? \"vs-dark\" : \"light\",\n                                    options: {\n                                        minimap: {\n                                            enabled: minimapEnabled\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1993,\n                                    columnNumber: 15\n                                }, this),\n                                teacherCursorPosition && editorRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TeacherCursor, {\n                                    editor: editorRef.current,\n                                    position: teacherCursorPosition\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2005,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1992,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1990,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1984,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: typingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 left-4 text-sm text-gray-400 bg-gray-800/80 px-3 py-1 rounded-full backdrop-blur-sm\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            typingUser,\n                            \" is typing...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 2017,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 2015,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gray-900 text-white border-t border-gray-700 h-1/3 overflow-hidden flex flex-col\",\n                        initial: {\n                            height: 0\n                        },\n                        animate: {\n                            height: '33%'\n                        },\n                        exit: {\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 bg-gray-800 border-b border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 2040,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearOutput,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 2042,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: testPistonAPI,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-blue-400 hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Test API\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 2049,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowOutput(false),\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 2056,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 2041,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 2039,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-auto font-mono text-sm whitespace-pre-wrap\",\n                                children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2067,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Executing code...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2068,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2066,\n                                    columnNumber: 19\n                                }, this) : executionError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400\",\n                                    children: executionError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2071,\n                                    columnNumber: 19\n                                }, this) : executionOutput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: executionOutput\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2073,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: 'Click \"Run Code\" to execute your code.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2075,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 2064,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 2032,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 2030,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: copySuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 right-4 text-sm text-white bg-green-500 px-3 py-1 rounded-full shadow-lg\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: \"Copied to clipboard!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 2085,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 2083,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showShortcuts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 max-w-md w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-700 dark:hover:text-white\",\n                                    onClick: ()=>setShowShortcuts(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2107,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    children: \"Keyboard Shortcuts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2113,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Run Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2115,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+Enter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2115,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Format Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2116,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Shift+Alt+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2116,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Find/Replace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2117,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2117,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Download Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2118,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+D\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2118,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Upload Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2119,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+U\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2119,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Minimap:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2120,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+M\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2120,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Theme:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2121,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+T\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2121,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Show Shortcuts:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 2122,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+H\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 2122,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 2114,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 2106,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 2100,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 2098,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 1721,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 1719,\n        columnNumber: 5\n    }, this);\n}\n_s(CodeEditor, \"sxNeq0t2LGrybDSMrUfzOiW785Q=\", false, function() {\n    return [\n        _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme\n    ];\n});\n_c = CodeEditor;\nconst TeacherCursor = (param)=>{\n    let { editor, position } = param;\n    _s1();\n    const [cursorStyle, setCursorStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherCursor.useEffect\": ()=>{\n            const updateCursorPosition = {\n                \"TeacherCursor.useEffect.updateCursorPosition\": ()=>{\n                    try {\n                        // Get the pixel position of the cursor\n                        const pixelPosition = editor.getScrolledVisiblePosition({\n                            lineNumber: position.lineNumber,\n                            column: position.column\n                        });\n                        if (pixelPosition) {\n                            // Get the editor container position\n                            const editorContainer = editor.getDomNode();\n                            if (editorContainer) {\n                                const containerRect = editorContainer.getBoundingClientRect();\n                                setCursorStyle({\n                                    position: 'absolute',\n                                    left: \"\".concat(pixelPosition.left, \"px\"),\n                                    top: \"\".concat(pixelPosition.top, \"px\"),\n                                    zIndex: 1000,\n                                    pointerEvents: 'none'\n                                });\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error updating teacher cursor position:', error);\n                    }\n                }\n            }[\"TeacherCursor.useEffect.updateCursorPosition\"];\n            // Update position immediately\n            updateCursorPosition();\n            // Update position when editor scrolls or layout changes\n            const scrollDisposable = editor.onDidScrollChange(updateCursorPosition);\n            const layoutDisposable = editor.onDidLayoutChange(updateCursorPosition);\n            return ({\n                \"TeacherCursor.useEffect\": ()=>{\n                    scrollDisposable.dispose();\n                    layoutDisposable.dispose();\n                }\n            })[\"TeacherCursor.useEffect\"];\n        }\n    }[\"TeacherCursor.useEffect\"], [\n        editor,\n        position\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: cursorStyle,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"teacher-cursor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"teacher-cursor-label\",\n                children: position.teacherName\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 2187,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 2186,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 2185,\n        columnNumber: 5\n    }, undefined);\n}; // Keyboard shortcuts global handler\n // useEffect(() => {\n //   const handler = (e: KeyboardEvent) => {\n //     if (e.altKey && e.key.toLowerCase() === \"d\") {\n //       e.preventDefault();\n //       handleDownload();\n //     } else if (e.altKey && e.key.toLowerCase() === \"u\") {\n //       e.preventDefault();\n //       fileInputRef.current?.click();\n //     } else if (e.altKey && e.key.toLowerCase() === \"m\") {\n //       e.preventDefault();\n //       setMinimapEnabled((v: boolean) => !v);\n //     } else if (e.altKey && e.key.toLowerCase() === \"t\") {\n //       e.preventDefault();\n //       setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n //     } else if (e.altKey && e.key.toLowerCase() === \"h\") {\n //       e.preventDefault();\n //       setShowShortcuts(true);\n //     }\n //   };\n //   window.addEventListener(\"keydown\", handler);\n //   return () => window.removeEventListener(\"keydown\", handler);\n // }, [theme, setTheme]);\n_s1(TeacherCursor, \"NrZZSKlPdtAtpq9NKiew7IOqw6k=\");\n_c1 = TeacherCursor;\nvar _c, _c1;\n$RefreshReg$(_c, \"CodeEditor\");\n$RefreshReg$(_c1, \"TeacherCursor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CodeEditor.tsx\n"));

/***/ })

});