import "../chunk-XDFCUUT6.js";

// locales/ar-SA.json
var labels = {
  paste: "\u0644\u0635\u0642",
  pasteAsPlaintext: "\u0627\u0644\u0644\u0635\u0642 \u0643\u0646\u0635 \u0639\u0627\u062F\u064A",
  pasteCharts: "\u0644\u0635\u0642 \u0627\u0644\u0631\u0633\u0648\u0645 \u0627\u0644\u0628\u064A\u0627\u0646\u064A\u0629",
  selectAll: "\u062A\u062D\u062F\u064A\u062F \u0627\u0644\u0643\u0644",
  multiSelect: "\u0625\u0636\u0627\u0641\u0629 \u0639\u0646\u0635\u0631 \u0644\u0644\u062A\u062D\u062F\u064A\u062F",
  moveCanvas: "\u0646\u0642\u0644 \u0644\u0648\u062D \u0627\u0644\u0631\u0633\u0645",
  cut: "\u0642\u0635",
  copy: "\u0646\u0633\u062E",
  copyAsPng: "\u0646\u0633\u062E \u0625\u0644\u0649 \u0627\u0644\u062D\u0627\u0641\u0638\u0629 \u0628\u0635\u064A\u063A\u0629 PNG",
  copyAsSvg: "\u0646\u0633\u062E \u0625\u0644\u0649 \u0627\u0644\u062D\u0627\u0641\u0638\u0629 \u0628\u0635\u064A\u063A\u0629 SVG",
  copyText: "\u0646\u0633\u062E \u0625\u0644\u0649 \u0627\u0644\u062D\u0627\u0641\u0638\u0629 \u0643\u0646\u0635",
  copySource: "",
  convertToCode: "",
  bringForward: "\u062C\u0644\u0628 \u0644\u0644\u0623\u0645\u0627\u0645",
  sendToBack: "\u0623\u0631\u0633\u0644 \u0644\u0644\u062E\u0644\u0641",
  bringToFront: "\u0623\u062D\u0636\u0631 \u0644\u0644\u0623\u0645\u0627\u0645",
  sendBackward: "\u0623\u0631\u0633\u0644 \u0644\u0644\u062E\u0644\u0641",
  delete: "\u062D\u0630\u0641",
  copyStyles: "\u0646\u0633\u062E \u0627\u0644\u0623\u0646\u0645\u0627\u0637",
  pasteStyles: "\u0644\u0635\u0642 \u0627\u0644\u0623\u0646\u0645\u0627\u0637",
  stroke: "\u0627\u0644\u062E\u0637",
  background: "\u0627\u0644\u062E\u0644\u0641\u064A\u0629",
  fill: "\u0627\u0644\u062A\u0639\u0628\u0626\u0629",
  strokeWidth: "\u0633\u064F\u0645\u0643 \u0627\u0644\u062E\u0637",
  strokeStyle: "\u0646\u0645\u0637 \u0627\u0644\u062E\u0637",
  strokeStyle_solid: "\u0645\u062A\u0635\u0644",
  strokeStyle_dashed: "\u0645\u062A\u0642\u0637\u0639",
  strokeStyle_dotted: "\u0645\u0646\u0642\u0637",
  sloppiness: "\u0627\u0644\u0625\u0645\u0627\u0644\u0629",
  opacity: "\u0627\u0644\u0634\u0641\u0627\u0641\u064A\u0629",
  textAlign: "\u0645\u062D\u0627\u0630\u0627\u0629 \u0627\u0644\u0646\u0635",
  edges: "\u0627\u0644\u062D\u0648\u0627\u0641",
  sharp: "\u062D\u0627\u062F\u0629",
  round: "\u062F\u0627\u0626\u0631\u064A\u0629",
  arrowheads: "\u0631\u0624\u0648\u0633 \u0627\u0644\u0623\u0633\u0647\u0645",
  arrowhead_none: "\u0644\u0627 \u0634\u064A\u0621",
  arrowhead_arrow: "\u0633\u0647\u0645",
  arrowhead_bar: "\u0634\u0631\u064A\u0637",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "\u0645\u062B\u0644\u062B",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "\u062D\u062C\u0645 \u0627\u0644\u062E\u0637",
  fontFamily: "\u0646\u0648\u0639 \u0627\u0644\u062E\u0637",
  addWatermark: '\u0625\u0636\u0627\u0641\u0629 "\u0645\u0635\u0646\u0648\u0639\u0629 \u0628\u0648\u0627\u0633\u0637\u0629 Excalidraw"',
  handDrawn: "\u0631\u0633\u0645 \u0628\u0627\u0644\u064A\u062F",
  normal: "\u0639\u0627\u062F\u064A",
  code: "\u0631\u0645\u0632",
  small: "\u0635\u063A\u064A\u0631",
  medium: "\u0645\u062A\u0648\u0633\u0637",
  large: "\u0643\u0628\u064A\u0631",
  veryLarge: "\u0643\u0628\u064A\u0631 \u062C\u062F\u0627",
  solid: "\u0643\u0627\u0645\u0644",
  hachure: "\u062E\u0637\u0648\u0637",
  zigzag: "\u0645\u062A\u0639\u0631\u062C",
  crossHatch: "\u062E\u0637\u0648\u0637 \u0645\u062A\u0642\u0637\u0639\u0629",
  thin: "\u0646\u062D\u064A\u0641",
  bold: "\u062F\u0627\u0643\u0646",
  left: "\u0627\u0644\u0640\u064A\u0633\u0627\u0631",
  center: "\u0648\u0633\u0637",
  right: "\u064A\u0645\u064A\u0646",
  extraBold: "\u0639\u0631\u064A\u0636",
  architect: "\u0645\u0639\u0645\u0627\u0631\u064A",
  artist: "\u0631\u0633\u0627\u0645",
  cartoonist: "\u0643\u0631\u062A\u0648\u0646\u064A",
  fileTitle: "\u0625\u0633\u0645 \u0627\u0644\u0645\u0644\u0641",
  colorPicker: "\u0645\u0646\u062A\u0642\u064A \u0627\u0644\u0644\u0648\u0646",
  canvasColors: "\u062A\u0633\u062A\u062E\u062F\u0645 \u0639\u0644\u0649 \u0627\u0644\u0642\u0645\u0627\u0634",
  canvasBackground: "\u062E\u0644\u0641\u064A\u0629 \u0627\u0644\u0644\u0648\u062D\u0629",
  drawingCanvas: "\u0644\u0648\u062D\u0629 \u0627\u0644\u0631\u0633\u0645",
  layers: "\u0627\u0644\u0637\u0628\u0642\u0627\u062A",
  actions: "\u0627\u0644\u0625\u062C\u0631\u0627\u0621\u0627\u062A",
  language: "\u0627\u0644\u0644\u063A\u0629",
  liveCollaboration: "\u0627\u0644\u062A\u0639\u0627\u0648\u0646 \u0627\u0644\u0645\u0628\u0627\u0634\u0631...",
  duplicateSelection: "\u062A\u0643\u0631\u0627\u0631",
  untitled: "\u063A\u064A\u0631 \u0645\u0639\u0646\u0648\u0646",
  name: "\u0627\u0644\u0627\u0633\u0645",
  yourName: "\u0627\u0633\u0645\u0643",
  madeWithExcalidraw: "\u0645\u0635\u0646\u0648\u0639\u0629 \u0628\u0648\u0627\u0633\u0637\u0629 Excalidraw",
  group: "\u062A\u062D\u062F\u064A\u062F \u0645\u062C\u0645\u0648\u0639\u0629",
  ungroup: "\u0625\u0644\u063A\u0627\u0621 \u062A\u062D\u062F\u064A\u062F \u0645\u062C\u0645\u0648\u0639\u0629",
  collaborators: "\u0627\u0644\u0645\u062A\u0639\u0627\u0648\u0646\u0648\u0646",
  showGrid: "\u0625\u0638\u0647\u0627\u0631 \u0627\u0644\u0634\u0628\u0643\u0629",
  addToLibrary: "\u0623\u0636\u0641 \u0625\u0644\u0649 \u0627\u0644\u0645\u0643\u062A\u0628\u0629",
  removeFromLibrary: "\u062D\u0630\u0641 \u0645\u0646 \u0627\u0644\u0645\u0643\u062A\u0628\u0629",
  libraryLoadingMessage: "\u062C\u0627\u0631\u064D \u062A\u062D\u0645\u064A\u0644 \u0627\u0644\u0645\u0643\u062A\u0628\u0629\u2026",
  libraries: "\u062A\u0635\u0641\u062D \u0627\u0644\u0645\u0643\u062A\u0628\u0627\u062A",
  loadingScene: "\u062C\u0627\u0631\u064A \u062A\u062D\u0645\u064A\u0644 \u0627\u0644\u0645\u0634\u0647\u062F\u2026",
  align: "\u0645\u062D\u0627\u0630\u0627\u0629",
  alignTop: "\u0645\u062D\u0627\u0630\u0627\u0629 \u0625\u0644\u0649 \u0627\uFEF7\u0639\u0644\u0649",
  alignBottom: "\u0645\u062D\u0627\u0630\u0627\u0629 \u0625\u0644\u0649 \u0627\uFEF7\u0633\u0641\u0644",
  alignLeft: "\u0645\u062D\u0627\u0630\u0627\u0629 \u0625\u0644\u0649 \u0627\u0644\u064A\u0633\u0627\u0631",
  alignRight: "\u0645\u062D\u0627\u0630\u0627\u0629 \u0625\u0644\u0649 \u0627\u0644\u064A\u0645\u064A\u0646",
  centerVertically: "\u062A\u0648\u0633\u064A\u0637 \u0639\u0645\u0648\u062F\u064A",
  centerHorizontally: "\u062A\u0648\u0633\u064A\u0637 \u0623\u0641\u0642\u064A",
  distributeHorizontally: "\u0627\u0644\u062A\u0648\u0632\u064A\u0639 \u0627\u0644\u0623\u0641\u0642\u064A",
  distributeVertically: "\u0627\u0644\u062A\u0648\u0632\u064A\u0639 \u0639\u0645\u0648\u062F\u064A\u0627\u064B",
  flipHorizontal: "\u0642\u0644\u0628 \u0639\u0627\u0645\u0648\u062F\u064A",
  flipVertical: "\u0642\u0644\u0628 \u0623\u0641\u0642\u064A",
  viewMode: "\u0646\u0645\u0637 \u0627\u0644\u0639\u0631\u0636",
  share: "\u0645\u0634\u0627\u0631\u0643\u0629",
  showStroke: "\u0625\u0638\u0647\u0627\u0631 \u0645\u0646\u062A\u0642\u064A \u0644\u0648\u0646 \u0627\u0644\u062E\u0637",
  showBackground: "\u0625\u0638\u0647\u0627\u0631 \u0645\u0646\u062A\u0642\u064A \u0644\u0648\u0646 \u0627\u0644\u062E\u0644\u0641\u064A\u0629",
  toggleTheme: "\u063A\u064A\u0631 \u0627\u0644\u0646\u0645\u0637",
  personalLib: "\u0627\u0644\u0645\u0643\u062A\u0628\u0629 \u0627\u0644\u0634\u062E\u0635\u064A\u0629",
  excalidrawLib: "\u0645\u0643\u062A\u0628\u062A\u0646\u0627",
  decreaseFontSize: "\u062A\u0635\u063A\u064A\u0631 \u062D\u062C\u0645 \u0627\u0644\u062E\u0637",
  increaseFontSize: "\u062A\u0643\u0628\u064A\u0631 \u062D\u062C\u0645 \u0627\u0644\u062E\u0637",
  unbindText: "\u0641\u0643 \u0631\u0628\u0637 \u0627\u0644\u0646\u0635",
  bindText: "\u0631\u0628\u0637 \u0627\u0644\u0646\u0635 \u0628\u0627\u0644\u062D\u0627\u0648\u064A\u0629",
  createContainerFromText: "\u0646\u0635 \u0645\u063A\u0644\u0641 \u0641\u064A \u062D\u0627\u0648\u064A\u0629",
  link: {
    edit: "\u062A\u0639\u062F\u064A\u0644 \u0627\u0644\u0631\u0627\u0628\u0637",
    editEmbed: "\u062A\u062D\u0631\u064A\u0631 \u0627\u0644\u0631\u0627\u0628\u0637 \u0648\u0625\u062F\u0631\u0627\u062C\u0647",
    create: "\u0625\u0646\u0634\u0627\u0621 \u0631\u0627\u0628\u0637",
    createEmbed: "\u0625\u0646\u0634\u0627\u0621 \u0631\u0627\u0628\u0637 \u0648 \u0625\u062F\u0631\u0627\u062C\u0647",
    label: "\u0631\u0627\u0628\u0637",
    labelEmbed: "\u0631\u0627\u0628\u0637 \u0648 \u0625\u062F\u0631\u0627\u062C",
    empty: "\u0644\u0645 \u064A\u062A\u0645 \u062A\u0639\u064A\u064A\u0646 \u0631\u0627\u0628\u0637"
  },
  lineEditor: {
    edit: "\u062A\u062D\u0631\u064A\u0631 \u0627\u0644\u0633\u0637\u0631",
    exit: "\u0627\u0644\u062E\u0631\u0648\u062C \u0645\u0646 \u0627\u0644\u0645\u064F\u062D\u0631\u0631"
  },
  elementLock: {
    lock: "\u0642\u0641\u0644",
    unlock: "\u0641\u062A\u062D",
    lockAll: "\u0642\u0641\u0644 \u0627\u0644\u0643\u0644",
    unlockAll: "\u0641\u062A\u062D \u0627\u0644\u0643\u0644"
  },
  statusPublished: "\u0646\u064F\u0634\u0631",
  sidebarLock: "\u0625\u0628\u0642\u0627\u0621 \u0627\u0644\u0634\u0631\u064A\u0637 \u0627\u0644\u062C\u0627\u0646\u0628\u064A \u0645\u0641\u062A\u0648\u062D",
  selectAllElementsInFrame: "\u062A\u062D\u062F\u064A\u062F \u062C\u0645\u064A\u0639 \u0627\u0644\u0639\u0646\u0627\u0635\u0631 \u0641\u064A \u0627\u0644\u0625\u0637\u0627\u0631",
  removeAllElementsFromFrame: "\u0625\u0632\u0627\u0644\u0629 \u062C\u0645\u064A\u0639 \u0627\u0644\u0639\u0646\u0627\u0635\u0631 \u0645\u0646 \u0627\u0644\u0625\u0637\u0627\u0631",
  eyeDropper: "\u0627\u062E\u062A\u064A\u0627\u0631 \u0627\u0644\u0644\u0648\u0646 \u0645\u0646 \u0627\u0644\u0642\u0645\u0627\u0634",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "\u0644\u0627 \u062A\u0648\u062C\u062F \u0639\u0646\u0627\u0635\u0631 \u0623\u0636\u064A\u0641\u062A \u0628\u0639\u062F...",
  hint_emptyLibrary: "\u062D\u062F\u062F \u0639\u0646\u0635\u0631 \u0639\u0644\u0649 \u0627\u0644\u0642\u0645\u0627\u0634 \u0644\u0625\u0636\u0627\u0641\u062A\u0647 \u0647\u0646\u0627\u060C \u0623\u0648 \u062A\u062B\u0628\u064A\u062A \u0645\u0643\u062A\u0628\u0629 \u0645\u0646 \u0627\u0644\u0645\u0633\u062A\u0648\u062F\u0639 \u0627\u0644\u0639\u0627\u0645 \u0623\u062F\u0646\u0627\u0647.",
  hint_emptyPrivateLibrary: "\u062D\u062F\u062F \u0639\u0646\u0635\u0631 \u0639\u0644\u0649 \u0627\u0644\u0642\u0645\u0627\u0634 \u0644\u0625\u0636\u0627\u0641\u062A\u0647 \u0647\u0646\u0627."
};
var buttons = {
  clearReset: "\u0625\u0639\u0627\u062F\u0629 \u062A\u0639\u064A\u064A\u0646 \u0627\u0644\u0644\u0648\u062D\u0629",
  exportJSON: "\u0635\u062F\u0631 \u0627\u0644\u0645\u0644\u0641",
  exportImage: "\u062A\u0635\u062F\u064A\u0631 \u0627\u0644\u0635\u0648\u0631\u0629...",
  export: "\u062D\u0641\u0638 \u0625\u0644\u0649...",
  copyToClipboard: "\u0646\u0633\u062E \u0625\u0644\u0649 \u0627\u0644\u062D\u0627\u0641\u0638\u0629",
  save: "\u0627\u062D\u0641\u0638 \u0644\u0644\u0645\u0644\u0641 \u0627\u0644\u062D\u0627\u0644\u064A",
  saveAs: "\u062D\u0641\u0638 \u0643\u0640",
  load: "\u0641\u062A\u062D",
  getShareableLink: "\u0627\u062D\u0635\u0644 \u0639\u0644\u0649 \u0631\u0627\u0628\u0637 \u0627\u0644\u0645\u0634\u0627\u0631\u0643\u0629",
  close: "\u063A\u0644\u0642",
  selectLanguage: "\u0627\u062E\u062A\u0631 \u0627\u0644\u0644\u063A\u0629",
  scrollBackToContent: "\u0627\u0644\u0631\u062C\u0648\u0639 \u0625\u0644\u0649 \u0627\u0644\u0645\u062D\u062A\u0648\u0649",
  zoomIn: "\u062A\u0643\u0628\u064A\u0631",
  zoomOut: "\u062A\u0635\u063A\u064A\u0631",
  resetZoom: "\u0625\u0639\u0627\u062F\u0629 \u062A\u0639\u064A\u064A\u0646 \u0627\u0644\u0634\u0627\u0634\u0629",
  menu: "\u0627\u0644\u0642\u0627\u0626\u0645\u0629",
  done: "\u062A\u0645",
  edit: "\u062A\u0639\u062F\u064A\u0644",
  undo: "\u062A\u0631\u0627\u062C\u0639",
  redo: "\u0625\u0639\u0627\u062F\u0629 \u062A\u0646\u0641\u064A\u0630",
  resetLibrary: "\u0625\u0639\u0627\u062F\u0629 \u0636\u0628\u0637 \u0627\u0644\u0645\u0643\u062A\u0628\u0629",
  createNewRoom: "\u0625\u0646\u0634\u0627\u0621 \u063A\u0631\u0641\u0629 \u062C\u062F\u064A\u062F\u0629",
  fullScreen: "\u0634\u0627\u0634\u0629 \u0643\u0627\u0645\u0644\u0629",
  darkMode: "\u0627\u0644\u0648\u0636\u0639 \u0627\u0644\u0645\u0638\u0644\u0645",
  lightMode: "\u0627\u0644\u0648\u0636\u0639 \u0627\u0644\u0645\u0636\u064A\u0621",
  zenMode: "\u0648\u0636\u0639 \u0627\u0644\u062A\u0623\u0645\u0644",
  objectsSnapMode: "\u0627\u0644\u062A\u0642\u0637 \u0625\u0644\u0649 \u0627\u0644\u0639\u0646\u0627\u0635\u0631",
  exitZenMode: "\u0625\u0644\u063A\u0627\u0621 \u0627\u0644\u0648\u0636\u0639 \u0627\u0644\u0644\u064A\u0644\u0649",
  cancel: "\u0625\u0644\u063A\u0627\u0621",
  clear: "\u0645\u0633\u062D",
  remove: "\u0625\u0632\u0627\u0644\u0629",
  embed: "\u062A\u0628\u062F\u064A\u0644 \u0627\u0644\u0625\u062F\u0631\u0627\u062C",
  publishLibrary: "\u0627\u0646\u0634\u0631",
  submit: "\u0623\u0631\u0633\u0644",
  confirm: "\u062A\u0623\u0643\u064A\u062F",
  embeddableInteractionButton: "\u0627\u0636\u063A\u0637 \u0644\u0644\u062A\u0641\u0627\u0639\u0644"
};
var alerts = {
  clearReset: "\u0647\u0630\u0627 \u0633\u064A\u064F\u0632\u064A\u0644 \u0643\u0627\u0645\u0644 \u0627\u0644\u0644\u0648\u062D\u0629. \u0647\u0644 \u0623\u0646\u062A \u0645\u062A\u0623\u0643\u062F\u061F",
  couldNotCreateShareableLink: "\u062A\u0639\u0630\u0631 \u0625\u0646\u0634\u0627\u0621 \u0631\u0627\u0628\u0637\u0629 \u0627\u0644\u0645\u0634\u0627\u0631\u0643\u0629.",
  couldNotCreateShareableLinkTooBig: "\u062A\u0639\u0630\u0631 \u0625\u0646\u0634\u0627\u0621 \u0631\u0627\u0628\u0637 \u0642\u0627\u0628\u0644 \u0644\u0644\u0645\u0634\u0627\u0631\u0643\u0629: \u0627\u0644\u0645\u0634\u0647\u062F \u0643\u0628\u064A\u0631 \u062C\u062F\u064B\u0627",
  couldNotLoadInvalidFile: "\u062A\u0639\u0630\u0631 \u0627\u0644\u062A\u062D\u0645\u064A\u0644\u060C \u0627\u0644\u0645\u0644\u0641 \u063A\u064A\u0631 \u0635\u0627\u0644\u062D",
  importBackendFailed: "\u0641\u0634\u0644 \u0627\u0644\u0627\u0633\u062A\u064A\u0631\u0627\u062F \u0645\u0646 \u0627\u0644\u062E\u0627\u062F\u0648\u0645.",
  cannotExportEmptyCanvas: "\u0644\u0627 \u064A\u0645\u0643\u0646 \u062A\u0635\u062F\u064A\u0631 \u0644\u0648\u062D\u0629 \u0641\u0627\u0631\u063A\u0629.",
  couldNotCopyToClipboard: "\u062A\u0639\u0630\u0631 \u0627\u0644\u0646\u0633\u062E \u0625\u0644\u0649 \u0627\u0644\u062D\u0627\u0641\u0638\u0629.",
  decryptFailed: "\u062A\u0639\u0630\u0631 \u0641\u0643 \u062A\u0634\u0641\u064A\u0631 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A.",
  uploadedSecurly: "\u062A\u0645 \u062A\u0623\u0645\u064A\u0646 \u0627\u0644\u062A\u062D\u0645\u064A\u0644 \u0628\u062A\u0634\u0641\u064A\u0631 \u0627\u0644\u0646\u0647\u0627\u064A\u0629 \u0625\u0644\u0649 \u0627\u0644\u0646\u0647\u0627\u064A\u0629\u060C \u0645\u0645\u0627 \u064A\u0639\u0646\u064A \u0623\u0646 \u062E\u0627\u062F\u0648\u0645 Excalidraw \u0648\u0627\u0644\u0623\u0637\u0631\u0627\u0641 \u0627\u0644\u062B\u0627\u0644\u062B\u0629 \u0644\u0627 \u064A\u0645\u0643\u0646\u0647\u0627 \u0642\u0631\u0627\u0621\u0629 \u0627\u0644\u0645\u062D\u062A\u0648\u0649.",
  loadSceneOverridePrompt: "\u062A\u062D\u0645\u064A\u0644 \u0627\u0644\u0631\u0633\u0645 \u0627\u0644\u062E\u0627\u0631\u062C\u064A \u0633\u064A\u062D\u0644 \u0645\u062D\u0644 \u0627\u0644\u0645\u062D\u062A\u0648\u0649 \u0627\u0644\u0645\u0648\u062C\u0648\u062F \u0644\u062F\u064A\u0643. \u0647\u0644 \u062A\u0631\u063A\u0628 \u0641\u064A \u0627\u0644\u0645\u062A\u0627\u0628\u0639\u0629\u061F",
  collabStopOverridePrompt: "\u0625\u064A\u0642\u0627\u0641 \u0627\u0644\u062C\u0644\u0633\u0629 \u0633\u064A\u0624\u062F\u064A \u0625\u0644\u0649 \u0627\u0644\u0643\u062A\u0627\u0628\u0629 \u0641\u0648\u0642 \u0631\u0633\u0648\u0645\u0643 \u0627\u0644\u0633\u0627\u0628\u0642\u0629 \u0627\u0644\u0645\u062E\u0632\u0646\u0629 \u062F\u0627\u062E\u0644\u064A\u0627. \u0647\u0644 \u0623\u0646\u062A \u0645\u062A\u0623\u0643\u062F\u061F\n\n(\u0625\u0630\u0627 \u0643\u0646\u062A \u062A\u0631\u063A\u0628 \u0641\u064A \u0627\u0644\u0627\u062D\u062A\u0641\u0627\u0638 \u0628\u0631\u0633\u0645\u0643 \u0627\u0644\u0645\u062E\u0632\u0646 \u062F\u0627\u062E\u0644\u064A\u0627\u060C \u0628\u0628\u0633\u0627\u0637\u0629 \u0623\u063A\u0644\u0642 \u0639\u0644\u0627\u0645\u0629 \u062A\u0628\u0648\u064A\u0628 \u0627\u0644\u0645\u062A\u0635\u0641\u062D \u0628\u062F\u0644\u0627\u064B \u0645\u0646 \u0630\u0644\u0643.)",
  errorAddingToLibrary: "\u062A\u0639\u0630\u0631 \u0625\u0636\u0627\u0641\u0629 \u0627\u0644\u0639\u0646\u0635\u0631 \u0644\u0644\u0645\u0643\u062A\u0628\u0629",
  errorRemovingFromLibrary: "\u062A\u0639\u0630\u0631 \u0625\u0632\u0627\u0644\u0629 \u0627\u0644\u0639\u0646\u0635\u0631 \u0645\u0646 \u0627\u0644\u0645\u0643\u062A\u0628\u0629",
  confirmAddLibrary: "\u0647\u0630\u0627 \u0633\u064A\u0636\u064A\u0641 {{numShapes}} \u0634\u0643\u0644 \u0625\u0644\u0649 \u0645\u0643\u062A\u0628\u062A\u0643. \u0647\u0644 \u0623\u0646\u062A \u0645\u062A\u0623\u0643\u062F\u061F",
  imageDoesNotContainScene: "\u064A\u0628\u062F\u0648 \u0623\u0646 \u0647\u0630\u0647 \u0627\u0644\u0635\u0648\u0631\u0629 \u0644\u0627 \u062A\u062D\u062A\u0648\u064A \u0639\u0644\u0649 \u0623\u064A \u0628\u064A\u0627\u0646\u0627\u062A \u0645\u0634\u0647\u062F. \u0647\u0644 \u0642\u0645\u062A \u0628\u062A\u0645\u0643\u064A\u0646 \u062A\u0636\u0645\u064A\u0646 \u0627\u0644\u0645\u0634\u0647\u062F \u0623\u062B\u0646\u0627\u0621 \u0627\u0644\u062A\u0635\u062F\u064A\u0631\u061F",
  cannotRestoreFromImage: "\u062A\u0639\u0630\u0631 \u0627\u0633\u062A\u0639\u0627\u062F\u0629 \u0627\u0644\u0645\u0634\u0647\u062F \u0645\u0646 \u0645\u0644\u0641 \u0627\u0644\u0635\u0648\u0631\u0629",
  invalidSceneUrl: "\u062A\u0639\u0630\u0631 \u0627\u0633\u062A\u064A\u0631\u0627\u062F \u0627\u0644\u0645\u0634\u0647\u062F \u0645\u0646 \u0639\u0646\u0648\u0627\u0646 URL \u0627\u0644\u0645\u062A\u0648\u0641\u0631. \u0625\u0645\u0627 \u0623\u0646\u0647\u0627 \u0645\u0634\u0648\u0647\u0629\u060C \u0623\u0648 \u0644\u0627 \u062A\u062D\u062A\u0648\u064A \u0639\u0644\u0649 \u0628\u064A\u0627\u0646\u0627\u062A Excalidraw JSON \u0635\u0627\u0644\u062D\u0629.",
  resetLibrary: "\u0647\u0630\u0627 \u0633\u0648\u0641 \u064A\u0645\u0633\u062D \u0645\u0643\u062A\u0628\u062A\u0643. \u0647\u0644 \u0623\u0646\u062A \u0645\u062A\u0623\u0643\u062F\u061F",
  removeItemsFromsLibrary: "\u062D\u0630\u0641 {{count}} \u0639\u0646\u0635\u0631 (\u0639\u0646\u0627\u0635\u0631) \u0645\u0646 \u0627\u0644\u0645\u0643\u062A\u0628\u0629\u061F",
  invalidEncryptionKey: "\u0645\u0641\u062A\u0627\u062D \u0627\u0644\u062A\u0634\u0641\u064A\u0631 \u064A\u062C\u0628 \u0623\u0646 \u064A\u0643\u0648\u0646 \u0645\u0646 22 \u062D\u0631\u0641\u0627\u064B. \u0627\u0644\u062A\u0639\u0627\u0648\u0646 \u0627\u0644\u0645\u0628\u0627\u0634\u0631 \u0645\u0639\u0637\u0644.",
  collabOfflineWarning: "\u0644\u0627 \u064A\u0648\u062C\u062F \u0627\u062A\u0635\u0627\u0644 \u0628\u0627\u0644\u0627\u0646\u062A\u0631\u0646\u062A.\n\u0644\u0646 \u064A\u062A\u0645 \u062D\u0641\u0638 \u0627\u0644\u062A\u063A\u064A\u064A\u0631\u0627\u062A \u0627\u0644\u062A\u064A \u0642\u0645\u062A \u0628\u0647\u0627!"
};
var errors = {
  unsupportedFileType: "\u0646\u0648\u0639 \u0627\u0644\u0645\u0644\u0641 \u063A\u064A\u0631 \u0645\u062F\u0639\u0648\u0645.",
  imageInsertError: "\u062A\u0639\u0630\u0631 \u0625\u062F\u0631\u0627\u062C \u0627\u0644\u0635\u0648\u0631\u0629. \u062D\u0627\u0648\u0644 \u0645\u0631\u0629 \u0623\u062E\u0631\u0649 \u0644\u0627\u062D\u0642\u0627\u064B...",
  fileTooBig: "\u0627\u0644\u0645\u0644\u0641 \u0643\u0628\u064A\u0631 \u062C\u062F\u0627\u064B. \u0627\u0644\u062D\u062F \u0627\u0644\u0623\u0642\u0635\u0649 \u0627\u0644\u0645\u0633\u0645\u0648\u062D \u0628\u0647 \u0644\u0644\u062D\u062C\u0645 \u0647\u0648 {{maxSize}}.",
  svgImageInsertError: "\u062A\u0639\u0630\u0631 \u0625\u062F\u0631\u0627\u062C \u0635\u0648\u0631\u0629 SVG. \u064A\u0628\u062F\u0648 \u0623\u0646 \u062A\u0631\u0645\u064A\u0632 SVG \u063A\u064A\u0631 \u0635\u062D\u064A\u062D.",
  failedToFetchImage: "",
  invalidSVGString: "SVG \u063A\u064A\u0631 \u0635\u0627\u0644\u062D.",
  cannotResolveCollabServer: "\u062A\u0639\u0630\u0631 \u0627\u0644\u0627\u062A\u0635\u0627\u0644 \u0628\u062E\u0627\u062F\u0645 \u0627\u0644\u062A\u0639\u0627\u0648\u0646. \u0627\u0644\u0631\u062C\u0627\u0621 \u0625\u0639\u0627\u062F\u0629 \u062A\u062D\u0645\u064A\u0644 \u0627\u0644\u0635\u0641\u062D\u0629 \u0648\u0627\u0644\u0645\u062D\u0627\u0648\u0644\u0629 \u0645\u0631\u0629 \u0623\u062E\u0631\u0649.",
  importLibraryError: "\u062A\u0639\u0630\u0631 \u062A\u062D\u0645\u064A\u0644 \u0627\u0644\u0645\u0643\u062A\u0628\u0629",
  collabSaveFailed: "\u062A\u0639\u0630\u0631 \u0627\u0644\u062D\u0641\u0638 \u0641\u064A \u0642\u0627\u0639\u062F\u0629 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A. \u0625\u0630\u0627 \u0627\u0633\u062A\u0645\u0631\u062A \u0627\u0644\u0645\u0634\u0627\u0643\u0644\u060C \u064A\u0641\u0636\u0644 \u0623\u0646 \u062A\u062D\u0641\u0638 \u0645\u0644\u0641\u0643 \u0645\u062D\u0644\u064A\u0627 \u0643\u064A \u0644\u0627 \u062A\u0641\u0642\u062F \u0639\u0645\u0644\u0643.",
  collabSaveFailed_sizeExceeded: "\u062A\u0639\u0630\u0631 \u0627\u0644\u062D\u0641\u0638 \u0641\u064A \u0642\u0627\u0639\u062F\u0629 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A\u060C \u064A\u0628\u062F\u0648 \u0623\u0646 \u0627\u0644\u0642\u0645\u0627\u0634 \u0643\u0628\u064A\u0631 \u0644\u0644\u063A\u0627\u064A\u0629\u060C \u064A\u0641\u0636\u0651\u0644 \u062D\u0641\u0638 \u0627\u0644\u0645\u0644\u0641 \u0645\u062D\u0644\u064A\u0627 \u0643\u064A \u0644\u0627 \u062A\u0641\u0642\u062F \u0639\u0645\u0644\u0643.",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "\u064A\u0628\u062F\u0648 \u0623\u0646\u0643 \u062A\u0633\u062A\u062E\u062F\u0645 \u0645\u062A\u0635\u0641\u062D Brave \u0645\u0639 \u0625\u0639\u062F\u0627\u062F <bold>\u062D\u0638\u0631 \u0635\u0627\u0631\u0645 \u0644\u062A\u062A\u0628\u0639 \u0627\u0644\u0628\u0635\u0645\u0629</bold>.",
    line2: "\u0642\u062F \u064A\u0624\u062F\u064A \u0647\u0630\u0627 \u0625\u0644\u0649 \u0643\u0633\u0631 <bold>\u0639\u0646\u0627\u0635\u0631 \u0627\u0644\u0646\u0635</bold> \u0641\u064A \u0627\u0644\u0631\u0633\u0648\u0645\u0627\u062A \u0627\u0644\u062E\u0627\u0635\u0629 \u0628\u0643.",
    line3: "\u0645\u0646 \u0627\u0644\u0645\u0633\u062A\u062D\u0633\u0646 \u0625\u0644\u063A\u0627\u0621 \u062A\u0641\u0639\u064A\u0644 \u0647\u0630\u0627 \u0627\u0644\u0625\u0639\u062F\u0627\u062F. \u064A\u0645\u0643\u0646\u0643 \u0627\u062A\u0628\u0627\u0639 <link>\u0647\u0630\u0647 \u0627\u0644\u062E\u0637\u0648\u0627\u062A</link> \u0644\u0641\u0639\u0644 \u0630\u0644\u0643.",
    line4: "\u0625\u0630\u0627 \u0644\u0645 \u064A\u0635\u0644\u062D \u062A\u0639\u0637\u064A\u0644 \u0647\u0630\u0627 \u0627\u0644\u0625\u0639\u062F\u0627\u062F \u0637\u0631\u064A\u0642\u0629 \u0639\u0631\u0636 \u0627\u0644\u0646\u0635\u0648\u0635\u060C \u0627\u0644\u0631\u062C\u0627\u0621 \u0643\u062A\u0627\u0628\u0629 <issueLink>\u0628\u0644\u0627\u063A</issueLink> \u0639\u0644\u0649 \u062D\u0633\u0627\u0628\u0646\u0627 \u0641\u064A GitHub\u060C \u0623\u0648 \u0631\u0627\u0633\u0644\u0646\u0627 \u0639\u0644\u0649 <discordLink>Discord</discordLink>"
  },
  libraryElementTypeError: {
    embeddable: "\u0644\u0627 \u064A\u0645\u0643\u0646 \u0625\u0636\u0627\u0641\u0629 \u0627\u0644\u0639\u0646\u0627\u0635\u0631 \u0627\u0644\u0642\u0627\u0628\u0644\u0629 \u0644\u0644\u062A\u0636\u0645\u064A\u0646 \u0641\u064A \u0627\u0644\u0645\u0643\u062A\u0628\u0629.",
    iframe: "",
    image: "\u0633\u0648\u0641 \u064A\u062A\u0645 \u062F\u0639\u0645 \u0625\u0636\u0627\u0641\u0629 \u0635\u0648\u0631 \u0625\u0644\u0649 \u0627\u0644\u0645\u0643\u062A\u0628\u0629 \u0642\u0631\u064A\u0628\u0627\u064B!"
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "\u062A\u062D\u062F\u064A\u062F",
  image: "\u0625\u062F\u0631\u0627\u062C \u0635\u0648\u0631\u0629",
  rectangle: "\u0645\u0633\u062A\u0637\u064A\u0644",
  diamond: "\u0645\u0636\u0644\u0639",
  ellipse: "\u062F\u0627\u0626\u0631\u0629",
  arrow: "\u0633\u0647\u0645",
  line: "\u062E\u0637",
  freedraw: "\u0631\u0633\u0645",
  text: "\u0646\u0635",
  library: "\u0645\u0643\u062A\u0628\u0629",
  lock: "\u0627\u0644\u062D\u0641\u0627\u0638 \u0639\u0644\u0649 \u0623\u062F\u0627\u0629 \u0627\u0644\u062A\u062D\u062F\u064A\u062F \u0646\u0634\u0637\u0629 \u0628\u0639\u062F \u0627\u0644\u0631\u0633\u0645",
  penMode: "\u0648\u0636\u0639 \u0627\u0644\u0642\u0644\u0645 - \u0627\u0645\u0646\u0639 \u0627\u0644\u0644\u0645\u0633",
  link: "\u0625\u0636\u0627\u0641\u0629/\u062A\u062D\u062F\u064A\u062B \u0627\u0644\u0631\u0627\u0628\u0637 \u0644\u0644\u0634\u0643\u0644 \u0627\u0644\u0645\u062D\u062F\u062F",
  eraser: "\u0645\u0645\u062D\u0627\u0629",
  frame: "\u0623\u062F\u0627\u0629 \u0627\u0644\u0625\u0637\u0627\u0631",
  magicframe: "",
  embeddable: "\u062A\u0636\u0645\u064A\u0646 \u0648\u064A\u0628",
  laser: "\u0645\u0624\u0634\u0631 \u0644\u064A\u0632\u0631",
  hand: "\u064A\u062F (\u0623\u062F\u0627\u0629 \u0627\u0644\u0625\u0632\u0627\u062D\u0629)",
  extraTools: "\u0627\u0644\u0645\u0632\u064A\u062F \u0645\u0646 \u0623\uFEF7\u062F\u0648\u0627\u062A",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "\u0625\u062C\u0631\u0627\u0621\u0627\u062A \u0627\u0644\u0644\u0648\u062D\u0629",
  selectedShapeActions: "\u0625\u062C\u0631\u0627\u0621\u0627\u062A \u0627\u0644\u0634\u0643\u0644 \u0627\u0644\u0645\u062D\u062F\u062F",
  shapes: "\u0627\u0644\u0623\u0634\u0643\u0627\u0644"
};
var hints = {
  canvasPanning: "\u0644\u062A\u062D\u0631\u064A\u0643 \u0627\u0644\u0642\u0645\u0627\u0634\u060C \u0627\u0636\u063A\u0637 \u0639\u0644\u0649 \u0639\u062C\u0644\u0629 \u0627\u0644\u0641\u0623\u0631\u0629 \u0623\u0648 \u0645\u0641\u062A\u0627\u062D \u0627\u0644\u0645\u0633\u0627\u0641\u0629 \u0623\u062B\u0646\u0627\u0621 \u0627\u0644\u0633\u062D\u0628\u060C \u0623\u0648 \u0627\u0633\u062A\u062E\u062F\u0645 \u0623\u062F\u0627\u0629 \u0627\u0644\u064A\u062F",
  linearElement: "\u0627\u0646\u0642\u0631 \u0644\u0628\u062F\u0621 \u0646\u0642\u0627\u0637 \u0645\u062A\u0639\u062F\u062F\u0629\u060C \u0627\u0633\u062D\u0628 \u0644\u062E\u0637 \u0648\u0627\u062D\u062F",
  freeDraw: "\u0627\u0646\u0642\u0631 \u0648\u0627\u0633\u062D\u0628\u060C \u0627\u0641\u0631\u062C \u0639\u0646\u062F \u0627\u0644\u0627\u0646\u062A\u0647\u0627\u0621",
  text: "\u0646\u0635\u064A\u062D\u0629: \u064A\u0645\u0643\u0646\u0643 \u0623\u064A\u0636\u064B\u0627 \u0625\u0636\u0627\u0641\u0629 \u0646\u0635 \u0628\u0627\u0644\u0646\u0642\u0631 \u0627\u0644\u0645\u0632\u062F\u0648\u062C \u0641\u064A \u0623\u064A \u0645\u0643\u0627\u0646 \u0628\u0623\u062F\u0627\u0629 \u0627\u0644\u0627\u062E\u062A\u064A\u0627\u0631",
  embeddable: "\u0627\u0636\u063A\u0637 \u0645\u0639 \u0627\u0644\u0633\u062D\u0628 \u0644\u0625\u0646\u0634\u0627\u0621 \u0645\u0648\u0642\u0639 \u0648\u064A\u0628 \u0645\u0636\u0645\u0651\u0646",
  text_selected: "\u0627\u0646\u0642\u0631 \u0646\u0642\u0631\u0627\u064B \u0645\u0632\u062F\u0648\u062C\u0627\u064B \u0623\u0648 \u0627\u0636\u063A\u0637 \u0627\u062F\u062E\u0627\u0644 \u0644\u062A\u0639\u062F\u064A\u0644 \u0627\u0644\u0646\u0635",
  text_editing: "\u0627\u0636\u063A\u0637 \u0639\u0644\u0649 Esc \u0623\u0648 (Ctrl \u0623\u0648 Cmd) + Enter \u0644\u0625\u0646\u0647\u0627\u0621 \u0627\u0644\u062A\u0639\u062F\u064A\u0644",
  linearElementMulti: "\u0627\u0646\u0642\u0631 \u0641\u0648\u0642 \u0627\u0644\u0646\u0642\u0637\u0629 \u0627\u0644\u0623\u062E\u064A\u0631\u0629 \u0623\u0648 \u0627\u0636\u063A\u0637 \u0639\u0644\u0649 Esc \u0623\u0648 Enter \u0644\u0644\u0625\u0646\u0647\u0627\u0621",
  lockAngle: "\u064A\u0645\u0643\u0646\u0643 \u062A\u0642\u064A\u064A\u062F \u0627\u0644\u0632\u0627\u0648\u064A\u0629 \u0628\u0627\u0644\u0636\u063A\u0637 \u0639\u0644\u0649 SHIFT",
  resize: "\u064A\u0645\u0643\u0646\u0643 \u062A\u0642\u064A\u064A\u062F \u0627\u0644\u0646\u0633\u0628 \u0628\u0627\u0644\u0636\u063A\u0637 \u0639\u0644\u0649 SHIFT \u0623\u062B\u0646\u0627\u0621 \u062A\u063A\u064A\u064A\u0631 \u0627\u0644\u062D\u062C\u0645\u060C\n\u0627\u0636\u063A\u0637 \u0639\u0644\u0649 ALT \u0644\u062A\u063A\u064A\u064A\u0631 \u0627\u0644\u062D\u062C\u0645 \u0645\u0646 \u0627\u0644\u0645\u0631\u0643\u0632",
  resizeImage: "\u064A\u0645\u0643\u0646\u0643 \u062A\u063A\u064A\u064A\u0631 \u0627\u0644\u062D\u062C\u0645 \u0628\u062D\u0631\u064A\u0629 \u0628\u0627\u0644\u0636\u063A\u0637 \u0628\u0623\u0633\u062A\u0645\u0631\u0627\u0631 \u0639\u0644\u0649 SHIFT\u060C\n\u0627\u0636\u063A\u0637 \u0628\u0623\u0633\u062A\u0645\u0631\u0627\u0631 \u0639\u0644\u0649 ALT \u0623\u064A\u0636\u0627 \u0644\u062A\u063A\u064A\u064A\u0631 \u0627\u0644\u062D\u062C\u0645 \u0645\u0646 \u0627\u0644\u0645\u0631\u0643\u0632",
  rotate: "\u064A\u0645\u0643\u0646\u0643 \u062A\u0642\u064A\u064A\u062F \u0627\u0644\u0632\u0648\u0627\u064A\u0627 \u0645\u0646 \u062E\u0644\u0627\u0644 \u0627\u0644\u0636\u063A\u0637 \u0639\u0644\u0649 SHIFT \u0623\u062B\u0646\u0627\u0621 \u0627\u0644\u062F\u0648\u0631\u0627\u0646",
  lineEditor_info: "\u0627\u0636\u063A\u0637 \u0639\u0644\u0649 \u0645\u0641\u062A\u0627\u062D (Ctrl \u0623\u0648 Cmd) \u0648 \u0627\u0646\u0642\u0631 \u0628\u0634\u0643\u0644 \u0645\u0632\u062F\u0648\u062C\u060C \u0623\u0648 \u0627\u0636\u063A\u0637 \u0639\u0644\u0649 \u0645\u0641\u062A\u0627\u062D\u064A (Ctrl \u0623\u0648 Cmd) \u0648 (Enter) \u0644\u062A\u0639\u062F\u064A\u0644 \u0627\u0644\u0646\u0642\u0627\u0637",
  lineEditor_pointSelected: "\u0627\u0636\u063A\u0637 \u0639\u0644\u0649 \u062D\u0630\u0641 \u0644\u0625\u0632\u0627\u0644\u0629 \u0627\u0644\u0646\u0642\u0637\u0629 (\u0627\u0644\u0646\u0651\u0650\u0642\u064E\u0627\u0637)\u060C Ctrl/Cmd+D \u0644\u0644\u062A\u0643\u0631\u0627\u0631\u060C \u0623\u0648 \u0627\u0633\u062D\u0628 \u0644\u0644\u0627\u0646\u062A\u0642\u0627\u0644",
  lineEditor_nothingSelected: "\u0627\u062E\u062A\u0631 \u0646\u0642\u0637\u0629 \u0644\u062A\u0639\u062F\u064A\u0644\u0647\u0627 (\u0627\u0636\u063A\u0637 \u0639\u0644\u0649 SHIFT \u0644\u062A\u062D\u062F\u064A\u062F \u0639\u062F\u0629 \u0646\u0650\u0642\u064E\u0627\u0637),\n\u0623\u0648 \u0627\u0636\u063A\u0637 \u0639\u0644\u0649 ALT \u0648 \u0627\u0646\u0642\u0631 \u0628\u0627\u0644\u0641\u0623\u0631\u0629 \u0644\u0625\u0636\u0627\u0641\u0629 \u0646\u0650\u0642\u064E\u0627\u0637 \u062C\u062F\u064A\u062F\u0629",
  placeImage: "\u0627\u0646\u0642\u0631 \u0644\u0648\u0636\u0639 \u0627\u0644\u0635\u0648\u0631\u0629\u060C \u0623\u0648 \u0627\u0646\u0642\u0631 \u0648\u0627\u0633\u062D\u0628 \u0644\u062A\u0639\u064A\u064A\u0646 \u062D\u062C\u0645\u0647\u0627 \u064A\u062F\u0648\u064A\u0627\u064B",
  publishLibrary: "\u0646\u0634\u0631 \u0645\u0643\u062A\u0628\u062A\u0643",
  bindTextToElement: "\u0627\u0636\u063A\u0637 \u0639\u0644\u0649 \u0625\u062F\u062E\u0627\u0644 \u0644\u0625\u0636\u0627\u0641\u0629 \u0646\u0635",
  deepBoxSelect: "\u0627\u0636\u063A\u0637 \u0639\u0644\u0649 Ctrl\\Cmd \u0644\u0644\u0627\u062E\u062A\u064A\u0627\u0631 \u0627\u0644\u0639\u0645\u064A\u0642\u060C \u0648\u0644\u0645\u0646\u0639 \u0627\u0644\u0633\u062D\u0628",
  eraserRevert: "\u0627\u0636\u063A\u0637 \u0639\u0644\u0649 Alt \u0644\u0627\u0633\u062A\u0639\u0627\u062F\u0629 \u0627\u0644\u0639\u0646\u0627\u0635\u0631 \u0627\u0644\u0645\u0639\u0644\u0651\u064E\u0645\u0629 \u0644\u0644\u062D\u0630\u0641",
  firefox_clipboard_write: '\u064A\u0645\u0643\u0646 \u0639\u0644\u0649 \u0627\u0644\u0623\u0631\u062C\u062D \u062A\u0645\u0643\u064A\u0646 \u0647\u0630\u0647 \u0627\u0644\u0645\u064A\u0632\u0629 \u0639\u0646 \u0637\u0631\u064A\u0642 \u062A\u0639\u064A\u064A\u0646 \u0639\u0644\u0645 "dom.events.asyncClipboard.clipboardItem" \u0625\u0644\u0649 "true". \u0644\u062A\u063A\u064A\u064A\u0631 \u0623\u0639\u0644\u0627\u0645 \u0627\u0644\u0645\u062A\u0635\u0641\u062D \u0641\u064A Firefox\u060C \u0642\u0645 \u0628\u0632\u064A\u0627\u0631\u0629 \u0635\u0641\u062D\u0629 "about:config".',
  disableSnapping: "\u0627\u0636\u063A\u0637 \u0639\u0644\u0649 Ctrl \u0623\u0648 Cmd \u0644\u062A\u0639\u0637\u064A\u0644 \u0627\u0644\u0627\u0644\u062A\u0642\u0627\u0637"
};
var canvasError = {
  cannotShowPreview: "\u062A\u0639\u0630\u0631 \u0639\u0631\u0636 \u0627\u0644\u0645\u0639\u0627\u064A\u0646\u0629",
  canvasTooBig: "\u0642\u062F \u062A\u0643\u0648\u0646 \u0627\u0644\u0644\u0648\u062D\u0629 \u0643\u0628\u064A\u0631\u0629 \u062C\u062F\u0627\u064B.",
  canvasTooBigTip: "\u0646\u0635\u064A\u062D\u0629: \u062D\u0627\u0648\u0644 \u062A\u062D\u0631\u064A\u0643 \u0627\u0644\u0639\u0646\u0627\u0635\u0631 \u0627\u0644\u0628\u0639\u064A\u062F\u0629 \u0628\u0634\u0643\u0644 \u0623\u0642\u0631\u0628 \u0642\u0644\u064A\u0644\u0627\u064B."
};
var errorSplash = {
  headingMain: "\u062D\u062F\u062B \u062E\u0637\u0623. \u062D\u0627\u0648\u0644 <button>\u062A\u062D\u062F\u064A\u062B \u0627\u0644\u0635\u0641\u062D\u0629</button>.",
  clearCanvasMessage: "\u0625\u0630\u0627 \u0644\u0645 \u062A\u0639\u0645\u0644 \u0625\u0639\u0627\u062F\u0629 \u0627\u0644\u062A\u062D\u0645\u064A\u0644\u060C \u062D\u0627\u0648\u0644 \u0645\u0631\u0629 \u0623\u062E\u0631\u0649 ",
  clearCanvasCaveat: " \u0647\u0630\u0627 \u0633\u064A\u0624\u062F\u064A \u0625\u0644\u0649 \u0641\u0642\u062F\u0627\u0646 \u0627\u0644\u0639\u0645\u0644 ",
  trackedToSentry: "\u062A\u0645 \u062A\u062A\u0628\u0639 \u0627\u0644\u062E\u0637\u0623 \u0641\u064A \u0627\u0644\u0645\u0639\u0631\u0641 {{eventId}} \u0639\u0644\u0649 \u0646\u0638\u0627\u0645\u0646\u0627.",
  openIssueMessage: "\u062D\u0631\u0635\u0646\u0627 \u0639\u0644\u0649 \u0639\u062F\u0645 \u0625\u0636\u0627\u0641\u0629 \u0645\u0639\u0644\u0648\u0645\u0627\u062A \u0627\u0644\u0645\u0634\u0647\u062F \u0641\u064A \u0628\u0644\u0627\u063A \u0627\u0644\u062E\u0637\u0623. \u0641\u064A \u062D\u0627\u0644 \u0643\u0648\u0646 \u0645\u0634\u0647\u062F\u0643 \u0644\u0627 \u064A\u062D\u0645\u0644 \u0623\u064A \u0645\u0639\u0644\u0648\u0645\u0627\u062A \u062E\u0627\u0635\u0629 \u0646\u0631\u062C\u0648 \u0627\u0644\u0645\u062A\u0627\u0628\u0639\u0629 \u0639\u0644\u0649 <button>\u0646\u0638\u0627\u0645 \u062A\u062A\u0628\u0639 \u0627\u0644\u0623\u062E\u0637\u0627\u0621</button>. \u0646\u0631\u062C\u0648 \u0625\u0636\u0627\u0641\u0629 \u0627\u0644\u0645\u0639\u0644\u0648\u0645\u0627\u062A \u0623\u062F\u0646\u0627\u0647 \u0628\u0646\u0633\u062E\u0647\u0627 \u0648\u0644\u0635\u0642\u0647\u0627 \u0641\u064A \u0645\u062D\u062A\u0648\u0649 \u0627\u0644\u0628\u0644\u0627\u063A \u0639\u0644\u0649 GitHub.",
  sceneContent: "\u0645\u062D\u062A\u0648\u0649 \u0627\u0644\u0645\u0634\u0647\u062F:"
};
var roomDialog = {
  desc_intro: "\u064A\u0645\u0643\u0646\u0643 \u062F\u0639\u0648\u0629 \u0627\u0644\u0622\u062E\u0631\u064A\u0646 \u0644\u0645\u0634\u0627\u0631\u0643\u062A\u0643 \u0646\u0641\u0633 \u0627\u0644\u062C\u0644\u0633\u0629 \u0627\u0644\u062A\u064A \u062A\u0639\u0645\u0644 \u0639\u0644\u064A\u0647\u0627.",
  desc_privacy: "\u0644\u0627 \u062A\u0642\u0644\u0642\u060C \u0627\u0644\u062C\u0644\u0633\u0629 \u062A\u0633\u062A\u062E\u062F\u0645 \u0627\u0644\u062A\u0634\u0641\u064A\u0631 \u0645\u0646 \u0627\u0644\u0646\u0647\u0627\u064A\u0629 \u0625\u0644\u0649 \u0627\u0644\u0646\u0647\u0627\u064A\u0629\u060C \u0644\u0630\u0644\u0643 \u0641\u0625\u0646 \u0623\u064A \u0634\u064A\u0621 \u062A\u0631\u0633\u0645\u0647 \u0633\u064A\u0628\u0642\u0649 \u062E\u0627\u0635\u0627\u064B. \u0644\u0646 \u064A\u062A\u0645\u0643\u0646 \u062D\u062A\u0649 \u0627\u0644\u062E\u0627\u062F\u0648\u0645 \u0627\u0644\u062E\u0627\u0635 \u0628\u0646\u0627 \u0645\u0646 \u0631\u0624\u064A\u0629 \u0645\u0627 \u062A\u0648\u0635\u0644\u062A \u0625\u0644\u064A\u0647.",
  button_startSession: "\u0628\u062F\u0621 \u0627\u0644\u062C\u0644\u0633\u0629",
  button_stopSession: "\u0625\u064A\u0642\u0627\u0641 \u0627\u0644\u062C\u0644\u0633\u0629",
  desc_inProgressIntro: "\u062A\u062C\u0631\u064A \u0627\u0644\u0622\u0646 \u0627\u0644\u0645\u0634\u0627\u0631\u0643\u0629 \u0627\u0644\u062D\u064A\u0629.",
  desc_shareLink: "\u0634\u0627\u0631\u0643 \u0647\u0630\u0627 \u0627\u0644\u0631\u0627\u0628\u0637 \u0645\u0639 \u0623\u064A \u0634\u062E\u0635 \u062A\u0631\u064A\u062F\u0647 \u0623\u0646 \u064A\u0634\u0627\u0631\u0643\u0643 \u0627\u0644\u062C\u0644\u0633\u0629:",
  desc_exitSession: "\u0625\u064A\u0642\u0627\u0641 \u0627\u0644\u062C\u0644\u0633\u0629 \u0633\u064A\u0624\u062F\u064A \u0625\u0644\u0649 \u0642\u0637\u0639 \u0627\u0644\u0627\u062A\u0635\u0627\u0644 \u0627\u0644\u062E\u0627\u0635 \u0628\u0643 \u0645\u0646 \u0627\u0644\u063A\u0631\u0641\u0629\u060C \u0648\u0644\u0643\u0646 \u0633\u062A\u062A\u0645\u0643\u0646 \u0645\u0646 \u0645\u0648\u0627\u0635\u0644\u0629 \u0627\u0644\u0639\u0645\u0644 \u0645\u0639 \u0627\u0644\u0645\u0634\u0647\u062F\u060C \u0645\u062D\u0644\u064A\u0627. \u0644\u0627\u062D\u0638 \u0623\u0646 \u0647\u0630\u0627 \u0644\u0646 \u064A\u0624\u062B\u0631 \u0639\u0644\u0649 \u0627\u0644\u0623\u0634\u062E\u0627\u0635 \u0627\u0644\u0622\u062E\u0631\u064A\u0646\u060C \u0648 \u0633\u064A\u0638\u0644\u0648\u0646 \u0642\u0627\u062F\u0631\u064A\u0646 \u0639\u0644\u0649 \u0627\u0644\u062A\u0639\u0627\u0648\u0646 \u0641\u064A \u0625\u0635\u062F\u0627\u0631\u0647\u0645.",
  shareTitle: "\u0627\u0644\u0627\u0646\u0636\u0645\u0627\u0645 \u0625\u0644\u0649 \u062C\u0644\u0633\u0629 \u062A\u0639\u0627\u0648\u0646 \u062D\u064A\u0629 \u0639\u0644\u0649 Excalidraw"
};
var errorDialog = {
  title: "\u062E\u0637\u0623"
};
var exportDialog = {
  disk_title: "\u062D\u0641\u0638 \u0627\u0644\u0645\u0644\u0641 \u0644\u0644\u062C\u0647\u0627\u0632",
  disk_details: "\u062A\u0635\u062F\u064A\u0631 \u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u0645\u0634\u0647\u062F \u0625\u0644\u0649 \u0645\u0644\u0641 \u064A\u0645\u0643\u0646\u0643 \u0627\u0644\u0627\u0633\u062A\u064A\u0631\u0627\u062F \u0645\u0646\u0647 \u0644\u0627\u062D\u0642\u0627\u064B.",
  disk_button: "\u0625\u062D\u0641\u0638 \u0644\u0645\u0644\u0641",
  link_title: "\u0631\u0627\u0628\u0637 \u0642\u0627\u0628\u0644 \u0644\u0644\u0645\u0634\u0627\u0631\u0643\u0629",
  link_details: "\u0635\u062F\u0631 \u0627\u0644\u0645\u0644\u0641 \u0644\u0644\u0645\u0634\u0627\u0647\u062F\u0629 \u0641\u0642\u0637.",
  link_button: "\u0627\u0644\u062A\u0635\u062F\u064A\u0631 \u0643\u0631\u0627\u0628\u0637",
  excalidrawplus_description: "\u062D\u0641\u0638 \u0627\u0644\u0645\u0634\u0647\u062F \u0625\u0644\u0649 \u0645\u0633\u0627\u062D\u0629 \u0627\u0644\u0639\u0645\u0644 +Excalidraw \u0627\u0644\u062E\u0627\u0635\u0629 \u0628\u0643.",
  excalidrawplus_button: "\u062A\u0635\u062F\u064A\u0631",
  excalidrawplus_exportError: "\u062A\u0639\u0630\u0631 \u0627\u0644\u062A\u0635\u062F\u064A\u0631 \u0625\u0644\u0649 +Excalidraw \u0641\u064A \u0627\u0644\u0648\u0642\u062A \u0627\u0644\u062D\u0627\u0644\u064A..."
};
var helpDialog = {
  blog: "\u0627\u0642\u0631\u0623 \u0645\u062F\u0648\u0646\u062A\u0646\u0627",
  click: "\u0627\u0646\u0642\u0631",
  deepSelect: "\u062A\u062D\u062F\u064A\u062F \u0639\u0645\u064A\u0642",
  deepBoxSelect: "\u062A\u062D\u062F\u064A\u062F \u0639\u0645\u064A\u0642 \u062F\u0627\u062E\u0644 \u0627\u0644\u0645\u0631\u0628\u0639\u060C \u0648\u0645\u0646\u0639 \u0627\u0644\u0633\u062D\u0628",
  curvedArrow: "\u0633\u0647\u0645 \u0645\u0627\u0626\u0644",
  curvedLine: "\u062E\u0637 \u0645\u0627\u0626\u0644",
  documentation: "\u062F\u0644\u064A\u0644 \u0627\u0644\u0627\u0633\u062A\u062E\u062F\u0627\u0645",
  doubleClick: "\u0627\u0646\u0642\u0631 \u0645\u0631\u062A\u064A\u0646",
  drag: "\u0627\u0633\u062D\u0628",
  editor: "\u0627\u0644\u0645\u062D\u0631\u0631",
  editLineArrowPoints: "\u062A\u062D\u0631\u064A\u0631 \u0633\u0637\u0631/\u0646\u0642\u0627\u0637 \u0633\u0647\u0645",
  editText: "\u062A\u0639\u062F\u064A\u0644 \u0627\u0644\u0646\u0635 / \u0625\u0636\u0627\u0641\u0629 \u062A\u0633\u0645\u064A\u0629",
  github: "\u0639\u062B\u0631\u062A \u0639\u0644\u0649 \u0645\u0634\u0643\u0644\u0629\u061F \u0625\u0631\u0633\u0627\u0644",
  howto: "\u0627\u062A\u0628\u0639 \u0627\u0644\u062A\u0639\u0644\u064A\u0645\u0627\u062A",
  or: "\u0623\u0648",
  preventBinding: "\u0645\u0646\u0639 \u0627\u0631\u062A\u0628\u0637 \u0627\u0644\u0633\u0647\u0645",
  tools: "\u0627\u0644\u0623\u062F\u0648\u0627\u062A",
  shortcuts: "\u0627\u062E\u062A\u0635\u0627\u0631\u0627\u062A \u0644\u0648\u062D\u0629 \u0627\u0644\u0645\u0641\u0627\u062A\u064A\u062D",
  textFinish: "\u0625\u0646\u0647\u0627\u0621 \u0627\u0644\u062A\u0639\u062F\u064A\u0644 (\u0645\u062D\u0631\u0631 \u0627\u0644\u0646\u0635)",
  textNewLine: "\u0623\u0636\u0641 \u0633\u0637\u0631 \u062C\u062F\u064A\u062F (\u0645\u062D\u0631\u0631 \u0646\u0635)",
  title: "\u0627\u0644\u0645\u0633\u0627\u0639\u062F\u0629",
  view: "\u0639\u0631\u0636",
  zoomToFit: "\u062A\u0643\u0628\u064A\u0631 \u0644\u0644\u0645\u0644\u0627\u0626\u0645\u0629",
  zoomToSelection: "\u062A\u0643\u0628\u064A\u0631 \u0644\u0644\u0639\u0646\u0635\u0631 \u0627\u0644\u0645\u062D\u062F\u062F",
  toggleElementLock: "\u0625\u063A\u0644\u0627\u0642/\u0641\u062A\u062D \u0627\u0644\u0645\u062D\u062F\u062F",
  movePageUpDown: "\u0646\u0642\u0644 \u0627\u0644\u0635\u0641\u062D\u0629 \u0623\u0639\u0644\u0649/\u0623\u0633\u0641\u0644",
  movePageLeftRight: "\u0646\u0642\u0644 \u0627\u0644\u0635\u0641\u062D\u0629 \u064A\u0633\u0627\u0631/\u064A\u0645\u064A\u0646"
};
var clearCanvasDialog = {
  title: "\u0645\u0633\u062D \u0627\u0644\u0644\u0648\u062D\u0629"
};
var publishDialog = {
  title: "\u0646\u0634\u0631 \u0627\u0644\u0645\u0643\u062A\u0628\u0629",
  itemName: "\u0625\u0633\u0645 \u0627\u0644\u0639\u0646\u0635\u0631",
  authorName: "\u0625\u0633\u0645 \u0627\u0644\u0645\u0624\u0644\u0641",
  githubUsername: "\u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645 \u0641\u064A \u062C\u064A\u062A \u0647\u0628",
  twitterUsername: "\u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645 \u0641\u064A \u062A\u0648\u064A\u062A\u0631",
  libraryName: "\u0627\u0633\u0645 \u0627\u0644\u0645\u0643\u062A\u0628\u0629",
  libraryDesc: "\u0648\u0635\u0641 \u0627\u0644\u0645\u0643\u062A\u0628\u0629",
  website: "\u0627\u0644\u0645\u0648\u0642\u0639",
  placeholder: {
    authorName: "\u0627\u0633\u0645\u0643 \u0623\u0648 \u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645",
    libraryName: "\u0627\u0633\u0645 \u0645\u0643\u062A\u0628\u062A\u0643",
    libraryDesc: "\u0648\u0635\u0641 \u0645\u0643\u062A\u0628\u062A\u0643 \u0644\u0645\u0633\u0627\u0639\u062F\u0629 \u0627\u0644\u0646\u0627\u0633 \u0639\u0644\u0649 \u0641\u0647\u0645 \u0627\u0633\u062A\u062E\u062F\u0627\u0645\u0647\u0627",
    githubHandle: "\u0645\u0639\u0627\u0644\u062C GitHub (\u0627\u062E\u062A\u064A\u0627\u0631\u064A)\u060C \u062D\u062A\u0649 \u062A\u062A\u0645\u0643\u0646 \u0645\u0646 \u062A\u062D\u0631\u064A\u0631 \u0627\u0644\u0645\u0643\u062A\u0628\u0629 \u0639\u0646\u062F \u0625\u0631\u0633\u0627\u0644\u0647\u0627 \u0644\u0644\u0645\u0631\u0627\u062C\u0639\u0629",
    twitterHandle: "\u0627\u0633\u0645 \u0645\u0633\u062A\u062E\u062F\u0645 \u062A\u0648\u064A\u062A\u0631 (\u0627\u062E\u062A\u064A\u0627\u0631\u064A)\u060C \u062D\u062A\u0649 \u0646\u0639\u0631\u0641 \u0645\u0646 \u0627\u0644\u0630\u064A \u0633\u064A\u062A\u0645 \u0627\u0644\u0625\u0634\u0627\u0631\u0629 \u0625\u0644\u064A\u0647 \u0639\u0646\u062F \u0627\u0644\u062A\u0631\u0648\u064A\u062C \u0639\u0628\u0631 \u062A\u0648\u064A\u062A\u0631",
    website: "\u0631\u0627\u0628\u0637 \u0625\u0644\u0649 \u0645\u0648\u0642\u0639\u0643 \u0627\u0644\u0634\u062E\u0635\u064A \u0623\u0648 \u0641\u064A \u0645\u0643\u0627\u0646 \u0622\u062E\u0631 (\u0627\u062E\u062A\u064A\u0627\u0631\u064A)"
  },
  errors: {
    required: "\u0645\u0637\u0644\u0648\u0628",
    website: "\u0623\u062F\u062E\u0644 \u0639\u0646\u0648\u0627\u0646 URL \u0635\u0627\u0644\u062D"
  },
  noteDescription: "\u062A\u0642\u062F\u064A\u0645 \u0645\u0643\u062A\u0628\u062A\u0643 \u0644\u062A\u0636\u0645\u064A\u0646\u0647\u0627 \u0641\u064A \u0645\u0633\u062A\u0648\u062F\u0639 \u0627\u0644\u0645\u0643\u062A\u0628\u0629 \u0627\u0644\u0639\u0627\u0645\u0629 <link></link> \u0644\u0623\u0634\u062E\u0627\u0635 \u0622\u062E\u0631\u064A\u0646 \u0644\u0627\u0633\u062A\u062E\u062F\u0627\u0645\u0647\u0627 \u0641\u064A \u0631\u0633\u0648\u0645\u0647\u0645.",
  noteGuidelines: "\u062A\u062D\u062A\u0627\u062C \u0627\u0644\u0645\u0643\u062A\u0628\u0629 \u0625\u0644\u0649 \u0627\u0644\u0645\u0648\u0627\u0641\u0642\u0629 \u0623\u0648\u0644\u0627. \u064A\u0631\u062C\u0649 \u0642\u0631\u0627\u0621\u0629 <link>\u0627\u0644\u0645\u0639\u0627\u064A\u064A\u0631</link> \u0642\u0628\u0644 \u062A\u0642\u062F\u064A\u0645\u0647\u0627. \u0633\u0648\u0641 \u062A\u062D\u062A\u0627\u062C \u0625\u0644\u0649 \u062D\u0633\u0627\u0628 GitHub \u0644\u0644\u062A\u0648\u0627\u0635\u0644 \u0648\u0625\u062C\u0631\u0627\u0621 \u0627\u0644\u062A\u063A\u064A\u064A\u0631\u0627\u062A \u0639\u0646\u062F \u0627\u0644\u0637\u0644\u0628\u060C \u0648\u0644\u0643\u0646 \u0644\u064A\u0633 \u0645\u0637\u0644\u0648\u0628\u0627 \u0628\u0634\u0643\u0644 \u0635\u0627\u0631\u0645.",
  noteLicense: "\u062A\u0642\u062F\u064A\u0645\u0643 \u064A\u0639\u0646\u064A \u0645\u0648\u0627\u0641\u0642\u062A\u0643 \u0639\u0644\u0649 \u0646\u0634\u0631 \u0627\u0644\u0645\u0643\u062A\u0628\u0629 \u0627\u0644\u0645\u0642\u062F\u0645\u0629 \u062A\u062D\u062A <link>MIT \u062A\u0631\u062E\u064A\u0635</link>\u060C \u0645\u0627 \u064A\u0639\u0646\u064A \u0623\u0646 \u0644\u0623\u064A \u0623\u062D\u062F \u0627\u0644\u062D\u0642 \u0641\u064A \u0627\u0633\u062A\u062E\u062F\u0627\u0645\u0647\u0627 \u062F\u0648\u0646 \u0642\u064A\u0648\u062F.",
  noteItems: "\u064A\u062C\u0628 \u0623\u0646 \u064A\u0643\u0648\u0646 \u0644\u0643\u0644 \u0639\u0646\u0635\u0631 \u0645\u0643\u062A\u0628\u0629 \u0627\u0633\u0645\u0647 \u0627\u0644\u062E\u0627\u0635 \u062D\u062A\u0649 \u064A\u0643\u0648\u0646 \u0642\u0627\u0628\u0644\u0627\u064B \u0644\u0644\u062A\u0635\u0641\u064A\u0629. \u0633\u064A\u062A\u0645 \u062A\u0636\u0645\u064A\u0646 \u0639\u0646\u0627\u0635\u0631 \u0627\u0644\u0645\u0643\u062A\u0628\u0629 \u0627\u0644\u062A\u0627\u0644\u064A\u0629:",
  atleastOneLibItem: "\u064A\u0631\u062C\u0649 \u062A\u062D\u062F\u064A\u062F \u0639\u0646\u0635\u0631 \u0645\u0643\u062A\u0628\u0629 \u0648\u0627\u062D\u062F \u0639\u0644\u0649 \u0627\u0644\u0623\u0642\u0644 \u0644\u0644\u0628\u062F\u0621",
  republishWarning: "\u0645\u0644\u0627\u062D\u0638\u0629: \u0628\u0639\u0636 \u0627\u0644\u0639\u0646\u0627\u0635\u0631 \u0627\u0644\u0645\u062D\u062F\u062F\u0629 \u0645\u0639\u064A\u0646\u0629 \u0639\u0644\u0649 \u0623\u0646\u0647 \u0646\u0634\u0631\u0647\u0627 \u0623\u0648 \u062A\u0642\u062F\u064A\u0645\u0647\u0627 \u0645\u0646 \u0642\u0628\u0644. \u064A\u062C\u0628 \u0639\u0644\u064A\u0643 \u0641\u0642\u0637 \u0625\u0639\u0627\u062F\u0629 \u0625\u0631\u0633\u0627\u0644 \u0627\u0644\u0639\u0646\u0627\u0635\u0631 \u0639\u0646\u062F \u062A\u062D\u062F\u064A\u062B \u0645\u0643\u062A\u0628\u0629 \u0645\u0648\u062C\u0648\u062F\u0629 \u0623\u0648 \u0625\u0631\u0633\u0627\u0644\u0647\u0627."
};
var publishSuccessDialog = {
  title: "\u062A\u0645 \u0625\u0631\u0633\u0627\u0644 \u0627\u0644\u0645\u0643\u062A\u0628\u0629",
  content: "\u0634\u0643\u0631\u0627 \u0644\u0643 {{authorName}}. \u0644\u0642\u062F \u062A\u0645 \u0625\u0631\u0633\u0627\u0644 \u0645\u0643\u062A\u0628\u062A\u0643 \u0644\u0644\u0645\u0631\u0627\u062C\u0639\u0629. \u064A\u0645\u0643\u0646\u0643 \u062A\u062A\u0628\u0639 \u0627\u0644\u062D\u0627\u0644\u0629"
};
var confirmDialog = {
  resetLibrary: "\u0625\u0639\u0627\u062F\u0629 \u0636\u0628\u0637 \u0627\u0644\u0645\u0643\u062A\u0628\u0629",
  removeItemsFromLib: "\u0625\u0632\u0627\u0644\u0629 \u0627\u0644\u0639\u0646\u0627\u0635\u0631 \u0627\u0644\u0645\u062D\u062F\u062F\u0629 \u0645\u0646 \u0627\u0644\u0645\u0643\u062A\u0628\u0629"
};
var imageExportDialog = {
  header: "\u062A\u0635\u062F\u064A\u0631 \u0627\u0644\u0635\u0648\u0631\u0629",
  label: {
    withBackground: "\u0627\u0644\u062E\u0644\u0641\u064A\u0629",
    onlySelected: "\u0627\u0644\u0645\u062D\u062F\u062F \u0641\u0642\u0637",
    darkMode: "\u0627\u0644\u0648\u0636\u0639 \u0627\u0644\u062F\u0627\u0643\u0646",
    embedScene: "\u062A\u0636\u0645\u064A\u0646 \u0627\u0644\u0645\u0634\u0647\u062F",
    scale: "\u0627\u0644\u062D\u062C\u0645",
    padding: "\u0627\u0644\u0647\u0648\u0627\u0645\u0634"
  },
  tooltip: {
    embedScene: "\u0633\u064A\u062A\u0645 \u062D\u0641\u0638 \u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u0645\u0634\u0647\u062F \u0641\u064A \u0645\u0644\u0641 PNG/SVG \u0627\u0644\u0645\u0635\u062F\u0651\u0631 \u0628\u062D\u064A\u062B \u064A\u0645\u0643\u0646 \u0627\u0633\u062A\u0639\u0627\u062F\u0629 \u0627\u0644\u0645\u0634\u0647\u062F \u0645\u0646\u0647.\n\u0633\u064A\u0632\u064A\u062F \u062D\u062C\u0645 \u0627\u0644\u0645\u0644\u0641 \u0627\u0644\u0645\u0635\u062F\u0631."
  },
  title: {
    exportToPng: "\u062A\u0635\u062F\u064A\u0631 \u0628\u0635\u064A\u063A\u0629 PNG",
    exportToSvg: "\u062A\u0635\u062F\u064A\u0631 \u0628\u0635\u064A\u063A\u0629 SVG",
    copyPngToClipboard: "\u0646\u0633\u062E \u0627\u0644\u0640 PNG \u0625\u0644\u0649 \u0627\u0644\u062D\u0627\u0641\u0638\u0629"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "\u0646\u0633\u062E \u0625\u0644\u0649 \u0627\u0644\u062D\u0627\u0641\u0638\u0629"
  }
};
var encrypted = {
  tooltip: "\u0631\u0633\u0648\u0645\u0627\u062A\u0643 \u0645\u0634\u0641\u0631\u0629 \u0645\u0646 \u0627\u0644\u0646\u0647\u0627\u064A\u0629 \u0625\u0644\u0649 \u0627\u0644\u0646\u0647\u0627\u064A\u0629 \u062D\u062A\u0649 \u0623\u0646 \u062E\u0648\u0627\u062F\u0645 Excalidraw \u0644\u0646 \u062A\u0631\u0627\u0647\u0627 \u0623\u0628\u062F\u0627.",
  link: "\u0645\u0634\u0627\u0631\u0643\u0629 \u0627\u0644\u0645\u062F\u0648\u0646\u0629 \u0641\u064A \u0627\u0644\u062A\u0634\u0641\u064A\u0631 \u0645\u0646 \u0627\u0644\u0646\u0647\u0627\u064A\u0629 \u0625\u0644\u0649 \u0627\u0644\u0646\u0647\u0627\u064A\u0629 \u0641\u064A Excalidraw"
};
var stats = {
  angle: "\u0627\u0644\u0632\u0627\u0648\u064A\u0629",
  element: "\u0639\u0646\u0635\u0631",
  elements: "\u0627\u0644\u0639\u0646\u0627\u0635\u0631",
  height: "\u0627\u0644\u0627\u0631\u062A\u0641\u0627\u0639",
  scene: "\u0627\u0644\u0645\u0634\u0647\u062F",
  selected: "\u0627\u0644\u0645\u062D\u062F\u062F",
  storage: "\u0627\u0644\u062A\u062E\u0632\u064A\u0646",
  title: "\u0625\u062D\u0635\u0627\u0626\u064A\u0627\u062A \u0644\u0644\u0645\u0647\u0648\u0648\u0633\u064A\u0646",
  total: "\u0627\u0644\u0645\u062C\u0645\u0648\u0639",
  version: "\u0627\u0644\u0625\u0635\u062F\u0627\u0631",
  versionCopy: "\u0627\u0646\u0642\u0631 \u0644\u0644\u0646\u0633\u062E",
  versionNotAvailable: "\u0627\u0644\u0625\u0635\u062F\u0627\u0631 \u063A\u064A\u0631 \u0645\u062A\u0648\u0641\u0631",
  width: "\u0627\u0644\u0639\u0631\u0636"
};
var toast = {
  addedToLibrary: "\u062A\u0645\u062A \u0627\u0644\u0627\u0636\u0627\u0641\u0629 \u0627\u0644\u0649 \u0627\u0644\u0645\u0643\u062A\u0628\u0629!",
  copyStyles: "\u0646\u0633\u062E\u062A \u0627\u0644\u0627\u0646\u0645\u0627\u0637.",
  copyToClipboard: "\u0646\u0633\u062E \u0625\u0644\u0649 \u0627\u0644\u062D\u0627\u0641\u0638\u0629.",
  copyToClipboardAsPng: "\u062A\u0645 \u0646\u0633\u062E {{exportSelection}} \u0625\u0644\u0649 \u0627\u0644\u062D\u0627\u0641\u0638\u0629 \u0628\u0635\u064A\u063A\u0629 PNG\n({{exportColorScheme}})",
  fileSaved: "\u062A\u0645 \u062D\u0641\u0638 \u0627\u0644\u0645\u0644\u0641.",
  fileSavedToFilename: "\u062D\u0641\u0638 \u0628\u0627\u0633\u0645 {filename}",
  canvas: "\u0644\u0648\u062D\u0629 \u0627\u0644\u0631\u0633\u0645",
  selection: "\u0627\u0644\u0639\u0646\u0635\u0631 \u0627\u0644\u0645\u062D\u062F\u062F",
  pasteAsSingleElement: "\u0627\u0633\u062A\u062E\u062F\u0645 {{shortcut}} \u0644\u0644\u0635\u0642 \u0643\u0639\u0646\u0635\u0631 \u0648\u0627\u062D\u062F\u060C\n\u0623\u0648 \u0644\u0635\u0642 \u0641\u064A \u0645\u062D\u0631\u0631 \u0646\u0635 \u0645\u0648\u062C\u0648\u062F",
  unableToEmbed: "\u062A\u0636\u0645\u064A\u0646 \u0647\u0630\u0627 \u0627\u0644\u0631\u0627\u0628\u0637 \u063A\u064A\u0631 \u0645\u0633\u0645\u0648\u062D \u062D\u0627\u0644\u064A\u064B\u0627. \u0627\u0641\u062A\u062D \u0628\u0644\u0627\u063A\u0627\u064B \u0639\u0644\u0649 GitHub \u0644\u0637\u0644\u0628 \u0639\u0646\u0648\u0627\u0646 Url \u0627\u0644\u0642\u0627\u0626\u0645\u0629 \u0627\u0644\u0628\u064A\u0636\u0627\u0621",
  unrecognizedLinkFormat: "\u0627\u0644\u0631\u0627\u0628\u0637 \u0627\u0644\u0630\u064A \u0636\u0645\u0646\u062A\u0647 \u0644\u0627 \u064A\u062A\u0637\u0627\u0628\u0642 \u0645\u0639 \u0627\u0644\u062A\u0646\u0633\u064A\u0642 \u0627\u0644\u0645\u062A\u0648\u0642\u0639. \u0627\u0644\u0631\u062C\u0627\u0621 \u0645\u062D\u0627\u0648\u0644\u0629 \u0644\u0635\u0642 \u0627\u0644\u0646\u0635 '\u0627\u0644\u0645\u0636\u0645\u0646' \u0627\u0644\u0645\u064F\u0632\u0648\u064E\u062F \u0645\u0646 \u0645\u0648\u0642\u0639 \u0627\u0644\u0645\u0635\u062F\u0631"
};
var colors = {
  transparent: "\u0634\u0641\u0627\u0641",
  black: "\u0623\u0633\u0648\u062F",
  white: "\u0623\u0628\u064A\u0636",
  red: "\u0623\u062D\u0645\u0631",
  pink: "\u0648\u0631\u062F\u064A",
  grape: "\u0639\u0646\u0628\u064A",
  violet: "\u0628\u0646\u0641\u0633\u062C\u064A",
  gray: "\u0631\u0645\u0627\u062F\u064A",
  blue: "\u0623\u0632\u0631\u0642",
  cyan: "\u0633\u0645\u0627\u0648\u064A",
  teal: "\u0623\u0632\u0631\u0642 \u0645\u062E\u0636\u0631",
  green: "\u0623\u062E\u0636\u0631",
  yellow: "\u0623\u0635\u0641\u0631",
  orange: "\u0628\u0631\u062A\u0642\u0627\u0644\u064A",
  bronze: "\u0628\u0631\u0648\u0646\u0632\u064A"
};
var welcomeScreen = {
  app: {
    center_heading: "\u062C\u0645\u064A\u0639 \u0628\u064A\u0627\u0646\u0627\u062A\u0643 \u0645\u062D\u0641\u0648\u0638\u0629 \u0645\u062D\u0644\u064A\u0627 \u0641\u064A \u0627\u0644\u0645\u062A\u0635\u0641\u062D \u0627\u0644\u062E\u0627\u0635 \u0628\u0643.",
    center_heading_plus: "\u0647\u0644 \u062A\u0631\u064A\u062F \u0627\u0644\u0630\u0647\u0627\u0628 \u0625\u0644\u0649 Excalidraw+ \u0628\u062F\u0644\u0627\u064B \u0645\u0646 \u0630\u0644\u0643\u061F",
    menuHint: "\u0627\u0644\u062A\u0635\u062F\u064A\u0631 \u0648\u0627\u0644\u062A\u0641\u0636\u064A\u0644\u0627\u062A \u0648\u0627\u0644\u0644\u063A\u0627\u062A ..."
  },
  defaults: {
    menuHint: "\u0627\u0644\u062A\u0635\u062F\u064A\u0631 \u0648\u0627\u0644\u062A\u0641\u0636\u064A\u0644\u0627\u062A \u0648\u063A\u064A\u0631\u0647\u0627...",
    center_heading: "\u0627\u0644\u0631\u0633\u0645 \u0627\u0644\u0628\u064A\u0627\u0646\u064A \u0627\u0644\u062A\u0635\u0648\u064A\u0631\u064A. \u0628\u0634\u0643\u0644 \u0645\u0628\u0633\u0637.",
    toolbarHint: "\u0627\u062E\u062A\u0631 \u0623\u062F\u0627\u0629 \u0648 \u0627\u0628\u062F\u0623 \u0627\u0644\u0631\u0633\u0645!",
    helpHint: "\u0627\u0644\u0627\u062E\u062A\u0635\u0627\u0631\u0627\u062A \u0648 \u0627\u0644\u0645\u0633\u0627\u0639\u062F\u0629"
  }
};
var colorPicker = {
  mostUsedCustomColors: "\u0627\u0644\u0623\u0644\u0648\u0627\u0646 \u0627\u0644\u0645\u062E\u0635\u0635\u0629 \u0627\u0644\u0623\u0643\u062B\u0631 \u0627\u0633\u062A\u062E\u062F\u0627\u0645\u0627",
  colors: "\u0627\u0644\u0623\u0644\u0648\u0627\u0646",
  shades: "\u0627\u0644\u062F\u0631\u062C\u0627\u062A",
  hexCode: "\u0631\u0645\u0632 Hex",
  noShades: "\u0644\u0627 \u062A\u062A\u0648\u0641\u0631 \u062F\u0631\u062C\u0627\u062A \u0644\u0647\u0630\u0627 \u0627\u0644\u0644\u0648\u0646"
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "\u062A\u0635\u062F\u064A\u0631 \u0643\u0635\u0648\u0631\u0629",
      button: "\u062A\u0635\u062F\u064A\u0631 \u0643\u0635\u0648\u0631\u0629",
      description: "\u062A\u0635\u062F\u064A\u0631 \u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u0645\u0634\u0647\u062F \u0625\u0644\u0649 \u0645\u0644\u0641 \u064A\u0645\u0643\u0646\u0643 \u0627\u0644\u0627\u0633\u062A\u064A\u0631\u0627\u062F \u0645\u0646\u0647 \u0644\u0627\u062D\u0642\u0627\u064B."
    },
    saveToDisk: {
      title: "\u062D\u0641\u0638 \u0627\u0644\u0645\u0644\u0641 \u0644\u0644\u062C\u0647\u0627\u0632",
      button: "\u062D\u0641\u0638 \u0627\u0644\u0645\u0644\u0641 \u0644\u0644\u062C\u0647\u0627\u0632",
      description: "\u062A\u0635\u062F\u064A\u0631 \u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u0645\u0634\u0647\u062F \u0625\u0644\u0649 \u0645\u0644\u0641 \u064A\u0645\u0643\u0646\u0643 \u0627\u0644\u0627\u0633\u062A\u064A\u0631\u0627\u062F \u0645\u0646\u0647 \u0644\u0627\u062D\u0642\u0627\u064B."
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "\u062A\u0635\u062F\u064A\u0631 \u0625\u0644\u0649 Excalidraw+",
      description: "\u062D\u0641\u0638 \u0627\u0644\u0645\u0634\u0647\u062F \u0625\u0644\u0649 \u0645\u0633\u0627\u062D\u0629 \u0627\u0644\u0639\u0645\u0644 +Excalidraw \u0627\u0644\u062E\u0627\u0635\u0629 \u0628\u0643."
    }
  },
  modal: {
    loadFromFile: {
      title: "\u062A\u062D\u0645\u064A\u0644 \u0645\u0646 \u0645\u0644\u0641",
      button: "\u062A\u062D\u0645\u064A\u0644 \u0645\u0646 \u0645\u0644\u0641",
      description: "\u0633\u064A\u062A\u0645 \u0627\u0644\u062A\u062D\u0645\u064A\u0644 \u0645\u0646 \u0627\u0644\u0645\u0644\u0641 <bold>\u0627\u0633\u062A\u0628\u062F\u0627\u0644 \u0627\u0644\u0645\u062D\u062A\u0648\u0649 \u0627\u0644\u0645\u0648\u062C\u0648\u062F</bold>.<br></br>\u064A\u0645\u0643\u0646\u0643 \u0627\u0644\u0646\u0633\u062E \u0627\u0644\u0627\u062D\u062A\u064A\u0627\u0637\u064A \u0644\u0631\u0633\u0645\u0643 \u0623\u0648\u0644\u0627\u064B \u0628\u0627\u0633\u062A\u062E\u062F\u0627\u0645 \u0623\u062D\u062F \u0627\u0644\u062E\u064A\u0627\u0631\u0627\u062A \u0623\u062F\u0646\u0627\u0647."
    },
    shareableLink: {
      title: "\u062A\u062D\u0645\u064A\u0644 \u0645\u0646 \u0631\u0627\u0628\u0637",
      button: "\u0627\u0633\u062A\u0628\u062F\u0627\u0644 \u0645\u062D\u062A\u0648\u0627\u064A",
      description: "\u0633\u064A\u062A\u0633\u0628\u0628 \u062A\u062D\u0645\u064A\u0644 \u0631\u0633\u0645\u0629 \u062E\u0627\u0631\u062C\u064A\u0629 <bold>\u0628\u0627\u0633\u062A\u0628\u062F\u0627\u0644 \u0645\u062D\u062A\u0648\u0627\u0643 \u0627\u0644\u0645\u0648\u062C\u0648\u062F \u062D\u0627\u0644\u064A\u0627\u064B</bold>.<br></br>\u0628\u0625\u0645\u0643\u0627\u0646\u0643 \u0625\u062C\u0631\u0627\u0621 \u0627\u0644\u0646\u0633\u062E \u0627\u0644\u0627\u062D\u062A\u064A\u0627\u0637\u064A \u0644\u0631\u0633\u0645\u062A\u0643 \u0627\u0644\u062D\u0627\u0644\u064A\u0629 \u0628\u0627\u0633\u062A\u062E\u062F\u0627\u0645 \u0623\u062D\u062F \u0627\u0644\u062E\u064A\u0627\u0631\u0627\u062A \u0623\u062F\u0646\u0627\u0647."
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var ar_SA_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  ar_SA_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=ar-SA-XORAP2EK.js.map
