"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_php_php_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/php/php.js":
/*!***********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/php/php.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/php/php.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*(#|//)region\\\\b\"),\n      end: new RegExp(\"^\\\\s*(#|//)endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  // ignoreCase: true,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.root\" }],\n      [/<!DOCTYPE/, \"metatag.html\", \"@doctype\"],\n      [/<!--/, \"comment.html\", \"@comment\"],\n      [/(<)(\\w+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)(script)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@style\" }]],\n      [/(<)([:\\w]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)(\\w+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/[^<]+/]\n      // text\n    ],\n    doctype: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.comment\" }],\n      [/[^>]+/, \"metatag.content.html\"],\n      [/>/, \"metatag.html\", \"@pop\"]\n    ],\n    comment: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.comment\" }],\n      [/-->/, \"comment.html\", \"@pop\"],\n      [/[^-]+/, \"comment.content.html\"],\n      [/./, \"comment.content.html\"]\n    ],\n    otherTag: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.otherTag\" }],\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    // -- BEGIN <script> tags handling\n    // After <script\n    script: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.script\" }],\n      [/type/, \"attribute.name\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(script\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <script ... type\n    scriptAfterType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.scriptAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type =\n    scriptAfterTypeEquals: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.scriptAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type = $S2\n    scriptWithCustomType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.scriptWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInEmbeddedState.scriptEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    // -- END <script> tags handling\n    // -- BEGIN <style> tags handling\n    // After <style\n    style: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.style\" }],\n      [/type/, \"attribute.name\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(style\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <style ... type\n    styleAfterType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.styleAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type =\n    styleAfterTypeEquals: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.styleAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type = $S2\n    styleWithCustomType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.styleWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInEmbeddedState.styleEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    // -- END <style> tags handling\n    phpInSimpleState: [\n      [/<\\?((php)|=)?/, \"metatag.php\"],\n      [/\\?>/, { token: \"metatag.php\", switchTo: \"@$S2.$S3\" }],\n      { include: \"phpRoot\" }\n    ],\n    phpInEmbeddedState: [\n      [/<\\?((php)|=)?/, \"metatag.php\"],\n      [\n        /\\?>/,\n        {\n          token: \"metatag.php\",\n          switchTo: \"@$S2.$S3\",\n          nextEmbedded: \"$S3\"\n        }\n      ],\n      { include: \"phpRoot\" }\n    ],\n    phpRoot: [\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@phpKeywords\": { token: \"keyword.php\" },\n            \"@phpCompileTimeConstants\": { token: \"constant.php\" },\n            \"@default\": \"identifier.php\"\n          }\n        }\n      ],\n      [\n        /[$a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@phpPreDefinedVariables\": {\n              token: \"variable.predefined.php\"\n            },\n            \"@default\": \"variable.php\"\n          }\n        }\n      ],\n      // brackets\n      [/[{}]/, \"delimiter.bracket.php\"],\n      [/[\\[\\]]/, \"delimiter.array.php\"],\n      [/[()]/, \"delimiter.parenthesis.php\"],\n      // whitespace\n      [/[ \\t\\r\\n]+/],\n      // comments\n      [/(#|\\/\\/)$/, \"comment.php\"],\n      [/(#|\\/\\/)/, \"comment.php\", \"@phpLineComment\"],\n      // block comments\n      [/\\/\\*/, \"comment.php\", \"@phpComment\"],\n      // strings\n      [/\"/, \"string.php\", \"@phpDoubleQuoteString\"],\n      [/'/, \"string.php\", \"@phpSingleQuoteString\"],\n      // delimiters\n      [/[\\+\\-\\*\\%\\&\\|\\^\\~\\!\\=\\<\\>\\/\\?\\;\\:\\.\\,\\@]/, \"delimiter.php\"],\n      // numbers\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?/, \"number.float.php\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float.php\"],\n      [/0[xX][0-9a-fA-F']*[0-9a-fA-F]/, \"number.hex.php\"],\n      [/0[0-7']*[0-7]/, \"number.octal.php\"],\n      [/0[bB][0-1']*[0-1]/, \"number.binary.php\"],\n      [/\\d[\\d']*/, \"number.php\"],\n      [/\\d/, \"number.php\"]\n    ],\n    phpComment: [\n      [/\\*\\//, \"comment.php\", \"@pop\"],\n      [/[^*]+/, \"comment.php\"],\n      [/./, \"comment.php\"]\n    ],\n    phpLineComment: [\n      [/\\?>/, { token: \"@rematch\", next: \"@pop\" }],\n      [/.$/, \"comment.php\", \"@pop\"],\n      [/[^?]+$/, \"comment.php\", \"@pop\"],\n      [/[^?]+/, \"comment.php\"],\n      [/./, \"comment.php\"]\n    ],\n    phpDoubleQuoteString: [\n      [/[^\\\\\"]+/, \"string.php\"],\n      [/@escapes/, \"string.escape.php\"],\n      [/\\\\./, \"string.escape.invalid.php\"],\n      [/\"/, \"string.php\", \"@pop\"]\n    ],\n    phpSingleQuoteString: [\n      [/[^\\\\']+/, \"string.php\"],\n      [/@escapes/, \"string.escape.php\"],\n      [/\\\\./, \"string.escape.invalid.php\"],\n      [/'/, \"string.php\", \"@pop\"]\n    ]\n  },\n  phpKeywords: [\n    \"abstract\",\n    \"and\",\n    \"array\",\n    \"as\",\n    \"break\",\n    \"callable\",\n    \"case\",\n    \"catch\",\n    \"cfunction\",\n    \"class\",\n    \"clone\",\n    \"const\",\n    \"continue\",\n    \"declare\",\n    \"default\",\n    \"do\",\n    \"else\",\n    \"elseif\",\n    \"enddeclare\",\n    \"endfor\",\n    \"endforeach\",\n    \"endif\",\n    \"endswitch\",\n    \"endwhile\",\n    \"extends\",\n    \"false\",\n    \"final\",\n    \"for\",\n    \"foreach\",\n    \"function\",\n    \"global\",\n    \"goto\",\n    \"if\",\n    \"implements\",\n    \"interface\",\n    \"instanceof\",\n    \"insteadof\",\n    \"namespace\",\n    \"new\",\n    \"null\",\n    \"object\",\n    \"old_function\",\n    \"or\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"resource\",\n    \"static\",\n    \"switch\",\n    \"throw\",\n    \"trait\",\n    \"try\",\n    \"true\",\n    \"use\",\n    \"var\",\n    \"while\",\n    \"xor\",\n    \"die\",\n    \"echo\",\n    \"empty\",\n    \"exit\",\n    \"eval\",\n    \"include\",\n    \"include_once\",\n    \"isset\",\n    \"list\",\n    \"require\",\n    \"require_once\",\n    \"return\",\n    \"print\",\n    \"unset\",\n    \"yield\",\n    \"__construct\"\n  ],\n  phpCompileTimeConstants: [\n    \"__CLASS__\",\n    \"__DIR__\",\n    \"__FILE__\",\n    \"__LINE__\",\n    \"__NAMESPACE__\",\n    \"__METHOD__\",\n    \"__FUNCTION__\",\n    \"__TRAIT__\"\n  ],\n  phpPreDefinedVariables: [\n    \"$GLOBALS\",\n    \"$_SERVER\",\n    \"$_GET\",\n    \"$_POST\",\n    \"$_FILES\",\n    \"$_REQUEST\",\n    \"$_SESSION\",\n    \"$_ENV\",\n    \"$_COOKIE\",\n    \"$php_errormsg\",\n    \"$HTTP_RAW_POST_DATA\",\n    \"$http_response_header\",\n    \"$argc\",\n    \"$argv\"\n  ],\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/php/php.js\n"));

/***/ })

}]);