<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Browser Socket.IO Test</title>
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
        }
        .connecting {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        .connected {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            width: 100%;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 Simple Socket.IO Test</h1>
        
        <div id="status" class="status connecting">
            🔄 Ready to connect...
        </div>
        
        <button onclick="connect()">🚀 Connect to Server</button>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let socket = null;
        const SERVER_URL = 'http://localhost:5002';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(message, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${className}`;
        }
        
        function connect() {
            log('🔄 Attempting to connect to ' + SERVER_URL);
            updateStatus('🔄 Connecting...', 'connecting');
            
            if (socket) {
                socket.disconnect();
            }
            
            // Try the simplest possible configuration first
            socket = io(SERVER_URL, {
                transports: ['polling'], // Start with polling only
                timeout: 15000,
                reconnectionAttempts: 3,
                reconnectionDelay: 2000,
                forceNew: true
            });
            
            socket.on('connect', () => {
                log('✅ Connected successfully!');
                log(`Socket ID: ${socket.id}`);
                log(`Transport: ${socket.io.engine.transport.name}`);
                updateStatus('✅ Connected!', 'connected');
                
                // Test basic communication
                socket.emit('test-message', { 
                    message: 'Hello from simple browser test!',
                    timestamp: new Date().toISOString()
                });
            });
            
            socket.on('disconnect', (reason) => {
                log(`❌ Disconnected: ${reason}`);
                updateStatus('❌ Disconnected', 'error');
            });
            
            socket.on('connect_error', (error) => {
                log(`🚨 Connection error: ${error.message}`);
                log(`Error type: ${error.type || 'unknown'}`);
                log(`Error description: ${error.description || 'none'}`);
                updateStatus('🚨 Connection Failed', 'error');
                
                // Try WebSocket if polling fails
                if (socket.io.engine.transport.name === 'polling') {
                    log('🔄 Polling failed, trying WebSocket...');
                    setTimeout(() => {
                        socket.disconnect();
                        socket = io(SERVER_URL, {
                            transports: ['websocket'],
                            timeout: 15000,
                            reconnectionAttempts: 2
                        });
                        setupWebSocketListeners();
                    }, 2000);
                }
            });
            
            socket.on('test-response', (data) => {
                log(`📨 Received response: ${JSON.stringify(data)}`);
            });
        }
        
        function setupWebSocketListeners() {
            socket.on('connect', () => {
                log('✅ WebSocket connected successfully!');
                log(`Socket ID: ${socket.id}`);
                updateStatus('✅ WebSocket Connected!', 'connected');
            });
            
            socket.on('connect_error', (error) => {
                log(`🚨 WebSocket error: ${error.message}`);
                updateStatus('🚨 All Transports Failed', 'error');
            });
        }
        
        // Auto-connect on page load
        window.addEventListener('load', () => {
            log('🌐 Page loaded, ready to test connection');
        });
    </script>
</body>
</html>
