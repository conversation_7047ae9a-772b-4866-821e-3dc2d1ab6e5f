"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_restructuredtext_restruc-00a031"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js":
/*!*************************************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/restructuredtext/restructuredtext.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"(\", close: \")\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*<!--\\\\s*#?region\\\\b.*-->\"),\n      end: new RegExp(\"^\\\\s*<!--\\\\s*#?endregion\\\\b.*-->\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".rst\",\n  control: /[\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  escapes: /\\\\(?:@control)/,\n  empty: [\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"link\",\n    \"meta\",\n    \"param\"\n  ],\n  alphanumerics: /[A-Za-z0-9]/,\n  simpleRefNameWithoutBq: /(?:@alphanumerics[-_+:.]*@alphanumerics)+|(?:@alphanumerics+)/,\n  simpleRefName: /(?:`@phrase`|@simpleRefNameWithoutBq)/,\n  phrase: /@simpleRefNameWithoutBq(?:\\s@simpleRefNameWithoutBq)*/,\n  citationName: /[A-Za-z][A-Za-z0-9-_.]*/,\n  blockLiteralStart: /(?:[!\"#$%&'()*+,-./:;<=>?@\\[\\]^_`{|}~]|[\\s])/,\n  precedingChars: /(?:[ -:/'\"<([{])/,\n  followingChars: /(?:[ -.,:;!?/'\")\\]}>]|$)/,\n  punctuation: /(=|-|~|`|#|\"|\\^|\\+|\\*|:|\\.|'|_|\\+)/,\n  tokenizer: {\n    root: [\n      //sections\n      [/^(@punctuation{3,}$){1,1}?/, \"keyword\"],\n      //line-blocks\n      //No rules on it\n      //bullet-lists\n      [/^\\s*([\\*\\-+‣•]|[a-zA-Z0-9]+\\.|\\([a-zA-Z0-9]+\\)|[a-zA-Z0-9]+\\))\\s/, \"keyword\"],\n      //literal-blocks\n      [/([ ]::)\\s*$/, \"keyword\", \"@blankLineOfLiteralBlocks\"],\n      [/(::)\\s*$/, \"keyword\", \"@blankLineOfLiteralBlocks\"],\n      { include: \"@tables\" },\n      { include: \"@explicitMarkupBlocks\" },\n      { include: \"@inlineMarkup\" }\n    ],\n    explicitMarkupBlocks: [\n      //citations\n      { include: \"@citations\" },\n      //footnotes\n      { include: \"@footnotes\" },\n      //directives\n      [\n        /^(\\.\\.\\s)(@simpleRefName)(::\\s)(.*)$/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"keyword\", \"\", \"\"]\n      ],\n      //hyperlink-targets\n      [\n        /^(\\.\\.)(\\s+)(_)(@simpleRefName)(:)(\\s+)(.*)/,\n        [{ token: \"\", next: \"hyperlinks\" }, \"\", \"\", \"string.link\", \"\", \"\", \"string.link\"]\n      ],\n      //anonymous-hyperlinks\n      [\n        /^((?:(?:\\.\\.)(?:\\s+))?)(__)(:)(\\s+)(.*)/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"\", \"\", \"\", \"string.link\"]\n      ],\n      [/^(__\\s+)(.+)/, [\"\", \"string.link\"]],\n      //substitution-definitions\n      [\n        /^(\\.\\.)( \\|)([^| ]+[^|]*[^| ]*)(\\| )(@simpleRefName)(:: .*)/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"\", \"string.link\", \"\", \"keyword\", \"\"],\n        \"@rawBlocks\"\n      ],\n      [/(\\|)([^| ]+[^|]*[^| ]*)(\\|_{0,2})/, [\"\", \"string.link\", \"\"]],\n      //comments\n      [/^(\\.\\.)([ ].*)$/, [{ token: \"\", next: \"@comments\" }, \"comment\"]]\n    ],\n    inlineMarkup: [\n      { include: \"@citationsReference\" },\n      { include: \"@footnotesReference\" },\n      //hyperlink-references\n      [/(@simpleRefName)(_{1,2})/, [\"string.link\", \"\"]],\n      //embedded-uris-and-aliases\n      [/(`)([^<`]+\\s+)(<)(.*)(>)(`)(_)/, [\"\", \"string.link\", \"\", \"string.link\", \"\", \"\", \"\"]],\n      //emphasis\n      [/\\*\\*([^\\\\*]|\\*(?!\\*))+\\*\\*/, \"strong\"],\n      [/\\*[^*]+\\*/, \"emphasis\"],\n      //inline-literals\n      [/(``)((?:[^`]|\\`(?!`))+)(``)/, [\"\", \"keyword\", \"\"]],\n      [/(__\\s+)(.+)/, [\"\", \"keyword\"]],\n      //interpreted-text\n      [/(:)((?:@simpleRefNameWithoutBq)?)(:`)([^`]+)(`)/, [\"\", \"keyword\", \"\", \"\", \"\"]],\n      [/(`)([^`]+)(`:)((?:@simpleRefNameWithoutBq)?)(:)/, [\"\", \"\", \"\", \"keyword\", \"\"]],\n      [/(`)([^`]+)(`)/, \"\"],\n      //inline-internal-targets\n      [/(_`)(@phrase)(`)/, [\"\", \"string.link\", \"\"]]\n    ],\n    citations: [\n      [\n        /^(\\.\\.\\s+\\[)((?:@citationName))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ]\n    ],\n    citationsReference: [[/(\\[)(@citationName)(\\]_)/, [\"\", \"string.link\", \"\"]]],\n    footnotes: [\n      [\n        /^(\\.\\.\\s+\\[)((?:[0-9]+))(\\]\\s+.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\"]\n      ],\n      [\n        /^(\\.\\.\\s+\\[)((?:#@simpleRefName?))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ],\n      [\n        /^(\\.\\.\\s+\\[)((?:\\*))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ]\n    ],\n    footnotesReference: [\n      [/(\\[)([0-9]+)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]],\n      [/(\\[)(#@simpleRefName?)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]],\n      [/(\\[)(\\*)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]]\n    ],\n    blankLineOfLiteralBlocks: [\n      [/^$/, \"\", \"@subsequentLinesOfLiteralBlocks\"],\n      [/^.*$/, \"\", \"@pop\"]\n    ],\n    subsequentLinesOfLiteralBlocks: [\n      [/(@blockLiteralStart+)(.*)/, [\"keyword\", \"\"]],\n      [/^(?!blockLiteralStart)/, \"\", \"@popall\"]\n    ],\n    subsequentLines: [\n      [/^[\\s]+.*/, \"\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    hyperlinks: [\n      [/^[\\s]+.*/, \"string.link\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    comments: [\n      [/^[\\s]+.*/, \"comment\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    tables: [\n      [/\\+-[+-]+/, \"keyword\"],\n      [/\\+=[+=]+/, \"keyword\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js\n"));

/***/ })

}]);