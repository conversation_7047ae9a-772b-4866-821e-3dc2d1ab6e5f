"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_less_less_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/less/less.js":
/*!*************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/less/less.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/less/less.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|([@#!.:]?[\\w-?]+%?)|[@#!.]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"],\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".less\",\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  identifierPlus: \"-?-?([a-zA-Z:.]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-:.]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@nestedJSBegin\" },\n      [\"[ \\\\t\\\\r\\\\n]+\", \"\"],\n      { include: \"@comments\" },\n      { include: \"@keyword\" },\n      { include: \"@strings\" },\n      { include: \"@numbers\" },\n      [\"[*_]?[a-zA-Z\\\\-\\\\s]+(?=:.*(;|(\\\\\\\\$)))\", \"attribute.name\", \"@attribute\"],\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"tag\", next: \"@urldeclaration\" }],\n      [\"[{}()\\\\[\\\\]]\", \"@brackets\"],\n      [\"[,:;]\", \"delimiter\"],\n      [\"#@identifierPlus\", \"tag.id\"],\n      [\"&\", \"tag\"],\n      [\"\\\\.@identifierPlus(?=\\\\()\", \"tag.class\", \"@attribute\"],\n      [\"\\\\.@identifierPlus\", \"tag.class\"],\n      [\"@identifierPlus\", \"tag\"],\n      { include: \"@operators\" },\n      [\"@(@identifier(?=[:,\\\\)]))\", \"variable\", \"@attribute\"],\n      [\"@(@identifier)\", \"variable\"],\n      [\"@\", \"key\", \"@atRules\"]\n    ],\n    nestedJSBegin: [\n      [\"``\", \"delimiter.backtick\"],\n      [\n        \"`\",\n        {\n          token: \"delimiter.backtick\",\n          next: \"@nestedJSEnd\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ]\n    ],\n    nestedJSEnd: [\n      [\n        \"`\",\n        {\n          token: \"delimiter.backtick\",\n          next: \"@pop\",\n          nextEmbedded: \"@pop\"\n        }\n      ]\n    ],\n    operators: [[\"[<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~]\", \"operator\"]],\n    keyword: [\n      [\n        \"(@[\\\\s]*import|![\\\\s]*important|true|false|when|iscolor|isnumber|isstring|iskeyword|isurl|ispixel|ispercentage|isem|hue|saturation|lightness|alpha|lighten|darken|saturate|desaturate|fadein|fadeout|fade|spin|mix|round|ceil|floor|percentage)\\\\b\",\n        \"keyword\"\n      ]\n    ],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"tag\", next: \"@pop\" }]\n    ],\n    attribute: [\n      { include: \"@nestedJSBegin\" },\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      { include: \"@numbers\" },\n      { include: \"@keyword\" },\n      [\"[a-zA-Z\\\\-]+(?=\\\\()\", \"attribute.value\", \"@attribute\"],\n      [\">\", \"operator\", \"@pop\"],\n      [\"@identifier\", \"attribute.value\"],\n      { include: \"@operators\" },\n      [\"@(@identifier)\", \"variable\"],\n      [\"[)\\\\}]\", \"@brackets\", \"@pop\"],\n      [\"[{}()\\\\[\\\\]>]\", \"@brackets\"],\n      [\"[;]\", \"delimiter\", \"@pop\"],\n      [\"[,=:]\", \"delimiter\"],\n      [\"\\\\s\", \"\"],\n      [\".\", \"attribute.value\"]\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [\".\", \"comment\"]\n    ],\n    numbers: [\n      [\"(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"attribute.value.number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"attribute.value.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"attribute.value.unit\",\n        \"@pop\"\n      ]\n    ],\n    strings: [\n      ['~?\"', { token: \"string.delimiter\", next: \"@stringsEndDoubleQuote\" }],\n      [\"~?'\", { token: \"string.delimiter\", next: \"@stringsEndQuote\" }]\n    ],\n    stringsEndDoubleQuote: [\n      ['\\\\\\\\\"', \"string\"],\n      ['\"', { token: \"string.delimiter\", next: \"@popall\" }],\n      [\".\", \"string\"]\n    ],\n    stringsEndQuote: [\n      [\"\\\\\\\\'\", \"string\"],\n      [\"'\", { token: \"string.delimiter\", next: \"@popall\" }],\n      [\".\", \"string\"]\n    ],\n    atRules: [\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      [\"[()]\", \"delimiter\"],\n      [\"[\\\\{;]\", \"delimiter\", \"@pop\"],\n      [\".\", \"key\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/less/less.js\n"));

/***/ })

}]);