import"../chunk-SRAX5OIU.js";var r={paste:"Yap\u0131\u015Fd\u0131r",pasteAsPlaintext:"D\xFCz m\u0259tn kimi yap\u0131\u015Fd\u0131r\u0131n",pasteCharts:"Diaq<PERSON>lar\u0131 yap\u0131\u015Fd\u0131r\u0131n",selectAll:"Ham\u0131s\u0131n\u0131 se\xE7",multiSelect:"Se\xE7im\u0259 element \u0259lav\u0259 edin",moveCanvas:"Kanvas\u0131 k\xF6\xE7\xFCr\xFCn",cut:"K\u0259s",copy:"Kopyala",copyAsPng:"PNG olaraq panoya kopyala",copyAsSvg:"SVG olaraq panoya kopyala",copyText:"M\u0259tn olaraq panoya kopyala",copySource:"",convertToCode:"",bringForward:"\xD6n\u0259 da\u015F\u0131",sendToBack:"Geriy\u0259 g\xF6nd\u0259rin",bringToFront:"\xD6n\u0259 g\u0259tirin",sendBackward:"Geriy\u0259 g\xF6nd\u0259rin",delete:"Sil",copyStyles:"Still\u0259ri kopyalay\u0131n",pasteStyles:"Still\u0259ri yap\u0131\u015Fd\u0131r\u0131n",stroke:"Strok r\u0259ngi",background:"Arxa fon",fill:"Doldur",strokeWidth:"Strok eni",strokeStyle:"Strok stili",strokeStyle_solid:"Solid",strokeStyle_dashed:"K\u0259sik",strokeStyle_dotted:"N\xF6qt\u0259li",sloppiness:"S\u0259liq\u0259sizlik",opacity:"\u015E\u0259ffafl\u0131q",textAlign:"M\u0259tni uy\u011Funla\u015Fd\u0131r",edges:"K\u0259narlar",sharp:"K\u0259skin",round:"D\u0259yirmi",arrowheads:"Ox uclar\u0131",arrowhead_none:"He\xE7 biri",arrowhead_arrow:"Ox",arrowhead_bar:"\xC7ubuq",arrowhead_circle:"",arrowhead_circle_outline:"",arrowhead_triangle:"\xDC\xE7bucaq",arrowhead_triangle_outline:"",arrowhead_diamond:"",arrowhead_diamond_outline:"",fontSize:"\u015Erift \xF6l\xE7\xFCs\xFC",fontFamily:"\u015Erift qrupu",addWatermark:'"Made with Excalidraw" \u0259lav\u0259 et',handDrawn:"\u018Fll\u0259 \xE7\u0259kilmi\u015F",normal:"Normal",code:"Kod",small:"Ki\xE7ik",medium:"Orta",large:"B\xF6y\xFCk",veryLarge:"\xC7ox b\xF6y\xFCk",solid:"Solid",hachure:"\u015Etrix",zigzag:"Ziqzaq",crossHatch:"\xC7arpaz d\u0259lik",thin:"\u0130nc\u0259",bold:"Qal\u0131n",left:"Sol",center:"M\u0259rk\u0259z",right:"Sa\u011F",extraBold:"Ekstra qal\u0131n",architect:"Memar",artist:"R\u0259ssam",cartoonist:"Karikatura\xE7\u0131",fileTitle:"Fayl ad\u0131",colorPicker:"R\u0259ng se\xE7\u0259n",canvasColors:"Kanvas \xFCz\u0259rind\u0259 istifad\u0259 olunur",canvasBackground:"Kanvas arxa fonu",drawingCanvas:"Kanvas \xE7\u0259km\u0259k",layers:"Qatlar",actions:"H\u0259r\u0259k\u0259tl\u0259r",language:"Dil",liveCollaboration:"Canl\u0131 \u0259m\u0259kda\u015Fl\u0131q...",duplicateSelection:"Dublikat",untitled:"Ba\u015Fl\u0131qs\u0131z",name:"Ad",yourName:"Ad\u0131n\u0131z",madeWithExcalidraw:"Excalidraw il\u0259 haz\u0131rlanm\u0131\u015Fd\u0131r",group:"Qrup \u015F\u0259klind\u0259 se\xE7im",ungroup:"Qrupsuz se\xE7im",collaborators:"",showGrid:"",addToLibrary:"",removeFromLibrary:"",libraryLoadingMessage:"",libraries:"",loadingScene:"",align:"",alignTop:"",alignBottom:"",alignLeft:"",alignRight:"",centerVertically:"",centerHorizontally:"",distributeHorizontally:"",distributeVertically:"",flipHorizontal:"",flipVertical:"",viewMode:"",share:"",showStroke:"",showBackground:"",toggleTheme:"",personalLib:"",excalidrawLib:"",decreaseFontSize:"",increaseFontSize:"",unbindText:"",bindText:"",createContainerFromText:"",link:{edit:"",editEmbed:"",create:"",createEmbed:"",label:"",labelEmbed:"",empty:""},lineEditor:{edit:"",exit:""},elementLock:{lock:"",unlock:"",lockAll:"",unlockAll:""},statusPublished:"",sidebarLock:"",selectAllElementsInFrame:"",removeAllElementsFromFrame:"",eyeDropper:"",textToDiagram:"",prompt:""},o={noItems:"",hint_emptyLibrary:"",hint_emptyPrivateLibrary:""},t={clearReset:"",exportJSON:"",exportImage:"",export:"",copyToClipboard:"",save:"",saveAs:"",load:"",getShareableLink:"",close:"",selectLanguage:"",scrollBackToContent:"",zoomIn:"",zoomOut:"",resetZoom:"",menu:"",done:"",edit:"",undo:"",redo:"",resetLibrary:"",createNewRoom:"",fullScreen:"",darkMode:"",lightMode:"",zenMode:"",objectsSnapMode:"",exitZenMode:"",cancel:"",clear:"",remove:"",embed:"",publishLibrary:"",submit:"",confirm:"",embeddableInteractionButton:""},i={clearReset:"",couldNotCreateShareableLink:"",couldNotCreateShareableLinkTooBig:"",couldNotLoadInvalidFile:"",importBackendFailed:"",cannotExportEmptyCanvas:"",couldNotCopyToClipboard:"",decryptFailed:"",uploadedSecurly:"",loadSceneOverridePrompt:"",collabStopOverridePrompt:"",errorAddingToLibrary:"",errorRemovingFromLibrary:"",confirmAddLibrary:"",imageDoesNotContainScene:"",cannotRestoreFromImage:"",invalidSceneUrl:"",resetLibrary:"",removeItemsFromsLibrary:"",invalidEncryptionKey:"",collabOfflineWarning:""},l={unsupportedFileType:"",imageInsertError:"",fileTooBig:"",svgImageInsertError:"",failedToFetchImage:"",invalidSVGString:"",cannotResolveCollabServer:"",importLibraryError:"",collabSaveFailed:"",collabSaveFailed_sizeExceeded:"",imageToolNotSupported:"",brave_measure_text_error:{line1:"",line2:"",line3:"",line4:""},libraryElementTypeError:{embeddable:"",iframe:"",image:""},asyncPasteFailedOnRead:"",asyncPasteFailedOnParse:"",copyToSystemClipboardFailed:""},n={selection:"",image:"",rectangle:"",diamond:"",ellipse:"",arrow:"",line:"",freedraw:"",text:"",library:"",lock:"",penMode:"",link:"",eraser:"",frame:"",magicframe:"",embeddable:"",laser:"",hand:"",extraTools:"",mermaidToExcalidraw:"",magicSettings:""},s={canvasActions:"",selectedShapeActions:"",shapes:""},d={canvasPanning:"",linearElement:"",freeDraw:"",text:"",embeddable:"",text_selected:"",text_editing:"",linearElementMulti:"",lockAngle:"",resize:"",resizeImage:"",rotate:"",lineEditor_info:"",lineEditor_pointSelected:"",lineEditor_nothingSelected:"",placeImage:"",publishLibrary:"",bindTextToElement:"",deepBoxSelect:"",eraserRevert:"",firefox_clipboard_write:"",disableSnapping:""},c={cannotShowPreview:"",canvasTooBig:"",canvasTooBigTip:""},m={headingMain:"",clearCanvasMessage:"",clearCanvasCaveat:"",trackedToSentry:"",openIssueMessage:"",sceneContent:""},p={desc_intro:"",desc_privacy:"",button_startSession:"",button_stopSession:"",desc_inProgressIntro:"",desc_shareLink:"",desc_exitSession:"",shareTitle:""},g={title:""},b={disk_title:"",disk_details:"",disk_button:"",link_title:"",link_details:"",link_button:"",excalidrawplus_description:"",excalidrawplus_button:"",excalidrawplus_exportError:""},u={blog:"",click:"",deepSelect:"",deepBoxSelect:"",curvedArrow:"",curvedLine:"",documentation:"",doubleClick:"",drag:"",editor:"",editLineArrowPoints:"",editText:"",github:"",howto:"",or:"",preventBinding:"",tools:"",shortcuts:"",textFinish:"",textNewLine:"",title:"",view:"",zoomToFit:"",zoomToSelection:"",toggleElementLock:"",movePageUpDown:"",movePageLeftRight:""},y={title:""},h={title:"",itemName:"",authorName:"",githubUsername:"",twitterUsername:"",libraryName:"",libraryDesc:"",website:"",placeholder:{authorName:"",libraryName:"",libraryDesc:"",githubHandle:"",twitterHandle:"",website:""},errors:{required:"",website:""},noteDescription:"",noteGuidelines:"",noteLicense:"",noteItems:"",atleastOneLibItem:"",republishWarning:""},k={title:"",content:""},S={resetLibrary:"",removeItemsFromLib:""},v={header:"",label:{withBackground:"",onlySelected:"",darkMode:"",embedScene:"",scale:"",padding:""},tooltip:{embedScene:""},title:{exportToPng:"",exportToSvg:"",copyPngToClipboard:""},button:{exportToPng:"",exportToSvg:"",copyPngToClipboard:""}},w={tooltip:"",link:""},x={angle:"",element:"",elements:"",height:"",scene:"",selected:"",storage:"",title:"",total:"",version:"",versionCopy:"",versionNotAvailable:"",width:""},T={addedToLibrary:"",copyStyles:"",copyToClipboard:"",copyToClipboardAsPng:"",fileSaved:"",fileSavedToFilename:"",canvas:"",selection:"",pasteAsSingleElement:"",unableToEmbed:"",unrecognizedLinkFormat:""},_={transparent:"",black:"",white:"",red:"",pink:"",grape:"",violet:"",gray:"",blue:"",cyan:"",teal:"",green:"",yellow:"",orange:"",bronze:""},L={app:{center_heading:"",center_heading_plus:"",menuHint:""},defaults:{menuHint:"",center_heading:"",toolbarHint:"",helpHint:""}},f={mostUsedCustomColors:"",colors:"",shades:"",hexCode:"",noShades:""},C={action:{exportToImage:{title:"",button:"",description:""},saveToDisk:{title:"",button:"",description:""},excalidrawPlus:{title:"",button:"",description:""}},modal:{loadFromFile:{title:"",button:"",description:""},shareableLink:{title:"",button:"",description:""}}},E={title:"",button:"",description:"",syntax:"",preview:""},F={labels:r,library:o,buttons:t,alerts:i,errors:l,toolBar:n,headings:s,hints:d,canvasError:c,errorSplash:m,roomDialog:p,errorDialog:g,exportDialog:b,helpDialog:u,clearCanvasDialog:y,publishDialog:h,publishSuccessDialog:k,confirmDialog:S,imageExportDialog:v,encrypted:w,stats:x,toast:T,colors:_,welcomeScreen:L,colorPicker:f,overwriteConfirm:C,mermaid:E};export{i as alerts,t as buttons,c as canvasError,y as clearCanvasDialog,f as colorPicker,_ as colors,S as confirmDialog,F as default,w as encrypted,g as errorDialog,m as errorSplash,l as errors,b as exportDialog,s as headings,u as helpDialog,d as hints,v as imageExportDialog,r as labels,o as library,E as mermaid,C as overwriteConfirm,h as publishDialog,k as publishSuccessDialog,p as roomDialog,x as stats,T as toast,n as toolBar,L as welcomeScreen};
