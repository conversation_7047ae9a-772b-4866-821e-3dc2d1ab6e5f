"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/TeacherControlPanel.tsx":
/*!************************************************!*\
  !*** ./src/components/TeacherControlPanel.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherControlPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit3,FiEye,FiLock,FiUnlock,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/EditPermissionContext */ \"(app-pages-browser)/./src/context/EditPermissionContext.tsx\");\n/* harmony import */ var _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSocketService */ \"(app-pages-browser)/./src/hooks/useSocketService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TeacherControlPanel(param) {\n    let { className = '' } = param;\n    _s();\n    const { isTeacher, students, grantEditPermission, revokeEditPermission } = (0,_context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__.useEditPermission)();\n    const { isReady, isConnected } = (0,_hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__.useSocketService)();\n    const [pending, setPending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Debug logging for student list updates\n    useEffect({\n        \"TeacherControlPanel.useEffect\": ()=>{\n            console.log('🎯 [TEACHER_PANEL] State update:', {\n                isTeacher,\n                studentCount: students.length,\n                isReady,\n                isConnected,\n                students: students.map({\n                    \"TeacherControlPanel.useEffect\": (s)=>({\n                            username: s.username,\n                            socketId: s.socketId,\n                            canEdit: s.canEdit\n                        })\n                }[\"TeacherControlPanel.useEffect\"])\n            });\n        }\n    }[\"TeacherControlPanel.useEffect\"], [\n        students,\n        isTeacher,\n        isReady,\n        isConnected\n    ]);\n    // Only show panel for teachers\n    if (!isTeacher) {\n        console.log('🎯 [TEACHER_PANEL] Not showing panel (not teacher)');\n        return null;\n    }\n    const handleToggle = (student)=>{\n        setPending(student.socketId);\n        if (student.canEdit) {\n            revokeEditPermission(student.socketId);\n        } else {\n            grantEditPermission(student.socketId);\n        }\n        setTimeout(()=>setPending(null), 1000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-full max-w-md bg-white border rounded-lg shadow-lg p-4 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiUsers, {\n                        className: \"text-blue-500 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-bold\",\n                        children: \"Student Access Panel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            !isReady || !isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-yellow-600 text-sm mb-2\",\n                children: \"Waiting for connection...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    students.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-400 flex flex-col items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiUsers, {\n                                className: \"w-8 h-8 mb-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"No students in the room yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this),\n                    students.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between bg-gray-50 rounded px-3 py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: student.username\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this),\n                                        student.canEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded bg-green-100 text-green-700 text-xs ml-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiEdit3, {\n                                                    className: \"w-3 h-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" Edit\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded bg-gray-100 text-gray-600 text-xs ml-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiEye, {\n                                                    className: \"w-3 h-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" View\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center px-3 py-1 rounded text-xs font-medium transition-all \".concat(student.canEdit ? 'bg-green-100 text-green-700 hover:bg-green-200' : 'bg-gray-100 text-gray-600 hover:bg-gray-200', \" \").concat(pending === student.socketId || !isReady || !isConnected ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                                    onClick: ()=>handleToggle(student),\n                                    disabled: pending === student.socketId || !isReady || !isConnected,\n                                    title: student.canEdit ? 'Revoke edit access' : 'Grant edit access',\n                                    children: student.canEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiUnlock, {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 36\n                                            }, this),\n                                            \" Revoke\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiLock, {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 88\n                                            }, this),\n                                            \" Grant\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, student.socketId, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_s(TeacherControlPanel, \"oCMkkMEC9UU4yBXi0XK0+5hcNko=\", false, function() {\n    return [\n        _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__.useEditPermission,\n        _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__.useSocketService\n    ];\n});\n_c = TeacherControlPanel;\nvar _c;\n$RefreshReg$(_c, \"TeacherControlPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TeacherControlPanel.tsx\n"));

/***/ })

});