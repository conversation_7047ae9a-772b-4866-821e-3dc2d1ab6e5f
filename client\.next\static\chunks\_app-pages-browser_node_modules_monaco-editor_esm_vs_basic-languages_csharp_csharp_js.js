"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_csharp_csharp_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/csharp/csharp.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/csharp/csharp.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/csharp/csharp.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\#\\$\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".cs\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  keywords: [\n    \"extern\",\n    \"alias\",\n    \"using\",\n    \"bool\",\n    \"decimal\",\n    \"sbyte\",\n    \"byte\",\n    \"short\",\n    \"ushort\",\n    \"int\",\n    \"uint\",\n    \"long\",\n    \"ulong\",\n    \"char\",\n    \"float\",\n    \"double\",\n    \"object\",\n    \"dynamic\",\n    \"string\",\n    \"assembly\",\n    \"is\",\n    \"as\",\n    \"ref\",\n    \"out\",\n    \"this\",\n    \"base\",\n    \"new\",\n    \"typeof\",\n    \"void\",\n    \"checked\",\n    \"unchecked\",\n    \"default\",\n    \"delegate\",\n    \"var\",\n    \"const\",\n    \"if\",\n    \"else\",\n    \"switch\",\n    \"case\",\n    \"while\",\n    \"do\",\n    \"for\",\n    \"foreach\",\n    \"in\",\n    \"break\",\n    \"continue\",\n    \"goto\",\n    \"return\",\n    \"throw\",\n    \"try\",\n    \"catch\",\n    \"finally\",\n    \"lock\",\n    \"yield\",\n    \"from\",\n    \"let\",\n    \"where\",\n    \"join\",\n    \"on\",\n    \"equals\",\n    \"into\",\n    \"orderby\",\n    \"ascending\",\n    \"descending\",\n    \"select\",\n    \"group\",\n    \"by\",\n    \"namespace\",\n    \"partial\",\n    \"class\",\n    \"field\",\n    \"event\",\n    \"method\",\n    \"param\",\n    \"public\",\n    \"protected\",\n    \"internal\",\n    \"private\",\n    \"abstract\",\n    \"sealed\",\n    \"static\",\n    \"struct\",\n    \"readonly\",\n    \"volatile\",\n    \"virtual\",\n    \"override\",\n    \"params\",\n    \"get\",\n    \"set\",\n    \"add\",\n    \"remove\",\n    \"operator\",\n    \"true\",\n    \"false\",\n    \"implicit\",\n    \"explicit\",\n    \"interface\",\n    \"enum\",\n    \"null\",\n    \"async\",\n    \"await\",\n    \"fixed\",\n    \"sizeof\",\n    \"stackalloc\",\n    \"unsafe\",\n    \"nameof\",\n    \"when\"\n  ],\n  namespaceFollows: [\"namespace\", \"using\"],\n  parenFollows: [\"if\", \"for\", \"while\", \"switch\", \"foreach\", \"using\", \"catch\", \"when\"],\n  operators: [\n    \"=\",\n    \"??\",\n    \"||\",\n    \"&&\",\n    \"|\",\n    \"^\",\n    \"&\",\n    \"==\",\n    \"!=\",\n    \"<=\",\n    \">=\",\n    \"<<\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"!\",\n    \"~\",\n    \"++\",\n    \"--\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"%=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"<<=\",\n    \">>=\",\n    \">>\",\n    \"=>\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  // escape sequences\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [\n        /\\@?[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@namespaceFollows\": {\n              token: \"keyword.$0\",\n              next: \"@namespace\"\n            },\n            \"@keywords\": {\n              token: \"keyword.$0\",\n              next: \"@qualified\"\n            },\n            \"@default\": { token: \"identifier\", next: \"@qualified\" }\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [\n        /}/,\n        {\n          cases: {\n            \"$S2==interpolatedstring\": {\n              token: \"string.quote\",\n              next: \"@pop\"\n            },\n            \"$S2==litinterpstring\": {\n              token: \"string.quote\",\n              next: \"@pop\"\n            },\n            \"@default\": \"@brackets\"\n          }\n        }\n      ],\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/[0-9_]*\\.[0-9_]+([eE][\\-+]?\\d+)?[fFdD]?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F_]+/, \"number.hex\"],\n      [/0[bB][01_]+/, \"number.hex\"],\n      // binary: use same theme style as hex\n      [/[0-9_]+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, { token: \"string.quote\", next: \"@string\" }],\n      [/\\$\\@\"/, { token: \"string.quote\", next: \"@litinterpstring\" }],\n      [/\\@\"/, { token: \"string.quote\", next: \"@litstring\" }],\n      [/\\$\"/, { token: \"string.quote\", next: \"@interpolatedstring\" }],\n      // characters\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    qualified: [\n      [\n        /[a-zA-Z_][\\w]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/\\./, \"delimiter\"],\n      [\"\", \"\", \"@pop\"]\n    ],\n    namespace: [\n      { include: \"@whitespace\" },\n      [/[A-Z]\\w*/, \"namespace\"],\n      [/[\\.=]/, \"delimiter\"],\n      [\"\", \"\", \"@pop\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      // [/\\/\\*/,    'comment', '@push' ],    // no nested comments :-(\n      [\"\\\\*/\", \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    litstring: [\n      [/[^\"]+/, \"string\"],\n      [/\"\"/, \"string.escape\"],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    litinterpstring: [\n      [/[^\"{]+/, \"string\"],\n      [/\"\"/, \"string.escape\"],\n      [/{{/, \"string.escape\"],\n      [/}}/, \"string.escape\"],\n      [/{/, { token: \"string.quote\", next: \"root.litinterpstring\" }],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    interpolatedstring: [\n      [/[^\\\\\"{]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/{{/, \"string.escape\"],\n      [/}}/, \"string.escape\"],\n      [/{/, { token: \"string.quote\", next: \"root.interpolatedstring\" }],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/^[ \\t\\v\\f]*#((r)|(load))(?=\\s)/, \"directive.csx\"],\n      [/^[ \\t\\v\\f]*#\\w.*$/, \"namespace.cpp\"],\n      [/[ \\t\\v\\f\\r\\n]+/, \"\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/csharp/csharp.js\n"));

/***/ })

}]);