{"version": 3, "sources": ["../../../locales/my-MM.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"ထား\",\n    \"pasteAsPlaintext\": \"\",\n    \"pasteCharts\": \"\",\n    \"selectAll\": \"အကုန်ရွေး\",\n    \"multiSelect\": \"ရွေးထားသည့်ထဲပုံထည့်\",\n    \"moveCanvas\": \"ကားချပ်ရွှေ့\",\n    \"cut\": \"\",\n    \"copy\": \"ကူး\",\n    \"copyAsPng\": \"PNG အနေဖြင့်ကူး\",\n    \"copyAsSvg\": \"SVG အနေဖြင့်ကူး\",\n    \"copyText\": \"\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"ရှေ့ပို့\",\n    \"sendToBack\": \"နောက်ဆုံးထား\",\n    \"bringToFront\": \"ရှေ့ဆုံးထား\",\n    \"sendBackward\": \"နောက်ပို့\",\n    \"delete\": \"ဖျက်\",\n    \"copyStyles\": \"ပုံစံကူး\",\n    \"pasteStyles\": \"ပုံစံထား\",\n    \"stroke\": \"မျဉ်း\",\n    \"background\": \"နောက်ခံ\",\n    \"fill\": \"ဖြည့်\",\n    \"strokeWidth\": \"မျဉ်းအထူ\",\n    \"strokeStyle\": \"မျဉ်းပုံစံ\",\n    \"strokeStyle_solid\": \"အပြည့်\",\n    \"strokeStyle_dashed\": \"မျဉ်းပြတ်\",\n    \"strokeStyle_dotted\": \"မျဉ်းစက်\",\n    \"sloppiness\": \"သေသပ်မှု\",\n    \"opacity\": \"ထင်ရှားမှု\",\n    \"textAlign\": \"စာသားညှိ\",\n    \"edges\": \"အစွန်း\",\n    \"sharp\": \"ထောင့်ချွန်\",\n    \"round\": \"ထောင့်ဝိုင်း\",\n    \"arrowheads\": \"မြှားခေါင်း\",\n    \"arrowhead_none\": \"ဘာမျှမရှိ\",\n    \"arrowhead_arrow\": \"မြှား\",\n    \"arrowhead_bar\": \"\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"စာလုံးအရွယ်\",\n    \"fontFamily\": \"စာလုံးပုံစံ\",\n    \"addWatermark\": \"\\\"Excalidraw ဖြင့်ဖန်တီးသည်။\\\" စာသားထည့်\",\n    \"handDrawn\": \"လက်ရေး\",\n    \"normal\": \"ပုံမှန်\",\n    \"code\": \"ကုဒ်\",\n    \"small\": \"အသေး\",\n    \"medium\": \"အလတ်\",\n    \"large\": \"အကြီး\",\n    \"veryLarge\": \"ပိုကြီး\",\n    \"solid\": \"အပြည့်\",\n    \"hachure\": \"မျဉ်းစောင်း\",\n    \"zigzag\": \"\",\n    \"crossHatch\": \"ဇကာကွက်\",\n    \"thin\": \"ပါး\",\n    \"bold\": \"ထူ\",\n    \"left\": \"ဘယ်\",\n    \"center\": \"အလယ်\",\n    \"right\": \"ညာ\",\n    \"extraBold\": \"ပိုထူ\",\n    \"architect\": \"ဗိသုကာ\",\n    \"artist\": \"ပန်းချီ\",\n    \"cartoonist\": \"ကာတွန်း\",\n    \"fileTitle\": \"\",\n    \"colorPicker\": \"အရောင်ရွေး\",\n    \"canvasColors\": \"\",\n    \"canvasBackground\": \"ကားချပ်နောက်ခံ\",\n    \"drawingCanvas\": \"ပုံဆွဲကားချပ်\",\n    \"layers\": \"အလွှာများ\",\n    \"actions\": \"လုပ်ဆောင်ချက်များ\",\n    \"language\": \"ဘာသာစကား\",\n    \"liveCollaboration\": \"\",\n    \"duplicateSelection\": \"ပွား\",\n    \"untitled\": \"အမည်မရှိ\",\n    \"name\": \"အမည်\",\n    \"yourName\": \"သင့်အမည်\",\n    \"madeWithExcalidraw\": \"Excalidraw ဖြင့်ဖန်တီးသည်။\",\n    \"group\": \"အုပ်စုဖွဲ့\",\n    \"ungroup\": \"အုပ်စုဖျက်သိမ်း\",\n    \"collaborators\": \"ပူးပေါင်းပါဝင်သူများ\",\n    \"showGrid\": \"\",\n    \"addToLibrary\": \"မှတ်တမ်းတင်\",\n    \"removeFromLibrary\": \"မှတ်တမ်းမှထုတ်\",\n    \"libraryLoadingMessage\": \"မှတ်တမ်းအား တင်သွင်းနေသည်…\",\n    \"libraries\": \"စာကြည့်တိုက်တွင်ရှာဖွေပါ\",\n    \"loadingScene\": \"မြင်ကွင်းဖော်နေသည်…\",\n    \"align\": \"ချိန်ညှိ\",\n    \"alignTop\": \"ထိပ်ညှိ\",\n    \"alignBottom\": \"အခြေညှိ\",\n    \"alignLeft\": \"ဘယ်ညှိ\",\n    \"alignRight\": \"ညာညှိ\",\n    \"centerVertically\": \"ဒေါင်လိုက်အလယ်ညှိ\",\n    \"centerHorizontally\": \"အလျားလိုက်အလယ်ညှိ\",\n    \"distributeHorizontally\": \"အလျားလိုက်\",\n    \"distributeVertically\": \"ထောင်လိုက်\",\n    \"flipHorizontal\": \"\",\n    \"flipVertical\": \"\",\n    \"viewMode\": \"\",\n    \"share\": \"\",\n    \"showStroke\": \"\",\n    \"showBackground\": \"\",\n    \"toggleTheme\": \"\",\n    \"personalLib\": \"\",\n    \"excalidrawLib\": \"\",\n    \"decreaseFontSize\": \"\",\n    \"increaseFontSize\": \"\",\n    \"unbindText\": \"\",\n    \"bindText\": \"\",\n    \"createContainerFromText\": \"\",\n    \"link\": {\n      \"edit\": \"\",\n      \"editEmbed\": \"\",\n      \"create\": \"\",\n      \"createEmbed\": \"\",\n      \"label\": \"\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"\",\n      \"exit\": \"\"\n    },\n    \"elementLock\": {\n      \"lock\": \"\",\n      \"unlock\": \"\",\n      \"lockAll\": \"\",\n      \"unlockAll\": \"\"\n    },\n    \"statusPublished\": \"\",\n    \"sidebarLock\": \"\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"\",\n    \"hint_emptyLibrary\": \"\",\n    \"hint_emptyPrivateLibrary\": \"\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"ကားချပ်ရှင်းလင်း\",\n    \"exportJSON\": \"\",\n    \"exportImage\": \"\",\n    \"export\": \"\",\n    \"copyToClipboard\": \"ကူးယူ\",\n    \"save\": \"\",\n    \"saveAs\": \"ပြောင်းသိမ်း\",\n    \"load\": \"\",\n    \"getShareableLink\": \"မျှဝေရန် လင့်ခ်ရယူ\",\n    \"close\": \"ပိတ်\",\n    \"selectLanguage\": \"ဘာသာစကားရွေးပါ\",\n    \"scrollBackToContent\": \"ကားချပ်ပြန်တည်\",\n    \"zoomIn\": \"ချဲ့\",\n    \"zoomOut\": \"ချုံ့\",\n    \"resetZoom\": \"ပုံမှန်ပြန်ထား\",\n    \"menu\": \"မီနူး\",\n    \"done\": \"ပြီးပြီ\",\n    \"edit\": \"ပြင်ဆင်\",\n    \"undo\": \"ပြန်ထား\",\n    \"redo\": \"ထပ်လုပ်\",\n    \"resetLibrary\": \"\",\n    \"createNewRoom\": \"အခန်းသစ်ဖွဲ့\",\n    \"fullScreen\": \"\",\n    \"darkMode\": \"\",\n    \"lightMode\": \"\",\n    \"zenMode\": \"\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"ဇင်မြင်ကွင်းမှထွက်\",\n    \"cancel\": \"\",\n    \"clear\": \"\",\n    \"remove\": \"\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"\",\n    \"submit\": \"\",\n    \"confirm\": \"\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"ကားချပ်တစ်ခုလုံးရှင်းလင်းပါတော့မည်။ အတည်ပြုပါ။\",\n    \"couldNotCreateShareableLink\": \"မျှဝေရန် လင့်ခ်မရယူနိုင်သေးပါ။\",\n    \"couldNotCreateShareableLinkTooBig\": \"မြင်ကွင်းအရမ်းကြီးနေသဖြင့် မျှဝေရန် လင့်ခ်မရယူနိုင်သေးပါ။\",\n    \"couldNotLoadInvalidFile\": \"လွဲမှားနေသောဖိုင်အား တင်၍မရပါ။\",\n    \"importBackendFailed\": \"Backend မှမလုပ်ဆောင်နိုင်သေးပါ။\",\n    \"cannotExportEmptyCanvas\": \"ကားချပ်အလွတ်အားထုတ်ယူ၍မရပါ။\",\n    \"couldNotCopyToClipboard\": \"\",\n    \"decryptFailed\": \"အချက်အလက်ဖော်ယူ၍မရပါ။\",\n    \"uploadedSecurly\": \"တင်သွင်းအချက်အလက်များအား နှစ်ဘက်စွန်းတိုင်လျှို့ဝှက်စနစ်အသုံးပြု၍လုံခြုံစွာထိန်းသိမ်းထားပါသဖြင့် Excalidraw ဆာဗာနှင့်ဆက်စပ်အဖွဲ့အစည်းများပင်လျှင်မဖတ်ရှုနိုင်ပါ။\",\n    \"loadSceneOverridePrompt\": \"လက်ရှိရေးဆွဲထားသမျှအား ပြင်ပမှတင်သွင်းသောပုံနှင့်အစားထိုးပါမည်။ ဆက်လက်ဆောင်ရွက်လိုပါသလား။\",\n    \"collabStopOverridePrompt\": \"\",\n    \"errorAddingToLibrary\": \"\",\n    \"errorRemovingFromLibrary\": \"\",\n    \"confirmAddLibrary\": \"{{numShapes}} ခုသောပုံသဏ္ဌာန်အားမှတ်တမ်းတင်ပါမည်။ အတည်ပြုပါ။\",\n    \"imageDoesNotContainScene\": \"\",\n    \"cannotRestoreFromImage\": \"ဤပုံဖြင့်မြင်ကွင်းပြန်လည်မရယူနိုင်ပါ။\",\n    \"invalidSceneUrl\": \"\",\n    \"resetLibrary\": \"\",\n    \"removeItemsFromsLibrary\": \"\",\n    \"invalidEncryptionKey\": \"\",\n    \"collabOfflineWarning\": \"\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"\",\n    \"imageInsertError\": \"\",\n    \"fileTooBig\": \"\",\n    \"svgImageInsertError\": \"\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"\",\n    \"cannotResolveCollabServer\": \"\",\n    \"importLibraryError\": \"\",\n    \"collabSaveFailed\": \"\",\n    \"collabSaveFailed_sizeExceeded\": \"\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"ရွေးချယ်\",\n    \"image\": \"\",\n    \"rectangle\": \"စတုဂံ\",\n    \"diamond\": \"စိန်\",\n    \"ellipse\": \"အဝိုင်း\",\n    \"arrow\": \"မြှား\",\n    \"line\": \"မျဉ်း\",\n    \"freedraw\": \"\",\n    \"text\": \"စာသား\",\n    \"library\": \"မှတ်တမ်း\",\n    \"lock\": \"ရွေးချယ်ထားသောကိရိယာကိုသာဆက်သုံး\",\n    \"penMode\": \"\",\n    \"link\": \"\",\n    \"eraser\": \"\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"ကားချပ်လုပ်ဆောင်ချက်\",\n    \"selectedShapeActions\": \"ပုံသဏ္ဌာန်လုပ်ဆောင်ချက်\",\n    \"shapes\": \"ပုံသဏ္ဌာန်များ\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"\",\n    \"linearElement\": \"အမှတ်များချမှတ်ရေးဆွဲရန်ကလစ်နှိပ်ပါ၊ မျဉ်းတစ်ကြောင်းတည်းအတွက် တရွတ်ဆွဲပါ။\",\n    \"freeDraw\": \"ကလစ်နှိပ်၍ တရွတ်ဆွဲပါ၊ ပြီးလျှင်လွှတ်ပါ။\",\n    \"text\": \"မှတ်ချက်။ ။မည်သည့်ကိရိယာရွေးထားသည်ဖြစ်စေ ကလစ်နှစ်ချက်နှိပ်၍စာသားထည့်နိုင်သည်\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"\",\n    \"text_editing\": \"\",\n    \"linearElementMulti\": \"နောက်ဆုံးအမှတ်ပေါ်တွင်ကလစ်နှိပ်ခြင်း၊ Escape (သို့) Enter နှိပ်ခြင်းတို့ဖြင့်အဆုံးသတ်နိုင်\",\n    \"lockAngle\": \"\",\n    \"resize\": \"အချိုးအစားကန့်သတ်ရန် Shift နှင့် ဗဟိုမှချိန်ညှိရန် Alt တို့ကိုနှိပ်ထားနိုင်သည်\",\n    \"resizeImage\": \"\",\n    \"rotate\": \"Shift ကိုနှိပ်ထားခြင်းဖြင့် ထောင့်အလိုက်လှည့်နိုင်သည်\",\n    \"lineEditor_info\": \"\",\n    \"lineEditor_pointSelected\": \"\",\n    \"lineEditor_nothingSelected\": \"\",\n    \"placeImage\": \"\",\n    \"publishLibrary\": \"\",\n    \"bindTextToElement\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"eraserRevert\": \"\",\n    \"firefox_clipboard_write\": \"\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"နမူနာမပြသနိုင်ပါ\",\n    \"canvasTooBig\": \"ကားချပ်အလွန်ကြီးကောင်းကြီးနေနိုင်သည်။\",\n    \"canvasTooBigTip\": \"မှတ်ချက်။ ။ဝေးကွာနေသော ပုံများ၊ စာများအား ပိုမိုနီးကပ်အောင်ရွှေ့ကြည့်ပါ။\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"ချို့ယွင်းမှုဖြစ်ပေါ်ခဲ့သဖြင့် ထပ်မံကြိုးစားကြည့်ရန် <button>စာမျက်နှာအား အသစ်ပြန်လည်ရယူပါ။</button>\",\n    \"clearCanvasMessage\": \"အသစ်ပြန်လည်မရယူနိုင်ပါက ထပ်မံကြိုးစားကြည့်ရန်<button>ကားချပ်အား ရှင်းလင်းပါ။</button>\",\n    \"clearCanvasCaveat\": \" ရေးဆွဲထားသည်များ ဆုံးရှုံးနိုင်သည် \",\n    \"trackedToSentry\": \"ချို့ယွင်းမှုသတ်မှတ်ချက် {{eventId}} အားစနစ်အတွင်းခြေရာကောက်ပြီးပါပြီ။\",\n    \"openIssueMessage\": \"ချို့ယွင်းမှုမှတ်တမ်းတွင် အရေးကြီးအချက်အလက်များပါဝင်မှုမရှိစေရန်အထူးသတိပြုပါသည်။ မပါဝင်ပါက ဆက်လက်ဆောင်ရွက်ရန် <button>ချို့ယွင်းမှုအားခြေရာကောက်ပါ။</button> အောက်ပါအချက်အလက်များအား Github တွင် Issue အနေဖြင့်ဖြည့်သွင်းဖော်ပြပေးပါ။\",\n    \"sceneContent\": \"မြင်ကွင်းပါအချက်အလက်။ ။\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"လက်ရှိမြင်ကွင်းတွင်ပူးပေါင်းရေးဆွဲရန် အခြားသူများအား ဖိတ်ကြားနိုင်သည်။\",\n    \"desc_privacy\": \"နှစ်ဘက်စွန်းတိုင်လျှို့ဝှက်ထားသဖြင့်ရေးဆွဲသမျှအား ဆာဗာပေါ်မှပင်လျှင်ကြည့်ရှုနိုင်မည်မဟုတ်ပါ။ မစိုးရိမ်ပါနှင့်။\",\n    \"button_startSession\": \"ပူးပေါင်းမှုစတင်\",\n    \"button_stopSession\": \"ပူးပေါင်းမှုအဆုံးသတ်\",\n    \"desc_inProgressIntro\": \"တိုက်ရိုက်ပူးပေါင်းရေးဆွဲမှုများပြုလုပ်နေပါသည်။\",\n    \"desc_shareLink\": \"ဤလင့်ခ်အား ပူးပေါင်းရေးဆွဲလိုသူများထံပေးပို့ပါ။ ။ \",\n    \"desc_exitSession\": \"ပူးပေါင်းမှုရပ်တန့်ပါက အဖွဲ့အတွင်းမှထွက်ခွာသွားမည်ဖြစ်သော်လည်း မိမိမြင်ကွင်းတွင်ဆက်လက်ရေးဆွဲနိုင်ပါမည်။ အဖွဲ့အတွင်းကျန်ရှိနေခဲ့သောအခြားပါဝင်သူများသည်လည်း ဆက်လက်ပူးပေါင်းရေးဆွဲနေနိုင်ပါလိမ့်မည်။\",\n    \"shareTitle\": \"\"\n  },\n  \"errorDialog\": {\n    \"title\": \"ချို့ယွင်းချက်\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"\",\n    \"disk_details\": \"\",\n    \"disk_button\": \"\",\n    \"link_title\": \"\",\n    \"link_details\": \"\",\n    \"link_button\": \"\",\n    \"excalidrawplus_description\": \"\",\n    \"excalidrawplus_button\": \"\",\n    \"excalidrawplus_exportError\": \"\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"\",\n    \"click\": \"\",\n    \"deepSelect\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"curvedArrow\": \"\",\n    \"curvedLine\": \"\",\n    \"documentation\": \"\",\n    \"doubleClick\": \"\",\n    \"drag\": \"\",\n    \"editor\": \"\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"\",\n    \"howto\": \"\",\n    \"or\": \"\",\n    \"preventBinding\": \"\",\n    \"tools\": \"\",\n    \"shortcuts\": \"\",\n    \"textFinish\": \"\",\n    \"textNewLine\": \"\",\n    \"title\": \"\",\n    \"view\": \"\",\n    \"zoomToFit\": \"\",\n    \"zoomToSelection\": \"\",\n    \"toggleElementLock\": \"\",\n    \"movePageUpDown\": \"\",\n    \"movePageLeftRight\": \"\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"\"\n  },\n  \"publishDialog\": {\n    \"title\": \"\",\n    \"itemName\": \"\",\n    \"authorName\": \"\",\n    \"githubUsername\": \"\",\n    \"twitterUsername\": \"\",\n    \"libraryName\": \"\",\n    \"libraryDesc\": \"\",\n    \"website\": \"\",\n    \"placeholder\": {\n      \"authorName\": \"\",\n      \"libraryName\": \"\",\n      \"libraryDesc\": \"\",\n      \"githubHandle\": \"\",\n      \"twitterHandle\": \"\",\n      \"website\": \"\"\n    },\n    \"errors\": {\n      \"required\": \"\",\n      \"website\": \"\"\n    },\n    \"noteDescription\": \"\",\n    \"noteGuidelines\": \"\",\n    \"noteLicense\": \"\",\n    \"noteItems\": \"\",\n    \"atleastOneLibItem\": \"\",\n    \"republishWarning\": \"\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"\",\n    \"content\": \"\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"\",\n    \"removeItemsFromLib\": \"\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"ရေးဆွဲထားသောပုံများအား နှစ်ဘက်စွန်းတိုင်လျှို့ဝှက်ထားသဖြင့် Excalidraw ၏ဆာဗာများပင်လျှင်မြင်တွေ့ရမည်မဟုတ်ပါ။\",\n    \"link\": \"\"\n  },\n  \"stats\": {\n    \"angle\": \"ထောင့်\",\n    \"element\": \"\",\n    \"elements\": \"\",\n    \"height\": \"အမြင့်\",\n    \"scene\": \"မြင်ကွင်း\",\n    \"selected\": \"ရွေးချယ်သည်\",\n    \"storage\": \"သိုလှောင်ခန်း\",\n    \"title\": \"အက္ခရာများအတွက်အချက်အလက်များ\",\n    \"total\": \"စုစုပေါင်း\",\n    \"version\": \"\",\n    \"versionCopy\": \"\",\n    \"versionNotAvailable\": \"\",\n    \"width\": \"အကျယ်\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"\",\n    \"copyStyles\": \"\",\n    \"copyToClipboard\": \"\",\n    \"copyToClipboardAsPng\": \"\",\n    \"fileSaved\": \"\",\n    \"fileSavedToFilename\": \"\",\n    \"canvas\": \"\",\n    \"selection\": \"\",\n    \"pasteAsSingleElement\": \"\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"\",\n    \"black\": \"\",\n    \"white\": \"\",\n    \"red\": \"\",\n    \"pink\": \"\",\n    \"grape\": \"\",\n    \"violet\": \"\",\n    \"gray\": \"\",\n    \"blue\": \"\",\n    \"cyan\": \"\",\n    \"teal\": \"\",\n    \"green\": \"\",\n    \"yellow\": \"\",\n    \"orange\": \"\",\n    \"bronze\": \"\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"\",\n      \"center_heading_plus\": \"\",\n      \"menuHint\": \"\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"\",\n      \"center_heading\": \"\",\n      \"toolbarHint\": \"\",\n      \"helpHint\": \"\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"\",\n    \"shades\": \"\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}