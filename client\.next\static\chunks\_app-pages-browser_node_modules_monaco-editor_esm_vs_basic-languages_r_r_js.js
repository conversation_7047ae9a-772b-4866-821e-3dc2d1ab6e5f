"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_r_r_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/r/r.js":
/*!*******************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/r/r.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/r/r.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".r\",\n  roxygen: [\n    \"@alias\",\n    \"@aliases\",\n    \"@assignee\",\n    \"@author\",\n    \"@backref\",\n    \"@callGraph\",\n    \"@callGraphDepth\",\n    \"@callGraphPrimitives\",\n    \"@concept\",\n    \"@describeIn\",\n    \"@description\",\n    \"@details\",\n    \"@docType\",\n    \"@encoding\",\n    \"@evalNamespace\",\n    \"@evalRd\",\n    \"@example\",\n    \"@examples\",\n    \"@export\",\n    \"@exportClass\",\n    \"@exportMethod\",\n    \"@exportPattern\",\n    \"@family\",\n    \"@field\",\n    \"@formals\",\n    \"@format\",\n    \"@import\",\n    \"@importClassesFrom\",\n    \"@importFrom\",\n    \"@importMethodsFrom\",\n    \"@include\",\n    \"@inherit\",\n    \"@inheritDotParams\",\n    \"@inheritParams\",\n    \"@inheritSection\",\n    \"@keywords\",\n    \"@md\",\n    \"@method\",\n    \"@name\",\n    \"@noMd\",\n    \"@noRd\",\n    \"@note\",\n    \"@param\",\n    \"@rawNamespace\",\n    \"@rawRd\",\n    \"@rdname\",\n    \"@references\",\n    \"@return\",\n    \"@S3method\",\n    \"@section\",\n    \"@seealso\",\n    \"@setClass\",\n    \"@slot\",\n    \"@source\",\n    \"@template\",\n    \"@templateVar\",\n    \"@title\",\n    \"@TODO\",\n    \"@usage\",\n    \"@useDynLib\"\n  ],\n  constants: [\n    \"NULL\",\n    \"FALSE\",\n    \"TRUE\",\n    \"NA\",\n    \"Inf\",\n    \"NaN\",\n    \"NA_integer_\",\n    \"NA_real_\",\n    \"NA_complex_\",\n    \"NA_character_\",\n    \"T\",\n    \"F\",\n    \"LETTERS\",\n    \"letters\",\n    \"month.abb\",\n    \"month.name\",\n    \"pi\",\n    \"R.version.string\"\n  ],\n  keywords: [\n    \"break\",\n    \"next\",\n    \"return\",\n    \"if\",\n    \"else\",\n    \"for\",\n    \"in\",\n    \"repeat\",\n    \"while\",\n    \"array\",\n    \"category\",\n    \"character\",\n    \"complex\",\n    \"double\",\n    \"function\",\n    \"integer\",\n    \"list\",\n    \"logical\",\n    \"matrix\",\n    \"numeric\",\n    \"vector\",\n    \"data.frame\",\n    \"factor\",\n    \"library\",\n    \"require\",\n    \"attach\",\n    \"detach\",\n    \"source\"\n  ],\n  special: [\"\\\\n\", \"\\\\r\", \"\\\\t\", \"\\\\b\", \"\\\\a\", \"\\\\f\", \"\\\\v\", \"\\\\'\", '\\\\\"', \"\\\\\\\\\"],\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[{}\\[\\]()]/, \"@brackets\"],\n      { include: \"@operators\" },\n      [/#'$/, \"comment.doc\"],\n      [/#'/, \"comment.doc\", \"@roxygen\"],\n      [/(^#.*$)/, \"comment\"],\n      [/\\s+/, \"white\"],\n      [/[,:;]/, \"delimiter\"],\n      [/@[a-zA-Z]\\w*/, \"tag\"],\n      [\n        /[a-zA-Z]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@constants\": \"constant\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    // Recognize Roxygen comments\n    roxygen: [\n      [\n        /@\\w+/,\n        {\n          cases: {\n            \"@roxygen\": \"tag\",\n            \"@eos\": { token: \"comment.doc\", next: \"@pop\" },\n            \"@default\": \"comment.doc\"\n          }\n        }\n      ],\n      [\n        /\\s+/,\n        {\n          cases: {\n            \"@eos\": { token: \"comment.doc\", next: \"@pop\" },\n            \"@default\": \"comment.doc\"\n          }\n        }\n      ],\n      [/.*/, { token: \"comment.doc\", next: \"@pop\" }]\n    ],\n    // Recognize positives, negatives, decimals, imaginaries, and scientific notation\n    numbers: [\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/-?(\\d*\\.)?\\d+([eE][+\\-]?\\d+)?/, \"number\"]\n    ],\n    // Recognize operators\n    operators: [\n      [/<{1,2}-/, \"operator\"],\n      [/->{1,2}/, \"operator\"],\n      [/%[^%\\s]+%/, \"operator\"],\n      [/\\*\\*/, \"operator\"],\n      [/%%/, \"operator\"],\n      [/&&/, \"operator\"],\n      [/\\|\\|/, \"operator\"],\n      [/<</, \"operator\"],\n      [/>>/, \"operator\"],\n      [/[-+=&|!<>^~*/:$]/, \"operator\"]\n    ],\n    // Recognize strings, including those broken across lines\n    strings: [\n      [/'/, \"string.escape\", \"@stringBody\"],\n      [/\"/, \"string.escape\", \"@dblStringBody\"]\n    ],\n    stringBody: [\n      [\n        /\\\\./,\n        {\n          cases: {\n            \"@special\": \"string\",\n            \"@default\": \"error-token\"\n          }\n        }\n      ],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/./, \"string\"]\n    ],\n    dblStringBody: [\n      [\n        /\\\\./,\n        {\n          cases: {\n            \"@special\": \"string\",\n            \"@default\": \"error-token\"\n          }\n        }\n      ],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/./, \"string\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/r/r.js\n"));

/***/ })

}]);