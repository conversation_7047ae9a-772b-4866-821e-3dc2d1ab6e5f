import "../chunk-XDFCUUT6.js";

// locales/kaa.json
var labels = {
  paste: "Qoy\u0131w",
  pasteAsPlaintext: "\xC1piway\u0131 tekst retinde qoy\u0131w",
  pasteCharts: "Diagrammalard\u0131 qoy\u0131w",
  selectAll: "Barl\u0131\u01F5\u0131n ta\u0144law",
  multiSelect: "",
  moveCanvas: "",
  cut: "Q\u0131y\u0131w",
  copy: "K\xF3shirip al\u0131w",
  copyAsPng: "Almas\u0131w buferine PNG retinde k\xF3shirip al\u0131w",
  copyAsSvg: "Almas\u0131w buferine SVG retinde k\xF3shirip al\u0131w",
  copyText: "Almas\u0131w buferine tekst retinde k\xF3shirip al\u0131w",
  copySource: "",
  convertToCode: "",
  bringForward: "",
  sendToBack: "",
  bringToFront: "",
  sendBackward: "",
  delete: "\xD3shiriw",
  copyStyles: "",
  pasteStyles: "",
  stroke: "Jiyek",
  background: "Fon",
  fill: "",
  strokeWidth: "",
  strokeStyle: "",
  strokeStyle_solid: "",
  strokeStyle_dashed: "",
  strokeStyle_dotted: "",
  sloppiness: "",
  opacity: "",
  textAlign: "",
  edges: "Q\u0131rlar",
  sharp: "",
  round: "",
  arrowheads: "",
  arrowhead_none: "Joq",
  arrowhead_arrow: "Jebe",
  arrowhead_bar: "",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "Shrift \xF3lshemi",
  fontFamily: "Shrift toplam\u0131",
  addWatermark: "",
  handDrawn: "",
  normal: "",
  code: "Kod",
  small: "",
  medium: "Ortasha",
  large: "\xDAlken",
  veryLarge: "J\xFAd\xE1 \xFAlken",
  solid: "",
  hachure: "",
  zigzag: "Zigzag",
  crossHatch: "",
  thin: "Ji\u0144ishke",
  bold: "Qal\u0131\u0144",
  left: "",
  center: "",
  right: "",
  extraBold: "",
  architect: "",
  artist: "S\xFAwretshi",
  cartoonist: "",
  fileTitle: "Fayl atamas\u0131",
  colorPicker: "Re\u0144di ta\u0144law",
  canvasColors: "",
  canvasBackground: "",
  drawingCanvas: "",
  layers: "Qatlamlar",
  actions: "H\xE1reketler",
  language: "Til",
  liveCollaboration: "",
  duplicateSelection: "Nusqa",
  untitled: "Atamas\u0131z",
  name: "Atamas\u0131",
  yourName: "At\u0131\u0144\u0131z",
  madeWithExcalidraw: "Excalidraw j\xE1rdeminde islengen",
  group: "",
  ungroup: "",
  collaborators: "Qatnas\u0131wsh\u0131lar",
  showGrid: "",
  addToLibrary: "Kitapxana\u01F5a qos\u0131w",
  removeFromLibrary: "Kitapxanadan al\u0131p taslaw",
  libraryLoadingMessage: "Kitapxana j\xFAklenbekte\u2026",
  libraries: "Kitapxanalard\u0131 k\xF3riw",
  loadingScene: "Saxna j\xFAklenbekte\u2026",
  align: "",
  alignTop: "",
  alignBottom: "",
  alignLeft: "",
  alignRight: "",
  centerVertically: "",
  centerHorizontally: "",
  distributeHorizontally: "",
  distributeVertically: "",
  flipHorizontal: "",
  flipVertical: "",
  viewMode: "K\xF3riw rejimi",
  share: "B\xF3lisiw",
  showStroke: "",
  showBackground: "",
  toggleTheme: "Teman\u0131 \xF3zgertiw",
  personalLib: "Jeke kitapxana",
  excalidrawLib: "Excalidraw kitapxanas\u0131",
  decreaseFontSize: "Shrift \xF3lshemin kishireytiw",
  increaseFontSize: "Shrift \xF3lshemin \xFAlkeytiw",
  unbindText: "",
  bindText: "",
  createContainerFromText: "",
  link: {
    edit: "Siltemeni \xF3zgertiw",
    editEmbed: "",
    create: "Siltemeni jarat\u0131w",
    createEmbed: "",
    label: "Silteme",
    labelEmbed: "",
    empty: ""
  },
  lineEditor: {
    edit: "Qatard\u0131 \xF3zgertiw",
    exit: "Qatard\u0131 \xF3zgertiw redaktor\u0131nan sh\u0131\u01F5\u0131w"
  },
  elementLock: {
    lock: "Qul\u0131plaw",
    unlock: "Qul\u0131ptan sh\u0131\u01F5ar\u0131w",
    lockAll: "Barl\u0131\u01F5\u0131n qul\u0131plaw",
    unlockAll: "Barl\u0131\u01F5\u0131n qul\u0131ptan sh\u0131\u01F5ar\u0131w"
  },
  statusPublished: "",
  sidebarLock: "",
  selectAllElementsInFrame: "",
  removeAllElementsFromFrame: "",
  eyeDropper: "",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "",
  hint_emptyLibrary: "",
  hint_emptyPrivateLibrary: ""
};
var buttons = {
  clearReset: "",
  exportJSON: "",
  exportImage: "S\xFAwretti eksportlaw...",
  export: "Retinde saqlaw...",
  copyToClipboard: "Almas\u0131w buferine k\xF3shirip al\u0131nd\u0131",
  save: "\xC1meldegi fayl\u01F5a saqlaw",
  saveAs: "Retinde saqlaw",
  load: "Ash\u0131w",
  getShareableLink: "",
  close: "Jab\u0131w",
  selectLanguage: "Tildi ta\u0144law",
  scrollBackToContent: "",
  zoomIn: "",
  zoomOut: "",
  resetZoom: "",
  menu: "Menyu",
  done: "Tay\u0131n",
  edit: "\xD3zgertiw",
  undo: "",
  redo: "",
  resetLibrary: "",
  createNewRoom: "",
  fullScreen: "Tol\u0131q ekran",
  darkMode: "Qara\u0144\u01F5\u0131 tema",
  lightMode: "Jaqt\u0131 tema",
  zenMode: "",
  objectsSnapMode: "",
  exitZenMode: "",
  cancel: "Biykarlaw",
  clear: "Tazalaw",
  remove: "\xD3shiriw",
  embed: "",
  publishLibrary: "Jariyalaw",
  submit: "Jiberiw",
  confirm: "Tast\u0131y\u0131qlaw",
  embeddableInteractionButton: ""
};
var alerts = {
  clearReset: "",
  couldNotCreateShareableLink: "",
  couldNotCreateShareableLinkTooBig: "",
  couldNotLoadInvalidFile: "",
  importBackendFailed: "",
  cannotExportEmptyCanvas: "",
  couldNotCopyToClipboard: "Almas\u0131w buferine k\xF3shirip al\u0131w \xE1melge aspad\u0131.",
  decryptFailed: "",
  uploadedSecurly: "",
  loadSceneOverridePrompt: "",
  collabStopOverridePrompt: "",
  errorAddingToLibrary: "",
  errorRemovingFromLibrary: "",
  confirmAddLibrary: "",
  imageDoesNotContainScene: "",
  cannotRestoreFromImage: "",
  invalidSceneUrl: "",
  resetLibrary: "",
  removeItemsFromsLibrary: "",
  invalidEncryptionKey: "",
  collabOfflineWarning: ""
};
var errors = {
  unsupportedFileType: "",
  imageInsertError: "",
  fileTooBig: "",
  svgImageInsertError: "",
  failedToFetchImage: "",
  invalidSVGString: "Jarams\u0131z SVG.",
  cannotResolveCollabServer: "",
  importLibraryError: "Kitapxanan\u0131 j\xFAklew \xE1melge aspad\u0131",
  collabSaveFailed: "",
  collabSaveFailed_sizeExceeded: "",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "",
    line2: "",
    line3: "",
    line4: ""
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "",
    image: ""
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "",
  image: "S\xFAwret qoy\u0131w",
  rectangle: "T\xF3rt m\xFAyeshlik",
  diamond: "",
  ellipse: "",
  arrow: "",
  line: "S\u0131z\u0131q",
  freedraw: "S\u0131z\u0131w",
  text: "Tekst",
  library: "Kitapxana",
  lock: "",
  penMode: "",
  link: "",
  eraser: "\xD3shirgish",
  frame: "",
  magicframe: "",
  embeddable: "",
  laser: "",
  hand: "",
  extraTools: "",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "",
  selectedShapeActions: "",
  shapes: "Figuralar"
};
var hints = {
  canvasPanning: "",
  linearElement: "",
  freeDraw: "",
  text: "",
  embeddable: "",
  text_selected: "",
  text_editing: "",
  linearElementMulti: "",
  lockAngle: "",
  resize: "",
  resizeImage: "",
  rotate: "",
  lineEditor_info: "",
  lineEditor_pointSelected: "",
  lineEditor_nothingSelected: "",
  placeImage: "",
  publishLibrary: "",
  bindTextToElement: "Tekst qos\u0131w ush\u0131n Enter t\xFAymesin bas\u0131\u0144",
  deepBoxSelect: "",
  eraserRevert: "",
  firefox_clipboard_write: "",
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "Ald\u0131nnan k\xF3riwdi k\xF3rsetiw m\xFAmkin emes",
  canvasTooBig: "",
  canvasTooBigTip: ""
};
var errorSplash = {
  headingMain: "",
  clearCanvasMessage: "",
  clearCanvasCaveat: "",
  trackedToSentry: "",
  openIssueMessage: "",
  sceneContent: ""
};
var roomDialog = {
  desc_intro: "",
  desc_privacy: "",
  button_startSession: "",
  button_stopSession: "",
  desc_inProgressIntro: "",
  desc_shareLink: "",
  desc_exitSession: "",
  shareTitle: ""
};
var errorDialog = {
  title: "Q\xE1telik"
};
var exportDialog = {
  disk_title: "Diskke saqlaw",
  disk_details: "",
  disk_button: "Fayl\u01F5a saqlaw",
  link_title: "",
  link_details: "",
  link_button: "Siltemege eksportlaw",
  excalidrawplus_description: "",
  excalidrawplus_button: "Eksportlaw",
  excalidrawplus_exportError: ""
};
var helpDialog = {
  blog: "Bizi\u0144 blogt\u0131 oq\u0131\u0144",
  click: "bas\u0131w",
  deepSelect: "",
  deepBoxSelect: "",
  curvedArrow: "",
  curvedLine: "",
  documentation: "H\xFAjjetshilik",
  doubleClick: "",
  drag: "",
  editor: "Redaktor",
  editLineArrowPoints: "",
  editText: "",
  github: "",
  howto: "",
  or: "yamasa",
  preventBinding: "",
  tools: "\xC1sbaplar",
  shortcuts: "",
  textFinish: "",
  textNewLine: "",
  title: "J\xE1rdem",
  view: "K\xF3riw",
  zoomToFit: "",
  zoomToSelection: "",
  toggleElementLock: "",
  movePageUpDown: "",
  movePageLeftRight: ""
};
var clearCanvasDialog = {
  title: ""
};
var publishDialog = {
  title: "",
  itemName: "",
  authorName: "Avtor at\u0131",
  githubUsername: "GitHub paydalan\u0131wsh\u0131 at\u0131",
  twitterUsername: "Twitter paydalan\u0131wsh\u0131 at\u0131",
  libraryName: "Kitapxana atamas\u0131",
  libraryDesc: "",
  website: "Veb-sayt",
  placeholder: {
    authorName: "At\u0131\u0144\u0131z yamasa paydalan\u0131wsh\u0131 at\u0131",
    libraryName: "Kitapxana\u0144\u0131z atamas\u0131",
    libraryDesc: "",
    githubHandle: "",
    twitterHandle: "",
    website: "Jeke veb-sayt\u0131\u0144\u0131z yamasa basqa saytqa silteme (m\xE1jb\xFAriy emes)"
  },
  errors: {
    required: "M\xE1jb\xFAriy",
    website: "Jaraml\u0131 URL m\xE1nzil kirgizi\u0144"
  },
  noteDescription: "",
  noteGuidelines: "",
  noteLicense: "",
  noteItems: "",
  atleastOneLibItem: "",
  republishWarning: ""
};
var publishSuccessDialog = {
  title: "Kitapxana jiberildi",
  content: ""
};
var confirmDialog = {
  resetLibrary: "Kitapxanan\u0131 qayta ornat\u0131w",
  removeItemsFromLib: ""
};
var imageExportDialog = {
  header: "S\xFAwretti eksportlaw",
  label: {
    withBackground: "Fon",
    onlySelected: "",
    darkMode: "Qara\u0144\u01F5\u0131 tema",
    embedScene: "",
    scale: "K\xF3lem",
    padding: ""
  },
  tooltip: {
    embedScene: ""
  },
  title: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "Almas\u0131w buferine k\xF3shirip al\u0131w"
  }
};
var encrypted = {
  tooltip: "",
  link: ""
};
var stats = {
  angle: "",
  element: "Element",
  elements: "Elementler",
  height: "",
  scene: "Saxna",
  selected: "Ta\u0144land\u0131",
  storage: "",
  title: "",
  total: "",
  version: "Versiya",
  versionCopy: "K\xF3shirip al\u0131w ush\u0131n bas\u0131\u0144",
  versionNotAvailable: "",
  width: "Eni"
};
var toast = {
  addedToLibrary: "Kitapxana\u01F5a qos\u0131ld\u0131",
  copyStyles: "",
  copyToClipboard: "Almas\u0131w buferine k\xF3shirip al\u0131nd\u0131.",
  copyToClipboardAsPng: "",
  fileSaved: "Fayl saqland\u0131.",
  fileSavedToFilename: "{filename} saqland\u0131",
  canvas: "",
  selection: "",
  pasteAsSingleElement: "",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "",
  black: "Qara",
  white: "Aq",
  red: "Q\u0131z\u0131l",
  pink: "Q\u0131z\u01F5\u0131lt",
  grape: "",
  violet: "Q\u0131z\u01F5\u0131lt k\xF3k",
  gray: "",
  blue: "K\xF3k",
  cyan: "K\xF3k aspan",
  teal: "Piruza",
  green: "Jas\u0131l",
  yellow: "Sar\u0131",
  orange: "Q\u0131z\u01F5\u0131lt sar\u0131",
  bronze: ""
};
var welcomeScreen = {
  app: {
    center_heading: "",
    center_heading_plus: "Excalidraw+ ge \xF3tiwdi q\xE1leysiz be?",
    menuHint: "Eksportlaw, sazlawlar, tiller, ..."
  },
  defaults: {
    menuHint: "Eksportlaw, sazlawlar h\xE1m basqa...",
    center_heading: "Diagrammalar. \xC1piway\u0131.",
    toolbarHint: "",
    helpHint: ""
  }
};
var colorPicker = {
  mostUsedCustomColors: "K\xF3p qollan\u0131latu\u01F5\u0131n arnawl\u0131 re\u0144ler",
  colors: "Re\u0144ler",
  shades: "",
  hexCode: "",
  noShades: ""
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "S\xFAwret retinde eksportlaw",
      button: "S\xFAwret retinde eksportlaw",
      description: ""
    },
    saveToDisk: {
      title: "Diskke saqlaw",
      button: "Diskke saqlaw",
      description: ""
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "",
      description: ""
    }
  },
  modal: {
    loadFromFile: {
      title: "Fayldan j\xFAklew",
      button: "Fayldan j\xFAklew",
      description: ""
    },
    shareableLink: {
      title: "Siltemeden j\xFAklew",
      button: "",
      description: ""
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var kaa_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  kaa_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=kaa-6BPSNM3R.js.map
