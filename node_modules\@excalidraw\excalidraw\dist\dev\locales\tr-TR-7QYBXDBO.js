import "../chunk-XDFCUUT6.js";

// locales/tr-TR.json
var labels = {
  paste: "Yap\u0131\u015Ft\u0131r",
  pasteAsPlaintext: "D\xFCz metin olarak yap\u0131\u015Ft\u0131r",
  pasteCharts: "Grafikleri yap\u0131\u015Ft\u0131r",
  selectAll: "T\xFCm\xFCn\xFC se\xE7",
  multiSelect: "Se\xE7ime \xF6ge ekle",
  moveCanvas: "Tuvali ta\u015F\u0131",
  cut: "Kes",
  copy: "Kopyala",
  copyAsPng: "Panoya PNG olarak kopyala",
  copyAsSvg: "Panoya SVG olarak kopyala",
  copyText: "Panoya metin olarak kopyala",
  copySource: "",
  convertToCode: "",
  bringForward: "Bir \xF6ne getir",
  sendToBack: "Arkaya g\xF6nder",
  bringToFront: "En \xF6ne getir",
  sendBackward: "Bir geriye g\xF6nder",
  delete: "Sil",
  copyStyles: "Stilleri kopyala",
  pasteStyles: "Stilleri yap\u0131\u015Ft\u0131r",
  stroke: "Vurgu",
  background: "Arka plan",
  fill: "Doldur",
  strokeWidth: "Kontur geni\u015Fli\u011Fi",
  strokeStyle: "Kontur stili",
  strokeStyle_solid: "Dolu",
  strokeStyle_dashed: "Kesik \xE7izgili",
  strokeStyle_dotted: "Noktal\u0131",
  sloppiness: "\xDCst\xFCn k\xF6r\xFCl\xFCk",
  opacity: "Opakl\u0131k",
  textAlign: "Metin hizala",
  edges: "Kenarlar",
  sharp: "Keskin",
  round: "Yuvarlak",
  arrowheads: "Ok u\xE7lar\u0131",
  arrowhead_none: "Yok",
  arrowhead_arrow: "Ok",
  arrowhead_bar: "\xC7izgi",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "\xDC\xE7gen",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "Yaz\u0131 tipi boyutu",
  fontFamily: "Yaz\u0131 tipi ailesi",
  addWatermark: '"Excalidraw ile yap\u0131ld\u0131" yaz\u0131s\u0131n\u0131 ekle',
  handDrawn: "El-yaz\u0131s\u0131",
  normal: "Normal",
  code: "Kod",
  small: "K\xFC\xE7\xFCk",
  medium: "Orta",
  large: "B\xFCy\xFCk",
  veryLarge: "\xC7ok geni\u015F",
  solid: "Dolu",
  hachure: "Taral\u0131",
  zigzag: "Zikzak",
  crossHatch: "\xC7apraz-taral\u0131",
  thin: "\u0130nce",
  bold: "Kal\u0131n",
  left: "Sol",
  center: "Ortala",
  right: "Sa\u011F",
  extraBold: "Ekstra kal\u0131n",
  architect: "Mimar",
  artist: "Sanat\xE7\u0131",
  cartoonist: "Karikat\xFCrist",
  fileTitle: "Dosya ad\u0131",
  colorPicker: "Renk se\xE7ici",
  canvasColors: "Tuvalin \xFCzerinde kullan\u0131ld\u0131",
  canvasBackground: "Tuval arka plan\u0131",
  drawingCanvas: "\xC7izim tuvali",
  layers: "Katmanlar",
  actions: "Eylemler",
  language: "Dil",
  liveCollaboration: "Canl\u0131 ortak \xE7al\u0131\u015Fma alan\u0131...",
  duplicateSelection: "\xC7o\u011Falt",
  untitled: "Ads\u0131z",
  name: "\u0130sim",
  yourName: "\u0130sminiz",
  madeWithExcalidraw: "Excalidraw ile yap\u0131ld\u0131",
  group: "Se\xE7imi grup yap",
  ungroup: "Se\xE7ilen grubu da\u011F\u0131t",
  collaborators: "Ortaklar",
  showGrid: "Izgaray\u0131 g\xF6ster",
  addToLibrary: "K\xFCt\xFCphaneye ekle",
  removeFromLibrary: "K\xFCt\xFCphaneden kald\u0131r",
  libraryLoadingMessage: "K\xFCt\xFCphane y\xFCkleniyor\u2026",
  libraries: "K\xFCt\xFCphanelere g\xF6zat",
  loadingScene: "Sahne y\xFCkleniyor\u2026",
  align: "Hizala",
  alignTop: "Yukar\u0131 hizala",
  alignBottom: "A\u015Fa\u011F\u0131 hizala",
  alignLeft: "Sola hizala",
  alignRight: "Sa\u011Fa hizala",
  centerVertically: "Dikeyde ortala",
  centerHorizontally: "Yatayda ortala",
  distributeHorizontally: "Yatay da\u011F\u0131t",
  distributeVertically: "Dikey da\u011F\u0131t",
  flipHorizontal: "Yatay d\xF6nd\xFCr",
  flipVertical: "Dikey d\xF6nd\xFCr",
  viewMode: "G\xF6r\xFCn\xFCm modu",
  share: "Payla\u015F",
  showStroke: "Kontur i\xE7in renk se\xE7iciyi g\xF6ster",
  showBackground: "Arkaplan i\xE7in renk se\xE7iciyi g\xF6ster",
  toggleTheme: "Temay\u0131 etkinle\u015Ftir/devre d\u0131\u015F\u0131 b\u0131rak",
  personalLib: "Ki\u015Fisel Kitapl\u0131k",
  excalidrawLib: "Excalidraw Kitapl\u0131\u011F\u0131",
  decreaseFontSize: "Yaz\u0131 Tipi Boyutunu K\xFC\xE7\xFClt",
  increaseFontSize: "Yaz\u0131 Tipi Boyutunu B\xFCy\xFClt",
  unbindText: "Metni \xE7\xF6z",
  bindText: "Metni ta\u015F\u0131y\u0131c\u0131ya ba\u011Fla",
  createContainerFromText: "Metni bile\u015Fen i\xE7inde sar",
  link: {
    edit: "Ba\u011Flant\u0131y\u0131 d\xFCzenle",
    editEmbed: "Ba\u011Flant\u0131y\u0131 d\xFCzenle & yerle\u015Ftir",
    create: "Ba\u011Flant\u0131 olu\u015Ftur",
    createEmbed: "Ba\u011Flant\u0131 olu\u015Ftur & yerle\u015Ftir",
    label: "Ba\u011Flant\u0131",
    labelEmbed: "Ba\u011Flant\u0131 & yerle\u015Ftirme",
    empty: "Herhangi bir ba\u011Flant\u0131 olu\u015Fturulmad\u0131"
  },
  lineEditor: {
    edit: "\xC7izgiyi d\xFCzenle",
    exit: "\xC7izgi d\xFCzenlemeden \xE7\u0131k"
  },
  elementLock: {
    lock: "Kilitle",
    unlock: "Kilidi Kald\u0131r",
    lockAll: "Hepsini kilitle",
    unlockAll: "Hepsinin kilidini kald\u0131r"
  },
  statusPublished: "Yay\u0131nland\u0131",
  sidebarLock: "Kenar \xE7ubu\u011Fu a\xE7\u0131k kals\u0131n",
  selectAllElementsInFrame: "\xC7er\xE7evedeki t\xFCm bile\u015Fenleri se\xE7",
  removeAllElementsFromFrame: "\xC7er\xE7evedeki t\xFCm bile\u015Fenleri sil",
  eyeDropper: "Tuvalden renk se\xE7",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "\xD6\u011Fe eklenmedi...",
  hint_emptyLibrary: "\xD6\u011Felerden birini eklemek i\xE7in \xF6\u011Feyi se\xE7iniz veya a\u015Fa\u011F\u0131daki genel k\xFCt\xFCphaneden \xF6\u011Feleri ekleyin.",
  hint_emptyPrivateLibrary: "Tuvalden bir eleman se\xE7erek sayfaya ekleyin."
};
var buttons = {
  clearReset: "Tuvali s\u0131f\u0131rla",
  exportJSON: "Dosyaya aktar",
  exportImage: "Resimleri d\u0131\u015Fa aktar...",
  export: "\u015Euraya kaydet...",
  copyToClipboard: "Panoya kopyala",
  save: "Ge\xE7erli dosyaya kaydet",
  saveAs: "Farkl\u0131 kaydet",
  load: "A\xE7",
  getShareableLink: "Payla\u015F\u0131labilir ba\u011Flant\u0131 al",
  close: "Kapat",
  selectLanguage: "Dil se\xE7in",
  scrollBackToContent: "\u0130\xE7eri\u011Fe geri d\xF6n",
  zoomIn: "Yak\u0131nla\u015Ft\u0131r",
  zoomOut: "Uzakla\u015Ft\u0131r",
  resetZoom: "Yak\u0131nla\u015Ft\u0131rmay\u0131 s\u0131f\u0131rla",
  menu: "Men\xFC",
  done: "Tamam",
  edit: "D\xFCzenle",
  undo: "Geri Al",
  redo: "Yeniden yap",
  resetLibrary: "K\xFCt\xFCphaneyi s\u0131f\u0131rla",
  createNewRoom: "Yeni oda olu\u015Ftur",
  fullScreen: "Tam ekran",
  darkMode: "Koyu tema",
  lightMode: "A\xE7\u0131k tema",
  zenMode: "Zen modu",
  objectsSnapMode: "Nesnelere hizala",
  exitZenMode: "Zen modundan \xE7\u0131k",
  cancel: "\u0130ptal",
  clear: "Temizle",
  remove: "Kald\u0131r",
  embed: "",
  publishLibrary: "Yay\u0131nla",
  submit: "G\xF6nder",
  confirm: "Onayla",
  embeddableInteractionButton: "Etkile\u015Fime girmek i\xE7in t\u0131kla"
};
var alerts = {
  clearReset: "Tuvalin tamam\u0131 temizlenecek. Emin misiniz?",
  couldNotCreateShareableLink: "Payla\u015F\u0131labilir ba\u011Flant\u0131 olu\u015Fturulamad\u0131.",
  couldNotCreateShareableLinkTooBig: "Payla\u015F\u0131labilir ba\u011Flant\u0131 olu\u015Fturulamad\u0131: sahne \xE7ok b\xFCy\xFCk",
  couldNotLoadInvalidFile: "Bilinmeyen dosya y\xFCklenemiyor",
  importBackendFailed: "Sunucudan i\xE7e aktarma ba\u015Far\u0131s\u0131z.",
  cannotExportEmptyCanvas: "Bo\u015F tuval d\u0131\u015Far\u0131ya aktar\u0131lamaz.",
  couldNotCopyToClipboard: "Panoya kopyalanam\u0131yor.",
  decryptFailed: "\u015Eifrelenmi\u015F veri \xE7\xF6z\xFCmlenemedi.",
  uploadedSecurly: "Y\xFCkleme u\xE7tan uca \u015Fifreleme ile korunmaktad\u0131r. Excalidraw sunucusu ve \xFC\xE7\xFCnc\xFCl \u015Fah\u0131slar i\xE7eri\u011Fi okuyamayacakt\u0131r.",
  loadSceneOverridePrompt: "Harici \xE7izimler y\xFCklemek mevcut olan i\xE7eri\u011Fi de\u011Fi\u015Ftirecektir. Devam etmek istiyor musunuz?",
  collabStopOverridePrompt: "Oturumu sonland\u0131rmak daha \xF6nceki, yerel olarak kaydedilmi\u015F \xE7izimin \xFCzerine kaydedilmesine sebep olacak. Emin misiniz?\n\n(Yerel \xE7iziminizi kaybetmemek i\xE7in taray\u0131c\u0131 sekmesini kapatabilirsiniz.)",
  errorAddingToLibrary: "\xD6\u011Fe k\xFCt\xFCphaneye eklenemedi",
  errorRemovingFromLibrary: "\xD6\u011Fe k\xFCt\xFCphaneden silinemedi",
  confirmAddLibrary: "Bu, kitapl\u0131\u011F\u0131n\u0131za {{numShapes}} tane \u015Fekil ekleyecek. Emin misiniz?",
  imageDoesNotContainScene: "Bu g\xF6r\xFCnt\xFC herhangi bir sahne verisi i\xE7ermiyor gibi g\xF6r\xFCn\xFCyor. D\u0131\u015Fa aktarma s\u0131ras\u0131nda sahne yerle\u015Ftirmeyi etkinle\u015Ftirdiniz mi?",
  cannotRestoreFromImage: "Sahne bu resim dosyas\u0131ndan geri y\xFCklenemedi",
  invalidSceneUrl: "Verilen ba\u011Flant\u0131dan \xE7al\u0131\u015Fma alan\u0131 y\xFCklenemedi. Dosya bozuk olabilir veya ge\xE7erli bir Excalidraw JSON verisi bulundurmuyor olabilir.",
  resetLibrary: "Bu i\u015Flem k\xFCt\xFCphanenizi s\u0131f\u0131rlayacak. Emin misiniz?",
  removeItemsFromsLibrary: "{{count}} \xF6\u011Fe(ler) kitapl\u0131ktan kald\u0131r\u0131ls\u0131n m\u0131?",
  invalidEncryptionKey: "\u015Eifreleme anahtar\u0131 22 karakter olmal\u0131. Canl\u0131 i\u015Fbirli\u011Fi devre d\u0131\u015F\u0131 b\u0131rak\u0131ld\u0131.",
  collabOfflineWarning: "\u0130nternet ba\u011Flant\u0131s\u0131 bulunamad\u0131. De\u011Fi\u015Fiklikleriniz kaydedilmeyecek!"
};
var errors = {
  unsupportedFileType: "Desteklenmeyen dosya t\xFCr\xFC.",
  imageInsertError: "G\xF6rsel eklenemedi. Daha sonra tekrar deneyin...",
  fileTooBig: "Dosya \xE7ok b\xFCy\xFCk. \u0130zin verilen maksimum boyut {{maxSize}}.",
  svgImageInsertError: "SVG resmi eklenemedi. SVG i\u015Faretlemesi ge\xE7ersiz g\xF6r\xFCn\xFCyor.",
  failedToFetchImage: "",
  invalidSVGString: "Ge\xE7ersiz SVG.",
  cannotResolveCollabServer: "\u0130\u015F birli\u011Fi sunucusuna ba\u011Flan\u0131lam\u0131yor. L\xFCtfen sayfay\u0131 yenileyip tekrar deneyin.",
  importLibraryError: "K\xFCt\xFCphane y\xFCklenemedi",
  collabSaveFailed: "Backend veritaban\u0131na kaydedilemedi. E\u011Fer problem devam ederse, \xE7al\u0131\u015Fman\u0131z\u0131 korumak i\xE7in dosyay\u0131 yerel olarak kaydetmelisiniz.",
  collabSaveFailed_sizeExceeded: "Backend veritaban\u0131na kaydedilemedi; tuval \xE7ok b\xFCy\xFCk. \xC7al\u0131\u015Fman\u0131z\u0131 korumak i\xE7in dosyay\u0131 yerel olarak kaydetmelisiniz.",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "",
    line2: "",
    line3: "",
    line4: ""
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "",
    image: "Resimleri k\xFCt\xFCphaneye ekleme deste\u011Fi yak\u0131nda geliyor!"
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "Se\xE7me",
  image: "G\xF6rsel ekle",
  rectangle: "Dikd\xF6rtgen",
  diamond: "Elmas",
  ellipse: "Elips",
  arrow: "Ok",
  line: "\xC7izgi",
  freedraw: "\xC7iz",
  text: "Yaz\u0131",
  library: "K\xFCt\xFCphane",
  lock: "Se\xE7ilen arac\u0131 \xE7izimden sonra aktif tut",
  penMode: "Kalem modu - dokunmay\u0131 engelle",
  link: "Se\xE7ilen \u015Fekil i\xE7in ba\u011Flant\u0131 Ekle/G\xFCncelle",
  eraser: "Silgi",
  frame: "\xC7er\xE7eve arac\u0131",
  magicframe: "",
  embeddable: "Web Yerle\u015Ftirme",
  laser: "Lazer i\u015Faret\xE7isi",
  hand: "",
  extraTools: "Daha fazla ara\xE7",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "Tuval eylemleri",
  selectedShapeActions: "Se\xE7ilen \u015Fekil aksiyonlar\u0131",
  shapes: "\u015Eekiller"
};
var hints = {
  canvasPanning: "",
  linearElement: "Birden fazla nokta i\xE7in t\u0131klay\u0131n, tek \xE7izgi i\xE7in s\xFCr\xFCkleyin",
  freeDraw: "T\u0131kla ve s\xFCr\xFCkle, bitirdi\u011Finde serbest b\u0131rak",
  text: "\u0130pucu: se\xE7me arac\u0131yla herhangi bir yere \xE7ift t\u0131klayarak da yaz\u0131 ekleyebilirsin",
  embeddable: "Web sitesi yerle\u015Ftirmek i\xE7in s\xFCr\xFCkle b\u0131rak",
  text_selected: "Metni d\xFCzenlemek i\xE7in \xE7ift t\u0131klay\u0131n veya ENTER'a bas\u0131n",
  text_editing: "D\xFCzenlemeyi bitirmek i\xE7in ESC veya Ctrl/Cmd+ENTER tu\u015Flar\u0131na bas\u0131n",
  linearElementMulti: "Bitirmek i\xE7in son noktaya t\u0131klay\u0131n ya da Escape veya Enter tu\u015Funa bas\u0131n",
  lockAngle: "SHIFT tu\u015Funa bas\u0131l\u0131 tutarak a\xE7\u0131y\u0131 koruyabilirsiniz",
  resize: "Yeniden boyutland\u0131r\u0131rken SHIFT tu\u015Funu bas\u0131l\u0131 tutarak oranlar\u0131 s\u0131n\u0131rlayabilirsiniz,\nmerkezden yeniden boyutland\u0131rmak i\xE7in ALT tu\u015Funu bas\u0131l\u0131 tutun",
  resizeImage: "SHIFT'e bas\u0131l\u0131 tutarak serbest\xE7e yeniden boyutland\u0131rabilirsiniz, merkezden yeniden boyutland\u0131rmak i\xE7in ALT tu\u015Funu bas\u0131l\u0131 tutun",
  rotate: "D\xF6nd\xFCr\xFCrken SHIFT tu\u015Funa bas\u0131l\u0131 tutarak a\xE7\u0131lar\u0131 koruyabilirsiniz",
  lineEditor_info: "Puanlar\u0131 d\xFCzenlemek i\xE7in ctrl veya cmd tu\u015Funa bas\u0131l\u0131 tutup \xE7ift t\u0131klay\u0131n veya enter tu\u015Funa bas\u0131n",
  lineEditor_pointSelected: "Sil tu\u015Funa basarak noktalar\u0131 silin,\nCtrl/Cmd + D ile \xE7o\u011Falt\u0131n, ya da s\xFCr\xFCkleyerek ta\u015F\u0131y\u0131n",
  lineEditor_nothingSelected: "D\xFCzenlemek i\xE7in bir nokta se\xE7in (birden fazla se\xE7mek i\xE7in SHIFT tu\u015Funu bas\u0131l\u0131 tutun),\nveya Alt tu\u015Funu bas\u0131l\u0131 tutun ve yeni noktalar eklemek i\xE7in t\u0131klay\u0131n",
  placeImage: "Resmi yerle\u015Ftirmek i\xE7in t\u0131klay\u0131n ya da boyutunu manuel olarak ayarlamak i\xE7in t\u0131klay\u0131p s\xFCr\xFCkleyin",
  publishLibrary: "Kendi kitapl\u0131\u011F\u0131n\u0131z\u0131 yay\u0131nlay\u0131n",
  bindTextToElement: "Enter tu\u015Funa basarak metin ekleyin",
  deepBoxSelect: "Ctrl/Cmd tu\u015Funa bas\u0131l\u0131 tutarak derin se\xE7im yap\u0131n ya da s\xFCr\xFCklemeyi engelleyin",
  eraserRevert: "Alt tu\u015Funa bas\u0131l\u0131 tutarak silinme i\xE7in i\u015Faretlenmi\u015F \xF6geleri tersine \xE7evirin",
  firefox_clipboard_write: "",
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "\xD6nizleme g\xF6sterilemiyor",
  canvasTooBig: "Kanvas \xE7ok b\xFCy\xFCk olabilir.",
  canvasTooBigTip: "\u0130pucu: En uzaktaki elemanlar\u0131 birbirine yak\u0131nla\u015Ft\u0131rmay\u0131 deneyin."
};
var errorSplash = {
  headingMain: "Hata olu\u015Ftu. L\xFCtfen <button>sayfay\u0131 yenilemeyi deneyin.</button>",
  clearCanvasMessage: "Yenileme sonras\u0131 sorun devam ediyorsa, l\xFCtfen <button>\xE7izim alan\u0131n\u0131 temizlemeyi deneyin.</button>",
  clearCanvasCaveat: " Bu, yapt\u0131\u011F\u0131n\u0131z de\u011Fi\u015Fiklikleri s\u0131f\u0131rlayacak ",
  trackedToSentry: "Tan\u0131mlay\u0131c\u0131 ile ilgili hata {{eventId}} sistemimize yakaland\u0131.",
  openIssueMessage: "Sahne bilginizi hata mesaj\u0131na yans\u0131tmamak i\xE7in olduk\xE7a dikkatli davrand\u0131k. E\u011Fer sahneniz gizli de\u011Filse hatay\u0131 l\xFCtfen \u015Furadan takip edin <button>hata takibi.</button> L\xFCtfen a\u015Fa\u011F\u0131ya GitHub sorununa kopyalayarak ve yap\u0131\u015Ft\u0131rarak bilgi ekleyin.",
  sceneContent: "Sahne i\xE7eri\u011Fi:"
};
var roomDialog = {
  desc_intro: "\xC7al\u0131\u015Fma alan\u0131n\u0131za, sizinle birlikte \xE7al\u0131\u015Fabilmeleri i\xE7in ba\u015Fkalar\u0131n\u0131 da ekleyebilirsiniz.",
  desc_privacy: "\xC7al\u0131\u015Fma ortam\u0131nda yapt\u0131klar\u0131n\u0131z ve \xE7izimleriniz u\xE7tan uca \u015Fifrelemeyle saklanmaktad\u0131r. Sunucular\u0131m\u0131z dahi bu verileri \u015Fifrelenmemi\u015F haliyle g\xF6remez.",
  button_startSession: "Oturumu ba\u015Flat",
  button_stopSession: "Oturumu sonland\u0131r",
  desc_inProgressIntro: "Ortak \xE7al\u0131\u015Fma ortam\u0131 olu\u015Fturuldu.",
  desc_shareLink: "Bu ba\u011Flant\u0131y\u0131 birlikte \xE7al\u0131\u015Faca\u011F\u0131n\u0131z ki\u015Filerle payla\u015Fabilirsiniz:",
  desc_exitSession: "\xC7al\u0131\u015Fma ortam\u0131n\u0131 kapatt\u0131\u011F\u0131n\u0131zda ortak \xE7al\u0131\u015Fmadan ayr\u0131lm\u0131\u015F olursunuz ancak kendi versiyonunuzda \xE7al\u0131\u015Fmaya devam edebilirsiniz. Bu durumda ortak \xE7al\u0131\u015Ft\u0131\u011F\u0131n\u0131z di\u011Fer ki\u015Filer etkilenmeyecek, \xE7al\u0131\u015Fma ortam\u0131ndaki versiyon \xFCzerinden \xE7al\u0131\u015Fmaya devam edebilecekler.",
  shareTitle: "Excalidraw'da canl\u0131 ortak cal\u0131\u015Fma oturumuna kat\u0131l"
};
var errorDialog = {
  title: "Hata"
};
var exportDialog = {
  disk_title: "Belle\u011Fe kaydet",
  disk_details: "Sahne verilerini daha sonra i\xE7e aktarabilece\u011Finiz bir dosyaya aktar\u0131n.",
  disk_button: "Dosyaya kaydet",
  link_title: "Payla\u015F\u0131labilir ba\u011Flant\u0131",
  link_details: "Salt okunur bir ba\u011Flant\u0131 olarak d\u0131\u015Fa aktar\u0131n.",
  link_button: "Ba\u011Flant\u0131 olarak d\u0131\u015Fa aktar",
  excalidrawplus_description: "Sahneyi Excalidraw+ \xE7al\u0131\u015Fma alan\u0131n\u0131za kaydedin.",
  excalidrawplus_button: "D\u0131\u015Fa aktar",
  excalidrawplus_exportError: "\u015Eu anda Excalidraw+'a aktar\u0131lamad\u0131..."
};
var helpDialog = {
  blog: "Blog'umuzu okuyun",
  click: "t\u0131kla",
  deepSelect: "Derin se\xE7im",
  deepBoxSelect: "Kutu i\xE7erisinde derin se\xE7im yap\u0131n, s\xFCr\xFCklemeyi engelleyin",
  curvedArrow: "E\u011Fri ok",
  curvedLine: "E\u011Fri \xE7izgi",
  documentation: "Dok\xFCmantasyon",
  doubleClick: "\xE7ift-t\u0131klama",
  drag: "s\xFCr\xFCkle",
  editor: "D\xFCzenleyici",
  editLineArrowPoints: "\xC7izgi/ok noktalar\u0131n\u0131 d\xFCzenle",
  editText: "Etiket / metin d\xFCzenle",
  github: "Bir hata m\u0131 buldun? Bildir",
  howto: "Rehberlerimizi takip edin",
  or: "veya",
  preventBinding: "Ok ba\u011Flamay\u0131 \xF6nleyin",
  tools: "Ara\xE7lar",
  shortcuts: "Klavye k\u0131sayollar\u0131",
  textFinish: "D\xFCzenlemeyi bitir (metin d\xFCzenleyici)",
  textNewLine: "Yeni sat\u0131r ekle (metin d\xFCzenleyici)",
  title: "Yard\u0131m",
  view: "G\xF6r\xFCn\xFCm",
  zoomToFit: "T\xFCm \xF6\u011Feleri s\u0131\u011Fd\u0131rmak i\xE7in yak\u0131nla\u015Ft\u0131r",
  zoomToSelection: "Se\xE7ime yak\u0131nla\u015F",
  toggleElementLock: "Se\xE7imi Kilitle/\xE7\xF6z",
  movePageUpDown: "Sayfay\u0131 yukar\u0131/a\u015Fa\u011F\u0131 kayd\u0131r",
  movePageLeftRight: "Sayfay\u0131 sola/sa\u011Fa kayd\u0131r"
};
var clearCanvasDialog = {
  title: "Tuvali temizle"
};
var publishDialog = {
  title: "Kitapl\u0131\u011F\u0131 yay\u0131nla",
  itemName: "\xD6\u011Fe ad\u0131",
  authorName: "Yazar ad\u0131",
  githubUsername: "G\u0131tHub kullan\u0131c\u0131 ad\u0131",
  twitterUsername: "Twitter kullan\u0131c\u0131 ad\u0131",
  libraryName: "Kitapl\u0131k ad\u0131",
  libraryDesc: "Kitapl\u0131k a\xE7\u0131klamas\u0131",
  website: "Web sitesi",
  placeholder: {
    authorName: "Ad\u0131n\u0131z ya da kullan\u0131c\u0131 ad\u0131n\u0131z",
    libraryName: "Kitapl\u0131\u011F\u0131n\u0131z\u0131n ad\u0131",
    libraryDesc: "\u0130nsanlar\u0131n kullan\u0131m\u0131n\u0131 anlamas\u0131na yard\u0131mc\u0131 olmak i\xE7in kitapl\u0131\u011F\u0131n\u0131z\u0131n a\xE7\u0131klamas\u0131",
    githubHandle: "Github ba\u011Flant\u0131s\u0131 ( tercihe ba\u011Fl\u0131), k\xFCt\xFCphane g\xF6zden ge\xE7irme i\xE7in onayland\u0131\u011F\u0131nda d\xFCzenleyebiliesiniz diye",
    twitterHandle: "Twitter kullan\u0131c\u0131 ad\u0131 ( tercihe ba\u011Fl\u0131), bu sayede Twitter \xFCzerinde payla\u015F\u0131ren \xE7al\u0131\u015Fman\u0131z\u0131 size atfedebiliriz",
    website: "Ki\u015Fisel web sayfan\u0131z\u0131 ya da ba\u015Fka bir yeri ba\u011Flay\u0131n (tercihe ba\u011Fl\u0131)"
  },
  errors: {
    required: "Gerekli",
    website: "Ge\xE7erli bir URL girin"
  },
  noteDescription: "Submit your library to be included in the <link>genel k\xFCt\xFCphane reposu</link>di\u011Fer insanlar \xE7izimlerinde kullanabilsin diye.",
  noteGuidelines: "\xD6nce k\xFCt\xFCphane elle onaylanmal\u0131. \u015Funu okuyun <link>y\xF6nergeler</link> onaylamadan \xF6nce. gerekli olmas\u0131 halinde ileti\u015Fim kurmak i\xE7in ve de\u011Fi\u015Fiklik i\xE7in Github hesab\u0131 gerekli, ama \xE7ok da illaki olmal\u0131 de\u011Fil.",
  noteLicense: "Bunu onaylayarak, k\xFCt\xFC\u011Fhanenin \u015Fu lisansla yay\u0131nlanmas\u0131n\u0131 onayl\u0131yorsunuz <link>MIT Lisans, </link>ki bu k\u0131saca herkesin onu k\u0131s\u0131tlama olmaks\u0131z\u0131n kullanabilece\u011Fi anlam\u0131na gelmektedir.",
  noteItems: "Her k\xFCt\xFCphane kendi ismine sahip olmal\u0131 ki tarama yapabilelim. \u015Eu k\xFCt\xFCphane \xF6geleri dahil edilecek:",
  atleastOneLibItem: "L\xFCtfen ba\u015Flamak i\xE7in en az bir tane k\xFCt\xFCphane \xF6gesi se\xE7in",
  republishWarning: "Not: se\xE7ilen \xF6gelerden bir k\u0131sm\u0131 zaten yay\u0131nlanm\u0131\u015F/g\xF6nderilmi\u015F. Yaln\u0131zca mevcut k\xFCt\xFCphane ve g\xF6nderileri g\xFCncellerken yeniden g\xF6nderme i\u015Flemi yapmal\u0131s\u0131n\u0131z."
};
var publishSuccessDialog = {
  title: "K\xFCt\xFCphane g\xF6nderildi",
  content: "Te\u015Fekk\xFCrler {{authorName}}. K\xFCt\xFCphaneniz g\xF6zden ge\xE7irme i\xE7in al\u0131nd\u0131. Durumu takip edebilirsiniz<link>burada</link>"
};
var confirmDialog = {
  resetLibrary: "K\xFCt\xFCphaneyi s\u0131f\u0131rla",
  removeItemsFromLib: "Se\xE7ilen \xF6geleri k\xFCt\xFCphaneden kald\u0131r"
};
var imageExportDialog = {
  header: "Resmi d\u0131\u015Fa aktar",
  label: {
    withBackground: "Arka plan",
    onlySelected: "Sadece se\xE7ilen",
    darkMode: "Karanl\u0131k mod",
    embedScene: "Sahne yerle\u015Ftir",
    scale: "\xD6l\xE7eklendir",
    padding: "D\u0131\u015F bo\u015Fluk"
  },
  tooltip: {
    embedScene: "Sahne verisi, sahnenin geri y\xFCklenebilmesi i\xE7in d\u0131\u015Far\u0131 aktar\u0131lan PNG/SVG dosyas\u0131na kaydedilecektir. Bu, d\u0131\u015Fa aktar\u0131lan dosya boyutunu artt\u0131racakt\u0131r."
  },
  title: {
    exportToPng: "PNG olarak d\u0131\u015Fa aktar",
    exportToSvg: "SVG olarak d\u0131\u015Fa aktar",
    copyPngToClipboard: ""
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "Panoya kopyala"
  }
};
var encrypted = {
  tooltip: "\xC7izimleriniz u\xE7tan-uca \u015Fifrelenmi\u015Ftir, Excalidraw'\u0131n sunucular\u0131 bile onlar\u0131 g\xF6remez.",
  link: "Excalidraw'da u\xE7tan uca \u015Fifreleme hakk\u0131nda blog yaz\u0131s\u0131"
};
var stats = {
  angle: "A\xE7\u0131",
  element: "Bile\u015Fen",
  elements: "Bile\u015Fenler",
  height: "Y\xFCkseklik",
  scene: "Sahne",
  selected: "Se\xE7ili",
  storage: "Depolama",
  title: "\u0130nekler i\xE7in istatistikler",
  total: "Toplam",
  version: "S\xFCr\xFCm",
  versionCopy: "Kopyalamak i\xE7in t\u0131kla",
  versionNotAvailable: "S\xFCr\xFCm mevcut de\u011Fil",
  width: "Geni\u015Flik"
};
var toast = {
  addedToLibrary: "K\xFCt\xFCphaneye eklendi",
  copyStyles: "Stiller kopyaland\u0131.",
  copyToClipboard: "Panoya kopyaland\u0131.",
  copyToClipboardAsPng: "{{exportSelection}} panoya PNG olarak\n({{exportColorScheme}}) kopyaland\u0131",
  fileSaved: "Dosya kaydedildi.",
  fileSavedToFilename: "{filename} kaydedildi",
  canvas: "tuval",
  selection: "se\xE7im",
  pasteAsSingleElement: "Tekil obje olarak yap\u0131\u015Ft\u0131rmak i\xE7in veya var olan bir metin edit\xF6r\xFCne yap\u0131\u015Ft\u0131rmak i\xE7in {{shortcut}} kullan\u0131n",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "\u015Eeffaf",
  black: "Siyah",
  white: "Beyaz",
  red: "K\u0131rm\u0131z\u0131",
  pink: "Pembe",
  grape: "Koyu Mor",
  violet: "Menek\u015Fe rengi",
  gray: "Gri",
  blue: "Mavi",
  cyan: "Camg\xF6be\u011Fi",
  teal: "Deniz mavisi",
  green: "Ye\u015Fil",
  yellow: "Sar\u0131",
  orange: "Turuncu",
  bronze: ""
};
var welcomeScreen = {
  app: {
    center_heading: "",
    center_heading_plus: "Ecalidraw+'a m\u0131 gitmek istediniz?",
    menuHint: "D\u0131\u015Fa aktar, se\xE7enekler, diller, ..."
  },
  defaults: {
    menuHint: "D\u0131\u015Fa aktar, se\xE7enekler, ve daha fazlas\u0131...",
    center_heading: "",
    toolbarHint: "Bir ara\xE7 se\xE7in ve \xE7izime ba\u015Flay\u0131n!",
    helpHint: "K\u0131sayollar & yard\u0131m"
  }
};
var colorPicker = {
  mostUsedCustomColors: "En \xE7ok kullan\u0131lan \xF6zel renkler",
  colors: "Renkler",
  shades: "",
  hexCode: "Hex kodu",
  noShades: ""
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "",
      button: "",
      description: ""
    },
    saveToDisk: {
      title: "",
      button: "Diske Kaydet",
      description: ""
    },
    excalidrawPlus: {
      title: "",
      button: "",
      description: ""
    }
  },
  modal: {
    loadFromFile: {
      title: "",
      button: "",
      description: ""
    },
    shareableLink: {
      title: "",
      button: "",
      description: ""
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var tr_TR_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  tr_TR_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=tr-TR-7QYBXDBO.js.map
