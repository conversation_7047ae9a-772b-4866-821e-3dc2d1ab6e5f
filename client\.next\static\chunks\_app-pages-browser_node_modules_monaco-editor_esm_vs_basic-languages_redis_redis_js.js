"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_redis_redis_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/redis/redis.js":
/*!***************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/redis/redis.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/redis/redis.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".redis\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \"APPEND\",\n    \"AUTH\",\n    \"BGREWRITEAOF\",\n    \"BGSAVE\",\n    \"BITCOUNT\",\n    \"BITFIELD\",\n    \"BITOP\",\n    \"BITPOS\",\n    \"BLPOP\",\n    \"BRPOP\",\n    \"BRPOPLPUSH\",\n    \"CLIENT\",\n    \"KILL\",\n    \"LIST\",\n    \"GETNAME\",\n    \"PAUSE\",\n    \"REPLY\",\n    \"SETNAME\",\n    \"CLUSTER\",\n    \"ADDSLOTS\",\n    \"COUNT-FAILURE-REPORTS\",\n    \"COUNTKEYSINSLOT\",\n    \"DELSLOTS\",\n    \"FAILOVER\",\n    \"FORGET\",\n    \"GETKEYSINSLOT\",\n    \"INFO\",\n    \"KEYSLOT\",\n    \"MEET\",\n    \"NODES\",\n    \"REPLICATE\",\n    \"RESET\",\n    \"SAVECONFIG\",\n    \"SET-CONFIG-EPOCH\",\n    \"SETSLOT\",\n    \"SLAVES\",\n    \"SLOTS\",\n    \"COMMAND\",\n    \"COUNT\",\n    \"GETKEYS\",\n    \"CONFIG\",\n    \"GET\",\n    \"REWRITE\",\n    \"SET\",\n    \"RESETSTAT\",\n    \"DBSIZE\",\n    \"DEBUG\",\n    \"OBJECT\",\n    \"SEGFAULT\",\n    \"DECR\",\n    \"DECRBY\",\n    \"DEL\",\n    \"DISCARD\",\n    \"DUMP\",\n    \"ECHO\",\n    \"EVAL\",\n    \"EVALSHA\",\n    \"EXEC\",\n    \"EXISTS\",\n    \"EXPIRE\",\n    \"EXPIREAT\",\n    \"FLUSHALL\",\n    \"FLUSHDB\",\n    \"GEOADD\",\n    \"GEOHASH\",\n    \"GEOPOS\",\n    \"GEODIST\",\n    \"GEORADIUS\",\n    \"GEORADIUSBYMEMBER\",\n    \"GETBIT\",\n    \"GETRANGE\",\n    \"GETSET\",\n    \"HDEL\",\n    \"HEXISTS\",\n    \"HGET\",\n    \"HGETALL\",\n    \"HINCRBY\",\n    \"HINCRBYFLOAT\",\n    \"HKEYS\",\n    \"HLEN\",\n    \"HMGET\",\n    \"HMSET\",\n    \"HSET\",\n    \"HSETNX\",\n    \"HSTRLEN\",\n    \"HVALS\",\n    \"INCR\",\n    \"INCRBY\",\n    \"INCRBYFLOAT\",\n    \"KEYS\",\n    \"LASTSAVE\",\n    \"LINDEX\",\n    \"LINSERT\",\n    \"LLEN\",\n    \"LPOP\",\n    \"LPUSH\",\n    \"LPUSHX\",\n    \"LRANGE\",\n    \"LREM\",\n    \"LSET\",\n    \"LTRIM\",\n    \"MGET\",\n    \"MIGRATE\",\n    \"MONITOR\",\n    \"MOVE\",\n    \"MSET\",\n    \"MSETNX\",\n    \"MULTI\",\n    \"PERSIST\",\n    \"PEXPIRE\",\n    \"PEXPIREAT\",\n    \"PFADD\",\n    \"PFCOUNT\",\n    \"PFMERGE\",\n    \"PING\",\n    \"PSETEX\",\n    \"PSUBSCRIBE\",\n    \"PUBSUB\",\n    \"PTTL\",\n    \"PUBLISH\",\n    \"PUNSUBSCRIBE\",\n    \"QUIT\",\n    \"RANDOMKEY\",\n    \"READONLY\",\n    \"READWRITE\",\n    \"RENAME\",\n    \"RENAMENX\",\n    \"RESTORE\",\n    \"ROLE\",\n    \"RPOP\",\n    \"RPOPLPUSH\",\n    \"RPUSH\",\n    \"RPUSHX\",\n    \"SADD\",\n    \"SAVE\",\n    \"SCARD\",\n    \"SCRIPT\",\n    \"FLUSH\",\n    \"LOAD\",\n    \"SDIFF\",\n    \"SDIFFSTORE\",\n    \"SELECT\",\n    \"SETBIT\",\n    \"SETEX\",\n    \"SETNX\",\n    \"SETRANGE\",\n    \"SHUTDOWN\",\n    \"SINTER\",\n    \"SINTERSTORE\",\n    \"SISMEMBER\",\n    \"SLAVEOF\",\n    \"SLOWLOG\",\n    \"SMEMBERS\",\n    \"SMOVE\",\n    \"SORT\",\n    \"SPOP\",\n    \"SRANDMEMBER\",\n    \"SREM\",\n    \"STRLEN\",\n    \"SUBSCRIBE\",\n    \"SUNION\",\n    \"SUNIONSTORE\",\n    \"SWAPDB\",\n    \"SYNC\",\n    \"TIME\",\n    \"TOUCH\",\n    \"TTL\",\n    \"TYPE\",\n    \"UNSUBSCRIBE\",\n    \"UNLINK\",\n    \"UNWATCH\",\n    \"WAIT\",\n    \"WATCH\",\n    \"ZADD\",\n    \"ZCARD\",\n    \"ZCOUNT\",\n    \"ZINCRBY\",\n    \"ZINTERSTORE\",\n    \"ZLEXCOUNT\",\n    \"ZRANGE\",\n    \"ZRANGEBYLEX\",\n    \"ZREVRANGEBYLEX\",\n    \"ZRANGEBYSCORE\",\n    \"ZRANK\",\n    \"ZREM\",\n    \"ZREMRANGEBYLEX\",\n    \"ZREMRANGEBYRANK\",\n    \"ZREMRANGEBYSCORE\",\n    \"ZREVRANGE\",\n    \"ZREVRANGEBYSCORE\",\n    \"ZREVRANK\",\n    \"ZSCORE\",\n    \"ZUNIONSTORE\",\n    \"SCAN\",\n    \"SSCAN\",\n    \"HSCAN\",\n    \"ZSCAN\"\n  ],\n  operators: [\n    // NOT SUPPORTED\n  ],\n  builtinFunctions: [\n    // NOT SUPPORTED\n  ],\n  builtinVariables: [\n    // NOT SUPPORTED\n  ],\n  pseudoColumns: [\n    // NOT SUPPORTED\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@pseudoColumns\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@scopes\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [\n        /[\\w@#$]+/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@operators\": \"operator\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    pseudoColumns: [\n      [\n        /[$][A-Za-z_][\\w@#$]*/,\n        {\n          cases: {\n            \"@pseudoColumns\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [\n      [/'/, { token: \"string\", next: \"@string\" }],\n      [/\"/, { token: \"string.double\", next: \"@stringDouble\" }]\n    ],\n    string: [\n      [/[^']+/, \"string\"],\n      [/''/, \"string\"],\n      [/'/, { token: \"string\", next: \"@pop\" }]\n    ],\n    stringDouble: [\n      [/[^\"]+/, \"string.double\"],\n      [/\"\"/, \"string.double\"],\n      [/\"/, { token: \"string.double\", next: \"@pop\" }]\n    ],\n    scopes: [\n      // NOT SUPPORTED\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/redis/redis.js\n"));

/***/ })

}]);