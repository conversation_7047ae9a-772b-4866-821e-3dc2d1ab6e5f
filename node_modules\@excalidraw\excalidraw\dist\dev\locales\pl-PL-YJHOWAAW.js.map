{"version": 3, "sources": ["../../../locales/pl-PL.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Wklej\",\n    \"pasteAsPlaintext\": \"Wklej jako zwykły tekst\",\n    \"pasteCharts\": \"Wklej wykresy\",\n    \"selectAll\": \"Zaznacz wszystko\",\n    \"multiSelect\": \"Dodaj element do zaznaczenia\",\n    \"moveCanvas\": \"Przesuń obszar roboczy\",\n    \"cut\": \"Wytnij\",\n    \"copy\": \"Kopiuj\",\n    \"copyAsPng\": \"Skopiuj do schowka jako plik PNG\",\n    \"copyAsSvg\": \"Skopiuj do schowka jako plik SVG\",\n    \"copyText\": \"Skopiuj do schowka jako tekst\",\n    \"copySource\": \"Skopiuj źródło do schowka\",\n    \"convertToCode\": \"Skonwertuj do kodu\",\n    \"bringForward\": \"Przenieś wyżej\",\n    \"sendToBack\": \"<PERSON>rzenie<PERSON> na spód\",\n    \"bringToFront\": \"<PERSON>rzeni<PERSON><PERSON> na wierzch\",\n    \"sendBackward\": \"Przeni<PERSON><PERSON> niżej\",\n    \"delete\": \"<PERSON><PERSON><PERSON>\",\n    \"copyStyles\": \"Kopiu<PERSON> style\",\n    \"pasteStyles\": \"Wklej style\",\n    \"stroke\": \"Kolor obramowania\",\n    \"background\": \"Kolor wypełnienia\",\n    \"fill\": \"Wypełnienie\",\n    \"strokeWidth\": \"Grubość obramowania\",\n    \"strokeStyle\": \"Styl obrysu\",\n    \"strokeStyle_solid\": \"Pełny\",\n    \"strokeStyle_dashed\": \"Kreskowany\",\n    \"strokeStyle_dotted\": \"Kropkowany\",\n    \"sloppiness\": \"Styl kreski\",\n    \"opacity\": \"Przeźroczystość\",\n    \"textAlign\": \"Wyrównanie tekstu\",\n    \"edges\": \"Krawędzie\",\n    \"sharp\": \"Ostry\",\n    \"round\": \"Zaokrąglij\",\n    \"arrowheads\": \"Groty\",\n    \"arrowhead_none\": \"Brak\",\n    \"arrowhead_arrow\": \"Strzałka\",\n    \"arrowhead_bar\": \"Kreska\",\n    \"arrowhead_circle\": \"Okrąg\",\n    \"arrowhead_circle_outline\": \"Okrąg (obrys)\",\n    \"arrowhead_triangle\": \"Trójkąt\",\n    \"arrowhead_triangle_outline\": \"Trójkąt (obrys)\",\n    \"arrowhead_diamond\": \"Romb\",\n    \"arrowhead_diamond_outline\": \"Romb (obrys)\",\n    \"fontSize\": \"Rozmiar tekstu\",\n    \"fontFamily\": \"Krój pisma\",\n    \"addWatermark\": \"Dodaj \\\"Zrobione w Excalidraw\\\"\",\n    \"handDrawn\": \"Odręczny\",\n    \"normal\": \"Normalny\",\n    \"code\": \"Kod\",\n    \"small\": \"Mały\",\n    \"medium\": \"Średni\",\n    \"large\": \"Duży\",\n    \"veryLarge\": \"Bardzo duży\",\n    \"solid\": \"Pełne\",\n    \"hachure\": \"Linie\",\n    \"zigzag\": \"Zygzak\",\n    \"crossHatch\": \"Zakreślone\",\n    \"thin\": \"Cienkie\",\n    \"bold\": \"Pogrubione\",\n    \"left\": \"Do lewej\",\n    \"center\": \"Do środka\",\n    \"right\": \"Do prawej\",\n    \"extraBold\": \"Ekstra pogrubione\",\n    \"architect\": \"Dokładny\",\n    \"artist\": \"Artystyczny\",\n    \"cartoonist\": \"Rysunkowy\",\n    \"fileTitle\": \"Nazwa pliku\",\n    \"colorPicker\": \"Paleta kolorów\",\n    \"canvasColors\": \"Używane na płótnie\",\n    \"canvasBackground\": \"Kolor dokumentu\",\n    \"drawingCanvas\": \"Obszar roboczy\",\n    \"layers\": \"Warstwy\",\n    \"actions\": \"Akcje\",\n    \"language\": \"Język\",\n    \"liveCollaboration\": \"Współpraca w czasie rzeczywistym...\",\n    \"duplicateSelection\": \"Powiel\",\n    \"untitled\": \"Bez tytułu\",\n    \"name\": \"Nazwa\",\n    \"yourName\": \"Twoje imię\",\n    \"madeWithExcalidraw\": \"Zrobione w Excalidraw\",\n    \"group\": \"Zgrupuj wybrane\",\n    \"ungroup\": \"Rozgrupuj wybrane\",\n    \"collaborators\": \"Współtwórcy\",\n    \"showGrid\": \"Pokaż siatkę\",\n    \"addToLibrary\": \"Dodaj do biblioteki\",\n    \"removeFromLibrary\": \"Usuń z biblioteki\",\n    \"libraryLoadingMessage\": \"Wczytywanie biblioteki…\",\n    \"libraries\": \"Przeglądaj biblioteki\",\n    \"loadingScene\": \"Wczytywanie sceny…\",\n    \"align\": \"Wyrównaj\",\n    \"alignTop\": \"Wyrównaj do góry\",\n    \"alignBottom\": \"Wyrównaj do dołu\",\n    \"alignLeft\": \"Wyrównaj do lewej\",\n    \"alignRight\": \"Wyrównaj do prawej\",\n    \"centerVertically\": \"Wyśrodkuj w pionie\",\n    \"centerHorizontally\": \"Wyśrodkuj w poziomie\",\n    \"distributeHorizontally\": \"Rozłóż poziomo\",\n    \"distributeVertically\": \"Rozłóż pionowo\",\n    \"flipHorizontal\": \"Odwróć w poziomie\",\n    \"flipVertical\": \"Odwróć w pionie\",\n    \"viewMode\": \"Tryb widoku\",\n    \"share\": \"Udostępnij\",\n    \"showStroke\": \"Pokaż próbnik kolorów obrysu\",\n    \"showBackground\": \"Pokaż próbnik koloru tła\",\n    \"toggleTheme\": \"Przełącz motyw\",\n    \"personalLib\": \"Biblioteka prywatna\",\n    \"excalidrawLib\": \"Biblioteka Excalidraw\",\n    \"decreaseFontSize\": \"Zmniejsz rozmiar czcionki\",\n    \"increaseFontSize\": \"Zwiększ rozmiar czcionki\",\n    \"unbindText\": \"Odłącz tekst od kontenera\",\n    \"bindText\": \"Połącz tekst z kontenerem\",\n    \"createContainerFromText\": \"Zawijaj tekst w kontenerze\",\n    \"link\": {\n      \"edit\": \"Edytuj łącze\",\n      \"editEmbed\": \"Edytuj i osadź link\",\n      \"create\": \"Utwórz łącze\",\n      \"createEmbed\": \"Stwórz i osadź link\",\n      \"label\": \"Łącze\",\n      \"labelEmbed\": \"Podlinkuj i osadź\",\n      \"empty\": \"Nie ustawiono linku\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Edytuj linię\",\n      \"exit\": \"Wyjdź z edytora linii\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Zablokuj\",\n      \"unlock\": \"Odblokuj\",\n      \"lockAll\": \"Zablokuj wszystko\",\n      \"unlockAll\": \"Odblokuj wszystko\"\n    },\n    \"statusPublished\": \"Opublikowano\",\n    \"sidebarLock\": \"Panel boczny zawsze otwarty\",\n    \"selectAllElementsInFrame\": \"Zaznacz wszystkie elementy w ramce\",\n    \"removeAllElementsFromFrame\": \"Usuń wszystkie elementy z ramki\",\n    \"eyeDropper\": \"Wybierz kolor z płótna\",\n    \"textToDiagram\": \"Tekst do diagramu\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Nie dodano jeszcze żadnych elementów...\",\n    \"hint_emptyLibrary\": \"Wybierz element na płótnie, aby go tutaj dodać, lub zainstaluj bibliotekę z poniższego publicznego repozytorium.\",\n    \"hint_emptyPrivateLibrary\": \"Wybierz element, aby dodać go tutaj.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Wyczyść dokument i zresetuj kolor dokumentu\",\n    \"exportJSON\": \"Eksportuj do pliku\",\n    \"exportImage\": \"Eksportuj obraz...\",\n    \"export\": \"Zapisz jako...\",\n    \"copyToClipboard\": \"Skopiuj do schowka\",\n    \"save\": \"Zapisz do bieżącego pliku\",\n    \"saveAs\": \"Zapisz jako\",\n    \"load\": \"Otwórz\",\n    \"getShareableLink\": \"Udostępnij\",\n    \"close\": \"Zamknij\",\n    \"selectLanguage\": \"Wybierz język\",\n    \"scrollBackToContent\": \"Wróć do obszaru roboczego\",\n    \"zoomIn\": \"Powiększ\",\n    \"zoomOut\": \"Pomniejsz\",\n    \"resetZoom\": \"Zresetuj powiększenie\",\n    \"menu\": \"Menu\",\n    \"done\": \"Gotowe\",\n    \"edit\": \"Edytuj\",\n    \"undo\": \"Cofnij\",\n    \"redo\": \"Przywróć\",\n    \"resetLibrary\": \"Resetuj bibliotekę\",\n    \"createNewRoom\": \"Utwórz nowy pokój\",\n    \"fullScreen\": \"Pełny ekran\",\n    \"darkMode\": \"Ciemny motyw\",\n    \"lightMode\": \"Jasny motyw\",\n    \"zenMode\": \"Tryb Zen\",\n    \"objectsSnapMode\": \"Przyciąganie do obiektów\",\n    \"exitZenMode\": \"Wyjdź z trybu Zen\",\n    \"cancel\": \"Anuluj\",\n    \"clear\": \"Wyczyść\",\n    \"remove\": \"Usuń\",\n    \"embed\": \"Przełącz osadzenie\",\n    \"publishLibrary\": \"Opublikuj\",\n    \"submit\": \"Prześlij\",\n    \"confirm\": \"Zatwierdź\",\n    \"embeddableInteractionButton\": \"Kliknij, aby wejść w interakcję\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"To spowoduje usunięcie wszystkiego z dokumentu. Czy chcesz kontynuować?\",\n    \"couldNotCreateShareableLink\": \"Wystąpił błąd przy generowaniu linka do udostępniania.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Nie można utworzyć linku do udostępnienia: scena jest za duża\",\n    \"couldNotLoadInvalidFile\": \"Nie udało się otworzyć pliku. Wybrany plik jest nieprawidłowy.\",\n    \"importBackendFailed\": \"Wystąpił błąd podczas importowania pliku.\",\n    \"cannotExportEmptyCanvas\": \"Najpierw musisz coś narysować, aby zapisać dokument.\",\n    \"couldNotCopyToClipboard\": \"Nie udało się skopiować do schowka.\",\n    \"decryptFailed\": \"Nie udało się odszyfrować danych.\",\n    \"uploadedSecurly\": \"By zapewnić Ci prywatność, udostępnianie projektu jest zabezpieczone szyfrowaniem end-to-end, co oznacza, że poza tobą i osobą z którą podzielisz się linkiem, nikt nie ma dostępu do tego co udostępniasz.\",\n    \"loadSceneOverridePrompt\": \"Wczytanie zewnętrznego rysunku zastąpi istniejącą zawartość. Czy chcesz kontynuować?\",\n    \"collabStopOverridePrompt\": \"Zatrzymanie sesji nadpisze poprzedni, zapisany lokalnie rysunek. Czy jesteś pewien?\\n\\n(Jeśli chcesz zachować swój lokalny rysunek, po prostu zamknij zakładkę przeglądarki.)\",\n    \"errorAddingToLibrary\": \"Nie udało się dodać elementu do biblioteki\",\n    \"errorRemovingFromLibrary\": \"Nie udało się usunąć elementu z biblioteki\",\n    \"confirmAddLibrary\": \"To doda {{numShapes}} kształtów do twojej biblioteki. Jesteś pewien?\",\n    \"imageDoesNotContainScene\": \"Ten obraz nie zawiera żadnych informacji o scenie. Czy włączyłeś osadzanie sceny podczas eksportu?\",\n    \"cannotRestoreFromImage\": \"Scena nie mogła zostać przywrócona z pliku obrazu\",\n    \"invalidSceneUrl\": \"Nie udało się zaimportować sceny z podanego adresu URL. Jest ona wadliwa lub nie zawiera poprawnych danych Excalidraw w formacie JSON.\",\n    \"resetLibrary\": \"To wyczyści twoją bibliotekę. Jesteś pewien?\",\n    \"removeItemsFromsLibrary\": \"Usunąć {{count}} element(ów) z biblioteki?\",\n    \"invalidEncryptionKey\": \"Klucz szyfrowania musi składać się z 22 znaków. Współpraca na żywo jest wyłączona.\",\n    \"collabOfflineWarning\": \"Brak połączenia z Internetem.\\nTwoje zmiany nie zostaną zapisane!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Nieobsługiwany typ pliku.\",\n    \"imageInsertError\": \"Nie udało się wstawić obrazu. Spróbuj ponownie później...\",\n    \"fileTooBig\": \"Plik jest zbyt duży. Maksymalny dozwolony rozmiar to {{maxSize}}.\",\n    \"svgImageInsertError\": \"Nie udało się wstawić obrazu SVG. Znacznik SVG wygląda na nieprawidłowy.\",\n    \"failedToFetchImage\": \"Nie udało się załadować obrazu.\",\n    \"invalidSVGString\": \"Nieprawidłowy SVG.\",\n    \"cannotResolveCollabServer\": \"Nie można połączyć się z serwerem współpracy w czasie rzeczywistym. Proszę odświeżyć stronę i spróbować ponownie.\",\n    \"importLibraryError\": \"Wystąpił błąd w trakcie ładowania biblioteki\",\n    \"collabSaveFailed\": \"Nie udało się zapisać w bazie danych. Jeśli problemy nie ustąpią, zapisz plik lokalnie, aby nie utracić swojej pracy.\",\n    \"collabSaveFailed_sizeExceeded\": \"Nie udało się zapisać w bazie danych — dokument jest za duży. Zapisz plik lokalnie, aby nie utracić swojej pracy.\",\n    \"imageToolNotSupported\": \"Dodawanie obrazów jest wyłączone.\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Wygląda na to, że używasz przeglądarki Brave z włączonym ustawieniem <bold>Agressively Block Fingerprinting</bold>.\",\n      \"line2\": \"Może to doprowadzić do złamania <bold>elementów tekstu</bold> na rysunkach.\",\n      \"line3\": \"Zdecydowanie zalecamy wyłączenie tego ustawienia. Możesz wykonać <link>te kroki</link>, aby to zrobić.\",\n      \"line4\": \"Jeśli wyłączenie tego ustawienia nie naprawia wyświetlania elementów tekstowych, zgłoś <issueLink>problem</issueLink> na naszym GitHubie lub napisz do nas na <discordLink>Discordzie</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"Elementy osadzone nie mogą zostać dodane do biblioteki.\",\n      \"iframe\": \"Elementy IFrame nie mogą zostać dodane do biblioteki.\",\n      \"image\": \"Dodawania obrazów do biblioteki nadejdzie wkrótce!\"\n    },\n    \"asyncPasteFailedOnRead\": \"Nie udało się wkleić (nie udało się odczytać ze schowka systemowego).\",\n    \"asyncPasteFailedOnParse\": \"Nie udało się wkleić.\",\n    \"copyToSystemClipboardFailed\": \"Nie udało się skopiować do schowka.\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Zaznaczenie\",\n    \"image\": \"Wstaw obraz\",\n    \"rectangle\": \"Prostokąt\",\n    \"diamond\": \"Romb\",\n    \"ellipse\": \"Elipsa\",\n    \"arrow\": \"Strzałka\",\n    \"line\": \"Linia\",\n    \"freedraw\": \"Rysuj\",\n    \"text\": \"Tekst\",\n    \"library\": \"Biblioteka\",\n    \"lock\": \"Zablokuj wybrane narzędzie\",\n    \"penMode\": \"Tryb pióra — zapobiegaj dotknięciom\",\n    \"link\": \"Dodaj/aktualizuj link dla wybranego kształtu\",\n    \"eraser\": \"Gumka\",\n    \"frame\": \"Ramka\",\n    \"magicframe\": \"Wireframe do kodu\",\n    \"embeddable\": \"Osadzenie z internetu\",\n    \"laser\": \"Wskaźnik laserowy\",\n    \"hand\": \"Ręka (narzędzie do przesuwania)\",\n    \"extraTools\": \"Więcej narzędzi\",\n    \"mermaidToExcalidraw\": \"Konwertuj diagram Mermaid do Excalidraw\",\n    \"magicSettings\": \"Ustawienia AI\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Narzędzia\",\n    \"selectedShapeActions\": \"Wybrane narzędzie\",\n    \"shapes\": \"Kształty\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Aby przesunąć płótno, przytrzymaj kółko myszy lub spację podczas przeciągania, albo użyj narzędzia ręki\",\n    \"linearElement\": \"Naciśnij, aby zrobić punkt, przeciągnij, aby narysować linię\",\n    \"freeDraw\": \"Naciśnij i przeciągnij by rysować, puść kiedy skończysz\",\n    \"text\": \"Wskazówka: możesz również dodać tekst klikając dwukrotnie gdziekolwiek za pomocą narzędzia zaznaczania\",\n    \"embeddable\": \"Kliknij i przeciągnij, aby stworzyć osadzenie strony\",\n    \"text_selected\": \"Kliknij dwukrotnie lub naciśnij ENTER, aby edytować tekst\",\n    \"text_editing\": \"Naciśnij Escape lub Ctrl (Cmd w macOS) + ENTER, aby zakończyć edycję\",\n    \"linearElementMulti\": \"Aby zakończyć krzywą, ponownie kliknij w ostatni punkt, bądź naciśnij Esc albo Enter\",\n    \"lockAngle\": \"Możesz ograniczyć kąt trzymając SHIFT\",\n    \"resize\": \"Możesz zachować proporcję trzymająć wcisnięty SHIFT, przytrzymaj ALT by zmienić rozmiar względem środka\",\n    \"resizeImage\": \"Możesz zmienić rozmiar swobodnie trzymając SHIFT,\\nprzytrzymaj ALT, aby przeskalować względem środka obiektu\",\n    \"rotate\": \"Możesz obracać element w równych odstępach trzymając wciśnięty SHIFT\",\n    \"lineEditor_info\": \"Przytrzymaj CtrlOrCmd i kliknij dwukrotnie lub naciśnij CtrlOrCmd + Enter, aby edytować punkty\",\n    \"lineEditor_pointSelected\": \"Naciśnij przycisk Delete, aby usunąć punkt. Ctrl/Cmd+D, aby go zduplikować. Przeciągnij, aby go przenieść\",\n    \"lineEditor_nothingSelected\": \"Wybierz punkt do edycji (przytrzymaj SHIFT, aby wybrać wiele),\\nlub przytrzymaj Alt i kliknij, aby dodać nowe punkty\",\n    \"placeImage\": \"Kliknij, aby umieścić obraz, lub kliknij i przeciągnij, aby ustawić jego rozmiar ręcznie\",\n    \"publishLibrary\": \"Opublikuj własną bibliotekę\",\n    \"bindTextToElement\": \"Wciśnij enter, aby dodać tekst\",\n    \"deepBoxSelect\": \"Przytrzymaj CtrlOrCmd, aby wybrać w obrębie grupy i uniknąć przeciągania\",\n    \"eraserRevert\": \"Przytrzymaj Alt, aby przywrócić elementy oznaczone do usunięcia\",\n    \"firefox_clipboard_write\": \"Ta funkcja może być włączona poprzez ustawienie flagi \\\"dom.events.asyncClipboard.clipboardItem\\\" na \\\"true\\\". Aby zmienić flagi przeglądarki w Firefox, odwiedź stronę \\\"about:config\\\".\",\n    \"disableSnapping\": \"Przytrzymaj Ctrl lub Cmd, aby wyłączyć przyciąganie\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Nie można wyświetlić podglądu\",\n    \"canvasTooBig\": \"Obszar roboczy może być za duży.\",\n    \"canvasTooBigTip\": \"Wskazówka: spróbuj nieco zbliżyć najdalej wysunięte elementy.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Wystąpił błąd. Spróbuj <button>odświeżyć stronę.</button>\",\n    \"clearCanvasMessage\": \"Jeśli odświeżenie strony nie zadziałało, spróbuj <button>usunąć wszystko z dokumentu.</button>\",\n    \"clearCanvasCaveat\": \" Pamiętaj tylko, że spowoduje to utratę całej twojej pracy \",\n    \"trackedToSentry\": \"Błąd o identyfikatorze {{eventId}} został zaraportowany w naszym systemie.\",\n    \"openIssueMessage\": \"Szanujemy twoją prywatność i raport nie zawierał żadnych danych dotyczących tego nad czym pracowałeś, natomiast jeżeli jesteś w stanie podzielić się tym nad czym pracowałeś, prosimy o dodatkowy raport poprzez <button>nasze narzędzie do raportowania błędów.</button> Prosimy o dołączenie poniższej informacji poprzez skopiowanie jej i umieszczenie jej w zgłoszeniu na portalu GitHub.\",\n    \"sceneContent\": \"Zawartość dokumentu:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Będziesz w stanie pracować wraz z osobami które zaprosisz do współpracy.\",\n    \"desc_privacy\": \"By zapewnić Ci prywatność, sesja współpracy na żywo jest zabezpieczona szyfrowaniem end-to-end, co oznacza, że poza tobą i osobami z którymi podzielisz się linkiem, nikt nie ma dostępu do tego co będziecie tworzyć.\",\n    \"button_startSession\": \"Rozpocznij sesję\",\n    \"button_stopSession\": \"Zakończ sesję\",\n    \"desc_inProgressIntro\": \"Sesja współpracy na żywo właśnie się rozpoczęła.\",\n    \"desc_shareLink\": \"Udostępnij ten link osobom, z którymi chcesz współpracować:\",\n    \"desc_exitSession\": \"Zakończenie sesji spowoduje odłączenie ciebie od pokoju, ale nadal będziesz mógł lokalnie kontynuować pracę. Zauważ, że osoby z którymi współpracowałeś nadal będą mogły współpracować.\",\n    \"shareTitle\": \"Dołącz do sesji współpracy na żywo w Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Wystąpił błąd\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Zapisz na dysku\",\n    \"disk_details\": \"Eksportuj dane sceny do pliku, z którego możesz importować później.\",\n    \"disk_button\": \"Zapisz do pliku\",\n    \"link_title\": \"Link do udostępnienia\",\n    \"link_details\": \"Eksportuj jako link tylko do odczytu.\",\n    \"link_button\": \"Wygeneruj link\",\n    \"excalidrawplus_description\": \"Zapisz scenę do swojego obszaru roboczego Excalidraw+.\",\n    \"excalidrawplus_button\": \"Eksportuj\",\n    \"excalidrawplus_exportError\": \"W tej chwili nie można wyeksportować do Excalidraw+...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Przeczytaj na naszym blogu\",\n    \"click\": \"kliknięcie\",\n    \"deepSelect\": \"Wybór w obrębie grupy\",\n    \"deepBoxSelect\": \"Wybór w obrębie grupy i unikanie przeciągania\",\n    \"curvedArrow\": \"Zakrzywiona strzałka\",\n    \"curvedLine\": \"Zakrzywiona linia\",\n    \"documentation\": \"Dokumentacja\",\n    \"doubleClick\": \"podwójne kliknięcie\",\n    \"drag\": \"przeciągnij\",\n    \"editor\": \"Edytor\",\n    \"editLineArrowPoints\": \"Edytuj punkty linii/strzałki\",\n    \"editText\": \"Edytuj tekst/dodaj etykietę\",\n    \"github\": \"Znalazłeś problem? Prześlij\",\n    \"howto\": \"Skorzystaj z instrukcji\",\n    \"or\": \"lub\",\n    \"preventBinding\": \"Zapobiegaj wiązaniu strzałek\",\n    \"tools\": \"Narzędzia\",\n    \"shortcuts\": \"Skróty klawiszowe\",\n    \"textFinish\": \"Zakończ edycję (edytor tekstu)\",\n    \"textNewLine\": \"Dodaj nowy wiersz (edytor tekstu)\",\n    \"title\": \"Pomoc\",\n    \"view\": \"Widok\",\n    \"zoomToFit\": \"Powiększ, aby wyświetlić wszystkie elementy\",\n    \"zoomToSelection\": \"Przybliż do zaznaczenia\",\n    \"toggleElementLock\": \"Zablokuj/odblokuj zaznaczenie\",\n    \"movePageUpDown\": \"Przesuń stronę w górę/w dół\",\n    \"movePageLeftRight\": \"Przenieś stronę w lewo/prawo\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Wyczyść płótno\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Opublikuj bibliotekę\",\n    \"itemName\": \"Nazwa elementu\",\n    \"authorName\": \"Nazwa autora\",\n    \"githubUsername\": \"Nazwa użytkownika na GitHubie\",\n    \"twitterUsername\": \"Nazwa użytkownika Twitter\",\n    \"libraryName\": \"Nazwa biblioteki\",\n    \"libraryDesc\": \"Opis biblioteki\",\n    \"website\": \"Strona internetowa\",\n    \"placeholder\": {\n      \"authorName\": \"Twoje imię lub nazwa użytkownika\",\n      \"libraryName\": \"Nazwa twojej biblioteki\",\n      \"libraryDesc\": \"Opis twojej biblioteki, aby pomóc innym zrozumieć jej działanie\",\n      \"githubHandle\": \"Uchwyt GitHub (opcjonalny), dzięki czemu możesz edytować bibliotekę po przesłaniu do sprawdzenia\",\n      \"twitterHandle\": \"Nazwa użytkownika w serwisie Twitter (opcjonalna), aby wiedzieć kogo oznaczyć przy promowaniu na Twitterze\",\n      \"website\": \"Link do Twojej osobistej strony internetowej lub gdzie indziej (opcjonalnie)\"\n    },\n    \"errors\": {\n      \"required\": \"Wymagane\",\n      \"website\": \"Wprowadź prawidłowy adres URL\"\n    },\n    \"noteDescription\": \"<link></link>dla innych osób do wykorzystania w swoich rysunkach.\",\n    \"noteGuidelines\": \"Biblioteka musi być najpierw zatwierdzona ręcznie. Przeczytaj <link>wytyczne</link>\",\n    \"noteLicense\": \"Wysyłając zgadzasz się, że biblioteka zostanie opublikowana pod <link>Licencja MIT, </link>w skrócie, każdy może z nich korzystać bez ograniczeń.\",\n    \"noteItems\": \"Każdy element biblioteki musi mieć własną nazwę, aby był filtrowalny. Uwzględnione zostaną następujące elementy biblioteki:\",\n    \"atleastOneLibItem\": \"Proszę wybrać co najmniej jeden element biblioteki, by rozpocząć\",\n    \"republishWarning\": \"Uwaga: niektóre z wybranych elementów są oznaczone jako już opublikowane/wysłane. Powinieneś ponownie przesłać elementy tylko wtedy, gdy aktualizujesz istniejącą bibliotekę lub zgłoszenie.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Biblioteka została przesłana\",\n    \"content\": \"Dziękujemy {{authorName}}. Twoja biblioteka została przesłana do sprawdzenia. Możesz śledzić jej stan<link>tutaj</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Zresetuj Bibliotekę\",\n    \"removeItemsFromLib\": \"Usuń wybrane elementy z biblioteki\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Eksportuj obraz\",\n    \"label\": {\n      \"withBackground\": \"Tło\",\n      \"onlySelected\": \"Tylko wybrane\",\n      \"darkMode\": \"Tryb ciemny\",\n      \"embedScene\": \"Osadź scenę\",\n      \"scale\": \"Skala\",\n      \"padding\": \"Dopełnienie\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"Dane sceny zostaną zapisane w eksportowanym pliku PNG/SVG tak, aby scena mogła zostać z niego przywrócona.\\nZwiększy to rozmiar eksportowanego pliku.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Zapisz jako PNG\",\n      \"exportToSvg\": \"Zapisz jako SVG\",\n      \"copyPngToClipboard\": \"Skopiuj do schowka jako PNG\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Skopiuj do schowka\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Twoje rysunki są zabezpieczone szyfrowaniem end-to-end, tak więc nawet w Excalidraw nie jesteśmy w stanie zobaczyć tego co tworzysz.\",\n    \"link\": \"Wpis na blogu dotyczący szyfrowania end-to-end w Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Kąt\",\n    \"element\": \"Element\",\n    \"elements\": \"Elementy\",\n    \"height\": \"Wysokość\",\n    \"scene\": \"Scena\",\n    \"selected\": \"Zaznaczenie\",\n    \"storage\": \"Pamięć\",\n    \"title\": \"Statystyki dla nerdów\",\n    \"total\": \"Łącznie\",\n    \"version\": \"Wersja\",\n    \"versionCopy\": \"Kliknij, aby skopiować\",\n    \"versionNotAvailable\": \"Wersja niedostępna\",\n    \"width\": \"Szerokość\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Dodano do biblioteki\",\n    \"copyStyles\": \"Skopiowano style.\",\n    \"copyToClipboard\": \"Skopiowano do schowka.\",\n    \"copyToClipboardAsPng\": \"Skopiowano {{exportSelection}} do schowka jako PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Zapisano plik.\",\n    \"fileSavedToFilename\": \"Zapisano jako {filename}\",\n    \"canvas\": \"płótno\",\n    \"selection\": \"zaznaczenie\",\n    \"pasteAsSingleElement\": \"Użyj {{shortcut}}, aby wkleić jako pojedynczy element,\\nlub wklej do istniejącego edytora tekstu\",\n    \"unableToEmbed\": \"Osadzenie tego linku jest obecnie niedozwolone. Zgłoś propozycję na portalu GitHub, aby dodać go do listy dozwolonych wyjątków\",\n    \"unrecognizedLinkFormat\": \"Osadzony link ma niewłaściwy format. Spróbuj wkleić całą zawartość pola \\\"embed\\\" z oryginalnej strony.\"\n  },\n  \"colors\": {\n    \"transparent\": \"Przezroczysty\",\n    \"black\": \"Czarny\",\n    \"white\": \"Biały\",\n    \"red\": \"Czerwony\",\n    \"pink\": \"Różowy\",\n    \"grape\": \"Winogronowy\",\n    \"violet\": \"Fioletowy\",\n    \"gray\": \"Szary\",\n    \"blue\": \"Niebieski\",\n    \"cyan\": \"Cyjanowy\",\n    \"teal\": \"Turkusowy\",\n    \"green\": \"Zielony\",\n    \"yellow\": \"Żółty\",\n    \"orange\": \"Pomarańczowy\",\n    \"bronze\": \"Brązowy\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Wszystkie dane są zapisywane lokalnie w przeglądarce.\",\n      \"center_heading_plus\": \"Czy zamiast tego chcesz przejść do Excalidraw+?\",\n      \"menuHint\": \"Eksportuj, preferencje, języki...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Eksportuj, preferencje i więcej...\",\n      \"center_heading\": \"Schematy uproszczone.\",\n      \"toolbarHint\": \"Wybierz narzędzie i zacznij rysować!\",\n      \"helpHint\": \"Skróty klawiaturowe i pomoc\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Najczęściej używane kolory\",\n    \"colors\": \"Kolory\",\n    \"shades\": \"Odcienie\",\n    \"hexCode\": \"Kod HEX\",\n    \"noShades\": \"Brak dostępnych odcieni dla tego koloru\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Eksportuj jako obraz\",\n        \"button\": \"Eksportuj jako obraz\",\n        \"description\": \"Eksportuj zawartość sceny jako obraz z możliwością importowania.\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Zapisz na dysku\",\n        \"button\": \"Zapisz na dysku\",\n        \"description\": \"Eksportuj zawartość sceny jako plik z możliwością importowania.\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Eksportuj do Excalidraw+\",\n        \"description\": \"Zapisz scenę do swojego obszaru roboczego Excalidraw+.\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Wczytaj z pliku\",\n        \"button\": \"Wczytaj z pliku\",\n        \"description\": \"Wczytanie z pliku <bold>nadpisze istniejącą zawartość</bold>.<br></br>Możesz najpierw utworzyć kopię zapasową swojego rysunku, używając jednej z poniższych opcji.\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Wczytaj z linku\",\n        \"button\": \"Nadpisz moją zawartość\",\n        \"description\": \"Wczytanie zewnętrznego pliku <bold>nadpisze istniejącą zawartość</bold>.<br></br>Możesz najpierw utworzyć kopię zapasową swojego rysunku, używając jednej z poniższych opcji.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"Konwertuj diagram Mermaid do Excalidraw\",\n    \"button\": \"Wstaw\",\n    \"description\": \"Obecnie wspierane są jedynie <flowchartLink>proste grafy</flowchartLink>, <sequenceLink>sekwencje</sequenceLink> i <classLink>diagramy klas</classLink>. Pozostałe typy będą wyświetlane jako obrazy w Excalidraw.\",\n    \"syntax\": \"Składnia diagramów Mermaid\",\n    \"preview\": \"Podgląd\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}