{"version": 3, "sources": ["../../../locales/pt-BR.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Colar\",\n    \"pasteAsPlaintext\": \"Colar como texto sem formatação\",\n    \"pasteCharts\": \"Colar gráficos\",\n    \"selectAll\": \"Selecionar tudo\",\n    \"multiSelect\": \"Adicionar elemento à seleção\",\n    \"moveCanvas\": \"Mover tela\",\n    \"cut\": \"Recortar\",\n    \"copy\": \"Copiar\",\n    \"copyAsPng\": \"Copiar para a área de transferência como PNG\",\n    \"copyAsSvg\": \"Copiar para a área de transferência como SVG\",\n    \"copyText\": \"Copiar para área de transferência como texto\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Trazer para a frente\",\n    \"sendToBack\": \"Enviar para o fundo\",\n    \"bringToFront\": \"Trazer para o primeiro plano\",\n    \"sendBackward\": \"Enviar para trás\",\n    \"delete\": \"Apagar\",\n    \"copyStyles\": \"Copiar os estilos\",\n    \"pasteStyles\": \"Colar os estilos\",\n    \"stroke\": \"Contorno\",\n    \"background\": \"Fundo\",\n    \"fill\": \"Preenchimento\",\n    \"strokeWidth\": \"Espessura do traço\",\n    \"strokeStyle\": \"Estilo de traço\",\n    \"strokeStyle_solid\": \"Sólido\",\n    \"strokeStyle_dashed\": \"Tracejado\",\n    \"strokeStyle_dotted\": \"Pontilhado\",\n    \"sloppiness\": \"Precisão do traço\",\n    \"opacity\": \"Opacidade\",\n    \"textAlign\": \"Alinhamento do texto\",\n    \"edges\": \"Arestas\",\n    \"sharp\": \"Pontudo\",\n    \"round\": \"Arredondado\",\n    \"arrowheads\": \"Pontas\",\n    \"arrowhead_none\": \"Nenhuma\",\n    \"arrowhead_arrow\": \"Flecha\",\n    \"arrowhead_bar\": \"Barra\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Triângulo\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Tamanho da fonte\",\n    \"fontFamily\": \"Família da fonte\",\n    \"addWatermark\": \"Adicionar \\\"Feito com Excalidraw\\\"\",\n    \"handDrawn\": \"Manuscrito\",\n    \"normal\": \"Normal\",\n    \"code\": \"Código\",\n    \"small\": \"Pequeno\",\n    \"medium\": \"Médio\",\n    \"large\": \"Grande\",\n    \"veryLarge\": \"Muito grande\",\n    \"solid\": \"Sólido\",\n    \"hachure\": \"Hachura\",\n    \"zigzag\": \"Zigue-zague\",\n    \"crossHatch\": \"Hachura cruzada\",\n    \"thin\": \"Fino\",\n    \"bold\": \"Espesso\",\n    \"left\": \"Esquerda\",\n    \"center\": \"Centralizar\",\n    \"right\": \"Direita\",\n    \"extraBold\": \"Muito espesso\",\n    \"architect\": \"Arquiteto\",\n    \"artist\": \"Artista\",\n    \"cartoonist\": \"Cartunista\",\n    \"fileTitle\": \"Nome do arquivo\",\n    \"colorPicker\": \"Seletor de cores\",\n    \"canvasColors\": \"Usado na tela\",\n    \"canvasBackground\": \"Fundo da tela\",\n    \"drawingCanvas\": \"Tela de desenho\",\n    \"layers\": \"Camadas\",\n    \"actions\": \"Ações\",\n    \"language\": \"Idioma\",\n    \"liveCollaboration\": \"Colaboração ao vivo...\",\n    \"duplicateSelection\": \"Duplicar\",\n    \"untitled\": \"Sem título\",\n    \"name\": \"Nome\",\n    \"yourName\": \"Seu nome\",\n    \"madeWithExcalidraw\": \"Feito com Excalidraw\",\n    \"group\": \"Agrupar seleção\",\n    \"ungroup\": \"Desagrupar seleção\",\n    \"collaborators\": \"Colaboradores\",\n    \"showGrid\": \"Mostrar grade\",\n    \"addToLibrary\": \"Adicionar à biblioteca\",\n    \"removeFromLibrary\": \"Remover da biblioteca\",\n    \"libraryLoadingMessage\": \"Carregando biblioteca…\",\n    \"libraries\": \"Procurar bibliotecas\",\n    \"loadingScene\": \"Carregando cena…\",\n    \"align\": \"Alinhamento\",\n    \"alignTop\": \"Alinhar ao topo\",\n    \"alignBottom\": \"Alinhar embaixo\",\n    \"alignLeft\": \"Alinhar à esquerda\",\n    \"alignRight\": \"Alinhar à direita\",\n    \"centerVertically\": \"Centralizar verticalmente\",\n    \"centerHorizontally\": \"Centralizar horizontalmente\",\n    \"distributeHorizontally\": \"Distribuir horizontalmente\",\n    \"distributeVertically\": \"Distribuir verticalmente\",\n    \"flipHorizontal\": \"Inverter horizontalmente\",\n    \"flipVertical\": \"Inverter verticalmente\",\n    \"viewMode\": \"Modo de visualização\",\n    \"share\": \"Compartilhar\",\n    \"showStroke\": \"Exibir seletor de cores do traço\",\n    \"showBackground\": \"Exibir seletor de cores do fundo\",\n    \"toggleTheme\": \"Alternar tema\",\n    \"personalLib\": \"Biblioteca Pessoal\",\n    \"excalidrawLib\": \"Biblioteca do Excalidraw\",\n    \"decreaseFontSize\": \"Diminuir o tamanho da fonte\",\n    \"increaseFontSize\": \"Aumentar o tamanho da fonte\",\n    \"unbindText\": \"Desvincular texto\",\n    \"bindText\": \"Vincular texto ao contêiner\",\n    \"createContainerFromText\": \"Envolver texto em um contêiner\",\n    \"link\": {\n      \"edit\": \"Editar link\",\n      \"editEmbed\": \"\",\n      \"create\": \"Criar link\",\n      \"createEmbed\": \"\",\n      \"label\": \"Link\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Editar linha\",\n      \"exit\": \"Sair do editor de linha\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Bloquear\",\n      \"unlock\": \"Desbloquear\",\n      \"lockAll\": \"Bloquear tudo\",\n      \"unlockAll\": \"Desbloquear tudo\"\n    },\n    \"statusPublished\": \"Publicado\",\n    \"sidebarLock\": \"Manter barra lateral aberta\",\n    \"selectAllElementsInFrame\": \"Selecionar todos os elementos no quadro\",\n    \"removeAllElementsFromFrame\": \"Remover todos os elementos do quadro\",\n    \"eyeDropper\": \"Escolher cor da tela\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Nenhum item adicionado ainda...\",\n    \"hint_emptyLibrary\": \"Selecione um item na tela para adicioná-lo aqui, ou instale uma biblioteca do repositório público, abaixo.\",\n    \"hint_emptyPrivateLibrary\": \"Selecione um item na tela para adicioná-lo aqui.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Limpar o canvas e redefinir a cor de fundo\",\n    \"exportJSON\": \"Exportar arquivo\",\n    \"exportImage\": \"Exportar imagem...\",\n    \"export\": \"Salvar como...\",\n    \"copyToClipboard\": \"Copiar para o clipboard\",\n    \"save\": \"Salvar para o arquivo atual\",\n    \"saveAs\": \"Salvar como\",\n    \"load\": \"Abrir\",\n    \"getShareableLink\": \"Obter um link de compartilhamento\",\n    \"close\": \"Fechar\",\n    \"selectLanguage\": \"Selecionar idioma\",\n    \"scrollBackToContent\": \"Voltar para o conteúdo\",\n    \"zoomIn\": \"Aumentar zoom\",\n    \"zoomOut\": \"Diminuir zoom\",\n    \"resetZoom\": \"Redefinir zoom\",\n    \"menu\": \"Menu\",\n    \"done\": \"Concluído\",\n    \"edit\": \"Editar\",\n    \"undo\": \"Desfazer\",\n    \"redo\": \"Refazer\",\n    \"resetLibrary\": \"Redefinir biblioteca\",\n    \"createNewRoom\": \"Criar nova sala\",\n    \"fullScreen\": \"Tela cheia\",\n    \"darkMode\": \"Modo escuro\",\n    \"lightMode\": \"Modo claro\",\n    \"zenMode\": \"Modo Zen\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Sair do modo zen\",\n    \"cancel\": \"Cancelar\",\n    \"clear\": \"Limpar\",\n    \"remove\": \"Remover\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Publicar\",\n    \"submit\": \"Enviar\",\n    \"confirm\": \"Confirmar\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Isto irá limpar toda a tela. Você tem certeza?\",\n    \"couldNotCreateShareableLink\": \"Não foi possível criar um link de compartilhamento.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Não foi possível criar um link compartilhável: a cena é muito grande\",\n    \"couldNotLoadInvalidFile\": \"Não foi possível carregar o arquivo inválido\",\n    \"importBackendFailed\": \"A importação do servidor falhou.\",\n    \"cannotExportEmptyCanvas\": \"Não é possível exportar um canvas vazio.\",\n    \"couldNotCopyToClipboard\": \"Não foi possível copiar para a área de transferência.\",\n    \"decryptFailed\": \"Não foi possível descriptografar os dados.\",\n    \"uploadedSecurly\": \"O upload foi protegido com criptografia de ponta a ponta, o que significa que o servidor do Excalidraw e terceiros não podem ler o conteúdo.\",\n    \"loadSceneOverridePrompt\": \"Carregar um desenho externo substituirá o seu conteúdo existente. Deseja continuar?\",\n    \"collabStopOverridePrompt\": \"Ao interromper a sessão, você substituirá seu desenho anterior, armazenado localmente. Você tem certeza?\\n\\n(Se você deseja manter seu desenho local, simplesmente feche a aba do navegador.)\",\n    \"errorAddingToLibrary\": \"Não foi possível adicionar o item à biblioteca\",\n    \"errorRemovingFromLibrary\": \"Não foi possível remover o item da biblioteca\",\n    \"confirmAddLibrary\": \"Isso adicionará {{numShapes}} forma(s) à sua biblioteca. Tem certeza?\",\n    \"imageDoesNotContainScene\": \"Esta imagem parece não conter dados de cenas. Você ativou a incorporação da cena durante a exportação?\",\n    \"cannotRestoreFromImage\": \"Não foi possível restaurar a cena deste arquivo de imagem\",\n    \"invalidSceneUrl\": \"Não foi possível importar a cena da URL fornecida. Ela está incompleta ou não contém dados JSON válidos do Excalidraw.\",\n    \"resetLibrary\": \"Isto limpará a sua biblioteca. Você tem certeza?\",\n    \"removeItemsFromsLibrary\": \"Excluir {{count}} item(ns) da biblioteca?\",\n    \"invalidEncryptionKey\": \"A chave de encriptação deve ter 22 caracteres. A colaboração ao vivo está desabilitada.\",\n    \"collabOfflineWarning\": \"Sem conexão com a internet disponível.\\nSuas alterações não serão salvas!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Tipo de arquivo não suportado.\",\n    \"imageInsertError\": \"Não foi possível inserir imagem. Tente novamente mais tarde...\",\n    \"fileTooBig\": \"O arquivo é muito grande. O tamanho máximo permitido é {{maxSize}}.\",\n    \"svgImageInsertError\": \"Não foi possível inserir a imagem SVG. A marcação SVG parece inválida.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"SVG Inválido.\",\n    \"cannotResolveCollabServer\": \"Não foi possível conectar-se ao servidor colaborativo. Por favor, recarregue a página e tente novamente.\",\n    \"importLibraryError\": \"Não foi possível carregar a biblioteca\",\n    \"collabSaveFailed\": \"Não foi possível salvar no banco de dados do servidor. Se os problemas persistirem, salve o arquivo localmente para garantir que não perca o seu trabalho.\",\n    \"collabSaveFailed_sizeExceeded\": \"Não foi possível salvar no banco de dados do servidor, a tela parece ser muito grande. Se os problemas persistirem, salve o arquivo localmente para garantir que não perca o seu trabalho.\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Parece que você está usando o navegador Brave com a configuração <bold>Bloquear Impressões Digitais</bold> no modo agressivo.\",\n      \"line2\": \"Isso pode acabar quebrando <bold>Elementos de Texto</bold> em seus desenhos.\",\n      \"line3\": \"Recomendamos fortemente desativar essa configuração. Você pode acessar o <link>passo a passo</link> sobre como fazer isso.\",\n      \"line4\": \"Se desativar essa configuração não corrigir a exibição de elementos de texto, por favor abra uma <issueLink>issue</issueLink> em nosso GitHub, ou mande uma mensagem em nosso <discordLink>Discord</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Seleção\",\n    \"image\": \"Inserir imagem\",\n    \"rectangle\": \"Retângulo\",\n    \"diamond\": \"Losango\",\n    \"ellipse\": \"Elipse\",\n    \"arrow\": \"Flecha\",\n    \"line\": \"Linha\",\n    \"freedraw\": \"Desenhar\",\n    \"text\": \"Texto\",\n    \"library\": \"Biblioteca\",\n    \"lock\": \"Manter ativa a ferramenta selecionada após desenhar\",\n    \"penMode\": \"Modo caneta — impede o toque\",\n    \"link\": \"Adicionar/Atualizar link para uma forma selecionada\",\n    \"eraser\": \"Borracha\",\n    \"frame\": \"Ferramenta de quadro\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"Mão (ferramenta de rolagem)\",\n    \"extraTools\": \"Mais ferramentas\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Ações da tela\",\n    \"selectedShapeActions\": \"Ações das formas selecionadas\",\n    \"shapes\": \"Formas\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Para mover a tela, segure a roda do mouse ou a barra de espaço enquanto arrasta ou use a ferramenta de mão\",\n    \"linearElement\": \"Clique para iniciar vários pontos, arraste para uma única linha\",\n    \"freeDraw\": \"Toque e arraste, solte quando terminar\",\n    \"text\": \"Dica: você também pode adicionar texto clicando duas vezes em qualquer lugar com a ferramenta de seleção\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"Clique duplo ou tecle ENTER para editar o texto\",\n    \"text_editing\": \"Pressione Esc ou Ctrl/Cmd+ENTER para encerrar a edição\",\n    \"linearElementMulti\": \"Clique no último ponto ou pressione Escape ou Enter para terminar\",\n    \"lockAngle\": \"Você pode restringir o ângulo segurando o SHIFT\",\n    \"resize\": \"Você pode restringir proporções segurando SHIFT enquanto redimensiona,\\nsegure ALT para redimensionar do centro\",\n    \"resizeImage\": \"Você pode redimensionar livremente segurando SHIFT,\\nsegure ALT para redimensionar a partir do centro\",\n    \"rotate\": \"Você pode restringir os ângulos segurando SHIFT enquanto gira\",\n    \"lineEditor_info\": \"Pressione CtrlOuCmd e duplo-clique ou pressione CtrlOuCmd + Enter para editar pontos\",\n    \"lineEditor_pointSelected\": \"Pressione Delete para remover o(s) ponto(s),\\nCtrl/Cmd+D para duplicar ou arraste para mover\",\n    \"lineEditor_nothingSelected\": \"Selecione um ponto para editar (segure SHIFT para selecionar vários) ou segure Alt e clique para adicionar novos pontos\",\n    \"placeImage\": \"Clique para colocar a imagem, ou clique e arraste para definir manualmente o seu tamanho\",\n    \"publishLibrary\": \"Publicar sua própria biblioteca\",\n    \"bindTextToElement\": \"Pressione Enter para adicionar o texto\",\n    \"deepBoxSelect\": \"Segure Ctrl/Cmd para seleção profunda e para evitar arrastar\",\n    \"eraserRevert\": \"Segure a tecla Alt para inverter os elementos marcados para exclusão\",\n    \"firefox_clipboard_write\": \"Esse recurso pode ser ativado configurando a opção \\\"dom.events.asyncClipboard.clipboardItem\\\" como \\\"true\\\". Para alterar os sinalizadores do navegador no Firefox, visite a página \\\"about:config\\\".\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Não é possível mostrar pré-visualização\",\n    \"canvasTooBig\": \"A tela pode ser muito grande.\",\n    \"canvasTooBigTip\": \"Dica: tente aproximar um pouco os elementos mais distantes.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Foi encontrado um erro. Tente <button>recarregar a página.</button>\",\n    \"clearCanvasMessage\": \"Se recarregar a página não funcionar, tente <button>limpando a tela.</button>\",\n    \"clearCanvasCaveat\": \" Isso resultará em perda de trabalho \",\n    \"trackedToSentry\": \"O erro com o identificador {{eventId}} foi rastreado no nosso sistema.\",\n    \"openIssueMessage\": \"Fomos muito cautelosos para não incluir suas informações de cena no erro. Se sua cena não for privada, por favor, considere seguir nosso <button>rastreador de bugs.</button> Por favor, inclua informações abaixo, copiando e colando para a issue do GitHub.\",\n    \"sceneContent\": \"Conteúdo da cena:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Você pode convidar pessoas para sua cena atual para colaborar com você.\",\n    \"desc_privacy\": \"Não se preocupe, a sessão usa criptografia de ponta a ponta; portanto, o que você desenhar permanecerá privado. Nem mesmo nosso servidor poderá ver o que você cria.\",\n    \"button_startSession\": \"Iniciar sessão\",\n    \"button_stopSession\": \"Parar sessão\",\n    \"desc_inProgressIntro\": \"A sessão de colaboração ao vivo está agora em andamento.\",\n    \"desc_shareLink\": \"Compartilhe este link com qualquer pessoa com quem você queira colaborar:\",\n    \"desc_exitSession\": \"Interrompendo a sessão você irá se desconectar da sala, mas você poderá continuar trabalhando com a cena localmente. Observe que isso não afetará outras pessoas, e elas ainda poderão colaborar em suas versões.\",\n    \"shareTitle\": \"Participe de uma sessão ao vivo de colaboração no Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Erro\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Salvar no computador\",\n    \"disk_details\": \"Exportar os dados da cena para um arquivo que você poderá importar mais tarde.\",\n    \"disk_button\": \"Salvar em um arquivo\",\n    \"link_title\": \"Link compartilhável\",\n    \"link_details\": \"Exportar como link de apenas leitura.\",\n    \"link_button\": \"Exportar link\",\n    \"excalidrawplus_description\": \"Salvar a cena na sua área de trabalho Excalidraw+.\",\n    \"excalidrawplus_button\": \"Exportar\",\n    \"excalidrawplus_exportError\": \"Não é possível exportar para o Excalidraw+ neste momento...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Leia o nosso blog\",\n    \"click\": \"clicar\",\n    \"deepSelect\": \"Seleção profunda\",\n    \"deepBoxSelect\": \"Use a seleção profunda dentro da caixa para previnir arrastar\",\n    \"curvedArrow\": \"Seta curva\",\n    \"curvedLine\": \"Linha curva\",\n    \"documentation\": \"Documentação\",\n    \"doubleClick\": \"clique duplo\",\n    \"drag\": \"arrastar\",\n    \"editor\": \"Editor\",\n    \"editLineArrowPoints\": \"Editar linha/ponta da seta\",\n    \"editText\": \"Editar texto / adicionar etiqueta\",\n    \"github\": \"Encontrou algum problema? Nos informe\",\n    \"howto\": \"Siga nossos guias\",\n    \"or\": \"ou\",\n    \"preventBinding\": \"Evitar fixação de seta\",\n    \"tools\": \"Ferramentas\",\n    \"shortcuts\": \"Atalhos de teclado\",\n    \"textFinish\": \"Encerrar edição (editor de texto)\",\n    \"textNewLine\": \"Adicionar nova linha (editor de texto)\",\n    \"title\": \"Ajudar\",\n    \"view\": \"Visualizar\",\n    \"zoomToFit\": \"Ampliar para encaixar todos os elementos\",\n    \"zoomToSelection\": \"Ampliar a seleção\",\n    \"toggleElementLock\": \"Bloquear/desbloquear seleção\",\n    \"movePageUpDown\": \"Mover a página para cima/baixo\",\n    \"movePageLeftRight\": \"Mover a página para esquerda/direita\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Limpar a tela\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Publicar biblioteca\",\n    \"itemName\": \"Nome do item\",\n    \"authorName\": \"Nome do autor\",\n    \"githubUsername\": \"Nome de usuário do GitHub\",\n    \"twitterUsername\": \"Nome de usuário do Twitter\",\n    \"libraryName\": \"Nome da Biblioteca\",\n    \"libraryDesc\": \"Descrição da biblioteca\",\n    \"website\": \"Site\",\n    \"placeholder\": {\n      \"authorName\": \"Seu nome ou nome de usuário\",\n      \"libraryName\": \"Nome da sua biblioteca\",\n      \"libraryDesc\": \"Descrição para ajudar as pessoas a entenderem o uso da sua da sua biblioteca\",\n      \"githubHandle\": \"Identificador do GitHub (opcional), para que você possa editar a biblioteca depois de enviar para revisão\",\n      \"twitterHandle\": \"Nome de usuário do Twitter (opcional), para que saibamos quem deve ser creditado se promovermos no Twitter\",\n      \"website\": \"Link para o seu site pessoal ou outro lugar (opcional)\"\n    },\n    \"errors\": {\n      \"required\": \"Obrigatório\",\n      \"website\": \"Informe uma URL válida\"\n    },\n    \"noteDescription\": \"Envie sua biblioteca para ser incluída no <link>repositório de biblioteca pública</link>para outras pessoas usarem em seus desenhos.\",\n    \"noteGuidelines\": \"A biblioteca precisa ser aprovada manualmente primeiro. Por favor leia o <link>orientações</link> antes de enviar. Você precisará de uma conta do GitHub para se comunicar e fazer alterações quando solicitado, mas não é estritamente necessário.\",\n    \"noteLicense\": \"Ao enviar, você concorda que a biblioteca será publicada sob a <link>Licença MIT, </link>o que, em suma, significa que qualquer pessoa pode utilizá-los sem restrições.\",\n    \"noteItems\": \"Cada item da biblioteca deve ter seu próprio nome para que seja filtrável. Os seguintes itens da biblioteca serão incluídos:\",\n    \"atleastOneLibItem\": \"Por favor, selecione pelo menos um item da biblioteca para começar\",\n    \"republishWarning\": \"Nota: alguns dos itens selecionados estão marcados como já publicado/enviado. Você só deve reenviar itens ao atualizar uma biblioteca existente ou submissão.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Biblioteca enviada\",\n    \"content\": \"Obrigado {{authorName}}. Sua biblioteca foi enviada para análise. Você pode acompanhar o status<link>aqui</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Redefinir biblioteca\",\n    \"removeItemsFromLib\": \"Remover itens selecionados da biblioteca\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Exportar imagem\",\n    \"label\": {\n      \"withBackground\": \"Fundo\",\n      \"onlySelected\": \"Somente selecionados\",\n      \"darkMode\": \"Modo escuro\",\n      \"embedScene\": \"Incorporar cena\",\n      \"scale\": \"Escala\",\n      \"padding\": \"Margem interna\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"Os dados da cena serão salvos no arquivo PNG/SVG exportado para que a cena possa ser restaurada a partir dele.\\nIsso aumentará o tamanho do arquivo exportado.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Exportar como PNG\",\n      \"exportToSvg\": \"Exportar como SVG\",\n      \"copyPngToClipboard\": \"Copiar PNG para área de transferência\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Copiar para a área de transferência\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Seus desenhos são criptografados de ponta a ponta, então os servidores do Excalidraw nunca os verão.\",\n    \"link\": \"Publicação de blog com criptografia de ponta a ponta no Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Ângulo\",\n    \"element\": \"Elemento\",\n    \"elements\": \"Elementos\",\n    \"height\": \"Altura\",\n    \"scene\": \"Cena\",\n    \"selected\": \"Selecionado\",\n    \"storage\": \"Armazenamento\",\n    \"title\": \"Estatísticas para nerds\",\n    \"total\": \"Total\",\n    \"version\": \"Versão\",\n    \"versionCopy\": \"Clique para copiar\",\n    \"versionNotAvailable\": \"Versão não disponível\",\n    \"width\": \"Largura\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Adicionado à biblioteca\",\n    \"copyStyles\": \"Estilos copiados.\",\n    \"copyToClipboard\": \"Copiado para área de transferência.\",\n    \"copyToClipboardAsPng\": \"{{exportSelection}} copiado para a área de transferência como PNG ({{exportColorScheme}})\",\n    \"fileSaved\": \"Arquivo salvo.\",\n    \"fileSavedToFilename\": \"Salvo em {filename}\",\n    \"canvas\": \"tela\",\n    \"selection\": \"seleção\",\n    \"pasteAsSingleElement\": \"Use {{shortcut}} para colar como um único elemento,\\nou cole em um editor de texto já existente\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Transparente\",\n    \"black\": \"Preto\",\n    \"white\": \"Branco\",\n    \"red\": \"Vermelho\",\n    \"pink\": \"Rosa\",\n    \"grape\": \"Uva\",\n    \"violet\": \"Violeta\",\n    \"gray\": \"Cinza\",\n    \"blue\": \"Azul\",\n    \"cyan\": \"Ciano\",\n    \"teal\": \"Verde-azulado\",\n    \"green\": \"Verde\",\n    \"yellow\": \"Amarelo\",\n    \"orange\": \"Laranja\",\n    \"bronze\": \"Bronze\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Todos os dados são salvos localmente no seu navegador.\",\n      \"center_heading_plus\": \"Você queria ir para o Excalidraw+ em vez disso?\",\n      \"menuHint\": \"Exportar, preferências, idiomas...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Exportar, preferências e mais...\",\n      \"center_heading\": \"Diagramas, Feito. Simples.\",\n      \"toolbarHint\": \"Escolha uma ferramenta e comece a desenhar!\",\n      \"helpHint\": \"Atalhos e ajuda\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Cores personalizadas mais usadas\",\n    \"colors\": \"Cores\",\n    \"shades\": \"Tons\",\n    \"hexCode\": \"Código hexadecimal\",\n    \"noShades\": \"Sem tons disponíveis para essa cor\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Exportar como imagem\",\n        \"button\": \"Exportar como imagem\",\n        \"description\": \"Exportar os dados da cena para um arquivo que você poderá importar mais tarde.\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Salvar no computador\",\n        \"button\": \"Salvar no computador\",\n        \"description\": \"Exportar os dados da cena para um arquivo que você poderá importar mais tarde.\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Exportar para Excalidraw+\",\n        \"description\": \"Salvar a cena na sua área de trabalho Excalidraw+.\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Carregar de arquivo\",\n        \"button\": \"Carregar de arquivo\",\n        \"description\": \"Carregar de um arquivo irá <bold> substituir o conteúdo existente</bold>.<br></br>Você pode salvar seu desenho primeiro usando uma das opções abaixo.\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Carregar de um link\",\n        \"button\": \"Substituir meu conteúdo\",\n        \"description\": \"Carregar um desenho externo irá <bold> substituir seu conteúdo existente</bold>.<br></br>Você pode salvar seu desenho antes utilizando uma das opções abaixo.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}