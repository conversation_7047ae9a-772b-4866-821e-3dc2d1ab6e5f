import "../chunk-XDFCUUT6.js";

// locales/sv-SE.json
var labels = {
  paste: "Klistra in",
  pasteAsPlaintext: "Klistra som oformaterad text",
  pasteCharts: "Klistra in diagram",
  selectAll: "Markera alla",
  multiSelect: "L\xE4gg till element till markering",
  moveCanvas: "Flytta canvas",
  cut: "Klipp ut",
  copy: "Kopiera",
  copyAsPng: "Kopiera till urklipp som PNG",
  copyAsSvg: "Kopiera till urklipp som SVG",
  copyText: "Ko<PERSON>ra till urklipp som text",
  copySource: "Kopiera k\xE4lla till urklipp",
  convertToCode: "Konvertera till kod",
  bringForward: "Flytta fram\xE5t",
  sendToBack: "Flytta underst",
  bringToFront: "Flytta fr\xE4mst",
  sendBackward: "Skicka bak\xE5t",
  delete: "Ta bort",
  copyStyles: "<PERSON><PERSON>ra stil",
  pasteStyles: "Klistra in stil",
  stroke: "Linje",
  background: "Bakgrund",
  fill: "Fyllnad",
  strokeWidth: "Linjebredd",
  strokeStyle: "Linjestil",
  strokeStyle_solid: "Solid",
  strokeStyle_dashed: "Streckad",
  strokeStyle_dotted: "Punktad",
  sloppiness: "Slarvighet",
  opacity: "Genomskinlighet",
  textAlign: "Textjustering",
  edges: "Kanter",
  sharp: "Skarp",
  round: "Rund",
  arrowheads: "Pilhuvuden",
  arrowhead_none: "Inga",
  arrowhead_arrow: "Pil",
  arrowhead_bar: "Stolpe",
  arrowhead_circle: "Cirkel",
  arrowhead_circle_outline: "Cirkel (kontur)",
  arrowhead_triangle: "Triangel",
  arrowhead_triangle_outline: "Triangel (kontur)",
  arrowhead_diamond: "Diamant",
  arrowhead_diamond_outline: "Diamant (kontur)",
  fontSize: "Teckenstorlek",
  fontFamily: "Teckensnitt",
  addWatermark: 'L\xE4gg till "Skapad med Excalidraw"',
  handDrawn: "Handritad",
  normal: "Normal",
  code: "Kod",
  small: "Liten",
  medium: "Medium",
  large: "Stor",
  veryLarge: "Mycket stor",
  solid: "Solid",
  hachure: "Skraffering",
  zigzag: "Sicksack",
  crossHatch: "Skraffera med kors",
  thin: "Tunn",
  bold: "Fet",
  left: "V\xE4nster",
  center: "Centrera",
  right: "H\xF6ger",
  extraBold: "Extra fet",
  architect: "Arkitekt",
  artist: "Artist",
  cartoonist: "Serietecknare",
  fileTitle: "Filnamn",
  colorPicker: "F\xE4rgv\xE4ljare",
  canvasColors: "Anv\xE4nds p\xE5 canvas",
  canvasBackground: "Canvas-bakgrund",
  drawingCanvas: "Ritar canvas",
  layers: "Lager",
  actions: "\xC5tg\xE4rder",
  language: "Spr\xE5k",
  liveCollaboration: "Samarbeta live...",
  duplicateSelection: "Duplicera",
  untitled: "Namnl\xF6s",
  name: "Namn",
  yourName: "Ditt namn",
  madeWithExcalidraw: "Skapad med Excalidraw",
  group: "Gruppera markering",
  ungroup: "Avgruppera markering",
  collaborators: "Medarbetare",
  showGrid: "Visa rutn\xE4t",
  addToLibrary: "L\xE4gg till i biblioteket",
  removeFromLibrary: "Ta bort fr\xE5n bibliotek",
  libraryLoadingMessage: "Laddar bibliotek\u2026",
  libraries: "Bl\xE4ddra i bibliotek",
  loadingScene: "Laddar skiss\u2026",
  align: "Justera",
  alignTop: "Justera \xF6verkant",
  alignBottom: "Justera underkant",
  alignLeft: "Justera v\xE4nster",
  alignRight: "Justera h\xF6ger",
  centerVertically: "Centrera vertikalt",
  centerHorizontally: "Centrera horisontellt",
  distributeHorizontally: "F\xF6rdela horisontellt",
  distributeVertically: "F\xF6rdela vertikalt",
  flipHorizontal: "V\xE4nd horisontellt",
  flipVertical: "V\xE4nd vertikalt",
  viewMode: "Visningsl\xE4ge",
  share: "Dela",
  showStroke: "Visa f\xE4rgv\xE4ljare f\xF6r linjef\xE4rg",
  showBackground: "Visa f\xE4rgv\xE4ljare f\xF6r bakgrundsf\xE4rg",
  toggleTheme: "V\xE4xla tema",
  personalLib: "Personligt bibliotek",
  excalidrawLib: "Excalidraw bibliotek",
  decreaseFontSize: "Minska fontstorleken",
  increaseFontSize: "\xD6ka fontstorleken",
  unbindText: "Koppla bort text",
  bindText: "Bind texten till beh\xE5llaren",
  createContainerFromText: "Radbryt text i en avgr\xE4nsad yta",
  link: {
    edit: "Redigera l\xE4nk",
    editEmbed: "Redigera l\xE4nk & b\xE4dda in",
    create: "Skapa l\xE4nk",
    createEmbed: "Skapa l\xE4nk & b\xE4dda in",
    label: "L\xE4nk",
    labelEmbed: "L\xE4nka & b\xE4dda in",
    empty: "Ingen l\xE4nk \xE4r angiven"
  },
  lineEditor: {
    edit: "Redigera linje",
    exit: "Avsluta linjeredigerare"
  },
  elementLock: {
    lock: "L\xE5s",
    unlock: "L\xE5s upp",
    lockAll: "L\xE5s alla",
    unlockAll: "L\xE5s upp alla"
  },
  statusPublished: "Publicerad",
  sidebarLock: "H\xE5ll sidof\xE4ltet \xF6ppet",
  selectAllElementsInFrame: "Markera alla element i rutan",
  removeAllElementsFromFrame: "Ta bort alla element fr\xE5n rutan",
  eyeDropper: "V\xE4lj f\xE4rg fr\xE5n canvas",
  textToDiagram: "Text till diagram",
  prompt: "Fr\xE5ga"
};
var library = {
  noItems: "Inga objekt tillagda \xE4nnu...",
  hint_emptyLibrary: "V\xE4lj ett objekt p\xE5 canvasen f\xF6r att l\xE4gga till det h\xE4r, eller installera ett bibliotek fr\xE5n det publika arkivet, nedan.",
  hint_emptyPrivateLibrary: "V\xE4lj ett objekt p\xE5 canvasen f\xF6r att l\xE4gga till det h\xE4r."
};
var buttons = {
  clearReset: "\xC5terst\xE4ll canvasen",
  exportJSON: "Exportera till fil",
  exportImage: "Exportera bild...",
  export: "Spara till...",
  copyToClipboard: "Kopiera till urklipp",
  save: "Spara till aktuell fil",
  saveAs: "Spara som",
  load: "\xD6ppna",
  getShareableLink: "H\xE4mta delbar l\xE4nk",
  close: "St\xE4ng",
  selectLanguage: "V\xE4lj spr\xE5k",
  scrollBackToContent: "Bl\xE4ddra tillbaka till inneh\xE5llet",
  zoomIn: "Zooma in",
  zoomOut: "Zooma ut",
  resetZoom: "\xC5terst\xE4ll zoom",
  menu: "Meny",
  done: "Klart",
  edit: "Redigera",
  undo: "\xC5ngra",
  redo: "G\xF6r om",
  resetLibrary: "\xC5terst\xE4ll bibliotek",
  createNewRoom: "Skapa ett nytt rum",
  fullScreen: "Helsk\xE4rm",
  darkMode: "M\xF6rkt l\xE4ge",
  lightMode: "Ljust l\xE4ge",
  zenMode: "Zen-l\xE4ge",
  objectsSnapMode: "F\xE4st mot objekt",
  exitZenMode: "G\xE5 ur zen-l\xE4ge",
  cancel: "Avbryt",
  clear: "Rensa",
  remove: "Ta bort",
  embed: "V\xE4xla inb\xE4ddning",
  publishLibrary: "Publicera",
  submit: "Skicka",
  confirm: "Bekr\xE4fta",
  embeddableInteractionButton: "Klicka f\xF6r att interagera"
};
var alerts = {
  clearReset: "Detta rensar hela canvasen. \xC4r du s\xE4ker?",
  couldNotCreateShareableLink: "Kunde inte skapa delbar l\xE4nk.",
  couldNotCreateShareableLinkTooBig: "Kunde inte skapa delbar l\xE4nk: skissen \xE4r f\xF6r stor",
  couldNotLoadInvalidFile: "Kunde inte ladda ogiltig fil",
  importBackendFailed: "Importering fr\xE5n backend misslyckades.",
  cannotExportEmptyCanvas: "Kan inte exportera tom canvas.",
  couldNotCopyToClipboard: "Kunde inte kopiera till urklipp.",
  decryptFailed: "Kunde inte avkryptera data.",
  uploadedSecurly: "Uppladdning har s\xE4krats med kryptering fr\xE5n \xE4nde till \xE4nde. vilket inneb\xE4r att Excalidraw server och tredje part inte kan l\xE4sa inneh\xE5llet.",
  loadSceneOverridePrompt: "Laddning av extern skiss kommer att ers\xE4tta ditt befintliga inneh\xE5ll. Vill du forts\xE4tta?",
  collabStopOverridePrompt: "Att stoppa sessionen kommer att skriva \xF6ver din f\xF6reg\xE5ende, lokalt lagrade skiss. \xC4r du s\xE4ker?\n\n(Om du vill beh\xE5lla din lokala skiss, st\xE4ng bara webbl\xE4sarfliken ist\xE4llet.)",
  errorAddingToLibrary: "Kunde inte l\xE4gga till objekt i biblioteket",
  errorRemovingFromLibrary: "Kunde inte ta bort objekt fr\xE5n biblioteket",
  confirmAddLibrary: "Detta kommer att l\xE4gga till {{numShapes}} form(er) till ditt bibliotek. \xC4r du s\xE4ker?",
  imageDoesNotContainScene: "Den h\xE4r bilden verkar inte inneh\xE5lla n\xE5gon skissdata. Har du aktiverat inb\xE4ddning av skiss under export?",
  cannotRestoreFromImage: "Skiss kunde inte \xE5terst\xE4llas fr\xE5n denna bildfil",
  invalidSceneUrl: "Det gick inte att importera skiss fr\xE5n den angivna webbadressen. Antingen har den fel format, eller s\xE5 inneh\xE5ller den ingen giltig Excalidraw JSON data.",
  resetLibrary: "Detta kommer att rensa ditt bibliotek. \xC4r du s\xE4ker?",
  removeItemsFromsLibrary: "Ta bort {{count}} objekt fr\xE5n biblioteket?",
  invalidEncryptionKey: "Krypteringsnyckeln m\xE5ste vara 22 tecken. Livesamarbetet \xE4r inaktiverat.",
  collabOfflineWarning: "Ingen internetanslutning tillg\xE4nglig.\nDina \xE4ndringar kommer inte att sparas!"
};
var errors = {
  unsupportedFileType: "Filtypen st\xF6ds inte.",
  imageInsertError: "Kunde inte infoga bild. F\xF6rs\xF6k igen senare...",
  fileTooBig: "Filen \xE4r f\xF6r stor. Maximal till\xE5ten storlek \xE4r {{maxSize}}.",
  svgImageInsertError: "Kunde inte infoga SVG-bild. SVG-koden ser ogiltig ut.",
  failedToFetchImage: "Kunde inte h\xE4mta bilden.",
  invalidSVGString: "Ogiltig SVG.",
  cannotResolveCollabServer: "Det gick inte att ansluta till samarbets-servern. Ladda om sidan och f\xF6rs\xF6k igen.",
  importLibraryError: "Kunde inte ladda bibliotek",
  collabSaveFailed: "Det gick inte att spara i backend-databasen. Om problemen kvarst\xE5r b\xF6r du spara filen lokalt f\xF6r att se till att du inte f\xF6rlorar ditt arbete.",
  collabSaveFailed_sizeExceeded: "Det gick inte att spara till backend-databasen, whiteboarden verkar vara f\xF6r stor. Du b\xF6r spara filen lokalt f\xF6r att du inte ska f\xF6rlora ditt arbete.",
  imageToolNotSupported: "Bilder \xE4r inaktiverade.",
  brave_measure_text_error: {
    line1: "Det ser ut som om du anv\xE4nder Brave-webbl\xE4saren med <bold>Aggressivt Blockera fingeravtryck</bold> inst\xE4llningen aktiverad.",
    line2: "Detta kan resultera i trasiga <bold>Textelement</bold> i dina ritningar.",
    line3: "Vi rekommenderar starkt att du inaktiverar den h\xE4r inst\xE4llningen. Du kan f\xF6lja <link>dessa steg</link> f\xF6r att inaktivera den.",
    line4: "Om inaktivering av denna inst\xE4llning inte \xE5tg\xE4rdar visningen av textelement, \xF6ppna ett <issueLink>\xE4rende</issueLink> p\xE5 v\xE5r GitHub, eller skriv till oss p\xE5 <discordLink>Discord</discordLink>"
  },
  libraryElementTypeError: {
    embeddable: "Inb\xE4ddbara element kan inte l\xE4ggas till i biblioteket.",
    iframe: "IFrame-element kan inte l\xE4ggas till i biblioteket.",
    image: "St\xF6d f\xF6r att l\xE4gga till bilder till biblioteket kommer snart!"
  },
  asyncPasteFailedOnRead: "Kunde inte klistra in (kunde inte l\xE4sa fr\xE5n urklipp).",
  asyncPasteFailedOnParse: "Kunde inte klistra in.",
  copyToSystemClipboardFailed: "Kunde inte kopiera till urklipp."
};
var toolBar = {
  selection: "Markering",
  image: "Infoga bild",
  rectangle: "Rektangel",
  diamond: "Diamant",
  ellipse: "Ellips",
  arrow: "Pil",
  line: "Linje",
  freedraw: "Rita",
  text: "Text",
  library: "Bibliotek",
  lock: "H\xE5ll valt verktyg aktivt efter ritande",
  penMode: "Pennl\xE4ge - f\xF6rhindra touch",
  link: "L\xE4gg till / Uppdatera l\xE4nk f\xF6r en vald form",
  eraser: "Radergummi",
  frame: "Rutverktyg",
  magicframe: "Tr\xE5dram till kod",
  embeddable: "B\xE4dda in (web)",
  laser: "Laserpekare",
  hand: "Hand (panoreringsverktyg)",
  extraTools: "Fler verktyg",
  mermaidToExcalidraw: "Mermaid till Excalidraw",
  magicSettings: "AI-inst\xE4llningar"
};
var headings = {
  canvasActions: "Canvas-\xE5tg\xE4rder",
  selectedShapeActions: "Valda form\xE5tg\xE4rder",
  shapes: "Former"
};
var hints = {
  canvasPanning: "F\xF6r att flytta whiteboarden, h\xE5ll mushjulet eller mellanslagstangenten medan du drar eller anv\xE4nd handverktyget",
  linearElement: "Klicka f\xF6r att starta flera punkter, dra f\xF6r en linje",
  freeDraw: "Klicka och dra, sl\xE4pp n\xE4r du \xE4r klar",
  text: "Tips: du kan ocks\xE5 l\xE4gga till text genom att dubbelklicka var som helst med markeringsverktyget",
  embeddable: "Klicka-dra f\xF6r att skapa en webbplats-inb\xE4ddning",
  text_selected: "Dubbelklicka eller tryck ENTER f\xF6r att redigera text",
  text_editing: "Tryck Escape eller CtrlOrCmd + ENTER f\xF6r att slutf\xF6ra redigeringen",
  linearElementMulti: "Klicka p\xE5 sista punkten eller tryck Escape eller Enter f\xF6r att avsluta",
  lockAngle: "Du kan begr\xE4nsa vinkeln genom att h\xE5lla SKIFT",
  resize: "Du kan beh\xE5lla proportioner genom att h\xE5lla SHIFT medan du \xE4ndrar storlek,\nh\xE5ller du ALT \xE4ndras storlek relativt mitten",
  resizeImage: "Du kan \xE4ndra storlek fritt genom att h\xE5lla SHIFT,\nh\xE5ll ALT f\xF6r att \xE4ndra storlek fr\xE5n mitten",
  rotate: "Du kan begr\xE4nsa vinklar genom att h\xE5lla SHIFT medan du roterar",
  lineEditor_info: "H\xE5ll Ctrl/Cmd och dubbelklicka eller tryck p\xE5 Ctrl/Cmd + Enter f\xF6r att redigera punkter",
  lineEditor_pointSelected: "Tryck p\xE5 Ta bort f\xF6r att ta bort punkt(er), Ctrl + D eller Cmd + D f\xF6r att duplicera, eller dra f\xF6r att flytta",
  lineEditor_nothingSelected: "V\xE4lj en punkt att redigera (h\xE5ll SHIFT f\xF6r att v\xE4lja flera),\neller h\xE5ll ned Alt och klicka f\xF6r att l\xE4gga till nya punkter",
  placeImage: "Klicka f\xF6r att placera bilden, eller klicka och dra f\xF6r att st\xE4lla in dess storlek manuellt",
  publishLibrary: "Publicera ditt eget bibliotek",
  bindTextToElement: "Tryck p\xE5 Enter f\xF6r att l\xE4gga till text",
  deepBoxSelect: "H\xE5ll Ctrl eller Cmd f\xF6r att djupv\xE4lja, och f\xF6r att f\xF6rhindra att dra",
  eraserRevert: "H\xE5ll Alt f\xF6r att \xE5terst\xE4lla de element som \xE4r markerade f\xF6r borttagning",
  firefox_clipboard_write: 'Denna funktion kan sannolikt aktiveras genom att st\xE4lla in "dom.events.asyncClipboard.clipboardItem" flaggan till "true". F\xF6r att \xE4ndra webbl\xE4sarens flaggor i Firefox, bes\xF6k "about:config" sidan.',
  disableSnapping: "H\xE5ll Ctrl eller Cmd f\xF6r att inaktivera f\xE4stning"
};
var canvasError = {
  cannotShowPreview: "Kan inte visa f\xF6rhandsgranskning",
  canvasTooBig: "Canvasen kan vara f\xF6r stor.",
  canvasTooBigTip: "Tips: prova att flytta de mest avl\xE4gsna elementen lite n\xE4rmare varandra."
};
var errorSplash = {
  headingMain: "Ett fel uppstod. F\xF6rs\xF6k <button>med att l\xE4sa in sidan p\xE5 nytt.</button>",
  clearCanvasMessage: "Om omladdning inte fungerar, f\xF6rs\xF6k <button>rensa canvasen.</button>",
  clearCanvasCaveat: " Detta kommer att leda till f\xF6rlust av arbete ",
  trackedToSentry: "Felet med identifieraren {{eventId}} sp\xE5rades p\xE5 v\xE5rt system.",
  openIssueMessage: "Vi var mycket f\xF6rsiktiga med att inte inkludera din skissinformation om felet. Om din skiss inte \xE4r privat, v\xE4nligen \xF6verv\xE4ga att f\xF6lja upp p\xE5 v\xE5r <button>buggsp\xE5rare.</button> V\xE4nligen inkludera information nedan genom att kopiera och klistra in i GitHub-problemet.",
  sceneContent: "Skissinneh\xE5ll:"
};
var roomDialog = {
  desc_intro: "Du kan bjuda in personer till din nuvarande skiss f\xF6r att samarbeta med dig.",
  desc_privacy: "Oroa dig inte, sessionen anv\xE4nder kryptering fr\xE5n \xE4nde till \xE4nde, s\xE5 vad du \xE4n ritar kommer att f\xF6rbli privat. Inte ens v\xE5r server kommer att kunna se vad du skissar.",
  button_startSession: "Starta sessionen",
  button_stopSession: "Stoppa session",
  desc_inProgressIntro: "Nu p\xE5g\xE5r en live-samarbetssession.",
  desc_shareLink: "Dela denna l\xE4nk med n\xE5gon du vill samarbeta med:",
  desc_exitSession: "Att avbryta sessionen kommer att koppla bort dig fr\xE5n rummet, men du kommer att kunna forts\xE4tta arbeta med skissen, lokalt. Observera att detta inte p\xE5verkar andra m\xE4nniskor, och de kommer fortfarande att kunna samarbeta p\xE5 deras version.",
  shareTitle: "Delta i en live-samarbetssession p\xE5 Excalidraw"
};
var errorDialog = {
  title: "Fel"
};
var exportDialog = {
  disk_title: "Spara till disk",
  disk_details: "Exportera skissdata till en fil som du kan importera fr\xE5n senare.",
  disk_button: "Spara till fil",
  link_title: "Delbar l\xE4nk",
  link_details: "Exportera som en skrivskyddad l\xE4nk.",
  link_button: "Exportera till l\xE4nk",
  excalidrawplus_description: "Spara skissen till din Excalidraw+ arbetsyta.",
  excalidrawplus_button: "Exportera",
  excalidrawplus_exportError: "Det gick inte att exportera till Excalidraw+ just nu..."
};
var helpDialog = {
  blog: "L\xE4s v\xE5r blogg",
  click: "klicka",
  deepSelect: "Djupval",
  deepBoxSelect: "Djupval inom boxen, och f\xF6rhindra att dra",
  curvedArrow: "B\xF6jd pil",
  curvedLine: "B\xF6jd linje",
  documentation: "Dokumentation",
  doubleClick: "dubbelklicka",
  drag: "dra",
  editor: "Redigerare",
  editLineArrowPoints: "Redigera linje-/pilpunkter",
  editText: "Redigera text / l\xE4gg till etikett",
  github: "Hittat ett problem? Rapportera",
  howto: "F\xF6lj v\xE5ra guider",
  or: "eller",
  preventBinding: "F\xF6rhindra pilbindning",
  tools: "Verktyg",
  shortcuts: "Tangentbordsgenv\xE4gar",
  textFinish: "Slutf\xF6r redigering (text)",
  textNewLine: "L\xE4gg till ny rad (text)",
  title: "Hj\xE4lp",
  view: "Visa",
  zoomToFit: "Zooma f\xF6r att rymma alla element",
  zoomToSelection: "Zooma till markering",
  toggleElementLock: "L\xE5s/L\xE5s upp valda",
  movePageUpDown: "Flytta sida upp/ner",
  movePageLeftRight: "Flytta sida v\xE4nster/h\xF6ger"
};
var clearCanvasDialog = {
  title: "Rensa canvas"
};
var publishDialog = {
  title: "Publicera bibliotek",
  itemName: "Objektnamn",
  authorName: "Upphovsmannens namn",
  githubUsername: "GitHub-anv\xE4ndarnamn",
  twitterUsername: "Twitter-anv\xE4ndarnamn",
  libraryName: "Biblioteksnamn",
  libraryDesc: "Biblioteksbeskrivning",
  website: "Webbplats",
  placeholder: {
    authorName: "Ditt namn eller anv\xE4ndarnamn",
    libraryName: "Namn p\xE5 ditt bibliotek",
    libraryDesc: "Beskrivning av ditt bibliotek f\xF6r att hj\xE4lpa m\xE4nniskor att f\xF6rst\xE5 dess anv\xE4ndning",
    githubHandle: "Github-anv\xE4ndarnamn (valfritt), s\xE5 att du kan redigera biblioteket n\xE4r du har skickat in det f\xF6r granskning",
    twitterHandle: "Twitter-anv\xE4ndarnamn (valfritt), s\xE5 vi vet vem att kreditera n\xE4r du marknadsf\xF6r p\xE5 Twitter",
    website: "L\xE4nk till din personliga webbplats eller n\xE5gon annan (valfritt)"
  },
  errors: {
    required: "Obligatoriskt",
    website: "Ange en giltig URL"
  },
  noteDescription: "Skicka ditt bibliotek f\xF6r att inkluderas i <link>det offentliga bibliotekets arkiv</link>f\xF6r andra m\xE4nniskor att anv\xE4nda i sina skisser.",
  noteGuidelines: "Biblioteket m\xE5ste godk\xE4nnas manuellt f\xF6rst. V\xE4nligen l\xE4s <link>riktlinjerna</link> innan du skickar in. Du beh\xF6ver ett GitHub-konto f\xF6r att kommunicera och g\xF6ra \xE4ndringar om s\xE5 \xF6nskas, men det kr\xE4vs inte.",
  noteLicense: "Genom att skicka in godk\xE4nner du att biblioteket kommer att publiceras under <link>MIT-licens, </link>vilket kort sagt betyder att vem som helst kan anv\xE4nda det utan restriktioner.",
  noteItems: "Varje objekt m\xE5ste ha sitt eget namn s\xE5 att det \xE4r filtrerbart. F\xF6ljande objekt kommer att inkluderas:",
  atleastOneLibItem: "V\xE4lj minst ett biblioteksobjekt f\xF6r att komma ig\xE5ng",
  republishWarning: "Obs: n\xE5gra av de markerade objekten \xE4r redan markerade som publicerade/skickade. Du b\xF6r endast skicka objekt igen n\xE4r du uppdaterar ett befintligt bibliotek eller inl\xE4mning."
};
var publishSuccessDialog = {
  title: "Bibliotek inskickat",
  content: "Tack {{authorName}}. Ditt bibliotek har skickats f\xF6r granskning. Du kan f\xF6lja status<link>h\xE4r</link>"
};
var confirmDialog = {
  resetLibrary: "\xC5terst\xE4ll bibliotek",
  removeItemsFromLib: "Ta bort markerade objekt fr\xE5n biblioteket"
};
var imageExportDialog = {
  header: "Exportera bild",
  label: {
    withBackground: "Bakgrund",
    onlySelected: "Endast markerade",
    darkMode: "M\xF6rkt l\xE4ge",
    embedScene: "B\xE4dda in skiss",
    scale: "Skala",
    padding: "Utfyllnad"
  },
  tooltip: {
    embedScene: "Skissdata kommer att sparas i den exporterade PNG/SVG-filen s\xE5 att skissen kan \xE5terst\xE4llas fr\xE5n den.\nKommer att \xF6ka exporterad filstorlek."
  },
  title: {
    exportToPng: "Exportera till PNG",
    exportToSvg: "Exportera till SVG",
    copyPngToClipboard: "Kopiera PNG till urklipp"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "Kopiera till urklipp"
  }
};
var encrypted = {
  tooltip: "Dina skisser \xE4r krypterade fr\xE5n \xE4nde till \xE4nde s\xE5 Excalidraws servrar kommer aldrig att se dem.",
  link: "Blogginl\xE4gg om kryptering fr\xE5n \xE4nde till \xE4nde i Excalidraw"
};
var stats = {
  angle: "Vinkel",
  element: "Element",
  elements: "Element",
  height: "H\xF6jd",
  scene: "Skiss",
  selected: "Valda",
  storage: "Lagring",
  title: "Statistik f\xF6r n\xF6rdar",
  total: "Totalt",
  version: "Version",
  versionCopy: "Klicka f\xF6r att kopiera",
  versionNotAvailable: "Versionen \xE4r inte tillg\xE4nglig",
  width: "Bredd"
};
var toast = {
  addedToLibrary: "Tillagd i biblioteket",
  copyStyles: "Kopierade stilar.",
  copyToClipboard: "Kopierad till urklipp.",
  copyToClipboardAsPng: "Kopierade {{exportSelection}} till urklipp som PNG\n({{exportColorScheme}})",
  fileSaved: "Fil sparad.",
  fileSavedToFilename: "Sparad till {filename}",
  canvas: "canvas",
  selection: "markering",
  pasteAsSingleElement: "Anv\xE4nd {{shortcut}} f\xF6r att klistra in som ett enda element,\neller klistra in i en befintlig textredigerare",
  unableToEmbed: "Att b\xE4dda in denna webbadress \xE4r f\xF6r n\xE4rvarande inte till\xE5tet. Skapa en problemrapport p\xE5 GitHub f\xF6r att beg\xE4ra att webbadressen vitlistas.",
  unrecognizedLinkFormat: "L\xE4nken du b\xE4ddade in matchar inte det f\xF6rv\xE4ntade formatet. F\xF6rs\xF6k klistra in 'embed'-str\xE4ngen som tillhandah\xE5lls av k\xE4llwebbplatsen"
};
var colors = {
  transparent: "Genomskinlig",
  black: "Svart",
  white: "Vit",
  red: "R\xF6d",
  pink: "Rosa",
  grape: "Lila",
  violet: "Violett",
  gray: "Gr\xE5",
  blue: "Bl\xE5",
  cyan: "Turkos",
  teal: "Bl\xE5gr\xF6n",
  green: "Gr\xF6n",
  yellow: "Gul",
  orange: "Orange",
  bronze: "Brons"
};
var welcomeScreen = {
  app: {
    center_heading: "All data sparas lokalt i din webbl\xE4sare.",
    center_heading_plus: "Ville du g\xE5 till Excalidraw+ ist\xE4llet?",
    menuHint: "Exportera, inst\xE4llningar, spr\xE5k, ..."
  },
  defaults: {
    menuHint: "Exportera, inst\xE4llningar och mer...",
    center_heading: "F\xF6renklade. Diagram.",
    toolbarHint: "V\xE4lj ett verktyg & b\xF6rja rita!",
    helpHint: "Genv\xE4gar & hj\xE4lp"
  }
};
var colorPicker = {
  mostUsedCustomColors: "Mest frekvent anv\xE4nda anpassade f\xE4rger",
  colors: "F\xE4rger",
  shades: "Nyanser",
  hexCode: "Hex-kod",
  noShades: "Inga nyanser tillg\xE4ngliga f\xF6r denna f\xE4rg"
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "Exportera som bild",
      button: "Exportera som bild",
      description: "Exportera scendata som en bild fr\xE5n vilken du kan importera senare."
    },
    saveToDisk: {
      title: "Spara till disk",
      button: "Spara till disk",
      description: "Exportera scendata till en fil fr\xE5n vilken du kan importera senare."
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "Exportera till Excalidraw+",
      description: "Spara skissen till din Excalidraw+ arbetsyta."
    }
  },
  modal: {
    loadFromFile: {
      title: "L\xE4s in fr\xE5n fil",
      button: "L\xE4s in fr\xE5n fil",
      description: "Laddar fr\xE5n en fil kommer <bold>ers\xE4tta ditt befintliga inneh\xE5ll</bold>.<br></br>Du kan s\xE4kerhetskopiera din ritning f\xF6rst med hj\xE4lp av ett av alternativen nedan."
    },
    shareableLink: {
      title: "L\xE4s in fr\xE5n l\xE4nk",
      button: "Ers\xE4tt mitt inneh\xE5ll",
      description: "Inl\xE4sning av en extern ritning kommer <bold>ers\xE4tta ditt befintliga inneh\xE5ll</bold>.<br></br>Du kan s\xE4kerhetskopiera din ritning f\xF6rst genom att anv\xE4nda ett av alternativen nedan."
    }
  }
};
var mermaid = {
  title: "Mermaid till Excalidraw",
  button: "Infoga",
  description: "F\xF6r n\xE4rvarande st\xF6ds endast <flowchartLink>Fl\xF6desdiagram</flowchartLink>,<sequenceLink> Sekvensdiagram </sequenceLink> och <classLink>Klassdiagram</classLink>. De andra typerna kommer att \xE5terges som bild i Excalidraw.",
  syntax: "Mermaid-syntax",
  preview: "F\xF6rhandsgranska"
};
var sv_SE_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  sv_SE_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=sv-SE-V32YHALQ.js.map
