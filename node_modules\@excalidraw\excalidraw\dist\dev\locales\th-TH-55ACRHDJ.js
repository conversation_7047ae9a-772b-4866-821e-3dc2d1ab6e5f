import "../chunk-XDFCUUT6.js";

// locales/th-TH.json
var labels = {
  paste: "\u0E27\u0E32\u0E07",
  pasteAsPlaintext: "\u0E27\u0E32\u0E07\u0E42\u0E14\u0E22\u0E44\u0E21\u0E48\u0E21\u0E35\u0E01\u0E32\u0E23\u0E08\u0E31\u0E14\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A",
  pasteCharts: "\u0E27\u0E32\u0E07\u0E41\u0E1C\u0E19\u0E20\u0E39\u0E21\u0E34",
  selectAll: "\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E17\u0E31\u0E49\u0E07\u0E2B\u0E21\u0E14",
  multiSelect: "",
  moveCanvas: "",
  cut: "\u0E15\u0E31\u0E14",
  copy: "\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E01",
  copyAsPng: "\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E07\u0E44\u0E1B\u0E22\u0E31\u0E07\u0E04\u0E25\u0E34\u0E1B\u0E1A\u0E2D\u0E23\u0E4C\u0E14\u0E40\u0E1B\u0E47\u0E19 PNG",
  copyAsSvg: "\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E07\u0E44\u0E1B\u0E22\u0E31\u0E07\u0E04\u0E25\u0E34\u0E1B\u0E1A\u0E2D\u0E23\u0E4C\u0E14\u0E40\u0E1B\u0E47\u0E19 SVG",
  copyText: "\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E07\u0E44\u0E1B\u0E22\u0E31\u0E07\u0E04\u0E25\u0E34\u0E1B\u0E1A\u0E2D\u0E23\u0E4C\u0E14\u0E40\u0E1B\u0E47\u0E19\u0E02\u0E49\u0E2D\u0E04\u0E27\u0E32\u0E21",
  copySource: "",
  convertToCode: "",
  bringForward: "\u0E19\u0E33\u0E02\u0E36\u0E49\u0E19\u0E02\u0E49\u0E32\u0E07\u0E1A\u0E19",
  sendToBack: "\u0E22\u0E49\u0E32\u0E22\u0E44\u0E1B\u0E02\u0E49\u0E32\u0E07\u0E25\u0E48\u0E32\u0E07",
  bringToFront: "\u0E19\u0E33\u0E02\u0E36\u0E49\u0E19\u0E02\u0E49\u0E32\u0E07\u0E2B\u0E19\u0E49\u0E32",
  sendBackward: "\u0E22\u0E49\u0E32\u0E22\u0E44\u0E1B\u0E02\u0E49\u0E32\u0E07\u0E2B\u0E25\u0E31\u0E07",
  delete: "\u0E25\u0E1A",
  copyStyles: "\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E01\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A",
  pasteStyles: "\u0E27\u0E32\u0E07\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A",
  stroke: "\u0E40\u0E2A\u0E49\u0E19\u0E02\u0E2D\u0E1A",
  background: "\u0E1E\u0E37\u0E49\u0E19\u0E2B\u0E25\u0E31\u0E07",
  fill: "\u0E40\u0E15\u0E34\u0E21\u0E2A\u0E35",
  strokeWidth: "\u0E19\u0E49\u0E33\u0E2B\u0E19\u0E31\u0E01\u0E40\u0E2A\u0E49\u0E19\u0E02\u0E2D\u0E1A",
  strokeStyle: "\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A\u0E40\u0E2A\u0E49\u0E19",
  strokeStyle_solid: "\u0E40\u0E2A\u0E49\u0E19\u0E17\u0E36\u0E1A",
  strokeStyle_dashed: "\u0E40\u0E2A\u0E49\u0E19\u0E1B\u0E23\u0E30",
  strokeStyle_dotted: "\u0E08\u0E38\u0E14",
  sloppiness: "\u0E04\u0E27\u0E32\u0E21\u0E40\u0E25\u0E2D\u0E30\u0E40\u0E17\u0E2D\u0E30",
  opacity: "\u0E04\u0E27\u0E32\u0E21\u0E17\u0E36\u0E1A\u0E41\u0E2A\u0E07",
  textAlign: "\u0E08\u0E31\u0E14\u0E02\u0E49\u0E2D\u0E04\u0E27\u0E32\u0E21",
  edges: "\u0E02\u0E2D\u0E1A",
  sharp: "",
  round: "",
  arrowheads: "\u0E2B\u0E31\u0E27\u0E25\u0E39\u0E01\u0E28\u0E23",
  arrowhead_none: "\u0E44\u0E21\u0E48\u0E21\u0E35",
  arrowhead_arrow: "\u0E25\u0E39\u0E01\u0E28\u0E23",
  arrowhead_bar: "\u0E41\u0E16\u0E1A",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "\u0E2A\u0E32\u0E21\u0E40\u0E2B\u0E25\u0E35\u0E48\u0E22\u0E21",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "\u0E02\u0E19\u0E32\u0E14\u0E15\u0E31\u0E27\u0E2D\u0E31\u0E01\u0E29\u0E23",
  fontFamily: "\u0E41\u0E1A\u0E1A\u0E15\u0E31\u0E27\u0E2D\u0E31\u0E01\u0E29\u0E23",
  addWatermark: '\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E25\u0E32\u0E22\u0E19\u0E49\u0E33 "\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E14\u0E49\u0E27\u0E22 Excalidraw"',
  handDrawn: "\u0E25\u0E32\u0E22\u0E21\u0E37\u0E2D",
  normal: "\u0E1B\u0E01\u0E15\u0E34",
  code: "\u0E42\u0E04\u0E49\u0E14",
  small: "\u0E40\u0E25\u0E47\u0E01",
  medium: "\u0E01\u0E25\u0E32\u0E07",
  large: "\u0E43\u0E2B\u0E0D\u0E48",
  veryLarge: "\u0E43\u0E2B\u0E0D\u0E48\u0E21\u0E32\u0E01",
  solid: "",
  hachure: "",
  zigzag: "",
  crossHatch: "",
  thin: "\u0E1A\u0E32\u0E07",
  bold: "\u0E2B\u0E19\u0E32",
  left: "\u0E0B\u0E49\u0E32\u0E22",
  center: "\u0E01\u0E25\u0E32\u0E07",
  right: "\u0E02\u0E27\u0E32",
  extraBold: "\u0E2B\u0E19\u0E32\u0E1E\u0E34\u0E40\u0E28\u0E29",
  architect: "",
  artist: "\u0E28\u0E34\u0E25\u0E1B\u0E34\u0E19",
  cartoonist: "",
  fileTitle: "\u0E0A\u0E37\u0E48\u0E2D\u0E44\u0E1F\u0E25\u0E4C",
  colorPicker: "\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E2A\u0E35\u0E17\u0E35\u0E48\u0E01\u0E33\u0E2B\u0E19\u0E14\u0E40\u0E2D\u0E07",
  canvasColors: "",
  canvasBackground: "",
  drawingCanvas: "",
  layers: "",
  actions: "\u0E01\u0E32\u0E23\u0E01\u0E23\u0E30\u0E17\u0E33",
  language: "\u0E20\u0E32\u0E29\u0E32",
  liveCollaboration: "",
  duplicateSelection: "\u0E17\u0E33\u0E2A\u0E33\u0E40\u0E19\u0E32",
  untitled: "\u0E44\u0E21\u0E48\u0E21\u0E35\u0E0A\u0E37\u0E48\u0E2D",
  name: "\u0E0A\u0E37\u0E48\u0E2D",
  yourName: "\u0E0A\u0E37\u0E48\u0E2D\u0E02\u0E2D\u0E07\u0E04\u0E38\u0E13",
  madeWithExcalidraw: "",
  group: "\u0E08\u0E31\u0E14\u0E01\u0E25\u0E38\u0E48\u0E21",
  ungroup: "\u0E22\u0E01\u0E40\u0E25\u0E34\u0E01\u0E01\u0E32\u0E23\u0E08\u0E31\u0E14\u0E01\u0E25\u0E38\u0E48\u0E21",
  collaborators: "",
  showGrid: "\u0E41\u0E2A\u0E14\u0E07\u0E40\u0E2A\u0E49\u0E19\u0E15\u0E32\u0E23\u0E32\u0E07",
  addToLibrary: "\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E44\u0E1B\u0E43\u0E19\u0E04\u0E25\u0E31\u0E07",
  removeFromLibrary: "\u0E19\u0E33\u0E2D\u0E2D\u0E01\u0E08\u0E32\u0E01\u0E04\u0E25\u0E31\u0E07",
  libraryLoadingMessage: "\u0E01\u0E33\u0E25\u0E31\u0E07\u0E42\u0E2B\u0E25\u0E14\u0E04\u0E25\u0E31\u0E07...",
  libraries: "",
  loadingScene: "\u0E01\u0E33\u0E25\u0E31\u0E07\u0E42\u0E2B\u0E25\u0E14\u0E09\u0E32\u0E01",
  align: "\u0E08\u0E31\u0E14\u0E15\u0E33\u0E41\u0E2B\u0E19\u0E48\u0E07",
  alignTop: "\u0E08\u0E31\u0E14\u0E0A\u0E34\u0E14\u0E14\u0E49\u0E32\u0E19\u0E1A\u0E19",
  alignBottom: "\u0E08\u0E31\u0E14\u0E0A\u0E34\u0E14\u0E14\u0E49\u0E32\u0E19\u0E25\u0E48\u0E32\u0E07",
  alignLeft: "\u0E08\u0E31\u0E14\u0E0A\u0E34\u0E14\u0E0B\u0E49\u0E32\u0E22",
  alignRight: "\u0E08\u0E31\u0E14\u0E0A\u0E34\u0E14\u0E02\u0E27\u0E32",
  centerVertically: "\u0E01\u0E36\u0E48\u0E07\u0E01\u0E25\u0E32\u0E07\u0E41\u0E19\u0E27\u0E15\u0E31\u0E49\u0E07",
  centerHorizontally: "\u0E01\u0E36\u0E48\u0E07\u0E01\u0E25\u0E32\u0E07\u0E41\u0E19\u0E27\u0E19\u0E2D\u0E19",
  distributeHorizontally: "\u0E01\u0E23\u0E30\u0E08\u0E32\u0E22\u0E41\u0E19\u0E27\u0E19\u0E2D\u0E19",
  distributeVertically: "\u0E01\u0E23\u0E30\u0E08\u0E32\u0E22\u0E41\u0E19\u0E27\u0E15\u0E31\u0E49\u0E07",
  flipHorizontal: "\u0E1E\u0E25\u0E34\u0E01\u0E41\u0E19\u0E27\u0E19\u0E2D\u0E19",
  flipVertical: "\u0E1E\u0E25\u0E34\u0E01\u0E41\u0E19\u0E27\u0E15\u0E31\u0E49\u0E07",
  viewMode: "\u0E42\u0E2B\u0E21\u0E14\u0E21\u0E38\u0E21\u0E21\u0E2D\u0E07",
  share: "\u0E41\u0E0A\u0E23\u0E4C",
  showStroke: "",
  showBackground: "",
  toggleTheme: "\u0E2A\u0E25\u0E31\u0E1A\u0E18\u0E35\u0E21",
  personalLib: "\u0E04\u0E25\u0E31\u0E07\u0E02\u0E2D\u0E07\u0E09\u0E31\u0E19",
  excalidrawLib: "\u0E04\u0E25\u0E31\u0E07\u0E02\u0E2D\u0E07 Excalidraw",
  decreaseFontSize: "\u0E25\u0E14\u0E02\u0E19\u0E32\u0E14\u0E15\u0E31\u0E27\u0E2D\u0E31\u0E01\u0E29\u0E23",
  increaseFontSize: "\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E02\u0E19\u0E32\u0E14\u0E15\u0E31\u0E27\u0E2D\u0E31\u0E01\u0E29\u0E23",
  unbindText: "\u0E22\u0E01\u0E40\u0E25\u0E34\u0E01\u0E01\u0E32\u0E23\u0E1C\u0E39\u0E01\u0E15\u0E34\u0E14",
  bindText: "",
  createContainerFromText: "",
  link: {
    edit: "\u0E41\u0E01\u0E49\u0E44\u0E02\u0E25\u0E34\u0E07\u0E01\u0E4C",
    editEmbed: "\u0E41\u0E01\u0E49\u0E44\u0E02\u0E25\u0E34\u0E07\u0E04\u0E4C\u0E41\u0E25\u0E30\u0E01\u0E32\u0E23\u0E1D\u0E31\u0E07",
    create: "\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E25\u0E34\u0E07\u0E04\u0E4C",
    createEmbed: "\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E25\u0E34\u0E07\u0E04\u0E4C\u0E41\u0E25\u0E30\u0E01\u0E32\u0E23\u0E1D\u0E31\u0E07",
    label: "\u0E25\u0E34\u0E07\u0E04\u0E4C",
    labelEmbed: "\u0E25\u0E34\u0E07\u0E04\u0E4C\u0E41\u0E25\u0E30\u0E01\u0E32\u0E23\u0E1D\u0E31\u0E07",
    empty: "\u0E44\u0E21\u0E48\u0E44\u0E14\u0E49\u0E43\u0E2A\u0E48\u0E25\u0E34\u0E07\u0E04\u0E4C"
  },
  lineEditor: {
    edit: "\u0E41\u0E01\u0E49\u0E44\u0E02\u0E40\u0E2A\u0E49\u0E19",
    exit: ""
  },
  elementLock: {
    lock: "\u0E25\u0E47\u0E2D\u0E01",
    unlock: "\u0E1B\u0E25\u0E14\u0E25\u0E47\u0E2D\u0E01",
    lockAll: "\u0E25\u0E47\u0E2D\u0E01\u0E17\u0E31\u0E49\u0E07\u0E2B\u0E21\u0E14",
    unlockAll: "\u0E1B\u0E25\u0E14\u0E25\u0E47\u0E2D\u0E01\u0E17\u0E31\u0E49\u0E07\u0E2B\u0E21\u0E14"
  },
  statusPublished: "\u0E40\u0E1C\u0E22\u0E41\u0E1E\u0E23\u0E48",
  sidebarLock: "",
  selectAllElementsInFrame: "",
  removeAllElementsFromFrame: "",
  eyeDropper: "",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "\u0E22\u0E31\u0E07\u0E44\u0E21\u0E48\u0E21\u0E35\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23\u0E17\u0E35\u0E48\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E40\u0E02\u0E49\u0E32\u0E44\u0E1B\u0E44\u0E14\u0E49",
  hint_emptyLibrary: "",
  hint_emptyPrivateLibrary: ""
};
var buttons = {
  clearReset: "\u0E23\u0E35\u0E40\u0E0B\u0E47\u0E17\u0E1C\u0E37\u0E19\u0E1C\u0E49\u0E32\u0E43\u0E1A",
  exportJSON: "\u0E2A\u0E48\u0E07\u0E2D\u0E2D\u0E01\u0E44\u0E1B\u0E22\u0E31\u0E07\u0E44\u0E1F\u0E25\u0E4C",
  exportImage: "\u0E2A\u0E48\u0E07\u0E2D\u0E2D\u0E01\u0E40\u0E1B\u0E47\u0E19\u0E23\u0E39\u0E1B\u0E20\u0E32\u0E1E",
  export: "\u0E1A\u0E31\u0E19\u0E17\u0E36\u0E01\u0E44\u0E1B\u0E22\u0E31\u0E07",
  copyToClipboard: "\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E01\u0E44\u0E1B\u0E22\u0E31\u0E07\u0E04\u0E25\u0E34\u0E1B\u0E1A\u0E2D\u0E23\u0E4C\u0E14",
  save: "\u0E1A\u0E31\u0E19\u0E17\u0E36\u0E01\u0E40\u0E1B\u0E47\u0E19\u0E44\u0E1F\u0E25\u0E4C\u0E1B\u0E31\u0E08\u0E08\u0E38\u0E1A\u0E31\u0E19",
  saveAs: "\u0E1A\u0E31\u0E19\u0E17\u0E36\u0E01\u0E40\u0E1B\u0E47\u0E19",
  load: "\u0E40\u0E1B\u0E34\u0E14",
  getShareableLink: "\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E25\u0E34\u0E07\u0E04\u0E4C\u0E17\u0E35\u0E48\u0E41\u0E0A\u0E23\u0E4C\u0E44\u0E14\u0E49",
  close: "\u0E1B\u0E34\u0E14",
  selectLanguage: "\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E20\u0E32\u0E29\u0E32",
  scrollBackToContent: "\u0E40\u0E25\u0E37\u0E48\u0E2D\u0E19\u0E01\u0E25\u0E31\u0E1A\u0E44\u0E1B\u0E14\u0E49\u0E32\u0E19\u0E1A\u0E19",
  zoomIn: "\u0E0B\u0E39\u0E21\u0E40\u0E02\u0E49\u0E32",
  zoomOut: "\u0E0B\u0E39\u0E21\u0E2D\u0E2D\u0E01",
  resetZoom: "\u0E23\u0E35\u0E40\u0E0B\u0E47\u0E15\u0E01\u0E32\u0E23\u0E0B\u0E39\u0E21",
  menu: "\u0E40\u0E21\u0E19\u0E39",
  done: "\u0E40\u0E2A\u0E23\u0E47\u0E08\u0E2A\u0E34\u0E49\u0E19",
  edit: "\u0E41\u0E01\u0E49\u0E44\u0E02",
  undo: "\u0E40\u0E25\u0E34\u0E01\u0E17\u0E33",
  redo: "\u0E17\u0E33\u0E0B\u0E49\u0E33",
  resetLibrary: "\u0E23\u0E35\u0E40\u0E0B\u0E47\u0E15\u0E04\u0E25\u0E31\u0E07",
  createNewRoom: "\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E2B\u0E49\u0E2D\u0E07\u0E43\u0E2B\u0E21\u0E48",
  fullScreen: "\u0E40\u0E15\u0E47\u0E21\u0E2B\u0E19\u0E49\u0E32\u0E08\u0E2D",
  darkMode: "\u0E42\u0E2B\u0E21\u0E14\u0E01\u0E25\u0E32\u0E07\u0E04\u0E37\u0E19",
  lightMode: "\u0E42\u0E2B\u0E21\u0E14\u0E01\u0E25\u0E32\u0E07\u0E27\u0E31\u0E19",
  zenMode: "\u0E42\u0E2B\u0E21\u0E14 Zen",
  objectsSnapMode: "",
  exitZenMode: "\u0E2D\u0E2D\u0E01\u0E08\u0E32\u0E01\u0E42\u0E2B\u0E21\u0E14 Zen",
  cancel: "\u0E22\u0E01\u0E40\u0E25\u0E34\u0E01",
  clear: "\u0E40\u0E04\u0E25\u0E35\u0E22\u0E23\u0E4C",
  remove: "\u0E25\u0E1A",
  embed: "\u0E2A\u0E25\u0E31\u0E1A\u0E01\u0E32\u0E23\u0E1D\u0E31\u0E07",
  publishLibrary: "\u0E40\u0E1C\u0E22\u0E41\u0E1E\u0E23\u0E48",
  submit: "\u0E15\u0E01\u0E25\u0E07",
  confirm: "\u0E22\u0E37\u0E19\u0E22\u0E31\u0E19",
  embeddableInteractionButton: "\u0E04\u0E25\u0E34\u0E01\u0E40\u0E1E\u0E37\u0E48\u0E2D\u0E1B\u0E0F\u0E34\u0E2A\u0E31\u0E21\u0E1E\u0E31\u0E19\u0E18\u0E4C"
};
var alerts = {
  clearReset: "",
  couldNotCreateShareableLink: "\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E25\u0E34\u0E07\u0E04\u0E4C\u0E44\u0E14\u0E49",
  couldNotCreateShareableLinkTooBig: "",
  couldNotLoadInvalidFile: "\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E42\u0E2B\u0E25\u0E14\u0E44\u0E1F\u0E25\u0E4C\u0E17\u0E35\u0E48\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14\u0E44\u0E14\u0E49",
  importBackendFailed: "\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14\u0E08\u0E32\u0E01\u0E01\u0E32\u0E23\u0E19\u0E33\u0E40\u0E02\u0E49\u0E32\u0E08\u0E32\u0E01\u0E23\u0E30\u0E1A\u0E1A\u0E2B\u0E25\u0E31\u0E07\u0E1A\u0E49\u0E32\u0E19",
  cannotExportEmptyCanvas: "\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E19\u0E33\u0E2D\u0E2D\u0E01\u0E08\u0E32\u0E01\u0E1C\u0E37\u0E19\u0E1C\u0E49\u0E32\u0E43\u0E1A\u0E17\u0E35\u0E48\u0E27\u0E48\u0E32\u0E07\u0E40\u0E1B\u0E25\u0E48\u0E32\u0E44\u0E14\u0E49",
  couldNotCopyToClipboard: "\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E01\u0E44\u0E1B\u0E22\u0E31\u0E07\u0E04\u0E25\u0E34\u0E1B\u0E1A\u0E2D\u0E23\u0E4C\u0E14\u0E44\u0E14\u0E49",
  decryptFailed: "\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E16\u0E2D\u0E14\u0E23\u0E2B\u0E31\u0E2A\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25\u0E44\u0E14\u0E49",
  uploadedSecurly: "\u0E01\u0E32\u0E23\u0E2D\u0E31\u0E1E\u0E42\u0E2B\u0E25\u0E14\u0E44\u0E14\u0E49\u0E16\u0E39\u0E01\u0E40\u0E02\u0E49\u0E32\u0E23\u0E2B\u0E31\u0E2A\u0E41\u0E1A\u0E1A end-to-end \u0E2B\u0E21\u0E32\u0E22\u0E04\u0E27\u0E32\u0E21\u0E27\u0E48\u0E32\u0E40\u0E0B\u0E34\u0E23\u0E4C\u0E1F\u0E40\u0E27\u0E2D\u0E23\u0E4C\u0E02\u0E2D\u0E07 Excalidraw \u0E41\u0E25\u0E30\u0E1A\u0E38\u0E04\u0E04\u0E25\u0E2D\u0E37\u0E48\u0E19\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E2D\u0E48\u0E32\u0E19\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25\u0E44\u0E14\u0E49",
  loadSceneOverridePrompt: "",
  collabStopOverridePrompt: "",
  errorAddingToLibrary: "\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23\u0E40\u0E02\u0E49\u0E32\u0E44\u0E1B\u0E43\u0E19\u0E04\u0E25\u0E31\u0E07\u0E44\u0E14\u0E49",
  errorRemovingFromLibrary: "\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E25\u0E1A\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23\u0E19\u0E35\u0E49\u0E2D\u0E2D\u0E01\u0E08\u0E32\u0E01\u0E04\u0E25\u0E31\u0E07\u0E44\u0E14\u0E49",
  confirmAddLibrary: "",
  imageDoesNotContainScene: "",
  cannotRestoreFromImage: "",
  invalidSceneUrl: "",
  resetLibrary: "",
  removeItemsFromsLibrary: "",
  invalidEncryptionKey: "",
  collabOfflineWarning: ""
};
var errors = {
  unsupportedFileType: "\u0E44\u0E21\u0E48\u0E23\u0E2D\u0E07\u0E23\u0E31\u0E1A\u0E0A\u0E19\u0E34\u0E14\u0E02\u0E2D\u0E07\u0E44\u0E1F\u0E25\u0E4C\u0E19\u0E35\u0E49",
  imageInsertError: "\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E23\u0E39\u0E1B\u0E20\u0E32\u0E1E\u0E44\u0E14\u0E49 \u0E25\u0E2D\u0E07\u0E2D\u0E35\u0E01\u0E04\u0E23\u0E31\u0E49\u0E07\u0E43\u0E19\u0E20\u0E32\u0E22\u0E2B\u0E25\u0E31\u0E07",
  fileTooBig: "",
  svgImageInsertError: "",
  failedToFetchImage: "",
  invalidSVGString: "\u0E44\u0E1F\u0E25\u0E4C SVG \u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14",
  cannotResolveCollabServer: "\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E40\u0E0A\u0E37\u0E48\u0E2D\u0E15\u0E48\u0E2D\u0E01\u0E31\u0E1A collab \u0E40\u0E0B\u0E34\u0E23\u0E4C\u0E1F\u0E40\u0E27\u0E2D\u0E23\u0E4C\u0E44\u0E14\u0E49 \u0E42\u0E1B\u0E23\u0E14\u0E25\u0E2D\u0E07\u0E42\u0E2B\u0E25\u0E14\u0E2B\u0E19\u0E49\u0E32\u0E19\u0E35\u0E49\u0E43\u0E2B\u0E21\u0E48\u0E41\u0E25\u0E30\u0E25\u0E2D\u0E07\u0E2D\u0E35\u0E01\u0E04\u0E23\u0E31\u0E49\u0E07",
  importLibraryError: "",
  collabSaveFailed: "",
  collabSaveFailed_sizeExceeded: "",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "",
    line2: "",
    line3: "",
    line4: ""
  },
  libraryElementTypeError: {
    embeddable: "\u0E01\u0E32\u0E23\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E2D\u0E07\u0E04\u0E4C\u0E1B\u0E23\u0E30\u0E01\u0E2D\u0E1A\u0E17\u0E35\u0E48\u0E1D\u0E31\u0E07\u0E22\u0E31\u0E07\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E40\u0E02\u0E49\u0E32\u0E44\u0E1B\u0E43\u0E19\u0E44\u0E25\u0E1A\u0E25\u0E32\u0E23\u0E35\u0E44\u0E14\u0E49",
    iframe: "",
    image: "\u0E01\u0E32\u0E23\u0E2A\u0E19\u0E31\u0E1A\u0E2A\u0E19\u0E38\u0E19\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E23\u0E39\u0E1B\u0E20\u0E32\u0E1E\u0E25\u0E07\u0E43\u0E19\u0E44\u0E25\u0E1A\u0E25\u0E32\u0E23\u0E35\u0E08\u0E30\u0E21\u0E32\u0E43\u0E19\u0E40\u0E23\u0E47\u0E27 \u0E46 \u0E19\u0E35\u0E49"
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "",
  image: "",
  rectangle: "\u0E2A\u0E35\u0E48\u0E40\u0E2B\u0E25\u0E35\u0E48\u0E22\u0E21\u0E1C\u0E37\u0E19\u0E1C\u0E49\u0E32",
  diamond: "",
  ellipse: "\u0E27\u0E07\u0E23\u0E35",
  arrow: "\u0E25\u0E39\u0E01\u0E28\u0E23",
  line: "",
  freedraw: "",
  text: "\u0E02\u0E49\u0E2D\u0E04\u0E27\u0E32\u0E21",
  library: "\u0E04\u0E25\u0E31\u0E07",
  lock: "",
  penMode: "",
  link: "",
  eraser: "\u0E22\u0E32\u0E07\u0E25\u0E1A",
  frame: "",
  magicframe: "",
  embeddable: "\u0E1D\u0E31\u0E07\u0E40\u0E27\u0E47\u0E1A",
  laser: "",
  hand: "",
  extraTools: "\u0E40\u0E04\u0E23\u0E37\u0E48\u0E2D\u0E07\u0E21\u0E37\u0E2D\u0E2D\u0E37\u0E48\u0E19\u0E46",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "",
  selectedShapeActions: "",
  shapes: "\u0E23\u0E39\u0E1B\u0E23\u0E48\u0E32\u0E07"
};
var hints = {
  canvasPanning: "",
  linearElement: "",
  freeDraw: "",
  text: "",
  embeddable: "\u0E04\u0E25\u0E34\u0E01\u0E41\u0E25\u0E30\u0E25\u0E32\u0E01\u0E40\u0E1E\u0E37\u0E48\u0E2D\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E01\u0E32\u0E23\u0E1D\u0E31\u0E07\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E40\u0E27\u0E47\u0E1A\u0E44\u0E0B\u0E15\u0E4C",
  text_selected: "\u0E04\u0E25\u0E34\u0E01\u0E2A\u0E2D\u0E07\u0E04\u0E23\u0E31\u0E49\u0E07\u0E2B\u0E23\u0E37\u0E2D\u0E01\u0E14 ENTER \u0E40\u0E1E\u0E37\u0E48\u0E2D\u0E41\u0E01\u0E49\u0E44\u0E02\u0E02\u0E49\u0E2D\u0E04\u0E27\u0E32\u0E21",
  text_editing: "\u0E01\u0E14\u0E1B\u0E38\u0E48\u0E21 Esc \u0E2B\u0E23\u0E37\u0E2D\u0E01\u0E14 Ctrl, Cmd + Enter \u0E40\u0E1E\u0E37\u0E48\u0E2D\u0E40\u0E2A\u0E23\u0E47\u0E08\u0E01\u0E32\u0E23\u0E41\u0E01\u0E49\u0E44\u0E02",
  linearElementMulti: "\u0E04\u0E25\u0E34\u0E01\u0E17\u0E35\u0E48\u0E08\u0E38\u0E14\u0E2A\u0E38\u0E14\u0E17\u0E49\u0E32\u0E22\u0E2B\u0E23\u0E37\u0E2D\u0E01\u0E14 Escape \u0E2B\u0E23\u0E37\u0E2D Enter \u0E40\u0E1E\u0E37\u0E48\u0E2D\u0E40\u0E2A\u0E23\u0E47\u0E08\u0E2A\u0E34\u0E49\u0E19",
  lockAngle: "",
  resize: "",
  resizeImage: "",
  rotate: "",
  lineEditor_info: "",
  lineEditor_pointSelected: "\u0E01\u0E14\u0E1B\u0E38\u0E48\u0E21 Delete \u0E40\u0E1E\u0E37\u0E48\u0E2D\u0E25\u0E1A\u0E08\u0E38\u0E14\n\u0E01\u0E14 Ctrl \u0E2B\u0E23\u0E37\u0E2D Cmd + D \u0E40\u0E1E\u0E37\u0E48\u0E2D\u0E17\u0E33\u0E0B\u0E49\u0E33\u0E2B\u0E23\u0E37\u0E2D\u0E25\u0E32\u0E01\u0E40\u0E1E\u0E37\u0E48\u0E2D\u0E40\u0E04\u0E25\u0E37\u0E48\u0E2D\u0E19\u0E22\u0E49\u0E32\u0E22",
  lineEditor_nothingSelected: "",
  placeImage: "",
  publishLibrary: "",
  bindTextToElement: "",
  deepBoxSelect: "",
  eraserRevert: "",
  firefox_clipboard_write: "",
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "",
  canvasTooBig: "",
  canvasTooBigTip: ""
};
var errorSplash = {
  headingMain: "<button>\u0E01\u0E33\u0E25\u0E31\u0E07\u0E23\u0E35\u0E42\u0E2B\u0E25\u0E14\u0E2B\u0E19\u0E49\u0E32</button>",
  clearCanvasMessage: "\u0E16\u0E49\u0E32\u0E42\u0E2B\u0E25\u0E14\u0E44\u0E21\u0E48\u0E44\u0E14\u0E49 \u0E43\u0E2B\u0E49\u0E25\u0E2D\u0E07 <button>\u0E40\u0E04\u0E25\u0E35\u0E22\u0E23\u0E4C\u0E1C\u0E37\u0E19\u0E1C\u0E49\u0E32\u0E43\u0E1A</button>",
  clearCanvasCaveat: "",
  trackedToSentry: "",
  openIssueMessage: "",
  sceneContent: ""
};
var roomDialog = {
  desc_intro: "",
  desc_privacy: "",
  button_startSession: "\u0E40\u0E23\u0E34\u0E48\u0E21\u0E40\u0E0B\u0E2A\u0E0A\u0E31\u0E19",
  button_stopSession: "\u0E2B\u0E22\u0E38\u0E14\u0E40\u0E0B\u0E2A\u0E0A\u0E31\u0E19",
  desc_inProgressIntro: "",
  desc_shareLink: "",
  desc_exitSession: "",
  shareTitle: ""
};
var errorDialog = {
  title: ""
};
var exportDialog = {
  disk_title: "",
  disk_details: "",
  disk_button: "",
  link_title: "",
  link_details: "",
  link_button: "",
  excalidrawplus_description: "",
  excalidrawplus_button: "",
  excalidrawplus_exportError: "\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E2A\u0E48\u0E07\u0E2D\u0E2D\u0E01\u0E44\u0E1B\u0E17\u0E35\u0E48 Excalidraw+ \u0E44\u0E14\u0E49\u0E43\u0E19\u0E02\u0E13\u0E30\u0E19\u0E35\u0E49"
};
var helpDialog = {
  blog: "\u0E2D\u0E48\u0E32\u0E19\u0E1A\u0E25\u0E47\u0E2D\u0E01\u0E02\u0E2D\u0E07\u0E1E\u0E27\u0E01\u0E40\u0E23\u0E32",
  click: "\u0E04\u0E25\u0E34\u0E01",
  deepSelect: "",
  deepBoxSelect: "",
  curvedArrow: "",
  curvedLine: "",
  documentation: "",
  doubleClick: "\u0E14\u0E31\u0E1A\u0E40\u0E1A\u0E34\u0E25\u0E04\u0E25\u0E34\u0E01",
  drag: "\u0E25\u0E32\u0E01",
  editor: "",
  editLineArrowPoints: "",
  editText: "\u0E41\u0E01\u0E49\u0E44\u0E02\u0E02\u0E49\u0E2D\u0E04\u0E27\u0E32\u0E21 / \u0E40\u0E1E\u0E34\u0E48\u0E21\u0E02\u0E49\u0E2D\u0E04\u0E27\u0E32\u0E21",
  github: "",
  howto: "",
  or: "",
  preventBinding: "",
  tools: "",
  shortcuts: "",
  textFinish: "",
  textNewLine: "",
  title: "\u0E0A\u0E48\u0E27\u0E22\u0E40\u0E2B\u0E25\u0E37\u0E2D",
  view: "\u0E14\u0E39",
  zoomToFit: "",
  zoomToSelection: "",
  toggleElementLock: "",
  movePageUpDown: "",
  movePageLeftRight: "\u0E22\u0E49\u0E32\u0E22\u0E2B\u0E19\u0E49\u0E32\u0E44\u0E1B\u0E14\u0E49\u0E32\u0E19 \u0E0B\u0E49\u0E32\u0E22/\u0E02\u0E27\u0E32"
};
var clearCanvasDialog = {
  title: ""
};
var publishDialog = {
  title: "",
  itemName: "",
  authorName: "\u0E0A\u0E37\u0E48\u0E2D\u0E40\u0E08\u0E49\u0E32\u0E02\u0E2D\u0E07",
  githubUsername: "\u0E0A\u0E37\u0E48\u0E2D\u0E1C\u0E39\u0E49\u0E43\u0E0A\u0E49 GitHub",
  twitterUsername: "\u0E0A\u0E37\u0E48\u0E2D\u0E1C\u0E39\u0E49\u0E43\u0E0A\u0E49 Twitter",
  libraryName: "",
  libraryDesc: "",
  website: "",
  placeholder: {
    authorName: "",
    libraryName: "",
    libraryDesc: "",
    githubHandle: "",
    twitterHandle: "",
    website: ""
  },
  errors: {
    required: "",
    website: ""
  },
  noteDescription: "",
  noteGuidelines: "",
  noteLicense: "",
  noteItems: "",
  atleastOneLibItem: "",
  republishWarning: ""
};
var publishSuccessDialog = {
  title: "",
  content: ""
};
var confirmDialog = {
  resetLibrary: "",
  removeItemsFromLib: ""
};
var imageExportDialog = {
  header: "",
  label: {
    withBackground: "",
    onlySelected: "",
    darkMode: "\u0E42\u0E2B\u0E21\u0E14\u0E01\u0E25\u0E32\u0E07\u0E04\u0E37\u0E19",
    embedScene: "",
    scale: "",
    padding: ""
  },
  tooltip: {
    embedScene: ""
  },
  title: {
    exportToPng: "\u0E2A\u0E48\u0E07\u0E2D\u0E2D\u0E01\u0E44\u0E1B\u0E40\u0E1B\u0E47\u0E19 SVG",
    exportToSvg: "\u0E2A\u0E48\u0E07\u0E2D\u0E2D\u0E01\u0E44\u0E1B\u0E40\u0E1B\u0E47\u0E19 SVG",
    copyPngToClipboard: "\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E01 PNG \u0E44\u0E1B\u0E22\u0E31\u0E07\u0E04\u0E25\u0E34\u0E1B\u0E1A\u0E2D\u0E23\u0E4C\u0E14"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E01\u0E44\u0E1B\u0E22\u0E31\u0E07\u0E04\u0E25\u0E34\u0E1B\u0E1A\u0E2D\u0E23\u0E4C\u0E14"
  }
};
var encrypted = {
  tooltip: "",
  link: ""
};
var stats = {
  angle: "",
  element: "",
  elements: "",
  height: "",
  scene: "",
  selected: "",
  storage: "",
  title: "",
  total: "",
  version: "",
  versionCopy: "",
  versionNotAvailable: "",
  width: ""
};
var toast = {
  addedToLibrary: "",
  copyStyles: "",
  copyToClipboard: "",
  copyToClipboardAsPng: "",
  fileSaved: "",
  fileSavedToFilename: "",
  canvas: "",
  selection: "",
  pasteAsSingleElement: "",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "",
  black: "",
  white: "",
  red: "",
  pink: "",
  grape: "",
  violet: "",
  gray: "",
  blue: "",
  cyan: "",
  teal: "\u0E1F\u0E49\u0E32\u0E19\u0E49\u0E33\u0E17\u0E30\u0E40\u0E25",
  green: "\u0E40\u0E02\u0E35\u0E22\u0E27",
  yellow: "\u0E40\u0E2B\u0E25\u0E37\u0E2D\u0E07",
  orange: "\u0E2A\u0E49\u0E21",
  bronze: "\u0E17\u0E2D\u0E07\u0E41\u0E14\u0E07"
};
var welcomeScreen = {
  app: {
    center_heading: "",
    center_heading_plus: "",
    menuHint: ""
  },
  defaults: {
    menuHint: "",
    center_heading: "",
    toolbarHint: "",
    helpHint: ""
  }
};
var colorPicker = {
  mostUsedCustomColors: "",
  colors: "",
  shades: "",
  hexCode: "",
  noShades: ""
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "",
      button: "",
      description: ""
    },
    saveToDisk: {
      title: "",
      button: "",
      description: ""
    },
    excalidrawPlus: {
      title: "",
      button: "",
      description: ""
    }
  },
  modal: {
    loadFromFile: {
      title: "",
      button: "",
      description: ""
    },
    shareableLink: {
      title: "",
      button: "",
      description: ""
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var th_TH_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  th_TH_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=th-TH-55ACRHDJ.js.map
