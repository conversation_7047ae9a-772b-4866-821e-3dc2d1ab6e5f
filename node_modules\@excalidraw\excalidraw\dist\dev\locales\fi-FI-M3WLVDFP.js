import "../chunk-XDFCUUT6.js";

// locales/fi-FI.json
var labels = {
  paste: "Liit\xE4",
  pasteAsPlaintext: "Liit\xE4 pelkk\xE4n\xE4 tekstin\xE4",
  pasteCharts: "Liit\xE4 kaaviot",
  selectAll: "Valitse kaikki",
  multiSelect: "Lis\xE4\xE4 kohde valintaan",
  moveCanvas: "Siirr\xE4 piirtoaluetta",
  cut: "<PERSON>ikka<PERSON>",
  copy: "Kopioi",
  copyAsPng: "Kopioi leikep\xF6yd\xE4lle PNG-tiedostona",
  copyAsSvg: "Kopio<PERSON> leikep\xF6yd\xE4lle SVG-tiedostona",
  copyText: "Kopioi tekstin\xE4",
  copySource: "",
  convertToCode: "",
  bringForward: "Tuo eteenp\xE4in",
  sendToBack: "Vie taakse",
  bringToFront: "Tuo eteen",
  sendBackward: "Vie taaksep\xE4in",
  delete: "Poista",
  copyStyles: "Kopioi tyyli",
  pasteStyles: "Liit\xE4 tyyli",
  stroke: "Piirto",
  background: "Tausta",
  fill: "T\xE4ytt\xF6",
  strokeWidth: "Viivan leveys",
  strokeStyle: "Viivan tyyli",
  strokeStyle_solid: "Yhten\xE4inen",
  strokeStyle_dashed: "Katkoviiva",
  strokeStyle_dotted: "Pisteviiva",
  sloppiness: "Viivan tarkkuus",
  opacity: "Peitt\xE4vyys",
  textAlign: "Tekstin tasaus",
  edges: "Reunat",
  sharp: "Ter\xE4v\xE4",
  round: "Py\xF6ristetty",
  arrowheads: "Nuolenk\xE4rjet",
  arrowhead_none: "Ei mit\xE4\xE4n",
  arrowhead_arrow: "Nuoli",
  arrowhead_bar: "Tasap\xE4\xE4",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "Kolmio",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "Kirjasinkoko",
  fontFamily: "Kirjasintyyppi",
  addWatermark: 'Lis\xE4\xE4 "Tehty Excalidrawilla"',
  handDrawn: "K\xE4sinpiirretty",
  normal: "Tavallinen",
  code: "Koodi",
  small: "Pieni",
  medium: "Keskikoko",
  large: "Suuri",
  veryLarge: "Eritt\xE4in suuri",
  solid: "Yhten\xE4inen",
  hachure: "Vinoviivoitus",
  zigzag: "",
  crossHatch: "Ristiviivoitus",
  thin: "Ohut",
  bold: "Lihavoitu",
  left: "Vasen",
  center: "Keskit\xE4",
  right: "Oikea",
  extraBold: "Eritt\xE4in lihavoitu",
  architect: "Arkkitehti",
  artist: "Taiteilija",
  cartoonist: "Sarjakuva",
  fileTitle: "Tiedostonimi",
  colorPicker: "V\xE4rin valinta",
  canvasColors: "K\xE4yt\xF6ss\xE4 piirtoalueella",
  canvasBackground: "Piirtoalueen tausta",
  drawingCanvas: "Piirtoalue",
  layers: "Tasot",
  actions: "Toiminnot",
  language: "Kieli",
  liveCollaboration: "Live Yhteisty\xF6...",
  duplicateSelection: "Monista",
  untitled: "Nimet\xF6n",
  name: "Nimi",
  yourName: "Nimesi",
  madeWithExcalidraw: "Tehty Excalidrawilla",
  group: "Ryhmit\xE4 valinta",
  ungroup: "Pura valittu ryhm\xE4",
  collaborators: "Yhteisty\xF6kumppanit",
  showGrid: "N\xE4yt\xE4 ruudukko",
  addToLibrary: "Lis\xE4\xE4 kirjastoon",
  removeFromLibrary: "Poista kirjastosta",
  libraryLoadingMessage: "Ladataan kirjastoa\u2026",
  libraries: "Selaa kirjastoja",
  loadingScene: "Ladataan ty\xF6t\xE4\u2026",
  align: "Tasaa",
  alignTop: "Tasaa yl\xF6s",
  alignBottom: "Tasaa alas",
  alignLeft: "Tasaa vasemmalle",
  alignRight: "Tasaa oikealle",
  centerVertically: "Keskit\xE4 pystysuunnassa",
  centerHorizontally: "Keskit\xE4 vaakasuunnassa",
  distributeHorizontally: "Jaa vaakasuunnassa",
  distributeVertically: "Jaa pystysuunnassa",
  flipHorizontal: "K\xE4\xE4nn\xE4 vaakasuunnassa",
  flipVertical: "K\xE4\xE4nn\xE4 pystysuunnassa",
  viewMode: "Katselutila",
  share: "Jaa",
  showStroke: "N\xE4yt\xE4 viivan v\xE4rin valitsin",
  showBackground: "N\xE4yt\xE4 taustav\xE4rin valitsin",
  toggleTheme: "Vaihda teema",
  personalLib: "Oma kirjasto",
  excalidrawLib: "Excalidraw kirjasto",
  decreaseFontSize: "Pienenn\xE4 kirjasinkokoa",
  increaseFontSize: "Kasvata kirjasinkokoa",
  unbindText: "Irroita teksti",
  bindText: "Kiinnit\xE4 teksti s\xE4ili\xF6\xF6n",
  createContainerFromText: "",
  link: {
    edit: "Muokkaa linkki\xE4",
    editEmbed: "",
    create: "Luo linkki",
    createEmbed: "",
    label: "Linkki",
    labelEmbed: "",
    empty: ""
  },
  lineEditor: {
    edit: "Muokkaa rivi\xE4",
    exit: "Poistu rivieditorista"
  },
  elementLock: {
    lock: "Lukitse",
    unlock: "Poista lukitus",
    lockAll: "Lukitse kaikki",
    unlockAll: "Poista lukitus kaikista"
  },
  statusPublished: "Julkaistu",
  sidebarLock: "Pid\xE4 sivupalkki avoinna",
  selectAllElementsInFrame: "",
  removeAllElementsFromFrame: "",
  eyeDropper: "",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "Kirjastossa ei ole viel\xE4 yht\xE4\xE4n kohdetta...",
  hint_emptyLibrary: "Valitse lis\xE4tt\xE4v\xE4 kohde piirtoalueelta, tai asenna alta julkinen kirjasto.",
  hint_emptyPrivateLibrary: "Valitse lis\xE4tt\xE4v\xE4 kohde piirtoalueelta."
};
var buttons = {
  clearReset: "Tyhjenn\xE4 piirtoalue",
  exportJSON: "Vie tiedostoon",
  exportImage: "Vie kuva...",
  export: "Tallenna nimell\xE4...",
  copyToClipboard: "Kopioi leikep\xF6yd\xE4lle",
  save: "Tallenna nykyiseen tiedostoon",
  saveAs: "Tallenna nimell\xE4",
  load: "Avaa",
  getShareableLink: "Hae jaettava linkki",
  close: "Sulje",
  selectLanguage: "Valitse kieli",
  scrollBackToContent: "N\xE4yt\xE4 sis\xE4lt\xF6",
  zoomIn: "L\xE4henn\xE4",
  zoomOut: "Loitonna",
  resetZoom: "Nollaa suurennuksen taso",
  menu: "Valikko",
  done: "Valmis",
  edit: "Muokkaa",
  undo: "Kumoa",
  redo: "Tee uudelleen",
  resetLibrary: "Tyhjenn\xE4 kirjasto",
  createNewRoom: "Luo huone",
  fullScreen: "Koko n\xE4ytt\xF6",
  darkMode: "Tumma tila",
  lightMode: "Vaalea tila",
  zenMode: "Zen-tila",
  objectsSnapMode: "",
  exitZenMode: "Poistu zen-tilasta",
  cancel: "Peruuta",
  clear: "Pyyhi",
  remove: "Poista",
  embed: "",
  publishLibrary: "Julkaise",
  submit: "L\xE4het\xE4",
  confirm: "Vahvista",
  embeddableInteractionButton: ""
};
var alerts = {
  clearReset: "T\xE4m\xE4 tyhjent\xE4\xE4 koko piirtoalueen. Jatketaanko?",
  couldNotCreateShareableLink: "Jaettavan linkin luominen ep\xE4onnistui.",
  couldNotCreateShareableLinkTooBig: "Jaettavaa linkki\xE4 ei voitu luoda: teos on liian suuri",
  couldNotLoadInvalidFile: "Virheellist\xE4 tiedostoa ei voitu avata",
  importBackendFailed: "Palvelimelta tuonti ep\xE4onnistui.",
  cannotExportEmptyCanvas: "Tyhj\xE4\xE4 piirtoaluetta ei voi vied\xE4.",
  couldNotCopyToClipboard: "Leikep\xF6yd\xE4lle vieminen ep\xE4onnistui.",
  decryptFailed: "Salauksen purkaminen ep\xE4onnistui.",
  uploadedSecurly: "L\xE4hetys on turvattu p\xE4\xE4st\xE4-p\xE4\xE4h\xE4n-salauksella. Excalidrawin palvelin ja kolmannet osapuolet eiv\xE4t voi lukea sis\xE4lt\xF6\xE4.",
  loadSceneOverridePrompt: "Ulkopuolisen piirroksen lataaminen korvaa nykyisen sis\xE4lt\xF6si. Jatketaanko?",
  collabStopOverridePrompt: "Istunnon lopettaminen korvaa aiemman, paikallisesti tallennetun piirustuksen. Jatketaanko?\n\n(Jos haluat s\xE4ilytt\xE4\xE4 paikallisesti tallennetun piirustuksen, sulje selaimen v\xE4lilehti lopettamisen sijaan.)",
  errorAddingToLibrary: "Kohdetta ei voitu lis\xE4t\xE4 kirjastoon",
  errorRemovingFromLibrary: "Kohdetta ei voitu poistaa kirjastosta",
  confirmAddLibrary: "T\xE4m\xE4 lis\xE4\xE4 {{numShapes}} muotoa kirjastoosi. Jatketaanko?",
  imageDoesNotContainScene: "T\xE4m\xE4 kuva ei n\xE4yt\xE4 sis\xE4lt\xE4v\xE4n piirrostietoja. Oletko ottanut k\xE4ytt\xF6\xF6n piirroksen tallennuksen viennin aikana?",
  cannotRestoreFromImage: "Teosta ei voitu palauttaa t\xE4st\xE4 kuvatiedostosta",
  invalidSceneUrl: "Teosta ei voitu tuoda annetusta URL-osoitteesta. Tallenne on vioittunut, tai osoitteessa ei ole Excalidraw JSON-dataa.",
  resetLibrary: "T\xE4m\xE4 tyhjent\xE4\xE4 kirjastosi. Jatketaanko?",
  removeItemsFromsLibrary: "Poista {{count}} kohdetta kirjastosta?",
  invalidEncryptionKey: "Salausavaimen on oltava 22 merkki\xE4 pitk\xE4. Live-yhteisty\xF6 ei ole k\xE4yt\xF6ss\xE4.",
  collabOfflineWarning: "Internet-yhteytt\xE4 ei ole saatavilla.\nMuutoksiasi ei tallenneta!"
};
var errors = {
  unsupportedFileType: "Tiedostotyyppi\xE4 ei tueta.",
  imageInsertError: "Kuvan lis\xE4\xE4minen ep\xE4onnistui. Yrit\xE4 my\xF6hemmin uudelleen...",
  fileTooBig: "Tiedosto on liian suuri. Suurin sallittu koko on {{maxSize}}.",
  svgImageInsertError: "SVG- kuvaa ei voitu lis\xE4t\xE4. Tiedoston SVG-sis\xE4lt\xF6 n\xE4ytt\xE4\xE4 virheelliselt\xE4.",
  failedToFetchImage: "",
  invalidSVGString: "Virheellinen SVG.",
  cannotResolveCollabServer: "Yhteyden muodostaminen collab-palvelimeen ep\xE4onnistui. Virkist\xE4 sivu ja yrit\xE4 uudelleen.",
  importLibraryError: "Kokoelman lataaminen ep\xE4onnistui",
  collabSaveFailed: "Ei voitu tallentaan palvelimen tietokantaan. Jos ongelmia esiintyy, sinun kannatta tallentaa tallentaa tiedosto paikallisesti varmistaaksesi, ett\xE4 et menet\xE4 ty\xF6t\xE4si.",
  collabSaveFailed_sizeExceeded: "Ei voitu tallentaan palvelimen tietokantaan. Jos ongelmia esiintyy, sinun kannatta tallentaa tallentaa tiedosto paikallisesti varmistaaksesi, ett\xE4 et menet\xE4 ty\xF6t\xE4si.",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "",
    line2: "",
    line3: "",
    line4: ""
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "",
    image: ""
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "Valinta",
  image: "Lis\xE4\xE4 kuva",
  rectangle: "Suorakulmio",
  diamond: "Vinoneli\xF6",
  ellipse: "Soikio",
  arrow: "Nuoli",
  line: "Viiva",
  freedraw: "Piirr\xE4",
  text: "Teksti",
  library: "Kirjasto",
  lock: "Pid\xE4 valittu ty\xF6kalu aktiivisena piirron j\xE4lkeen",
  penMode: "Kyn\xE4tila - est\xE4 kosketus",
  link: "Lis\xE4\xE4/p\xE4ivit\xE4 linkki valitulle muodolle",
  eraser: "Poistoty\xF6kalu",
  frame: "",
  magicframe: "",
  embeddable: "",
  laser: "",
  hand: "K\xE4si (panning-ty\xF6kalu)",
  extraTools: "",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "Piirtoalueen toiminnot",
  selectedShapeActions: "Valitun muodon toiminnot",
  shapes: "Muodot"
};
var hints = {
  canvasPanning: "Piirtoalueen liikuttamiseksi pid\xE4 hiiren py\xF6r\xE4\xE4 tai v\xE4lily\xF6nti\xE4 pohjassa tai k\xE4yt\xE4 k\xE4sity\xF6kalua",
  linearElement: "Klikkaa piirt\xE4\xE4ksesi useampi piste, raahaa piirt\xE4\xE4ksesi yksitt\xE4inen viiva",
  freeDraw: "Paina ja raahaa, p\xE4\xE4st\xE4 irti kun olet valmis",
  text: "Vinkki: voit my\xF6s lis\xE4t\xE4 teksti\xE4 kaksoisnapsauttamalla mihin tahansa valintaty\xF6kalulla",
  embeddable: "",
  text_selected: "Kaksoisnapsauta tai paina ENTER muokataksesi teksti\xE4",
  text_editing: "Paina Escape tai CtrlOrCmd+ENTER lopettaaksesi muokkaamisen",
  linearElementMulti: "Lopeta klikkaamalla viimeist\xE4 pistett\xE4, painamalla Escape- tai Enter-n\xE4pp\xE4int\xE4",
  lockAngle: "Voit rajoittaa kulmaa pit\xE4m\xE4ll\xE4 SHIFT-n\xE4pp\xE4int\xE4 alaspainettuna",
  resize: "Voit rajoittaa mittasuhteet pit\xE4m\xE4ll\xE4 SHIFT-n\xE4pp\xE4int\xE4 alaspainettuna kun muutat kokoa, pid\xE4 ALT-n\xE4pp\xE4int\xE4 alaspainettuna muuttaaksesi kokoa keskipisteen suhteen",
  resizeImage: "Voit muuttaa kokoa vapaasti pit\xE4m\xE4ll\xE4 SHIFTi\xE4 pohjassa, pid\xE4 ALT pohjassa muuttaaksesi kokoa keskipisteen ymp\xE4ri",
  rotate: "Voit rajoittaa kulman pit\xE4m\xE4ll\xE4 SHIFT pohjassa py\xF6ritt\xE4ess\xE4si",
  lineEditor_info: "Pid\xE4 CtrlOrCmd pohjassa ja kaksoisnapsauta tai paina CtrlOrCmd + Enter muokataksesi pisteit\xE4",
  lineEditor_pointSelected: "Poista piste(et) painamalla delete, monista painamalla CtrlOrCmd+D, tai liikuta raahaamalla",
  lineEditor_nothingSelected: "Valitse muokattava piste (monivalinta pit\xE4m\xE4ll\xE4 SHIFT pohjassa), tai paina Alt ja klikkaa lis\xE4t\xE4ksesi uusia pisteit\xE4",
  placeImage: "Klikkaa asettaaksesi kuvan, tai klikkaa ja raahaa asettaaksesi sen koon manuaalisesti",
  publishLibrary: "Julkaise oma kirjasto",
  bindTextToElement: "Lis\xE4\xE4 teksti\xE4 painamalla enter",
  deepBoxSelect: "K\xE4yt\xE4 syv\xE4valintaa ja est\xE4 raahaus painamalla CtrlOrCmd",
  eraserRevert: "Pid\xE4 Alt alaspainettuna, kumotaksesi merkittyjen elementtien poistamisen",
  firefox_clipboard_write: 'T\xE4m\xE4 ominaisuus voidaan todenn\xE4k\xF6isesti ottaa k\xE4ytt\xF6\xF6n asettamalla "dom.events.asyncClipboard.clipboardItem" kohta "true":ksi. Vaihtaaksesi selaimen kohdan Firefoxissa, k\xE4y "about:config" sivulla.',
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "Esikatselua ei voitu n\xE4ytt\xE4\xE4",
  canvasTooBig: "Piirtoalue saattaa olla liian suuri.",
  canvasTooBigTip: "Vinkki: yrit\xE4 siirt\xE4\xE4 kaukaisimpia elementtej\xE4 hieman l\xE4hemm\xE4s toisiaan."
};
var errorSplash = {
  headingMain: "Tapahtui virhe. Yrit\xE4 <button>sivun lataamista uudelleen.</button>",
  clearCanvasMessage: "Mik\xE4li sivun lataaminen uudelleen ei auta, yrit\xE4 <button>tyhjent\xE4\xE4 piirtoalue.</button>",
  clearCanvasCaveat: " T\xE4m\xE4 johtaa ty\xF6n menetykseen ",
  trackedToSentry: "Virhe tunnisteella {{eventId}} tallennettiin j\xE4rjestelm\xE4\xE4mme.",
  openIssueMessage: "Olimme varovaisia emmek\xE4 sis\xE4llytt\xE4neet tietoa piirroksestasi virheeseen. Mik\xE4li piirroksesi ei ole yksityinen, harkitsethan kertovasi meille <button>virheenseurantaj\xE4rjestelm\xE4ss\xE4mme.</button> Sis\xE4llyt\xE4 alla olevat tiedot kopioimalla ne GitHub-ongelmaan.",
  sceneContent: "Piirroksen tiedot:"
};
var roomDialog = {
  desc_intro: "Voit kutsua ihmisi\xE4 piirrokseesi tekem\xE4\xE4n yhteisty\xF6t\xE4 kanssasi.",
  desc_privacy: "\xC4l\xE4 huoli, istunto k\xE4ytt\xE4\xE4 p\xE4\xE4st\xE4-p\xE4\xE4h\xE4n-salausta, joten mit\xE4 tahansa piirr\xE4tkin, se pysyy salassa. Edes palvelimemme eiv\xE4t n\xE4e mit\xE4 keksit.",
  button_startSession: "Aloita istunto",
  button_stopSession: "Lopeta istunto",
  desc_inProgressIntro: "Jaettu istunto on nyt k\xE4ynniss\xE4.",
  desc_shareLink: "Jaa t\xE4m\xE4 linkki kenelle tahansa, jonka kanssa haluat tehd\xE4 yhteisty\xF6t\xE4:",
  desc_exitSession: "Istunnon pys\xE4ytt\xE4minen katkaisee yhteyden huoneeseen, mutta voit viel\xE4 jatkaa ty\xF6skentely\xE4 paikallisesti. Huomaa, ett\xE4 t\xE4m\xE4 ei vaikuta muihin k\xE4ytt\xE4jiin ja he voivat jatkaa oman versionsa parissa ty\xF6skentely\xE4.",
  shareTitle: "Liity Excalidraw live-yhteisty\xF6istuntoon"
};
var errorDialog = {
  title: "Virhe"
};
var exportDialog = {
  disk_title: "Tallenna levylle",
  disk_details: "Vie ty\xF6n tiedot tiedostoon, josta sen voi tuoda my\xF6hemmin.",
  disk_button: "Tallenna tiedostoon",
  link_title: "Jaettava linkki",
  link_details: "Vie vain luku -linkkin\xE4.",
  link_button: "Vie linkkin\xE4",
  excalidrawplus_description: "Tallenna teos Excalidraw+ tilaan.",
  excalidrawplus_button: "Vie",
  excalidrawplus_exportError: "Ei voitu vied\xE4 Excalidraw+-palveluun t\xE4ll\xE4 hetkell\xE4..."
};
var helpDialog = {
  blog: "Lue blogiamme",
  click: "klikkaa",
  deepSelect: "Syv\xE4valinta",
  deepBoxSelect: "K\xE4yt\xE4 syv\xE4valintaa ja est\xE4 raahaus",
  curvedArrow: "Kaareva nuoli",
  curvedLine: "Kaareva viiva",
  documentation: "K\xE4ytt\xF6ohjeet",
  doubleClick: "kaksoisnapsautus",
  drag: "ved\xE4",
  editor: "Muokkausohjelma",
  editLineArrowPoints: "",
  editText: "",
  github: "L\xF6ysitk\xF6 ongelman? Kerro meille",
  howto: "Seuraa oppaitamme",
  or: "tai",
  preventBinding: "Est\xE4 nuolten kiinnitys",
  tools: "Ty\xF6kalut",
  shortcuts: "Pikan\xE4pp\xE4imet",
  textFinish: "Lopeta muokkaus (tekstieditori)",
  textNewLine: "Lis\xE4\xE4 uusi rivi (tekstieditori)",
  title: "Ohjeet",
  view: "N\xE4kym\xE4",
  zoomToFit: "N\xE4yt\xE4 kaikki elementit",
  zoomToSelection: "N\xE4yt\xE4 valinta",
  toggleElementLock: "Lukitse / poista lukitus valinta",
  movePageUpDown: "Siirr\xE4 sivua yl\xF6s/alas",
  movePageLeftRight: "Siirr\xE4 sivua vasemmalle/oikealle"
};
var clearCanvasDialog = {
  title: "Pyyhi piirtoalue"
};
var publishDialog = {
  title: "Julkaise kirjasto",
  itemName: "Kohteen nimi",
  authorName: "Tekij\xE4n nimi",
  githubUsername: "GitHub-k\xE4ytt\xE4j\xE4tunnus",
  twitterUsername: "Twitter-k\xE4ytt\xE4j\xE4tunnus",
  libraryName: "Kirjaston nimi",
  libraryDesc: "Kirjaston kuvaus",
  website: "Verkkosivu",
  placeholder: {
    authorName: "Nimesi tai k\xE4ytt\xE4j\xE4nimesi",
    libraryName: "Kirjastosi nimi",
    libraryDesc: "Kirjaston kuvaus, joka auttaa ihmisi\xE4 ymm\xE4rt\xE4m\xE4\xE4n sen k\xE4ytt\xF6tarkoitukset",
    githubHandle: "GitHub-tunnuksesi (valinnainen), jotta voit muokata kirjastoa sen j\xE4lkeen kun se on l\xE4hetetty tarkastettavaksi",
    twitterHandle: "Twitter-tunnus (valinnainen), jotta tied\xE4mme ket\xE4 kiitt\xE4\xE4 kun viestimme Twitteriss\xE4",
    website: "Linkki henkil\xF6kohtaiselle verkkosivustollesi tai muualle (valinnainen)"
  },
  errors: {
    required: "Pakollinen",
    website: "Sy\xF6t\xE4 oikeamuotoinen URL-osoite"
  },
  noteDescription: "L\xE4het\xE4 kirjastosi, jotta se voidaan sis\xE4llytt\xE4\xE4 <link>julkisessa kirjastolistauksessa</link>muiden k\xE4ytt\xF6\xF6n omissa piirrustuksissaan.",
  noteGuidelines: "Kirjasto on ensin hyv\xE4ksytt\xE4v\xE4 manuaalisesti. Ole hyv\xE4 ja lue <link>ohjeet</link> ennen l\xE4hett\xE4mist\xE4. Tarvitset GitHub-tilin, jotta voit viesti\xE4 ja tehd\xE4 muutoksia pyydett\xE4ess\xE4, mutta se ei ole ehdottoman v\xE4ltt\xE4m\xE4t\xF6nt\xE4.",
  noteLicense: "L\xE4hett\xE4m\xE4ll\xE4 hyv\xE4ksyt ett\xE4 kirjasto julkaistaan <link>MIT-lisenssin </link>alla, mik\xE4 lyhyesti antaa muiden k\xE4ytt\xE4\xE4 sit\xE4 ilman rajoituksia.",
  noteItems: "Jokaisella kirjaston kohteella on oltava oma nimens\xE4 suodatusta varten. Seuraavat kirjaston kohteet sis\xE4ltyv\xE4t:",
  atleastOneLibItem: "Valitse v\xE4hint\xE4\xE4n yksi kirjaston kohde aloittaaksesi",
  republishWarning: "Huom! Osa valituista kohteista on merkitty jo julkaistu/l\xE4hetetyiksi. L\xE4het\xE4 kohteita uudelleen vain p\xE4ivitett\xE4ess\xE4 olemassa olevaa kirjastoa tai ehdotusta."
};
var publishSuccessDialog = {
  title: "Kirjasto l\xE4hetetty",
  content: "Kiitos {{authorName}}. Kirjastosi on l\xE4hetetty tarkistettavaksi. Voit seurata sen tilaa<link>t\xE4\xE4ll\xE4</link>"
};
var confirmDialog = {
  resetLibrary: "Tyhjenn\xE4 kirjasto",
  removeItemsFromLib: "Poista valitut kohteet kirjastosta"
};
var imageExportDialog = {
  header: "",
  label: {
    withBackground: "",
    onlySelected: "",
    darkMode: "",
    embedScene: "",
    scale: "",
    padding: ""
  },
  tooltip: {
    embedScene: ""
  },
  title: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  },
  button: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  }
};
var encrypted = {
  tooltip: "Piirroksesi ovat p\xE4\xE4st\xE4-p\xE4\xE4h\xE4n-salattuja, joten Excalidrawin palvelimet eiv\xE4t koskaan n\xE4e niit\xE4.",
  link: "Blogiartikkeli p\xE4\xE4st\xE4 p\xE4\xE4h\xE4n -salauksesta Excalidraw:ssa"
};
var stats = {
  angle: "Kulma",
  element: "Elementti",
  elements: "Elementit",
  height: "Korkeus",
  scene: "Teos",
  selected: "Valitut",
  storage: "Tallennustila",
  title: "Tilastoja n\xF6rteille",
  total: "Yhteens\xE4",
  version: "Versio",
  versionCopy: "Klikkaa kopioidaksesi",
  versionNotAvailable: "Versio ei saatavilla",
  width: "Leveys"
};
var toast = {
  addedToLibrary: "Lis\xE4tty kirjastoon",
  copyStyles: "Tyylit kopioitiin.",
  copyToClipboard: "Kopioitiin leikep\xF6yd\xE4lle.",
  copyToClipboardAsPng: "Kopioitiin {{exportSelection}} leikep\xF6yd\xE4lle PNG:n\xE4\n({{exportColorScheme}})",
  fileSaved: "Tiedosto tallennettu.",
  fileSavedToFilename: "Tallennettiin kohteeseen {filename}",
  canvas: "piirtoalue",
  selection: "valinta",
  pasteAsSingleElement: "K\xE4yt\xE4 {{shortcut}} liitt\xE4\xE4ksesi yhten\xE4 elementtin\xE4,\ntai liitt\xE4\xE4ksesi olemassa olevaan tekstieditoriin",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "L\xE4pin\xE4kyv\xE4",
  black: "",
  white: "",
  red: "",
  pink: "",
  grape: "",
  violet: "",
  gray: "",
  blue: "",
  cyan: "",
  teal: "",
  green: "",
  yellow: "",
  orange: "",
  bronze: ""
};
var welcomeScreen = {
  app: {
    center_heading: "Kaikki tietosi on tallennettu paikallisesti selaimellesi.",
    center_heading_plus: "Haluatko sen sijaan menn\xE4 Excalidraw+:aan?",
    menuHint: "Vie, asetukset, kielet, ..."
  },
  defaults: {
    menuHint: "Vie, asetukset ja lis\xE4\xE4...",
    center_heading: "Kaaviot. Tehty. Yksinkertaiseksi.",
    toolbarHint: "Valitse ty\xF6kalu ja aloita piirt\xE4minen!",
    helpHint: "Pikan\xE4pp\xE4imet & ohje"
  }
};
var colorPicker = {
  mostUsedCustomColors: "",
  colors: "",
  shades: "",
  hexCode: "",
  noShades: ""
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "",
      button: "",
      description: ""
    },
    saveToDisk: {
      title: "",
      button: "",
      description: ""
    },
    excalidrawPlus: {
      title: "",
      button: "",
      description: ""
    }
  },
  modal: {
    loadFromFile: {
      title: "",
      button: "",
      description: ""
    },
    shareableLink: {
      title: "",
      button: "",
      description: ""
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var fi_FI_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  fi_FI_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=fi-FI-M3WLVDFP.js.map
