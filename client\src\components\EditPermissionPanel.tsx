'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiU<PERSON><PERSON>, <PERSON>Eye, FiEdit3, FiToggleLeft, FiToggleRight, FiChevronDown, FiChevronUp, FiShield, FiLock, FiUnlock } from 'react-icons/fi';
import { useEditPermission } from '@/context/EditPermissionContext';

interface EditPermissionPanelProps {
  className?: string;
}

export default function EditPermissionPanel({ className = '' }: EditPermissionPanelProps) {
  const { isTeacher, users, setEditPermission } = useEditPermission();
  const [isExpanded, setIsExpanded] = useState(false);
  const [permissionChanges, setPermissionChanges] = useState<Record<string, boolean>>({});

  // Only show panel for teachers
  if (!isTeacher) {
    return null;
  }

  const students = users.filter(user => user.role === 'student');
  const teachers = users.filter(user => user.role === 'teacher');

  const handlePermissionToggle = (socketId: string, currentPermission: boolean) => {
    const newPermission = !currentPermission;
    
    // Update local state for immediate UI feedback
    setPermissionChanges(prev => ({
      ...prev,
      [socketId]: newPermission
    }));

    // Send permission change to server
    setEditPermission(socketId, newPermission);

    // Log the change
    const user = users.find(u => u.socketId === socketId);
    console.log(`🔧 Teacher toggled edit permission for ${user?.username}: ${currentPermission} → ${newPermission}`);

    // Clear the local change after a delay (server will update the real state)
    setTimeout(() => {
      setPermissionChanges(prev => {
        const updated = { ...prev };
        delete updated[socketId];
        return updated;
      });
    }, 1000);
  };

  const getEffectivePermission = (user: any) => {
    // Check if there's a pending change
    if (permissionChanges.hasOwnProperty(user.socketId)) {
      return permissionChanges[user.socketId];
    }
    // Otherwise use the current permission
    return user.canEdit;
  };

  const getUserIcon = (user: any) => {
    const canEdit = getEffectivePermission(user);
    if (user.role === 'teacher') {
      return <FiShield className="text-blue-500" />;
    }
    return canEdit ? <FiEdit3 className="text-green-500" /> : <FiEye className="text-gray-500" />;
  };

  const getUserBadge = (user: any) => {
    const canEdit = getEffectivePermission(user);
    if (user.role === 'teacher') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          <FiShield className="w-3 h-3 mr-1" />
          Teacher
        </span>
      );
    }
    
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
        canEdit 
          ? 'bg-green-100 text-green-800' 
          : 'bg-gray-100 text-gray-600'
      }`}>
        {canEdit ? (
          <>
            <FiEdit3 className="w-3 h-3 mr-1" />
            Editor
          </>
        ) : (
          <>
            <FiEye className="w-3 h-3 mr-1" />
            View-only
          </>
        )}
      </span>
    );
  };

  const totalUsers = users.length;
  const editableStudents = students.filter(user => getEffectivePermission(user)).length;

  return (
    <div className={`bg-white rounded-lg shadow-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div 
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
            <FiUsers className="w-4 h-4 text-blue-600" />
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900">Edit Permissions</h3>
            <p className="text-xs text-gray-500">
              {editableStudents}/{students.length} students can edit
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-500">{totalUsers} users</span>
          {isExpanded ? (
            <FiChevronUp className="w-4 h-4 text-gray-400" />
          ) : (
            <FiChevronDown className="w-4 h-4 text-gray-400" />
          )}
        </div>
      </div>

      {/* Expanded Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="border-t border-gray-200"
          >
            <div className="p-4 space-y-4">
              {/* Teachers Section */}
              {teachers.length > 0 && (
                <div>
                  <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide mb-2">
                    Teachers ({teachers.length})
                  </h4>
                  <div className="space-y-2">
                    {teachers.map((teacher) => (
                      <div
                        key={teacher.socketId}
                        className="flex items-center justify-between p-2 bg-blue-50 rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          {getUserIcon(teacher)}
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {teacher.username}
                            </p>
                            <p className="text-xs text-gray-500">Always has edit access</p>
                          </div>
                        </div>
                        {getUserBadge(teacher)}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Students Section */}
              {students.length > 0 && (
                <div>
                  <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide mb-2">
                    Students ({students.length})
                  </h4>
                  <div className="space-y-2">
                    {students.map((student) => {
                      const canEdit = getEffectivePermission(student);
                      const isPending = permissionChanges.hasOwnProperty(student.socketId);
                      
                      return (
                        <div
                          key={student.socketId}
                          className={`flex items-center justify-between p-3 rounded-lg border transition-all ${
                            isPending 
                              ? 'bg-yellow-50 border-yellow-200' 
                              : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            {getUserIcon(student)}
                            <div>
                              <p className="text-sm font-medium text-gray-900">
                                {student.username}
                                {isPending && (
                                  <span className="ml-2 text-xs text-yellow-600">
                                    (updating...)
                                  </span>
                                )}
                              </p>
                              <p className="text-xs text-gray-500">
                                {canEdit ? 'Can edit code' : 'View-only access'}
                              </p>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-3">
                            {getUserBadge(student)}
                            
                            {/* Permission Toggle */}
                            <button
                              onClick={() => handlePermissionToggle(student.socketId, canEdit)}
                              disabled={isPending}
                              className={`flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium transition-all ${
                                canEdit
                                  ? 'bg-green-100 text-green-700 hover:bg-green-200'
                                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                              } ${isPending ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                              title={canEdit ? 'Click to revoke edit access' : 'Click to grant edit access'}
                            >
                              {canEdit ? (
                                <>
                                  <FiUnlock className="w-3 h-3" />
                                  <span>Revoke</span>
                                </>
                              ) : (
                                <>
                                  <FiLock className="w-3 h-3" />
                                  <span>Grant</span>
                                </>
                              )}
                            </button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* No Students Message */}
              {students.length === 0 && (
                <div className="text-center py-6">
                  <FiUsers className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No students in the room yet</p>
                  <p className="text-xs text-gray-400">Students will appear here when they join</p>
                </div>
              )}

              {/* Quick Actions */}
              {students.length > 0 && (
                <div className="pt-3 border-t border-gray-200">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => {
                        students.forEach(student => {
                          if (!getEffectivePermission(student)) {
                            handlePermissionToggle(student.socketId, false);
                          }
                        });
                      }}
                      className="flex-1 px-3 py-2 text-xs font-medium text-green-700 bg-green-100 rounded-lg hover:bg-green-200 transition-colors"
                    >
                      <FiUnlock className="w-3 h-3 inline mr-1" />
                      Grant All
                    </button>
                    <button
                      onClick={() => {
                        students.forEach(student => {
                          if (getEffectivePermission(student)) {
                            handlePermissionToggle(student.socketId, true);
                          }
                        });
                      }}
                      className="flex-1 px-3 py-2 text-xs font-medium text-red-700 bg-red-100 rounded-lg hover:bg-red-200 transition-colors"
                    >
                      <FiLock className="w-3 h-3 inline mr-1" />
                      Revoke All
                    </button>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
