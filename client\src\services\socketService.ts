// client/src/services/socketService.ts
import { io, Socket } from 'socket.io-client';
import axios from 'axios';

// Update event types to match backend payloads
// Add userId and username everywhere needed

type ServerToClientEvents = {
  'code-update': (code: string) => void;
  'user-typing': (data: { username: string; userId?: string }) => void;
  'user-joined': (users: Array<{ socketId: string; username: string; userId?: string; role: string }>) => void;
  'user-left': (users: Array<{ socketId: string; username: string; userId?: string }>) => void;
  'highlight-line': (data: { roomId: string; startLine: number; endLine: number; comment?: string }) => void;
  'cursor-move': (data: { userId: string; position: { x: number; y: number } }) => void; // New event for cursor movement
  'room-users-updated': (data: { users: Array<any>, count?: number }) => void; // Add 'room-users-updated' to ServerToClientEvents
};

type ClientToServerEvents = {
  'create-room': (
    data: { username: string; roomId?: string; userId?: string },
    callback: (res: { roomId: string; username?: string; error?: string }) => void
  ) => void;
  'join-room': (
    data: { roomId: string; username: string; userId?: string },
    callback: (res: {
      error?: string;
      success?: boolean;
      users?: Array<{ socketId: string; username: string; userId?: string; role: string }>;
      username?: string;
      role?: string; // Include role in the response
    }) => void,
  ) => void,
  'code-change': (data: { roomId: string; code: string }) => void;
  'typing': (data: { roomId: string; username: string; userId?: string }) => void;
  'leave-room': (roomId: string) => void;
  'highlight-line': (data: { roomId: string; startLine: number; endLine: number; comment?: string }) => void;
  'cursor-move': (data: { roomId: string; userId: string; position: { x: number; y: number } }) => void; // New event for cursor movement
  'validate-room': (
    data: { roomId: string },
    callback: (response: { exists: boolean }) => void
  ) => void;
}

// Fix: Ensure SOCKET_URL is defined before use (move it above all uses)
const SOCKET_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:5000';

// Create a singleton socket instance
class SocketService {
  private static instance: SocketService;
  private socket: Socket<ServerToClientEvents, ClientToServerEvents> | null = null;
  private listeners: Map<string, Function[]> = new Map();
  private connected = false;

  private constructor() {
    this.initSocket();
  }

  public static getInstance(): SocketService {
    if (!SocketService.instance) {
      SocketService.instance = new SocketService();
    }
    return SocketService.instance;
  }

  private initSocket() {
    try {
      console.log('Initializing socket connection to', SOCKET_URL);

      // Use polling first for better compatibility, then upgrade to websocket
      this.socket = io(SOCKET_URL, {
        reconnectionAttempts: 10,
        reconnectionDelay: 1000,
        timeout: 10000,
        transports: ['polling', 'websocket'], // Try polling first, then websocket
        autoConnect: true,
        forceNew: true,
        upgrade: true, // Allow upgrading to websocket after initial connection
      });

      console.log('Socket instance created successfully');
      this.setupEventListeners();
    } catch (error) {
      console.error('Error initializing socket:', error);
    }
  }

  private setupEventListeners() {
    if (!this.socket) return;

    // Clear any existing listeners to prevent duplicates
    this.socket.removeAllListeners();

    // Connection events
    this.socket.on('connect', () => {
      console.log('Socket connected successfully:', this.socket?.id);
      this.connected = true;
      this.emitEvent('connect', null);
    });

    this.socket.on('disconnect', (reason) => {
      console.log(`Socket disconnected. Reason: ${reason}`);
      this.connected = false;
      this.emitEvent('disconnect', reason);

      if (reason !== 'io client disconnect') {
        console.log('Reconnection attempt will start in 2 seconds...');
        setTimeout(() => {
          console.log('Attempting to reconnect...');
          this.connect();
        }, 2000);
      }
    });

    this.socket.on('error', (error) => {
      console.error('Socket error:', error?.message || error || 'Unknown error');
      this.connected = false;

      // Handle websocket errors specifically
      if (error === 'websocket error' || (typeof error === 'string' && error.includes('websocket'))) {
        console.log('WebSocket error detected via error event - switching to polling');
        setTimeout(() => {
          try {
            this.socket?.disconnect();
            this.socket = io(SOCKET_URL, {
              reconnectionAttempts: 5,
              reconnectionDelay: 1000,
              timeout: 15000,
              transports: ['polling'], // Use only polling
              autoConnect: true,
              forceNew: true,
              upgrade: false,
            });
            this.setupEventListeners();
          } catch (e) {
            console.error('Error switching to polling after error event:', e);
          }
        }, 500);
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error?.message || 'Unknown error');
      this.connected = false;

      // Handle specific error types
      if (error?.message?.includes('xhr poll error')) {
        console.log('XHR polling error detected - will try alternative connection method');

        // Set a flag to track this specific error
        if (typeof window !== 'undefined') {
          window.localStorage.setItem('socket_xhr_error', 'true');
        }

        // Emit the error event
        this.emitEvent('error', 'XHR polling error - trying alternative connection');

        // Try the alternative transport after a short delay
        setTimeout(() => {
          this.tryAlternativeTransport();
        }, 1000);
      }
      else if (error?.message?.includes('websocket') || error?.message?.includes('WebSocket') || error?.type === 'TransportError') {
        console.log('WebSocket error detected - will try polling transport');

        // Set a flag to track this specific error
        if (typeof window !== 'undefined') {
          window.localStorage.setItem('socket_websocket_error', 'true');
        }

        // Try polling immediately
        setTimeout(() => {
          try {
            console.log('Switching to polling transport...');
            this.socket?.disconnect();
            this.socket = io(SOCKET_URL, {
              reconnectionAttempts: 5,
              reconnectionDelay: 1000,
              timeout: 10000,
              transports: ['polling'], // Use only polling for this fallback
              autoConnect: true,
              forceNew: true,
              upgrade: false, // Don't try to upgrade to websocket if it's failing
            });
            this.setupEventListeners();
          } catch (e) {
            console.error('Error switching to polling transport:', e);
          }
        }, 1000);

        this.emitEvent('error', 'WebSocket error - trying polling transport');
      }
      else {
        // For other errors, just emit the error event
        this.emitEvent('error', error?.message || 'Unknown error');

        // Try to reconnect after a delay
        setTimeout(() => {
          this.connect();
        }, 3000);
      }
    });

    // Transport and reconnection events
    if (this.socket.io) {
      this.socket.io.on('error', (error) => {
        console.error('Transport error:', error);
      });

      this.socket.io.on('reconnect_attempt', (attempt) => {
        console.log(`Reconnection attempt ${attempt}`);
      });

      this.socket.io.on('reconnect', (attemptNumber) => {
        this.connected = true;
        this.emitEvent('connect', null);
      });

      this.socket.io.on('reconnect_error', (error) => {
        console.error('Reconnection error:', error);
      });

      this.socket.io.on('reconnect_failed', () => {
        this.emitEvent('error', 'Failed to reconnect after multiple attempts');

        // Try a different approach after all reconnection attempts fail
        setTimeout(() => {
          this.initSocket();
        }, 3000);
      });
    }

    // Application-specific events
    this.socket.on('code-update', (code) => {
      this.emitEvent('code-update', code);
    });

    this.socket.on('user-typing', (data) => {
      this.emitEvent('user-typing', data);
    });

    this.socket.on('user-joined', (users) => {
      this.emitEvent('user-joined', users);
    });

    this.socket.on('user-left', (users) => {
      this.emitEvent('user-left', users);
    });

    this.socket.on('highlight-line', (data) => {
      this.emitEvent('highlight-line', data);
    });

    this.socket.on('cursor-move', ({ userId, position }) => {
      console.log(`Cursor move received from user ${userId}:`, position);
      this.listeners.get('cursor-move')?.forEach((callback) => callback({ userId, position }));
    });

    this.socket.on('room-users-updated', (data: { users: Array<any>, count?: number }) => {
      this.emitEvent('room-users-updated', data);
    });
  }

  // Add event listener
  public on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)?.push(callback);
  }

  // Remove event listener
  public off(event: string, callback: Function) {
    if (!this.listeners.has(event)) return;

    const callbacks = this.listeners.get(event) || [];
    const index = callbacks.indexOf(callback);
    if (index !== -1) {
      callbacks.splice(index, 1);
    }
  }

  // Emit event to listeners
  private emitEvent(event: string, data: any) {
    if (!this.listeners.has(event)) return;

    const callbacks = this.listeners.get(event) || [];
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in ${event} listener:`, error);
      }
    });
  }

  // Check if socket is connected
  public isConnected(): boolean {
    return this.connected && !!this.socket?.connected;
  }

  // Connect to socket server with fallback mechanisms
  public connect() {
    if (!this.socket) {
      this.initSocket();
    } else if (!this.socket.connected) {
      console.log('Socket exists but not connected, attempting to reconnect...');

      // Try to reconnect with existing socket
      try {
        this.socket.connect();
      } catch (error) {
        console.error('Error reconnecting with existing socket:', error);

        // If reconnection fails, create a new socket
        this.initSocket();
      }
    }

    // Set a timeout to check if connection was successful
    setTimeout(() => {
      if (!this.isConnected()) {
        this.tryAlternativeTransport();
      }
    }, 5000);
  }

  // Try alternative transport method if WebSocket fails
  private tryAlternativeTransport() {
    try {
      // Disconnect existing socket if any
      if (this.socket) {
        this.socket.disconnect();
      }

      // Create new socket with both transports but prioritize polling
      this.socket = io(SOCKET_URL, {
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 10000,
        transports: ['polling', 'websocket'], // Try polling first, then websocket
        autoConnect: true,
        forceNew: true,
        upgrade: true, // Allow upgrading to websocket after initial connection
      });

      this.setupEventListeners();
    } catch (error) {
      console.error('Error setting up alternative transport:', error);
    }
  }

  // Disconnect from socket server
  public disconnect() {
    if (this.socket) {
      this.socket.disconnect();
    }
  }

  // Create a new room
  public createRoom(username: string, roomId?: string): Promise<{ roomId: string, username: string }> {
    return new Promise((resolve, reject) => {
      if (!this.socket || !this.isConnected()) {
        return reject(new Error('Socket not connected'));
      }

      // Generate a unique userId if not already stored
      let userId = typeof window !== 'undefined' ? window.localStorage.getItem('userId') : null;
      if (!userId) {
        userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        if (typeof window !== 'undefined') {
          window.localStorage.setItem('userId', userId);
        }
      }

      this.socket.emit('create-room', { username, roomId, userId }, (response) => {
        if (response.error) {
          reject(new Error(response.error));
        } else {
          // If the server validated and possibly changed the username, update it locally
          if (response.username && response.username !== username) {
            if (typeof window !== 'undefined') {
              window.localStorage.setItem('username', response.username);
            }
          }

          resolve({
            roomId: response.roomId,
            username: response.username || username
          });
        }
      });
    });
  }

  // Join an existing room with fallback to HTTP if socket fails
  public joinRoom(roomId: string, username: string): Promise<{ users: Array<{ userId: string, username: string, isCurrentUser: boolean }>, username: string }> {
    return new Promise(async (resolve, reject) => {
      // Generate a unique userId if not already stored
      let userId = typeof window !== 'undefined' ? window.localStorage.getItem('userId') : null;
      if (!userId) {
        userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        if (typeof window !== 'undefined') {
          window.localStorage.setItem('userId', userId);
        }
      }

      // Check if we have a socket connection
      if (!this.socket || !this.isConnected()) {
        // Try to connect
        this.connect();

        // Wait a bit to see if connection succeeds
        await new Promise(r => setTimeout(r, 2000));

        // If still not connected, use HTTP fallback
        if (!this.isConnected()) {
          return this.joinRoomViaHttp(roomId, username, userId, resolve, reject);
        }
      }

      // If we reach here, we have a socket connection, so use it
      try {
        if (!this.socket) {
          throw new Error('Socket is null');
        }
        // Emit join-room with userId, username, and roomId
        this.socket.emit('join-room', { roomId, username, userId }, (response) => {
          if (response.error) {
            // If socket join fails, try HTTP fallback
            this.joinRoomViaHttp(roomId, username, userId, resolve, reject);
          } else if (response.success) {
            // If the server validated and possibly changed the username, update it locally
            if (response.username && response.username !== username) {
              if (typeof window !== 'undefined') {
                window.localStorage.setItem('username', response.username);
              }
            }

            // Mark the current user in the users list
            const usersWithCurrentFlag = (response.users || []).map((user: { userId?: string; username: string; socketId: string }) => ({
              ...user,
              userId: user.userId || '',
              isCurrentUser: (user.userId || '') === userId
            }));

            resolve({
              users: usersWithCurrentFlag || [],
              username: response.username || username
            });
          } else {
            // If socket join fails, try HTTP fallback
            this.joinRoomViaHttp(roomId, username, userId, resolve, reject);
          }
        });

        // Set a timeout in case the callback never fires
        setTimeout(() => {
          // If we haven't resolved or rejected yet, try HTTP fallback
          this.joinRoomViaHttp(roomId, username, userId, resolve, reject);
        }, 5000);
      } catch (error) {
        // If socket join throws an exception, try HTTP fallback
        this.joinRoomViaHttp(roomId, username, userId, resolve, reject);
      }
    });
  }

  // HTTP fallback for joining a room when socket fails
  private joinRoomViaHttp(
    roomId: string,
    username: string,
    userId: string,
    resolve: (value: { users: Array<{ userId: string, username: string, isCurrentUser: boolean }>, username: string }) => void,
    reject: (reason: Error) => void
  ) {
    // Use axios to make a direct HTTP request to the server
    const apiUrl = `${SOCKET_URL}/api/join-room`;

    axios.post(apiUrl, { roomId, username, userId })
      .then(response => {
        if (response.data.error) {
          reject(new Error(response.data.error));
        } else {
          // If the server validated and possibly changed the username, update it locally
          if (response.data.username && response.data.username !== username) {
            if (typeof window !== 'undefined') {
              window.localStorage.setItem('username', response.data.username);
            }
          }

          // Create a default user list with at least the current user
          const users = response.data.users || [{
            userId,
            username: response.data.username || username,
            socketId: 'http-fallback'
          }];

          // Fix: When mapping users, always provide a fallback for userId (empty string if undefined)
          const usersWithCurrentFlag = users.map((user: { userId?: string; username: string; socketId: string }) => ({
            ...user,
            userId: user.userId || '',
            isCurrentUser: (user.userId || '') === userId
          }));

          resolve({
            users: usersWithCurrentFlag,
            username: response.data.username || username
          });

          // Try to reconnect socket after successful HTTP fallback
          setTimeout(() => this.connect(), 1000);
        }
      })
      .catch(error => {
        // If HTTP fallback also fails, create a minimal response with just the current user
        const fallbackUser = {
          userId,
          username,
          isCurrentUser: true
        };

        // Resolve with just the current user to allow the UI to function
        resolve({
          users: [fallbackUser],
          username
        });

        // Try to reconnect socket after error
        setTimeout(() => this.connect(), 2000);
      });
  }

  // Send code changes to the server with HTTP fallback
  public sendCodeChange(roomId: string, code: string) {
    // If socket is connected, use it
    if (this.socket && this.isConnected()) {
      try {
        // Use volatile for code changes to prevent queueing
        // This helps prevent outdated updates from being sent
        this.socket.volatile.emit('code-change', { roomId, code });
        return true;
      } catch (error) {
        // Fall through to HTTP fallback
      }
    } else {
    }

    // HTTP fallback for code changes
    this.sendCodeChangeViaHttp(roomId, code);
    return true;
  }

  // HTTP fallback for sending code changes
  private sendCodeChangeViaHttp(roomId: string, code: string) {
    // Only send HTTP fallback for significant changes to reduce traffic
    // Store the last sent code to avoid sending duplicates
    const lastSentCode = typeof window !== 'undefined' ? window.localStorage.getItem(`last_http_code_${roomId}`) : null;
    if (lastSentCode === code) {
      return; // Don't send duplicate code
    }

    // Use axios to make a direct HTTP request to the server
    const apiUrl = `${SOCKET_URL}/api/code-change`;
    const userId = typeof window !== 'undefined' ? window.localStorage.getItem('userId') || '' : '';

    axios.post(apiUrl, {
      roomId,
      code,
      userId
    })
    .then(response => {
      // Store the sent code to avoid duplicates
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(`last_http_code_${roomId}`, code);
      }
    })
    .catch(error => {
      console.error('Error sending code change via HTTP:', error);
    });

    // Try to reconnect socket
    if (!this.isConnected()) {
      setTimeout(() => this.connect(), 1000);
    }
  }

  // Send typing notification
  public sendTyping(roomId: string, username: string) {
    if (!this.socket || !this.isConnected()) {
      return;
    }

    // Use the exact username provided by the user without any modifications
    // This ensures we use exactly what the user entered on the dashboard
    const validUsername = username;

    // Get userId from localStorage
    const userId = typeof window !== 'undefined'
      ? window.localStorage.getItem('userId') || `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      : `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    try {
      this.socket.emit('typing', { roomId, username: validUsername, userId });
    } catch (error) {
    }
  }

  // Emit a highlight-line event to the server
  public sendHighlightLine(roomId: string, startLine: number, endLine: number, comment?: string) {
    if (!this.socket || !this.isConnected()) return;
    try {
      this.socket.emit('highlight-line', { roomId, startLine, endLine, comment });
    } catch (error) {
      // Optionally handle error
    }
  }

  // Leave a room
  public leaveRoom(roomId: string) {
    if (!this.socket || !this.isConnected()) return;

    try {
      this.socket.emit('leave-room', roomId);
    } catch (error) {
    }
  }

  // Add a public method to listen for the next connect event
  public onConnect(callback: () => void) {
    if (this.isConnected()) {
      callback();
    } else if (this.socket) {
      this.socket.once('connect', callback);
    } else {
      // If socket is not initialized, initialize and then listen
      this.initSocket();
      // Wait for the socket to be created, then attach the listener
      setTimeout(() => {
        this.socket?.once('connect', callback);
      }, 100);
    }
  }

  public getSocket(): Socket<ServerToClientEvents, ClientToServerEvents> | null {
    return this.socket;
  }

  // Emit cursor movement
  public sendCursorMove(roomId: string, userId: string, position: { x: number; y: number }) {
    if (this.socket && this.connected) {
      this.socket.emit("cursor-move", { roomId, userId, position });
    }
  }

  // Add a method to validate if a room exists on the server
  public async validateRoom(roomId: string): Promise<{ exists: boolean }> {
    if (!this.socket || !this.isConnected()) {
      throw new Error("Socket not connected");
    }

    return new Promise((resolve, reject) => {
      if (!this.socket) {
        throw new Error("Socket is not initialized");
      }
      this.socket.emit("validate-room", { roomId }, (response: { exists: boolean }) => {
        if (response) {
          resolve(response);
        } else {
          reject(new Error("Failed to validate room"));
        }
      });
    });
  }
}

// Export the class itself instead of calling getInstance during export
export default SocketService;
