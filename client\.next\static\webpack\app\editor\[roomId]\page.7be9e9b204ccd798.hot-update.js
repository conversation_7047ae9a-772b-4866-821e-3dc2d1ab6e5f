"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var monaco_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! monaco-editor */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.main.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlignLeft,FiCode,FiCopy,FiDownload,FiHelpCircle,FiLoader,FiMoon,FiPlay,FiSearch,FiSun,FiUpload,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_socketService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/socketService */ \"(app-pages-browser)/./src/services/socketService.ts\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/throttle */ \"(app-pages-browser)/./node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/SocketContext */ \"(app-pages-browser)/./src/context/SocketContext.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_formatCode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/formatCode */ \"(app-pages-browser)/./src/utils/formatCode.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/ThemeContext */ \"(app-pages-browser)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CodeEditor(param) {\n    let { roomId, username: initialUsername } = param;\n    _s();\n    var _s1 = $RefreshSig$();\n    const { isConnected } = (0,_context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket)();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"// Start coding...\");\n    // Use a ref to always track the latest code value\n    const latestCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"// Start coding...\");\n    // Get username from localStorage if available, otherwise use initialUsername\n    const storedUsername =  true ? localStorage.getItem(\"username\") : 0;\n    console.log(\"Initial username: \".concat(initialUsername, \", Stored username: \").concat(storedUsername));\n    // Track the username internally to handle server validation\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUsername || initialUsername);\n    const [typingUser, setTypingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeUsers, setActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUserList, setShowUserList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"javascript\");\n    const [copySuccess, setCopySuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Code execution states\n    const [isExecuting, setIsExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionOutput, setExecutionOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [executionError, setExecutionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOutput, setShowOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Piston API runtimes\n    const [runtimes, setRuntimes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [runtimesLoaded, setRuntimesLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    // Theme and minimap state (ensure these are always defined at the top)\n    const { theme, setTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme)();\n    const [showShortcuts, setShowShortcuts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [minimapEnabled, setMinimapEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Language-specific code snippets\n    const SNIPPETS = {\n        javascript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            }\n        ],\n        typescript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction(): void {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            },\n            {\n                label: \"Type Declaration\",\n                value: \"type MyType = {\\n  key: string;\\n};\"\n            }\n        ],\n        python3: [\n            {\n                label: \"Function\",\n                value: \"def my_function():\\n    # code\\n    pass\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in range(10):\\n    # code\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition:\\n    # code\"\n            },\n            {\n                label: \"Print\",\n                value: \"print('Hello, World!')\"\n            }\n        ],\n        java: [\n            {\n                label: \"Main Method\",\n                value: \"public static void main(String[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"System.out.println(\\\"Hello, World!\\\");\"\n            }\n        ],\n        csharp: [\n            {\n                label: \"Main Method\",\n                value: \"static void Main(string[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"Console.WriteLine(\\\"Hello, World!\\\");\"\n            }\n        ],\n        c: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"printf(\\\"Hello, World!\\\\n\\\");\"\n            }\n        ],\n        cpp: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"std::cout << \\\"Hello, World!\\\" << std::endl;\"\n            }\n        ],\n        go: [\n            {\n                label: \"Main Function\",\n                value: \"func main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i := 0; i < 10; i++ {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"fmt.Println(\\\"Hello, World!\\\")\"\n            }\n        ],\n        ruby: [\n            {\n                label: \"Method\",\n                value: \"def my_method\\n  # code\\nend\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..9\\n  # code\\nend\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition\\n  # code\\nend\"\n            },\n            {\n                label: \"Print\",\n                value: \"puts 'Hello, World!'\"\n            }\n        ],\n        rust: [\n            {\n                label: \"Main Function\",\n                value: \"fn main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..10 {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"println!(\\\"Hello, World!\\\");\"\n            }\n        ],\n        php: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for ($i = 0; $i < 10; $i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if ($condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"echo 'Hello, World!';\"\n            }\n        ]\n    };\n    // Declare the `socketService` instance at the top of the file\n    const socketService = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n    // Leave room handler\n    const handleLeaveRoom = ()=>{\n        socketService.leaveRoom(roomId);\n        router.push(\"/dashboard\");\n    };\n    // Handle editor mounting\n    const handleEditorDidMount = (editor)=>{\n        editorRef.current = editor;\n        setIsLoading(false);\n        // Auto-focus the editor after mounting\n        setTimeout(()=>{\n            if (editor) {\n                editor.focus();\n                // Add keyboard shortcut (Ctrl+Enter) to run code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.CtrlCmd | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.Enter, ()=>{\n                    executeCode();\n                });\n                // Add keyboard shortcut (Shift+Alt+F) to format code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Shift | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Alt | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.KeyF, ()=>{\n                    formatCurrentCode();\n                });\n            }\n        }, 500) // Small delay to ensure the editor is fully ready\n        ;\n    };\n    // Track if the change is from a remote update\n    const isRemoteUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Create a throttled version of the code change handler\n    // This prevents sending too many updates when typing quickly\n    // but ensures updates are sent at regular intervals\n    const throttledCodeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default()({\n        \"CodeEditor.useRef\": (code)=>{\n            if (roomId && isConnected) {\n                console.log(\"Sending throttled code update to room \".concat(roomId, \", length: \").concat(code.length));\n                socketService.sendCodeChange(roomId, code);\n            }\n        }\n    }[\"CodeEditor.useRef\"], 50) // Use throttle with a short interval for more responsive updates\n    ).current;\n    // Create a debounced version of the typing notification\n    const debouncedTypingNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default()({\n        \"CodeEditor.useCallback[debouncedTypingNotification]\": ()=>{\n            if (roomId && isConnected) {\n                // Always send the current username with typing notifications\n                console.log(\"Sending typing notification with username: \".concat(username));\n                socketService.sendTyping(roomId, username);\n            }\n        }\n    }[\"CodeEditor.useCallback[debouncedTypingNotification]\"], 1000), [\n        roomId,\n        isConnected,\n        username\n    ]);\n    // Handle editor change\n    const handleEditorChange = (value)=>{\n        if (typeof value !== \"string\") return;\n        // Make sure loading is off during code changes\n        setIsLoading(false);\n        // If this change is from a remote update, just update the state and return\n        if (isRemoteUpdate.current) {\n            console.log(\"Handling remote update, not emitting\");\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            isRemoteUpdate.current = false;\n            return;\n        }\n        // Only process if the value actually changed\n        if (value !== latestCodeRef.current) {\n            // Update local state immediately\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            // Send typing notification (debounced)\n            debouncedTypingNotification();\n            // Send code update (throttled)\n            throttledCodeChange(value);\n            // If not connected, try to reconnect\n            if (!isConnected) {\n                console.log(\"Socket not connected, attempting to reconnect\");\n                socketService.connect();\n            }\n        }\n    };\n    // Copy code to clipboard\n    const copyCodeToClipboard = ()=>{\n        if (navigator.clipboard && code) {\n            navigator.clipboard.writeText(code).then(()=>{\n                setCopySuccess(true);\n                setTimeout(()=>setCopySuccess(false), 2000);\n            }).catch((err)=>{\n                console.error('Failed to copy code: ', err);\n            });\n        }\n    };\n    // Toggle user list\n    const toggleUserList = ()=>{\n        setShowUserList((prev)=>!prev);\n    };\n    // Change language\n    const changeLanguage = (lang)=>{\n        setLanguage(lang);\n        // Log available runtimes for this language\n        if (runtimesLoaded && runtimes.length > 0) {\n            const availableRuntimes = runtimes.filter((runtime)=>runtime.language === lang || runtime.aliases && runtime.aliases.includes(lang));\n            if (availableRuntimes.length > 0) {\n                console.log(\"Available runtimes for \".concat(lang, \":\"), availableRuntimes);\n            } else {\n                console.log(\"No runtimes available for \".concat(lang));\n            }\n        }\n    };\n    // Format code\n    const formatCurrentCode = ()=>{\n        if (!code || !editorRef.current) return;\n        try {\n            // Format the code based on the current language\n            const formattedCode = (0,_utils_formatCode__WEBPACK_IMPORTED_MODULE_8__.formatCode)(code, language);\n            // Only update if the code actually changed\n            if (formattedCode !== code) {\n                // Update the editor\n                const model = editorRef.current.getModel();\n                if (model) {\n                    // Store current cursor position/selection\n                    const currentPosition = editorRef.current.getPosition();\n                    const currentSelection = editorRef.current.getSelection();\n                    // Update the editor content\n                    editorRef.current.executeEdits('format', [\n                        {\n                            range: model.getFullModelRange(),\n                            text: formattedCode,\n                            forceMoveMarkers: true\n                        }\n                    ]);\n                    // Restore cursor position if possible\n                    if (currentPosition) {\n                        editorRef.current.setPosition(currentPosition);\n                    }\n                    if (currentSelection) {\n                        editorRef.current.setSelection(currentSelection);\n                    }\n                    // Update state and ref\n                    setCode(formattedCode);\n                    latestCodeRef.current = formattedCode;\n                    // Send the formatted code to other users\n                    if (roomId && isConnected) {\n                        console.log(\"Sending formatted code to room \".concat(roomId, \", length: \").concat(formattedCode.length));\n                        socketService.sendCodeChange(roomId, formattedCode);\n                    }\n                    // Show a success message\n                    console.log('Code formatted successfully');\n                }\n            } else {\n                console.log('Code is already formatted');\n            }\n        } catch (error) {\n            console.error('Error formatting code:', error);\n        }\n    };\n    // Clear execution output\n    const clearOutput = ()=>{\n        setExecutionOutput(null);\n        setExecutionError(null);\n    };\n    // Fetch and store available runtimes from Piston API\n    const fetchRuntimes = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get('https://emkc.org/api/v2/piston/runtimes');\n            console.log('Piston API status:', response.status);\n            console.log('Available runtimes:', response.data);\n            // Store the runtimes in state\n            setRuntimes(response.data);\n            setRuntimesLoaded(true);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error fetching Piston API runtimes:', error);\n            return {\n                success: false,\n                error: axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) ? \"\".concat(error.message, \" - \").concat(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) || 'Unknown', \" \").concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText) || '') : String(error)\n            };\n        }\n    };\n    // Check if the Piston API is available\n    const checkPistonAPI = async ()=>{\n        // If we haven't loaded runtimes yet, fetch them\n        if (!runtimesLoaded) {\n            return await fetchRuntimes();\n        }\n        // If we already have runtimes, just return success\n        if (runtimes.length > 0) {\n            return {\n                success: true,\n                data: runtimes\n            };\n        }\n        // If we've tried to load runtimes but have none, try again\n        return await fetchRuntimes();\n    };\n    // Test the Piston API with a hardcoded sample payload\n    const testPistonAPI = async ()=>{\n        setIsExecuting(true);\n        setExecutionError(null);\n        setExecutionOutput(null);\n        setShowOutput(true);\n        try {\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Find JavaScript runtime\n            const jsRuntimes = apiStatus.data.filter((runtime)=>runtime.language === \"javascript\" || runtime.aliases && runtime.aliases.includes(\"javascript\"));\n            if (jsRuntimes.length === 0) {\n                setExecutionError('No JavaScript runtime found. Please try a different language.');\n                return;\n            }\n            const jsRuntime = jsRuntimes[0];\n            console.log(\"Using JavaScript runtime: \".concat(jsRuntime.language, \" \").concat(jsRuntime.version));\n            // Sample JavaScript code that should work\n            const samplePayload = {\n                language: jsRuntime.language,\n                version: jsRuntime.version,\n                files: [\n                    {\n                        name: \"main.js\",\n                        content: \"console.log('Hello, World!');\"\n                    }\n                ],\n                stdin: \"\",\n                args: []\n            };\n            console.log('Testing Piston API with sample payload:', JSON.stringify(samplePayload, null, 2));\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', samplePayload);\n            console.log('Sample execution response:', response.data);\n            if (response.data.run) {\n                setExecutionOutput(\"API Test Successful!\\n\\n\" + \"Output: \" + response.data.run.stdout + \"\\n\\n\" + \"The API is working correctly. You can now run your own code.\");\n            } else {\n                setExecutionError('API test failed. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error testing Piston API:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                setExecutionError(\"API Test Failed: \".concat(error.response.status, \" \").concat(error.response.statusText, \"\\n\\n\") + JSON.stringify(error.response.data, null, 2));\n            } else {\n                setExecutionError(error instanceof Error ? \"API Test Error: \".concat(error.message) : 'An unknown error occurred while testing the API.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // Execute code using Piston API\n    const executeCode = async ()=>{\n        if (!code || isExecuting) return;\n        // Map Monaco editor language to Piston API language\n        const languageMap = {\n            javascript: \"javascript\",\n            typescript: \"typescript\",\n            python: \"python3\",\n            python3: \"python3\",\n            java: \"java\",\n            csharp: \"csharp\",\n            c: \"c\",\n            cpp: \"cpp\",\n            go: \"go\",\n            ruby: \"ruby\",\n            rust: \"rust\",\n            php: \"php\"\n        };\n        // Get the mapped language\n        const pistonLanguage = languageMap[language] || language;\n        // Check if we have runtimes loaded\n        if (!runtimesLoaded || runtimes.length === 0) {\n            setExecutionError('Runtimes not loaded yet. Please try again in a moment.');\n            setShowOutput(true);\n            return;\n        }\n        // Find available runtimes for the selected language\n        const availableRuntimes = runtimes.filter((runtime)=>runtime.language === pistonLanguage || runtime.aliases && runtime.aliases.includes(pistonLanguage));\n        // Check if the language is supported\n        if (availableRuntimes.length === 0) {\n            // Get a list of supported languages from the runtimes\n            const supportedLanguages = [\n                ...new Set(runtimes.flatMap((runtime)=>[\n                        runtime.language,\n                        ...runtime.aliases || []\n                    ]))\n            ].sort();\n            setExecutionError(\"The language '\".concat(language, \"' (mapped to '\").concat(pistonLanguage, \"') is not supported by the Piston API.\\n\\n\") + \"Supported languages: \".concat(supportedLanguages.join(', ')));\n            setShowOutput(true);\n            return;\n        }\n        // Get the latest version of the runtime\n        const selectedRuntime = availableRuntimes[0];\n        try {\n            setIsExecuting(true);\n            setExecutionError(null);\n            setExecutionOutput(null);\n            setShowOutput(true);\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Determine file extension based on language\n            let fileExtension = '';\n            if (selectedRuntime.language === 'python3' || selectedRuntime.language === 'python') fileExtension = '.py';\n            else if (selectedRuntime.language === 'javascript') fileExtension = '.js';\n            else if (selectedRuntime.language === 'typescript') fileExtension = '.ts';\n            else if (selectedRuntime.language === 'java') fileExtension = '.java';\n            else if (selectedRuntime.language === 'csharp') fileExtension = '.cs';\n            else if (selectedRuntime.language === 'c') fileExtension = '.c';\n            else if (selectedRuntime.language === 'cpp') fileExtension = '.cpp';\n            else if (selectedRuntime.language === 'go') fileExtension = '.go';\n            else if (selectedRuntime.language === 'rust') fileExtension = '.rs';\n            else if (selectedRuntime.language === 'ruby') fileExtension = '.rb';\n            else if (selectedRuntime.language === 'php') fileExtension = '.php';\n            else fileExtension = \".\".concat(selectedRuntime.language);\n            console.log(\"Selected runtime: \".concat(selectedRuntime.language, \" \").concat(selectedRuntime.version));\n            // Prepare the payload according to Piston API documentation\n            const payload = {\n                language: selectedRuntime.language,\n                version: selectedRuntime.version,\n                files: [\n                    {\n                        name: \"main\".concat(fileExtension),\n                        content: code\n                    }\n                ],\n                stdin: '',\n                args: [],\n                compile_timeout: 10000,\n                run_timeout: 5000\n            };\n            // Log the payload for debugging\n            console.log(\"Executing \".concat(pistonLanguage, \" code with payload:\"), JSON.stringify(payload, null, 2));\n            // Make the API request\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', payload);\n            console.log('Execution response:', response.data);\n            const result = response.data;\n            if (result.run) {\n                // Format the output\n                let output = '';\n                let hasOutput = false;\n                // Add compile output if available (for compiled languages)\n                if (result.compile && result.compile.stderr) {\n                    output += \"Compilation output:\\n\".concat(result.compile.stderr, \"\\n\\n\");\n                    hasOutput = true;\n                }\n                // Add standard output\n                if (result.run.stdout) {\n                    output += result.run.stdout;\n                    hasOutput = true;\n                }\n                // Add error output\n                if (result.run.stderr) {\n                    if (hasOutput) output += '\\n';\n                    output += \"Error output:\\n\".concat(result.run.stderr);\n                    hasOutput = true;\n                }\n                // Add exit code if non-zero\n                if (result.run.code !== 0) {\n                    if (hasOutput) output += '\\n';\n                    output += \"\\nProcess exited with code \".concat(result.run.code);\n                    hasOutput = true;\n                }\n                if (!hasOutput) {\n                    output = 'Program executed successfully with no output.';\n                }\n                setExecutionOutput(output);\n            } else {\n                setExecutionError('Failed to execute code. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error executing code:', error);\n            // Handle Axios errors with more detailed information\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                const statusCode = error.response.status;\n                const responseData = error.response.data;\n                console.error('API Error Details:', {\n                    status: statusCode,\n                    data: responseData\n                });\n                // Format a more helpful error message\n                if (statusCode === 400) {\n                    setExecutionError(\"API Error (400 Bad Request): \".concat(JSON.stringify(responseData), \"\\n\\n\") + 'This usually means the API request format is incorrect. ' + 'Please check the console for more details.');\n                } else if (statusCode === 429) {\n                    setExecutionError('Rate limit exceeded. Please try again later.');\n                } else {\n                    setExecutionError(\"API Error (\".concat(statusCode, \"): \").concat(JSON.stringify(responseData), \"\\n\\n\") + 'Please check the console for more details.');\n                }\n            } else {\n                // Handle non-Axios errors\n                setExecutionError(error instanceof Error ? \"Error: \".concat(error.message) : 'An unknown error occurred while executing the code.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // This function will be called when we receive a code update from another user\n    const handleCodeUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleCodeUpdate]\": (incomingCode)=>{\n            console.log(\"Remote code update received, length:\", incomingCode.length);\n            // Make sure loading is off during code updates\n            setIsLoading(false);\n            // Only update if the code is different from our latest code\n            if (incomingCode !== latestCodeRef.current) {\n                try {\n                    // Set the flag to indicate this is a remote update\n                    isRemoteUpdate.current = true;\n                    // Update the editor if it's mounted\n                    if (editorRef.current) {\n                        console.log(\"Updating editor with remote code\");\n                        // Use the editor's model to update the value to preserve cursor position\n                        const model = editorRef.current.getModel();\n                        if (model) {\n                            // Store current cursor position/selection\n                            const currentPosition = editorRef.current.getPosition();\n                            const currentSelection = editorRef.current.getSelection();\n                            // Use executeEdits with better handling of cursor position\n                            editorRef.current.executeEdits('remote', [\n                                {\n                                    range: model.getFullModelRange(),\n                                    text: incomingCode,\n                                    forceMoveMarkers: true\n                                }\n                            ]);\n                            // Restore cursor position if possible\n                            if (currentPosition) {\n                                editorRef.current.setPosition(currentPosition);\n                            }\n                            if (currentSelection) {\n                                editorRef.current.setSelection(currentSelection);\n                            }\n                            // Also update our state and ref\n                            setCode(incomingCode);\n                            latestCodeRef.current = incomingCode;\n                        }\n                    } else {\n                        // If editor isn't mounted yet, just update the state and ref\n                        console.log(\"Editor not mounted, updating state only\");\n                        setCode(incomingCode);\n                        latestCodeRef.current = incomingCode;\n                        isRemoteUpdate.current = false;\n                    }\n                } catch (error) {\n                    console.error(\"Error updating editor with remote code:\", error);\n                    isRemoteUpdate.current = false;\n                }\n            } else {\n                console.log(\"Remote code matches current code, ignoring\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleCodeUpdate]\"], []);\n    // State to track cursor positions\n    const [cursorPositions, setCursorPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Emit cursor movement\n    const handleMouseMove = (e)=>{\n        const position = {\n            x: e.clientX,\n            y: e.clientY\n        };\n        _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().sendCursorMove(roomId, username, position);\n    };\n    // Listen for cursor movement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            const handleCursorMove = {\n                \"CodeEditor.useEffect.handleCursorMove\": (param)=>{\n                    let { userId, position } = param;\n                    setCursorPositions({\n                        \"CodeEditor.useEffect.handleCursorMove\": (prev)=>({\n                                ...prev,\n                                [userId]: position\n                            })\n                    }[\"CodeEditor.useEffect.handleCursorMove\"]);\n                }\n            }[\"CodeEditor.useEffect.handleCursorMove\"];\n            _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().on(\"cursor-move\", handleCursorMove);\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().off(\"cursor-move\", handleCursorMove);\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId\n    ]);\n    // Render cursors\n    const renderCursors = ()=>{\n        return Object.entries(cursorPositions).map((param)=>{\n            let [userId, position] = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    left: position.x,\n                    top: position.y,\n                    backgroundColor: \"red\",\n                    width: 10,\n                    height: 10,\n                    borderRadius: \"50%\",\n                    pointerEvents: \"none\"\n                }\n            }, userId, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 718,\n                columnNumber: 7\n            }, this);\n        });\n    };\n    // Fetch runtimes when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            fetchRuntimes();\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": _s1(()=>{\n            _s1();\n            // Handle user typing notifications\n            const handleUserTyping = {\n                \"CodeEditor.useEffect.handleUserTyping\": (param)=>{\n                    let { username, userId } = param;\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    // Don't show typing indicator for current user\n                    if (userId && userId === currentUserId) {\n                        return;\n                    }\n                    // Use the exact username without any modifications\n                    // This ensures we display exactly what the user entered on the dashboard\n                    setTypingUser(username);\n                    console.log(\"User typing: \".concat(username));\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserTyping\": ()=>setTypingUser(null)\n                    }[\"CodeEditor.useEffect.handleUserTyping\"], 2000);\n                }\n            }[\"CodeEditor.useEffect.handleUserTyping\"];\n            // Handle user list updates from both user-joined/user-left and room-users-updated events\n            const handleUserListUpdate = {\n                \"CodeEditor.useEffect.handleUserListUpdate\": (data)=>{\n                    console.log('User list update received:', data);\n                    let users = [];\n                    // Handle different event formats\n                    if (Array.isArray(data)) {\n                        // This is from user-joined or user-left events\n                        users = data;\n                    } else if (data && Array.isArray(data.users)) {\n                        // This is from room-users-updated event\n                        users = data.users.map({\n                            \"CodeEditor.useEffect.handleUserListUpdate\": (user)=>{\n                                if (typeof user === 'string') {\n                                    // If it's just a username string, convert to user object\n                                    return {\n                                        username: user\n                                    };\n                                }\n                                return user;\n                            }\n                        }[\"CodeEditor.useEffect.handleUserListUpdate\"]);\n                    }\n                    console.log('Processed users:', users);\n                    console.log('Current username state:', username);\n                    console.log('Stored username:', localStorage.getItem('username'));\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    console.log('Current userId from localStorage:', currentUserId);\n                    // Filter out any invalid users and map to display names\n                    const usernames = users.filter({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>user && user.username\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]).map({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>{\n                            const displayName = user.username;\n                            const isCurrentUser = user.userId === currentUserId;\n                            return isCurrentUser ? \"\".concat(displayName, \" (you)\") : displayName;\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]);\n                    console.log('Setting active users:', usernames);\n                    // Only update if we have users to display\n                    if (usernames.length > 0) {\n                        setActiveUsers(usernames);\n                    } else if (activeUsers.length === 0) {\n                        // If we don't have users from the server but we're connected, add ourselves\n                        const storedUsername = localStorage.getItem('username');\n                        if (storedUsername) {\n                            setActiveUsers([\n                                \"\".concat(storedUsername, \" (you)\")\n                            ]);\n                        }\n                    }\n                    // Log the current state of the active users list\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserListUpdate\": ()=>{\n                            console.log('Active users state after update:', activeUsers);\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate\"], 100);\n                }\n            }[\"CodeEditor.useEffect.handleUserListUpdate\"];\n            // Handle request for initial code from new users\n            const handleGetInitialCode1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n                \"CodeEditor.useEffect.useCallback[handleGetInitialCode]\": (data)=>{\n                    console.log(\"Received request for initial code from \".concat(data.requestingUsername, \" (\").concat(data.requestingUserId, \")\"));\n                    // Only respond if we have code and we're connected\n                    if (roomId && latestCodeRef.current && latestCodeRef.current.trim() !== \"// Start coding...\") {\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        console.log(\"Sending initial code to \".concat(data.requestingUsername, \", length: \").concat(latestCodeRef.current.length));\n                        socketServiceInstance.sendInitialCode(roomId, latestCodeRef.current, data.requestingUserId);\n                    } else {\n                        console.log(\"Not sending initial code - no meaningful code to share or not in room\");\n                    }\n                }\n            }[\"CodeEditor.useEffect.useCallback[handleGetInitialCode]\"], [\n                roomId\n            ]);\n            // Handle receiving initial code as a new user\n            const handleInitialCodeReceived1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n                \"CodeEditor.useEffect.useCallback[handleInitialCodeReceived]\": (data)=>{\n                    console.log(\"Received initial code, length: \".concat(data.code.length));\n                    // Only apply initial code if we don't have meaningful code yet\n                    // This prevents overwriting code that the user might have already started typing\n                    if (latestCodeRef.current === \"// Start coding...\" || latestCodeRef.current.trim() === \"\") {\n                        console.log(\"Applying received initial code\");\n                        // Set the flag to indicate this is a remote update\n                        isRemoteUpdate.current = true;\n                        // Update the code state and ref\n                        setCode(data.code);\n                        latestCodeRef.current = data.code;\n                        // Update the editor if it's mounted\n                        if (editorRef.current) {\n                            editorRef.current.setValue(data.code);\n                        }\n                    } else {\n                        console.log(\"Not applying initial code - user already has meaningful code\");\n                    }\n                }\n            }[\"CodeEditor.useEffect.useCallback[handleInitialCodeReceived]\"], []);\n            // Register event listeners\n            const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n            socketServiceInstance.on('code-update', handleCodeUpdate);\n            socketServiceInstance.on('user-typing', handleUserTyping);\n            socketServiceInstance.on('user-joined', handleUserListUpdate);\n            socketServiceInstance.on('user-left', handleUserListUpdate);\n            socketServiceInstance.on('room-users-updated', handleUserListUpdate);\n            socketServiceInstance.on('get-initial-code', handleGetInitialCode1);\n            socketServiceInstance.on('initial-code-received', handleInitialCodeReceived1);\n            // Join the room when component mounts\n            if (roomId) {\n                console.log(\"Joining room:\", roomId);\n                // Only show loading on initial join, not for code updates\n                if (!editorRef.current) {\n                    setIsLoading(true);\n                }\n                const joinRoom = {\n                    \"CodeEditor.useEffect.joinRoom\": async ()=>{\n                        try {\n                            if (socketServiceInstance.isConnected()) {\n                                console.log(\"Socket connected, joining room\");\n                                const { username: validatedUsername, users } = await socketServiceInstance.joinRoom(roomId, username);\n                                console.log(\"Successfully joined room:\", roomId);\n                                console.log(\"Users in room:\", users);\n                                if (validatedUsername !== username) {\n                                    console.log(\"Server validated username from \".concat(username, \" to \").concat(validatedUsername));\n                                    setUsername(validatedUsername);\n                                    localStorage.setItem('username', validatedUsername);\n                                }\n                                if (users && Array.isArray(users)) {\n                                    console.log('Setting active users from join response');\n                                    handleUserListUpdate(users);\n                                }\n                                setIsLoading(false);\n                            } else {\n                                console.log(\"Socket not connected, trying to connect...\");\n                                await new Promise({\n                                    \"CodeEditor.useEffect.joinRoom\": (resolve)=>socketServiceInstance.onConnect({\n                                            \"CodeEditor.useEffect.joinRoom\": ()=>resolve()\n                                        }[\"CodeEditor.useEffect.joinRoom\"])\n                                }[\"CodeEditor.useEffect.joinRoom\"]);\n                                // After connect, try join again\n                                await joinRoom();\n                                return;\n                            }\n                        } catch (error) {\n                            console.error(\"Error joining room:\", error);\n                            setIsLoading(false);\n                        }\n                    }\n                }[\"CodeEditor.useEffect.joinRoom\"];\n                // Start the join process\n                joinRoom();\n                // Set a timeout to hide the loading screen even if the join fails\n                setTimeout({\n                    \"CodeEditor.useEffect\": ()=>{\n                        setIsLoading(false);\n                    }\n                }[\"CodeEditor.useEffect\"], 3000);\n            }\n            // Clean up event listeners when component unmounts\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    // Use an instance of SocketService\n                    const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                    socketServiceInstance.off('code-update', handleCodeUpdate);\n                    socketServiceInstance.off('user-typing', handleUserTyping);\n                    socketServiceInstance.off('user-joined', handleUserListUpdate);\n                    socketServiceInstance.off('user-left', handleUserListUpdate);\n                    socketServiceInstance.off('room-users-updated', handleUserListUpdate);\n                    socketServiceInstance.off('get-initial-code', handleGetInitialCode1);\n                    socketServiceInstance.off('initial-code-received', handleInitialCodeReceived1);\n                    // Leave the room when component unmounts\n                    if (roomId) {\n                        socketServiceInstance.leaveRoom(roomId);\n                    }\n                }\n            })[\"CodeEditor.useEffect\"];\n        }, \"yzhhjOykwJuMtBIvd+FLBlGB68w=\")\n    }[\"CodeEditor.useEffect\"], [\n        roomId,\n        initialUsername,\n        username,\n        handleCodeUpdate,\n        handleGetInitialCode,\n        handleInitialCodeReceived\n    ]);\n    // Download code as file\n    const handleDownload = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"code-\".concat(roomId || \"session\", \".txt\");\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    // Upload code from file\n    const handleUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            var _event_target;\n            if (typeof ((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result) === \"string\") {\n                setCode(event.target.result);\n                latestCodeRef.current = event.target.result;\n            }\n        };\n        reader.readAsText(file);\n    };\n    // Show Monaco's find/replace dialog\n    const handleFindReplace = ()=>{\n        if (editorRef.current) {\n            const action = editorRef.current.getAction(\"actions.find\");\n            if (action) action.run();\n        }\n    };\n    // Insert code snippet\n    const insertSnippet = (snippet)=>{\n        if (editorRef.current) {\n            const model = editorRef.current.getModel();\n            const position = editorRef.current.getPosition();\n            if (model && position) {\n                editorRef.current.executeEdits(\"snippet\", [\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(position.lineNumber, position.column, position.lineNumber, position.column),\n                        text: snippet,\n                        forceMoveMarkers: true\n                    }\n                ]);\n                editorRef.current.focus();\n            }\n        }\n    };\n    // Use the instance of SocketService for the connect method\n    const handleReconnect = ()=>{\n        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n        socketServiceInstance.connect();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-[80vh] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-800 shadow-xl flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"bg-gray-100 dark:bg-zinc-800 px-4 py-2 flex flex-wrap items-center justify-between border-b border-gray-200 dark:border-gray-700 gap-2 min-h-[48px]\",\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    style: {\n                        zIndex: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-3 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 min-w-[90px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1002,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-400 truncate\",\n                                            children: isConnected ? 'Connected' : 'Disconnected'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1003,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded ml-1 transition-colors\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1001,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: language,\n                                    onChange: (e)=>changeLanguage(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"javascript\",\n                                            children: \"JavaScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1022,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"typescript\",\n                                            children: \"TypeScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1023,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"python3\",\n                                            children: \"Python\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1024,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"java\",\n                                            children: \"Java\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1025,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"csharp\",\n                                            children: \"C#\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1026,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"c\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1027,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"cpp\",\n                                            children: \"C++\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1028,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"go\",\n                                            children: \"Go\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1029,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ruby\",\n                                            children: \"Ruby\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1030,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rust\",\n                                            children: \"Rust\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1031,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"php\",\n                                            children: \"PHP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1017,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: formatCurrentCode,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm bg-blue-600 hover:bg-blue-700 text-white transition-colors whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiAlignLeft, {\n                                            size: 14\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1042,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Format\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1043,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1036,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: executeCode,\n                                    disabled: isExecuting,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm \".concat(isExecuting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700', \" text-white transition-colors whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: isExecuting ? 1 : 1.05\n                                    },\n                                    whileTap: {\n                                        scale: isExecuting ? 1 : 0.95\n                                    },\n                                    children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                                className: \"animate-spin\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1056,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Running...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1057,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiPlay, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1061,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Run Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1062,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1047,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Room:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1069,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-white dark:bg-zinc-700 px-2 py-1 rounded font-mono text-xs truncate max-w-[120px]\",\n                                            children: roomId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1070,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                            onClick: copyCodeToClipboard,\n                                            className: \"text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            title: \"Copy code to clipboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCopy, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1078,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1071,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1068,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 999,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-2 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowOutput((prev)=>!prev),\n                                    className: \"flex items-center space-x-1 text-sm \".concat(showOutput ? 'text-blue-500 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300', \" hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCode, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1091,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Output\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1092,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1085,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: toggleUserList,\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUsers, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                activeUsers.length,\n                                                \" online\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1103,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1096,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"flex items-center space-x-1 text-sm text-yellow-600 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSun, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1114,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiMoon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1114,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setMinimapEnabled((v)=>!v),\n                                    className: \"flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Minimap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Minimap\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1125,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleFindReplace,\n                                    className: \"flex items-center space-x-1 text-sm text-green-600 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Find/Replace\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSearch, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1136,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1129,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleDownload,\n                                    className: \"flex items-center space-x-1 text-sm text-indigo-600 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Download Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiDownload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1147,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"flex items-center space-x-1 text-sm text-pink-600 dark:text-pink-300 bg-pink-100 dark:bg-pink-900/30 hover:bg-pink-200 dark:hover:bg-pink-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Upload Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUpload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1158,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \".js,.ts,.py,.java,.c,.cpp,.go,.rb,.rs,.php,.txt\",\n                                    style: {\n                                        display: \"none\"\n                                    },\n                                    onChange: handleUpload\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    onChange: (e)=>e.target.value && insertSnippet(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[100px]\",\n                                    defaultValue: \"\",\n                                    title: \"Insert Snippet\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Snippets\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1175,\n                                            columnNumber: 15\n                                        }, this),\n                                        (SNIPPETS[language] || SNIPPETS['javascript']).map((snippet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: snippet.value,\n                                                children: snippet.label\n                                            }, snippet.label, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1177,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1169,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowShortcuts(true),\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Keyboard Shortcuts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiHelpCircle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1189,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleLeaveRoom,\n                                    className: \"flex items-center space-x-1 text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors px-2 py-1 rounded-md ml-2 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Leave Room\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1199,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1193,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1083,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 992,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showUserList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute right-0 top-12 bg-white dark:bg-zinc-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 w-48 overflow-hidden\",\n                        initial: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            height: 'auto'\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 border-b border-gray-200 dark:border-gray-700 font-medium text-sm text-gray-600 dark:text-gray-300\",\n                                children: \"Active Users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1214,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"max-h-48 overflow-y-auto\",\n                                children: activeUsers.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.li, {\n                                        className: \"px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-zinc-700 flex items-center space-x-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full bg-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1226,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: user\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1227,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1219,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1217,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1207,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1205,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"h-[calc(100%-40px)] \".concat(showOutput ? 'pb-[33%]' : ''),\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.4,\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                height: \"100%\",\n                                defaultLanguage: language,\n                                defaultValue: code,\n                                onChange: handleEditorChange,\n                                onMount: handleEditorDidMount,\n                                theme: theme === \"dark\" ? \"vs-dark\" : \"light\",\n                                options: {\n                                    minimap: {\n                                        enabled: minimapEnabled\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1245,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1244,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1242,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1236,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: typingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 left-4 text-sm text-gray-400 bg-gray-800/80 px-3 py-1 rounded-full backdrop-blur-sm\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            typingUser,\n                            \" is typing...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1261,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1259,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gray-900 text-white border-t border-gray-700 h-1/3 overflow-hidden flex flex-col\",\n                        initial: {\n                            height: 0\n                        },\n                        animate: {\n                            height: '33%'\n                        },\n                        exit: {\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 bg-gray-800 border-b border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1284,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearOutput,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1286,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: testPistonAPI,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-blue-400 hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Test API\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1293,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowOutput(false),\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1300,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1285,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1283,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-auto font-mono text-sm whitespace-pre-wrap\",\n                                children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1311,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Executing code...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1312,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1310,\n                                    columnNumber: 19\n                                }, this) : executionError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400\",\n                                    children: executionError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1315,\n                                    columnNumber: 19\n                                }, this) : executionOutput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: executionOutput\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1317,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: 'Click \"Run Code\" to execute your code.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1319,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1308,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1276,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1274,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: copySuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 right-4 text-sm text-white bg-green-500 px-3 py-1 rounded-full shadow-lg\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: \"Copied to clipboard!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1329,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1327,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showShortcuts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 max-w-md w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-700 dark:hover:text-white\",\n                                    onClick: ()=>setShowShortcuts(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1351,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    children: \"Keyboard Shortcuts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1357,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Run Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1359,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+Enter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1359,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Format Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1360,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Shift+Alt+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1360,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Find/Replace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1361,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1361,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Download Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1362,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+D\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1362,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Upload Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1363,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+U\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1363,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Minimap:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1364,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+M\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1364,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Theme:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1365,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+T\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1365,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Show Shortcuts:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1366,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+H\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1366,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1358,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1350,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1344,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1342,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 990,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 988,\n        columnNumber: 5\n    }, this);\n} // Keyboard shortcuts global handler\n // useEffect(() => {\n //   const handler = (e: KeyboardEvent) => {\n //     if (e.altKey && e.key.toLowerCase() === \"d\") {\n //       e.preventDefault();\n //       handleDownload();\n //     } else if (e.altKey && e.key.toLowerCase() === \"u\") {\n //       e.preventDefault();\n //       fileInputRef.current?.click();\n //     } else if (e.altKey && e.key.toLowerCase() === \"m\") {\n //       e.preventDefault();\n //       setMinimapEnabled((v: boolean) => !v);\n //     } else if (e.altKey && e.key.toLowerCase() === \"t\") {\n //       e.preventDefault();\n //       setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n //     } else if (e.altKey && e.key.toLowerCase() === \"h\") {\n //       e.preventDefault();\n //       setShowShortcuts(true);\n //     }\n //   };\n //   window.addEventListener(\"keydown\", handler);\n //   return () => window.removeEventListener(\"keydown\", handler);\n // }, [theme, setTheme]);\n_s(CodeEditor, \"2WwyeeZiqxytaE++jGkyfL2MSN0=\", false, function() {\n    return [\n        _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme\n    ];\n});\n_c = CodeEditor;\nvar _c;\n$RefreshReg$(_c, \"CodeEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CodeEditor.tsx\n"));

/***/ })

});