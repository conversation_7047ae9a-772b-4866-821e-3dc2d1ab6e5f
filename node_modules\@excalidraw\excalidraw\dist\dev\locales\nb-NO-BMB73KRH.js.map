{"version": 3, "sources": ["../../../locales/nb-NO.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Lim inn\",\n    \"pasteAsPlaintext\": \"Lim inn som klartekst\",\n    \"pasteCharts\": \"Lim inn diagrammer\",\n    \"selectAll\": \"Velg alt\",\n    \"multiSelect\": \"Legg til element i utvalg\",\n    \"moveCanvas\": \"Flytt lerretet\",\n    \"cut\": \"<PERSON>lip<PERSON> ut\",\n    \"copy\": \"Kopier\",\n    \"copyAsPng\": \"Kopier til PNG\",\n    \"copyAsSvg\": \"Kopier til utklippstavlen som SVG\",\n    \"copyText\": \"Kopier til utklippstavlen som tekst\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Flytt framover\",\n    \"sendToBack\": \"Send bakerst\",\n    \"bringToFront\": \"Flytt forrest\",\n    \"sendBackward\": \"Send bakover\",\n    \"delete\": \"Slett\",\n    \"copyStyles\": \"Kopier stiler\",\n    \"pasteStyles\": \"Lim inn stiler\",\n    \"stroke\": \"Strek\",\n    \"background\": \"Bakgrunn\",\n    \"fill\": \"Fyll\",\n    \"strokeWidth\": \"Strektykkelse\",\n    \"strokeStyle\": \"Strekstil\",\n    \"strokeStyle_solid\": \"Heltrukket\",\n    \"strokeStyle_dashed\": \"Stiplet\",\n    \"strokeStyle_dotted\": \"Prikket\",\n    \"sloppiness\": \"Ujevnhet\",\n    \"opacity\": \"Synlighet\",\n    \"textAlign\": \"Tekstjustering\",\n    \"edges\": \"Kanter\",\n    \"sharp\": \"Skarp\",\n    \"round\": \"Rund\",\n    \"arrowheads\": \"Pilspisser\",\n    \"arrowhead_none\": \"Ingen\",\n    \"arrowhead_arrow\": \"Pil\",\n    \"arrowhead_bar\": \"Søyle\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Trekant\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Skriftstørrelse\",\n    \"fontFamily\": \"Fontfamilie\",\n    \"addWatermark\": \"Legg til \\\"Laget med Excalidraw\\\"\",\n    \"handDrawn\": \"Håndtegnet\",\n    \"normal\": \"Normal\",\n    \"code\": \"Kode\",\n    \"small\": \"Liten\",\n    \"medium\": \"Medium\",\n    \"large\": \"Stor\",\n    \"veryLarge\": \"Svært stor\",\n    \"solid\": \"Helfarge\",\n    \"hachure\": \"Skravert\",\n    \"zigzag\": \"Sikk-sakk\",\n    \"crossHatch\": \"Krysskravert\",\n    \"thin\": \"Tynn\",\n    \"bold\": \"Tykk\",\n    \"left\": \"Venstre\",\n    \"center\": \"Midtstill\",\n    \"right\": \"Høyre\",\n    \"extraBold\": \"Ekstra tykk\",\n    \"architect\": \"Arkitekt\",\n    \"artist\": \"Kunstner\",\n    \"cartoonist\": \"Tegner\",\n    \"fileTitle\": \"Filnavn\",\n    \"colorPicker\": \"Fargevelger\",\n    \"canvasColors\": \"Brukes på lerretet\",\n    \"canvasBackground\": \"Lerretsbakgrunn\",\n    \"drawingCanvas\": \"Lerret\",\n    \"layers\": \"Lag\",\n    \"actions\": \"Handlinger\",\n    \"language\": \"Språk\",\n    \"liveCollaboration\": \"Sanntids-samarbeid...\",\n    \"duplicateSelection\": \"Dupliser\",\n    \"untitled\": \"Uten navn\",\n    \"name\": \"Navn\",\n    \"yourName\": \"Ditt navn\",\n    \"madeWithExcalidraw\": \"Laget med Excalidraw\",\n    \"group\": \"Gruppér utvalg\",\n    \"ungroup\": \"Avgruppér utvalg\",\n    \"collaborators\": \"Samarbeidspartnere\",\n    \"showGrid\": \"Vis rutenett\",\n    \"addToLibrary\": \"Legg til i bibliotek\",\n    \"removeFromLibrary\": \"Fjern fra bibliotek\",\n    \"libraryLoadingMessage\": \"Laster bibliotek…\",\n    \"libraries\": \"Bla gjennom biblioteker\",\n    \"loadingScene\": \"Laster inn scene…\",\n    \"align\": \"Juster\",\n    \"alignTop\": \"Juster øverst\",\n    \"alignBottom\": \"Juster nederst\",\n    \"alignLeft\": \"Juster venstre\",\n    \"alignRight\": \"Juster høyre\",\n    \"centerVertically\": \"Midtstill vertikalt\",\n    \"centerHorizontally\": \"Midtstill horisontalt\",\n    \"distributeHorizontally\": \"Distribuer horisontalt\",\n    \"distributeVertically\": \"Distribuer vertikalt\",\n    \"flipHorizontal\": \"Snu horisontalt\",\n    \"flipVertical\": \"Snu vertikalt\",\n    \"viewMode\": \"Visningsmodus\",\n    \"share\": \"Del\",\n    \"showStroke\": \"Vis fargevelger for kantfarge\",\n    \"showBackground\": \"Vis fargevelger for bakgrunnsfarge\",\n    \"toggleTheme\": \"Veksle tema\",\n    \"personalLib\": \"Personlig bibliotek\",\n    \"excalidrawLib\": \"Excalidraw-bibliotek\",\n    \"decreaseFontSize\": \"Reduser skriftstørrelse\",\n    \"increaseFontSize\": \"Øk skriftstørrelse\",\n    \"unbindText\": \"Avbind tekst\",\n    \"bindText\": \"Bind tekst til beholderen\",\n    \"createContainerFromText\": \"La tekst flyte i en beholder\",\n    \"link\": {\n      \"edit\": \"Rediger lenke\",\n      \"editEmbed\": \"Rediger lenke og bygg inn\",\n      \"create\": \"Opprett lenke\",\n      \"createEmbed\": \"Opprett lenke og bygg inn\",\n      \"label\": \"Lenke\",\n      \"labelEmbed\": \"Lenk & bygg inn\",\n      \"empty\": \"Ingen lenke er valgt\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Rediger linje\",\n      \"exit\": \"Avslutt linjeredigering\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Lås\",\n      \"unlock\": \"Lås opp\",\n      \"lockAll\": \"Lås alle\",\n      \"unlockAll\": \"Lås opp alle\"\n    },\n    \"statusPublished\": \"Publisert\",\n    \"sidebarLock\": \"Holde sidemenyen åpen\",\n    \"selectAllElementsInFrame\": \"Velg alle elementene i rammen\",\n    \"removeAllElementsFromFrame\": \"Fjern alle elementer fra rammen\",\n    \"eyeDropper\": \"Velg farge fra lerretet\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Ingen elementer lagt til ennå...\",\n    \"hint_emptyLibrary\": \"Velg et objekt på lerretet for å legge det til her, eller installer et bibliotek fra den offentlige samlingen under.\",\n    \"hint_emptyPrivateLibrary\": \"Velg et objekt på lerretet for å legge det til her.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Tøm lerretet og tilbakestill bakgrunnsfargen\",\n    \"exportJSON\": \"Eksporter til fil\",\n    \"exportImage\": \"Eksporter bilde...\",\n    \"export\": \"Lagre som...\",\n    \"copyToClipboard\": \"Kopier til utklippstavle\",\n    \"save\": \"Lagre til aktiv fil\",\n    \"saveAs\": \"Lagre som\",\n    \"load\": \"Åpne\",\n    \"getShareableLink\": \"Få delingslenke\",\n    \"close\": \"Lukk\",\n    \"selectLanguage\": \"Velg språk\",\n    \"scrollBackToContent\": \"Skroll tilbake til innhold\",\n    \"zoomIn\": \"Zoom inn\",\n    \"zoomOut\": \"Zoom ut\",\n    \"resetZoom\": \"Nullstill zoom\",\n    \"menu\": \"Meny\",\n    \"done\": \"Ferdig\",\n    \"edit\": \"Rediger\",\n    \"undo\": \"Angre\",\n    \"redo\": \"Gjør om\",\n    \"resetLibrary\": \"Nullstill bibliotek\",\n    \"createNewRoom\": \"Opprett et nytt rom\",\n    \"fullScreen\": \"Fullskjerm\",\n    \"darkMode\": \"Mørk modus\",\n    \"lightMode\": \"Lys modus\",\n    \"zenMode\": \"Zen-modus\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Avslutt zen-modus\",\n    \"cancel\": \"Avbryt\",\n    \"clear\": \"Tøm\",\n    \"remove\": \"Fjern\",\n    \"embed\": \"Slå av/på innebygging\",\n    \"publishLibrary\": \"Publiser\",\n    \"submit\": \"Send inn\",\n    \"confirm\": \"Bekreft\",\n    \"embeddableInteractionButton\": \"Klikk for å samhandle\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Dette vil tømme lerretet. Er du sikker?\",\n    \"couldNotCreateShareableLink\": \"Kunne ikke lage delbar lenke.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Kunne ikke opprette lenke til deling: scenen er for stor\",\n    \"couldNotLoadInvalidFile\": \"Kunne ikke laste inn ugyldig fil\",\n    \"importBackendFailed\": \"Importering av backend feilet.\",\n    \"cannotExportEmptyCanvas\": \"Kan ikke eksportere et tomt lerret.\",\n    \"couldNotCopyToClipboard\": \"Kunne ikke kopiere til utklippstavlen.\",\n    \"decryptFailed\": \"Kunne ikke dekryptere data.\",\n    \"uploadedSecurly\": \"Opplastingen er kryptert og kan ikke leses av Excalidraw-serveren eller tredjeparter.\",\n    \"loadSceneOverridePrompt\": \"Å laste inn ekstern tegning vil erstatte det eksisterende innholdet. Ønsker du å fortsette?\",\n    \"collabStopOverridePrompt\": \"Hvis du slutter økten, overskrives din forrige, lokalt lagrede tegning. Er du sikker?\\n\\n(Hvis du ønsker å beholde din lokale tegning, bare lukk nettleserfanen i stedet.)\",\n    \"errorAddingToLibrary\": \"Kunne ikke legge element i biblioteket\",\n    \"errorRemovingFromLibrary\": \"Kunne ikke fjerne element fra biblioteket\",\n    \"confirmAddLibrary\": \"Dette vil legge til {{numShapes}} figur(er) i biblioteket ditt. Er du sikker?\",\n    \"imageDoesNotContainScene\": \"Det ser ikke ut til at dette bildet inneholder noen scenedata. Har du aktivert innebygging av scene under eksporten?\",\n    \"cannotRestoreFromImage\": \"Scenen kunne ikke gjenopprettes fra denne bildefilen\",\n    \"invalidSceneUrl\": \"Kunne ikke importere scene fra den oppgitte URL-en. Den er enten ødelagt, eller inneholder ikke gyldig Excalidraw JSON-data.\",\n    \"resetLibrary\": \"Dette vil tømme biblioteket ditt. Er du sikker?\",\n    \"removeItemsFromsLibrary\": \"Slett {{count}} element(er) fra biblioteket?\",\n    \"invalidEncryptionKey\": \"Krypteringsnøkkel må ha 22 tegn. Live-samarbeid er deaktivert.\",\n    \"collabOfflineWarning\": \"Ingen Internett-tilkobling tilgjengelig.\\nEndringer dine vil ikke bli lagret!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Filtypen støttes ikke.\",\n    \"imageInsertError\": \"Kunne ikke sette inn bildet. Prøv igjen senere...\",\n    \"fileTooBig\": \"Filen er for stor. Maksimal tillatt størrelse er {{maxSize}}.\",\n    \"svgImageInsertError\": \"Kunne ikke sette inn SVG-bilde. SVG-koden ser ugyldig ut.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"Ugyldig SVG.\",\n    \"cannotResolveCollabServer\": \"Kunne ikke koble til samarbeidsserveren. Vennligst oppdater siden og prøv på nytt.\",\n    \"importLibraryError\": \"Kunne ikke laste bibliotek\",\n    \"collabSaveFailed\": \"Kan ikke lagre i backend-databasen. Hvis problemer vedvarer, bør du lagre filen lokalt for å sikre at du ikke mister arbeidet.\",\n    \"collabSaveFailed_sizeExceeded\": \"Kunne ikke lagre til backend-databasen, lerretet ser ut til å være for stort. Du bør lagre filen lokalt for å sikre at du ikke mister arbeidet ditt.\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Ser ut som om du bruker Brave nettleser med <bold>Aggressivt Block Finger</bold> -innstillingen aktivert.\",\n      \"line2\": \"Dette kan resultere i å bryte <bold>tekst-elementene</bold> i tegningene.\",\n      \"line3\": \"Vi anbefaler på det sterkeste å deaktivere denne innstillingen. Du kan følge <link>disse trinnene</link> om hvordan du gjør det.\",\n      \"line4\": \"Hvis deaktivering av denne innstillingen ikke fikser visningen av tekstelementer, vennligst åpne en <issueLink>sak</issueLink> på vår GitHub, eller skriv oss på <discordLink>Discord</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"Innebygde elementer kan ikke legges til i biblioteket.\",\n      \"iframe\": \"\",\n      \"image\": \"Støtte for å legge til bilder i biblioteket kommer snart!\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Velg\",\n    \"image\": \"Sett inn bilde\",\n    \"rectangle\": \"Rektangel\",\n    \"diamond\": \"Diamant\",\n    \"ellipse\": \"Ellipse\",\n    \"arrow\": \"Pil\",\n    \"line\": \"Linje\",\n    \"freedraw\": \"Tegn\",\n    \"text\": \"Tekst\",\n    \"library\": \"Bibliotek\",\n    \"lock\": \"Behold merket verktøy som aktivt\",\n    \"penMode\": \"Pennemodus - forhindre berøring\",\n    \"link\": \"Legg til / oppdater link for en valgt figur\",\n    \"eraser\": \"Viskelær\",\n    \"frame\": \"Rammeverktøy\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"Nettinnbygging\",\n    \"laser\": \"\",\n    \"hand\": \"Hånd (panoreringsverktøy)\",\n    \"extraTools\": \"Flere verktøy\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Handlinger: lerret\",\n    \"selectedShapeActions\": \"Handlinger: valgt objekt\",\n    \"shapes\": \"Former\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"For å flytte lerretet, hold musehjulet eller mellomromstasten mens du drar, eller bruk hånd-verktøyet\",\n    \"linearElement\": \"Klikk for å starte linje med flere punkter, eller dra for en enkel linje\",\n    \"freeDraw\": \"Klikk og dra, slipp når du er ferdig\",\n    \"text\": \"Tips: du kan også legge til tekst ved å dobbeltklikke hvor som helst med utvalgsverktøyet\",\n    \"embeddable\": \"Klikk og dra for å opprette en nettside innebygd\",\n    \"text_selected\": \"Dobbeltklikk eller trykk ENTER for å redigere tekst\",\n    \"text_editing\": \"Trykk Escape eller Ctrl/Cmd+Enter for å fullføre redigering\",\n    \"linearElementMulti\": \"Klikk på siste punkt eller trykk Escape eller Enter for å fullføre\",\n    \"lockAngle\": \"Du kan låse vinkelen ved å holde nede SHIFT\",\n    \"resize\": \"Du kan beholde forholdet ved å trykke SHIFT mens du endrer størrelse,\\ntrykk ALT for å endre størrelsen fra midten\",\n    \"resizeImage\": \"Du kan endre størrelse fritt ved å holde SHIFT,\\nhold ALT for å endre størrelse fra midten\",\n    \"rotate\": \"Du kan låse vinklene ved å holde SHIFT mens du roterer\",\n    \"lineEditor_info\": \"Hold Ctrl/Cmd og dobbelklikk eller trykk Ctrl/Cmd + Enter for å endre punkter\",\n    \"lineEditor_pointSelected\": \"Trykk på Slett for å fjerne punktet, Ctrl / Cmd+D for å duplisere, eller dra for å flytte\",\n    \"lineEditor_nothingSelected\": \"Velg et punkt å redigere (hold SHIFT for å velge flere),\\neller hold Alt og klikk for å legge til nye punkter\",\n    \"placeImage\": \"Klikk for å plassere bildet, eller klikk og dra for å angi størrelsen manuelt\",\n    \"publishLibrary\": \"Publiser ditt eget bibliotek\",\n    \"bindTextToElement\": \"Trykk Enter for å legge til tekst\",\n    \"deepBoxSelect\": \"Hold CTRL/CMD for å markere dypt og forhindre flytting\",\n    \"eraserRevert\": \"Hold Alt for å reversere elementene merket for sletting\",\n    \"firefox_clipboard_write\": \"Denne funksjonen kan sannsynligvis aktiveres ved å sette \\\"dom.events.asyncClipboard.clipboardItem\\\" flagget til \\\"true\\\". For å endre nettleserens flagg i Firefox, besøk \\\"about:config\\\"-siden.\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Kan ikke vise forhåndsvisning\",\n    \"canvasTooBig\": \"Lerretet kan være for stort.\",\n    \"canvasTooBigTip\": \"Tips: Prøv å flytte de ytterste elementene litt tettere sammen.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"En feil oppsto. Prøv <button>å laste siden på nytt.</button>\",\n    \"clearCanvasMessage\": \"Om ny sidelasting ikke fungerer, prøv <button>å tømme lerretet.</button>\",\n    \"clearCanvasCaveat\": \" Dette vil føre til tap av arbeid \",\n    \"trackedToSentry\": \"Feilen med identifikator {{eventId}} ble logget i vårt system.\",\n    \"openIssueMessage\": \"Vi er veldig nøye med å ikke inkludere dine scene-opplysninger i feilen. Hvis din scene ikke er privat, vurder å følge opp i vårt <button>feilrapporteringssystem.</button> Ta med opplysningene nedenfor ved å kopiere og lime inn i GitHub-saken.\",\n    \"sceneContent\": \"Scene-innhold:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Du kan invitere personer til scenen din for å samarbeide med deg.\",\n    \"desc_privacy\": \"Ta det med ro, sesjonen bruker ende-til-ende-kryptering, så alt du tegner forblir privat. Ikke en gang serveren vår kan se hva du lager.\",\n    \"button_startSession\": \"Start økt\",\n    \"button_stopSession\": \"Stopp sesjon\",\n    \"desc_inProgressIntro\": \"Sanntids-samarbeidsøkt er nå i gang.\",\n    \"desc_shareLink\": \"Del denne linken med de du vil samarbeide med:\",\n    \"desc_exitSession\": \"Dersom du avslutter sesjonen blir du frakoblet rommet, men du kan fortsette å arbeide med scenen lokalt. Vær oppmerksom på at dette ikke vil påvirke andre personer, og de vil fortsatt ha mulighet til å samarbeide på deres versjon.\",\n    \"shareTitle\": \"Bli med i en live samarbeidsøkt på Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Feil\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Lagre til disk\",\n    \"disk_details\": \"Eksporter scene-dataene til en fil som du kan importere fra senere.\",\n    \"disk_button\": \"Lagre til fil\",\n    \"link_title\": \"Delbar lenke\",\n    \"link_details\": \"Eksporter som en skrivebeskyttet lenke.\",\n    \"link_button\": \"Eksporter til lenke\",\n    \"excalidrawplus_description\": \"Lagre scenen til ditt Excalidraw+ arbeidsområde.\",\n    \"excalidrawplus_button\": \"Eksporter\",\n    \"excalidrawplus_exportError\": \"Kunne ikke eksportere til Excalidraw+ for øyeblikket...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Les bloggen vår\",\n    \"click\": \"klikk\",\n    \"deepSelect\": \"Marker dypt\",\n    \"deepBoxSelect\": \"Marker dypt innad i boks og forhindre flytting\",\n    \"curvedArrow\": \"Buet pil\",\n    \"curvedLine\": \"Buet linje\",\n    \"documentation\": \"Dokumentasjon\",\n    \"doubleClick\": \"dobbeltklikk\",\n    \"drag\": \"dra\",\n    \"editor\": \"Redigeringsvisning\",\n    \"editLineArrowPoints\": \"Rediger linje/pilpunkter\",\n    \"editText\": \"Rediger tekst / legg til etikett\",\n    \"github\": \"Funnet et problem? Send inn\",\n    \"howto\": \"Følg våre veiledninger\",\n    \"or\": \"eller\",\n    \"preventBinding\": \"Forhindre pilbinding\",\n    \"tools\": \"Verktøy\",\n    \"shortcuts\": \"Tastatursnarveier\",\n    \"textFinish\": \"Fullfør redigering (teksteditor)\",\n    \"textNewLine\": \"Legg til ny linje (teksteditor)\",\n    \"title\": \"Hjelp\",\n    \"view\": \"Vis\",\n    \"zoomToFit\": \"Zoom for å se alle elementer\",\n    \"zoomToSelection\": \"Zoom til utvalg\",\n    \"toggleElementLock\": \"Lås/lås opp utvalg\",\n    \"movePageUpDown\": \"Flytt side opp/ned\",\n    \"movePageLeftRight\": \"Flytt siden til venstre/høyre\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Tøm lerret\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Publiser bibliotek\",\n    \"itemName\": \"Elementnavn\",\n    \"authorName\": \"Forfatterens navn\",\n    \"githubUsername\": \"GitHub-brukernavnet\",\n    \"twitterUsername\": \"Twitter-brukernavn\",\n    \"libraryName\": \"Biblioteknavn\",\n    \"libraryDesc\": \"Beskrivelse av bibliotek\",\n    \"website\": \"Nettsted\",\n    \"placeholder\": {\n      \"authorName\": \"Ditt navn eller brukernavn\",\n      \"libraryName\": \"Navnet på biblioteket ditt\",\n      \"libraryDesc\": \"Beskrivelse av biblioteket ditt for å hjelpe folk med å forstå bruken\",\n      \"githubHandle\": \"Github-brukernavn (valgfritt), slik at du kan redigere biblioteket når du har sendt inn for gjennomgang\",\n      \"twitterHandle\": \"Twitter-brukernavn (valgfritt), slik at vi vet hvem vi skal kreditere når promotert på Twitter\",\n      \"website\": \"Lenke til din personlige nettside eller et annet sted (valgfritt)\"\n    },\n    \"errors\": {\n      \"required\": \"Påkrevd\",\n      \"website\": \"Angi en gyldig nettadresse\"\n    },\n    \"noteDescription\": \"Send inn biblioteket ditt som skal inkluderes i <link>kildekode for offentlig bibliotek</link>for andre å bruke dem i tegninger.\",\n    \"noteGuidelines\": \"Biblioteket må godkjennes manuelt først. Les <link>retningslinjene</link> før innsending. Du vil trenge en GitHub-konto for å kommunisere og gjøre endringer hvis ønsket, men det er ikke påkrevd.\",\n    \"noteLicense\": \"Ved å sende inn godtar du at biblioteket blir publisert under <link>MIT-lisens, </link>som kortfattet betyr at andre kan bruke dem uten begrensninger.\",\n    \"noteItems\": \"Hvert bibliotek må ha sitt eget navn, så det er filtrerbart. Følgende bibliotekselementer vil bli inkludert:\",\n    \"atleastOneLibItem\": \"Vennligst velg minst ett bibliotek for å komme i gang\",\n    \"republishWarning\": \"Merk: noen av de valgte elementene er merket som allerede publisert/sendt. Du bør kun sende inn elementer på nytt når du oppdaterer et eksisterende bibliotek eller innlevering.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Bibliotek innsendt\",\n    \"content\": \"Takk {{authorName}}. Ditt bibliotek har blitt sendt inn for gjennomgang. Du kan spore statusen<link>her</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Nullstill bibliotek\",\n    \"removeItemsFromLib\": \"Fjern valgte elementer fra bibliotek\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Eksporter bilde\",\n    \"label\": {\n      \"withBackground\": \"Bakgrunn\",\n      \"onlySelected\": \"Kun valgte\",\n      \"darkMode\": \"Mørk modus\",\n      \"embedScene\": \"Bygg inn scene\",\n      \"scale\": \"Skalering\",\n      \"padding\": \"Avstand\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"Scenedata vil bli lagret i den eksporterte PNG/SVG-filen, slik at scenen kan gjenopprettes fra den.\\nDet vil øke den eksporterte filstørrelsen.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Eksporter til PNG\",\n      \"exportToSvg\": \"Eksporter til SVG\",\n      \"copyPngToClipboard\": \"Kopier PNG til utklippstavlen\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Kopier til utklippstavle\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Dine tegninger er ende-til-ende-krypterte slik at Excalidraw sine servere aldri vil se dem.\",\n    \"link\": \"Blogginnlegg om ende-til-ende-kryptering i Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Vinkel\",\n    \"element\": \"Element\",\n    \"elements\": \"Elementer\",\n    \"height\": \"Høyde\",\n    \"scene\": \"Scene\",\n    \"selected\": \"Valgt\",\n    \"storage\": \"Lagring\",\n    \"title\": \"Statistikk for nerder\",\n    \"total\": \"Totalt\",\n    \"version\": \"Versjon\",\n    \"versionCopy\": \"Klikk for å kopiere\",\n    \"versionNotAvailable\": \"Versjon ikke tilgjengelig\",\n    \"width\": \"Bredde\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Lagt til i biblioteket\",\n    \"copyStyles\": \"Kopierte stiler.\",\n    \"copyToClipboard\": \"Kopiert til utklippstavlen.\",\n    \"copyToClipboardAsPng\": \"Kopierte {{exportSelection}} til utklippstavlen som PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Fil lagret.\",\n    \"fileSavedToFilename\": \"Lagret til {filename}\",\n    \"canvas\": \"lerret\",\n    \"selection\": \"utvalg\",\n    \"pasteAsSingleElement\": \"Bruk {{shortcut}} for å lime inn som ett enkelt element,\\neller lim inn i en eksisterende tekstbehandler\",\n    \"unableToEmbed\": \"Innbygging av denne nettadressen er ikke tillatt. Oppret en sak på GitHub for å be om url-hvitelisting\",\n    \"unrecognizedLinkFormat\": \"Linken du bygget inn samsvarer ikke med det forventede formatet. Prøv å lime inn \\\"bygg inn\\\"-strengen fra kildesiden\"\n  },\n  \"colors\": {\n    \"transparent\": \"Gjennomsiktig\",\n    \"black\": \"Svart\",\n    \"white\": \"Hvit\",\n    \"red\": \"Rød\",\n    \"pink\": \"Rosa\",\n    \"grape\": \"Drue\",\n    \"violet\": \"Fiolett\",\n    \"gray\": \"Grå\",\n    \"blue\": \"Blå\",\n    \"cyan\": \"Turkis\",\n    \"teal\": \"Blågrønn\",\n    \"green\": \"Grønn\",\n    \"yellow\": \"Gul\",\n    \"orange\": \"Oransje\",\n    \"bronze\": \"Bronse\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Alle dine data lagres lokalt i din nettleser.\",\n      \"center_heading_plus\": \"Ønsker du å gå til Excalidraw+ i stedet?\",\n      \"menuHint\": \"Eksporter, innstillinger, språk, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Eksporter, innstillinger og mer...\",\n      \"center_heading\": \"Diagrammer. Gjort. Enkelt.\",\n      \"toolbarHint\": \"Velg et verktøy og start å tegne!\",\n      \"helpHint\": \"Snarveier & hjelp\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Mest brukte egendefinerte farger\",\n    \"colors\": \"Farger\",\n    \"shades\": \"Toner\",\n    \"hexCode\": \"Heksadesimal kode\",\n    \"noShades\": \"Ingen toner tilgjengelig for denne fargen\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Eksporter som bilde\",\n        \"button\": \"Eksporter som bilde\",\n        \"description\": \"Eksporter scene-dataene til en fil som du kan importere fra senere.\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Lagre til disk\",\n        \"button\": \"Lagre til disk\",\n        \"description\": \"Eksporter scene-dataene til en fil som du kan importere fra senere.\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Eksporter til Excalidraw+\",\n        \"description\": \"Lagre scenen til ditt Excalidraw+-arbeidsområde.\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Last inn fra fil\",\n        \"button\": \"Last inn fra fil\",\n        \"description\": \"Å laste fra en fil vil <bold>erstatte ditt eksisterende innhold</bold>.<br></br>Du kan sikkerhetskopiere tegningen din først ved å bruke en av valgene under.\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Last inn fra lenke\",\n        \"button\": \"Erstatt innholdet mitt\",\n        \"description\": \"Lasting av ekstern tegning vil <bold>erstatte ditt eksisterende innhold</bold>.<br></br>Du kan sikkerhetskopiere tegningen din først ved å bruke en av valgene nedenfor.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}