import "../chunk-XDFCUUT6.js";

// locales/ro-RO.json
var labels = {
  paste: "Lipire",
  pasteAsPlaintext: "Inserare ca text simplu",
  pasteCharts: "Lipire diagrame",
  selectAll: "Selectare total\u0103",
  multiSelect: "Adaug\u0103 element la selec\u021Bie",
  moveCanvas: "Mutare p\xE2nz\u0103",
  cut: "Decupare",
  copy: "Copiere",
  copyAsPng: "Copiere \xEEn memoria temporar\u0103 ca PNG",
  copyAsSvg: "Copiere \xEEn memoria temporar\u0103 ca SVG",
  copyText: "Copiere \xEEn memoria temporar\u0103 ca text",
  copySource: "Copiere surs\u0103 \xEEn memoria temporar\u0103",
  convertToCode: "Convertire \xEEn cod",
  bringForward: "Aducere \xEEn plan apropiat",
  sendToBack: "Trimitere \xEEn ultimul plan",
  bringToFront: "Aducere \xEEn prim plan",
  sendBackward: "Trimitere \xEEn plan secundar",
  delete: "\u0218tergere",
  copyStyles: "Copiere stiluri",
  pasteStyles: "Lipire stiluri",
  stroke: "Contur",
  background: "Fundal",
  fill: "Umplere",
  strokeWidth: "L\u0103\u021Bimea conturului",
  strokeStyle: "Stilul conturului",
  strokeStyle_solid: "Ne\xEEntrerupt",
  strokeStyle_dashed: "Liniu\u021Be",
  strokeStyle_dotted: "Punctat",
  sloppiness: "Aspectul tras\u0103rii",
  opacity: "Opacitate",
  textAlign: "Alinierea textului",
  edges: "Margini",
  sharp: "Ascu\u021Bite",
  round: "Rotunde",
  arrowheads: "V\xE2rfuri de s\u0103geat\u0103",
  arrowhead_none: "Niciunul",
  arrowhead_arrow: "S\u0103geat\u0103",
  arrowhead_bar: "Bar\u0103",
  arrowhead_circle: "Cerc",
  arrowhead_circle_outline: "Cerc (contur)",
  arrowhead_triangle: "Triunghi",
  arrowhead_triangle_outline: "Triunghi (contur)",
  arrowhead_diamond: "Romb",
  arrowhead_diamond_outline: "Romb (contur)",
  fontSize: "Dimensiune font",
  fontFamily: "Familia de fonturi",
  addWatermark: "Adaug\u0103 \u201ERealizat cu Excalidraw\u201D",
  handDrawn: "Scris de m\xE2n\u0103",
  normal: "Normal",
  code: "Cod",
  small: "Mic\u0103",
  medium: "Medie",
  large: "Mare",
  veryLarge: "Foarte mare",
  solid: "Plin\u0103",
  hachure: "Ha\u0219ur\u0103",
  zigzag: "Zigzag",
  crossHatch: "Ha\u0219ur\u0103 transversal\u0103",
  thin: "Sub\u021Bire",
  bold: "\xCEngro\u0219at\u0103",
  left: "St\xE2nga",
  center: "Centru",
  right: "Dreapta",
  extraBold: "Extra \xEEngro\u0219at\u0103",
  architect: "Arhitect",
  artist: "Artist",
  cartoonist: "Caricaturist",
  fileTitle: "Nume de fi\u0219ier",
  colorPicker: "Selector de culoare",
  canvasColors: "Folosite pe p\xE2nz\u0103",
  canvasBackground: "Fundalul p\xE2nzei",
  drawingCanvas: "P\xE2nz\u0103 pentru desenat",
  layers: "Straturi",
  actions: "Ac\u021Biuni",
  language: "Limb\u0103",
  liveCollaboration: "Colaborare \xEEn direct...",
  duplicateSelection: "Duplicare",
  untitled: "Nedenumit",
  name: "Nume",
  yourName: "Numele t\u0103u",
  madeWithExcalidraw: "Realizat cu Excalidraw",
  group: "Grupare selec\u021Bie",
  ungroup: "Degrupare selec\u021Bie",
  collaborators: "Colaboratori",
  showGrid: "Afi\u0219are gril\u0103",
  addToLibrary: "Ad\u0103ugare la bibliotec\u0103",
  removeFromLibrary: "Eliminare din bibliotec\u0103",
  libraryLoadingMessage: "Se \xEEncarc\u0103 biblioteca\u2026",
  libraries: "R\u0103sfoie\u0219te bibliotecile",
  loadingScene: "Se \xEEncarc\u0103 scena\u2026",
  align: "Aliniere",
  alignTop: "Aliniere sus",
  alignBottom: "Aliniere jos",
  alignLeft: "Aliniere la st\xE2nga",
  alignRight: "Aliniere la dreapta",
  centerVertically: "Centrare vertical\u0103",
  centerHorizontally: "Centrare orizontal\u0103",
  distributeHorizontally: "Distribuie orizontal",
  distributeVertically: "Distribuie vertical",
  flipHorizontal: "R\u0103sturnare orizontal\u0103",
  flipVertical: "R\u0103sturnare vertical\u0103",
  viewMode: "Mod de vizualizare",
  share: "Distribuie",
  showStroke: "Afi\u0219are selector culoare contur",
  showBackground: "Afi\u0219are selector culoare fundal",
  toggleTheme: "Comutare tem\u0103",
  personalLib: "Biblioteca personal\u0103",
  excalidrawLib: "Biblioteca Excalidraw",
  decreaseFontSize: "Mic\u0219oreaz\u0103 dimensiunea fontului",
  increaseFontSize: "M\u0103re\u0219te dimensiunea fontului",
  unbindText: "Deconectare text",
  bindText: "Legare text de container",
  createContainerFromText: "\xCEncadrare text \xEEntr-un container",
  link: {
    edit: "Editare URL",
    editEmbed: "Editare URL \u0219i \xEEncorporare",
    create: "Creare URL",
    createEmbed: "Creare URL \u0219i \xEEncorporare",
    label: "URL",
    labelEmbed: "URL \u0219i \xEEncorporare",
    empty: "Nu este setat niciun URL"
  },
  lineEditor: {
    edit: "Editare linie",
    exit: "P\u0103r\u0103sire editor de linii"
  },
  elementLock: {
    lock: "Blocare",
    unlock: "Deblocare",
    lockAll: "Blocare toate",
    unlockAll: "Deblocare toate"
  },
  statusPublished: "Publicat",
  sidebarLock: "P\u0103streaz\u0103 deschis\u0103 bara lateral\u0103",
  selectAllElementsInFrame: "",
  removeAllElementsFromFrame: "",
  eyeDropper: "Alegere culoare din p\xE2nz\u0103",
  textToDiagram: "Text la diagram\u0103",
  prompt: "Solicitare"
};
var library = {
  noItems: "Niciun element ad\u0103ugat \xEEnc\u0103...",
  hint_emptyLibrary: "Selecteaz\u0103 un element de pe p\xE2nz\u0103 pentru a-l ad\u0103uga aici sau instaleaz\u0103 o bibliotec\u0103 din depozitul public, de mai jos.",
  hint_emptyPrivateLibrary: "Selecteaz\u0103 un element de pe p\xE2nz\u0103 pentru a-l ad\u0103uga aici."
};
var buttons = {
  clearReset: "Resetare p\xE2nz\u0103",
  exportJSON: "Exportare la fi\u0219iere",
  exportImage: "Exportare imagine...",
  export: "Salvare \xEEn...",
  copyToClipboard: "Copiere \xEEn memoria temporar\u0103",
  save: "Salvare \xEEn fi\u0219ierul curent",
  saveAs: "Salvare ca",
  load: "Deschidere",
  getShareableLink: "Ob\u021Bine URL partajabil",
  close: "\xCEnchidere",
  selectLanguage: "Selectare limb\u0103",
  scrollBackToContent: "Derulare \xEEnapoi la con\u021Binut",
  zoomIn: "Apropiere",
  zoomOut: "Dep\u0103rtare",
  resetZoom: "Resetare transfocare",
  menu: "Meniu",
  done: "Efectuat",
  edit: "Edit",
  undo: "Anulare",
  redo: "Refacere",
  resetLibrary: "Resetare bibliotec\u0103",
  createNewRoom: "Creare camer\u0103 nou\u0103",
  fullScreen: "Ecran complet",
  darkMode: "Mod \xEEntunecat",
  lightMode: "Mod luminos",
  zenMode: "Mod zen",
  objectsSnapMode: "Ancorare la obiecte",
  exitZenMode: "Ie\u0219ire din modul zen",
  cancel: "Anulare",
  clear: "\u0218tergere",
  remove: "Eliminare",
  embed: "Comutare \xEEncorporare",
  publishLibrary: "Publicare",
  submit: "Trimitere",
  confirm: "Confirmare",
  embeddableInteractionButton: "Clic pentru interac\u021Bionare"
};
var alerts = {
  clearReset: "Aceast\u0103 op\u021Biune va \u0219terge \xEEntreaga p\xE2nz\u0103. Confirmi?",
  couldNotCreateShareableLink: "Nu s-a putut crea un URL partajabil.",
  couldNotCreateShareableLinkTooBig: "Nu s-a putut crea un URL partajabil: scena este prea mare",
  couldNotLoadInvalidFile: "Fi\u0219ierul invalid nu a putut fi \xEEnc\u0103rcat",
  importBackendFailed: "Importarea de la nivel de server a e\u0219uat.",
  cannotExportEmptyCanvas: "Nu se poate exporta p\xE2nza goal\u0103.",
  couldNotCopyToClipboard: "Nu s-a putut copia \xEEn memoria temporar\u0103.",
  decryptFailed: "Datele nu au putut fi decriptate.",
  uploadedSecurly: "\xCEnc\u0103rcarea a fost securizat\u0103 prin criptare integral\u0103, \xEEnsemn\xE2nd c\u0103 serverul Excalidraw \u0219i ter\u021Bii nu pot citi con\u021Binutul.",
  loadSceneOverridePrompt: "\xCEnc\u0103rcarea desenului extern va \xEEnlocui con\u021Binutul existent. Dore\u0219ti s\u0103 continui?",
  collabStopOverridePrompt: "Oprirea sesiunii va suprascrie desenul anterior stocat local. Confirmi alegerea?\n\n(Dac\u0103 vrei s\u0103 p\u0103strezi desenul local, pur \u0219i simplu \xEEnchide fila navigatorului \xEEn schimb.)",
  errorAddingToLibrary: "Elementul nu a putut fi ad\u0103ugat \xEEn bibliotec\u0103",
  errorRemovingFromLibrary: "Elementul nu a putut fi eliminat din bibliotec\u0103",
  confirmAddLibrary: "Aceast\u0103 ac\u021Biune va ad\u0103uga {{numShapes}} form\u0103(e) la biblioteca ta. Confirmi?",
  imageDoesNotContainScene: "Aceast\u0103 imagine nu pare s\u0103 con\u021Bin\u0103 date de scen\u0103. Ai activat \xEEncorporarea scenei \xEEn timpul exportului?",
  cannotRestoreFromImage: "Scena nu a putut fi restaurat\u0103 din acest fi\u0219ier de imagine",
  invalidSceneUrl: "Scena nu a putut fi importat\u0103 din URL-ul furnizat. Este fie incorect format\u0103, fie nu con\u021Bine date JSON Excalidraw valide.",
  resetLibrary: "Aceast\u0103 op\u021Biune va elimina con\u021Binutul din bibliotec\u0103. Confirmi?",
  removeItemsFromsLibrary: "\u0218tergi {{count}} element(e) din bibliotec\u0103?",
  invalidEncryptionKey: "Cheia de criptare trebuie s\u0103 aib\u0103 22 de caractere. Colaborarea \xEEn direct este dezactivat\u0103.",
  collabOfflineWarning: "Nu este disponibil\u0103 nicio conexiune la internet.\nModific\u0103rile nu vor fi salvate!"
};
var errors = {
  unsupportedFileType: "Tip de fi\u0219ier neacceptat.",
  imageInsertError: "Imaginea nu a putut fi introdus\u0103. Re\xEEncearc\u0103 mai t\xE2rziu...",
  fileTooBig: "Fi\u0219ierul este prea mare. Dimensiunea maxim\u0103 permis\u0103 este de {{maxSize}}.",
  svgImageInsertError: "Imaginea SVG nu a putut fi introdus. Marcajul SVG pare invalid.",
  failedToFetchImage: "Preluarea imaginii a e\u0219uat.",
  invalidSVGString: "SVG invalid.",
  cannotResolveCollabServer: "Nu a putut fi realizat\u0103 conexiunea la serverul de colaborare. Re\xEEncarc\u0103 pagina \u0219i \xEEncearc\u0103 din nou.",
  importLibraryError: "Biblioteca nu a putut fi \xEEnc\u0103rcat\u0103",
  collabSaveFailed: "Nu s-a putut salva \xEEn baza de date la nivel de server. Dac\u0103 problemele persist\u0103, ar trebui s\u0103 salvezi fi\u0219ierul la nivel local pentru a te asigura c\u0103 nu \xEE\u021Bi pierzi munca.",
  collabSaveFailed_sizeExceeded: "Nu s-a putut salva \xEEn baza de date la nivel de server, \xEEntruc\xE2t se pare c\u0103 p\xE2nza este prea mare. Ar trebui s\u0103 salvezi fi\u0219ierul la nivel local pentru a te asigura c\u0103 nu \xEE\u021Bi pierzi munca.",
  imageToolNotSupported: "Imaginile sunt dezactivate.",
  brave_measure_text_error: {
    line1: "Se pare c\u0103 folose\u0219ti navigatorul Brave cu op\u021Biunea <bold>strict\u0103 pentru blocarea amprent\u0103rii</bold>.",
    line2: "Acest lucru poate duce la \xEEntreruperea <bold>elementelor text</bold> din desene.",
    line3: "\xCE\u021Bi recomand\u0103m ferm s\u0103 dezactivezi aceast\u0103 setare. Po\u021Bi urma <link>ace\u0219ti pa\u0219i</link> pentru a face acest lucru.",
    line4: "Dac\u0103 dezactivarea acestei set\u0103ri nu duce la remedierea afi\u0219\u0103rii elementelor text, deschide un tichet de <issueLink>problem\u0103</issueLink> pe pagina noastr\u0103 de GitHub sau scrie-ne pe <discordLink>Discord</discordLink>"
  },
  libraryElementTypeError: {
    embeddable: "Elementele \xEEncorporabile nu pot fi ad\u0103ugate la bibliotec\u0103.",
    iframe: "Elementele iFrame nu pot fi ad\u0103ugate la bibliotec\u0103.",
    image: "\xCEn cur\xE2nd vor putea fi ad\u0103ugate imagini \xEEn bibliotec\u0103!"
  },
  asyncPasteFailedOnRead: "Lipirea nu a putut fi efectuat\u0103 (nu s-a putut citit din memoria temporar\u0103 a sistemului).",
  asyncPasteFailedOnParse: "Lipirea nu a putut fi efectuat\u0103.",
  copyToSystemClipboardFailed: "Nu s-a putut copia \xEEn memoria temporar\u0103."
};
var toolBar = {
  selection: "Selec\u021Bie",
  image: "Introducere imagine",
  rectangle: "Dreptunghi",
  diamond: "Romb",
  ellipse: "Elips\u0103",
  arrow: "S\u0103geat\u0103",
  line: "Linie",
  freedraw: "Desenare",
  text: "Text",
  library: "Bibliotec\u0103",
  lock: "Men\u021Bine activ instrumentul selectat dup\u0103 desenare",
  penMode: "Mod stilou \u2013 \xEEmpiedic\u0103 atingerea",
  link: "Ad\u0103ugare/actualizare URL pentru forma selectat\u0103",
  eraser: "Radier\u0103",
  frame: "",
  magicframe: "Structur\u0103-de-fire la cod",
  embeddable: "\xCEncorporare web",
  laser: "Indicator laser",
  hand: "M\xE2n\u0103 (instrument de panoramare)",
  extraTools: "",
  mermaidToExcalidraw: "Mermaid la Excalidraw",
  magicSettings: "Set\u0103ri IA"
};
var headings = {
  canvasActions: "Ac\u021Biuni pentru p\xE2nz\u0103",
  selectedShapeActions: "Ac\u021Biuni pentru forma selectat\u0103",
  shapes: "Forme"
};
var hints = {
  canvasPanning: "Pentru a muta p\xE2nz\u0103, \u021Bine ap\u0103sat\u0103 roti\u021Ba mausului sau bara de spa\u021Biu sau folose\u0219te instrumentul \xEEn form\u0103 de m\xE2n\u0103",
  linearElement: "D\u0103 clic pentru a crea mai multe puncte, gliseaz\u0103 pentru a forma o singur\u0103 linie",
  freeDraw: "D\u0103 clic pe p\xE2nz\u0103 \u0219i gliseaz\u0103 cursorul, apoi elibereaz\u0103-l c\xE2nd ai terminat",
  text: "Sfat: po\u021Bi ad\u0103uga text \u0219i d\xE2nd dublu clic oriunde cu instrumentul de selec\u021Bie",
  embeddable: "D\u0103 clic \u0219i trage pentru a crea un cod de \xEEncorporare de pagin\u0103 web",
  text_selected: "D\u0103 dublu clic sau apas\u0103 tasta Enter pentru a edita textul",
  text_editing: "Apas\u0103 tasta Escape sau Ctrl sau Cmd + Enter pentru a finaliza editarea",
  linearElementMulti: "D\u0103 clic pe ultimul punct sau apas\u0103 tasta Escape sau tasta Enter pentru a termina",
  lockAngle: "Po\u021Bi constr\xE2nge unghiul prin \u021Binerea ap\u0103sat\u0103 a tastei SHIFT",
  resize: "Po\u021Bi constr\xE2nge propor\u021Biile, \u021Bin\xE2nd ap\u0103sat\u0103 tasta SHIFT \xEEn timp ce redimensionezi,\n\u021Bine ap\u0103sat\u0103 tasta ALT pentru a redimensiona de la centru",
  resizeImage: "Po\u021Bi redimensiona liber \u021Bin\xE2nd ap\u0103sat\u0103 tasta SHIFT,\n\u021Bine ap\u0103sat\u0103 tasta ALT pentru a redimensiona din centru",
  rotate: "Po\u021Bi constr\xE2nge unghiurile, \u021Bin\xE2nd ap\u0103sat\u0103 tasta SHIFT \xEEn timp ce rote\u0219ti",
  lineEditor_info: "\u021Aine ap\u0103sat\u0103 tasta Ctrl sau Cmd \u0219i d\u0103 dublu clic sau apas\u0103 tasta Ctrl sau Cmd + Enter pentru a edita puncte",
  lineEditor_pointSelected: "Apas\u0103 tasta Delete pentru a elimina punctele,\ncombina\u021Bia de taste Ctrl sau Cmd + D pentru a le duplica sau gliseaz\u0103-le pentru a le schimba pozi\u021Bia",
  lineEditor_nothingSelected: "Selecteaz\u0103 un punct pentru a-l edita (\u021Bine ap\u0103sat\u0103 tasta SHIFT pentru a selecta mai multe),\nsau \u021Bine ap\u0103sat\u0103 tasta Alt \u0219i d\u0103 clic pentru a ad\u0103uga puncte noi",
  placeImage: "D\u0103 clic pentru a pozi\u021Biona imaginea sau d\u0103 clic \u0219i gliseaz\u0103 pentru a seta manual dimensiunea imaginii",
  publishLibrary: "Public\u0103 propria bibliotec\u0103",
  bindTextToElement: "Apas\u0103 tasta Enter pentru a ad\u0103uga text",
  deepBoxSelect: "\u021Aine ap\u0103sat\u0103 tasta Ctrl sau Cmd pentru a efectua selectarea de ad\xE2ncime \u0219i pentru a preveni glisarea",
  eraserRevert: "\u021Aine ap\u0103sat\u0103 tasta Alt pentru a anula elementele marcate pentru \u0219tergere",
  firefox_clipboard_write: "Aceast\u0103 caracteristic\u0103 poate fi probabil activat\u0103 prin setarea preferin\u021Bei \u201Edom.events.asyncClipboard.clipboardItem\u201D ca \u201Etrue\u201D. Pentru a schimba preferin\u021Bele navigatorului \xEEn Firefox, acceseaz\u0103 pagina \u201Eabout:config\u201D.",
  disableSnapping: "\u021Aine ap\u0103sat CtrlOrCmd pentru a dezactiva ancorarea"
};
var canvasError = {
  cannotShowPreview: "Nu se poate afi\u0219a previzualizarea",
  canvasTooBig: "P\xE2nza poate fi prea mare.",
  canvasTooBigTip: "Sfat: \xEEncearc\u0103 s\u0103 apropii pu\u021Bin mai mult elementele cele mai \xEEndep\u0103rtate."
};
var errorSplash = {
  headingMain: "A ap\u0103rut o eroare. \xCEncearc\u0103 <button>s\u0103 re\xEEncarci pagina</button>.",
  clearCanvasMessage: "Dac\u0103 re\xEEnc\u0103rcarea nu func\u021Bioneaz\u0103, \xEEncearc\u0103 <button>s\u0103 \u0219tergi p\xE2nza</button>.",
  clearCanvasCaveat: " Acest lucru va duce la pierderea progresului ",
  trackedToSentry: "Eroarea cu identificatorul {{eventId}} a fost urm\u0103rit\u0103 \xEEn sistemul nostru.",
  openIssueMessage: "Am luat m\u0103suri de precau\u021Bie pentru a nu include informa\u021Bii despre scen\u0103 \xEEn eroare. Dac\u0103 scena nu este privat\u0103, ofer\u0103-ne mai multe informa\u021Bii \xEEn <button>monitorul nostru pentru erori</button>. Include informa\u021Biile de mai jos copiindu-le \u0219i lipindu-le \xEEn tichetul cu problem\u0103 de pe GitHub.",
  sceneContent: "Con\u021Binutul scenei:"
};
var roomDialog = {
  desc_intro: "Po\u021Bi invita alte persoane pentru a colabora la scena actual\u0103.",
  desc_privacy: "Nu te \xEEngrijora. Sesiunea utilizeaz\u0103 criptarea integral\u0103, astfel \xEEnc\xE2t orice desenezi va r\u0103m\xE2ne privat. Nici m\u0103car serverul nostru nu va putea vedea pe ce ai lucrat.",
  button_startSession: "Pornire sesiune",
  button_stopSession: "Oprire sesiune",
  desc_inProgressIntro: "Sesiunea de colaborare \xEEn direct este \xEEn curs de desf\u0103\u0219urare.",
  desc_shareLink: "Distribuie acest URL persoanelor cu care dore\u0219ti s\u0103 colaborezi:",
  desc_exitSession: "Oprirea sesiunii te va deconecta de la sal\u0103, \xEEns\u0103 vei putea lucra \xEEn continuare, pe plan local, cu scena. Re\u021Bine c\u0103 aceast\u0103 op\u021Biune nu va afecta alte persoane, iar acestea vor putea s\u0103 colaboreze \xEEn continuare pe versiunea lor.",
  shareTitle: "Al\u0103tur\u0103-te unei sesiuni de colaborare \xEEn direct pe Excalidraw"
};
var errorDialog = {
  title: "Eroare"
};
var exportDialog = {
  disk_title: "Salvare pe disc",
  disk_details: "Export\u0103 datele scenei pe un fi\u0219ier din care po\u021Bi importa mai t\xE2rziu.",
  disk_button: "Salvare \xEEn fi\u0219ier",
  link_title: "URL partajabil",
  link_details: "Export\u0103 ca URL doar \xEEn citire.",
  link_button: "Exportare \xEEn URL",
  excalidrawplus_description: "Salveaz\u0103 scena \xEEn spa\u021Biul de lucru Excalidraw+.",
  excalidrawplus_button: "Exportare",
  excalidrawplus_exportError: "Excalidraw+ nu a putut fi exportat \xEEn acest moment..."
};
var helpDialog = {
  blog: "Cite\u0219te blogul nostru",
  click: "clic",
  deepSelect: "Selectare de ad\xE2ncime",
  deepBoxSelect: "Selectare de ad\xE2ncime \xEEn caset\u0103 \u0219i prevenire glisare",
  curvedArrow: "S\u0103geat\u0103 curbat\u0103",
  curvedLine: "Linie curbat\u0103",
  documentation: "Documenta\u021Bie",
  doubleClick: "dublu clic",
  drag: "glisare",
  editor: "Editor",
  editLineArrowPoints: "Editare puncte de s\u0103geat\u0103/r\xE2nd",
  editText: "Editare text/ad\u0103ugare etichet\u0103",
  github: "Ai \xEEnt\xE2mpinat o problem\u0103? Trimite un raport",
  howto: "Urm\u0103re\u0219te ghidurile noastre",
  or: "sau",
  preventBinding: "\xCEmpiedic\u0103 legarea s\u0103ge\u021Bii",
  tools: "Instrumente",
  shortcuts: "Comenzi rapide de la tastatur\u0103",
  textFinish: "Finalizeaz\u0103 editarea (editor de text)",
  textNewLine: "Adaug\u0103 o linie nou\u0103 (editor de text)",
  title: "Ajutor",
  view: "Vizualizare",
  zoomToFit: "Transfocare pentru a cuprinde totul",
  zoomToSelection: "Transfocare la selec\u021Bie",
  toggleElementLock: "Blocare/deblocare selec\u021Bie",
  movePageUpDown: "Deplasare pagin\u0103 sus/jos",
  movePageLeftRight: "Deplasare pagin\u0103 st\xE2nga/dreapta"
};
var clearCanvasDialog = {
  title: "\u0218tergere p\xE2nz\u0103"
};
var publishDialog = {
  title: "Publicare bibliotec\u0103",
  itemName: "Denumirea elementului",
  authorName: "Numele autorului",
  githubUsername: "Numele de utilizator GitHub",
  twitterUsername: "Numele de utilizator Twitter",
  libraryName: "Denumirea bibliotecii",
  libraryDesc: "Descrierea bibliotecii",
  website: "Pagin\u0103 de internet",
  placeholder: {
    authorName: "Numele sau numele t\u0103u de utilizator",
    libraryName: "Numele bibliotecii tale",
    libraryDesc: "Descrierea bibliotecii tale pentru a ajuta oamenii s\u0103 \xEEn\u021Beleag\u0103 utilizarea acesteia",
    githubHandle: "Numele de utilizator GitHub (op\u021Bional), pentru a putea edita biblioteca odat\u0103 ce este trimis\u0103 spre revizuire",
    twitterHandle: "Numele de utilizator Twitter (op\u021Bional), pentru a indica sursa la promovarea pe Twitter",
    website: "Trimitere c\u0103tre pagina ta personal\u0103 de internet sau altundeva (op\u021Bional)"
  },
  errors: {
    required: "Obligatoriu",
    website: "Introdu un URL valid"
  },
  noteDescription: "Trimite-\u021Bi biblioteca pentru a fi inclus\u0103 \xEEn <link>depozitul de biblioteci publice</link> \xEEn vederea utiliz\u0103rii de c\u0103tre alte persoane \xEEn desenele lor.",
  noteGuidelines: "Biblioteca trebuie aprobat\u0103 manual mai \xEEnt\xE2i. Cite\u0219te <link>orient\u0103rile</link> \xEEnainte de trimitere. Vei avea nevoie de un cont GitHub pentru a comunica \u0219i efectua modific\u0103ri, dac\u0103 este cazul, \xEEns\u0103 nu este strict necesar.",
  noteLicense: "Prin trimiterea bibliotecii, e\u0219ti de acord c\u0103 aceasta va fi publicat\u0103 sub <link>Licen\u021Ba MIT, </link>care, pe scurt, \xEEnseamn\u0103 c\u0103 oricine o poate folosi f\u0103r\u0103 restric\u021Bii.",
  noteItems: "Fiecare element din bibliotec\u0103 trebuie s\u0103 aib\u0103 propriul nume astfel \xEEnc\xE2t s\u0103 fie filtrabil. Urm\u0103toarele elemente din bibliotec\u0103 vor fi incluse:",
  atleastOneLibItem: "Selecteaz\u0103 cel pu\u021Bin un element din bibliotec\u0103 pentru a \xEEncepe",
  republishWarning: "Observa\u021Bie: unele dintre elementele selectate sunt marcate ca fiind deja publicate/trimise. Ar trebui s\u0103 retrimi\u021Bi elemente numai atunci c\xE2nd actualizezi o trimitere sau o bibliotec\u0103 existent\u0103."
};
var publishSuccessDialog = {
  title: "Bibliotec\u0103 trimis\u0103",
  content: "\xCE\u021Bi mul\u021Bumim, {{authorName}}. Biblioteca a fost trimis\u0103 spre revizuire. Po\u021Bi urm\u0103ri starea <link>aici</link>"
};
var confirmDialog = {
  resetLibrary: "Resetare bibliotec\u0103",
  removeItemsFromLib: "Elimin\u0103 elementele selectate din bibliotec\u0103"
};
var imageExportDialog = {
  header: "Exportare imagine",
  label: {
    withBackground: "Fundal",
    onlySelected: "Numai selec\u021Bia",
    darkMode: "Mod \xEEntunecat",
    embedScene: "\xCEncorporare scen\u0103",
    scale: "Scal\u0103",
    padding: "Spa\u021Biere"
  },
  tooltip: {
    embedScene: "Datele scenei vor fi salvate \xEEn fi\u0219ierul PNG/SVG exportat, astfel c\u0103 scena va putea fi restaurat\u0103 din acesta.\nVa cre\u0219te dimensiunea fi\u0219ierului exportat."
  },
  title: {
    exportToPng: "Exportare ca PNG",
    exportToSvg: "Exportare ca SVG",
    copyPngToClipboard: "Copiere PNG \xEEn memoria temporar\u0103"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "Copiere \xEEn memoria temporar\u0103"
  }
};
var encrypted = {
  tooltip: "Desenele tale sunt criptate integral, astfel c\u0103 serverele Excalidraw nu le vor vedea niciodat\u0103.",
  link: "Articol de blog pe criptarea integral\u0103 din Excalidraw"
};
var stats = {
  angle: "Unghi",
  element: "Element",
  elements: "Elemente",
  height: "\xCEn\u0103l\u021Bime",
  scene: "Scen\u0103",
  selected: "Selectate",
  storage: "Stocare",
  title: "Statistici pentru pasiona\u021Bi",
  total: "Total",
  version: "Versiune",
  versionCopy: "Clic pentru copiere",
  versionNotAvailable: "Versiune indisponibil\u0103",
  width: "L\u0103\u021Bime"
};
var toast = {
  addedToLibrary: "Ad\u0103ugat \xEEn bibliotec\u0103",
  copyStyles: "Stiluri copiate.",
  copyToClipboard: "Copiat \xEEn memoria temporar\u0103.",
  copyToClipboardAsPng: "S-a copiat {{exportSelection}} \xEEn memoria temporar\u0103 sub form\u0103 de PNG\n({{exportColorScheme}})",
  fileSaved: "Fi\u0219ier salvat.",
  fileSavedToFilename: "Salvat \xEEn {filename}",
  canvas: "p\xE2nza",
  selection: "selec\u021Bia",
  pasteAsSingleElement: "Folose\u0219te {{shortcut}} pentru a insera ca un singur element\nsau insera \xEEntr-un editor de text existent",
  unableToEmbed: "\xCEncorporarea acestui URL nu este permis\u0103 momentan. Deschide\u021Bi un tichet cu probleme pe GitHub pentru a solicita ad\u0103ugarea acestui URL \xEEn lista alb\u0103",
  unrecognizedLinkFormat: "URL-ul pe care l-ai \xEEncorporat nu coincide cu formatul a\u0219teptat. \xCEncearc\u0103 s\u0103 lipe\u0219ti \u0219irul \u201Ede \xEEncorporat\u201D furnizat de pagina surs\u0103"
};
var colors = {
  transparent: "Transparent",
  black: "Negru",
  white: "Alb",
  red: "Ro\u0219u",
  pink: "Roz",
  grape: "Struguriu",
  violet: "Violet",
  gray: "Gri",
  blue: "Albastru",
  cyan: "Cyan",
  teal: "Cyan-verde",
  green: "Verde",
  yellow: "Galben",
  orange: "Portocaliu",
  bronze: "Bronz"
};
var welcomeScreen = {
  app: {
    center_heading: "Toate datele tale sunt salvate local \xEEn navigatorul t\u0103u.",
    center_heading_plus: "Ai vrut s\u0103 mergi \xEEn schimb la Excalidraw+?",
    menuHint: "Exportare, preferin\u021Be, limbi, ..."
  },
  defaults: {
    menuHint: "Exportare, preferin\u021Be \u0219i mai multe...",
    center_heading: "Diagrame. F\u0103cute. Simple.",
    toolbarHint: "Alege un instrument \u0219i \xEEncepe s\u0103 desenezi!",
    helpHint: "Comenzi rapide \u0219i ajutor"
  }
};
var colorPicker = {
  mostUsedCustomColors: "Cele mai utilizate culori personalizate",
  colors: "Culori",
  shades: "Nuan\u021Be",
  hexCode: "Cod hexa",
  noShades: "Nu este disponibil\u0103 nicio nuan\u021B\u0103 pentru aceast\u0103 culoare"
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "Exportare ca imagine",
      button: "Exportare ca imagine",
      description: "Export\u0103 datele scenei ca fi\u0219ier din care po\u021Bi importa mai t\xE2rziu."
    },
    saveToDisk: {
      title: "Salvare pe disc",
      button: "Salvare pe disc",
      description: "Export\u0103 datele scenei pe un fi\u0219ier din care po\u021Bi importa mai t\xE2rziu."
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "Exportare \xEEn Excalidraw+",
      description: "Salveaz\u0103 scena \xEEn spa\u021Biul de lucru Excalidraw+."
    }
  },
  modal: {
    loadFromFile: {
      title: "\xCEnc\u0103rcare din fi\u0219ier",
      button: "\xCEnc\u0103rcare din fi\u0219ier",
      description: "\xCEnc\u0103rcarea dintr-un fi\u0219ier va <bold>\xEEnlocui con\u021Binutul existent</bold>.<br></br>Po\u021Bi face mai \xEEnt\xE2i o copie de rezerv\u0103 a desenului folosind una dintre op\u021Biunile de mai jos."
    },
    shareableLink: {
      title: "\xCEnc\u0103rcare din lnk",
      button: "\xCEnlocuie\u0219te con\u021Binutul meu",
      description: "\xCEnc\u0103rcarea unui desen extern va <bold>\xEEnlocui con\u021Binutul existent</bold>.<br></br>Po\u021Bi face mai \xEEnt\xE2i o copie de rezerv\u0103 a desenului folosind una dintre op\u021Biunile de mai jos."
    }
  }
};
var mermaid = {
  title: "Mermaid la Excalidraw",
  button: "Introducere",
  description: "\xCEn prezent, numai <flowchartLink>Organigramele</flowchartLink>, <sequenceLink>Diagramele de secven\u021B\u0103</sequenceLink> \u0219i <classLink>Diagramele de clas\u0103</classLink> sunt acceptate. Celelalte tipuri vor fi redate ca imagine \xEEn Excalidraw.",
  syntax: "Sintax\u0103 Mermaid",
  preview: "Previzualizare"
};
var ro_RO_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  ro_RO_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=ro-RO-L575VRQA.js.map
