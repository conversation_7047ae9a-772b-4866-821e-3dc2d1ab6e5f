import type React from "react";
import type { ExcalidrawElement, OrderedExcalidrawElement } from "../element/types";
import type { AppClassProperties, AppState, ExcalidrawProps, BinaryFiles, UIAppState } from "../types";
import type { CaptureUpdateActionType } from "../store";
export type ActionSource = "ui" | "keyboard" | "contextMenu" | "api" | "commandPalette";
/** if false, the action should be prevented */
export type ActionResult = {
    elements?: readonly ExcalidrawElement[] | null;
    appState?: Partial<AppState> | null;
    files?: BinaryFiles | null;
    captureUpdate: CaptureUpdateActionType;
    replaceFiles?: boolean;
} | false;
type ActionFn = (elements: readonly OrderedExcalidrawElement[], appState: Readonly<AppState>, formData: any, app: AppClassProperties) => ActionResult | Promise<ActionResult>;
export type UpdaterFn = (res: ActionResult) => void;
export type ActionFilterFn = (action: Action) => void;
export type ActionName = "copy" | "cut" | "paste" | "copyAsPng" | "copyAsSvg" | "copyText" | "sendBackward" | "bringForward" | "sendToBack" | "bringToFront" | "copyStyles" | "selectAll" | "pasteStyles" | "gridMode" | "zenMode" | "objectsSnapMode" | "stats" | "changeStrokeColor" | "changeBackgroundColor" | "changeFillStyle" | "changeStrokeWidth" | "changeStrokeShape" | "changeSloppiness" | "changeStrokeStyle" | "changeArrowhead" | "changeArrowType" | "changeOpacity" | "changeFontSize" | "toggleCanvasMenu" | "toggleEditMenu" | "undo" | "redo" | "finalize" | "changeProjectName" | "changeExportBackground" | "changeExportEmbedScene" | "changeExportScale" | "saveToActiveFile" | "saveFileToDisk" | "loadScene" | "duplicateSelection" | "deleteSelectedElements" | "changeViewBackgroundColor" | "clearCanvas" | "zoomIn" | "zoomOut" | "resetZoom" | "zoomToFit" | "zoomToFitSelection" | "zoomToFitSelectionInViewport" | "changeFontFamily" | "changeTextAlign" | "changeVerticalAlign" | "toggleFullScreen" | "toggleShortcuts" | "group" | "ungroup" | "goToCollaborator" | "addToLibrary" | "changeRoundness" | "alignTop" | "alignBottom" | "alignLeft" | "alignRight" | "alignVerticallyCentered" | "alignHorizontallyCentered" | "distributeHorizontally" | "distributeVertically" | "flipHorizontal" | "flipVertical" | "viewMode" | "exportWithDarkMode" | "toggleTheme" | "increaseFontSize" | "decreaseFontSize" | "unbindText" | "hyperlink" | "bindText" | "unlockAllElements" | "toggleElementLock" | "toggleLinearEditor" | "toggleEraserTool" | "toggleHandTool" | "selectAllElementsInFrame" | "removeAllElementsFromFrame" | "updateFrameRendering" | "setFrameAsActiveTool" | "setEmbeddableAsActiveTool" | "createContainerFromText" | "wrapTextInContainer" | "commandPalette" | "autoResize" | "elementStats" | "searchMenu" | "copyElementLink" | "linkToElement" | "cropEditor" | "wrapSelectionInFrame";
export type PanelComponentProps = {
    elements: readonly ExcalidrawElement[];
    appState: AppState;
    updateData: <T = any>(formData?: T) => void;
    appProps: ExcalidrawProps;
    data?: Record<string, any>;
    app: AppClassProperties;
};
export interface Action {
    name: ActionName;
    label: string | ((elements: readonly ExcalidrawElement[], appState: Readonly<AppState>, app: AppClassProperties) => string);
    keywords?: string[];
    icon?: React.ReactNode | ((appState: UIAppState, elements: readonly ExcalidrawElement[]) => React.ReactNode);
    PanelComponent?: React.FC<PanelComponentProps>;
    perform: ActionFn;
    keyPriority?: number;
    keyTest?: (event: React.KeyboardEvent | KeyboardEvent, appState: AppState, elements: readonly ExcalidrawElement[], app: AppClassProperties) => boolean;
    predicate?: (elements: readonly ExcalidrawElement[], appState: AppState, appProps: ExcalidrawProps, app: AppClassProperties) => boolean;
    checked?: (appState: Readonly<AppState>) => boolean;
    trackEvent: false | {
        category: "toolbar" | "element" | "canvas" | "export" | "history" | "menu" | "collab" | "hyperlink" | "search_menu";
        action?: string;
        predicate?: (appState: Readonly<AppState>, elements: readonly ExcalidrawElement[], value: any) => boolean;
    };
    /** if set to `true`, allow action to be performed in viewMode.
     *  Defaults to `false` */
    viewMode?: boolean;
}
export {};
