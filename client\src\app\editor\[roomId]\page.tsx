"use client"

import { useParams } from "next/navigation"
import { useEffect, useState } from "react"
import CodeEditor from "@/components/CodeEditor"
import ProtectedRoute from "@/components/ProtectedRoute"
import { EditPermissionProvider } from "@/context/EditPermissionContext"
import EditPermissionPanel from "@/components/EditPermissionPanel"
import PermissionStatusIndicator, { PermissionChangeIndicator } from "@/components/PermissionStatusIndicator"

export default function EditorPage() {
  // Get the params and handle the type safely
  const params = useParams()
  // Extract roomId and ensure it's a string
  const roomId = params?.roomId as string
  const [username, setUsername] = useState<string>("Anonymous") // Default username

  useEffect(() => {
    // Debugging: Check if roomId is being extracted
    console.log("Extracted roomId:", roomId)

    if (!roomId) {
      console.error("Invalid or missing room ID")
      return
    }

    if (typeof window !== "undefined") {
      const storedUsername = localStorage.getItem("username")
      if (storedUsername) {
        console.log("Using stored username:", storedUsername)
        setUsername(storedUsername)
      } else {
        console.log("No stored username found, redirecting to dashboard")
        // Generate a random username suffix
        const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
        const defaultUsername = `User${randomSuffix}`
        localStorage.setItem("username", defaultUsername)
        setUsername(defaultUsername)
      }
    }
  }, [roomId])

  if (!roomId) {
    return <div>Error: Room ID is missing. Please join a valid room.</div>
  }

  return (
    <ProtectedRoute>
      <EditPermissionProvider>
        <main className="flex flex-col items-center justify-center min-h-screen p-4 gap-6 relative overflow-hidden">
          {/* Background elements */}
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-blue-900/10 to-purple-900/10 dark:from-blue-900/20 dark:to-purple-900/20" />
            <div className="absolute inset-0 bg-grid-pattern opacity-10" />
          </div>

          {/* Header with room info and permission status */}
          <div className="w-full max-w-6xl flex items-center justify-between mb-4">
            <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
              RealCode - Room: {roomId}
            </h1>
            <PermissionStatusIndicator size="lg" />
          </div>

          {/* Main content area */}
          <div className="w-full max-w-6xl flex gap-4">
            {/* Code Editor */}
            <div className="flex-1">
              <CodeEditor roomId={roomId} username={username} />
            </div>

            {/* Permission Panel (right sidebar) */}
            <div className="w-80 flex-shrink-0">
              <EditPermissionPanel />
            </div>
          </div>

          {/* Permission change notifications */}
          <PermissionChangeIndicator />
        </main>
      </EditPermissionProvider>
    </ProtectedRoute>
  )
}