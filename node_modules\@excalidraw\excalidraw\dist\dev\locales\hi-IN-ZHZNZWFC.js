import "../chunk-XDFCUUT6.js";

// locales/hi-IN.json
var labels = {
  paste: "\u091A\u093F\u092A\u0915\u093E\u090F\u0901",
  pasteAsPlaintext: "\u0938\u093E\u0926\u0947 \u092A\u093E\u0920 \u0915\u0947 \u0930\u0942\u092A \u092E\u0947\u0902 \u091A\u093F\u092A\u0915\u093E\u090F\u0902",
  pasteCharts: "\u091A\u093E\u0930\u094D\u091F \u091A\u093F\u092A\u0915\u093E\u090F\u0901",
  selectAll: "\u0938\u092D\u0940 \u091A\u0941\u0928\u0947\u0902",
  multiSelect: "\u0906\u0915\u093E\u0930 \u0915\u094B \u091A\u092F\u0928 \u092E\u0947\u0902 \u091C\u094B\u095C\u0947\u0902",
  moveCanvas: "\u0915\u0948\u0928\u0935\u093E\u0938 \u0915\u094B \u0938\u094D\u0925\u093E\u0928\u093E\u0902\u0924\u0930\u093F\u0924 \u0915\u0930\u0947\u0902",
  cut: "\u0915\u093E\u091F\u0947\u0902",
  copy: "\u092A\u094D\u0930\u0924\u093F\u0932\u093F\u092A\u093F",
  copyAsPng: "\u0915\u094D\u0932\u093F\u092A\u092C\u094B\u0930\u094D\u0921 \u092A\u0930 \u0915\u0949\u092A\u0940 \u0915\u0930\u0947\u0902 ,\u092A\u0940\u090F\u0928\u091C\u0940 \u0915\u0947 \u0930\u0942\u092A \u092E\u0947\u0902",
  copyAsSvg: "\u0915\u094D\u0932\u093F\u092A\u092C\u094B\u0930\u094D\u0921 \u092A\u0930 \u0915\u0949\u092A\u0940 \u0915\u0930\u0947\u0902,\u090F\u0938\u0935\u0940\u091C\u0940 \u0915\u0947 \u0930\u0942\u092A \u092E\u0947\u0902",
  copyText: "\u0932\u0947\u0916\u0928 \u0915\u0947 \u0930\u0942\u092A \u092E\u0947\u0902 \u092A\u091F\u0932 \u092A\u0930 \u0915\u0949\u092A\u0940 \u0915\u0930\u0947\u0902",
  copySource: "\u0938\u094D\u0924\u094D\u0930\u094B\u0924 \u0915\u094B \u092A\u094D\u0930\u0924\u093F-\u092B\u0932\u0915 \u092A\u0947 \u092A\u094D\u0930\u0924\u093F\u0932\u093F\u092A\u093F\u0924 \u0915\u0930\u0947.",
  convertToCode: "\u0938\u093E\u0902\u0915\u0947\u0924\u093F\u0915 \u0932\u093F\u092A\u093F \u092E\u0947\u0902 \u092A\u0930\u093F\u0935\u0930\u094D\u0924\u093F\u0924 \u0915\u0930\u0947",
  bringForward: "\u0938\u093E\u092E\u0928\u0947 \u0932\u093E\u090F\u0902",
  sendToBack: "\u092A\u0940\u091B\u0947 \u092D\u0947\u091C\u0947\u0902",
  bringToFront: "\u0938\u093E\u092E\u0928\u0947 \u0932\u093E\u090F\u0901",
  sendBackward: "\u092A\u0940\u091A\u0947 \u092D\u0940\u091C\u0947",
  delete: "\u092E\u093F\u091F\u093E\u090F",
  copyStyles: "\u0915\u0949\u092A\u0940 \u0938\u094D\u091F\u093E\u0907\u0932",
  pasteStyles: "\u0938\u094D\u091F\u093E\u0907\u0932 \u092A\u0947\u0938\u094D\u091F \u0915\u0930\u0947\u0902",
  stroke: "\u0930\u0947\u0916\u093E",
  background: "\u092A\u0943\u0937\u094D\u0920\u092D\u0942\u092E\u093F",
  fill: "\u092D\u0930\u0947\u0902",
  strokeWidth: "\u0930\u0947\u0916\u093E \u0915\u0940 \u091A\u094C\u0921\u093C\u093E\u0908",
  strokeStyle: "\u0938\u094D\u091F\u094D\u0930\u094B\u0915 \u0915\u093E \u0906\u0915\u093E\u0930",
  strokeStyle_solid: "\u0920\u094B\u0938",
  strokeStyle_dashed: "\u0921\u0948\u0936",
  strokeStyle_dotted: "\u092C\u093F\u0902\u0926\u0940\u0926\u093E\u0930",
  sloppiness: "\u092C\u0947\u0922\u093C\u0902\u0917\u093E\u092A\u0928",
  opacity: "\u0905\u092A\u093E\u0930\u0926\u0930\u094D\u0936\u093F\u0924\u093E",
  textAlign: "\u091F\u0947\u0915\u094D\u0938\u094D\u091F \u0938\u0902\u0930\u0947\u0916\u0928",
  edges: "\u0915\u093F\u0928\u093E\u0930\u093E",
  sharp: "\u0928\u0941\u0915\u0940\u0932\u093E",
  round: "\u0917\u094B\u0932",
  arrowheads: "\u0924\u0940\u0930 \u0936\u0940\u0930\u094D\u0937",
  arrowhead_none: "\u0915\u094B\u0908 \u092D\u0940 \u0928\u0939\u0940\u0902",
  arrowhead_arrow: "\u0924\u0940\u0930",
  arrowhead_bar: "\u092C\u093E\u0930",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "\u0924\u094D\u0930\u093F\u0915\u094B\u0923",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "\u092B\u093C\u0949\u0928\u094D\u091F \u0915\u093E \u0906\u0915\u093E\u0930",
  fontFamily: "\u092B\u093C\u0949\u0928\u094D\u091F \u0915\u093E \u092A\u0930\u093F\u0935\u093E\u0930",
  addWatermark: '\u0910\u0921 "\u092E\u0947\u0921 \u0935\u093F\u0925 \u090F\u0915\u094D\u0938\u0915\u0948\u0932\u093F\u0921\u0930\u093E\u0935"',
  handDrawn: "\u0939\u093E\u0925 \u0938\u0947 \u092C\u0928\u093E\u092F\u093E \u0939\u0941\u0906",
  normal: "\u0938\u093E\u0927\u093E\u0930\u0923",
  code: "\u0915\u094B\u0921",
  small: "\u091B\u094B\u091F\u093E",
  medium: "\u092E\u0927\u094D\u092F\u092E",
  large: "\u092C\u0921\u093C\u093E",
  veryLarge: "\u092C\u0939\u0941\u0924 \u092C\u0921\u093C\u093E",
  solid: "\u0926\u0943\u0922\u093C",
  hachure: "\u0939\u0948\u0936\u0942\u0930",
  zigzag: "\u0924\u0947\u0922\u093C\u0940 \u092E\u0947\u0922\u093C\u0940",
  crossHatch: "\u0915\u094D\u0930\u0949\u0938 \u0939\u0948\u091A",
  thin: "\u092A\u0924\u0932\u093E",
  bold: "\u092E\u094B\u091F\u093E",
  left: "\u092C\u093E\u090F\u0902",
  center: "\u092E\u0927\u094D\u092F",
  right: "\u0926\u093E\u090F\u0901",
  extraBold: "\u092C\u0939\u0941\u0924 \u092E\u094B\u091F\u093E",
  architect: "\u0935\u093E\u0938\u094D\u0924\u0941\u0915\u093E\u0930",
  artist: "\u0915\u0932\u093E\u0915\u093E\u0930",
  cartoonist: "\u0935\u094D\u092F\u0902\u0917\u094D\u092F \u091A\u093F\u0924\u094D\u0930\u0915\u093E\u0930",
  fileTitle: "\u092B\u093C\u093E\u0907\u0932 \u0915\u093E \u0928\u093E\u092E",
  colorPicker: "\u0930\u0902\u0917 \u091A\u092F\u0928",
  canvasColors: "\u0915\u0945\u0928\u0935\u093E\u0938 \u092A\u0930 \u092A\u094D\u0930\u092F\u094B\u0917\u093F\u0924",
  canvasBackground: "\u0915\u0948\u0928\u0935\u093E\u0938 \u092C\u0948\u0915\u0917\u094D\u0930\u093E\u0909\u0902\u0921",
  drawingCanvas: "\u0915\u0948\u0928\u0935\u093E\u0938 \u092C\u0928\u093E \u0930\u0939\u0947 \u0939\u0948\u0902",
  layers: "\u092A\u0930\u0924\u0947\u0902",
  actions: "\u0915\u093E\u0930\u094D\u0930\u0935\u093E\u0908",
  language: "\u092D\u093E\u0937\u093E",
  liveCollaboration: "\u091C\u0940\u0935\u0902\u0924 \u0938\u0939\u092F\u094B\u0917...",
  duplicateSelection: "\u0921\u0941\u092A\u094D\u0932\u093F\u0915\u0947\u091F",
  untitled: "\u0905\u0936\u0940\u0930\u094D\u0937\u093F\u0924",
  name: "\u0928\u093E\u092E",
  yourName: "\u0906\u092A\u0915\u093E \u0928\u093E\u092E",
  madeWithExcalidraw: "\u092E\u0947\u0921 \u0935\u093F\u0925 \u090F\u0915\u094D\u0938\u0915\u0948\u0932\u093F\u0921\u0930\u093E\u0935",
  group: "\u0938\u092E\u0942\u0939 \u091A\u092F\u0928",
  ungroup: "\u0938\u092E\u0942\u0939 \u091A\u092F\u0928 \u0905\u0938\u092E\u0942\u0939\u0940\u0915\u0943\u0924 \u0915\u0930\u0947\u0902",
  collaborators: "\u0938\u0939\u092F\u094B\u0917\u0940",
  showGrid: "\u0917\u094D\u0930\u093F\u0921 \u0926\u093F\u0916\u093E\u090F\u0902",
  addToLibrary: "\u0932\u093E\u0907\u092C\u094D\u0930\u0947\u0930\u0940 \u0938\u0947 \u091C\u094B\u0921\u093C\u0947\u0902",
  removeFromLibrary: "\u0932\u093E\u0907\u092C\u094D\u0930\u0947\u0930\u0940 \u0938\u0947 \u0928\u093F\u0915\u093E\u0932\u0947\u0902",
  libraryLoadingMessage: "\u0932\u093E\u0907\u092C\u094D\u0930\u0947\u0930\u0940 \u0916\u0941\u0932 \u0930\u0939\u0940 \u0939\u0948",
  libraries: "\u0932\u093E\u0907\u092C\u094D\u0930\u0947\u0930\u0940 \u092C\u094D\u0930\u093E\u0909\u091C\u093C \u0915\u0930\u0947\u0902",
  loadingScene: "\u0926\u0943\u0936\u094D\u092F \u0916\u0941\u0932 \u0930\u0939\u093E \u0939\u0948",
  align: "\u0938\u0902\u0930\u0947\u0916\u093F\u0924 \u0915\u0930\u0947\u0902",
  alignTop: "\u090A\u092A\u0930 \u0938\u0902\u0930\u0947\u0916\u093F\u0924 \u0915\u0930\u0947\u0902",
  alignBottom: "\u0928\u0940\u091A\u0947 \u0938\u0902\u0930\u0947\u0916\u093F\u0924 \u0915\u0930\u0947\u0902",
  alignLeft: "\u092C\u093E\u092F\u0947\u0902 \u0938\u0902\u0930\u0947\u0916\u093F\u0924 \u0915\u0930\u0947\u0902",
  alignRight: "\u0926\u093E\u092F\u0947\u0902 \u0938\u0902\u0930\u0947\u0916\u093F\u0924 \u0915\u0930\u0947\u0902",
  centerVertically: "\u0932\u0902\u092C\u0935\u0924 \u0915\u0947\u0928\u094D\u0926\u094D\u0930\u093F\u0924",
  centerHorizontally: "\u0915\u094D\u0937\u0948\u0924\u093F\u091C \u0915\u0947\u0928\u094D\u0926\u094D\u0930\u093F\u0924",
  distributeHorizontally: "\u0915\u094D\u0937\u0948\u0924\u093F\u091C \u0930\u0942\u092A \u0938\u0947 \u0935\u093F\u0924\u0930\u093F\u0924 \u0915\u0930\u0947\u0902",
  distributeVertically: "\u0916\u0921\u093C\u0940 \u0930\u0942\u092A \u0938\u0947 \u0935\u093F\u0924\u0930\u093F\u0924 \u0915\u0930\u0947\u0902",
  flipHorizontal: "\u0926\u093E\u092F\u0947\u0902 \u092C\u093E\u092F\u0947\u0902 \u092A\u0932\u091F\u0947",
  flipVertical: "\u090A\u092A\u0930 \u0928\u0940\u091A\u0947 \u092A\u0932\u091F\u0947",
  viewMode: "\u0905\u0932\u0917 \u0905\u0932\u0917 \u0926\u0947\u0916\u0947\u0902",
  share: "\u0936\u0947\u092F\u0930 \u0915\u0930\u0947\u0902",
  showStroke: "",
  showBackground: "\u092A\u0943\u0937\u094D\u0920\u092D\u0942\u092E\u093F \u0930\u0902\u0917 \u0935\u0930\u0915\u093C \u0926\u093F\u0916\u093E\u092F\u0947",
  toggleTheme: "",
  personalLib: "\u0935\u0948\u092F\u0915\u094D\u0924\u093F\u0915 \u0938\u092E\u0942\u0939\u0915\u094B\u0937",
  excalidrawLib: "\u090F\u0915\u094D\u0938\u0915\u0947\u0932\u0940\u0921\u094D\u0930\u0949 \u0938\u092E\u0942\u0939\u0915\u094B\u0937",
  decreaseFontSize: "\u0906\u0915\u093E\u0930 \u0918\u091F\u093E\u0907\u0910",
  increaseFontSize: "\u092B\u093C\u0949\u0928\u094D\u091F \u0906\u0915\u093E\u0930 \u092C\u0922\u093C\u093E\u090F\u0901",
  unbindText: "\u0932\u093F\u092A\u093F \u0915\u094B \u092C\u0902\u0927\u092E\u0941\u0915\u094D\u0924 \u0915\u0930\u0947\u0902",
  bindText: "\u0932\u0947\u0916\u0928 \u0915\u094B \u0915\u094B\u0936 \u0938\u0947 \u091C\u094B\u0921\u093C\u0947",
  createContainerFromText: "\u092E\u0942\u0932\u092A\u093E\u0920 \u0915\u0902\u091F\u0947\u0928\u0930 \u092E\u0947\u0902 \u092E\u094B\u0921 \u0915\u0947 \u0926\u093F\u0916\u093E\u090F",
  link: {
    edit: "\u0915\u0921\u093C\u0940 \u0938\u0902\u092A\u093E\u0926\u093F\u0924 \u0915\u0930\u0947",
    editEmbed: "",
    create: "",
    createEmbed: "",
    label: "",
    labelEmbed: "",
    empty: ""
  },
  lineEditor: {
    edit: "\u0930\u0947\u0916\u093E \u0938\u0902\u092A\u093E\u0926\u093F\u0924 \u0915\u0930\u0947",
    exit: "\u0930\u0947\u0916\u093E \u0938\u0902\u092A\u093E\u0926\u0915 \u0915\u0947 \u092C\u093E\u0939\u0930"
  },
  elementLock: {
    lock: "\u0924\u093E\u0932\u0947 \u092E\u0947\u0902 \u0930\u0916\u0947\u0902",
    unlock: "\u0924\u093E\u0932\u0947 \u0938\u0947 \u092C\u093E\u0939\u0930",
    lockAll: "\u0938\u092C \u0924\u093E\u0932\u0947 \u0915\u0947 \u0905\u0902\u0926\u0930 \u0930\u0916\u0947",
    unlockAll: "\u0938\u092C \u0924\u093E\u0932\u0947 \u0915\u0947 \u092C\u093E\u0939\u0930 \u0928\u093F\u0915\u093E\u0932\u0947"
  },
  statusPublished: "\u092A\u094D\u0930\u0915\u093E\u0936\u093F\u0924",
  sidebarLock: "\u0938\u093E\u0907\u0921\u092C\u093E\u0930 \u0916\u0941\u0932\u093E \u0930\u0916\u0947.",
  selectAllElementsInFrame: "",
  removeAllElementsFromFrame: "",
  eyeDropper: "\u091A\u093F\u0924\u094D\u0930\u092B\u0932\u0915 \u0938\u0947 \u0930\u0902\u0917 \u091A\u0941\u0928\u0947",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "\u0905\u092D\u0940 \u0924\u0915 \u0915\u094B\u0908 \u0906\u0907\u091F\u092E \u091C\u094B\u0921\u093E \u0928\u0939\u0940\u0902 \u0917\u092F\u093E.",
  hint_emptyLibrary: "\u092F\u0939\u093E\u0901 \u091C\u094B\u0921\u093C\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F \u092A\u091F\u0932 \u0938\u0947 \u090F\u0915 \u0935\u0938\u094D\u0924\u0941 \u091A\u0941\u0928\u0947, \u0905\u0925\u0935\u093E \u091C\u0928 \u0915\u094B\u0937 \u0938\u0947 \u090F\u0915 \u0938\u0902\u0917\u094D\u0930\u0939 \u0928\u0940\u091A\u0947 \u0938\u094D\u0925\u093E\u092A\u093F\u0924 \u0915\u0930\u0947\u0902.",
  hint_emptyPrivateLibrary: "\u092F\u0939\u093E\u0901 \u091C\u094B\u0921\u093C\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F \u092A\u091F\u0932 \u0938\u0947 \u090F\u0915 \u0935\u0938\u094D\u0924\u0941 \u091A\u0941\u0928\u0947."
};
var buttons = {
  clearReset: "\u0915\u0948\u0928\u0935\u093E\u0938 \u0930\u0940\u0938\u0947\u091F \u0915\u0930\u0947\u0902",
  exportJSON: "",
  exportImage: "\u092A\u094D\u0930\u0924\u093F\u092E\u093E \u0928\u093F\u0930\u094D\u092F\u093E\u0924 \u0915\u0930\u0947...",
  export: "\u092F\u0902\u0939\u093E \u0938\u0941\u0930\u0915\u094D\u0937\u093F\u0924 \u0915\u0930\u0947...",
  copyToClipboard: "\u0915\u094D\u0932\u093F\u092A\u092C\u094B\u0930\u094D\u0921 \u092A\u0930 \u092A\u094D\u0930\u0924\u093F\u0932\u093F\u092A\u093F \u092C\u0928\u093E\u090F\u0901",
  save: "",
  saveAs: "\u0938\u0947\u0935 \u0915\u0930\u0947 \u0907\u0938 \u0924\u0930\u0939",
  load: "\u0916\u094B\u0932\u0947\u0902",
  getShareableLink: "\u0938\u093E\u091D\u093E \u0915\u0930\u0928\u0947 \u092F\u094B\u0917\u094D\u092F \u0932\u093F\u0902\u0915 \u092A\u094D\u0930\u093E\u092A\u094D\u0924 \u0915\u0930\u0947\u0902",
  close: "\u092C\u0902\u0926 \u0915\u0930\u0947\u0902",
  selectLanguage: "\u092D\u093E\u0937\u093E \u091A\u0941\u0928\u0947\u0902",
  scrollBackToContent: "\u0938\u093E\u092E\u0917\u094D\u0930\u0940 \u092A\u0930 \u0935\u093E\u092A\u0938 \u0938\u094D\u0915\u094D\u0930\u0949\u0932 \u0915\u0930\u0947\u0902",
  zoomIn: "\u092C\u095C\u093E \u0915\u0930\u0947\u0902",
  zoomOut: "\u091B\u094B\u091F\u093E \u0915\u0930\u0947\u0902",
  resetZoom: "\u091C\u093C\u0942\u092E \u0930\u0940\u0938\u0947\u091F \u0915\u0930\u0947\u0902",
  menu: "\u092E\u0947\u0928\u094D\u092F\u0942",
  done: "\u0938\u092E\u093E\u092A\u094D\u0924",
  edit: "\u0938\u0902\u0936\u094B\u0927\u0928 \u0915\u0930\u0947\u0902",
  undo: "\u092A\u0942\u0930\u094D\u0935\u0935\u0924\u094D \u0915\u0930\u0947\u0902",
  redo: "\u092B\u093F\u0930 \u0938\u0947 \u0915\u0930\u0947\u0902",
  resetLibrary: "",
  createNewRoom: "\u090F\u0915 \u0928\u092F\u093E \u0915\u092E\u0930\u093E \u092C\u0928\u093E\u090F\u0902",
  fullScreen: "\u092A\u0942\u0930\u0940 \u0938\u094D\u0915\u094D\u0930\u0940\u0928",
  darkMode: "\u0921\u093E\u0930\u094D\u0915 \u092E\u094B\u0921",
  lightMode: "\u0932\u093E\u0907\u091F \u092E\u094B\u0921",
  zenMode: "\u091C\u093C\u0947\u0928 \u092E\u094B\u0921",
  objectsSnapMode: "\u0935\u0938\u094D\u0924\u0941\u0913\u0902 \u0938\u0947 \u092A\u0915\u0921\u093C\u0947",
  exitZenMode: "\u091C\u0947\u0928 \u092E\u094B\u0921 \u0938\u0947 \u092C\u093E\u0939\u0930 \u0928\u093F\u0915\u0932\u0947\u0902",
  cancel: "",
  clear: "\u0938\u093E\u092B\u093C \u0915\u0930\u0947",
  remove: "\u0939\u091F\u093E\u090F\u0902",
  embed: "",
  publishLibrary: "\u092A\u094D\u0930\u0915\u093E\u0936\u093F\u0924 \u0915\u0930\u0947\u0902",
  submit: "\u092A\u094D\u0930\u0938\u094D\u0924\u0941\u0924 \u0915\u0930\u0947",
  confirm: "\u092A\u0941\u0937\u094D\u091F\u093F \u0915\u0930\u0947\u0902",
  embeddableInteractionButton: ""
};
var alerts = {
  clearReset: "\u0907\u0938\u0938\u0947 \u092A\u0942\u0930\u093E \u0915\u0948\u0928\u0935\u093E\u0938 \u0938\u093E\u092B \u0939\u094B \u091C\u093E\u090F\u0917\u093E\u0964 \u0915\u094D\u092F\u093E \u0906\u092A\u0915\u094B \u092F\u0915\u0940\u0928 \u0939\u0948?",
  couldNotCreateShareableLink: "\u0938\u093E\u091D\u093E \u0915\u0930\u0928\u0947 \u092F\u094B\u0917\u094D\u092F \u0932\u093F\u0902\u0915 \u0928\u0939\u0940\u0902 \u092C\u0928\u093E\u092F\u093E \u091C\u093E \u0938\u0915\u093E\u0964",
  couldNotCreateShareableLinkTooBig: "\u0932\u093F\u0902\u0915 \u0936\u0947\u092F\u0930 \u0928\u0939\u0940\u0902 \u0915\u0930 \u0938\u0915\u0924\u093E: \u0926\u0943\u0936\u094D\u092F \u092C\u0939\u0941\u0924 \u092C\u0921\u093C\u093E",
  couldNotLoadInvalidFile: "\u0905\u092E\u093E\u0928\u094D\u092F \u092B\u093C\u093E\u0907\u0932 \u0932\u094B\u0921 \u0928\u0939\u0940\u0902 \u0915\u0940 \u091C\u093E \u0938\u0915\u0940",
  importBackendFailed: "\u092C\u0948\u0915\u090F\u0902\u0921 \u0938\u0947 \u0906\u092F\u093E\u0924 \u0915\u0930\u0928\u093E \u0935\u093F\u092B\u0932 \u0930\u0939\u093E\u0964",
  cannotExportEmptyCanvas: "\u0916\u093E\u0932\u0940 \u0915\u0948\u0928\u0935\u093E\u0938 \u0928\u093F\u0930\u094D\u092F\u093E\u0924 \u0928\u0939\u0940\u0902 \u0915\u0930 \u0938\u0915\u0924\u093E\u0964",
  couldNotCopyToClipboard: "\u0915\u094D\u0932\u093F\u092A\u092C\u094B\u0930\u094D\u0921 \u092A\u0930 \u0915\u0949\u092A\u0940 \u0928\u0939\u0940\u0902 \u0915\u093F\u092F\u093E \u091C\u093E \u0938\u0915\u093E",
  decryptFailed: "\u0921\u0947\u091F\u093E \u0915\u094B \u0921\u093F\u0915\u094D\u0930\u093F\u092A\u094D\u091F \u0928\u0939\u0940\u0902 \u0915\u093F\u092F\u093E \u091C\u093E \u0938\u0915\u093E\u0964",
  uploadedSecurly: "\u0905\u092A\u0932\u094B\u0921 \u0915\u094B \u090F\u0902\u0921-\u091F\u0942-\u090F\u0902\u0921 \u090F\u0928\u094D\u0915\u094D\u0930\u093F\u092A\u094D\u0936\u0928 \u0915\u0947 \u0938\u093E\u0925 \u0938\u0941\u0930\u0915\u094D\u0937\u093F\u0924 \u0915\u093F\u092F\u093E \u0917\u092F\u093E \u0939\u0948, \u091C\u093F\u0938\u0915\u093E \u092E\u0924\u0932\u092C \u0939\u0948 \u0915\u093F \u090F\u0915\u094D\u0938\u0915\u094D\u0932\u0942\u0938\u093F\u0935 \u0938\u0930\u094D\u0935\u0930 \u0914\u0930 \u0925\u0930\u094D\u0921 \u092A\u093E\u0930\u094D\u091F\u0940 \u0915\u0902\u091F\u0947\u0902\u091F \u0928\u0939\u0940\u0902 \u092A\u0922\u093C \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964",
  loadSceneOverridePrompt: "\u0932\u094B\u0921 \u0939\u094B \u0930\u0939\u093E \u0939\u0948 \u092C\u093E\u0939\u0930\u0940 \u0921\u094D\u0930\u093E\u0907\u0902\u0917 \u0906\u092A\u0915\u0947 \u092E\u094C\u091C\u0942\u0926\u093E \u0938\u093E\u092E\u0917\u094D\u0930\u0940 \u0915\u094B \u092C\u0926\u0932 \u0926\u0947\u0917\u093E\u0964 \u0915\u094D\u092F\u093E \u0906\u092A \u091C\u093E\u0930\u0940 \u0930\u0916\u0928\u093E \u091A\u093E\u0939\u0924\u0947 \u0939\u0948\u0902?",
  collabStopOverridePrompt: "\u091A\u093E\u0932\u0942 \u0938\u0924\u094D\u0930 \u0938\u092E\u093E\u092A\u094D\u0924\u093F \u0938\u0947 \u0906\u092A\u0915\u093E \u0938\u0902\u0917\u094D\u0930\u0939\u093F\u0924 \u092A\u0942\u0930\u094D\u0935 \u0938\u094D\u0925\u093E\u0928\u0940\u092F \u0905\u0927\u093F\u0932\u0947\u0916\u0928 \u0928\u0937\u094D\u091F \u0939\u094B\u0915\u0930 \u092A\u0941\u0928\u0903 \u0905\u0927\u093F\u0932\u0947\u0916\u093F\u0924 \u0939\u094B\u0917\u093E, \u0915\u094D\u092F\u093E \u0906\u092A\u0915\u094B \u092F\u0915\u093C\u0940\u0928 \u0939\u0948\u0902? ( \u092F\u0926\u0940 \u0906\u092A\u0915\u094B \u092A\u0942\u0930\u094D\u0935 \u0938\u094D\u0925\u093E\u092A\u093F\u0924 \u0905\u0927\u093F\u0932\u0947\u0916\u0928 \u0938\u0941\u0930\u0915\u094D\u0937\u093F\u0924 \u091A\u093E\u0939\u093F\u092F\u0947 \u0924\u094B \u092C\u0938 \u092C\u094D\u0930\u093E\u0909\u091C\u093C\u0930 \u091F\u0948\u092C \u092C\u0902\u0926 \u0915\u0930\u0947)",
  errorAddingToLibrary: "\u0938\u0902\u0917\u094D\u0930\u0939 \u092E\u0947\u0902 \u091C\u094B\u0921\u093E \u0928 \u091C\u093E \u0938\u0915\u093E",
  errorRemovingFromLibrary: "\u0938\u0902\u0917\u094D\u0930\u0939 \u0938\u0947 \u0939\u091F\u093E\u092F\u093E \u0928\u0939\u0940\u0902 \u091C\u093E \u0938\u0915\u093E",
  confirmAddLibrary: "\u0932\u093E\u0907\u092C\u094D\u0930\u0947\u0930\u0940 \u091C\u094B\u0921\u093C\u0947\u0902 \u092A\u0941\u0937\u094D\u200D\u091F\u093F \u0915\u0930\u0947\u0902 \u0906\u0915\u093E\u0930 \u0938\u0902\u0916\u094D\u092F\u093E",
  imageDoesNotContainScene: "\u0910\u0938\u093E \u0932\u0917\u0924\u093E \u0939\u0948 \u0915\u093F \u0907\u0938 \u091B\u0935\u093F \u092E\u0947\u0902 \u0915\u094B\u0908 \u0926\u0943\u0936\u094D\u092F \u0921\u0947\u091F\u093E \u0928\u0939\u0940\u0902 \u0939\u0948\u0964 \u0915\u094D\u092F\u093E \u0906\u092A\u0928\u0947 \u0928\u093F\u0930\u094D\u092F\u093E\u0924 \u0915\u0947 \u0926\u094C\u0930\u093E\u0928 \u0926\u0943\u0936\u094D\u092F \u090F\u092E\u094D\u092C\u0947\u0921\u093F\u0902\u0917 \u0905\u0928\u0941\u092E\u0924\u093F\u0924 \u0915\u0940 \u0939\u0948?",
  cannotRestoreFromImage: "\u091B\u0935\u093F \u092B\u093C\u093E\u0907\u0932 \u092C\u0939\u093E\u0932 \u0926\u0943\u0936\u094D\u092F \u0928\u0939\u0940\u0902 \u0939\u0948",
  invalidSceneUrl: "\u0926\u093F\u092F\u0947 \u0917\u092F\u0947 \u092F\u0941\u0906\u0930\u0947\u0932 \u0938\u0947 \u0926\u0943\u0936\u094D\u092F \u0906\u092F\u093E\u0924 \u0928\u0939\u0940\u0902 \u0915\u093F\u092F\u093E \u091C\u093E \u0938\u0915\u093E. \u092F\u0939 \u092F\u093E \u0924\u094B \u0905\u0928\u0941\u091A\u093F\u0924 \u0939\u0948, \u092F\u093E \u0907\u0938\u092E\u0947\u0902 \u0909\u091A\u093F\u0924 Excalidraw JSON \u0921\u0947\u091F\u093E \u0928\u0939\u0940\u0902 \u0939\u0948\u0964",
  resetLibrary: "\u092F\u0939 \u092A\u0942\u0930\u093E \u0938\u0902\u0917\u094D\u0930\u0939 \u0930\u093F\u0915\u094D\u0924 \u0915\u0930\u0947\u0917\u093E. \u0915\u094D\u092F\u093E \u0906\u092A\u0915\u094B \u092F\u0915\u093C\u0940\u0928 \u0939\u0948\u0902?",
  removeItemsFromsLibrary: "{{count}} \u0935\u0938\u094D\u0924\u0941(\u092F\u0947\u0902) \u0938\u0902\u0917\u094D\u0930\u0939 \u0938\u0947 \u0939\u091F\u093E\u092F\u0947\u0902?",
  invalidEncryptionKey: "\u0915\u0942\u091F\u0932\u0947\u0916\u0928 \u0915\u0941\u0902\u091C\u0940 22 \u0905\u0915\u094D\u0937\u0930\u094B\u0902 \u0915\u0940 \u0939\u094B\u0928\u0940 \u091A\u093E\u0939\u093F\u092F\u0947, \u0907\u0938\u0932\u093F\u092F\u0947 \u091C\u0940\u0935\u0902\u0924 \u0938\u0939\u092F\u094B\u0917 \u0905\u0915\u094D\u0937\u092E \u0939\u0948\u0902",
  collabOfflineWarning: "\u0915\u094B\u0908 \u0907\u0902\u091F\u0930\u0928\u0947\u091F \u0915\u0928\u0947\u0915\u094D\u0936\u0928 \u0909\u092A\u0932\u092C\u094D\u0927 \u0928\u0939\u0940\u0902 \u0939\u0948\u0964\n\u0906\u092A\u0915\u0947 \u092C\u0926\u0932\u093E\u0935 \u0938\u0939\u0947\u091C\u0947 \u0928\u0939\u0940\u0902 \u091C\u093E\u090F\u0902\u0917\u0947!"
};
var errors = {
  unsupportedFileType: "\u0905\u0938\u092E\u0930\u094D\u0925\u093F\u0924 \u092B\u093E\u0907\u0932 \u092A\u094D\u0930\u0915\u093E\u0930",
  imageInsertError: "\u091B\u0935\u093F \u0938\u092E\u094D\u092E\u093F\u0932\u093F\u0924 \u0928\u0939\u0940\u0902 \u0915\u0940 \u091C\u093E \u0938\u0915\u0940. \u092A\u0941\u0928\u0903 \u092A\u094D\u0930\u092F\u0924\u094D\u0928 \u0915\u0930\u0947...",
  fileTooBig: "\u092B\u093C\u093E\u0907\u0932 \u091C\u093C\u0930\u0942\u0930\u0924 \u0938\u0947 \u091C\u093C\u094D\u092F\u093E\u0926\u093E \u092C\u0921\u093C\u0940 \u0939\u0948\u0902. \u0905\u0927\u093F\u0915\u0924\u092E \u0905\u0928\u0941\u092E\u093F\u0924 \u092A\u0930\u093F\u092E\u093E\u0923 {{maxSize}} \u0939\u0948\u0902",
  svgImageInsertError: "\u090F\u0938\u0935\u0940\u091C\u0940 \u091B\u0935\u093F \u0938\u092E\u094D\u092E\u093F\u0932\u093F\u0924 \u0928\u0939\u0940\u0902 \u0915\u0930 \u0938\u0915\u0947, \u090F\u0938\u0935\u0940\u091C\u0940 \u0930\u091A\u0928\u093E \u0905\u0928\u0941\u091A\u093F\u0924 \u0939\u0948\u0902",
  failedToFetchImage: "",
  invalidSVGString: "\u0905\u0928\u0941\u091A\u093F\u0924 SVG",
  cannotResolveCollabServer: "\u0915\u0949\u0932\u0947\u092C \u0938\u0930\u094D\u0935\u0930 \u0938\u0947 \u0915\u0928\u0947\u0915\u094D\u0936\u0928 \u0928\u0939\u0940\u0902 \u0939\u094B \u092A\u093E \u0930\u0939\u093E. \u0915\u0943\u092A\u092F\u093E \u092A\u0943\u0937\u094D\u0920 \u0915\u094B \u092A\u0941\u0928\u0903 \u0932\u093E\u0928\u0947 \u0915\u093E \u092A\u094D\u0930\u092F\u093E\u0938 \u0915\u0930\u0947.",
  importLibraryError: "\u0938\u0902\u0917\u094D\u0930\u0939 \u092A\u094D\u0930\u0924\u093F\u0937\u094D\u0920\u093E\u092A\u093F\u0924 \u0928\u0939\u0940\u0902 \u0915\u093F\u092F\u093E \u091C\u093E \u0938\u0915\u093E",
  collabSaveFailed: "\u0915\u093F\u0938\u0940 \u0915\u093E\u0930\u0923 \u0935\u0936 \u0905\u0902\u0926\u0930\u0942\u0928\u0940 \u0921\u0947\u091F\u093E\u092C\u0947\u0938 \u092E\u0947\u0902 \u0938\u0939\u0947\u091C\u093E \u0928\u0939\u0940\u0902 \u091C\u093E \u0938\u0915\u093E\u0964 \u092F\u0926\u093F \u0938\u092E\u0938\u094D\u092F\u093E \u092C\u0928\u0940 \u0930\u0939\u0924\u0940 \u0939\u0948, \u0924\u094B \u0915\u093F\u092F\u0947 \u0915\u093E\u092E \u0915\u094B \u0916\u094B\u0928\u0947 \u0928 \u0926\u0947\u0928\u0947 \u0915\u0947 \u0932\u093F\u092F\u0947 \u0905\u092A\u0928\u0940 \u092B\u093C\u093E\u0907\u0932 \u0915\u094B \u0938\u094D\u0925\u093E\u0928\u0940\u092F \u0930\u0942\u092A \u0938\u0947 \u0938\u0939\u0947\u091C\u0947\u0964",
  collabSaveFailed_sizeExceeded: "\u0932\u0917\u0924\u093E \u0939\u0948 \u0915\u093F \u092A\u0943\u0937\u094D\u0920 \u0924\u0932 \u0915\u093E\u092B\u093C\u0940 \u092C\u0921\u093C\u093E \u0939\u0948, \u0907\u0938\u094D\u0915\u093E\u0930\u0923 \u0905\u0902\u0926\u0930\u0942\u0928\u0940 \u0921\u0947\u091F\u093E\u092C\u0947\u0938 \u092E\u0947\u0902 \u0938\u0939\u0947\u091C\u093E \u0928\u0939\u0940\u0902 \u091C\u093E \u0938\u0915\u093E\u0964 \u0915\u093F\u092F\u0947 \u0915\u093E\u092E \u0915\u094B \u0916\u094B\u0928\u0947 \u0928 \u0926\u0947\u0928\u0947 \u0915\u0947 \u0932\u093F\u092F\u0947 \u0905\u092A\u0928\u0940 \u092B\u093C\u093E\u0907\u0932 \u0915\u094B \u0938\u094D\u0925\u093E\u0928\u0940\u092F \u0930\u0942\u092A \u0938\u0947 \u0938\u0939\u0947\u091C\u0947\u0964",
  imageToolNotSupported: "\u092A\u094D\u0930\u0924\u093F\u092E\u093E\u092F\u0947\u0902 \u0905\u0915\u094D\u0937\u092E \u0915\u0940 \u0917\u092F\u0940 \u0939\u0948\u0902",
  brave_measure_text_error: {
    line1: "\u0932\u0917\u0924\u093E \u0939\u0948 \u0915\u093F \u0906\u092A Brave \u092C\u094D\u0930\u093E\u0909\u091C\u093C\u0930 \u0915\u093E \u0909\u092A\u092F\u094B\u0917 \u0915\u0930 \u0930\u0939\u0947 \u0914\u0930 \u0938\u093E\u0925 \u092E\u0947\u0902 <bold>\u0906\u0915\u094D\u0930\u093E\u092E\u0915 \u0909\u0901\u0917\u0932\u093F\u092F\u094B \u0915\u0947 \u091B\u093E\u092A </bold> \u0915\u093E \u091A\u092F\u0928 \u0915\u093F\u092F\u093E \u0939\u0941\u0935\u093E \u0939\u0948",
    line2: "\u092F\u0939 \u0906\u092A\u0915\u0947 \u091A\u093F\u0924\u094D\u0930\u094B\u0902 \u0915\u0947 <bold>\u092A\u093E\u0920 \u0924\u0924\u094D\u0935\u094B\u0902</bold>\u0915\u094B \u0916\u0902\u0921\u093F\u0924 \u0915\u0930 \u0938\u0915\u0924\u093E \u0939\u0948\u0902",
    line3: "\u0939\u092E\u0947\u0902 \u0906\u092A\u0938\u0947 \u0920\u094B\u0938 \u0906\u0917\u094D\u0930\u0939 \u0939\u0948 \u0915\u0940 \u0906\u092A \u0938\u0947\u091F\u094D\u091F\u093F\u0902\u0917 \u092E\u0947\u0902 \u0907\u0938 \u0935\u093F\u0915\u0932\u094D\u092A \u0915\u093E \u091A\u092F\u0928 \u0928\u093E \u0915\u0930\u0947.<link> \u0907\u0938 \u0905\u0928\u0941\u0915\u094D\u0930\u092E </link> \u0915\u093E \u092A\u093E\u0932\u0928 \u0915\u0930\u0915\u0947 \u0907\u0938\u0915\u093E \u092A\u0924\u093E \u0932\u0917\u093E \u0938\u0915\u0924\u0947 \u0939\u0948\u0902",
    line4: "\u092F\u0926\u093F \u0907\u0938 \u0938\u0947\u091F\u093F\u0902\u0917\u094D\u0938 \u0915\u094B \u0905\u0915\u094D\u0937\u092E \u0915\u0930\u0928\u0947 \u092A\u0930 \u092D\u0940 \u092A\u0943\u0937\u094D\u0920 \u0920\u0940\u0915 \u0928\u0939\u0940\u0902 \u0926\u093F\u0916\u0924\u093E \u0939\u094B \u0924\u094B, \u0939\u092E\u093E\u0930\u0947 GitHub \u092A\u0930 \u090F\u0915 <issueLink>\u092E\u0941\u0926\u094D\u0926\u093E \u092A\u094D\u0930\u0938\u094D\u0924\u0941\u0924</issueLink> \u0915\u0930\u0947, \u092F\u093E \u0939\u092E\u0947\u0902 <discordLink>\u0921\u093F\u0938\u094D\u0915\u094B\u0930\u094D\u0921</discordLink> \u092A\u0930 \u0932\u093F\u0916\u093F\u0924 \u0938\u092E\u094D\u092A\u0930\u094D\u0915 \u0915\u0930\u0947\u0902"
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "\u0906\u092F\u092B\u093C\u094D\u0930\u0947\u092E \u0924\u0924\u094D\u0935 \u0938\u092E\u0942\u0939\u0915\u094B\u0937 \u092E\u0947\u0902 \u091C\u094B\u0921\u093E \u0928\u0939\u0940\u0902 \u091C\u093E \u0938\u0915\u093E.",
    image: ""
  },
  asyncPasteFailedOnRead: "\u091A\u093F\u092A\u0915\u093E\u092F\u093E \u0928\u0939\u0940\u0902 \u091C\u093E \u0938\u0915\u093E (\u0938\u093F\u0938\u094D\u091F\u092E \u0915\u094D\u0932\u093F\u092A\u092C\u094B\u0930\u094D\u0921 \u0938\u0947 \u092A\u0922\u093C\u093E \u0928\u0939\u0940\u0902 \u091C\u093E \u0938\u0915\u093E).",
  asyncPasteFailedOnParse: "\u091A\u093F\u092A\u0915\u093E\u092F\u093E \u0928\u0939\u0940\u0902 \u091C\u093E \u0938\u0915\u093E.",
  copyToSystemClipboardFailed: "\u0915\u094D\u0932\u093F\u092A\u092C\u094B\u0930\u094D\u0921 \u092A\u0930 \u092A\u094D\u0930\u0924\u093F\u0932\u093F\u092A\u093F \u0928\u0939\u0940\u0902 \u092C\u0928\u093E\u0908 \u091C\u093E \u0938\u0915\u0940."
};
var toolBar = {
  selection: "\u091A\u092F\u0928",
  image: "\u091B\u0935\u093F \u0938\u092E\u094D\u092E\u093F\u0932\u093F\u0924 \u0915\u0930\u0947\u0902",
  rectangle: "\u0906\u092F\u093E\u0924",
  diamond: "\u0924\u093F\u0930\u094D\u092F\u0917\u094D\u0935\u0930\u094D\u0917",
  ellipse: "\u0926\u0940\u0930\u094D\u0918\u0935\u0943\u0924\u094D\u0924",
  arrow: "\u0924\u0940\u0930",
  line: "\u0930\u0947\u0916\u093E",
  freedraw: "\u091A\u093F\u0924\u094D\u0930\u093E\u0902\u0924\u093F\u0924 \u0915\u0930\u0947",
  text: "\u092A\u093E\u0920",
  library: "\u0932\u093E\u0907\u092C\u094D\u0930\u0947\u0930\u0940",
  lock: "\u0921\u094D\u0930\u093E\u0907\u0902\u0917 \u0915\u0947 \u092C\u093E\u0926 \u091A\u092F\u0928\u093F\u0924 \u091F\u0942\u0932 \u0915\u094B \u0938\u0915\u094D\u0930\u093F\u092F \u0930\u0916\u0947\u0902",
  penMode: "\u092A\u0947\u0928 \u0915\u093E \u092E\u094B\u0921 - \u0938\u094D\u092A\u0930\u094D\u0936 \u091F\u093E\u0932\u0947",
  link: "",
  eraser: "\u0930\u092C\u0921\u093C",
  frame: "",
  magicframe: "\u0924\u093E\u0930\u093F\u0915 \u0922\u093E\u0901\u091A\u0947\u0902 \u0915\u094B \u0938\u093E\u0902\u0915\u0947\u0924\u093F\u0915 \u0932\u093F\u092A\u093F \u092E\u0947\u0902",
  embeddable: "",
  laser: "\u0932\u0947\u0938\u0930 \u091F\u0949\u0930\u094D\u091A",
  hand: "\u0939\u093E\u0925 ( \u0916\u093F\u0938\u0915\u093E\u0928\u0947 \u0915\u093E \u0914\u091C\u093C\u093E\u0930)",
  extraTools: "",
  mermaidToExcalidraw: "\u092E\u0930\u094D\u092E\u0947\u0921 \u0938\u0947 \u090F\u0915\u094D\u0938\u0915\u093E\u0932\u0940 \u092E\u0947\u0902",
  magicSettings: "\u0915\u0943\u0924\u093F\u092E \u092C\u0941\u0926\u094D\u0927\u093F\u092E\u0924\u094D\u0924\u093E \u0938\u0947\u091F\u093F\u0902\u0917\u094D\u0938"
};
var headings = {
  canvasActions: "\u0915\u0948\u0928\u0935\u093E\u0938 \u0915\u094D\u0930\u093F\u092F\u093E",
  selectedShapeActions: "\u091A\u092F\u0928\u093F\u0924 \u0906\u0915\u0943\u0924\u093F \u0915\u094D\u0930\u093F\u092F\u093E\u090F\u0902",
  shapes: "\u0906\u0915\u0943\u0924\u093F\u092F\u093E\u0901"
};
var hints = {
  canvasPanning: "\u0915\u0948\u0928\u0935\u093E\u0938 \u0915\u094B \u0938\u0930\u0915\u093E\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F, \u0921\u094D\u0930\u0948\u0917 \u0915\u0930\u0924\u0947 \u0938\u092E\u092F \u092E\u093E\u0909\u0938 \u0935\u094D\u0939\u0940\u0932 \u0915\u094B \u092A\u0915\u0921\u093C\u0947 \u0930\u0916\u0947 \u092F\u093E \u0938\u094D\u092A\u0947\u0938\u092C\u093E\u0930 \u0915\u094B \u0926\u092C\u093E\u090F \u0930\u0916\u0947, \u0905\u0925\u0935\u093E \u0939\u093E\u0925 \u0935\u093E\u0932\u0947 \u0914\u091C\u093C\u093E\u0930 \u0915\u093E \u0909\u092A\u092F\u094B\u0917 \u0915\u0930\u0947\u0902",
  linearElement: "\u0915\u0908 \u092C\u093F\u0902\u0926\u0941\u0913\u0902 \u0915\u094B \u0936\u0941\u0930\u0942 \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F \u0915\u094D\u0932\u093F\u0915 \u0915\u0930\u0947\u0902, \u0938\u093F\u0902\u0917\u0932 \u0932\u093E\u0907\u0928 \u0915\u0947 \u0932\u093F\u090F \u0916\u0940\u0902\u091A\u0947\u0902",
  freeDraw: "\u0915\u094D\u0932\u093F\u0915 \u0915\u0930\u0947\u0902 \u0914\u0930 \u0916\u0940\u0902\u091A\u0947\u0902\u0964 \u0938\u092E\u093E\u092A\u094D\u0924 \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F, \u091B\u094B\u0921\u093C\u094B",
  text: "\u0906\u092A \u091A\u092F\u0928 \u091F\u0942\u0932 \u0938\u0947 \u0915\u0939\u0940\u0902 \u092D\u0940 \u0921\u092C\u0932-\u0915\u094D\u0932\u093F\u0915 \u0915\u0930\u0915\u0947 \u091F\u0947\u0915\u094D\u0938\u094D\u091F \u091C\u094B\u0921\u093C \u0938\u0915\u0924\u0947 \u0939\u0948\u0902",
  embeddable: "",
  text_selected: "",
  text_editing: "",
  linearElementMulti: "\u0905\u0902\u0924\u093F\u092E \u092C\u093F\u0902\u0926\u0941 \u092A\u0930 \u0915\u094D\u0932\u093F\u0915 \u0915\u0930\u0947\u0902 \u092F\u093E \u0938\u092E\u093E\u092A\u094D\u0924 \u0939\u094B\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F \u090F\u0938\u094D\u0915\u0947\u092A \u092F\u093E \u090F\u0902\u091F\u0930 \u0926\u092C\u093E\u090F\u0902",
  lockAngle: "\u0906\u092A \u0918\u0942\u0930\u094D\u0923\u0928 \u0915\u0930\u0924\u0947 \u0938\u092E\u092F SHIFT \u092A\u0915\u0921\u093C\u0915\u0930 \u0915\u094B\u0923\u094B\u0902 \u0915\u094B \u092E\u094B\u0921\u093C \u0938\u0915\u0924\u0947 \u0939\u0948\u0902",
  resize: "\u0906\u0915\u093E\u0930 \u092C\u0926\u0932\u0924\u0947 \u0938\u092E\u092F \u0906\u092A SHIFT \u0915\u094B \u092A\u0915\u0921\u093C \u0915\u0930 \u0905\u0928\u0941\u092A\u093E\u0924 \u092E\u0947\u0902 \u0915\u092E\u0940 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902,\n\u0915\u0947\u0902\u0926\u094D\u0930 \u0938\u0947 \u0906\u0915\u093E\u0930 \u092C\u0926\u0932\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F ALT \u0926\u092C\u093E\u090F \u0930\u0916\u0947\u0902",
  resizeImage: "",
  rotate: "\u0906\u092A \u0918\u0942\u0930\u094D\u0923\u0928 \u0915\u0930\u0924\u0947 \u0938\u092E\u092F SHIFT \u092A\u0915\u0921\u093C\u0915\u0930 \u0915\u094B\u0923\u094B\u0902 \u0915\u094B \u0935\u093F\u0935\u0936 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902",
  lineEditor_info: "\u092C\u093F\u0902\u0926\u0941\u0913\u0902 \u0915\u094B \u0938\u092E\u094D\u092A\u093E\u0926\u093F\u0924 \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F CtrlOrCmd \u0915\u094B \u0926\u092C\u093E\u092F\u0947\u0902 \u0930\u0916\u0924\u0947 \u0939\u0941\u092F\u0947 \u0921\u092C\u0932 \u0915\u094D\u0932\u093F\u0915 \u0915\u0930\u0947, \u0905\u0925\u0935\u093E CtrlOrCmd + Enter \u0938\u093E\u0925 \u0926\u092C\u093E\u092F\u0947",
  lineEditor_pointSelected: "",
  lineEditor_nothingSelected: "",
  placeImage: "",
  publishLibrary: "",
  bindTextToElement: "",
  deepBoxSelect: "",
  eraserRevert: "\u092E\u093F\u091F\u093E\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F \u091A\u0941\u0928\u0947 \u0939\u0941\u090F \u091A\u0940\u091C\u094B\u0902 \u0915\u094B \u0928\u093E \u091A\u0941\u0928\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F Alt \u0938\u093E\u0925 \u092E\u0947\u0902 \u0926\u092C\u093E\u090F",
  firefox_clipboard_write: '"dom.events.asyncClipboard.clipboardItem" \u092B\u093C\u094D\u0932\u0948\u0917 \u0915\u094B "true" \u092A\u0930 \u0938\u0947\u091F \u0915\u0930\u0915\u0947 \u0907\u0938 \u0938\u0941\u0935\u093F\u0927\u093E \u0915\u094B \u0938\u0902\u092D\u0935\u0924\u0903 \u0938\u0915\u094D\u0937\u092E \u0915\u093F\u092F\u093E \u091C\u093E \u0938\u0915\u0924\u093E \u0939\u0948\u0964 Firefox \u092E\u0947\u0902 \u092C\u094D\u0930\u093E\u0909\u091C\u093C\u0930 \u092B\u093C\u094D\u0932\u0948\u0917 \u092C\u0926\u0932\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F, "about:config" \u092A\u0943\u0937\u094D\u0920 \u092A\u0930 \u091C\u093E\u090F\u0901\u0964',
  disableSnapping: "\u0938\u094D\u0928\u0948\u092A\u093F\u0902\u0917 \u0915\u094B \u0928\u093F\u0937\u094D\u0915\u094D\u0930\u093F\u092F \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F CtrlOrCmd \u0926\u092C\u093E\u090F \u0930\u0916\u0947\u0902"
};
var canvasError = {
  cannotShowPreview: "\u092A\u0942\u0930\u094D\u0935\u093E\u0935\u0932\u094B\u0915\u0928 \u0928\u0939\u0940\u0902 \u0926\u093F\u0916\u093E \u0938\u0915\u0924\u0947 \u0939\u0948\u0902",
  canvasTooBig: "\u0915\u0948\u0928\u0935\u093E\u0938 \u092C\u0939\u0941\u0924 \u092C\u0921\u093C\u093E",
  canvasTooBigTip: "\u0915\u0948\u0928\u0935\u093E\u0938 \u092C\u0939\u0941\u0924 \u092C\u0921\u093C\u093E \u091F\u093F\u092A"
};
var errorSplash = {
  headingMain: "\u090F\u0915 \u0924\u094D\u0930\u0941\u091F\u093F \u0915\u093E \u0938\u093E\u092E\u0928\u093E \u0915\u0930\u0928\u093E \u092A\u0921\u093C\u093E\u0964 \u092A\u094D\u0930\u092F\u0924\u094D\u0928 <button>\u0907\u0938 \u092A\u0943\u0937\u094D\u0920 \u0915\u094B \u092A\u0941\u0928\u0903 \u0932\u094B\u0921 \u0915\u0930\u0947\u0902</button>",
  clearCanvasMessage: "\u092F\u0926\u093F \u092A\u0941\u0928\u0903 \u0932\u094B\u0921 \u0915\u0930\u0928\u093E \u0915\u093E\u092E \u0928\u0939\u0940\u0902 \u0915\u0930\u0924\u093E \u0939\u0948, \u0924\u094B \u092A\u094D\u0930\u092F\u093E\u0938 \u0915\u0930\u0947\u0902 <button>\u0915\u0948\u0928\u0935\u093E\u0938 \u0938\u093E\u092B \u0915\u0930\u0928\u093E\u0964</button>",
  clearCanvasCaveat: " \u0907\u0938\u0938\u0947 \u0915\u093E\u092E \u0915\u093E \u0928\u0941\u0915\u0938\u093E\u0928 \u0939\u094B\u0917\u093E ",
  trackedToSentry: "\u092A\u0939\u091A\u093E\u0928\u0915\u0930\u094D\u0924\u093E \u0915\u0947 \u0938\u093E\u0925 \u0924\u094D\u0930\u0941\u091F\u093F {{eventId}} \u0939\u092E\u093E\u0930\u0947 \u0938\u093F\u0938\u094D\u091F\u092E \u092A\u0930 \u0928\u091C\u093C\u0930 \u0930\u0916\u0940 \u0917\u0908 \u0925\u0940\u0964",
  openIssueMessage: "\u0939\u092E \u092C\u0939\u0941\u0924 \u0938\u0924\u0930\u094D\u0915 \u0925\u0947 \u0915\u093F \u0924\u094D\u0930\u0941\u091F\u093F \u092A\u0930 \u0906\u092A\u0915\u0940 \u0926\u0943\u0936\u094D\u092F \u091C\u093E\u0928\u0915\u093E\u0930\u0940 \u0936\u093E\u092E\u093F\u0932 \u0928 \u0915\u0930\u0947\u0902\u0964 \u092F\u0926\u093F \u0906\u092A\u0915\u093E \u0926\u0943\u0936\u094D\u092F \u0928\u093F\u091C\u0940 \u0928\u0939\u0940\u0902 \u0939\u0948, \u0924\u094B \u0915\u0943\u092A\u092F\u093E \u0939\u092E\u093E\u0930\u0947 \u092C\u093E\u0930\u0947 \u092E\u0947\u0902 \u0935\u093F\u091A\u093E\u0930 \u0915\u0930\u0947\u0902 <button>\u092C\u0917 \u091F\u094D\u0930\u0948\u0915\u0930</button> \u0915\u0943\u092A\u092F\u093E GitHub \u092E\u0941\u0926\u094D\u0926\u0947 \u0915\u094B \u0915\u0949\u092A\u0940 \u0914\u0930 \u092A\u0947\u0938\u094D\u091F \u0915\u0930\u0915\u0947 \u0928\u0940\u091A\u0947 \u0926\u0940 \u0917\u0908 \u091C\u093E\u0928\u0915\u093E\u0930\u0940 \u0936\u093E\u092E\u093F\u0932 \u0915\u0930\u0947\u0902\u0964",
  sceneContent: "\u0926\u0943\u0936\u094D\u092F \u0938\u093E\u092E\u0917\u094D\u0930\u0940:"
};
var roomDialog = {
  desc_intro: "\u0906\u092A \u0905\u092A\u0928\u0947 \u0935\u0930\u094D\u0924\u092E\u093E\u0928 \u0926\u0943\u0936\u094D\u092F \u0915\u0947 \u0932\u094B\u0917\u094B\u0902 \u0915\u094B \u0905\u092A\u0928\u0947 \u0938\u093E\u0925 \u0938\u0939\u092F\u094B\u0917 \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F \u0906\u092E\u0902\u0924\u094D\u0930\u093F\u0924 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964",
  desc_privacy: "\u091A\u093F\u0902\u0924\u093E \u0928 \u0915\u0930\u0947\u0902, \u0938\u0924\u094D\u0930 \u0905\u0902\u0924-\u0938\u0947-\u0905\u0902\u0924 \u090F\u0928\u094D\u0915\u094D\u0930\u093F\u092A\u094D\u0936\u0928 \u0915\u093E \u0909\u092A\u092F\u094B\u0917 \u0915\u0930\u0924\u093E \u0939\u0948, \u0907\u0938\u0932\u093F\u090F \u0906\u092A \u091C\u094B \u092D\u0940 \u0921\u094D\u0930\u093E \u0915\u0930\u0947\u0902\u0917\u0947 \u0935\u0939 \u0928\u093F\u091C\u0940 \u0930\u0939\u0947\u0917\u093E\u0964 \u092F\u0939\u093E\u0902 \u0924\u0915 \u0915\u093F \u0939\u092E\u093E\u0930\u093E \u0938\u0930\u094D\u0935\u0930 \u092D\u0940 \u0928\u0939\u0940\u0902 \u0926\u0947\u0916 \u092A\u093E\u090F\u0917\u093E \u0915\u093F \u0906\u092A \u0915\u094D\u092F\u093E \u0915\u0930 \u0930\u0939\u0947 \u0939\u0948\u0902\u0964",
  button_startSession: "\u0938\u0924\u094D\u0930 \u092A\u094D\u0930\u093E\u0930\u0902\u092D \u0915\u0930\u0947\u0902",
  button_stopSession: "\u0938\u0924\u094D\u0930 \u0930\u0941\u0915\u0947\u0902",
  desc_inProgressIntro: "\u0932\u093E\u0907\u0935 \u0938\u0939\u092F\u094B\u0917 \u0938\u0924\u094D\u0930 \u0905\u092C \u091C\u093E\u0930\u0940 \u0939\u0948\u0964",
  desc_shareLink: "\u0907\u0938 \u0932\u093F\u0902\u0915 \u0915\u094B \u0906\u092A \u091C\u093F\u0938 \u0915\u093F\u0938\u0940 \u0915\u0947 \u0938\u093E\u0925 \u092D\u0940 \u0938\u0939\u092F\u094B\u0917 \u0915\u0930\u0928\u093E \u091A\u093E\u0939\u0924\u0947 \u0939\u0948\u0902, \u0909\u0938\u0915\u0947 \u0938\u093E\u0925 \u0938\u093E\u091D\u093E \u0915\u0930\u0947\u0902",
  desc_exitSession: "\u0938\u0924\u094D\u0930 \u0930\u094B\u0915\u0928\u093E \u0906\u092A\u0915\u094B \u0930\u0942\u092E \u0938\u0947 \u092C\u093E\u0939\u0930 \u0915\u0930 \u0926\u0947\u0917\u093E, \u0932\u0947\u0915\u093F\u0928 \u0906\u092A \u0938\u094D\u0925\u093E\u0928\u0940\u092F \u0938\u094D\u0924\u0930 \u092A\u0930 \u0926\u0943\u0936\u094D\u092F \u0915\u0947 \u0938\u093E\u0925 \u0915\u093E\u092E \u0915\u0930\u0928\u093E \u091C\u093E\u0930\u0940 \u0930\u0916 \u092A\u093E\u090F\u0902\u0917\u0947\u0964 \u0927\u094D\u092F\u093E\u0928 \u0926\u0947\u0902 \u0915\u093F \u092F\u0939 \u0905\u0928\u094D\u092F \u0932\u094B\u0917\u094B\u0902 \u0915\u094B \u092A\u094D\u0930\u092D\u093E\u0935\u093F\u0924 \u0928\u0939\u0940\u0902 \u0915\u0930\u0947\u0917\u093E, \u0914\u0930 \u0935\u0947 \u0905\u092D\u0940 \u092D\u0940 \u0905\u092A\u0928\u0947 \u0938\u0902\u0938\u094D\u0915\u0930\u0923 \u092A\u0930 \u0938\u0939\u092F\u094B\u0917 \u0915\u0930\u0928\u0947 \u092E\u0947\u0902 \u0938\u0915\u094D\u0937\u092E \u0939\u094B\u0902\u0917\u0947\u0964",
  shareTitle: ""
};
var errorDialog = {
  title: "\u0917\u0932\u0924\u0940"
};
var exportDialog = {
  disk_title: "",
  disk_details: "",
  disk_button: "",
  link_title: "",
  link_details: "",
  link_button: "",
  excalidrawplus_description: "",
  excalidrawplus_button: "",
  excalidrawplus_exportError: ""
};
var helpDialog = {
  blog: "\u0939\u092E\u093E\u0930\u093E \u092C\u094D\u0932\u0949\u0917 \u092A\u0922\u0947",
  click: "\u0915\u094D\u0932\u093F\u0915 \u0915\u0930\u0947\u0902",
  deepSelect: "",
  deepBoxSelect: "",
  curvedArrow: "\u0935\u0915\u094D\u0930 \u0924\u0940\u0930",
  curvedLine: "\u0935\u0915\u094D\u0930 \u0930\u0947\u0916\u093E",
  documentation: "",
  doubleClick: "",
  drag: "\u0916\u0940\u0902\u091A\u0947\u0902",
  editor: "\u0938\u0902\u092A\u093E\u0926\u0915",
  editLineArrowPoints: "\u0930\u0947\u0916\u093E/\u0924\u0940\u0930 \u092C\u093F\u0902\u0926\u0941 \u0938\u092E\u094D\u092A\u093E\u0926\u093F\u0924 \u0915\u0930\u0947",
  editText: "\u092A\u093E\u0920\u094D\u092F \u0938\u092E\u094D\u092A\u093E\u0926\u093F\u0924 \u0915\u0930\u0947/ \u0932\u0947\u092C\u0932 \u091C\u094B\u0921\u093C\u0947",
  github: "\u092E\u0941\u0926\u094D\u0926\u093E \u092E\u093F\u0932\u093E? \u092A\u094D\u0930\u0938\u094D\u0924\u0941\u0924 \u0915\u0930\u0947\u0902",
  howto: "\u0939\u092E\u093E\u0930\u0947 \u0917\u093E\u0907\u0921 \u0915\u093E \u092A\u093E\u0932\u0928 \u0915\u0930\u0947\u0902",
  or: "\u092F\u093E",
  preventBinding: "\u0924\u0940\u0930 \u092C\u0902\u0927\u0928 \u0930\u094B\u0915\u0947\u0902",
  tools: "\u0914\u091C\u093C\u093E\u0930",
  shortcuts: "\u0915\u0940\u092C\u094B\u0930\u094D\u0921 \u0915\u0947 \u0936\u0949\u0930\u094D\u091F\u0915\u091F\u094D\u0938",
  textFinish: "",
  textNewLine: "",
  title: "\u092E\u0926\u0926",
  view: "\u0926\u0943\u0936\u094D\u092F",
  zoomToFit: "\u0938\u092D\u0940 \u0924\u0924\u094D\u0935\u094B\u0902 \u0915\u094B \u092B\u093F\u091F \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F \u091C\u093C\u0942\u092E \u0915\u0930\u0947\u0902",
  zoomToSelection: "\u091A\u092F\u0928 \u0924\u0915 \u095B\u0942\u092E \u0915\u0930\u0947",
  toggleElementLock: "\u0924\u093E\u0932\u0947 \u0915\u0947 \u0905\u0902\u0926\u0930/\u092C\u093E\u0939\u0930 \u091A\u0941\u0928\u093E\u0935",
  movePageUpDown: "\u092A\u0943\u0937\u094D\u0920 \u090A\u092A\u0930/\u0928\u0940\u091A\u0947 \u0915\u0930\u0947",
  movePageLeftRight: "\u092A\u0943\u0937\u094D\u0920 \u092C\u093E\u092F\u0940/\u0926\u093E\u092F\u0940 \u0924\u0930\u092B \u0915\u0930\u0947"
};
var clearCanvasDialog = {
  title: ""
};
var publishDialog = {
  title: "",
  itemName: "",
  authorName: "",
  githubUsername: "",
  twitterUsername: "",
  libraryName: "",
  libraryDesc: "",
  website: "",
  placeholder: {
    authorName: "",
    libraryName: "",
    libraryDesc: "",
    githubHandle: "",
    twitterHandle: "",
    website: ""
  },
  errors: {
    required: "",
    website: "\u092E\u093E\u0928\u094D\u092F URL \u092A\u094D\u0930\u0935\u093F\u0937\u094D\u091F \u0915\u0930\u0947\u0902"
  },
  noteDescription: "\u0938\u0902\u0917\u094D\u0930\u0939 \u0938\u092E\u094D\u092E\u093F\u0932\u093F\u0924 \u0915\u0930\u0928\u0947 \u0939\u0947\u0924\u0941 \u092A\u094D\u0930\u0938\u094D\u0924\u0941\u0924 \u0915\u0930\u0947\u0902 <link>\u0938\u093E\u0930\u094D\u0935\u091C\u0928\u093F\u0915 \u0938\u0902\u0917\u094D\u0930\u0939\u093E\u0932\u092F</link>\u0905\u0928\u094D\u092F \u0935\u0915\u094D\u0924\u093F\u092F\u094B\u0902 \u0915\u094B \u0909\u0928\u0915\u0947 \u091A\u093F\u0924\u094D\u0930\u0915\u093E\u0930\u0940 \u092E\u0947\u0902 \u0909\u092A\u092F\u094B\u0917 \u0915\u0947 \u0932\u093F\u092F\u0947",
  noteGuidelines: "\u0938\u0902\u0917\u094D\u0930\u0939 \u0915\u094B \u092A\u0939\u0932\u0947 \u0938\u094D\u0935\u0940\u0915\u0943\u0924\u093F \u0906\u0935\u0936\u094D\u092F\u0915 \u0915\u0943\u092A\u092F\u093E \u092F\u0939 \u092A\u0922\u093C\u0947\u0902 <link>\u0926\u093F\u0936\u093E-\u0928\u093F\u0930\u094D\u0926\u0947\u0936</link>",
  noteLicense: "\u091C\u092E\u093E \u0915\u0930\u0915\u0947, \u0906\u092A \u0938\u0939\u092E\u0924 \u0939\u0948\u0902 \u0915\u093F \u0938\u0902\u0917\u094D\u0930\u0939\u0923 \u0915\u094B <link>MIT \u0932\u093E\u0907\u0938\u0947\u0902\u0938</link> \u0915\u0947 \u0924\u0939\u0924 \u092A\u094D\u0930\u0915\u093E\u0936\u093F\u0924 \u0915\u093F\u092F\u093E \u091C\u093E\u090F\u0917\u093E, \u091C\u093F\u0938\u0915\u093E \u0938\u0902\u0915\u094D\u0937\u093F\u092A\u094D\u0924 \u0905\u0930\u094D\u0925 \u0939\u0948 \u0915\u093F \u0915\u094B\u0908 \u092D\u0940 \u092C\u093F\u0928\u093E \u0915\u093F\u0938\u0940 \u092A\u094D\u0930\u0924\u093F\u092C\u0902\u0927 \u0915\u0947 \u0909\u0928\u0915\u093E \u0909\u092A\u092F\u094B\u0917 \u0915\u0930 \u0938\u0915\u0924\u093E \u0939\u0948\u0964",
  noteItems: "",
  atleastOneLibItem: "",
  republishWarning: "\u091F\u093F\u092A\u094D\u092A\u0923\u0940: \u0915\u0941\u091B \u091A\u0941\u0928\u0947 \u0939\u0941\u0935\u0947 \u0906\u0907\u091F\u092E \u092A\u0939\u0932\u0947 \u0939\u0940 \u092A\u094D\u0930\u0915\u093E\u0936\u093F\u0924/\u092A\u094D\u0930\u0938\u094D\u0924\u0941\u0924 \u0915\u093F\u090F \u091C\u093E \u091A\u0941\u0915\u0947 \u0939\u0948\u0902\u0964 \u0915\u093F\u0938\u0940 \u092A\u094D\u0930\u0915\u093E\u0936\u093F\u0924 \u0938\u0902\u0917\u094D\u0930\u0939 \u0915\u094B \u0905\u0926\u094D\u092F\u0924\u0928 \u0915\u0930\u0924\u0947 \u0938\u092E\u092F \u092F\u093E \u092A\u0939\u0932\u0947 \u0938\u0947 \u092A\u094D\u0930\u0938\u094D\u0924\u0941\u0924 \u0906\u0907\u091F\u092E \u0915\u094B \u092A\u0941\u0928\u094D\u0939\u093E \u092A\u094D\u0930\u0938\u094D\u0924\u0941\u0924 \u0915\u0930\u0924\u0947 \u0938\u092E\u092F, \u0906\u092A \u092C\u0938 \u0909\u0938\u0947 \u0915\u0947\u0935\u0932 \u0905\u0926\u094D\u092F\u0924\u0928 \u0915\u0930\u0947\u0902 \u0964"
};
var publishSuccessDialog = {
  title: "",
  content: "{{authorName}} \u0927\u0928\u094D\u092F\u0935\u093E\u0926. \u0906\u092A\u0915\u093E \u0938\u0902\u0917\u094D\u0930\u0939\u0923 \u0938\u092E\u0940\u0915\u094D\u0937\u093E \u0915\u0947 \u0932\u093F\u090F \u0926\u0930\u094D\u091C \u0939\u094B \u091A\u0941\u0915\u093E \u0939\u0948. \u0938\u092E\u0940\u0915\u094D\u0937\u093E \u0938\u094D\u0925\u093F\u0924\u093F <link>\u092F\u0939\u093E\u0901</link>\u091C\u093E\u0928 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902."
};
var confirmDialog = {
  resetLibrary: "",
  removeItemsFromLib: ""
};
var imageExportDialog = {
  header: "",
  label: {
    withBackground: "",
    onlySelected: "",
    darkMode: "",
    embedScene: "",
    scale: "",
    padding: ""
  },
  tooltip: {
    embedScene: ""
  },
  title: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  },
  button: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  }
};
var encrypted = {
  tooltip: "\u0906\u092A\u0915\u0947 \u091A\u093F\u0924\u094D\u0930 \u0905\u0902\u0924-\u0938\u0947-\u0905\u0902\u0924 \u090F\u0928\u094D\u0915\u094D\u0930\u093F\u092A\u094D\u091F\u0947\u0921 \u0939\u0948\u0902, \u0907\u0938\u0932\u093F\u090F \u090F\u0915\u094D\u0938\u0915\u094D\u0932\u0942\u0938\u093F\u0935\u094D\u0930\u0949\u0935 \u0915\u0947 \u0938\u0930\u094D\u0935\u0930 \u0909\u0928\u094D\u0939\u0947\u0902 \u0915\u092D\u0940 \u0928\u0939\u0940\u0902 \u0926\u0947\u0916\u0947\u0902\u0917\u0947\u0964",
  link: ""
};
var stats = {
  angle: "\u0915\u094B\u0923",
  element: "\u090F\u0932\u093F\u092E\u0947\u0902\u091F",
  elements: "\u090F\u0932\u093F\u092E\u0947\u0902\u091F",
  height: "\u090A\u0902\u091A\u093E\u0908",
  scene: "\u0926\u0943\u0936\u094D\u092F",
  selected: "\u091A\u092F\u0928\u093F\u0924",
  storage: "\u0938\u0902\u0917\u094D\u0930\u0939",
  title: "\u092C\u0947\u0935\u0915\u0942\u092B \u0915\u0947 \u0932\u093F\u090F \u0906\u0901\u0915\u0921\u093C\u0947",
  total: "\u0915\u0941\u0932",
  version: "\u0938\u0902\u0938\u094D\u0915\u0930\u0923",
  versionCopy: "\u0915\u093E\u0945\u092A\u0940 \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F \u0915\u094D\u0932\u093F\u0915 \u0915\u0930\u0947\u0902",
  versionNotAvailable: "\u0938\u0902\u0938\u094D\u0915\u0930\u0923 \u0909\u092A\u0932\u092C\u094D\u0927 \u0928\u0939\u0940\u0902 \u0939\u0948",
  width: "\u091A\u094C\u0921\u093C\u093E\u0908"
};
var toast = {
  addedToLibrary: "",
  copyStyles: "\u0915\u093E\u0945\u092A\u0940 \u0915\u0940\u090F \u0938\u094D\u091F\u093E\u0907\u0932",
  copyToClipboard: "\u0915\u094D\u0932\u093F\u092A\u092C\u094B\u0930\u094D\u0921 \u092E\u0947\u0902 \u0915\u0949\u092A\u0940 \u0915\u0940\u090F",
  copyToClipboardAsPng: "",
  fileSaved: "",
  fileSavedToFilename: "",
  canvas: "",
  selection: "",
  pasteAsSingleElement: "\u090F\u0915 \u0905\u0935\u092F\u0935 \u0915\u0947 \u0930\u0942\u092A \u092E\u0947\u0902 \u091A\u093F\u092A\u0915\u093E\u0928\u0947 \u0915\u0947 \u0932\u093F\u090F {{shortcut}} \u0915\u093E \u0909\u092A\u092F\u094B\u0917 \u0915\u0930\u0947\u0902,\n\u092F\u093E \u0915\u093F\u0938\u0940 \u092E\u094C\u091C\u0942\u0926\u093E \u092A\u093E\u0920 \u0938\u0902\u092A\u093E\u0926\u0915 \u092E\u0947\u0902 \u091A\u093F\u092A\u0915\u093E\u092F\u0947\u0902",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "",
  black: "\u0915\u093E\u0932\u093E",
  white: "\u0938\u092B\u093C\u0947\u0926",
  red: "\u0932\u093E\u0932",
  pink: "\u0917\u0941\u0932\u093E\u092C\u0940",
  grape: "\u0905\u0902\u0917\u0942\u0930\u0940",
  violet: "\u091C\u093E\u092E\u0941\u0928\u0940",
  gray: "\u0917\u0939\u0930\u093E",
  blue: "\u0928\u0940\u0932\u093E",
  cyan: "\u0906\u0938\u092E\u093E\u0928\u0940",
  teal: "\u0939\u0930\u093E-\u0928\u0940\u0932\u093E",
  green: "\u0939\u0930\u093E",
  yellow: "\u092A\u0940\u0932\u093E",
  orange: "\u0928\u093E\u0930\u0902\u0917\u0940",
  bronze: "\u0915\u093E\u0902\u0938\u094D\u092F"
};
var welcomeScreen = {
  app: {
    center_heading: "\u0906\u092A\u0915\u093E \u0938\u0930\u094D\u0935 \u0921\u0947\u091F\u093E \u092C\u094D\u0930\u093E\u0909\u091C\u093C\u0930 \u0915\u0947 \u092D\u0940\u0924\u0930 \u0938\u094D\u0925\u093E\u0928\u093F\u0915 \u091C\u0917\u0939 \u092A\u0947 \u0938\u0941\u0930\u0915\u094D\u0937\u093F\u0924 \u0915\u093F\u092F\u093E \u0917\u092F\u093E.",
    center_heading_plus: "\u092C\u091C\u093E\u092F \u0906\u092A\u0915\u094B Excalidraw+ \u092A\u0930 \u091C\u093E\u0928\u093E \u0939\u0948?",
    menuHint: "\u0928\u093F\u0930\u094D\u092F\u093E\u0924, \u092A\u0938\u0902\u0926, \u092D\u093E\u0937\u093E\u092F\u0947\u0902, ..."
  },
  defaults: {
    menuHint: "\u0928\u093F\u0930\u094D\u092F\u093E\u0924, \u092A\u0938\u0902\u0926, \u0914\u0930 \u092D\u0940...",
    center_heading: "\u091A\u093F\u0924\u094D\u0930\u093E\u0902\u0915\u0928\u0964 \u092C\u0928\u093E\u092F\u093E \u0917\u092F\u093E\u0964 \u0938\u0930\u0932\u0964",
    toolbarHint: "\u090F\u0915 \u0914\u091C\u093E\u0930 \u091A\u0941\u0928\u0947 \u0914\u0930 \u091A\u093F\u0924\u094D\u0930\u0915\u093E\u0930\u0940 \u092A\u094D\u0930\u093E\u0930\u0902\u092D \u0915\u0930\u0947!",
    helpHint: "\u0936\u0949\u0930\u094D\u091F\u094D\u0915\u091F \u0914\u0930 \u0938\u0939\u093E\u092F\u094D\u092F"
  }
};
var colorPicker = {
  mostUsedCustomColors: "\u0905\u0927\u093F\u0915\u093E\u0902\u0936 \u0909\u092A\u092F\u094B\u0917\u093F\u0924 \u0930\u0902\u0917",
  colors: "\u0930\u0902\u0917",
  shades: "\u091B\u093E\u092F\u093E",
  hexCode: "\u0939\u0947\u0915\u094D\u0938 \u0915\u094B\u0921",
  noShades: "\u0907\u0938 \u0930\u0902\u0917 \u0915\u0940 \u0915\u094B\u0908 \u091B\u093E\u092F\u093E \u0909\u092A\u0932\u092C\u094D\u0927 \u0928\u0939\u0940\u0902 \u0939\u0948\u0902"
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "\u091B\u0935\u093F \u0938\u094D\u0935\u0930\u0942\u092A \u092E\u0947\u0902 \u0928\u093F\u0930\u094D\u092F\u093E\u0924 \u0915\u0930\u0947",
      button: "\u091B\u0935\u093F \u0938\u094D\u0935\u0930\u0942\u092A \u0928\u093F\u0930\u094D\u092F\u093E\u0924 \u0915\u0930\u0947",
      description: "\u0926\u0943\u0937\u094D\u092F \u0921\u0947\u091F\u093E \u091B\u0935\u093F \u0938\u094D\u0935\u0930\u0942\u092A \u092E\u0947\u0902 \u0928\u093F\u0930\u094D\u092F\u093E\u0924 \u0915\u0930\u0947, \u0909\u0938 \u0938\u094D\u0935\u0930\u0942\u092A \u0938\u0947 \u0906\u092A \u0909\u0938\u0947 \u092A\u0941\u0928\u0903 \u0906\u092F\u093E\u0924 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u094B"
    },
    saveToDisk: {
      title: "\u0921\u093F\u0938\u094D\u0915 \u092E\u0947\u0902 \u0938\u092E\u094D\u0939\u093E\u0932\u0947",
      button: "\u0921\u093F\u0938\u094D\u0915 \u092E\u0947\u0902 \u0938\u092E\u094D\u0939\u093E\u0932\u0947",
      description: "\u0926\u0943\u0937\u094D\u092F \u0921\u0947\u091F\u093E \u092C\u093E\u0939\u0930\u0940 \u092B\u093C\u093E\u0907\u0932 \u092E\u0947\u0902 \u0928\u093F\u0930\u094D\u092F\u093E\u0924 \u0915\u0930\u0947, \u091C\u0939\u093E\u0901\u0938\u0947 \u0906\u092A \u0909\u0938\u0947 \u092A\u0941\u0928\u0903 \u0906\u092F\u093E\u0924 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u094B"
    },
    excalidrawPlus: {
      title: "\u090F\u0915\u094D\u0937\u0915\u093E\u0932\u0940\u0921\u094D\u0930\u0949+",
      button: "\u090F\u0915\u094D\u0937\u0915\u093E\u0932\u0940\u0921\u094D\u0930\u0949+ \u092E\u0947\u0902 \u0928\u093F\u0930\u094D\u092F\u093E\u0924 \u0915\u0930\u0947",
      description: "\u0926\u0943\u0937\u094D\u092F \u0915\u094B \u0906\u092A\u0915\u0947 \u090F\u0915\u094D\u0937\u0915\u093E\u0932\u0940\u0921\u094D\u0930\u0949+ \u0915\u0947 \u0915\u0930\u094D\u092F\u0938\u094D\u0925\u0932 \u092E\u0947\u0902 \u0938\u092E\u094D\u0939\u093E\u0932\u0947"
    }
  },
  modal: {
    loadFromFile: {
      title: "\u092B\u093C\u093E\u0907\u0932 \u0938\u0947 \u0932\u094B\u0921 \u0915\u0930\u0947\u0902:",
      button: "\u092B\u093C\u093E\u0907\u0932 \u0938\u0947 \u0932\u094B\u0921 \u0915\u0930\u0947\u0902:",
      description: "\u092B\u093C\u093E\u0907\u0932 \u0938\u0947 \u0932\u094B\u0921 \u0915\u0930\u0928\u0947 \u092A\u0930 <bold>\u092F\u0939 \u0906\u092A\u0915\u0947 \u0915\u093E\u0930\u094D\u092F \u0915\u0940 \u091C\u0917\u0939 \u0932\u0947\u0932\u0947\u0917\u093E </bold><br></br>\u0906\u092A\u0915\u0940 \u0921\u094D\u0930\u0949\u0907\u0902\u0917 \u0928\u093F\u092E\u094D\u0928 \u0926\u0930\u094D\u0936\u093F\u0924 \u0935\u093F\u0915\u0932\u094D\u092A\u094B \u092E\u0947\u0902 \u0938\u0947 \u090F\u0915 \u091A\u0941\u0928\u0915\u0947 \u0914\u0930 \u0909\u092A\u092F\u094B\u0917 \u0915\u0930\u0915\u0947 \u0938\u092E\u094D\u0939\u093E\u0932 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902"
    },
    shareableLink: {
      title: "\u0932\u093F\u0902\u0915 \u0938\u0947 \u0932\u094B\u0921 \u0915\u0930\u0947\u0902:",
      button: "\u0907\u0938 \u091C\u0917\u0939 \u092A\u094D\u0930\u0924\u093F\u0938\u094D\u0925\u093E\u092A\u093F\u0924 \u0915\u0930\u0947",
      description: "\u092C\u093E\u0939\u0930 \u0915\u093E \u091A\u093F\u0924\u094D\u0930 \u0932\u094B\u0921 \u0915\u0930\u0928\u0947 \u092A\u0930 <bold>\u092F\u0939 \u0906\u092A\u0915\u0947 \u0915\u093E\u0930\u094D\u092F \u0915\u0940 \u091C\u0917\u0939 \u0932\u0947\u0932\u0947\u0917\u093E </bold><br></br>\u0906\u092A \u0906\u092A\u0915\u0940 \u0921\u094D\u0930\u0949\u0907\u0902\u0917 \u092A\u0939\u0932\u0947 \u0928\u093F\u092E\u094D\u0928 \u0926\u0930\u094D\u0936\u093F\u0924 \u0935\u093F\u0915\u0932\u094D\u092A\u094B \u092E\u0947\u0902 \u0938\u0947 \u090F\u0915 \u091A\u0941\u0928\u0915\u0947 \u0914\u0930 \u0909\u092A\u092F\u094B\u0917 \u0915\u0930\u0915\u0947 \u0938\u092E\u094D\u0939\u093E\u0932 \u0938\u0915\u0924\u0947 \u0939\u094B\u0902."
    }
  }
};
var mermaid = {
  title: "\u092E\u0930\u094D\u092E\u0947\u0921 \u0938\u0947 \u090F\u0915\u094D\u0938\u0915\u093E\u0932\u0940 \u092E\u0947\u0902",
  button: "\u0905\u0902\u0926\u0930 \u0921\u093E\u0932\u0947",
  description: "",
  syntax: "\u092E\u0930\u094D\u092E\u0947\u0921 \u0938\u0902\u0930\u091A\u0928\u093E \u0928\u093F\u092F\u092E",
  preview: "\u092A\u0942\u0930\u094D\u0935\u093E\u0935\u0932\u094B\u0915\u0928"
};
var hi_IN_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  hi_IN_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=hi-IN-ZHZNZWFC.js.map
