import{$ as Fn,$a as Oh,$b as Zh,$c as D,$d as bm,$e as Ib,$f as ni,$g as li,$h as pn,$i as pi,$j as op,A as To,Aa as kt,Ab as sa,Ac as af,Ad as rn,Ae as nb,Af as Gb,Ag as gx,Ah as vs,<PERSON> as tE,Aj as Qt,Ak as Ao,B as Wr,Ba as cr,Bb as Dl,Bc as lf,Bd as qn,Be as ib,Bf as Yb,Bg as hx,Bh as Dx,Bi as ko,Bj as Us,Bk as GE,C as ta,Ca as _h,Cb as Pl,Cc as DT,Cd as Af,Ce as ab,Cf as Vb,Cg as Ea,Ch as Px,Ci as oE,Cj as wE,D as Rn,Da as aa,Db as Hn,Dc as sf,Dd as sm,De as lb,Df as Wb,Dg as fx,Dh as Rx,<PERSON> as rE,Dj as T<PERSON>,<PERSON> as co,<PERSON><PERSON> as Lt,<PERSON><PERSON> as Vh,<PERSON>c as Ol,<PERSON> as Yl,<PERSON><PERSON> as sb,<PERSON>f as Kb,Eg as bx,<PERSON>h as wa,<PERSON>i as di,Ej as CE,F as Kr,<PERSON>a as Je,Fb as Gn,Fc as $n,Fd as _f,Fe as cb,Ff as Xb,Fg as sn,Fh as Fm,Fi as nE,Fj as IE,G as re,Ga as Mh,Gb as ca,Gc as $t,Gd as Mf,Ge as db,Gf as $b,Gg as xx,Gh as Nx,Gi as Ps,Gj as ui,H as mo,Ha as Dh,Hb as rt,Hc as Xo,Hd as Df,He as mb,Hf as Zb,Hg as Ex,Hh as Ox,Hi as iE,Hj as qm,I as Nn,Ia as ot,Ib as ur,Ic as cf,Id as fa,Ie as pb,If as jb,Ig as us,Ih as Fx,Ii as mi,Ij as SE,J as On,Ja as Xt,Jb as Rl,Jc as We,Jd as Pf,Je as ub,Jf as Jb,Jg as cn,Jh as Bm,Ji as Rs,Jj as Qm,K as yl,Ka as Ph,Kb as Io,Kc as Fl,Kd as Rf,Ke as gb,Kf as qb,Kg as gs,Kh as ws,Ki as aE,Kj as kE,L as Vd,La as Jd,Lb as Wh,Lc as df,Ld as Qn,Le as hb,Lf as os,Lg as ya,Lh as zm,Li as Ns,Lj as LE,M as vl,Ma as Bn,Mb as Kh,Mc as po,Md as Nf,Me as fb,Mf as Qb,Mg as Zo,Mh as Um,Mi as Lo,Mj as fo,N as uh,Na as zn,Nb as At,Nc as mf,Nd as Vl,Ne as nn,Nf as ex,Ng as yx,Nh as Bx,Ni as Vm,Nj as Jo,O as oa,Oa as qd,Ob as Yn,Oc as pf,Od as Wl,Oe as Xl,Of as Sm,Og as Er,Oh as zx,Oi as lE,Oj as bo,P as gh,Pa as Al,Pb as Ae,Pc as Zn,Pd as Of,Pe as bb,Pf as rs,Pg as Jt,Ph as Ux,Pi as Os,Pj as P,Q as hh,Qa as Qd,Qb as Xh,Qc as Le,Qd as Ff,Qe as xb,Qf as br,Qg as Lm,Qh as Hx,Qi as Fs,Qj as q,R as Wd,Ra as em,Rb as $h,Rc as uf,Rd as cm,Re as Eb,Rf as $o,Rg as Am,Rh as Hm,Ri as sE,Rj as ep,S as ra,Sa as Rh,Sb as Vn,Sc as gf,Sd as dm,Se as yb,Sf as ho,Sg as hs,Sh as Gx,Si as cE,Sj as hn,T as je,Ta as tm,Tb as da,Tc as hf,Td as Bf,Te as vb,Tf as km,Tg as fs,Th as Yx,Ti as dE,Tj as ka,U as wl,Ua as z,Ub as im,Uc as Bl,Ud as zf,Ue as wb,Uf as tx,Ug as vx,Uh as Ts,Ui as zT,Uj as gi,V as fh,Va as bt,Vb as qr,Vc as ff,Vd as mm,Ve as Tb,Vf as ri,Vg as qt,Vh as Ta,Vi as vr,Vj as tp,W as bh,Wa as dr,Wb as xe,Wc as hr,Wd as pm,We as $l,Wf as ba,Wg as L,Wh as Cs,Wi as Ia,Wj as AE,X as xh,Xa as om,Xb as Et,Xc as uo,Xd as um,Xe as Cb,Xf as So,Xg as wx,Xh as Is,Xi as Wm,Xj as Hs,Y as Eh,Ya as mr,Yb as Qr,Yc as zl,Yd as gm,Ye as Zl,Yf as ox,Yg as _m,Yh as Ss,Yi as Km,Yj as _E,Z as yh,Za as Nh,Zb as Wn,Zc as Ul,Zd as hm,Ze as Cm,Zf as rx,Zg as dn,Zh as ks,Zi as Xm,Zj as ME,_ as vh,_a as rm,_b as Y,_c as bf,_d as fm,_e as ei,_f as ns,_g as Tx,_h as Ls,_i as $m,_j as Gs,a as oh,aa as Kd,ab as Co,ac as Nl,ad as Zt,ae as xm,af as Sb,ag as is,ah as Mm,ai as Vx,aj as yt,ak as rp,b as Yr,ba as Xd,bb as Xr,bc as ie,bd as Te,be as Uf,bf as kb,bg as xr,bh as Cx,bi as Wx,bj as Se,bk as np,c as rh,ca as wh,cb as lt,cc as he,cd as xf,ce as Hf,cf as Lb,cg as as,ch as mn,ci as Kx,cj as Bt,ck as DE,d as nh,da as Th,db as $r,dc as Ne,dd as go,de as Em,df as Ab,dg as ls,dh as Ix,di as Xx,dj as Zm,dk as PE,e as Ho,ea as Tl,eb as Un,ec as ee,ed as fr,ee as ym,ef as _b,eg as nx,eh as si,ei as As,ej as wr,ek as RE,f as hl,fa as Cl,fb as Zr,fc as jh,fd as jn,fe as Kl,ff as jl,fg as ix,fh as bs,fi as $x,fj as gn,fk as NE,g as fl,ga as $d,gb as Fh,gc as Wo,gd as Ef,ge as jt,gf as Jl,gg as te,gh as Dm,gi as Zx,gj as jm,gk as hi,h as bl,ha as Ch,hb as Bh,hc as Jh,hd as yf,he as vm,hf as ti,hg as y,hh as Sx,hi as Gm,hj as mE,hk as OE,i as ih,ia as Ih,ib as jr,ic as ma,id as vf,ie as wm,if as ql,ig as ss,ih as kx,ii as ci,ij as pE,ik as FE,j as ah,ja as Sh,jb as Be,jc as Ko,jd as on,je as Gf,jf as Ql,jg as an,jh as Pm,ji as jx,jj as uE,jk as BE,k as lh,ka as kh,kb as A,kc as qh,kd as Jn,ke as Yf,kf as Mb,kg as cs,kh as PT,ki as Jx,kj as Jm,kk as ip,l as so,la as Il,lb as Re,lc as en,ld as wf,le as Vf,lf as oi,lg as ii,lh as Rm,li as qx,lj as gE,lk as fi,m as xl,ma as sr,mb as xt,mc as ut,md as Tf,me as Wf,mf as Db,mg as ds,mh as va,mi as _s,mj as hE,mk as UT,n as Go,na as Lh,nb as la,nc as qe,nd as Cf,ne as Tm,nf as Pb,ng as ai,ni as Qx,nj as fE,nk as ap,o as sh,oa as Zd,ob as pr,oc as Kn,od as If,oe as Kf,of as Rb,og as ax,oi as yr,oj as nt,ok as lp,p as ch,pa as Ah,pb as _l,pc as Qh,pd as Hl,pe as Xf,pf as Ft,pg as lx,pi as un,pj as K,pk as se,q as dh,qa as Sl,qb as nm,qc as ef,qd as Sf,qe as $f,qf as Nb,qg as sx,qh as Lx,qi as RT,qj as bE,qk as sp,r as Qi,ra as jd,rb as Vo,rc as pa,rd as am,re as Zf,rf as Ob,rg as ms,rh as Ax,ri as Ca,rj as Sa,rk as Ys,s as ea,sa as Kt,sb as Jr,sc as gr,sd as lm,se as jf,sf as Fb,sg as cx,sh as xs,si as NT,sj as xE,sk as zE,t as Vr,ta as na,tb as Ml,tc as Xn,td as kf,te as Jf,tf as es,tg as ln,th as Es,ti as OT,tj as EE,tk as ke,u as El,ua as St,ub as zh,uc as tf,ud as Ot,ue as qf,uf as Bb,ug as dx,uh as Nm,ui as FT,uj as Ue,uk as UE,v as mh,va as kl,vb as Uh,vc as Nt,vd as Lf,ve as Qf,vf as zb,vg as mx,vh as ys,vi as BT,vj as Bs,vk as $,w as Gd,wa as ia,wb as Hh,wc as of,wd as ua,we as eb,wf as Ub,wg as ps,wh as _x,wi as Ms,wj as yE,wk as Vs,x as ph,xa as Yo,xb as Q,xc as rf,xd as ga,xe as tb,xf as Hb,xg as xa,xh as Om,xi as eE,xj as zs,xk as Ee,y as Yd,ya as pt,yb as Gh,yc as nf,yd as ha,ye as ob,yf as Im,yg as px,yh as jo,yi as Ds,yj as vE,yk as HE,z as we,za as Ll,zb as Yh,zc as tn,zd as Gl,ze as rb,zf as ts,zg as ux,zh as Mx,zi as Ym,zj as ue,zk as fn}from"./chunk-FX7ZIABN.js";import{a as v}from"./chunk-SQ5PDB2P.js";import{J as Hd}from"./chunk-6U3AYISY.js";import{c as th}from"./chunk-Z3N5DIM6.js";import{b as Qg,c as eh,d as C}from"./chunk-SRAX5OIU.js";import vM,{useEffect as wM}from"react";import{useEffect as JT,useState as qT}from"react";import{atom as ye,createStore as HT}from"jotai";import{createIsolation as GT}from"jotai-scope";var YE=GT();var{useAtom:ce,useSetAtom:Tr,useAtomValue:Ws,useStore:LM}=YE,VE=YE.Provider,st=HT();var YT=Qg({"./locales/ar-SA.json":()=>import("./locales/ar-SA-G6X2FPQ2.js"),"./locales/az-AZ.json":()=>import("./locales/az-AZ-76LH7QW2.js"),"./locales/bg-BG.json":()=>import("./locales/bg-BG-XCXSNQG7.js"),"./locales/bn-BD.json":()=>import("./locales/bn-BD-2XOGV67Q.js"),"./locales/ca-ES.json":()=>import("./locales/ca-ES-6MX7JW3Y.js"),"./locales/cs-CZ.json":()=>import("./locales/cs-CZ-2BRQDIVT.js"),"./locales/da-DK.json":()=>import("./locales/da-DK-5WZEPLOC.js"),"./locales/de-DE.json":()=>import("./locales/de-DE-XR44H4JA.js"),"./locales/el-GR.json":()=>import("./locales/el-GR-BZB4AONW.js"),"./locales/en.json":()=>import("./locales/en-B4ZKOASM.js"),"./locales/es-ES.json":()=>import("./locales/es-ES-U4NZUMDT.js"),"./locales/eu-ES.json":()=>import("./locales/eu-ES-A7QVB2H4.js"),"./locales/fa-IR.json":()=>import("./locales/fa-IR-HGAKTJCU.js"),"./locales/fi-FI.json":()=>import("./locales/fi-FI-Z5N7JZ37.js"),"./locales/fr-FR.json":()=>import("./locales/fr-FR-RHASNOE6.js"),"./locales/gl-ES.json":()=>import("./locales/gl-ES-HMX3MZ6V.js"),"./locales/he-IL.json":()=>import("./locales/he-IL-6SHJWFNN.js"),"./locales/hi-IN.json":()=>import("./locales/hi-IN-IWLTKZ5I.js"),"./locales/hu-HU.json":()=>import("./locales/hu-HU-A5ZG7DT2.js"),"./locales/id-ID.json":()=>import("./locales/id-ID-SAP4L64H.js"),"./locales/it-IT.json":()=>import("./locales/it-IT-JPQ66NNP.js"),"./locales/ja-JP.json":()=>import("./locales/ja-JP-DBVTYXUO.js"),"./locales/kaa.json":()=>import("./locales/kaa-6HZHGXH3.js"),"./locales/kab-KAB.json":()=>import("./locales/kab-KAB-ZGHBKWFO.js"),"./locales/kk-KZ.json":()=>import("./locales/kk-KZ-P5N5QNE5.js"),"./locales/km-KH.json":()=>import("./locales/km-KH-HSX4SM5Z.js"),"./locales/ko-KR.json":()=>import("./locales/ko-KR-MTYHY66A.js"),"./locales/ku-TR.json":()=>import("./locales/ku-TR-6OUDTVRD.js"),"./locales/lt-LT.json":()=>import("./locales/lt-LT-XHIRWOB4.js"),"./locales/lv-LV.json":()=>import("./locales/lv-LV-5QDEKY6T.js"),"./locales/mr-IN.json":()=>import("./locales/mr-IN-CRQNXWMA.js"),"./locales/my-MM.json":()=>import("./locales/my-MM-5M5IBNSE.js"),"./locales/nb-NO.json":()=>import("./locales/nb-NO-T6EIAALU.js"),"./locales/nl-NL.json":()=>import("./locales/nl-NL-IS3SIHDZ.js"),"./locales/nn-NO.json":()=>import("./locales/nn-NO-6E72VCQL.js"),"./locales/oc-FR.json":()=>import("./locales/oc-FR-POXYY2M6.js"),"./locales/pa-IN.json":()=>import("./locales/pa-IN-N4M65BXN.js"),"./locales/percentages.json":()=>import("./locales/percentages-BXMCSKIN.js"),"./locales/pl-PL.json":()=>import("./locales/pl-PL-T2D74RX3.js"),"./locales/pt-BR.json":()=>import("./locales/pt-BR-5N22H2LF.js"),"./locales/pt-PT.json":()=>import("./locales/pt-PT-UZXXM6DQ.js"),"./locales/ro-RO.json":()=>import("./locales/ro-RO-JPDTUUEW.js"),"./locales/ru-RU.json":()=>import("./locales/ru-RU-B4JR7IUQ.js"),"./locales/si-LK.json":()=>import("./locales/si-LK-N5RQ5JYF.js"),"./locales/sk-SK.json":()=>import("./locales/sk-SK-C5VTKIMK.js"),"./locales/sl-SI.json":()=>import("./locales/sl-SI-NN7IZMDC.js"),"./locales/sv-SE.json":()=>import("./locales/sv-SE-XGPEYMSR.js"),"./locales/ta-IN.json":()=>import("./locales/ta-IN-2NMHFXQM.js"),"./locales/th-TH.json":()=>import("./locales/th-TH-HPSO5L25.js"),"./locales/tr-TR.json":()=>import("./locales/tr-TR-DEFEU3FU.js"),"./locales/uk-UA.json":()=>import("./locales/uk-UA-QMV73CPH.js"),"./locales/vi-VN.json":()=>import("./locales/vi-VN-M7AON7JQ.js"),"./locales/zh-CN.json":()=>import("./locales/zh-CN-LNUGB5OW.js"),"./locales/zh-HK.json":()=>import("./locales/zh-HK-E62DVLB3.js"),"./locales/zh-TW.json":()=>import("./locales/zh-TW-RAJ6MFWO.js")});var VT=85,_o={code:"en",label:"English"},xi=[_o,...[{code:"ar-SA",label:"\u0627\u0644\u0639\u0631\u0628\u064A\u0629",rtl:!0},{code:"bg-BG",label:"\u0411\u044A\u043B\u0433\u0430\u0440\u0441\u043A\u0438"},{code:"ca-ES",label:"Catal\xE0"},{code:"cs-CZ",label:"\u010Cesky"},{code:"de-DE",label:"Deutsch"},{code:"el-GR",label:"\u0395\u03BB\u03BB\u03B7\u03BD\u03B9\u03BA\u03AC"},{code:"es-ES",label:"Espa\xF1ol"},{code:"eu-ES",label:"Euskara"},{code:"fa-IR",label:"\u0641\u0627\u0631\u0633\u06CC",rtl:!0},{code:"fi-FI",label:"Suomi"},{code:"fr-FR",label:"Fran\xE7ais"},{code:"gl-ES",label:"Galego"},{code:"he-IL",label:"\u05E2\u05D1\u05E8\u05D9\u05EA",rtl:!0},{code:"hi-IN",label:"\u0939\u093F\u0928\u094D\u0926\u0940"},{code:"hu-HU",label:"Magyar"},{code:"id-ID",label:"Bahasa Indonesia"},{code:"it-IT",label:"Italiano"},{code:"ja-JP",label:"\u65E5\u672C\u8A9E"},{code:"kab-KAB",label:"Taqbaylit"},{code:"kk-KZ",label:"\u049A\u0430\u0437\u0430\u049B \u0442\u0456\u043B\u0456"},{code:"ko-KR",label:"\uD55C\uAD6D\uC5B4"},{code:"ku-TR",label:"Kurd\xEE"},{code:"lt-LT",label:"Lietuvi\u0173"},{code:"lv-LV",label:"Latvie\u0161u"},{code:"my-MM",label:"Burmese"},{code:"nb-NO",label:"Norsk bokm\xE5l"},{code:"nl-NL",label:"Nederlands"},{code:"nn-NO",label:"Norsk nynorsk"},{code:"oc-FR",label:"Occitan"},{code:"pa-IN",label:"\u0A2A\u0A70\u0A1C\u0A3E\u0A2C\u0A40"},{code:"pl-PL",label:"Polski"},{code:"pt-BR",label:"Portugu\xEAs Brasileiro"},{code:"pt-PT",label:"Portugu\xEAs"},{code:"ro-RO",label:"Rom\xE2n\u0103"},{code:"ru-RU",label:"\u0420\u0443\u0441\u0441\u043A\u0438\u0439"},{code:"sk-SK",label:"Sloven\u010Dina"},{code:"sv-SE",label:"Svenska"},{code:"sl-SI",label:"Sloven\u0161\u010Dina"},{code:"tr-TR",label:"T\xFCrk\xE7e"},{code:"uk-UA",label:"\u0423\u043A\u0440\u0430\u0457\u043D\u0441\u044C\u043A\u0430"},{code:"zh-CN",label:"\u7B80\u4F53\u4E2D\u6587"},{code:"zh-TW",label:"\u7E41\u9AD4\u4E2D\u6587"},{code:"vi-VN",label:"Ti\u1EBFng Vi\u1EC7t"},{code:"mr-IN",label:"\u092E\u0930\u093E\u0920\u0940"}].filter(e=>th[e.code]>=VT).sort((e,o)=>e.label>o.label?1:-1)],Xs="__test__";v.DEV&&xi.unshift({code:Xs,label:"test language"},{code:`${Xs}.rtl`,label:"\u202Atest language (rtl)\u202C",rtl:!0});var bi=_o,Ks={},$s=async e=>{if(bi=e,document.documentElement.dir=bi.rtl?"rtl":"ltr",document.documentElement.lang=bi.code,e.code.startsWith(Xs))Ks={};else try{Ks=await YT(`./locales/${bi.code}.json`)}catch(o){console.error(`Failed to load language ${e.code}:`,o.message),Ks=Hd}st.set(KE,e.code)},qo=()=>bi,WE=(e,o)=>{for(let t=0;t<o.length;++t){let r=o[t];if(e[r]===void 0)return;e=e[r]}if(typeof e=="string")return e},g=(e,o,t)=>{if(bi.code.startsWith(Xs))return`\u202A[[${o?`${e}(${JSON.stringify(o).slice(1,-1)})`:e}]]\u202C`;let r=e.split("."),n=WE(Ks,r)||WE(Hd,r)||t;if(n===void 0){let i=`Can't find translation for ${e}`;if(v.PROD)return console.warn(i),"";throw new Error(i)}if(o)for(let i in o)n=n.replace(`{{${i}}}`,String(o[i]));return n},KE=ye(_o.code),Ve=()=>{let e=Ws(KE);return{t:g,langCode:e}};import{useState as XT,useEffect as $T}from"react";import WT from"react";import{jsx as cp}from"react/jsx-runtime";var KT=({size:e="1em",circleWidth:o=8,synchronized:t=!1,className:r=""})=>{let i=-(WT.useRef(Date.now()).current%1600);return cp("div",{className:`Spinner ${r}`,children:cp("svg",{viewBox:"0 0 100 100",style:{width:e,height:e,"--spinner-delay":t?`${i}ms`:0},children:cp("circle",{cx:"50",cy:"50",r:50-o/2,strokeWidth:o,fill:"none",strokeMiterlimit:"10"})})})},ct=KT;import ZT from"clsx";import{jsx as dp,jsxs as jT}from"react/jsx-runtime";var Zs=({delay:e,theme:o})=>{let[t,r]=XT(!!e);return $T(()=>{if(!e)return;let n=setTimeout(()=>{r(!1)},e);return()=>clearTimeout(n)},[e]),t?null:jT("div",{className:ZT("LoadingMessage",{"LoadingMessage--dark":o===re.DARK}),children:[dp("div",{children:dp(ct,{})}),dp("div",{className:"LoadingMessage-text",children:g("labels.loadingScene")})]})};import{jsx as QT}from"react/jsx-runtime";var XE=e=>{let[o,t]=qT(!0);return JT(()=>{let r=async()=>{await $s(n),t(!1)},n=xi.find(i=>i.code===e.langCode)||_o;r()},[e.langCode]),o?QT(Zs,{theme:e.theme}):e.children};import Uo,{useContext as Hr}from"react";import{flushSync as _n}from"react-dom";import q5 from"roughjs/bin/rough";import mT from"clsx";import{nanoid as Q5}from"nanoid";import mp,{useEffect as eC,useRef as $E,useState as tC}from"react";import ZE from"clsx";import{jsx as La,jsxs as Aa}from"react/jsx-runtime";var X=mp.forwardRef(({size:e="medium",visible:o=!0,className:t="",...r},n)=>{let{id:i}=Ke(),a=mp.useRef(null);mp.useImperativeHandle(n,()=>a.current);let l=`ToolIcon_size_${e}`,[s,c]=tC(!1),m=$E(!0),d=async u=>{let h="onClick"in r&&r.onClick?.(u);if(Gn(h))try{c(!0),await h}catch(f){if(f instanceof Xn)console.warn(f);else throw f}finally{m.current&&c(!1)}};eC(()=>(m.current=!0,()=>{m.current=!1}),[]);let p=$E(null);if(r.type==="button"||r.type==="icon"||r.type==="submit"){let u=r.type==="icon"?"button":r.type;return Aa("button",{className:ZE("ToolIcon_type_button",l,t,o&&!r.hidden?"ToolIcon_type_button--show":"ToolIcon_type_button--hide",{ToolIcon:!r.hidden,"ToolIcon--selected":r.selected,"ToolIcon--plain":r.type==="icon"}),style:r.style,"data-testid":r["data-testid"],hidden:r.hidden,title:r.title,"aria-label":r["aria-label"],type:u,onClick:d,ref:a,disabled:s||r.isLoading||!!r.disabled,children:[(r.icon||r.label)&&Aa("div",{className:"ToolIcon__icon","aria-hidden":"true","aria-disabled":!!r.disabled,children:[r.icon||r.label,r.keyBindingLabel&&La("span",{className:"ToolIcon__keybinding",children:r.keyBindingLabel}),r.isLoading&&La(ct,{})]}),r.showAriaLabel&&Aa("div",{className:"ToolIcon__label",children:[r["aria-label"]," ",s&&La(ct,{})]}),r.children]})}return Aa("label",{className:ZE("ToolIcon",t),title:r.title,onPointerDown:u=>{p.current=u.pointerType||null,r.onPointerDown?.({pointerType:u.pointerType||null})},onPointerUp:()=>{requestAnimationFrame(()=>{p.current=null})},children:[La("input",{className:`ToolIcon_type_radio ${l}`,type:"radio",name:r.name,"aria-label":r["aria-label"],"aria-keyshortcuts":r["aria-keyshortcuts"],"data-testid":r["data-testid"],id:`${i}-${r.id}`,onChange:()=>{r.onChange?.({pointerType:p.current})},checked:r.checked,ref:a}),Aa("div",{className:"ToolIcon__icon",children:[r.icon,r.keyBindingLabel&&La("span",{className:"ToolIcon__keybinding",children:r.keyBindingLabel})]})]})});X.displayName="ToolButton";import{jsx as rC}from"react/jsx-runtime";var oC=(e,o,t)=>{let r=new Set($(e.filter(m=>ie(m)),o).map(m=>m.id)),n={},i=t.scene.getNonDeletedElementsMap(),a=new Set;for(let m of r){let d=ko(e,m);for(let p of d)if(!a.has(p.id)){if(qe(p)){let u=Qt(p,i);u&&(n[u.id]=!0)}else n[p.id]=!0;a.add(p.id)}}let l=!0,s=e.map(m=>{if(o.selectedElementIds[m.id]){let d=qe(m)?Qt(m,i):null;return m.frameId&&r.has(m.frameId)?(l=!1,n[m.id]=!0,m):d?.frameId&&r.has(d?.frameId)?m:(m.boundElements&&m.boundElements.forEach(p=>{let u=t.scene.getNonDeletedElementsMap().get(p.id);u&&ee(u)&&(P(u,{startBinding:m.id===u.startBinding?.elementId?null:u.startBinding,endBinding:m.id===u.endBinding?.elementId?null:u.endBinding}),P(u,{points:u.points}))}),q(m,{isDeleted:!0}))}return m.frameId&&r.has(m.frameId)?(l=!1,qe(m)||(n[m.id]=!0),q(m,{frameId:null})):qe(m)&&o.selectedElementIds[m.containerId]?q(m,{isDeleted:!0}):m}),c=o.editingGroupId;if(l&&o.editingGroupId){let m=Le(s,o.editingGroupId).filter(d=>!d.isDeleted);if(m.length>1)m[0]&&(n[m[0].id]=!0);else{c=null,m[0]&&(n[m[0].id]=!0);let d=m[0];if(d){let p=d.groupIds.findIndex(h=>h===o.editingGroupId),u=d.groupIds[p+1];if(u){let h=Le(s,u).filter(f=>!f.isDeleted);h.length>1&&(c=u,h.forEach(f=>{n[f.id]=!0}))}}}}return{elements:s,appState:{...o,...We({selectedElementIds:n,editingGroupId:c},s,o,null)}}},jE=(e,o)=>{if(e.editingGroupId){let t=Le(se(o),e.editingGroupId);if(t.length)return{...e,selectedElementIds:{[t[0].id]:!0}}}return e},_a=D({name:"deleteSelectedElements",label:"labels.delete",icon:Ot,trackEvent:{category:"element",action:"delete"},perform:(e,o,t,r)=>{if(o.editingLinearElement){let{elementId:a,selectedPointsIndices:l,startBindingElement:s,endBindingElement:c}=o.editingLinearElement,m=r.scene.getNonDeletedElementsMap(),d=K.getElement(a,m);if(!d||l==null)return!1;if(d.points.length<2){let u=e.map(f=>f.id===d.id?q(f,{isDeleted:!0}):f),h=jE(o,u);return{elements:u,appState:{...h,editingLinearElement:null},captureUpdate:L.IMMEDIATELY}}let p={startBindingElement:l?.includes(0)?null:s,endBindingElement:l?.includes(d.points.length-1)?null:c};return K.deletePoints(d,l),{elements:e,appState:{...o,editingLinearElement:{...o.editingLinearElement,...p,selectedPointsIndices:l?.[0]>0?[l[0]-1]:[0]}},captureUpdate:L.IMMEDIATELY}}let{elements:n,appState:i}=oC(e,o,r);return fs(n,n.filter(a=>a.isDeleted)),i=jE(i,n),{elements:n,appState:{...i,activeTool:Be(o,{type:"selection"}),multiElement:null,activeEmbeddable:null},captureUpdate:ke(se(e),o)?L.IMMEDIATELY:L.EVENTUALLY}},keyTest:(e,o,t)=>(e.key===y.BACKSPACE||e.key===y.DELETE)&&!e[y.CTRL_OR_CMD],PanelComponent:({elements:e,appState:o,updateData:t})=>rC(X,{type:"button",icon:Ot,title:g("labels.delete"),"aria-label":g("labels.delete"),onClick:()=>t(null),visible:ke(se(e),o)})});var pp=(e,o)=>e.frameId===o||e.id===o,qE=(e,o,t)=>{let r=[],n=[],i=null,a=-1,l=Q(t||$(e,o,{includeBoundTextElement:!0,includeElementsInFrames:!0}));for(;++a<e.length;){let s=e[a];l.get(s.id)?(n.length&&(r=r.concat(n),n=[]),r.push(a),i=a+1):s.isDeleted&&i===a?(i=a+1,n.push(a)):n=[]}return r},nC=e=>{let o=0;return e.reduce((t,r,n)=>(n>0&&e[n-1]!==r-1&&(o=++o),(t[o]||(t[o]=[])).push(r),t),[])},JE=(e,o,t)=>{if("containerId"in e&&e.containerId){let r=bo.getScene(e).getElement(e.containerId);if(r)return t==="left"?Math.min(o.indexOf(r),o.indexOf(e)):Math.max(o.indexOf(r),o.indexOf(e))}else{let r=e.boundElements?.find(n=>n.type!=="arrow")?.id;if(r){let n=bo.getScene(e).getElement(r);if(n)return t==="left"?Math.min(o.indexOf(n),o.indexOf(e)):Math.max(o.indexOf(n),o.indexOf(e))}}},iC=(e,o)=>{let t=-1,r=-1;return e.forEach((n,i)=>{pp(n,o)&&(t===-1&&(t=i),r=i)}),t===-1?[]:e.slice(t,r+1)},aC=(e,o,t,r,n)=>{let i=o[t],a=d=>d.isDeleted?!1:n?d.frameId===n:e.editingGroupId?d.groupIds.includes(e.editingGroupId):!0,l=r==="left"?Vo(o,d=>a(d),Math.max(0,t-1)):nm(o,d=>a(d),t+1),s=o[l];if(!s)return-1;if(e.editingGroupId){if(i?.groupIds.join("")===s?.groupIds.join(""))return JE(s,o,r)??l;if(!s?.groupIds.includes(e.editingGroupId))return-1}if(!n&&(s.frameId||ie(s))){let d=iC(o,s.frameId||s.id);return r==="left"?o.indexOf(d[0]):o.indexOf(d[d.length-1])}if(!s.groupIds.length)return JE(s,o,r)??l;let c=e.editingGroupId?s.groupIds[s.groupIds.indexOf(e.editingGroupId)-1]:s.groupIds[s.groupIds.length-1],m=Le(o,c);return m.length?r==="left"?o.indexOf(m[0]):o.indexOf(m[m.length-1]):l},QE=(e,o)=>o.reduce((t,r)=>{let n=e[r];return t.set(n.id,n),t},new Map),ey=(e,o,t)=>{let r=qE(e,o),n=QE(e,r),i=nC(r);t==="right"&&(i=i.reverse());let a=new Set(r.filter(l=>ie(e[l])).map(l=>e[l].id));return i.forEach((l,s)=>{let c=l[0],m=l[l.length-1],d=t==="left"?c:m,p=l.some(T=>{let E=e[T];return E.frameId&&a.has(E.frameId)})?null:e[d]?.frameId,u=aC(o,e,d,t,p);if(u===-1||d===u)return;let h=t==="left"?e.slice(0,u):e.slice(0,c),f=e.slice(c,m+1),b=t==="left"?e.slice(u,c):e.slice(m+1,u+1),x=t==="left"?e.slice(m+1):e.slice(u+1);e=t==="left"?[...h,...f,...b,...x]:[...h,...b,...f,...x]}),fo(e,n),e},ty=(e,o,t,r,n)=>{let i=qE(e,o,n),a=QE(e,i),l=[],s,c;if(t==="left"){if(r)s=nm(e,h=>pp(h,r));else if(o.editingGroupId){let h=Le(e,o.editingGroupId);if(!h.length)return e;s=e.indexOf(h[0])}else s=0;c=i[i.length-1]}else{if(r)c=Vo(e,h=>pp(h,r));else if(o.editingGroupId){let h=Le(e,o.editingGroupId);if(!h.length)return e;c=e.indexOf(h[h.length-1])}else c=e.length-1;s=i[0]}s===-1&&(s=0);for(let h=s;h<c+1;h++)i.includes(h)||l.push(e[h]);let m=Array.from(a.values()),d=e.slice(0,s),p=e.slice(c+1),u=t==="left"?[...d,...m,...l,...p]:[...d,...l,...m,...p];return fo(u,a),u};function oy(e,o,t,r){let n=Q($(e,o,{includeBoundTextElement:!0,includeElementsInFrames:!0})),i={regularElements:[],frameChildren:new Map},a=new Set;for(let c of e)n.has(c.id)&&ie(c)&&a.add(c.id);for(let c of e)if(n.has(c.id))if(ie(c)||c.frameId&&a.has(c.frameId))i.regularElements.push(c);else if(!c.frameId)i.regularElements.push(c);else{let m=i.frameChildren.get(c.frameId)||[];m.push(c),i.frameChildren.set(c.frameId,m)}let l=e,s=Array.from(i.frameChildren.entries());for(let[c,m]of s)l=r(e,o,t,c,m);return r(l,o,t,null,i.regularElements)}var ry=(e,o)=>ey(e,o,"left"),ny=(e,o)=>ey(e,o,"right"),iy=(e,o)=>oy(e,o,"left",ty),ay=(e,o)=>oy(e,o,"right",ty);import{jsx as js}from"react/jsx-runtime";var up=D({name:"sendBackward",label:"labels.sendBackward",keywords:["move down","zindex","layer"],icon:pm,trackEvent:{category:"element"},perform:(e,o)=>({elements:ry(e,o),appState:o,captureUpdate:L.IMMEDIATELY}),keyPriority:40,keyTest:e=>e[y.CTRL_OR_CMD]&&!e.shiftKey&&e.code===te.BRACKET_LEFT,PanelComponent:({updateData:e,appState:o})=>js("button",{type:"button",className:"zIndexButton",onClick:()=>e(null),title:`${g("labels.sendBackward")} \u2014 ${A("CtrlOrCmd+[")}`,children:pm})}),gp=D({name:"bringForward",label:"labels.bringForward",keywords:["move up","zindex","layer"],icon:mm,trackEvent:{category:"element"},perform:(e,o)=>({elements:ny(e,o),appState:o,captureUpdate:L.IMMEDIATELY}),keyPriority:40,keyTest:e=>e[y.CTRL_OR_CMD]&&!e.shiftKey&&e.code===te.BRACKET_RIGHT,PanelComponent:({updateData:e,appState:o})=>js("button",{type:"button",className:"zIndexButton",onClick:()=>e(null),title:`${g("labels.bringForward")} \u2014 ${A("CtrlOrCmd+]")}`,children:mm})}),hp=D({name:"sendToBack",label:"labels.sendToBack",keywords:["move down","zindex","layer"],icon:gm,trackEvent:{category:"element"},perform:(e,o)=>({elements:iy(e,o),appState:o,captureUpdate:L.IMMEDIATELY}),keyTest:e=>so?e[y.CTRL_OR_CMD]&&e.altKey&&e.code===te.BRACKET_LEFT:e[y.CTRL_OR_CMD]&&e.shiftKey&&e.code===te.BRACKET_LEFT,PanelComponent:({updateData:e,appState:o})=>js("button",{type:"button",className:"zIndexButton",onClick:()=>e(null),title:`${g("labels.sendToBack")} \u2014 ${so?A("CtrlOrCmd+Alt+["):A("CtrlOrCmd+Shift+[")}`,children:gm})}),fp=D({name:"bringToFront",label:"labels.bringToFront",keywords:["move up","zindex","layer"],icon:um,trackEvent:{category:"element"},perform:(e,o)=>({elements:ay(e,o),appState:o,captureUpdate:L.IMMEDIATELY}),keyTest:e=>so?e[y.CTRL_OR_CMD]&&e.altKey&&e.code===te.BRACKET_RIGHT:e[y.CTRL_OR_CMD]&&e.shiftKey&&e.code===te.BRACKET_RIGHT,PanelComponent:({updateData:e,appState:o})=>js("button",{type:"button",className:"zIndexButton",onClick:t=>e(null),title:`${g("labels.bringToFront")} \u2014 ${so?A("CtrlOrCmd+Alt+]"):A("CtrlOrCmd+Shift+]")}`,children:um})});var bp=D({name:"selectAll",label:"labels.selectAll",icon:Bb,trackEvent:{category:"canvas"},viewMode:!1,perform:(e,o,t,r)=>{if(o.editingLinearElement)return!1;let n=e.filter(i=>!i.isDeleted&&!(Y(i)&&i.containerId)&&!i.locked).reduce((i,a)=>(i[a.id]=!0,i),{});return{appState:{...o,...We({editingGroupId:null,selectedElementIds:n},se(e),o,r),selectedLinearElement:Object.keys(n).length===1&&he(e[0])?new K(e[0]):null},captureUpdate:L.IMMEDIATELY}},keyTest:e=>e[y.CTRL_OR_CMD]&&e.key===y.A});var lC=e=>{let o=e.slice(),t=new Set,r=i=>{let a=i[0]?.groupIds?.join(""),l=[i[0]],s=[];for(let c of i.slice(1))c.groupIds?.join("")===a?l.push(c):s.push(c);return s.length?[...l,...r(s)]:l},n=new Map;return o.forEach((i,a)=>{if(!n.has(i.id))if(i.groupIds?.length){let l=i.groupIds[i.groupIds.length-1],s=o.slice(a).filter(c=>{let m=c?.groupIds?.some(d=>d===l);return m&&n.set(c.id,!0),m});for(let c of r(s))t.add(c)}else t.add(i)}),t.size!==e.length?(console.error("normalizeGroupElementOrder: lost some elements... bailing!"),e):[...t]},sC=e=>{let o=Gh(e),t=e.slice(),r=new Set;return t.forEach((n,i)=>{n&&(n.boundElements?.length?(r.add(n),t[i]=null,n.boundElements.forEach(a=>{let l=o.get(a.id);l&&a.type==="text"&&(r.add(l[0]),t[l[1]]=null)})):n.type==="text"&&n.containerId&&o.get(n.containerId)?.[0].boundElements?.find(l=>l.id===n.id)||(r.add(n),t[i]=null))}),r.size!==e.length?(console.error("normalizeBoundElementsOrder: lost some elements... bailing!"),e):[...r]},ly=e=>sC(lC(e));import{jsx as dC}from"react/jsx-runtime";var xp=D({name:"duplicateSelection",label:"labels.duplicateSelection",icon:ua,trackEvent:{category:"element"},perform:(e,o,t,r)=>{if(o.editingLinearElement)try{let i=K.duplicateSelectedPoints(o,r.scene.getNonDeletedElementsMap());return{elements:e,appState:i,captureUpdate:L.IMMEDIATELY}}catch{return!1}let n=cC(e,o);if(r.props.onDuplicate&&n.elements){let i=r.props.onDuplicate(n.elements,e);i&&(n.elements=i)}return{...n,captureUpdate:L.IMMEDIATELY}},keyTest:e=>e[y.CTRL_OR_CMD]&&e.key===y.D,PanelComponent:({elements:e,appState:o,updateData:t})=>dC(X,{type:"button",icon:ua,title:`${g("labels.duplicateSelection")} \u2014 ${A("CtrlOrCmd+D")}`,"aria-label":g("labels.duplicateSelection"),onClick:()=>t(null),visible:ke(se(e),o)})}),cC=(e,o)=>{let t=new Map,r=[],n=[],i=new Map,a=new Map,l=Q(e),s=f=>{let x=im(f).reduce((T,E)=>{if(m.has(E.id))return T;m.set(E.id,!0);let w=as(o.editingGroupId,t,E,{x:E.x+Wd/2,y:E.y+Wd/2});return m.set(w.id,!0),a.set(w.id,w),i.set(E.id,w.id),n.push(E),r.push(w),T.push(w),T},[]);return Array.isArray(f)?x:x[0]||null};e=ly(e);let c=Q($(e,o,{includeBoundTextElement:!0,includeElementsInFrames:!0})),m=new Map,d=e.slice(),p=(f,b)=>{Io(f!==-1,"targetIndex === -1 "),!(!Array.isArray(b)&&!b)&&d.splice(f+1,0,...im(b))},u=new Set(e.filter(f=>c.has(f.id)&&ie(f)).map(f=>f.id));for(let f of e){if(m.has(f.id)||!c.has(f.id))continue;let b=df(o,f);if(b){let x=Le(e,b).flatMap(E=>ie(E)?[...ko(e,E.id),E]:[E]),T=Vo(d,E=>E.groupIds?.includes(b));p(T,s(x));continue}if(!(f.frameId&&u.has(f.frameId))){if(ie(f)){let x=f.id,T=ko(e,x),E=Vo(d,w=>w.frameId===x||w.id===x);p(E,s([...T,f]));continue}if(ut(f)){let x=ue(f,l),T=Vo(d,E=>E.id===f.id||"containerId"in E&&E.containerId===f.id);x?p(T,s([f,x])):p(T,s(f));continue}if(qe(f)){let x=Qt(f,l),T=Vo(d,E=>E.id===f.id||E.id===x?.id);x?p(T,s([x,f])):p(T,s(f));continue}p(Vo(d,x=>x.id===f.id),s(f))}}Bs(d,n,i),hs(d,n,i),Ms(d,n,i);let h=Ys(r);return{elements:d,appState:{...o,...We({editingGroupId:o.editingGroupId,selectedElementIds:h.reduce((f,b)=>(qe(b)||(f[b.id]=!0),f),{})},se(d),o,null)}}};import{useEffect as Fp,useMemo as F2,useRef as Bp,useState as B2}from"react";var mC=new Set(["command_palette","export"]),le=(e,o,t,r)=>{try{if(typeof window>"u"||v.VITE_WORKER_ID||v.VITE_APP_ENABLE_TRACKING!=="true"||!mC.has(e)||v.DEV)return;v.PROD||console.info("trackEvent",{category:e,action:o,label:t,value:r}),window.sa_event&&window.sa_event(o,{category:e,label:t,value:r})}catch(n){console.error("error during analytics",n)}};import hC from"clsx";import{forwardRef as pC}from"react";import uC from"clsx";import{jsx as gC}from"react/jsx-runtime";var Js=pC((e,o)=>{let{title:t,className:r,testId:n,active:i,standalone:a,icon:l,onClick:s}=e;return gC("button",{type:"button",ref:o,title:t,"data-testid":n,className:uC(r,{standalone:a,active:i}),onClick:s,children:l},t)});import{jsx as Ep,jsxs as fC}from"react/jsx-runtime";var eo=e=>Ep("div",{className:"buttonList",children:e.options.map(o=>e.type==="button"?Ep(Js,{icon:o.icon,title:o.text,testId:o.testId,active:o.active??e.value===o.value,onClick:t=>e.onClick(o.value,t)},o.text):fC("label",{className:hC({active:e.value===o.value}),title:o.text,children:[Ep("input",{type:"radio",name:e.group,onChange:()=>e.onChange(o.value),checked:e.value===o.value,"data-testid":o.testId}),o.icon]},o.text))});import bC from"clsx";import{jsx as yp}from"react/jsx-runtime";var sy=({onChange:e,type:o,activeColor:t,topPicks:r})=>{let n;return o==="elementStroke"&&(n=hl),o==="elementBackground"&&(n=fl),o==="canvasBackground"&&(n=bl),r&&(n=r),n?yp("div",{className:"color-picker__top-picks",children:n.map(i=>yp("button",{className:bC("color-picker__button",{active:i===t,"is-transparent":i==="transparent"||!i}),style:{"--swatch-color":i},type:"button",title:i,onClick:()=>e(i),"data-testid":`color-top-pick-${i}`,children:yp("div",{className:"color-picker__button-outline"})},i))}):(console.error("Invalid type for TopPicks"),null)};import{jsx as xC}from"react/jsx-runtime";var qs=()=>xC("div",{style:{width:1,height:"1rem",backgroundColor:"var(--default-border-color)",margin:"0 auto"}});import by,{useEffect as xy,useState as OC}from"react";import vC from"clsx";import{useEffect as wC,useRef as TC}from"react";var Cr=({palette:e,color:o})=>{for(let[t,r]of Object.entries(e))if(Array.isArray(r)){let n=r.indexOf(o);if(n>-1)return{colorName:t,shade:n}}else if(r===o)return{colorName:t,shade:null};return null},Ma=[["q","w","e","r","t"],["a","s","d","f","g"],["z","x","c","v","b"]].flat(),vp=({color:e,palette:o})=>!Object.values(o).flat().includes(e),dy=(e,o,t)=>{let r={elementBackground:"backgroundColor",elementStroke:"strokeColor"},n=e.filter(a=>{if(a.isDeleted)return!1;let l=a[r[o]];return vp({color:l,palette:t})}),i=new Map;return n.forEach(a=>{let l=a[r[o]];i.has(l)?i.set(l,i.get(l)+1):i.set(l,1)}),[...i.entries()].sort((a,l)=>l[1]-a[1]).map(a=>a[0]).slice(0,oh)},to=ye(null),cy=(e,o,t)=>(e*299+o*587+t*114)/1e3>=160?"black":"white",my=(e,o)=>{if(o){let i=new Option().style;if(i.color=e,i.color){let a=i.color.replace(/^(rgb|rgba)\(/,"").replace(/\)$/,"").replace(/\s/g,"").split(","),l=parseInt(a[0]),s=parseInt(a[1]),c=parseInt(a[2]);return cy(l,s,c)}}if(e==="transparent")return"black";let t=parseInt(e.substring(1,3),16),r=parseInt(e.substring(3,5),16),n=parseInt(e.substring(5,7),16);return cy(t,r,n)};import{jsxs as yC}from"react/jsx-runtime";var EC=({color:e,keyLabel:o,isCustomColor:t=!1,isShade:r=!1})=>yC("div",{className:"color-picker__button__hotkey-label",style:{color:my(e,t)},children:[r&&"\u21E7",o]}),Ei=EC;import{jsx as Da,jsxs as py}from"react/jsx-runtime";var uy=({hex:e,onChange:o,palette:t})=>{let r=Cr({color:e||"transparent",palette:t}),[n,i]=ce(to),a=TC(null);if(wC(()=>{a.current&&n==="shades"&&a.current.focus()},[r,n]),r){let{colorName:l,shade:s}=r,c=t[l];if(Array.isArray(c))return Da("div",{className:"color-picker-content--default shades",children:c.map((m,d)=>py("button",{ref:d===s&&n==="shades"?a:void 0,tabIndex:-1,type:"button",className:vC("color-picker__button color-picker__button--large",{active:d===s}),"aria-label":"Shade",title:`${l} - ${d+1}`,style:m?{"--swatch-color":m}:void 0,onClick:()=>{o(m),i("shades")},children:[Da("div",{className:"color-picker__button-outline"}),Da(Ei,{color:m,keyLabel:d+1,isShade:!0})]},d))})}return py("div",{className:"color-picker-content--default",style:{position:"relative"},tabIndex:-1,children:[Da("button",{type:"button",tabIndex:-1,className:"color-picker__button color-picker__button--large color-picker__button--no-focus-visible"}),Da("div",{tabIndex:-1,style:{position:"absolute",top:0,left:0,right:0,bottom:0,display:"flex",alignItems:"center",justifyContent:"center",textAlign:"center",fontSize:"0.75rem"},children:g("colorPicker.noShades")})]})};import CC from"clsx";import{useEffect as IC,useRef as SC}from"react";import{jsx as wp,jsxs as LC}from"react/jsx-runtime";var kC=({palette:e,color:o,onChange:t,label:r,activeShade:n})=>{let i=Cr({color:o||"transparent",palette:e}),[a,l]=ce(to),s=SC(null);return IC(()=>{s.current&&a==="baseColors"&&s.current.focus()},[i?.colorName,a]),wp("div",{className:"color-picker-content--default",children:Object.entries(e).map(([c,m],d)=>{let p=(Array.isArray(m)?m[n]:m)||"transparent",u=Ma[d],h=g(`colors.${c.replace(/\d+/,"")}`,null,"");return LC("button",{ref:i?.colorName===c?s:void 0,tabIndex:-1,type:"button",className:CC("color-picker__button color-picker__button--large",{active:i?.colorName===c,"is-transparent":p==="transparent"||!p}),onClick:()=>{t(p),l("baseColors")},title:`${h}${p.startsWith("#")?` ${p}`:""} \u2014 ${u}`,"aria-label":`${h} \u2014 ${u}`,style:p?{"--swatch-color":p}:void 0,"data-testid":`color-${c}`,children:[wp("div",{className:"color-picker__button-outline"}),wp(Ei,{color:p,keyLabel:u})]},c)})})},gy=kC;import AC from"clsx";import{useEffect as _C,useRef as MC}from"react";import{jsx as Tp,jsxs as DC}from"react/jsx-runtime";var hy=({colors:e,color:o,onChange:t,label:r})=>{let[n,i]=ce(to),a=MC(null);return _C(()=>{a.current&&a.current.focus()},[o,n]),Tp("div",{className:"color-picker-content--default",children:e.map((l,s)=>DC("button",{ref:o===l?a:void 0,tabIndex:-1,type:"button",className:AC("color-picker__button color-picker__button--large",{active:o===l,"is-transparent":l==="transparent"||!l}),onClick:()=>{t(l),i("custom")},title:l,"aria-label":r,style:{"--swatch-color":l},children:[Tp("div",{className:"color-picker__button-outline"}),Tp(Ei,{color:l,keyLabel:s+1,isCustomColor:!0})]},s))})};var Cp=(e,o,t)=>{let r=Math.ceil(t/Yr);switch(o=o??-1,e){case"ArrowLeft":{let n=o-1;return n<0?t-1:n}case"ArrowRight":return(o+1)%t;case"ArrowDown":{let n=o+Yr;return n>=t?o%Yr:n}case"ArrowUp":{let n=o-Yr,i=n<0?Yr*r+n:n;return i>=t?void 0:i}}},PC=({e,colorObj:o,onChange:t,palette:r,customColors:n,setActiveColorPickerSection:i,activeShade:a})=>{if(o?.shade!=null&&["Digit1","Digit2","Digit3","Digit4","Digit5"].includes(e.code)&&e.shiftKey){let l=Number(e.code.slice(-1))-1;return t(r[o.colorName][l]),i("shades"),!0}if(["1","2","3","4","5"].includes(e.key)&&n[Number(e.key)-1])return t(n[Number(e.key)-1]),i("custom"),!0;if(Ma.includes(e.key)){let l=Ma.indexOf(e.key),s=Object.keys(r)[l],c=r[s],m=Array.isArray(c)?c[a]:c;return t(m),i("baseColors"),!0}return!1},fy=({event:e,activeColorPickerSection:o,palette:t,color:r,onChange:n,customColors:i,setActiveColorPickerSection:a,updateData:l,activeShade:s,onEyeDropperToggle:c,onEscape:m})=>{if(e[y.CTRL_OR_CMD])return!1;if(e.key===y.ESCAPE)return m(e),!0;if(e.key===y.ALT)return c(!0),!0;if(e.key===y.I)return c(),!0;let d=Cr({color:r,palette:t});if(e.key===y.TAB){let p={custom:!!i.length,baseColors:!0,shades:d?.shade!=null,hex:!0},u=Object.entries(p).reduce((T,[E,w])=>(w&&T.push(E),T),[]),h=u.indexOf(o),f=e.shiftKey?-1:1,b=h+f>u.length-1?0:h+f<0?u.length-1:h+f,x=u[b];return x&&a(x),x==="custom"?n(i[0]):x==="baseColors"&&(Object.entries(t).find(([E,w])=>Array.isArray(w)?w.includes(r):w===r?E:null)||n(Ho.black)),e.preventDefault(),e.stopPropagation(),!0}if(PC({e,colorObj:d,onChange:n,palette:t,customColors:i,setActiveColorPickerSection:a,activeShade:s}))return!0;if(o==="shades"&&d){let{shade:p}=d,u=Cp(e.key,p,Yr);if(u!==void 0)return n(t[d.colorName][u]),!0}if(o==="baseColors"&&d){let{colorName:p}=d,u=Object.keys(t),h=u.indexOf(p),f=Cp(e.key,h,u.length);if(f!==void 0){let b=u[f],x=t[b];return n(Array.isArray(x)?x[s]:x),!0}}if(o==="custom"){let p=i.indexOf(r),u=Cp(e.key,p,i.length);if(u!==void 0){let h=i[u];return n(h),!0}}return!1};import{jsx as NC}from"react/jsx-runtime";var RC=({children:e})=>NC("div",{className:"color-picker__heading",children:e}),yi=RC;import{jsx as bn,jsxs as Qs}from"react/jsx-runtime";var Ey=({color:e,onChange:o,label:t,type:r,elements:n,palette:i,updateData:a,children:l,onEyeDropperToggle:s,onEscape:c})=>{let[m]=by.useState(()=>r==="canvasBackground"?[]:dy(n,r,i)),[d,p]=ce(to),u=Cr({color:e,palette:i});xy(()=>{if(!d){let x=vp({color:e,palette:i}),T=x&&!m.includes(e);p(T?"hex":x?"custom":u?.shade!=null?"shades":"baseColors")}},[d,e,i,p,u,m]);let[h,f]=OC(u?.shade??(r==="elementBackground"?nh:rh));xy(()=>{u?.shade!=null&&f(u.shade);let x=T=>{T.key===y.ALT&&s(!1)};return document.addEventListener("keyup",x,{capture:!0}),()=>{document.removeEventListener("keyup",x,{capture:!0})}},[u,s]);let b=by.useRef(null);return bn("div",{role:"dialog","aria-modal":"true","aria-label":g("labels.colorPicker"),children:Qs("div",{ref:b,onKeyDown:x=>{fy({event:x,activeColorPickerSection:d,palette:i,color:e,onChange:o,onEyeDropperToggle:s,customColors:m,setActiveColorPickerSection:p,updateData:a,activeShade:h,onEscape:c})&&(x.preventDefault(),x.stopPropagation())},className:"color-picker-content properties-content",tabIndex:-1,children:[!!m.length&&Qs("div",{children:[bn(yi,{children:g("colorPicker.mostUsedCustomColors")}),bn(hy,{colors:m,color:e,label:g("colorPicker.mostUsedCustomColors"),onChange:o})]}),Qs("div",{children:[bn(yi,{children:g("colorPicker.colors")}),bn(gy,{color:e,label:t,palette:i,onChange:o,activeShade:h})]}),Qs("div",{children:[bn(yi,{children:g("colorPicker.shades")}),bn(uy,{hex:e,onChange:o,palette:i})]}),l]})})};import*as rc from"@radix-ui/react-popover";import e2 from"clsx";import{useRef as t2}from"react";import{useCallback as VC,useEffect as Sp,useRef as Ty,useState as WC}from"react";import{useEffect as UC,useRef as HC}from"react";import{createPortal as GC}from"react-dom";import yy from"react";var Ip=yy.createContext(null),ve=()=>yy.useContext(Ip);import{useState as FC,useLayoutEffect as vy}from"react";var ec=e=>{let[o,t]=FC(null),r=me(),{theme:n}=ve(),{container:i}=Ke();return vy(()=>{o&&(o.className="",o.classList.add("excalidraw",...e?.className?.split(/\s+/)||[]),o.classList.toggle("excalidraw--mobile",r.editor.isMobile),o.classList.toggle("theme--dark",n===re.DARK))},[o,n,r.editor.isMobile,e?.className]),vy(()=>{let a=e?.parentSelector?i?.querySelector(e.parentSelector):document.body;if(!a)return;let l=document.createElement("div");return a.appendChild(l),t(l),()=>{a.removeChild(l)}},[i,e?.parentSelector]),o};import{useEffect as BC}from"react";function vi(e,o,t){BC(()=>{function r(n){let i=n;if(!e.current)return;let a=t?.(i,e.current);if(a===!0)return;if(a===!1)return o(i);e.current.contains(i.target)||!document.documentElement.contains(i.target)||i.target.closest("[data-radix-portal]")||i.target===document.documentElement&&document.body.style.pointerEvents==="none"||i.target.closest("[data-prevent-outside-click]")||o(i)}return document.addEventListener("pointerdown",r),document.addEventListener("touchstart",r),()=>{document.removeEventListener("pointerdown",r),document.removeEventListener("touchstart",r)}},[e,o,t])}import{useRef as zC}from"react";var Ir=e=>{let o=zC(e);return Object.assign(o.current,e),o.current};import{jsx as YC}from"react/jsx-runtime";var Sr=ye(null),wy=({onCancel:e,onChange:o,onSelect:t,colorPickerType:r})=>{let n=ec({className:"excalidraw-eye-dropper-backdrop",parentSelector:".excalidraw-eye-dropper-container"}),i=ve(),a=kr(),l=He(),s=$(a,i),c=Ir({app:l,onCancel:e,onChange:o,onSelect:t,selectedElements:s}),{container:m}=Ke();UC(()=>{let p=d.current;if(!p||!l.canvas||!n)return;let u=!1,h=l.canvas.getContext("2d"),f=({clientX:I,clientY:_})=>{let k=h.getImageData((I-i.offsetLeft)*window.devicePixelRatio,(_-i.offsetTop)*window.devicePixelRatio,1,1).data;return lh(k[0],k[1],k[2])},b=({clientX:I,clientY:_,altKey:k})=>{p.style.top=`${_+20}px`,p.style.left=`${I+20}px`;let R=f({clientX:I,clientY:_});u&&c.onChange(r,R,c.selectedElements,{altKey:k}),p.style.background=R},x=()=>{c.onCancel()},T=(I,_)=>{c.onSelect(I,_)},E=I=>{u=!0,I.stopImmediatePropagation()},w=I=>{u=!1,m?.focus(),I.stopImmediatePropagation(),I.preventDefault(),T(f(I),I)},S=I=>{I.key===y.ESCAPE&&(I.preventDefault(),I.stopImmediatePropagation(),x())};return n.tabIndex=-1,n.focus(),b({clientX:c.app.lastViewportPosition.x,clientY:c.app.lastViewportPosition.y,altKey:!1}),n.addEventListener("keydown",S),n.addEventListener("pointerdown",E),n.addEventListener("pointerup",w),window.addEventListener("pointermove",b,{passive:!0}),window.addEventListener("blur",x),()=>{u=!1,n.removeEventListener("keydown",S),n.removeEventListener("pointerdown",E),n.removeEventListener("pointerup",w),window.removeEventListener("pointermove",b),window.removeEventListener("blur",x)}},[c,l.canvas,n,r,m,i.offsetLeft,i.offsetTop]);let d=HC(null);return vi(d,()=>{e()},p=>!!p.target.closest(".excalidraw-eye-dropper-trigger, .excalidraw-eye-dropper-backdrop")),n?GC(YC("div",{ref:d,className:"excalidraw-eye-dropper-preview"}),n):null};import KC from"clsx";import{Fragment as XC,jsx as tc,jsxs as Cy}from"react/jsx-runtime";var Iy=({color:e,onChange:o,label:t,colorPickerType:r})=>{let n=me(),[i,a]=WC(e),[l,s]=ce(to);Sp(()=>{a(e)},[e]);let c=VC(h=>{let f=h.toLowerCase(),b=Sy(f);b&&o(b),a(f)},[o]),m=Ty(null),d=Ty(null);Sp(()=>{m.current&&m.current.focus()},[l]);let[p,u]=ce(Sr);return Sp(()=>()=>{u(null)},[u]),Cy("div",{className:"color-picker__input-label",children:[tc("div",{className:"color-picker__input-hash",children:"#"}),tc("input",{ref:l==="hex"?m:void 0,style:{border:0,padding:0},spellCheck:!1,className:"color-picker-input","aria-label":t,onChange:h=>{c(h.target.value)},value:(i||"").replace(/^#/,""),onBlur:()=>{a(e)},tabIndex:-1,onFocus:()=>s("hex"),onKeyDown:h=>{h.key!==y.TAB&&(h.key===y.ESCAPE&&d.current?.focus(),h.stopPropagation())}}),!n.editor.isMobile&&Cy(XC,{children:[tc("div",{style:{width:"1px",height:"1.25rem",backgroundColor:"var(--default-border-color)"}}),tc("div",{ref:d,className:KC("excalidraw-eye-dropper-trigger",{selected:p}),onClick:()=>u(h=>h?null:{keepOpenOnAlt:!1,onSelect:f=>o(f),colorPickerType:r}),title:`${g("labels.eyeDropper")} \u2014 ${y.I.toLocaleUpperCase()} or ${A("Alt")} `,children:Ab})]})]})};import JC from"react";import qC from"clsx";import*as wi from"@radix-ui/react-popover";import $C from"react";import ZC from"clsx";import{jsx as jC}from"react/jsx-runtime";var Qe=$C.forwardRef(({children:e,padding:o,className:t,style:r},n)=>jC("div",{className:ZC("Island",t),style:{"--padding":o,...r},ref:n,children:e}));import{jsx as kp,jsxs as QC}from"react/jsx-runtime";var oc=JC.forwardRef(({className:e,container:o,children:t,style:r,onClose:n,onKeyDown:i,onFocusOutside:a,onPointerLeave:l,onPointerDownOutside:s},c)=>{let m=me();return kp(wi.Portal,{container:o,children:QC(wi.Content,{ref:c,className:qC("focus-visible-none",e),"data-prevent-outside-click":!0,side:m.editor.isMobile&&!m.viewport.isLandscape?"bottom":"right",align:m.editor.isMobile&&!m.viewport.isLandscape?"center":"start",alignOffset:-16,sideOffset:20,style:{zIndex:"var(--zIndex-popup)"},onPointerLeave:l,onKeyDown:i,onFocusOutside:a,onPointerDownOutside:s,onCloseAutoFocus:d=>{d.stopPropagation(),d.preventDefault(),o&&!Oh(document.activeElement)&&o.focus(),n()},children:[kp(Qe,{padding:3,style:r,children:t}),kp(wi.Arrow,{width:20,height:10,style:{fill:"var(--popup-bg-color)",filter:"drop-shadow(rgba(0, 0, 0, 0.05) 0px 3px 2px)"}})]})})});import{jsx as xo,jsxs as Lp}from"react/jsx-runtime";var ky=e=>{let o=new Option().style;return o.color=e,!!o.color},Sy=e=>Jr(e)?e:ky(`#${e}`)?`#${e}`:ky(e)?e:null,o2=({type:e,color:o,onChange:t,label:r,elements:n,palette:i=Ho,updateData:a})=>{let{container:l}=Ke(),[,s]=ce(to),[c,m]=ce(Sr),d=Lp("div",{children:[xo(yi,{children:g("colorPicker.hexCode")}),xo(Iy,{color:o,label:r,onChange:h=>{t(h)},colorPickerType:e})]}),p=t2(null),u=()=>{p.current?.querySelector(".color-picker-content")?.focus()};return xo(oc,{container:l,style:{maxWidth:"13rem"},onFocusOutside:h=>{u(),h.preventDefault()},onPointerDownOutside:h=>{c&&h.preventDefault()},onClose:()=>{a({openPopup:null}),s(null)},children:i?xo(Ey,{palette:i,color:o,onChange:h=>{t(h)},onEyeDropperToggle:h=>{m(f=>h?(f=f||{keepOpenOnAlt:!0,onSelect:t,colorPickerType:e},f.keepOpenOnAlt=!0,f):h===!1||f?null:{keepOpenOnAlt:!1,onSelect:t,colorPickerType:e})},onEscape:h=>{c?m(null):a({openPopup:null})},label:r,type:e,elements:n,updateData:a,children:d}):d})},r2=({label:e,color:o,type:t})=>xo(rc.Trigger,{type:"button",className:e2("color-picker__button active-color properties-trigger",{"is-transparent":o==="transparent"||!o}),"aria-label":e,style:o?{"--swatch-color":o}:void 0,title:t==="elementStroke"?g("labels.showStroke"):g("labels.showBackground"),children:xo("div",{className:"color-picker__button-outline"})}),Pa=({type:e,color:o,onChange:t,label:r,elements:n,palette:i=Ho,topPicks:a,updateData:l,appState:s})=>xo("div",{children:Lp("div",{role:"dialog","aria-modal":"true",className:"color-picker-container",children:[xo(sy,{activeColor:o,onChange:t,type:e,topPicks:a}),xo(qs,{}),Lp(rc.Root,{open:s.openPopup===e,onOpenChange:c=>{l({openPopup:c?e:null})},children:[xo(r2,{color:o,label:r,type:e}),s.openPopup===e&&xo(o2,{type:e,color:o,onChange:t,label:r,elements:n,palette:i,updateData:l})]})]})});import nc,{useEffect as l2}from"react";import*as Ti from"@radix-ui/react-popover";import s2 from"clsx";import{jsx as n2}from"react/jsx-runtime";var Lr=({icon:e})=>n2("span",{style:{width:"1em",margin:"0 0.5ex 0 0.5ex",display:"inline-block",lineHeight:0,verticalAlign:"middle"},children:e});import{Fragment as a2,jsx as Ly,jsxs as Ay}from"react/jsx-runtime";var i2=({label:e,open:o,openTrigger:t,children:r,className:n})=>Ay(a2,{children:[Ay("div",{style:{cursor:"pointer",display:"flex",justifyContent:"space-between",alignItems:"center"},className:n,onClick:t,children:[e,Ly(Lr,{icon:o?Qb:os})]}),o&&Ly("div",{style:{display:"flex",flexDirection:"column"},children:r})]}),Ra=i2;import{jsx as xn,jsxs as Ap}from"react/jsx-runtime";var c2=ye(!1);function d2({options:e,value:o,label:t,onChange:r,onClose:n,numberOfOptionsToAlwaysShow:i=e.length}){let a=me(),l=u=>{let h=e.find(f=>f.keyBinding===u.key.toLowerCase());if(!(u.metaKey||u.altKey||u.ctrlKey)&&h)r(h.value),u.preventDefault();else if(u.key===y.TAB){let f=e.findIndex(x=>x.value===o),b=u.shiftKey?(e.length+f-1)%e.length:(f+1)%e.length;r(e[b].value)}else if(an(u.key)){let f=qo().rtl,b=e.findIndex(x=>x.value===o);if(b!==-1){let x=e.length,T=b;switch(u.key){case(f?y.ARROW_LEFT:y.ARROW_RIGHT):T=(b+1)%x;break;case(f?y.ARROW_RIGHT:y.ARROW_LEFT):T=(x+b-1)%x;break;case y.ARROW_DOWN:{T=(b+(i??1))%x;break}case y.ARROW_UP:{T=(x+b-(i??1))%x;break}}r(e[T].value)}u.preventDefault()}else(u.key===y.ESCAPE||u.key===y.ENTER)&&(u.preventDefault(),n());u.nativeEvent.stopImmediatePropagation(),u.stopPropagation()},[s,c]=ce(c2),m=nc.useMemo(()=>e.slice(0,i),[e,i]),d=nc.useMemo(()=>e.slice(i),[e,i]);l2(()=>{m.some(u=>u.value===o)||c(!0)},[o,m,c]);let p=u=>xn("div",{className:"picker-content",children:u.map((h,f)=>Ap("button",{type:"button",className:s2("picker-option",{active:o===h.value}),onClick:b=>{r(h.value)},title:`${h.text} ${h.keyBinding&&`\u2014 ${h.keyBinding.toUpperCase()}`}`,"aria-label":h.text||"none","aria-keyshortcuts":h.keyBinding||void 0,ref:b=>{o===h.value&&setTimeout(()=>{b?.focus()},0)},children:[h.icon,h.keyBinding&&xn("span",{className:"picker-keybinding",children:h.keyBinding})]},h.text))});return xn(Ti.Content,{side:a.editor.isMobile&&!a.viewport.isLandscape?"top":"bottom",align:"start",sideOffset:12,style:{zIndex:"var(--zIndex-popup)"},onKeyDown:l,children:Ap("div",{className:"picker",role:"dialog","aria-modal":"true","aria-label":t,children:[p(m),d.length>0&&xn(Ra,{label:g("labels.more_options"),open:s,openTrigger:()=>{c(u=>!u)},className:"picker-collapsible",children:p(d)})]})})}function _p({value:e,label:o,options:t,onChange:r,group:n="",numberOfOptionsToAlwaysShow:i}){let[a,l]=nc.useState(!1),s=nc.useRef(null);return xn("div",{children:Ap(Ti.Root,{open:a,onOpenChange:c=>l(c),children:[xn(Ti.Trigger,{name:n,type:"button","aria-label":o,onClick:()=>l(!a),ref:s,className:a?"active":"",children:t.find(c=>c.value===e)?.icon}),a&&xn(d2,{options:t,value:e,label:o,onChange:r,onClose:()=>{l(!1)},numberOfOptionsToAlwaysShow:i})]})})}import D2,{useCallback as P2,useMemo as R2}from"react";import*as zy from"@radix-ui/react-popover";import S2,{useMemo as Ar,useState as k2,useRef as L2,useEffect as A2,useCallback as _2}from"react";import m2 from"clsx";import p2 from"react";import{jsx as u2,jsxs as g2}from"react/jsx-runtime";var ic=p2.forwardRef(({className:e,placeholder:o,onChange:t},r)=>g2("div",{className:m2("QuickSearch__wrapper",e),children:[Ft,u2("input",{ref:r,className:"QuickSearch__input",type:"text",placeholder:o,onChange:n=>t(n.target.value.trim().toLowerCase())})]}));import h2 from"clsx";import{Children as f2}from"react";import{jsx as _y}from"react/jsx-runtime";var ac=({className:e,placeholder:o,children:t})=>{let r=!f2.count(t);return _y("div",{className:h2("ScrollableList__wrapper",e),role:"menu",children:r?_y("div",{className:"empty",children:o}):t})};import{jsx as b2,jsxs as x2}from"react/jsx-runtime";var My=({children:e,className:o="",style:t,title:r})=>x2("div",{className:`dropdown-menu-group ${o}`,style:t,children:[r&&b2("p",{className:"dropdown-menu-group-title",children:r}),e]}),Na=My;My.displayName="DropdownMenuGroup";import{useEffect as C2,useRef as I2}from"react";import E2,{useContext as y2}from"react";var Mp=E2.createContext({}),lc=(e="",o=!1,t=!1)=>`dropdown-menu-item dropdown-menu-item-base ${e}
  ${o?"dropdown-menu-item--selected":""} ${t?"dropdown-menu-item--hovered":""}`.trim(),sc=(e,o)=>{let t=y2(Mp);return ur(e,r=>{let n=new CustomEvent("menu.itemSelect",{bubbles:!0,cancelable:!0});o?.(n),n.defaultPrevented||t.onSelect?.(n)})};import{Fragment as w2,jsx as Dp,jsxs as T2}from"react/jsx-runtime";var v2=({textStyle:e,icon:o,shortcut:t,children:r})=>{let n=me();return T2(w2,{children:[o&&Dp("div",{className:"dropdown-menu-item__icon",children:o}),Dp("div",{style:e,className:"dropdown-menu-item__text",children:r}),t&&!n.editor.isMobile&&Dp("div",{className:"dropdown-menu-item__shortcut",children:t})]})},cc=v2;import{jsx as Pp}from"react/jsx-runtime";var Rp=({icon:e,value:o,order:t,children:r,shortcut:n,className:i,hovered:a,selected:l,textStyle:s,onSelect:c,onClick:m,...d})=>{let p=sc(m,c),u=I2(null);return C2(()=>{a&&(t===0?u.current?.scrollIntoView({block:"end"}):u.current?.scrollIntoView({block:"nearest"}))},[a,t]),Pp("button",{...d,ref:u,value:o,onClick:p,className:lc(i,l,a),title:d.title??d["aria-label"],children:Pp(cc,{textStyle:s,icon:e,shortcut:n,children:r})})};Rp.displayName="DropdownMenuItem";var Ci={GREEN:"green",RED:"red",BLUE:"blue"},dc=({type:e=Ci.BLUE,children:o})=>{let{theme:t}=Mo(),r={display:"inline-flex",marginLeft:"auto",padding:"2px 4px",borderRadius:6,fontSize:9,fontFamily:"Cascadia, monospace",border:t===re.LIGHT?"1.5px solid white":"none"};switch(e){case Ci.GREEN:Object.assign(r,{backgroundColor:"var(--background-color-badge)",color:"var(--color-badge)"});break;case Ci.RED:Object.assign(r,{backgroundColor:"pink",color:"darkred"});break;case Ci.BLUE:default:Object.assign(r,{background:"var(--color-promo)",color:"var(--color-surface-lowest)"})}return Pp("div",{className:"DropDownMenuItemBadge",style:r,children:o})};dc.displayName="DropdownMenuItemBadge";Rp.Badge=dc;var vt=Rp;var Dy=({event:e,inputRef:o,hoveredFont:t,filteredFonts:r,onClose:n,onSelect:i,onHover:a})=>{if(!e[y.CTRL_OR_CMD]&&e.shiftKey&&e.key.toLowerCase()===y.F)return o.current?.focus(),!0;if(e.key===y.ESCAPE)return n(),!0;if(e.key===y.ENTER)return t?.value&&i(t.value),!0;if(e.key===y.ARROW_DOWN)return t?.next?a(t.next.value):r[0]?.value&&a(r[0].value),!0;if(e.key===y.ARROW_UP)return t?.prev?a(t.prev.value):r[r.length-1]?.value&&a(r[r.length-1].value),!0};import{jsx as Oa,jsxs as Py}from"react/jsx-runtime";var Ry=S2.memo(({selectedFontFamily:e,hoveredFontFamily:o,onSelect:t,onHover:r,onLeave:n,onOpen:i,onClose:a})=>{let{container:l}=Ke(),{fonts:s}=He(),{showDeprecatedFonts:c}=Do(),[m,d]=k2(""),p=L2(null),u=Ar(()=>Array.from(br.registered.entries()).filter(([k,{metadata:R}])=>!R.serverSide&&!R.fallback).map(([k,{metadata:R,fontFaces:M}])=>{let N={value:k,icon:R.icon??Xl,text:M[0]?.fontFace?.family??"Unknown"};return R.deprecated&&Object.assign(N,{deprecated:R.deprecated,badge:{type:Ci.RED,placeholder:g("fontList.badge.old")}}),N}).sort((k,R)=>k.text.toLowerCase()>R.text.toLowerCase()?1:-1),[]),h=Ar(()=>new Set(s.getSceneFamilies()),[e]),f=Ar(()=>u.filter(k=>h.has(k.value)),[u,h]),b=Ar(()=>u.filter(k=>!h.has(k.value)&&(c||!k.deprecated)),[u,h,c]),x=Ar(()=>Yh([...f,...b].filter(k=>k.text?.toLowerCase().includes(m))),[f,b,m]),T=Ar(()=>{let k;return o?k=x.find(R=>R.value===o):e&&(k=x.find(R=>R.value===e)),!k&&m&&(x[0]?.value?r(x[0].value):n()),k},[o,e,m,x,r,n]),E=_2(k=>{Dy({event:k,inputRef:p,hoveredFont:T,filteredFonts:x,onSelect:t,onHover:r,onClose:a})&&(k.preventDefault(),k.stopPropagation())},[T,x,t,r,a]);A2(()=>(i(),()=>{a()}),[]);let w=Ar(()=>x.filter(k=>h.has(k.value)),[x,h]),S=Ar(()=>x.filter(k=>!h.has(k.value)),[x,h]),I=(k,R)=>Py(vt,{icon:k.icon,value:k.value,order:R,textStyle:{fontFamily:Xr({fontFamily:k.value})},hovered:k.value===T?.value,selected:k.value===e,tabIndex:k.value===e?0:-1,onClick:M=>{t(Number(M.currentTarget.value))},onMouseMove:()=>{T?.value!==k.value&&r(k.value)},children:[k.text,k.badge&&Oa(dc,{type:k.badge.type,children:k.badge.placeholder})]},k.value),_=[];return w.length&&_.push(Oa(Na,{title:g("fontList.sceneFonts"),children:w.map(I)},"group_1")),S.length&&_.push(Oa(Na,{title:g("fontList.availableFonts"),children:S.map((k,R)=>I(k,R+w.length))},"group_2")),Py(oc,{className:"properties-content",container:l,style:{width:"15rem"},onClose:a,onPointerLeave:n,onKeyDown:E,children:[Oa(ic,{ref:p,placeholder:g("quickSearch.placeholder"),onChange:$r(d,20)}),Oa(ac,{className:"dropdown-menu fonts manual-hover",placeholder:g("fontList.empty"),children:_.length?_:null})]})},(e,o)=>e.selectedFontFamily===o.selectedFontFamily&&e.hoveredFontFamily===o.hoveredFontFamily);import*as Ny from"@radix-ui/react-popover";import{useMemo as M2}from"react";import{jsx as Np}from"react/jsx-runtime";var Oy=({selectedFontFamily:e})=>{let o=M2(()=>!!(e&&!Fy(e)),[e]);return Np(Ny.Trigger,{asChild:!0,children:Np("div",{children:Np(Js,{standalone:!0,icon:Sf,title:g("labels.showFonts"),className:"properties-trigger",testId:"font-family-show-fonts",active:o,onClick:()=>{}})})})};import{jsx as mc,jsxs as By}from"react/jsx-runtime";var Uy=[{value:Kr.Excalifont,icon:Hl,text:g("labels.handDrawn"),testId:"font-family-hand-drawn"},{value:Kr.Nunito,icon:Xl,text:g("labels.normal"),testId:"font-family-normal"},{value:Kr["Comic Shanns"],icon:bb,text:g("labels.code"),testId:"font-family-code"}],N2=new Set(Uy.map(e=>e.value)),Fy=e=>e?N2.has(e):!1,Hy=D2.memo(({isOpened:e,selectedFontFamily:o,hoveredFontFamily:t,onSelect:r,onHover:n,onLeave:i,onPopupChange:a})=>{let l=R2(()=>Uy,[]),s=P2(c=>{c&&r(c)},[r]);return By("div",{role:"dialog","aria-modal":"true",className:"FontPicker__container",children:[mc(eo,{type:"button",options:l,value:o,onClick:s}),mc(qs,{}),By(zy.Root,{open:e,onOpenChange:a,children:[mc(Oy,{selectedFontFamily:o}),e&&mc(Ry,{selectedFontFamily:o,hoveredFontFamily:t,onSelect:s,onHover:n,onLeave:i,onOpen:()=>a(!0),onClose:()=>a(!1)})]})]})},(e,o)=>e.isOpened===o.isOpened&&e.selectedFontFamily===o.selectedFontFamily&&e.hoveredFontFamily===o.hoveredFontFamily);import Gy,{useEffect as O2}from"react";import{jsx as Op,jsxs as Yy}from"react/jsx-runtime";var Vy=({updateData:e,appState:o,elements:t,testId:r})=>{let n=Gy.useRef(null),i=Gy.useRef(null),a=gt(t,o,l=>l.opacity,!0,o.currentItemOpacity);return O2(()=>{if(n.current&&i.current){let l=n.current,s=i.current,c=l.offsetWidth,m=15,d=a/100*(c-m)+m/2;s.style.left=`${d}px`,l.style.background=`linear-gradient(to right, var(--color-slider-track) 0%, var(--color-slider-track) ${a}%, var(--button-bg) ${a}%, var(--button-bg) 100%)`}},[a]),Yy("label",{className:"control-label",children:[g("labels.opacity"),Yy("div",{className:"range-wrapper",children:[Op("input",{ref:n,type:"range",min:"0",max:"100",step:"10",onChange:l=>{e(+l.target.value)},value:a,className:"range-input","data-testid":r}),Op("div",{className:"value-bubble",ref:i,children:a!==0?a:null}),Op("div",{className:"zero-label",children:"0"})]})]})};import{Fragment as Xy,jsx as ae,jsxs as zt}from"react/jsx-runtime";var Ky=.1,wt=(e,o,t,r=!1)=>{let n=Q($(e,o,{includeBoundTextElement:r}));return e.map(i=>n.get(i.id)||i.id===o.editingTextElement?.id?t(i):i)},gt=function(e,o,t,r,n){let i=o.editingTextElement,a=se(e),l=null;return i&&(l=t(i)),l||(ke(a,o)?l=UE(r===!0?a:a.filter(c=>r(c)),o,t)??(typeof n=="function"?n(!0):n):l=typeof n=="function"?n(!1):n),l},z2=(e,o)=>qe(o)||!o.autoResize?o:P(o,{x:e.textAlign==="left"?e.x:e.x+(e.width-o.width)/(e.textAlign==="center"?2:1),y:e.y+(e.height-o.height)/2},!1),zp=(e,o,t,r,n)=>{let i=new Set,a=wt(e,o,s=>{if(Y(s)){let c=r(s);i.add(c);let m=q(s,{fontSize:c});return Ue(m,t.scene.getContainerElement(s),t.scene.getNonDeletedElementsMap()),m=z2(s,m),m}return s},!0),l=Q(a);return $(e,o,{includeBoundTextElement:!0}).forEach(s=>{Y(s)&&Jt(s,l)}),{elements:a,appState:{...o,currentItemFontSize:i.size===1?[...i][0]:n??o.currentItemFontSize},captureUpdate:L.IMMEDIATELY}},U2=D({name:"changeStrokeColor",label:"labels.stroke",trackEvent:!1,perform:(e,o,t)=>({...t.currentItemStrokeColor&&{elements:wt(e,o,r=>Ia(r.type)?q(r,{strokeColor:t.currentItemStrokeColor}):r,!0)},appState:{...o,...t},captureUpdate:t.currentItemStrokeColor?L.IMMEDIATELY:L.EVENTUALLY}),PanelComponent:({elements:e,appState:o,updateData:t,appProps:r})=>zt(Xy,{children:[ae("h3",{"aria-hidden":"true",children:g("labels.stroke")}),ae(Pa,{topPicks:hl,palette:ih,type:"elementStroke",label:g("labels.stroke"),color:gt(e,o,n=>n.strokeColor,!0,o.currentItemStrokeColor),onChange:n=>t({currentItemStrokeColor:n}),elements:e,appState:o,updateData:t})]})}),H2=D({name:"changeBackgroundColor",label:"labels.changeBackground",trackEvent:!1,perform:(e,o,t)=>({...t.currentItemBackgroundColor&&{elements:wt(e,o,r=>q(r,{backgroundColor:t.currentItemBackgroundColor}))},appState:{...o,...t},captureUpdate:t.currentItemBackgroundColor?L.IMMEDIATELY:L.EVENTUALLY}),PanelComponent:({elements:e,appState:o,updateData:t,appProps:r})=>zt(Xy,{children:[ae("h3",{"aria-hidden":"true",children:g("labels.background")}),ae(Pa,{topPicks:fl,palette:ah,type:"elementBackground",label:g("labels.background"),color:gt(e,o,n=>n.backgroundColor,!0,o.currentItemBackgroundColor),onChange:n=>t({currentItemBackgroundColor:n}),elements:e,appState:o,updateData:t})]})}),G2=D({name:"changeFillStyle",label:"labels.fill",trackEvent:!1,perform:(e,o,t,r)=>(le("element","changeFillStyle",`${t} (${r.device.editor.isMobile?"mobile":"desktop"})`),{elements:wt(e,o,n=>q(n,{fillStyle:t})),appState:{...o,currentItemFillStyle:t},captureUpdate:L.IMMEDIATELY}),PanelComponent:({elements:e,appState:o,updateData:t})=>{let r=$(e,o),n=r.length>0&&r.every(i=>i.fillStyle==="zigzag");return zt("fieldset",{children:[ae("legend",{children:g("labels.fill")}),ae(eo,{type:"button",options:[{value:"hachure",text:`${n?g("labels.zigzag"):g("labels.hachure")} (${A("Alt-Click")})`,icon:n?Gf:Yf,active:n?!0:void 0,testId:"fill-hachure"},{value:"cross-hatch",text:g("labels.crossHatch"),icon:Vf,testId:"fill-cross-hatch"},{value:"solid",text:g("labels.solid"),icon:Wf,testId:"fill-solid"}],value:gt(e,o,i=>i.fillStyle,i=>i.hasOwnProperty("fillStyle"),i=>i?null:o.currentItemFillStyle),onClick:(i,a)=>{let l=a.altKey&&i==="hachure"&&r.every(s=>s.fillStyle==="hachure")?"zigzag":i;t(l)}})]})}}),Y2=D({name:"changeStrokeWidth",label:"labels.strokeWidth",trackEvent:!1,perform:(e,o,t)=>({elements:wt(e,o,r=>q(r,{strokeWidth:t})),appState:{...o,currentItemStrokeWidth:t},captureUpdate:L.IMMEDIATELY}),PanelComponent:({elements:e,appState:o,updateData:t})=>zt("fieldset",{children:[ae("legend",{children:g("labels.strokeWidth")}),ae(eo,{group:"stroke-width",options:[{value:kl.thin,text:g("labels.thin"),icon:Tm,testId:"strokeWidth-thin"},{value:kl.bold,text:g("labels.bold"),icon:Kf,testId:"strokeWidth-bold"},{value:kl.extraBold,text:g("labels.extraBold"),icon:Xf,testId:"strokeWidth-extraBold"}],value:gt(e,o,r=>r.strokeWidth,r=>r.hasOwnProperty("strokeWidth"),r=>r?null:o.currentItemStrokeWidth),onChange:r=>t(r)})]})}),V2=D({name:"changeSloppiness",label:"labels.sloppiness",trackEvent:!1,perform:(e,o,t)=>({elements:wt(e,o,r=>q(r,{seed:pa(),roughness:t})),appState:{...o,currentItemRoughness:t},captureUpdate:L.IMMEDIATELY}),PanelComponent:({elements:e,appState:o,updateData:t})=>zt("fieldset",{children:[ae("legend",{children:g("labels.sloppiness")}),ae(eo,{group:"sloppiness",options:[{value:0,text:g("labels.architect"),icon:jf},{value:1,text:g("labels.artist"),icon:Jf},{value:2,text:g("labels.cartoonist"),icon:qf}],value:gt(e,o,r=>r.roughness,r=>r.hasOwnProperty("roughness"),r=>r?null:o.currentItemRoughness),onChange:r=>t(r)})]})}),p8=D({name:"changeStrokeStyle",label:"labels.strokeStyle",trackEvent:!1,perform:(e,o,t)=>({elements:wt(e,o,r=>q(r,{strokeStyle:t})),appState:{...o,currentItemStrokeStyle:t},captureUpdate:L.IMMEDIATELY}),PanelComponent:({elements:e,appState:o,updateData:t})=>zt("fieldset",{children:[ae("legend",{children:g("labels.strokeStyle")}),ae(eo,{group:"strokeStyle",options:[{value:"solid",text:g("labels.strokeStyle_solid"),icon:Tm},{value:"dashed",text:g("labels.strokeStyle_dashed"),icon:$f},{value:"dotted",text:g("labels.strokeStyle_dotted"),icon:Zf}],value:gt(e,o,r=>r.strokeStyle,r=>r.hasOwnProperty("strokeStyle"),r=>r?null:o.currentItemStrokeStyle),onChange:r=>t(r)})]})}),W2=D({name:"changeOpacity",label:"labels.opacity",trackEvent:!1,perform:(e,o,t)=>({elements:wt(e,o,r=>q(r,{opacity:t}),!0),appState:{...o,currentItemOpacity:t},captureUpdate:L.IMMEDIATELY}),PanelComponent:({elements:e,appState:o,updateData:t})=>ae(Vy,{updateData:t,elements:e,appState:o,testId:"opacity"})}),K2=D({name:"changeFontSize",label:"labels.fontSize",trackEvent:!1,perform:(e,o,t,r)=>zp(e,o,r,()=>t,t),PanelComponent:({elements:e,appState:o,updateData:t,app:r})=>zt("fieldset",{children:[ae("legend",{children:g("labels.fontSize")}),ae(eo,{group:"font-size",options:[{value:16,text:g("labels.small"),icon:ub,testId:"fontSize-small"},{value:20,text:g("labels.medium"),icon:gb,testId:"fontSize-medium"},{value:28,text:g("labels.large"),icon:hb,testId:"fontSize-large"},{value:36,text:g("labels.veryLarge"),icon:fb,testId:"fontSize-veryLarge"}],value:gt(e,o,n=>{if(Y(n))return n.fontSize;let i=ue(n,r.scene.getNonDeletedElementsMap());return i?i.fontSize:null},n=>Y(n)||ue(n,r.scene.getNonDeletedElementsMap())!==null,n=>n?null:o.currentItemFontSize||Nn),onChange:n=>t(n)})]})}),Up=D({name:"decreaseFontSize",label:"labels.decreaseFontSize",icon:nn,trackEvent:!1,perform:(e,o,t,r)=>zp(e,o,r,n=>Math.round(1/(1+Ky)*n.fontSize)),keyTest:e=>e[y.CTRL_OR_CMD]&&e.shiftKey&&(e.key===y.CHEVRON_LEFT||e.key===y.COMMA)}),Hp=D({name:"increaseFontSize",label:"labels.increaseFontSize",icon:nn,trackEvent:!1,perform:(e,o,t,r)=>zp(e,o,r,n=>Math.round(n.fontSize*(1+Ky))),keyTest:e=>e[y.CTRL_OR_CMD]&&e.shiftKey&&(e.key===y.CHEVRON_RIGHT||e.key===y.PERIOD)}),X2=D({name:"changeFontFamily",label:"labels.fontFamily",trackEvent:!1,perform:(e,o,t,r)=>{let{cachedElements:n,resetAll:i,resetContainers:a,...l}=t;if(i)return{elements:wt(e,o,f=>{let b=n?.get(f.id);return b?q(f,{...b}):f},!0),appState:{...o,...l},captureUpdate:L.NEVER};let{currentItemFontFamily:s,currentHoveredFontFamily:c}=t,m=L.EVENTUALLY,d,p=!1;if(s)d=s,m=L.IMMEDIATELY;else if(c){d=c,m=L.EVENTUALLY;let h=$(e,o,{includeBoundTextElement:!0}).filter(f=>Y(f));if(h.length>200)p=!0;else{let f=0,b=0;for(;f<h.length&&b<5e3;){let x=h[f];b+=x?.originalText.length||0,f++}b>5e3&&(p=!0)}}let u={appState:{...o,...l},captureUpdate:m};if(d&&!p){let h=new Map,f=new Set,b=!1,x=Array.from(br.loadedFontsCache.values()),T=Object.entries(Kr).find(([S,I])=>I===d)?.[0];c&&T&&x.some(S=>S.startsWith(T))&&(b=!0),Object.assign(u,{elements:wt(e,o,S=>{if(Y(S)&&(S.fontFamily!==d||s)){let I=q(S,{fontFamily:d,lineHeight:$o(d)}),_=n?.get(S.containerId||"")||{},k=r.scene.getContainerElement(S);return a&&k&&_&&P(k,{..._},!1),b||(f=new Set([...f,...Array.from(I.originalText)])),h.set(I,k),I}return S},!0)});let E=`10px ${Xr({fontFamily:d})}`,w=Array.from(f.values()).join();if(b||window.document.fonts.check(E,w))for(let[S,I]of h)Ue(S,I,r.scene.getNonDeletedElementsMap(),!1);else window.document.fonts.load(E,w).then(S=>{for(let[I,_]of h){let k=r.scene.getElement(I.id),R=_?r.scene.getElement(_.id):null;k&&Ue(k,R,r.scene.getNonDeletedElementsMap(),!1)}r.fonts.onLoaded(S)})}return u},PanelComponent:({elements:e,appState:o,app:t,updateData:r})=>{let n=Bp(new Map),i=Bp(null),[a,l]=B2({}),s=Bp(!0),c=F2(()=>{let m=(d,p)=>gt(d,o,u=>{if(Y(u))return u.fontFamily;let h=ue(u,p);return h?h.fontFamily:null},u=>Y(u)||ue(u,p)!==null,u=>u?null:o.currentItemFontFamily||On);return a.openPopup==="fontFamily"&&o.openPopup==="fontFamily"?m(Array.from(n.current?.values()??[]),n.current):!a.openPopup&&o.openPopup!=="fontFamily"?m(e,t.scene.getNonDeletedElementsMap()):i.current},[a.openPopup,o,e,t.scene]);return Fp(()=>{i.current=c},[c]),Fp(()=>{Object.keys(a).length&&(r(a),l({}))},[a]),Fp(()=>(s.current=!1,()=>{s.current=!0}),[]),zt("fieldset",{children:[ae("legend",{children:g("labels.fontFamily")}),ae(Hy,{isOpened:o.openPopup==="fontFamily",selectedFontFamily:c,hoveredFontFamily:o.currentHoveredFontFamily,onSelect:m=>{l({openPopup:null,currentHoveredFontFamily:null,currentItemFontFamily:m}),n.current.clear()},onHover:m=>{l({currentHoveredFontFamily:m,cachedElements:new Map(n.current),resetContainers:!0})},onLeave:()=>{l({currentHoveredFontFamily:null,cachedElements:new Map(n.current),resetAll:!0})},onPopupChange:m=>{if(m){n.current.clear();let{editingTextElement:d}=o;if(d?.type==="text"){let p=t.scene.getElement(d.id);n.current.set(d.id,q(p||d,{},!0))}else{let p=$(e,o,{includeBoundTextElement:!0});for(let u of p)n.current.set(u.id,q(u,{},!0))}l({openPopup:"fontFamily"})}else{let d={openPopup:null,currentHoveredFontFamily:null,cachedElements:new Map(n.current),resetAll:!0};s.current?r({...a,...d}):l(d),n.current.clear()}}})]})}}),$2=D({name:"changeTextAlign",label:"Change text alignment",trackEvent:!1,perform:(e,o,t,r)=>({elements:wt(e,o,n=>{if(Y(n)){let i=q(n,{textAlign:t});return Ue(i,r.scene.getContainerElement(n),r.scene.getNonDeletedElementsMap()),i}return n},!0),appState:{...o,currentItemTextAlign:t},captureUpdate:L.IMMEDIATELY}),PanelComponent:({elements:e,appState:o,updateData:t,app:r})=>{let n=r.scene.getNonDeletedElementsMap();return zt("fieldset",{children:[ae("legend",{children:g("labels.textAlign")}),ae(eo,{group:"text-align",options:[{value:"left",text:g("labels.left"),icon:xb,testId:"align-left"},{value:"center",text:g("labels.center"),icon:Eb,testId:"align-horizontal-center"},{value:"right",text:g("labels.right"),icon:yb,testId:"align-right"}],value:gt(e,o,i=>{if(Y(i))return i.textAlign;let a=ue(i,n);return a?a.textAlign:null},i=>Y(i)||ue(i,n)!==null,i=>i?null:o.currentItemTextAlign),onChange:i=>t(i)})]})}}),Z2=D({name:"changeVerticalAlign",label:"Change vertical alignment",trackEvent:{category:"element"},perform:(e,o,t,r)=>({elements:wt(e,o,n=>{if(Y(n)){let i=q(n,{verticalAlign:t});return Ue(i,r.scene.getContainerElement(n),r.scene.getNonDeletedElementsMap()),i}return n},!0),appState:{...o},captureUpdate:L.IMMEDIATELY}),PanelComponent:({elements:e,appState:o,updateData:t,app:r})=>ae("fieldset",{children:ae(eo,{group:"text-align",options:[{value:Kt.TOP,text:g("labels.alignTop"),icon:ae(vb,{theme:o.theme}),testId:"align-top"},{value:Kt.MIDDLE,text:g("labels.centerVertically"),icon:ae(Tb,{theme:o.theme}),testId:"align-middle"},{value:Kt.BOTTOM,text:g("labels.alignBottom"),icon:ae(wb,{theme:o.theme}),testId:"align-bottom"}],value:gt(e,o,n=>{if(Y(n)&&n.containerId)return n.verticalAlign;let i=ue(n,r.scene.getNonDeletedElementsMap());return i?i.verticalAlign:null},n=>Y(n)||ue(n,r.scene.getNonDeletedElementsMap())!==null,n=>n?null:Kt.MIDDLE),onChange:n=>t(n)})})}),u8=D({name:"changeRoundness",label:"Change edge roundness",trackEvent:!1,perform:(e,o,t)=>({elements:wt(e,o,r=>ee(r)?r:q(r,{roundness:t==="round"?{type:Kn(r.type)?St.ADAPTIVE_RADIUS:St.PROPORTIONAL_RADIUS}:null})),appState:{...o,currentItemRoundness:t},captureUpdate:L.IMMEDIATELY}),PanelComponent:({elements:e,appState:o,updateData:t})=>{let n=Vs(se(e),o).some(i=>i.roundness?.type===St.LEGACY);return zt("fieldset",{children:[ae("legend",{children:g("labels.edges")}),ae(eo,{group:"edges",options:[{value:"sharp",text:g("labels.sharp"),icon:Qf},{value:"round",text:g("labels.round"),icon:eb}],value:gt(e,o,i=>n?null:i.roundness?"round":"sharp",i=>!Ne(i)&&i.hasOwnProperty("roundness"),i=>i?null:o.currentItemRoundness),onChange:i=>t(i)})]})}}),Wy=e=>[{value:null,text:g("labels.arrowhead_none"),keyBinding:"q",icon:tb},{value:"arrow",text:g("labels.arrowhead_arrow"),keyBinding:"w",icon:ae(ob,{flip:e})},{value:"triangle",text:g("labels.arrowhead_triangle"),icon:ae(ab,{flip:e}),keyBinding:"e"},{value:"triangle_outline",text:g("labels.arrowhead_triangle_outline"),icon:ae(lb,{flip:e}),keyBinding:"r"},{value:"circle",text:g("labels.arrowhead_circle"),keyBinding:"a",icon:ae(rb,{flip:e})},{value:"circle_outline",text:g("labels.arrowhead_circle_outline"),keyBinding:"s",icon:ae(nb,{flip:e})},{value:"diamond",text:g("labels.arrowhead_diamond"),icon:ae(sb,{flip:e}),keyBinding:"d"},{value:"diamond_outline",text:g("labels.arrowhead_diamond_outline"),icon:ae(cb,{flip:e}),keyBinding:"f"},{value:"bar",text:g("labels.arrowhead_bar"),keyBinding:"z",icon:ae(ib,{flip:e})},{value:"crowfoot_one",text:g("labels.arrowhead_crowfoot_one"),icon:ae(mb,{flip:e}),keyBinding:"c"},{value:"crowfoot_many",text:g("labels.arrowhead_crowfoot_many"),icon:ae(db,{flip:e}),keyBinding:"x"},{value:"crowfoot_one_or_many",text:g("labels.arrowhead_crowfoot_one_or_many"),icon:ae(pb,{flip:e}),keyBinding:"v"}],g8=D({name:"changeArrowhead",label:"Change arrowheads",trackEvent:!1,perform:(e,o,t)=>({elements:wt(e,o,r=>{if(he(r)){let{position:n,type:i}=t;if(n==="start")return q(r,{startArrowhead:i});if(n==="end")return q(r,{endArrowhead:i})}return r}),appState:{...o,[t.position==="start"?"currentItemStartArrowhead":"currentItemEndArrowhead"]:t.type},captureUpdate:L.IMMEDIATELY}),PanelComponent:({elements:e,appState:o,updateData:t})=>{let r=qo().rtl;return zt("fieldset",{children:[ae("legend",{children:g("labels.arrowheads")}),zt("div",{className:"iconSelectList buttonList",children:[ae(_p,{label:"arrowhead_start",options:Wy(!r),value:gt(e,o,n=>he(n)&&pi(n.type)?n.startArrowhead:o.currentItemStartArrowhead,!0,o.currentItemStartArrowhead),onChange:n=>t({position:"start",type:n}),numberOfOptionsToAlwaysShow:4}),ae(_p,{label:"arrowhead_end",group:"arrowheads",options:Wy(!!r),value:gt(e,o,n=>he(n)&&pi(n.type)?n.endArrowhead:o.currentItemEndArrowhead,!0,o.currentItemEndArrowhead),onChange:n=>t({position:"end",type:n}),numberOfOptionsToAlwaysShow:4})]})]})}}),h8=D({name:"changeArrowType",label:"Change arrow types",trackEvent:!1,perform:(e,o,t,r)=>{let n=wt(e,o,l=>{if(!Ne(l))return l;let s=q(l,{roundness:t===Je.round?{type:St.PROPORTIONAL_RADIUS}:null,elbowed:t===Je.elbow,points:t===Je.elbow||l.elbowed?[l.points[0],l.points[l.points.length-1]]:l.points});if(ee(s)){let c=r.scene.getNonDeletedElementsMap();r.dismissLinearEditor();let m=K.getPointAtIndexGlobalCoordinates(s,0,c),d=K.getPointAtIndexGlobalCoordinates(s,-1,c),p=!s.startBinding&&Er(pr(m),e,c,o.zoom,!1,!0),u=!s.endBinding&&Er(pr(d),e,c,o.zoom,!1,!0),h=p||s.startBinding&&c.get(s.startBinding.elementId),f=u||s.endBinding&&c.get(s.endBinding.elementId),b=p?Lm(s,p,"start"):m,x=u?Lm(s,u,"end"):d;p&&Zo(s,p,"start",c),u&&Zo(s,u,"end",c),P(s,{points:[b,x].map(T=>z(T[0]-s.x,T[1]-s.y)),...h&&s.startBinding?{startBinding:{...s.startBinding,...Am(s,h,"start",c)}}:{},...f&&s.endBinding?{endBinding:{...s.endBinding,...Am(s,f,"end",c)}}:{}}),K.updateEditorMidPointsCache(s,c,r.state)}return s}),i={...o,currentItemArrowType:t},a=o.selectedLinearElement?.elementId;if(a){let l=n.find(s=>s.id===a);l&&(i.selectedLinearElement=new K(l))}return{elements:n,appState:i,captureUpdate:L.IMMEDIATELY}},PanelComponent:({elements:e,appState:o,updateData:t})=>zt("fieldset",{children:[ae("legend",{children:g("labels.arrowtypes")}),ae(eo,{group:"arrowtypes",options:[{value:Je.sharp,text:g("labels.arrowtype_sharp"),icon:jb,testId:"sharp-arrow"},{value:Je.round,text:g("labels.arrowtype_round"),icon:qb,testId:"round-arrow"},{value:Je.elbow,text:g("labels.arrowtype_elbowed"),icon:Jb,testId:"elbow-arrow"}],value:gt(e,o,r=>Ne(r)?r.elbowed?Je.elbow:r.roundness?Je.round:Je.sharp:null,r=>Ne(r),r=>r?null:o.currentItemArrowType),onChange:r=>t(r)})]})});var Qo=({viewportX:e,viewportY:o,nextZoom:t},r)=>{let n=e-r.offsetLeft,i=o-r.offsetTop,a=r.zoom.value,l=r.scrollX+(n-n/a),s=r.scrollY+(i-i/a),c=-(n-n/t),m=-(i-i/t);return{scrollX:l+c,scrollY:s+m,zoom:{value:t}}};import{useEffect as j2}from"react";import{jsx as q2}from"react/jsx-runtime";var Ii=()=>{let e=document.querySelector(".excalidraw-tooltip");if(e)return e;let o=document.createElement("div");return document.body.appendChild(o),o.classList.add("excalidraw-tooltip"),o},Gp=(e,o,t="bottom")=>{let r=e.getBoundingClientRect(),n=window.innerWidth,i=window.innerHeight,a=5,l=o.left+o.width/2-r.width/2;l<0?l=a:l+r.width>=n&&(l=n-r.width-a);let s;t==="bottom"?(s=o.top+o.height+a,s+r.height>=i&&(s=o.top-r.height-a)):(s=o.top-r.height-a,s<0&&(s=o.top+o.height+a)),Object.assign(e.style,{top:`${s}px`,left:`${l}px`})},J2=(e,o,t,r)=>{o.classList.add("excalidraw-tooltip--visible"),o.style.minWidth=r?"50ch":"10ch",o.style.maxWidth=r?"50ch":"15ch",o.textContent=t;let n=e.getBoundingClientRect();Gp(o,n)},Ut=({children:e,label:o,long:t=!1,style:r,disabled:n})=>(j2(()=>()=>Ii().classList.remove("excalidraw-tooltip--visible"),[]),n?null:q2("div",{className:"excalidraw-tooltip-wrapper",onPointerEnter:i=>J2(i.currentTarget,Ii(),o,t),onPointerLeave:()=>Ii().classList.remove("excalidraw-tooltip--visible"),style:r,children:e}));import{jsx as hc,jsxs as oI}from"react/jsx-runtime";var Q2=D({name:"changeViewBackgroundColor",label:"labels.canvasBackground",paletteName:"Change canvas background color",trackEvent:!1,predicate:(e,o,t,r)=>!!r.props.UIOptions.canvasActions.changeViewBackgroundColor&&!o.viewModeEnabled,perform:(e,o,t)=>({appState:{...o,...t},captureUpdate:t.viewBackgroundColor?L.IMMEDIATELY:L.EVENTUALLY}),PanelComponent:({elements:e,appState:o,updateData:t,appProps:r})=>hc(Pa,{palette:null,topPicks:bl,label:g("labels.canvasBackground"),type:"canvasBackground",color:o.viewBackgroundColor,onChange:n=>t({viewBackgroundColor:n}),"data-testid":"canvas-background-picker",elements:e,appState:o,updateData:t})}),er=D({name:"clearCanvas",label:"labels.clearCanvas",paletteName:"Clear canvas",icon:Ot,trackEvent:{category:"canvas"},predicate:(e,o,t,r)=>!!r.props.UIOptions.canvasActions.clearCanvas&&!o.viewModeEnabled&&o.openDialog?.name!=="elementLinkSelector",perform:(e,o,t,r)=>(r.imageCache.clear(),{elements:e.map(n=>q(n,{isDeleted:!0})),appState:{...$n(),files:{},theme:o.theme,penMode:o.penMode,penDetected:o.penDetected,exportBackground:o.exportBackground,exportEmbedScene:o.exportEmbedScene,gridSize:o.gridSize,gridStep:o.gridStep,gridModeEnabled:o.gridModeEnabled,stats:o.stats,pasteDialog:o.pasteDialog,activeTool:o.activeTool.type==="image"?{...o.activeTool,type:"selection"}:o.activeTool},captureUpdate:L.IMMEDIATELY})}),pc=D({name:"zoomIn",label:"buttons.zoomIn",viewMode:!0,icon:am,trackEvent:{category:"canvas"},perform:(e,o,t,r)=>({appState:{...o,...Qo({viewportX:o.width/2+o.offsetLeft,viewportY:o.height/2+o.offsetTop,nextZoom:Ao(o.zoom.value+Fn)},o),userToFollow:null},captureUpdate:L.EVENTUALLY}),PanelComponent:({updateData:e,appState:o})=>hc(X,{type:"button",className:"zoom-in-button zoom-button",icon:am,title:`${g("buttons.zoomIn")} \u2014 ${A("CtrlOrCmd++")}`,"aria-label":g("buttons.zoomIn"),disabled:o.zoom.value>=Xd,onClick:()=>{e(null)}}),keyTest:e=>(e.code===te.EQUAL||e.code===te.NUM_ADD)&&(e[y.CTRL_OR_CMD]||e.shiftKey)}),uc=D({name:"zoomOut",label:"buttons.zoomOut",icon:lm,viewMode:!0,trackEvent:{category:"canvas"},perform:(e,o,t,r)=>({appState:{...o,...Qo({viewportX:o.width/2+o.offsetLeft,viewportY:o.height/2+o.offsetTop,nextZoom:Ao(o.zoom.value-Fn)},o),userToFollow:null},captureUpdate:L.EVENTUALLY}),PanelComponent:({updateData:e,appState:o})=>hc(X,{type:"button",className:"zoom-out-button zoom-button",icon:lm,title:`${g("buttons.zoomOut")} \u2014 ${A("CtrlOrCmd+-")}`,"aria-label":g("buttons.zoomOut"),disabled:o.zoom.value<=Kd,onClick:()=>{e(null)}}),keyTest:e=>(e.code===te.MINUS||e.code===te.NUM_SUBTRACT)&&(e[y.CTRL_OR_CMD]||e.shiftKey)}),gc=D({name:"resetZoom",label:"buttons.resetZoom",icon:kf,viewMode:!0,trackEvent:{category:"canvas"},perform:(e,o,t,r)=>({appState:{...o,...Qo({viewportX:o.width/2+o.offsetLeft,viewportY:o.height/2+o.offsetTop,nextZoom:Ao(1)},o),userToFollow:null},captureUpdate:L.EVENTUALLY}),PanelComponent:({updateData:e,appState:o})=>hc(Ut,{label:g("buttons.resetZoom"),style:{height:"100%"},children:oI(X,{type:"button",className:"reset-zoom-button zoom-button",title:g("buttons.resetZoom"),"aria-label":g("buttons.resetZoom"),onClick:()=>{e(null)},children:[(o.zoom.value*100).toFixed(0),"%"]})}),keyTest:e=>(e.code===te.ZERO||e.code===te.NUM_ZERO)&&(e[y.CTRL_OR_CMD]||e.shiftKey)}),eI=(e,o,t=1)=>{let[r,n,i,a]=e,l=i-r,s=o.width/l,c=a-n,m=o.height/c,p=Math.min(s,m)*ot(t,.1,1);return Math.min(p,1)},$y=({bounds:e,appState:o,canvasOffsets:t,fitToViewport:r=!1,viewportZoomFactor:n=1,minZoom:i=-1/0,maxZoom:a=1/0})=>{n=ot(n,Kd,Xd);let[l,s,c,m]=e,d=(l+c)/2,p=(s+m)/2,u=t?.left??0,h=t?.top??0,f=t?.right??0,b=t?.bottom??0,x=o.width-u-f,T=o.height-h-b,E;if(r){let I=c-l,_=m-s;E=Math.min(x/I,T/_)*n}else E=eI(e,{width:x,height:T},n);let w=Ao(ot(Ph(E,Fn,"floor"),i,a)),S=HE({scenePoint:{x:d,y:p},viewportDimensions:{width:o.width,height:o.height},offsets:t,zoom:{value:w}});return{appState:{...o,scrollX:S.scrollX,scrollY:S.scrollY,zoom:{value:w}},captureUpdate:L.EVENTUALLY}},Fa=({canvasOffsets:e,targetElements:o,appState:t,fitToViewport:r,viewportZoomFactor:n,minZoom:i,maxZoom:a})=>{let l=Se(se(o));return $y({canvasOffsets:e,bounds:l,appState:t,fitToViewport:r,viewportZoomFactor:n,minZoom:i,maxZoom:a})},$8=D({name:"zoomToFitSelectionInViewport",label:"labels.zoomToFitViewport",icon:ts,trackEvent:{category:"canvas"},perform:(e,o,t,r)=>{let n=r.scene.getSelectedElements(o);return Fa({targetElements:n.length?n:e,appState:{...o,userToFollow:null},fitToViewport:!1,canvasOffsets:r.getEditorUIOffsets()})},keyTest:e=>e.code===te.TWO&&e.shiftKey&&!e.altKey&&!e[y.CTRL_OR_CMD]}),Z8=D({name:"zoomToFitSelection",label:"helpDialog.zoomToSelection",icon:ts,trackEvent:{category:"canvas"},perform:(e,o,t,r)=>{let n=r.scene.getSelectedElements(o);return Fa({targetElements:n.length?n:e,appState:{...o,userToFollow:null},fitToViewport:!0,canvasOffsets:r.getEditorUIOffsets()})},keyTest:e=>e.code===te.THREE&&e.shiftKey&&!e.altKey&&!e[y.CTRL_OR_CMD]}),tI=D({name:"zoomToFit",label:"helpDialog.zoomToFit",icon:ts,viewMode:!0,trackEvent:{category:"canvas"},perform:(e,o,t,r)=>Fa({targetElements:e,appState:{...o,userToFollow:null},fitToViewport:!1,canvasOffsets:r.getEditorUIOffsets()}),keyTest:e=>e.code===te.ONE&&e.shiftKey&&!e.altKey&&!e[y.CTRL_OR_CMD]}),En=D({name:"toggleTheme",label:(e,o)=>o.theme===re.DARK?"buttons.lightMode":"buttons.darkMode",keywords:["toggle","dark","light","mode","theme"],icon:e=>e.theme===re.LIGHT?ga:ha,viewMode:!0,trackEvent:{category:"canvas"},perform:(e,o,t)=>({appState:{...o,theme:t||(o.theme===re.LIGHT?re.DARK:re.LIGHT)},captureUpdate:L.EVENTUALLY}),keyTest:e=>e.altKey&&e.shiftKey&&e.code===te.D,predicate:(e,o,t,r)=>!!r.props.UIOptions.canvasActions.toggleTheme}),j8=D({name:"toggleEraserTool",label:"toolBar.eraser",trackEvent:{category:"toolbar"},perform:(e,o)=>{let t;return $t(o)?t=Be(o,{...o.activeTool.lastActiveTool||{type:"selection"},lastActiveToolBeforeEraser:null}):t=Be(o,{type:"eraser",lastActiveToolBeforeEraser:o.activeTool}),{appState:{...o,selectedElementIds:{},selectedGroupIds:{},activeEmbeddable:null,activeTool:t},captureUpdate:L.IMMEDIATELY}},keyTest:e=>e.key===y.E}),Zy=D({name:"toggleHandTool",label:"toolBar.hand",paletteName:"Toggle hand tool",trackEvent:{category:"toolbar"},icon:Zl,viewMode:!1,perform:(e,o,t,r)=>{let n;return Xo(o)?n=Be(o,{...o.activeTool.lastActiveTool||{type:"selection"},lastActiveToolBeforeEraser:null}):(n=Be(o,{type:"hand",lastActiveToolBeforeEraser:o.activeTool}),Te(r.interactiveCanvas,we.GRAB)),{appState:{...o,selectedElementIds:{},selectedGroupIds:{},activeEmbeddable:null,activeTool:n},captureUpdate:L.IMMEDIATELY}},keyTest:e=>!e.altKey&&!e[y.CTRL_OR_CMD]&&e.key===y.H});import{jsx as rI}from"react/jsx-runtime";var Ht=D({name:"finalize",label:"",trackEvent:!1,perform:(e,o,t,r)=>{let{interactiveCanvas:n,focusContainer:i,scene:a}=r,l=a.getNonDeletedElementsMap();if(o.editingLinearElement){let{elementId:p,startBindingElement:u,endBindingElement:h}=o.editingLinearElement,f=K.getElement(p,l);if(f)return Wo(f)&&us(f,u,h,l,a),{elements:f.points.length<2||hn(f)?e.filter(b=>b.id!==f.id):void 0,appState:{...o,cursorButton:"up",editingLinearElement:null},captureUpdate:L.IMMEDIATELY}}let s=e,c=o.pendingImageElementId&&a.getElement(o.pendingImageElementId);c&&P(c,{isDeleted:!0},!1),window.document.activeElement instanceof HTMLElement&&i();let m=o.multiElement?o.multiElement:o.newElement?.type==="freedraw"?o.newElement:null;if(m){if(m.type!=="freedraw"&&o.lastPointerDownWith!=="touch"){let{points:u,lastCommittedPoint:h}=m;(!h||u[u.length-1]!==h)&&P(m,{points:m.points.slice(0,-1)})}hn(m)&&(s=s.filter(u=>u.id!==m.id));let p=xa(m.points,o.zoom.value);if((m.type==="line"||m.type==="freedraw")&&p){let u=m.points,h=u[0];P(m,{points:u.map((f,b)=>b===u.length-1?z(h[0],h[1]):f)})}if(Wo(m)&&!p&&m.points.length>1){let[u,h]=K.getPointAtIndexGlobalCoordinates(m,-1,Q(e));ya(m,o,{x:u,y:h},l,e)}}(!o.activeTool.locked&&o.activeTool.type!=="freedraw"||!m)&&Zt(n);let d;return o.activeTool.type==="eraser"?d=Be(o,{...o.activeTool.lastActiveTool||{type:"selection"},lastActiveToolBeforeEraser:null}):d=Be(o,{type:"selection"}),{elements:s,appState:{...o,cursorButton:"up",activeTool:(o.activeTool.locked||o.activeTool.type==="freedraw")&&m?o.activeTool:d,activeEmbeddable:null,newElement:null,selectionElement:null,multiElement:null,editingTextElement:null,startBoundElement:null,suggestedBindings:[],selectedElementIds:m&&!o.activeTool.locked&&o.activeTool.type!=="freedraw"?{...o.selectedElementIds,[m.id]:!0}:o.selectedElementIds,selectedLinearElement:m&&he(m)?new K(m):o.selectedLinearElement,pendingImageElementId:null},captureUpdate:L.IMMEDIATELY}},keyTest:(e,o)=>e.key===y.ESCAPE&&(o.editingLinearElement!==null||!o.newElement&&o.multiElement===null)||(e.key===y.ESCAPE||e.key===y.ENTER)&&o.multiElement!==null,PanelComponent:({appState:e,updateData:o,data:t})=>rI(X,{type:"button",icon:Ff,title:g("buttons.done"),"aria-label":g("buttons.done"),onClick:o,visible:e.multiElement!=null,size:t?.size||"medium",style:{pointerEvents:"all"}})});import{useState as nI}from"react";import{jsx as jy,jsxs as iI}from"react/jsx-runtime";var Jy=e=>{let{id:o}=Ke(),[t,r]=nI(e.value),n=a=>{e.ignoreFocus||Uh(a.target);let l=a.target.value;l!==e.value&&e.onChange(l)},i=a=>{if(a.key===y.ENTER){if(a.preventDefault(),a.nativeEvent.isComposing||a.keyCode===229)return;a.currentTarget.blur()}};return iI("div",{className:"ProjectName",children:[jy("label",{className:"ProjectName-label",htmlFor:"filename",children:`${e.label}:`}),jy("input",{type:"text",className:"TextInput",onBlur:n,onKeyDown:i,id:`${o}-filename`,value:t,onChange:a=>r(a.target.value)})]})};import{jsx as Ba}from"react/jsx-runtime";var Qy=e=>{let o=e.title||(e.value===re.DARK?g("buttons.lightMode"):g("buttons.darkMode"));return Ba(X,{type:"icon",icon:e.value===re.LIGHT?qy.MOON:qy.SUN,title:o,"aria-label":o,onClick:()=>e.onChange(e.value===re.DARK?re.LIGHT:re.DARK),"data-testid":"toggle-dark-mode"})},qy={SUN:Ba("svg",{width:"512",height:"512",className:"rtl-mirror",viewBox:"0 0 512 512",children:Ba("path",{fill:"currentColor",d:"M256 160c-52.9 0-96 43.1-96 96s43.1 96 96 96 96-43.1 96-96-43.1-96-96-96zm246.4 80.5l-94.7-47.3 33.5-100.4c4.5-13.6-8.4-26.5-21.9-21.9l-100.4 33.5-47.4-94.8c-6.4-12.8-24.6-12.8-31 0l-47.3 94.7L92.7 70.8c-13.6-4.5-26.5 8.4-21.9 21.9l33.5 100.4-94.7 47.4c-12.8 6.4-12.8 24.6 0 31l94.7 47.3-33.5 100.5c-4.5 13.6 8.4 26.5 21.9 21.9l100.4-33.5 47.3 94.7c6.4 12.8 24.6 12.8 31 0l47.3-94.7 100.4 33.5c13.6 4.5 26.5-8.4 21.9-21.9l-33.5-100.4 94.7-47.3c13-6.5 13-24.7.2-31.1zm-155.9 106c-49.9 49.9-131.1 49.9-181 0-49.9-49.9-49.9-131.1 0-181 49.9-49.9 131.1-49.9 181 0 49.9 49.9 49.9 131.1 0 181z"})}),MOON:Ba("svg",{width:"512",height:"512",className:"rtl-mirror",viewBox:"0 0 512 512",children:Ba("path",{fill:"currentColor",d:"M283.211 512c78.962 0 151.079-35.925 198.857-94.792 7.068-8.708-.639-21.43-11.562-19.35-124.203 23.654-238.262-71.576-238.262-196.954 0-72.222 38.662-138.635 101.498-174.394 9.686-5.512 7.25-20.197-3.756-22.23A258.156 258.156 0 0 0 283.211 0c-141.309 0-256 114.511-256 256 0 141.309 114.511 256 256 256z"})})};var yn=(e,{selectedElementIds:o},t)=>{e=se(e);let r=t&&ke(e,{selectedElementIds:o}),n=null,i=r?$(e,{selectedElementIds:o},{includeBoundTextElement:!0}):e;return r&&(i.length===1&&ie(i[0])?(n=i[0],i=Os(e,n)):i.length>1&&(i=$(e,{selectedElementIds:o},{includeBoundTextElement:!0,includeElementsInFrames:!0}))),{exportingFrame:n,exportedElements:At(i)}},vn=async(e,o,t,r,{exportBackground:n,exportPadding:i=sr,viewBackgroundColor:a,name:l=t.name||_h,fileHandle:s=null,exportingFrame:c=null})=>{if(o.length===0)throw new Error(g("alerts.cannotExportEmptyCanvas"));if(e==="svg"||e==="clipboard-svg"){let d=As(o,{exportBackground:n,exportWithDarkMode:t.exportWithDarkMode,viewBackgroundColor:a,exportPadding:i,exportScale:t.exportScale,exportEmbedScene:t.exportEmbedScene&&e==="svg"},r,{exportingFrame:c});if(e==="svg")return Dm(d.then(p=>new Blob([p.outerHTML],{type:je.svg})),{description:"Export to SVG",name:l,extension:t.exportEmbedScene?"excalidraw.svg":"svg",mimeTypes:[ra.svg],fileHandle:s});if(e==="clipboard-svg"){let p=await d.then(u=>u.outerHTML);try{await yr(p)}catch{throw new Error(g("errors.copyToSystemClipboardFailed"))}return}}let m=Xx(o,t,r,{exportBackground:n,viewBackgroundColor:a,exportPadding:i,exportingFrame:c});if(e==="png"){let d=jo(m);return t.exportEmbedScene&&(d=d.then(p=>import("./data/image-7KUKJ7J4.js").then(({encodePngMetadata:u})=>u({blob:p,metadata:Bm(o,t,r,"local")})))),Dm(d,{description:"Export to PNG",name:l,extension:t.exportEmbedScene?"excalidraw.png":"png",mimeTypes:[ra.png],fileHandle:s})}else if(e==="clipboard")try{let d=jo(m);await Qx(d)}catch(d){throw console.warn(d),d.name==="CANVAS_POSSIBLY_TOO_BIG"?new Error(g("canvasError.canvasTooBig")):Go&&d.name==="TypeError"?new Error(`${g("alerts.couldNotCopyToClipboard")}

${g("hints.firefox_clipboard_write")}`):new Error(g("alerts.couldNotCopyToClipboard"))}else throw new Error("Unsupported export type")};var ev=async(e,o,t,r)=>{let{exportBackground:n,viewBackgroundColor:i,fileHandle:a}=o,l=Lx(a);if(!a||!Ax(l))throw new Error("fileHandle should exist and should be of type svg or png when resaving");o={...o,exportEmbedScene:!0};let{exportedElements:s,exportingFrame:c}=yn(e,o,!1);return await vn(l,s,o,t,{exportBackground:n,viewBackgroundColor:i,name:r,fileHandle:a,exportingFrame:c}),{fileHandle:a}};import aI from"clsx";import{jsx as tv,jsxs as lI}from"react/jsx-runtime";var za=({children:e,checked:o,onChange:t,className:r})=>lI("div",{className:aI("Checkbox",r,{"is-checked":o}),onClick:n=>{t(!o,n),n.currentTarget.querySelector(".Checkbox-box").focus()},children:[tv("button",{type:"button",className:"Checkbox-box",role:"checkbox","aria-checked":o,children:Df}),tv("div",{className:"Checkbox-label",children:e})]});import{Fragment as sI,jsx as tr,jsxs as cI}from"react/jsx-runtime";var Yp=D({name:"changeProjectName",label:"labels.fileTitle",trackEvent:!1,perform:(e,o,t)=>({appState:{...o,name:t},captureUpdate:L.EVENTUALLY}),PanelComponent:({appState:e,updateData:o,appProps:t,data:r,app:n})=>tr(Jy,{label:g("labels.fileTitle"),value:n.getName(),onChange:i=>o(i),ignoreFocus:r?.ignoreFocus??!1})}),ov=D({name:"changeExportScale",label:"imageExportDialog.scale",trackEvent:{category:"export",action:"scale"},perform:(e,o,t)=>({appState:{...o,exportScale:t},captureUpdate:L.EVENTUALLY}),PanelComponent:({elements:e,appState:o,updateData:t})=>{let r=se(e),i=ke(r,o)?$(r,o):r;return tr(sI,{children:Il.map(a=>{let[l,s]=$x(i,sr,a),c=`${g("imageExportDialog.label.scale")} ${a}x (${l}x${s})`;return tr(X,{size:"small",type:"radio",icon:`${a}x`,name:"export-canvas-scale",title:c,"aria-label":c,id:"export-canvas-scale",checked:a===o.exportScale,onChange:()=>t(a)},a)})})}}),Vp=D({name:"changeExportBackground",label:"imageExportDialog.label.withBackground",trackEvent:{category:"export",action:"toggleBackground"},perform:(e,o,t)=>({appState:{...o,exportBackground:t},captureUpdate:L.EVENTUALLY}),PanelComponent:({appState:e,updateData:o})=>tr(za,{checked:e.exportBackground,onChange:t=>o(t),children:g("imageExportDialog.label.withBackground")})}),fc=D({name:"changeExportEmbedScene",label:"imageExportDialog.tooltip.embedScene",trackEvent:{category:"export",action:"embedScene"},perform:(e,o,t)=>({appState:{...o,exportEmbedScene:t},captureUpdate:L.EVENTUALLY}),PanelComponent:({appState:e,updateData:o})=>cI(za,{checked:e.exportEmbedScene,onChange:t=>o(t),children:[g("imageExportDialog.label.embedScene"),tr(Ut,{label:g("imageExportDialog.tooltip.embedScene"),long:!0,children:tr("div",{className:"excalidraw-tooltip-icon",children:Bf})})]})}),bc=D({name:"saveToActiveFile",label:"buttons.save",icon:rn,trackEvent:{category:"export"},predicate:(e,o,t,r)=>!!r.props.UIOptions.canvasActions.saveToActiveFile&&!!o.fileHandle&&!o.viewModeEnabled,perform:async(e,o,t,r)=>{let n=!!o.fileHandle;try{let{fileHandle:i}=xs(o.fileHandle)?await ev(e,o,r.files,r.getName()):await ws(e,o,r.files,r.getName());return{captureUpdate:L.EVENTUALLY,appState:{...o,fileHandle:i,toast:n?{message:i?.name?g("toast.fileSavedToFilename").replace("{filename}",`"${i.name}"`):g("toast.fileSaved")}:null}}}catch(i){return i?.name!=="AbortError"?console.error(i):console.warn(i),{captureUpdate:L.EVENTUALLY}}},keyTest:e=>e.key===y.S&&e[y.CTRL_OR_CMD]&&!e.shiftKey}),Ua=D({name:"saveFileToDisk",label:"exportDialog.disk_title",icon:rn,viewMode:!0,trackEvent:{category:"export"},perform:async(e,o,t,r)=>{try{let{fileHandle:n}=await ws(e,{...o,fileHandle:null},r.files,r.getName());return{captureUpdate:L.EVENTUALLY,appState:{...o,openDialog:null,fileHandle:n,toast:{message:g("toast.fileSaved")}}}}catch(n){return n?.name!=="AbortError"?console.error(n):console.warn(n),{captureUpdate:L.EVENTUALLY}}},keyTest:e=>e.key===y.S&&e.shiftKey&&e[y.CTRL_OR_CMD],PanelComponent:({updateData:e})=>tr(X,{type:"button",icon:Rf,title:g("buttons.saveAs"),"aria-label":g("buttons.saveAs"),showAriaLabel:me().editor.isMobile,hidden:!si,onClick:()=>e(null),"data-testid":"save-as-button"})}),Si=D({name:"loadScene",label:"buttons.load",trackEvent:{category:"export"},predicate:(e,o,t,r)=>!!r.props.UIOptions.canvasActions.loadScene&&!o.viewModeEnabled,perform:async(e,o,t,r)=>{try{let{elements:n,appState:i,files:a}=await zm(o,e);return{elements:n,appState:i,files:a,captureUpdate:L.IMMEDIATELY}}catch(n){return n?.name==="AbortError"?(console.warn(n),!1):{elements:e,appState:{...o,errorMessage:n.message},files:r.files,captureUpdate:L.EVENTUALLY}}},keyTest:e=>e[y.CTRL_OR_CMD]&&e.key===y.O}),rv=D({name:"exportWithDarkMode",label:"imageExportDialog.label.darkMode",trackEvent:{category:"export",action:"toggleTheme"},perform:(e,o,t)=>({appState:{...o,exportWithDarkMode:t},captureUpdate:L.EVENTUALLY}),PanelComponent:({appState:e,updateData:o})=>tr("div",{style:{display:"flex",justifyContent:"flex-end",marginTop:"-45px",marginBottom:"10px"},children:tr(Qy,{value:e.exportWithDarkMode?re.DARK:re.LIGHT,onChange:t=>{o(t===re.DARK)},title:g("imageExportDialog.label.darkMode")})})});var nv="{}",Wp=D({name:"copyStyles",label:"labels.copyStyles",icon:Im,trackEvent:{category:"element"},perform:(e,o,t,r)=>{let n=[],i=e.find(a=>o.selectedElementIds[a.id]);if(n.push(i),i&&ut(i)){let a=ue(i,r.scene.getNonDeletedElementsMap());n.push(a)}return i&&(nv=JSON.stringify(n)),{appState:{...o,toast:{message:g("toast.copyStyles")}},captureUpdate:L.EVENTUALLY}},keyTest:e=>e[y.CTRL_OR_CMD]&&e.altKey&&e.code===te.C}),Kp=D({name:"pasteStyles",label:"labels.pasteStyles",icon:Im,trackEvent:{category:"element"},perform:(e,o,t,r)=>{let n=JSON.parse(nv),i=n[0],a=n[1];if(!qh(i))return{elements:e,captureUpdate:L.EVENTUALLY};let l=$(e,o,{includeBoundTextElement:!0}),s=l.map(c=>c.id);return{elements:e.map(c=>{if(s.includes(c.id)){let m=i;if(Y(c)&&c.containerId&&(m=a),!m)return c;let d=q(c,{backgroundColor:m?.backgroundColor,strokeWidth:m?.strokeWidth,strokeColor:m?.strokeColor,strokeStyle:m?.strokeStyle,fillStyle:m?.fillStyle,opacity:m?.opacity,roughness:m?.roughness,roundness:m.roundness?Qh(m.roundness.type,c)?m.roundness:ef(c):null});if(Y(d)){let p=m.fontSize||Nn,u=m.fontFamily||On;d=q(d,{fontSize:p,fontFamily:u,textAlign:m.textAlign||yl,lineHeight:m.lineHeight||$o(u)});let h=null;d.containerId&&(h=l.find(f=>Y(d)&&f.id===d.containerId)||null),Ue(d,h,r.scene.getNonDeletedElementsMap())}return d.type==="arrow"&&Ne(m)&&(d=q(d,{startArrowhead:m.startArrowhead,endArrowhead:m.endArrowhead})),ie(c)&&(d=q(d,{roundness:null,backgroundColor:"transparent"})),d}return c}),captureUpdate:L.IMMEDIATELY}},keyTest:e=>e[y.CTRL_OR_CMD]&&e.altKey&&e.code===te.V});import{jsx as iv}from"react/jsx-runtime";var dI=D({name:"toggleCanvasMenu",label:"buttons.menu",trackEvent:{category:"menu"},perform:(e,o)=>({appState:{...o,openMenu:o.openMenu==="canvas"?null:"canvas"},captureUpdate:L.EVENTUALLY}),PanelComponent:({appState:e,updateData:o})=>iv(X,{type:"button",icon:Gl,"aria-label":g("buttons.menu"),onClick:o,selected:e.openMenu==="canvas"})}),mI=D({name:"toggleEditMenu",label:"buttons.edit",trackEvent:{category:"menu"},perform:(e,o)=>({appState:{...o,openMenu:o.openMenu==="shape"?null:"shape"},captureUpdate:L.EVENTUALLY}),PanelComponent:({elements:e,appState:o,updateData:t})=>iv(X,{visible:fi(o,se(e)),type:"button",icon:Nf,"aria-label":g("buttons.edit"),onClick:t,selected:o.openMenu==="shape"})}),wn=D({name:"toggleShortcuts",label:"welcomeScreen.defaults.helpHint",icon:Af,viewMode:!0,trackEvent:{category:"menu",action:"toggleHelpDialog"},perform:(e,o,t,{focusContainer:r})=>(o.openDialog?.name==="help"&&r(),{appState:{...o,openDialog:o.openDialog?.name==="help"?null:{name:"help"}},captureUpdate:L.EVENTUALLY}),keyTest:e=>e.key===y.QUESTION_MARK});import{jsx as ki}from"react/jsx-runtime";var pI=e=>{if(e.length>=2){let o=e[0].groupIds;for(let t of o)if(e.reduce((r,n)=>r&&Zn(n,t),!0))return!0}return!1},av=(e,o,t)=>{let r=t.scene.getSelectedElements({selectedElementIds:o.selectedElementIds,includeBoundTextElement:!0});return r.length>=2&&!pI(r)&&!Fs(r)},Xp=D({name:"group",label:"labels.group",icon:e=>ki(vm,{theme:e.theme}),trackEvent:{category:"element"},perform:(e,o,t,r)=>{let n=rE(r.scene.getSelectedElements({selectedElementIds:o.selectedElementIds,includeBoundTextElement:!0}));if(n.length<2)return{appState:o,elements:e,captureUpdate:L.EVENTUALLY};let i=po(o);if(i.length===1){let b=i[0],x=new Set(Le(e,b).map(w=>w.id)),T=new Set(n.map(w=>w.id));if(new Set([...Array.from(x),...Array.from(T)]).size===x.size)return{appState:o,elements:e,captureUpdate:L.EVENTUALLY}}let a=[...e];new Set(n.map(b=>b.frameId)).size>1&&tE(n).forEach((x,T)=>{Rs(x,r.scene.getNonDeletedElementsMap())});let s=gr(),c=Q(n);a=a.map(b=>c.get(b.id)?q(b,{groupIds:gf(b.groupIds,s,o.editingGroupId)}):b);let m=Le(a,s),d=m[m.length-1],p=a.lastIndexOf(d),u=a.slice(p+1),h=a.slice(0,p).filter(b=>!Zn(b,s)),f=fo([...h,...m,...u],Q(m));return{appState:{...o,...cf(s,{...o,selectedGroupIds:{}},se(a))},elements:f,captureUpdate:L.IMMEDIATELY}},predicate:(e,o,t,r)=>av(e,o,r),keyTest:e=>!e.shiftKey&&e[y.CTRL_OR_CMD]&&e.key===y.G,PanelComponent:({elements:e,appState:o,updateData:t,app:r})=>ki(X,{hidden:!av(e,o,r),type:"button",icon:ki(vm,{theme:o.theme}),onClick:()=>t(null),title:`${g("labels.group")} \u2014 ${A("CtrlOrCmd+G")}`,"aria-label":g("labels.group"),visible:ke(se(e),o)})}),$p=D({name:"ungroup",label:"labels.ungroup",icon:e=>ki(wm,{theme:e.theme}),trackEvent:{category:"element"},perform:(e,o,t,r)=>{let n=po(o),i=Q(e);if(n.length===0)return{appState:o,elements:e,captureUpdate:L.EVENTUALLY};let a=[...e],l=[];a=a.map(p=>{qe(p)&&l.push(p.id);let u=hf(p.groupIds,o.selectedGroupIds);return u.length===p.groupIds.length?p:q(p,{groupIds:u})});let s=We(o,se(a),o,null),c=r.scene.getSelectedElements(o),m=new Set(c.filter(p=>p.frameId).map(p=>p.frameId));return oE(e).filter(p=>m.has(p.id)).forEach(p=>{p&&(a=Ns(a,di(a,p,o,i),p,r))}),s.selectedElementIds=Object.entries(s.selectedElementIds).reduce((p,[u,h])=>(h&&!l.includes(u)&&(p[u]=!0),p),{}),{appState:{...o,...s},elements:a,captureUpdate:L.IMMEDIATELY}},keyTest:e=>e.shiftKey&&e[y.CTRL_OR_CMD]&&e.key===y.G.toUpperCase(),predicate:(e,o)=>po(o).length>0,PanelComponent:({elements:e,appState:o,updateData:t})=>ki(X,{type:"button",hidden:po(o).length===0,icon:ki(wm,{theme:o.theme}),onClick:()=>t(null),title:`${g("labels.ungroup")} \u2014 ${A("CtrlOrCmd+Shift+G")}`,"aria-label":g("labels.ungroup"),visible:ke(se(e),o)})});var xc=(e,o,t,r,n,i,a)=>{e.beginPath(),e.moveTo(o+i,t),e.lineTo(o+r-i,t),e.quadraticCurveTo(o+r,t,o+r,t+i),e.lineTo(o+r,t+n-i),e.quadraticCurveTo(o+r,t+n,o+r-i,t+n),e.lineTo(o+i,t+n),e.quadraticCurveTo(o,t+n,o,t+n-i),e.lineTo(o,t+i),e.quadraticCurveTo(o,t,o+i,t),e.closePath(),e.fill(),a&&(e.strokeStyle=a),e.stroke()};function uI(e){let o=0;if(e.length===0)return o;for(let t=0;t<e.length;t++){let r=e.charCodeAt(t);o=(o<<5)-o+r}return o}var Tn=(e,o)=>`hsl(${Math.abs(uI(o?.id||e))%37*10}, 100%, 83%)`,lv=e=>{let o=e?.trim()?.codePointAt(0);return(o?String.fromCodePoint(o):"?").toUpperCase()},sv=({context:e,renderConfig:o,appState:t,normalizedWidth:r,normalizedHeight:n})=>{for(let[i,a]of o.remotePointerViewportCoords){let{x:l,y:s}=a,c=t.collaborators.get(i);l-=t.offsetLeft,s-=t.offsetTop;let m=11,d=14,p=l<0||l>r-m||s<0||s>n-d;l=Math.max(l,0),l=Math.min(l,r-m),s=Math.max(s,0),s=Math.min(s,n-d);let u=Tn(i,c);e.save(),e.strokeStyle=u,e.fillStyle=u;let h=o.remotePointerUserStates.get(i),f=p||h==="idle"||h==="away";f&&(e.globalAlpha=.3),o.remotePointerButton.get(i)==="down"&&(e.beginPath(),e.arc(l,s,15,0,2*Math.PI,!1),e.lineWidth=3,e.strokeStyle="#ffffff88",e.stroke(),e.closePath(),e.beginPath(),e.arc(l,s,15,0,2*Math.PI,!1),e.lineWidth=1,e.strokeStyle=u,e.stroke(),e.closePath());let b=t.theme===re.DARK?"#2f6330":hh,x=c?.isSpeaking;x&&(e.fillStyle=b,e.strokeStyle=b,e.lineWidth=10,e.lineJoin="round",e.beginPath(),e.moveTo(l,s),e.lineTo(l+0,s+14),e.lineTo(l+4,s+9),e.lineTo(l+11,s+8),e.closePath(),e.stroke(),e.fill()),e.fillStyle=oa,e.strokeStyle=oa,e.lineWidth=6,e.lineJoin="round",e.beginPath(),e.moveTo(l,s),e.lineTo(l+0,s+14),e.lineTo(l+4,s+9),e.lineTo(l+11,s+8),e.closePath(),e.stroke(),e.fill(),e.fillStyle=u,e.strokeStyle=u,e.lineWidth=2,e.lineJoin="round",e.beginPath(),f?(e.moveTo(l-1,s-1),e.lineTo(l-1,s+15),e.lineTo(l+5,s+10),e.lineTo(l+12,s+9),e.closePath(),e.fill()):(e.moveTo(l,s),e.lineTo(l+0,s+14),e.lineTo(l+4,s+9),e.lineTo(l+11,s+8),e.closePath(),e.fill(),e.stroke());let T=o.remotePointerUsernames.get(i)||"";if(!p&&T){e.font="600 12px sans-serif";let E=(x?l+0:l)+m/2,w=(x?s+0:s)+d+2,S=5,I=3,_=e.measureText(T),k=_.actualBoundingBoxDescent+_.actualBoundingBoxAscent,R=Math.max(k,12),M=E-1,N=w-1,G=_.width+2+S*2+2,H=R+2+I*2+2;if(e.roundRect?(e.beginPath(),e.roundRect(M,N,G,H,8),e.fillStyle=u,e.fill(),e.strokeStyle=oa,e.stroke(),x&&(e.beginPath(),e.roundRect(M-2,N-2,G+4,H+4,8),e.strokeStyle=b,e.stroke())):xc(e,M,N,G,H,8,oa),e.fillStyle=gh,e.fillText(T,E+S+1,w+I+_.actualBoundingBoxAscent+Math.floor((R-k)/2)+2),x){e.fillStyle=b;let V=8,F=8,O=5;e.fillRect(M+G+F,N+(H/2-V/2),2,V),e.fillRect(M+G+F+O,N+(H/2-V*2/2),2,V*2),e.fillRect(M+G+F+O*2,N+(H/2-V/2),2,V)}}e.restore(),e.closePath()}};import{useState as gI}from"react";import hI from"clsx";import{jsx as cv}from"react/jsx-runtime";var Zp=({color:e,onClick:o,name:t,src:r,className:n})=>{let i=lv(t),[a,l]=gI(!1),s=!a&&r,c=s?void 0:{background:e};return cv("div",{className:hI("Avatar",n),style:c,onClick:o,children:s?cv("img",{className:"Avatar-img",src:r,alt:i,referrerPolicy:"no-referrer",onError:()=>l(!0)}):i})};import fI from"clsx";import{jsx as Po,jsxs as Ec}from"react/jsx-runtime";var bI=D({name:"goToCollaborator",label:"Go to a collaborator",viewMode:!0,trackEvent:{category:"collab"},perform:(e,o,t)=>!t.socketId||o.userToFollow?.socketId===t.socketId||t.isCurrentUser?{appState:{...o,userToFollow:null},captureUpdate:L.EVENTUALLY}:{appState:{...o,userToFollow:{socketId:t.socketId,username:t.username||""},openMenu:o.openMenu==="canvas"?null:o.openMenu},captureUpdate:L.EVENTUALLY},PanelComponent:({updateData:e,data:o,appState:t})=>{let{socketId:r,collaborator:n,withName:i,isBeingFollowed:a}=o,l=Tn(r,n),s=fI({"is-followed":a,"is-current-user":n.isCurrentUser===!0,"is-speaking":n.isSpeaking,"is-in-call":n.isInCall,"is-muted":n.isMuted}),c=n.isInCall?n.isSpeaking?Ec("div",{className:"UserList__collaborator-status-icon-speaking-indicator",title:g("userList.hint.isSpeaking"),children:[Po("div",{}),Po("div",{}),Po("div",{})]}):n.isMuted?Po("div",{className:"UserList__collaborator-status-icon-microphone-muted",title:g("userList.hint.micMuted"),children:Fb}):Po("div",{title:g("userList.hint.inCall"),children:Ob}):null;return i?Ec("div",{className:`dropdown-menu-item dropdown-menu-item-base UserList__collaborator ${s}`,style:{"--avatar-size":"1.5rem"},onClick:()=>e(n),children:[Po(Zp,{color:l,onClick:()=>{},name:n.username||"",src:n.avatarUrl,className:s}),Po("div",{className:"UserList__collaborator-name",children:n.username}),Ec("div",{className:"UserList__collaborator-status-icons","aria-hidden":!0,children:[a&&Po("div",{className:"UserList__collaborator-status-icon-is-followed",title:g("userList.hint.followStatus"),children:oi}),c]})]}):Ec("div",{className:`UserList__collaborator UserList__collaborator--avatar-only ${s}`,children:[Po(Zp,{color:l,onClick:()=>{e(n)},name:n.username||"",src:n.avatarUrl,className:s}),c&&Po("div",{className:"UserList__collaborator-status-icon",children:c})]})}});var jp=D({name:"addToLibrary",trackEvent:{category:"element"},perform:(e,o,t,r)=>{let n=r.scene.getSelectedElements({selectedElementIds:o.selectedElementIds,includeBoundTextElement:!0,includeElementsInFrames:!0});for(let i of Ll)if(n.some(a=>a.type===i))return{captureUpdate:L.EVENTUALLY,appState:{...o,errorMessage:g(`errors.libraryElementTypeError.${i}`)}};return r.library.getLatestLibrary().then(i=>r.library.setLibrary([{id:gr(),status:"unpublished",elements:n.map(xr),created:Date.now()},...i])).then(()=>({captureUpdate:L.EVENTUALLY,appState:{...o,toast:{message:g("toast.addedToLibrary")}}})).catch(i=>({captureUpdate:L.EVENTUALLY,appState:{...o,errorMessage:i.message}}))},label:"labels.addToLibrary"});var dv=(e,o,t,r)=>{let n=Bl(e,o),i=Bt(e);return n.flatMap(a=>{let l=xI(a,i,t);return a.map(s=>{let c=P(s,{x:s.x+l.x,y:s.y+l.y});return Jt(s,r.getNonDeletedElementsMap(),{simultaneouslyUpdated:a}),c})})},xI=(e,o,{axis:t,position:r})=>{let n=Bt(e),[i,a]=t==="x"?["minX","maxX"]:["minY","maxY"],l={x:0,y:0};return r==="start"?{...l,[t]:o[i]-n[i]}:r==="end"?{...l,[t]:o[a]-n[a]}:{...l,[t]:(o[i]+o[a])/2-(n[i]+n[a])/2}};import{jsx as Ai}from"react/jsx-runtime";var _t=(e,o)=>{let t=o.scene.getSelectedElements(e);return t.length>1&&!t.some(r=>ie(r))},Li=(e,o,t,r)=>{let n=t.scene.getSelectedElements(o),i=Q(e),a=dv(n,i,r,t.scene),l=Q(a);return Lo(e.map(s=>l.get(s.id)||s),o,t)},EI=D({name:"alignTop",label:"labels.alignTop",icon:hm,trackEvent:{category:"element"},predicate:(e,o,t,r)=>_t(o,r),perform:(e,o,t,r)=>({appState:o,elements:Li(e,o,r,{position:"start",axis:"y"}),captureUpdate:L.IMMEDIATELY}),keyTest:e=>e[y.CTRL_OR_CMD]&&e.shiftKey&&e.key===y.ARROW_UP,PanelComponent:({elements:e,appState:o,updateData:t,app:r})=>Ai(X,{hidden:!_t(o,r),type:"button",icon:hm,onClick:()=>t(null),title:`${g("labels.alignTop")} \u2014 ${A("CtrlOrCmd+Shift+Up")}`,"aria-label":g("labels.alignTop"),visible:ke(se(e),o)})}),yI=D({name:"alignBottom",label:"labels.alignBottom",icon:fm,trackEvent:{category:"element"},predicate:(e,o,t,r)=>_t(o,r),perform:(e,o,t,r)=>({appState:o,elements:Li(e,o,r,{position:"end",axis:"y"}),captureUpdate:L.IMMEDIATELY}),keyTest:e=>e[y.CTRL_OR_CMD]&&e.shiftKey&&e.key===y.ARROW_DOWN,PanelComponent:({elements:e,appState:o,updateData:t,app:r})=>Ai(X,{hidden:!_t(o,r),type:"button",icon:fm,onClick:()=>t(null),title:`${g("labels.alignBottom")} \u2014 ${A("CtrlOrCmd+Shift+Down")}`,"aria-label":g("labels.alignBottom"),visible:ke(se(e),o)})}),vI=D({name:"alignLeft",label:"labels.alignLeft",icon:bm,trackEvent:{category:"element"},predicate:(e,o,t,r)=>_t(o,r),perform:(e,o,t,r)=>({appState:o,elements:Li(e,o,r,{position:"start",axis:"x"}),captureUpdate:L.IMMEDIATELY}),keyTest:e=>e[y.CTRL_OR_CMD]&&e.shiftKey&&e.key===y.ARROW_LEFT,PanelComponent:({elements:e,appState:o,updateData:t,app:r})=>Ai(X,{hidden:!_t(o,r),type:"button",icon:bm,onClick:()=>t(null),title:`${g("labels.alignLeft")} \u2014 ${A("CtrlOrCmd+Shift+Left")}`,"aria-label":g("labels.alignLeft"),visible:ke(se(e),o)})}),wI=D({name:"alignRight",label:"labels.alignRight",icon:xm,trackEvent:{category:"element"},predicate:(e,o,t,r)=>_t(o,r),perform:(e,o,t,r)=>({appState:o,elements:Li(e,o,r,{position:"end",axis:"x"}),captureUpdate:L.IMMEDIATELY}),keyTest:e=>e[y.CTRL_OR_CMD]&&e.shiftKey&&e.key===y.ARROW_RIGHT,PanelComponent:({elements:e,appState:o,updateData:t,app:r})=>Ai(X,{hidden:!_t(o,r),type:"button",icon:xm,onClick:()=>t(null),title:`${g("labels.alignRight")} \u2014 ${A("CtrlOrCmd+Shift+Right")}`,"aria-label":g("labels.alignRight"),visible:ke(se(e),o)})}),TI=D({name:"alignVerticallyCentered",label:"labels.centerVertically",icon:Em,trackEvent:{category:"element"},predicate:(e,o,t,r)=>_t(o,r),perform:(e,o,t,r)=>({appState:o,elements:Li(e,o,r,{position:"center",axis:"y"}),captureUpdate:L.IMMEDIATELY}),PanelComponent:({elements:e,appState:o,updateData:t,app:r})=>Ai(X,{hidden:!_t(o,r),type:"button",icon:Em,onClick:()=>t(null),title:g("labels.centerVertically"),"aria-label":g("labels.centerVertically"),visible:ke(se(e),o)})}),CI=D({name:"alignHorizontallyCentered",label:"labels.centerHorizontally",icon:ym,trackEvent:{category:"element"},predicate:(e,o,t,r)=>_t(o,r),perform:(e,o,t,r)=>({appState:o,elements:Li(e,o,r,{position:"center",axis:"x"}),captureUpdate:L.IMMEDIATELY}),PanelComponent:({elements:e,appState:o,updateData:t,app:r})=>Ai(X,{hidden:!_t(o,r),type:"button",icon:ym,onClick:()=>t(null),title:g("labels.centerHorizontally"),"aria-label":g("labels.centerHorizontally"),visible:ke(se(e),o)})});var mv=(e,o,t)=>{let[r,n,i,a]=t.axis==="x"?["minX","midX","maxX","width"]:["minY","midY","maxY","height"],l=Bt(e),s=Bl(e,o).map(p=>[p,Bt(p)]).sort((p,u)=>p[1][n]-u[1][n]),c=0;for(let p of s)c+=p[1][a];let m=(l[a]-c)/(s.length-1);if(m<0){let p=s.findIndex(b=>b[1][r]===l[r]),u=s.findIndex(b=>b[1][i]===l[i]),h=(s[u][1][n]-s[p][1][n])/(s.length-1),f=s[p][1][n];return s.flatMap(([b,x],T)=>{let E={x:0,y:0};return T!==p&&T!==u&&(f+=h,E[t.axis]=f-x[n]),b.map(w=>q(w,{x:w.x+E.x,y:w.y+E.y}))})}let d=l[r];return s.flatMap(([p,u])=>{let h={x:0,y:0};return h[t.axis]=d-u[r],d+=m,d+=u[a],p.map(f=>q(f,{x:f.x+h.x,y:f.y+h.y}))})};import{jsx as gv}from"react/jsx-runtime";var pv=(e,o)=>{let t=o.scene.getSelectedElements(e);return t.length>1&&!t.some(r=>ie(r))},uv=(e,o,t,r)=>{let n=t.scene.getSelectedElements(o),i=mv(n,t.scene.getNonDeletedElementsMap(),r),a=Q(i);return Lo(e.map(l=>a.get(l.id)||l),o,t)},II=D({name:"distributeHorizontally",label:"labels.distributeHorizontally",trackEvent:{category:"element"},perform:(e,o,t,r)=>({appState:o,elements:uv(e,o,r,{space:"between",axis:"x"}),captureUpdate:L.IMMEDIATELY}),keyTest:e=>!e[y.CTRL_OR_CMD]&&e.altKey&&e.code===te.H,PanelComponent:({elements:e,appState:o,updateData:t,app:r})=>gv(X,{hidden:!pv(o,r),type:"button",icon:Uf,onClick:()=>t(null),title:`${g("labels.distributeHorizontally")} \u2014 ${A("Alt+H")}`,"aria-label":g("labels.distributeHorizontally"),visible:ke(se(e),o)})}),SI=D({name:"distributeVertically",label:"labels.distributeVertically",trackEvent:{category:"element"},perform:(e,o,t,r)=>({appState:o,elements:uv(e,o,r,{space:"between",axis:"y"}),captureUpdate:L.IMMEDIATELY}),keyTest:e=>!e[y.CTRL_OR_CMD]&&e.altKey&&e.code===te.V,PanelComponent:({elements:e,appState:o,updateData:t,app:r})=>gv(X,{hidden:!pv(o,r),type:"button",icon:Hf,onClick:()=>t(null),title:`${g("labels.distributeVertically")} \u2014 ${A("Alt+V")}`,"aria-label":g("labels.distributeVertically"),visible:ke(se(e),o)})});var Jp=D({name:"flipHorizontal",label:"labels.flipHorizontal",icon:Hb,trackEvent:{category:"element"},perform:(e,o,t,r)=>({elements:Lo(hv(e,r.scene.getNonDeletedElementsMap(),o,"horizontal",r),o,r),appState:o,captureUpdate:L.IMMEDIATELY}),keyTest:e=>e.shiftKey&&e.code===te.H}),qp=D({name:"flipVertical",label:"labels.flipVertical",icon:Ub,trackEvent:{category:"element"},perform:(e,o,t,r)=>({elements:Lo(hv(e,r.scene.getNonDeletedElementsMap(),o,"vertical",r),o,r),appState:o,captureUpdate:L.IMMEDIATELY}),keyTest:e=>e.shiftKey&&e.code===te.V&&!e[y.CTRL_OR_CMD]}),hv=(e,o,t,r,n)=>{let i=$(se(e),t,{includeBoundTextElement:!0,includeElementsInFrames:!0}),a=kI(i,o,t,r,n),l=Q(a);return e.map(s=>l.get(s.id)||s)},kI=(e,o,t,r,n)=>{if(e.every(u=>Ne(u)&&(u.startBinding||u.endBinding)))return e.map(u=>{let h=u;return q(h,{startArrowhead:h.endArrowhead,endArrowhead:h.startArrowhead})});let{midX:i,midY:a}=Bt(e);OE(e,o,"nw",n.scene,new Map(Array.from(o.values()).map(u=>[u.id,xr(u)])),{flipByX:r==="horizontal",flipByY:r==="vertical",shouldResizeFromCenter:!0,shouldMaintainAspectRatio:!0}),cn(e.filter(he),o,n.scene.getNonDeletedElements(),n.scene,sn(t),[],t.zoom);let{elbowArrows:l,otherElements:s}=e.reduce((u,h)=>ee(h)?{...u,elbowArrows:u.elbowArrows.concat(h)}:{...u,otherElements:u.otherElements.concat(h)},{elbowArrows:[],otherElements:[]}),{midX:c,midY:m}=Bt(e),[d,p]=[i-c,a-m];return s.forEach(u=>P(u,{x:u.x+d,y:u.y+p})),l.forEach(u=>P(u,{x:u.x+d,y:u.y+p})),e};var _i=D({name:"copy",label:"labels.copy",icon:ua,trackEvent:{category:"element"},perform:async(e,o,t,r)=>{let n=r.scene.getSelectedElements({selectedElementIds:o.selectedElementIds,includeBoundTextElement:!0,includeElementsInFrames:!0});try{await Jx(n,r.files,t)}catch(i){return{captureUpdate:L.EVENTUALLY,appState:{...o,errorMessage:i.message}}}return{captureUpdate:L.EVENTUALLY}},keyTest:void 0}),Qp=D({name:"paste",label:"labels.paste",trackEvent:{category:"element"},perform:async(e,o,t,r)=>{let n;try{n=await qx()}catch(i){return i.name==="AbortError"||i.name==="NotAllowedError"?!1:(console.error(`actionPaste ${i.name}: ${i.message}`),Go?{captureUpdate:L.EVENTUALLY,appState:{...o,errorMessage:g("hints.firefox_clipboard_write")}}:{captureUpdate:L.EVENTUALLY,appState:{...o,errorMessage:g("errors.asyncPasteFailedOnRead")}})}try{r.pasteFromClipboard(jx({types:n}))}catch(i){return console.error(i),{captureUpdate:L.EVENTUALLY,appState:{...o,errorMessage:g("errors.asyncPasteFailedOnParse")}}}return{captureUpdate:L.EVENTUALLY}},keyTest:void 0}),yc=D({name:"cut",label:"labels.cut",icon:Ib,trackEvent:{category:"element"},perform:(e,o,t,r)=>(_i.perform(e,o,t,r),_a.perform(e,o,null,r)),keyTest:e=>e[y.CTRL_OR_CMD]&&e.key===y.X}),vc=D({name:"copyAsSvg",label:"labels.copyAsSvg",icon:Gb,trackEvent:{category:"element"},perform:async(e,o,t,r)=>{if(!r.canvas)return{captureUpdate:L.EVENTUALLY};let{exportedElements:n,exportingFrame:i}=yn(e,o,!0);try{await vn("clipboard-svg",n,o,r.files,{...o,exportingFrame:i,name:r.getName()});let a=r.scene.getSelectedElements({selectedElementIds:o.selectedElementIds,includeBoundTextElement:!0,includeElementsInFrames:!0});return{appState:{toast:{message:g("toast.copyToClipboardAsSvg",{exportSelection:a.length?g("toast.selection"):g("toast.canvas"),exportColorScheme:o.exportWithDarkMode?g("buttons.darkMode"):g("buttons.lightMode")})}},captureUpdate:L.EVENTUALLY}}catch(a){return console.error(a),{appState:{errorMessage:a.message},captureUpdate:L.EVENTUALLY}}},predicate:e=>Gm&&e.length>0,keywords:["svg","clipboard","copy"]}),wc=D({name:"copyAsPng",label:"labels.copyAsPng",icon:Yb,trackEvent:{category:"element"},perform:async(e,o,t,r)=>{if(!r.canvas)return{captureUpdate:L.EVENTUALLY};let n=r.scene.getSelectedElements({selectedElementIds:o.selectedElementIds,includeBoundTextElement:!0,includeElementsInFrames:!0}),{exportedElements:i,exportingFrame:a}=yn(e,o,!0);try{return await vn("clipboard",i,o,r.files,{...o,exportingFrame:a,name:r.getName()}),{appState:{...o,toast:{message:g("toast.copyToClipboardAsPng",{exportSelection:n.length?g("toast.selection"):g("toast.canvas"),exportColorScheme:o.exportWithDarkMode?g("buttons.darkMode"):g("buttons.lightMode")})}},captureUpdate:L.EVENTUALLY}}catch(l){return console.error(l),{appState:{...o,errorMessage:l.message},captureUpdate:L.EVENTUALLY}}},predicate:e=>ci&&e.length>0,keyTest:e=>e.code===te.C&&e.altKey&&e.shiftKey,keywords:["png","clipboard","copy"]}),Tc=D({name:"copyText",label:"labels.copyText",trackEvent:{category:"element"},perform:(e,o,t,r)=>{let n=r.scene.getSelectedElements({selectedElementIds:o.selectedElementIds,includeBoundTextElement:!0});try{yr(Qm(n))}catch{throw new Error(g("errors.copyToSystemClipboardFailed"))}return{captureUpdate:L.EVENTUALLY}},predicate:(e,o,t,r)=>Gm&&r.scene.getSelectedElements({selectedElementIds:o.selectedElementIds,includeBoundTextElement:!0}).some(Y),keywords:["text","clipboard","copy"]});var Cc=D({name:"gridMode",icon:$b,keywords:["snap"],label:"labels.toggleGrid",viewMode:!0,trackEvent:{category:"canvas",predicate:e=>e.gridModeEnabled},perform(e,o){return{appState:{...o,gridModeEnabled:!this.checked(o),objectsSnapModeEnabled:!1},captureUpdate:L.EVENTUALLY}},checked:e=>e.gridModeEnabled,predicate:(e,o,t)=>t.gridModeEnabled===void 0,keyTest:e=>e[y.CTRL_OR_CMD]&&e.code===te.QUOTE});var Mi=D({name:"zenMode",label:"buttons.zenMode",icon:Wb,paletteName:"Toggle zen mode",viewMode:!0,trackEvent:{category:"canvas",predicate:e=>!e.zenModeEnabled},perform(e,o){return{appState:{...o,zenModeEnabled:!this.checked(o)},captureUpdate:L.EVENTUALLY}},checked:e=>e.zenModeEnabled,predicate:(e,o,t)=>typeof t.zenModeEnabled>"u",keyTest:e=>!e[y.CTRL_OR_CMD]&&e.altKey&&e.code===te.Z});var eu=D({name:"objectsSnapMode",label:"buttons.objectsSnapMode",icon:Vb,viewMode:!1,trackEvent:{category:"canvas",predicate:e=>!e.objectsSnapModeEnabled},perform(e,o){return{appState:{...o,objectsSnapModeEnabled:!this.checked(o),gridModeEnabled:!1},captureUpdate:L.EVENTUALLY}},checked:e=>e.objectsSnapModeEnabled,predicate:(e,o,t)=>typeof t.objectsSnapModeEnabled>"u",keyTest:e=>!e[y.CTRL_OR_CMD]&&e.altKey&&e.code===te.S});var Di=D({name:"stats",label:"stats.fullTitle",icon:zb,paletteName:"Toggle stats",viewMode:!0,trackEvent:{category:"menu"},keywords:["edit","attributes","customize"],perform(e,o){return{appState:{...o,stats:{...o.stats,open:!this.checked(o)}},captureUpdate:L.EVENTUALLY}},checked:e=>e.stats.open,keyTest:e=>!e[y.CTRL_OR_CMD]&&e.altKey&&e.code===te.SLASH});var tu=D({name:"unbindText",label:"labels.unbindText",trackEvent:{category:"element"},predicate:(e,o,t,r)=>r.scene.getSelectedElements(o).some(i=>ut(i)),perform:(e,o,t,r)=>{let n=r.scene.getSelectedElements(o),i=r.scene.getNonDeletedElementsMap();return n.forEach(a=>{let l=ue(a,i);if(l){let{width:s,height:c}=Nt(l.originalText,lt(l),l.lineHeight),m=EE(a.id);xE(a.id);let{x:d,y:p}=zs(a,l,i);P(l,{containerId:null,width:s,height:c,text:l.originalText,x:d,y:p}),P(a,{boundElements:a.boundElements?.filter(u=>u.id!==l.id),height:m||a.height})}}),{elements:e,appState:o,captureUpdate:L.IMMEDIATELY}}}),ou=D({name:"bindText",label:"labels.bindText",trackEvent:{category:"element"},predicate:(e,o,t,r)=>{let n=r.scene.getSelectedElements(o);if(n.length===2){let i=Y(n[0])||Y(n[1]),a;if(Ko(n[0])?a=n[0]:Ko(n[1])&&(a=n[1]),i&&a&&ue(a,r.scene.getNonDeletedElementsMap())===null)return!0}return!1},perform:(e,o,t,r)=>{let n=r.scene.getSelectedElements(o),i,a;Y(n[0])&&Ko(n[1])?(i=n[0],a=n[1]):(i=n[1],a=n[0]),P(i,{containerId:a.id,verticalAlign:Kt.MIDDLE,textAlign:na.CENTER,autoResize:!0}),P(a,{boundElements:(a.boundElements||[]).concat({type:"text",id:i.id})});let l=a.height;return Ue(i,a,r.scene.getNonDeletedElementsMap()),Sa(a.id,l),{elements:LI(e,a,i),appState:{...o,selectedElementIds:{[a.id]:!0}},captureUpdate:L.IMMEDIATELY}}}),LI=(e,o,t)=>{let r=e.slice(),n=r.findIndex(a=>a.id===t.id);r.splice(n,1);let i=r.findIndex(a=>a.id===o.id);return r.splice(i+1,0,t),fo(r,Q([o,t])),r},AI=(e,o,t)=>{let r=e.slice(),n=r.findIndex(a=>a.id===o.id);r.splice(n,1);let i=r.findIndex(a=>a.id===t.id);return r.splice(i,0,o),fo(r,Q([o,t])),r},fv=D({name:"wrapTextInContainer",label:"labels.createContainerFromText",trackEvent:{category:"element"},predicate:(e,o,t,r)=>{let n=r.scene.getSelectedElements(o),i=n.every(a=>Y(a));return n.length>0&&i},perform:(e,o,t,r)=>{let n=r.scene.getSelectedElements(o),i=e.slice(),a={};for(let l of n)if(Y(l)){let s=ho({type:"rectangle",backgroundColor:o.currentItemBackgroundColor,boundElements:[...l.boundElements||[],{id:l.id,type:"text"}],angle:l.angle,fillStyle:o.currentItemFillStyle,strokeColor:o.currentItemStrokeColor,roughness:o.currentItemRoughness,strokeWidth:o.currentItemStrokeWidth,strokeStyle:o.currentItemStrokeStyle,roundness:o.currentItemRoundness==="round"?{type:Kn("rectangle")?St.ADAPTIVE_RADIUS:St.PROPORTIONAL_RADIUS}:null,opacity:100,locked:!1,x:l.x-jd,y:l.y-jd,width:ui(l.width,"rectangle"),height:ui(l.height,"rectangle"),groupIds:l.groupIds,frameId:l.frameId});if(l.boundElements?.length){let c=l.boundElements.filter(d=>d.type==="arrow").map(d=>d.id);i.filter(d=>c.includes(d.id)).forEach(d=>{let p=d.startBinding,u=d.endBinding;p?.elementId===l.id&&(p={...p,elementId:s.id}),u?.elementId===l.id&&(u={...u,elementId:s.id}),(p||u)&&P(d,{startBinding:p,endBinding:u},!1)})}P(l,{containerId:s.id,verticalAlign:Kt.MIDDLE,boundElements:null,textAlign:na.CENTER,autoResize:!0},!1),Ue(l,s,r.scene.getNonDeletedElementsMap()),i=AI([...i,s],s,l),a[s.id]=!0}return{elements:i,appState:{...o,selectedElementIds:a},captureUpdate:L.IMMEDIATELY}}});import{useCallback as bv,useEffect as xv,useLayoutEffect as _I,useRef as MI,useState as DI}from"react";import PI from"clsx";import{jsx as Pi,jsxs as Ev}from"react/jsx-runtime";var iu=380,RI=42,ru=5,yv=85,NI=500,nu=!1,Ic=new Map,vv=({element:e,elementsMap:o,setAppState:t,onLinkOpen:r,setToast:n,updateEmbedValidationStatus:i})=>{let a=Mo(),l=Do(),s=me(),c=e.link||"",[m,d]=DI(c),p=MI(null),u=a.showHyperlinkPopup==="editor",h=bv(()=>{if(!p.current)return;let E=uo(p.current.value)||null;if(!e.link&&E&&le("hyperlink","create"),Et(e)){if(a.activeEmbeddable?.element===e&&t({activeEmbeddable:null}),!E){P(e,{link:null}),i(e,!1);return}if(!li(E,l.validateEmbeddable))E&&n({message:g("toast.unableToEmbed"),closable:!0}),e.link&&Ic.set(e.id,e.link),P(e,{link:E}),i(e,!1);else{let{width:w,height:S}=e,I=dn(E);I?.error instanceof URIError&&n({message:g("toast.unrecognizedLinkFormat"),closable:!0});let _=I?I.intrinsicSize.w/I.intrinsicSize.h:1,k=Ic.get(e.id)!==e.link;P(e,{...k?{width:I?.type==="video"?w>S?w:S*_:w,height:I?.type==="video"&&w>S?w/_:S}:{},link:E}),i(e,!0),Ic.has(e.id)&&Ic.delete(e.id)}}else P(e,{link:E})},[e,n,l.validateEmbeddable,a.activeEmbeddable,t,i]);_I(()=>()=>{h()},[h]),xv(()=>{u&&p?.current&&!(s.viewport.isMobile||s.isTouchScreen)&&p.current.select()},[u,s.viewport.isMobile,s.isTouchScreen]),xv(()=>{let E=null,w=S=>{if(u)return;E&&clearTimeout(E),FI(e,o,a,z(S.clientX,S.clientY))&&(E=window.setTimeout(()=>{t({showHyperlinkPopup:!1})},NI))};return window.addEventListener("pointermove",w,!1),()=>{window.removeEventListener("pointermove",w,!1),E&&clearTimeout(E)}},[a,e,u,t,o]);let f=bv(()=>{le("hyperlink","delete"),P(e,{link:null}),t({showHyperlinkPopup:!1})},[t,e]),b=()=>{le("hyperlink","edit","popup-ui"),t({showHyperlinkPopup:"editor"})},{x,y:T}=wv(e,a,o);return a.contextMenu||a.selectedElementsAreBeingDragged||a.resizingElement||a.isRotating||a.openMenu||a.viewModeEnabled?null:Ev("div",{className:"excalidraw-hyperlinkContainer",style:{top:`${T}px`,left:`${x}px`,width:iu,padding:ru},children:[u?Pi("input",{className:PI("excalidraw-hyperlinkContainer-input"),placeholder:g("labels.link.hint"),ref:p,value:m,onChange:E=>d(E.target.value),autoFocus:!0,onKeyDown:E=>{E.stopPropagation(),E[y.CTRL_OR_CMD]&&E.key===y.K&&E.preventDefault(),(E.key===y.ENTER||E.key===y.ESCAPE)&&(h(),t({showHyperlinkPopup:"info"}))}}):e.link?Pi("a",{href:uo(e.link||""),className:"excalidraw-hyperlinkContainer-link",target:zl(e.link)?"_self":"_blank",onClick:E=>{if(e.link&&r){let w=Pl("excalidraw-link",E.nativeEvent);r({...e,link:uo(e.link)},w),w.defaultPrevented&&E.preventDefault()}},rel:"noopener noreferrer",children:e.link}):Pi("div",{className:"excalidraw-hyperlinkContainer-link",children:g("labels.link.empty")}),Ev("div",{className:"excalidraw-hyperlinkContainer__buttons",children:[!u&&Pi(X,{type:"button",title:g("buttons.edit"),"aria-label":g("buttons.edit"),label:g("buttons.edit"),onClick:b,className:"excalidraw-hyperlinkContainer--edit",icon:Hl}),Pi(X,{type:"button",title:g("labels.linkToElement"),"aria-label":g("labels.linkToElement"),label:g("labels.linkToElement"),onClick:()=>{t({openDialog:{name:"elementLinkSelector",sourceElementId:e.id}})},icon:rs}),c&&!Et(e)&&Pi(X,{type:"button",title:g("buttons.remove"),"aria-label":g("buttons.remove"),label:g("buttons.remove"),onClick:f,className:"excalidraw-hyperlinkContainer--remove",icon:Ot})]})]})},wv=(e,o,t)=>{let[r,n]=yt(e,t),{x:i,y:a}=xt({sceneX:r+e.width/2,sceneY:n},o),l=i-o.offsetLeft-iu/2,s=a-o.offsetTop-yv;return{x:l,y:s}},au=(e,o)=>{let t=$(e,o);return Et(t[0])?"labels.link.editEmbed":t[0]?.link?"labels.link.edit":"labels.link.create"},Ha=null,Tv=(e,o,t)=>{Ha&&clearTimeout(Ha),Ha=window.setTimeout(()=>OI(e,o,t),wh)},OI=(e,o,t)=>{if(!e.link)return;let r=Ii();r.classList.add("excalidraw-tooltip--visible"),r.style.maxWidth="20rem",r.textContent=pn(e.link)?g("labels.link.goToElement"):e.link;let[n,i,a,l]=yt(e,t),[s,c,m,d]=Gx([n,i,a,l],e.angle,o),p=xt({sceneX:s,sceneY:c},o);Gp(r,{left:p.x,top:p.y,width:m,height:d},"top"),le("hyperlink","tooltip","link-icon"),nu=!0},lu=()=>{Ha&&clearTimeout(Ha),nu&&(nu=!1,Ii().classList.remove("excalidraw-tooltip--visible"))},FI=(e,o,t,[r,n])=>{let{x:i,y:a}=Re({clientX:r,clientY:n},t),l=15/t.zoom.value;if(ax(i,a,e,o))return!1;let[s,c,m]=yt(e,o);if(i>=s&&i<=m&&a>=c-yv&&a<=c)return!1;let{x:d,y:p}=wv(e,t,o);return!(r>=d-l&&r<=d+iu+ru*2+l&&n>=p-l&&n<=p+l+ru*2+RI)};import{jsx as BI}from"react/jsx-runtime";var Ga=D({name:"hyperlink",label:(e,o)=>au(e,o),icon:fa,perform:(e,o)=>o.showHyperlinkPopup==="editor"?!1:{elements:e,appState:{...o,showHyperlinkPopup:"editor",openMenu:null},captureUpdate:L.IMMEDIATELY},trackEvent:{category:"hyperlink",action:"click"},keyTest:e=>e[y.CTRL_OR_CMD]&&e.key===y.K,predicate:(e,o)=>$(e,o).length===1,PanelComponent:({elements:e,appState:o,updateData:t})=>{let r=$(e,o);return BI(X,{type:"button",icon:fa,"aria-label":g(au(e,o)),title:`${Et(e[0])?g("labels.link.labelEmbed"):g("labels.link.label")} - ${A("CtrlOrCmd+K")}`,onClick:()=>t(null),selected:r.length===1&&!!r[0].link})}});var su=e=>e.every(o=>!o.locked),cu=D({name:"toggleElementLock",label:(e,o,t)=>{let r=t.scene.getSelectedElements({selectedElementIds:o.selectedElementIds,includeBoundTextElement:!1});return r.length===1&&!ie(r[0])?r[0].locked?"labels.elementLock.unlock":"labels.elementLock.lock":su(r)?"labels.elementLock.lockAll":"labels.elementLock.unlockAll"},icon:(e,o)=>{let t=$(o,e);return su(t)?Jn:on},trackEvent:{category:"element"},predicate:(e,o,t,r)=>{let n=r.scene.getSelectedElements(o);return n.length>0&&!n.some(i=>i.locked&&i.frameId)},perform:(e,o,t,r)=>{let n=r.scene.getSelectedElements({selectedElementIds:o.selectedElementIds,includeBoundTextElement:!0,includeElementsInFrames:!0});if(!n.length)return!1;let i=su(n),a=Q(n);return{elements:e.map(l=>a.has(l.id)?q(l,{locked:i}):l),appState:{...o,selectedLinearElement:i?null:o.selectedLinearElement},captureUpdate:L.IMMEDIATELY}},keyTest:(e,o,t,r)=>e.key.toLocaleLowerCase()===y.L&&e[y.CTRL_OR_CMD]&&e.shiftKey&&r.scene.getSelectedElements({selectedElementIds:o.selectedElementIds,includeBoundTextElement:!1}).length>0}),Cv=D({name:"unlockAllElements",paletteName:"Unlock all elements",trackEvent:{category:"canvas"},viewMode:!1,icon:on,predicate:(e,o)=>$(e,o).length===0&&e.some(r=>r.locked),perform:(e,o)=>{let t=e.filter(r=>r.locked);return{elements:e.map(r=>r.locked?q(r,{locked:!1}):r),appState:{...o,selectedElementIds:Object.fromEntries(t.map(r=>[r.id,!0]))},captureUpdate:L.IMMEDIATELY}},label:"labels.elementLock.unlockAll"});import{useEffect as Kc,useRef as Ak,useState as Wc}from"react";import tk from"clsx";import{useEffect as ok,useState as rk}from"react";import{useCallback as zI,useState as UI}from"react";var Iv=()=>{let[e,o]=UI(null),t=zI(r=>o(r),[]);return[e,t]};import{createPortal as HI}from"react-dom";import GI from"clsx";import{useRef as YI}from"react";import{jsx as Sv,jsxs as VI}from"react/jsx-runtime";var kv=e=>{let{closeOnClickOutside:o=!0}=e,t=ec({className:"excalidraw-modal-container"}),r=YI(document.body.classList.contains("excalidraw-animations-disabled"));if(!t)return null;let n=i=>{i.key===y.ESCAPE&&(i.nativeEvent.stopImmediatePropagation(),i.stopPropagation(),e.onCloseRequest())};return HI(VI("div",{className:GI("Modal",e.className,{"animations-disabled":r.current}),role:"dialog","aria-modal":"true",onKeyDown:n,"aria-labelledby":e.labelledBy,"data-prevent-outside-click":!0,children:[Sv("div",{className:"Modal__background",onClick:o?e.onCloseRequest:void 0}),Sv("div",{className:"Modal__content",style:{"--max-width":`${e.maxWidth}px`},tabIndex:0,children:e.children})]}),t)};import{useState as uw,useCallback as yu,useMemo as gw,useEffect as dw,memo as hw,useRef as qS}from"react";import{useEffect as _v,useRef as Mv}from"react";import{useEffect as WI,useState as KI}from"react";var du=ye(new Map),Lv=async e=>await Ca({elements:e,appState:{exportBackground:!1,viewBackgroundColor:Ho.white},files:null,renderEmbeddables:!1,skipInliningFonts:!0}),Av=(e,o,t)=>{let[r,n]=KI();return WI(()=>{if(o)if(e){let i=t.get(e);i?n(i):(async()=>{let a=await Lv(o);a.querySelector(".style-fonts")?.remove(),a&&(t.set(e,a),n(a))})()}else(async()=>{let i=await Lv(o);n(i)})()},[e,o,t,n]),r},Sc=()=>{let[e]=ce(du);return{clearLibraryCache:()=>e.clear(),deleteItemsFromLibraryCache:r=>{r.forEach(n=>e.delete(n))},svgCache:e}};var kc=class{constructor(){C(this,"jobs",[]);C(this,"running",!1)}tick(){if(this.running)return;let o=this.jobs.shift();o?(this.running=!0,o.promise.resolve(da(o.jobFactory,...o.args).finally(()=>{this.running=!1,this.tick()}))):this.running=!1}push(o,...t){let r=Ml();return this.jobs.push({jobFactory:o,promise:r,args:t}),this.tick(),r}};var XI=["excalidraw.com","raw.githubusercontent.com/excalidraw/excalidraw-libraries"],Pv=new qt,Cn=ye({status:"loaded",isInitialized:!1,libraryItems:[]}),Ya=e=>At(e),$I=(e,o)=>!e.find(t=>t.elements.length!==o.elements.length?!1:t.elements.every((r,n)=>r.id===o.elements[n].id&&r.versionNonce===o.elements[n].versionNonce)),Rv=(e,o)=>{let t=[];for(let r of o)$I(e,r)&&t.push(r);return[...t,...e]},Nv=(e,o)=>{let t=Q(o),r={deletedItems:new Map,addedItems:new Map};for(let i of e)t.has(i.id)||r.deletedItems.set(i.id,i);let n=Q(e);for(let i of o)n.has(i.id)||r.addedItems.set(i.id,i);return r},mu=class{constructor(o){C(this,"currLibraryItems",[]);C(this,"prevLibraryItems",Ya(this.currLibraryItems));C(this,"app");C(this,"updateQueue",[]);C(this,"getLastUpdateTask",()=>this.updateQueue[this.updateQueue.length-1]);C(this,"notifyListeners",()=>{if(this.updateQueue.length>0)st.set(Cn,o=>({status:"loading",libraryItems:this.currLibraryItems,isInitialized:o.isInitialized}));else{st.set(Cn,{status:"loaded",libraryItems:this.currLibraryItems,isInitialized:!0});try{let o=this.prevLibraryItems;this.prevLibraryItems=Ya(this.currLibraryItems);let t=Ya(this.currLibraryItems);this.app.props.onLibraryChange?.(t),Pv.trigger(Nv(o,t),t)}catch(o){console.error(o)}}});C(this,"destroy",()=>{this.updateQueue=[],this.currLibraryItems=[],st.set(du,new Map)});C(this,"resetLibrary",()=>this.setLibrary([]));C(this,"getLatestLibrary",()=>new Promise(async o=>{try{let t=await(this.getLastUpdateTask()||this.currLibraryItems);this.updateQueue.length>0?o(this.getLatestLibrary()):o(Ya(t))}catch{return o(this.currLibraryItems)}}));C(this,"updateLibrary",async({libraryItems:o,prompt:t=!1,merge:r=!1,openLibraryMenu:n=!1,defaultStatus:i="unpublished"})=>(n&&this.app.setState({openSidebar:{name:pt.name,tab:ia}}),this.setLibrary(()=>new Promise(async(a,l)=>{try{let s=await(typeof o=="function"&&!(o instanceof Blob)?o(this.currLibraryItems):o),c;s instanceof Blob?c=await Om(s,i):c=va(s,i),!t||window.confirm(g("alerts.confirmAddLibrary",{numShapes:c.length}))?(t&&this.app.focusContainer(),a(r?Rv(this.currLibraryItems,c):c)):l(new Xn)}catch(s){l(s)}}))));C(this,"setLibrary",o=>{let t=new Promise(async(r,n)=>{try{await this.getLastUpdateTask(),typeof o=="function"&&(o=o(this.currLibraryItems)),this.currLibraryItems=Ya(await o),r(this.currLibraryItems)}catch(i){n(i)}}).catch(r=>{if(r.name==="AbortError")return console.warn("Library update aborted by user"),this.currLibraryItems;throw r}).finally(()=>{this.updateQueue=this.updateQueue.filter(r=>r!==t),this.notifyListeners()});return this.updateQueue.push(t),this.notifyListeners(),t});this.app=o}},Ov=mu,_c=e=>{let t=Math.ceil(Math.sqrt(e.length)),r=[],n=u=>e.slice(u*t,u*t+t).reduce((f,b)=>{let{height:x}=Bt(b.elements);return Math.max(f,x)},0),i=u=>{let h=0,f=0,b=0;for(let x of e){if(h%t===0&&(f=0),f===u){let{width:T}=Bt(x.elements);b=Math.max(b,T)}h++,f++}return b},a=0,l=0,s=0,c=0,m=0,d=0,p=0;for(let u of e){m&&m%t===0&&(l+=s+50,a=0,d=0,p++),d===0&&(s=n(p)),c=i(d);let{minX:h,minY:f,width:b,height:x}=Bt(u.elements),T=(c-b)/2,E=(s-x)/2;r.push(...u.elements.map(w=>({...w,x:w.x+a+T-h,y:w.y+l+E-f}))),a+=c+50,m++,d++}return r},ZI=(e,o=XI)=>{if(typeof o=="function"?o(e):o.some(t=>{let r=new URL(`https://${t.replace(/^https?:\/\//,"")}`),{hostname:n,pathname:i}=new URL(e);return new RegExp(`(^|\\.)${r.hostname}$`).test(n)&&new RegExp(`^${r.pathname.replace(/\/+$/,"")}(/+|$)`).test(i)}))return!0;throw new Error(`Invalid or disallowed library URL: "${e}"`)},pu=()=>{let e=new URLSearchParams(window.location.hash.slice(1)).get(Cl.addLibrary)||new URLSearchParams(window.location.search).get(Tl.addLibrary),o=e?new URLSearchParams(window.location.hash.slice(1)).get("token"):null;return e?{libraryUrl:e,idToken:o}:null},_r=class _r{constructor(o){C(this,"adapter");this.adapter=o}static async getLibraryItems(o,t,r=!0){let n=()=>new Promise(async(i,a)=>{try{let l=await o.load({source:t});i(va(l?.libraryItems||[],"published"))}catch(l){a(l)}});return r?_r.queue.push(n):n()}getLibraryItems(o){return _r.getLibraryItems(this.adapter,o,!1)}};C(_r,"queue",new kc),C(_r,"run",async(o,t)=>{let r=new _r(o);return _r.queue.push(()=>t(r))});var Ri=_r,Va=0,Lc=0,Ac=e=>lp(e.map(o=>`${o.id}:${ap(o.elements)}`).sort().join()),Dv=async(e,o)=>{try{return Lc++,await Ri.run(e,async t=>{let r=Q(await t.getLibraryItems("save"));for(let[l]of o.deletedItems)r.delete(l);let n=[];for(let[l,s]of o.addedItems)r.has(l)?r.set(l,s):n.push(s);let i=n.concat(Array.from(r.values())),a=Ac(i);return a!==Va&&await e.save({libraryItems:i}),Va=a,i})}finally{Lc--}},jI=e=>{let{excalidrawAPI:o}=e,t=Mv(e);t.current=e;let r=Mv(!1);_v(()=>{if(!o)return;r.current=!1;let n=async({libraryUrl:l,idToken:s})=>{let c=new Promise(async(d,p)=>{try{l=decodeURIComponent(l),l=Ul(l),ZI(l,t.current.validateLibraryUrl);let h=await(await fetch(l)).blob();d(h)}catch(u){p(u)}}),m=s!==o.id;await(m&&document.hidden?new Promise(d=>{window.addEventListener("focus",()=>d(),{once:!0})}):null);try{await o.updateLibrary({libraryItems:c,prompt:m,merge:!0,defaultStatus:"published",openLibraryMenu:!0})}catch(d){throw o.updateScene({appState:{errorMessage:d.message}}),d}finally{if(window.location.hash.includes(Cl.addLibrary)){let d=new URLSearchParams(window.location.hash.slice(1));d.delete(Cl.addLibrary),window.history.replaceState({},ea,`#${d.toString()}`)}else if(window.location.search.includes(Tl.addLibrary)){let d=new URLSearchParams(window.location.search);d.delete(Tl.addLibrary),window.history.replaceState({},ea,`?${d.toString()}`)}}},i=l=>{l.preventDefault();let s=pu();s&&(l.stopImmediatePropagation(),window.history.replaceState({},"",l.oldURL),n(s))},a=pu();if(a&&n(a),"getInitialLibraryItems"in t.current&&t.current.getInitialLibraryItems&&(console.warn("useHandleLibrar `opts.getInitialLibraryItems` is deprecated. Use `opts.adapter` instead."),Promise.resolve(t.current.getInitialLibraryItems()).then(l=>{o.updateLibrary({libraryItems:l,merge:!0})}).catch(l=>{console.error(`UseHandeLibrary getInitialLibraryItems failed: ${l?.message}`)})),"adapter"in t.current&&t.current.adapter){let l=t.current.adapter,s=t.current.migrationAdapter,c=Ml();s?c.resolve(da(s.load).then(async m=>{let d=null;try{if(!m)return Ri.getLibraryItems(l,"load");d=va(m.libraryItems||[],"published");let p=await Dv(l,Nv([],d));try{await s.clear()}catch(u){console.error(`couldn't delete legacy library data: ${u.message}`)}return p}catch(p){return console.error(`couldn't migrate legacy library data: ${p.message}`),d}}).catch(m=>(console.error(`error during library migration: ${m.message}`),Ri.getLibraryItems(l,"load")))):c.resolve(da(Ri.getLibraryItems,l,"load")),o.updateLibrary({libraryItems:c.then(m=>{let d=m||[];return Va=Ac(d),d}),merge:!0}).finally(()=>{r.current=!0})}return window.addEventListener("hashchange",i),()=>{window.removeEventListener("hashchange",i)}},[o]),_v(()=>{let n=Pv.on(async(a,l)=>{let s=r.current,c="adapter"in t.current&&t.current.adapter||null;try{c&&Va!==Ac(l)&&await Dv(c,a)}catch(m){console.error(`couldn't persist library update: ${m.message}`,a),s&&t.current.excalidrawAPI&&t.current.excalidrawAPI.updateScene({appState:{errorMessage:g("errors.saveLibraryError")}})}}),i=a=>{Lc&&Hh(a)};return window.addEventListener("beforeunload",i),()=>{window.removeEventListener("beforeunload",i),n(),Va=0,Lc=0}},[])};import{useCallback as Fi,useEffect as XS,useMemo as sw,useRef as $S,useState as ZS}from"react";import{forwardRef as Fv}from"react";import Bv from"clsx";import{jsx as zv}from"react/jsx-runtime";var JI=Fv(({children:e,gap:o,align:t,justifyContent:r,className:n,style:i},a)=>zv("div",{className:Bv("Stack Stack_horizontal",n),style:{"--gap":o,alignItems:t,justifyContent:r,...i},ref:a,children:e})),qI=Fv(({children:e,gap:o,align:t,justifyContent:r,className:n,style:i},a)=>zv("div",{className:Bv("Stack Stack_vertical",n),style:{"--gap":o,justifyItems:t,justifyContent:r,...i},ref:a,children:e})),it={Row:JI,Col:qI};import{jsx as eS}from"react/jsx-runtime";var QI=({theme:e,id:o,libraryReturnUrl:t})=>{let r=t||window.location.origin+window.location.pathname;return eS("a",{className:"library-menu-browse-button",href:`${v.VITE_APP_LIBRARY_URL}?target=${window.name||"_blank"}&referrer=${r}&useHash=true&token=${o}&theme=${e}&version=${Sl.excalidrawLibrary}`,target:"_excalidraw_libraries",children:g("labels.libraries")})},Uv=QI;import tS from"clsx";import{jsx as oS,jsxs as rS}from"react/jsx-runtime";var Mc=({libraryReturnUrl:e,theme:o,id:t,style:r,children:n,className:i})=>rS("div",{className:tS("library-menu-control-buttons",i),style:r,children:[oS(Uv,{id:t,libraryReturnUrl:e,theme:o}),n]});import{useCallback as LS,useState as hu}from"react";import Dc from"react";var nS=/({{[\w-]+}})|(<[\w-]+>)|(<\/[\w-]+>)/g,iS=/{{([\w-]+)}}/,aS=/<([\w-]+)>/,lS=/<\/([\w-]+)>/,sS=(e,o)=>{let t=[{name:"",children:[]}];return e.split(nS).filter(Boolean).forEach(r=>{let n=r.match(aS),i=r.match(lS),a=r.match(iS);if(n!==null){let l=n[1];o.hasOwnProperty(l)?t.push({name:l,children:[]}):console.warn(`Trans: missed to pass in prop ${l} for interpolating ${e}`)}else if(i!==null)if(i[1]===t[t.length-1].name){let s=t.pop(),c=Dc.createElement(Dc.Fragment,{},...s.children),m=o[s.name];typeof m=="function"&&t[t.length-1].children.push(m(c))}else console.warn(`Trans: unexpected end tag ${r} for interpolating ${e}`);else if(a!==null){let l=a[1];o.hasOwnProperty(l)?t[t.length-1].children.push(o[l]):console.warn(`Trans: key ${l} not in props for interpolating ${e}`)}else t[t.length-1].children.push(r)}),t.length!==1&&console.warn(`Trans: stack not empty for interpolating ${e}`),t[0].children},cS=({i18nKey:e,children:o,...t})=>{let{t:r}=Ve();return Dc.createElement(Dc.Fragment,{},...sS(r(e),t))},Tt=cS;import{flushSync as Hv}from"react-dom";import dS from"clsx";import{jsx as Pc,jsxs as pS}from"react/jsx-runtime";var mS=({label:e,onClick:o,className:t,children:r,actionType:n,type:i="button",isLoading:a,...l})=>{let s=n?`Dialog__action-button--${n}`:"";return pS("button",{className:dS("Dialog__action-button",s,t),type:i,"aria-label":e,onClick:o,...l,children:[r&&Pc("div",{style:a?{visibility:"hidden"}:{},children:r}),Pc("div",{style:a?{visibility:"hidden"}:{},children:e}),a&&Pc("div",{style:{position:"absolute",inset:0},children:Pc(ct,{})})]})},or=mS;import{jsx as Gv,jsxs as Yv}from"react/jsx-runtime";var uS=e=>{let{onConfirm:o,onCancel:t,children:r,confirmText:n=g("buttons.confirm"),cancelText:i=g("buttons.cancel"),className:a="",...l}=e,s=pe(),c=Tr(Ni),{container:m}=Ke();return Yv(Ge,{onCloseRequest:t,size:"small",...l,className:`confirm-dialog ${a}`,children:[r,Yv("div",{className:"confirm-dialog-buttons",children:[Gv(or,{label:i,onClick:()=>{s({openMenu:null}),c(!1),Hv(()=>{t()}),m?.focus()}}),Gv(or,{label:n,onClick:()=>{s({openMenu:null}),c(!1),Hv(()=>{o()}),m?.focus()},actionType:"danger"})]})]})},Rc=uS;import{useCallback as gS,useEffect as gu,useRef as Vv,useState as uu}from"react";import Nc from"open-color";var ro=class{static has(o){try{return!!window.localStorage.getItem(o)}catch(t){return console.warn(`localStorage.getItem error: ${t.message}`),!1}}static get(o){try{let t=window.localStorage.getItem(o);return t?JSON.parse(t):null}catch(t){return console.warn(`localStorage.getItem error: ${t.message}`),null}}};C(ro,"set",(o,t)=>{try{return window.localStorage.setItem(o,JSON.stringify(t)),!0}catch(r){return console.warn(`localStorage.setItem error: ${r.message}`),!1}}),C(ro,"delete",o=>{try{window.localStorage.removeItem(o)}catch(t){console.warn(`localStorage.removeItem error: ${t.message}`)}});import{jsx as de,jsxs as ht}from"react/jsx-runtime";var hS=async e=>{let r=Math.round(8),n=Math.max(Math.round(128/64),2),i=Bh(e,6),a=document.createElement("canvas");a.width=i[0].length*128+(i[0].length+1)*(r*2)-r*2,a.height=i.length*128+(i.length+1)*(r*2)-r*2;let l=a.getContext("2d");l.fillStyle=Nc.white,l.fillRect(0,0,a.width,a.height);for(let[s,c]of e.entries()){let m=await un({elements:c.elements,files:null,maxWidthOrHeight:128}),{width:d,height:p}=m,u=Math.floor(s/6)*(128+r*2),h=s%6*(128+r*2);l.drawImage(m,h+(128-d)/2+r,u+(128-p)/2+r),l.lineWidth=n,l.strokeStyle=Nc.gray[4],l.strokeRect(h+r/2,u+r/2,128+r,128+r)}return await wa(new File([await jo(a)],"preview",{type:je.png}),{outputType:je.jpg,maxWidthOrHeight:5e3})},fS=({libItem:e,appState:o,index:t,onChange:r,onRemove:n})=>{let i=Vv(null),a=Vv(null);return gu(()=>{let l=i.current;l&&(async()=>{let s=await Ca({elements:e.elements,appState:{...o,viewBackgroundColor:Nc.white,exportBackground:!0},files:null,skipInliningFonts:!0});l.innerHTML=s.outerHTML})()},[e.elements,o]),ht("div",{className:"single-library-item",children:[e.status==="published"&&de("span",{className:"single-library-item-status",children:g("labels.statusPublished")}),de("div",{ref:i,className:"single-library-item__svg"}),de(X,{"aria-label":g("buttons.remove"),type:"button",icon:jt,className:"single-library-item--remove",onClick:n.bind(null,e.id),title:g("buttons.remove")}),ht("div",{style:{display:"flex",margin:"0.8rem 0",width:"100%",fontSize:"14px",fontWeight:500,flexDirection:"column"},children:[ht("label",{style:{display:"flex",justifyContent:"space-between",flexDirection:"column"},children:[ht("div",{style:{padding:"0.5em 0"},children:[de("span",{style:{fontWeight:500,color:Nc.gray[6]},children:g("publishDialog.itemName")}),de("span",{"aria-hidden":"true",className:"required",children:"*"})]}),de("input",{type:"text",ref:a,style:{width:"80%",padding:"0.2rem"},defaultValue:e.name,placeholder:"Item name",onChange:l=>{r(l.target.value,t)}})]}),de("span",{className:"error",children:e.error})]})]})},bS=({onClose:e,libraryItems:o,appState:t,onSuccess:r,onError:n,updateItemsInStorage:i,onRemove:a})=>{let[l,s]=uu({authorName:"",githubHandle:"",name:"",description:"",twitterHandle:"",website:""}),[c,m]=uu(!1);gu(()=>{let E=ro.get(cr.PUBLISH_LIBRARY);E&&s(E)},[]);let[d,p]=uu(o.slice());gu(()=>{p(o.slice())},[o]);let u=E=>{s({...l,[E.target.name]:E.target.value})},h=async E=>{E.preventDefault(),m(!0);let w=[],S=!1;if(d.forEach(N=>{let G="";N.name||(G=g("publishDialog.errors.required"),S=!0),w.push({...N,error:G})}),S){p(w),m(!1);return}let I=await hS(d),_={type:fh.excalidrawLibrary,version:Sl.excalidrawLibrary,source:bh,libraryItems:d},k=JSON.stringify(_,null,2),R=new Blob([k],{type:"application/json"}),M=new FormData;M.append("excalidrawLib",R),M.append("previewImage",I),M.append("previewImageType",I.type),M.append("title",l.name),M.append("authorName",l.authorName),M.append("githubHandle",l.githubHandle),M.append("name",l.name),M.append("description",l.description),M.append("twitterHandle",l.twitterHandle),M.append("website",l.website),fetch(`${v.VITE_APP_LIBRARY_BACKEND}/submit`,{method:"post",body:M}).then(N=>N.ok?N.json().then(({url:G})=>{ro.delete(cr.PUBLISH_LIBRARY),r({url:G,authorName:l.authorName,items:d})}):N.json().catch(()=>{throw new Error(N.statusText||"something went wrong")}).then(G=>{throw new Error(G.message||N.statusText||"something went wrong")}),N=>{console.error(N),n(N),m(!1)}).catch(N=>{console.error(N),n(N),m(!1)})},f=()=>{let E=[];return d.forEach((w,S)=>{E.push(de("div",{className:"single-library-item-wrapper",children:de(fS,{libItem:w,appState:t,index:S,onChange:(I,_)=>{let k=d.slice();k[_].name=I,p(k)},onRemove:a})},S))}),de("div",{className:"selected-library-items",children:E})},b=gS(()=>{i(d),ro.set(cr.PUBLISH_LIBRARY,l),e()},[d,e,i,l]),x=!!o.length,T=o.some(E=>E.status==="published");return de(Ge,{onCloseRequest:b,title:g("publishDialog.title"),className:"publish-library",children:x?ht("form",{onSubmit:h,children:[de("div",{className:"publish-library-note",children:de(Tt,{i18nKey:"publishDialog.noteDescription",link:E=>de("a",{href:"https://libraries.excalidraw.com",target:"_blank",rel:"noopener noreferrer",children:E})})}),de("span",{className:"publish-library-note",children:de(Tt,{i18nKey:"publishDialog.noteGuidelines",link:E=>de("a",{href:"https://github.com/excalidraw/excalidraw-libraries#guidelines",target:"_blank",rel:"noopener noreferrer",children:E})})}),de("div",{className:"publish-library-note",children:g("publishDialog.noteItems")}),T&&de("span",{className:"publish-library-note publish-library-warning",children:g("publishDialog.republishWarning")}),f(),ht("div",{className:"publish-library__fields",children:[ht("label",{children:[ht("div",{children:[de("span",{children:g("publishDialog.libraryName")}),de("span",{"aria-hidden":"true",className:"required",children:"*"})]}),de("input",{type:"text",name:"name",required:!0,value:l.name,onChange:u,placeholder:g("publishDialog.placeholder.libraryName")})]}),ht("label",{style:{alignItems:"flex-start"},children:[ht("div",{children:[de("span",{children:g("publishDialog.libraryDesc")}),de("span",{"aria-hidden":"true",className:"required",children:"*"})]}),de("textarea",{name:"description",rows:4,required:!0,value:l.description,onChange:u,placeholder:g("publishDialog.placeholder.libraryDesc")})]}),ht("label",{children:[ht("div",{children:[de("span",{children:g("publishDialog.authorName")}),de("span",{"aria-hidden":"true",className:"required",children:"*"})]}),de("input",{type:"text",name:"authorName",required:!0,value:l.authorName,onChange:u,placeholder:g("publishDialog.placeholder.authorName")})]}),ht("label",{children:[de("span",{children:g("publishDialog.githubUsername")}),de("input",{type:"text",name:"githubHandle",value:l.githubHandle,onChange:u,placeholder:g("publishDialog.placeholder.githubHandle")})]}),ht("label",{children:[de("span",{children:g("publishDialog.twitterUsername")}),de("input",{type:"text",name:"twitterHandle",value:l.twitterHandle,onChange:u,placeholder:g("publishDialog.placeholder.twitterHandle")})]}),ht("label",{children:[de("span",{children:g("publishDialog.website")}),de("input",{type:"text",name:"website",pattern:"https?://.+",title:g("publishDialog.errors.website"),value:l.website,onChange:u,placeholder:g("publishDialog.placeholder.website")})]}),de("span",{className:"publish-library-note",children:de(Tt,{i18nKey:"publishDialog.noteLicense",link:E=>de("a",{href:"https://github.com/excalidraw/excalidraw-libraries/blob/main/LICENSE",target:"_blank",rel:"noopener noreferrer",children:E})})})]}),ht("div",{className:"publish-library__buttons",children:[de(or,{label:g("buttons.cancel"),onClick:b,"data-testid":"cancel-clear-canvas-button"}),de(or,{type:"submit",label:g("buttons.submit"),actionType:"primary",isLoading:c})]})]}):de("p",{style:{padding:"1em",textAlign:"center",fontWeight:500},children:g("publishDialog.atleastOneLibItem")})})},Wv=bS;import xS from"clsx";import{jsx as ES}from"react/jsx-runtime";var Kv=({className:e="",children:o,onToggle:t,title:r,...n})=>{let i=me(),a=xS(`dropdown-menu-button ${e}`,"zen-mode-transition",{"dropdown-menu-button--mobile":i.editor.isMobile}).trim();return ES("button",{"data-prevent-outside-click":!0,className:a,onClick:t,type:"button","data-testid":"dropdown-menu-button",title:r,...n,children:o})},Xv=Kv;Kv.displayName="DropdownMenuTrigger";import{jsx as yS}from"react/jsx-runtime";var $v=()=>yS("div",{style:{height:"1px",backgroundColor:"var(--default-border-color)",margin:".5rem 0"}}),Zv=$v;$v.displayName="DropdownMenuSeparator";import vS from"clsx";import{useEffect as wS,useRef as TS}from"react";import{jsx as Oc}from"react/jsx-runtime";var jv=({children:e,onClickOutside:o,className:t="",onSelect:r,style:n})=>{let i=me(),a=TS(null),l=Ir({onClickOutside:o});vi(a,()=>{l.onClickOutside?.()}),wS(()=>{let c=d=>{d.key===y.ESCAPE&&(d.stopImmediatePropagation(),l.onClickOutside?.())},m={capture:!0};return document.addEventListener("keydown",c,m),()=>{document.removeEventListener("keydown",c,m)}},[l]);let s=vS(`dropdown-menu ${t}`,{"dropdown-menu--mobile":i.editor.isMobile}).trim();return Oc(Mp.Provider,{value:{onSelect:r},children:Oc("div",{ref:a,className:s,style:n,"data-testid":"dropdown-menu",children:i.editor.isMobile?Oc(it.Col,{className:"dropdown-menu-container",children:e}):Oc(Qe,{className:"dropdown-menu-container",padding:2,style:{zIndex:2},children:e})})})};jv.displayName="DropdownMenuContent";var Jv=jv;import{jsx as qv}from"react/jsx-runtime";var Qv=({icon:e,shortcut:o,href:t,children:r,onSelect:n,className:i="",selected:a,rel:l="noreferrer",...s})=>{let c=sc(s.onClick,n);return qv("a",{...s,href:t,target:"_blank",rel:"noreferrer",className:lc(i,a),title:s.title??s["aria-label"],onClick:c,children:qv(cc,{icon:e,shortcut:o,children:r})})},Oi=Qv;Qv.displayName="DropdownMenuItemLink";import{jsx as IS}from"react/jsx-runtime";var CS=({children:e,className:o="",selected:t,...r})=>IS("div",{...r,className:`dropdown-menu-item-base dropdown-menu-item-custom ${o} ${t?"dropdown-menu-item--selected":""}`.trim(),children:e}),ew=CS;import Fc from"react";var tw=e=>{let o=Fc.Children.toArray(e).find(t=>Fc.isValidElement(t)&&typeof t.type!="string"&&t?.type.displayName&&t.type.displayName==="DropdownMenuTrigger");return o||null},ow=e=>{let o=Fc.Children.toArray(e).find(t=>Fc.isValidElement(t)&&typeof t.type!="string"&&t?.type.displayName&&t.type.displayName==="DropdownMenuContent");return o||null};import{Fragment as SS,jsxs as kS}from"react/jsx-runtime";var rr=({children:e,open:o})=>{let t=tw(e),r=ow(e);return kS(SS,{children:[t,o&&r]})};rr.Trigger=Xv;rr.Content=Jv;rr.Item=vt;rr.ItemLink=Oi;rr.ItemCustom=ew;rr.Group=Na;rr.Separator=Zv;var Ce=rr;rr.displayName="DropdownMenu";import AS from"clsx";import{jsx as Mt,jsxs as Bc}from"react/jsx-runtime";var _S=(e,o)=>e.filter(t=>o.includes(t.id)),MS=({setAppState:e,selectedItems:o,library:t,onRemoveFromLibrary:r,resetLibrary:n,onSelectItems:i,appState:a,className:l})=>{let[s]=ce(Cn),[c,m]=ce(Ni),d=()=>{let M=o.length?g("alerts.removeItemsFromsLibrary",{count:o.length}):g("alerts.resetLibrary"),N=o.length?g("confirmDialog.removeItemsFromLib"):g("confirmDialog.resetLibrary");return Mt(Rc,{onConfirm:()=>{o.length?r():n(),u(!1)},onCancel:()=>{u(!1)},title:N,children:Mt("p",{children:M})})},[p,u]=hu(!1),h=!!o.length,f=h?s.libraryItems.filter(M=>o.includes(M.id)):s.libraryItems,b=h?g("buttons.remove"):g("buttons.resetLibrary"),[x,T]=hu(!1),[E,w]=hu(null),S=LS(()=>Bc(Ge,{onCloseRequest:()=>w(null),title:g("publishSuccessDialog.title"),className:"publish-library-success",size:"small",children:[Mt("p",{children:Mt(Tt,{i18nKey:"publishSuccessDialog.content",authorName:E.authorName,link:M=>Mt("a",{href:E?.url,target:"_blank",rel:"noopener noreferrer",children:M})})}),Mt(X,{type:"button",title:g("buttons.close"),"aria-label":g("buttons.close"),label:g("buttons.close"),onClick:()=>w(null),"data-testid":"publish-library-success-close",className:"publish-library-success-close"})]}),[w,E]),I=(M,N)=>{T(!1),w({url:M.url,authorName:M.authorName});let G=N.slice();G.forEach(H=>{o.includes(H.id)&&(H.status="published")}),t.setLibrary(G)},_=async()=>{try{await t.updateLibrary({libraryItems:bs({description:"Excalidraw library files"}),merge:!0,openLibraryMenu:!0})}catch(M){if(M?.name==="AbortError"){console.warn(M);return}e({errorMessage:g("errors.importLibraryError")})}},k=async()=>{let M=h?f:await t.getLatestLibrary();Bx(M).catch(_l).catch(N=>{e({errorMessage:N.message})})},R=()=>Bc(Ce,{open:c,children:[Mt(Ce.Trigger,{onToggle:()=>m(!c),children:yf}),Bc(Ce.Content,{onClickOutside:()=>m(!1),onSelect:()=>m(!1),className:"library-menu",children:[!h&&Mt(Ce.Item,{onSelect:_,icon:Qn,"data-testid":"lib-dropdown--load",children:g("buttons.load")}),!!f.length&&Mt(Ce.Item,{onSelect:k,icon:rn,"data-testid":"lib-dropdown--export",children:g("buttons.export")}),!!f.length&&Mt(Ce.Item,{onSelect:()=>u(!0),icon:Ot,children:b}),h&&Mt(Ce.Item,{icon:Cb,onSelect:()=>T(!0),"data-testid":"lib-dropdown--remove",children:g("buttons.publishLibrary")})]})]});return Bc("div",{className:AS("library-menu-dropdown-container",l),children:[R(),o.length>0&&Mt("div",{className:"library-actions-counter",children:o.length}),p&&d(),x&&Mt(Wv,{onClose:()=>T(!1),libraryItems:_S(s.libraryItems,o),appState:a,onSuccess:M=>I(M,s.libraryItems),onError:M=>window.alert(M),updateItemsInStorage:()=>t.setLibrary(s.libraryItems),onRemove:M=>i(o.filter(N=>N!==M))}),E&&S()]})},fu=({selectedItems:e,onSelectItems:o,className:t})=>{let{library:r}=He(),{clearLibraryCache:n,deleteItemsFromLibraryCache:i}=Sc(),a=ve(),l=pe(),[s]=ce(Cn),c=async d=>{let p=d.filter(u=>!e.includes(u.id));r.setLibrary(p).catch(()=>{l({errorMessage:g("alerts.errorRemovingFromLibrary")})}),i(e),o([])};return Mt(MS,{appState:a,setAppState:l,selectedItems:e,onSelectItems:o,library:r,onRemoveFromLibrary:()=>c(s.libraryItems),resetLibrary:()=>{r.resetLibrary(),n()},className:t})};import{memo as US,useEffect as HS,useState as GS}from"react";import rw from"clsx";import{memo as DS,useEffect as PS,useRef as RS,useState as NS}from"react";import{jsx as zc,jsxs as OS}from"react/jsx-runtime";var nw=DS(({id:e,elements:o,isPending:t,onClick:r,selected:n,onToggle:i,onDrag:a,svgCache:l})=>{let s=RS(null),c=Av(e,o,l);PS(()=>{let h=s.current;if(h)return c&&(h.innerHTML=c.outerHTML),()=>{h.innerHTML=""}},[c]);let[m,d]=NS(!1),p=me().editor.isMobile,u=t&&zc("div",{className:"library-unit__adder",children:Ef});return OS("div",{className:rw("library-unit",{"library-unit__active":o,"library-unit--hover":o&&m,"library-unit--selected":n,"library-unit--skeleton":!c}),onMouseEnter:()=>d(!0),onMouseLeave:()=>d(!1),children:[zc("div",{className:rw("library-unit__dragger",{"library-unit__pulse":!!t}),ref:s,draggable:!!o,onClick:o||t?h=>{e&&h.shiftKey?i(e,h):r(e)}:void 0,onDragStart:h=>{if(!e){h.preventDefault();return}d(!1),a(e,h)}}),u,e&&o&&(m||p||n)&&zc(za,{checked:n,onChange:(h,f)=>i(e,f),className:"library-unit__checkbox"})]})}),iw=()=>zc("div",{className:"library-unit library-unit--skeleton"});import FS,{useCallback as BS}from"react";function zS(){return[!1,BS(o=>o(),[])]}var aw=FS.useTransition||zS;import{Fragment as YS,jsx as Uc}from"react/jsx-runtime";var bu=({children:e})=>Uc("div",{className:"library-menu-items-container__grid",children:e}),Hc=US(({items:e,onItemSelectToggle:o,onItemDrag:t,isItemSelected:r,onClick:n,svgCache:i,itemsRenderedPerBatch:a})=>{let[,l]=aw(),[s,c]=GS(0);return HS(()=>{s<e.length&&l(()=>{c(s+a)})},[s,e.length,l,a]),Uc(YS,{children:e.map((m,d)=>d<s?Uc(nw,{elements:m?.elements,isPending:!m?.id&&!!m?.elements,onClick:n,svgCache:i,id:m?.id,selected:r(m.id),onToggle:o,onDrag:t},m?.id??d):Uc(iw,{},d))})});import{useEffect as VS}from"react";import WS from"lodash.throttle";var KS=ye(0),lw=e=>{let[o,t]=ce(KS);return VS(()=>{let{current:r}=e;if(!r)return;let n=WS(()=>{let{scrollTop:i}=r;t(i)},200);return r.addEventListener("scroll",n),()=>{n.cancel(),r.removeEventListener("scroll",n)}},[e,t]),o};import{Fragment as cw,jsx as Dt,jsxs as Bi}from"react/jsx-runtime";var jS=17,JS=64;function xu({isLoading:e,libraryItems:o,onAddToLibrary:t,onInsertLibraryItems:r,pendingElements:n,theme:i,id:a,libraryReturnUrl:l,onSelectItems:s,selectedItems:c}){let m=$S(null),d=lw(m);XS(()=>{d>0&&m.current?.scrollTo(0,d)},[]);let{svgCache:p}=Sc(),u=sw(()=>o.filter(M=>M.status!=="published"),[o]),h=sw(()=>o.filter(M=>M.status==="published"),[o]),f=!o.length&&!n.length,b=!n.length&&!u.length&&!h.length,[x,T]=ZS(null),E=Fi((M,N)=>{let G=!c.includes(M),H=[...u,...h];if(G){if(N.shiftKey&&x){let V=H.findIndex(oe=>oe.id===x),F=H.findIndex(oe=>oe.id===M);if(V===-1||F===-1){s([...c,M]);return}let O=Q(c),j=H.reduce((oe,W,ne)=>((ne>=V&&ne<=F||O.has(W.id))&&oe.push(W.id),oe),[]);s(j)}else s([...c,M]);T(M)}else T(null),s(c.filter(V=>V!==M))},[x,s,h,c,u]),w=Fi(M=>{let N;return c.includes(M)?N=o.filter(G=>c.includes(G.id)):N=o.filter(G=>G.id===M),N.map(G=>({...G,elements:ls(G.elements,{randomizeSeed:!0})}))},[o,c]),S=Fi((M,N)=>{N.dataTransfer.setData(je.excalidrawlib,Um(w(M)))},[w]),I=Fi(M=>M?c.includes(M):!1,[c]),_=Fi(()=>{t(n)},[n,t]),k=Fi(M=>{M&&r(w(M))},[w,r]),R=p.size>=o.length?JS:jS;return Bi("div",{className:"library-menu-items-container",style:n.length||u.length||h.length?{justifyContent:"flex-start"}:{borderBottom:0},children:[!b&&Dt(fu,{selectedItems:c,onSelectItems:s,className:"library-menu-dropdown-container--in-heading"}),Bi(it.Col,{className:"library-menu-items-container__items",align:"start",gap:1,style:{flex:h.length>0?1:"0 1 auto",marginBottom:0},ref:m,children:[Bi(cw,{children:[!b&&Dt("div",{className:"library-menu-items-container__header",children:g("labels.personalLib")}),e&&Dt("div",{style:{position:"absolute",top:"var(--container-padding-y)",right:"var(--container-padding-x)",transform:"translateY(50%)"},children:Dt(ct,{})}),!n.length&&!u.length?Bi("div",{className:"library-menu-items__no-items",children:[Dt("div",{className:"library-menu-items__no-items__label",children:g("library.noItems")}),Dt("div",{className:"library-menu-items__no-items__hint",children:h.length>0?g("library.hint_emptyPrivateLibrary"):g("library.hint_emptyLibrary")})]}):Bi(bu,{children:[n.length>0&&Dt(Hc,{itemsRenderedPerBatch:R,items:[{id:null,elements:n}],onItemSelectToggle:E,onItemDrag:S,onClick:_,isItemSelected:I,svgCache:p}),Dt(Hc,{itemsRenderedPerBatch:R,items:u,onItemSelectToggle:E,onItemDrag:S,onClick:k,isItemSelected:I,svgCache:p})]})]}),Bi(cw,{children:[(h.length>0||n.length>0||u.length>0)&&Dt("div",{className:"library-menu-items-container__header library-menu-items-container__header--excal",children:g("labels.excalidrawLib")}),h.length>0?Dt(bu,{children:Dt(Hc,{itemsRenderedPerBatch:R,items:h,onItemSelectToggle:E,onItemDrag:S,onClick:k,isItemSelected:I,svgCache:p})}):u.length>0?Dt("div",{style:{margin:"1rem 0",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"100%",fontSize:".9rem"},children:g("library.noItems")}):null]}),f&&Dt(Mc,{style:{padding:"16px 0",width:"100%"},id:a,libraryReturnUrl:l,theme:i,children:Dt(fu,{selectedItems:c,onSelectItems:s})})]})]})}import{jsx as Mr,jsxs as pw}from"react/jsx-runtime";var Ni=ye(!1),mw=({children:e})=>Mr("div",{className:"layer-ui__library",children:e}),QS=hw(({onInsertLibraryItems:e,pendingElements:o,onAddToLibrary:t,setAppState:r,libraryReturnUrl:n,library:i,id:a,theme:l,selectedItems:s,onSelectItems:c})=>{let[m]=ce(Cn),d=yu(h=>{(async(b,x)=>{le("element","addToLibrary","ui");for(let E of Ll)if(b.some(w=>w.type===E))return r({errorMessage:g(`errors.libraryElementTypeError.${E}`)});let T=[{status:"unpublished",elements:b,id:gr(),created:Date.now()},...x];t(),i.setLibrary(T).catch(()=>{r({errorMessage:g("alerts.errorAddingToLibrary")})})})(h,m.libraryItems)},[t,i,r,m.libraryItems]),p=gw(()=>m.libraryItems,[m]);if(m.status==="loading"&&!m.isInitialized)return Mr(mw,{children:Mr("div",{className:"layer-ui__library-message",children:pw("div",{children:[Mr(ct,{size:"2em"}),Mr("span",{children:g("labels.libraryLoadingMessage")})]})})});let u=m.libraryItems.length>0||o.length>0;return pw(mw,{children:[Mr(xu,{isLoading:m.status==="loading",libraryItems:p,onAddToLibrary:d,onInsertLibraryItems:e,pendingElements:o,id:a,libraryReturnUrl:n,theme:l,onSelectItems:c,selectedItems:s}),u&&Mr(Mc,{className:"library-menu-control-buttons--at-bottom",style:{padding:"16px 12px 0 12px"},id:a,libraryReturnUrl:n,theme:l})]})}),Eu=(e,o)=>({elements:e,pending:$(e,{selectedElementIds:o},{includeBoundTextElement:!0,includeElementsInFrames:!0}),selectedElementIds:o}),ek=(e,o)=>{let t=kr(),[r,n]=uw(()=>Eu(t,e.selectedElementIds)),i=qS(new Map);return dw(()=>{for(let a of r.pending)i.current.set(a.id,a.version)},[r.pending]),dw(()=>{o.state.cursorButton==="up"&&o.state.activeTool.type==="selection"&&n(a=>{if(!rt(a.selectedElementIds,e.selectedElementIds))return i.current.clear(),Eu(t,e.selectedElementIds);let l=o.scene.getNonDeletedElementsMap();for(let s of Object.keys(e.selectedElementIds)){let c=l.get(s)?.version;if(c&&c!==i.current.get(s))return Eu(t,e.selectedElementIds)}return a})},[o,o.state.cursorButton,o.state.activeTool.type,e.selectedElementIds,t]),r.pending},fw=hw(()=>{let e=He(),{onInsertElements:o}=e,t=Do(),r=ve(),n=pe(),[i,a]=uw([]),l=gw(()=>e.library,[e.library]),s=ek(r,e),c=yu(d=>{o(_c(d))},[o]),m=yu(()=>{n({selectedElementIds:{},selectedGroupIds:{},activeEmbeddable:null})},[n]);return Mr(QS,{pendingElements:s,onInsertLibraryItems:c,onAddToLibrary:m,setAppState:n,libraryReturnUrl:t.libraryReturnUrl,library:l,id:e.id,theme:r.theme,selectedItems:i,onSelectItems:a})});import{jsx as Wa,jsxs as ik}from"react/jsx-runtime";function nk(e){if(e&&typeof e=="number")return e;switch(e){case"small":return 550;case"wide":return 1024;case"regular":default:return 800}}var Ge=e=>{let[o,t]=Iv(),[r]=rk(document.activeElement),{id:n}=Ke(),i=me().viewport.isMobile;ok(()=>{if(!o)return;let c=ca(o);setTimeout(()=>{c.length>0&&e.autofocus!==!1&&(c[1]||c[0]).focus()});let m=d=>{if(d.key===y.TAB){let p=ca(o),{activeElement:u}=document,h=p.findIndex(f=>f===u);h===0&&d.shiftKey?(p[p.length-1].focus(),d.preventDefault()):h===p.length-1&&!d.shiftKey&&(p[0].focus(),d.preventDefault())}};return o.addEventListener("keydown",m),()=>o.removeEventListener("keydown",m)},[o,e.autofocus]);let a=pe(),l=Tr(Ni),s=()=>{a({openMenu:null}),l(!1),r.focus(),e.onCloseRequest()};return Wa(kv,{className:tk("Dialog",e.className,{"Dialog--fullscreen":i}),labelledBy:"dialog-title",maxWidth:nk(e.size),onCloseRequest:s,closeOnClickOutside:e.closeOnClickOutside,children:ik(Qe,{ref:t,children:[e.title&&Wa("h2",{id:`${n}-dialog-title`,className:"Dialog__title",children:Wa("span",{className:"Dialog__titleContent",children:e.title})}),i&&Wa("button",{className:"Dialog__close",onClick:s,title:g("buttons.close"),"aria-label":g("buttons.close"),type:"button",children:jt}),Wa("div",{className:"Dialog__content",children:e.children})]})})};import{forwardRef as sk,useRef as ck,useImperativeHandle as dk,useLayoutEffect as mk,useState as pk}from"react";import vu from"clsx";import ak from"clsx";import{jsx as lk}from"react/jsx-runtime";var Gt=({type:e="button",onSelect:o,selected:t,children:r,className:n="",...i})=>lk("button",{onClick:ur(i.onClick,a=>{o()}),type:e,className:ak("excalidraw-button",n,{selected:t}),...i,children:r});import{jsx as wu,jsxs as bw}from"react/jsx-runtime";var zi=sk(({onChange:e,label:o,fullWidth:t,placeholder:r,readonly:n,selectOnRender:i,onKeyDown:a,isRedacted:l=!1,icon:s,className:c,...m},d)=>{let p=ck(null);dk(d,()=>p.current),mk(()=>{i&&(p.current?.focus(),p.current?.select())},[i]);let[u,h]=pk(!1);return bw("div",{className:vu("ExcTextField",c,{"ExcTextField--fullWidth":t,"ExcTextField--hasIcon":!!s}),onClick:()=>{p.current?.focus()},children:[s,o&&wu("div",{className:"ExcTextField__label",children:o}),bw("div",{className:vu("ExcTextField__input",{"ExcTextField__input--readonly":n}),children:[wu("input",{className:vu({"is-redacted":"value"in m&&m.value&&l&&!u}),readOnly:n,value:"value"in m?m.value:void 0,defaultValue:"defaultValue"in m?m.defaultValue:void 0,placeholder:r,ref:p,onChange:f=>e?.(f.target.value),onKeyDown:a}),l&&wu(Gt,{onSelect:()=>h(!u),style:{border:0,userSelect:"none"},children:u?Db:oi})]})]})});import Sw from"clsx";var uk={toggleTheme:[A("Shift+Alt+D")],saveScene:[A("CtrlOrCmd+S")],loadScene:[A("CtrlOrCmd+O")],clearCanvas:[A("CtrlOrCmd+Delete")],imageExport:[A("CtrlOrCmd+Shift+E")],commandPalette:[A("CtrlOrCmd+/"),A("CtrlOrCmd+Shift+P")],cut:[A("CtrlOrCmd+X")],copy:[A("CtrlOrCmd+C")],paste:[A("CtrlOrCmd+V")],copyStyles:[A("CtrlOrCmd+Alt+C")],pasteStyles:[A("CtrlOrCmd+Alt+V")],selectAll:[A("CtrlOrCmd+A")],deleteSelectedElements:[A("Delete")],duplicateSelection:[A("CtrlOrCmd+D"),A(`Alt+${g("helpDialog.drag")}`)],sendBackward:[A("CtrlOrCmd+[")],bringForward:[A("CtrlOrCmd+]")],sendToBack:[so?A("CtrlOrCmd+Alt+["):A("CtrlOrCmd+Shift+[")],bringToFront:[so?A("CtrlOrCmd+Alt+]"):A("CtrlOrCmd+Shift+]")],copyAsPng:[A("Shift+Alt+C")],group:[A("CtrlOrCmd+G")],ungroup:[A("CtrlOrCmd+Shift+G")],gridMode:[A("CtrlOrCmd+'")],zenMode:[A("Alt+Z")],objectsSnapMode:[A("Alt+S")],stats:[A("Alt+/")],addToLibrary:[],flipHorizontal:[A("Shift+H")],flipVertical:[A("Shift+V")],viewMode:[A("Alt+R")],hyperlink:[A("CtrlOrCmd+K")],toggleElementLock:[A("CtrlOrCmd+Shift+L")],resetZoom:[A("CtrlOrCmd+0")],zoomOut:[A("CtrlOrCmd+-")],zoomIn:[A("CtrlOrCmd++")],zoomToFitSelection:[A("Shift+3")],zoomToFit:[A("Shift+1")],zoomToFitSelectionInViewport:[A("Shift+2")],toggleEraserTool:[A("E")],toggleHandTool:[A("H")],setFrameAsActiveTool:[A("F")],saveFileToDisk:[A("CtrlOrCmd+S")],saveToActiveFile:[A("CtrlOrCmd+S")],toggleShortcuts:[A("?")],searchMenu:[A("CtrlOrCmd+F")],wrapSelectionInFrame:[]},Ye=(e,o=0)=>{let t=uk[e];return t&&t.length>0?t[o]||t[0]:""};import _k from"fuzzy";var gk="\\u0300-\\u036f",hk="\\ufe20-\\ufe2f",fk="\\u20d0-\\u20ff",bk=gk+hk+fk,xk=`[${bk}]`,Ek=RegExp(xk,"g"),yk=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,vk={\u00C0:"A",\u00C1:"A",\u00C2:"A",\u00C3:"A",\u00C4:"A",\u00C5:"A",\u00E0:"a",\u00E1:"a",\u00E2:"a",\u00E3:"a",\u00E4:"a",\u00E5:"a",\u00C7:"C",\u00E7:"c",\u00D0:"D",\u00F0:"d",\u00C8:"E",\u00C9:"E",\u00CA:"E",\u00CB:"E",\u00E8:"e",\u00E9:"e",\u00EA:"e",\u00EB:"e",\u00CC:"I",\u00CD:"I",\u00CE:"I",\u00CF:"I",\u00EC:"i",\u00ED:"i",\u00EE:"i",\u00EF:"i",\u00D1:"N",\u00F1:"n",\u00D2:"O",\u00D3:"O",\u00D4:"O",\u00D5:"O",\u00D6:"O",\u00D8:"O",\u00F2:"o",\u00F3:"o",\u00F4:"o",\u00F5:"o",\u00F6:"o",\u00F8:"o",\u00D9:"U",\u00DA:"U",\u00DB:"U",\u00DC:"U",\u00F9:"u",\u00FA:"u",\u00FB:"u",\u00FC:"u",\u00DD:"Y",\u00FD:"y",\u00FF:"y",\u00C6:"E",\u00E6:"e",\u00DE:"T",\u00FE:"t",\u00DF:"s",\u0100:"A",\u0102:"A",\u0104:"A",\u0101:"a",\u0103:"a",\u0105:"a",\u0106:"C",\u0108:"C",\u010A:"C",\u010C:"C",\u0107:"c",\u0109:"c",\u010B:"c",\u010D:"c",\u010E:"D",\u0110:"D",\u010F:"d",\u0111:"d",\u0112:"E",\u0114:"E",\u0116:"E",\u0118:"E",\u011A:"E",\u0113:"e",\u0115:"e",\u0117:"e",\u0119:"e",\u011B:"e",\u011C:"G",\u011E:"G",\u0120:"G",\u0122:"G",\u011D:"g",\u011F:"g",\u0121:"g",\u0123:"g",\u0124:"H",\u0126:"H",\u0125:"h",\u0127:"h",\u0128:"I",\u012A:"I",\u012C:"I",\u012E:"I",\u0130:"I",\u0129:"i",\u012B:"i",\u012D:"i",\u012F:"i",\u0131:"i",\u0134:"J",\u0135:"j",\u0136:"K",\u0137:"k",\u0138:"k",\u0139:"L",\u013B:"L",\u013D:"L",\u013F:"L",\u0141:"L",\u013A:"l",\u013C:"l",\u013E:"l",\u0140:"l",\u0142:"l",\u0143:"N",\u0145:"N",\u0147:"N",\u014A:"N",\u0144:"n",\u0146:"n",\u0148:"n",\u014B:"n",\u014C:"O",\u014E:"O",\u0150:"O",\u014D:"o",\u014F:"o",\u0151:"o",\u0154:"R",\u0156:"R",\u0158:"R",\u0155:"r",\u0157:"r",\u0159:"r",\u015A:"S",\u015C:"S",\u015E:"S",\u0160:"S",\u015B:"s",\u015D:"s",\u015F:"s",\u0161:"s",\u0162:"T",\u0164:"T",\u0166:"T",\u0163:"t",\u0165:"t",\u0167:"t",\u0168:"U",\u016A:"U",\u016C:"U",\u016E:"U",\u0170:"U",\u0172:"U",\u0169:"u",\u016B:"u",\u016D:"u",\u016F:"u",\u0171:"u",\u0173:"u",\u0174:"W",\u0175:"w",\u0176:"Y",\u0177:"y",\u0178:"Y",\u0179:"Z",\u017B:"Z",\u017D:"Z",\u017A:"z",\u017C:"z",\u017E:"z",\u0132:"I",\u0133:"i",\u0152:"E",\u0153:"e",\u0149:"n",\u017F:"s"},Tu=e=>e.replace(yk,o=>vk[o]||o).replace(Ek,"");import{useState as Ck}from"react";import Su from"clsx";import Cu from"react";import Ro from"tunnel-rat";import{createIsolation as wk}from"jotai-scope";var Iu=Cu.createContext(null),_e=()=>Cu.useContext(Iu),Tk=wk(),xw=()=>Cu.useMemo(()=>({MainMenuTunnel:Ro(),WelcomeScreenMenuHintTunnel:Ro(),WelcomeScreenToolbarHintTunnel:Ro(),WelcomeScreenHelpHintTunnel:Ro(),WelcomeScreenCenterTunnel:Ro(),FooterCenterTunnel:Ro(),DefaultSidebarTriggerTunnel:Ro(),DefaultSidebarTabTriggersTunnel:Ro(),OverwriteConfirmDialogTunnel:Ro(),TTDDialogTriggerTunnel:Ro(),tunnelsJotai:Tk}),[]);import{Fragment as nr,jsx as Me,jsxs as et}from"react/jsx-runtime";var ku=(e,o)=>{let t=o[0]?.type||null;for(let r of o)if(r.type!==t){t=null;break}return Ia(e.activeTool.type)&&e.activeTool.type!=="image"&&t!=="image"&&t!=="frame"&&t!=="magicframe"||o.some(r=>Ia(r.type))},Lu=(e,o)=>vr(e.activeTool.type)||o.some(t=>vr(t.type)),Gc=({appState:e,elementsMap:o,renderAction:t,app:r})=>{let n=Vs(o,e),i=!1;n.length===2&&(ut(n[0])||ut(n[1]))&&(i=!0);let a=!!(e.editingTextElement||e.newElement),l=me(),s=document.documentElement.getAttribute("dir")==="rtl",c=vr(e.activeTool.type)&&!Jr(e.currentItemBackgroundColor)||n.some(h=>vr(h.type)&&!Jr(h.backgroundColor)),m=n.length===1||i,d=!e.editingLinearElement&&n.length===1&&he(n[0])&&!ee(n[0]),p=!e.croppingElementId&&n.length===1&&xe(n[0]),u=!i&&_t(e,r);return et("div",{className:"panelColumn",children:[Me("div",{children:ku(e,n)&&t("changeStrokeColor")}),Lu(e,n)&&Me("div",{children:t("changeBackgroundColor")}),c&&t("changeFillStyle"),(Wm(e.activeTool.type)||n.some(h=>Wm(h.type)))&&t("changeStrokeWidth"),(e.activeTool.type==="freedraw"||n.some(h=>h.type==="freedraw"))&&t("changeStrokeShape"),(Km(e.activeTool.type)||n.some(h=>Km(h.type)))&&et(nr,{children:[t("changeStrokeStyle"),t("changeSloppiness")]}),(Xm(e.activeTool.type)||n.some(h=>Xm(h.type)))&&Me(nr,{children:t("changeRoundness")}),($m(e.activeTool.type)||n.some(h=>$m(h.type)))&&Me(nr,{children:t("changeArrowType")}),(e.activeTool.type==="text"||n.some(Y))&&et(nr,{children:[t("changeFontFamily"),t("changeFontSize"),(e.activeTool.type==="text"||CE(n,o))&&t("changeTextAlign")]}),TE(n,o)&&t("changeVerticalAlign"),(pi(e.activeTool.type)||n.some(h=>pi(h.type)))&&Me(nr,{children:t("changeArrowhead")}),t("changeOpacity"),et("fieldset",{children:[Me("legend",{children:g("labels.layers")}),et("div",{className:"buttonList",children:[t("sendToBack"),t("sendBackward"),t("bringForward"),t("bringToFront")]})]}),u&&!i&&et("fieldset",{children:[Me("legend",{children:g("labels.align")}),et("div",{className:"buttonList",children:[s?et(nr,{children:[t("alignRight"),t("alignHorizontallyCentered"),t("alignLeft")]}):et(nr,{children:[t("alignLeft"),t("alignHorizontallyCentered"),t("alignRight")]}),n.length>2&&t("distributeHorizontally"),Me("div",{style:{flexBasis:"100%",height:0}}),et("div",{style:{display:"flex",flexWrap:"wrap",gap:".5rem",marginTop:"-0.5rem"},children:[t("alignTop"),t("alignVerticallyCentered"),t("alignBottom"),n.length>2&&t("distributeVertically")]})]})]}),!a&&n.length>0&&et("fieldset",{children:[Me("legend",{children:g("labels.actions")}),et("div",{className:"buttonList",children:[!l.editor.isMobile&&t("duplicateSelection"),!l.editor.isMobile&&t("deleteSelectedElements"),t("group"),t("ungroup"),m&&t("hyperlink"),p&&t("cropEditor"),d&&t("toggleLinearEditor")]})]})]})},Yc=({activeTool:e,appState:o,app:t,UIOptions:r})=>{let[n,i]=Ck(!1),a=e.type==="frame",l=e.type==="laser",s=e.type==="embeddable",{TTDDialogTriggerTunnel:c}=_e();return et(nr,{children:[ms.map(({value:m,icon:d,key:p,numericKey:u,fillable:h},f)=>{if(r.tools?.[m]===!1)return null;let b=g(`toolBar.${m}`),x=p&&mr(typeof p=="string"?p:p[0]),T=x?`${x} ${g("helpDialog.or")} ${u}`:`${u}`;return Me(X,{className:Su("Shape",{fillable:h}),type:"radio",icon:d,checked:e.type===m,name:"editor-current-shape",title:`${mr(b)} \u2014 ${T}`,keyBindingLabel:u||x,"aria-label":mr(b),"aria-keyshortcuts":T,"data-testid":`toolbar-${m}`,onPointerDown:({pointerType:E})=>{!o.penDetected&&E==="pen"&&t.togglePenMode(!0)},onChange:({pointerType:E})=>{o.activeTool.type!==m&&le("toolbar",m,"ui"),m==="image"?t.setActiveTool({type:m,insertOnCanvasDirectly:E!=="mouse"}):t.setActiveTool({type:m})}},m)}),Me("div",{className:"App-toolbar__divider"}),et(Ce,{open:n,children:[Me(Ce.Trigger,{className:Su("App-toolbar__extra-tools-trigger",{"App-toolbar__extra-tools-trigger--selected":a||s||l&&!t.props.isCollaborating}),onToggle:()=>i(!n),title:g("toolBar.extraTools"),children:_b}),et(Ce.Content,{onClickOutside:()=>i(!1),onSelect:()=>i(!1),className:"App-toolbar__extra-tools-dropdown",children:[Me(Ce.Item,{onSelect:()=>t.setActiveTool({type:"frame"}),icon:jl,shortcut:y.F.toLocaleUpperCase(),"data-testid":"toolbar-frame",selected:a,children:g("toolBar.frame")}),Me(Ce.Item,{onSelect:()=>t.setActiveTool({type:"embeddable"}),icon:Lf,"data-testid":"toolbar-embeddable",selected:s,children:g("toolBar.embeddable")}),Me(Ce.Item,{onSelect:()=>t.setActiveTool({type:"laser"}),icon:ql,"data-testid":"toolbar-laser",selected:l,shortcut:y.K.toLocaleUpperCase(),children:g("toolBar.laser")}),Me("div",{style:{margin:"6px 0",fontSize:14,fontWeight:600},children:"Generate"}),t.props.aiEnabled!==!1&&Me(c.Out,{}),Me(Ce.Item,{onSelect:()=>t.setOpenDialog({name:"ttd",tab:"mermaid"}),icon:Jl,"data-testid":"toolbar-embeddable",children:g("toolBar.mermaidToExcalidraw")}),t.props.aiEnabled!==!1&&t.plugins.diagramToCode&&Me(nr,{children:et(Ce.Item,{onSelect:()=>t.onMagicframeToolSelect(),icon:Ql,"data-testid":"toolbar-magicframe",children:[g("toolBar.magicframe"),Me(Ce.Item.Badge,{children:"AI"})]})})]})]})]})},Ew=({renderAction:e,zoom:o})=>Me(it.Col,{gap:1,className:co.ZOOM_ACTIONS,children:et(it.Row,{align:"center",children:[e("zoomOut"),e("resetZoom"),e("zoomIn")]})}),yw=({renderAction:e,className:o})=>et("div",{className:`undo-redo-buttons ${o}`,children:[Me("div",{className:"undo-button-container",children:Me(Ut,{label:g("buttons.undo"),children:e("undo")})}),Me("div",{className:"redo-button-container",children:et(Ut,{label:g("buttons.redo"),children:[" ",e("redo")]})})]}),vw=({actionManager:e,showExitZenModeBtn:o})=>Me("button",{type:"button",className:Su("disable-zen-mode",{"disable-zen-mode--visible":o}),onClick:()=>e.executeAction(Mi),children:g("buttons.exitZenMode")}),ww=({renderAction:e,className:o})=>Me("div",{className:`finalize-button ${o}`,children:e("finalize",{size:"small"})});import{useRef as Ik}from"react";var Au=e=>{let o=Ik({userFn:e});return o.current.userFn=e,o.current.stableFn||(o.current.stableFn=(...t)=>o.current.userFn(...t)),o.current.stableFn};import{jsx as kk,jsxs as Sk}from"react/jsx-runtime";var In=ye(null),Tw=()=>{let[e,o]=ce(In),t=Xe();return e&&e==="clearCanvas"?kk(Rc,{onConfirm:()=>{t.executeAction(er),o(null)},onCancel:()=>o(null),title:g("clearCanvasDialog.title"),children:Sk("p",{className:"clear-canvas__content",children:[" ",g("alerts.clearReset")]})}):null};var _u={};eh(_u,{toggleTheme:()=>Lk});var Lk={...En,category:"App",label:"Toggle theme",perform:({actionManager:e})=>{e.executeAction(En,"commandPalette")}};var Vc=D({name:"copyElementLink",label:"labels.copyElementLink",icon:ei,trackEvent:{category:"element"},perform:async(e,o,t,r)=>{let n=$(e,o);try{if(window.location){let i=ks(n,o);return i?(await yr(r.props.generateLinkForSelection?r.props.generateLinkForSelection(i.id,i.type):Ss(i.id,i.type)),{appState:{toast:{message:g("toast.elementLinkCopied"),closable:!0}},captureUpdate:L.EVENTUALLY}):{appState:o,elements:e,app:r,captureUpdate:L.EVENTUALLY}}}catch(i){console.error(i)}return{appState:o,elements:e,app:r,captureUpdate:L.EVENTUALLY}},predicate:(e,o)=>Ls($(e,o))}),Cw=D({name:"linkToElement",label:"labels.linkToElement",icon:rs,perform:(e,o,t,r)=>{let n=$(e,o);return n.length!==1||!Ls(n)?{elements:e,appState:o,app:r,captureUpdate:L.EVENTUALLY}:{appState:{...o,openDialog:{name:"elementLinkSelector",sourceElementId:$(e,o)[0].id}},captureUpdate:L.IMMEDIATELY}},predicate:(e,o,t,r)=>{let n=$(e,o);return o.openDialog?.name!=="elementLinkSelector"&&n.length===1&&Ls(n)},trackEvent:!1});import{jsx as Ct,jsxs as No}from"react/jsx-runtime";var Mk=ye(null),ze={app:"App",export:"Export",tools:"Tools",editor:"Editor",elements:"Elements",links:"Links"},Dk=e=>{switch(e){case ze.app:return 1;case ze.export:return 2;case ze.editor:return 3;case ze.tools:return 4;case ze.elements:return 5;case ze.links:return 6;default:return 10}},Xc=({shortcut:e,className:o,children:t})=>{let r=e.replace("++","+$").split("+");return No("div",{className:Sw("shortcut",o),children:[r.map((n,i)=>Ct("div",{className:"shortcut-wrapper",children:Ct("div",{className:"shortcut-key",children:n==="$"?"+":n})},n)),Ct("div",{className:"shortcut-desc",children:t})]})},kw=e=>!e.altKey&&e[y.CTRL_OR_CMD]&&(e.shiftKey&&e.key.toLowerCase()===y.P||e.key===y.SLASH),VK=Object.assign(e=>{let o=ve(),t=pe();return Kc(()=>{let r=n=>{kw(n)&&(n.preventDefault(),n.stopPropagation(),t(i=>{let a=i.openDialog?.name==="commandPalette"?null:{name:"commandPalette"};return a&&le("command_palette","open","shortcut"),{openDialog:a}}))};return window.addEventListener("keydown",r,{capture:!0}),()=>window.removeEventListener("keydown",r,{capture:!0})},[t]),o.openDialog?.name!=="commandPalette"?null:Ct(Pk,{...e})},{defaultItems:_u});function Pk({customCommandPaletteItems:e}){let o=He(),t=ve(),r=pe(),n=Do(),i=Xe(),[a,l]=ce(Mk),[s,c]=Wc(null),m=Ak(null),d=Ir({uiAppState:t,customCommandPaletteItems:e,appProps:n});Kc(()=>{let{uiAppState:I,customCommandPaletteItems:_,appProps:k}=d,R=H=>{let V="";return H.label&&(typeof H.label=="function"?V=g(H.label(o.scene.getNonDeletedElements(),I,o)):V=g(H.label)),V},M=H=>typeof H.icon=="function"?H.icon(I,o.scene.getNonDeletedElements()):H.icon,N=[],G=(H,V,F)=>{let O={label:R(H),icon:M(H),category:V,shortcut:Ye(H.name),keywords:H.keywords,predicate:H.predicate,viewMode:H.viewMode,perform:()=>{i.executeAction(H,"commandPalette")}};return F?F(O,H):O};if(I&&o.scene&&i){let H=[i.actions.group,i.actions.ungroup,i.actions.cut,i.actions.copy,i.actions.deleteSelectedElements,i.actions.wrapSelectionInFrame,i.actions.copyStyles,i.actions.pasteStyles,i.actions.bringToFront,i.actions.bringForward,i.actions.sendBackward,i.actions.sendToBack,i.actions.alignTop,i.actions.alignBottom,i.actions.alignLeft,i.actions.alignRight,i.actions.alignVerticallyCentered,i.actions.alignHorizontallyCentered,i.actions.duplicateSelection,i.actions.flipHorizontal,i.actions.flipVertical,i.actions.zoomToFitSelection,i.actions.zoomToFitSelectionInViewport,i.actions.increaseFontSize,i.actions.decreaseFontSize,i.actions.toggleLinearEditor,i.actions.cropEditor,Ga,Vc,Cw].map(W=>G(W,ze.elements,(ne,ge)=>({...ne,predicate:ge.predicate?ge.predicate:(be,mt,wo,Dn)=>$(be,mt).length>0}))),V=[i.actions.toggleHandTool,i.actions.setFrameAsActiveTool].map(W=>G(W,ze.tools)),F=[i.actions.undo,i.actions.redo,i.actions.zoomIn,i.actions.zoomOut,i.actions.resetZoom,i.actions.zoomToFit,i.actions.zenMode,i.actions.viewMode,i.actions.gridMode,i.actions.objectsSnapMode,i.actions.toggleShortcuts,i.actions.selectAll,i.actions.toggleElementLock,i.actions.unlockAllElements,i.actions.stats].map(W=>G(W,ze.editor)),O=[i.actions.saveToActiveFile,i.actions.saveFileToDisk,i.actions.copyAsPng,i.actions.copyAsSvg].map(W=>G(W,ze.export));N=[...H,...F,{label:R(er),icon:M(er),shortcut:Ye(er.name),category:ze.editor,keywords:["delete","destroy"],viewMode:!1,perform:()=>{st.set(In,"clearCanvas")}},{label:g("buttons.exportImage"),category:ze.export,icon:Wl,shortcut:Ye("imageExport"),keywords:["export","image","png","jpeg","svg","clipboard","picture"],perform:()=>{r({openDialog:{name:"imageExport"}})}},...O];let j=[{label:g("toolBar.library"),category:ze.app,icon:jn,viewMode:!1,perform:()=>{I.openSidebar?r({openSidebar:null}):r({openSidebar:{name:pt.name,tab:pt.defaultTab}})}},{label:g("search.title"),category:ze.app,icon:Ft,viewMode:!0,perform:()=>{i.executeAction(Ka)}},{label:g("labels.changeStroke"),keywords:["color","outline"],category:ze.elements,icon:Vl,viewMode:!1,predicate:(W,ne)=>{let ge=$(W,ne);return ge.length>0&&ku(ne,ge)},perform:()=>{r(W=>({openMenu:W.openMenu==="shape"?null:"shape",openPopup:"elementStroke"}))}},{label:g("labels.changeBackground"),keywords:["color","fill"],icon:Vl,category:ze.elements,viewMode:!1,predicate:(W,ne)=>{let ge=$(W,ne);return ge.length>0&&Lu(ne,ge)},perform:()=>{r(W=>({openMenu:W.openMenu==="shape"?null:"shape",openPopup:"elementBackground"}))}},{label:g("labels.canvasBackground"),keywords:["color"],icon:Vl,category:ze.editor,viewMode:!1,perform:()=>{r(W=>({openMenu:W.openMenu==="canvas"?null:"canvas",openPopup:"canvasBackground"}))}},...ms.reduce((W,ne)=>{let{value:ge,icon:be,key:mt,numericKey:wo}=ne;if(k.UIOptions.tools?.[ge]===!1)return W;let Pn=mt&&mr(typeof mt=="string"?mt:mt[0])||wo,pl={label:g(`toolBar.${ge}`),category:ze.tools,shortcut:Pn,icon:be,keywords:["toolbar"],viewMode:!1,perform:({event:ul})=>{ge==="image"?o.setActiveTool({type:ge,insertOnCanvasDirectly:ul.type==="keydown"}):o.setActiveTool({type:ge})}};return W.push(pl),W},[]),...V,{label:g("toolBar.lock"),category:ze.tools,icon:I.activeTool.locked?Jn:on,shortcut:y.Q.toLocaleUpperCase(),viewMode:!1,perform:()=>{o.toggleLock()}},{label:`${g("labels.textToDiagram")}...`,category:ze.tools,icon:Rb,viewMode:!1,predicate:k.aiEnabled,perform:()=>{r(W=>({...W,openDialog:{name:"ttd",tab:"text-to-diagram"}}))}},{label:`${g("toolBar.mermaidToExcalidraw")}...`,category:ze.tools,icon:Jl,viewMode:!1,predicate:k.aiEnabled,perform:()=>{r(W=>({...W,openDialog:{name:"ttd",tab:"mermaid"}}))}}],oe=[...N,...j,..._||[]].map(W=>({...W,icon:W.icon||es,order:W.order??Dk(W.category),haystack:`${Tu(W.label.toLocaleLowerCase())} ${W.keywords?.join(" ")||""}`}));c(oe),l(oe.find(W=>W.label===a?.label)??null)}},[d,o,i,c,a?.label,l,r]);let[p,u]=Wc(""),[h,f]=Wc(null),[b,x]=Wc({}),T=I=>{r({openDialog:null},I),u("")},E=(I,_)=>{t.openDialog?.name==="commandPalette"&&(_.stopPropagation(),_.preventDefault(),document.body.classList.add("excalidraw-animations-disabled"),T(()=>{I.perform({actionManager:i,event:_}),l(I),requestAnimationFrame(()=>{document.body.classList.remove("excalidraw-animations-disabled")})}))},w=Au(I=>I.viewMode===!1&&t.viewModeEnabled?!1:typeof I.predicate=="function"?I.predicate(o.scene.getNonDeletedElements(),t,n,o):I.predicate===void 0||I.predicate),S=Au(I=>{let _=Co(I.target)||kw(I)||I.key===y.ESCAPE;if(_&&I.key!==y.ARROW_UP&&I.key!==y.ARROW_DOWN&&I.key!==y.ENTER)return;let k=Object.values(b).flat(),R=a&&!p&&w(a);if(I.key===y.ARROW_UP){I.preventDefault();let M=k.findIndex(H=>H.label===h?.label);if(R){if(M===0){f(a);return}if(h===a){let H=k[k.length-1];H&&f(H);return}}let N;M===-1?N=k.length-1:N=M===0?k.length-1:(M-1)%k.length;let G=k[N];G&&f(G);return}if(I.key===y.ARROW_DOWN){I.preventDefault();let M=k.findIndex(H=>H.label===h?.label);if(R){if(!h||M===k.length-1){f(a);return}if(h===a){let H=k[0];H&&f(H);return}}let N=(M+1)%k.length,G=k[N];G&&f(G);return}if(I.key===y.ENTER&&h&&setTimeout(()=>{E(h,I)}),!_){if(I.stopPropagation(),/^[a-zA-Z0-9]$/.test(I.key)){m?.current?.focus();return}I.preventDefault()}});return Kc(()=>(window.addEventListener("keydown",S,{capture:!0}),()=>window.removeEventListener("keydown",S,{capture:!0})),[S]),Kc(()=>{if(!s)return;let I=M=>{let N={};for(let G of M)N[G.category]?N[G.category].push(G):N[G.category]=[G];return N},_=s.filter(w).sort((M,N)=>M.order-N.order),k=!p&&a&&w(a);if(!p){x(I(k?_.filter(M=>M.label!==a?.label):_)),f(k?a:_[0]||null);return}let R=Tu(p.toLocaleLowerCase().replace(/[<>_| -]/g,""));_=_k.filter(R,_,{extract:M=>M.haystack}).sort((M,N)=>N.score-M.score).map(M=>M.original),x(I(_)),f(_[0]??null)},[p,s,w,a]),No(Ge,{onCloseRequest:()=>T(),closeOnClickOutside:!0,title:!1,size:720,autofocus:!0,className:"command-palette-dialog",children:[Ct(zi,{value:p,placeholder:g("commandPalette.search.placeholder"),onChange:I=>{u(I)},selectOnRender:!0,ref:m}),!o.device.viewport.isMobile&&No("div",{className:"shortcuts-wrapper",children:[Ct(Xc,{shortcut:"\u2191\u2193",children:g("commandPalette.shortcuts.select")}),Ct(Xc,{shortcut:"\u21B5",children:g("commandPalette.shortcuts.confirm")}),Ct(Xc,{shortcut:A("Esc"),children:g("commandPalette.shortcuts.close")})]}),No("div",{className:"commands",children:[a&&!p&&No("div",{className:"command-category",children:[No("div",{className:"command-category-title",children:[g("commandPalette.recents"),Ct("div",{className:"icon",style:{marginLeft:"6px"},children:Nb})]}),Ct(Iw,{command:a,isSelected:a.label===h?.label,onClick:I=>E(a,I),disabled:!w(a),onMouseMove:()=>f(a),showShortcut:!o.device.viewport.isMobile,appState:t})]}),Object.keys(b).length>0?Object.keys(b).map((I,_)=>No("div",{className:"command-category",children:[Ct("div",{className:"command-category-title",children:I}),b[I].map(k=>Ct(Iw,{command:k,isSelected:k.label===h?.label,onClick:R=>E(k,R),onMouseMove:()=>f(k),showShortcut:!o.device.viewport.isMobile,appState:t},k.label))]},I)):s?No("div",{className:"no-match",children:[Ct("div",{className:"icon",children:Ft})," ",g("commandPalette.search.noMatch")]}):null]})]})}var Iw=({command:e,isSelected:o,disabled:t,onMouseMove:r,onClick:n,showShortcut:i,appState:a})=>{let l=()=>{};return No("div",{className:Sw("command-item",{"item-selected":o,"item-disabled":t}),ref:s=>{o&&!t&&s?.scrollIntoView?.({block:"nearest"})},onClick:t?l:n,onMouseMove:t?l:r,title:t?g("commandPalette.itemNotAvailable"):"",children:[No("div",{className:"name",children:[e.icon&&Ct(Lr,{icon:typeof e.icon=="function"?e.icon(a):e.icon}),e.label]}),i&&e.shortcut&&Ct(Xc,{shortcut:e.shortcut})]})};import{jsx as Rk}from"react/jsx-runtime";var Mu=D({name:"toggleLinearEditor",category:ze.elements,label:(e,o,t)=>t.scene.getSelectedElements({selectedElementIds:o.selectedElementIds})[0]?.type==="arrow"?"labels.lineEditor.editArrow":"labels.lineEditor.edit",keywords:["line"],trackEvent:{category:"element"},predicate:(e,o,t,r)=>{let n=r.scene.getSelectedElements(o);return!!(!o.editingLinearElement&&n.length===1&&he(n[0])&&!ee(n[0]))},perform(e,o,t,r){let n=r.scene.getSelectedElements({selectedElementIds:o.selectedElementIds,includeBoundTextElement:!0})[0],i=o.editingLinearElement?.elementId===n.id?null:new K(n);return{appState:{...o,editingLinearElement:i},captureUpdate:L.IMMEDIATELY}},PanelComponent:({appState:e,updateData:o,app:t})=>{let r=t.scene.getSelectedElements({selectedElementIds:e.selectedElementIds})[0],n=g(r.type==="arrow"?"labels.lineEditor.editArrow":"labels.lineEditor.edit");return Rk(X,{type:"button",icon:Zb,title:n,"aria-label":n,onClick:()=>o(null)})}});var Ka=D({name:"searchMenu",icon:Ft,keywords:["search","find"],label:"search.title",viewMode:!0,trackEvent:{category:"search_menu",action:"toggle",predicate:e=>e.gridModeEnabled},perform(e,o,t,r){if(o.openSidebar?.name===pt.name&&o.openSidebar.tab===Yo){let n=r.excalidrawContainerValue.container?.querySelector(`.${co.SEARCH_MENU_INPUT_WRAPPER} input`);return n?.matches(":focus")?{appState:{...o,openSidebar:null},captureUpdate:L.EVENTUALLY}:(n?.focus(),n?.select(),!1)}return{appState:{...o,openSidebar:{name:pt.name,tab:Yo},openDialog:null},captureUpdate:L.EVENTUALLY}},checked:e=>e.gridModeEnabled,predicate:(e,o,t)=>t.gridModeEnabled===void 0,keyTest:e=>e[y.CTRL_OR_CMD]&&e.key===y.F});import{jsx as Nk}from"react/jsx-runtime";var Du=D({name:"cropEditor",label:"helpDialog.cropStart",icon:Sm,viewMode:!0,trackEvent:{category:"menu"},keywords:["image","crop"],perform(e,o,t,r){let n=r.scene.getSelectedElements({selectedElementIds:o.selectedElementIds,includeBoundTextElement:!0})[0];return{appState:{...o,isCropping:!1,croppingElementId:n.id},captureUpdate:L.IMMEDIATELY}},predicate:(e,o,t,r)=>{let n=r.scene.getSelectedElements(o);return!!(!o.croppingElementId&&n.length===1&&xe(n[0]))},PanelComponent:({appState:e,updateData:o,app:t})=>{let r=g("helpDialog.cropStart");return Nk(X,{type:"button",icon:Sm,title:r,"aria-label":r,onClick:()=>o(null)})}});var Sn=class{constructor(o=!0,t=!0){this.isUndoStackEmpty=o;this.isRedoStackEmpty=t}},Xa=class e{constructor(){C(this,"onHistoryChangedEmitter",new qt);C(this,"undoStack",[]);C(this,"redoStack",[])}get isUndoStackEmpty(){return this.undoStack.length===0}get isRedoStackEmpty(){return this.redoStack.length===0}clear(){this.undoStack.length=0,this.redoStack.length=0}record(o,t){let r=Pu.create(t,o);r.isEmpty()||(this.undoStack.push(r.inverse()),r.elementsChange.isEmpty()||(this.redoStack.length=0),this.onHistoryChangedEmitter.trigger(new Sn(this.isUndoStackEmpty,this.isRedoStackEmpty)))}undo(o,t,r){return this.perform(o,t,r,()=>e.pop(this.undoStack),n=>e.push(this.redoStack,n,o))}redo(o,t,r){return this.perform(o,t,r,()=>e.pop(this.redoStack),n=>e.push(this.undoStack,n,o))}perform(o,t,r,n,i){try{let a=n();if(a===null)return;let l=o,s=t,c=!1;for(;a;){try{[l,s,c]=a.applyTo(l,s,r)}finally{i(a)}if(c)break;a=n()}return[l,s]}finally{this.onHistoryChangedEmitter.trigger(new Sn(this.isUndoStackEmpty,this.isRedoStackEmpty))}}static pop(o){if(!o.length)return null;let t=o.pop();return t!==void 0?t:null}static push(o,t,r){let n=t.inverse().applyLatestChanges(r);return o.push(n)}},Pu=class e{constructor(o,t){this.appStateChange=o;this.elementsChange=t}static create(o,t){return new e(o,t)}inverse(){return new e(this.appStateChange.inverse(),this.elementsChange.inverse())}applyTo(o,t,r){let[n,i]=this.elementsChange.applyTo(o,r.elements),[a,l]=this.appStateChange.applyTo(t,n);return[n,a,i||l]}applyLatestChanges(o){let t=this.elementsChange.applyLatestChanges(o);return e.create(this.appStateChange,t)}isEmpty(){return this.appStateChange.isEmpty()&&this.elementsChange.isEmpty()}};import{useEffect as Ok,useState as Fk}from"react";var Ru=(e,o)=>{let[t,r]=Fk(o);return Ok(()=>{let n=e.on(i=>{r(i)});return()=>{n()}},[e]),t};import{jsx as Mw}from"react/jsx-runtime";var Lw=(e,o,t)=>{if(!o.multiElement&&!o.resizingElement&&!o.editingTextElement&&!o.newElement&&!o.selectedElementsAreBeingDragged&&!o.selectionElement&&!e.flowChartCreator.isCreatingChart){let r=t();if(!r)return{captureUpdate:L.EVENTUALLY};let[n,i]=r,a=Array.from(n.values());return{appState:i,elements:a,captureUpdate:L.NEVER}}return{captureUpdate:L.EVENTUALLY}},Aw=(e,o)=>({name:"undo",label:"buttons.undo",icon:cm,trackEvent:{category:"history"},viewMode:!1,perform:(t,r,n,i)=>Lw(i,r,()=>e.undo(Q(t),r,o.snapshot)),keyTest:t=>t[y.CTRL_OR_CMD]&&ss(t,y.Z)&&!t.shiftKey,PanelComponent:({updateData:t,data:r})=>{let{isUndoStackEmpty:n}=Ru(e.onHistoryChangedEmitter,new Sn(e.isUndoStackEmpty,e.isRedoStackEmpty));return Mw(X,{type:"button",icon:cm,"aria-label":g("buttons.undo"),onClick:t,size:r?.size||"medium",disabled:n,"data-testid":"button-undo"})}}),_w=(e,o)=>({name:"redo",label:"buttons.redo",icon:dm,trackEvent:{category:"history"},viewMode:!1,perform:(t,r,n,i)=>Lw(i,r,()=>e.redo(Q(t),r,o.snapshot)),keyTest:t=>t[y.CTRL_OR_CMD]&&t.shiftKey&&ss(t,y.Z)||xl&&t.ctrlKey&&!t.shiftKey&&ss(t,y.Y),PanelComponent:({updateData:t,data:r})=>{let{isRedoStackEmpty:n}=Ru(e.onHistoryChangedEmitter,new Sn(e.isUndoStackEmpty,e.isRedoStackEmpty));return Mw(X,{type:"button",icon:dm,"aria-label":g("buttons.redo"),onClick:t,size:r?.size||"medium",disabled:n,"data-testid":"button-redo"})}});import{jsx as Bk}from"react/jsx-runtime";var Nu=(e,o,t,r,n,i)=>{if(e.trackEvent)try{typeof e.trackEvent=="object"&&(!e.trackEvent.predicate||e.trackEvent.predicate(t,r,i))&&le(e.trackEvent.category,e.trackEvent.action||e.name,`${o} (${n.device.editor.isMobile?"mobile":"desktop"})`)}catch(a){console.error("error while logging action:",a)}},$c=class{constructor(o,t,r,n){C(this,"actions",{});C(this,"updater");C(this,"getAppState");C(this,"getElementsIncludingDeleted");C(this,"app");C(this,"renderAction",(o,t)=>{let r=this.app.props.UIOptions.canvasActions;if(this.actions[o]&&"PanelComponent"in this.actions[o]&&(!(o in r)||r[o])){let n=this.actions[o],i=n.PanelComponent;i.displayName="PanelComponent";let a=this.getElementsIncludingDeleted(),l=this.getAppState(),s=c=>{Nu(n,"ui",l,a,this.app,c),this.updater(n.perform(this.getElementsIncludingDeleted(),this.getAppState(),c,this.app))};return Bk(i,{elements:this.getElementsIncludingDeleted(),appState:this.getAppState(),updateData:s,appProps:this.app.props,app:this.app,data:t})}return null});C(this,"isActionEnabled",o=>{let t=this.getElementsIncludingDeleted(),r=this.getAppState();return!o.predicate||o.predicate(t,r,this.app.props,this.app)});this.updater=i=>{if(Gn(i))i.then(a=>o(a));else return o(i)},this.getAppState=t,this.getElementsIncludingDeleted=r,this.app=n}registerAction(o){this.actions[o.name]=o}registerAll(o){o.forEach(t=>this.registerAction(t))}handleKeyDown(o){let t=this.app.props.UIOptions.canvasActions,r=Object.values(this.actions).sort((s,c)=>(c.keyPriority||0)-(s.keyPriority||0)).filter(s=>(s.name in t?t[s.name]:!0)&&s.keyTest&&s.keyTest(o,this.getAppState(),this.getElementsIncludingDeleted(),this.app));if(r.length!==1)return r.length>1&&console.warn("Canceling as multiple actions match this shortcut",r),!1;let n=r[0];if(this.getAppState().viewModeEnabled&&n.viewMode!==!0)return!1;let i=this.getElementsIncludingDeleted(),a=this.getAppState(),l=null;return Nu(n,"keyboard",a,i,this.app,null),o.preventDefault(),o.stopPropagation(),this.updater(r[0].perform(i,a,l,this.app)),!0}executeAction(o,t="api",r=null){let n=this.getElementsIncludingDeleted(),i=this.getAppState();Nu(o,t,i,n,this.app,r),this.updater(o.perform(n,i,r,this.app))}};var Ou=e=>{let o=Array.from(e.values());return{x:Dw(o,t=>t.x)/o.length,y:Dw(o,t=>t.y)/o.length}},Fu=([e,o])=>Math.hypot(e.x-o.x,e.y-o.y),Dw=(e,o)=>e.reduce((t,r)=>t+o(r),0);import Gk from"clsx";import{useLayoutEffect as zk,useRef as Pw,useEffect as Rw}from"react";import{unstable_batchedUpdates as Uk}from"react-dom";import{jsx as Hk}from"react/jsx-runtime";var Nw=({children:e,left:o,top:t,onCloseRequest:r,fitInViewport:n=!1,offsetLeft:i=0,offsetTop:a=0,viewportWidth:l=window.innerWidth,viewportHeight:s=window.innerHeight})=>{let c=Pw(null);Rw(()=>{let d=c.current;if(!d)return;d.contains(document.activeElement)||d.focus();let p=u=>{if(u.key===y.TAB){let h=ca(d),{activeElement:f}=document,b=h.findIndex(x=>x===f);f===d?(u.shiftKey?h[h.length-1]?.focus():h[0].focus(),u.preventDefault(),u.stopImmediatePropagation()):b===0&&u.shiftKey?(h[h.length-1]?.focus(),u.preventDefault(),u.stopImmediatePropagation()):b===h.length-1&&!u.shiftKey&&(h[0]?.focus(),u.preventDefault(),u.stopImmediatePropagation())}};return d.addEventListener("keydown",p),()=>d.removeEventListener("keydown",p)},[]);let m=Pw(null);return zk(()=>{if(n&&c.current&&t!=null&&o!=null){let d=c.current,{width:p,height:u}=d.getBoundingClientRect();if(m.current?.top===t&&m.current?.left===o)return;m.current={top:t,left:o},p>=l?(d.style.width=`${l}px`,d.style.left="0px",d.style.overflowX="scroll"):o+p-i>l?d.style.left=`${l-p-10}px`:d.style.left=`${o}px`,u>=s?(d.style.height=`${s-20}px`,d.style.top="10px",d.style.overflowY="scroll"):t+u-a>s?d.style.top=`${s-u}px`:d.style.top=`${t}px`}},[t,o,n,l,s,i,a]),Rw(()=>{if(r){let d=p=>{c.current?.contains(p.target)||Uk(()=>r(p))};return document.addEventListener("pointerdown",d,!1),()=>document.removeEventListener("pointerdown",d,!1)}},[r]),Hk("div",{className:"popover",ref:c,tabIndex:-1,children:e})};import Yk from"react";import{jsx as Ui,jsxs as Vk}from"react/jsx-runtime";var $e="separator",Ow=Yk.memo(({actionManager:e,items:o,top:t,left:r,onClose:n})=>{let i=Mo(),a=kr(),l=o.reduce((s,c)=>(c&&(c===$e||!c.predicate||c.predicate(a,i,e.app.props,e.app))&&s.push(c),s),[]);return Ui(Nw,{onCloseRequest:()=>{n()},top:t,left:r,fitInViewport:!0,offsetLeft:i.offsetLeft,offsetTop:i.offsetTop,viewportWidth:i.width,viewportHeight:i.height,children:Ui("ul",{className:"context-menu",onContextMenu:s=>s.preventDefault(),children:l.map((s,c)=>{if(s===$e)return!l[c-1]||l[c-1]===$e?null:Ui("hr",{className:"context-menu-item-separator"},c);let m=s.name,d="";return s.label&&(typeof s.label=="function"?d=g(s.label(a,i,e.app)):d=g(s.label)),Ui("li",{"data-testid":m,onClick:()=>{n(()=>{e.executeAction(s,"contextMenu")})},children:Vk("button",{type:"button",className:Gk("context-menu-item",{dangerous:m==="deleteSelectedElements",checkmark:s.checked?.(i)}),children:[Ui("div",{className:"context-menu-item__label",children:d}),Ui("kbd",{className:"context-menu-item__shortcut",children:m?Ye(m):""})]})},c)})})})});import rl from"clsx";import j_ from"react";import Wk,{useState as Kk}from"react";import{Fragment as Xk,jsx as Bu}from"react/jsx-runtime";var Fw=({children:e,onClose:o})=>{let[t,r]=Kk(!!e),{container:n}=Ke(),i=Wk.useCallback(()=>{r(!1),o&&o(),n?.focus()},[o,n]);return Bu(Xk,{children:t&&Bu(Ge,{size:"small",onCloseRequest:i,title:g("errorDialog.title"),children:Bu("div",{style:{whiteSpace:"pre-wrap"},children:e})})})};import{useEffect as Hw,useRef as iL,useState as Pr}from"react";import $k from"clsx";import{jsx as Bw,jsxs as Zk}from"react/jsx-runtime";var Zc=function({onChange:e,value:o,choices:t,name:r}){return Bw("div",{className:"RadioGroup",children:t.map(n=>Zk("div",{className:$k("RadioGroup__choice",{active:n.value===o}),title:n.ariaLabel,children:[Bw("input",{name:r,type:"radio",checked:n.value===o,onChange:()=>e(n.value),"aria-label":n.ariaLabel}),n.label]},String(n.value)))})};import jk from"clsx";import{jsx as zw}from"react/jsx-runtime";var $a=({title:e,name:o,checked:t,onChange:r,disabled:n=!1})=>zw("div",{className:jk("Switch",{toggled:t,disabled:n}),children:zw("input",{name:o,id:o,title:e,type:"checkbox",checked:t,disabled:n,onChange:()=>r(!t),onKeyDown:i=>{i.key===" "&&r(!t)}})});import{forwardRef as Jk,useState as qk}from"react";import Qk from"clsx";import{jsx as jc,jsxs as eL}from"react/jsx-runtime";var Dr=Jk(({children:e,icon:o,onClick:t,label:r,variant:n="filled",color:i="primary",size:a="medium",fullWidth:l,className:s,status:c},m)=>{let[d,p]=qk(!1),u=async f=>{let b=t?.(f);if(Gn(b)){let x=window.setTimeout(()=>{p(!0)},50);try{await b}catch(T){if(T instanceof Xn)console.warn(T);else throw T}finally{clearTimeout(x),p(!1)}}},h=d?"loading":c;return i=h==="success"?"success":i,jc("button",{className:Qk("ExcButton",`ExcButton--color-${i}`,`ExcButton--variant-${n}`,`ExcButton--size-${a}`,`ExcButton--status-${h}`,{"ExcButton--fullWidth":l},s),onClick:u,type:"button","aria-label":r,ref:m,disabled:h==="loading"||h==="success",children:eL("div",{className:"ExcButton__contents",children:[h==="loading"?jc(ct,{className:"ExcButton__statusIcon"}):h==="success"&&jc("div",{className:"ExcButton__statusIcon",children:kb}),o&&jc("div",{className:"ExcButton__icon","aria-hidden":!0,children:o}),n!=="icon"&&(e??r)]})})});import{useCallback as tL,useRef as oL,useState as rL}from"react";var nL=2e3,Uw=()=>{let[e,o]=rL(null),t=oL(0),r=()=>{clearTimeout(t.current),o("success"),t.current=window.setTimeout(()=>{o(null)},nL)},n=tL(()=>{o(null)},[]);return{copyStatus:e,resetCopyStatus:n,onCopy:r}};import{jsx as De,jsxs as Rr}from"react/jsx-runtime";var aL="filter"in document.createElement("canvas").getContext("2d"),lL=()=>Rr("div",{children:[De("h3",{children:g("canvasError.cannotShowPreview")}),De("p",{children:De("span",{children:g("canvasError.canvasTooBig")})}),Rr("em",{children:["(",g("canvasError.canvasTooBigTip"),")"]})]}),sL=({appStateSnapshot:e,elementsSnapshot:o,files:t,actionManager:r,onExportImage:n,name:i})=>{let a=ke(o,e),[l,s]=Pr(i),[c,m]=Pr(a),[d,p]=Pr(e.exportBackground),[u,h]=Pr(e.exportWithDarkMode),[f,b]=Pr(e.exportEmbedScene),[x,T]=Pr(e.exportScale),E=iL(null),[w,S]=Pr(null),{onCopy:I,copyStatus:_,resetCopyStatus:k}=Uw();Hw(()=>{k()},[l,d,u,x,f,k]);let{exportedElements:R,exportingFrame:M}=yn(o,e,c);return Hw(()=>{let N=E.current;if(!N)return;let G=N.offsetWidth,H=N.offsetHeight;G&&un({elements:R,appState:{...e,name:l,exportBackground:d,exportWithDarkMode:u,exportScale:x,exportEmbedScene:f},files:t,exportPadding:sr,maxWidthOrHeight:Math.max(G,H),exportingFrame:M}).then(V=>(S(null),jo(V).then(()=>{N.replaceChildren(V)}).catch(F=>{throw F.name==="CANVAS_POSSIBLY_TOO_BIG"?new Error(g("canvasError.canvasTooBig")):F}))).catch(V=>{console.error(V),S(V)})},[e,t,R,M,l,d,u,x,f]),Rr("div",{className:"ImageExportModal",children:[De("h3",{children:g("imageExportDialog.header")}),Rr("div",{className:"ImageExportModal__preview",children:[De("div",{className:"ImageExportModal__preview__canvas",ref:E,children:w&&De(lL,{})}),De("div",{className:"ImageExportModal__preview__filename",children:!si&&De("input",{type:"text",className:"TextInput",value:l,style:{width:"30ch"},onChange:N=>{s(N.target.value),r.executeAction(Yp,"ui",N.target.value)}})})]}),Rr("div",{className:"ImageExportModal__settings",children:[De("h3",{children:g("imageExportDialog.header")}),a&&De(Za,{label:g("imageExportDialog.label.onlySelected"),name:"exportOnlySelected",children:De($a,{name:"exportOnlySelected",checked:c,onChange:N=>{m(N)}})}),De(Za,{label:g("imageExportDialog.label.withBackground"),name:"exportBackgroundSwitch",children:De($a,{name:"exportBackgroundSwitch",checked:d,onChange:N=>{p(N),r.executeAction(Vp,"ui",N)}})}),aL&&De(Za,{label:g("imageExportDialog.label.darkMode"),name:"exportDarkModeSwitch",children:De($a,{name:"exportDarkModeSwitch",checked:u,onChange:N=>{h(N),r.executeAction(rv,"ui",N)}})}),De(Za,{label:g("imageExportDialog.label.embedScene"),tooltip:g("imageExportDialog.tooltip.embedScene"),name:"exportEmbedSwitch",children:De($a,{name:"exportEmbedSwitch",checked:f,onChange:N=>{b(N),r.executeAction(fc,"ui",N)}})}),De(Za,{label:g("imageExportDialog.label.scale"),name:"exportScale",children:De(Zc,{name:"exportScale",value:x,onChange:N=>{T(N),r.executeAction(ov,"ui",N)},choices:Il.map(N=>({value:N,label:`${N}\xD7`}))})}),Rr("div",{className:"ImageExportModal__settings__buttons",children:[De(Dr,{className:"ImageExportModal__settings__buttons__button",label:g("imageExportDialog.title.exportToPng"),onClick:()=>n(wl.png,R,{exportingFrame:M}),icon:Cm,children:g("imageExportDialog.button.exportToPng")}),De(Dr,{className:"ImageExportModal__settings__buttons__button",label:g("imageExportDialog.title.exportToSvg"),onClick:()=>n(wl.svg,R,{exportingFrame:M}),icon:Cm,children:g("imageExportDialog.button.exportToSvg")}),(ci||Go)&&De(Dr,{className:"ImageExportModal__settings__buttons__button",label:g("imageExportDialog.title.copyPngToClipboard"),status:_,onClick:async()=>{await n(wl.clipboard,R,{exportingFrame:M}),I()},icon:ei,children:g("imageExportDialog.button.copyPngToClipboard")})]})]})]})},Za=({label:e,children:o,tooltip:t,name:r})=>Rr("div",{className:"ImageExportModal__settings__setting",title:e,children:[Rr("label",{htmlFor:r,className:"ImageExportModal__settings__setting__label",children:[e,t&&De(Ut,{label:t,long:!0,children:Sb})]}),De("div",{className:"ImageExportModal__settings__setting__content",children:o})]}),Gw=({elements:e,appState:o,files:t,actionManager:r,onExportImage:n,onCloseRequest:i,name:a})=>{let[{appStateSnapshot:l,elementsSnapshot:s}]=Pr(()=>({appStateSnapshot:At(o),elementsSnapshot:At(e)}));return De(Ge,{onCloseRequest:i,size:"wide",title:!1,children:De(sL,{elementsSnapshot:s,appStateSnapshot:l,files:t,actionManager:r,onExportImage:n,name:a})})};import cL from"clsx";import{jsx as dL}from"react/jsx-runtime";var Jc=({children:e,side:o,className:t})=>dL("div",{className:cL("FixedSideContainer",`FixedSideContainer_side_${o}`,t),children:e});var Hi=100,kn=100,Hu=e=>{switch(e){case y.ARROW_UP:return"up";case y.ARROW_DOWN:return"down";case y.ARROW_RIGHT:return"right";case y.ARROW_LEFT:return"left";default:return"right"}},Yw=(e,o,t,r)=>{let n=[...t.values()].reduce((i,a)=>{let l;if(ee(a)&&(l=a[e==="predecessors"?"startBinding":"endBinding"])&&a[e==="predecessors"?"endBinding":"startBinding"]?.elementId===o.id){let s=t.get(l.elementId);if(!s)return i;Io(ma(s),"not an ExcalidrawBindableElement");let c=e==="predecessors"?a.points[a.points.length-1]:[0,0],m=fx(o,mx(o),[c[0]+a.x,c[1]+a.y]);i.push({relative:s,heading:m})}return i},[]);switch(r){case"up":return n.filter(i=>Ea(i.heading,hx)).map(i=>i.relative);case"down":return n.filter(i=>Ea(i.heading,ux)).map(i=>i.relative);case"right":return n.filter(i=>Ea(i.heading,px)).map(i=>i.relative);case"left":return n.filter(i=>Ea(i.heading,gx)).map(i=>i.relative)}},zu=(e,o,t)=>Yw("successors",e,o,t),Uu=(e,o,t)=>Yw("predecessors",e,o,t),mL=(e,o,t)=>{let r=kn+e.width;if(t==="up"||t==="down"){let l=Hi+e.height,s=e.x,c=e.x+e.width;if(o.every(m=>m.x+m.width<s||m.x>c))return{x:0,y:l*(t==="up"?-1:1)}}else if(t==="right"||t==="left"){let l=e.y,s=e.y+e.height;if(o.every(c=>c.y+c.height<l||c.y>s))return{x:(kn+e.width)*(t==="left"?-1:1),y:0}}if(t==="up"||t==="down"){let l=Hi+e.height,s=(o.length===0,l),c=o.length===0?0:(o.length+1)%2===0?(o.length+1)/2*r:o.length/2*r*-1;return t==="up"?{x:c,y:s*-1}:{x:c,y:s}}let n=Hi+e.height,i=(o.length===0,kn+e.width),a=o.length===0?0:(o.length+1)%2===0?(o.length+1)/2*n:o.length/2*n*-1;return t==="left"?{x:i*-1,y:a}:{x:i,y:a}},pL=(e,o,t,r)=>{let n=zu(e,o,r),i=Uu(e,o,r),a=mL(e,[...n,...i],r),l=ho({type:e.type,x:e.x+a.x,y:e.y+a.y,width:e.width,height:e.height,roundness:e.roundness,roughness:e.roughness,backgroundColor:e.backgroundColor,strokeColor:e.strokeColor,strokeWidth:e.strokeWidth,opacity:e.opacity,fillStyle:e.fillStyle,strokeStyle:e.strokeStyle});Io(en(l),"not an ExcalidrawFlowchartNodeElement");let s=Vw(e,l,o,r,t);return{nextNode:l,bindingArrow:s}},uL=(e,o,t,r,n)=>{let i=[];for(let a=0;a<n;a++){let l,s;if(r==="left"||r==="right"){let d=Hi*(n-1)+n*e.height,p=e.y+e.height/2-d/2,u=kn+e.width;r==="left"&&(u*=-1),l=e.x+u;let h=(Hi+e.height)*a;s=p+h}else{let d=kn*(n-1)+n*e.width,p=e.x+e.width/2-d/2,u=Hi+e.height;r==="up"&&(u*=-1),s=e.y+u;let h=(kn+e.width)*a;l=p+h}let c=ho({type:e.type,x:l,y:s,width:e.width,height:e.height,roundness:e.roundness,roughness:e.roughness,backgroundColor:e.backgroundColor,strokeColor:e.strokeColor,strokeWidth:e.strokeWidth,opacity:e.opacity,fillStyle:e.fillStyle,strokeStyle:e.strokeStyle});Io(en(c),"not an ExcalidrawFlowchartNodeElement");let m=Vw(e,c,o,r,t);i.push(c),i.push(m)}return i},Vw=(e,o,t,r,n)=>{let i,a;switch(r){case"up":{i=e.x+e.width/2,a=e.y-6;break}case"down":{i=e.x+e.width/2,a=e.y+e.height+6;break}case"right":{i=e.x+e.width+6,a=e.y+e.height/2;break}case"left":{i=e.x-6,a=e.y+e.height/2;break}}let s,c;switch(r){case"up":{s=o.x+o.width/2-i,c=o.y+o.height-a+6;break}case"down":{s=o.x+o.width/2-i,c=o.y-a-6;break}case"right":{s=o.x-i-6,c=o.y-a+o.height/2;break}case"left":{s=o.x+o.width-i+6,c=o.y-a+o.height/2;break}}let m=ni({type:"arrow",x:i,y:a,startArrowhead:null,endArrowhead:n.currentItemEndArrowhead,strokeColor:e.strokeColor,strokeStyle:e.strokeStyle,strokeWidth:e.strokeWidth,opacity:e.opacity,roughness:e.roughness,points:[z(0,0),z(s,c)],elbowed:!0});Zo(m,e,"start",t),Zo(m,o,"end",t);let d=new Map;d.set(e.id,e),d.set(o.id,o),d.set(m.id,m),K.movePoints(m,[{index:1,point:m.points[1]}]);let p=kx(m,Vn(new Map([...t.entries(),[e.id,e],[o.id,o],[m.id,m]])),{points:m.points});return{...m,...p}},qc=class{constructor(){C(this,"isExploring",!1);C(this,"sameLevelNodes",[]);C(this,"sameLevelIndex",0);C(this,"direction",null);C(this,"visitedNodes",new Set)}clear(){this.isExploring=!1,this.sameLevelNodes=[],this.sameLevelIndex=0,this.direction=null,this.visitedNodes.clear()}exploreByDirection(o,t,r){if(!ma(o))return null;if(r!==this.direction&&this.clear(),this.visitedNodes.has(o.id)||this.visitedNodes.add(o.id),this.isExploring&&r===this.direction&&this.sameLevelNodes.length>1)return this.sameLevelIndex=(this.sameLevelIndex+1)%this.sameLevelNodes.length,this.sameLevelNodes[this.sameLevelIndex].id;let n=[...zu(o,t,r),...Uu(o,t,r)];if(n.length>0)return this.sameLevelIndex=0,this.isExploring=!0,this.sameLevelNodes=n,this.direction=r,this.visitedNodes.add(n[0].id),n[0].id;if(r===this.direction||!this.isExploring){this.isExploring||this.visitedNodes.add(o.id);let a=["up","right","down","left"].filter(l=>l!==r).map(l=>[...zu(o,t,l),...Uu(o,t,l)]).flat().filter(l=>!this.visitedNodes.has(l.id));for(let l of a)if(!this.visitedNodes.has(l.id))return this.visitedNodes.add(l.id),this.isExploring=!0,this.direction=r,l.id}return null}},Qc=class{constructor(){C(this,"isCreatingChart",!1);C(this,"numberOfNodes",0);C(this,"direction","right");C(this,"pendingNodes",null)}createNodes(o,t,r,n){if(n!==this.direction){let{nextNode:i,bindingArrow:a}=pL(o,t,r,n);this.numberOfNodes=1,this.isCreatingChart=!0,this.direction=n,this.pendingNodes=[i,a]}else{this.numberOfNodes+=1;let i=uL(o,t,r,n,this.numberOfNodes);this.isCreatingChart=!0,this.direction=n,this.pendingNodes=i}if(o.frameId){let i=t.get(o.frameId);Io(i&&Zh(i),"not an ExcalidrawFrameElement"),i&&this.pendingNodes.every(a=>eE([a],i,t)||Ds(a,i,t))&&(this.pendingNodes=this.pendingNodes.map(a=>P(a,{frameId:o.frameId},!1)))}}clear(){this.isCreatingChart=!1,this.pendingNodes=null,this.direction=null,this.numberOfNodes=0}},Ww=(e,o)=>{for(let[,t]of o)if(t.type==="arrow"&&(t.startBinding?.elementId===e.id||t.endBinding?.elementId===e.id))return!0;return!1};import{jsx as Kw}from"react/jsx-runtime";var gL=({appState:e,isMobile:o,device:t,app:r})=>{let{activeTool:n,isResizing:i,isRotating:a,lastPointerDownWith:l}=e,s=e.multiElement!==null;if(e.openSidebar?.name===pt.name&&e.openSidebar.tab===Yo&&e.searchMatches?.length)return g("hints.dismissSearch");if(e.openSidebar&&!t.editor.canFitSidebar)return null;if($t(e))return g("hints.eraserRevert");if(n.type==="arrow"||n.type==="line")return s?g("hints.linearElementMulti"):n.type==="arrow"?g("hints.arrowTool",{arrowShortcut:A("A")}):g("hints.linearElement");if(n.type==="freedraw")return g("hints.freeDraw");if(n.type==="text")return g("hints.text");if(n.type==="embeddable")return g("hints.embeddable");if(e.activeTool.type==="image"&&e.pendingImageElementId)return g("hints.placeImage");let c=r.scene.getSelectedElements(e);if(i&&l==="mouse"&&c.length===1){let m=c[0];return he(m)&&m.points.length===2?g("hints.lockAngle"):xe(m)?g("hints.resizeImage"):g("hints.resize")}if(a&&l==="mouse")return g("hints.rotate");if(c.length===1&&Y(c[0]))return g("hints.text_selected");if(e.editingTextElement)return g("hints.text_editing");if(e.croppingElementId)return g("hints.leaveCropEditor");if(c.length===1&&xe(c[0]))return g("hints.enterCropEditor");if(n.type==="selection"){if(e.selectionElement&&!c.length&&!e.editingTextElement&&!e.editingLinearElement)return g("hints.deepBoxSelect");if(gn(r)&&e.selectedElementsAreBeingDragged)return g("hints.disableSnapping");if(!c.length&&!o)return g("hints.canvasPanning");if(c.length===1){if(he(c[0]))return e.editingLinearElement?e.editingLinearElement.selectedPointsIndices?g("hints.lineEditor_pointSelected"):g("hints.lineEditor_nothingSelected"):g("hints.lineEditor_info");if(!e.newElement&&!e.selectedElementsAreBeingDragged&&Ko(c[0]))return en(c[0])?Ww(c[0],r.scene.getNonDeletedElementsMap())?[g("hints.bindTextToElement"),g("hints.createFlowchart")]:[g("hints.bindTextToElement"),g("hints.createFlowchart")]:g("hints.bindTextToElement")}}return null},ed=({appState:e,isMobile:o,device:t,app:r})=>{let n=gL({appState:e,isMobile:o,device:t,app:r});if(!n)return null;let i=Array.isArray(n)?n.map(a=>A(a).replace(/\. ?$/,"")).join(". "):A(n);return Kw("div",{className:"HintViewer",children:Kw("span",{children:i})})};import hL from"clsx";import{jsx as $w,jsxs as bL}from"react/jsx-runtime";var fL="medium",Xw={CHECKED:Jn,UNCHECKED:on},td=e=>bL("label",{className:hL("ToolIcon ToolIcon__lock",`ToolIcon_size_${fL}`,{"is-mobile":e.isMobile}),title:`${e.title} \u2014 Q`,children:[$w("input",{className:"ToolIcon_type_checkbox",type:"checkbox",name:e.name,onChange:e.onChange,checked:e.checked,"aria-label":e.title,"data-testid":"toolbar-lock"}),$w("div",{className:"ToolIcon__icon",children:e.checked?Xw.CHECKED:Xw.UNCHECKED})]});import{Fragment as xL,jsx as Zw,jsxs as EL}from"react/jsx-runtime";var Nr=({heading:e,children:o,...t})=>{let{id:r}=Ke(),n=Zw("h2",{className:"visually-hidden",id:`${r}-${e}-title`,children:g(`headings.${e}`)});return Zw("section",{...t,"aria-labelledby":`${r}-${e}-title`,children:typeof o=="function"?o(n):EL(xL,{children:[n,o]})})};var Eo=4,Oo=6,jw="rgba(0,0,0,0.3)",Jw=(e,o,t,r)=>{if(!e.length)return{horizontal:null,vertical:null};let[n,i,a,l]=Se(e),s=o/r.zoom.value,c=t/r.zoom.value,m=o-s,d=t-c,p={top:parseInt(la("sat"))||0,bottom:parseInt(la("sab"))||0,left:parseInt(la("sal"))||0,right:parseInt(la("sar"))||0},u=qo().rtl,h=-r.scrollX+m/2+p.left,f=-r.scrollY+d/2+p.top,b=h+s-p.right,x=f+c-p.bottom,T=Math.min(n,h),E=Math.min(i,f),w=Math.max(a,b),S=Math.max(l,x);return{horizontal:h===T&&b===w?null:{x:Math.max(p.left,Eo)+(h-T)/(w-T)*o,y:t-Oo-Math.max(Eo,p.bottom),width:(b-h)/(w-T)*o-Math.max(Eo*2,p.left+p.right),height:Oo},vertical:f===E&&x===S?null:{x:u?Math.max(p.left,Eo):o-Oo-Math.max(p.right,Eo),y:(f-E)/(S-E)*t+Math.max(p.top,Eo),width:Oo,height:(x-f)/(S-E)*t-Math.max(Eo*2,p.top+p.bottom)}}},Gu=(e,o,t)=>{let[r,n]=[e.horizontal,e.vertical].map(a=>a!=null&&a.x<=o&&o<=a.x+a.width&&a.y<=t&&t<=a.y+a.height);return{isOverEither:r||n,isOverHorizontal:r,isOverVertical:n}};import yL from"clsx";import{jsx as qw,jsxs as wL}from"react/jsx-runtime";var vL="medium",od=e=>e.penDetected?wL("label",{className:yL("ToolIcon ToolIcon__penMode",`ToolIcon_size_${vL}`,{"is-mobile":e.isMobile}),title:`${e.title}`,children:[qw("input",{className:"ToolIcon_type_checkbox",type:"checkbox",name:e.name,onChange:e.onChange,checked:e.checked,"aria-label":e.title}),qw("div",{className:"ToolIcon__icon",children:If})]}):null;import TL from"clsx";import{jsx as CL}from"react/jsx-runtime";var rd=e=>CL(X,{className:TL("Shape",{fillable:!1}),type:"radio",icon:Zl,name:"editor-current-shape",checked:e.checked,title:`${e.title} \u2014 H`,keyBindingLabel:e.isMobile?void 0:y.H.toLocaleUpperCase(),"aria-label":`${e.title} \u2014 H`,"aria-keyshortcuts":y.H,"data-testid":"toolbar-hand",onChange:()=>e.onChange?.()});import{Fragment as IL,jsx as dt,jsxs as ir}from"react/jsx-runtime";var Qw=({appState:e,elements:o,actionManager:t,setAppState:r,onLockToggle:n,onHandToolToggle:i,onPenModeToggle:a,renderTopRightUI:l,renderCustomStats:s,renderSidebars:c,device:m,renderWelcomeScreen:d,UIOptions:p,app:u})=>{let{WelcomeScreenCenterTunnel:h,MainMenuTunnel:f,DefaultSidebarTriggerTunnel:b}=_e(),x=()=>ir(Jc,{side:"top",className:"App-top-bar",children:[d&&dt(h.Out,{}),dt(Nr,{heading:"shapes",children:E=>dt(it.Col,{gap:4,align:"center",children:ir(it.Row,{gap:1,className:"App-toolbar-container",children:[ir(Qe,{padding:1,className:"App-toolbar App-toolbar--mobile",children:[E,dt(it.Row,{gap:1,children:dt(Yc,{appState:e,activeTool:e.activeTool,UIOptions:p,app:u})})]}),l&&l(!0,e),ir("div",{className:"mobile-misc-tools-container",children:[!e.viewModeEnabled&&e.openDialog?.name!=="elementLinkSelector"&&dt(b.Out,{}),dt(od,{checked:e.penMode,onChange:()=>a(null),title:g("toolBar.penMode"),isMobile:!0,penDetected:e.penDetected}),dt(td,{checked:e.activeTool.locked,onChange:n,title:g("toolBar.lock"),isMobile:!0}),dt(rd,{checked:Xo(e),onChange:()=>i(),title:g("toolBar.hand"),isMobile:!0})]})]})})}),dt(ed,{appState:e,isMobile:!0,device:m,app:u})]}),T=()=>e.viewModeEnabled||e.openDialog?.name==="elementLinkSelector"?dt("div",{className:"App-toolbar-content",children:dt(f.Out,{})}):ir("div",{className:"App-toolbar-content",children:[dt(f.Out,{}),t.renderAction("toggleEditMenu"),t.renderAction(e.multiElement?"finalize":"duplicateSelection"),t.renderAction("deleteSelectedElements"),ir("div",{children:[t.renderAction("undo"),t.renderAction("redo")]})]});return ir(IL,{children:[c(),!e.viewModeEnabled&&e.openDialog?.name!=="elementLinkSelector"&&x(),dt("div",{className:"App-bottom-bar",style:{marginBottom:Oo+Eo*2,marginLeft:Oo+Eo*2,marginRight:Oo+Eo*2},children:ir(Qe,{padding:0,children:[e.openMenu==="shape"&&!e.viewModeEnabled&&e.openDialog?.name!=="elementLinkSelector"&&fi(e,o)?dt(Nr,{className:"App-mobile-menu",heading:"selectedShapeActions",children:dt(Gc,{appState:e,elementsMap:u.scene.getNonDeletedElementsMap(),renderAction:t.renderAction,app:u})}):null,ir("footer",{className:"App-toolbar",children:[T(),e.scrolledOutside&&!e.openMenu&&!e.openSidebar&&dt("button",{type:"button",className:"scroll-back-to-content",onClick:()=>{r(E=>({...fn(o,E)}))},children:g("buttons.scrollBackToContent")})]})]})})]})};import SL from"open-color";import kL,{useLayoutEffect as LL,useRef as AL,useState as _L}from"react";import{jsx as ja,jsxs as ML}from"react/jsx-runtime";var e0=e=>{let o=AL(null),[t,r]=_L(null);return LL(()=>{if(!e.spreadsheet)return;let n=Zx(e.chartType,e.spreadsheet,0,0);r(n);let i,a=o.current;return(async()=>(i=await As(n,{exportBackground:!1,viewBackgroundColor:SL.white},null,{skipInliningFonts:!0}),i.querySelector(".style-fonts")?.remove(),a.replaceChildren(),a.appendChild(i),e.selected&&a.parentNode.focus()))(),()=>{a.replaceChildren()}},[e.spreadsheet,e.chartType,e.selected]),ja("button",{type:"button",className:"ChartPreview",onClick:()=>{t&&e.onClick(e.chartType,t)},children:ja("div",{ref:o})})},t0=({setAppState:e,appState:o,onClose:t})=>{let{onInsertElements:r}=He(),n=kL.useCallback(()=>{t&&t()},[t]),i=(a,l)=>{r(l),le("paste","chart",a),e({currentChartType:a,pasteDialog:{shown:!1,data:null}})};return ja(Ge,{size:"small",onCloseRequest:n,title:g("labels.pasteCharts"),className:"PasteChartDialog",autofocus:!1,children:ML("div",{className:"container",children:[ja(e0,{chartType:"bar",spreadsheet:o.pasteDialog.data,selected:o.currentChartType==="bar",onClick:i}),ja(e0,{chartType:"line",spreadsheet:o.pasteDialog.data,selected:o.currentChartType==="line",onClick:i})]})})};import DL from"react";import{Fragment as r0,jsx as B,jsxs as Yt}from"react/jsx-runtime";var PL=()=>Yt("div",{className:"HelpDialog__header",children:[Yt("a",{className:"HelpDialog__btn",href:"https://docs.excalidraw.com",target:"_blank",rel:"noopener noreferrer",children:[B("div",{className:"HelpDialog__link-icon",children:sm}),g("helpDialog.documentation")]}),Yt("a",{className:"HelpDialog__btn",href:"https://plus.excalidraw.com/blog",target:"_blank",rel:"noopener noreferrer",children:[B("div",{className:"HelpDialog__link-icon",children:sm}),g("helpDialog.blog")]}),Yt("a",{className:"HelpDialog__btn",href:"https://github.com/excalidraw/excalidraw/issues",target:"_blank",rel:"noopener noreferrer",children:[B("div",{className:"HelpDialog__link-icon",children:Yl}),g("helpDialog.github")]}),Yt("a",{className:"HelpDialog__btn",href:"https://youtube.com/@excalidraw",target:"_blank",rel:"noopener noreferrer",children:[B("div",{className:"HelpDialog__link-icon",children:Xb}),"YouTube"]})]}),RL=e=>Yt(r0,{children:[B("h3",{children:e.title}),B("div",{className:"HelpDialog__islands-container",children:e.children})]}),Yu=e=>Yt("div",{className:`HelpDialog__island ${e.className}`,children:[B("h4",{className:"HelpDialog__island-title",children:e.caption}),B("div",{className:"HelpDialog__island-content",children:e.children})]});function*NL(e,o){let t=!0;for(let r of e)t||(yield o),t=!1,yield r}var OL=e=>e.replace(/\b[a-z]\b/,o=>o.toUpperCase()),U=({label:e,shortcuts:o,isOr:t=!0})=>{let r=o.map(n=>(n.endsWith("++")?[...n.slice(0,-2).split("+"),"+"]:n.split("+")).map(a=>B(FL,{children:OL(a)},a)));return Yt("div",{className:"HelpDialog__shortcut",children:[B("div",{children:e}),B("div",{className:"HelpDialog__key-container",children:[...NL(r,t?g("helpDialog.or"):null)]})]})},FL=e=>B("kbd",{className:"HelpDialog__key",...e}),o0=({onClose:e})=>{let o=DL.useCallback(()=>{e&&e()},[e]);return B(r0,{children:Yt(Ge,{onCloseRequest:o,title:g("helpDialog.title"),className:"HelpDialog",children:[B(PL,{}),Yt(RL,{title:g("helpDialog.shortcuts"),children:[Yt(Yu,{className:"HelpDialog__island--tools",caption:g("helpDialog.tools"),children:[B(U,{label:g("toolBar.hand"),shortcuts:[y.H]}),B(U,{label:g("toolBar.selection"),shortcuts:[y.V,y[1]]}),B(U,{label:g("toolBar.rectangle"),shortcuts:[y.R,y[2]]}),B(U,{label:g("toolBar.diamond"),shortcuts:[y.D,y[3]]}),B(U,{label:g("toolBar.ellipse"),shortcuts:[y.O,y[4]]}),B(U,{label:g("toolBar.arrow"),shortcuts:[y.A,y[5]]}),B(U,{label:g("toolBar.line"),shortcuts:[y.L,y[6]]}),B(U,{label:g("toolBar.freedraw"),shortcuts:[y.P,y[7]]}),B(U,{label:g("toolBar.text"),shortcuts:[y.T,y[8]]}),B(U,{label:g("toolBar.image"),shortcuts:[y[9]]}),B(U,{label:g("toolBar.eraser"),shortcuts:[y.E,y[0]]}),B(U,{label:g("toolBar.frame"),shortcuts:[y.F]}),B(U,{label:g("toolBar.laser"),shortcuts:[y.K]}),B(U,{label:g("labels.eyeDropper"),shortcuts:[y.I,"Shift+S","Shift+G"]}),B(U,{label:g("helpDialog.editLineArrowPoints"),shortcuts:[A("CtrlOrCmd+Enter")]}),B(U,{label:g("helpDialog.editText"),shortcuts:[A("Enter")]}),B(U,{label:g("helpDialog.textNewLine"),shortcuts:[A("Enter"),A("Shift+Enter")]}),B(U,{label:g("helpDialog.textFinish"),shortcuts:[A("Esc"),A("CtrlOrCmd+Enter")]}),B(U,{label:g("helpDialog.curvedArrow"),shortcuts:["A",g("helpDialog.click"),g("helpDialog.click"),g("helpDialog.click")],isOr:!1}),B(U,{label:g("helpDialog.curvedLine"),shortcuts:["L",g("helpDialog.click"),g("helpDialog.click"),g("helpDialog.click")],isOr:!1}),B(U,{label:g("helpDialog.cropStart"),shortcuts:[g("helpDialog.doubleClick"),A("Enter")],isOr:!0}),B(U,{label:g("helpDialog.cropFinish"),shortcuts:[A("Enter"),A("Escape")],isOr:!0}),B(U,{label:g("toolBar.lock"),shortcuts:[y.Q]}),B(U,{label:g("helpDialog.preventBinding"),shortcuts:[A("CtrlOrCmd")]}),B(U,{label:g("toolBar.link"),shortcuts:[A("CtrlOrCmd+K")]})]}),Yt(Yu,{className:"HelpDialog__island--view",caption:g("helpDialog.view"),children:[B(U,{label:g("buttons.zoomIn"),shortcuts:[A("CtrlOrCmd++")]}),B(U,{label:g("buttons.zoomOut"),shortcuts:[A("CtrlOrCmd+-")]}),B(U,{label:g("buttons.resetZoom"),shortcuts:[A("CtrlOrCmd+0")]}),B(U,{label:g("helpDialog.zoomToFit"),shortcuts:["Shift+1"]}),B(U,{label:g("helpDialog.zoomToSelection"),shortcuts:["Shift+2"]}),B(U,{label:g("helpDialog.movePageUpDown"),shortcuts:["PgUp/PgDn"]}),B(U,{label:g("helpDialog.movePageLeftRight"),shortcuts:["Shift+PgUp/PgDn"]}),B(U,{label:g("buttons.zenMode"),shortcuts:[A("Alt+Z")]}),B(U,{label:g("buttons.objectsSnapMode"),shortcuts:[A("Alt+S")]}),B(U,{label:g("labels.toggleGrid"),shortcuts:[A("CtrlOrCmd+'")]}),B(U,{label:g("labels.viewMode"),shortcuts:[A("Alt+R")]}),B(U,{label:g("labels.toggleTheme"),shortcuts:[A("Alt+Shift+D")]}),B(U,{label:g("stats.fullTitle"),shortcuts:[A("Alt+/")]}),B(U,{label:g("search.title"),shortcuts:[Ye("searchMenu")]}),B(U,{label:g("commandPalette.title"),shortcuts:Go?[Ye("commandPalette")]:[Ye("commandPalette"),Ye("commandPalette",1)]})]}),Yt(Yu,{className:"HelpDialog__island--editor",caption:g("helpDialog.editor"),children:[B(U,{label:g("helpDialog.createFlowchart"),shortcuts:[A("CtrlOrCmd+Arrow Key")],isOr:!0}),B(U,{label:g("helpDialog.navigateFlowchart"),shortcuts:[A("Alt+Arrow Key")],isOr:!0}),B(U,{label:g("labels.moveCanvas"),shortcuts:[A(`Space+${g("helpDialog.drag")}`),A(`Wheel+${g("helpDialog.drag")}`)],isOr:!0}),B(U,{label:g("buttons.clearReset"),shortcuts:[A("CtrlOrCmd+Delete")]}),B(U,{label:g("labels.delete"),shortcuts:[A("Delete")]}),B(U,{label:g("labels.cut"),shortcuts:[A("CtrlOrCmd+X")]}),B(U,{label:g("labels.copy"),shortcuts:[A("CtrlOrCmd+C")]}),B(U,{label:g("labels.paste"),shortcuts:[A("CtrlOrCmd+V")]}),B(U,{label:g("labels.pasteAsPlaintext"),shortcuts:[A("CtrlOrCmd+Shift+V")]}),B(U,{label:g("labels.selectAll"),shortcuts:[A("CtrlOrCmd+A")]}),B(U,{label:g("labels.multiSelect"),shortcuts:[A(`Shift+${g("helpDialog.click")}`)]}),B(U,{label:g("helpDialog.deepSelect"),shortcuts:[A(`CtrlOrCmd+${g("helpDialog.click")}`)]}),B(U,{label:g("helpDialog.deepBoxSelect"),shortcuts:[A(`CtrlOrCmd+${g("helpDialog.drag")}`)]}),(ci||Go)&&B(U,{label:g("labels.copyAsPng"),shortcuts:[A("Shift+Alt+C")]}),B(U,{label:g("labels.copyStyles"),shortcuts:[A("CtrlOrCmd+Alt+C")]}),B(U,{label:g("labels.pasteStyles"),shortcuts:[A("CtrlOrCmd+Alt+V")]}),B(U,{label:g("labels.sendToBack"),shortcuts:[so?A("CtrlOrCmd+Alt+["):A("CtrlOrCmd+Shift+[")]}),B(U,{label:g("labels.bringToFront"),shortcuts:[so?A("CtrlOrCmd+Alt+]"):A("CtrlOrCmd+Shift+]")]}),B(U,{label:g("labels.sendBackward"),shortcuts:[A("CtrlOrCmd+[")]}),B(U,{label:g("labels.bringForward"),shortcuts:[A("CtrlOrCmd+]")]}),B(U,{label:g("labels.alignTop"),shortcuts:[A("CtrlOrCmd+Shift+Up")]}),B(U,{label:g("labels.alignBottom"),shortcuts:[A("CtrlOrCmd+Shift+Down")]}),B(U,{label:g("labels.alignLeft"),shortcuts:[A("CtrlOrCmd+Shift+Left")]}),B(U,{label:g("labels.alignRight"),shortcuts:[A("CtrlOrCmd+Shift+Right")]}),B(U,{label:g("labels.duplicateSelection"),shortcuts:[A("CtrlOrCmd+D"),A(`Alt+${g("helpDialog.drag")}`)]}),B(U,{label:g("helpDialog.toggleElementLock"),shortcuts:[A("CtrlOrCmd+Shift+L")]}),B(U,{label:g("buttons.undo"),shortcuts:[A("CtrlOrCmd+Z")]}),B(U,{label:g("buttons.redo"),shortcuts:xl?[A("CtrlOrCmd+Y"),A("CtrlOrCmd+Shift+Z")]:[A("CtrlOrCmd+Shift+Z")]}),B(U,{label:g("labels.group"),shortcuts:[A("CtrlOrCmd+G")]}),B(U,{label:g("labels.ungroup"),shortcuts:[A("CtrlOrCmd+Shift+G")]}),B(U,{label:g("labels.flipHorizontal"),shortcuts:[A("Shift+H")]}),B(U,{label:g("labels.flipVertical"),shortcuts:[A("Shift+V")]}),B(U,{label:g("labels.showStroke"),shortcuts:[A("S")]}),B(U,{label:g("labels.showBackground"),shortcuts:[A("G")]}),B(U,{label:g("labels.showFonts"),shortcuts:[A("Shift+F")]}),B(U,{label:g("labels.decreaseFontSize"),shortcuts:[A("CtrlOrCmd+Shift+<")]}),B(U,{label:g("labels.increaseFontSize"),shortcuts:[A("CtrlOrCmd+Shift+>")]})]})]})]})})};import nd,{useLayoutEffect as BL}from"react";import n0 from"clsx";import*as Or from"@radix-ui/react-popover";import{Fragment as YL,jsx as Fo,jsxs as id}from"react/jsx-runtime";var zL=4,UL=8,HL=({shouldWrap:e,children:o,username:t})=>e?Fo(Ut,{label:t||"Unknown user",children:o}):Fo(YL,{children:o}),Vu=({actionManager:e,collaborator:o,socketId:t,withName:r=!1,shouldWrapWithTooltip:n=!1,isBeingFollowed:i})=>{let a={socketId:t,collaborator:o,withName:r,isBeingFollowed:i},l=e.renderAction("goToCollaborator",a);return Fo(HL,{username:o.username,shouldWrap:n,children:l},t)},GL=["avatarUrl","id","socketId","username","isInCall","isSpeaking","isMuted"],ad=nd.memo(({className:e,mobile:o,collaborators:t,userToFollow:r})=>{let n=Xe(),i=new Map;t.forEach((f,b)=>{let x=f.id||b;i.set(x,{...f,socketId:b})});let a=Array.from(i.values()).filter(f=>f.username?.trim()),[l,s]=nd.useState(""),c=a.filter(f=>f.username?.toLowerCase().includes(l)),m=nd.useRef(null);BL(()=>{if(m.current){let f=x=>{let T=Math.max(1,Math.min(8,Math.floor(x/38)));p(T)};if(f(m.current.clientWidth),!Qi)return;let b=new ResizeObserver(x=>{for(let T of x){let{width:E}=T.contentRect;f(E)}});return b.observe(m.current),()=>{b.disconnect()}}},[]);let[d,p]=nd.useState(zL),h=a.slice(0,d-1).map(f=>Vu({actionManager:n,collaborator:f,socketId:f.socketId,shouldWrapWithTooltip:!0,isBeingFollowed:f.socketId===r}));return o?Fo("div",{className:n0("UserList UserList_mobile",e),children:a.map(f=>Vu({actionManager:n,collaborator:f,socketId:f.socketId,shouldWrapWithTooltip:!0,isBeingFollowed:f.socketId===r}))}):Fo("div",{className:"UserList__wrapper",ref:m,children:id("div",{className:n0("UserList",e),style:{"--max-avatars":d},children:[h,a.length>d-1&&id(Or.Root,{children:[id(Or.Trigger,{className:"UserList__more",children:["+",a.length-d+1]}),Fo(Or.Content,{style:{zIndex:2,width:"15rem",textAlign:"left"},align:"end",sideOffset:10,children:id(Qe,{padding:2,children:[a.length>=UL&&Fo(ic,{placeholder:g("quickSearch.placeholder"),onChange:s}),Fo(ac,{className:"dropdown-menu UserList__collaborators",placeholder:g("userList.empty"),children:c.length>0?[Fo("div",{className:"hint",children:g("userList.hint.text")}),c.map(f=>Vu({actionManager:n,collaborator:f,socketId:f.socketId,withName:!0,isBeingFollowed:f.socketId===r}))]:[]}),Fo(Or.Arrow,{width:20,height:10,style:{fill:"var(--popup-bg-color)",filter:"drop-shadow(rgba(0, 0, 0, 0.05) 0px 3px 2px)"}})]})})]})]})})},(e,o)=>{if(e.collaborators.size!==o.collaborators.size||e.mobile!==o.mobile||e.className!==o.className||e.userToFollow!==o.userToFollow)return!1;let t=o.collaborators.keys();for(let[r,n]of e.collaborators){let i=o.collaborators.get(r);if(!i||r!==t.next().value||!rt(n,i,GL))return!1}return!0});import WL from"react";import Wu from"open-color";import{jsx as VL}from"react/jsx-runtime";var Ku=({children:e,color:o})=>VL("div",{className:"Card",style:{"--card-color":o==="primary"?"var(--color-primary)":Wu[o][7],"--card-color-darker":o==="primary"?"var(--color-primary-darker)":Wu[o][8],"--card-color-darkest":o==="primary"?"var(--color-primary-darkest)":Wu[o][9]},children:e});import{Fragment as XL,jsx as yo,jsxs as ld}from"react/jsx-runtime";var KL=({elements:e,appState:o,setAppState:t,files:r,actionManager:n,exportOpts:i,canvas:a,onCloseRequest:l})=>{let{onExportToBackend:s}=i;return yo("div",{className:"ExportDialog ExportDialog--json",children:ld("div",{className:"ExportDialog-cards",children:[i.saveFileToDisk&&ld(Ku,{color:"lime",children:[yo("div",{className:"Card-icon",children:Of}),yo("h2",{children:g("exportDialog.disk_title")}),ld("div",{className:"Card-details",children:[g("exportDialog.disk_details"),!si&&n.renderAction("changeProjectName")]}),yo(X,{className:"Card-button",type:"button",title:g("exportDialog.disk_button"),"aria-label":g("exportDialog.disk_button"),showAriaLabel:!0,onClick:()=>{n.executeAction(Ua,"ui")}})]}),s&&ld(Ku,{color:"pink",children:[yo("div",{className:"Card-icon",children:fa}),yo("h2",{children:g("exportDialog.link_title")}),yo("div",{className:"Card-details",children:g("exportDialog.link_details")}),yo(X,{className:"Card-button",type:"button",title:g("exportDialog.link_button"),"aria-label":g("exportDialog.link_button"),showAriaLabel:!0,onClick:async()=>{try{le("export","link",`ui (${Vh()})`),await s(e,o,r),l()}catch(c){t({errorMessage:c.message})}}})]}),i.renderCustomUI&&i.renderCustomUI(e,o,r,a)]})})},i0=({elements:e,appState:o,files:t,actionManager:r,exportOpts:n,canvas:i,setAppState:a})=>{let l=WL.useCallback(()=>{a({openDialog:null})},[a]);return yo(XL,{children:o.openDialog?.name==="jsonExport"&&yo(Ge,{onCloseRequest:l,title:g("buttons.export"),children:yo(KL,{elements:e,appState:o,setAppState:a,files:t,actionManager:r,onCloseRequest:l,exportOpts:n,canvas:i})})})};import sd from"clsx";import{jsx as $L}from"react/jsx-runtime";var a0=e=>$L("button",{className:"help-icon",onClick:e.onClick,type:"button",title:`${g("helpDialog.title")} \u2014 ?`,"aria-label":g("helpDialog.title"),children:qn});import{jsx as Bo,jsxs as Xu}from"react/jsx-runtime";var l0=({appState:e,actionManager:o,showExitZenModeBtn:t,renderWelcomeScreen:r})=>{let{FooterCenterTunnel:n,WelcomeScreenHelpHintTunnel:i}=_e(),a=me(),l=!e.viewModeEnabled&&e.multiElement&&a.isTouchScreen;return Xu("footer",{role:"contentinfo",className:"layer-ui__wrapper__footer App-menu App-menu_bottom",children:[Bo("div",{className:sd("layer-ui__wrapper__footer-left zen-mode-transition",{"layer-ui__wrapper__footer-left--transition-left":e.zenModeEnabled}),children:Bo(it.Col,{gap:2,children:Xu(Nr,{heading:"canvasActions",children:[Bo(Ew,{renderAction:o.renderAction,zoom:e.zoom}),!e.viewModeEnabled&&Bo(yw,{renderAction:o.renderAction,className:sd("zen-mode-transition",{"layer-ui__wrapper__footer-left--transition-bottom":e.zenModeEnabled})}),l&&Bo(ww,{renderAction:o.renderAction,className:sd("zen-mode-transition",{"layer-ui__wrapper__footer-left--transition-left":e.zenModeEnabled})})]})})}),Bo(n.Out,{}),Bo("div",{className:sd("layer-ui__wrapper__footer-right zen-mode-transition",{"transition-right":e.zenModeEnabled}),children:Xu("div",{style:{position:"relative"},children:[r&&Bo(i.Out,{}),Bo(a0,{onClick:()=>o.executeAction(wn)})]})}),Bo(vw,{actionManager:o,showExitZenModeBtn:t})]})},s0=l0;l0.displayName="Footer";import{useEffect as x0,useLayoutEffect as E0,useRef as og,useState as oA,forwardRef as y0,useImperativeHandle as rA,useCallback as f0}from"react";import ZL from"react";var cd=ZL.createContext({});import jL from"clsx";import{useContext as JL}from"react";import{jsx as $u,jsxs as c0}from"react/jsx-runtime";var Zu=({children:e,className:o})=>{let t=me(),r=JL(cd),n=!!(t.editor.canFitSidebar&&r.shouldRenderDockButton);return c0("div",{className:jL("sidebar__header",o),"data-testid":"sidebar-header",children:[e,c0("div",{className:"sidebar__header__buttons",children:[n&&$u(Ut,{label:g("labels.sidebarLock"),children:$u(Gt,{onSelect:()=>r.onDock?.(!r.docked),selected:!!r.docked,className:"sidebar__dock","data-testid":"sidebar-dock","aria-label":g("labels.sidebarLock"),children:vf})}),$u(Gt,{"data-testid":"sidebar-close",className:"sidebar__close",onSelect:r.onCloseRequest,"aria-label":g("buttons.close"),children:jt})]})]})};Zu.displayName="SidebarHeader";import nA from"clsx";import qL from"clsx";import{jsx as ju,jsxs as d0}from"react/jsx-runtime";var Ju=({name:e,tab:o,icon:t,title:r,children:n,onToggle:i,className:a,style:l})=>{let s=pe(),c=ve();return d0("label",{title:r,className:"sidebar-trigger__label-element",children:[ju("input",{className:"ToolIcon_type_checkbox",type:"checkbox",onChange:m=>{document.querySelector(".layer-ui__wrapper")?.classList.remove("animate");let d=m.target.checked;s({openSidebar:d?{name:e,tab:o}:null}),i?.(d)},checked:c.openSidebar?.name===e,"aria-label":r,"aria-keyshortcuts":"0"}),d0("div",{className:qL("sidebar-trigger",a),style:l,children:[t&&ju("div",{children:t}),n&&ju("div",{className:"sidebar-trigger__label",children:n})]})]})};Ju.displayName="SidebarTrigger";import*as m0 from"@radix-ui/react-tabs";import{jsx as QL}from"react/jsx-runtime";var qu=({children:e,...o})=>QL(m0.List,{className:"sidebar-triggers",...o,children:e});qu.displayName="SidebarTabTriggers";import*as u0 from"@radix-ui/react-tabs";import{jsx as p0}from"react/jsx-runtime";var Qu=({children:e,tab:o,onSelect:t,...r})=>p0(u0.Trigger,{value:o,asChild:!0,onSelect:t,children:p0("button",{type:"button",className:"excalidraw-button sidebar-tab-trigger",...r,children:e})});Qu.displayName="SidebarTabTrigger";import*as g0 from"@radix-ui/react-tabs";import{jsx as eA}from"react/jsx-runtime";var eg=({children:e,...o})=>{let t=ve(),r=pe();if(!t.openSidebar)return null;let{name:n}=t.openSidebar;return eA(g0.Root,{className:"sidebar-tabs-root",value:t.openSidebar.tab,onValueChange:i=>r(a=>({...a,openSidebar:{...a.openSidebar,name:n,tab:i}})),...o,children:e})};eg.displayName="SidebarTabs";import*as h0 from"@radix-ui/react-tabs";import{jsx as tA}from"react/jsx-runtime";var tg=({tab:e,children:o,...t})=>tA(h0.Content,{...t,value:e,"data-testid":e,children:o});tg.displayName="SidebarTab";import{jsx as b0}from"react/jsx-runtime";import{createElement as iA}from"react";var Ja=ye(!1),v0=y0(({name:e,children:o,onDock:t,docked:r,className:n,...i},a)=>{v.DEV&&t&&r==null&&console.warn("Sidebar: `docked` must be set when `onDock` is supplied for the sidebar to be user-dockable. To hide this message, either pass `docked` or remove `onDock`");let l=pe(),s=Tr(Ja);E0(()=>(s(!!r),()=>{s(!1)}),[s,r]);let c=og({});c.current.onCloseRequest=()=>{l({openSidebar:null})},c.current.onDock=u=>t?.(u),c.current=Hn(c.current,{docked:r,shouldRenderDockButton:!!t&&r!=null});let m=og(null);rA(a,()=>m.current);let d=me(),p=f0(()=>{document.querySelector(".Dialog")||l({openSidebar:null})},[l]);return vi(m,f0(u=>{u.target.closest(".sidebar-trigger")||(!r||!d.editor.canFitSidebar)&&p()},[p,r,d.editor.canFitSidebar])),x0(()=>{let u=h=>{h.key===y.ESCAPE&&(!r||!d.editor.canFitSidebar)&&p()};return document.addEventListener("keydown",u),()=>{document.removeEventListener("keydown",u)}},[p,r,d.editor.canFitSidebar]),b0(Qe,{...i,className:nA("sidebar",{"sidebar--docked":r},n),ref:m,children:b0(cd.Provider,{value:c.current,children:o})})});v0.displayName="SidebarInner";var no=Object.assign(y0((e,o)=>{let t=ve(),{onStateChange:r}=e,n=og(t.openSidebar);x0(()=>{(!t.openSidebar&&n?.current?.name===e.name||t.openSidebar?.name===e.name&&n?.current?.name!==e.name||n.current?.name===e.name)&&t.openSidebar!==n.current&&r?.(t.openSidebar?.name!==e.name?null:t.openSidebar),n.current=t.openSidebar},[t.openSidebar,r,e.name]);let[i,a]=oA(!1);return E0(()=>(a(!0),()=>a(!1)),[]),i&&t.openSidebar?.name===e.name?iA(v0,{...e,ref:o,key:e.name}):null}),{Header:Zu,TabTriggers:qu,TabTrigger:Qu,Tabs:eg,Tab:tg,Trigger:Ju});no.displayName="Sidebar";var ig={};eh(ig,{ChangeCanvasBackground:()=>R0,ClearCanvas:()=>D0,CommandPalette:()=>A0,Export:()=>N0,Help:()=>M0,LiveCollaborationTrigger:()=>F0,LoadScene:()=>S0,SaveAsImage:()=>L0,SaveToActiveFile:()=>k0,SearchMenu:()=>_0,Socials:()=>O0,ToggleTheme:()=>P0});import lA from"clsx";var rg=ye({active:!1});async function w0({title:e,description:o,actionLabel:t,color:r}){return new Promise(n=>{st.set(rg,{active:!0,onConfirm:()=>n(!0),onClose:()=>n(!1),onReject:()=>n(!1),title:e,description:o,actionLabel:t,color:r})})}import{Fragment as aA,jsx as ng,jsxs as T0}from"react/jsx-runtime";var C0=({value:e,shortcut:o,onChange:t,choices:r,children:n,name:i})=>{let a=me();return T0(aA,{children:[T0("div",{className:"dropdown-menu-item-base dropdown-menu-item-bare",children:[ng("label",{className:"dropdown-menu-item__text",htmlFor:i,children:n}),ng(Zc,{name:i,value:e,onChange:t,choices:r})]}),o&&!a.editor.isMobile&&ng("div",{className:"dropdown-menu-item__shortcut dropdown-menu-item__shortcut--orphaned",children:o})]})};C0.displayName="DropdownMenuItemContentRadio";var I0=C0;import{Fragment as sA,jsx as tt,jsxs as B0}from"react/jsx-runtime";var S0=()=>{let{t:e}=Ve(),o=Xe(),t=kr();return o.isActionEnabled(Si)?tt(vt,{icon:Qn,onSelect:async()=>{(!t.length||await w0({title:e("overwriteConfirm.modal.loadFromFile.title"),actionLabel:e("overwriteConfirm.modal.loadFromFile.button"),color:"warning",description:tt(Tt,{i18nKey:"overwriteConfirm.modal.loadFromFile.description",bold:n=>tt("strong",{children:n}),br:()=>tt("br",{})})}))&&o.executeAction(Si)},"data-testid":"load-button",shortcut:Ye("loadScene"),"aria-label":e("buttons.load"),children:e("buttons.load")}):null};S0.displayName="LoadScene";var k0=()=>{let{t:e}=Ve(),o=Xe();return o.isActionEnabled(bc)?tt(vt,{shortcut:Ye("saveScene"),"data-testid":"save-button",onSelect:()=>o.executeAction(bc),icon:Pf,"aria-label":`${e("buttons.save")}`,children:`${e("buttons.save")}`}):null};k0.displayName="SaveToActiveFile";var L0=()=>{let e=pe(),{t:o}=Ve();return tt(vt,{icon:Wl,"data-testid":"image-export-button",onSelect:()=>e({openDialog:{name:"imageExport"}}),shortcut:Ye("imageExport"),"aria-label":o("buttons.exportImage"),children:o("buttons.exportImage")})};L0.displayName="SaveAsImage";var A0=e=>{let o=pe(),{t}=Ve();return tt(vt,{icon:es,"data-testid":"command-palette-button",onSelect:()=>{le("command_palette","open","menu"),o({openDialog:{name:"commandPalette"}})},shortcut:Ye("commandPalette"),"aria-label":t("commandPalette.title"),className:e?.className,children:t("commandPalette.title")})};A0.displayName="CommandPalette";var _0=e=>{let{t:o}=Ve(),t=Xe();return tt(vt,{icon:Ft,"data-testid":"search-menu-button",onSelect:()=>{t.executeAction(Ka)},shortcut:Ye("searchMenu"),"aria-label":o("search.title"),className:e?.className,children:o("search.title")})};_0.displayName="SearchMenu";var M0=()=>{let{t:e}=Ve(),o=Xe();return tt(vt,{"data-testid":"help-menu-item",icon:qn,onSelect:()=>o.executeAction(wn),shortcut:"?","aria-label":e("helpDialog.title"),children:e("helpDialog.title")})};M0.displayName="Help";var D0=()=>{let{t:e}=Ve(),o=Tr(In);return Xe().isActionEnabled(er)?tt(vt,{icon:Ot,onSelect:()=>o("clearCanvas"),"data-testid":"clear-canvas-button","aria-label":e("buttons.clearReset"),children:e("buttons.clearReset")}):null};D0.displayName="ClearCanvas";var P0=e=>{let{t:o}=Ve(),t=ve(),r=Xe(),n=Ye("toggleTheme");return r.isActionEnabled(En)?e?.allowSystemTheme?tt(I0,{name:"theme",value:e.theme,onChange:i=>e.onSelect(i),choices:[{value:re.LIGHT,label:ha,ariaLabel:`${o("buttons.lightMode")} - ${n}`},{value:re.DARK,label:ga,ariaLabel:`${o("buttons.darkMode")} - ${n}`},{value:"system",label:Kb,ariaLabel:o("buttons.systemMode")}],children:o("labels.theme")}):tt(vt,{onSelect:i=>{if(i.preventDefault(),e?.onSelect)e.onSelect(t.theme===re.DARK?re.LIGHT:re.DARK);else return r.executeAction(En)},icon:t.theme===re.DARK?ha:ga,"data-testid":"toggle-dark-mode",shortcut:n,"aria-label":t.theme===re.DARK?o("buttons.lightMode"):o("buttons.darkMode"),children:t.theme===re.DARK?o("buttons.lightMode"):o("buttons.darkMode")}):null};P0.displayName="ToggleTheme";var R0=()=>{let{t:e}=Ve(),o=ve(),t=Xe(),r=Do();return o.viewModeEnabled||!r.UIOptions.canvasActions.changeViewBackgroundColor?null:B0("div",{style:{marginTop:"0.5rem"},children:[tt("div",{"data-testid":"canvas-background-label",style:{fontSize:".75rem",marginBottom:".5rem"},children:e("labels.canvasBackground")}),tt("div",{style:{padding:"0 0.625rem"},children:t.renderAction("changeViewBackgroundColor")})]})};R0.displayName="ChangeCanvasBackground";var N0=()=>{let{t:e}=Ve(),o=pe();return tt(vt,{icon:rn,onSelect:()=>{o({openDialog:{name:"jsonExport"}})},"data-testid":"json-export-button","aria-label":e("buttons.export"),children:e("buttons.export")})};N0.displayName="Export";var O0=()=>{let{t:e}=Ve();return B0(sA,{children:[tt(Oi,{icon:Yl,href:"https://github.com/excalidraw/excalidraw","aria-label":"GitHub",children:"GitHub"}),tt(Oi,{icon:Mf,href:"https://x.com/excalidraw","aria-label":"X",children:e("labels.followUs")}),tt(Oi,{icon:_f,href:"https://discord.gg/UexuTaE","aria-label":"Discord",children:e("labels.discordChat")})]})};O0.displayName="Socials";var F0=({onSelect:e,isCollaborating:o})=>{let{t}=Ve();return tt(vt,{"data-testid":"collab-button",icon:Kl,className:lA({"active-collab":o}),onSelect:e,children:t("labels.liveCollaboration")})};F0.displayName="LiveCollaborationTrigger";import{useLayoutEffect as cA,useRef as dA}from"react";import{jsx as mA}from"react/jsx-runtime";var ar=(e,o)=>{let t=ye(0),r=n=>{let{tunnelsJotai:{useAtom:i}}=_e(),[,a]=i(t),l=dA({preferHost:!1,counter:0});return cA(()=>{let s=l.current;return a(c=>{let m=c+1;return s.counter=m,m}),()=>{a(c=>{let m=c-1;return s.counter=m,m||(s.preferHost=!1),m})}},[a]),n.__fallback||(l.current.preferHost=!0),!l.current.counter&&n.__fallback&&l.current.preferHost||l.current.counter>1&&n.__fallback?null:mA(o,{...n})};return r.displayName=e,r};import{jsx as dd,jsxs as ag}from"react/jsx-runtime";var pA=Object.assign(ar("MainMenu",({children:e,onSelect:o})=>{let{MainMenuTunnel:t}=_e(),r=me(),n=ve(),i=pe(),a=r.editor.isMobile?void 0:()=>i({openMenu:null});return dd(t.In,{children:ag(Ce,{open:n.openMenu==="canvas",children:[dd(Ce.Trigger,{onToggle:()=>{i({openMenu:n.openMenu==="canvas"?null:"canvas"})},"data-testid":"main-menu-trigger",className:"main-menu-trigger",children:Gl}),ag(Ce.Content,{onClickOutside:a,onSelect:ur(o,()=>{i({openMenu:null})}),children:[e,r.editor.isMobile&&n.collaborators.size>0&&ag("fieldset",{className:"UserList-Wrapper",children:[dd("legend",{children:g("labels.collaborators")}),dd(ad,{mobile:!0,collaborators:n.collaborators,userToFollow:n.userToFollow?.socketId||null})]})]})]})})}),{Trigger:Ce.Trigger,Item:Ce.Item,ItemLink:Ce.ItemLink,ItemCustom:Ce.ItemCustom,Group:Ce.Group,Separator:Ce.Separator,DefaultItems:ig}),ft=pA;import{jsx as Gi,jsxs as hA}from"react/jsx-runtime";var md=({title:e,children:o,actionLabel:t,onClick:r})=>hA("div",{className:"OverwriteConfirm__Actions__Action",children:[Gi("h4",{children:e}),Gi("div",{className:"OverwriteConfirm__Actions__Action__content",children:o}),Gi(Dr,{variant:"outlined",color:"muted",label:t,size:"large",fullWidth:!0,onClick:r})]}),uA=()=>{let{t:e}=Ve(),o=Xe(),t=pe();return Gi(md,{title:e("overwriteConfirm.action.exportToImage.title"),actionLabel:e("overwriteConfirm.action.exportToImage.button"),onClick:()=>{o.executeAction(fc,"ui",!0),t({openDialog:{name:"imageExport"}})},children:e("overwriteConfirm.action.exportToImage.description")})},gA=()=>{let{t:e}=Ve(),o=Xe();return Gi(md,{title:e("overwriteConfirm.action.saveToDisk.title"),actionLabel:e("overwriteConfirm.action.saveToDisk.button"),onClick:()=>{o.executeAction(Ua,"ui")},children:e("overwriteConfirm.action.saveToDisk.description")})},lg=Object.assign(({children:e})=>Gi("div",{className:"OverwriteConfirm__Actions",children:e}),{ExportToImage:uA,SaveToDisk:gA});import{jsx as Fr,jsxs as z0}from"react/jsx-runtime";var pd=Object.assign(ar("OverwriteConfirmDialog",({children:e})=>{let{OverwriteConfirmDialogTunnel:o}=_e(),[t,r]=ce(rg);if(!t.active)return null;let n=()=>{t.onClose(),r(a=>({...a,active:!1}))},i=()=>{t.onConfirm(),r(a=>({...a,active:!1}))};return Fr(o.In,{children:Fr(Ge,{onCloseRequest:n,title:!1,size:916,children:z0("div",{className:"OverwriteConfirm",children:[Fr("h3",{children:t.title}),z0("div",{className:`OverwriteConfirm__Description OverwriteConfirm__Description--color-${t.color}`,children:[Fr("div",{className:"OverwriteConfirm__Description__icon",children:Lb}),Fr("div",{children:t.description}),Fr("div",{className:"OverwriteConfirm__Description__spacer"}),Fr(Dr,{color:t.color,size:"large",label:t.actionLabel,onClick:i})]}),Fr(lg,{children:e})]})})})}),{Actions:lg,Action:md});import MA from"clsx";import{Fragment as fA,memo as bA,useEffect as qa,useRef as sg,useState as U0}from"react";import xA from"lodash.debounce";import EA from"clsx";import{Fragment as _A,jsx as Vt,jsxs as Qa}from"react/jsx-runtime";var yA=ye(""),cg=ye(null),vA=350,G0=()=>{let e=He(),o=pe(),t=sg(null),[r,n]=ce(yA),i=r.trim(),[a,l]=U0(!1),[s,c]=U0({nonce:null,items:[]}),m=sg(null),d=sg(void 0),[p,u]=ce(cg),h=e.scene.getNonDeletedElementsMap();qa(()=>{a||(i!==m.current||e.scene.getSceneNonce()!==d.current)&&(m.current=null,H0(i,e,(E,w)=>{c({nonce:pa(),items:E}),m.current=i,d.current=e.scene.getSceneNonce(),o({searchMatches:E.map(S=>({id:S.textElement.id,focus:!1,matchedLines:S.matchedLines}))})}))},[a,i,h,e,o,u,d]);let f=()=>{s.items.length>0&&u(E=>E===null?0:(E+1)%s.items.length)},b=()=>{s.items.length>0&&u(E=>E===null?0:E-1<0?s.items.length-1:E-1)};qa(()=>{o(E=>({searchMatches:E.searchMatches.map((w,S)=>S===p?{...w,focus:!0}:{...w,focus:!1})}))},[p,o]),qa(()=>{if(s.items.length>0&&p!==null){let E=s.items[p];if(E){let w=e.state.zoom.value,S=So({text:E.searchQuery,x:E.textElement.x+(E.matchedLines[0]?.offsetX??0),y:E.textElement.y+(E.matchedLines[0]?.offsetY??0),width:E.matchedLines[0]?.width,height:E.matchedLines[0]?.height,fontSize:E.textElement.fontSize,fontFamily:E.textElement.fontFamily}),I=14,_=E.textElement.fontSize,k=_*w<I;if(!gi([S],e.canvas.width/window.devicePixelRatio,e.canvas.height/window.devicePixelRatio,{offsetLeft:e.state.offsetLeft,offsetTop:e.state.offsetTop,scrollX:e.state.scrollX,scrollY:e.state.scrollY,zoom:e.state.zoom},e.scene.getNonDeletedElementsMap(),e.getEditorUIOffsets())||k){let R;k?_>=I?R={fitToContent:!0}:R={fitToViewport:!0,maxZoom:Xt(I/_,1)}:R={fitToContent:!0},e.scrollToContent(S,{animate:!0,duration:300,...R,canvasOffsets:e.getEditorUIOffsets()})}}}},[p,s,e]),qa(()=>()=>{u(null),m.current=null,d.current=void 0,o({searchMatches:[]}),l(!1)},[o,u]);let x=Ir({goToNextItem:f,goToPreviousItem:b,searchMatches:s});qa(()=>{let E=w=>{if(w.key===y.ESCAPE&&!e.state.openDialog&&!e.state.openPopup){w.preventDefault(),w.stopPropagation(),o({openSidebar:null});return}w[y.CTRL_OR_CMD]&&w.key===y.F&&(w.preventDefault(),w.stopPropagation(),t.current?.matches(":focus")?o({openSidebar:null}):(e.state.openDialog&&o({openDialog:null}),t.current?.focus(),t.current?.select())),w.target instanceof HTMLElement&&w.target.closest(".layer-ui__search")&&x.searchMatches.items.length&&(w.key===y.ENTER&&(w.stopPropagation(),x.goToNextItem()),w.key===y.ARROW_UP?(w.stopPropagation(),x.goToPreviousItem()):w.key===y.ARROW_DOWN&&(w.stopPropagation(),x.goToNextItem()))};return Ae(window,"keydown",E,{capture:!0,passive:!1})},[o,x,e]);let T=`${s.items.length} ${s.items.length===1?g("search.singleResult"):g("search.multipleResults")}`;return Qa("div",{className:"layer-ui__search",children:[Vt("div",{className:"layer-ui__search-header",children:Vt(zi,{className:co.SEARCH_MENU_INPUT_WRAPPER,value:r,ref:t,placeholder:g("search.placeholder"),icon:Ft,onChange:E=>{n(E),l(!0);let w=E.trim();H0(w,e,(S,I)=>{c({nonce:pa(),items:S}),u(I),m.current=w,d.current=e.scene.getSceneNonce(),o({searchMatches:S.map(_=>({id:_.textElement.id,focus:!1,matchedLines:_.matchedLines}))}),l(!1)})},selectOnRender:!0})}),Qa("div",{className:"layer-ui__search-count",children:[s.items.length>0&&Qa(_A,{children:[p!==null&&p>-1?Qa("div",{children:[p+1," / ",T]}):Vt("div",{children:T}),Qa("div",{className:"result-nav",children:[Vt(Gt,{onSelect:()=>{f()},className:"result-nav-btn",children:os}),Vt(Gt,{onSelect:()=>{b()},className:"result-nav-btn",children:ex})]})]}),s.items.length===0&&i&&m.current&&Vt("div",{style:{margin:"1rem auto"},children:g("search.noMatch")})]}),Vt(IA,{matches:s,onItemClick:u,focusIndex:p,searchQuery:i})]})},wA=e=>{let o=[e.preview.moreBefore?"...":"",e.preview.previewText.slice(0,e.preview.indexInSearchQuery),e.preview.previewText.slice(e.preview.indexInSearchQuery,e.preview.indexInSearchQuery+e.searchQuery.length),e.preview.previewText.slice(e.preview.indexInSearchQuery+e.searchQuery.length),e.preview.moreAfter?"...":""];return Vt("div",{tabIndex:-1,className:EA("layer-ui__result-item",{active:e.highlighted}),onClick:e.onClick,ref:t=>{e.highlighted&&t?.scrollIntoView({behavior:"auto",block:"nearest"})},children:Vt("div",{className:"preview-text",children:o.flatMap((t,r)=>Vt(fA,{children:r===2?Vt("b",{children:t}):t},r))})})},TA=e=>Vt("div",{className:"layer-ui__search-result-container",children:e.matches.items.map((o,t)=>Vt(wA,{searchQuery:e.searchQuery,preview:o.preview,highlighted:t===e.focusIndex,onClick:()=>e.onItemClick(t)},o.textElement.id+o.index))}),CA=(e,o)=>e.matches.nonce===o.matches.nonce&&e.focusIndex===o.focusIndex,IA=bA(TA,CA),SA=(e,o,t)=>{let i=e.slice(0,o),a=i.split(/\s+/),l=i.endsWith(" "),s=a.length-2-1-(l?0:1),c=a.slice(s<=0?0:s).join(" ")+(l?" ":""),m=20;c=c.length>m?c.slice(-m):c;let d=e.slice(o+t.length),p=d.split(/\s+/),u=!d.startsWith(" "),h=u?6:5,f=(u?"":" ")+p.slice(0,h).join(" ");return{indexInSearchQuery:c.length,previewText:c+t+f,moreBefore:s>0,moreAfter:p.length>h}},kA=(e,o)=>{let t=e.split(`
`),r=[],n=0;for(let i=0;i<t.length;i++){let a=t[i],l=t[i+1];if(l){let s=o.indexOf(l,n);if(s>a.length+n){let c=s-(a.length+n);for(;c>0;)a+=" ",c--}}r.push(a),n=n+a.length}return r.join(`
`)},LA=(e,o,t)=>{let n=kA(e.text,e.originalText).split(`
`),i=[],a=0,l=0;for(let d of n){let p=a,u=p+d.length-1;i.push({line:d,startIndex:p,endIndex:u,lineNumber:l}),a=u+1,l++}let s=t,c=e.originalText.slice(t,t+o.length),m=[];for(let d of i){if(c==="")break;if(s>=d.startIndex&&s<=d.endIndex){let p=d.endIndex+1-s,u=d.line.slice(0,s-d.startIndex),h=c.slice(0,p);c=c.slice(p);let f=Nt(u,lt(e),e.lineHeight);if(u===""&&(f.width=0),e.textAlign!=="left"&&d.line.length>0){let w=Nt(d.line,lt(e),e.lineHeight),S=e.textAlign==="center"?(e.width-w.width)/2:e.width-w.width;f.width+=S}let{width:b,height:x}=Nt(h,lt(e),e.lineHeight),T=f.width,E=d.lineNumber*f.height;m.push({offsetX:T,offsetY:E,width:b,height:x}),s+=p}}return m},AA=e=>e.replace(/[.*+?^${}()|[\]\\-]/g,"\\$&"),H0=xA((e,o,t)=>{if(!e||e===""){t([],null);return}let n=o.scene.getNonDeletedElements().filter(c=>Y(c));n.sort((c,m)=>c.y-m.y);let i=[],a=new RegExp(AA(e),"gi");for(let c of n){let m=null,d=c.originalText;for(;(m=a.exec(d))!==null;){let p=SA(d,m.index,e),u=LA(c,e,m.index);u.length>0&&i.push({textElement:c,searchQuery:e,preview:p,index:m.index,matchedLines:u})}}let l=new Set(o.visibleElements.map(c=>c.id)),s=i.findIndex(c=>l.has(c.textElement.id))??null;t(i,s)},vA);import{jsx as vo,jsxs as Y0}from"react/jsx-runtime";import{createElement as DA}from"react";var V0=ar("DefaultSidebarTrigger",e=>{let{DefaultSidebarTriggerTunnel:o}=_e();return vo(o.In,{children:vo(no.Trigger,{...e,className:"default-sidebar-trigger",name:pt.name})})});V0.displayName="DefaultSidebarTrigger";var W0=({children:e})=>{let{DefaultSidebarTabTriggersTunnel:o}=_e();return vo(o.In,{children:e})};W0.displayName="DefaultTabTriggers";var ud=Object.assign(ar("DefaultSidebar",({children:e,className:o,onDock:t,docked:r,...n})=>{let i=ve(),a=pe(),{DefaultSidebarTabTriggersTunnel:l}=_e(),s=i.openSidebar?.tab===Yo;return DA(no,{...n,name:"default",key:"default",className:MA("default-sidebar",o),docked:s||(r??i.defaultSidebarDockedPreference),onDock:s||t===!1||!t&&r!=null?void 0:ur(t,c=>{a({defaultSidebarDockedPreference:c})})},Y0(no.Tabs,{children:[vo(no.Header,{children:Y0(no.TabTriggers,{children:[vo(no.TabTrigger,{tab:Yo,children:Ft}),vo(no.TabTrigger,{tab:ia,children:jn}),vo(l.Out,{})]})}),vo(no.Tab,{tab:ia,children:vo(fw,{})}),vo(no.Tab,{tab:Yo,children:vo(G0,{})}),e]}))}),{Trigger:V0,TabTriggers:W0});import PA from"clsx";import{jsx as K0,jsxs as NA}from"react/jsx-runtime";var RA="small",X0=e=>NA("label",{className:PA("ToolIcon ToolIcon__LaserPointer",`ToolIcon_size_${RA}`,{"is-mobile":e.isMobile}),title:`${e.title}`,children:[K0("input",{className:"ToolIcon_type_checkbox",type:"checkbox",name:e.name,onChange:e.onChange,checked:e.checked,"aria-label":e.title,"data-testid":"toolbar-LaserPointer"}),K0("div",{className:"ToolIcon__icon",children:ql})]});import{useState as q0,useRef as Q0,useEffect as e1,useDeferredValue as GA}from"react";var OA=({canvasRef:e,setError:o})=>{let t=e.current;if(!t)return;let r=t.parentElement;r&&(r.style.background="",o(null),t.replaceChildren())},gd=async({canvasRef:e,mermaidToExcalidrawLib:o,mermaidDefinition:t,setError:r,data:n})=>{let i=e.current,a=i?.parentElement;if(!(!i||!a)){if(!t){OA({canvasRef:e,setError:r});return}try{let l=await o.api,s;try{s=await l.parseMermaidToExcalidraw(t)}catch{s=await l.parseMermaidToExcalidraw(t.replace(/"/g,"'"))}let{elements:c,files:m}=s;r(null),n.current={elements:Yi(c,{regenerateIds:!0}),files:m};let d=await un({elements:n.current.elements,files:n.current.files,exportPadding:sr,maxWidthOrHeight:Math.max(a.offsetWidth,a.offsetHeight)*window.devicePixelRatio});try{await jo(d)}catch(p){throw p.name==="CANVAS_POSSIBLY_TOO_BIG"?new Error(g("canvasError.canvasTooBig")):p}a.style.background="var(--default-bg-color)",i.replaceChildren(d)}catch(l){throw a.style.background="var(--default-bg-color)",t&&r(l),l}}},el=e=>{ro.set(cr.MERMAID_TO_EXCALIDRAW,e)},hd=({app:e,data:o,text:t,shouldSaveMermaidDataToStorage:r})=>{let{elements:n,files:i}=o.current;n.length&&(e.addElementsFromPasteOrLibrary({elements:n,files:i,position:"center",fitToContent:!0}),e.setOpenDialog(null),r&&t&&el(t))};import{jsx as FA}from"react/jsx-runtime";var fd=({children:e})=>FA("div",{className:"ttd-dialog-panels",children:e});import $0 from"clsx";import{jsx as dg,jsxs as tl}from"react/jsx-runtime";var Vi=({label:e,children:o,panelAction:t,panelActionDisabled:r=!1,onTextSubmitInProgess:n,renderTopRight:i,renderSubmitShortcut:a,renderBottomRight:l})=>tl("div",{className:"ttd-dialog-panel",children:[tl("div",{className:"ttd-dialog-panel__header",children:[dg("label",{children:e}),i?.()]}),o,tl("div",{className:$0("ttd-dialog-panel-button-container",{invisible:!t}),style:{display:"flex",alignItems:"center"},children:[tl(Gt,{className:"ttd-dialog-panel-button",onSelect:t?t.action:()=>{},disabled:r||n,children:[tl("div",{className:$0({invisible:n}),children:[t?.label,t?.icon&&dg("span",{children:t.icon})]}),n&&dg(ct,{})]}),!r&&!n&&a?.(),l?.()]})]});import{useEffect as BA,useRef as Z0}from"react";import{jsx as zA}from"react/jsx-runtime";var bd=({input:e,placeholder:o,onChange:t,onKeyboardSubmit:r})=>{let n=Z0(null),i=Z0(r);return i.current=r,BA(()=>{if(!i.current)return;let a=n.current;if(a){let l=s=>{s[y.CTRL_OR_CMD]&&s.key===y.ENTER&&(s.preventDefault(),i.current?.())};return a.addEventListener("keydown",l),()=>{a.removeEventListener("keydown",l)}}},[]),zA("textarea",{className:"ttd-dialog-input",onChange:t,value:e,placeholder:o,autoFocus:!0,ref:n})};import{jsx as xd,jsxs as j0}from"react/jsx-runtime";var UA=({error:e})=>j0("div",{"data-testid":"ttd-dialog-output-error",className:"ttd-dialog-output-error",children:["Error! ",xd("p",{children:e})]}),Ed=({error:e,canvasRef:o,loaded:t})=>j0("div",{className:"ttd-dialog-output-wrapper",children:[e&&xd(UA,{error:e.message}),t?xd("div",{ref:o,style:{opacity:e?"0.15":1},className:"ttd-dialog-output-canvas-container"}):xd(ct,{size:"2rem"})]});import{jsx as J0,jsxs as HA}from"react/jsx-runtime";var yd=()=>HA("div",{className:"ttd-dialog-submit-shortcut",children:[J0("div",{className:"ttd-dialog-submit-shortcut__key",children:A("CtrlOrCmd")}),J0("div",{className:"ttd-dialog-submit-shortcut__key",children:A("Enter")})]});import{Fragment as WA,jsx as zo,jsxs as o1}from"react/jsx-runtime";var YA=`flowchart TD
 A[Christmas] -->|Get money| B(Go shopping)
 B --> C{Let me think}
 C -->|One| D[Laptop]
 C -->|Two| E[iPhone]
 C -->|Three| F[Car]`,t1=$r(el,300),VA=({mermaidToExcalidrawLib:e})=>{let[o,t]=q0(()=>ro.get(cr.MERMAID_TO_EXCALIDRAW)||YA),r=GA(o.trim()),[n,i]=q0(null),a=Q0(null),l=Q0({elements:[],files:null}),s=He();e1(()=>{gd({canvasRef:a,data:l,mermaidToExcalidrawLib:e,setError:i,mermaidDefinition:r}).catch(m=>{Dl()&&console.error("Failed to parse mermaid definition",m)}),t1(r)},[r,e]),e1(()=>()=>{t1.flush()},[]);let c=()=>{hd({app:s,data:l,text:o,shouldSaveMermaidDataToStorage:!0})};return o1(WA,{children:[zo("div",{className:"ttd-dialog-desc",children:zo(Tt,{i18nKey:"mermaid.description",flowchartLink:m=>zo("a",{href:"https://mermaid.js.org/syntax/flowchart.html",children:m}),sequenceLink:m=>zo("a",{href:"https://mermaid.js.org/syntax/sequenceDiagram.html",children:m}),classLink:m=>zo("a",{href:"https://mermaid.js.org/syntax/classDiagram.html",children:m})})}),o1(fd,{children:[zo(Vi,{label:g("mermaid.syntax"),children:zo(bd,{input:o,placeholder:"Write Mermaid diagram defintion here...",onChange:m=>t(m.target.value),onKeyboardSubmit:()=>{c()}})}),zo(Vi,{label:g("mermaid.preview"),panelAction:{action:()=>{c()},label:g("mermaid.button"),icon:ti},renderSubmitShortcut:()=>zo(yd,{}),children:zo(Ed,{canvasRef:a,loaded:e.loaded,error:n})})]})]})},r1=VA;import*as i1 from"@radix-ui/react-tabs";import{useRef as n1}from"react";import{jsx as KA}from"react/jsx-runtime";var a1=e=>{let o=pe(),t=n1(null),r=n1(0);return KA(i1.Root,{ref:t,className:"ttd-dialog-tabs-root",value:e.tab,onValueChange:n=>{if(!n)return;let i=t.current?.closest(".Modal__content");if(i){let a=i.offsetHeight||0;a>r.current&&(r.current=a,i.style.minHeight=`min(${r.current}px, 100%)`)}e.dialog==="ttd"&&Kh(["text-to-diagram","mermaid"],n)&&o({openDialog:{name:e.dialog,tab:n}})},children:e.children})};a1.displayName="TTDDialogTabs";var l1=a1;import{useEffect as ZA,useRef as pg,useState as Td}from"react";import*as s1 from"@radix-ui/react-tabs";import{jsx as XA}from"react/jsx-runtime";var mg=({children:e,...o})=>XA(s1.List,{className:"ttd-dialog-triggers",...o,children:e});mg.displayName="TTDDialogTabTriggers";import*as d1 from"@radix-ui/react-tabs";import{jsx as c1}from"react/jsx-runtime";var vd=({children:e,tab:o,onSelect:t,...r})=>c1(d1.Trigger,{value:o,asChild:!0,onSelect:t,children:c1("button",{type:"button",className:"ttd-dialog-tab-trigger",...r,children:e})});vd.displayName="TTDDialogTabTrigger";import*as m1 from"@radix-ui/react-tabs";import{jsx as $A}from"react/jsx-runtime";var wd=({tab:e,children:o,...t})=>$A(m1.Content,{...t,value:e,children:o});wd.displayName="TTDDialogTab";import{jsx as It,jsxs as Br}from"react/jsx-runtime";var ug=3,Wi=1e3,jA=ye(null),JA=ye(null),gg=e=>{let o=ve();return o.openDialog?.name!=="ttd"?null:It(qA,{...e,tab:o.openDialog.tab})},qA=ar("TTDDialogBase",({tab:e,...o})=>{let t=He(),r=pe(),n=pg(null),[i,a]=ce(JA),[l,s]=Td(i?.prompt??""),c=l.trim(),m=I=>{s(I.target.value),a(_=>({generatedResponse:_?.generatedResponse??null,prompt:I.target.value}))},[d,p]=Td(!1),[u,h]=ce(jA),f=async()=>{if(c.length>Wi||c.length<ug||d||u?.rateLimitRemaining===0||"__fallback"in o){c.length<ug&&S(new Error(`Prompt is too short (min ${ug} characters)`)),c.length>Wi&&S(new Error(`Prompt is too long (max ${Wi} characters)`));return}try{p(!0),le("ai","generate","ttd");let{generatedResponse:I,error:_,rateLimit:k,rateLimitRemaining:R}=await o.onTextSubmit(c);if(typeof I=="string"&&a(M=>({generatedResponse:I,prompt:M?.prompt??null})),Jd(k)&&Jd(R)&&h({rateLimit:k,rateLimitRemaining:R}),_){S(_);return}if(!I){S(new Error("Generation failed"));return}try{await gd({canvasRef:n,data:E,mermaidToExcalidrawLib:x,setError:S,mermaidDefinition:I}),le("ai","mermaid parse success","ttd")}catch(M){console.info(`%cTTD mermaid render errror: ${M.message}`,"color: red"),console.info(`>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
TTD mermaid definition render errror: ${M.message}`,"color: yellow"),le("ai","mermaid parse failed","ttd"),S(new Error("Generated an invalid diagram :(. You may also try a different prompt."))}}catch(I){let _=I.message;(!_||_==="Failed to fetch")&&(_="Request failed"),S(new Error(_))}finally{p(!1)}},b=pg(f);b.current=f;let[x,T]=Td({loaded:!1,api:import("@excalidraw/mermaid-to-excalidraw")});ZA(()=>{(async()=>{await x.api,T(_=>({..._,loaded:!0}))})()},[x.api]);let E=pg({elements:[],files:null}),[w,S]=Td(null);return It(Ge,{className:"ttd-dialog",onCloseRequest:()=>{t.setOpenDialog(null)},size:1200,title:!1,...o,autofocus:!1,children:Br(l1,{dialog:"ttd",tab:e,children:["__fallback"in o&&o.__fallback?It("p",{className:"dialog-mermaid-title",children:g("mermaid.title")}):Br(mg,{children:[It(vd,{tab:"text-to-diagram",children:Br("div",{style:{display:"flex",alignItems:"center"},children:[g("labels.textToDiagram"),It("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",padding:"1px 6px",marginLeft:"10px",fontSize:10,borderRadius:"12px",background:"var(--color-promo)",color:"var(--color-surface-lowest)"},children:"AI Beta"})]})}),It(vd,{tab:"mermaid",children:"Mermaid"})]}),It(wd,{className:"ttd-dialog-content",tab:"mermaid",children:It(r1,{mermaidToExcalidrawLib:x})}),!("__fallback"in o)&&Br(wd,{className:"ttd-dialog-content",tab:"text-to-diagram",children:[It("div",{className:"ttd-dialog-desc",children:"Currently we use Mermaid as a middle step, so you'll get best results if you describe a diagram, workflow, flow chart, and similar."}),Br(fd,{children:[It(Vi,{label:g("labels.prompt"),panelAction:{action:f,label:"Generate",icon:ti},onTextSubmitInProgess:d,panelActionDisabled:c.length>Wi||u?.rateLimitRemaining===0,renderTopRight:()=>u?Br("div",{className:"ttd-dialog-rate-limit",style:{fontSize:12,marginLeft:"auto",color:u.rateLimitRemaining===0?"var(--color-danger)":void 0},children:[u.rateLimitRemaining," requests left today"]}):null,renderSubmitShortcut:()=>It(yd,{}),renderBottomRight:()=>{if(typeof i?.generatedResponse=="string")return Br("div",{className:"excalidraw-link",style:{marginLeft:"auto",fontSize:14},onClick:()=>{typeof i?.generatedResponse=="string"&&(el(i.generatedResponse),r({openDialog:{name:"ttd",tab:"mermaid"}}))},children:["View as Mermaid",It(Lr,{icon:ti})]});let I=c.length/Wi;return I>.8?Br("div",{style:{marginLeft:"auto",fontSize:12,fontFamily:"monospace",color:I>1?"var(--color-danger)":void 0},children:["Length: ",c.length,"/",Wi]}):null},children:It(bd,{onChange:m,input:l,placeholder:"Describe what you want to see...",onKeyboardSubmit:()=>{b.current()}})}),It(Vi,{label:"Preview",panelAction:{action:()=>{console.info("Panel action clicked"),hd({app:t,data:E})},label:"Insert",icon:ti},children:It(Ed,{canvasRef:n,error:w,loaded:x.loaded})})]})]})]})})});import{useEffect as I1,useMemo as yg,useState as G_,memo as Y_}from"react";import V_ from"lodash.throttle";import{useEffect as u1,useRef as Id,useState as QA}from"react";import e_ from"clsx";var p1=.01,Pt=(e,o)=>!(o==="height"&&Y(e)||o==="width"&&Y(e)||o==="angle"&&ie(e)),Oe=(e,o)=>{let t=e+o/2;return t-t%o},Ki=(e,o,t)=>Object.keys(e).map(r=>({original:(t??o).get(r),latest:o.get(r)})).filter(r=>r.original!==void 0&&r.latest!==void 0);var Ln=(e,o,t,r,n,i,a,l=!0)=>{let s=r.get(t.id);if(!s)return;let[c,m]=[t.x+t.width/2,t.y+t.height/2],[d,p]=bt(z(t.x,t.y),z(c,m),t.angle),u=e-d,h=o-p,[f,b]=bt(z(e,o),z(c+u,m+h),-t.angle);P(s,{x:f,y:b},l),Cd(s,r,n,i);let x=ue(t,a);if(x){let T=r.get(x.id);T&&P(T,{x:x.x+u,y:x.y+h},l)}},Xi=(e,o)=>{let r=po(o).map(n=>Le(e,n).reduce((i,a)=>(i[a.id]=!0,i),{}));return e.filter(n=>!hr(n)).forEach(n=>{r.push({[n.id]:!0})}),r},Cd=(e,o,t,r,n)=>{he(e)?cn([e],o,t,r,!0,[],n?.zoom):Jt(e,o,n)};import{jsx as hg,jsxs as o_}from"react/jsx-runtime";var t_=({label:e,icon:o,dragInputCallback:t,value:r,elements:n,editable:i=!0,shouldKeepAspectRatio:a,property:l,scene:s,appState:c,sensitivity:m=1})=>{let d=He(),p=Id(null),u=Id(null),[h,f]=QA(r.toString()),b=Id(null);b.current||(b.current={originalAppState:At(c),originalElements:n,lastUpdatedValue:h,updatePending:!1}),u1(()=>{let E=r.toString();f(E),b.current.lastUpdatedValue=E},[r]);let x=(E,w,S)=>{if(!b.current.updatePending)return!1;b.current.updatePending=!1;let I=Number(E);if(isNaN(I)){f(r.toString());return}let _=Number(I.toFixed(2)),k=Number(r);(isNaN(k)||Math.abs(_-k)>=p1)&&(b.current.lastUpdatedValue=E,t({accumulatedChange:0,instantChange:0,originalElements:w,originalElementsMap:d.scene.getNonDeletedElementsMap(),shouldKeepAspectRatio:a,shouldChangeByStepSize:!1,scene:s,nextValue:_,property:l,originalAppState:S,setInputValue:R=>f(String(R))}),d.syncActionResult({captureUpdate:L.IMMEDIATELY}))},T=Id({});return T.current.handleInputValue=x,u1(()=>{let E=p.current,w=T.current;return()=>{let S=E?.value;S&&w.handleInputValue?.(S,b.current.originalElements,b.current.originalAppState),window.removeEventListener("pointermove",w.onPointerMove,!1),window.removeEventListener("pointerup",w.onPointerUp,!1)}},[i]),i?o_("div",{className:e_("drag-input-container",!i&&"disabled"),"data-testid":e,children:[hg("div",{className:"drag-input-label",ref:u,onPointerDown:E=>{if(p.current&&i){document.body.classList.add("excalidraw-cursor-resize");let w=Number(p.current.value);isNaN(w)&&(w=0);let S=null,I=d.scene.getNonDeletedElements().reduce((H,V)=>(H.set(V.id,xr(V)),H),new Map),_=n.map(H=>I.get(H.id)),k=At(c),R=0,M=0,N=H=>{if(S&&I!==null&&_!==null){let V=H.clientX-S.x;V!==0&&(M+=V,Math.abs(M)>=m&&(M=Math.sign(M)*Math.floor(Math.abs(M)/m),R+=M,t({accumulatedChange:R,instantChange:M,originalElements:_,originalElementsMap:I,shouldKeepAspectRatio:a,shouldChangeByStepSize:H.shiftKey,property:l,scene:s,originalAppState:k,setInputValue:F=>f(String(F))}),M=0))}S={x:H.clientX,y:H.clientY}},G=()=>{window.removeEventListener("pointermove",N,!1),d.syncActionResult({captureUpdate:L.IMMEDIATELY}),S=null,R=0,M=0,_=null,I=null,document.body.classList.remove("excalidraw-cursor-resize"),window.removeEventListener("pointerup",G,!1)};T.current.onPointerMove=N,T.current.onPointerUp=G,window.addEventListener("pointermove",N,!1),window.addEventListener("pointerup",G,!1)}},onPointerEnter:()=>{u.current&&(u.current.style.cursor="ew-resize")},children:o?hg(Lr,{icon:o}):e}),hg("input",{className:"drag-input",autoComplete:"off",spellCheck:"false",onKeyDown:E=>{if(i){let w=E.target;w instanceof HTMLInputElement&&E.key===y.ENTER&&(x(w.value,n,c),d.focusContainer())}},ref:p,value:h,onChange:E=>{b.current.updatePending=!0,f(E.target.value)},onFocus:E=>{E.target.select(),b.current.originalElements=n,b.current.originalAppState=At(c)},onBlur:E=>{h?i&&x(E.target.value,b.current.originalElements,b.current.originalAppState):f(r.toString())},disabled:!i})]}):null},at=t_;import{jsx as a_}from"react/jsx-runtime";var g1=10,r_=e=>e.type==="image",n_=({accumulatedChange:e,originalElements:o,originalElementsMap:t,shouldKeepAspectRatio:r,shouldChangeByStepSize:n,nextValue:i,property:a,originalAppState:l,instantChange:s,scene:c})=>{let m=c.getNonDeletedElementsMap(),d=o[0],p=m.get(d.id);if(d&&p){let u=r||r_(d),h=d.width/d.height;if(l.croppingElementId===d.id){let E=m.get(d.id);if(!E||!xe(E)||!E.crop)return;let w=E.crop,S={...w},I=E.scale[0]===-1,_=E.scale[1]===-1,{width:k,height:R}=mn(E),M=w.naturalWidth/k,N=w.naturalHeight/R,G=I?w.width+w.x:w.naturalWidth-w.x,H=_?w.height+w.y:w.naturalHeight-w.y,V=Mm*M,F=Mm*N;if(i!==void 0){if(a==="width"){let ne=i*M,ge=ot(ne,V,G);S={...S,width:ge,x:I?w.x+w.width-ge:w.x}}else if(a==="height"){let ne=i*N,ge=ot(ne,F,H);S={...S,height:ge,y:_?w.y+w.height-ge:w.y}}P(E,{crop:S,width:S.width/(w.naturalWidth/k),height:S.height/(w.naturalHeight/R)});return}let O=a==="width"?s:0,j=a==="height"?s:0,oe=ot(w.width+O,V,G),W=ot(w.height+j,V,H);S={...w,x:I?w.x+w.width-oe:w.x,y:_?w.y+w.height-W:w.y,width:oe,height:W},P(E,{crop:S,width:S.width/(w.naturalWidth/k),height:S.height/(w.naturalHeight/R)});return}if(i!==void 0){let E=Math.max(a==="width"?i:u?i*h:d.width,Lt),w=Math.max(a==="height"?i:u?i/h:d.height,Lt);hi(E,w,p,d,m,t,a==="width"?"e":"s",{shouldMaintainAspectRatio:u});return}let f=a==="width"?e:0,b=a==="height"?e:0,x=Math.max(0,d.width+f);a==="width"&&(n?x=Oe(x,g1):x=Math.round(x));let T=Math.max(0,d.height+b);a==="height"&&(n?T=Oe(T,g1):T=Math.round(T)),u&&(a==="width"?T=Math.round(x/h*100)/100:x=Math.round(T*h*100)/100),T=Math.max(Lt,T),x=Math.max(Lt,x),hi(x,T,p,d,m,t,a==="width"?"e":"s",{shouldMaintainAspectRatio:u})}},i_=({property:e,element:o,scene:t,appState:r})=>{let n=Xt(e==="width"?o.width:o.height,2);if(r.croppingElementId&&r.croppingElementId===o.id&&xe(o)&&o.crop){let{width:i,height:a}=mn(o);if(e==="width"){let l=i/o.crop.naturalWidth;n=Xt(o.crop.width*l,2)}if(e==="height"){let l=a/o.crop.naturalHeight;n=Xt(o.crop.height*l,2)}}return a_(at,{label:e==="width"?"W":"H",elements:[o],dragInputCallback:n_,value:n,editable:Pt(o,e),scene:t,appState:r,property:e})},fg=i_;import{jsx as d_}from"react/jsx-runtime";var l_=15,s_=({accumulatedChange:e,originalElements:o,shouldChangeByStepSize:t,nextValue:r,scene:n})=>{let i=n.getNonDeletedElementsMap(),a=n.getNonDeletedElements(),l=o[0];if(l&&!ee(l)){let s=i.get(l.id);if(!s)return;if(r!==void 0){let h=Bn(r);P(s,{angle:h}),Cd(s,i,a,n);let f=ue(s,i);f&&!Ne(s)&&P(f,{angle:h});return}let c=Math.round(zn(l.angle)*100)/100,m=Math.round(e),d=(c+m)%360;t&&(d=Oe(d,l_)),d=d<0?d+360:d;let p=Bn(d);P(s,{angle:p}),Cd(s,i,a,n);let u=ue(s,i);u&&!Ne(s)&&P(u,{angle:p})}},c_=({element:e,scene:o,appState:t,property:r})=>d_(at,{label:"A",icon:$l,value:Math.round(zn(e.angle)%360*100)/100,elements:[e],dragInputCallback:s_,editable:Pt(e,"angle"),scene:o,appState:t,property:r}),h1=c_;import{jsx as g_}from"react/jsx-runtime";var f1=4,m_=4,p_=({accumulatedChange:e,originalElements:o,shouldChangeByStepSize:t,nextValue:r,scene:n})=>{let i=n.getNonDeletedElementsMap(),a=o[0];if(a){let l=i.get(a.id);if(!l||!Y(l))return;let s;if(r!==void 0)s=Math.max(Math.round(r),f1);else if(a.type==="text"){let c=Math.round(a.fontSize),m=Math.round(e);s=Math.max(c+m,f1),t&&(s=Oe(s,m_))}s&&(P(l,{fontSize:s}),Ue(l,n.getContainerElement(l),n.getNonDeletedElementsMap()))}},u_=({element:e,scene:o,appState:t,property:r})=>{let n=Y(e)?e:ut(e)?ue(e,o.getNonDeletedElementsMap()):null;return n?g_(at,{label:"F",value:Math.round(n.fontSize*10)/10,elements:[n],dragInputCallback:p_,icon:nn,appState:t,scene:o,property:r}):null},b1=u_;import{useMemo as h_}from"react";import{jsx as y_}from"react/jsx-runtime";var $i=10,f_=(e,o,t,r)=>{let n=r.x-e,i=r.y-o,a=r.width*t,l=r.height*t,s=e+n*t,c=o+i*t;return{width:a,height:l,x:s,y:c,...PE(r,a,l,!1),...Y(r)?{fontSize:r.fontSize*t}:{}}},b_=(e,o,t,r,n,i,a,l)=>{let s=f_(e,o,r,i);P(n,s,!1);let c=ue(i,l);if(c){let m=c.fontSize*r;Jt(n,a,{newSize:{width:s.width,height:s.height}});let d=a.get(c.id);d&&Y(d)&&(P(d,{fontSize:m},!1),yE(n,a,t==="width"?"e":"s",!0))}},x1=(e,o,t,r,n,i,a,l,s,c)=>{i==="width"?o=Math.round(e/r*100)/100:e=Math.round(o*r*100)/100;let m=o/t;for(let d=0;d<l.length;d++){let p=l[d],u=a[d];b_(n[0],n[1],i,m,u,p,s,c)}},x_=({accumulatedChange:e,originalElements:o,originalElementsMap:t,originalAppState:r,shouldChangeByStepSize:n,nextValue:i,scene:a,property:l})=>{let s=a.getNonDeletedElementsMap(),c=Xi(o,r);if(i!==void 0){for(let p of c){let u=Ki(p,s,t);if(u.length>1){let h=u.map(R=>R.latest),f=u.map(R=>R.original),[b,x,T,E]=Se(f),w=T-b,S=E-x,I=w/S,_=Math.max(Lt,l==="width"?Math.max(0,i):w),k=Math.max(Lt,l==="height"?Math.max(0,i):S);x1(_,k,S,I,z(b,x),l,h,f,s,t)}else{let[h]=u,f=h?.latest,b=h?.original;if(f&&b&&Pt(f,l)){let x=l==="width"?Math.max(0,i):f.width;l==="width"&&(n?x=Oe(x,$i):x=Math.round(x));let T=l==="height"?Math.max(0,i):f.height;l==="height"&&(n?T=Oe(T,$i):T=Math.round(T)),x=Math.max(Lt,x),T=Math.max(Lt,T),hi(x,T,f,b,s,t,l==="width"?"e":"s",{shouldInformMutation:!1})}}}a.triggerUpdate();return}let m=l==="width"?e:0,d=l==="height"?e:0;for(let p of c){let u=Ki(p,s,t);if(u.length>1){let h=u.map(R=>R.latest),f=u.map(R=>R.original),[b,x,T,E]=Se(f),w=T-b,S=E-x,I=w/S,_=Math.max(0,w+m);l==="width"&&(n?_=Oe(_,$i):_=Math.round(_));let k=Math.max(0,S+d);l==="height"&&(n?k=Oe(k,$i):k=Math.round(k)),_=Math.max(Lt,_),k=Math.max(Lt,k),x1(_,k,S,I,z(b,x),l,h,f,s,t)}else{let[h]=u,f=h?.latest,b=h?.original;if(f&&b&&Pt(f,l)){let x=Math.max(0,b.width+m);l==="width"&&(n?x=Oe(x,$i):x=Math.round(x));let T=Math.max(0,b.height+d);l==="height"&&(n?T=Oe(T,$i):T=Math.round(T)),x=Math.max(Lt,x),T=Math.max(Lt,T),hi(x,T,f,b,s,t,l==="width"?"e":"s",{shouldInformMutation:!1})}}}a.triggerUpdate()},E_=({property:e,elements:o,elementsMap:t,atomicUnits:r,scene:n,appState:i})=>{let a=h_(()=>r.map(c=>{let m=Ki(c,t);if(m.length>1){let[p,u,h,f]=Se(m.map(b=>b.latest));return Math.round((e==="width"?h-p:f-u)*100)/100}let[d]=m;return Math.round((e==="width"?d.latest.width:d.latest.height)*100)/100}),[t,r,e]),l=new Set(a).size===1?Math.round(a[0]*100)/100:"Mixed",s=a.length>0;return y_(at,{label:e==="width"?"W":"H",elements:o,dragInputCallback:x_,value:l,editable:s,appState:i,property:e,scene:n})},bg=E_;import{jsx as C_}from"react/jsx-runtime";var v_=15,w_=({accumulatedChange:e,originalElements:o,shouldChangeByStepSize:t,nextValue:r,property:n,scene:i})=>{let a=i.getNonDeletedElementsMap(),l=o.map(c=>a.get(c.id)).filter(c=>c&&!hr(c)&&Pt(c,n)),s=o.filter(c=>!hr(c)&&Pt(c,n));if(r!==void 0){let c=Bn(r);for(let m of l){if(!m)continue;P(m,{angle:c},!1);let d=ue(m,a);d&&!Ne(m)&&P(d,{angle:c},!1)}i.triggerUpdate();return}for(let c=0;c<l.length;c++){let m=l[c];if(!m)continue;let d=s[c],p=Math.round(zn(d.angle)*100)/100,u=Math.round(e),h=(p+u)%360;t&&(h=Oe(h,v_)),h=h<0?h+360:h;let f=Bn(h);P(m,{angle:f},!1);let b=ue(m,a);b&&!Ne(m)&&P(b,{angle:f},!1)}i.triggerUpdate()},T_=({elements:e,scene:o,appState:t,property:r})=>{let n=e.filter(s=>!hr(s)&&Pt(s,"angle")),i=n.map(s=>Math.round(zn(s.angle)%360*100)/100),a=new Set(i).size===1?i[0]:"Mixed",l=n.some(s=>Pt(s,"angle"));return C_(at,{label:"A",icon:$l,value:a,elements:e,dragInputCallback:w_,editable:l,appState:t,scene:o,property:r})},E1=T_;import{jsx as A_}from"react/jsx-runtime";var y1=4,I_=4,S_=(e,o)=>e.reduce((t,r)=>{if(!r||hr(r))return t;if(Y(r))return t.push(r),t;if(ut(r)){let n=ue(r,o);if(n)return t.push(n),t}return t},[]),k_=({accumulatedChange:e,originalElements:o,shouldChangeByStepSize:t,nextValue:r,scene:n})=>{let i=n.getNonDeletedElementsMap(),a=o.map(s=>i.get(s.id)),l;if(r){l=Math.max(Math.round(r),y1);for(let s of a)P(s,{fontSize:l},!1),Ue(s,n.getContainerElement(s),i,!1);n.triggerUpdate()}else{let s=o;for(let c=0;c<a.length;c++){let m=a[c],d=s[c],p=Math.round(d.fontSize),u=Math.round(e),h=Math.max(p+u,y1);t&&(h=Oe(h,I_)),P(m,{fontSize:h},!1),Ue(m,n.getContainerElement(m),i,!1)}n.triggerUpdate()}},L_=({elements:e,scene:o,appState:t,property:r,elementsMap:n})=>{let i=S_(e,n);if(!i.length)return null;let a=i.map(c=>Math.round(c.fontSize*10)/10),l=new Set(a).size===1?a[0]:"Mixed",s=a.length>0;return A_(at,{label:"F",icon:nn,elements:i,dragInputCallback:k_,value:l,editable:s,scene:o,property:r,appState:t})},v1=L_;import{jsx as D_}from"react/jsx-runtime";var w1=10,__=({accumulatedChange:e,instantChange:o,originalElements:t,originalElementsMap:r,shouldChangeByStepSize:n,nextValue:i,property:a,scene:l,originalAppState:s})=>{let c=l.getNonDeletedElementsMap(),m=l.getNonDeletedElements(),d=t[0],[p,u]=[d.x+d.width/2,d.y+d.height/2],[h,f]=bt(z(d.x,d.y),z(p,u),d.angle);if(s.croppingElementId===d.id){let w=c.get(d.id);if(!w||!xe(w)||!w.crop)return;let S=w.crop,I=S,_=w.scale[0]===-1,k=w.scale[1]===-1,{width:R,height:M}=mn(w);if(i!==void 0){if(a==="x"){let H=i*(S.naturalWidth/R);_?I={...S,x:ot(S.naturalWidth-H-S.width,0,S.naturalWidth-S.width)}:I={...S,x:ot(i*(S.naturalWidth/R),0,S.naturalWidth-S.width)}}a==="y"&&(I={...S,y:ot(i*(S.naturalHeight/M),0,S.naturalHeight-S.height)}),P(w,{crop:I});return}let N=(a==="x"?o:0)*(_?-1:1),G=(a==="y"?o:0)*(k?-1:1);I={...S,x:ot(S.x+N,0,S.naturalWidth-S.width),y:ot(S.y+G,0,S.naturalHeight-S.height)},P(w,{crop:I});return}if(i!==void 0){Ln(a==="x"?i:h,a==="y"?i:f,d,c,m,l,r);return}let b=a==="x"?e:0,x=a==="y"?e:0,T=a==="x"?Math.round(n?Oe(d.x+b,w1):h+b):h,E=a==="y"?Math.round(n?Oe(d.y+x,w1):f+x):f;Ln(T,E,d,c,m,l,r)},M_=({property:e,element:o,elementsMap:t,scene:r,appState:n})=>{let[i,a]=bt(z(o.x,o.y),z(o.x+o.width/2,o.y+o.height/2),o.angle),l=Xt(e==="x"?i:a,2);if(n.croppingElementId===o.id&&xe(o)&&o.crop){let s=Ix(o);s&&(l=Xt(e==="x"?s.x:s.y,2))}return D_(at,{label:e==="x"?"X":"Y",elements:[o],dragInputCallback:__,scene:r,value:l,property:e,appState:n})},xg=M_;import{useMemo as P_}from"react";import{jsx as z_}from"react/jsx-runtime";var R_=10,N_=(e,o,t,r,n,i,a,l)=>{for(let s=0;s<r.length;s++){let c=n[s],[m,d]=[c.x+c.width/2,c.y+c.height/2],[p,u]=bt(z(c.x,c.y),z(m,d),c.angle),h=e==="x"?Math.round(p+o):p,f=e==="y"?Math.round(u+t):u;Ln(h,f,c,i,r,l,a,!1)}},O_=(e,o,t,r,n,i,a)=>{let[l,s,,]=Se(t),c=e-l,m=o-s;for(let d=0;d<t.length;d++){let p=t[d],u=r.get(p.id);if(u&&(!Y(u)||!u.containerId)){let[h,f]=[u.x+u.width/2,u.y+u.height/2],[b,x]=bt(z(u.x,u.y),z(h,f),u.angle);Ln(b+c,x+m,p,r,n,a,i,!1)}}},F_=({accumulatedChange:e,originalElements:o,originalElementsMap:t,shouldChangeByStepSize:r,nextValue:n,property:i,scene:a,originalAppState:l})=>{let s=a.getNonDeletedElementsMap(),c=a.getNonDeletedElements();if(n!==void 0){for(let u of Xi(o,l)){let h=Ki(u,s,t);if(h.length>1){let[f,b,,]=Se(h.map(E=>E.latest));O_(i==="x"?n:f,i==="y"?n:b,h.map(E=>E.original),s,c,t,a)}else{let f=h[0]?.original,b=h[0]?.latest;if(f&&b&&Pt(b,i)){let[x,T]=[f.x+f.width/2,f.y+f.height/2],[E,w]=bt(z(f.x,f.y),z(x,T),f.angle);Ln(i==="x"?n:E,i==="y"?n:w,f,s,c,a,t,!1)}}}a.triggerUpdate();return}let m=r?Oe(e,R_):e;N_(i,i==="x"?m:0,i==="y"?m:0,o,o,s,t,a),a.triggerUpdate()},B_=({property:e,elements:o,elementsMap:t,atomicUnits:r,scene:n,appState:i})=>{let a=P_(()=>r.map(s=>{let c=Object.keys(s).map(f=>t.get(f)).filter(f=>f!==void 0);if(c.length>1){let[f,b]=Se(c);return Math.round((e==="x"?f:b)*100)/100}let[m]=c,[d,p]=[m.x+m.width/2,m.y+m.height/2],[u,h]=bt(z(m.x,m.y),z(d,p),m.angle);return Math.round((e==="x"?u:h)*100)/100}),[r,t,e]),l=new Set(a).size===1?a[0]:"Mixed";return z_(at,{label:e==="x"?"X":"Y",elements:o,dragInputCallback:F_,value:l,property:e,scene:n,appState:i})},Eg=B_;import{jsx as H_}from"react/jsx-runtime";var T1=5,U_=({property:e,scene:o,appState:t,setAppState:r})=>H_(at,{label:"Grid step",sensitivity:8,elements:[],dragInputCallback:({nextValue:n,instantChange:i,shouldChangeByStepSize:a,setInputValue:l})=>{r(s=>{let c;return n?c=n:i&&(c=a?Oe(s.gridStep+T1*Math.sign(i),T1):s.gridStep+i),c?(c=GE(c),l(c),{gridStep:c}):(l(s.gridStep),null)})},scene:o,value:t.gridStep,property:e,appState:t}),C1=U_;import W_ from"clsx";import{Fragment as vg,jsx as J,jsxs as Rt}from"react/jsx-runtime";var K_=50,ol=e=>{let o=Mo(),t=e.app.scene.getSceneNonce()||1,r=e.app.scene.getSelectedElements({selectedElementIds:o.selectedElementIds,includeBoundTextElement:!1}),n=gn(e.app);return J(X_,{...e,appState:o,sceneNonce:t,selectedElements:r,gridModeEnabled:n})},Pe=({children:e,columns:o=1,heading:t,style:r,...n})=>J("div",{className:W_("exc-stats__row",{"exc-stats__row--heading":t}),style:{gridTemplateColumns:`repeat(${o}, 1fr)`,...r},...n,children:e});Pe.displayName="StatsRow";var Sd=({children:e,order:o,style:t,...r})=>J("div",{className:"exc-stats__rows",style:{order:o,...t},...r,children:e});Sd.displayName="StatsRows";ol.StatsRow=Pe;ol.StatsRows=Sd;var X_=Y_(({app:e,onClose:o,renderCustomStats:t,selectedElements:r,appState:n,sceneNonce:i,gridModeEnabled:a})=>{let l=e.scene,s=l.getNonDeletedElements(),c=l.getNonDeletedElementsMap(),m=pe(),d=r.length===1?r[0]:null,p=r.length>1?r:null,u=n.croppingElementId&&xe(d),h=u?mn(d):null,[f,b]=G_({width:0,height:0}),x=yg(()=>V_(w=>{let S=Se(w);b({width:Math.round(S[2])-Math.round(S[0]),height:Math.round(S[3])-Math.round(S[1])})},K_),[]);I1(()=>{x(s)},[i,s,x]),I1(()=>()=>x.cancel(),[x]);let T=yg(()=>Xi(r,n),[r,n]),E=yg(()=>Fs(r),[r]);return J("div",{className:"exc-stats",children:Rt(Qe,{padding:3,children:[Rt("div",{className:"title",children:[J("h2",{children:g("stats.title")}),J("div",{className:"close",onClick:o,children:jt})]}),Rt(Ra,{label:J("h3",{children:g("stats.generalStats")}),open:!!(n.stats.panels&aa.generalStats),openTrigger:()=>m(w=>({stats:{open:!0,panels:w.stats.panels^aa.generalStats}})),children:[Rt(Sd,{children:[J(Pe,{heading:!0,children:g("stats.scene")}),Rt(Pe,{columns:2,children:[J("div",{children:g("stats.shapes")}),J("div",{children:s.length})]}),Rt(Pe,{columns:2,children:[J("div",{children:g("stats.width")}),J("div",{children:f.width})]}),Rt(Pe,{columns:2,children:[J("div",{children:g("stats.height")}),J("div",{children:f.height})]}),a&&Rt(vg,{children:[J(Pe,{heading:!0,children:"Canvas"}),J(Pe,{children:J(C1,{property:"gridStep",scene:l,appState:n,setAppState:m})})]})]}),t?.(s,n)]}),!E&&r.length>0&&J("div",{id:"elementStats",style:{marginTop:12},children:J(Ra,{label:J("h3",{children:g("stats.elementProperties")}),open:!!(n.stats.panels&aa.elementProperties),openTrigger:()=>m(w=>({stats:{open:!0,panels:w.stats.panels^aa.elementProperties}})),children:Rt(Sd,{children:[d&&Rt(vg,{children:[u&&J(Pe,{heading:!0,children:g("labels.unCroppedDimension")}),n.croppingElementId&&xe(d)&&h&&Rt(Pe,{columns:2,children:[J("div",{children:g("stats.width")}),J("div",{children:Xt(h.width,2)})]}),n.croppingElementId&&xe(d)&&h&&Rt(Pe,{columns:2,children:[J("div",{children:g("stats.height")}),J("div",{children:Xt(h.height,2)})]}),J(Pe,{heading:!0,"data-testid":"stats-element-type",children:n.croppingElementId?g("labels.imageCropping"):g(`element.${d.type}`)}),J(Pe,{children:J(xg,{element:d,property:"x",elementsMap:c,scene:l,appState:n})}),J(Pe,{children:J(xg,{element:d,property:"y",elementsMap:c,scene:l,appState:n})}),J(Pe,{children:J(fg,{property:"width",element:d,scene:l,appState:n})}),J(Pe,{children:J(fg,{property:"height",element:d,scene:l,appState:n})}),!ee(d)&&J(Pe,{children:J(h1,{property:"angle",element:d,scene:l,appState:n})}),J(Pe,{children:J(b1,{property:"fontSize",element:d,scene:l,appState:n})})]}),p&&Rt(vg,{children:[ff(p)&&J(Pe,{heading:!0,children:g("element.group")}),Rt(Pe,{columns:2,style:{margin:"0.3125rem 0"},children:[J("div",{children:g("stats.shapes")}),J("div",{children:r.length})]}),J(Pe,{children:J(Eg,{property:"x",elements:p,elementsMap:c,atomicUnits:T,scene:l,appState:n})}),J(Pe,{children:J(Eg,{property:"y",elements:p,elementsMap:c,atomicUnits:T,scene:l,appState:n})}),J(Pe,{children:J(bg,{property:"width",elements:p,elementsMap:c,atomicUnits:T,scene:l,appState:n})}),J(Pe,{children:J(bg,{property:"height",elements:p,elementsMap:c,atomicUnits:T,scene:l,appState:n})}),J(Pe,{children:J(E1,{property:"angle",elements:p,scene:l,appState:n})}),J(Pe,{children:J(v1,{property:"fontSize",elements:p,scene:l,appState:n,elementsMap:c})})]})]})})})]})})},(e,o)=>e.sceneNonce===o.sceneNonce&&e.selectedElements===o.selectedElements&&e.appState.stats.panels===o.appState.stats.panels&&e.gridModeEnabled===o.gridModeEnabled&&e.appState.gridStep===o.appState.gridStep&&e.appState.croppingElementId===o.appState.croppingElementId);import{useCallback as $_,useEffect as S1,useState as k1}from"react";import{jsx as Zi,jsxs as kd}from"react/jsx-runtime";var Z_=({sourceElementId:e,onClose:o,elementsMap:t,appState:r,generateLinkForSelection:n=Ss})=>{let i=t.get(e)?.link??null,[a,l]=k1(i),[s,c]=k1(!1);S1(()=>{let d=$(t,r),p=i;if(d.length>0&&n){let u=ks(d,r);u&&(p=uo(n(u.id,u.type)))}l(p)},[t,r,r.selectedElementIds,i,n]);let m=$_(()=>{if(a&&a!==t.get(e)?.link){let d=t.get(e);d&&P(d,{link:a})}if(!a&&s&&e){let d=t.get(e);d&&P(d,{link:null})}o?.()},[e,a,t,s,o]);return S1(()=>{let d=p=>{r.openDialog?.name==="elementLinkSelector"&&p.key===y.ENTER&&m(),r.openDialog?.name==="elementLinkSelector"&&p.key===y.ESCAPE&&o?.()};return window.addEventListener("keydown",d),()=>{window.removeEventListener("keydown",d)}},[r,o,m]),kd("div",{className:"ElementLinkDialog",children:[kd("div",{className:"ElementLinkDialog__header",children:[Zi("h2",{children:g("elementLink.title")}),Zi("p",{children:g("elementLink.desc")})]}),kd("div",{className:"ElementLinkDialog__input",children:[Zi(zi,{value:a??"",onChange:d=>{s||c(!0),l(d)},onKeyDown:d=>{d.key===y.ENTER&&m()},className:"ElementLinkDialog__input-field",selectOnRender:!0}),i&&a&&Zi(X,{type:"button",title:g("buttons.remove"),"aria-label":g("buttons.remove"),label:g("buttons.remove"),onClick:()=>{l(null),c(!0)},className:"ElementLinkDialog__remove",icon:Ot})]}),kd("div",{className:"ElementLinkDialog__actions",children:[Zi(or,{label:g("buttons.cancel"),onClick:()=>{o?.()},style:{marginRight:10}}),Zi(or,{label:g("buttons.confirm"),onClick:m,actionType:"primary"})]})]})},L1=Z_;import{Fragment as _1,jsx as Z,jsxs as Wt}from"react/jsx-runtime";var J_=({UIOptions:e})=>Wt(ft,{__fallback:!0,children:[Z(ft.DefaultItems.LoadScene,{}),Z(ft.DefaultItems.SaveToActiveFile,{}),e.canvasActions.export&&Z(ft.DefaultItems.Export,{}),e.canvasActions.saveAsImage&&Z(ft.DefaultItems.SaveAsImage,{}),Z(ft.DefaultItems.SearchMenu,{}),Z(ft.DefaultItems.Help,{}),Z(ft.DefaultItems.ClearCanvas,{}),Z(ft.Separator,{}),Z(ft.Group,{title:"Excalidraw links",children:Z(ft.DefaultItems.Socials,{})}),Z(ft.Separator,{}),Z(ft.DefaultItems.ToggleTheme,{}),Z(ft.DefaultItems.ChangeCanvasBackground,{})]}),q_=()=>Wt(pd,{__fallback:!0,children:[Z(pd.Actions.SaveToDisk,{}),Z(pd.Actions.ExportToImage,{})]}),Q_=({actionManager:e,appState:o,files:t,setAppState:r,elements:n,canvas:i,onLockToggle:a,onHandToolToggle:l,onPenModeToggle:s,showExitZenModeBtn:c,renderTopRightUI:m,renderCustomStats:d,UIOptions:p,onExportImage:u,renderWelcomeScreen:h,children:f,app:b,isCollaborating:x,generateLinkForSelection:T})=>{let E=me(),w=xw(),S=w.tunnelsJotai.Provider,[I,_]=ce(Sr),k=()=>p.canvasActions.export?Z(i0,{elements:n,appState:o,files:t,actionManager:e,exportOpts:p.canvasActions.export,canvas:i,setAppState:r}):null,R=()=>!p.canvasActions.saveAsImage||o.openDialog?.name!=="imageExport"?null:Z(Gw,{elements:n,appState:o,files:t,actionManager:e,onExportImage:u,onCloseRequest:()=>r({openDialog:null}),name:b.getName()}),M=()=>Wt("div",{style:{position:"relative"},children:[Z(w.MainMenuTunnel.Out,{}),h&&Z(w.WelcomeScreenMenuHintTunnel.Out,{})]}),N=()=>Z(Nr,{heading:"selectedShapeActions",className:rl("selected-shape-actions zen-mode-transition",{"transition-left":o.zenModeEnabled}),children:Z(Qe,{className:co.SHAPE_ACTIONS_MENU,padding:2,style:{maxHeight:`${o.height-166}px`},children:Z(Gc,{appState:o,elementsMap:b.scene.getNonDeletedElementsMap(),renderAction:e.renderAction,app:b})})}),G=()=>{let O=fi(o,n),j=o.stats.open&&!o.zenModeEnabled&&!o.viewModeEnabled&&o.openDialog?.name!=="elementLinkSelector";return Z(Jc,{side:"top",children:Wt("div",{className:"App-menu App-menu_top",children:[Wt(it.Col,{gap:6,className:rl("App-menu_top__left"),children:[M(),O&&N()]}),!o.viewModeEnabled&&o.openDialog?.name!=="elementLinkSelector"&&Z(Nr,{heading:"shapes",className:"shapes-section",children:oe=>Wt("div",{style:{position:"relative"},children:[h&&Z(w.WelcomeScreenToolbarHintTunnel.Out,{}),Z(it.Col,{gap:4,align:"start",children:Wt(it.Row,{gap:1,className:rl("App-toolbar-container",{"zen-mode":o.zenModeEnabled}),children:[Wt(Qe,{padding:1,className:rl("App-toolbar",{"zen-mode":o.zenModeEnabled}),children:[Z(ed,{appState:o,isMobile:E.editor.isMobile,device:E,app:b}),oe,Wt(it.Row,{gap:1,children:[Z(od,{zenModeEnabled:o.zenModeEnabled,checked:o.penMode,onChange:()=>s(null),title:g("toolBar.penMode"),penDetected:o.penDetected}),Z(td,{checked:o.activeTool.locked,onChange:a,title:g("toolBar.lock")}),Z("div",{className:"App-toolbar__divider"}),Z(rd,{checked:Xo(o),onChange:()=>l(),title:g("toolBar.hand"),isMobile:!0}),Z(Yc,{appState:o,activeTool:o.activeTool,UIOptions:p,app:b})]})]}),x&&Z(Qe,{style:{marginLeft:8,alignSelf:"center",height:"fit-content"},children:Z(X0,{title:g("toolBar.laser"),checked:o.activeTool.type===kt.laser,onChange:()=>b.setActiveTool({type:kt.laser}),isMobile:!0})})]})})]})}),Wt("div",{className:rl("layer-ui__wrapper__top-right zen-mode-transition",{"transition-right":o.zenModeEnabled}),children:[o.collaborators.size>0&&Z(ad,{collaborators:o.collaborators,userToFollow:o.userToFollow?.socketId||null}),m?.(E.editor.isMobile,o),!o.viewModeEnabled&&o.openDialog?.name!=="elementLinkSelector"&&(!V||o.openSidebar?.name!==pt.name)&&Z(w.DefaultSidebarTriggerTunnel.Out,{}),j&&Z(ol,{app:b,onClose:()=>{e.executeAction(Di)},renderCustomStats:d})]})]})})},H=()=>Z(ud,{__fallback:!0,onDock:O=>{le("sidebar",`toggleDock (${O?"dock":"undock"})`,`(${E.editor.isMobile?"mobile":"desktop"})`)}}),V=Ws(Ja),F=Wt(_1,{children:[f,Z(J_,{UIOptions:p}),Z(ud.Trigger,{__fallback:!0,icon:jn,title:mr(g("toolBar.library")),onToggle:O=>{O&&le("sidebar",`${pt.name} (open)`,`button (${E.editor.isMobile?"mobile":"desktop"})`)},tab:pt.defaultTab,children:g("toolBar.library")}),Z(q_,{}),o.openDialog?.name==="ttd"&&Z(gg,{__fallback:!0}),o.isLoading&&Z(Zs,{delay:250}),o.errorMessage&&Z(Fw,{onClose:()=>r({errorMessage:null}),children:o.errorMessage}),I&&!E.editor.isMobile&&Z(wy,{colorPickerType:I.colorPickerType,onCancel:()=>{_(null)},onChange:(O,j,oe,{altKey:W})=>{if(!(O!=="elementBackground"&&O!=="elementStroke"))if(oe.length){for(let ne of oe)P(ne,{[W&&I.swapPreviewOnAlt?O==="elementBackground"?"strokeColor":"backgroundColor":O==="elementBackground"?"backgroundColor":"strokeColor"]:j},!1),fr.delete(ne);bo.getScene(oe[0])?.triggerUpdate()}else r(O==="elementBackground"?{currentItemBackgroundColor:j}:{currentItemStrokeColor:j})},onSelect:(O,j)=>{_(oe=>oe?.keepOpenOnAlt&&j.altKey?oe:null),I?.onSelect?.(O,j)}}),o.openDialog?.name==="help"&&Z(o0,{onClose:()=>{r({openDialog:null})}}),Z(Tw,{}),o.openDialog?.name==="elementLinkSelector"&&Z(L1,{sourceElementId:o.openDialog.sourceElementId,onClose:()=>{r({openDialog:null})},elementsMap:b.scene.getNonDeletedElementsMap(),appState:o,generateLinkForSelection:T}),Z(w.OverwriteConfirmDialogTunnel.Out,{}),R(),k(),o.pasteDialog.shown&&Z(t0,{setAppState:r,appState:o,onClose:()=>r({pasteDialog:{shown:!1,data:null}})}),E.editor.isMobile&&Z(Qw,{app:b,appState:o,elements:n,actionManager:e,renderJSONExportDialog:k,renderImageExportDialog:R,setAppState:r,onLockToggle:a,onHandToolToggle:l,onPenModeToggle:s,renderTopRightUI:m,renderCustomStats:d,renderSidebars:H,device:E,renderWelcomeScreen:h,UIOptions:p}),!E.editor.isMobile&&Wt(_1,{children:[Wt("div",{className:"layer-ui__wrapper",style:o.openSidebar&&V&&E.editor.canFitSidebar?{width:"calc(100% - var(--right-sidebar-width))"}:{},children:[h&&Z(w.WelcomeScreenCenterTunnel.Out,{}),G(),Z(s0,{appState:o,actionManager:e,showExitZenModeBtn:c,renderWelcomeScreen:h}),o.scrolledOutside&&Z("button",{type:"button",className:"scroll-back-to-content",onClick:()=>{r(O=>({...fn(n,O)}))},children:g("buttons.scrollBackToContent")})]}),H()]})]});return Z(Ip.Provider,{value:o,children:Z(S,{children:Z(Iu.Provider,{value:w,children:F})})})},A1=e=>{let{suggestedBindings:o,startBoundElement:t,cursorButton:r,scrollX:n,scrollY:i,...a}=e;return a},e5=(e,o)=>{if(e.children!==o.children)return!1;let{canvas:t,appState:r,...n}=e,{canvas:i,appState:a,...l}=o;return rt(A1(r),A1(a),{selectedElementIds:rt,selectedGroupIds:rt})&&rt(n,l)},M1=j_.memo(Q_,e5);import{useCallback as t5,useEffect as o5,useRef as r5}from"react";import{jsx as D1,jsxs as i5}from"react/jsx-runtime";var n5=5e3,P1=({message:e,onClose:o,closable:t=!1,duration:r=n5,style:n})=>{let i=r5(0),a=r!==1/0,l=t5(()=>{a&&(i.current=window.setTimeout(()=>o(),r))},[o,r,a]);return o5(()=>{if(a)return l(),()=>clearTimeout(i.current)},[l,e,r,a]),i5("div",{className:"Toast",onMouseEnter:a?()=>clearTimeout(i?.current):void 0,onMouseLeave:a?l:void 0,style:n,children:[D1("p",{className:"Toast__message",children:e}),t&&D1(X,{icon:jt,"aria-label":"close",type:"icon",onClick:o,className:"close"})]})};var wg=D({name:"viewMode",label:"labels.viewMode",paletteName:"Toggle view mode",icon:oi,viewMode:!0,trackEvent:{category:"canvas",predicate:e=>!e.viewModeEnabled},perform(e,o){return{appState:{...o,viewModeEnabled:!this.checked(o)},captureUpdate:L.EVENTUALLY}},checked:e=>e.viewModeEnabled,predicate:(e,o,t)=>typeof t.viewModeEnabled>"u",keyTest:e=>!e[y.CTRL_OR_CMD]&&e.altKey&&e.code===te.R});import eM from"lodash.throttle";var R1=(e,o)=>{let t=o.scene.getSelectedElements(e);return t.length===1&&ie(t[0])},N1=D({name:"selectAllElementsInFrame",label:"labels.selectAllElementsInFrame",trackEvent:{category:"canvas"},perform:(e,o,t,r)=>{let n=r.scene.getSelectedElements(o).at(0)||null;if(ie(n)){let i=ko(se(e),n.id).filter(a=>!(a.type==="text"&&a.containerId));return{elements:e,appState:{...o,selectedElementIds:i.reduce((a,l)=>(a[l.id]=!0,a),{})},captureUpdate:L.IMMEDIATELY}}return{elements:e,appState:o,captureUpdate:L.EVENTUALLY}},predicate:(e,o,t,r)=>R1(o,r)}),O1=D({name:"removeAllElementsFromFrame",label:"labels.removeAllElementsFromFrame",trackEvent:{category:"history"},perform:(e,o,t,r)=>{let n=r.scene.getSelectedElements(o).at(0)||null;return ie(n)?{elements:aE(e,n),appState:{...o,selectedElementIds:{[n.id]:!0}},captureUpdate:L.IMMEDIATELY}:{elements:e,appState:o,captureUpdate:L.EVENTUALLY}},predicate:(e,o,t,r)=>R1(o,r)}),zse=D({name:"updateFrameRendering",label:"labels.updateFrameRendering",viewMode:!0,trackEvent:{category:"canvas"},perform:(e,o)=>({elements:e,appState:{...o,frameRendering:{...o.frameRendering,enabled:!o.frameRendering.enabled}},captureUpdate:L.EVENTUALLY}),checked:e=>e.frameRendering.enabled}),Use=D({name:"setFrameAsActiveTool",label:"toolBar.frame",trackEvent:{category:"toolbar"},icon:jl,viewMode:!1,perform:(e,o,t,r)=>{let n=Be(o,{type:"frame"});return go(r.interactiveCanvas,{...o,activeTool:n}),{elements:e,appState:{...o,activeTool:Be(o,{type:"frame"})},captureUpdate:L.EVENTUALLY}},keyTest:e=>!e[y.CTRL_OR_CMD]&&!e.shiftKey&&!e.altKey&&e.key.toLocaleLowerCase()===y.F}),F1=D({name:"wrapSelectionInFrame",label:"labels.wrapSelectionInFrame",trackEvent:{category:"element"},predicate:(e,o,t,r)=>{let n=$(e,o);return n.length>0&&!n.some(i=>ie(i))},perform:(e,o,t,r)=>{let n=$(e,o),[i,a,l,s]=Se(n,r.scene.getNonDeletedElementsMap()),c=16,m=ri({x:i-c,y:a-c,width:l-i+c*2,height:s-a+c*2});if(o.editingGroupId){let p=Le(n,o.editingGroupId);for(let u of p){let h=u.groupIds.indexOf(o.editingGroupId);P(u,{groupIds:u.groupIds.slice(0,h)},!1)}}return{elements:mi([...r.scene.getElementsIncludingDeleted(),m],n,m,o),appState:{selectedElementIds:{[m.id]:!0}},captureUpdate:L.IMMEDIATELY}}});import{jsx as io,jsxs as B1}from"react/jsx-runtime";var a5=()=>B1("div",{"data-testid":"brave-measure-text-error",children:[io("p",{children:io(Tt,{i18nKey:"errors.brave_measure_text_error.line1",bold:e=>io("span",{style:{fontWeight:600},children:e})})}),io("p",{children:io(Tt,{i18nKey:"errors.brave_measure_text_error.line2",bold:e=>io("span",{style:{fontWeight:600},children:e})})}),io("p",{children:io(Tt,{i18nKey:"errors.brave_measure_text_error.line3",link:e=>io("a",{href:"http://docs.excalidraw.com/docs/@excalidraw/excalidraw/faq#turning-off-aggresive-block-fingerprinting-in-brave-browser",children:e})})}),io("p",{children:io(Tt,{i18nKey:"errors.brave_measure_text_error.line4",issueLink:e=>io("a",{href:"https://github.com/excalidraw/excalidraw/issues/new",children:e}),discordLink:e=>B1("a",{href:"https://discord.gg/UexuTaE",children:[e,"."]})})})]}),z1=a5;var Ld={width:100,height:0},zr=100,l5=(e,o,t)=>{let r=So({x:0,y:0,textAlign:na.CENTER,verticalAlign:Kt.MIDDLE,...o,containerId:e.id,strokeColor:o.strokeColor||e.strokeColor});return Object.assign(e,{boundElements:(e.boundElements||[]).concat({type:"text",id:r.id})}),Ue(r,e,t),[e,r]},U1=(e,o,t,r,n)=>{let i,a;if(Object.assign(e,{startBinding:e?.startBinding||null,endBinding:e.endBinding||null}),o){let m=o?.width??zr,d=o?.height??zr,p;o.id&&(p=r.getElement(o.id),p||console.error(`No element for start binding with id ${o.id} found`));let u=o.x||e.x-m,h=o.y||e.y-d/2,f=p?p.type:o.type;if(f){if(f==="text"){let b="";p&&p.type==="text"?b=p.text:o.type==="text"&&(b=o.text),b||console.error(`No text found for start binding text element for ${e.id}`),i=So({x:u,y:h,type:"text",...p,...o,text:b}),Object.assign(i,{x:o.x||e.x-i.width,y:o.y||e.y-i.height/2})}else switch(f){case"rectangle":case"ellipse":case"diamond":{i=ho({x:u,y:h,width:m,height:d,...p,...o,type:f});break}default:Rl(e,`Unhandled element start type "${o.type}"`,!0)}Zo(e,i,"start",n)}}if(t){let m=t?.height??zr,d=t?.width??zr,p;t.id&&(p=r.getElement(t.id),p||console.error(`No element for end binding with id ${t.id} found`));let u=t.x||e.x+e.width,h=t.y||e.y-m/2,f=p?p.type:t.type;if(f){if(f==="text"){let b="";p&&p.type==="text"?b=p.text:t.type==="text"&&(b=t.text),b||console.error(`No text found for end binding text element for ${e.id}`),a=So({x:u,y:h,type:"text",...p,...t,text:b}),Object.assign(a,{y:t.y||e.y-a.height/2})}else switch(f){case"rectangle":case"ellipse":case"diamond":{a=ho({x:u,y:h,width:d,height:m,...p,...t,type:f});break}default:Rl(e,`Unhandled element end type "${f}"`,!0)}Zo(e,a,"end",n)}}if(e.points.length<2)return{linearElement:e,startBoundElement:i,endBoundElement:a};let l=e.points.length-1,s=.5,c=At(e.points);return e.points[l][0]>e.points[l-1][0]&&(c[0][0]=s,c[l][0]-=s),e.points[l][0]<e.points[l-1][0]&&(c[0][0]=-s,c[l][0]+=s),e.points[l][1]>e.points[l-1][1]&&(c[0][1]=s,c[l][1]-=s),e.points[l][1]<e.points[l-1][1]&&(c[0][1]=-s,c[l][1]+=s),Object.assign(e,{points:c}),{linearElement:e,startBoundElement:i,endBoundElement:a}},Tg=class{constructor(){C(this,"excalidrawElements",new Map);C(this,"add",o=>{o&&this.excalidrawElements.set(o.id,o)});C(this,"getElements",()=>Jo(Array.from(this.excalidrawElements.values())));C(this,"getElementsMap",()=>Vn(Q(this.getElements())));C(this,"getElement",o=>this.excalidrawElements.get(o))}},Yi=(e,o)=>{if(!e)return[];let t=At(e),r=new Tg,n=new Map,i=new Map;for(let l of t){let s,c=l.id;switch(o?.regenerateIds!==!1&&Object.assign(l,{id:gr()}),l.type){case"rectangle":case"ellipse":case"diamond":{let d=l?.label?.text&&l.width===void 0?0:l?.width||zr,p=l?.label?.text&&l.height===void 0?0:l?.height||zr;s=ho({...l,width:d,height:p});break}case"line":{let d=l.width||Ld.width,p=l.height||Ld.height;s=ns({width:d,height:p,points:[z(0,0),z(d,p)],...l});break}case"arrow":{let d=l.width||Ld.width,p=l.height||Ld.height;s=ni({width:d,height:p,endArrowhead:"arrow",points:[z(0,0),z(d,p)],...l,type:"arrow"}),Object.assign(s,Sx(s.points));break}case"text":{let d=l?.fontFamily||On,p=l?.fontSize||Nn,u=l?.lineHeight||$o(d),h=l.text??"",f=tn(h),b=Nt(f,lt({fontFamily:d,fontSize:p}),u);s=So({width:b.width,height:b.height,fontFamily:d,fontSize:p,...l});break}case"image":{s=is({width:l?.width||zr,height:l?.height||zr,...l});break}case"frame":{s=ri({x:0,y:0,...l});break}case"magicframe":{s=ba({x:0,y:0,...l});break}case"freedraw":case"iframe":case"embeddable":{s=l;break}default:s=l,Rl(l,`Unhandled element type "${l.type}"`,!0)}r.getElement(s.id)?console.error(`Duplicate id found for ${s.id}`):(r.add(s),n.set(s.id,l),c&&i.set(c,s.id))}let a=r.getElementsMap();for(let[l,s]of n){let c=r.getElement(l);switch(s.type){case"rectangle":case"ellipse":case"diamond":case"arrow":{if(s.label?.text){let[m,d]=l5(c,s?.label,a);if(r.add(m),r.add(d),Ne(m)){let p=s.type==="arrow"?s?.start:void 0,u=s.type==="arrow"?s?.end:void 0;if(p&&p.id){let x=i.get(p.id);x&&Object.assign(p,{id:x})}if(u&&u.id){let x=i.get(u.id);x&&Object.assign(u,{id:x})}let{linearElement:h,startBoundElement:f,endBoundElement:b}=U1(m,p,u,r,a);m=h,r.add(h),r.add(f),r.add(b)}}else switch(s.type){case"arrow":{let{start:m,end:d}=s;if(m&&m.id){let f=i.get(m.id);Object.assign(m,{id:f})}if(d&&d.id){let f=i.get(d.id);Object.assign(d,{id:f})}let{linearElement:p,startBoundElement:u,endBoundElement:h}=U1(c,m,d,r,a);r.add(p),r.add(u),r.add(h);break}}break}}}for(let[l,s]of n){if(s.type!=="frame"&&s.type!=="magicframe")continue;let c=r.getElement(l);if(!c)throw new Error(`Excalidraw element with id ${l} doesn't exist`);let m=[];s.children.forEach(w=>{let S=i.get(w);if(!S)throw new Error(`Element with ${w} wasn't mapped correctly`);let I=r.getElement(S);if(!I)throw new Error(`Frame element with id ${S} doesn't exist`);Object.assign(I,{frameId:c.id}),I?.boundElements?.forEach(_=>{let k=r.getElement(_.id);if(!k)throw new Error(`Bound element with id ${_.id} doesn't exist`);Object.assign(k,{frameId:c.id}),m.push(k)}),m.push(I)});let[d,p,u,h]=Se(m),f=10;d=d-f,p=p-f,u=u+f,h=h+f;let b=c?.x||d,x=c?.y||p,T=c?.width||u-d,E=c?.height||h-p;Object.assign(c,{x:b,y:x,width:T,height:E}),Dl()&&s.children.length&&(c?.x||c?.y||c?.width||c?.height)&&console.info("User provided frame attributes are being considered, if you find this inaccurate, please remove any of the attributes - x, y, width and height so frame coordinates and dimensions are calculated automatically")}return r.getElements()};import I5,{useEffect as S5,useRef as k5}from"react";import{unstable_batchedUpdates as H1}from"react-dom";import{version as s5}from"react";var Fe=e=>o=>{H1(e,o)},Ad=e=>Un(o=>{H1(e,o)}),ji=(()=>{let e;try{let t=s5.split(".");e=Number(t[0])>17}catch{e=!1}let o=!1;return()=>window.EXCALIDRAW_THROTTLE_RENDER===!0?e?!0:(o||(o=!0,console.warn("Excalidraw: render throttling is disabled on React versions < 18.")),!1):!1})();var c5="#ff6b6b",d5="#ff0000",G1=1,Y1=2,V1=(e,o)=>{if(!o.snapLines.length)return;let t=o.theme===re.LIGHT||o.zenModeEnabled?c5:d5,r=(o.zenModeEnabled?G1*1.5:G1)/o.zoom.value;e.save(),e.translate(o.scrollX,o.scrollY);for(let n of o.snapLines)n.type==="pointer"?(e.lineWidth=r,e.strokeStyle=t,p5(n,e,o)):n.type==="gap"?(e.lineWidth=r,e.strokeStyle=t,u5(n.points[0],n.points[1],n.direction,o,e)):n.type==="points"&&(e.lineWidth=r,e.strokeStyle=t,m5(n,e,o));e.restore()},m5=(e,o,t)=>{if(!t.zenModeEnabled){let r=e.points[0],n=e.points[e.points.length-1];ao(r,n,o)}for(let r of e.points)W1(r,t,o)},p5=(e,o,t)=>{W1(e.points[0],t,o),t.zenModeEnabled||ao(e.points[0],e.points[1],o)},W1=([e,o],t,r)=>{r.save();let n=(t.zenModeEnabled?Y1*1.5:Y1)/t.zoom.value;r.beginPath(),r.moveTo(e-n,o-n),r.lineTo(e+n,o+n),r.moveTo(e+n,o-n),r.lineTo(e-n,o+n),r.stroke(),r.restore()},ao=(e,o,t)=>{t.beginPath(),t.lineTo(e[0],e[1]),t.lineTo(o[0],o[1]),t.stroke()},u5=(e,o,t,r,n)=>{let i=8/r.zoom.value,a=i/2,l=i/4;if(t==="horizontal"){let s=[(e[0]+o[0])/2,e[1]];r.zenModeEnabled||ao(z(e[0],e[1]-i),z(e[0],e[1]+i),n),ao(z(s[0]-l,s[1]-a),z(s[0]-l,s[1]+a),n),ao(z(s[0]+l,s[1]-a),z(s[0]+l,s[1]+a),n),r.zenModeEnabled||(ao(z(o[0],o[1]-i),z(o[0],o[1]+i),n),ao(e,o,n))}else{let s=[e[0],(e[1]+o[1])/2];r.zenModeEnabled||ao(z(e[0]-i,e[1]),z(e[0]+i,e[1]),n),ao(z(s[0]-a,s[1]-l),z(s[0]+a,s[1]-l),n),ao(z(s[0]-a,s[1]+l),z(s[0]+a,s[1]+l),n),r.zenModeEnabled||(ao(z(o[0]-i,o[1]),z(o[0]+i,o[1]),n),ao(e,o,n))}};import _d from"open-color";var g5=(e,o)=>{Io(o.selectedLinearElement,"selectedLinearElement is null");let{segmentMidPointHoveredCoords:t}=o.selectedLinearElement;Io(t,"midPointCoords is null"),e.save(),e.translate(o.scrollX,o.scrollY),X1(t,e,o),e.restore()},h5=(e,o,t)=>{let{elementId:r,hoverPointIndex:n}=o.selectedLinearElement;if(o.editingLinearElement?.selectedPointsIndices?.includes(n))return;let i=K.getElement(r,t);if(!i)return;let a=K.getPointAtIndexGlobalCoordinates(i,n,t);e.save(),e.translate(o.scrollX,o.scrollY),X1(a,e,o),e.restore()},X1=(e,o,t)=>{o.fillStyle="rgba(105, 101, 219, 0.4)",Ta(o,e[0],e[1],K.POINT_HANDLE_SIZE/t.zoom.value,!1)},nl=(e,o,t,r,n,i,a,l,s=!1,c=0)=>{e.save(),e.translate(i,a),e.rotate(l),s&&e.fillRect(o-i,t-a,r,n),c&&e.roundRect?(e.beginPath(),e.roundRect(o-i,t-a,r,n,c),e.stroke(),e.closePath()):e.strokeRect(o-i,t-a,r,n),e.restore()},f5=(e,o,t,r,n,i)=>{e.save(),e.translate(r,n),e.rotate(i),e.beginPath(),e.moveTo(0,t/2),e.lineTo(o/2,0),e.lineTo(0,-t/2),e.lineTo(-o/2,0),e.closePath(),e.stroke(),e.restore()},Cg=(e,o,t,r,n,i=!1)=>{e.strokeStyle="#5e5ad8",e.setLineDash([]),e.fillStyle="rgba(255, 255, 255, 0.9)",n?e.fillStyle="rgba(134, 131, 226, 0.9)":i&&(e.fillStyle="rgba(177, 151, 252, 0.7)"),Ta(e,t[0],t[1],r/o.zoom.value,!i)},b5=(e,o,t,r,n,i)=>{e.beginPath(),e.ellipse(r,n,o/2,t/2,i,0,Math.PI*2),e.stroke()},x5=(e,o,t,r)=>{let[n,i,a,l]=yt(o,t),s=a-n,c=l-i;e.strokeStyle="rgba(0,0,0,.05)";let m=r.value<1?r.value:1;e.lineWidth=xx/m;let d=e.lineWidth/2+Ex,p=ps(Math.min(o.width,o.height),o);switch(o.type){case"rectangle":case"text":case"image":case"iframe":case"embeddable":case"frame":case"magicframe":nl(e,n-d,i-d,s+d*2,c+d*2,n+s/2,i+c/2,o.angle,void 0,p);break;case"diamond":let u=Math.hypot(s,c),h=d*u/c,f=d*u/s;f5(e,s+h*2,c+f*2,n+s/2,i+c/2,o.angle);break;case"ellipse":b5(e,s+d*2,c+d*2,n+s/2,i+c/2,o.angle);break}},E5=(e,o,t,r)=>{let[n,i,a]=o,l=vx(a,a.width,a.height,r);e.strokeStyle="rgba(0,0,0,0)",e.fillStyle="rgba(0,0,0,.05)",(i==="both"?[0,-1]:i==="start"?[0]:[-1]).forEach(c=>{let[m,d]=K.getPointAtIndexGlobalCoordinates(n,c,t);Ta(e,m,d,l)})},$1=(e,o,t)=>{let{angle:r,x1:n,y1:i,x2:a,y2:l,selectionColors:s,cx:c,cy:m,dashed:d,activeEmbeddable:p}=t,u=a-n,h=l-i,b=(t.padding??vl*2)/o.zoom.value,x=8/o.zoom.value,T=4/o.zoom.value;e.save(),e.translate(o.scrollX,o.scrollY),e.lineWidth=(p?4:1)/o.zoom.value;let E=s.length;for(let w=0;w<E;++w)e.strokeStyle=s[w],d&&e.setLineDash([x,T+(x+T)*(E-1)]),e.lineDashOffset=(x+T)*w,nl(e,n-b,i-b,u+b*2,h+b*2,c,m,r);e.restore()},y5=(e,o,t,r)=>{let n=Array.isArray(t)?E5:x5;e.save(),e.translate(o.scrollX,o.scrollY),n(e,t,r,o.zoom),e.restore()},v5=(e,o,t,r)=>{let[n,i,a,l]=yt(t,r),s=a-n,c=l-i;e.strokeStyle="rgb(0,118,255)",e.lineWidth=mo.strokeWidth/o.zoom.value,e.save(),e.translate(o.scrollX,o.scrollY),nl(e,n,i,s,c,n+s/2,i+c/2,t.angle,!1,mo.radius/o.zoom.value),e.restore()},w5=(e,o,t)=>{let r=t.filter(l=>l.groupIds.length===0),n=t.filter(l=>l.groupIds.length>0),i=l=>{let[s,c,m,d]=Se(l);return{angle:0,x1:s,x2:m,y1:c,y2:d,selectionColors:["rgb(0,118,255)"],dashed:!1,cx:s+(m-s)/2,cy:c+(d-c)/2,activeEmbeddable:!1}},a=l=>{let s=Le(t,l);return i(s)};Object.entries(mf(n,o)).filter(([l,s])=>s).map(([l,s])=>l).map(l=>a(l)).concat(r.map(l=>i([l]))).forEach(l=>$1(e,o,l))},Ig=(e,o,t,r)=>{if(!o.selectedLinearElement)return;e.save(),e.translate(o.scrollX,o.scrollY),e.lineWidth=1/o.zoom.value;let n=K.getPointsGlobalCoordinates(t,r),{POINT_HANDLE_SIZE:i}=K,a=o.editingLinearElement?i:i/2;if(n.forEach((l,s)=>{if(ee(t)&&s!==0&&s!==n.length-1)return;let c=!!o.editingLinearElement?.selectedPointsIndices?.includes(s);Cg(e,o,l,a,c)}),ee(t)){let l=t.fixedSegments?.map(s=>s.index)||[];n.slice(0,-1).forEach((s,c)=>{K.isSegmentTooShort(t,n[c+1],n[c],c,o.zoom)||Cg(e,o,z((s[0]+n[c+1][0])/2,(s[1]+n[c+1][1])/2),i/2,!1,!l.includes(c+1))})}else K.getEditorMidPoints(t,r,o).filter((s,c,m)=>s!==null&&!(ee(t)&&(c===0||c===m.length-1))).forEach(s=>{(o.editingLinearElement||n.length===2)&&Cg(e,o,s,i/2,!1,!0)});e.restore()},K1=(e,o,t,r,n)=>{Object.keys(r).forEach(i=>{let a=r[i];if(a!==void 0){let[l,s,c,m]=a;e.save(),e.lineWidth=1/t.zoom.value,o.selectionColor&&(e.strokeStyle=o.selectionColor),i==="rotation"?Ta(e,l+c/2,s+m/2,c/2):e.roundRect?(e.beginPath(),e.roundRect(l,s,c,m,2/t.zoom.value),e.fill(),e.stroke()):nl(e,l,s,c,m,l+c/2,s+m/2,n,!0),e.restore()}})},T5=(e,o,t,r,n)=>{let[i,a,,,l,s]=yt(r,n),c=3,m=20,d=c/t.zoom.value,p=d/2,u=l-i+d,h=s-a+d,f=Math.min(m/t.zoom.value,u),b=Math.min(m/t.zoom.value,h);e.save(),e.fillStyle=o.selectionColor,e.strokeStyle=o.selectionColor,e.lineWidth=d,[[[-u,-h],[0,p],[f,p],[p,0],[p,b]],[[u-p,-h],[p,p],[-f+p,p],[0,0],[0,b]],[[-u,h],[0,-p],[f,-p],[p,0],[p,-b]],[[u-p,h],[p,-p],[-f+p,-p],[0,0],[0,-b]]].forEach(T=>{let[[E,w],[S,I],[_,k],[R,M],[N,G]]=T;e.save(),e.translate(l,s),e.rotate(r.angle),e.beginPath(),e.moveTo(E+S,w+I),e.lineTo(E+_,w+k),e.stroke(),e.beginPath(),e.moveTo(E+R,w+M),e.lineTo(E+N,w+G),e.stroke(),e.restore()}),e.restore()},C5=(e,o,t,r)=>{o.save();let n=vl*2/t.zoom.value,i=e.width+n*2,a=e.height+n*2,l=e.x+i/2,s=e.y+a/2,c=-(i/2+n),m=-(a/2+n);o.translate(l+t.scrollX,s+t.scrollY),o.rotate(e.angle),o.lineWidth=1/t.zoom.value,o.strokeStyle=r,o.strokeRect(c,m,i,a),o.restore()},Z1=({canvas:e,elementsMap:o,visibleElements:t,selectedElements:r,allElementsMap:n,scale:i,appState:a,renderConfig:l,device:s})=>{if(e===null)return{atLeastOneVisibleElement:!1,elementsMap:o};let[c,m]=Cs(e,i),d=Is({canvas:e,scale:i,normalizedWidth:c,normalizedHeight:m});d.save(),d.scale(a.zoom.value,a.zoom.value);let p;if(t.forEach(f=>{a.editingLinearElement?.elementId===f.id&&f&&(p=f)}),p&&Ig(d,a,p,o),a.selectionElement&&!a.isCropping)try{cE(a.selectionElement,d,a,l.selectionColor)}catch(f){console.error(f)}if(a.editingTextElement&&Y(a.editingTextElement)){let f=n.get(a.editingTextElement.id);f&&!f.autoResize&&C5(f,d,a,l.selectionColor)}a.isBindingEnabled&&a.suggestedBindings.filter(f=>f!=null).forEach(f=>{y5(d,a,f,o)}),a.frameToHighlight&&v5(d,a,a.frameToHighlight,o),a.elementsToHighlight&&w5(d,a,a.elementsToHighlight);let u=r.some(f=>ie(f));if(r.length===1&&a.editingLinearElement?.elementId===r[0].id&&Ig(d,a,r[0],o),ee(r[0])&&a.selectedLinearElement&&a.selectedLinearElement.segmentMidPointHoveredCoords?g5(d,a):a.selectedLinearElement&&a.selectedLinearElement.hoverPointIndex>=0&&!(ee(r[0])&&a.selectedLinearElement.hoverPointIndex>0&&a.selectedLinearElement.hoverPointIndex<r[0].points.length-1)&&h5(d,a,o),!a.multiElement&&!a.editingLinearElement){let f=Gs(r,a),b=r.length===1&&he(r[0]);b&&a.selectedLinearElement?.elementId===r[0].id&&!r[0].locked&&Ig(d,a,r[0],o);let x=l.selectionColor||_d.black;if(f){let T=Q(r),E=[];for(let S of o.values()){let I=[],_=l.remoteSelectedElementIds.get(S.id);if(b&&ee(S)&&(S.startBinding||S.endBinding)||(T.has(S.id)&&!Fl(a,S)&&I.push(x),_&&I.push(..._.map(k=>Tn(k,a.collaborators.get(k))))),I.length){let[k,R,M,N,G,H]=yt(S,o,!0);E.push({angle:S.angle,x1:k,y1:R,x2:M,y2:N,selectionColors:I,dashed:!!_,cx:G,cy:H,activeEmbeddable:a.activeEmbeddable?.element===S&&a.activeEmbeddable.state==="active",padding:S.id===a.croppingElementId||xe(S)?0:void 0})}}let w=S=>{let I=Le(o,S),[_,k,R,M]=Se(I);E.push({angle:0,x1:_,x2:R,y1:k,y2:M,selectionColors:[_d.black],dashed:!0,cx:_+(R-_)/2,cy:k+(M-k)/2,activeEmbeddable:!1})};for(let S of po(a))w(S);a.editingGroupId&&w(a.editingGroupId),E.forEach(S=>$1(d,a,S))}if(d.save(),d.translate(a.scrollX,a.scrollY),r.length===1){d.fillStyle=_d.white;let T=ME(r[0],a.zoom,o,"mouse",Hs(s));if(!a.viewModeEnabled&&f&&!Y(a.editingTextElement)&&!a.croppingElementId&&K1(d,l,a,T,r[0].angle),a.croppingElementId&&!a.isCropping){let E=o.get(a.croppingElementId);E&&xe(E)&&T5(d,l,a,E,o)}}else if(r.length>1&&!a.isRotating){let T=vl*2/a.zoom.value;d.fillStyle=_d.white;let[E,w,S,I]=Se(r),_=d.getLineDash();d.setLineDash([2/a.zoom.value]);let k=d.lineWidth;d.lineWidth=1/a.zoom.value,d.strokeStyle=x,nl(d,E-T,w-T,S-E+T*2,I-w+T*2,(E+S)/2,(w+I)/2,0),d.lineWidth=k,d.setLineDash(_);let R=_E([E,w,S,I,(E+S)/2,(w+I)/2],0,a.zoom,"mouse",u?{...Hs(s),rotation:!0}:Hs(s));r.some(M=>!M.locked)&&K1(d,l,a,R,0)}d.restore()}a.searchMatches.forEach(({id:f,focus:b,matchedLines:x})=>{let T=o.get(f);if(T&&Y(T)){let[E,w,,,S,I]=yt(T,o,!0);d.save(),a.theme===re.LIGHT?b?d.fillStyle="rgba(255, 124, 0, 0.4)":d.fillStyle="rgba(255, 226, 0, 0.4)":b?d.fillStyle="rgba(229, 82, 0, 0.4)":d.fillStyle="rgba(99, 52, 0, 0.4)",d.translate(a.scrollX,a.scrollY),d.translate(S,I),d.rotate(T.angle),x.forEach(_=>{d.fillRect(E+_.offsetX-S,w+_.offsetY-I,_.width,_.height)}),d.restore()}}),V1(d,a),d.restore(),sv({context:d,renderConfig:l,appState:a,normalizedWidth:c,normalizedHeight:m});let h;return l.renderScrollbars&&(h=Jw(t,c,m,a),d.save(),d.fillStyle=jw,d.strokeStyle="rgba(255,255,255,0.8)",[h.horizontal,h.vertical].forEach(f=>{f&&xc(d,f.x,f.y,f.width,f.height,Oo/2)}),d.restore()),{scrollBars:h,atLeastOneVisibleElement:t.length>0,elementsMap:o}},Sg=Un(e=>{let o=Z1(e);e.callback?.(o)},{trailing:!0}),j1=(e,o)=>{if(o){Sg(e);return}let t=Z1(e);return e.callback(t),t};import{jsx as _5}from"react/jsx-runtime";var L5=e=>{let o=k5(!1);return S5(()=>{if(!o.current){o.current=!0;return}let t=new Map,r=new Map,n=new Map,i=new Map,a=new Map;e.appState.collaborators.forEach((s,c)=>{if(s.selectedElementIds)for(let m of Object.keys(s.selectedElementIds))n.has(m)||n.set(m,[]),n.get(m).push(c);!s.pointer||s.pointer.renderCursor===!1||(s.username&&i.set(c,s.username),s.userState&&a.set(c,s.userState),r.set(c,xt({sceneX:s.pointer.x,sceneY:s.pointer.y},e.appState)),t.set(c,s.button))});let l=e.containerRef?.current&&getComputedStyle(e.containerRef.current).getPropertyValue("--color-selection")||"#6965db";j1({canvas:e.canvas,elementsMap:e.elementsMap,visibleElements:e.visibleElements,selectedElements:e.selectedElements,allElementsMap:e.allElementsMap,scale:window.devicePixelRatio,appState:e.appState,renderConfig:{remotePointerViewportCoords:r,remotePointerButton:t,remoteSelectedElementIds:n,remotePointerUsernames:i,remotePointerUserStates:a,selectionColor:l,renderScrollbars:!1},device:e.device,callback:e.renderInteractiveSceneCallback},ji())}),_5("canvas",{className:"excalidraw__canvas interactive",style:{width:e.appState.width,height:e.appState.height,cursor:e.appState.viewModeEnabled?we.GRAB:we.AUTO},width:e.appState.width*e.scale,height:e.appState.height*e.scale,ref:e.handleCanvasRef,onContextMenu:e.onContextMenu,onPointerMove:e.onPointerMove,onPointerUp:e.onPointerUp,onPointerCancel:e.onPointerCancel,onTouchMove:e.onTouchMove,onPointerDown:e.onPointerDown,onDoubleClick:e.appState.viewModeEnabled?void 0:e.onDoubleClick,children:g("labels.drawingCanvas")})},J1=e=>({zoom:e.zoom,scrollX:e.scrollX,scrollY:e.scrollY,width:e.width,height:e.height,viewModeEnabled:e.viewModeEnabled,openDialog:e.openDialog,editingGroupId:e.editingGroupId,editingLinearElement:e.editingLinearElement,selectedElementIds:e.selectedElementIds,frameToHighlight:e.frameToHighlight,offsetLeft:e.offsetLeft,offsetTop:e.offsetTop,theme:e.theme,pendingImageElementId:e.pendingImageElementId,selectionElement:e.selectionElement,selectedGroupIds:e.selectedGroupIds,selectedLinearElement:e.selectedLinearElement,multiElement:e.multiElement,isBindingEnabled:e.isBindingEnabled,suggestedBindings:e.suggestedBindings,isRotating:e.isRotating,elementsToHighlight:e.elementsToHighlight,collaborators:e.collaborators,activeEmbeddable:e.activeEmbeddable,snapLines:e.snapLines,zenModeEnabled:e.zenModeEnabled,editingTextElement:e.editingTextElement,isCropping:e.isCropping,croppingElementId:e.croppingElementId,searchMatches:e.searchMatches}),A5=(e,o)=>e.selectionNonce!==o.selectionNonce||e.sceneNonce!==o.sceneNonce||e.scale!==o.scale||e.elementsMap!==o.elementsMap||e.visibleElements!==o.visibleElements||e.selectedElements!==o.selectedElements?!1:rt(J1(e.appState),J1(o.appState)),kg=I5.memo(L5,A5);import M5,{useEffect as D5,useRef as q1}from"react";import{jsx as N5}from"react/jsx-runtime";var P5=e=>{let o=q1(null),t=q1(!1);return D5(()=>{let r=o.current;if(!r)return;let n=e.canvas;t.current||(t.current=!0,r.replaceChildren(n),n.classList.add("excalidraw__canvas","static"));let i=`${e.appState.width}px`,a=`${e.appState.height}px`;n.style.width!==i&&(n.style.width=i),n.style.height!==a&&(n.style.height=a);let l=e.appState.width*e.scale,s=e.appState.height*e.scale;n.width!==l&&(n.width=l),n.height!==s&&(n.height=s),Kx({canvas:n,rc:e.rc,scale:e.scale,elementsMap:e.elementsMap,allElementsMap:e.allElementsMap,visibleElements:e.visibleElements,appState:e.appState,renderConfig:e.renderConfig},ji())}),N5("div",{className:"excalidraw__canvas-wrapper",ref:o})},Q1=e=>({zoom:e.zoom,scrollX:e.scrollX,scrollY:e.scrollY,width:e.width,height:e.height,viewModeEnabled:e.viewModeEnabled,openDialog:e.openDialog,hoveredElementIds:e.hoveredElementIds,offsetLeft:e.offsetLeft,offsetTop:e.offsetTop,theme:e.theme,pendingImageElementId:e.pendingImageElementId,shouldCacheIgnoreZoom:e.shouldCacheIgnoreZoom,viewBackgroundColor:e.viewBackgroundColor,exportScale:e.exportScale,selectedElementsAreBeingDragged:e.selectedElementsAreBeingDragged,gridSize:e.gridSize,gridStep:e.gridStep,frameRendering:e.frameRendering,selectedElementIds:e.selectedElementIds,frameToHighlight:e.frameToHighlight,editingGroupId:e.editingGroupId,currentHoveredFontFamily:e.currentHoveredFontFamily,croppingElementId:e.croppingElementId}),R5=(e,o)=>e.sceneNonce!==o.sceneNonce||e.scale!==o.scale||e.elementsMap!==o.elementsMap||e.visibleElements!==o.visibleElements?!1:rt(Q1(e.appState),Q1(o.appState))&&rt(e.renderConfig,o.renderConfig),Lg=M5.memo(P5,R5);var il=class{constructor(o){C(this,"scene");C(this,"getRenderableElements",(()=>{let o=({elementsMap:r,zoom:n,offsetLeft:i,offsetTop:a,scrollX:l,scrollY:s,height:c,width:m})=>{let d=[];for(let p of r.values())ka(p,m,c,{zoom:n,offsetLeft:i,offsetTop:a,scrollX:l,scrollY:s},r)&&d.push(p);return d},t=({elements:r,editingTextElement:n,newElementId:i,pendingImageElementId:a})=>{let l=Vn(new Map);for(let s of r)xe(s)&&a===s.id||i!==s.id&&(!n||n.type!=="text"||s.id!==n.id)&&l.set(s.id,s);return l};return Wh(({zoom:r,offsetLeft:n,offsetTop:i,scrollX:a,scrollY:l,height:s,width:c,editingTextElement:m,newElementId:d,pendingImageElementId:p,sceneNonce:u})=>{let h=this.scene.getNonDeletedElements(),f=t({elements:h,editingTextElement:m,newElementId:d,pendingImageElementId:p}),b=o({elementsMap:f,zoom:r,offsetLeft:n,offsetTop:i,scrollX:a,scrollY:l,height:s,width:c});return{elementsMap:f,visibleElements:b}})})());this.scene=o}destroy(){Sg.cancel(),Wx.cancel(),this.getRenderableElements.clear()}};import{useEffect as O5,useRef as F5}from"react";import{jsx as eT}from"react/jsx-runtime";var tT=({trails:e})=>{let o=F5(null);return O5(()=>{if(o.current)for(let t of e)t.start(o.current);return()=>{for(let t of e)t.stop()}},e),eT("div",{className:"SVGLayer",children:eT("svg",{ref:o})})};import{jsx as U5}from"react/jsx-runtime";var B5=5,z5=(e,o,t)=>{let[r,n]=yt(e,t),{x:i,y:a}=xt({sceneX:r+e.width,sceneY:n},o),l=i-o.offsetLeft+10,s=a-o.offsetTop;return{x:l,y:s}},Ag=({children:e,element:o,elementsMap:t})=>{let r=Mo();if(r.contextMenu||r.newElement||r.resizingElement||r.isRotating||r.openMenu||r.viewModeEnabled)return null;let{x:n,y:i}=z5(o,r,t);return U5("div",{className:"excalidraw-canvas-buttons",style:{top:`${i}px`,left:`${n}px`,padding:B5},children:e})};import H5 from"clsx";import{jsx as oT,jsxs as Y5}from"react/jsx-runtime";var G5="small",Md=e=>Y5("label",{className:H5("ToolIcon ToolIcon__MagicButton",`ToolIcon_size_${G5}`,{"is-mobile":e.isMobile}),title:`${e.title}`,children:[oT("input",{className:"ToolIcon_type_checkbox",type:"checkbox",name:e.name,onChange:e.onChange,checked:e.checked,"aria-label":e.title}),oT("div",{className:"ToolIcon__icon",children:e.icon})]});import{jsx as _g,jsxs as rT}from"react/jsx-runtime";var V5=({height:e,width:o,userToFollow:t,onDisconnect:r})=>_g("div",{className:"follow-mode",style:{width:o,height:e},children:rT("div",{className:"follow-mode__badge",children:[rT("div",{className:"follow-mode__badge__label",children:["Following"," ",_g("span",{className:"follow-mode__badge__username",title:t.username,children:t.username})]}),_g("button",{type:"button",onClick:r,className:"follow-mode__disconnect-btn",children:jt})]})}),nT=V5;var Dd=class{constructor(){C(this,"targets",new WeakMap);C(this,"rafIds",new WeakMap)}register(o,t){this.targets.set(o,{callback:t,stopped:!0})}start(o){let t=this.targets.get(o);t&&(this.rafIds.has(o)||(this.targets.set(o,{...t,stopped:!1}),this.scheduleFrame(o)))}stop(o){let t=this.targets.get(o);t&&!t.stopped&&this.targets.set(o,{...t,stopped:!0}),this.cancelFrame(o)}constructFrame(o){return t=>{let r=this.targets.get(o);if(!r)return;let n=this.onFrame(r,t);!r.stopped&&!n?this.scheduleFrame(o):this.cancelFrame(o)}}scheduleFrame(o){let t=requestAnimationFrame(this.constructFrame(o));this.rafIds.set(o,t)}cancelFrame(o){if(this.rafIds.has(o)){let t=this.rafIds.get(o);cancelAnimationFrame(t)}this.rafIds.delete(o)}onFrame(o,t){return o.callback(t)??!1}};import{LaserPointer as W5}from"@excalidraw/laser-pointer";var An=class{constructor(o,t,r){this.animationFrameHandler=o;this.app=t;this.options=r;C(this,"currentTrail");C(this,"pastTrails",[]);C(this,"container");C(this,"trailElement");this.animationFrameHandler.register(this,this.onFrame.bind(this)),this.trailElement=document.createElementNS(Ah,"path")}get hasCurrentTrail(){return!!this.currentTrail}hasLastPoint(o,t){if(this.currentTrail){let r=this.currentTrail.originalPoints.length;return this.currentTrail.originalPoints[r-1][0]===o&&this.currentTrail.originalPoints[r-1][1]===t}return!1}start(o){o&&(this.container=o),this.trailElement.parentNode!==this.container&&this.container&&this.container.appendChild(this.trailElement),this.animationFrameHandler.start(this)}stop(){this.animationFrameHandler.stop(this),this.trailElement.parentNode===this.container&&this.container?.removeChild(this.trailElement)}startPath(o,t){this.currentTrail=new W5(this.options),this.currentTrail.addPoint([o,t,performance.now()]),this.update()}addPointToPath(o,t){this.currentTrail&&(this.currentTrail.addPoint([o,t,performance.now()]),this.update())}endPath(){this.currentTrail&&(this.currentTrail.close(),this.currentTrail.options.keepHead=!1,this.pastTrails.push(this.currentTrail),this.currentTrail=void 0,this.update())}update(){this.start()}onFrame(){let o=[];for(let r of this.pastTrails)o.push(this.drawTrail(r,this.app.state));if(this.currentTrail){let r=this.drawTrail(this.currentTrail,this.app.state);o.push(r)}this.pastTrails=this.pastTrails.filter(r=>r.getStrokeOutline().length!==0),o.length===0&&this.stop();let t=o.join(" ").trim();this.trailElement.setAttribute("d",t),this.trailElement.setAttribute("fill",(this.options.fill??(()=>"black"))(this))}drawTrail(o,t){let r=o.getStrokeOutline(o.options.size/t.zoom.value).map(([n,i])=>{let a=xt({sceneX:n,sceneY:i},t);return[a.x,a.y]});return Xh(r,!0)}};var Pd=class{constructor(o,t){this.animationFrameHandler=o;this.app=t;C(this,"localTrail");C(this,"collabTrails",new Map);C(this,"container");this.animationFrameHandler.register(this,this.onFrame.bind(this)),this.localTrail=new An(o,t,{...this.getTrailOptions(),fill:()=>Yd})}getTrailOptions(){return{simplify:0,streamline:.4,sizeMapping:o=>{let n=Math.max(0,1-(performance.now()-o.pressure)/1e3),i=(50-Math.min(50,o.totalLength-o.currentIndex))/50;return Math.min(Zr(i),Zr(n))}}}startPath(o,t){this.localTrail.startPath(o,t)}addPointToPath(o,t){this.localTrail.addPointToPath(o,t)}endPath(){this.localTrail.endPath()}start(o){this.container=o,this.animationFrameHandler.start(this),this.localTrail.start(o)}stop(){this.animationFrameHandler.stop(this),this.localTrail.stop()}onFrame(){this.updateCollabTrails()}updateCollabTrails(){if(!(!this.container||this.app.state.collaborators.size===0)){for(let[o,t]of this.app.state.collaborators.entries()){let r;this.collabTrails.has(o)?r=this.collabTrails.get(o):(r=new An(this.animationFrameHandler,this.app,{...this.getTrailOptions(),fill:()=>t.pointer?.laserColor||Tn(o,t)}),r.start(this.container),this.collabTrails.set(o,r)),t.pointer&&t.pointer.tool==="laser"&&(t.button==="down"&&!r.hasCurrentTrail&&r.startPath(t.pointer.x,t.pointer.y),t.button==="down"&&r.hasCurrentTrail&&!r.hasLastPoint(t.pointer.x,t.pointer.y)&&r.addPointToPath(t.pointer.x,t.pointer.y),t.button==="up"&&r.hasCurrentTrail&&(r.addPointToPath(t.pointer.x,t.pointer.y),r.endPath()))}for(let o of this.collabTrails.keys())this.app.state.collaborators.has(o)||(this.collabTrails.get(o).stop(),this.collabTrails.delete(o))}}};var K5=(e,o,t,r,n,i)=>{let{zoom:a}=r,l=180*t/Math.PI,s=e*(a.value-1)/2,c=o*(a.value-1)/2;return e>n&&a.value!==1&&(s=n*(a.value-1)/2),o>i&&a.value!==1&&(c=i*(a.value-1)/2),`translate(${s}px, ${c}px) scale(${a.value}) rotate(${l}deg)`},iT=({id:e,onChange:o,onSubmit:t,getViewportCoords:r,element:n,canvas:i,excalidrawContainer:a,app:l,autoSelect:s=!0})=>{let c=(F,O)=>{if(!O.style.fontFamily||!O.style.fontSize)return!1;let j=O.style.fontFamily.replace(/"/g,"");return Xr({fontFamily:F.fontFamily})!==j||`${F.fontSize}px`!==O.style.fontSize},m=()=>{let F=l.state,O=bo.getScene(n)?.getElement(e);if(!O)return;let{textAlign:j,verticalAlign:oe}=O,W=l.scene.getNonDeletedElementsMap();if(O&&Y(O)){let ne=O.x,ge=O.y,be=Qt(O,l.scene.getNonDeletedElementsMap()),mt=O.width,wo=O.height,Dn=O.width,Pn=O.height;if(be&&O.containerId){if(Ne(be)){let lr=K.getBoundTextElementPosition(be,O,W);ne=lr.x,ge=lr.y}let gl=c(O,d),qi;if(gl?qi=Sa(be.id,be.height):(qi=bE[be.id],qi||(qi=Sa(be.id,be.height))),Dn=qm(be,O),Pn=SE(be,O),!Ne(be)&&wo>Pn){let lr=ui(wo,be.type);P(be,{height:lr});return}else if(!Ne(be)&&be.height>qi.height&&wo<Pn){let lr=ui(wo,be.type);P(be,{height:lr})}else{let{y:lr}=zs(be,O,W);ge=lr}}let[pl,ul]=r(ne,ge),_T=d.selectionStart,Ud=d.selectionEnd,Jg=d.value.length;if(_T===Ud&&Ud!==Jg){let gl=Jg-Ud;d.selectionStart=d.value.length-gl,d.selectionEnd=d.value.length-gl}be?mt+=.5:(Dn=(F.width-8-pl)/F.zoom.value,mt=Math.min(mt,Dn)),wo*=1.05;let MT=lt(O),qg=(F.height-ul)/F.zoom.value;Object.assign(d.style,{font:MT,lineHeight:O.lineHeight,width:`${mt}px`,height:`${wo}px`,left:`${pl}px`,top:`${ul}px`,transform:K5(mt,wo,wE(O,be),F,Dn,qg),textAlign:j,verticalAlign:oe,color:O.strokeColor,opacity:O.opacity/100,filter:"var(--theme-filter)",maxHeight:`${qg}px`}),d.scrollTop=0,sa()&&(d.style.fontFamily=Xr(O)),P(O,{x:ne,y:ge})}},d=document.createElement("textarea");d.dir="auto",d.tabIndex=0,d.dataset.type="wysiwyg",d.wrap="off",d.classList.add("excalidraw-wysiwyg");let p="pre",u="normal";(qe(n)||!n.autoResize)&&(p="pre-wrap",u="break-word"),Object.assign(d.style,{position:"absolute",display:"inline-block",minHeight:"1em",backfaceVisibility:"hidden",margin:0,padding:0,border:0,outline:0,resize:"none",background:"transparent",overflow:"hidden",zIndex:"var(--zIndex-wysiwyg)",wordBreak:u,whiteSpace:p,overflowWrap:"break-word",boxSizing:"content-box"}),d.value=n.originalText,m(),o&&(d.onpaste=async F=>{let O=await _s(F,!0);if(!O.text)return;let j=tn(O.text);if(!j)return;let oe=Qt(n,l.scene.getNonDeletedElementsMap()),W=lt({fontSize:l.state.currentItemFontSize,fontFamily:l.state.currentItemFontFamily});if(oe){let ne=ue(oe,l.scene.getNonDeletedElementsMap()),ge=Ol(`${d.value}${j}`,W,qm(oe,ne)),be=sf(ge,W);d.style.width=`${be}px`}},d.oninput=()=>{let F=tn(d.value);if(d.value!==F){let O=d.selectionStart;d.value=F,d.selectionStart=O,d.selectionEnd=O}o(d.value)}),d.onkeydown=F=>{if(!F.shiftKey&&pc.keyTest(F))F.preventDefault(),l.actionManager.executeAction(pc),m();else if(!F.shiftKey&&uc.keyTest(F))F.preventDefault(),l.actionManager.executeAction(uc),m();else if(!F.shiftKey&&gc.keyTest(F))F.preventDefault(),l.actionManager.executeAction(gc),m();else if(Up.keyTest(F))l.actionManager.executeAction(Up);else if(Hp.keyTest(F))l.actionManager.executeAction(Hp);else if(F.key===y.ESCAPE)F.preventDefault(),S=!0,I();else if(F.key===y.ENTER&&F[y.CTRL_OR_CMD]){if(F.preventDefault(),F.isComposing||F.keyCode===229)return;S=!0,I()}else if(F.key===y.TAB||F[y.CTRL_OR_CMD]&&(F.code===te.BRACKET_LEFT||F.code===te.BRACKET_RIGHT)){if(F.preventDefault(),F.isComposing)return;F.shiftKey||F.code===te.BRACKET_LEFT?T():x(),d.dispatchEvent(new Event("input"))}};let h=4,f=" ".repeat(h),b=new RegExp(`^ {1,${h}}`),x=()=>{let{selectionStart:F,selectionEnd:O}=d,j=E(),oe=d.value;j.forEach(W=>{let ne=oe.slice(0,W),ge=oe.slice(W);oe=`${ne}${f}${ge}`}),d.value=oe,d.selectionStart=F+h,d.selectionEnd=O+h*j.length},T=()=>{let{selectionStart:F,selectionEnd:O}=d,j=E(),oe=[],W=d.value;j.forEach(ne=>{let ge=W.slice(ne,ne+h).match(b);if(ge){let be=W.slice(0,ne),mt=W.slice(ne+ge[0].length);W=`${be}${mt}`,oe.push(ne)}}),d.value=W,oe.length&&(F>oe[oe.length-1]?d.selectionStart=Math.max(F-h,oe[oe.length-1]):d.selectionStart=F,d.selectionEnd=Math.max(d.selectionStart,O-h*oe.length))},E=()=>{let{selectionStart:F,selectionEnd:O,value:j}=d,oe=j.slice(0,F).match(/[^\n]*$/)[0].length;return F=F-oe,j.slice(F,O).split(`
`).reduce((ne,ge,be,mt)=>ne.concat(be?ne[be-1]+mt[be-1].length+1:F),[]).reverse()},w=F=>{F.target instanceof HTMLCanvasElement&&(F.preventDefault(),F.stopPropagation())},S=!1,I=()=>{if(H)return;H=!0,_();let F=bo.getScene(n)?.getElement(n.id);if(!F)return;let O=Qt(F,l.scene.getNonDeletedElementsMap());if(O){if(d.value.trim()){let j=vE(O);!j||j!==n.id?P(O,{boundElements:(O.boundElements||[]).concat({type:"text",id:n.id})}):Ne(O)&&ep(O)}else P(O,{boundElements:O.boundElements?.filter(j=>!Y(j))});Ue(F,O,l.scene.getNonDeletedElementsMap())}t({viaKeyboard:S,nextOriginalText:d.value})},_=()=>{d.onblur=null,d.oninput=null,d.onkeydown=null,V&&V.disconnect(),window.removeEventListener("resize",m),window.removeEventListener("wheel",w,!0),window.removeEventListener("pointerdown",M),window.removeEventListener("pointerup",k),window.removeEventListener("blur",I),window.removeEventListener("beforeunload",I),N(),G(),d.remove()},k=F=>{window.removeEventListener("pointerup",k);let O=F?.target,j=O instanceof HTMLElement&&O.classList.contains("properties-trigger");setTimeout(()=>{d.onblur=I,j||d.focus()})},R=()=>{d.onblur=null,window.addEventListener("pointerup",k),window.addEventListener("blur",I)},M=F=>{let O=F?.target;if(F.button===To.WHEEL){O instanceof HTMLTextAreaElement&&(F.preventDefault(),l.handleCanvasPanUsingWheelOrSpaceDrag(F)),R();return}let j=O instanceof HTMLElement&&O.classList.contains("properties-trigger");(F.target instanceof HTMLElement||F.target instanceof SVGElement)&&F.target.closest(`.${co.SHAPE_ACTIONS_MENU}, .${co.ZOOM_ACTIONS}`)&&!Co(F.target)||j?R():F.target instanceof HTMLCanvasElement&&!sa()&&requestAnimationFrame(()=>{I()})},N=l.scene.onUpdate(()=>{m(),!!document.activeElement?.closest(".properties-content")||d.focus()}),G=l.onScrollChangeEmitter.on(()=>{m()}),H=!1;s&&d.select(),k();let V=null;i&&"ResizeObserver"in window?(V=new window.ResizeObserver(()=>{m()}),V.observe(i)):window.addEventListener("resize",m),d.onpointerdown=F=>F.stopPropagation(),requestAnimationFrame(()=>{window.addEventListener("pointerdown",M,{capture:!0})}),window.addEventListener("beforeunload",I),a?.querySelector(".excalidraw-textEditorContainer").appendChild(d)};var aT=D({name:"autoResize",label:"labels.autoResize",icon:null,trackEvent:{category:"element"},predicate:(e,o,t,r)=>{let n=$(e,o);return n.length===1&&Y(n[0])&&!n[0].autoResize},perform:(e,o,t,r)=>{let n=$(e,o);return{appState:o,elements:e.map(i=>{if(i.id===n[0].id&&Y(i)){let a=Nt(i.originalText,lt(i),i.lineHeight);return q(i,{autoResize:!0,width:a.width,height:a.height,text:i.originalText})}return i}),captureUpdate:L.IMMEDIATELY}}});var lT=e=>{let o=["flowchart","graph","sequenceDiagram","classDiagram","stateDiagram","stateDiagram-v2","erDiagram","journey","gantt","pie","quadrantChart","requirementDiagram","gitGraph","C4Context","mindmap","timeline","zenuml","sankey","xychart","block"];return new RegExp(`^(?:%%{.*?}%%[\\s\\n]*)?\\b(?:${o.map(r=>`\\s*${r}(-beta)?`).join("|")})\\b`).test(e.trim())};import{useEffect as $5,useRef as Z5}from"react";var sT=({canvas:e,rc:o,newElement:t,elementsMap:r,allElementsMap:n,scale:i,appState:a,renderConfig:l})=>{if(e){let[s,c]=Cs(e,i),m=Is({canvas:e,scale:i,normalizedWidth:s,normalizedHeight:c});m.save(),m.scale(a.zoom.value,a.zoom.value),t&&t.type!=="selection"?dE(t,r,n,o,m,l,a):m.clearRect(0,0,s,c)}},X5=Un(e=>{sT(e)},{trailing:!0}),cT=(e,o)=>{if(o){X5(e);return}sT(e)};import{jsx as J5}from"react/jsx-runtime";var j5=e=>{let o=Z5(null);return $5(()=>{o.current&&cT({canvas:o.current,scale:e.scale,newElement:e.appState.newElement,elementsMap:e.elementsMap,allElementsMap:e.allElementsMap,rc:e.rc,renderConfig:e.renderConfig,appState:e.appState},ji())}),J5("canvas",{className:"excalidraw__canvas",style:{width:e.appState.width,height:e.appState.height},width:e.appState.width*e.scale,height:e.appState.height*e.scale,ref:o})},dT=j5;import{Fragment as oM,jsx as Ie,jsxs as Nd}from"react/jsx-runtime";var gT=Uo.createContext(null),hT=Uo.createContext(null),fT={viewport:{isMobile:!1,isLandscape:!1},editor:{isMobile:!1,canFitSidebar:!1},isTouchScreen:!1},Og=Uo.createContext(fT);Og.displayName="DeviceContext";var Fg=Uo.createContext({container:null,id:null});Fg.displayName="ExcalidrawContainerContext";var Bg=Uo.createContext([]);Bg.displayName="ExcalidrawElementsContext";var zg=Uo.createContext({...$n(),width:0,height:0,offsetLeft:0,offsetTop:0});zg.displayName="ExcalidrawAppStateContext";var Ug=Uo.createContext(()=>{console.warn("Uninitialized ExcalidrawSetAppStateContext context!")});Ug.displayName="ExcalidrawSetAppStateContext";var Hg=Uo.createContext(null);Hg.displayName="ExcalidrawActionManagerContext";var He=()=>Hr(gT),Do=()=>Hr(hT),me=()=>Hr(Og),Ke=()=>Hr(Fg),kr=()=>Hr(Bg),Mo=()=>Hr(zg),pe=()=>Hr(Ug),Xe=()=>Hr(Hg),al=!1,Mg=0,Mn=!1,ll=!1,Dg=!1,Pg={horizontal:null,vertical:null},Ur=0,sl=!1,Rg=new Map,Rd=!1,pT=0,uT=!1,cl=null,fe={pointers:new Map,lastCenter:null,initialDistance:null,initialScale:null},Ng=class e extends Uo.Component{constructor(t){super(t);C(this,"canvas");C(this,"interactiveCanvas",null);C(this,"rc");C(this,"unmounted",!1);C(this,"actionManager");C(this,"device",fT);C(this,"excalidrawContainerRef",Uo.createRef());C(this,"scene");C(this,"fonts");C(this,"renderer");C(this,"visibleElements");C(this,"resizeObserver");C(this,"nearestScrollableContainer");C(this,"library");C(this,"libraryItemsFromStorage");C(this,"id");C(this,"store");C(this,"history");C(this,"excalidrawContainerValue");C(this,"files",{});C(this,"imageCache",new Map);C(this,"iFrameRefs",new Map);C(this,"embedsValidationStatus",new Map);C(this,"initializedEmbeds",new Set);C(this,"elementsPendingErasure",new Set);C(this,"flowChartCreator",new Qc);C(this,"flowChartNavigator",new qc);C(this,"hitLinkElement");C(this,"lastPointerDownEvent",null);C(this,"lastPointerUpEvent",null);C(this,"lastPointerMoveEvent",null);C(this,"lastPointerMoveCoords",null);C(this,"lastViewportPosition",{x:0,y:0});C(this,"animationFrameHandler",new Dd);C(this,"laserTrails",new Pd(this.animationFrameHandler,this));C(this,"eraserTrail",new An(this.animationFrameHandler,this,{streamline:.2,size:5,keepHead:!0,sizeMapping:t=>{let i=Math.max(0,1-(performance.now()-t.pressure)/200),a=(10-Math.min(10,t.totalLength-t.currentIndex))/10;return Math.min(Zr(a),Zr(i))},fill:()=>this.state.theme===re.LIGHT?"rgba(0, 0, 0, 0.2)":"rgba(255, 255, 255, 0.2)"}));C(this,"onChangeEmitter",new qt);C(this,"onPointerDownEmitter",new qt);C(this,"onPointerUpEmitter",new qt);C(this,"onUserFollowEmitter",new qt);C(this,"onScrollChangeEmitter",new qt);C(this,"missingPointerEventCleanupEmitter",new qt);C(this,"onRemoveEventListenersEmitter",new qt);C(this,"getEffectiveGridSize",()=>gn(this)?this.state.gridSize:null);C(this,"updateEmbedValidationStatus",(t,r)=>{this.embedsValidationStatus.set(t.id,r),fr.delete(t)});C(this,"updateEmbeddables",()=>{let t=new Set,r=!1;this.scene.getNonDeletedElements().filter(n=>{if(Et(n)){if(t.add(n.id),!this.embedsValidationStatus.has(n.id)){r=!0;let i=li(n.link,this.props.validateEmbeddable);this.updateEmbedValidationStatus(n,i)}}else Qr(n)&&t.add(n.id);return!1}),r&&this.scene.triggerUpdate(),this.iFrameRefs.forEach((n,i)=>{t.has(i)||this.iFrameRefs.delete(i)})});C(this,"getFrameNameDOMId",t=>`${this.id}-frame-name-${t.id}`);C(this,"frameNameBoundsCache",{get:t=>{let r=this.frameNameBoundsCache._cache.get(t.id);if(!r||r.zoom!==this.state.zoom.value||r.versionNonce!==t.versionNonce){let n=document.getElementById(this.getFrameNameDOMId(t));if(n){let i=n.getBoundingClientRect(),a=Re({clientX:i.x,clientY:i.y},this.state),l=Re({clientX:i.right,clientY:i.bottom},this.state);return r={x:a.x,y:a.y,width:l.x-a.x,height:l.y-a.y,angle:0,zoom:this.state.zoom.value,versionNonce:t.versionNonce},this.frameNameBoundsCache._cache.set(t.id,r),r}return null}return r},_cache:new Map});C(this,"resetEditingFrame",t=>{t&&P(t,{name:t.name?.trim()||null}),this.setState({editingFrame:null})});C(this,"renderFrameNames",()=>{if(!this.state.frameRendering.enabled||!this.state.frameRendering.name)return this.state.editingFrame&&this.resetEditingFrame(null),null;let t=this.state.theme===re.DARK;return this.scene.getNonDeletedFramesLikes().map(r=>{if(!ka(r,this.canvas.width/window.devicePixelRatio,this.canvas.height/window.devicePixelRatio,{offsetLeft:this.state.offsetLeft,offsetTop:this.state.offsetTop,scrollX:this.state.scrollX,scrollY:this.state.scrollY,zoom:this.state.zoom},this.scene.getNonDeletedElementsMap()))return this.state.editingFrame===r.id&&this.resetEditingFrame(r),null;let{x:n,y:i}=xt({sceneX:r.x,sceneY:r.y},this.state),a=6,l,s=lE(r);if(r.id===this.state.editingFrame){let c=s;l=Ie("input",{autoFocus:!0,value:c,onChange:m=>{P(r,{name:m.target.value})},onFocus:m=>m.target.select(),onBlur:()=>this.resetEditingFrame(r),onKeyDown:m=>{(m.key===y.ESCAPE||m.key===y.ENTER)&&this.resetEditingFrame(r)},style:{background:this.state.viewBackgroundColor,filter:t?Th:"none",zIndex:2,border:"none",display:"block",padding:`${a}px`,borderRadius:4,boxShadow:"inset 0 0 0 1px var(--color-primary)",fontFamily:"Assistant",fontSize:"14px",transform:`translate(-${a}px, ${a}px)`,color:"var(--color-gray-80)",overflow:"hidden",maxWidth:`${document.body.clientWidth-n-a}px`},size:c.length+1||1,dir:"auto",autoComplete:"off",autoCapitalize:"off",autoCorrect:"off"})}else l=s;return Ie("div",{id:this.getFrameNameDOMId(r),style:{position:"absolute",bottom:`${this.state.height+mo.nameOffsetY-i+this.state.offsetTop}px`,left:`${n-this.state.offsetLeft}px`,zIndex:2,fontSize:mo.nameFontSize,color:t?mo.nameColorDarkTheme:mo.nameColorLightTheme,lineHeight:mo.nameLineHeight,width:"max-content",maxWidth:`${r.width}px`,overflow:r.id===this.state.editingFrame?"visible":"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",cursor:we.MOVE,pointerEvents:this.state.viewModeEnabled?Wr.disabled:Wr.enabled},onPointerDown:c=>this.handleCanvasPointerDown(c),onWheel:c=>this.handleWheel(c),onContextMenu:this.handleCanvasContextMenu,onDoubleClick:()=>{this.setState({editingFrame:r.id})},children:l},r.id)})});C(this,"focusContainer",()=>{this.excalidrawContainerRef.current?.focus()});C(this,"getSceneElementsIncludingDeleted",()=>this.scene.getElementsIncludingDeleted());C(this,"getSceneElements",()=>this.scene.getNonDeletedElements());C(this,"onInsertElements",t=>{this.addElementsFromPasteOrLibrary({elements:t,position:"center",files:null})});C(this,"onExportImage",async(t,r,n)=>{le("export",t,"ui");let i=await vn(t,r,this.state,this.files,{exportBackground:this.state.exportBackground,name:this.getName(),viewBackgroundColor:this.state.viewBackgroundColor,exportingFrame:n.exportingFrame}).catch(_l).catch(a=>{console.error(a),this.setState({errorMessage:a.message})});this.state.exportEmbedScene&&i&&xs(i)&&this.setState({fileHandle:i})});C(this,"magicGenerations",new Map);C(this,"updateMagicGeneration",({frameElement:t,data:r})=>{r.status==="pending"?P(t,{customData:{generationData:void 0}},!1):P(t,{customData:{generationData:r}},!1),this.magicGenerations.set(t.id,r),this.triggerRender()});C(this,"plugins",{});C(this,"onMagicframeToolSelect",()=>{let t=this.scene.getSelectedElements({selectedElementIds:this.state.selectedElementIds});if(t.length===0)this.setActiveTool({type:kt.magicframe}),le("ai","tool-select (empty-selection)","d2c");else{let r=t.length===1&&Nl(t[0])&&t[0];if(!r&&t.some(i=>ie(i)||i.frameId)){this.setActiveTool({type:kt.magicframe});return}le("ai","tool-select (existing selection)","d2c");let n;if(r)n=r;else{let[i,a,l,s]=Se(t),c=50;n=ba({...mo,x:i-c,y:a-c,width:l-i+c*2,height:s-a+c*2,opacity:100,locked:!1}),this.scene.insertElement(n);for(let m of t)P(m,{frameId:n.id});this.setState({selectedElementIds:{[n.id]:!0}})}this.onMagicFrameGenerate(n,"upstream")}});C(this,"openEyeDropper",({type:t})=>{st.set(Sr,{swapPreviewOnAlt:!0,colorPickerType:t==="stroke"?"elementStroke":"elementBackground",onSelect:(r,n)=>{let i=t==="background"&&n.altKey||t==="stroke"&&!n.altKey;!this.scene.getSelectedElements(this.state).length||this.state.activeTool.type!=="selection"?i?this.syncActionResult({appState:{...this.state,currentItemStrokeColor:r},captureUpdate:L.IMMEDIATELY}):this.syncActionResult({appState:{...this.state,currentItemBackgroundColor:r},captureUpdate:L.IMMEDIATELY}):this.updateScene({elements:this.scene.getElementsIncludingDeleted().map(l=>this.state.selectedElementIds[l.id]?q(l,{[i?"strokeColor":"backgroundColor"]:r}):l),captureUpdate:L.IMMEDIATELY})},keepOpenOnAlt:!1})});C(this,"dismissLinearEditor",()=>{setTimeout(()=>{this.setState({editingLinearElement:null})})});C(this,"syncActionResult",Fe(t=>{if(this.unmounted||t===!1)return;t.captureUpdate===L.NEVER?this.store.shouldUpdateSnapshot():t.captureUpdate===L.IMMEDIATELY&&this.store.shouldCaptureIncrement();let r=!1,n=null;if(t.elements&&(this.scene.replaceAllElements(t.elements),r=!0),t.files&&(this.addMissingFiles(t.files,t.replaceFiles),this.addNewImagesToImageCache()),t.appState||n||this.state.contextMenu){let i=t?.appState?.viewModeEnabled||!1,a=t?.appState?.zenModeEnabled||!1,l=t?.appState?.theme||this.props.theme||re.LIGHT,s=t?.appState?.name??this.state.name,c=t?.appState?.errorMessage??this.state.errorMessage;typeof this.props.viewModeEnabled<"u"&&(i=this.props.viewModeEnabled),typeof this.props.zenModeEnabled<"u"&&(a=this.props.zenModeEnabled),n=t.appState?.editingTextElement||null,t.elements&&n&&t.elements.forEach(m=>{n?.id===m.id&&n!==m&&sp(m)&&Y(m)&&(n=m)}),n?.isDeleted&&(n=null),this.setState(m=>{let d=t.appState||{};return{...m,...d,contextMenu:null,editingTextElement:n,viewModeEnabled:i,zenModeEnabled:a,theme:l,name:s,errorMessage:c}}),r=!0}!r&&t.captureUpdate!==L.EVENTUALLY&&this.scene.triggerUpdate()}));C(this,"onBlur",Fe(()=>{Mn=!1,this.setState({isBindingEnabled:!0})}));C(this,"onUnload",()=>{this.onBlur()});C(this,"disableEvent",t=>{t.preventDefault()});C(this,"resetHistory",()=>{this.history.clear()});C(this,"resetStore",()=>{this.store.clear()});C(this,"resetScene",Fe(t=>{this.scene.replaceAllElements([]),this.setState(r=>({...$n(),isLoading:t?.resetLoadingState?!1:r.isLoading,theme:this.state.theme})),this.resetStore(),this.resetHistory()}));C(this,"initializeScene",async()=>{"launchQueue"in window&&"LaunchParams"in window&&window.launchQueue.setConsumer(async n=>{if(!n.files.length)return;let i=n.files[0],a=await i.getFile();this.loadFileToCanvas(new File([a],a.name||"",{type:a.type}),i)}),this.props.theme&&this.setState({theme:this.props.theme}),this.state.isLoading||this.setState({isLoading:!0});let t=null;try{typeof this.props.initialData=="function"?t=await this.props.initialData()||null:t=await this.props.initialData||null,t?.libraryItems&&this.library.updateLibrary({libraryItems:t.libraryItems,merge:!0}).catch(n=>{console.error(n)})}catch(n){console.error(n),t={appState:{errorMessage:n.message||"Encountered an error during importing or restoring scene data"}}}let r=Rm(t,null,null,{repairBindings:!0});r.appState={...r.appState,theme:this.props.theme||r.appState.theme,openSidebar:r.appState?.openSidebar||this.state.openSidebar,activeTool:r.appState.activeTool.type==="image"?{...r.appState.activeTool,type:"selection"}:r.appState.activeTool,isLoading:!1,toast:this.state.toast},t?.scrollToContent&&(r.appState={...r.appState,...fn(r.elements,{...r.appState,width:this.state.width,height:this.state.height,offsetTop:this.state.offsetTop,offsetLeft:this.state.offsetLeft})}),this.resetStore(),this.resetHistory(),this.syncActionResult({...r,captureUpdate:L.NEVER}),this.clearImageShapeCache(),this.fonts.loadSceneFonts().then(n=>{this.fonts.onLoaded(n)}),pn(window.location.href)&&this.scrollToContent(window.location.href,{animate:!1})});C(this,"isMobileBreakpoint",(t,r)=>t<Ch||r<Sh&&t<Ih);C(this,"refreshViewportBreakpoints",()=>{if(!this.excalidrawContainerRef.current)return;let{clientWidth:r,clientHeight:n}=document.body,i=this.device.viewport,a=Hn(i,{isLandscape:r>n,isMobile:this.isMobileBreakpoint(r,n)});return i!==a?(this.device={...this.device,viewport:a},!0):!1});C(this,"refreshEditorBreakpoints",()=>{let t=this.excalidrawContainerRef.current;if(!t)return;let{width:r,height:n}=t.getBoundingClientRect(),i=this.props.UIOptions.dockedSidebarBreakpoint!=null?this.props.UIOptions.dockedSidebarBreakpoint:kh,a=this.device.editor,l=Hn(a,{isMobile:this.isMobileBreakpoint(r,n),canFitSidebar:r>i});return a!==l?(this.device={...this.device,editor:l},!0):!1});C(this,"onResize",Fe(()=>{this.scene.getElementsIncludingDeleted().forEach(t=>fr.delete(t)),this.refreshViewportBreakpoints(),this.updateDOMRect(),Qi||this.refreshEditorBreakpoints(),this.setState({})}));C(this,"onFullscreenChange",()=>{!document.fullscreenElement&&this.state.activeEmbeddable?.state==="active"&&this.setState({activeEmbeddable:null})});C(this,"renderInteractiveSceneCallback",({atLeastOneVisibleElement:t,scrollBars:r,elementsMap:n})=>{r&&(Pg=r);let i=this.state.editingTextElement?!1:!t&&n.size>0;this.state.scrolledOutside!==i&&this.setState({scrolledOutside:i}),this.scheduleImageRefresh()});C(this,"onScroll",$r(()=>{let{offsetTop:t,offsetLeft:r}=this.getCanvasOffsets();this.setState(n=>n.offsetLeft===r&&n.offsetTop===t?null:{offsetTop:t,offsetLeft:r})},vh));C(this,"onCut",Fe(t=>{!this.excalidrawContainerRef.current?.contains(document.activeElement)||Co(t.target)||(this.actionManager.executeAction(yc,"keyboard",t),t.preventDefault(),t.stopPropagation())}));C(this,"onCopy",Fe(t=>{!this.excalidrawContainerRef.current?.contains(document.activeElement)||Co(t.target)||(this.actionManager.executeAction(_i,"keyboard",t),t.preventDefault(),t.stopPropagation())}));C(this,"onTouchStart",t=>{if(ch&&t.preventDefault(),!al){al=!0,clearTimeout(Mg),Mg=window.setTimeout(e.resetTapTwice,Eh);return}if(al&&t.touches.length===1){let r=t.touches[0];this.handleCanvasDoubleClick({clientX:r.clientX,clientY:r.clientY}),al=!1,clearTimeout(Mg)}t.touches.length===2&&this.setState({selectedElementIds:Ee({},this.state),activeEmbeddable:null})});C(this,"onTouchEnd",t=>{this.resetContextMenuTimer(),t.touches.length>0?this.setState({previousSelectedElementIds:{},selectedElementIds:Ee(this.state.previousSelectedElementIds,this.state)}):fe.pointers.clear()});C(this,"pasteFromClipboard",Fe(async t=>{let r=!!Rd,n=document.activeElement,i=this.excalidrawContainerRef.current?.contains(n);if(t&&!i)return;let a=document.elementFromPoint(this.lastViewportPosition.x,this.lastViewportPosition.y);if(t&&(!(a instanceof HTMLCanvasElement)||Co(n)))return;let{x:l,y:s}=Re({clientX:this.lastViewportPosition.x,clientY:this.lastViewportPosition.y},this.state),c=t?.clipboardData?.files[0],m=await _s(t,r);if(!c&&!r){if(m.mixedContent)return this.addElementsFromMixedContentPaste(m.mixedContent,{isPlainPaste:r,sceneX:l,sceneY:s});if(m.text){let d=m.text.trim();d.startsWith("<svg")&&d.endsWith("</svg>")&&(c=Fm(d))}}if(Es(c)&&!m.spreadsheet){if(!this.isToolSupported("image")){this.setState({errorMessage:g("errors.imageToolNotSupported")});return}let d=this.createImageElement({sceneX:l,sceneY:s});this.insertImageElement(d,c),this.initializeImageDimensions(d),this.setState({selectedElementIds:Ee({[d.id]:!0},this.state)});return}if(this.props.onPaste)try{if(await this.props.onPaste(m,t)===!1)return}catch(d){console.error(d)}if(m.errorMessage)this.setState({errorMessage:m.errorMessage});else if(m.spreadsheet&&!r)this.setState({pasteDialog:{data:m.spreadsheet,shown:!0}});else if(m.elements){let d=m.programmaticAPI?Yi(m.elements):m.elements;this.addElementsFromPasteOrLibrary({elements:d,files:m.files||null,position:"cursor",retainSeed:r})}else if(m.text){if(m.text&&lT(m.text)){let u=await import("@excalidraw/mermaid-to-excalidraw");try{let{elements:h,files:f}=await u.parseMermaidToExcalidraw(m.text),b=Yi(h,{regenerateIds:!0});this.addElementsFromPasteOrLibrary({elements:b,files:f,position:"cursor"});return}catch(h){console.warn(`parsing pasted text as mermaid definition failed: ${h.message}`)}}let d=$h(m.text).split(/\n+/).map(u=>u.trim()).filter(Boolean),p=d.map(u=>Tx(u)).filter(u=>li(u,this.props.validateEmbeddable)&&(/^(http|https):\/\/[^\s/$.?#].[^\s]*$/.test(u)||dn(u)?.type==="video"));if(!Rd&&p.length>0&&p.length===d.length){let u=[];for(let h of p){let f=u[u.length-1],b=this.insertEmbeddableElement({sceneX:f?f.x+f.width+20:l,sceneY:s,link:uo(h)});b&&u.push(b)}u.length&&this.setState({selectedElementIds:Object.fromEntries(u.map(h=>[h.id,!0]))});return}this.addTextFromPaste(m.text,r)}this.setActiveTool({type:"selection"}),t?.preventDefault()}));C(this,"addElementsFromPasteOrLibrary",t=>{let r=Pm(t.elements,null,void 0),[n,i,a,l]=Se(r),s=jr(n,a)/2,c=jr(i,l)/2,m=typeof t.position=="object"?t.position.clientX:t.position==="cursor"?this.lastViewportPosition.x:this.state.width/2+this.state.offsetLeft,d=typeof t.position=="object"?t.position.clientY:t.position==="cursor"?this.lastViewportPosition.y:this.state.height/2+this.state.offsetTop,{x:p,y:u}=Re({clientX:m,clientY:d},this.state),h=p-s,f=u-c,[b,x]=nt(h,f,this.getEffectiveGridSize()),T=ls(r.map(k=>q(k,{x:k.x+b-n,y:k.y+x-i})),{randomizeSeed:!t.retainSeed}),E=this.scene.getElementsIncludingDeleted(),w=[...E,...T];w=this.props.onDuplicate?.(w,E)||w,fo(w,Q(T));let I=this.getTopLayerFrameAtSceneCoords({x:p,y:u});if(I){let k=iE(T,I);mi(w,k,I,this.state)}this.scene.replaceAllElements(w),T.forEach(k=>{if(Y(k)&&qe(k)){let R=Qt(k,this.scene.getElementsMapIncludingDeleted());Ue(k,R,this.scene.getElementsMapIncludingDeleted())}}),sh&&br.loadElementsFonts(T).then(k=>{this.fonts.onLoaded(k)}),t.files&&this.addMissingFiles(t.files),this.store.shouldCaptureIncrement();let _=Ys(T);this.setState({...this.state,openSidebar:this.state.openSidebar&&this.device.editor.canFitSidebar&&st.get(Ja)?this.state.openSidebar:null,...We({editingGroupId:null,selectedElementIds:_.reduce((k,R)=>(qe(R)||(k[R.id]=!0),k),{})},this.scene.getNonDeletedElements(),this.state,this)},()=>{t.files&&this.addNewImagesToImageCache()}),this.setActiveTool({type:"selection"}),t.fitToContent&&this.scrollToContent(T,{fitToContent:!0,canvasOffsets:this.getEditorUIOffsets()})});C(this,"setAppState",(t,r)=>{this.setState(t,r)});C(this,"removePointer",t=>{Ur&&this.resetContextMenuTimer(),fe.pointers.delete(t.pointerId)});C(this,"toggleLock",(t="ui")=>{this.state.activeTool.locked||le("toolbar","toggleLock",`${t} (${this.device.editor.isMobile?"mobile":"desktop"})`),this.setState(r=>({activeTool:{...r.activeTool,...Be(this.state,r.activeTool.locked?{type:"selection"}:r.activeTool),locked:!r.activeTool.locked}}))});C(this,"updateFrameRendering",t=>{this.setState(r=>{let n=typeof t=="function"?t(r.frameRendering):t;return{frameRendering:{enabled:n?.enabled??r.frameRendering.enabled,clip:n?.clip??r.frameRendering.clip,name:n?.name??r.frameRendering.name,outline:n?.outline??r.frameRendering.outline}}})});C(this,"togglePenMode",t=>{this.setState(r=>({penMode:t??!r.penMode,penDetected:!0}))});C(this,"onHandToolToggle",()=>{this.actionManager.executeAction(Zy)});C(this,"zoomCanvas",t=>{this.setState({...Qo({viewportX:this.state.width/2+this.state.offsetLeft,viewportY:this.state.height/2+this.state.offsetTop,nextZoom:Ao(t)},this.state)})});C(this,"cancelInProgressAnimation",null);C(this,"scrollToContent",(t=this.scene.getNonDeletedElements(),r)=>{if(typeof t=="string"){let s;if(pn(t)?s=Vx(t):s=t,s){let c=this.scene.getElementsFromId(s);c?.length?this.scrollToContent(c,{fitToContent:r?.fitToContent??!0,animate:r?.animate??!0}):pn(t)&&this.setState({toast:{message:g("elementLink.notFound"),duration:3e3,closable:!0}})}return}this.cancelInProgressAnimation?.();let n=Array.isArray(t)?t:[t],i=this.state.zoom,a=this.state.scrollX,l=this.state.scrollY;if(r?.fitToContent||r?.fitToViewport){let{appState:s}=Fa({canvasOffsets:r.canvasOffsets,targetElements:n,appState:this.state,fitToViewport:!!r?.fitToViewport,viewportZoomFactor:r?.viewportZoomFactor,minZoom:r?.minZoom,maxZoom:r?.maxZoom});i=s.zoom,a=s.scrollX,l=s.scrollY}else{let s=fn(n,this.state);a=s.scrollX,l=s.scrollY}if(r?.animate){let s=this.state.scrollX,c=this.state.scrollY,m=this.state.zoom.value,d=Fh({fromValues:{scrollX:s,scrollY:c,zoom:m},toValues:{scrollX:a,scrollY:l,zoom:i.value},interpolateValue:(p,u,h,f)=>{if(f==="zoom")return p*Math.pow(u/p,Zr(h))},onStep:({scrollX:p,scrollY:u,zoom:h})=>{this.setState({scrollX:p,scrollY:u,zoom:{value:h}})},onStart:()=>{this.setState({shouldCacheIgnoreZoom:!0})},onEnd:()=>{this.setState({shouldCacheIgnoreZoom:!1})},onCancel:()=>{this.setState({shouldCacheIgnoreZoom:!1})},duration:r?.duration??500});this.cancelInProgressAnimation=()=>{d(),this.cancelInProgressAnimation=null}}else this.setState({scrollX:a,scrollY:l,zoom:i})});C(this,"maybeUnfollowRemoteUser",()=>{this.state.userToFollow&&this.setState({userToFollow:null})});C(this,"translateCanvas",t=>{this.cancelInProgressAnimation?.(),this.maybeUnfollowRemoteUser(),this.setState(t)});C(this,"setToast",t=>{this.setState({toast:t})});C(this,"restoreFileFromShare",async()=>{try{let t=await caches.open("web-share-target"),r=await t.match("shared-file");if(r){let n=await r.blob(),i=new File([n],n.name||"",{type:n.type});this.loadFileToCanvas(i,null),await t.delete("shared-file"),window.history.replaceState(null,ea,window.location.pathname)}}catch(t){this.setState({errorMessage:t.message})}});C(this,"addFiles",Fe(t=>{let{addedFiles:r}=this.addMissingFiles(t);this.clearImageShapeCache(r),this.scene.triggerUpdate(),this.addNewImagesToImageCache()}));C(this,"addMissingFiles",(t,r=!1)=>{let n=r?{}:{...this.files},i={},a=Array.isArray(t)?t:Object.values(t);for(let l of a)if(!n[l.id]&&(i[l.id]=l,n[l.id]=l,l.mimeType===je.svg))try{let s=Dx(Hm(Rx(l.dataURL)),je.svg);l.dataURL!==s&&(l.version=(l.version??1)+1,l.dataURL=s)}catch(s){console.error(s)}return this.files=n,{addedFiles:i}});C(this,"updateScene",Fe(t=>{let r=Jo(t.elements??[]);if(t.captureUpdate&&t.captureUpdate!==L.EVENTUALLY){let n=this.store.snapshot.appState,i=this.store.snapshot.elements,a=t.appState?Object.assign({},n,t.appState):n,l=t.elements?this.store.filterUncomittedElements(this.scene.getElementsMapIncludingDeleted(),Q(r)):i;t.captureUpdate===L.IMMEDIATELY?this.store.captureIncrement(l,a):t.captureUpdate===L.NEVER&&this.store.updateSnapshot(l,a)}t.appState&&this.setState(t.appState),t.elements&&this.scene.replaceAllElements(r),t.collaborators&&this.setState({collaborators:t.collaborators})}));C(this,"triggerRender",t=>{t===!0?this.scene.triggerUpdate():this.setState({})});C(this,"toggleSidebar",({name:t,tab:r,force:n})=>{let i;n===void 0?i=this.state.openSidebar?.name===t&&this.state.openSidebar?.tab===r?null:t:i=n?t:null;let a=i?{name:i}:null;return a&&r&&(a.tab=r),this.setState({openSidebar:a}),!!i});C(this,"updateCurrentCursorPosition",Fe(t=>{this.lastViewportPosition.x=t.clientX,this.lastViewportPosition.y=t.clientY}));C(this,"getEditorUIOffsets",()=>{let t=this.excalidrawContainerRef?.current?.querySelector(".App-toolbar")?.getBoundingClientRect()?.bottom??0,r=this.excalidrawContainerRef?.current?.querySelector(".sidebar")?.getBoundingClientRect(),n=this.excalidrawContainerRef?.current?.querySelector(".App-menu__left")?.getBoundingClientRect(),i=16;return qo().rtl?{top:t+i,right:Math.max(this.state.width-(n?.left??this.state.width),0)+i,bottom:i,left:Math.max(r?.right??0,0)+i}:{top:t+i,right:Math.max(this.state.width-(r?.left??this.state.width)+i,0),bottom:i,left:Math.max(n?.right??0,0)+i}});C(this,"onKeyDown",Fe(t=>{if("Proxy"in window&&(!t.shiftKey&&/^[A-Z]$/.test(t.key)||t.shiftKey&&/^[a-z]$/.test(t.key))&&(t=new Proxy(t,{get(a,l){let s=a[l];return typeof s=="function"?s.bind(a):l==="key"?t.shiftKey?a.key.toUpperCase():a.key.toLowerCase():s}})),!rm(t.target)){if((t.key===y.ESCAPE||t.key===y.ENTER)&&this.state.croppingElementId){this.finishImageCropping();return}let a=$(this.scene.getNonDeletedElementsMap(),this.state);if(a.length===1&&xe(a[0])&&t.key===y.ENTER){this.startImageCropping(a[0]);return}if(t.key===y.ESCAPE&&this.flowChartCreator.isCreatingChart){this.flowChartCreator.clear(),this.triggerRender(!0);return}let l=an(t.key);if(t[y.CTRL_OR_CMD]&&l&&!t.shiftKey){t.preventDefault();let s=$(this.scene.getNonDeletedElementsMap(),this.state);s.length===1&&en(s[0])&&this.flowChartCreator.createNodes(s[0],this.scene.getNonDeletedElementsMap(),this.state,Hu(t.key)),this.flowChartCreator.pendingNodes?.length&&!gi(this.flowChartCreator.pendingNodes,this.canvas.width/window.devicePixelRatio,this.canvas.height/window.devicePixelRatio,{offsetLeft:this.state.offsetLeft,offsetTop:this.state.offsetTop,scrollX:this.state.scrollX,scrollY:this.state.scrollY,zoom:this.state.zoom},this.scene.getNonDeletedElementsMap(),this.getEditorUIOffsets())&&this.scrollToContent(this.flowChartCreator.pendingNodes,{animate:!0,duration:300,fitToContent:!0,canvasOffsets:this.getEditorUIOffsets()});return}if(t.altKey){let s=$(this.scene.getNonDeletedElementsMap(),this.state);if(s.length===1&&l){t.preventDefault();let c=this.flowChartNavigator.exploreByDirection(s[0],this.scene.getNonDeletedElementsMap(),Hu(t.key));if(c){this.setState(d=>({selectedElementIds:Ee({[c]:!0},d)}));let m=this.scene.getNonDeletedElementsMap().get(c);m&&!gi([m],this.canvas.width/window.devicePixelRatio,this.canvas.height/window.devicePixelRatio,{offsetLeft:this.state.offsetLeft,offsetTop:this.state.offsetTop,scrollX:this.state.scrollX,scrollY:this.state.scrollY,zoom:this.state.zoom},this.scene.getNonDeletedElementsMap(),this.getEditorUIOffsets())&&this.scrollToContent(m,{animate:!0,duration:300,canvasOffsets:this.getEditorUIOffsets()})}return}}}if(t[y.CTRL_OR_CMD]&&t.key===y.P&&!t.shiftKey&&!t.altKey){this.setToast({message:g("commandPalette.shortcutHint",{shortcut:Ye("commandPalette")})}),t.preventDefault();return}if(t[y.CTRL_OR_CMD]&&t.key.toLowerCase()===y.V&&(Rd=t.shiftKey,clearTimeout(pT),pT=window.setTimeout(()=>{Rd=!1},100)),t[y.CTRL_OR_CMD]&&Co(t.target)&&(t.code===te.MINUS||t.code===te.EQUAL)){t.preventDefault();return}if(Co(t.target)&&t.key!==y.ESCAPE||an(t.key)&&rm(t.target))return;if(t.key===y.QUESTION_MARK){this.setState({openDialog:{name:"help"}});return}else if(t.key.toLowerCase()===y.E&&t.shiftKey&&t[y.CTRL_OR_CMD]){t.preventDefault(),this.setState({openDialog:{name:"imageExport"}});return}if(t.key===y.PAGE_UP||t.key===y.PAGE_DOWN){let a=(t.shiftKey?this.state.width:this.state.height)/this.state.zoom.value;t.key===y.PAGE_DOWN&&(a=-a),t.shiftKey?this.translateCanvas(l=>({scrollX:l.scrollX+a})):this.translateCanvas(l=>({scrollY:l.scrollY+a}))}if(this.state.openDialog?.name==="elementLinkSelector"||this.actionManager.handleKeyDown(t)||this.state.viewModeEnabled)return;if(t[y.CTRL_OR_CMD]&&this.state.isBindingEnabled&&this.setState({isBindingEnabled:!1}),an(t.key)){let a=this.scene.getSelectedElements({selectedElementIds:this.state.selectedElementIds,includeBoundTextElement:!0,includeElementsInFrames:!0}),l=a.find(ee),s=new Set;a.filter(ee).filter(p=>{let u=p.startBinding&&!a.some(f=>f.id===p.startBinding?.elementId),h=p.endBinding&&!a.some(f=>f.id===p.endBinding?.elementId);return u||h}).forEach(p=>s.add(p.id)),a=a.filter(p=>!s.has(p.id));let c=this.getEffectiveGridSize()&&(t.shiftKey?Gd:this.getEffectiveGridSize())||(t.shiftKey?mh:Gd),m=0,d=0;t.key===y.ARROW_LEFT?m=-c:t.key===y.ARROW_RIGHT?m=c:t.key===y.ARROW_UP?d=-c:t.key===y.ARROW_DOWN&&(d=c),a.forEach(p=>{P(p,{x:p.x+m,y:p.y+d},!1),Jt(p,this.scene.getNonDeletedElementsMap(),{simultaneouslyUpdated:a})}),this.setState({suggestedBindings:gs(a.filter(p=>p.id!==l?.id||c!==0),this.scene.getNonDeletedElementsMap(),this.state.zoom)}),this.scene.triggerUpdate(),t.preventDefault()}else if(t.key===y.ENTER){let a=this.scene.getSelectedElements(this.state);if(a.length===1){let l=a[0];if(t[y.CTRL_OR_CMD])he(l)&&(!this.state.editingLinearElement||this.state.editingLinearElement.elementId!==a[0].id)&&(this.store.shouldCaptureIncrement(),ee(l)||this.setState({editingLinearElement:new K(l)}));else if(Y(l)||IE(l)){let s;Y(l)||(s=l);let c=Us(l,this.state,this.scene.getNonDeletedElementsMap()),m=c.x,d=c.y;this.startTextEditing({sceneX:m,sceneY:d,container:s}),t.preventDefault();return}else ie(l)&&this.setState({editingFrame:l.id})}}else if(!t.ctrlKey&&!t.altKey&&!t.metaKey&&!this.state.newElement&&!this.state.selectionElement&&!this.state.selectedElementsAreBeingDragged){let a=cx(t.key);a?(this.state.activeTool.type!==a&&le("toolbar",a,`keyboard (${this.device.editor.isMobile?"mobile":"desktop"})`),a==="arrow"&&this.state.activeTool.type==="arrow"&&this.setState(l=>({currentItemArrowType:l.currentItemArrowType===Je.sharp?Je.round:l.currentItemArrowType===Je.round?Je.elbow:Je.sharp})),this.setActiveTool({type:a}),t.stopPropagation()):t.key===y.Q&&(this.toggleLock("keyboard"),t.stopPropagation())}if(t.key===y.SPACE&&fe.pointers.size===0&&(Mn=!0,Te(this.interactiveCanvas,we.GRAB),t.preventDefault()),(t.key===y.G||t.key===y.S)&&!t.altKey&&!t[y.CTRL_OR_CMD]){let a=this.scene.getSelectedElements(this.state);if(this.state.activeTool.type==="selection"&&!a.length)return;t.key===y.G&&(vr(this.state.activeTool.type)||a.some(l=>vr(l.type)))&&(this.setState({openPopup:"elementBackground"}),t.stopPropagation()),t.key===y.S&&(this.setState({openPopup:"elementStroke"}),t.stopPropagation())}if(!t[y.CTRL_OR_CMD]&&t.shiftKey&&t.key.toLowerCase()===y.F){let a=this.scene.getSelectedElements(this.state);if(this.state.activeTool.type==="selection"&&!a.length)return;(this.state.activeTool.type==="text"||a.find(l=>Y(l)||ue(l,this.scene.getNonDeletedElementsMap())))&&(t.preventDefault(),this.setState({openPopup:"fontFamily"}))}if(t.key===y.K&&!t.altKey&&!t[y.CTRL_OR_CMD]){this.state.activeTool.type==="laser"?this.setActiveTool({type:"selection"}):this.setActiveTool({type:"laser"});return}t[y.CTRL_OR_CMD]&&(t.key===y.BACKSPACE||t.key===y.DELETE)&&st.set(In,"clearCanvas");let r=t.key.toLocaleLowerCase(),n=r===y.S&&t.shiftKey,i=t.key===y.I||r===y.G&&t.shiftKey;(n||i)&&this.openEyeDropper({type:n?"stroke":"background"})}));C(this,"onKeyUp",Fe(t=>{if(t.key===y.SPACE&&(this.state.viewModeEnabled||this.state.openDialog?.name==="elementLinkSelector"?Te(this.interactiveCanvas,we.GRAB):this.state.activeTool.type==="selection"?Zt(this.interactiveCanvas):(go(this.interactiveCanvas,this.state),this.setState({selectedElementIds:Ee({},this.state),selectedGroupIds:{},editingGroupId:null,activeEmbeddable:null})),Mn=!1),!t[y.CTRL_OR_CMD]&&!this.state.isBindingEnabled&&this.setState({isBindingEnabled:!0}),an(t.key)&&(cn(this.scene.getSelectedElements(this.state).filter(he),this.scene.getNonDeletedElementsMap(),this.scene.getNonDeletedElements(),this.scene,sn(this.state),this.state.selectedLinearElement?.selectedPointsIndices??[],this.state.zoom),this.setState({suggestedBindings:[]})),t.altKey||this.flowChartNavigator.isExploring&&(this.flowChartNavigator.clear(),this.syncActionResult({captureUpdate:L.IMMEDIATELY})),!t[y.CTRL_OR_CMD]&&this.flowChartCreator.isCreatingChart){this.flowChartCreator.pendingNodes?.length&&this.scene.insertElements(this.flowChartCreator.pendingNodes);let r=this.flowChartCreator.pendingNodes?.[0];r&&(this.setState(n=>({selectedElementIds:Ee({[r.id]:!0},n)})),gi([r],this.canvas.width/window.devicePixelRatio,this.canvas.height/window.devicePixelRatio,{offsetLeft:this.state.offsetLeft,offsetTop:this.state.offsetTop,scrollX:this.state.scrollX,scrollY:this.state.scrollY,zoom:this.state.zoom},this.scene.getNonDeletedElementsMap(),this.getEditorUIOffsets())||this.scrollToContent(r,{animate:!0,duration:300,canvasOffsets:this.getEditorUIOffsets()})),this.flowChartCreator.clear(),this.syncActionResult({captureUpdate:L.IMMEDIATELY})}}));C(this,"isToolSupported",t=>this.props.UIOptions.tools?.[t]!==!1);C(this,"setActiveTool",t=>{if(!this.isToolSupported(t.type)){console.warn(`"${t.type}" tool is disabled via "UIOptions.canvasActions.tools.${t.type}"`);return}let r=Be(this.state,t);r.type==="hand"?Te(this.interactiveCanvas,we.GRAB):Mn||go(this.interactiveCanvas,{...this.state,activeTool:r}),Nh(document.activeElement)&&this.focusContainer(),jh(r.type)||this.setState({suggestedBindings:[]}),r.type==="image"&&this.onImageAction({insertOnCanvasDirectly:(t.type==="image"&&t.insertOnCanvasDirectly)??!1}),this.setState(n=>{let i={snapLines:n.snapLines.length?[]:n.snapLines,originSnapOffset:null,activeEmbeddable:null};return r.type==="freedraw"&&this.store.shouldCaptureIncrement(),r.type!=="selection"?{...n,activeTool:r,selectedElementIds:Ee({},n),selectedGroupIds:Ee({},n),editingGroupId:null,multiElement:null,...i}:{...n,activeTool:r,...i}})});C(this,"setOpenDialog",t=>{this.setState({openDialog:t})});C(this,"setCursor",t=>{Te(this.interactiveCanvas,t)});C(this,"resetCursor",()=>{Zt(this.interactiveCanvas)});C(this,"isTouchScreenMultiTouchGesture",()=>fe.pointers.size>=2);C(this,"getName",()=>this.state.name||this.props.name||`${g("labels.untitled")}-${om()}`);C(this,"onGestureStart",Fe(t=>{t.preventDefault(),this.isTouchScreenMultiTouchGesture()&&this.setState({selectedElementIds:Ee({},this.state),activeEmbeddable:null}),fe.initialScale=this.state.zoom.value}));C(this,"onGestureChange",Fe(t=>{if(t.preventDefault(),this.isTouchScreenMultiTouchGesture())return;let r=fe.initialScale;r&&this.setState(n=>({...Qo({viewportX:this.lastViewportPosition.x,viewportY:this.lastViewportPosition.y,nextZoom:Ao(r*t.scale)},n)}))}));C(this,"onGestureEnd",Fe(t=>{t.preventDefault(),this.isTouchScreenMultiTouchGesture()&&this.setState({previousSelectedElementIds:{},selectedElementIds:Ee(this.state.previousSelectedElementIds,this.state)}),fe.initialScale=null}));C(this,"startTextEditing",({sceneX:t,sceneY:r,insertAtParentCenter:n=!0,container:i,autoEdit:a=!0})=>{let l=!1,s=n&&this.getTextWysiwygSnappedToCenterPosition(t,r,this.state,i);i&&s&&(ue(i,this.scene.getNonDeletedElementsMap())||(l=!0));let c=null,m=this.scene.getSelectedElements(this.state);m.length===1?Y(m[0])?c=m[0]:i?c=ue(m[0],this.scene.getNonDeletedElementsMap()):c=this.getTextElementAtPosition(t,r):c=this.getTextElementAtPosition(t,r);let d=c?.fontFamily||this.state.currentItemFontFamily,p=c?.lineHeight||$o(d),u=this.state.currentItemFontSize;if(!c&&l&&i&&!Ne(i)){let x=of(lt({fontSize:u,fontFamily:d}),p),T=lf(u,p),E=Math.max(i.height,T),w=Math.max(i.width,x);P(i,{height:E,width:w}),t=i.x+w/2,r=i.y+E/2,s&&(s=this.getTextWysiwygSnappedToCenterPosition(t,r,this.state,i))}let h=this.getTopLayerFrameAtSceneCoords({x:t,y:r}),f=c||So({x:s?s.elementCenterX:t,y:s?s.elementCenterY:r,strokeColor:this.state.currentItemStrokeColor,backgroundColor:this.state.currentItemBackgroundColor,fillStyle:this.state.currentItemFillStyle,strokeWidth:this.state.currentItemStrokeWidth,strokeStyle:this.state.currentItemStrokeStyle,roughness:this.state.currentItemRoughness,opacity:this.state.currentItemOpacity,text:"",fontSize:u,fontFamily:d,textAlign:s?"center":this.state.currentItemTextAlign,verticalAlign:s?Kt.MIDDLE:Vd,containerId:l?i?.id:void 0,groupIds:i?.groupIds??[],lineHeight:p,angle:i?.angle??0,frameId:h?h.id:null});if(!c&&l&&i&&P(i,{boundElements:(i.boundElements||[]).concat({type:"text",id:f.id})}),this.setState({editingTextElement:f}),!c)if(i&&l){let b=this.scene.getElementIndex(i.id);this.scene.insertElementAtIndex(f,b+1)}else this.scene.insertElement(f);a||c||i?this.handleTextWysiwyg(f,{isExistingElement:!!c}):this.setState({newElement:f,multiElement:null})});C(this,"startImageCropping",t=>{this.store.shouldCaptureIncrement(),this.setState({croppingElementId:t.id})});C(this,"finishImageCropping",()=>{this.state.croppingElementId&&(this.store.shouldCaptureIncrement(),this.setState({croppingElementId:null}))});C(this,"handleCanvasDoubleClick",t=>{if(this.state.multiElement||this.state.activeTool.type!=="selection")return;let r=this.scene.getSelectedElements(this.state),{x:n,y:i}=Re(t,this.state);if(r.length===1&&he(r[0])){if(t[y.CTRL_OR_CMD]&&(!this.state.editingLinearElement||this.state.editingLinearElement.elementId!==r[0].id)&&!ee(r[0])){this.store.shouldCaptureIncrement(),this.setState({editingLinearElement:new K(r[0])});return}else if(this.state.selectedLinearElement&&ee(r[0])){let l=K.getSegmentMidpointHitCoords(this.state.selectedLinearElement,{x:n,y:i},this.state,this.scene.getNonDeletedElementsMap()),s=l?K.getSegmentMidPointIndex(this.state.selectedLinearElement,this.state,l,this.scene.getNonDeletedElementsMap()):-1;if(s&&s>-1){this.store.shouldCaptureIncrement(),K.deleteFixedSegment(r[0],s);let c=K.getSegmentMidpointHitCoords({...this.state.selectedLinearElement,segmentMidPointHoveredCoords:null},{x:n,y:i},this.state,this.scene.getNonDeletedElementsMap()),m=c?K.getSegmentMidPointIndex(this.state.selectedLinearElement,this.state,c,this.scene.getNonDeletedElementsMap()):null;this.setState({selectedLinearElement:{...this.state.selectedLinearElement,pointerDownState:{...this.state.selectedLinearElement.pointerDownState,segmentMidpoint:{index:m,value:l,added:!1}},segmentMidPointHoveredCoords:c}});return}}}if(r.length===1&&xe(r[0])){this.startImageCropping(r[0]);return}if(Zt(this.interactiveCanvas),po(this.state).length>0){let l=this.getElementAtPosition(n,i),s=l&&uf(l,this.state.selectedGroupIds);if(s){this.store.shouldCaptureIncrement(),this.setState(c=>({...c,...We({editingGroupId:s,selectedElementIds:{[l.id]:!0}},this.scene.getNonDeletedElements(),c,this)}));return}}if(Zt(this.interactiveCanvas),!t[y.CTRL_OR_CMD]&&!this.state.viewModeEnabled){let l=this.getElementAtPosition(n,i);if(Wn(l)){this.setState({activeEmbeddable:{element:l,state:"active"}});return}let s=this.getTextBindableContainerAtPosition(n,i);if(s&&(ut(s)||!Jr(s.backgroundColor)||ai({x:n,y:i,element:s,shape:ln(s,this.scene.getNonDeletedElementsMap()),threshold:this.getElementHitThreshold()}))){let c=Us(s,this.state,this.scene.getNonDeletedElementsMap());n=c.x,i=c.y}this.startTextEditing({sceneX:n,sceneY:i,insertAtParentCenter:!t.altKey,container:s})}});C(this,"getElementLinkAtPosition",(t,r)=>{let n=this.scene.getNonDeletedElements(),i=-1;for(let a=n.length-1;a>=0;a--){let l=n[a];if(r&&l.id===r.id&&(i=a),l.link&&a>=i&&Ts(l,this.scene.getNonDeletedElementsMap(),this.state,z(t.x,t.y),this.device.editor.isMobile))return l}});C(this,"redirectToLink",(t,r)=>{let n=dr(z(this.lastPointerDownEvent.clientX,this.lastPointerDownEvent.clientY),z(this.lastPointerUpEvent.clientX,this.lastPointerUpEvent.clientY));if(!this.hitLinkElement||n>Vr)return;let i=Re(this.lastPointerDownEvent,this.state),a=this.scene.getNonDeletedElementsMap(),l=Ts(this.hitLinkElement,a,this.state,z(i.x,i.y),this.device.editor.isMobile),s=Re(this.lastPointerUpEvent,this.state),c=Ts(this.hitLinkElement,a,this.state,z(s.x,s.y),this.device.editor.isMobile);if(l&&c){lu();let m=this.hitLinkElement.link;if(m){m=uo(m);let d;if(this.props.onLinkOpen&&(d=Pl("excalidraw-link",t.nativeEvent),this.props.onLinkOpen({...this.hitLinkElement,link:m},d)),!d?.defaultPrevented){let p=zl(m)?"_self":"_blank",u=window.open(void 0,p);u&&(u.opener=null,u.location=m)}}}});C(this,"getTopLayerFrameAtSceneCoords",t=>{let r=this.scene.getNonDeletedElementsMap(),n=this.scene.getNonDeletedFramesLikes().filter(i=>Ym(t,i,r));return n.length?n[n.length-1]:null});C(this,"handleCanvasPointerMove",t=>{this.savePointer(t.clientX,t.clientY,this.state.cursorButton),this.lastPointerMoveEvent=t.nativeEvent,fe.pointers.has(t.pointerId)&&fe.pointers.set(t.pointerId,{x:t.clientX,y:t.clientY});let r=fe.initialScale;if(fe.pointers.size===2&&fe.lastCenter&&r&&fe.initialDistance){let u=Ou(fe.pointers),h=u.x-fe.lastCenter.x,f=u.y-fe.lastCenter.y;fe.lastCenter=u;let b=Fu(Array.from(fe.pointers.values())),x=this.state.activeTool.type==="freedraw"&&this.state.penMode?1:b/fe.initialDistance,T=x?Ao(r*x):this.state.zoom.value;this.setState(E=>{let w=Qo({viewportX:u.x,viewportY:u.y,nextZoom:T},E);this.translateCanvas({zoom:w.zoom,scrollX:w.scrollX+2*(h/T),scrollY:w.scrollY+2*(f/T),shouldCacheIgnoreZoom:!0})}),this.resetShouldCacheIgnoreZoomDebounced()}else fe.lastCenter=fe.initialDistance=fe.initialScale=null;if(Mn||ll||Dg||Xo(this.state))return;let i=Gu(Pg,t.clientX-this.state.offsetLeft,t.clientY-this.state.offsetTop).isOverEither;!this.state.newElement&&!this.state.selectionElement&&!this.state.selectedElementsAreBeingDragged&&!this.state.multiElement&&(i?Zt(this.interactiveCanvas):go(this.interactiveCanvas,this.state));let a=Re(t,this.state),{x:l,y:s}=a;if(!this.state.newElement&&fE(this.state.activeTool.type)){let{originOffset:u,snapLines:h}=hE(this.scene.getNonDeletedElements(),this,{x:l,y:s},t,this.scene.getNonDeletedElementsMap());this.setState(f=>{let b=Yn(f.snapLines,h),x=f.originSnapOffset?Yn(f.originSnapOffset,u):u;return f.snapLines===b&&f.originSnapOffset===x?null:{snapLines:b,originSnapOffset:x}})}else!this.state.newElement&&!this.state.selectedElementsAreBeingDragged&&!this.state.selectionElement&&this.setState(u=>u.snapLines.length?{snapLines:[]}:null);if(this.state.editingLinearElement&&!this.state.editingLinearElement.isDragging){let u=K.handlePointerMove(t,l,s,this,this.scene.getNonDeletedElementsMap());u&&u!==this.state.editingLinearElement&&_n(()=>{this.setState({editingLinearElement:u})}),u?.lastUncommittedPoint!=null?this.maybeSuggestBindingAtCursor(a,u.elbowed):_n(()=>{this.setState({suggestedBindings:[]})})}if(Jh(this.state.activeTool.type)){let{newElement:u}=this.state;Wo(u,!1)?this.maybeSuggestBindingsForLinearElementAtCoords(u,[a],this.state.startBoundElement):this.maybeSuggestBindingAtCursor(a,!1)}if(this.state.multiElement){let{multiElement:u}=this.state,{x:h,y:f}=u,{points:b,lastCommittedPoint:x}=u,T=b[b.length-1];if(go(this.interactiveCanvas,this.state),T===x)dr(z(l-h,s-f),T)>=El?P(u,{points:[...b,z(l-h,s-f)]},!1):Te(this.interactiveCanvas,we.POINTER);else if(b.length>2&&x&&dr(z(l-h,s-f),x)<El)Te(this.interactiveCanvas,we.POINTER),P(u,{points:b.slice(0,-1)},!1);else{let[E,w]=nt(l,s,t[y.CTRL_OR_CMD]||ee(u)?null:this.getEffectiveGridSize()),[S,I]=u?.lastCommittedPoint??[0,0],_=E-h-S,k=w-f-I;ds(t)&&({width:_,height:k}=tp(S+h,I+f,E,w)),xa(b,this.state.zoom.value)&&Te(this.interactiveCanvas,we.POINTER),P(u,{points:[...b.slice(0,-1),z(S+_,I+k)]},!1,{isDragging:!0}),this.triggerRender(!1)}return}if(!!t.buttons||this.state.activeTool.type!=="selection"&&this.state.activeTool.type!=="text"&&this.state.activeTool.type!=="eraser")return;let m=this.scene.getNonDeletedElements(),d=this.scene.getSelectedElements(this.state);if(d.length===1&&!i&&!this.state.editingLinearElement){if(this.state.selectedLinearElement&&this.handleHoverSelectedLinearElement(this.state.selectedLinearElement,l,s),(!this.state.selectedLinearElement||this.state.selectedLinearElement.hoverPointIndex===-1)&&this.state.openDialog?.name!=="elementLinkSelector"&&!(d.length===1&&ee(d[0]))){let u=op(m,this.state,l,s,this.state.zoom,t.pointerType,this.scene.getNonDeletedElementsMap(),this.device);if(u&&u.transformHandleType){Te(this.interactiveCanvas,np(u));return}}}else if(d.length>1&&!i&&this.state.openDialog?.name!=="elementLinkSelector"){let u=rp(Se(d),l,s,this.state.zoom,t.pointerType,this.device);if(u){Te(this.interactiveCanvas,np({transformHandleType:u}));return}}let p=this.getElementAtPosition(a.x,a.y);this.hitLinkElement=this.getElementLinkAtPosition(a,p),!$t(this.state)&&(this.hitLinkElement&&!this.state.selectedElementIds[this.hitLinkElement.id]?(Te(this.interactiveCanvas,we.POINTER),Tv(this.hitLinkElement,this.state,this.scene.getNonDeletedElementsMap())):(lu(),p&&(p.link||Et(p))&&this.state.selectedElementIds[p.id]&&!this.state.contextMenu&&!this.state.showHyperlinkPopup?this.setState({showHyperlinkPopup:"info"}):this.state.activeTool.type==="text"?Te(this.interactiveCanvas,Y(p)?we.TEXT:we.CROSSHAIR):this.state.viewModeEnabled?Te(this.interactiveCanvas,we.GRAB):this.state.openDialog?.name==="elementLinkSelector"?Te(this.interactiveCanvas,we.AUTO):i?Te(this.interactiveCanvas,we.AUTO):this.state.selectedLinearElement?this.handleHoverSelectedLinearElement(this.state.selectedLinearElement,l,s):t[y.CTRL_OR_CMD]?Te(this.interactiveCanvas,we.AUTO):(p||this.isHittingCommonBoundingBoxOfSelectedElements(a,d))&&!p?.locked&&(p&&Wn(p)&&this.isIframeLikeElementCenter(p,t,l,s)?(Te(this.interactiveCanvas,we.POINTER),this.setState({activeEmbeddable:{element:p,state:"hover"}})):(!p||!ee(p))&&(Te(this.interactiveCanvas,we.MOVE),this.state.activeEmbeddable?.state==="hover"&&this.setState({activeEmbeddable:null})))),this.state.openDialog?.name==="elementLinkSelector"&&p?this.setState(u=>({hoveredElementIds:Yn(u.hoveredElementIds,We({editingGroupId:u.editingGroupId,selectedElementIds:{[p.id]:!0}},this.scene.getNonDeletedElements(),u,this).selectedElementIds)})):this.state.openDialog?.name==="elementLinkSelector"&&!p&&this.setState(u=>({hoveredElementIds:Yn(u.hoveredElementIds,{})})))});C(this,"handleEraser",(t,r,n)=>{this.eraserTrail.addPointToPath(n.x,n.y);let i=!1,a=new Set,l=this.scene.getNonDeletedElements(),s=u=>{for(let h of u){if(h.locked)return;if(t.altKey?this.elementsPendingErasure.delete(h.id)&&(i=!0):this.elementsPendingErasure.has(h.id)||(i=!0,this.elementsPendingErasure.add(h.id)),i&&h.groupIds?.length){let f=h.groupIds.at(-1);if(!a.has(f)){a.add(f);let b=Le(l,f);for(let x of b)t.altKey?this.elementsPendingErasure.delete(x.id):this.elementsPendingErasure.add(x.id)}}}},c=dr(z(r.lastCoords.x,r.lastCoords.y),z(n.x,n.y)),m=this.getElementHitThreshold(),d={...r.lastCoords},p=0;for(;p<=c;){let u=this.getElementsAtPosition(d.x,d.y);if(s(u),p===c)break;p=Math.min(p+m,c);let h=p/c,f=(1-h)*d.x+h*n.x,b=(1-h)*d.y+h*n.y;d.x=f,d.y=b}if(r.lastCoords.x=n.x,r.lastCoords.y=n.y,i){for(let u of this.scene.getNonDeletedElements())qe(u)&&(this.elementsPendingErasure.has(u.id)||this.elementsPendingErasure.has(u.containerId))&&(t.altKey?(this.elementsPendingErasure.delete(u.id),this.elementsPendingErasure.delete(u.containerId)):(this.elementsPendingErasure.add(u.id),this.elementsPendingErasure.add(u.containerId)));this.elementsPendingErasure=new Set(this.elementsPendingErasure),this.triggerRender()}});C(this,"handleTouchMove",t=>{sl=!0});C(this,"handleCanvasPointerDown",t=>{let r=t.target;if(r.setPointerCapture&&r.setPointerCapture(t.pointerId),this.maybeCleanupAfterMissingPointerUp(t.nativeEvent),this.maybeUnfollowRemoteUser(),this.state.searchMatches&&(this.setState(d=>({searchMatches:d.searchMatches.map(p=>({...p,focus:!1}))})),st.set(cg,null)),this.state.contextMenu&&this.setState({contextMenu:null}),this.state.snapLines&&this.setAppState({snapLines:[]}),this.updateGestureOnPointerDown(t),t.pointerType==="touch"&&this.state.newElement&&this.state.newElement.type==="freedraw"){let d=this.state.newElement;this.updateScene({...d.points.length<10?{elements:this.scene.getElementsIncludingDeleted().filter(p=>p.id!==d.id)}:{},appState:{newElement:null,editingTextElement:null,startBoundElement:null,suggestedBindings:[],selectedElementIds:Ee(Object.keys(this.state.selectedElementIds).filter(p=>p!==d.id).reduce((p,u)=>(p[u]=this.state.selectedElementIds[u],p),{}),this.state)},captureUpdate:this.state.openDialog?.name==="elementLinkSelector"?L.EVENTUALLY:L.NEVER});return}let n=document.getSelection();if(n?.anchorNode&&n.removeAllRanges(),this.maybeOpenContextMenuAfterPointerDownOnTouchDevices(t),!this.state.penDetected&&t.pointerType==="pen"&&this.setState(d=>({penMode:!0,penDetected:!0})),!this.device.isTouchScreen&&["pen","touch"].includes(t.pointerType)&&(this.device=Hn(this.device,{isTouchScreen:!0})),ll||(this.lastPointerDownEvent=t,this.handleCanvasPanUsingWheelOrSpaceDrag(t)))return;if(this.setState({lastPointerDownWith:t.pointerType,cursorButton:"down"}),this.savePointer(t.clientX,t.clientY,"down"),t.button===To.ERASER&&this.state.activeTool.type!==kt.eraser){this.setState({activeTool:Be(this.state,{type:kt.eraser,lastActiveToolBeforeEraser:this.state.activeTool})},()=>{this.handleCanvasPointerDown(t);let d=()=>{p(),u?.(),$t(this.state)&&this.setState({activeTool:Be(this.state,{...this.state.activeTool.lastActiveTool||{type:kt.selection},lastActiveToolBeforeEraser:null})})},p=Ae(window,"pointerup",d,{once:!0}),u;requestAnimationFrame(()=>{u=this.missingPointerEventCleanupEmitter.once(d)})});return}if(t.button!==To.MAIN&&t.button!==To.TOUCH&&t.button!==To.ERASER||fe.pointers.size>1)return;let i=this.initialPointerDownState(t);if(this.setState({selectedElementsAreBeingDragged:!1}),this.handleDraggingScrollBar(t,i)||(this.clearSelectionIfNotUsingSelection(),this.updateBindingEnabledOnPointerMove(t),this.handleSelectionOnPointerDown(t,i))||!(!this.state.penMode||t.pointerType!=="touch"||this.state.activeTool.type==="selection"||this.state.activeTool.type==="text"||this.state.activeTool.type==="image"))return;if(this.state.activeTool.type==="text")this.handleTextOnPointerDown(t,i);else if(this.state.activeTool.type==="arrow"||this.state.activeTool.type==="line")this.handleLinearElementOnPointerDown(t,this.state.activeTool.type,i);else if(this.state.activeTool.type==="image"){Te(this.interactiveCanvas,we.CROSSHAIR);let d=this.state.pendingImageElementId&&this.scene.getElement(this.state.pendingImageElementId);if(!d)return;this.setState({newElement:d,pendingImageElementId:null,multiElement:null});let{x:p,y:u}=Re(t,this.state),h=this.getTopLayerFrameAtSceneCoords({x:p,y:u});P(d,{x:p,y:u,frameId:h?h.id:null})}else this.state.activeTool.type==="freedraw"?this.handleFreeDrawElementOnPointerDown(t,this.state.activeTool.type,i):this.state.activeTool.type==="custom"?go(this.interactiveCanvas,this.state):this.state.activeTool.type===kt.frame||this.state.activeTool.type===kt.magicframe?this.createFrameElementOnPointerDown(i,this.state.activeTool.type):this.state.activeTool.type==="laser"?this.laserTrails.startPath(i.lastCoords.x,i.lastCoords.y):this.state.activeTool.type!=="eraser"&&this.state.activeTool.type!=="hand"&&this.createGenericElementOnPointerDown(this.state.activeTool.type,i);this.props?.onPointerDown?.(this.state.activeTool,i),this.onPointerDownEmitter.trigger(this.state.activeTool,i,t),this.state.activeTool.type==="eraser"&&this.eraserTrail.startPath(i.lastCoords.x,i.lastCoords.y);let l=this.onPointerMoveFromPointerDownHandler(i),s=this.onPointerUpFromPointerDownHandler(i),c=this.onKeyDownFromPointerDownHandler(i),m=this.onKeyUpFromPointerDownHandler(i);this.missingPointerEventCleanupEmitter.once(d=>s(d||t.nativeEvent)),(!this.state.viewModeEnabled||this.state.activeTool.type==="laser")&&(window.addEventListener("pointermove",l),window.addEventListener("pointerup",s),window.addEventListener("keydown",c),window.addEventListener("keyup",m),i.eventListeners.onMove=l,i.eventListeners.onUp=s,i.eventListeners.onKeyUp=m,i.eventListeners.onKeyDown=c)});C(this,"handleCanvasPointerUp",t=>{this.removePointer(t),this.lastPointerUpEvent=t;let r=Re({clientX:t.clientX,clientY:t.clientY},this.state),n=t.timeStamp-(this.lastPointerDownEvent?.timeStamp??0);if(this.device.editor.isMobile&&n<300){let i=this.getElementAtPosition(r.x,r.y);if(Wn(i)&&this.isIframeLikeElementCenter(i,t,r.x,r.y)){this.handleEmbeddableCenterClick(i);return}}if(this.device.isTouchScreen){let i=this.getElementAtPosition(r.x,r.y);this.hitLinkElement=this.getElementLinkAtPosition(r,i)}this.hitLinkElement&&!this.state.selectedElementIds[this.hitLinkElement.id]?n<300&&Wn(this.hitLinkElement)&&!Yx(this.hitLinkElement,this.scene.getNonDeletedElementsMap(),this.state,z(r.x,r.y))?this.handleEmbeddableCenterClick(this.hitLinkElement):this.redirectToLink(t,this.device.isTouchScreen):this.state.viewModeEnabled&&this.setState({activeEmbeddable:null,selectedElementIds:{}})});C(this,"maybeOpenContextMenuAfterPointerDownOnTouchDevices",t=>{t.pointerType==="touch"&&(sl=!1,Ur?sl=!0:Ur=window.setTimeout(()=>{Ur=0,sl||this.handleCanvasContextMenu(t)},yh))});C(this,"resetContextMenuTimer",()=>{clearTimeout(Ur),Ur=0,sl=!1});C(this,"maybeCleanupAfterMissingPointerUp",t=>{cl?.(),this.missingPointerEventCleanupEmitter.trigger(t).clear()});C(this,"handleCanvasPanUsingWheelOrSpaceDrag",t=>{if(!(fe.pointers.size<=1&&(t.button===To.WHEEL||t.button===To.MAIN&&Mn||Xo(this.state)||this.state.viewModeEnabled)))return!1;ll=!0,this.focusContainer(),this.state.editingTextElement||t.preventDefault();let r=!1,n=typeof window===void 0?!1:/Linux/.test(window.navigator.platform);Te(this.interactiveCanvas,we.GRABBING);let{clientX:i,clientY:a}=t,l=Ad(c=>{let m=i-c.clientX,d=a-c.clientY;if(i=c.clientX,a=c.clientY,n&&!r&&(Math.abs(m)>1||Math.abs(d)>1)){r=!0;let p=h=>{document.body.removeEventListener("paste",p),h.stopPropagation()},u=()=>{setTimeout(()=>{document.body.removeEventListener("paste",p),window.removeEventListener("pointerup",u)},100)};document.body.addEventListener("paste",p),window.addEventListener("pointerup",u)}this.translateCanvas({scrollX:this.state.scrollX-m/this.state.zoom.value,scrollY:this.state.scrollY-d/this.state.zoom.value})}),s=Fe(cl=()=>{cl=null,ll=!1,Mn||(this.state.viewModeEnabled?Te(this.interactiveCanvas,we.GRAB):go(this.interactiveCanvas,this.state)),this.setState({cursorButton:"up"}),this.savePointer(t.clientX,t.clientY,"up"),window.removeEventListener("pointermove",l),window.removeEventListener("pointerup",s),window.removeEventListener("blur",s),l.flush()});return window.addEventListener("blur",s),window.addEventListener("pointermove",l,{passive:!0}),window.addEventListener("pointerup",s),!0});C(this,"clearSelectionIfNotUsingSelection",()=>{this.state.activeTool.type!=="selection"&&this.setState({selectedElementIds:Ee({},this.state),selectedGroupIds:{},editingGroupId:null,activeEmbeddable:null})});C(this,"handleSelectionOnPointerDown",(t,r)=>{if(this.state.activeTool.type==="selection"){let n=this.scene.getNonDeletedElements(),i=this.scene.getNonDeletedElementsMap(),a=this.scene.getSelectedElements(this.state);if(a.length===1&&!this.state.editingLinearElement&&!ee(a[0])&&!(this.state.selectedLinearElement&&this.state.selectedLinearElement.hoverPointIndex!==-1)){let l=op(n,this.state,r.origin.x,r.origin.y,this.state.zoom,t.pointerType,this.scene.getNonDeletedElementsMap(),this.device);l!=null&&(l.transformHandleType==="rotation"?(this.setState({resizingElement:l.element}),r.resize.handleType=l.transformHandleType):(this.state.croppingElementId||this.setState({resizingElement:l.element}),r.resize.handleType=l.transformHandleType))}else a.length>1&&(r.resize.handleType=rp(Se(a),r.origin.x,r.origin.y,this.state.zoom,t.pointerType,this.device));if(r.resize.handleType)r.resize.isResizing=!0,r.resize.offset=pr(RE(r.resize.handleType,a,i,r.origin.x,r.origin.y)),a.length===1&&he(a[0])&&a[0].points.length===2&&(r.resize.arrowDirection=NE(r.resize.handleType,a[0]));else{if(this.state.selectedLinearElement){let c=this.state.editingLinearElement||this.state.selectedLinearElement,m=K.handlePointerDown(t,this,this.store,r.origin,c,this.scene);if(m.hitElement&&(r.hit.element=m.hitElement),m.linearElementEditor&&(this.setState({selectedLinearElement:m.linearElementEditor}),this.state.editingLinearElement&&this.setState({editingLinearElement:m.linearElementEditor})),m.didAddPoint)return!0}if(r.hit.element=r.hit.element??this.getElementAtPosition(r.origin.x,r.origin.y),this.hitLinkElement=this.getElementLinkAtPosition(r.origin,r.hit.element),this.hitLinkElement)return!0;if(this.state.croppingElementId&&r.hit.element?.id!==this.state.croppingElementId&&this.finishImageCropping(),r.hit.element&&this.getElementLinkAtPosition({x:r.origin.x,y:r.origin.y},r.hit.element))return!1;r.hit.allHitElements=this.getElementsAtPosition(r.origin.x,r.origin.y);let l=r.hit.element,s=r.hit.allHitElements.some(c=>this.isASelectedElement(c));if((l===null||!s)&&!t.shiftKey&&!r.hit.hasHitCommonBoundingBoxOfSelectedElements&&this.clearSelection(l),this.state.editingLinearElement)this.setState({selectedElementIds:Ee({[this.state.editingLinearElement.elementId]:!0},this.state)});else if(l!=null){if(t[y.CTRL_OR_CMD])return this.state.selectedElementIds[l.id]||(r.hit.wasAddedToSelection=!0),this.setState(c=>({...pf(c,l),previousSelectedElementIds:this.state.selectedElementIds})),!1;this.state.selectedElementIds[l.id]||(this.state.editingGroupId&&!Zn(l,this.state.editingGroupId)&&this.setState({selectedElementIds:Ee({},this.state),selectedGroupIds:{},editingGroupId:null,activeEmbeddable:null}),!s&&!r.hit.hasHitCommonBoundingBoxOfSelectedElements&&(this.setState(c=>{let m={...c.selectedElementIds,[l.id]:!0},d=[];if(Object.keys(c.selectedElementIds).forEach(p=>{let u=this.scene.getElement(p);u&&d.push(u)}),ie(l))ko(d,l.id).forEach(p=>{delete m[p.id]});else if(l.frameId)m[l.frameId]&&delete m[l.id];else{let p=l.groupIds,u=new Set(p.flatMap(h=>Le(this.scene.getNonDeletedElements(),h)).filter(h=>ie(h)).map(h=>h.id));u.size>0&&d.forEach(h=>{h.frameId&&u.has(h.frameId)&&(delete m[h.id],h.groupIds.flatMap(f=>Le(this.scene.getNonDeletedElements(),f)).forEach(f=>{delete m[f.id]}))})}return c.openDialog?.name==="elementLinkSelector"&&(l.groupIds.some(p=>c.selectedGroupIds[p])||(m={[l.id]:!0})),{...We({editingGroupId:c.editingGroupId,selectedElementIds:m},this.scene.getNonDeletedElements(),c,this),showHyperlinkPopup:l.link||Et(l)?"info":!1}}),r.hit.wasAddedToSelection=!0))}this.setState({previousSelectedElementIds:this.state.selectedElementIds})}}return!1});C(this,"handleTextOnPointerDown",(t,r)=>{if(this.state.editingTextElement)return;let n=r.origin.x,i=r.origin.y,a=this.getElementAtPosition(n,i,{includeBoundTextElement:!0}),l=this.getTextBindableContainerAtPosition(n,i);ut(a)&&(l=a,n=a.x+a.width/2,i=a.y+a.height/2),this.startTextEditing({sceneX:n,sceneY:i,insertAtParentCenter:!t.altKey,container:l,autoEdit:!1}),Zt(this.interactiveCanvas),this.state.activeTool.locked||this.setState({activeTool:Be(this.state,{type:"selection"})})});C(this,"handleFreeDrawElementOnPointerDown",(t,r,n)=>{let[i,a]=nt(n.origin.x,n.origin.y,null),l=this.getTopLayerFrameAtSceneCoords({x:i,y:a}),s=t.pressure===.5,c=rx({type:r,x:i,y:a,strokeColor:this.state.currentItemStrokeColor,backgroundColor:this.state.currentItemBackgroundColor,fillStyle:this.state.currentItemFillStyle,strokeWidth:this.state.currentItemStrokeWidth,strokeStyle:this.state.currentItemStrokeStyle,roughness:this.state.currentItemRoughness,opacity:this.state.currentItemOpacity,roundness:null,simulatePressure:s,locked:!1,frameId:l?l.id:null,points:[z(0,0)],pressures:s?[]:[t.pressure]});this.scene.insertElement(c),this.setState(d=>{let p={...d.selectedElementIds};return delete p[c.id],{selectedElementIds:Ee(p,d)}});let m=Er(n.origin,this.scene.getNonDeletedElements(),this.scene.getNonDeletedElementsMap(),this.state.zoom);this.setState({newElement:c,startBoundElement:m,suggestedBindings:[]})});C(this,"insertIframeElement",({sceneX:t,sceneY:r,width:n,height:i})=>{let[a,l]=nt(t,r,this.lastPointerDownEvent?.[y.CTRL_OR_CMD]?null:this.getEffectiveGridSize()),s=tx({type:"iframe",x:a,y:l,strokeColor:"transparent",backgroundColor:"transparent",fillStyle:this.state.currentItemFillStyle,strokeWidth:this.state.currentItemStrokeWidth,strokeStyle:this.state.currentItemStrokeStyle,roughness:this.state.currentItemRoughness,roundness:this.getCurrentItemRoundness("iframe"),opacity:this.state.currentItemOpacity,locked:!1,width:n,height:i});return this.scene.insertElement(s),s});C(this,"insertEmbeddableElement",({sceneX:t,sceneY:r,link:n})=>{let[i,a]=nt(t,r,this.lastPointerDownEvent?.[y.CTRL_OR_CMD]?null:this.getEffectiveGridSize()),l=dn(n);if(!l)return;l.error instanceof URIError&&this.setToast({message:g("toast.unrecognizedLinkFormat"),closable:!0});let s=km({type:"embeddable",x:i,y:a,strokeColor:"transparent",backgroundColor:"transparent",fillStyle:this.state.currentItemFillStyle,strokeWidth:this.state.currentItemStrokeWidth,strokeStyle:this.state.currentItemStrokeStyle,roughness:this.state.currentItemRoughness,roundness:this.getCurrentItemRoundness("embeddable"),opacity:this.state.currentItemOpacity,locked:!1,width:l.intrinsicSize.w,height:l.intrinsicSize.h,link:n});return this.scene.insertElement(s),s});C(this,"createImageElement",({sceneX:t,sceneY:r,addToFrameUnderCursor:n=!0})=>{let[i,a]=nt(t,r,this.lastPointerDownEvent?.[y.CTRL_OR_CMD]?null:this.getEffectiveGridSize()),l=n?this.getTopLayerFrameAtSceneCoords({x:i,y:a}):null;return is({type:"image",x:i,y:a,strokeColor:this.state.currentItemStrokeColor,backgroundColor:this.state.currentItemBackgroundColor,fillStyle:this.state.currentItemFillStyle,strokeWidth:this.state.currentItemStrokeWidth,strokeStyle:this.state.currentItemStrokeStyle,roughness:this.state.currentItemRoughness,roundness:null,opacity:this.state.currentItemOpacity,locked:!1,frameId:l?l.id:null})});C(this,"handleLinearElementOnPointerDown",(t,r,n)=>{if(this.state.multiElement){let{multiElement:i}=this.state;if(i.type==="line"&&xa(i.points,this.state.zoom.value)){P(i,{lastCommittedPoint:i.points[i.points.length-1]}),this.actionManager.executeAction(Ht);return}if(ee(i)&&i.points.length>1){P(i,{lastCommittedPoint:i.points[i.points.length-1]}),this.actionManager.executeAction(Ht);return}let{x:a,y:l,lastCommittedPoint:s}=i;if(i.points.length>1&&s&&dr(z(n.origin.x-a,n.origin.y-l),s)<El){this.actionManager.executeAction(Ht);return}this.setState(c=>({selectedElementIds:Ee({...c.selectedElementIds,[i.id]:!0},c)})),P(i,{lastCommittedPoint:i.points[i.points.length-1]}),Te(this.interactiveCanvas,we.POINTER)}else{let[i,a]=nt(n.origin.x,n.origin.y,t[y.CTRL_OR_CMD]?null:this.getEffectiveGridSize()),l=this.getTopLayerFrameAtSceneCoords({x:i,y:a}),{currentItemStartArrowhead:s,currentItemEndArrowhead:c}=this.state,[m,d]=r==="arrow"?[s,c]:[null,null],p=r==="arrow"?ni({type:r,x:i,y:a,strokeColor:this.state.currentItemStrokeColor,backgroundColor:this.state.currentItemBackgroundColor,fillStyle:this.state.currentItemFillStyle,strokeWidth:this.state.currentItemStrokeWidth,strokeStyle:this.state.currentItemStrokeStyle,roughness:this.state.currentItemRoughness,opacity:this.state.currentItemOpacity,roundness:this.state.currentItemArrowType===Je.round?{type:St.PROPORTIONAL_RADIUS}:null,startArrowhead:m,endArrowhead:d,locked:!1,frameId:l?l.id:null,elbowed:this.state.currentItemArrowType===Je.elbow,fixedSegments:this.state.currentItemArrowType===Je.elbow?[]:null}):ns({type:r,x:i,y:a,strokeColor:this.state.currentItemStrokeColor,backgroundColor:this.state.currentItemBackgroundColor,fillStyle:this.state.currentItemFillStyle,strokeWidth:this.state.currentItemStrokeWidth,strokeStyle:this.state.currentItemStrokeStyle,roughness:this.state.currentItemRoughness,opacity:this.state.currentItemOpacity,roundness:this.state.currentItemRoundness==="round"?{type:St.PROPORTIONAL_RADIUS}:null,locked:!1,frameId:l?l.id:null});this.setState(h=>{let f={...h.selectedElementIds};return delete f[p.id],{selectedElementIds:Ee(f,h)}}),P(p,{points:[...p.points,z(0,0)]});let u=Er(n.origin,this.scene.getNonDeletedElements(),this.scene.getNonDeletedElementsMap(),this.state.zoom,ee(p),ee(p));this.scene.insertElement(p),this.setState({newElement:p,startBoundElement:u,suggestedBindings:[]})}});C(this,"createGenericElementOnPointerDown",(t,r)=>{let[n,i]=nt(r.origin.x,r.origin.y,this.lastPointerDownEvent?.[y.CTRL_OR_CMD]?null:this.getEffectiveGridSize()),a=this.getTopLayerFrameAtSceneCoords({x:n,y:i}),l={x:n,y:i,strokeColor:this.state.currentItemStrokeColor,backgroundColor:this.state.currentItemBackgroundColor,fillStyle:this.state.currentItemFillStyle,strokeWidth:this.state.currentItemStrokeWidth,strokeStyle:this.state.currentItemStrokeStyle,roughness:this.state.currentItemRoughness,opacity:this.state.currentItemOpacity,roundness:this.getCurrentItemRoundness(t),locked:!1,frameId:a?a.id:null},s;t==="embeddable"?s=km({type:"embeddable",...l}):s=ho({type:t,...l}),s.type==="selection"?this.setState({selectionElement:s}):(this.scene.insertElement(s),this.setState({multiElement:null,newElement:s}))});C(this,"createFrameElementOnPointerDown",(t,r)=>{let[n,i]=nt(t.origin.x,t.origin.y,this.lastPointerDownEvent?.[y.CTRL_OR_CMD]?null:this.getEffectiveGridSize()),a={x:n,y:i,opacity:this.state.currentItemOpacity,locked:!1,...mo},l=r===kt.magicframe?ba(a):ri(a);this.scene.insertElement(l),this.setState({multiElement:null,newElement:l})});C(this,"restoreReadyToEraseElements",()=>{this.elementsPendingErasure=new Set,this.triggerRender()});C(this,"eraseElements",()=>{let t=!1,r=this.scene.getElementsIncludingDeleted().map(n=>this.elementsPendingErasure.has(n.id)||n.frameId&&this.elementsPendingErasure.has(n.frameId)||qe(n)&&this.elementsPendingErasure.has(n.containerId)?(t=!0,q(n,{isDeleted:!0})):n);this.elementsPendingErasure=new Set,t&&(this.store.shouldCaptureIncrement(),this.scene.replaceAllElements(r))});C(this,"initializeImage",async({imageFile:t,imageElement:r,showCursorImagePreview:n=!1})=>{if(!Es(t))throw new Error(g("errors.unsupportedFileType"));let i=t.type;if(Te(this.interactiveCanvas,"wait"),i===je.svg)try{t=Fm(Hm(await t.text()),t.name)}catch(m){throw console.warn(m),new Error(g("errors.svgImageInsertError"))}let a=await(this.props.generateIdForFile?.(t)||Mx(t));if(!a)throw console.warn("Couldn't generate file id or the supplied `generateIdForFile` didn't resolve to one."),new Error(g("errors.imageInsertError"));if(!this.files[a]?.dataURL){try{t=await wa(t,{maxWidthOrHeight:Lh})}catch(m){console.error("Error trying to resizing image file on insertion",m)}if(t.size>Zd)throw new Error(g("errors.fileTooBig",{maxSize:`${Math.trunc(Zd/1024/1024)}MB`}))}if(n){let m=this.files[a]?.dataURL,d=m&&Px(m);this.setImagePreviewCursor(d||t)}let s=this.files[a]?.dataURL||await vs(t),c=P(r,{fileId:a},!1);return new Promise(async(m,d)=>{try{this.addMissingFiles([{mimeType:i,id:a,dataURL:s,created:Date.now(),lastRetrieved:Date.now()}]);let p=this.imageCache.get(a);p||(this.addNewImagesToImageCache(),await this.updateImageCache([c])),p?.image instanceof Promise&&await p.image,this.state.pendingImageElementId!==c.id&&this.state.newElement?.id!==c.id&&this.initializeImageDimensions(c,!0),m(c)}catch(p){console.error(p),d(new Error(g("errors.imageInsertError")))}finally{n||Zt(this.interactiveCanvas)}})});C(this,"insertImageElement",async(t,r,n)=>{if(!this.isToolSupported("image")){this.setState({errorMessage:g("errors.imageToolNotSupported")});return}this.scene.insertElement(t);try{return await this.initializeImage({imageFile:r,imageElement:t,showCursorImagePreview:n})}catch(i){return P(t,{isDeleted:!0}),this.actionManager.executeAction(Ht),this.setState({errorMessage:i.message||g("errors.imageInsertError")}),null}});C(this,"setImagePreviewCursor",async t=>{let n;try{n=await wa(t,{maxWidthOrHeight:96})}catch(a){throw a.cause==="UNSUPPORTED"?new Error(g("errors.unsupportedFileType")):a}let i=await vs(n);if(t.type===je.svg){let a=await zx(i),l=Math.min(a.height,96),s=l*(a.width/a.height);s>96&&(s=96,l=s*(a.height/a.width));let c=document.createElement("canvas");c.height=l,c.width=s,c.getContext("2d").drawImage(a,0,0,s,l),i=c.toDataURL(je.svg)}this.state.pendingImageElementId&&Te(this.interactiveCanvas,`url(${i}) 4 4, auto`)});C(this,"onImageAction",async({insertOnCanvasDirectly:t})=>{try{let r=this.state.width/2+this.state.offsetLeft,n=this.state.height/2+this.state.offsetTop,{x:i,y:a}=Re({clientX:r,clientY:n},this.state),l=await bs({description:"Image",extensions:Object.keys(ra)}),s=this.createImageElement({sceneX:i,sceneY:a,addToFrameUnderCursor:!1});t?(this.insertImageElement(s,l),this.initializeImageDimensions(s),this.setState({selectedElementIds:Ee({[s.id]:!0},this.state)},()=>{this.actionManager.executeAction(Ht)})):this.setState({pendingImageElementId:s.id},()=>{this.insertImageElement(s,l,!0)})}catch(r){r.name!=="AbortError"?console.error(r):console.warn(r),this.setState({pendingImageElementId:null,newElement:null,activeTool:Be(this.state,{type:"selection"})},()=>{this.actionManager.executeAction(Ht)})}});C(this,"initializeImageDimensions",(t,r=!1)=>{let n=qr(t)&&this.imageCache.get(t.fileId)?.image;if(!n||n instanceof Promise){if(t.width<Vr/this.state.zoom.value&&t.height<Vr/this.state.zoom.value){let i=100/this.state.zoom.value;P(t,{x:t.x-i/2,y:t.y-i/2,width:i,height:i})}return}if(r||t.width<Vr/this.state.zoom.value&&t.height<Vr/this.state.zoom.value){let i=Math.max(this.state.height-120,160),a=Math.min(i,Math.floor(this.state.height*.5)/this.state.zoom.value),l=Math.min(n.naturalHeight,a),s=l*(n.naturalWidth/n.naturalHeight),c=t.x+t.width/2-s/2,m=t.y+t.height/2-l/2;P(t,{x:c,y:m,width:s,height:l,crop:null})}});C(this,"updateImageCache",async(t,r=this.files)=>{let{updatedFiles:n,erroredFiles:i}=await Ux({imageCache:this.imageCache,fileIds:t.map(a=>a.fileId),files:r});if(n.size||i.size)for(let a of t)n.has(a.fileId)&&fr.delete(a);return i.size&&this.scene.replaceAllElements(this.scene.getElementsIncludingDeleted().map(a=>qr(a)&&i.has(a.fileId)?q(a,{status:"error"}):a)),{updatedFiles:n,erroredFiles:i}});C(this,"addNewImagesToImageCache",async(t=Hx(this.scene.getNonDeletedElements()),r=this.files)=>{let n=t.filter(i=>!i.isDeleted&&!this.imageCache.has(i.fileId));if(n.length){let{updatedFiles:i}=await this.updateImageCache(n,r);i.size&&this.scene.triggerUpdate()}});C(this,"scheduleImageRefresh",eM(()=>{this.addNewImagesToImageCache()},xh));C(this,"updateBindingEnabledOnPointerMove",t=>{let r=bx(t);this.state.isBindingEnabled!==r&&this.setState({isBindingEnabled:r})});C(this,"maybeSuggestBindingAtCursor",(t,r)=>{let n=Er(t,this.scene.getNonDeletedElements(),this.scene.getNonDeletedElementsMap(),this.state.zoom,!1,r);this.setState({suggestedBindings:n!=null?[n]:[]})});C(this,"maybeSuggestBindingsForLinearElementAtCoords",(t,r,n)=>{if(!r.length)return;let i=r.reduce((a,l)=>{let s=Er(l,this.scene.getNonDeletedElements(),this.scene.getNonDeletedElementsMap(),this.state.zoom,ee(t),ee(t));return s!=null&&!yx(t,n?.id,s)&&a.push(s),a},[]);this.setState({suggestedBindings:i})});C(this,"handleInteractiveCanvasRef",t=>{t!==null?(this.interactiveCanvas=t,this.interactiveCanvas.addEventListener("touchstart",this.onTouchStart,{passive:!1}),this.interactiveCanvas.addEventListener("touchend",this.onTouchEnd)):(this.interactiveCanvas?.removeEventListener("touchstart",this.onTouchStart),this.interactiveCanvas?.removeEventListener("touchend",this.onTouchEnd))});C(this,"handleAppOnDrop",async t=>{let{file:r,fileHandle:n}=await Ox(t),{x:i,y:a}=Re(t,this.state);try{if(Es(r)&&this.isToolSupported("image")){if(r?.type===je.png||r?.type===je.svg)try{let c=await ys(r,this.state,this.scene.getElementsIncludingDeleted(),n);this.syncActionResult({...c,appState:{...c.appState||this.state,isLoading:!1},replaceFiles:!0,captureUpdate:L.IMMEDIATELY});return}catch(c){if(c.name!=="EncodingError")throw new Error(g("alerts.couldNotLoadInvalidFile"))}let s=this.createImageElement({sceneX:i,sceneY:a});this.insertImageElement(s,r),this.initializeImageDimensions(s),this.setState({selectedElementIds:Ee({[s.id]:!0},this.state)});return}}catch(s){return this.setState({isLoading:!1,errorMessage:s.message})}let l=t.dataTransfer.getData(je.excalidrawlib);if(l&&typeof l=="string"){try{let s=_x(l);this.addElementsFromPasteOrLibrary({elements:_c(s),position:t,files:null})}catch(s){this.setState({errorMessage:s.message})}return}if(r&&await this.loadFileToCanvas(r,n),t.dataTransfer?.types?.includes("text/plain")){let s=t.dataTransfer?.getData("text");if(s&&li(s,this.props.validateEmbeddable)&&(/^(http|https):\/\/[^\s/$.?#].[^\s]*$/.test(s)||dn(s)?.type==="video")){let c=this.insertEmbeddableElement({sceneX:i,sceneY:a,link:uo(s)});c&&this.setState({selectedElementIds:{[c.id]:!0}})}}});C(this,"loadFileToCanvas",async(t,r)=>{t=await Fx(t);try{let n=this.scene.getElementsIncludingDeleted(),i;try{i=await Nm(t,this.state,n,r)}catch(a){let l=a instanceof tf;if(l&&a.code==="IMAGE_NOT_CONTAINS_SCENE_DATA"&&!this.isToolSupported("image")){this.setState({isLoading:!1,errorMessage:g("errors.imageToolNotSupported")});return}let s=l?g("alerts.cannotRestoreFromImage"):g("alerts.couldNotLoadInvalidFile");this.setState({isLoading:!1,errorMessage:s})}if(!i)return;i.type===je.excalidraw?(Jo(n.concat(i.data.elements)),this.store.updateSnapshot(Q(n),this.state),this.setState({isLoading:!0}),this.syncActionResult({...i.data,appState:{...i.data.appState||this.state,isLoading:!1},replaceFiles:!0,captureUpdate:L.IMMEDIATELY})):i.type===je.excalidrawlib&&await this.library.updateLibrary({libraryItems:t,merge:!0,openLibraryMenu:!0}).catch(a=>{console.error(a),this.setState({errorMessage:g("errors.importLibraryError")})})}catch(n){this.setState({isLoading:!1,errorMessage:n.message})}});C(this,"handleCanvasContextMenu",t=>{if(t.preventDefault(),("pointerType"in t.nativeEvent&&t.nativeEvent.pointerType==="touch"||"pointerType"in t.nativeEvent&&t.nativeEvent.pointerType==="pen"&&t.button!==To.SECONDARY)&&this.state.activeTool.type!=="selection")return;let{x:r,y:n}=Re(t,this.state),i=this.getElementAtPosition(r,n,{preferSelected:!0,includeLockedElements:!0}),a=this.scene.getSelectedElements(this.state),l=this.isHittingCommonBoundingBoxOfSelectedElements({x:r,y:n},a),s=i||l?"element":"canvas",c=this.excalidrawContainerRef.current,{top:m,left:d}=c.getBoundingClientRect(),p=t.clientX-d,u=t.clientY-m;le("contextMenu","openContextMenu",s),this.setState({...i&&!this.state.selectedElementIds[i.id]?{...this.state,...We({editingGroupId:this.state.editingGroupId,selectedElementIds:{[i.id]:!0}},this.scene.getNonDeletedElements(),this.state,this),selectedLinearElement:he(i)?new K(i):null}:this.state,showHyperlinkPopup:!1},()=>{this.setState({contextMenu:{top:u,left:p,items:this.getContextMenuItems(s)}})})});C(this,"maybeDragNewGenericElement",(t,r,n=!0)=>{let i=this.state.selectionElement,a=t.lastCoords;if(i&&this.state.activeTool.type!=="eraser"){ip({newElement:i,elementType:this.state.activeTool.type,originX:t.origin.x,originY:t.origin.y,x:a.x,y:a.y,width:jr(t.origin.x,a.x),height:jr(t.origin.y,a.y),shouldMaintainAspectRatio:ii(r),shouldResizeFromCenter:cs(r),zoom:this.state.zoom.value,informMutation:n});return}let l=this.state.newElement;if(!l)return;let[s,c]=nt(a.x,a.y,r[y.CTRL_OR_CMD]?null:this.getEffectiveGridSize()),m=qr(l)&&this.imageCache.get(l.fileId)?.image,d=m&&!(m instanceof Promise)?m.width/m.height:null;this.maybeCacheReferenceSnapPoints(r,[l]);let{snapOffset:p,snapLines:u}=gE(l,this,r,{x:t.originInGrid.x+(this.state.originSnapOffset?.x??0),y:t.originInGrid.y+(this.state.originSnapOffset?.y??0)},{x:s-t.originInGrid.x,y:c-t.originInGrid.y},this.scene.getNonDeletedElementsMap());s+=p.x,c+=p.y,this.setState({snapLines:u}),ip({newElement:l,elementType:this.state.activeTool.type,originX:t.originInGrid.x,originY:t.originInGrid.y,x:s,y:c,width:jr(t.originInGrid.x,s),height:jr(t.originInGrid.y,c),shouldMaintainAspectRatio:xe(l)?!ii(r):ii(r),shouldResizeFromCenter:cs(r),zoom:this.state.zoom.value,widthAspectRatio:d,originOffset:this.state.originSnapOffset,informMutation:n}),this.setState({newElement:l}),(this.state.activeTool.type===kt.frame||this.state.activeTool.type===kt.magicframe)&&this.setState({elementsToHighlight:di(this.scene.getNonDeletedElements(),l,this.state,this.scene.getNonDeletedElementsMap())})});C(this,"maybeHandleCrop",(t,r)=>{if(!this.state.croppingElementId)return!1;let n=t.resize.handleType,i=t.lastCoords,[a,l]=nt(i.x-t.resize.offset.x,i.y-t.resize.offset.y,r[y.CTRL_OR_CMD]?null:this.getEffectiveGridSize()),s=this.scene.getNonDeletedElementsMap().get(this.state.croppingElementId);if(n&&s&&xe(s)){let c=t.originalElements.get(s.id),m=qr(s)&&this.imageCache.get(s.fileId)?.image;if(c&&xe(c)&&m&&!(m instanceof Promise)){let[d,p]=nt(i.x,i.y,r[y.CTRL_OR_CMD]?null:this.getEffectiveGridSize()),u={x:d-t.originInGrid.x,y:p-t.originInGrid.y};this.maybeCacheReferenceSnapPoints(r,[s]);let{snapOffset:h,snapLines:f}=Jm([s],[c],this,r,u,n);P(s,Cx(s,n,m.naturalWidth,m.naturalHeight,a+h.x,l+h.y,r.shiftKey?c.width/c.height:void 0)),Jt(s,this.scene.getNonDeletedElementsMap(),{newSize:{width:s.width,height:s.height}}),this.setState({isCropping:n&&n!=="rotation",snapLines:f})}return!0}return!1});C(this,"maybeHandleResize",(t,r)=>{let n=this.scene.getSelectedElements(this.state),i=n.filter(d=>ie(d)),a=t.resize.handleType;if(i.length>0&&a==="rotation"||n.length===1&&ee(n[0])||this.state.croppingElementId)return!1;this.setState({isResizing:a&&a!=="rotation",isRotating:a==="rotation",activeEmbeddable:null});let l=t.lastCoords,[s,c]=nt(l.x-t.resize.offset.x,l.y-t.resize.offset.y,r[y.CTRL_OR_CMD]?null:this.getEffectiveGridSize()),m=new Map;if(i.forEach(d=>{ko(this.scene.getNonDeletedElements(),d.id).forEach(u=>{m.set(d.id+u.id,{x:u.x-d.x,y:u.y-d.y})})}),!this.state.selectedElementsAreBeingDragged){let[d,p]=nt(l.x,l.y,r[y.CTRL_OR_CMD]?null:this.getEffectiveGridSize()),u={x:d-t.originInGrid.x,y:p-t.originInGrid.y},h=[...t.originalElements.values()];this.maybeCacheReferenceSnapPoints(r,n);let{snapOffset:f,snapLines:b}=Jm(n,$(h,this.state),this,r,u,a);s+=f.x,c+=f.y,this.setState({snapLines:b})}if(DE(t.originalElements,a,n,this.scene.getElementsMapIncludingDeleted(),this.scene,ds(r),cs(r),n.some(d=>xe(d))?!ii(r):ii(r),s,c,t.resize.center.x,t.resize.center.y)){let d=gs(n,this.scene.getNonDeletedElementsMap(),this.state.zoom),p=new Set;return i.forEach(u=>{di(this.scene.getNonDeletedElements(),u,this.state,this.scene.getNonDeletedElementsMap()).forEach(h=>p.add(h))}),this.setState({elementsToHighlight:[...p],suggestedBindings:d}),!0}return!1});C(this,"getContextMenuItems",t=>{let r=[];return r.push(wc,vc),t==="canvas"?this.state.viewModeEnabled?[...r,Cc,Mi,wg,Di]:[Qp,$e,wc,vc,Tc,$e,bp,Cv,$e,Cc,eu,Mi,wg,Di]:(r.push(Tc),this.state.viewModeEnabled?[_i,...r]:[$e,yc,_i,Qp,$e,N1,O1,F1,$e,Du,$e,...r,$e,Wp,Kp,$e,Xp,aT,tu,ou,fv,$p,$e,jp,$e,up,gp,hp,fp,$e,Jp,qp,$e,Mu,$e,Ga,Vc,$e,xp,cu,$e,_a])});C(this,"handleWheel",Fe(t=>{if(!(t.target instanceof HTMLCanvasElement||t.target instanceof HTMLTextAreaElement||t.target instanceof HTMLIFrameElement)){t[y.CTRL_OR_CMD]&&t.preventDefault();return}if(t.preventDefault(),ll)return;let{deltaX:r,deltaY:n}=t;if(t.metaKey||t.ctrlKey){let i=Math.sign(n),a=Fn*100,l=Math.abs(n),s=n;l>a&&(s=a*i);let c=this.state.zoom.value-s/100;c+=Math.log10(Math.max(1,this.state.zoom.value))*-i*Math.min(1,l/20),this.translateCanvas(m=>({...Qo({viewportX:this.lastViewportPosition.x,viewportY:this.lastViewportPosition.y,nextZoom:Ao(c)},m),shouldCacheIgnoreZoom:!0})),this.resetShouldCacheIgnoreZoomDebounced();return}if(t.shiftKey){this.translateCanvas(({zoom:i,scrollX:a})=>({scrollX:a-(n||r)/i.value}));return}this.translateCanvas(({zoom:i,scrollX:a,scrollY:l})=>({scrollX:a-r/i.value,scrollY:l-n/i.value}))}));C(this,"savePointer",(t,r,n)=>{if(!t||!r)return;let{x:i,y:a}=Re({clientX:t,clientY:r},this.state);isNaN(i)||isNaN(a);let l={x:i,y:a,tool:this.state.activeTool.type==="laser"?"laser":"pointer"};this.props.onPointerUpdate?.({pointer:l,button:n,pointersMap:fe.pointers})});C(this,"resetShouldCacheIgnoreZoomDebounced",$r(()=>{this.unmounted||this.setState({shouldCacheIgnoreZoom:!1})},300));C(this,"updateDOMRect",t=>{if(this.excalidrawContainerRef?.current){let r=this.excalidrawContainerRef.current,{width:n,height:i,left:a,top:l}=r.getBoundingClientRect(),{width:s,height:c,offsetTop:m,offsetLeft:d}=this.state;if(n===s&&i===c&&a===d&&l===m){t&&t();return}this.setState({width:n,height:i,offsetLeft:a,offsetTop:l},()=>{t&&t()})}});C(this,"refresh",()=>{this.setState({...this.getCanvasOffsets()})});let r=$n(),{excalidrawAPI:n,viewModeEnabled:i=!1,zenModeEnabled:a=!1,gridModeEnabled:l=!1,objectsSnapModeEnabled:s=!1,theme:c=r.theme,name:m=`${g("labels.untitled")}-${om()}`}=t;if(this.state={...r,theme:c,isLoading:!0,...this.getCanvasOffsets(),viewModeEnabled:i,zenModeEnabled:a,objectsSnapModeEnabled:s,gridModeEnabled:l??r.gridModeEnabled,name:m,width:window.innerWidth,height:window.innerHeight},this.id=Q5(),this.library=new Ov(this),this.actionManager=new $c(this.syncActionResult,()=>this.state,()=>this.scene.getElementsIncludingDeleted(),this),this.scene=new bo,this.canvas=document.createElement("canvas"),this.rc=q5.canvas(this.canvas),this.renderer=new il(this.scene),this.visibleElements=[],this.store=new wx,this.history=new Xa,n){let d={updateScene:this.updateScene,updateLibrary:this.library.updateLibrary,addFiles:this.addFiles,resetScene:this.resetScene,getSceneElementsIncludingDeleted:this.getSceneElementsIncludingDeleted,history:{clear:this.resetHistory},scrollToContent:this.scrollToContent,getSceneElements:this.getSceneElements,getAppState:()=>this.state,getFiles:()=>this.files,getName:this.getName,registerAction:p=>{this.actionManager.registerAction(p)},refresh:this.refresh,setToast:this.setToast,id:this.id,setActiveTool:this.setActiveTool,setCursor:this.setCursor,resetCursor:this.resetCursor,updateFrameRendering:this.updateFrameRendering,toggleSidebar:this.toggleSidebar,onChange:p=>this.onChangeEmitter.on(p),onPointerDown:p=>this.onPointerDownEmitter.on(p),onPointerUp:p=>this.onPointerUpEmitter.on(p),onScrollChange:p=>this.onScrollChangeEmitter.on(p),onUserFollow:p=>this.onUserFollowEmitter.on(p)};typeof n=="function"?n(d):console.error("excalidrawAPI should be a function!")}this.excalidrawContainerValue={container:this.excalidrawContainerRef.current,id:this.id},this.fonts=new br(this.scene),this.history=new Xa,this.actionManager.registerAll(bf),this.actionManager.registerAction(Aw(this.history,this.store)),this.actionManager.registerAction(_w(this.history,this.store))}onWindowMessage(t){if(t.origin!=="https://player.vimeo.com"&&t.origin!=="https://www.youtube.com")return;let r=null;try{r=JSON.parse(t.data)}catch{}if(r)switch(t.origin){case"https://player.vimeo.com":if(r.method==="paused"){let n=null,i=document.body.querySelectorAll("iframe.excalidraw__embeddable");if(!i)break;for(let a of i)a.contentWindow===t.source&&(n=a.contentWindow);n?.postMessage(JSON.stringify({method:r.value?"play":"pause",value:!0}),"*")}break;case"https://www.youtube.com":if(r.event==="infoDelivery"&&r.info&&r.id&&typeof r.info.playerState=="number"){let n=r.id,i=r.info.playerState;Object.values(ta).includes(i)&&Rg.set(n,i)}break}}cacheEmbeddableRef(t,r){r&&this.iFrameRefs.set(t.id,r)}getHTMLIFrameElement(t){return this.iFrameRefs.get(t.id)}handleEmbeddableCenterClick(t){if(this.state.activeEmbeddable?.element===t&&this.state.activeEmbeddable?.state==="active"||(setTimeout(()=>{this.setState({activeEmbeddable:{element:t,state:"active"},selectedElementIds:{[t.id]:!0},newElement:null,selectionElement:null})},100),Qr(t)))return;let r=this.getHTMLIFrameElement(t);if(r?.contentWindow){if(r.src.includes("youtube")){let n=Rg.get(t.id);switch(n||(Rg.set(t.id,ta.UNSTARTED),r.contentWindow.postMessage(JSON.stringify({event:"listening",id:t.id}),"*")),n){case ta.PLAYING:case ta.BUFFERING:r.contentWindow?.postMessage(JSON.stringify({event:"command",func:"pauseVideo",args:""}),"*");break;default:r.contentWindow?.postMessage(JSON.stringify({event:"command",func:"playVideo",args:""}),"*")}}r.src.includes("player.vimeo.com")&&r.contentWindow.postMessage(JSON.stringify({method:"paused"}),"*")}}isIframeLikeElementCenter(t,r,n,i){return t&&!r.altKey&&!r.shiftKey&&!r.metaKey&&!r.ctrlKey&&(this.state.activeEmbeddable?.element!==t||this.state.activeEmbeddable?.state==="hover"||!this.state.activeEmbeddable)&&n>=t.x+t.width/3&&n<=t.x+2*t.width/3&&i>=t.y+t.height/3&&i<=t.y+2*t.height/3}renderEmbeddables(){let t=this.state.zoom.value,r=this.state.width,n=this.state.height,i=this.scene.getNonDeletedElements().filter(a=>Et(a)&&this.embedsValidationStatus.get(a.id)===!0||Qr(a));return Ie(oM,{children:i.map(a=>{let{x:l,y:s}=xt({sceneX:a.x,sceneY:a.y},this.state),c=ka(a,r,n,this.state,this.scene.getNonDeletedElementsMap()),m=this.initializedEmbeds.has(a.id);if(c&&!m&&this.initializedEmbeds.add(a.id),!(c||m))return null;let p;if(Qr(a)){p=null;let f=(a.customData?.generationData??this.magicGenerations.get(a.id))||{status:"error",message:"No generation data",code:"ERR_NO_GENERATION_DATA"};if(f.status==="done"){let b=f.html;p={intrinsicSize:{w:a.width,h:a.height},type:"document",srcdoc:()=>b}}else if(f.status==="pending")p={intrinsicSize:{w:a.width,h:a.height},type:"document",srcdoc:()=>_m(`
                    <style>
                      html, body {
                        width: 100%;
                        height: 100%;
                        color: ${this.state.theme===re.DARK?"white":"black"};
                      }
                      body {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-direction: column;
                        gap: 1rem;
                      }

                      .Spinner {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-left: auto;
                        margin-right: auto;
                      }

                      .Spinner svg {
                        animation: rotate 1.6s linear infinite;
                        transform-origin: center center;
                        width: 40px;
                        height: 40px;
                      }

                      .Spinner circle {
                        stroke: currentColor;
                        animation: dash 1.6s linear 0s infinite;
                        stroke-linecap: round;
                      }

                      @keyframes rotate {
                        100% {
                          transform: rotate(360deg);
                        }
                      }

                      @keyframes dash {
                        0% {
                          stroke-dasharray: 1, 300;
                          stroke-dashoffset: 0;
                        }
                        50% {
                          stroke-dasharray: 150, 300;
                          stroke-dashoffset: -200;
                        }
                        100% {
                          stroke-dasharray: 1, 300;
                          stroke-dashoffset: -280;
                        }
                      }
                    </style>
                    <div class="Spinner">
                      <svg
                        viewBox="0 0 100 100"
                      >
                        <circle
                          cx="50"
                          cy="50"
                          r="46"
                          stroke-width="8"
                          fill="none"
                          stroke-miter-limit="10"
                        />
                      </svg>
                    </div>
                    <div>Generating...</div>
                  `)};else{let b;f.code==="ERR_GENERATION_INTERRUPTED"?b="Generation was interrupted...":b=f.message||"Generation failed",p={intrinsicSize:{w:a.width,h:a.height},type:"document",srcdoc:()=>_m(`
                    <style>
                    html, body {
                      height: 100%;
                    }
                      body {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        color: ${Ho.red[3]};
                      }
                      h1, h3 {
                        margin-top: 0;
                        margin-bottom: 0.5rem;
                      }
                    </style>
                    <h1>Error!</h1>
                    <h3>${b}</h3>
                  `)}}}else p=dn(Ul(a.link||""));let u=this.state.activeEmbeddable?.element===a&&this.state.activeEmbeddable?.state==="active",h=this.state.activeEmbeddable?.element===a&&this.state.activeEmbeddable?.state==="hover";return Ie("div",{className:mT("excalidraw__embeddable-container",{"is-hovered":h}),style:{transform:c?`translate(${l-this.state.offsetLeft}px, ${s-this.state.offsetTop}px) scale(${t})`:"none",display:c?"block":"none",opacity:sE(a,Ps(a,this.scene.getNonDeletedElementsMap()),this.elementsPendingErasure,null,this.state.openDialog?.name==="elementLinkSelector"?Mh:1),"--embeddable-radius":`${ps(Math.min(a.width,a.height),a)}px`},children:Nd("div",{className:"excalidraw__embeddable-container__inner",style:{width:c?`${a.width}px`:0,height:c?`${a.height}px`:0,transform:c?`rotate(${a.angle}rad)`:"none",pointerEvents:u?Wr.enabled:Wr.disabled},children:[h&&Ie("div",{className:"excalidraw__embeddable-hint",children:g("buttons.embeddableInteractionButton")}),Ie("div",{className:"excalidraw__embeddable__outer",style:{padding:`${a.strokeWidth}px`},children:(Et(a)?this.props.renderEmbeddable?.(a,this.state):null)??Ie("iframe",{ref:f=>this.cacheEmbeddableRef(a,f),className:"excalidraw__embeddable",srcDoc:p?.type==="document"?p.srcdoc(this.state.theme):void 0,src:p?.type!=="document"?p?.link??"":void 0,scrolling:"no",referrerPolicy:"no-referrer-when-downgrade",title:"Excalidraw Embedded Content",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0,sandbox:`${p?.sandbox?.allowSameOrigin?"allow-same-origin":""} allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-presentation allow-downloads`})})]})},a.id)})})}toggleOverscrollBehavior(t){document.documentElement.style.overscrollBehaviorX=t.type==="pointerenter"?"none":"auto"}render(){let t=this.scene.getSelectedElements(this.state),{renderTopRightUI:r,renderCustomStats:n}=this.props,i=this.scene.getSceneNonce(),{elementsMap:a,visibleElements:l}=this.renderer.getRenderableElements({sceneNonce:i,zoom:this.state.zoom,offsetLeft:this.state.offsetLeft,offsetTop:this.state.offsetTop,scrollX:this.state.scrollX,scrollY:this.state.scrollY,height:this.state.height,width:this.state.width,editingTextElement:this.state.editingTextElement,newElementId:this.state.newElement?.id,pendingImageElementId:this.state.pendingImageElementId});this.visibleElements=l;let s=this.scene.getNonDeletedElementsMap(),c="setPointerCapture"in HTMLElement.prototype?!1:this.state.selectionElement||this.state.newElement||this.state.selectedElementsAreBeingDragged||this.state.resizingElement||this.state.activeTool.type==="laser"&&this.state.cursorButton==="down",m=t[0];return Ie("div",{className:mT("excalidraw excalidraw-container",{"excalidraw--view-mode":this.state.viewModeEnabled||this.state.openDialog?.name==="elementLinkSelector","excalidraw--mobile":this.device.editor.isMobile}),style:{"--ui-pointerEvents":c?Wr.disabled:Wr.enabled,"--right-sidebar-width":"302px"},ref:this.excalidrawContainerRef,onDrop:this.handleAppOnDrop,tabIndex:0,onKeyDown:this.props.handleKeyboardGlobally?void 0:this.onKeyDown,onPointerEnter:this.toggleOverscrollBehavior,onPointerLeave:this.toggleOverscrollBehavior,children:Ie(gT.Provider,{value:this,children:Ie(hT.Provider,{value:this.props,children:Ie(Fg.Provider,{value:this.excalidrawContainerValue,children:Ie(Og.Provider,{value:this.device,children:Ie(Ug.Provider,{value:this.setAppState,children:Ie(zg.Provider,{value:this.state,children:Nd(Bg.Provider,{value:this.scene.getNonDeletedElements(),children:[Nd(Hg.Provider,{value:this.actionManager,children:[Ie(M1,{canvas:this.canvas,appState:this.state,files:this.files,setAppState:this.setAppState,actionManager:this.actionManager,elements:this.scene.getNonDeletedElements(),onLockToggle:this.toggleLock,onPenModeToggle:this.togglePenMode,onHandToolToggle:this.onHandToolToggle,langCode:qo().code,renderTopRightUI:r,renderCustomStats:n,showExitZenModeBtn:typeof this.props?.zenModeEnabled>"u"&&this.state.zenModeEnabled,UIOptions:this.props.UIOptions,onExportImage:this.onExportImage,renderWelcomeScreen:!this.state.isLoading&&this.state.showWelcomeScreen&&this.state.activeTool.type==="selection"&&!this.state.zenModeEnabled&&!this.scene.getElementsIncludingDeleted().length,app:this,isCollaborating:this.props.isCollaborating,generateLinkForSelection:this.props.generateLinkForSelection,children:this.props.children}),Ie("div",{className:"excalidraw-textEditorContainer"}),Ie("div",{className:"excalidraw-contextMenuContainer"}),Ie("div",{className:"excalidraw-eye-dropper-container"}),Ie(tT,{trails:[this.laserTrails,this.eraserTrail]}),t.length===1&&this.state.openDialog?.name!=="elementLinkSelector"&&this.state.showHyperlinkPopup&&Ie(vv,{element:m,elementsMap:s,setAppState:this.setAppState,onLinkOpen:this.props.onLinkOpen,setToast:this.setToast,updateEmbedValidationStatus:this.updateEmbedValidationStatus},m.id),this.props.aiEnabled!==!1&&t.length===1&&Nl(m)&&Ie(Ag,{element:m,elementsMap:a,children:Ie(Md,{title:g("labels.convertToCode"),icon:Ql,checked:!1,onChange:()=>this.onMagicFrameGenerate(m,"button")})}),t.length===1&&Qr(m)&&m.customData?.generationData?.status==="done"&&Nd(Ag,{element:m,elementsMap:a,children:[Ie(Md,{title:g("labels.copySource"),icon:ei,checked:!1,onChange:()=>this.onIframeSrcCopy(m)}),Ie(Md,{title:"Enter fullscreen",icon:Mb,checked:!1,onChange:()=>{let d=this.getHTMLIFrameElement(m);if(d)try{d.requestFullscreen(),this.setState({activeEmbeddable:{element:m,state:"active"},selectedElementIds:{[m.id]:!0},newElement:null,selectionElement:null})}catch(p){console.warn(p),this.setState({errorMessage:"Couldn't enter fullscreen"})}}})]}),this.state.toast!==null&&Ie(P1,{message:this.state.toast.message,onClose:()=>this.setToast(null),duration:this.state.toast.duration,closable:this.state.toast.closable}),this.state.contextMenu&&Ie(Ow,{items:this.state.contextMenu.items,top:this.state.contextMenu.top,left:this.state.contextMenu.left,actionManager:this.actionManager,onClose:d=>{this.setState({contextMenu:null},()=>{this.focusContainer(),d?.()})}}),Ie(Lg,{canvas:this.canvas,rc:this.rc,elementsMap:a,allElementsMap:s,visibleElements:l,sceneNonce:i,selectionNonce:this.state.selectionElement?.versionNonce,scale:window.devicePixelRatio,appState:this.state,renderConfig:{imageCache:this.imageCache,isExporting:!1,renderGrid:gn(this),canvasBackgroundColor:this.state.viewBackgroundColor,embedsValidationStatus:this.embedsValidationStatus,elementsPendingErasure:this.elementsPendingErasure,pendingFlowchartNodes:this.flowChartCreator.pendingNodes}}),this.state.newElement&&Ie(dT,{appState:this.state,scale:window.devicePixelRatio,rc:this.rc,elementsMap:a,allElementsMap:s,renderConfig:{imageCache:this.imageCache,isExporting:!1,renderGrid:!1,canvasBackgroundColor:this.state.viewBackgroundColor,embedsValidationStatus:this.embedsValidationStatus,elementsPendingErasure:this.elementsPendingErasure,pendingFlowchartNodes:null}}),Ie(kg,{containerRef:this.excalidrawContainerRef,canvas:this.interactiveCanvas,elementsMap:a,visibleElements:l,allElementsMap:s,selectedElements:t,sceneNonce:i,selectionNonce:this.state.selectionElement?.versionNonce,scale:window.devicePixelRatio,appState:this.state,device:this.device,renderInteractiveSceneCallback:this.renderInteractiveSceneCallback,handleCanvasRef:this.handleInteractiveCanvasRef,onContextMenu:this.handleCanvasContextMenu,onPointerMove:this.handleCanvasPointerMove,onPointerUp:this.handleCanvasPointerUp,onPointerCancel:this.removePointer,onTouchMove:this.handleTouchMove,onPointerDown:this.handleCanvasPointerDown,onDoubleClick:this.handleCanvasDoubleClick}),this.state.userToFollow&&Ie(nT,{width:this.state.width,height:this.state.height,userToFollow:this.state.userToFollow,onDisconnect:this.maybeUnfollowRemoteUser}),this.renderFrameNames()]}),this.renderEmbeddables()]})})})})})})})})}setPlugins(t){Object.assign(this.plugins,t)}async onMagicFrameGenerate(t,r){let n=this.plugins.diagramToCode?.generate;if(!n){this.setState({errorMessage:"No diagram to code plugin found"});return}let i=Os(this.scene.getNonDeletedElements(),t).filter(l=>!Nl(l));if(!i.length){r==="button"?(this.setState({errorMessage:"Cannot generate from an empty frame"}),le("ai","generate (no-children)","d2c")):this.setActiveTool({type:"magicframe"});return}let a=this.insertIframeElement({sceneX:t.x+t.width+30,sceneY:t.y,width:t.width,height:t.height});if(a){this.updateMagicGeneration({frameElement:a,data:{status:"pending"}}),this.setState({selectedElementIds:{[a.id]:!0}}),le("ai","generate (start)","d2c");try{let{html:l}=await n({frame:t,children:i});if(le("ai","generate (success)","d2c"),!l.trim()){this.updateMagicGeneration({frameElement:a,data:{status:"error",code:"ERR_OAI",message:"Nothing genereated :("}});return}let s=l.includes("<!DOCTYPE html>")&&l.includes("</html>")?l.slice(l.indexOf("<!DOCTYPE html>"),l.indexOf("</html>")+7):l;this.updateMagicGeneration({frameElement:a,data:{status:"done",html:s}})}catch(l){le("ai","generate (failed)","d2c"),this.updateMagicGeneration({frameElement:a,data:{status:"error",code:"ERR_OAI",message:l.message||"Unknown error during generation"}})}}}onIframeSrcCopy(t){t.customData?.generationData?.status==="done"&&(yr(t.customData.generationData.html),this.setToast({message:"copied to clipboard",closable:!1,duration:1500}))}clearImageShapeCache(t){let r=t??this.files;this.scene.getNonDeletedElements().forEach(n=>{qr(n)&&r[n.fileId]&&(this.imageCache.delete(n.fileId),fr.delete(n))})}async componentDidMount(){if(this.unmounted=!1,this.excalidrawContainerValue.container=this.excalidrawContainerRef.current,v.MODE===Rn.TEST||v.DEV){let r=this.setState.bind(this);Object.defineProperties(window.h,{state:{configurable:!0,get:()=>this.state},setState:{configurable:!0,value:(...n)=>this.setState(...n)},app:{configurable:!0,value:this},history:{configurable:!0,value:this.history},store:{configurable:!0,value:this.store},fonts:{configurable:!0,value:this.fonts}})}this.store.onStoreIncrementEmitter.on(r=>{this.history.record(r.elementsChange,r.appStateChange)}),this.scene.onUpdate(this.triggerRender),this.addEventListeners(),this.props.autoFocus&&this.excalidrawContainerRef.current&&this.focusContainer(),sa()||(this.refreshViewportBreakpoints(),this.refreshEditorBreakpoints()),Qi&&this.excalidrawContainerRef.current&&(this.resizeObserver=new ResizeObserver(()=>{this.refreshEditorBreakpoints(),this.updateDOMRect()}),this.resizeObserver?.observe(this.excalidrawContainerRef.current)),new URLSearchParams(window.location.search.slice(1)).has("web-share-target")?this.restoreFileFromShare():this.updateDOMRect(this.initializeScene),dh()&&!nf()&&this.setState({errorMessage:Ie(z1,{})})}componentWillUnmount(){window.launchQueue?.setConsumer(()=>{}),this.renderer.destroy(),this.scene.destroy(),this.scene=new bo,this.fonts=new br(this.scene),this.renderer=new il(this.scene),this.files={},this.imageCache.clear(),this.resizeObserver?.disconnect(),this.unmounted=!0,this.removeEventListeners(),this.library.destroy(),this.laserTrails.stop(),this.eraserTrail.stop(),this.onChangeEmitter.clear(),this.store.onStoreIncrementEmitter.clear(),fr.destroy(),wr.destroy(),clearTimeout(Ur),ke.clearCache(),We.clearCache(),Ur=0,document.documentElement.style.overscrollBehaviorX=""}removeEventListeners(){this.onRemoveEventListenersEmitter.trigger()}addEventListeners(){this.removeEventListeners(),this.props.handleKeyboardGlobally&&this.onRemoveEventListenersEmitter.once(Ae(document,"keydown",this.onKeyDown,!1)),this.onRemoveEventListenersEmitter.once(Ae(this.excalidrawContainerRef.current,"wheel",this.handleWheel,{passive:!1}),Ae(window,"message",this.onWindowMessage,!1),Ae(document,"pointerup",this.removePointer,{passive:!1}),Ae(document,"copy",this.onCopy,{passive:!1}),Ae(document,"keyup",this.onKeyUp,{passive:!0}),Ae(document,"pointermove",this.updateCurrentCursorPosition,{passive:!1}),Ae(document.fonts,"loadingdone",t=>{let r=t.fontfaces;this.fonts.onLoaded(r)},{passive:!1}),Ae(document,"gesturestart",this.onGestureStart,!1),Ae(document,"gesturechange",this.onGestureChange,!1),Ae(document,"gestureend",this.onGestureEnd,!1),Ae(window,"focus",()=>{this.maybeCleanupAfterMissingPointerUp(null),this.triggerRender(!0)},{passive:!1})),!this.state.viewModeEnabled&&(this.onRemoveEventListenersEmitter.once(Ae(document,"fullscreenchange",this.onFullscreenChange,{passive:!1}),Ae(document,"paste",this.pasteFromClipboard,{passive:!1}),Ae(document,"cut",this.onCut,{passive:!1}),Ae(window,"resize",this.onResize,!1),Ae(window,"unload",this.onUnload,!1),Ae(window,"blur",this.onBlur,!1),Ae(this.excalidrawContainerRef.current,"wheel",this.handleWheel,{passive:!1}),Ae(this.excalidrawContainerRef.current,"dragover",this.disableEvent,!1),Ae(this.excalidrawContainerRef.current,"drop",this.disableEvent,!1)),this.props.detectScroll&&this.onRemoveEventListenersEmitter.once(Ae(zh(this.excalidrawContainerRef.current),"scroll",this.onScroll,{passive:!1})))}componentDidUpdate(t,r){this.updateEmbeddables();let n=this.scene.getElementsIncludingDeleted(),i=this.scene.getElementsMapIncludingDeleted(),a=this.scene.getNonDeletedElementsMap();!this.state.showWelcomeScreen&&!n.length&&this.setState({showWelcomeScreen:!0}),t.UIOptions.dockedSidebarBreakpoint!==this.props.UIOptions.dockedSidebarBreakpoint&&this.refreshEditorBreakpoints(),r.userToFollow&&!this.state.collaborators.has(r.userToFollow.socketId)&&this.maybeUnfollowRemoteUser(),(r.zoom.value!==this.state.zoom.value||r.scrollX!==this.state.scrollX||r.scrollY!==this.state.scrollY)&&(this.props?.onScrollChange?.(this.state.scrollX,this.state.scrollY,this.state.zoom),this.onScrollChangeEmitter.trigger(this.state.scrollX,this.state.scrollY,this.state.zoom)),r.userToFollow!==this.state.userToFollow&&(r.userToFollow&&this.onUserFollowEmitter.trigger({userToFollow:r.userToFollow,action:"UNFOLLOW"}),this.state.userToFollow&&this.onUserFollowEmitter.trigger({userToFollow:this.state.userToFollow,action:"FOLLOW"})),Object.keys(this.state.selectedElementIds).length&&$t(this.state)&&this.setState({activeTool:Be(this.state,{type:"selection"})}),this.state.activeTool.type==="eraser"&&r.theme!==this.state.theme&&xf(this.interactiveCanvas,this.state.theme),r.activeTool.type==="selection"&&this.state.activeTool.type!=="selection"&&this.state.showHyperlinkPopup&&this.setState({showHyperlinkPopup:!1}),t.langCode!==this.props.langCode&&this.updateLanguage(),$t(r)&&!$t(this.state)&&this.eraserTrail.endPath(),t.viewModeEnabled!==this.props.viewModeEnabled&&this.setState({viewModeEnabled:!!this.props.viewModeEnabled}),r.viewModeEnabled!==this.state.viewModeEnabled&&(this.addEventListeners(),this.deselectElements()),(r.openDialog?.name==="elementLinkSelector"||this.state.openDialog?.name==="elementLinkSelector")&&r.openDialog?.name!==this.state.openDialog?.name&&(this.deselectElements(),this.setState({hoveredElementIds:{}})),t.zenModeEnabled!==this.props.zenModeEnabled&&this.setState({zenModeEnabled:!!this.props.zenModeEnabled}),t.theme!==this.props.theme&&this.props.theme&&this.setState({theme:this.props.theme}),this.excalidrawContainerRef.current?.classList.toggle("theme--dark",this.state.theme===re.DARK),this.state.editingLinearElement&&!this.state.selectedElementIds[this.state.editingLinearElement.elementId]&&setTimeout(()=>{this.state.editingLinearElement&&this.actionManager.executeAction(Ht)}),this.state.editingTextElement?.isDeleted&&this.setState({editingTextElement:null}),this.state.selectedLinearElement&&!this.state.selectedElementIds[this.state.selectedLinearElement.elementId]&&this.setState({selectedLinearElement:null});let{multiElement:s}=r;r.activeTool!==this.state.activeTool&&s!=null&&sn(this.state)&&Wo(s,!1)&&ya(s,this.state,pr(K.getPointAtIndexGlobalCoordinates(s,-1,a)),this.scene.getNonDeletedElementsMap(),this.scene.getNonDeletedElements()),this.store.commit(i,this.state),this.state.isLoading||(this.props.onChange?.(n,this.state,this.files),this.onChangeEmitter.trigger(n,this.state,this.files))}static resetTapTwice(){al=!1}async addElementsFromMixedContentPaste(t,{isPlainPaste:r,sceneX:n,sceneY:i}){if(!r&&t.some(a=>a.type==="imageUrl")&&this.isToolSupported("image")){let a=t.filter(p=>p.type==="imageUrl").map(p=>p.value),l=await Promise.all(a.map(async p=>{try{return{file:await Nx(p)}}catch(u){let h=u.message;return u.cause==="FETCH_ERROR"?h=g("errors.failedToFetchImage"):u.cause==="UNSUPPORTED"&&(h=g("errors.unsupportedFileType")),{errorMessage:h}}})),s=i,c=!1,m={};for(let p of l)if(p.file){let u=this.createImageElement({sceneX:n,sceneY:s}),h=await this.insertImageElement(u,p.file);h&&(c||(c=!0,s-=h.height/2),P(h,{y:s},!1),s=u.y+u.height+25,m[u.id]=!0)}this.setState({selectedElementIds:Ee(m,this.state)});let d=l.find(p=>!!p.errorMessage);d&&d.errorMessage&&this.setState({errorMessage:d.errorMessage})}else{let a=t.filter(l=>l.type==="text");a.length&&this.addTextFromPaste(a.map(l=>l.value).join(`

`),r)}}addTextFromPaste(t,r=!1){let{x:n,y:i}=Re({clientX:this.lastViewportPosition.x,clientY:this.lastViewportPosition.y},this.state),a={x:n,y:i,strokeColor:this.state.currentItemStrokeColor,backgroundColor:this.state.currentItemBackgroundColor,fillStyle:this.state.currentItemFillStyle,strokeWidth:this.state.currentItemStrokeWidth,strokeStyle:this.state.currentItemStrokeStyle,roundness:null,roughness:this.state.currentItemRoughness,opacity:this.state.currentItemOpacity,text:t,fontSize:this.state.currentItemFontSize,fontFamily:this.state.currentItemFontFamily,textAlign:yl,verticalAlign:Vd,locked:!1},l=lt({fontSize:a.fontSize,fontFamily:a.fontFamily}),s=$o(a.fontFamily),[c,,m]=Zm(this.state),d=Math.max(Math.min((m-c)*.5,800),200),p=10,u=i,h=r?[t]:t.split(`
`),f=h.reduce((b,x,T)=>{let E=tn(x).trim();if(E.length){let w=this.getTopLayerFrameAtSceneCoords({x:n,y:u}),S=Nt(E,l,s),I=S.width>d,_=I?Ol(E,l,d):E;S=I?Nt(_,l,s):S;let k=n-S.width/2,R=u-S.height/2,M=So({...a,x:k,y:R,text:_,originalText:E,lineHeight:s,autoResize:!I,frameId:w?w.id:null});b.push(M),u+=M.height+p}else h[T-1]?.trim()&&(u+=af(a.fontSize,s)+p);return b},[]);f.length!==0&&(this.scene.insertElements(f),this.setState({selectedElementIds:Ee(Object.fromEntries(f.map(b=>[b.id,!0])),this.state)}),!r&&f.length>1&&uT===!1&&!this.device.editor.isMobile&&(this.setToast({message:g("toast.pasteAsSingleElement",{shortcut:A("CtrlOrCmd+Shift+V")}),duration:5e3}),uT=!0),this.store.shouldCaptureIncrement())}handleTextWysiwyg(t,{isExistingElement:r=!1}){let n=this.scene.getElementsMapIncludingDeleted(),i=(a,l)=>{this.scene.replaceAllElements([...this.scene.getElementsIncludingDeleted().map(s=>s.id===t.id&&Y(s)?q(s,{originalText:a,isDeleted:l??s.isDeleted,...ox(s,Qt(s,n),n,a)}):s)])};iT({id:t.id,canvas:this.canvas,getViewportCoords:(a,l)=>{let{x:s,y:c}=xt({sceneX:a,sceneY:l},this.state);return[s-this.state.offsetLeft,c-this.state.offsetTop]},onChange:Fe(a=>{i(a,!1),sp(t)&&Jt(t,this.scene.getNonDeletedElementsMap())}),onSubmit:Fe(({viaKeyboard:a,nextOriginalText:l})=>{let s=!l.trim();if(i(l,s),!s&&a){let c=t.containerId?t.containerId:t.id;_n(()=>{this.setState(m=>({selectedElementIds:Ee({...m.selectedElementIds,[c]:!0},m)}))})}s&&fs(this.scene.getNonDeletedElements(),[t]),(!s||r)&&this.store.shouldCaptureIncrement(),_n(()=>{this.setState({newElement:null,editingTextElement:null})}),this.state.activeTool.locked&&go(this.interactiveCanvas,this.state),this.focusContainer()}),element:t,excalidrawContainer:this.excalidrawContainerRef.current,app:this,autoSelect:!this.device.isTouchScreen}),this.deselectElements(),i(t.originalText,!1)}deselectElements(){this.setState({selectedElementIds:Ee({},this.state),selectedGroupIds:{},editingGroupId:null,activeEmbeddable:null})}getTextElementAtPosition(t,r){let n=this.getElementAtPosition(t,r,{includeBoundTextElement:!0});return n&&Y(n)&&!n.isDeleted?n:null}getElementAtPosition(t,r,n){let i=this.getElementsAtPosition(t,r,n?.includeBoundTextElement,n?.includeLockedElements);if(i.length>1){if(n?.preferSelected){for(let l=i.length-1;l>-1;l--)if(this.state.selectedElementIds[i[l].id])return i[l]}let a=i[i.length-1];return ai({x:t,y:r,element:a,shape:ln(a,this.scene.getNonDeletedElementsMap()),threshold:this.getElementHitThreshold()/2,frameNameBound:ie(a)?this.frameNameBoundsCache.get(a):null})?a:i[i.length-2]}return i.length===1?i[0]:null}getElementsAtPosition(t,r,n=!1,i=!1){let a=[],l=this.scene.getNonDeletedElementsMap();return(n&&i?this.scene.getNonDeletedElements():this.scene.getNonDeletedElements().filter(c=>(i||!c.locked)&&(n||!(Y(c)&&c.containerId)))).filter(c=>this.hitElement(t,r,c)).filter(c=>{let m=Ps(c,l);return m&&this.state.frameRendering.enabled&&this.state.frameRendering.clip?Ym({x:t,y:r},m,l):!0}).filter(c=>Qr(c)?(a.push(c),!1):!0).concat(a)}getElementHitThreshold(){return uh/this.state.zoom.value}hitElement(t,r,n,i=!0){if(i&&this.state.selectedElementIds[n.id]&&Gs([n],this.state)){let l=nx(n,this.scene.getNonDeletedElementsMap(),xe(n)?0:this.getElementHitThreshold());if(ix(z(t,r),l))return!0}return sx(t,r,dx(n,this.scene.getNonDeletedElementsMap()))?!0:ai({x:t,y:r,element:n,shape:ln(n,this.scene.getNonDeletedElementsMap()),threshold:this.getElementHitThreshold(),frameNameBound:ie(n)?this.frameNameBoundsCache.get(n):null})}getTextBindableContainerAtPosition(t,r){let n=this.scene.getNonDeletedElements(),i=this.scene.getSelectedElements(this.state);if(i.length===1)return Ko(i[0],!1)?i[0]:null;let a=null;for(let l=n.length-1;l>=0;--l){if(n[l].isDeleted)continue;let[s,c,m,d]=yt(n[l],this.scene.getNonDeletedElementsMap());if(Ne(n[l])&&ai({x:t,y:r,element:n[l],shape:ln(n[l],this.scene.getNonDeletedElementsMap()),threshold:this.getElementHitThreshold()})){a=n[l];break}else if(s<t&&t<m&&c<r&&r<d){a=n[l];break}}return Ko(a,!1)?a:null}handleHoverSelectedLinearElement(t,r,n){let i=this.scene.getNonDeletedElementsMap(),a=K.getElement(t.elementId,i);if(a)if(this.state.selectedLinearElement){let l=-1,s=null;ai({x:r,y:n,element:a,shape:ln(a,this.scene.getNonDeletedElementsMap())})?(l=K.getPointIndexUnderCursor(a,i,this.state.zoom,r,n),s=K.getSegmentMidpointHitCoords(t,{x:r,y:n},this.state,this.scene.getNonDeletedElementsMap()),(ee(a)?l===0||l===a.points.length-1:l>=0)||s?Te(this.interactiveCanvas,we.POINTER):this.hitElement(r,n,a)&&Te(this.interactiveCanvas,we.MOVE)):this.hitElement(r,n,a)&&(!ee(a)||!(a.startBinding||a.endBinding))&&Te(this.interactiveCanvas,we.MOVE),this.state.selectedLinearElement.hoverPointIndex!==l&&this.setState({selectedLinearElement:{...this.state.selectedLinearElement,hoverPointIndex:l}}),K.arePointsEqual(this.state.selectedLinearElement.segmentMidPointHoveredCoords,s)||this.setState({selectedLinearElement:{...this.state.selectedLinearElement,segmentMidPointHoveredCoords:s}})}else Te(this.interactiveCanvas,we.AUTO)}updateGestureOnPointerDown(t){fe.pointers.set(t.pointerId,{x:t.clientX,y:t.clientY}),fe.pointers.size===2&&(fe.lastCenter=Ou(fe.pointers),fe.initialScale=this.state.zoom.value,fe.initialDistance=Fu(Array.from(fe.pointers.values())))}initialPointerDownState(t){let r=Re(t,this.state),n=this.scene.getSelectedElements(this.state),[i,a,l,s]=Se(n),c=n.findIndex(ee)===0;return{origin:r,withCmdOrCtrl:t[y.CTRL_OR_CMD],originInGrid:pr(nt(r.x,r.y,t[y.CTRL_OR_CMD]||c?null:this.getEffectiveGridSize())),scrollbars:Gu(Pg,t.clientX-this.state.offsetLeft,t.clientY-this.state.offsetTop),lastCoords:{...r},originalElements:this.scene.getNonDeletedElements().reduce((m,d)=>(m.set(d.id,xr(d)),m),new Map),resize:{handleType:!1,isResizing:!1,offset:{x:0,y:0},arrowDirection:"origin",center:{x:(l+i)/2,y:(s+a)/2}},hit:{element:null,allHitElements:[],wasAddedToSelection:!1,hasBeenDuplicated:!1,hasHitCommonBoundingBoxOfSelectedElements:this.isHittingCommonBoundingBoxOfSelectedElements(r,n)},drag:{hasOccurred:!1,offset:null},eventListeners:{onMove:null,onUp:null,onKeyUp:null,onKeyDown:null},boxSelection:{hasOccurred:!1}}}handleDraggingScrollBar(t,r){if(!(r.scrollbars.isOverEither&&!this.state.multiElement))return!1;Dg=!0,r.lastCoords.x=t.clientX,r.lastCoords.y=t.clientY;let n=Ad(a=>{a.target instanceof HTMLElement&&this.handlePointerMoveOverScrollbars(a,r)}),i=Fe(()=>{cl=null,Dg=!1,go(this.interactiveCanvas,this.state),this.setState({cursorButton:"up"}),this.savePointer(t.clientX,t.clientY,"up"),window.removeEventListener("pointermove",n),window.removeEventListener("pointerup",i),n.flush()});return cl=i,window.addEventListener("pointermove",n),window.addEventListener("pointerup",i),!0}isASelectedElement(t){return t!=null&&this.state.selectedElementIds[t.id]}isHittingCommonBoundingBoxOfSelectedElements(t,r){if(r.length<2)return!1;let n=this.getElementHitThreshold(),[i,a,l,s]=Se(r);return t.x>i-n&&t.x<l+n&&t.y>a-n&&t.y<s+n}getCurrentItemRoundness(t){return this.state.currentItemRoundness==="round"?{type:Kn(t)?St.ADAPTIVE_RADIUS:St.PROPORTIONAL_RADIUS}:null}maybeCacheReferenceSnapPoints(t,r,n=!1){jm({event:t,app:this,selectedElements:r})&&(n||!wr.getReferenceSnapPoints())&&wr.setReferenceSnapPoints(pE(this.scene.getNonDeletedElements(),r,this.state,this.scene.getNonDeletedElementsMap()))}maybeCacheVisibleGaps(t,r,n=!1){jm({event:t,app:this,selectedElements:r})&&(n||!wr.getVisibleGaps())&&wr.setVisibleGaps(mE(this.scene.getNonDeletedElements(),r,this.state,this.scene.getNonDeletedElementsMap()))}onKeyDownFromPointerDownHandler(t){return Fe(r=>{this.maybeHandleResize(t,r)||this.maybeDragNewGenericElement(t,r)})}onKeyUpFromPointerDownHandler(t){return Fe(r=>{r.key===y.ALT&&r.preventDefault(),!this.maybeHandleResize(t,r)&&this.maybeDragNewGenericElement(t,r)})}onPointerMoveFromPointerDownHandler(t){return Ad(r=>{if(this.state.openDialog?.name==="elementLinkSelector")return;let n=Re(r,this.state);if(this.state.selectedLinearElement&&this.state.selectedLinearElement.elbowed&&this.state.selectedLinearElement.pointerDownState.segmentMidpoint.index){let[p,u]=nt(n.x,n.y,r[y.CTRL_OR_CMD]?null:this.getEffectiveGridSize()),h=this.state.selectedLinearElement.pointerDownState.segmentMidpoint.index;if(h<0){let b=K.getSegmentMidpointHitCoords({...this.state.selectedLinearElement,segmentMidPointHoveredCoords:null},{x:p,y:u},this.state,this.scene.getNonDeletedElementsMap());h=b?K.getSegmentMidPointIndex(this.state.selectedLinearElement,this.state,b,this.scene.getNonDeletedElementsMap()):-1}let f=K.moveFixedSegment(this.state.selectedLinearElement,h,p,u,this.scene.getNonDeletedElementsMap());_n(()=>{this.state.selectedLinearElement&&this.setState({selectedLinearElement:{...this.state.selectedLinearElement,segmentMidPointHoveredCoords:f.segmentMidPointHoveredCoords,pointerDownState:f.pointerDownState}})});return}let i=this.lastPointerMoveCoords??t.origin;if(this.lastPointerMoveCoords=n,t.drag.offset===null&&(t.drag.offset=pr(BE(this.scene.getSelectedElements(this.state),t.origin.x,t.origin.y))),!(r.target instanceof HTMLElement)||this.handlePointerMoveOverScrollbars(r,t))return;if($t(this.state)){this.handleEraser(r,t,n);return}this.state.activeTool.type==="laser"&&this.laserTrails.addPointToPath(n.x,n.y);let[l,s]=nt(n.x,n.y,r[y.CTRL_OR_CMD]?null:this.getEffectiveGridSize());if(!t.drag.hasOccurred&&(this.state.activeTool.type==="arrow"||this.state.activeTool.type==="line")&&dr(z(n.x,n.y),z(t.origin.x,t.origin.y))<Vr)return;if(t.resize.isResizing&&(t.lastCoords.x=n.x,t.lastCoords.y=n.y,this.maybeHandleCrop(t,r)||this.maybeHandleResize(t,r)))return!0;let c=this.scene.getNonDeletedElementsMap();if(this.state.selectedLinearElement){let p=this.state.editingLinearElement||this.state.selectedLinearElement;if(K.shouldAddMidpoint(this.state.selectedLinearElement,n,this.state,c)){let h=K.addMidpoint(this.state.selectedLinearElement,n,this,!r[y.CTRL_OR_CMD],c);if(!h)return;_n(()=>{this.state.selectedLinearElement&&this.setState({selectedLinearElement:{...this.state.selectedLinearElement,pointerDownState:h.pointerDownState,selectedPointsIndices:h.selectedPointsIndices}}),this.state.editingLinearElement&&this.setState({editingLinearElement:{...this.state.editingLinearElement,pointerDownState:h.pointerDownState,selectedPointsIndices:h.selectedPointsIndices}})});return}else if(p.pointerDownState.segmentMidpoint.value!==null&&!p.pointerDownState.segmentMidpoint.added)return;if(K.handlePointDragging(r,this,n.x,n.y,(h,f)=>{this.maybeSuggestBindingsForLinearElementAtCoords(h,f)},p,this.scene)){t.lastCoords.x=n.x,t.lastCoords.y=n.y,t.drag.hasOccurred=!0,this.state.editingLinearElement&&!this.state.editingLinearElement.isDragging&&this.setState({editingLinearElement:{...this.state.editingLinearElement,isDragging:!0}}),this.state.selectedLinearElement.isDragging||this.setState({selectedLinearElement:{...this.state.selectedLinearElement,isDragging:!0}});return}}let m=t.hit.allHitElements.some(p=>this.isASelectedElement(p)),d=this.state.editingLinearElement&&r.shiftKey&&this.state.editingLinearElement.elementId===t.hit.element?.id;if((m||t.hit.hasHitCommonBoundingBoxOfSelectedElements)&&!d){let p=this.scene.getSelectedElements(this.state);if(p.every(b=>b.locked))return;let u=p.find(b=>ie(b)),h=this.getTopLayerFrameAtSceneCoords(n),f=h&&!u?h:null;if(this.state.frameToHighlight!==f&&_n(()=>{this.setState({frameToHighlight:f})}),t.drag.hasOccurred=!0,p.length>0&&!t.withCmdOrCtrl&&!this.state.editingTextElement&&this.state.activeEmbeddable?.state!=="active"){let b={x:n.x-t.origin.x,y:n.y-t.origin.y},x=[...t.originalElements.values()],T=r.shiftKey;if(T){let S=Math.abs(b.x),I=Math.abs(b.y),_=T&&S<I,k=T&&S>I;_&&(b.x=0),k&&(b.y=0)}if(this.state.croppingElementId){let S=this.scene.getNonDeletedElementsMap().get(this.state.croppingElementId);if(S&&xe(S)&&S.crop!==null&&t.hit.element===S){let I=S.crop,_=qr(S)&&this.imageCache.get(S.fileId)?.image;if(_&&!(_ instanceof Promise)){let k=Rh(qd(n.x-i.x,n.y-i.y),Math.max(this.state.zoom.value,2)),[R,M,N,G,H,V]=yt(S,c),F=Al(bt(z(R,M),z(H,V),S.angle)),O=Al(bt(z(N,M),z(H,V),S.angle)),j=Al(bt(z(R,G),z(H,V),S.angle)),oe=tm(em(O,F)),W=tm(em(j,F)),ne=qd(Qd(k,oe),Qd(k,W)),ge={...I,x:ot(I.x-ne[0]*Math.sign(S.scale[0]),0,_.naturalWidth-I.width),y:ot(I.y-ne[1]*Math.sign(S.scale[1]),0,_.naturalHeight-I.height)};P(S,{crop:ge});return}}}this.maybeCacheVisibleGaps(r,p),this.maybeCacheReferenceSnapPoints(r,p);let{snapOffset:E,snapLines:w}=uE(x,b,this,r,this.scene.getNonDeletedElementsMap());if(this.setState({snapLines:w}),this.state.editingFrame||FE(t,p,b,this.scene,E,r[y.CTRL_OR_CMD]?null:this.getEffectiveGridSize()),this.setState({selectedElementsAreBeingDragged:!0,selectionElement:null}),(p.length!==1||!ee(p[0]))&&this.setState({suggestedBindings:gs(p,this.scene.getNonDeletedElementsMap(),this.state.zoom)}),r.altKey&&!t.hit.hasBeenDuplicated){t.hit.hasBeenDuplicated=!0;let S=[],I=[],_=new Map,k=new Map,R=t.hit.element,M=new Set(this.scene.getSelectedElements({selectedElementIds:this.state.selectedElementIds,includeBoundTextElement:!0,includeElementsInFrames:!0}).map(V=>V.id)),N=this.scene.getElementsIncludingDeleted();for(let V of N){let F=M.has(V.id)||V.id===R?.id&&t.hit.wasAddedToSelection;if(Math.abs(V.x)>1e7||Math.abs(V.x)>1e7||Math.abs(V.width)>1e7||Math.abs(V.height)>1e7){console.error("Alt+dragging element in scene with invalid dimensions",V.x,V.y,V.width,V.height,F);return}if(F){let O=as(this.state.editingGroupId,_,V);if(Math.abs(O.x)>1e7||Math.abs(O.x)>1e7||Math.abs(O.width)>1e7||Math.abs(O.height)>1e7){console.error("Alt+dragging duplicated element with invalid dimensions",O.x,O.y,O.width,O.height);return}let j=t.originalElements.get(V.id);if(Math.abs(j.x)>1e7||Math.abs(j.x)>1e7||Math.abs(j.width)>1e7||Math.abs(j.height)>1e7){console.error("Alt+dragging duplicated element with invalid dimensions",j.x,j.y,j.width,j.height);return}P(O,{x:j.x,y:j.y}),t.originalElements.set(O.id,O),S.push(O),I.push(V),k.set(V.id,O.id)}else S.push(V)}let G=[...S,...I];G=this.props.onDuplicate?.(G,N)||G,fo(G,Q(I)),Bs(S,I,k),hs(G,I,k,"duplicatesServeAsOld"),Ms(G,I,k),this.scene.replaceAllElements(G),this.maybeCacheVisibleGaps(r,p,!0),this.maybeCacheReferenceSnapPoints(r,p,!0)}return}}if(this.state.selectionElement)t.lastCoords.x=n.x,t.lastCoords.y=n.y,this.maybeDragNewGenericElement(t,r);else{let p=this.state.newElement;if(!p)return;if(p.type==="freedraw"){let u=p.points,h=n.x-p.x,f=n.y-p.y,b=u.length>0&&u[u.length-1];if(!(b&&b[0]===h&&b[1]===f)){let T=p.simulatePressure?p.pressures:[...p.pressures,r.pressure];P(p,{points:[...u,z(h,f)],pressures:T},!1),this.setState({newElement:p})}}else if(he(p)){t.drag.hasOccurred=!0;let u=p.points,h=l-p.x,f=s-p.y;ds(r)&&u.length===2&&({width:h,height:f}=tp(p.x,p.y,n.x,n.y)),u.length===1?P(p,{points:[...u,z(h,f)]},!1):(u.length===2||u.length>1&&ee(p))&&P(p,{points:[...u.slice(0,-1),z(h,f)]},!1,{isDragging:!0}),this.setState({newElement:p}),Wo(p,!1)&&this.maybeSuggestBindingsForLinearElementAtCoords(p,[n],this.state.startBoundElement)}else t.lastCoords.x=n.x,t.lastCoords.y=n.y,this.maybeDragNewGenericElement(t,r,!1)}if(this.state.activeTool.type==="selection"){t.boxSelection.hasOccurred=!0;let p=this.scene.getNonDeletedElements();if(this.state.editingLinearElement)K.handleBoxSelection(r,this.state,this.setState.bind(this),this.scene.getNonDeletedElementsMap());else{let u=!0;!r.shiftKey&&ke(p,this.state)&&(t.withCmdOrCtrl&&t.hit.element?this.setState(f=>We({...f,selectedElementIds:{[t.hit.element.id]:!0}},this.scene.getNonDeletedElements(),f,this)):u=!1);let h=this.state.selectionElement?zE(p,this.state.selectionElement,this.scene.getNonDeletedElementsMap(),!1):[];this.setState(f=>{let b={...u&&f.selectedElementIds,...h.reduce((x,T)=>(x[T.id]=!0,x),{})};return t.hit.element&&(h.length?delete b[t.hit.element.id]:b[t.hit.element.id]=!0),f=u?f:{...f,selectedGroupIds:{},editingGroupId:null},{...We({editingGroupId:f.editingGroupId,selectedElementIds:b},this.scene.getNonDeletedElements(),f,this),selectedLinearElement:h.length===1&&he(h[0])?new K(h[0]):null,showHyperlinkPopup:h.length===1&&(h[0].link||Et(h[0]))?"info":!1}})}}})}handlePointerMoveOverScrollbars(t,r){if(r.scrollbars.isOverHorizontal){let n=t.clientX,i=n-r.lastCoords.x;return this.translateCanvas({scrollX:this.state.scrollX-i/this.state.zoom.value}),r.lastCoords.x=n,!0}if(r.scrollbars.isOverVertical){let n=t.clientY,i=n-r.lastCoords.y;return this.translateCanvas({scrollY:this.state.scrollY-i/this.state.zoom.value}),r.lastCoords.y=n,!0}return!1}onPointerUpFromPointerDownHandler(t){return Fe(r=>{this.removePointer(r),t.eventListeners.onMove&&t.eventListeners.onMove.flush();let{newElement:n,resizingElement:i,croppingElementId:a,multiElement:l,activeTool:s,isResizing:c,isRotating:m,isCropping:d}=this.state;this.setState(b=>({isResizing:!1,isRotating:!1,isCropping:!1,resizingElement:null,selectionElement:null,frameToHighlight:null,elementsToHighlight:null,cursorButton:"up",snapLines:Yn(b.snapLines,[]),originSnapOffset:null})),this.lastPointerMoveCoords=null,wr.setReferenceSnapPoints(null),wr.setVisibleGaps(null),this.savePointer(r.clientX,r.clientY,"up"),this.setState({selectedElementsAreBeingDragged:!1});let p=this.scene.getNonDeletedElementsMap();if(t.drag.hasOccurred&&t.hit?.element?.id){let b=p.get(t.hit.element.id);ma(b)&&b.boundElements?.filter(x=>x.type==="arrow").map(x=>p.get(x.id)).filter(x=>ee(x)).forEach(x=>{x&&P(x,{},!0)})}if(this.state.editingLinearElement)if(!t.boxSelection.hasOccurred&&t.hit?.element?.id!==this.state.editingLinearElement.elementId)this.actionManager.executeAction(Ht);else{let b=K.handlePointerUp(r,this.state.editingLinearElement,this.state,this.scene);b!==this.state.editingLinearElement&&this.setState({editingLinearElement:b,suggestedBindings:[]})}else if(this.state.selectedLinearElement){if(this.state.selectedLinearElement.elbowed){let b=K.getElement(this.state.selectedLinearElement.elementId,this.scene.getNonDeletedElementsMap());b&&P(b,{},!0)}if(t.hit?.element?.id!==this.state.selectedLinearElement.elementId)this.scene.getSelectedElements(this.state).length>1&&this.setState({selectedLinearElement:null});else{let b=K.handlePointerUp(r,this.state.selectedLinearElement,this.state,this.scene),{startBindingElement:x,endBindingElement:T}=b,E=this.scene.getElement(b.elementId);Wo(E)&&us(E,x,T,p,this.scene),b!==this.state.selectedLinearElement&&this.setState({selectedLinearElement:{...b,selectedPointsIndices:null},suggestedBindings:[]})}}if(this.missingPointerEventCleanupEmitter.clear(),window.removeEventListener("pointermove",t.eventListeners.onMove),window.removeEventListener("pointerup",t.eventListeners.onUp),window.removeEventListener("keydown",t.eventListeners.onKeyDown),window.removeEventListener("keyup",t.eventListeners.onKeyUp),this.state.pendingImageElementId&&this.setState({pendingImageElementId:null}),this.props?.onPointerUp?.(s,t),this.onPointerUpEmitter.trigger(this.state.activeTool,t,r),n?.type==="freedraw"){let b=Re(r,this.state),x=n.points,T=b.x-n.x,E=b.y-n.y;T===x[0][0]&&E===x[0][1]&&(E+=1e-4,T+=1e-4);let w=n.simulatePressure?[]:[...n.pressures,r.pressure];P(n,{points:[...x,z(T,E)],pressures:w,lastCommittedPoint:z(T,E)}),this.actionManager.executeAction(Ht);return}if(xe(n)){let b=n;try{this.initializeImageDimensions(b),this.setState({selectedElementIds:Ee({[b.id]:!0},this.state)},()=>{this.actionManager.executeAction(Ht)})}catch(x){console.error(x),this.scene.replaceAllElements(this.scene.getElementsIncludingDeleted().filter(T=>T.id!==b.id)),this.actionManager.executeAction(Ht)}return}if(he(n)){n.points.length>1&&this.store.shouldCaptureIncrement();let b=Re(r,this.state);!t.drag.hasOccurred&&n&&!l?(P(n,{points:[...n.points,z(b.x-n.x,b.y-n.y)]}),this.setState({multiElement:n,newElement:n})):t.drag.hasOccurred&&!l&&(sn(this.state)&&Wo(n,!1)&&ya(n,this.state,b,this.scene.getNonDeletedElementsMap(),this.scene.getNonDeletedElements()),this.setState({suggestedBindings:[],startBoundElement:null}),s.locked?this.setState(x=>({newElement:null})):(Zt(this.interactiveCanvas),this.setState(x=>({newElement:null,activeTool:Be(this.state,{type:"selection"}),selectedElementIds:Ee({...x.selectedElementIds,[n.id]:!0},x),selectedLinearElement:new K(n)}))),this.scene.triggerUpdate());return}if(Y(n)){let b=rf(lt({fontSize:n.fontSize,fontFamily:n.fontFamily}),n.lineHeight);n.width<b&&P(n,{autoResize:!0}),this.resetCursor(),this.handleTextWysiwyg(n,{isExistingElement:!0})}if(s.type!=="selection"&&n&&hn(n)){this.updateScene({elements:this.scene.getElementsIncludingDeleted().filter(b=>b.id!==n.id),appState:{newElement:null},captureUpdate:L.NEVER});return}if(ie(n)){let b=nE(this.scene.getElementsIncludingDeleted(),n,this.scene.getNonDeletedElementsMap());this.scene.replaceAllElements(mi(this.scene.getElementsMapIncludingDeleted(),b,n,this.state))}if(n&&(P(n,AE(n)),this.scene.triggerUpdate()),t.drag.hasOccurred){let b=Re(r,this.state);if(this.state.selectedLinearElement&&this.state.selectedLinearElement.isDragging){let x=this.scene.getElement(this.state.selectedLinearElement.elementId);if(x?.frameId){let T=Ps(x,p);T&&x&&(Ds(x,T,this.scene.getNonDeletedElementsMap())||(P(x,{groupIds:[]}),Rs([x],this.scene.getNonDeletedElementsMap()),this.scene.triggerUpdate()))}}else{let x=this.getTopLayerFrameAtSceneCoords(b),T=this.scene.getSelectedElements(this.state),E=this.scene.getElementsMapIncludingDeleted(),w=S=>{if(S.length>0){for(let I of S){let _=I.groupIds.indexOf(this.state.editingGroupId);P(I,{groupIds:I.groupIds.slice(0,_)},!1)}E.forEach(I=>{I.groupIds.length&&Le(E,I.groupIds[I.groupIds.length-1]).length<2&&P(I,{groupIds:[]},!1)}),this.setState({editingGroupId:null})}};if(x&&!this.state.selectedElementIds[x.id]){let S=T.filter(I=>I.frameId!==x.id&&Vm(I,E,this.state));this.state.editingGroupId&&w(S),E=mi(E,S,x,this.state)}else if(!x&&this.state.editingGroupId){let S=T.filter(I=>I.frameId&&!Vm(I,E,this.state));w(S)}E=Lo(E,this.state,this),this.scene.replaceAllElements(E)}}if(i&&this.store.shouldCaptureIncrement(),i&&hn(i)&&this.updateScene({elements:this.scene.getElementsIncludingDeleted().filter(b=>b.id!==i.id),captureUpdate:L.NEVER}),t.resize.isResizing){let b=Lo(this.scene.getElementsIncludingDeleted(),this.state,this),x=this.scene.getSelectedElements(this.state).filter(T=>ie(T));for(let T of x)b=Ns(b,di(this.scene.getElementsIncludingDeleted(),T,this.state,p),T,this);this.scene.replaceAllElements(b)}let u=t.hit.element;this.state.selectedLinearElement?.elementId!==u?.id&&he(u)&&this.scene.getSelectedElements(this.state).length===1&&this.setState({selectedLinearElement:new K(u)}),(!a||a&&(!u&&!d||u&&u.id!==a))&&this.finishImageCropping();let h=this.lastPointerDownEvent,f=this.lastPointerUpEvent||this.lastPointerMoveEvent;if($t(this.state)&&h&&f){if(this.eraserTrail.endPath(),dr(z(h.clientX,h.clientY),z(f.clientX,f.clientY))===0){let x=Re({clientX:f.clientX,clientY:f.clientY},this.state);this.getElementsAtPosition(x.x,x.y).forEach(E=>this.elementsPendingErasure.add(E.id))}this.eraseElements();return}else this.elementsPendingErasure.size&&this.restoreReadyToEraseElements();if(u&&!t.drag.hasOccurred&&!t.hit.wasAddedToSelection&&(!this.state.editingLinearElement||!t.boxSelection.hasOccurred)&&(r.shiftKey&&!this.state.editingLinearElement?this.state.selectedElementIds[u.id]?Fl(this.state,u)?this.setState(b=>{let x={...b.selectedElementIds};for(let T of u.groupIds.flatMap(E=>Le(this.scene.getNonDeletedElements(),E)))delete x[T.id];return{selectedGroupIds:{...b.selectedElementIds,...u.groupIds.map(T=>({[T]:!1})).reduce((T,E)=>({...T,...E}),{})},selectedElementIds:Ee(x,b)}}):this.state.selectedLinearElement?.isDragging||this.setState(b=>{let x={...b.selectedElementIds};delete x[u.id];let T=$(this.scene.getNonDeletedElements(),{selectedElementIds:x});return{...We({editingGroupId:b.editingGroupId,selectedElementIds:x},this.scene.getNonDeletedElements(),b,this),selectedLinearElement:T.length===1&&he(T[0])?new K(T[0]):b.selectedLinearElement}}):u.frameId&&this.state.selectedElementIds[u.frameId]?this.setState(b=>{let x={...b.selectedElementIds,[u.id]:!0};return delete x[u.frameId],(this.scene.getElement(u.frameId)?.groupIds??[]).flatMap(T=>Le(this.scene.getNonDeletedElements(),T)).forEach(T=>{delete x[T.id]}),{...We({editingGroupId:b.editingGroupId,selectedElementIds:x},this.scene.getNonDeletedElements(),b,this),showHyperlinkPopup:u.link||Et(u)?"info":!1}}):this.setState(b=>({selectedElementIds:Ee({...b.selectedElementIds,[u.id]:!0},b)})):this.setState(b=>({...We({editingGroupId:b.editingGroupId,selectedElementIds:{[u.id]:!0}},this.scene.getNonDeletedElements(),b,this),selectedLinearElement:he(u)&&b.selectedLinearElement?.elementId!==u.id?new K(u):b.selectedLinearElement}))),!(u&&ee(u))&&!t.drag.hasOccurred&&!this.state.isResizing&&(u&&lx({x:t.origin.x,y:t.origin.y,element:u,shape:ln(u,this.scene.getNonDeletedElementsMap()),threshold:this.getElementHitThreshold(),frameNameBound:ie(u)?this.frameNameBoundsCache.get(u):null},p)||!u&&t.hit.hasHitCommonBoundingBoxOfSelectedElements)){this.state.editingLinearElement?this.setState({editingLinearElement:null}):this.setState({selectedElementIds:Ee({},this.state),selectedGroupIds:{},editingGroupId:null,activeEmbeddable:null}),Te(this.interactiveCanvas,we.AUTO);return}if(!s.locked&&s.type!=="freedraw"&&n&&this.setState(b=>({selectedElementIds:Ee({...b.selectedElementIds,[n.id]:!0},b),showHyperlinkPopup:Et(n)&&!n.link?"editor":b.showHyperlinkPopup})),(s.type!=="selection"||ke(this.scene.getNonDeletedElements(),this.state)||!rt(this.state.previousSelectedElementIds,this.state.selectedElementIds))&&this.store.shouldCaptureIncrement(),t.drag.hasOccurred||c||m||d){let b=this.scene.getSelectedElements(this.state).filter(he);cn(b,this.scene.getNonDeletedElementsMap(),this.scene.getNonDeletedElements(),this.scene,sn(this.state),this.state.selectedLinearElement?.selectedPointsIndices??[],this.state.zoom)}if(s.type==="laser"){this.laserTrails.endPath();return}!s.locked&&s.type!=="freedraw"?(Zt(this.interactiveCanvas),this.setState({newElement:null,suggestedBindings:[],activeTool:Be(this.state,{type:"selection"})})):this.setState({newElement:null,suggestedBindings:[]}),u&&this.lastPointerUpEvent&&this.lastPointerDownEvent&&this.lastPointerUpEvent.timeStamp-this.lastPointerDownEvent.timeStamp<300&&fe.pointers.size<=1&&Wn(u)&&this.isIframeLikeElementCenter(u,this.lastPointerUpEvent,t.origin.x,t.origin.y)&&this.handleEmbeddableCenterClick(u)})}clearSelection(t){this.setState(r=>({selectedElementIds:Ee({},r),activeEmbeddable:null,selectedGroupIds:{},editingGroupId:r.editingGroupId&&t!=null&&Zn(t,r.editingGroupId)?r.editingGroupId:null})),this.setState({selectedElementIds:Ee({},this.state),activeEmbeddable:null,previousSelectedElementIds:this.state.selectedElementIds})}getTextWysiwygSnappedToCenterPosition(t,r,n,i){if(i){let a=i.x+i.width/2,l=i.y+i.height/2,s=Us(i,n,this.scene.getNonDeletedElementsMap());if(s&&(a=s.x,l=s.y),Math.hypot(t-a,r-l)<ph){let{x:d,y:p}=xt({sceneX:a,sceneY:l},n);return{viewportX:d,viewportY:p,elementCenterX:a,elementCenterY:l}}}}getCanvasOffsets(){if(this.excalidrawContainerRef?.current){let t=this.excalidrawContainerRef.current,{left:r,top:n}=t.getBoundingClientRect();return{offsetLeft:r,offsetTop:n}}return{offsetLeft:0,offsetTop:0}}async updateLanguage(){let t=xi.find(r=>r.code===this.props.langCode)||_o;await $s(t),this.setAppState({})}},tM=()=>{(v.MODE===Rn.TEST||v.DEV)&&(window.h=window.h||{},Object.defineProperties(window.h,{elements:{configurable:!0,get(){return this.app?.scene.getElementsIncludingDeleted()},set(e){return this.app?.scene.replaceAllElements(Jo(e))}},scene:{configurable:!0,get(){return this.app?.scene}}}))};tM();var bT=Ng;var rM=()=>{Array.prototype.at||Object.defineProperty(Array.prototype,"at",{value:function(e){if(e=Math.trunc(e)||0,e<0&&(e+=this.length),!(e<0||e>=this.length))return this[e]},writable:!0,enumerable:!1,configurable:!0}),Element.prototype.replaceChildren||(Element.prototype.replaceChildren=function(...e){this.innerHTML="",this.append(...e)})},xT=rM;import nM from"clsx";import{jsx as ET}from"react/jsx-runtime";var yT=({children:e})=>{let{FooterCenterTunnel:o}=_e(),t=ve();return ET(o.In,{children:ET("div",{className:nM("footer-center zen-mode-transition",{"layer-ui__wrapper__footer-left--transition-bottom":t.zenModeEnabled}),children:e})})},iM=yT;yT.displayName="FooterCenter";import{jsx as Gr,jsxs as wT}from"react/jsx-runtime";var aM=()=>Gr("svg",{viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"ExcalidrawLogo-icon",children:Gr("path",{d:"M39.9 32.889a.326.326 0 0 0-.279-.056c-2.094-3.083-4.774-6-7.343-8.833l-.419-.472a.212.212 0 0 0-.056-.139.586.586 0 0 0-.167-.111l-.084-.083-.056-.056c-.084-.167-.28-.278-.475-.167-.782.39-1.507.973-2.206 1.528-.92.722-1.842 1.445-2.708 2.25a8.405 8.405 0 0 0-.977 1.028c-.14.194-.028.361.14.444-.615.611-1.23 1.223-1.843 1.861a.315.315 0 0 0-.084.223c0 .083.056.166.111.194l1.09.833v.028c1.535 1.528 4.244 3.611 7.12 5.861.418.334.865.667 1.284 1 .195.223.39.473.558.695.084.11.28.139.391.055.056.056.14.111.196.167a.398.398 0 0 0 .167.056.255.255 0 0 0 .224-.111.394.394 0 0 0 .055-.167c.029 0 .028.028.056.028a.318.318 0 0 0 .224-.084l5.082-5.528a.309.309 0 0 0 0-.444Zm-14.63-1.917a.485.485 0 0 0 .111.14c.586.5 1.2 1 1.843 1.555l-2.569-1.945-.251-.166c-.056-.028-.112-.084-.168-.111l-.195-.167.056-.056.055-.055.112-.111c.866-.861 2.346-2.306 3.1-3.028-.81.805-2.43 3.167-2.095 3.944Zm8.767 6.89-2.122-1.612a44.713 44.713 0 0 0-2.625-2.5c1.145.861 2.122 1.611 2.262 1.75 1.117.972 1.06.806 1.815 1.445l.921.666a1.06 1.06 0 0 1-.251.25Zm.558.416-.056-.028c.084-.055.168-.111.252-.194l-.196.222ZM1.089 5.75c.055.361.14.722.195 1.056.335 1.833.67 3.5 1.284 4.75l.252.944c.084.361.223.806.363.917 1.424 1.25 3.602 3.11 5.947 4.889a.295.295 0 0 0 .363 0s0 .027.028.027a.254.254 0 0 0 .196.084.318.318 0 0 0 .223-.084c2.988-3.305 5.221-6.027 6.813-8.305.112-.111.14-.278.14-.417.111-.111.195-.25.307-.333.111-.111.111-.306 0-.39l-.028-.027c0-.055-.028-.139-.084-.167-.698-.666-1.2-1.138-1.731-1.638-.922-.862-1.871-1.75-3.881-3.75l-.028-.028c-.028-.028-.056-.056-.112-.056-.558-.194-1.703-.389-3.127-.639C6.087 2.223 3.21 1.723.614.944c0 0-.168 0-.196.028l-.083.084c-.028.027-.056.055-.224.11h.056-.056c.028.167.028.278.084.473 0 .055.112.5.112.555l.782 3.556Zm15.496 3.278-.335-.334c.084.112.196.195.335.334Zm-3.546 4.666-.056.056c0-.028.028-.056.056-.056Zm-2.038-10c.168.167.866.834 1.033.973-.726-.334-2.54-1.167-3.379-1.445.838.167 1.983.334 2.346.472ZM1.424 2.306c.419.722.754 3.222 1.089 5.666-.196-.778-.335-1.555-.503-2.278-.251-1.277-.503-2.416-.838-3.416.056 0 .14 0 .252.028Zm-.168-.584c-.112 0-.223-.028-.307-.028 0-.027 0-.055-.028-.055.14 0 .223.028.335.083Zm-1.089.222c0-.027 0-.027 0 0ZM39.453 1.333c.028-.11-.558-.61-.363-.639.42-.027.42-.666 0-.666-.558.028-1.144.166-1.675.25-.977.194-1.982.389-2.96.61-2.205.473-4.383.973-6.561 1.557-.67.194-1.424.333-2.066.666-.224.111-.196.333-.084.472-.056.028-.084.028-.14.056-.195.028-.363.056-.558.083-.168.028-.252.167-.224.334 0 .027.028.083.028.11-1.173 1.556-2.485 3.195-3.909 4.945-1.396 1.611-2.876 3.306-4.356 5.056-4.719 5.5-10.052 11.75-15.943 17.25a.268.268 0 0 0 0 .389c.028.027.056.055.084.055-.084.084-.168.14-.252.222-.056.056-.084.111-.084.167a.605.605 0 0 0-.111.139c-.112.111-.112.305.028.389.111.11.307.11.39-.028.029-.028.029-.056.056-.056a.44.44 0 0 1 .615 0c.335.362.67.723.977 1.028l-.698-.583c-.112-.111-.307-.083-.39.028-.113.11-.085.305.027.389l7.427 6.194c.056.056.112.056.196.056s.14-.028.195-.084l.168-.166c.028.027.083.027.111.027.084 0 .14-.027.196-.083 10.052-10.055 18.15-17.639 27.42-24.417.083-.055.111-.166.111-.25.112 0 .196-.083.251-.194 1.704-5.194 2.039-9.806 2.15-12.083v-.028c0-.028.028-.056.028-.083.028-.056.028-.084.028-.084a1.626 1.626 0 0 0-.111-1.028ZM21.472 9.5c.446-.5.893-1.028 1.34-1.5-2.876 3.778-7.65 9.583-14.408 16.5 4.607-5.083 9.242-10.333 13.068-15ZM5.193 35.778h.084-.084Zm3.462 3.194c-.027-.028-.027-.028 0-.028v.028Zm4.16-3.583c.224-.25.448-.472.699-.722 0 0 0 .027.028.027-.252.223-.475.445-.726.695Zm1.146-1.111c.14-.14.279-.334.446-.5l.028-.028c1.648-1.694 3.351-3.389 5.082-5.111l.028-.028c.419-.333.921-.694 1.368-1.028a379.003 379.003 0 0 0-6.952 6.695ZM24.794 6.472c-.921 1.195-1.954 2.778-2.82 4.028-2.736 3.944-11.532 13.583-11.727 13.75a1976.983 1976.983 0 0 1-8.042 7.639l-.167.167c-.14-.167-.14-.417.028-.556C14.49 19.861 22.03 10.167 25.074 5.917c-.084.194-.14.36-.28.555Zm4.83 5.695c-1.116-.64-1.646-1.64-1.34-2.611l.084-.334c.028-.083.084-.194.14-.277.307-.5.754-.917 1.257-1.167.027 0 .055 0 .083-.028-.028-.056-.028-.139-.028-.222.028-.167.14-.278.335-.278.335 0 1.369.306 1.76.639.111.083.223.194.335.305.14.167.363.445.474.667.056.028.112.306.196.445.056.222.111.472.084.694-.028.028 0 .194-.028.194a2.668 2.668 0 0 1-.363 1.028c-.028.028-.028.056-.056.084l-.028.027c-.14.223-.335.417-.53.556-.643.444-1.369.583-2.095.389 0 0-.195-.084-.28-.111Zm8.154-.834a39.098 39.098 0 0 1-.893 3.167c0 .028-.028.083 0 .111-.056 0-.084.028-.14.056-2.206 1.61-4.356 3.305-6.506 5.028 1.843-1.64 3.686-3.306 5.613-4.945.558-.5.949-1.139 1.06-1.861l.28-1.667v-.055c.14-.334.67-.195.586.166Z",fill:"currentColor"})}),lM=()=>wT("svg",{viewBox:"0 0 450 55",xmlns:"http://www.w3.org/2000/svg",fill:"none",className:"ExcalidrawLogo-text",children:[Gr("path",{d:"M429.27 96.74c2.47-1.39 4.78-3.02 6.83-4.95 1.43-1.35 2.73-2.86 3.81-4.51-.66.9-1.4 1.77-2.23 2.59-2.91 2.84-5.72 5.09-8.42 6.87h.01ZM343.6 69.36c.33 3.13.58 6.27.79 9.4.09 1.37.18 2.75.25 4.12-.12-4.46-.27-8.93-.5-13.39-.11-2.08-.24-4.16-.4-6.24-.06 1.79-.11 3.85-.13 6.11h-.01ZM378.47 98.34c.01-.37.07-1.13.01-6.51-.11 1.9-.22 3.81-.31 5.71-.07 1.42-.22 2.91-.16 4.35.39.03.78.07 1.17.1-.92-.85-.76-2.01-.72-3.66l.01.01ZM344.09 86.12c-.09-2.41-.22-4.83-.39-7.24v12.21c.15-.05.32-.09.47-.14.05-1.61-.03-3.23-.09-4.83h.01ZM440.69 66.79c-.22-.34-.45-.67-.69-.99-3.71-4.87-9.91-7.14-15.65-8.55-1.05-.26-2.12-.49-3.18-.71 2.29.59 4.48 1.26 6.64 2.02 7.19 2.54 10.57 5.41 12.88 8.23ZM305.09 72.46l1.2 3.6c.84 2.53 1.67 5.06 2.46 7.61.24.78.5 1.57.73 2.36.22-.04.44-.08.67-.12a776.9 776.9 0 0 1-5.01-13.57c-.02.04-.03.09-.05.13v-.01ZM345.49 90.25v.31c1.48-.42 3.05-.83 4.66-1.2-1.56.25-3.12.52-4.66.89ZM371.02 90.22c0-.57-.04-1.14-.11-1.71-.06-.02-.12-.04-.19-.05-.21-.05-.43-.08-.65-.11.42.16.74.88.95 1.87ZM398.93 54.23c-.13 0-.27-.01-.4-.02l.03.4c.11-.15.23-.27.37-.38ZM401.57 62.28v-.15c-1.22-.24-2.86-.61-3.23-1.25-.09-.15-.18-.51-.27-.98-.09.37-.2.73-.33 1.09 1.24.56 2.52.98 3.83 1.29ZM421.73 88.68c-2.97 1.65-6.28 3.12-9.69 3.68v.18c4.72-.14 11.63-3.85 16.33-8.38-2.04 1.75-4.33 3.24-6.63 4.53l-.01-.01ZM411.28 80.92c-.05-1.2-.09-2.4-.15-3.6-.21 5.66-.46 11.38-.47 14.51.24-.02.48-.04.71-.07.15-3.61.05-7.23-.09-10.83v-.01Z",transform:"translate(-144.023 -51.76)"}),Gr("path",{d:"M425.38 67.41c-3.5-1.45-7.19-2.57-14.06-3.62.09 1.97.06 4.88-.03 8.12.03.04.06.09.06.15.19 1.36.28 2.73.37 4.1.25 3.77.39 7.55.41 11.33 0 1.38-.01 2.76-.07 4.13 1.4-.25 2.78-.65 4.12-1.15 4.07-1.5 7.94-3.78 11.28-6.54 2.33-1.92 5.13-4.49 5.88-7.58.63-3.53-2.45-6.68-7.97-8.96l.01.02ZM411.35 92.53v-.06l-.34.03c.11.01.22.03.34.03ZM314.26 64.06c-.23-.59-.47-1.17-.7-1.75.57 1.62 1.11 3.25 1.6 4.9l.15.54 2.35 6.05c.32.82.66 1.64.98 2.46-1.38-4.1-2.83-8.17-4.39-12.2h.01ZM156.82 103.07c-.18.13-.38.23-.58.33 1.32-.03 2.66-.2 3.93-.34.86-.09 1.72-.22 2.58-.33-2.12.1-4.12.17-5.94.34h.01ZM210.14 68.88s.03.04.05.07c.18-.31.39-.64.58-.96-.21.3-.42.6-.64.89h.01ZM201.65 82.8c-.5.77-1.02 1.56-1.49 2.37 1.11-1.55 2.21-3.1 3.2-4.59-.23.23-.49.51-.75.79-.32.47-.65.95-.96 1.43ZM194.03 98.66c-.33-.4-.65-.84-1.05-1.17-.24-.2-.07-.49.17-.56-.23-.26-.42-.5-.63-.75 1.51-2.55 3.93-5.87 6.4-9.28-.17-.08-.29-.28-.2-.49.04-.09.09-.17.13-.26-1.21 1.78-2.42 3.55-3.61 5.33-.87 1.31-1.74 2.64-2.54 4-.29.5-.63 1.04-.87 1.61.81.65 1.63 1.27 2.47 1.88-.09-.11-.18-.21-.27-.32v.01ZM307.79 82.93c-1-3.17-2.05-6.32-3.1-9.48-1.62 4.08-3.69 9.17-6.16 15.19 3.32-1.04 6.77-1.87 10.27-2.5-.32-1.08-.67-2.15-1.01-3.21ZM149.5 80.7c.05-1.71.04-3.43 0-5.14-.1 2.26-.16 4.51-.22 6.77-.02.73-.03 1.46-.04 2.19.14-1.27.2-2.55.24-3.82h.02ZM228.98 98.3c.39 1.25.91 3.03.94 3.91.06-.03.12-.07.17-.1.08-1.29-.55-2.65-1.11-3.81ZM307.72 53.36c.81.5 1.53 1.04 2.07 1.49-.38-.8-.78-1.58-1.21-2.35-.17.03-.34.06-.51.11-.43.12-.86.26-1.29.41.35-.01.53.1.94.34ZM283.69 96.14c3.91-7.25 6.89-13.35 8.88-18.15l1.1-2.66c-1.27 2.64-2.56 5.27-3.83 7.9-1.53 3.15-3.06 6.31-4.58 9.47-.87 1.81-1.76 3.62-2.54 5.47.04.02.07.04.11.07.05.05.1.09.15.14.05-.73.27-1.48.71-2.24ZM289.92 103.23s-.04.01-.05.03c0-.02.04-.03.05-.04.05-.05.11-.1.16-.15l.21-.21c-.55 0-1.5-.27-2.55-.72.4.26.8.51 1.22.74.24.13.48.26.73.37.05.02.1.03.14.05a.27.27 0 0 1 .08-.07h.01ZM269.23 68.49c-.39-.19-.82-.48-1.33-.87-3.06-1.56-6.31-2.78-9.36-2.35-3.5.49-5.7 1.11-7.74 2.44 5.71-2.6 12.82-2.07 18.44.79l-.01-.01ZM177.87 53.69l1.06.03c-.96-.22-2-.25-2.89-.3-4.95-.26-9.99.33-14.86 1.19-2.44.43-4.88.95-7.28 1.59 9.09-1.76 15.69-2.77 23.97-2.51ZM219.85 55.51c-.18.12-.36.27-.56.45-.45.53-.86 1.11-1.26 1.66-1.91 2.61-3.71 5.31-5.57 7.95l-.12.18 8.05-10.11c-.18-.05-.36-.1-.55-.13h.01ZM510.71 54.1c.12-.15.29-.3.53-.45.69-.4 3.72-.63 5.87-.74-.36-.02-.73-.04-1.09-.05-1.84-.03-3.67.09-5.49.35.05.3.12.59.18.88v.01ZM510.76 86.02c1.37-3.07 2.49-6.27 3.57-9.46.55-1.64 1.12-3.3 1.6-4.97-1.59 4.01-3.67 9.14-6.2 15.3.24-.08.5-.14.74-.22.1-.22.19-.44.29-.65ZM566.95 75.76c.11-.02.23.03.31.11-.05-.13-.09-.26-.14-.39-.05.09-.11.18-.17.28ZM511.33 86.41c3.08-.89 6.24-1.62 9.46-2.14-1.51-3.98-2.98-7.96-4.39-11.87-.05.15-.09.31-.14.46-1.02 3.32-2.15 6.61-3.39 9.85-.48 1.25-.98 2.49-1.53 3.7h-.01ZM578.24 74.45c.11-.44.23-.87.35-1.31-.31.7-.64 1.39-.97 2.08.09.21.19.4.28.61.12-.46.23-.92.35-1.38h-.01ZM520.62 53.11c-.09 0-.18-.01-.28-.02.38.34.29 1.08.93 2.53l6.65 17.15c2.2 5.68 4.69 11.36 7.41 16.87l1.06 2.17c-2.95-7.05-5.92-14.08-8.87-21.13-1.58-3.79-3.16-7.59-4.7-11.4-.78-1.92-1.73-3.89-2.25-5.91-.03-.1 0-.19.04-.26h.01ZM578.78 77.87c1.45-5.77 3.07-10.43 3.58-13.36.05-.34.16-.88.31-1.55-.67 1.79-1.37 3.56-2.08 5.33-.12.43-.23.86-.35 1.29-.65 2.43-1.29 4.86-1.9 7.3.14.33.29.65.43 1l.01-.01ZM545.3 94.66c.02-.44.03-.83.05-1.12.02-1.01.05-2.02.11-3.02.03-6.66-.46-14.33-1.46-22.8-.13-.42-.27-1.24-.56-2.89 0-.02 0-.04-.01-.06.62 6.61.95 13.25 1.32 19.87.17 3.08.33 6.16.52 9.23.02.25.03.52.04.78l-.01.01ZM580.77 102.81c.13.2.27.38.37.49.27-.11.53-.22.8-.32-.43.09-.82.05-1.17-.16v-.01ZM530.48 104.07h.33c-.36-.13-.71-.32-1.04-.56.14.24.3.47.45.7.06-.08.14-.13.26-.13v-.01ZM542.63 58.82c.06.23.11.47.15.71.14-.33.36-.62.7-.86-.28.05-.57.11-.85.15ZM583.81 57.87c.15-.7.29-1.41.42-2.11-.14.45-.28.9-.42 1.34-.46 1.44-.89 2.89-1.31 4.34.44-1.19.88-2.37 1.31-3.57ZM523.62 91.48c-4.66 1.17-9.05 2.89-14.02 5.27 4.65-1.84 9.48-3.29 14.28-4.63-.09-.22-.17-.41-.26-.64ZM460.64 78.3c-.04-2.9-.11-5.81-.28-8.71-.1-1.68-.17-3.43-.5-5.09-.07.02-.14.03-.2.05.3 6.54.45 12.17.51 17.12.17-.07.34-.14.51-.2 0-1.06-.01-2.11-.03-3.17h-.01ZM470.63 63.24c-3.38-.26-6.81.32-10.1 1.1.41 2.01.47 4.14.57 6.18.18 3.55.25 7.11.27 10.67 3.31-1.38 6.5-3.12 9.3-5.35 1.96-1.56 3.86-3.41 5.02-5.66.73-1.41 1.19-3.22.26-4.65-1.09-1.7-3.46-2.14-5.32-2.29ZM460.29 63.68c1-.24 2.01-.46 3.04-.65-1.15.16-2.37.38-3.71.69v.13c.07-.02.15-.04.22-.05.11-.13.3-.18.45-.11v-.01ZM457.24 100.96c.43-.03.86-.07 1.29-.11.14-.49.27-.99.38-1.49-.44.7-1 1.23-1.67 1.6ZM482.88 104.98c-.18.23-.36.38-.55.47.14.09.27.19.4.28a70.76 70.76 0 0 0 4.37-4.63c.76-.89 1.52-1.81 2.19-2.77-.3-.27-.61-.53-.92-.79-.07 1.94-4.62 6.32-5.49 7.45v-.01Z",transform:"translate(-144.023 -51.76)"}),Gr("path",{d:"M474.36 63.31c-.4-.16-.84-.27-1.29-.37 1.56.42 3.08 1.22 3.76 2.74.62 1.4.32 2.95-.28 4.32.7-1.22.94-2.34.74-3.47-.24-1.33-1.19-2.54-2.93-3.21v-.01ZM477.34 89.18c-1.2-.81-2.4-1.62-3.6-2.42-.14.1-.26.19-.4.29 1.4.67 2.73 1.39 4 2.13ZM465.88 93.85c.37.25.74.5 1.1.75.46.32.92.65 1.38.97-1.57-1.2-2.01-1.61-2.49-1.72h.01ZM574.92 90.06c-2.28-5.21-4.93-11.13-5.67-12.26-.1-.15-1.57-3.01-1.63-3.08 0 0-.01.02-.02.02.4 1.37 1.09 2.69 1.65 3.99 2.14 4.95 4.36 9.86 6.67 14.73.6 1.26 1.21 2.52 1.83 3.78-.75-2.01-1.64-4.45-2.83-7.18ZM448.73 65.29c.1.2.22.38.34.57.22-.02.43-.06.65-.08v-.08c-.14-.05-.25 0-.99-.41ZM460.16 94.81c-.02.31-.06.59-.1.89-.03 1.71-.33 3.43-.79 5.07.15-.02.3-.03.45-.05.01-.04.02-.08.03-.11.09-.34.15-.69.2-1.03.17-1.07.25-2.16.33-3.24.05-.69.08-1.39.12-2.08-.27.1-.27.26-.24.57v-.02Z",transform:"translate(-144.023 -51.76)"}),Gr("path",{d:"m328.67 98.12-3.22-6.58c-1.29-2.63-2.53-5.29-3.72-7.97-.25-.85-.52-1.69-.79-2.53-.81-2.57-1.67-5.12-2.55-7.67-1.92-5.53-3.9-11.08-6.32-16.41-.72-1.58-1.46-3.44-2.63-4.79-.03-.17-.16-.29-.34-.36a.282.282 0 0 0-.23-.04c-.06-.01-.12 0-.18.01-.74.06-1.5.38-2.19.61-2.22.77-4.4 1.64-6.63 2.38-.03-.08-.06-.16-.09-.25-.15-.42-.82-.24-.67.19.03.09.07.19.1.28l-.18.06c-.36.11-.28.6 0 .68.18 1.18.63 2.36.98 3.49.03.09.06.17.08.26-.08.23-.17.46-.24.64-.37.98-.79 1.94-1.21 2.9-1.27 2.89-2.62 5.75-3.98 8.6-3.18 6.67-6.44 13.31-9.64 19.97-1.08 2.25-2.2 4.5-3.15 6.81-.13.32.24.5.5.37 1.34 1.33 2.84 2.5 4.4 3.57.65.44 1.31.87 2.01 1.24.4.22.86.48 1.33.5.24.01.35-.19.33-.37.11-.1.21-.21.28-.28.41-.41.81-.84 1.2-1.26.85-.92 1.69-1.87 2.5-2.84 6.31-2.34 12.6-4.31 18.71-5.84 2.14 5.3 3.43 8.43 3.97 9.58.55 1.05 1.15 1.88 1.82 2.52 1.32.56 6.96-.03 9.23-1.96.87-1.28 1.19-2.67.93-4.15-.09-.5-.22-.95-.4-1.33l-.01-.03Zm-20.09-45.61c.43.77.83 1.56 1.21 2.35-.54-.45-1.27-.99-2.07-1.49-.42-.24-.6-.35-.94-.34.43-.15.85-.29 1.29-.41.17-.05.34-.08.51-.11Zm-25.86 45.66c.78-1.85 1.67-3.66 2.54-5.47 1.51-3.16 3.05-6.31 4.58-9.47 1.28-2.63 2.56-5.26 3.83-7.9l-1.1 2.66c-1.99 4.79-4.97 10.9-8.88 18.15-.43.76-.66 1.51-.71 2.24-.05-.05-.1-.09-.15-.14a.259.259 0 0 0-.11-.07Zm6.24 4.71c-.42-.23-.82-.48-1.22-.74 1.05.45 2 .72 2.55.72l-.21.21c-.05.05-.11.1-.16.15-.01.01-.04.03-.05.04 0-.02.03-.02.05-.03a.27.27 0 0 0-.08.07c-.05-.02-.1-.03-.14-.05-.25-.1-.49-.24-.73-.37h-.01Zm15.73-29.43c1.05 3.15 2.1 6.31 3.1 9.48.34 1.06.69 2.13 1.01 3.21-3.5.63-6.95 1.46-10.27 2.5 2.48-6.03 4.54-11.11 6.16-15.19Zm4.79 12.57c-.23-.79-.49-1.58-.73-2.36-.79-2.54-1.63-5.08-2.46-7.61l-1.2-3.6c.02-.04.04-.09.05-.13 1.6 4.45 3.28 9 5.01 13.57l-.67.12v.01Zm5.83-18.27-.15-.54c-.49-1.64-1.03-3.28-1.6-4.9.23.58.47 1.17.7 1.75 1.56 4.03 3.01 8.1 4.39 12.2-.33-.82-.67-1.64-.98-2.46l-2.35-6.05h-.01ZM390.43 79.37c-.13-10.43-.22-17.5-.24-19.97-.24-1.6.21-2.88-.65-3.65-.14-.13-.32-.23-.52-.32h.03c.45 0 .45-.69 0-.7-1.75-.03-3.5-.04-5.25-.14-1.38-.08-2.76-.21-4.15-.31-.07 0-.12.01-.17.04-.21-.07-.47.03-.45.31l.03.45c-.11.14-.19.3-.22.5-.21 1.26-.32 13.67-.36 23.59-.32 5.79-.67 11.57-.97 17.36-.09 1.73-.29 3.54-.21 5.3-.39.02-.38.64.04.69v.12c.05.44.74.45.7 0v-.06c1.1.09 2.2.21 3.3.3 1.14.19 2.44.2 3.29.17 1.73-.05 2.92-.05 3.8-.37.45-.05.9-.11 1.35-.17.44-.06.25-.73-.19-.67h-.01c.24-.32.45-.72.62-1.25.66-1.84.41-6.36.34-11.33l-.13-9.9.02.01Zm-12.26 18.17c.09-1.91.2-3.81.31-5.71.06 5.38 0 6.14-.01 6.51-.05 1.65-.21 2.81.72 3.66-.39-.04-.78-.07-1.17-.1-.06-1.44.09-2.93.16-4.35l-.01-.01ZM588.97 53.85c-2.06-.25-3.17-.51-3.76-.6a.3.3 0 0 1 .04-.08c.22-.39-.39-.75-.6-.35-.56 1.02-.9 2.19-1.26 3.29-.61 1.88-1.17 3.78-1.72 5.68-.63 2.19-1.24 4.39-1.83 6.59-.81 2.03-1.67 4.05-2.61 6.03-1.7-3.64-3.11-6.04-4.03-7.57-2.26-3.74-2.85-5.48-3.57-6.08l.31-.09c.43-.12.25-.8-.19-.67-1.06.3-2.12.6-3.17.95-.93.32-1.85.69-2.76 1.07-.13.05-.19.16-.22.27-.04.02-.08.05-.11.07-.04-.06-.07-.12-.11-.18a.354.354 0 0 0-.48-.12c-.16.09-.22.32-.13.48l.33.54c0 .09.02.18.06.28.51 1.16.78 1.38.72 1.47-2.42 3.44-5.41 7.86-6.2 9.1-1.27 1.97-2.01 3.14-2.45 3.84l-.91-6.56-.43-4.1c-.19-1.85-.37-3.23-.53-4.13-.19-1.1-.3-2.15-.45-3.16-.2-1.36-.29-2.06-.47-2.42h.04c.45.02.45-.68 0-.7-3.43-.16-6.81.94-10.17 1.48-.24-.22-.73-.04-.58.32.24.59.33 1.25.43 1.87.17 1.06.29 2.13.4 3.2.32 3.09.53 6.2.74 9.3.44 6.75.77 13.51 1.17 20.26.11 1.95.13 3.96.46 5.89.05.3.37.31.55.14.74 1.71 2.87 1.27 6.13 1.27 1.34 0 2.39.04 2.99-.11.02.32.48.53.63.18 3.61-8.26 7.41-16.46 12.05-24.2.03-.05.04-.1.05-.15.3.73.64 1.45.94 2.16.97 2.26 1.97 4.52 2.98 6.76 2.26 5.03 4.54 10.07 7.09 14.96.47.9.94 1.79 1.47 2.65.2.32.4.67.66.96-.18.25 0 .68.34.54.91-.38 1.82-.75 2.76-1.07 1.04-.35 2.11-.65 3.17-.95.39-.11.28-.66-.07-.68.62-.4.95-.96.87-1.91-.3-3.34.72-7.47.86-8.52l2.14-11.43c1.75-10.74 3.13-17.51 3.23-20.86.02-.49.08-2.84.13-3.24.17-1.25.48-1-4.96-1.65l.03-.02Zm-46.19 5.67c-.04-.24-.09-.48-.15-.71l.85-.15c-.34.24-.56.53-.7.86Zm1.95 25.12c-.36-6.63-.7-13.26-1.32-19.87 0 .02 0 .04.01.06.29 1.65.44 2.47.56 2.89 1 8.46 1.5 16.14 1.46 22.8-.06.99-.1 2-.11 3.02-.01.29-.03.68-.05 1.12-.01-.26-.03-.53-.04-.78-.19-3.08-.35-6.16-.52-9.23l.01-.01Zm36.4 18.66c-.11-.11-.24-.29-.37-.49.35.21.74.26 1.17.16-.27.11-.53.22-.8.32v.01Zm-.89-33.72c.12-.43.23-.86.35-1.29.71-1.77 1.41-3.55 2.08-5.33-.15.68-.26 1.22-.31 1.55-.5 2.94-2.13 7.59-3.58 13.36-.15-.35-.29-.66-.43-1 .61-2.44 1.25-4.87 1.9-7.3l-.01.01Zm3.56-12.48c.14-.44.28-.89.42-1.34-.13.7-.27 1.41-.42 2.11-.43 1.19-.86 2.38-1.31 3.57.42-1.45.85-2.9 1.31-4.34Zm-5.22 16.05c-.11.44-.23.87-.35 1.31-.12.46-.23.92-.35 1.38-.1-.22-.19-.4-.28-.61.34-.69.66-1.38.97-2.08h.01Zm-11.64 2.62c.06-.1.12-.19.17-.28.05.13.09.26.14.39a.398.398 0 0 0-.31-.11Zm2.3 2.98c-.56-1.3-1.25-2.63-1.65-3.99 0 0 .01-.02.02-.02.06.08 1.52 2.93 1.63 3.08.73 1.13 3.38 7.04 5.67 12.26 1.2 2.73 2.08 5.17 2.83 7.18-.62-1.25-1.23-2.51-1.83-3.78-2.31-4.87-4.53-9.78-6.67-14.73ZM275.92 87.03c-1.06-2.18-1.13-3.45-2.44-2.93-1.52.57-2.94 1.3-4.5 2.1-1.4.72-2.68 1.44-3.92 2.12.01-.25-.24-.5-.51-.34-4.8 2.93-12.41 4.7-17.28 1.31-1.98-1.77-3.32-4.15-3.97-5.78-.29-.95-.49-1.94-.63-2.93-.14-3.34 1.58-6.53 3.9-9.12.8-.79 1.68-1.51 2.66-2.12 3.7-2.3 8.22-3.07 12.51-2.51 2.71.35 5.32 1.24 7.71 2.55.39.22.75-.39.35-.6-.18-.1-.37-.18-.55-.27.56.27 1.03.33 1.51.19l-.48.39c-.15.11-.23.3-.13.48.09.15.33.24.48.13 1.3-.97 2.46-2.09 3.45-3.37.37-.29.64-.6.65-.97v-.02c.08-.33-.03-.7-.21-1.08-.31-.87-.98-2.01-2.19-3.26-2.43-2.52-3.79-3.45-5.68-4.26-1.14-.49-3.12-1.06-4.42-1.23-3.28-.42-10.64-1.21-18.18 4.11-7.74 5.46-11.94 12.3-12.23 20.61-.08 2.06.04 3.98.34 5.71.74 4.18 2.57 8 5.44 11.34 4.26 4.99 9.76 7.52 16.34 7.52 4.85 0 9.69-1.77 14.89-4.62.23-.12.45-.23.68-.35 2.19-1.1 4.37-2.23 6.46-3.5.49-.3 1.03-.61 1.5-.98 1.47-.87 1.11-1.12.49-2.95-.39-1.14-.76-2.7-2.06-5.36l.02-.01Zm-17.38-21.76c3.05-.42 6.31.79 9.36 2.35.51.39.94.68 1.33.87-5.61-2.86-12.72-3.39-18.44-.79 2.05-1.33 4.24-1.95 7.74-2.44l.01.01ZM443.67 72.67c-.4-2.2-1.15-4.33-2.37-6.22-1.49-2.32-3.58-4.19-5.91-5.64-6.17-3.81-13.75-5.11-20.83-6.01-3.23-.41-6.47-.69-9.72-.92l-1.39-.12c-.85-.07-1.52-.1-2.05-.1-1.08-.06-2.17-.12-3.25-.17-.08 0-.14.02-.19.05-.1.05-.18.14-.16.3.27 2.55-.01 5.12-.92 7.52-.15.38.4.56.62.28 1.32.59 2.68 1.05 4.08 1.37 0 2.78-.14 7.58-.33 12.91 0 0 0 .02-.01.03-.61 3.66-.79 7.42-1 11.12-.23 4.01-.43 8.03-.44 12.05 0 .64 0 1.28.03 1.93.02.31 0 .68.15.96.06.11.14.16.24.17-.2.17-.21.54.11.59 3.83.67 7.78.71 11.68.25 2.3-.19 4.87-.65 7.65-1.56 1.85-.54 3.67-1.18 5.43-1.91 7.2-3.02 14.31-8.07 17.35-15.53.76-1.86 1.17-3.8 1.31-5.75.3-1.93.28-3.82-.09-5.58l.01-.02Zm-19.32-15.42c5.74 1.41 11.94 3.68 15.65 8.55.25.32.47.65.69.99-2.3-2.82-5.68-5.69-12.88-8.23-2.16-.76-4.35-1.43-6.64-2.02 1.06.21 2.13.45 3.18.71Zm-25.82-3.04c.13 0 .27.01.4.02-.14.1-.26.23-.37.38 0-.13-.02-.26-.03-.4Zm34.82 22.17c-.75 3.09-3.55 5.66-5.88 7.58-3.35 2.76-7.21 5.03-11.28 6.54-1.33.49-2.71.9-4.12 1.15.06-1.38.08-2.76.07-4.13-.02-3.78-.16-7.56-.41-11.33-.09-1.37-.18-2.74-.37-4.1 0-.06-.03-.11-.06-.15.09-3.25.12-6.16.03-8.12 6.86 1.05 10.56 2.17 14.06 3.62 5.52 2.28 8.59 5.44 7.97 8.96l-.01-.02Zm-22 16.15c-.12 0-.23-.02-.34-.03l.34-.03v.06Zm-.69-.7c0-3.13.26-8.84.47-14.51.06 1.2.11 2.41.15 3.6.15 3.6.25 7.23.09 10.83-.24.03-.48.05-.71.07v.01Zm-12.33-30.94c.37.63 2.01 1.01 3.23 1.25v.15c-1.31-.31-2.59-.73-3.83-1.29.12-.36.23-.72.33-1.09.08.48.18.84.27.98Zm13.7 31.65v-.18c3.41-.56 6.71-2.02 9.69-3.68 2.31-1.28 4.59-2.78 6.63-4.53-4.69 4.53-11.61 8.24-16.33 8.38l.01.01Zm24.07-.75c-2.05 1.93-4.37 3.56-6.83 4.95 2.7-1.78 5.52-4.03 8.42-6.87.82-.82 1.56-1.69 2.23-2.59-1.08 1.65-2.38 3.16-3.81 4.51h-.01ZM187.16 92.14c-.79-2.47-2.1-7.12-3.1-6.87-.19-.01-2.09.77-4.08 1.54-3.06 1.18-5.91 2.13-10.09 2.82-2.74.42-5.87 1.01-10.61 1.06.04-3.34.05-6.01.05-7.99 7.97-.65 12.33-2.11 16.37-3.55 1.11-.39 2.69-1.01 2.63-1.8-.08-.35-.55-1.39-1.17-2.61-.47-1.16-.98-2.31-1.61-3.38-.42-.71-1.04-1.69-1.86-2.06-.11-.08-.22-.13-.29-.12-.02 0-.04 0-.07.01-.19-.04-.39-.05-.6-.01-.17.03-.24.15-.25.28-.04.02-.09.04-.14.05-4.33 1.48-8.85 2.33-13.24 3.61a499.1 499.1 0 0 0-.31-8.19c4.51-.99 8.88-1.38 13.11-1.82 3.68-.38 6.28.12 7.47.34.59.11.9.16 1.16.18h.1c-.1.37.44.66.62.28.02-.04.03-.08.05-.13.15.2.53.22.62-.1.17-.58.19-1.21.21-1.81v-.36c.03-.15.05-.3.07-.45.52-2.47.33-5.09-.64-7.44-.11-.27-.44-.28-.6-.14-.08-.21-.15-.42-.24-.62-.19-.41-.79-.05-.6.35.03.07.05.15.09.22-.98-.42-2.15-.54-3.17-.63-2.17-.19-4.37-.14-6.54 0-5.7.35-11.4 1.3-16.91 2.79-2.08.56-4.13 1.22-6.14 2-4.54 1.05-3.79 1.51-2.17 6.07.18.51.46 1.68.54 1.94.82 2.47 1.08 2.13 3.1 2.13s0 .05 0 .08h.52c-.48 2.66-.51 5.45-.62 8.13-.15 3.48-.22 6.96-.28 10.45 0 .41-.01.82-.02 1.23-.16.29-.33.57-.51.85-.05.38-.09.77-.14 1.18-.42 3.52-.59 6.48-.52 8.8v.34c.02.47.05.76.06.87.16 1.57-.26 3.47 1.35 3.79 1.61.32 3.5.55 4.85.55.11 0 .22-.02.33-.02 1.79.24 3.67.05 5.45-.12 2.85-.28 5.69-.7 8.51-1.19 3.03-.53 6.05-1.14 9.04-1.86 2.4-.58 4.82-1.19 7.13-2.06.51-.19 1.73-.57 2.46-1.14 1.81-.68 2.18-1 1.57-2.67-.23-.62-.48-1.49-.91-2.78l-.03-.02Zm-11.12-38.71c.89.05 1.93.08 2.89.3-.33 0-.68-.02-1.06-.03-8.28-.26-14.88.75-23.97 2.51 2.41-.64 4.85-1.16 7.28-1.59 4.87-.86 9.91-1.45 14.86-1.19Zm-26.53 22.13c.03 1.71.04 3.43 0 5.14-.04 1.27-.11 2.55-.24 3.82 0-.73.02-1.46.04-2.19.05-2.26.12-4.51.22-6.77h-.02Zm6.73 27.85c.2-.1.4-.21.58-.33 1.82-.17 3.82-.24 5.94-.34-.86.11-1.72.24-2.58.33-1.27.14-2.61.31-3.93.34h-.01ZM534.48 85.44c-3.52-8.38-7.07-16.75-10.5-25.17-.63-1.54-1.25-3.09-1.86-4.65-.31-.8-.65-1.6-.87-2.43-.04-.17-.17-.24-.31-.25.1-.2 0-.51-.29-.53-1.59-.08-3.18-.22-4.78-.25-1.96-.03-3.91.13-5.84.42-.31.05-.31.38-.13.56-.03.06-.05.14-.04.22.23 1.54.63 3.06 1.16 4.53.13.35.27.7.41 1.06l-2.68 6.18c-.11.03-.2.09-.25.22-.67 1.9-1.52 3.73-2.34 5.56a536.85 536.85 0 0 1-3.9 8.45c-2.64 5.64-5.34 11.25-7.91 16.93-.44.97-.88 1.94-1.29 2.93-.2.48-.47 1-.55 1.52v.05c-.02.12.02.26.16.34 1.19.73 2.41 1.41 3.66 2.05 1.2.62 2.45 1.25 3.76 1.61.43.12.62-.55.19-.67-1.13-.31-2.2-.83-3.24-1.36 1.09.36 2.1.69 2.75.93 2.82 1.01 2.38 1.1 4.3-3.75 2.1-1.09 4.34-1.96 6.53-2.79 4.35-1.64 8.8-3.03 13.27-4.29.82 2.01 1.77 3.97 2.72 5.92.35.83.62 1.45.79 1.82.22.42.45.8.69 1.15.17.33.33.67.5 1 .42.8.84 1.63 1.4 2.35.23.29.6 0 .55-.31 1.53-.02 3.06-.07 4.58-.27.92-.12 1.82-.32 2.71-.54 1.39-.27 3.85-1.11 3.74-1.42-.67-1.96-1.55-3.87-2.34-5.78-1.57-3.78-3.16-7.56-4.75-11.33v-.01Zm-11.65-26.16c1.54 3.81 3.12 7.6 4.7 11.4 2.94 7.05 5.91 14.09 8.87 21.13l-1.06-2.17c-2.71-5.51-5.2-11.19-7.41-16.87l-6.65-17.15c-.65-1.45-.55-2.19-.93-2.53.09 0 .18.01.28.02a.29.29 0 0 0-.04.26c.52 2.02 1.47 3.98 2.25 5.91h-.01Zm-6.58 13.58c.05-.15.09-.31.14-.46 1.41 3.92 2.88 7.9 4.39 11.87-3.22.52-6.38 1.25-9.46 2.14.55-1.22 1.05-2.46 1.53-3.7 1.24-3.24 2.37-6.53 3.39-9.85h.01Zm-.23-20c.36 0 .73.03 1.09.05-2.15.1-5.18.33-5.87.74-.24.15-.41.3-.53.45-.06-.29-.13-.58-.18-.88 1.82-.26 3.65-.39 5.49-.35v-.01Zm-.09 18.72c-.49 1.67-1.05 3.33-1.6 4.97-1.07 3.19-2.19 6.38-3.57 9.46-.09.21-.19.43-.29.65-.25.07-.5.14-.74.22 2.53-6.16 4.61-11.29 6.2-15.3Zm-6.34 25.16c4.97-2.38 9.37-4.1 14.02-5.27l.26.64c-4.8 1.35-9.63 2.8-14.28 4.63Zm20.17 6.76c.33.23.68.42 1.04.56h-.33c-.12 0-.21.06-.26.13-.15-.23-.31-.45-.45-.7v.01ZM226.57 91.75c-3.55-4.74-6.68-9.11-9.31-12.99 9.2-15.25 10.05-17.81 10.35-18.38.17-.34 1.09-2.27.64-2.53-1.13-.65-1.03-.65-2.97-1.71-1.19-.65-3.04-1.61-4.53-2.12-1.71-.59-1.24-.36-3 2.77-.06.1-.11.2-.17.3-.75 1.02-1.48 2.05-2.2 3.09-1.88 2.71-3.73 5.45-5.69 8.1-3.68-4.91-6.88-8.76-9.51-11.43-.15-.15-.3-.29-.46-.42-1.27-1.28-7.24 3.53-7.93 5.58-.09.09-.19.16-.28.25-.27.26.03.64.33.58.19.65.5 1.29.94 1.91 3.85 5.06 7.19 9.76 9.94 14-1.23 2.61-3.06 5-4.67 7.38l-2.28 3.33c-.5.66-.93 1.23-1.29 1.69-.67.93-2.09 2.61-2.3 3.87-.51.85-1.16 1.84-1.29 2.83-.06.44.61.63.67.19.01-.08.04-.15.06-.22 1.36 1.08 2.76 2.11 4.19 3.11 1.3.91 2.62 1.85 4.04 2.56.21.1.4 0 .48-.17.24.07.48.14.72.2.44.1.62-.57.19-.67-2.02-.48-3.77-1.57-5.23-3.02-.47-.46-.9-.96-1.32-1.46 1.74 1.35 4.2 2.89 5.89 4.14 1.39 1.03 2.85-2.27 4.22-4.2 1.86-2.64 3.96-5.86 5.52-8.29l10.39 14.51c.67.81 1.14 1.21 1.57 1.36-.05.24.12.51.41.4 1.53-.58 3.05-1.19 4.54-1.87 1.52-.69 3.06-1.45 4.36-2.5a.28.28 0 0 0 .12-.23c1.66-1.1.81-1.74-1.41-4.91-1.13-1.58-1.71-2.36-3.7-5.01l-.03-.02Zm2.41 6.54c.56 1.15 1.19 2.52 1.11 3.81-.06.04-.12.07-.17.1-.03-.88-.55-2.66-.94-3.91Zm-16.51-32.73c1.86-2.65 3.65-5.35 5.57-7.95.4-.55.81-1.13 1.26-1.66.19-.18.38-.33.56-.45.18.03.36.08.55.13l-8.05 10.11.12-.18h-.01ZM192.7 95.48c.79-1.37 1.66-2.69 2.54-4 1.19-1.79 2.4-3.56 3.61-5.33-.04.09-.09.17-.13.26-.1.22.03.41.2.49-2.47 3.42-4.89 6.73-6.4 9.28.21.24.4.48.63.75-.24.07-.4.36-.17.56.4.33.72.77 1.05 1.17.09.11.18.21.27.32-.84-.61-1.66-1.24-2.47-1.88.24-.57.58-1.11.87-1.61v-.01Zm7.46-10.32c.47-.81.98-1.59 1.49-2.37.31-.48.64-.95.96-1.43.26-.29.52-.56.75-.79-.99 1.48-2.09 3.03-3.2 4.59Zm10.03-16.22s-.03-.05-.05-.07c.22-.29.43-.59.64-.89-.2.32-.4.65-.58.96h-.01ZM371.54 87.96c-.01-.08-.01-.16-.03-.23-.06-.38-.58-.29-.66.03-.3-.05-.6-.08-.81-.11-1.14-.15-2.29-.19-3.44-.2 1.04-.09 2.09-.18 3.14-.23.45-.02.45-.72 0-.7-6.57.35-13.14 1.23-19.65 2.11-1.53.21-3.05.42-4.57.68-.01 0-.02.01-.04.01-.04-3.33-.13-6.66-.24-9.99-.19-5.7-.4-11.41-.88-17.1-.13-1.51-.23-3.07-.49-4.58 0-.25 0-.48-.02-.68-.06-1.19-.04-2.61-.68-2.78-.16-.07-.72-.16-1.5-.24.22-.17.16-.62-.2-.63-1.19-.04-2.39.09-3.57.23-1.2.14-2.41.32-3.59.6-.16-.1-.41-.06-.5.12-.06.02-.13.03-.19.05-.35.1-.29.55-.03.66-.26.6-.19 2.27-.21 3-.02.66-.66 33.73-.9 40.3-.03.65.06 1.12.04 1.45-.16 3.05.87 4.96 6.34 3.93 1.09-.08 2.75-.77 5.36-1.43 4.13-1.04 5.78-1.52 6.2-1.65 6.43-1.69 6.78-1.97 11.72-2.43.55-.05 4.8-.38 6.03-.3.64.04 1.19.07 1.65.1.09 0 .16-.03.24-.05.1.27.56.33.66-.02.39-1.32.61-2.71.78-4.08.2-1.61.29-3.24.15-4.86.24.03.52-.23.38-.53-.09-.2-.27-.33-.49-.43v-.02Zm-.63.56c.07.57.11 1.14.11 1.71-.21-.99-.53-1.71-.95-1.87.22.03.44.06.65.11.06.01.12.04.19.05Zm-25.41 1.73c1.54-.36 3.1-.64 4.66-.89-1.61.37-3.18.77-4.66 1.2v-.31Zm-.86-7.37c-.07-1.37-.16-2.75-.25-4.12-.21-3.13-.45-6.27-.79-9.4.02-2.25.08-4.31.13-6.11.16 2.08.29 4.16.4 6.24.23 4.46.38 8.93.5 13.39h.01Zm-.94-4c.16 2.41.29 4.83.39 7.24.06 1.6.14 3.22.09 4.83-.15.05-.32.09-.47.14V78.88h-.01ZM483.72 92.83c-3.05-2.28-6.22-4.4-9.38-6.51 8.86-6.49 13.49-12.95 13.73-19.23.04-.76 0-1.5-.13-2.2-.67-3.82-3.5-6.68-8.39-8.48.13.04.27.08.4.13 3.92 1.39 7.74 4.23 8.5 8.56.34 1.95-.05 3.96-.98 5.69-.21.4.39.75.6.35 1.86-3.46 1.46-7.55-.97-10.63-3.53-4.47-9.76-5.88-15.16-6.16-2.32-.12-4.64-.04-6.95.19-6 .32-12.71 1.68-17.63 3.21-.37.11-.67.23-.92.35-.2-.17-.62.02-.57.37v.03c-.64.68-.18 1.64.48 3.21.38.91.67 1.89 1.15 2.58.32.76.68 1.51 1.13 2.19.14.21.38.19.53.07.19-.02.38-.05.57-.08v1.57c-.06.06-.1.13-.11.23-.27 4.18-.34 8.38-.48 12.57l-.3 9.03c-.24 3.91-.44 6.77-.46 7.26-.05.88-.11 1.95.07 2.81-.01.22-.02.43-.04.65 0 .11-.02.23-.03.35 0 .05-.03.27-.01.16-.05.4.5.59.64.28.05.04.12.08.2.08 1.75.13 3.5.28 5.25.3 1.69.02 3.38-.12 5.06-.32.08.23.36.39.55.15.06-.08.11-.17.16-.26.18-.09.24-.32.18-.48.05-.2.1-.4.13-.6.16-.86.25-1.74.33-2.62.11-1.17.17-2.34.23-3.51.15-.01.32-.03.52-.04.36-.03 1.73-.15 2.06-.15.39 0 .7-.02.95-.04 1.76 1.11 3.45 2.35 5.14 3.55 2.83 2.01 5.64 4.04 8.47 6.04 1.42 1 2.85 2 4.29 2.97.1.06.19.07.27.04.08 0 .17-.02.25-.1 1.61-1.56 3.15-3.18 4.6-4.88.75-.88 1.49-1.78 2.15-2.73.01.01.03.02.04.03.34.3.83-.2.49-.49-2.16-1.9-4.34-3.76-6.64-5.48l.03-.01Zm-6.38-3.65a55.72 55.72 0 0 0-4-2.13c.14-.1.26-.19.4-.29 1.2.81 2.4 1.61 3.6 2.42Zm-20.1 11.78c.67-.37 1.23-.91 1.67-1.6-.11.5-.24 1-.38 1.49-.43.04-.86.08-1.29.11Zm2.38-37.24c1.34-.31 2.56-.52 3.71-.69-1.03.19-2.04.41-3.04.65-.14-.07-.34-.02-.45.11-.07.02-.15.04-.22.05v-.13.01Zm.04.84c.07-.02.14-.03.2-.05.34 1.66.41 3.41.5 5.09.17 2.9.24 5.81.28 8.71l.03 3.17c-.17.07-.34.14-.51.2-.06-4.96-.21-10.58-.51-17.12h.01Zm16.04 5.62c-1.16 2.25-3.06 4.1-5.02 5.66-2.8 2.23-5.99 3.97-9.3 5.35-.01-3.56-.09-7.12-.27-10.67-.1-2.04-.16-4.16-.57-6.18 3.3-.78 6.72-1.36 10.1-1.1 1.85.14 4.23.59 5.32 2.29.92 1.43.46 3.24-.26 4.65Zm.85-.18c.6-1.37.9-2.92.28-4.32-.67-1.52-2.2-2.32-3.76-2.74.46.1.89.21 1.29.37 1.74.67 2.69 1.88 2.93 3.21.2 1.13-.05 2.25-.74 3.47V70Zm-27.47-4.14c-.12-.19-.23-.38-.34-.57.74.42.85.36.99.41v.08c-.22.03-.43.06-.65.08Zm11.21 30.46c-.08 1.08-.16 2.17-.33 3.24-.05.35-.11.69-.2 1.03 0 .04-.02.07-.03.11-.15.02-.3.04-.45.05.45-1.64.76-3.36.79-5.07.03-.29.08-.57.1-.89-.03-.31-.03-.47.24-.57-.04.69-.07 1.39-.12 2.08v.02Zm5.6-2.47c.48.11.92.52 2.49 1.72-.46-.32-.92-.65-1.38-.97-.37-.25-.73-.5-1.1-.75h-.01Zm21.23 7.24a70.76 70.76 0 0 1-4.37 4.63c-.14-.09-.27-.19-.4-.28.19-.09.37-.24.55-.47.87-1.14 5.43-5.51 5.49-7.45.31.26.62.53.92.79-.67.97-1.42 1.88-2.19 2.77v.01Z",fill:"currentColor",transform:"translate(-144.023 -51.76)"})]}),vT=({style:e,size:o="small",withText:t})=>wT("div",{className:`ExcalidrawLogo is-${o}`,style:e,children:[Gr(aM,{}),t&&Gr(lM,{})]});import{Fragment as IT,jsx as Ze,jsxs as Gg}from"react/jsx-runtime";var Yg=({icon:e,shortcut:o,children:t})=>{let r=me();return Gg(IT,{children:[Ze("div",{className:"welcome-screen-menu-item__icon",children:e}),Ze("div",{className:"welcome-screen-menu-item__text",children:t}),o&&!r.editor.isMobile&&Ze("div",{className:"welcome-screen-menu-item__shortcut",children:o})]})};Yg.displayName="WelcomeScreenMenuItemContent";var dl=({onSelect:e,children:o,icon:t,shortcut:r,className:n="",...i})=>Ze("button",{...i,type:"button",className:`welcome-screen-menu-item ${n}`,onClick:e,children:Ze(Yg,{icon:t,shortcut:r,children:o})});dl.displayName="WelcomeScreenMenuItem";var TT=({children:e,href:o,icon:t,shortcut:r,className:n="",...i})=>Ze("a",{...i,className:`welcome-screen-menu-item ${n}`,href:o,target:"_blank",rel:"noreferrer",children:Ze(Yg,{icon:t,shortcut:r,children:e})});TT.displayName="WelcomeScreenMenuItemLink";var lo=({children:e})=>{let{WelcomeScreenCenterTunnel:o}=_e();return Ze(o.In,{children:Ze("div",{className:"welcome-screen-center",children:e||Gg(IT,{children:[Ze(Vg,{}),Ze(Wg,{children:g("welcomeScreen.defaults.center_heading")}),Gg(Kg,{children:[Ze($g,{}),Ze(Xg,{})]})]})})})};lo.displayName="Center";var Vg=({children:e})=>Ze("div",{className:"welcome-screen-center__logo excalifont welcome-screen-decor",children:e||Ze(vT,{withText:!0})});Vg.displayName="Logo";var Wg=({children:e})=>Ze("div",{className:"welcome-screen-center__heading welcome-screen-decor excalifont",children:e});Wg.displayName="Heading";var Kg=({children:e})=>Ze("div",{className:"welcome-screen-menu",children:e});Kg.displayName="Menu";var Xg=()=>{let e=Xe();return Ze(dl,{onSelect:()=>e.executeAction(wn),shortcut:"?",icon:qn,children:g("helpDialog.title")})};Xg.displayName="MenuItemHelp";var $g=()=>{let e=ve(),o=Xe();return e.viewModeEnabled?null:Ze(dl,{onSelect:()=>o.executeAction(Si),shortcut:Ye("loadScene"),icon:Qn,children:g("buttons.load")})};$g.displayName="MenuItemLoadScene";var CT=({onSelect:e})=>{let{t:o}=Ve();return Ze(dl,{shortcut:null,onSelect:e,icon:Kl,children:o("labels.liveCollaboration")})};CT.displayName="MenuItemLiveCollaborationTrigger";lo.Logo=Vg;lo.Heading=Wg;lo.Menu=Kg;lo.MenuItem=dl;lo.MenuItemLink=TT;lo.MenuItemHelp=Xg;lo.MenuItemLoadScene=$g;lo.MenuItemLiveCollaborationTrigger=CT;import{jsx as Ji,jsxs as Zg}from"react/jsx-runtime";var Od=({children:e})=>{let{WelcomeScreenMenuHintTunnel:o}=_e();return Ji(o.In,{children:Zg("div",{className:"excalifont welcome-screen-decor welcome-screen-decor-hint welcome-screen-decor-hint--menu",children:[wf,Ji("div",{className:"welcome-screen-decor-hint__label",children:e||g("welcomeScreen.defaults.menuHint")})]})})};Od.displayName="MenuHint";var Fd=({children:e})=>{let{WelcomeScreenToolbarHintTunnel:o}=_e();return Ji(o.In,{children:Zg("div",{className:"excalifont welcome-screen-decor welcome-screen-decor-hint welcome-screen-decor-hint--toolbar",children:[Ji("div",{className:"welcome-screen-decor-hint__label",children:e||g("welcomeScreen.defaults.toolbarHint")}),Cf]})})};Fd.displayName="ToolbarHint";var Bd=({children:e})=>{let{WelcomeScreenHelpHintTunnel:o}=_e();return Ji(o.In,{children:Zg("div",{className:"excalifont welcome-screen-decor welcome-screen-decor-hint welcome-screen-decor-hint--help",children:[Ji("div",{children:e||g("welcomeScreen.defaults.helpHint")}),Tf]})})};Bd.displayName="HelpHint";import{Fragment as ST,jsx as ml,jsxs as cM}from"react/jsx-runtime";var zd=e=>ml(ST,{children:e.children||cM(ST,{children:[ml(lo,{}),ml(Od,{}),ml(Fd,{}),ml(Bd,{})]})});zd.displayName="WelcomeScreen";zd.Center=lo;zd.Hints={MenuHint:Od,ToolbarHint:Fd,HelpHint:Bd};var sM=zd;import dM from"clsx";import{jsx as pM,jsxs as uM}from"react/jsx-runtime";var kT=({isCollaborating:e,onSelect:o,...t})=>{let r=ve(),n=r.width<830;return uM(Gt,{...t,className:dM("collab-button",{active:e}),type:"button",onSelect:o,style:{position:"relative",width:n?void 0:"auto"},title:g("labels.liveCollaboration"),children:[n?zf:g("labels.share"),r.collaborators.size>0&&pM("div",{className:"CollabButton-collaborators",children:r.collaborators.size})]})},mM=kT;kT.displayName="LiveCollaborationTrigger";import gM from"lodash.throttle";var hM=(e,o,t)=>!!(o&&(o.id===e.editingTextElement?.id||o.id===e.resizingElement?.id||o.id===e.newElement?.id||o.version>t.version||o.version===t.version&&o.versionNonce<t.versionNonce)),fM=gM((e,o,t)=>{if(v.DEV||v.MODE===Rn.TEST||window?.DEBUG_FRACTIONAL_INDICES){let r=Jo(e.map(n=>({...n})));kE(r,{shouldThrow:v.DEV||v.MODE===Rn.TEST,includeBoundTextValidation:!0,reconciliationContext:{localElements:o,remoteElements:t}})}},1e3*60,{leading:!0,trailing:!1}),bM=(e,o,t)=>{let r=Q(e),n=[],i=new Set;for(let l of o)if(!i.has(l.id)){let s=r.get(l.id),c=hM(t,s,l);s&&c?(n.push(s),i.add(s.id)):(n.push(l),i.add(l.id))}for(let l of e)i.has(l.id)||(n.push(l),i.add(l.id));let a=LE(n);return fM(a,e,o),Jo(a),a};import{jsx as LT,jsxs as xM}from"react/jsx-runtime";var AT=({children:e,icon:o})=>{let{TTDDialogTriggerTunnel:t}=_e(),r=pe();return LT(t.In,{children:xM(Ce.Item,{onSelect:()=>{le("ai","dialog open","ttd"),r({openDialog:{name:"ttd",tab:"text-to-diagram"}})},icon:o??Pb,children:[e??g("labels.textToDiagram"),LT(Ce.Item.Badge,{children:"AI"})]})})};AT.displayName="TTDDialogTrigger";import{useLayoutEffect as EM}from"react";var yM=e=>{let o=He();return EM(()=>{o.setPlugins({diagramToCode:{generate:e.generate}})},[o,e.generate]),null};import{jsx as jg}from"react/jsx-runtime";xT();var TM=e=>{let{onChange:o,initialData:t,excalidrawAPI:r,isCollaborating:n=!1,onPointerUpdate:i,renderTopRightUI:a,langCode:l=_o.code,viewModeEnabled:s,zenModeEnabled:c,gridModeEnabled:m,libraryReturnUrl:d,theme:p,name:u,renderCustomStats:h,onPaste:f,detectScroll:b=!0,handleKeyboardGlobally:x=!1,onLibraryChange:T,autoFocus:E=!1,generateIdForFile:w,onLinkOpen:S,generateLinkForSelection:I,onPointerDown:_,onPointerUp:k,onScrollChange:R,onDuplicate:M,children:N,validateEmbeddable:G,renderEmbeddable:H,aiEnabled:V,showDeprecatedFonts:F}=e,O=e.UIOptions?.canvasActions,j={...e.UIOptions,canvasActions:{...$d.canvasActions,...O},tools:{image:e.UIOptions?.tools?.image??!0}};return O?.export&&(j.canvasActions.export.saveFileToDisk=O.export?.saveFileToDisk??$d.canvasActions.export.saveFileToDisk),j.canvasActions.toggleTheme===null&&typeof p>"u"&&(j.canvasActions.toggleTheme=!0),wM(()=>{(async()=>{await import("canvas-roundrect-polyfill")})();let W=ne=>{typeof ne.scale=="number"&&ne.scale!==1&&ne.preventDefault()};return document.addEventListener("touchmove",W,{passive:!1}),()=>{document.removeEventListener("touchmove",W)}},[]),jg(VE,{store:st,children:jg(XE,{langCode:l,theme:p,children:jg(bT,{onChange:o,initialData:t,excalidrawAPI:r,isCollaborating:n,onPointerUpdate:i,renderTopRightUI:a,langCode:l,viewModeEnabled:s,zenModeEnabled:c,gridModeEnabled:m,libraryReturnUrl:d,theme:p,name:u,renderCustomStats:h,UIOptions:j,onPaste:f,detectScroll:b,handleKeyboardGlobally:x,onLibraryChange:T,autoFocus:E,generateIdForFile:w,onLinkOpen:S,generateLinkForSelection:I,onPointerDown:_,onPointerUp:k,onScrollChange:R,onDuplicate:M,validateEmbeddable:G,renderEmbeddable:H,aiEnabled:V!==!1,showDeprecatedFonts:F,children:N})})})},CM=(e,o)=>{if(e.children!==o.children)return!1;let{initialData:t,UIOptions:r={},...n}=e,{initialData:i,UIOptions:a={},...l}=o,s=Object.keys(r),c=Object.keys(a);return s.length!==c.length?!1:s.every(d=>d==="canvasActions"?Object.keys(r.canvasActions).every(u=>u==="export"&&r?.canvasActions?.export&&a?.canvasActions?.export?r.canvasActions.export.saveFileToDisk===a.canvasActions.export.saveFileToDisk:r?.canvasActions?.[u]===a?.canvasActions?.[u]):r[d]===a[d])&&rt(n,l)},IM=vM.memo(TM,CM);IM.displayName="Excalidraw";export{Gt as Button,L as CaptureUpdateAction,Yd as DEFAULT_LASER_COLOR,ud as DefaultSidebar,yM as DiagramToCodePlugin,IM as Excalidraw,Kr as FONT_FAMILY,iM as Footer,mM as LiveCollaborationTrigger,je as MIME_TYPES,ft as MainMenu,St as ROUNDNESS,no as Sidebar,ol as Stats,re as THEME,gg as TTDDialog,AT as TTDDialogTrigger,Dh as UserIdleState,sM as WelcomeScreen,ep as bumpVersion,Yi as convertToExcalidrawElements,_o as defaultLang,FT as elementPartiallyOverlapsWithOrContainsBBox,BT as elementsOverlappingBBox,RT as exportToBlob,un as exportToCanvas,NT as exportToClipboard,Ca as exportToSvg,Se as getCommonBounds,vs as getDataURL,zT as getFreeDrawSvgPath,Ac as getLibraryItemsHash,se as getNonDeletedElements,UT as getSceneVersion,Qm as getTextFromElements,Zm as getVisibleSceneBounds,ap as hashElementsVersion,lp as hashString,OT as isElementInsideBBox,pn as isElementLink,hn as isInvisiblySmallElement,he as isLinearElement,xi as languages,ys as loadFromBlob,Om as loadLibraryFromBlob,Nm as loadSceneOrLibraryFromBlob,Rv as mergeLibraryItems,P as mutateElement,q as newElementWith,uo as normalizeLink,pu as parseLibraryTokensFromUrl,bM as reconcileElements,Rm as restore,PT as restoreAppState,Pm as restoreElements,va as restoreLibraryItems,xt as sceneCoordsToViewportCoords,Bm as serializeAsJSON,Um as serializeLibraryAsJSON,DT as setCustomTextMetricsProvider,me as useDevice,jI as useHandleLibrary,Ve as useI18n,Re as viewportCoordsToSceneCoords,$y as zoomToFitBounds};
