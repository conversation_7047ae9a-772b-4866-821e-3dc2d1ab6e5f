"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_msdax_msdax_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/msdax/msdax.js":
/*!***************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/msdax/msdax.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/msdax/msdax.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"{\", \"}\"]\n  ],\n  autoClosingPairs: [\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".msdax\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.brackets\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    // Query keywords\n    \"VAR\",\n    \"RETURN\",\n    \"NOT\",\n    \"EVALUATE\",\n    \"DATATABLE\",\n    \"ORDER\",\n    \"BY\",\n    \"START\",\n    \"AT\",\n    \"DEFINE\",\n    \"MEASURE\",\n    \"ASC\",\n    \"DESC\",\n    \"IN\",\n    // Datatable types\n    \"BOOLEAN\",\n    \"DOUBLE\",\n    \"INTEGER\",\n    \"DATETIME\",\n    \"CURRENCY\",\n    \"STRING\"\n  ],\n  functions: [\n    // Relational\n    \"CLOSINGBALANCEMONTH\",\n    \"CLOSINGBALANCEQUARTER\",\n    \"CLOSINGBALANCEYEAR\",\n    \"DATEADD\",\n    \"DATESBETWEEN\",\n    \"DATESINPERIOD\",\n    \"DATESMTD\",\n    \"DATESQTD\",\n    \"DATESYTD\",\n    \"ENDOFMONTH\",\n    \"ENDOFQUARTER\",\n    \"ENDOFYEAR\",\n    \"FIRSTDATE\",\n    \"FIRSTNONBLANK\",\n    \"LASTDATE\",\n    \"LASTNONBLANK\",\n    \"NEXTDAY\",\n    \"NEXTMONTH\",\n    \"NEXTQUARTER\",\n    \"NEXTYEAR\",\n    \"OPENINGBALANCEMONTH\",\n    \"OPENINGBALANCEQUARTER\",\n    \"OPENINGBALANCEYEAR\",\n    \"PARALLELPERIOD\",\n    \"PREVIOUSDAY\",\n    \"PREVIOUSMONTH\",\n    \"PREVIOUSQUARTER\",\n    \"PREVIOUSYEAR\",\n    \"SAMEPERIODLASTYEAR\",\n    \"STARTOFMONTH\",\n    \"STARTOFQUARTER\",\n    \"STARTOFYEAR\",\n    \"TOTALMTD\",\n    \"TOTALQTD\",\n    \"TOTALYTD\",\n    \"ADDCOLUMNS\",\n    \"ADDMISSINGITEMS\",\n    \"ALL\",\n    \"ALLEXCEPT\",\n    \"ALLNOBLANKROW\",\n    \"ALLSELECTED\",\n    \"CALCULATE\",\n    \"CALCULATETABLE\",\n    \"CALENDAR\",\n    \"CALENDARAUTO\",\n    \"CROSSFILTER\",\n    \"CROSSJOIN\",\n    \"CURRENTGROUP\",\n    \"DATATABLE\",\n    \"DETAILROWS\",\n    \"DISTINCT\",\n    \"EARLIER\",\n    \"EARLIEST\",\n    \"EXCEPT\",\n    \"FILTER\",\n    \"FILTERS\",\n    \"GENERATE\",\n    \"GENERATEALL\",\n    \"GROUPBY\",\n    \"IGNORE\",\n    \"INTERSECT\",\n    \"ISONORAFTER\",\n    \"KEEPFILTERS\",\n    \"LOOKUPVALUE\",\n    \"NATURALINNERJOIN\",\n    \"NATURALLEFTOUTERJOIN\",\n    \"RELATED\",\n    \"RELATEDTABLE\",\n    \"ROLLUP\",\n    \"ROLLUPADDISSUBTOTAL\",\n    \"ROLLUPGROUP\",\n    \"ROLLUPISSUBTOTAL\",\n    \"ROW\",\n    \"SAMPLE\",\n    \"SELECTCOLUMNS\",\n    \"SUBSTITUTEWITHINDEX\",\n    \"SUMMARIZE\",\n    \"SUMMARIZECOLUMNS\",\n    \"TOPN\",\n    \"TREATAS\",\n    \"UNION\",\n    \"USERELATIONSHIP\",\n    \"VALUES\",\n    \"SUM\",\n    \"SUMX\",\n    \"PATH\",\n    \"PATHCONTAINS\",\n    \"PATHITEM\",\n    \"PATHITEMREVERSE\",\n    \"PATHLENGTH\",\n    \"AVERAGE\",\n    \"AVERAGEA\",\n    \"AVERAGEX\",\n    \"COUNT\",\n    \"COUNTA\",\n    \"COUNTAX\",\n    \"COUNTBLANK\",\n    \"COUNTROWS\",\n    \"COUNTX\",\n    \"DISTINCTCOUNT\",\n    \"DIVIDE\",\n    \"GEOMEAN\",\n    \"GEOMEANX\",\n    \"MAX\",\n    \"MAXA\",\n    \"MAXX\",\n    \"MEDIAN\",\n    \"MEDIANX\",\n    \"MIN\",\n    \"MINA\",\n    \"MINX\",\n    \"PERCENTILE.EXC\",\n    \"PERCENTILE.INC\",\n    \"PERCENTILEX.EXC\",\n    \"PERCENTILEX.INC\",\n    \"PRODUCT\",\n    \"PRODUCTX\",\n    \"RANK.EQ\",\n    \"RANKX\",\n    \"STDEV.P\",\n    \"STDEV.S\",\n    \"STDEVX.P\",\n    \"STDEVX.S\",\n    \"VAR.P\",\n    \"VAR.S\",\n    \"VARX.P\",\n    \"VARX.S\",\n    \"XIRR\",\n    \"XNPV\",\n    // Scalar\n    \"DATE\",\n    \"DATEDIFF\",\n    \"DATEVALUE\",\n    \"DAY\",\n    \"EDATE\",\n    \"EOMONTH\",\n    \"HOUR\",\n    \"MINUTE\",\n    \"MONTH\",\n    \"NOW\",\n    \"SECOND\",\n    \"TIME\",\n    \"TIMEVALUE\",\n    \"TODAY\",\n    \"WEEKDAY\",\n    \"WEEKNUM\",\n    \"YEAR\",\n    \"YEARFRAC\",\n    \"CONTAINS\",\n    \"CONTAINSROW\",\n    \"CUSTOMDATA\",\n    \"ERROR\",\n    \"HASONEFILTER\",\n    \"HASONEVALUE\",\n    \"ISBLANK\",\n    \"ISCROSSFILTERED\",\n    \"ISEMPTY\",\n    \"ISERROR\",\n    \"ISEVEN\",\n    \"ISFILTERED\",\n    \"ISLOGICAL\",\n    \"ISNONTEXT\",\n    \"ISNUMBER\",\n    \"ISODD\",\n    \"ISSUBTOTAL\",\n    \"ISTEXT\",\n    \"USERNAME\",\n    \"USERPRINCIPALNAME\",\n    \"AND\",\n    \"FALSE\",\n    \"IF\",\n    \"IFERROR\",\n    \"NOT\",\n    \"OR\",\n    \"SWITCH\",\n    \"TRUE\",\n    \"ABS\",\n    \"ACOS\",\n    \"ACOSH\",\n    \"ACOT\",\n    \"ACOTH\",\n    \"ASIN\",\n    \"ASINH\",\n    \"ATAN\",\n    \"ATANH\",\n    \"BETA.DIST\",\n    \"BETA.INV\",\n    \"CEILING\",\n    \"CHISQ.DIST\",\n    \"CHISQ.DIST.RT\",\n    \"CHISQ.INV\",\n    \"CHISQ.INV.RT\",\n    \"COMBIN\",\n    \"COMBINA\",\n    \"CONFIDENCE.NORM\",\n    \"CONFIDENCE.T\",\n    \"COS\",\n    \"COSH\",\n    \"COT\",\n    \"COTH\",\n    \"CURRENCY\",\n    \"DEGREES\",\n    \"EVEN\",\n    \"EXP\",\n    \"EXPON.DIST\",\n    \"FACT\",\n    \"FLOOR\",\n    \"GCD\",\n    \"INT\",\n    \"ISO.CEILING\",\n    \"LCM\",\n    \"LN\",\n    \"LOG\",\n    \"LOG10\",\n    \"MOD\",\n    \"MROUND\",\n    \"ODD\",\n    \"PERMUT\",\n    \"PI\",\n    \"POISSON.DIST\",\n    \"POWER\",\n    \"QUOTIENT\",\n    \"RADIANS\",\n    \"RAND\",\n    \"RANDBETWEEN\",\n    \"ROUND\",\n    \"ROUNDDOWN\",\n    \"ROUNDUP\",\n    \"SIGN\",\n    \"SIN\",\n    \"SINH\",\n    \"SQRT\",\n    \"SQRTPI\",\n    \"TAN\",\n    \"TANH\",\n    \"TRUNC\",\n    \"BLANK\",\n    \"CONCATENATE\",\n    \"CONCATENATEX\",\n    \"EXACT\",\n    \"FIND\",\n    \"FIXED\",\n    \"FORMAT\",\n    \"LEFT\",\n    \"LEN\",\n    \"LOWER\",\n    \"MID\",\n    \"REPLACE\",\n    \"REPT\",\n    \"RIGHT\",\n    \"SEARCH\",\n    \"SUBSTITUTE\",\n    \"TRIM\",\n    \"UNICHAR\",\n    \"UNICODE\",\n    \"UPPER\",\n    \"VALUE\"\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@complexIdentifiers\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[({})]/, \"@brackets\"],\n      [\n        /[a-z_][a-zA-Z0-9_]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@functions\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [/\\/\\/+.*/, \"comment\"],\n      [/\\/\\*/, { token: \"comment.quote\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^*/]+/, \"comment\"],\n      [/\\*\\//, { token: \"comment.quote\", next: \"@pop\" }],\n      [/./, \"comment\"]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [\n      [/N\"/, { token: \"string\", next: \"@string\" }],\n      [/\"/, { token: \"string\", next: \"@string\" }]\n    ],\n    string: [\n      [/[^\"]+/, \"string\"],\n      [/\"\"/, \"string\"],\n      [/\"/, { token: \"string\", next: \"@pop\" }]\n    ],\n    complexIdentifiers: [\n      [/\\[/, { token: \"identifier.quote\", next: \"@bracketedIdentifier\" }],\n      [/'/, { token: \"identifier.quote\", next: \"@quotedIdentifier\" }]\n    ],\n    bracketedIdentifier: [\n      [/[^\\]]+/, \"identifier\"],\n      [/]]/, \"identifier\"],\n      [/]/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    quotedIdentifier: [\n      [/[^']+/, \"identifier\"],\n      [/''/, \"identifier\"],\n      [/'/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/msdax/msdax.js\n"));

/***/ })

}]);