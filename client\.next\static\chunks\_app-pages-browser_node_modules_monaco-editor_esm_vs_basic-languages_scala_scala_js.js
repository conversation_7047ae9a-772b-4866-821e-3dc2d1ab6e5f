"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_scala_scala_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/scala/scala.js":
/*!***************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/scala/scala.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/scala/scala.ts\nvar conf = {\n  /*\n   * `...` is allowed as an identifier.\n   * $ is allowed in identifiers.\n   * unary_<op> is allowed as an identifier.\n   * <name>_= is allowed as an identifier.\n   */\n  wordPattern: /(unary_[@~!#%^&*()\\-=+\\\\|:<>\\/?]+)|([a-zA-Z_$][\\w$]*?_=)|(`[^`]+`)|([a-zA-Z_$][\\w$]*)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?region\\\\b)|(?:<editor-fold\\\\b))\"),\n      end: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?endregion\\\\b)|(?:</editor-fold>))\")\n    }\n  }\n};\nvar language = {\n  tokenPostfix: \".scala\",\n  // We can't easily add everything from Dotty, but we can at least add some of its keywords\n  keywords: [\n    \"asInstanceOf\",\n    \"catch\",\n    \"class\",\n    \"classOf\",\n    \"def\",\n    \"do\",\n    \"else\",\n    \"extends\",\n    \"finally\",\n    \"for\",\n    \"foreach\",\n    \"forSome\",\n    \"if\",\n    \"import\",\n    \"isInstanceOf\",\n    \"macro\",\n    \"match\",\n    \"new\",\n    \"object\",\n    \"package\",\n    \"return\",\n    \"throw\",\n    \"trait\",\n    \"try\",\n    \"type\",\n    \"until\",\n    \"val\",\n    \"var\",\n    \"while\",\n    \"with\",\n    \"yield\",\n    // Dotty-specific:\n    \"given\",\n    \"enum\",\n    \"then\"\n  ],\n  // Dotty-specific:\n  softKeywords: [\"as\", \"export\", \"extension\", \"end\", \"derives\", \"on\"],\n  constants: [\"true\", \"false\", \"null\", \"this\", \"super\"],\n  modifiers: [\n    \"abstract\",\n    \"final\",\n    \"implicit\",\n    \"lazy\",\n    \"override\",\n    \"private\",\n    \"protected\",\n    \"sealed\"\n  ],\n  // Dotty-specific:\n  softModifiers: [\"inline\", \"opaque\", \"open\", \"transparent\", \"using\"],\n  name: /(?:[a-z_$][\\w$]*|`[^`]+`)/,\n  type: /(?:[A-Z][\\w$]*)/,\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/^\\\\%@#]+/,\n  digits: /\\d+(_+\\d+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  // C# style strings\n  escapes: /\\\\(?:[btnfr\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  fstring_conv: /[bBhHsScCdoxXeEfgGaAt]|[Tn](?:[HIklMSLNpzZsQ]|[BbhAaCYyjmde]|[RTrDFC])/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // strings\n      [/\\braw\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@rawstringt\" }],\n      [/\\braw\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@rawstring\" }],\n      [/\\bs\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstringt\" }],\n      [/\\bs\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstring\" }],\n      [/\\bf\"\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@fstringt\" }],\n      [/\\bf\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@fstring\" }],\n      [/\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@stringt\" }],\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?[fFdD]?/, \"number.float\", \"@allowMethod\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?[fFdD]?/, \"number.float\", \"@allowMethod\"],\n      [/0[xX](@hexdigits)[Ll]?/, \"number.hex\", \"@allowMethod\"],\n      [/(@digits)[fFdD]/, \"number.float\", \"@allowMethod\"],\n      [/(@digits)[lL]?/, \"number\", \"@allowMethod\"],\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_)\\b/, \"keyword\", \"@allowMethod\"],\n      // identifiers and keywords\n      [/\\bimport\\b/, \"keyword\", \"@import\"],\n      [/\\b(case)([ \\t]+)(class)\\b/, [\"keyword.modifier\", \"white\", \"keyword\"]],\n      [/\\bcase\\b/, \"keyword\", \"@case\"],\n      [/\\bva[lr]\\b/, \"keyword\", \"@vardef\"],\n      [\n        /\\b(def)([ \\t]+)((?:unary_)?@symbols|@name(?:_=)|@name)/,\n        [\"keyword\", \"white\", \"identifier\"]\n      ],\n      [/@name(?=[ \\t]*:(?!:))/, \"variable\"],\n      [/(\\.)(@name|@symbols)/, [\"operator\", { token: \"@rematch\", next: \"@allowMethod\" }]],\n      [/([{(])(\\s*)(@name(?=\\s*=>))/, [\"@brackets\", \"white\", \"variable\"]],\n      [\n        /@name/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@softKeywords\": \"keyword\",\n            \"@modifiers\": \"keyword.modifier\",\n            \"@softModifiers\": \"keyword.modifier\",\n            \"@constants\": {\n              token: \"constant\",\n              next: \"@allowMethod\"\n            },\n            \"@default\": {\n              token: \"identifier\",\n              next: \"@allowMethod\"\n            }\n          }\n        }\n      ],\n      [/@type/, \"type\", \"@allowMethod\"],\n      // whitespace\n      { include: \"@whitespace\" },\n      // @ annotations.\n      [/@[a-zA-Z_$][\\w$]*(?:\\.[a-zA-Z_$][\\w$]*)*/, \"annotation\"],\n      // delimiters and operators\n      [/[{(]/, \"@brackets\"],\n      [/[})]/, \"@brackets\", \"@allowMethod\"],\n      [/\\[/, \"operator.square\"],\n      [/](?!\\s*(?:va[rl]|def|type)\\b)/, \"operator.square\", \"@allowMethod\"],\n      [/]/, \"operator.square\"],\n      [/([=-]>|<-|>:|<:|:>|<%)(?=[\\s\\w()[\\]{},\\.\"'`])/, \"keyword\"],\n      [/@symbols/, \"operator\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,\\.]/, \"delimiter\"],\n      // symbols\n      [/'[a-zA-Z$][\\w$]*(?!')/, \"attribute.name\"],\n      // characters\n      [/'[^\\\\']'/, \"string\", \"@allowMethod\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", { token: \"string\", next: \"@allowMethod\" }]],\n      [/'/, \"string.invalid\"]\n    ],\n    import: [\n      [/;/, \"delimiter\", \"@pop\"],\n      [/^|$/, \"\", \"@pop\"],\n      [/[ \\t]+/, \"white\"],\n      [/[\\n\\r]+/, \"white\", \"@pop\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/@name|@type/, \"type\"],\n      [/[(){}]/, \"@brackets\"],\n      [/[[\\]]/, \"operator.square\"],\n      [/[\\.,]/, \"delimiter\"]\n    ],\n    allowMethod: [\n      [/^|$/, \"\", \"@pop\"],\n      [/[ \\t]+/, \"white\"],\n      [/[\\n\\r]+/, \"white\", \"@pop\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/(?==>[\\s\\w([{])/, \"keyword\", \"@pop\"],\n      [\n        /(@name|@symbols)(?=[ \\t]*[[({\"'`]|[ \\t]+(?:[+-]?\\.?\\d|\\w))/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword\", next: \"@pop\" },\n            \"->|<-|>:|<:|<%\": { token: \"keyword\", next: \"@pop\" },\n            \"@default\": { token: \"@rematch\", next: \"@pop\" }\n          }\n        }\n      ],\n      [\"\", \"\", \"@pop\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    case: [\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_|true|false|null|this|super)\\b/, \"keyword\", \"@allowMethod\"],\n      [/\\bif\\b|=>/, \"keyword\", \"@pop\"],\n      [/`[^`]+`/, \"identifier\", \"@allowMethod\"],\n      [/@name/, \"variable\", \"@allowMethod\"],\n      [/:::?|\\||@(?![a-z_$])/, \"keyword\"],\n      { include: \"@root\" }\n    ],\n    vardef: [\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_|true|false|null|this|super)\\b/, \"keyword\"],\n      [/@name/, \"variable\"],\n      [/:::?|\\||@(?![a-z_$])/, \"keyword\"],\n      [/=|:(?!:)/, \"operator\", \"@pop\"],\n      [/$/, \"white\", \"@pop\"],\n      { include: \"@root\" }\n    ],\n    string: [\n      [/[^\\\\\"\\n\\r]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ]\n    ],\n    stringt: [\n      [/[^\\\\\"\\n\\r]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\"/, \"string\"]\n    ],\n    fstring: [\n      [/@escapes/, \"string.escape\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/%%/, \"string\"],\n      [\n        /(%)([\\-#+ 0,(])(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/,\n        [\"metatag\", \"keyword.modifier\", \"number\", \"metatag\"]\n      ],\n      [/(%)(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/, [\"metatag\", \"number\", \"metatag\"]],\n      [/(%)([\\-#+ 0,(])(@fstring_conv)/, [\"metatag\", \"keyword.modifier\", \"metatag\"]],\n      [/(%)(@fstring_conv)/, [\"metatag\", \"metatag\"]],\n      [/./, \"string\"]\n    ],\n    fstringt: [\n      [/@escapes/, \"string.escape\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/%%/, \"string\"],\n      [\n        /(%)([\\-#+ 0,(])(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/,\n        [\"metatag\", \"keyword.modifier\", \"number\", \"metatag\"]\n      ],\n      [/(%)(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/, [\"metatag\", \"number\", \"metatag\"]],\n      [/(%)([\\-#+ 0,(])(@fstring_conv)/, [\"metatag\", \"keyword.modifier\", \"metatag\"]],\n      [/(%)(@fstring_conv)/, [\"metatag\", \"metatag\"]],\n      [/./, \"string\"]\n    ],\n    sstring: [\n      [/@escapes/, \"string.escape\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/./, \"string\"]\n    ],\n    sstringt: [\n      [/@escapes/, \"string.escape\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/./, \"string\"]\n    ],\n    interp: [[/{/, \"operator\", \"@push\"], [/}/, \"operator\", \"@pop\"], { include: \"@root\" }],\n    rawstring: [\n      [/[^\"]/, \"string\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ]\n    ],\n    rawstringt: [\n      [/[^\"]/, \"string\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\"/, \"string\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/scala/scala.js\n"));

/***/ })

}]);