"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var monaco_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! monaco-editor */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.main.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlignLeft,FiCode,FiCopy,FiDownload,FiHelpCircle,FiLoader,FiMoon,FiPlay,FiSearch,FiSun,FiUpload,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_socketService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/socketService */ \"(app-pages-browser)/./src/services/socketService.ts\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/throttle */ \"(app-pages-browser)/./node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/SocketContext */ \"(app-pages-browser)/./src/context/SocketContext.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_formatCode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/formatCode */ \"(app-pages-browser)/./src/utils/formatCode.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/ThemeContext */ \"(app-pages-browser)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CodeEditor(param) {\n    let { roomId, username: initialUsername } = param;\n    _s();\n    const { isConnected } = (0,_context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket)();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"// Start coding...\");\n    // Use a ref to always track the latest code value\n    const latestCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"// Start coding...\");\n    // Get username from localStorage if available, otherwise use initialUsername\n    const storedUsername =  true ? localStorage.getItem(\"username\") : 0;\n    console.log(\"Initial username: \".concat(initialUsername, \", Stored username: \").concat(storedUsername));\n    // Track the username internally to handle server validation\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUsername || initialUsername);\n    const [typingUser, setTypingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeUsers, setActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUserList, setShowUserList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"javascript\");\n    const [copySuccess, setCopySuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [teacherSelectionDecorations, setTeacherSelectionDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Code execution states\n    const [isExecuting, setIsExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionOutput, setExecutionOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [executionError, setExecutionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOutput, setShowOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Piston API runtimes\n    const [runtimes, setRuntimes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [runtimesLoaded, setRuntimesLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    // Theme and minimap state (ensure these are always defined at the top)\n    const { theme, setTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme)();\n    const [showShortcuts, setShowShortcuts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [minimapEnabled, setMinimapEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Language-specific code snippets\n    const SNIPPETS = {\n        javascript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            }\n        ],\n        typescript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction(): void {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            },\n            {\n                label: \"Type Declaration\",\n                value: \"type MyType = {\\n  key: string;\\n};\"\n            }\n        ],\n        python3: [\n            {\n                label: \"Function\",\n                value: \"def my_function():\\n    # code\\n    pass\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in range(10):\\n    # code\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition:\\n    # code\"\n            },\n            {\n                label: \"Print\",\n                value: \"print('Hello, World!')\"\n            }\n        ],\n        java: [\n            {\n                label: \"Main Method\",\n                value: \"public static void main(String[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"System.out.println(\\\"Hello, World!\\\");\"\n            }\n        ],\n        csharp: [\n            {\n                label: \"Main Method\",\n                value: \"static void Main(string[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"Console.WriteLine(\\\"Hello, World!\\\");\"\n            }\n        ],\n        c: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"printf(\\\"Hello, World!\\\\n\\\");\"\n            }\n        ],\n        cpp: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"std::cout << \\\"Hello, World!\\\" << std::endl;\"\n            }\n        ],\n        go: [\n            {\n                label: \"Main Function\",\n                value: \"func main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i := 0; i < 10; i++ {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"fmt.Println(\\\"Hello, World!\\\")\"\n            }\n        ],\n        ruby: [\n            {\n                label: \"Method\",\n                value: \"def my_method\\n  # code\\nend\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..9\\n  # code\\nend\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition\\n  # code\\nend\"\n            },\n            {\n                label: \"Print\",\n                value: \"puts 'Hello, World!'\"\n            }\n        ],\n        rust: [\n            {\n                label: \"Main Function\",\n                value: \"fn main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..10 {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"println!(\\\"Hello, World!\\\");\"\n            }\n        ],\n        php: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for ($i = 0; $i < 10; $i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if ($condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"echo 'Hello, World!';\"\n            }\n        ]\n    };\n    // Declare the `socketService` instance at the top of the file\n    const socketService = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n    // Leave room handler\n    const handleLeaveRoom = ()=>{\n        socketService.leaveRoom(roomId);\n        router.push(\"/dashboard\");\n    };\n    // Handle editor mounting\n    const handleEditorDidMount = (editor)=>{\n        editorRef.current = editor;\n        setIsLoading(false);\n        // Auto-focus the editor after mounting\n        setTimeout(()=>{\n            if (editor) {\n                editor.focus();\n                // Add keyboard shortcut (Ctrl+Enter) to run code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.CtrlCmd | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.Enter, ()=>{\n                    executeCode();\n                });\n                // Add keyboard shortcut (Shift+Alt+F) to format code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Shift | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Alt | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.KeyF, ()=>{\n                    formatCurrentCode();\n                });\n                // Add teacher selection change listener\n                if (userRole === 'teacher') {\n                    editor.onDidChangeCursorSelection((e)=>{\n                        const selection = e.selection;\n                        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                        // Only broadcast if there's an actual selection (not just cursor position)\n                        if (!selection.isEmpty() && roomId) {\n                            console.log('Teacher made selection:', selection);\n                            socketServiceInstance.sendTeacherSelection(roomId, {\n                                startLineNumber: selection.startLineNumber,\n                                startColumn: selection.startColumn,\n                                endLineNumber: selection.endLineNumber,\n                                endColumn: selection.endColumn\n                            });\n                        } else if (roomId) {\n                            // Clear selection when teacher deselects\n                            socketServiceInstance.clearTeacherSelection(roomId);\n                        }\n                    });\n                }\n            }\n        }, 500) // Small delay to ensure the editor is fully ready\n        ;\n    };\n    // Track if the change is from a remote update\n    const isRemoteUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Create a throttled version of the code change handler\n    // This prevents sending too many updates when typing quickly\n    // but ensures updates are sent at regular intervals\n    const throttledCodeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default()({\n        \"CodeEditor.useRef\": (code)=>{\n            if (roomId && isConnected) {\n                console.log(\"Sending throttled code update to room \".concat(roomId, \", length: \").concat(code.length));\n                socketService.sendCodeChange(roomId, code);\n            }\n        }\n    }[\"CodeEditor.useRef\"], 50) // Use throttle with a short interval for more responsive updates\n    ).current;\n    // Create a debounced version of the typing notification\n    const debouncedTypingNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default()({\n        \"CodeEditor.useCallback[debouncedTypingNotification]\": ()=>{\n            if (roomId && isConnected) {\n                // Always send the current username with typing notifications\n                console.log(\"Sending typing notification with username: \".concat(username));\n                socketService.sendTyping(roomId, username);\n            }\n        }\n    }[\"CodeEditor.useCallback[debouncedTypingNotification]\"], 1000), [\n        roomId,\n        isConnected,\n        username\n    ]);\n    // Handle editor change\n    const handleEditorChange = (value)=>{\n        if (typeof value !== \"string\") return;\n        // Make sure loading is off during code changes\n        setIsLoading(false);\n        // If this change is from a remote update, just update the state and return\n        if (isRemoteUpdate.current) {\n            console.log(\"Handling remote update, not emitting\");\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            isRemoteUpdate.current = false;\n            return;\n        }\n        // Only process if the value actually changed\n        if (value !== latestCodeRef.current) {\n            // Update local state immediately\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            // Send typing notification (debounced)\n            debouncedTypingNotification();\n            // Send code update (throttled)\n            throttledCodeChange(value);\n            // If not connected, try to reconnect\n            if (!isConnected) {\n                console.log(\"Socket not connected, attempting to reconnect\");\n                socketService.connect();\n            }\n        }\n    };\n    // Copy code to clipboard\n    const copyCodeToClipboard = ()=>{\n        if (navigator.clipboard && code) {\n            navigator.clipboard.writeText(code).then(()=>{\n                setCopySuccess(true);\n                setTimeout(()=>setCopySuccess(false), 2000);\n            }).catch((err)=>{\n                console.error('Failed to copy code: ', err);\n            });\n        }\n    };\n    // Toggle user list\n    const toggleUserList = ()=>{\n        setShowUserList((prev)=>!prev);\n    };\n    // Change language\n    const changeLanguage = (lang)=>{\n        setLanguage(lang);\n        // Log available runtimes for this language\n        if (runtimesLoaded && runtimes.length > 0) {\n            const availableRuntimes = runtimes.filter((runtime)=>runtime.language === lang || runtime.aliases && runtime.aliases.includes(lang));\n            if (availableRuntimes.length > 0) {\n                console.log(\"Available runtimes for \".concat(lang, \":\"), availableRuntimes);\n            } else {\n                console.log(\"No runtimes available for \".concat(lang));\n            }\n        }\n    };\n    // Format code\n    const formatCurrentCode = ()=>{\n        if (!code || !editorRef.current) return;\n        try {\n            // Format the code based on the current language\n            const formattedCode = (0,_utils_formatCode__WEBPACK_IMPORTED_MODULE_8__.formatCode)(code, language);\n            // Only update if the code actually changed\n            if (formattedCode !== code) {\n                // Update the editor\n                const model = editorRef.current.getModel();\n                if (model) {\n                    // Store current cursor position/selection\n                    const currentPosition = editorRef.current.getPosition();\n                    const currentSelection = editorRef.current.getSelection();\n                    // Update the editor content\n                    editorRef.current.executeEdits('format', [\n                        {\n                            range: model.getFullModelRange(),\n                            text: formattedCode,\n                            forceMoveMarkers: true\n                        }\n                    ]);\n                    // Restore cursor position if possible\n                    if (currentPosition) {\n                        editorRef.current.setPosition(currentPosition);\n                    }\n                    if (currentSelection) {\n                        editorRef.current.setSelection(currentSelection);\n                    }\n                    // Update state and ref\n                    setCode(formattedCode);\n                    latestCodeRef.current = formattedCode;\n                    // Send the formatted code to other users\n                    if (roomId && isConnected) {\n                        console.log(\"Sending formatted code to room \".concat(roomId, \", length: \").concat(formattedCode.length));\n                        socketService.sendCodeChange(roomId, formattedCode);\n                    }\n                    // Show a success message\n                    console.log('Code formatted successfully');\n                }\n            } else {\n                console.log('Code is already formatted');\n            }\n        } catch (error) {\n            console.error('Error formatting code:', error);\n        }\n    };\n    // Clear execution output\n    const clearOutput = ()=>{\n        setExecutionOutput(null);\n        setExecutionError(null);\n    };\n    // Fetch and store available runtimes from Piston API\n    const fetchRuntimes = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get('https://emkc.org/api/v2/piston/runtimes');\n            console.log('Piston API status:', response.status);\n            console.log('Available runtimes:', response.data);\n            // Store the runtimes in state\n            setRuntimes(response.data);\n            setRuntimesLoaded(true);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error fetching Piston API runtimes:', error);\n            return {\n                success: false,\n                error: axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) ? \"\".concat(error.message, \" - \").concat(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) || 'Unknown', \" \").concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText) || '') : String(error)\n            };\n        }\n    };\n    // Check if the Piston API is available\n    const checkPistonAPI = async ()=>{\n        // If we haven't loaded runtimes yet, fetch them\n        if (!runtimesLoaded) {\n            return await fetchRuntimes();\n        }\n        // If we already have runtimes, just return success\n        if (runtimes.length > 0) {\n            return {\n                success: true,\n                data: runtimes\n            };\n        }\n        // If we've tried to load runtimes but have none, try again\n        return await fetchRuntimes();\n    };\n    // Test the Piston API with a hardcoded sample payload\n    const testPistonAPI = async ()=>{\n        setIsExecuting(true);\n        setExecutionError(null);\n        setExecutionOutput(null);\n        setShowOutput(true);\n        try {\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Find JavaScript runtime\n            const jsRuntimes = apiStatus.data.filter((runtime)=>runtime.language === \"javascript\" || runtime.aliases && runtime.aliases.includes(\"javascript\"));\n            if (jsRuntimes.length === 0) {\n                setExecutionError('No JavaScript runtime found. Please try a different language.');\n                return;\n            }\n            const jsRuntime = jsRuntimes[0];\n            console.log(\"Using JavaScript runtime: \".concat(jsRuntime.language, \" \").concat(jsRuntime.version));\n            // Sample JavaScript code that should work\n            const samplePayload = {\n                language: jsRuntime.language,\n                version: jsRuntime.version,\n                files: [\n                    {\n                        name: \"main.js\",\n                        content: \"console.log('Hello, World!');\"\n                    }\n                ],\n                stdin: \"\",\n                args: []\n            };\n            console.log('Testing Piston API with sample payload:', JSON.stringify(samplePayload, null, 2));\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', samplePayload);\n            console.log('Sample execution response:', response.data);\n            if (response.data.run) {\n                setExecutionOutput(\"API Test Successful!\\n\\n\" + \"Output: \" + response.data.run.stdout + \"\\n\\n\" + \"The API is working correctly. You can now run your own code.\");\n            } else {\n                setExecutionError('API test failed. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error testing Piston API:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                setExecutionError(\"API Test Failed: \".concat(error.response.status, \" \").concat(error.response.statusText, \"\\n\\n\") + JSON.stringify(error.response.data, null, 2));\n            } else {\n                setExecutionError(error instanceof Error ? \"API Test Error: \".concat(error.message) : 'An unknown error occurred while testing the API.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // Execute code using Piston API\n    const executeCode = async ()=>{\n        if (!code || isExecuting) return;\n        // Map Monaco editor language to Piston API language\n        const languageMap = {\n            javascript: \"javascript\",\n            typescript: \"typescript\",\n            python: \"python3\",\n            python3: \"python3\",\n            java: \"java\",\n            csharp: \"csharp\",\n            c: \"c\",\n            cpp: \"cpp\",\n            go: \"go\",\n            ruby: \"ruby\",\n            rust: \"rust\",\n            php: \"php\"\n        };\n        // Get the mapped language\n        const pistonLanguage = languageMap[language] || language;\n        // Check if we have runtimes loaded\n        if (!runtimesLoaded || runtimes.length === 0) {\n            setExecutionError('Runtimes not loaded yet. Please try again in a moment.');\n            setShowOutput(true);\n            return;\n        }\n        // Find available runtimes for the selected language\n        const availableRuntimes = runtimes.filter((runtime)=>runtime.language === pistonLanguage || runtime.aliases && runtime.aliases.includes(pistonLanguage));\n        // Check if the language is supported\n        if (availableRuntimes.length === 0) {\n            // Get a list of supported languages from the runtimes\n            const supportedLanguages = [\n                ...new Set(runtimes.flatMap((runtime)=>[\n                        runtime.language,\n                        ...runtime.aliases || []\n                    ]))\n            ].sort();\n            setExecutionError(\"The language '\".concat(language, \"' (mapped to '\").concat(pistonLanguage, \"') is not supported by the Piston API.\\n\\n\") + \"Supported languages: \".concat(supportedLanguages.join(', ')));\n            setShowOutput(true);\n            return;\n        }\n        // Get the latest version of the runtime\n        const selectedRuntime = availableRuntimes[0];\n        try {\n            setIsExecuting(true);\n            setExecutionError(null);\n            setExecutionOutput(null);\n            setShowOutput(true);\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Determine file extension based on language\n            let fileExtension = '';\n            if (selectedRuntime.language === 'python3' || selectedRuntime.language === 'python') fileExtension = '.py';\n            else if (selectedRuntime.language === 'javascript') fileExtension = '.js';\n            else if (selectedRuntime.language === 'typescript') fileExtension = '.ts';\n            else if (selectedRuntime.language === 'java') fileExtension = '.java';\n            else if (selectedRuntime.language === 'csharp') fileExtension = '.cs';\n            else if (selectedRuntime.language === 'c') fileExtension = '.c';\n            else if (selectedRuntime.language === 'cpp') fileExtension = '.cpp';\n            else if (selectedRuntime.language === 'go') fileExtension = '.go';\n            else if (selectedRuntime.language === 'rust') fileExtension = '.rs';\n            else if (selectedRuntime.language === 'ruby') fileExtension = '.rb';\n            else if (selectedRuntime.language === 'php') fileExtension = '.php';\n            else fileExtension = \".\".concat(selectedRuntime.language);\n            console.log(\"Selected runtime: \".concat(selectedRuntime.language, \" \").concat(selectedRuntime.version));\n            // Prepare the payload according to Piston API documentation\n            const payload = {\n                language: selectedRuntime.language,\n                version: selectedRuntime.version,\n                files: [\n                    {\n                        name: \"main\".concat(fileExtension),\n                        content: code\n                    }\n                ],\n                stdin: '',\n                args: [],\n                compile_timeout: 10000,\n                run_timeout: 5000\n            };\n            // Log the payload for debugging\n            console.log(\"Executing \".concat(pistonLanguage, \" code with payload:\"), JSON.stringify(payload, null, 2));\n            // Make the API request\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', payload);\n            console.log('Execution response:', response.data);\n            const result = response.data;\n            if (result.run) {\n                // Format the output\n                let output = '';\n                let hasOutput = false;\n                // Add compile output if available (for compiled languages)\n                if (result.compile && result.compile.stderr) {\n                    output += \"Compilation output:\\n\".concat(result.compile.stderr, \"\\n\\n\");\n                    hasOutput = true;\n                }\n                // Add standard output\n                if (result.run.stdout) {\n                    output += result.run.stdout;\n                    hasOutput = true;\n                }\n                // Add error output\n                if (result.run.stderr) {\n                    if (hasOutput) output += '\\n';\n                    output += \"Error output:\\n\".concat(result.run.stderr);\n                    hasOutput = true;\n                }\n                // Add exit code if non-zero\n                if (result.run.code !== 0) {\n                    if (hasOutput) output += '\\n';\n                    output += \"\\nProcess exited with code \".concat(result.run.code);\n                    hasOutput = true;\n                }\n                if (!hasOutput) {\n                    output = 'Program executed successfully with no output.';\n                }\n                setExecutionOutput(output);\n            } else {\n                setExecutionError('Failed to execute code. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error executing code:', error);\n            // Handle Axios errors with more detailed information\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                const statusCode = error.response.status;\n                const responseData = error.response.data;\n                console.error('API Error Details:', {\n                    status: statusCode,\n                    data: responseData\n                });\n                // Format a more helpful error message\n                if (statusCode === 400) {\n                    setExecutionError(\"API Error (400 Bad Request): \".concat(JSON.stringify(responseData), \"\\n\\n\") + 'This usually means the API request format is incorrect. ' + 'Please check the console for more details.');\n                } else if (statusCode === 429) {\n                    setExecutionError('Rate limit exceeded. Please try again later.');\n                } else {\n                    setExecutionError(\"API Error (\".concat(statusCode, \"): \").concat(JSON.stringify(responseData), \"\\n\\n\") + 'Please check the console for more details.');\n                }\n            } else {\n                // Handle non-Axios errors\n                setExecutionError(error instanceof Error ? \"Error: \".concat(error.message) : 'An unknown error occurred while executing the code.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // This function will be called when we receive a code update from another user\n    const handleCodeUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleCodeUpdate]\": (incomingCode)=>{\n            console.log(\"Remote code update received, length:\", incomingCode.length);\n            // Make sure loading is off during code updates\n            setIsLoading(false);\n            // Only update if the code is different from our latest code\n            if (incomingCode !== latestCodeRef.current) {\n                try {\n                    // Set the flag to indicate this is a remote update\n                    isRemoteUpdate.current = true;\n                    // Update the editor if it's mounted\n                    if (editorRef.current) {\n                        console.log(\"Updating editor with remote code\");\n                        // Use the editor's model to update the value to preserve cursor position\n                        const model = editorRef.current.getModel();\n                        if (model) {\n                            // Store current cursor position/selection\n                            const currentPosition = editorRef.current.getPosition();\n                            const currentSelection = editorRef.current.getSelection();\n                            // Use executeEdits with better handling of cursor position\n                            editorRef.current.executeEdits('remote', [\n                                {\n                                    range: model.getFullModelRange(),\n                                    text: incomingCode,\n                                    forceMoveMarkers: true\n                                }\n                            ]);\n                            // Restore cursor position if possible\n                            if (currentPosition) {\n                                editorRef.current.setPosition(currentPosition);\n                            }\n                            if (currentSelection) {\n                                editorRef.current.setSelection(currentSelection);\n                            }\n                            // Also update our state and ref\n                            setCode(incomingCode);\n                            latestCodeRef.current = incomingCode;\n                        }\n                    } else {\n                        // If editor isn't mounted yet, just update the state and ref\n                        console.log(\"Editor not mounted, updating state only\");\n                        setCode(incomingCode);\n                        latestCodeRef.current = incomingCode;\n                        isRemoteUpdate.current = false;\n                    }\n                } catch (error) {\n                    console.error(\"Error updating editor with remote code:\", error);\n                    isRemoteUpdate.current = false;\n                }\n            } else {\n                console.log(\"Remote code matches current code, ignoring\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleCodeUpdate]\"], []);\n    // State to track cursor positions\n    const [cursorPositions, setCursorPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Emit cursor movement\n    const handleMouseMove = (e)=>{\n        const position = {\n            x: e.clientX,\n            y: e.clientY\n        };\n        _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().sendCursorMove(roomId, username, position);\n    };\n    // Listen for cursor movement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            const handleCursorMove = {\n                \"CodeEditor.useEffect.handleCursorMove\": (param)=>{\n                    let { userId, position } = param;\n                    setCursorPositions({\n                        \"CodeEditor.useEffect.handleCursorMove\": (prev)=>({\n                                ...prev,\n                                [userId]: position\n                            })\n                    }[\"CodeEditor.useEffect.handleCursorMove\"]);\n                }\n            }[\"CodeEditor.useEffect.handleCursorMove\"];\n            _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().on(\"cursor-move\", handleCursorMove);\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().off(\"cursor-move\", handleCursorMove);\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId\n    ]);\n    // Render cursors\n    const renderCursors = ()=>{\n        return Object.entries(cursorPositions).map((param)=>{\n            let [userId, position] = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    left: position.x,\n                    top: position.y,\n                    backgroundColor: \"red\",\n                    width: 10,\n                    height: 10,\n                    borderRadius: \"50%\",\n                    pointerEvents: \"none\"\n                }\n            }, userId, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 742,\n                columnNumber: 7\n            }, this);\n        });\n    };\n    // Fetch runtimes when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            fetchRuntimes();\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    // Handle request for initial code from new users\n    const handleGetInitialCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleGetInitialCode]\": (data)=>{\n            console.log(\"Received request for initial code from \".concat(data.requestingUsername, \" (\").concat(data.requestingUserId, \")\"));\n            // Only respond if we have code and we're connected\n            if (roomId && latestCodeRef.current && latestCodeRef.current.trim() !== \"// Start coding...\") {\n                const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                console.log(\"Sending initial code to \".concat(data.requestingUsername, \", length: \").concat(latestCodeRef.current.length));\n                socketServiceInstance.sendInitialCode(roomId, latestCodeRef.current, data.requestingUserId);\n            } else {\n                console.log(\"Not sending initial code - no meaningful code to share or not in room\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleGetInitialCode]\"], [\n        roomId\n    ]);\n    // Handle receiving initial code as a new user\n    const handleInitialCodeReceived = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleInitialCodeReceived]\": (data)=>{\n            console.log(\"Received initial code, length: \".concat(data.code.length));\n            // Only apply initial code if we don't have meaningful code yet\n            // This prevents overwriting code that the user might have already started typing\n            if (latestCodeRef.current === \"// Start coding...\" || latestCodeRef.current.trim() === \"\") {\n                console.log(\"Applying received initial code\");\n                // Set the flag to indicate this is a remote update\n                isRemoteUpdate.current = true;\n                // Update the code state and ref\n                setCode(data.code);\n                latestCodeRef.current = data.code;\n                // Update the editor if it's mounted\n                if (editorRef.current) {\n                    editorRef.current.setValue(data.code);\n                }\n            } else {\n                console.log(\"Not applying initial code - user already has meaningful code\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleInitialCodeReceived]\"], []);\n    // Handle teacher selection highlighting\n    const handleTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleTeacherSelection]\": (data)=>{\n            console.log(\"Received teacher selection from \".concat(data.teacherName, \":\"), data.selection);\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't show teacher highlights to the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                const model = editor.getModel();\n                if (!model || !data.selection) {\n                    return;\n                }\n                // Convert selection to Monaco range\n                const range = new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(data.selection.startLineNumber, data.selection.startColumn, data.selection.endLineNumber, data.selection.endColumn);\n                // Clear previous teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [\n                    {\n                        range: range,\n                        options: {\n                            className: 'teacher-selection-highlight',\n                            hoverMessage: {\n                                value: \"Teacher \".concat(data.teacherName, \" highlighted this text\")\n                            },\n                            stickiness: monaco_editor__WEBPACK_IMPORTED_MODULE_3__.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges\n                        }\n                    }\n                ]);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error applying teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    // Handle clearing teacher selection\n    const handleClearTeacherSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleClearTeacherSelection]\": (data)=>{\n            console.log(\"Teacher \".concat(data.teacherName, \" cleared selection\"));\n            if (!editorRef.current || userRole === 'teacher') {\n                return; // Don't clear for the teacher themselves\n            }\n            try {\n                const editor = editorRef.current;\n                // Clear all teacher selection decorations\n                const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, []);\n                setTeacherSelectionDecorations(newDecorations);\n            } catch (error) {\n                console.error('Error clearing teacher selection:', error);\n            }\n        }\n    }[\"CodeEditor.useCallback[handleClearTeacherSelection]\"], [\n        userRole,\n        teacherSelectionDecorations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            // Handle user typing notifications\n            const handleUserTyping = {\n                \"CodeEditor.useEffect.handleUserTyping\": (param)=>{\n                    let { username, userId } = param;\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    // Don't show typing indicator for current user\n                    if (userId && userId === currentUserId) {\n                        return;\n                    }\n                    // Use the exact username without any modifications\n                    // This ensures we display exactly what the user entered on the dashboard\n                    setTypingUser(username);\n                    console.log(\"User typing: \".concat(username));\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserTyping\": ()=>setTypingUser(null)\n                    }[\"CodeEditor.useEffect.handleUserTyping\"], 2000);\n                }\n            }[\"CodeEditor.useEffect.handleUserTyping\"];\n            // Handle user list updates from both user-joined/user-left and room-users-updated events\n            const handleUserListUpdate = {\n                \"CodeEditor.useEffect.handleUserListUpdate\": (data)=>{\n                    console.log('User list update received:', data);\n                    let users = [];\n                    // Handle different event formats\n                    if (Array.isArray(data)) {\n                        // This is from user-joined or user-left events\n                        users = data;\n                    } else if (data && Array.isArray(data.users)) {\n                        // This is from room-users-updated event\n                        users = data.users.map({\n                            \"CodeEditor.useEffect.handleUserListUpdate\": (user)=>{\n                                if (typeof user === 'string') {\n                                    // If it's just a username string, convert to user object\n                                    return {\n                                        username: user\n                                    };\n                                }\n                                return user;\n                            }\n                        }[\"CodeEditor.useEffect.handleUserListUpdate\"]);\n                    }\n                    console.log('Processed users:', users);\n                    console.log('Current username state:', username);\n                    console.log('Stored username:', localStorage.getItem('username'));\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    console.log('Current userId from localStorage:', currentUserId);\n                    // Filter out any invalid users and map to display names\n                    const usernames = users.filter({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>user && user.username\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]).map({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>{\n                            const displayName = user.username;\n                            const isCurrentUser = user.userId === currentUserId;\n                            return isCurrentUser ? \"\".concat(displayName, \" (you)\") : displayName;\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]);\n                    console.log('Setting active users:', usernames);\n                    // Only update if we have users to display\n                    if (usernames.length > 0) {\n                        setActiveUsers(usernames);\n                    } else if (activeUsers.length === 0) {\n                        // If we don't have users from the server but we're connected, add ourselves\n                        const storedUsername = localStorage.getItem('username');\n                        if (storedUsername) {\n                            setActiveUsers([\n                                \"\".concat(storedUsername, \" (you)\")\n                            ]);\n                        }\n                    }\n                    // Log the current state of the active users list\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserListUpdate\": ()=>{\n                            console.log('Active users state after update:', activeUsers);\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate\"], 100);\n                }\n            }[\"CodeEditor.useEffect.handleUserListUpdate\"];\n            // Register event listeners\n            const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n            socketServiceInstance.on('code-update', handleCodeUpdate);\n            socketServiceInstance.on('user-typing', handleUserTyping);\n            socketServiceInstance.on('user-joined', handleUserListUpdate);\n            socketServiceInstance.on('user-left', handleUserListUpdate);\n            socketServiceInstance.on('room-users-updated', handleUserListUpdate);\n            socketServiceInstance.on('get-initial-code', handleGetInitialCode);\n            socketServiceInstance.on('initial-code-received', handleInitialCodeReceived);\n            socketServiceInstance.on('teacher-selection', handleTeacherSelection);\n            socketServiceInstance.on('clear-teacher-selection', handleClearTeacherSelection);\n            // Join the room when component mounts\n            if (roomId) {\n                console.log(\"Joining room:\", roomId);\n                // Only show loading on initial join, not for code updates\n                if (!editorRef.current) {\n                    setIsLoading(true);\n                }\n                const joinRoom = {\n                    \"CodeEditor.useEffect.joinRoom\": async ()=>{\n                        try {\n                            if (socketServiceInstance.isConnected()) {\n                                console.log(\"Socket connected, joining room\");\n                                const { username: validatedUsername, users } = await socketServiceInstance.joinRoom(roomId, username);\n                                console.log(\"Successfully joined room:\", roomId);\n                                console.log(\"Users in room:\", users);\n                                if (validatedUsername !== username) {\n                                    console.log(\"Server validated username from \".concat(username, \" to \").concat(validatedUsername));\n                                    setUsername(validatedUsername);\n                                    localStorage.setItem('username', validatedUsername);\n                                }\n                                if (users && Array.isArray(users)) {\n                                    console.log('Setting active users from join response');\n                                    handleUserListUpdate(users);\n                                }\n                                setIsLoading(false);\n                            } else {\n                                console.log(\"Socket not connected, trying to connect...\");\n                                await new Promise({\n                                    \"CodeEditor.useEffect.joinRoom\": (resolve)=>socketServiceInstance.onConnect({\n                                            \"CodeEditor.useEffect.joinRoom\": ()=>resolve()\n                                        }[\"CodeEditor.useEffect.joinRoom\"])\n                                }[\"CodeEditor.useEffect.joinRoom\"]);\n                                // After connect, try join again\n                                await joinRoom();\n                                return;\n                            }\n                        } catch (error) {\n                            console.error(\"Error joining room:\", error);\n                            setIsLoading(false);\n                        }\n                    }\n                }[\"CodeEditor.useEffect.joinRoom\"];\n                // Start the join process\n                joinRoom();\n                // Set a timeout to hide the loading screen even if the join fails\n                setTimeout({\n                    \"CodeEditor.useEffect\": ()=>{\n                        setIsLoading(false);\n                    }\n                }[\"CodeEditor.useEffect\"], 3000);\n            }\n            // Clean up event listeners when component unmounts\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    // Use an instance of SocketService\n                    const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                    socketServiceInstance.off('code-update', handleCodeUpdate);\n                    socketServiceInstance.off('user-typing', handleUserTyping);\n                    socketServiceInstance.off('user-joined', handleUserListUpdate);\n                    socketServiceInstance.off('user-left', handleUserListUpdate);\n                    socketServiceInstance.off('room-users-updated', handleUserListUpdate);\n                    socketServiceInstance.off('get-initial-code', handleGetInitialCode);\n                    socketServiceInstance.off('initial-code-received', handleInitialCodeReceived);\n                    socketServiceInstance.off('teacher-selection', handleTeacherSelection);\n                    socketServiceInstance.off('clear-teacher-selection', handleClearTeacherSelection);\n                    // Leave the room when component unmounts\n                    if (roomId) {\n                        socketServiceInstance.leaveRoom(roomId);\n                    }\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId,\n        initialUsername,\n        username,\n        handleCodeUpdate,\n        handleGetInitialCode,\n        handleInitialCodeReceived\n    ]);\n    // Download code as file\n    const handleDownload = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"code-\".concat(roomId || \"session\", \".txt\");\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    // Upload code from file\n    const handleUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            var _event_target;\n            if (typeof ((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result) === \"string\") {\n                setCode(event.target.result);\n                latestCodeRef.current = event.target.result;\n            }\n        };\n        reader.readAsText(file);\n    };\n    // Show Monaco's find/replace dialog\n    const handleFindReplace = ()=>{\n        if (editorRef.current) {\n            const action = editorRef.current.getAction(\"actions.find\");\n            if (action) action.run();\n        }\n    };\n    // Insert code snippet\n    const insertSnippet = (snippet)=>{\n        if (editorRef.current) {\n            const model = editorRef.current.getModel();\n            const position = editorRef.current.getPosition();\n            if (model && position) {\n                editorRef.current.executeEdits(\"snippet\", [\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(position.lineNumber, position.column, position.lineNumber, position.column),\n                        text: snippet,\n                        forceMoveMarkers: true\n                    }\n                ]);\n                editorRef.current.focus();\n            }\n        }\n    };\n    // Use the instance of SocketService for the connect method\n    const handleReconnect = ()=>{\n        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n        socketServiceInstance.connect();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-[80vh] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-800 shadow-xl flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"bg-gray-100 dark:bg-zinc-800 px-4 py-2 flex flex-wrap items-center justify-between border-b border-gray-200 dark:border-gray-700 gap-2 min-h-[48px]\",\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    style: {\n                        zIndex: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-3 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 min-w-[90px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1092,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-400 truncate\",\n                                            children: isConnected ? 'Connected' : 'Disconnected'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1093,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded ml-1 transition-colors\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1097,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1091,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: language,\n                                    onChange: (e)=>changeLanguage(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"javascript\",\n                                            children: \"JavaScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"typescript\",\n                                            children: \"TypeScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1113,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"python3\",\n                                            children: \"Python\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"java\",\n                                            children: \"Java\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"csharp\",\n                                            children: \"C#\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1116,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"c\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"cpp\",\n                                            children: \"C++\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"go\",\n                                            children: \"Go\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ruby\",\n                                            children: \"Ruby\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rust\",\n                                            children: \"Rust\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"php\",\n                                            children: \"PHP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: formatCurrentCode,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm bg-blue-600 hover:bg-blue-700 text-white transition-colors whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiAlignLeft, {\n                                            size: 14\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Format\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1133,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: executeCode,\n                                    disabled: isExecuting,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm \".concat(isExecuting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700', \" text-white transition-colors whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: isExecuting ? 1 : 1.05\n                                    },\n                                    whileTap: {\n                                        scale: isExecuting ? 1 : 0.95\n                                    },\n                                    children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                                className: \"animate-spin\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1146,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Running...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1147,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiPlay, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1151,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Run Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1152,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Room:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-white dark:bg-zinc-700 px-2 py-1 rounded font-mono text-xs truncate max-w-[120px]\",\n                                            children: roomId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                            onClick: copyCodeToClipboard,\n                                            className: \"text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            title: \"Copy code to clipboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCopy, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1168,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1161,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1158,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1089,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-2 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowOutput((prev)=>!prev),\n                                    className: \"flex items-center space-x-1 text-sm \".concat(showOutput ? 'text-blue-500 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300', \" hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCode, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Output\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: toggleUserList,\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUsers, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                activeUsers.length,\n                                                \" online\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"flex items-center space-x-1 text-sm text-yellow-600 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSun, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1204,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiMoon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1204,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1197,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setMinimapEnabled((v)=>!v),\n                                    className: \"flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Minimap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Minimap\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1215,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleFindReplace,\n                                    className: \"flex items-center space-x-1 text-sm text-green-600 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Find/Replace\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSearch, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1226,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1219,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleDownload,\n                                    className: \"flex items-center space-x-1 text-sm text-indigo-600 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Download Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiDownload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1237,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"flex items-center space-x-1 text-sm text-pink-600 dark:text-pink-300 bg-pink-100 dark:bg-pink-900/30 hover:bg-pink-200 dark:hover:bg-pink-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Upload Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUpload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1248,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \".js,.ts,.py,.java,.c,.cpp,.go,.rb,.rs,.php,.txt\",\n                                    style: {\n                                        display: \"none\"\n                                    },\n                                    onChange: handleUpload\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    onChange: (e)=>e.target.value && insertSnippet(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[100px]\",\n                                    defaultValue: \"\",\n                                    title: \"Insert Snippet\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Snippets\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1265,\n                                            columnNumber: 15\n                                        }, this),\n                                        (SNIPPETS[language] || SNIPPETS['javascript']).map((snippet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: snippet.value,\n                                                children: snippet.label\n                                            }, snippet.label, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1267,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowShortcuts(true),\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Keyboard Shortcuts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiHelpCircle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1279,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleLeaveRoom,\n                                    className: \"flex items-center space-x-1 text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors px-2 py-1 rounded-md ml-2 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Leave Room\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1289,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1283,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1173,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1082,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showUserList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute right-0 top-12 bg-white dark:bg-zinc-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 w-48 overflow-hidden\",\n                        initial: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            height: 'auto'\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 border-b border-gray-200 dark:border-gray-700 font-medium text-sm text-gray-600 dark:text-gray-300\",\n                                children: \"Active Users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1304,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"max-h-48 overflow-y-auto\",\n                                children: activeUsers.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.li, {\n                                        className: \"px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-zinc-700 flex items-center space-x-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full bg-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1316,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: user\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1317,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1309,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1307,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1297,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1295,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"h-[calc(100%-40px)] \".concat(showOutput ? 'pb-[33%]' : ''),\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.4,\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                height: \"100%\",\n                                defaultLanguage: language,\n                                defaultValue: code,\n                                onChange: handleEditorChange,\n                                onMount: handleEditorDidMount,\n                                theme: theme === \"dark\" ? \"vs-dark\" : \"light\",\n                                options: {\n                                    minimap: {\n                                        enabled: minimapEnabled\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1335,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1334,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1332,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1326,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: typingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 left-4 text-sm text-gray-400 bg-gray-800/80 px-3 py-1 rounded-full backdrop-blur-sm\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            typingUser,\n                            \" is typing...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1351,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1349,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gray-900 text-white border-t border-gray-700 h-1/3 overflow-hidden flex flex-col\",\n                        initial: {\n                            height: 0\n                        },\n                        animate: {\n                            height: '33%'\n                        },\n                        exit: {\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 bg-gray-800 border-b border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1374,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearOutput,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1376,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: testPistonAPI,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-blue-400 hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Test API\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1383,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowOutput(false),\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1390,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1375,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1373,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-auto font-mono text-sm whitespace-pre-wrap\",\n                                children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1401,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Executing code...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1402,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1400,\n                                    columnNumber: 19\n                                }, this) : executionError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400\",\n                                    children: executionError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1405,\n                                    columnNumber: 19\n                                }, this) : executionOutput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: executionOutput\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1407,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: 'Click \"Run Code\" to execute your code.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1409,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1398,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1366,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1364,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: copySuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 right-4 text-sm text-white bg-green-500 px-3 py-1 rounded-full shadow-lg\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: \"Copied to clipboard!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1419,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1417,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showShortcuts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 max-w-md w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-700 dark:hover:text-white\",\n                                    onClick: ()=>setShowShortcuts(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1441,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    children: \"Keyboard Shortcuts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1447,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Run Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1449,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+Enter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1449,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Format Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1450,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Shift+Alt+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1450,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Find/Replace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1451,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1451,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Download Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1452,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+D\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1452,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Upload Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1453,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+U\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1453,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Minimap:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1454,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+M\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1454,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Theme:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1455,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+T\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1455,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Show Shortcuts:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1456,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+H\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1456,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1448,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1440,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1434,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1432,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 1080,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 1078,\n        columnNumber: 5\n    }, this);\n} // Keyboard shortcuts global handler\n // useEffect(() => {\n //   const handler = (e: KeyboardEvent) => {\n //     if (e.altKey && e.key.toLowerCase() === \"d\") {\n //       e.preventDefault();\n //       handleDownload();\n //     } else if (e.altKey && e.key.toLowerCase() === \"u\") {\n //       e.preventDefault();\n //       fileInputRef.current?.click();\n //     } else if (e.altKey && e.key.toLowerCase() === \"m\") {\n //       e.preventDefault();\n //       setMinimapEnabled((v: boolean) => !v);\n //     } else if (e.altKey && e.key.toLowerCase() === \"t\") {\n //       e.preventDefault();\n //       setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n //     } else if (e.altKey && e.key.toLowerCase() === \"h\") {\n //       e.preventDefault();\n //       setShowShortcuts(true);\n //     }\n //   };\n //   window.addEventListener(\"keydown\", handler);\n //   return () => window.removeEventListener(\"keydown\", handler);\n // }, [theme, setTheme]);\n_s(CodeEditor, \"Cf9tT16S7JToKFFdKJngpzRFyDc=\", false, function() {\n    return [\n        _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme\n    ];\n});\n_c = CodeEditor;\nvar _c;\n$RefreshReg$(_c, \"CodeEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CodeEditor.tsx\n"));

/***/ })

});