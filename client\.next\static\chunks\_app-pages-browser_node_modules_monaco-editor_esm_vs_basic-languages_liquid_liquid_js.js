"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_liquid_liquid_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/liquid/liquid.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/liquid/liquid.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.api.js\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/basic-languages/liquid/liquid.ts\nvar EMPTY_ELEMENTS = [\n  \"area\",\n  \"base\",\n  \"br\",\n  \"col\",\n  \"embed\",\n  \"hr\",\n  \"img\",\n  \"input\",\n  \"keygen\",\n  \"link\",\n  \"menuitem\",\n  \"meta\",\n  \"param\",\n  \"source\",\n  \"track\",\n  \"wbr\"\n];\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  brackets: [\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"],\n    [\"{{\", \"}}\"],\n    [\"{%\", \"%}\"],\n    [\"{\", \"}\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"%\", close: \"%\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      afterText: /^<\\/(\\w[\\w\\d]*)\\s*>$/i,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n      }\n    },\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  builtinTags: [\n    \"if\",\n    \"else\",\n    \"elseif\",\n    \"endif\",\n    \"render\",\n    \"assign\",\n    \"capture\",\n    \"endcapture\",\n    \"case\",\n    \"endcase\",\n    \"comment\",\n    \"endcomment\",\n    \"cycle\",\n    \"decrement\",\n    \"for\",\n    \"endfor\",\n    \"include\",\n    \"increment\",\n    \"layout\",\n    \"raw\",\n    \"endraw\",\n    \"render\",\n    \"tablerow\",\n    \"endtablerow\",\n    \"unless\",\n    \"endunless\"\n  ],\n  builtinFilters: [\n    \"abs\",\n    \"append\",\n    \"at_least\",\n    \"at_most\",\n    \"capitalize\",\n    \"ceil\",\n    \"compact\",\n    \"date\",\n    \"default\",\n    \"divided_by\",\n    \"downcase\",\n    \"escape\",\n    \"escape_once\",\n    \"first\",\n    \"floor\",\n    \"join\",\n    \"json\",\n    \"last\",\n    \"lstrip\",\n    \"map\",\n    \"minus\",\n    \"modulo\",\n    \"newline_to_br\",\n    \"plus\",\n    \"prepend\",\n    \"remove\",\n    \"remove_first\",\n    \"replace\",\n    \"replace_first\",\n    \"reverse\",\n    \"round\",\n    \"rstrip\",\n    \"size\",\n    \"slice\",\n    \"sort\",\n    \"sort_natural\",\n    \"split\",\n    \"strip\",\n    \"strip_html\",\n    \"strip_newlines\",\n    \"times\",\n    \"truncate\",\n    \"truncatewords\",\n    \"uniq\",\n    \"upcase\",\n    \"url_decode\",\n    \"url_encode\",\n    \"where\"\n  ],\n  constants: [\"true\", \"false\"],\n  operators: [\"==\", \"!=\", \">\", \"<\", \">=\", \"<=\"],\n  symbol: /[=><!]+/,\n  identifier: /[a-zA-Z_][\\w]*/,\n  tokenizer: {\n    root: [\n      [/\\{\\%\\s*comment\\s*\\%\\}/, \"comment.start.liquid\", \"@comment\"],\n      [/\\{\\{/, { token: \"@rematch\", switchTo: \"@liquidState.root\" }],\n      [/\\{\\%/, { token: \"@rematch\", switchTo: \"@liquidState.root\" }],\n      [/(<)([\\w\\-]+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)([:\\w]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)([\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/\\{/, \"delimiter.html\"],\n      [/[^<{]+/]\n      // text\n    ],\n    comment: [\n      [/\\{\\%\\s*endcomment\\s*\\%\\}/, \"comment.end.liquid\", \"@pop\"],\n      [/./, \"comment.content.liquid\"]\n    ],\n    otherTag: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@liquidState.otherTag\"\n        }\n      ],\n      [\n        /\\{\\%/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@liquidState.otherTag\"\n        }\n      ],\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    liquidState: [\n      [/\\{\\{/, \"delimiter.output.liquid\"],\n      [/\\}\\}/, { token: \"delimiter.output.liquid\", switchTo: \"@$S2.$S3\" }],\n      [/\\{\\%/, \"delimiter.tag.liquid\"],\n      [/raw\\s*\\%\\}/, \"delimiter.tag.liquid\", \"@liquidRaw\"],\n      [/\\%\\}/, { token: \"delimiter.tag.liquid\", switchTo: \"@$S2.$S3\" }],\n      { include: \"liquidRoot\" }\n    ],\n    liquidRaw: [\n      [/^(?!\\{\\%\\s*endraw\\s*\\%\\}).+/],\n      [/\\{\\%/, \"delimiter.tag.liquid\"],\n      [/@identifier/],\n      [/\\%\\}/, { token: \"delimiter.tag.liquid\", next: \"@root\" }]\n    ],\n    liquidRoot: [\n      [/\\d+(\\.\\d+)?/, \"number.liquid\"],\n      [/\"[^\"]*\"/, \"string.liquid\"],\n      [/'[^']*'/, \"string.liquid\"],\n      [/\\s+/],\n      [\n        /@symbol/,\n        {\n          cases: {\n            \"@operators\": \"operator.liquid\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/\\./],\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@constants\": \"keyword.liquid\",\n            \"@builtinFilters\": \"predefined.liquid\",\n            \"@builtinTags\": \"predefined.liquid\",\n            \"@default\": \"variable.liquid\"\n          }\n        }\n      ],\n      [/[^}|%]/, \"variable.liquid\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/liquid/liquid.js\n"));

/***/ })

}]);