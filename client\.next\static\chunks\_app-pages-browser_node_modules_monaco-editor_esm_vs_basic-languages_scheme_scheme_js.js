"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_scheme_scheme_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/scheme/scheme.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/scheme/scheme.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/scheme/scheme.ts\nvar conf = {\n  comments: {\n    lineComment: \";\",\n    blockComment: [\"#|\", \"|#\"]\n  },\n  brackets: [\n    [\"(\", \")\"],\n    [\"{\", \"}\"],\n    [\"[\", \"]\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  ignoreCase: true,\n  tokenPostfix: \".scheme\",\n  brackets: [\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" }\n  ],\n  keywords: [\n    \"case\",\n    \"do\",\n    \"let\",\n    \"loop\",\n    \"if\",\n    \"else\",\n    \"when\",\n    \"cons\",\n    \"car\",\n    \"cdr\",\n    \"cond\",\n    \"lambda\",\n    \"lambda*\",\n    \"syntax-rules\",\n    \"format\",\n    \"set!\",\n    \"quote\",\n    \"eval\",\n    \"append\",\n    \"list\",\n    \"list?\",\n    \"member?\",\n    \"load\"\n  ],\n  constants: [\"#t\", \"#f\"],\n  operators: [\"eq?\", \"eqv?\", \"equal?\", \"and\", \"or\", \"not\", \"null?\"],\n  tokenizer: {\n    root: [\n      [/#[xXoObB][0-9a-fA-F]+/, \"number.hex\"],\n      [/[+-]?\\d+(?:(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)?/, \"number.float\"],\n      [\n        /(?:\\b(?:(define|define-syntax|define-macro))\\b)(\\s+)((?:\\w|\\-|\\!|\\?)*)/,\n        [\"keyword\", \"white\", \"variable\"]\n      ],\n      { include: \"@whitespace\" },\n      { include: \"@strings\" },\n      [\n        /[a-zA-Z_#][a-zA-Z0-9_\\-\\?\\!\\*]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@constants\": \"constant\",\n            \"@operators\": \"operators\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    comment: [\n      [/[^\\|#]+/, \"comment\"],\n      [/#\\|/, \"comment\", \"@push\"],\n      [/\\|#/, \"comment\", \"@pop\"],\n      [/[\\|#]/, \"comment\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/#\\|/, \"comment\", \"@comment\"],\n      [/;.*$/, \"comment\"]\n    ],\n    strings: [\n      [/\"$/, \"string\", \"@popall\"],\n      [/\"(?=.)/, \"string\", \"@multiLineString\"]\n    ],\n    multiLineString: [\n      [/[^\\\\\"]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/\\\\./, \"string.escape\"],\n      [/\"/, \"string\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbW9uYWNvLWVkaXRvci9lc20vdnMvYmFzaWMtbGFuZ3VhZ2VzL3NjaGVtZS9zY2hlbWUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUdBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsT0FBTyxLQUFLO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsTUFBTSxRQUFRLFlBQVksR0FBRztBQUM3QixNQUFNLHVCQUF1QjtBQUM3QixNQUFNLHVCQUF1QjtBQUM3QixNQUFNO0FBQ047QUFDQTtBQUNBLE1BQU0sUUFBUSxZQUFZLEdBQUc7QUFDN0IsTUFBTSx1QkFBdUI7QUFDN0IsTUFBTSx1QkFBdUI7QUFDN0IsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSx1REFBdUQ7QUFDN0QsTUFBTSxRQUFRLFlBQVksNkJBQTZCO0FBQ3ZELE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLHdCQUF3QjtBQUNoQyxRQUFRLHFCQUFxQjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUlFIiwic291cmNlcyI6WyJDOlxcUHJvamVjdFxccmVhbGNvZGVcXG5vZGVfbW9kdWxlc1xcbW9uYWNvLWVkaXRvclxcZXNtXFx2c1xcYmFzaWMtbGFuZ3VhZ2VzXFxzY2hlbWVcXHNjaGVtZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiEtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4gKiBWZXJzaW9uOiAwLjUyLjIoNDA0NTQ1YmRlZDFkZjZmZmE0MWVhMGFmNGU4ZGRiMjE5MDE4YzZjMSlcbiAqIFJlbGVhc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZVxuICogaHR0cHM6Ly9naXRodWIuY29tL21pY3Jvc29mdC9tb25hY28tZWRpdG9yL2Jsb2IvbWFpbi9MSUNFTlNFLnR4dFxuICotLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cblxuLy8gc3JjL2Jhc2ljLWxhbmd1YWdlcy9zY2hlbWUvc2NoZW1lLnRzXG52YXIgY29uZiA9IHtcbiAgY29tbWVudHM6IHtcbiAgICBsaW5lQ29tbWVudDogXCI7XCIsXG4gICAgYmxvY2tDb21tZW50OiBbXCIjfFwiLCBcInwjXCJdXG4gIH0sXG4gIGJyYWNrZXRzOiBbXG4gICAgW1wiKFwiLCBcIilcIl0sXG4gICAgW1wie1wiLCBcIn1cIl0sXG4gICAgW1wiW1wiLCBcIl1cIl1cbiAgXSxcbiAgYXV0b0Nsb3NpbmdQYWlyczogW1xuICAgIHsgb3BlbjogXCJ7XCIsIGNsb3NlOiBcIn1cIiB9LFxuICAgIHsgb3BlbjogXCJbXCIsIGNsb3NlOiBcIl1cIiB9LFxuICAgIHsgb3BlbjogXCIoXCIsIGNsb3NlOiBcIilcIiB9LFxuICAgIHsgb3BlbjogJ1wiJywgY2xvc2U6ICdcIicgfVxuICBdLFxuICBzdXJyb3VuZGluZ1BhaXJzOiBbXG4gICAgeyBvcGVuOiBcIntcIiwgY2xvc2U6IFwifVwiIH0sXG4gICAgeyBvcGVuOiBcIltcIiwgY2xvc2U6IFwiXVwiIH0sXG4gICAgeyBvcGVuOiBcIihcIiwgY2xvc2U6IFwiKVwiIH0sXG4gICAgeyBvcGVuOiAnXCInLCBjbG9zZTogJ1wiJyB9XG4gIF1cbn07XG52YXIgbGFuZ3VhZ2UgPSB7XG4gIGRlZmF1bHRUb2tlbjogXCJcIixcbiAgaWdub3JlQ2FzZTogdHJ1ZSxcbiAgdG9rZW5Qb3N0Zml4OiBcIi5zY2hlbWVcIixcbiAgYnJhY2tldHM6IFtcbiAgICB7IG9wZW46IFwiKFwiLCBjbG9zZTogXCIpXCIsIHRva2VuOiBcImRlbGltaXRlci5wYXJlbnRoZXNpc1wiIH0sXG4gICAgeyBvcGVuOiBcIntcIiwgY2xvc2U6IFwifVwiLCB0b2tlbjogXCJkZWxpbWl0ZXIuY3VybHlcIiB9LFxuICAgIHsgb3BlbjogXCJbXCIsIGNsb3NlOiBcIl1cIiwgdG9rZW46IFwiZGVsaW1pdGVyLnNxdWFyZVwiIH1cbiAgXSxcbiAga2V5d29yZHM6IFtcbiAgICBcImNhc2VcIixcbiAgICBcImRvXCIsXG4gICAgXCJsZXRcIixcbiAgICBcImxvb3BcIixcbiAgICBcImlmXCIsXG4gICAgXCJlbHNlXCIsXG4gICAgXCJ3aGVuXCIsXG4gICAgXCJjb25zXCIsXG4gICAgXCJjYXJcIixcbiAgICBcImNkclwiLFxuICAgIFwiY29uZFwiLFxuICAgIFwibGFtYmRhXCIsXG4gICAgXCJsYW1iZGEqXCIsXG4gICAgXCJzeW50YXgtcnVsZXNcIixcbiAgICBcImZvcm1hdFwiLFxuICAgIFwic2V0IVwiLFxuICAgIFwicXVvdGVcIixcbiAgICBcImV2YWxcIixcbiAgICBcImFwcGVuZFwiLFxuICAgIFwibGlzdFwiLFxuICAgIFwibGlzdD9cIixcbiAgICBcIm1lbWJlcj9cIixcbiAgICBcImxvYWRcIlxuICBdLFxuICBjb25zdGFudHM6IFtcIiN0XCIsIFwiI2ZcIl0sXG4gIG9wZXJhdG9yczogW1wiZXE/XCIsIFwiZXF2P1wiLCBcImVxdWFsP1wiLCBcImFuZFwiLCBcIm9yXCIsIFwibm90XCIsIFwibnVsbD9cIl0sXG4gIHRva2VuaXplcjoge1xuICAgIHJvb3Q6IFtcbiAgICAgIFsvI1t4WG9PYkJdWzAtOWEtZkEtRl0rLywgXCJudW1iZXIuaGV4XCJdLFxuICAgICAgWy9bKy1dP1xcZCsoPzooPzpcXC5cXGQqKT8oPzpbZUVdWystXT9cXGQrKT8pPy8sIFwibnVtYmVyLmZsb2F0XCJdLFxuICAgICAgW1xuICAgICAgICAvKD86XFxiKD86KGRlZmluZXxkZWZpbmUtc3ludGF4fGRlZmluZS1tYWNybykpXFxiKShcXHMrKSgoPzpcXHd8XFwtfFxcIXxcXD8pKikvLFxuICAgICAgICBbXCJrZXl3b3JkXCIsIFwid2hpdGVcIiwgXCJ2YXJpYWJsZVwiXVxuICAgICAgXSxcbiAgICAgIHsgaW5jbHVkZTogXCJAd2hpdGVzcGFjZVwiIH0sXG4gICAgICB7IGluY2x1ZGU6IFwiQHN0cmluZ3NcIiB9LFxuICAgICAgW1xuICAgICAgICAvW2EtekEtWl8jXVthLXpBLVowLTlfXFwtXFw/XFwhXFwqXSovLFxuICAgICAgICB7XG4gICAgICAgICAgY2FzZXM6IHtcbiAgICAgICAgICAgIFwiQGtleXdvcmRzXCI6IFwia2V5d29yZFwiLFxuICAgICAgICAgICAgXCJAY29uc3RhbnRzXCI6IFwiY29uc3RhbnRcIixcbiAgICAgICAgICAgIFwiQG9wZXJhdG9yc1wiOiBcIm9wZXJhdG9yc1wiLFxuICAgICAgICAgICAgXCJAZGVmYXVsdFwiOiBcImlkZW50aWZpZXJcIlxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgXVxuICAgIF0sXG4gICAgY29tbWVudDogW1xuICAgICAgWy9bXlxcfCNdKy8sIFwiY29tbWVudFwiXSxcbiAgICAgIFsvI1xcfC8sIFwiY29tbWVudFwiLCBcIkBwdXNoXCJdLFxuICAgICAgWy9cXHwjLywgXCJjb21tZW50XCIsIFwiQHBvcFwiXSxcbiAgICAgIFsvW1xcfCNdLywgXCJjb21tZW50XCJdXG4gICAgXSxcbiAgICB3aGl0ZXNwYWNlOiBbXG4gICAgICBbL1sgXFx0XFxyXFxuXSsvLCBcIndoaXRlXCJdLFxuICAgICAgWy8jXFx8LywgXCJjb21tZW50XCIsIFwiQGNvbW1lbnRcIl0sXG4gICAgICBbLzsuKiQvLCBcImNvbW1lbnRcIl1cbiAgICBdLFxuICAgIHN0cmluZ3M6IFtcbiAgICAgIFsvXCIkLywgXCJzdHJpbmdcIiwgXCJAcG9wYWxsXCJdLFxuICAgICAgWy9cIig/PS4pLywgXCJzdHJpbmdcIiwgXCJAbXVsdGlMaW5lU3RyaW5nXCJdXG4gICAgXSxcbiAgICBtdWx0aUxpbmVTdHJpbmc6IFtcbiAgICAgIFsvW15cXFxcXCJdKyQvLCBcInN0cmluZ1wiLCBcIkBwb3BhbGxcIl0sXG4gICAgICBbL1teXFxcXFwiXSsvLCBcInN0cmluZ1wiXSxcbiAgICAgIFsvXFxcXC4vLCBcInN0cmluZy5lc2NhcGVcIl0sXG4gICAgICBbL1wiLywgXCJzdHJpbmdcIiwgXCJAcG9wYWxsXCJdLFxuICAgICAgWy9cXFxcJC8sIFwic3RyaW5nXCJdXG4gICAgXVxuICB9XG59O1xuZXhwb3J0IHtcbiAgY29uZixcbiAgbGFuZ3VhZ2Vcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/scheme/scheme.js\n"));

/***/ })

}]);