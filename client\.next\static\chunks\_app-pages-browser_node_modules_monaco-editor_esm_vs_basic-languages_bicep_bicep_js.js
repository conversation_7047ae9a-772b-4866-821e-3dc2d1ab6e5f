"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_bicep_bicep_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/bicep/bicep.js":
/*!***************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/bicep/bicep.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/bicep/bicep.ts\nvar bounded = (text) => `\\\\b${text}\\\\b`;\nvar identifierStart = \"[_a-zA-Z]\";\nvar identifierContinue = \"[_a-zA-Z0-9]\";\nvar identifier = bounded(`${identifierStart}${identifierContinue}*`);\nvar keywords = [\n  \"targetScope\",\n  \"resource\",\n  \"module\",\n  \"param\",\n  \"var\",\n  \"output\",\n  \"for\",\n  \"in\",\n  \"if\",\n  \"existing\"\n];\nvar namedLiterals = [\"true\", \"false\", \"null\"];\nvar nonCommentWs = `[ \\\\t\\\\r\\\\n]`;\nvar numericLiteral = `[0-9]+`;\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\" },\n    { open: \"'''\", close: \"'''\" }\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: \"'''\", close: \"'''\", notIn: [\"string\", \"comment\"] }\n  ],\n  autoCloseBefore: \":.,=}])' \\n\t\",\n  indentationRules: {\n    increaseIndentPattern: new RegExp(\"^((?!\\\\/\\\\/).)*(\\\\{[^}\\\"'`]*|\\\\([^)\\\"'`]*|\\\\[[^\\\\]\\\"'`]*)$\"),\n    decreaseIndentPattern: new RegExp(\"^((?!.*?\\\\/\\\\*).*\\\\*/)?\\\\s*[\\\\}\\\\]].*$\")\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".bicep\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  symbols: /[=><!~?:&|+\\-*/^%]+/,\n  keywords,\n  namedLiterals,\n  escapes: `\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|'|\\\\\\${)`,\n  tokenizer: {\n    root: [{ include: \"@expression\" }, { include: \"@whitespace\" }],\n    stringVerbatim: [\n      { regex: `(|'|'')[^']`, action: { token: \"string\" } },\n      { regex: `'''`, action: { token: \"string.quote\", next: \"@pop\" } }\n    ],\n    stringLiteral: [\n      { regex: `\\\\\\${`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `[^\\\\\\\\'$]+`, action: { token: \"string\" } },\n      { regex: \"@escapes\", action: { token: \"string.escape\" } },\n      { regex: `\\\\\\\\.`, action: { token: \"string.escape.invalid\" } },\n      { regex: `'`, action: { token: \"string\", next: \"@pop\" } }\n    ],\n    bracketCounting: [\n      { regex: `{`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `}`, action: { token: \"delimiter.bracket\", next: \"@pop\" } },\n      { include: \"expression\" }\n    ],\n    comment: [\n      { regex: `[^\\\\*]+`, action: { token: \"comment\" } },\n      { regex: `\\\\*\\\\/`, action: { token: \"comment\", next: \"@pop\" } },\n      { regex: `[\\\\/*]`, action: { token: \"comment\" } }\n    ],\n    whitespace: [\n      { regex: nonCommentWs },\n      { regex: `\\\\/\\\\*`, action: { token: \"comment\", next: \"@comment\" } },\n      { regex: `\\\\/\\\\/.*$`, action: { token: \"comment\" } }\n    ],\n    expression: [\n      { regex: `'''`, action: { token: \"string.quote\", next: \"@stringVerbatim\" } },\n      { regex: `'`, action: { token: \"string.quote\", next: \"@stringLiteral\" } },\n      { regex: numericLiteral, action: { token: \"number\" } },\n      {\n        regex: identifier,\n        action: {\n          cases: {\n            \"@keywords\": { token: \"keyword\" },\n            \"@namedLiterals\": { token: \"keyword\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      }\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/bicep/bicep.js\n"));

/***/ })

}]);