{"version": 3, "sources": ["../../../locales/hu-HU.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Beillesztés\",\n    \"pasteAsPlaintext\": \"Beillesztés formázatlan szövegként\",\n    \"pasteCharts\": \"Grafikon beillesztése\",\n    \"selectAll\": \"Összes kijelölése\",\n    \"multiSelect\": \"Elem hozzáadása a kijelöléshez\",\n    \"moveCanvas\": \"Vászon mozgatása\",\n    \"cut\": \"Kivágás\",\n    \"copy\": \"Másolás\",\n    \"copyAsPng\": \"Vágólapra másolás mint PNG\",\n    \"copyAsSvg\": \"Vágólapra másolás mint SVG\",\n    \"copyText\": \"Vágólapra másolás szövegként\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Előrébb hozás\",\n    \"sendToBack\": \"Hátraküldés\",\n    \"bringToFront\": \"Előrehozás\",\n    \"sendBackward\": \"<PERSON><PERSON><PERSON><PERSON><PERSON> küldés\",\n    \"delete\": \"Törlés\",\n    \"copyStyles\": \"Stílus másolása\",\n    \"pasteStyles\": \"Stílus beillesztése\",\n    \"stroke\": \"<PERSON><PERSON><PERSON><PERSON>al\",\n    \"background\": \"Háttér\",\n    \"fill\": \"Kitöltés\",\n    \"strokeWidth\": \"Körvonal vastagsága\",\n    \"strokeStyle\": \"Körvonal stílusa\",\n    \"strokeStyle_solid\": \"Kitöltött\",\n    \"strokeStyle_dashed\": \"Szaggatott\",\n    \"strokeStyle_dotted\": \"Pontozott\",\n    \"sloppiness\": \"Stílus\",\n    \"opacity\": \"Áttetszőség\",\n    \"textAlign\": \"Szöveg igazítása\",\n    \"edges\": \"Szélek\",\n    \"sharp\": \"Éles\",\n    \"round\": \"Kerek\",\n    \"arrowheads\": \"Nyílhegyek\",\n    \"arrowhead_none\": \"Nincs\",\n    \"arrowhead_arrow\": \"Nyíl\",\n    \"arrowhead_bar\": \"Oszlop\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Háromszög\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Betűméret\",\n    \"fontFamily\": \"Betűkészlet család\",\n    \"addWatermark\": \"Add hozzá, hogy \\\"Excalidraw-val készült\\\"\",\n    \"handDrawn\": \"Kézzel rajzolt\",\n    \"normal\": \"Normál\",\n    \"code\": \"Kód\",\n    \"small\": \"Kicsi\",\n    \"medium\": \"Közepes\",\n    \"large\": \"Nagy\",\n    \"veryLarge\": \"Nagyon nagy\",\n    \"solid\": \"Kitöltött\",\n    \"hachure\": \"Vonalkázott\",\n    \"zigzag\": \"Cikkcakk\",\n    \"crossHatch\": \"Keresztcsíkozott\",\n    \"thin\": \"Vékony\",\n    \"bold\": \"Félkövér\",\n    \"left\": \"Bal\",\n    \"center\": \"Közép\",\n    \"right\": \"Jobb\",\n    \"extraBold\": \"Extra Félkövér\",\n    \"architect\": \"Tervezői\",\n    \"artist\": \"Művészi\",\n    \"cartoonist\": \"Karikatúrás\",\n    \"fileTitle\": \"Fájlnév\",\n    \"colorPicker\": \"Színválasztó\",\n    \"canvasColors\": \"Rajzvászonon használt\",\n    \"canvasBackground\": \"Vászon háttérszíne\",\n    \"drawingCanvas\": \"Rajzvászon\",\n    \"layers\": \"Rétegek\",\n    \"actions\": \"Műveletek\",\n    \"language\": \"Nyelv\",\n    \"liveCollaboration\": \"Élő együttműködés...\",\n    \"duplicateSelection\": \"Duplikálás\",\n    \"untitled\": \"Névtelen\",\n    \"name\": \"Név\",\n    \"yourName\": \"Neved\",\n    \"madeWithExcalidraw\": \"Excalidraw-val készült\",\n    \"group\": \"Csoportosítás\",\n    \"ungroup\": \"Csoportbontás\",\n    \"collaborators\": \"Közreműködők\",\n    \"showGrid\": \"Rács megjelenítése\",\n    \"addToLibrary\": \"Hozzáadás a könyvtárhoz\",\n    \"removeFromLibrary\": \"Eltávólítás a könyvtárból\",\n    \"libraryLoadingMessage\": \"Könyvtár betöltése…\",\n    \"libraries\": \"Könyvtárak böngészése\",\n    \"loadingScene\": \"Jelenet betöltése…\",\n    \"align\": \"Igazítás\",\n    \"alignTop\": \"Felülre igazítás\",\n    \"alignBottom\": \"Alulra igazítás\",\n    \"alignLeft\": \"Balra igazítás\",\n    \"alignRight\": \"Jobbra igazítás\",\n    \"centerVertically\": \"Függőlegesen középre igazított\",\n    \"centerHorizontally\": \"Vízszintesen középre igazított\",\n    \"distributeHorizontally\": \"Vízszintes elosztás\",\n    \"distributeVertically\": \"Függőleges elosztás\",\n    \"flipHorizontal\": \"Vízszintes tükrözés\",\n    \"flipVertical\": \"Függőleges tükrözés\",\n    \"viewMode\": \"Nézet\",\n    \"share\": \"Megosztás\",\n    \"showStroke\": \"Körvonal színválasztó megjelenítése\",\n    \"showBackground\": \"Háttérszín-választó megjelenítése\",\n    \"toggleTheme\": \"Téma váltása\",\n    \"personalLib\": \"Személyes könyvtár\",\n    \"excalidrawLib\": \"Excalidraw könyvtár\",\n    \"decreaseFontSize\": \"Betűméret csökkentése\",\n    \"increaseFontSize\": \"Betűméret növelése\",\n    \"unbindText\": \"Szövegkötés feloldása\",\n    \"bindText\": \"\",\n    \"createContainerFromText\": \"Szöveg bekeretezése\",\n    \"link\": {\n      \"edit\": \"Hivatkozás szerkesztése\",\n      \"editEmbed\": \"Link szerkesztése / beágyazása\",\n      \"create\": \"Hivatkozás létrehozása\",\n      \"createEmbed\": \"Link létrehozása / beágyazása\",\n      \"label\": \"Hivatkozás\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"\",\n      \"exit\": \"\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Rögzítés\",\n      \"unlock\": \"Rögzítés feloldása\",\n      \"lockAll\": \"Összes rögzítése\",\n      \"unlockAll\": \"Összes feloldása\"\n    },\n    \"statusPublished\": \"\",\n    \"sidebarLock\": \"\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"\",\n    \"hint_emptyLibrary\": \"\",\n    \"hint_emptyPrivateLibrary\": \"\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Vászon törlése\",\n    \"exportJSON\": \"Exportálás fájlba\",\n    \"exportImage\": \"Kép exportálása...\",\n    \"export\": \"Mentés másként...\",\n    \"copyToClipboard\": \"Vágólapra másolás\",\n    \"save\": \"Mentés az aktuális fájlba\",\n    \"saveAs\": \"Mentés másként\",\n    \"load\": \"Megnyitás\",\n    \"getShareableLink\": \"Megosztható link létrehozása\",\n    \"close\": \"Bezárás\",\n    \"selectLanguage\": \"Nyelv kiválasztása\",\n    \"scrollBackToContent\": \"Visszagörgetés a tartalomhoz\",\n    \"zoomIn\": \"Nagyítás\",\n    \"zoomOut\": \"Kicsinyítés\",\n    \"resetZoom\": \"Nagyítás alaphelyzetbe\",\n    \"menu\": \"Menü\",\n    \"done\": \"Kész\",\n    \"edit\": \"Szerkesztés\",\n    \"undo\": \"Vissza\",\n    \"redo\": \"Újra\",\n    \"resetLibrary\": \"Könyvtár alaphelyzetbe állítása\",\n    \"createNewRoom\": \"Új szoba létrehozása\",\n    \"fullScreen\": \"Teljes képernyő\",\n    \"darkMode\": \"Sötét mód\",\n    \"lightMode\": \"Világos mód\",\n    \"zenMode\": \"Letisztult mód\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Kilépés a letisztult módból\",\n    \"cancel\": \"Mégsem\",\n    \"clear\": \"Kiűrítés\",\n    \"remove\": \"Eltávolítás\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Közzététel\",\n    \"submit\": \"Elküldés\",\n    \"confirm\": \"Megerősítés\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Ez a művelet törli a vászont. Biztos benne?\",\n    \"couldNotCreateShareableLink\": \"Nem sikerült megosztható linket létrehozni.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Nem sikerült megosztható linket látrehozni: túl nagy a jelenet\",\n    \"couldNotLoadInvalidFile\": \"Nem sikerült betölteni a helytelen fájlt\",\n    \"importBackendFailed\": \"Nem sikerült betölteni a szerverről.\",\n    \"cannotExportEmptyCanvas\": \"Üres vászont nem lehet exportálni.\",\n    \"couldNotCopyToClipboard\": \"\",\n    \"decryptFailed\": \"Nem sikerült visszafejteni a titkosított adatot.\",\n    \"uploadedSecurly\": \"A feltöltést végpontok közötti titkosítással biztosítottuk, ami azt jelenti, hogy egy harmadik fél nem tudja megnézni a tartalmát, beleértve az Excalidraw szervereit is.\",\n    \"loadSceneOverridePrompt\": \"A betöltött külső rajz felül fogja írnia meglévőt. Szeretnéd folytatni?\",\n    \"collabStopOverridePrompt\": \"A munkamenet leállítása felül fogja írni az előzőleg helyben tárolt rajzot. Biztosan ezt akarod?\\n(Ha meg akarod tartani a helyben tárolt rajzot, egyszerűen csak zárd be a böngésző fület)\",\n    \"errorAddingToLibrary\": \"A tétel nem addható hozzá a könyvtárhoz\",\n    \"errorRemovingFromLibrary\": \"A tétel nem távolítható el a könyvtárból\",\n    \"confirmAddLibrary\": \"Ez a művelet {{numShapes}} formát fog hozzáadni a könyvtáradhoz. Biztos vagy benne?\",\n    \"imageDoesNotContainScene\": \"Úgy tűnik, hogy ez a kép nem tartalmaz jelenetadatokat. Engedélyezted a jelenetbeágyazást az exportálás során?\",\n    \"cannotRestoreFromImage\": \"A jelenet visszaállítása nem sikerült ebből a kép fájlból\",\n    \"invalidSceneUrl\": \"Nem sikerült importálni a jelenetet a megadott URL-ről. Rossz formátumú, vagy nem tartalmaz érvényes Excalidraw JSON-adatokat.\",\n    \"resetLibrary\": \"Ezzel törlöd a könyvtárát. biztos vagy ebben?\",\n    \"removeItemsFromsLibrary\": \"{{count}} elemet törölsz a könyvtárból?\",\n    \"invalidEncryptionKey\": \"A titkosítási kulcsnak 22 karakterből kell állnia. Az élő együttműködés le van tiltva.\",\n    \"collabOfflineWarning\": \"\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Nem támogatott fájltípus.\",\n    \"imageInsertError\": \"Nem sikerült beszúrni a képet. Próbáld újra később...\",\n    \"fileTooBig\": \"A fájl túl nagy. A megengedett maximális méret {{maxSize}}.\",\n    \"svgImageInsertError\": \"Nem sikerült beszúrni az SVG-képet. Az SVG szintaktika érvénytelennek tűnik.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"Érvénytelen SVG.\",\n    \"cannotResolveCollabServer\": \"\",\n    \"importLibraryError\": \"\",\n    \"collabSaveFailed\": \"\",\n    \"collabSaveFailed_sizeExceeded\": \"\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Kijelölés\",\n    \"image\": \"Kép beszúrása\",\n    \"rectangle\": \"Téglalap\",\n    \"diamond\": \"Rombusz\",\n    \"ellipse\": \"Ellipszis\",\n    \"arrow\": \"Nyíl\",\n    \"line\": \"Vonal\",\n    \"freedraw\": \"Rajzolás\",\n    \"text\": \"Szöveg\",\n    \"library\": \"Könyvtár\",\n    \"lock\": \"Rajzolás után az aktív eszközt tartsa kijelölve\",\n    \"penMode\": \"\",\n    \"link\": \"Hivatkozás hozzáadása/frissítése a kiválasztott alakzathoz\",\n    \"eraser\": \"Radír\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"Weblap beágyazása\",\n    \"laser\": \"Lézermutató\",\n    \"hand\": \"\",\n    \"extraTools\": \"További eszközök\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Vászon műveletek\",\n    \"selectedShapeActions\": \"Kijelölt forma műveletei\",\n    \"shapes\": \"Alakzatok\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"\",\n    \"linearElement\": \"Kattintással görbe, az eger húzásával pedig egyenes nyilat rajzolhatsz\",\n    \"freeDraw\": \"Kattints és húzd, majd engedd el, amikor végeztél\",\n    \"text\": \"Tipp: A kijelölés eszközzel a dupla kattintás új szöveget hoz létre\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"Kattints duplán, vagy nyomj entert a szöveg szerkesztéséhez\",\n    \"text_editing\": \"Nyomjd meg az Escape vagy a Ctrl/Cmd+ENTER billentyűkombinációt a szerkesztés befejezéséhez\",\n    \"linearElementMulti\": \"Kattints a következő ív pozíciójára, vagy fejezd be a nyilat az Escape vagy Enter megnyomásával\",\n    \"lockAngle\": \"A SHIFT billentyű lenyomva tartásával korlátozhatja forgatás szögét\",\n    \"resize\": \"A SHIFT billentyű lenyomva tartásával az átméretezés megtartja az arányokat,\\naz ALT lenyomva tartásával pedig a középpont egy helyben marad\",\n    \"resizeImage\": \"A SHIFT billentyű lenyomva tartásával szabadon átméretezheted,\\ntartsd lenyomva az ALT billentyűt a középről való átméretezéshez\",\n    \"rotate\": \"A SHIFT billentyű lenyomva tartásával korlátozhatja a szögek illesztését\",\n    \"lineEditor_info\": \"\",\n    \"lineEditor_pointSelected\": \"Nyomd meg a Törlés gombot a pont(ok) eltávolításához,\\nA Ctrl/Cmd+D a többszörözéshez, vagy húzással mozgathatja\",\n    \"lineEditor_nothingSelected\": \"Válaszd ki a szerkeszteni kívánt pontot (több kijelöléséhez tartsd lenyomva a SHIFT billentyűt),\\nvagy Alt, és kattintson az új pontok hozzáadásához\",\n    \"placeImage\": \"Kattints a kép elhelyezéséhez, vagy kattints és méretezd manuálisan\",\n    \"publishLibrary\": \"Tedd közzé saját könyvtáradat\",\n    \"bindTextToElement\": \"Nyomd meg az Entert szöveg hozzáadáshoz\",\n    \"deepBoxSelect\": \"Tartsd lenyomva a Ctrl/Cmd billentyűt a mély kijelöléshez és a húzás megakadályozásához\",\n    \"eraserRevert\": \"\",\n    \"firefox_clipboard_write\": \"\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Előnézet nem jeleníthető meg\",\n    \"canvasTooBig\": \"A vászon talán túl nagy.\",\n    \"canvasTooBigTip\": \"Tipp: próbáld meg a legtávolabbi elemeket közelebb hozni egy máshoz.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Hiba történt. Próbáld <button>újratölteni az oldalt.</button>\",\n    \"clearCanvasMessage\": \"Ha az újratöltés nem működik, próbáld <button>letörölni a vászont.</button>\",\n    \"clearCanvasCaveat\": \" Ezzel az eddigi munka elveszik \",\n    \"trackedToSentry\": \"A hibakód azonosítóval {{eventId}} nyomon van követve a rendszerünkben.\",\n    \"openIssueMessage\": \"Vigyáztunk arra, hogy a jelenthez tartozó információ ne jelenjen meg a hibaüzenetben. Ha a jeleneted nem bizalmas, kérjük add hozzá a <button>hibakövető rendszerünkhöz.</button> Kérjük, másolja be az alábbi információkat a GitHub problémába.\",\n    \"sceneContent\": \"Jelenet tartalma:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Meghívhat embereket a jelenlegi jelenetbe, hogy együttműködjenek önnel.\",\n    \"desc_privacy\": \"Ne aggódj, a munkamenet végpontok közötti titkosítást használ, tehát bármit rajzolsz, privát marad. Még a szerverünkről se lehet belenézni.\",\n    \"button_startSession\": \"Munkamenet indítása\",\n    \"button_stopSession\": \"Munkamenet leállítása\",\n    \"desc_inProgressIntro\": \"Az élő együttműködési munkamenet folyamatban van.\",\n    \"desc_shareLink\": \"Ossza meg ezt a linket bárkivel, akivel együtt szeretne működni:\",\n    \"desc_exitSession\": \"Az munkamenet leállítása kilépteti önt a szobából, de folytathatja a munkát a saját gépén. Vegye figyelembe, hogy ez nem érinti más emberek munkáját és ők továbbra is együttműködhetnek a saját változatukon.\",\n    \"shareTitle\": \"Csatlakozás egy élő együttműködési munkamenethez az Excalidraw-ban\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Hiba\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Mentés lemezre\",\n    \"disk_details\": \"Exportálja a jelenetadatokat egy fájlba, amelyből később importálhatja.\",\n    \"disk_button\": \"Mentés fájlba\",\n    \"link_title\": \"Megosztható hivatkozás\",\n    \"link_details\": \"Exportálás csak olvasható hivatkozásként.\",\n    \"link_button\": \"Exportálás hivatkozásba\",\n    \"excalidrawplus_description\": \"Mentse el a jelenetet az Excalidraw+ munkaterületére.\",\n    \"excalidrawplus_button\": \"Exportálás\",\n    \"excalidrawplus_exportError\": \"Jelenleg nem lehet exportálni az Excalidraw+-ba...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Olvasd a blogunkat\",\n    \"click\": \"kattintás\",\n    \"deepSelect\": \"Mély kijelölés\",\n    \"deepBoxSelect\": \"Mély kijelölés a dobozon belül, és a húzás megakadályozása\",\n    \"curvedArrow\": \"Ívelt nyíl\",\n    \"curvedLine\": \"Ívelt vonal\",\n    \"documentation\": \"Dokumentáció\",\n    \"doubleClick\": \"dupla kattintás\",\n    \"drag\": \"vonszolás\",\n    \"editor\": \"Szerkesztő\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"Hibát találtál? Küld be\",\n    \"howto\": \"Kövesd az útmutatóinkat\",\n    \"or\": \"vagy\",\n    \"preventBinding\": \"A nyíl ne ragadjon\",\n    \"tools\": \"\",\n    \"shortcuts\": \"Gyorsbillentyűk\",\n    \"textFinish\": \"Szerkesztés befejezése (szöveg)\",\n    \"textNewLine\": \"Új sor hozzáadása (szöveg)\",\n    \"title\": \"Segítség\",\n    \"view\": \"Nézet\",\n    \"zoomToFit\": \"Az összes elem látótérbe hozása\",\n    \"zoomToSelection\": \"Kijelölésre nagyítás\",\n    \"toggleElementLock\": \"\",\n    \"movePageUpDown\": \"\",\n    \"movePageLeftRight\": \"\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Rajzvászon alaphelyzetbe\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Könyvtár közzététele\",\n    \"itemName\": \"Tétel neve\",\n    \"authorName\": \"Szerző neve\",\n    \"githubUsername\": \"GitHub felhasználónév\",\n    \"twitterUsername\": \"Twitter felhasználónév\",\n    \"libraryName\": \"Könyvtár neve\",\n    \"libraryDesc\": \"Könyvtár leírása\",\n    \"website\": \"Weboldal\",\n    \"placeholder\": {\n      \"authorName\": \"Neved vagy felhasználóneved\",\n      \"libraryName\": \"A könyvtárad neve\",\n      \"libraryDesc\": \"A könyvtárad használatát segítő leírás\",\n      \"githubHandle\": \"GitHub-handle(opcionális), így szerkesztheted a könyvtárat, miután elküldted ellenőrzésre\",\n      \"twitterHandle\": \"Twitter-felhasználónév (opcionális), így tudjuk, kinek kell jóváírni a Twitteren keresztüli reklámozást\",\n      \"website\": \"Hivatkozás személyes webhelyedre vagy máshová (nem kötelező)\"\n    },\n    \"errors\": {\n      \"required\": \"Kötelező\",\n      \"website\": \"Adj meg egy érvényes URL-t\"\n    },\n    \"noteDescription\": \"Küld be könyvtáradat, hogy bekerüljön a <link>nyilvános könyvtár tárolóba</link>hogy mások is felhasználhassák a rajzaikban.\",\n    \"noteGuidelines\": \"A könyvtárat először manuálisan kell jóváhagyni. Kérjük, olvassa el a <link>segédletet</link> benyújtása előtt. Szüksége lesz egy GitHub-fiókra a kommunikációhoz és a módosításokhoz, ha kérik, de ez nem feltétlenül szükséges.\",\n    \"noteLicense\": \"A beküldéssel elfogadja, hogy a könyvtár a következő alatt kerül közzétételre <link>MIT Licensz </link>ami röviden azt jelenti, hogy bárki korlátozás nélkül használhatja őket.\",\n    \"noteItems\": \"Minden könyvtárelemnek saját nevével kell rendelkeznie, hogy szűrhető legyen. A következő könyvtári tételek kerülnek bele:\",\n    \"atleastOneLibItem\": \"A kezdéshez válassz ki legalább egy könyvtári elemet\",\n    \"republishWarning\": \"\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"A könyvtár beküldve\",\n    \"content\": \"Köszönjük {{authorName}}. Könyvtáradat elküldtük felülvizsgálatra. Nyomon követheted az állapotot<link>itt</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Könyvtár alaphelyzetbe állítása\",\n    \"removeItemsFromLib\": \"A kiválasztott elemek eltávolítása a könyvtárból\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Kép exportálása\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Vágólapra másolás\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"A rajzaidat végpontok közötti titkosítással tároljuk, tehát az Excalidraw szervereiről se tud más belenézni.\",\n    \"link\": \"Blogbejegyzés a végpontok közötti titkosításról az Excalidraw-ban\"\n  },\n  \"stats\": {\n    \"angle\": \"Szög\",\n    \"element\": \"Elem\",\n    \"elements\": \"Elemek\",\n    \"height\": \"Magasság\",\n    \"scene\": \"Jelenet\",\n    \"selected\": \"Kijelölt\",\n    \"storage\": \"Tárhely\",\n    \"title\": \"Statisztikák\",\n    \"total\": \"Összesen\",\n    \"version\": \"Verzió\",\n    \"versionCopy\": \"Kattints a másoláshoz\",\n    \"versionNotAvailable\": \"A verzió nem elérhető\",\n    \"width\": \"Szélesség\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Könyvtárhoz adva\",\n    \"copyStyles\": \"Másolt stílusok.\",\n    \"copyToClipboard\": \"Vágólapra másolva.\",\n    \"copyToClipboardAsPng\": \"Az {{exportSelection}} PNG formátumban a vágólapra másolva \\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Fájl elmentve.\",\n    \"fileSavedToFilename\": \"Mentve mint {filename}\",\n    \"canvas\": \"rajzvászon\",\n    \"selection\": \"kijelölés\",\n    \"pasteAsSingleElement\": \"\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Átlátszó\",\n    \"black\": \"Fekete\",\n    \"white\": \"Fehér\",\n    \"red\": \"Piros\",\n    \"pink\": \"Rózsaszín\",\n    \"grape\": \"\",\n    \"violet\": \"Ibolya\",\n    \"gray\": \"Szürke\",\n    \"blue\": \"Kék\",\n    \"cyan\": \"Cián\",\n    \"teal\": \"Kékes-zöld\",\n    \"green\": \"Zöld\",\n    \"yellow\": \"Sárga\",\n    \"orange\": \"Narancssárga\",\n    \"bronze\": \"Bronz\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"\",\n      \"center_heading_plus\": \"\",\n      \"menuHint\": \"\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"\",\n      \"center_heading\": \"\",\n      \"toolbarHint\": \"\",\n      \"helpHint\": \"\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"\",\n    \"shades\": \"\",\n    \"hexCode\": \"Hexadecimális kód\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Exportálás képként\",\n        \"button\": \"Exportálás képként\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Mentés a lemezre\",\n        \"button\": \"Mentés a lemezre\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Betöltés fájlból\",\n        \"button\": \"Betöltés fájlból\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Feltöltás linkből\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}