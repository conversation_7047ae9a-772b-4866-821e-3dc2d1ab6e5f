import "../chunk-XDFCUUT6.js";

// locales/ca-ES.json
var labels = {
  paste: "Enganxa",
  pasteAsPlaintext: "Enganxar com a text pla",
  pasteCharts: "Enganxa els diagrames",
  selectAll: "Selecciona-ho tot",
  multiSelect: "Afegeix un element a la selecci\xF3",
  moveCanvas: "Mou el llen\xE7",
  cut: "Retalla",
  copy: "Copia",
  copyAsPng: "Copia al porta-retalls com a PNG",
  copyAsSvg: "Copia al porta-retalls com a SVG",
  copyText: "Copia al porta-retalls com a text",
  copySource: "Copia l'origen al porta-retalls",
  convertToCode: "",
  bringForward: "Porta endavant",
  sendToBack: "Envia enrere",
  bringToFront: "Porta al davant",
  sendBackward: "Envia al fons",
  delete: "Elimina",
  copyStyles: "Copia els estils",
  pasteStyles: "Enganxa els estils",
  stroke: "Color del tra\xE7",
  background: "Color del fons",
  fill: "Estil del fons",
  strokeWidth: "Amplada del tra\xE7",
  strokeStyle: "Estil del tra\xE7",
  strokeStyle_solid: "S\xF2lid",
  strokeStyle_dashed: "Guions",
  strokeStyle_dotted: "Punts",
  sloppiness: "Estil del tra\xE7",
  opacity: "Opacitat",
  textAlign: "Alineaci\xF3 del text",
  edges: "Vores",
  sharp: "Agut",
  round: "Arrodonit",
  arrowheads: "Puntes de fletxa",
  arrowhead_none: "Cap",
  arrowhead_arrow: "Fletxa",
  arrowhead_bar: "Barra",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "Triangle",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "Mida de lletra",
  fontFamily: "Tipus de lletra",
  addWatermark: "Afegeix-hi \xABFet amb Excalidraw\xBB",
  handDrawn: "Dibuixat a m\xE0",
  normal: "Normal",
  code: "Codi",
  small: "Petit",
  medium: "Mitj\xE0",
  large: "Gran",
  veryLarge: "Molt gran",
  solid: "S\xF2lid",
  hachure: "Ratlletes",
  zigzag: "",
  crossHatch: "Ratlletes creuades",
  thin: "Fi",
  bold: "Negreta",
  left: "Esquerra",
  center: "Centre",
  right: "Dreta",
  extraBold: "Extra negreta",
  architect: "Arquitecte",
  artist: "Artista",
  cartoonist: "Dibuixant",
  fileTitle: "Nom del fitxer",
  colorPicker: "Selector de colors",
  canvasColors: "Usat al llen\xE7",
  canvasBackground: "Fons del llen\xE7",
  drawingCanvas: "Llen\xE7 de dibuix",
  layers: "Capes",
  actions: "Accions",
  language: "Llengua",
  liveCollaboration: "Col\xB7laboraci\xF3 en directe...",
  duplicateSelection: "Duplica",
  untitled: "Sense t\xEDtol",
  name: "Nom",
  yourName: "El vostre nom",
  madeWithExcalidraw: "Fet amb Excalidraw",
  group: "Agrupa la selecci\xF3",
  ungroup: "Desagrupa la selecci\xF3",
  collaborators: "Col\xB7laboradors",
  showGrid: "Mostra la graella",
  addToLibrary: "Afegir a la biblioteca",
  removeFromLibrary: "Eliminar de la biblioteca",
  libraryLoadingMessage: "S'est\xE0 carregant la biblioteca\u2026",
  libraries: "Explora les biblioteques",
  loadingScene: "S'est\xE0 carregant l'escena\u2026",
  align: "Alinea",
  alignTop: "Alinea a la part superior",
  alignBottom: "Alinea a la part inferior",
  alignLeft: "Alinea a l\u2019esquerra",
  alignRight: "Alinea a la dreta",
  centerVertically: "Centra verticalment",
  centerHorizontally: "Centra horitzontalment",
  distributeHorizontally: "Distribueix horitzontalment",
  distributeVertically: "Distribueix verticalment",
  flipHorizontal: "Capgira horitzontalment",
  flipVertical: "Capgira verticalment",
  viewMode: "Mode de visualitzaci\xF3",
  share: "Comparteix",
  showStroke: "Mostra el selector de color del tra\xE7",
  showBackground: "Mostra el selector de color de fons",
  toggleTheme: "Activa o desactiva el tema",
  personalLib: "Biblioteca personal",
  excalidrawLib: "Biblioteca d'Excalidraw",
  decreaseFontSize: "Redueix la mida de la lletra",
  increaseFontSize: "Augmenta la mida de la lletra",
  unbindText: "Desvincular el text",
  bindText: "Ajusta el text al contenidor",
  createContainerFromText: "",
  link: {
    edit: "Edita l'enlla\xE7",
    editEmbed: "Edita l'enlla\xE7 i incrusta-ho",
    create: "Crea un enlla\xE7",
    createEmbed: "",
    label: "Enlla\xE7",
    labelEmbed: "",
    empty: "No s'ha definit cap enlla\xE7"
  },
  lineEditor: {
    edit: "Editar l\xEDnia",
    exit: "Sortir de l'editor de l\xEDnia"
  },
  elementLock: {
    lock: "Bloca",
    unlock: "Desbloca",
    lockAll: "Bloca-ho tot",
    unlockAll: "Desbloca-ho tot"
  },
  statusPublished: "Publicat",
  sidebarLock: "Mant\xE9 la barra lateral oberta",
  selectAllElementsInFrame: "Selecciona tots els elements del marc",
  removeAllElementsFromFrame: "Eliminat tots els elements del marc",
  eyeDropper: "",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "Encara no s'hi han afegit elements...",
  hint_emptyLibrary: "Trieu un element o un llen\xE7 per a afegir-lo aqu\xED, o instal\xB7leu una biblioteca del repositori p\xFAblic, m\xE9s avall.",
  hint_emptyPrivateLibrary: "Trieu un element o un llen\xE7 per a afegir-lo aqu\xED."
};
var buttons = {
  clearReset: "Neteja el llen\xE7",
  exportJSON: "Exporta a un fitxer",
  exportImage: "Exporta la imatge...",
  export: "Guardar a...",
  copyToClipboard: "Copia al porta-retalls",
  save: "Desa al fitxer actual",
  saveAs: "Anomena i desa",
  load: "Obrir",
  getShareableLink: "Obt\xE9 l'enlla\xE7 per a compartir",
  close: "Tanca",
  selectLanguage: "Trieu la llengua",
  scrollBackToContent: "Torna al contingut",
  zoomIn: "Apropa't",
  zoomOut: "Allunya't",
  resetZoom: "Restableix el zoom",
  menu: "Men\xFA",
  done: "Fet",
  edit: "Edita",
  undo: "Desf\xE9s",
  redo: "Ref\xE9s",
  resetLibrary: "Restableix la biblioteca",
  createNewRoom: "Crea una sala nova",
  fullScreen: "Pantalla completa",
  darkMode: "Mode fosc",
  lightMode: "Mode clar",
  zenMode: "Mode zen",
  objectsSnapMode: "",
  exitZenMode: "Surt de mode zen",
  cancel: "Cancel\xB7la",
  clear: "Neteja",
  remove: "Suprimeix",
  embed: "",
  publishLibrary: "Publica",
  submit: "Envia",
  confirm: "Confirma",
  embeddableInteractionButton: "Feu clic per interactuar"
};
var alerts = {
  clearReset: "S'esborrar\xE0 tot el llen\xE7. N'esteu segur?",
  couldNotCreateShareableLink: "No s'ha pogut crear un enlla\xE7 per a compartir.",
  couldNotCreateShareableLinkTooBig: "No s\u2019ha pogut crear un enlla\xE7 per a compartir: l\u2019escena \xE9s massa gran",
  couldNotLoadInvalidFile: "No s'ha pogut carregar un fitxer no v\xE0lid",
  importBackendFailed: "Importaci\xF3 fallida.",
  cannotExportEmptyCanvas: "No es pot exportar un llen\xE7 buit.",
  couldNotCopyToClipboard: "No s'ha pogut copiar al porta-retalls.",
  decryptFailed: "No s'ha pogut desencriptar.",
  uploadedSecurly: "La c\xE0rrega s'ha assegurat amb xifratge punta a punta, cosa que significa que el servidor Excalidraw i tercers no poden llegir el contingut.",
  loadSceneOverridePrompt: "Si carregas aquest dibuix extern, substituir\xE1 el que tens. Vols continuar?",
  collabStopOverridePrompt: "Aturar la sessi\xF3 provocar\xE0 la sobreescriptura del dibuix previ, que hi ha desat en l'emmagatzematge local. N'esteu segur?\n\n(Si voleu conservar el dibuix local, tanqueu la pentanya del navegador en comptes d'aturar la sessi\xF3).",
  errorAddingToLibrary: "No s'ha pogut afegir l'element a la biblioteca",
  errorRemovingFromLibrary: "No s'ha pogut eliminar l'element de la biblioteca",
  confirmAddLibrary: "Aix\xF2 afegir\xE0 {{numShapes}} forma(es) a la vostra biblioteca. Est\xE0s segur?",
  imageDoesNotContainScene: "Aquesta imatge no sembla contenir cap dada d'escena. Heu activat l'incrustaci\xF3 de l'escena durant l'exportaci\xF3?",
  cannotRestoreFromImage: "L\u2019escena no s\u2019ha pogut restaurar des d\u2019aquest fitxer d\u2019imatge",
  invalidSceneUrl: "No s'ha pogut importar l'escena des de l'adre\xE7a URL proporcionada. Est\xE0 malformada o no cont\xE9 dades Excalidraw JSON v\xE0lides.",
  resetLibrary: "Aix\xF2 buidar\xE0 la biblioteca. N'esteu segur?",
  removeItemsFromsLibrary: "Suprimir {{count}} element(s) de la biblioteca?",
  invalidEncryptionKey: "La clau d'encriptaci\xF3 ha de tenir 22 car\xE0cters. La col\xB7laboraci\xF3 en directe est\xE0 desactivada.",
  collabOfflineWarning: "Sense connexi\xF3 a internet disponible.\nEls vostres canvis no seran guardats!"
};
var errors = {
  unsupportedFileType: "Tipus de fitxer no suportat.",
  imageInsertError: "No s'ha pogut insertar la imatge, torneu-ho a provar m\xE9s tard...",
  fileTooBig: "El fitxer \xE9s massa gros. La mida m\xE0xima permesa \xE9s {{maxSize}}.",
  svgImageInsertError: "No ha estat possible inserir la imatge SVG. Les marques SVG semblen inv\xE0lides.",
  failedToFetchImage: "",
  invalidSVGString: "SVG no v\xE0lid.",
  cannotResolveCollabServer: "No ha estat possible connectar amb el servidor collab. Si us plau recarregueu la p\xE0gina i torneu a provar.",
  importLibraryError: "No s'ha pogut carregar la biblioteca",
  collabSaveFailed: "No s'ha pogut desar a la base de dades de fons. Si els problemes persisteixen, haur\xEDeu de desar el fitxer localment per assegurar-vos que no perdeu el vostre treball.",
  collabSaveFailed_sizeExceeded: "No s'ha pogut desar a la base de dades de fons, sembla que el llen\xE7 \xE9s massa gran. Haur\xEDeu de desar el fitxer localment per assegurar-vos que no perdeu el vostre treball.",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "",
    line2: "",
    line3: "",
    line4: ""
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "",
    image: ""
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "No s'ha pogut enganxar.",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "Selecci\xF3",
  image: "Insereix imatge",
  rectangle: "Rectangle",
  diamond: "Rombe",
  ellipse: "El\xB7lipse",
  arrow: "Fletxa",
  line: "L\xEDnia",
  freedraw: "Dibuix",
  text: "Text",
  library: "Biblioteca",
  lock: "Mantenir activa l'eina seleccionada despr\xE8s de dibuixar",
  penMode: "Mode de llapis - evita tocar",
  link: "Afegeix / actualitza l'enlla\xE7 per a la forma seleccionada",
  eraser: "Esborrador",
  frame: "",
  magicframe: "",
  embeddable: "",
  laser: "",
  hand: "M\xE0 (eina de despla\xE7ament)",
  extraTools: "",
  mermaidToExcalidraw: "De Mermaid a Excalidraw",
  magicSettings: "Prefer\xE8ncies d'IA"
};
var headings = {
  canvasActions: "Accions del llen\xE7",
  selectedShapeActions: "Accions per a les formes seleccionades",
  shapes: "Formes"
};
var hints = {
  canvasPanning: "Per moure el llen\xE7, manteniu premuda la roda del ratol\xED o la barra espaiadora mentre arrossegueu o utilitzeu l'eina manual",
  linearElement: "Feu clic per a dibuixar m\xFAltiples punts; arrossegueu per a una sola l\xEDnia",
  freeDraw: "Feu clic i arrossegueu, deixeu anar per a finalitzar",
  text: "Consell: tamb\xE9 podeu afegir text fent doble clic en qualsevol lloc amb l'eina de selecci\xF3",
  embeddable: "",
  text_selected: "Feu doble clic o premeu Retorn per a editar el text",
  text_editing: "Premeu Escapada o Ctrl+Retorn (o Ordre+Retorn) per a finalitzar l'edici\xF3",
  linearElementMulti: "Feu clic a l'ultim punt, o pitgeu Esc o Retorn per a finalitzar",
  lockAngle: "Per restringir els angles, mantenir premut el maj\xFAscul (SHIFT)",
  resize: "Per restringir les proporcions mentres es canvia la mida, mantenir premut el maj\xFAscul (SHIFT); per canviar la mida des del centre, mantenir premut ALT",
  resizeImage: "Podeu redimensionar lliurement prement MAJ\xDASCULA;\nper a redimensionar des del centre, premeu ALT",
  rotate: "Per restringir els angles mentre gira, mantenir premut el maj\xFAscul (SHIFT)",
  lineEditor_info: "Mantingueu premut Ctrl o Cmd i feu doble clic o premeu Ctrl o Cmd + Retorn per editar els punts",
  lineEditor_pointSelected: "Premeu Suprimir per a eliminar el(s) punt(s), CtrlOrCmd+D per a duplicar-lo, o arrossegueu-lo per a moure'l",
  lineEditor_nothingSelected: "Seleccioneu un punt per a editar-lo (premeu SHIFT si voleu\nselecci\xF3 m\xFAltiple), o manteniu Alt i feu clic per a afegir m\xE9s punts",
  placeImage: "Feu clic per a col\xB7locar la imatge o clic i arrossegar per a establir-ne la mida manualment",
  publishLibrary: "Publiqueu la vostra pr\xF2pia llibreria",
  bindTextToElement: "Premeu enter per a afegir-hi text",
  deepBoxSelect: "Manteniu CtrlOrCmd per a selecci\xF3 profunda, i per a evitar l'arrossegament",
  eraserRevert: "Mantingueu premuda Alt per a revertir els elements seleccionats per a esborrar",
  firefox_clipboard_write: '\xC9s probable que aquesta funci\xF3 es pugui activar posant la marca "dom.events.asyncClipboard.clipboardItem" a "true". Per canviar les marques del navegador al Firefox, visiteu la p\xE0gina "about:config".',
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "No es pot mostrar la previsualitzaci\xF3",
  canvasTooBig: "Pot ser que el llen\xE7 sigui massa gran.",
  canvasTooBigTip: "Consell: proveu d\u2019acostar una mica els elements m\xE9s allunyats."
};
var errorSplash = {
  headingMain: "S'ha produ\xEFt un error. Proveu <button>recarregar la p\xE0gina.</button>",
  clearCanvasMessage: "Si la rec\xE0rrega no funciona, proveu <button>esborrar el llen\xE7.</button>",
  clearCanvasCaveat: " Aix\xF2 resultar\xE0 en la p\xE8rdua de feina ",
  trackedToSentry: "L'error amb l'identificador {{eventId}} s'ha rastrejat en el nostre sistema.",
  openIssueMessage: "An\xE0vem amb molta cura de no incloure la informaci\xF3 de la vostra escena en l'error. Si l'escena no \xE9s privada, podeu fer-ne el seguiment al nostre <button>rastrejador d'errors.</button> Incloeu la informaci\xF3 a continuaci\xF3 copiant i enganxant a GitHub Issues.",
  sceneContent: "Contingut de l'escena:"
};
var roomDialog = {
  desc_intro: "Podeu convidar persones a la vostra escena actual a col\xB7laborar amb v\xF3s.",
  desc_privacy: "No us preocupeu, la sessi\xF3 utilitza el xifratge de punta a punta, de manera que qualsevol cosa que dibuixeu romandr\xE0 privada. Ni tan sols el nostre servidor podr\xE0 veure qu\xE8 feu.",
  button_startSession: "Inicia la sessi\xF3",
  button_stopSession: "Atura la sessi\xF3",
  desc_inProgressIntro: "La sessi\xF3 de col\xB7laboraci\xF3 en directe est\xE0 en marxa.",
  desc_shareLink: "Comparteix aquest enlla\xE7 amb qualsevol persona amb qui vulgueu col\xB7laborar:",
  desc_exitSession: "Si atureu la sessi\xF3, us desconectareu de la sala, per\xF2 podreu continuar treballant amb el dibuix localment. Tingues en compte que aix\xF2 no afectar\xE0 a altres persones, i encara podran col\xB7laborar en la seva versi\xF3.",
  shareTitle: "Uniu-vos a una sessi\xF3 de col\xB7laboraci\xF3 en directe a Excalidraw"
};
var errorDialog = {
  title: "Error"
};
var exportDialog = {
  disk_title: "Desa al disc",
  disk_details: "Exporta les dades de l'escena a un fitxer que despr\xE9s podreu importar.",
  disk_button: "Desa en un fitxer",
  link_title: "Enlla\xE7 per a compartir",
  link_details: "Exporta com a un enlla\xE7 de nom\xE9s lectura.",
  link_button: "Exporta a un enlla\xE7",
  excalidrawplus_description: "Desa l'escena en el vostre espai de treball Excalidraw+.",
  excalidrawplus_button: "Exporta",
  excalidrawplus_exportError: "No \xE9s possible exportar a Excalidraw+ ara mateix..."
};
var helpDialog = {
  blog: "Llegiu el nostre blog",
  click: "clic",
  deepSelect: "Selecci\xF3 profunda",
  deepBoxSelect: "Seleccioneu profundament dins del quadre i eviteu arrossegar",
  curvedArrow: "Fletxa corba",
  curvedLine: "L\xEDnia corba",
  documentation: "Documentaci\xF3",
  doubleClick: "doble clic",
  drag: "arrossega",
  editor: "Editor",
  editLineArrowPoints: "",
  editText: "",
  github: "Hi heu trobat un problema? Informeu-ne",
  howto: "Seguiu les nostres guies",
  or: "o",
  preventBinding: "Prevenir vinculaci\xF3 de la fletxa",
  tools: "Eines",
  shortcuts: "Dreceres de teclat",
  textFinish: "Finalitza l'edici\xF3 (editor de text)",
  textNewLine: "Afegeix una l\xEDnia nova (editor de text)",
  title: "Ajuda",
  view: "Visualitzaci\xF3",
  zoomToFit: "Zoom per veure tots els elements",
  zoomToSelection: "Zoom per veure la selecci\xF3",
  toggleElementLock: "Blocar/desblocar la selecci\xF3",
  movePageUpDown: "Mou la p\xE0gina cap amunt/a baix",
  movePageLeftRight: "Mou la p\xE0gina cap a l'esquerra/dreta"
};
var clearCanvasDialog = {
  title: "Neteja el llen\xE7"
};
var publishDialog = {
  title: "Publica la biblioteca",
  itemName: "Nom de l'element",
  authorName: "Nom de l'autor/a",
  githubUsername: "Nom d'usuari de GitHub",
  twitterUsername: "Nom d'usuari de Twitter",
  libraryName: "Nom de la biblioteca",
  libraryDesc: "Descripci\xF3 de la biblioteca",
  website: "Lloc web",
  placeholder: {
    authorName: "Nom o usuari",
    libraryName: "Nom de la vostra biblioteca",
    libraryDesc: "Descripci\xF3 de la biblioteca per a ajudar a la gent a entendre'n el funcionament",
    githubHandle: "Identificador de GitHub (opcional), per tal que pugueu editar la biblioteca una vegada enviada per a ser revisada",
    twitterHandle: "Usuari de twitter (opcional), per tal que puguem donar-vos cr\xE8dit quan fem la promoci\xF3 a Twitter",
    website: "Enlla\xE7 al vostre lloc web personal o a qualsevol altre (opcional)"
  },
  errors: {
    required: "Requerit",
    website: "Introdu\xEFu una URL v\xE0lida"
  },
  noteDescription: "Envieu la vostra biblioteca perqu\xE8 sigui inclosa al <link>repositori p\xFAblic</link>per tal que altres persones puguin fer-ne \xFAs en els seus dibuixos.",
  noteGuidelines: "La biblioteca ha de ser aprovada manualment. Si us plau, llegiu les <link>directrius</link> abans d'enviar-hi res. Necessitareu un compte de GitHub per a comunicar i fer-hi canvis si cal, per\xF2 no \xE9s requisit imprescindible.",
  noteLicense: "Quan l'envieu, accepteu que la biblioteca sigui publicada sota la <link>llic\xE8ncia MIT, </link>que, en resum, vol dir que qualsevol persona pot fer-ne \xFAs sense restriccions.",
  noteItems: "Cada element de la biblioteca ha de tenir el seu propi nom per tal que sigui filtrable. S'hi inclouran els elements seg\xFCents:",
  atleastOneLibItem: "Si us plau, seleccioneu si m\xE9s no un element de la biblioteca per a comen\xE7ar",
  republishWarning: "Nota: alguns dels elements seleccionats s'han marcat com a publicats/enviats. Nom\xE9s haur\xEDeu de reenviar elements quan actualitzeu una biblioteca existent."
};
var publishSuccessDialog = {
  title: "Biblioteca enviada",
  content: "Gr\xE0cies, {{authorName}}. La vostra biblioteca ha estat enviada per a ser revisada. Podeu comprovar-ne l'estat<link>aqu\xED</link>"
};
var confirmDialog = {
  resetLibrary: "Restableix la biblioteca",
  removeItemsFromLib: "Suprimeix els elements seleccionats de la llibreria"
};
var imageExportDialog = {
  header: "",
  label: {
    withBackground: "",
    onlySelected: "Nom\xE9s els seleccionats",
    darkMode: "Mode fosc",
    embedScene: "",
    scale: "",
    padding: ""
  },
  tooltip: {
    embedScene: ""
  },
  title: {
    exportToPng: "Exporta a PNG",
    exportToSvg: "Exporta a SVG",
    copyPngToClipboard: "Copia el PNG al porta-retalls"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "",
    copyPngToClipboard: ""
  }
};
var encrypted = {
  tooltip: "Els vostres dibuixos estan xifrats de punta a punta de manera que els servidors d\u2019Excalidraw no els veuran mai.",
  link: "Article del blog sobre encriptaci\xF3 d'extrem a extrem a Excalidraw"
};
var stats = {
  angle: "Angle",
  element: "Element",
  elements: "Elements",
  height: "Altura",
  scene: "Escena",
  selected: "Seleccionat",
  storage: "Emmagatzematge",
  title: "Estad\xEDstiques per nerds",
  total: "Total",
  version: "Versi\xF3",
  versionCopy: "Feu clic per a copiar",
  versionNotAvailable: "Versi\xF3 no disponible",
  width: "Amplada"
};
var toast = {
  addedToLibrary: "Afegit a la biblioteca",
  copyStyles: "S'han copiat els estils.",
  copyToClipboard: "S'ha copiat al porta-retalls.",
  copyToClipboardAsPng: "S'ha copiat {{exportSelection}} al porta-retalls en format PNG\n({{exportColorScheme}})",
  fileSaved: "S'ha desat el fitxer.",
  fileSavedToFilename: "S'ha desat a {filename}",
  canvas: "el llen\xE7",
  selection: "la selecci\xF3",
  pasteAsSingleElement: "Fer servir {{shortcut}} per enganxar com un sol element,\no enganxeu-lo en un editor de text existent",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "Transparent",
  black: "",
  white: "",
  red: "",
  pink: "",
  grape: "",
  violet: "",
  gray: "",
  blue: "",
  cyan: "",
  teal: "",
  green: "Verd",
  yellow: "Groc",
  orange: "Taronja",
  bronze: "Bronze"
};
var welcomeScreen = {
  app: {
    center_heading: "Totes les vostres dades es guarden localment al vostre navegador.",
    center_heading_plus: "Vols anar a Excalidraw+ en comptes?",
    menuHint: "Exportar, prefer\xE8ncies, llenguatges..."
  },
  defaults: {
    menuHint: "Exportar, prefer\xE8ncies i m\xE9s...",
    center_heading: "Diagrames. Fer. Simple.",
    toolbarHint: "Selecciona una eina i comen\xE7a a dibuixar!",
    helpHint: "Dreceres i ajuda"
  }
};
var colorPicker = {
  mostUsedCustomColors: "",
  colors: "",
  shades: "",
  hexCode: "",
  noShades: ""
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "Exporta com a imatge",
      button: "Exporta com a imatge",
      description: ""
    },
    saveToDisk: {
      title: "Desa al disc",
      button: "Desa al disc",
      description: ""
    },
    excalidrawPlus: {
      title: "",
      button: "",
      description: ""
    }
  },
  modal: {
    loadFromFile: {
      title: "Carrega des d'un fitxer",
      button: "Carrega des d'un fitxer",
      description: ""
    },
    shareableLink: {
      title: "Carrega des d'un enlla\xE7",
      button: "",
      description: ""
    }
  }
};
var mermaid = {
  title: "De Mermaid a Excalidraw",
  button: "Insereix",
  description: "",
  syntax: "Sintaxi de Mermaid",
  preview: "Previsualitzaci\xF3"
};
var ca_ES_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  ca_ES_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=ca-ES-YNNMFRQX.js.map
