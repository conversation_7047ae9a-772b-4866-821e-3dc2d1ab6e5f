{"version": 3, "sources": ["../../../locales/zh-HK.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"貼上\",\n    \"pasteAsPlaintext\": \"\",\n    \"pasteCharts\": \"貼上圖表\",\n    \"selectAll\": \"全選\",\n    \"multiSelect\": \"多重選取\",\n    \"moveCanvas\": \"移動畫布\",\n    \"cut\": \"剪下\",\n    \"copy\": \"複製\",\n    \"copyAsPng\": \"以 PNG 格式複製\",\n    \"copyAsSvg\": \"以 SVG 格式複製\",\n    \"copyText\": \"\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"往上一層移動\",\n    \"sendToBack\": \"移到最底層\",\n    \"bringToFront\": \"移到最上層\",\n    \"sendBackward\": \"往下一層移動\",\n    \"delete\": \"刪除\",\n    \"copyStyles\": \"複製樣式\",\n    \"pasteStyles\": \"套用樣式\",\n    \"stroke\": \"筆跡顏色\",\n    \"background\": \"填充顏色\",\n    \"fill\": \"背景樣式\",\n    \"strokeWidth\": \"筆跡寬度\",\n    \"strokeStyle\": \"筆跡線條\",\n    \"strokeStyle_solid\": \"實線\",\n    \"strokeStyle_dashed\": \"虛線（較密）\",\n    \"strokeStyle_dotted\": \"虛線（較疏）\",\n    \"sloppiness\": \"筆跡風格\",\n    \"opacity\": \"透明度\",\n    \"textAlign\": \"文字對齊\",\n    \"edges\": \"邊角樣式\",\n    \"sharp\": \"銳角\",\n    \"round\": \"圓角\",\n    \"arrowheads\": \"箭嘴\",\n    \"arrowhead_none\": \"無箭嘴\",\n    \"arrowhead_arrow\": \"普通箭嘴\",\n    \"arrowhead_bar\": \"平頭條狀\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"三角箭嘴\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"字型大小\",\n    \"fontFamily\": \"字體\",\n    \"addWatermark\": \"加入「使用 Excalidraw 製圖」水印\",\n    \"handDrawn\": \"手繪體\",\n    \"normal\": \"電腦字體\",\n    \"code\": \"等寬體\",\n    \"small\": \"細\",\n    \"medium\": \"中\",\n    \"large\": \"大\",\n    \"veryLarge\": \"勁大\",\n    \"solid\": \"實心\",\n    \"hachure\": \"斜線\",\n    \"zigzag\": \"\",\n    \"crossHatch\": \"交叉格仔\",\n    \"thin\": \"幼\",\n    \"bold\": \"粗\",\n    \"left\": \"靠左對齊\",\n    \"center\": \"置中對齊\",\n    \"right\": \"靠右對齊\",\n    \"extraBold\": \"勁粗\",\n    \"architect\": \"手繪風格\",\n    \"artist\": \"藝術家風格\",\n    \"cartoonist\": \"卡通風格\",\n    \"fileTitle\": \"檔案名稱\",\n    \"colorPicker\": \"\",\n    \"canvasColors\": \"\",\n    \"canvasBackground\": \"畫布背景顏色\",\n    \"drawingCanvas\": \"畫布\",\n    \"layers\": \"圖層\",\n    \"actions\": \"動作\",\n    \"language\": \"🌏 語言\",\n    \"liveCollaboration\": \"\",\n    \"duplicateSelection\": \"製作副本\",\n    \"untitled\": \"未命名的作品\",\n    \"name\": \"\",\n    \"yourName\": \"你的名稱\",\n    \"madeWithExcalidraw\": \"使用 Excalidraw 製圖\",\n    \"group\": \"建立物件群組\",\n    \"ungroup\": \"取消物件群組\",\n    \"collaborators\": \"已連線的協作者\",\n    \"showGrid\": \"顯示網格\",\n    \"addToLibrary\": \"加入作品庫\",\n    \"removeFromLibrary\": \"從作品庫中移除\",\n    \"libraryLoadingMessage\": \"正在載入作品庫…\",\n    \"libraries\": \"瀏覽作品庫\",\n    \"loadingScene\": \"載入畫布中…\",\n    \"align\": \"物件對齊\",\n    \"alignTop\": \"水平置頂\",\n    \"alignBottom\": \"水平置底\",\n    \"alignLeft\": \"垂直靠左對齊\",\n    \"alignRight\": \"垂直靠右對齊\",\n    \"centerVertically\": \"垂直置中\",\n    \"centerHorizontally\": \"水平置中\",\n    \"distributeHorizontally\": \"左右等距\",\n    \"distributeVertically\": \"上下等距\",\n    \"flipHorizontal\": \"左右反轉\",\n    \"flipVertical\": \"上下反轉\",\n    \"viewMode\": \"唯讀模式\",\n    \"share\": \"\",\n    \"showStroke\": \"\",\n    \"showBackground\": \"\",\n    \"toggleTheme\": \"\",\n    \"personalLib\": \"\",\n    \"excalidrawLib\": \"\",\n    \"decreaseFontSize\": \"\",\n    \"increaseFontSize\": \"\",\n    \"unbindText\": \"\",\n    \"bindText\": \"\",\n    \"createContainerFromText\": \"\",\n    \"link\": {\n      \"edit\": \"\",\n      \"editEmbed\": \"\",\n      \"create\": \"\",\n      \"createEmbed\": \"\",\n      \"label\": \"\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"\",\n      \"exit\": \"\"\n    },\n    \"elementLock\": {\n      \"lock\": \"\",\n      \"unlock\": \"\",\n      \"lockAll\": \"\",\n      \"unlockAll\": \"\"\n    },\n    \"statusPublished\": \"\",\n    \"sidebarLock\": \"\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"\",\n    \"hint_emptyLibrary\": \"\",\n    \"hint_emptyPrivateLibrary\": \"\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"清空畫布\",\n    \"exportJSON\": \"\",\n    \"exportImage\": \"\",\n    \"export\": \"\",\n    \"copyToClipboard\": \"\",\n    \"save\": \"\",\n    \"saveAs\": \"\",\n    \"load\": \"\",\n    \"getShareableLink\": \"\",\n    \"close\": \"\",\n    \"selectLanguage\": \"\",\n    \"scrollBackToContent\": \"\",\n    \"zoomIn\": \"\",\n    \"zoomOut\": \"\",\n    \"resetZoom\": \"\",\n    \"menu\": \"\",\n    \"done\": \"\",\n    \"edit\": \"\",\n    \"undo\": \"\",\n    \"redo\": \"\",\n    \"resetLibrary\": \"\",\n    \"createNewRoom\": \"\",\n    \"fullScreen\": \"\",\n    \"darkMode\": \"\",\n    \"lightMode\": \"\",\n    \"zenMode\": \"\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"\",\n    \"cancel\": \"\",\n    \"clear\": \"\",\n    \"remove\": \"\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"\",\n    \"submit\": \"\",\n    \"confirm\": \"\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"【‼️ 警告 ‼️ 無法復原的動作】你確定要清空呢塊畫布嗎？\",\n    \"couldNotCreateShareableLink\": \"\",\n    \"couldNotCreateShareableLinkTooBig\": \"\",\n    \"couldNotLoadInvalidFile\": \"\",\n    \"importBackendFailed\": \"\",\n    \"cannotExportEmptyCanvas\": \"無嘢可以匯出喎～畫吓嘢先？\",\n    \"couldNotCopyToClipboard\": \"\",\n    \"decryptFailed\": \"\",\n    \"uploadedSecurly\": \"\",\n    \"loadSceneOverridePrompt\": \"\",\n    \"collabStopOverridePrompt\": \"\",\n    \"errorAddingToLibrary\": \"\",\n    \"errorRemovingFromLibrary\": \"\",\n    \"confirmAddLibrary\": \"\",\n    \"imageDoesNotContainScene\": \"\",\n    \"cannotRestoreFromImage\": \"\",\n    \"invalidSceneUrl\": \"\",\n    \"resetLibrary\": \"\",\n    \"removeItemsFromsLibrary\": \"\",\n    \"invalidEncryptionKey\": \"\",\n    \"collabOfflineWarning\": \"\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"\",\n    \"imageInsertError\": \"\",\n    \"fileTooBig\": \"\",\n    \"svgImageInsertError\": \"\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"\",\n    \"cannotResolveCollabServer\": \"\",\n    \"importLibraryError\": \"\",\n    \"collabSaveFailed\": \"\",\n    \"collabSaveFailed_sizeExceeded\": \"\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"\",\n    \"image\": \"\",\n    \"rectangle\": \"\",\n    \"diamond\": \"\",\n    \"ellipse\": \"\",\n    \"arrow\": \"\",\n    \"line\": \"\",\n    \"freedraw\": \"\",\n    \"text\": \"\",\n    \"library\": \"\",\n    \"lock\": \"\",\n    \"penMode\": \"\",\n    \"link\": \"\",\n    \"eraser\": \"\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"畫布動作\",\n    \"selectedShapeActions\": \"\",\n    \"shapes\": \"\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"\",\n    \"linearElement\": \"\",\n    \"freeDraw\": \"\",\n    \"text\": \"\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"\",\n    \"text_editing\": \"\",\n    \"linearElementMulti\": \"\",\n    \"lockAngle\": \"\",\n    \"resize\": \"\",\n    \"resizeImage\": \"\",\n    \"rotate\": \"\",\n    \"lineEditor_info\": \"\",\n    \"lineEditor_pointSelected\": \"\",\n    \"lineEditor_nothingSelected\": \"\",\n    \"placeImage\": \"\",\n    \"publishLibrary\": \"\",\n    \"bindTextToElement\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"eraserRevert\": \"\",\n    \"firefox_clipboard_write\": \"\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"無法顯示預覽\",\n    \"canvasTooBig\": \"塊畫布太大啦，縮細啲先啦？\",\n    \"canvasTooBigTip\": \"\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"\",\n    \"clearCanvasMessage\": \"如果重新整理頁面都係睇唔到，你可以<button>清空畫布</button>\",\n    \"clearCanvasCaveat\": \"（注意：呢個動作會直接丟棄你嘅作品，並且無法復原）\",\n    \"trackedToSentry\": \"\",\n    \"openIssueMessage\": \"\",\n    \"sceneContent\": \"\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"\",\n    \"desc_privacy\": \"\",\n    \"button_startSession\": \"\",\n    \"button_stopSession\": \"\",\n    \"desc_inProgressIntro\": \"\",\n    \"desc_shareLink\": \"\",\n    \"desc_exitSession\": \"\",\n    \"shareTitle\": \"\"\n  },\n  \"errorDialog\": {\n    \"title\": \"\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"\",\n    \"disk_details\": \"\",\n    \"disk_button\": \"\",\n    \"link_title\": \"\",\n    \"link_details\": \"\",\n    \"link_button\": \"\",\n    \"excalidrawplus_description\": \"\",\n    \"excalidrawplus_button\": \"\",\n    \"excalidrawplus_exportError\": \"\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"\",\n    \"click\": \"\",\n    \"deepSelect\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"curvedArrow\": \"\",\n    \"curvedLine\": \"\",\n    \"documentation\": \"\",\n    \"doubleClick\": \"\",\n    \"drag\": \"\",\n    \"editor\": \"\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"\",\n    \"howto\": \"\",\n    \"or\": \"\",\n    \"preventBinding\": \"\",\n    \"tools\": \"\",\n    \"shortcuts\": \"\",\n    \"textFinish\": \"\",\n    \"textNewLine\": \"\",\n    \"title\": \"\",\n    \"view\": \"\",\n    \"zoomToFit\": \"\",\n    \"zoomToSelection\": \"\",\n    \"toggleElementLock\": \"\",\n    \"movePageUpDown\": \"\",\n    \"movePageLeftRight\": \"\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"\"\n  },\n  \"publishDialog\": {\n    \"title\": \"\",\n    \"itemName\": \"\",\n    \"authorName\": \"\",\n    \"githubUsername\": \"\",\n    \"twitterUsername\": \"\",\n    \"libraryName\": \"\",\n    \"libraryDesc\": \"\",\n    \"website\": \"\",\n    \"placeholder\": {\n      \"authorName\": \"\",\n      \"libraryName\": \"\",\n      \"libraryDesc\": \"\",\n      \"githubHandle\": \"\",\n      \"twitterHandle\": \"\",\n      \"website\": \"\"\n    },\n    \"errors\": {\n      \"required\": \"\",\n      \"website\": \"\"\n    },\n    \"noteDescription\": \"\",\n    \"noteGuidelines\": \"\",\n    \"noteLicense\": \"\",\n    \"noteItems\": \"\",\n    \"atleastOneLibItem\": \"\",\n    \"republishWarning\": \"\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"\",\n    \"content\": \"\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"\",\n    \"removeItemsFromLib\": \"\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"\",\n    \"link\": \"\"\n  },\n  \"stats\": {\n    \"angle\": \"\",\n    \"element\": \"\",\n    \"elements\": \"\",\n    \"height\": \"\",\n    \"scene\": \"\",\n    \"selected\": \"\",\n    \"storage\": \"\",\n    \"title\": \"\",\n    \"total\": \"\",\n    \"version\": \"\",\n    \"versionCopy\": \"\",\n    \"versionNotAvailable\": \"\",\n    \"width\": \"\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"\",\n    \"copyStyles\": \"\",\n    \"copyToClipboard\": \"\",\n    \"copyToClipboardAsPng\": \"\",\n    \"fileSaved\": \"\",\n    \"fileSavedToFilename\": \"\",\n    \"canvas\": \"畫布\",\n    \"selection\": \"\",\n    \"pasteAsSingleElement\": \"\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"\",\n    \"black\": \"\",\n    \"white\": \"\",\n    \"red\": \"\",\n    \"pink\": \"\",\n    \"grape\": \"\",\n    \"violet\": \"\",\n    \"gray\": \"\",\n    \"blue\": \"\",\n    \"cyan\": \"\",\n    \"teal\": \"\",\n    \"green\": \"\",\n    \"yellow\": \"\",\n    \"orange\": \"\",\n    \"bronze\": \"\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"\",\n      \"center_heading_plus\": \"\",\n      \"menuHint\": \"\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"\",\n      \"center_heading\": \"\",\n      \"toolbarHint\": \"\",\n      \"helpHint\": \"\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"\",\n    \"shades\": \"\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}