/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%7B%22id%22%3A%227f9ed16b4fff2e48eeac48cc53af5424b5b0291d44%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%7B%22id%22%3A%227f9ed16b4fff2e48eeac48cc53af5424b5b0291d44%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"7f9ed16b4fff2e48eeac48cc53af5424b5b0291d44\": () => (/* reexport safe */ C_Project_realcode_client_node_modules_clerk_nextjs_dist_esm_app_router_server_actions_js__WEBPACK_IMPORTED_MODULE_0__.invalidateCacheAction)\n/* harmony export */ });\n/* harmony import */ var C_Project_realcode_client_node_modules_clerk_nextjs_dist_esm_app_router_server_actions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWFjdGlvbi1lbnRyeS1sb2FkZXIuanM/YWN0aW9ucz0lNUIlNUIlMjJDJTNBJTVDJTVDUHJvamVjdCU1QyU1Q3JlYWxjb2RlJTVDJTVDY2xpZW50JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDJTQwY2xlcmslNUMlNUNuZXh0anMlNUMlNUNkaXN0JTVDJTVDZXNtJTVDJTVDYXBwLXJvdXRlciU1QyU1Q3NlcnZlci1hY3Rpb25zLmpzJTIyJTJDJTVCJTdCJTIyaWQlMjIlM0ElMjI3ZjllZDE2YjRmZmYyZTQ4ZWVhYzQ4Y2M1M2FmNTQyNGI1YjAyOTFkNDQlMjIlMkMlMjJleHBvcnRlZE5hbWUlMjIlM0ElMjJpbnZhbGlkYXRlQ2FjaGVBY3Rpb24lMjIlN0QlNUQlNUQlNUQmX19jbGllbnRfaW1wb3J0ZWRfXz10cnVlISIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDNkwiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGludmFsaWRhdGVDYWNoZUFjdGlvbiBhcyBcIjdmOWVkMTZiNGZmZjJlNDhlZWFjNDhjYzUzYWY1NDI0YjViMDI5MWQ0NFwiIH0gZnJvbSBcIkM6XFxcXFByb2plY3RcXFxccmVhbGNvZGVcXFxcY2xpZW50XFxcXG5vZGVfbW9kdWxlc1xcXFxAY2xlcmtcXFxcbmV4dGpzXFxcXGRpc3RcXFxcZXNtXFxcXGFwcC1yb3V0ZXJcXFxcc2VydmVyLWFjdGlvbnMuanNcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%7B%22id%22%3A%227f9ed16b4fff2e48eeac48cc53af5424b5b0291d44%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CProject%5Crealcode%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProject%5Crealcode%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CProject%5Crealcode%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProject%5Crealcode%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CProject%5Crealcode%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProject%5Crealcode%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f59b66d8a290199ea29179bca6dae3b4b4a16b454%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227fcbf338e0eabed52b3de56621b23e86b71bbccc4a%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fe3a6bbad02aa5d1ffcff38f7b7802f4ca03b4854%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%5D%5D%5D&__client_imported__=!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f59b66d8a290199ea29179bca6dae3b4b4a16b454%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227fcbf338e0eabed52b3de56621b23e86b71bbccc4a%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fe3a6bbad02aa5d1ffcff38f7b7802f4ca03b4854%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%5D%5D%5D&__client_imported__=! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"7f59b66d8a290199ea29179bca6dae3b4b4a16b454\": () => (/* reexport safe */ C_Project_realcode_client_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.deleteKeylessAction),\n/* harmony export */   \"7fcbf338e0eabed52b3de56621b23e86b71bbccc4a\": () => (/* reexport safe */ C_Project_realcode_client_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.syncKeylessConfigAction),\n/* harmony export */   \"7fe3a6bbad02aa5d1ffcff38f7b7802f4ca03b4854\": () => (/* reexport safe */ C_Project_realcode_client_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.createOrReadKeylessAction)\n/* harmony export */ });\n/* harmony import */ var C_Project_realcode_client_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js */ \"(rsc)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1hY3Rpb24tZW50cnktbG9hZGVyLmpzP2FjdGlvbnM9JTVCJTVCJTIyQyUzQSU1QyU1Q1Byb2plY3QlNUMlNUNyZWFsY29kZSU1QyU1Q2NsaWVudCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1QyU0MGNsZXJrJTVDJTVDbmV4dGpzJTVDJTVDZGlzdCU1QyU1Q2VzbSU1QyU1Q2FwcC1yb3V0ZXIlNUMlNUNrZXlsZXNzLWFjdGlvbnMuanMlMjIlMkMlNUIlN0IlMjJpZCUyMiUzQSUyMjdmNTliNjZkOGEyOTAxOTllYTI5MTc5YmNhNmRhZTNiNGI0YTE2YjQ1NCUyMiUyQyUyMmV4cG9ydGVkTmFtZSUyMiUzQSUyMmRlbGV0ZUtleWxlc3NBY3Rpb24lMjIlN0QlMkMlN0IlMjJpZCUyMiUzQSUyMjdmY2JmMzM4ZTBlYWJlZDUyYjNkZTU2NjIxYjIzZTg2YjcxYmJjY2M0YSUyMiUyQyUyMmV4cG9ydGVkTmFtZSUyMiUzQSUyMnN5bmNLZXlsZXNzQ29uZmlnQWN0aW9uJTIyJTdEJTJDJTdCJTIyaWQlMjIlM0ElMjI3ZmUzYTZiYmFkMDJhYTVkMWZmY2ZmMzhmN2I3ODAyZjRjYTAzYjQ4NTQlMjIlMkMlMjJleHBvcnRlZE5hbWUlMjIlM0ElMjJjcmVhdGVPclJlYWRLZXlsZXNzQWN0aW9uJTIyJTdEJTVEJTVEJTVEJl9fY2xpZW50X2ltcG9ydGVkX189ISIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUM0TDtBQUNJO0FBQ0UiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlbGV0ZUtleWxlc3NBY3Rpb24gYXMgXCI3ZjU5YjY2ZDhhMjkwMTk5ZWEyOTE3OWJjYTZkYWUzYjRiNGExNmI0NTRcIiB9IGZyb20gXCJDOlxcXFxQcm9qZWN0XFxcXHJlYWxjb2RlXFxcXGNsaWVudFxcXFxub2RlX21vZHVsZXNcXFxcQGNsZXJrXFxcXG5leHRqc1xcXFxkaXN0XFxcXGVzbVxcXFxhcHAtcm91dGVyXFxcXGtleWxlc3MtYWN0aW9ucy5qc1wiXG5leHBvcnQgeyBzeW5jS2V5bGVzc0NvbmZpZ0FjdGlvbiBhcyBcIjdmY2JmMzM4ZTBlYWJlZDUyYjNkZTU2NjIxYjIzZTg2YjcxYmJjY2M0YVwiIH0gZnJvbSBcIkM6XFxcXFByb2plY3RcXFxccmVhbGNvZGVcXFxcY2xpZW50XFxcXG5vZGVfbW9kdWxlc1xcXFxAY2xlcmtcXFxcbmV4dGpzXFxcXGRpc3RcXFxcZXNtXFxcXGFwcC1yb3V0ZXJcXFxca2V5bGVzcy1hY3Rpb25zLmpzXCJcbmV4cG9ydCB7IGNyZWF0ZU9yUmVhZEtleWxlc3NBY3Rpb24gYXMgXCI3ZmUzYTZiYmFkMDJhYTVkMWZmY2ZmMzhmN2I3ODAyZjRjYTAzYjQ4NTRcIiB9IGZyb20gXCJDOlxcXFxQcm9qZWN0XFxcXHJlYWxjb2RlXFxcXGNsaWVudFxcXFxub2RlX21vZHVsZXNcXFxcQGNsZXJrXFxcXG5leHRqc1xcXFxkaXN0XFxcXGVzbVxcXFxhcHAtcm91dGVyXFxcXGtleWxlc3MtYWN0aW9ucy5qc1wiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f59b66d8a290199ea29179bca6dae3b4b4a16b454%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227fcbf338e0eabed52b3de56621b23e86b71bbccc4a%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fe3a6bbad02aa5d1ffcff38f7b7802f4ca03b4854%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%5D%5D%5D&__client_imported__=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cclient%5C%5CClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cclient%5C%5Ckeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5Chooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ToastContainer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cgrid-pattern.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CClientAnimatePresence.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CSocketContext.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cclient%5C%5CClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cclient%5C%5Ckeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5Chooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ToastContainer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cgrid-pattern.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CClientAnimatePresence.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CSocketContext.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js */ \"(rsc)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js */ \"(rsc)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js */ \"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js */ \"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js */ \"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js */ \"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-toastify/dist/index.mjs */ \"(rsc)/./node_modules/react-toastify/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientAnimatePresence.tsx */ \"(rsc)/./src/components/ClientAnimatePresence.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navbar.tsx */ \"(rsc)/./src/components/Navbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.tsx */ \"(rsc)/./src/context/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/SocketContext.tsx */ \"(rsc)/./src/context/SocketContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/ThemeContext.tsx */ \"(rsc)/./src/context/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cclient%5C%5CClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cclient%5C%5Ckeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5Chooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ToastContainer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cgrid-pattern.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CClientAnimatePresence.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CSocketContext.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQcm9qZWN0JTVDJTVDcmVhbGNvZGUlNUMlNUNjbGllbnQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXNGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxQcm9qZWN0XFxcXHJlYWxjb2RlXFxcXGNsaWVudFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0XFxyZWFsY29kZVxcY2xpZW50XFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b69f2aa3b323\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcUHJvamVjdFxccmVhbGNvZGVcXGNsaWVudFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYjY5ZjJhYTNiMzIzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/grid-pattern.css":
/*!**********************************!*\
  !*** ./src/app/grid-pattern.css ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"293dae56b2da\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dyaWQtcGF0dGVybi5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0XFxyZWFsY29kZVxcY2xpZW50XFxzcmNcXGFwcFxcZ3JpZC1wYXR0ZXJuLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjI5M2RhZTU2YjJkYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/grid-pattern.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _grid_pattern_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./grid-pattern.css */ \"(rsc)/./src/app/grid-pattern.css\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @clerk/nextjs */ \"(rsc)/./node_modules/@clerk/nextjs/dist/esm/index.js\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/AuthContext */ \"(rsc)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_SocketContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/context/SocketContext */ \"(rsc)/./src/context/SocketContext.tsx\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Navbar */ \"(rsc)/./src/components/Navbar.tsx\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(rsc)/./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify */ \"(rsc)/./node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var _components_ClientAnimatePresence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ClientAnimatePresence */ \"(rsc)/./src/components/ClientAnimatePresence.tsx\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/context/ThemeContext */ \"(rsc)/./src/context/ThemeContext.tsx\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"RealCode | Realtime Code Collaboration\",\n    description: \"Realtime collaborative coding platform\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_10___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_11___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_12__.ClerkProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_ThemeContext__WEBPACK_IMPORTED_MODULE_9__.ThemeProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_SocketContext__WEBPACK_IMPORTED_MODULE_4__.SocketProvider, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientAnimatePresence__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                        children: children\n                                    }, \"main-content\", false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_7__.ToastContainer, {\n                                    position: \"bottom-right\",\n                                    autoClose: 3000,\n                                    hideProgressBar: false,\n                                    newestOnTop: true,\n                                    closeOnClick: true,\n                                    rtl: false,\n                                    pauseOnFocusLoss: true,\n                                    draggable: true,\n                                    pauseOnHover: true,\n                                    theme: \"colored\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Project\\realcode\\client\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ClientAnimatePresence.tsx":
/*!**************************************************!*\
  !*** ./src/components/ClientAnimatePresence.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\ClientAnimatePresence.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Project\\realcode\\client\\src\\components\\ClientAnimatePresence.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Project\\realcode\\client\\src\\components\\Navbar.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Project\\realcode\\client\\src\\context\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Project\\realcode\\client\\src\\context\\AuthContext.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./src/context/SocketContext.tsx":
/*!***************************************!*\
  !*** ./src/context/SocketContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),
/* harmony export */   useSocket: () => (/* binding */ useSocket)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useSocket = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSocket() from the server but useSocket is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Project\\realcode\\client\\src\\context\\SocketContext.tsx",
"useSocket",
);const SocketProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SocketProvider() from the server but SocketProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Project\\realcode\\client\\src\\context\\SocketContext.tsx",
"SocketProvider",
);

/***/ }),

/***/ "(rsc)/./src/context/ThemeContext.tsx":
/*!**************************************!*\
  !*** ./src/context/ThemeContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Project\\realcode\\client\\src\\context\\ThemeContext.tsx",
"ThemeProvider",
);const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Project\\realcode\\client\\src\\context\\ThemeContext.tsx",
"useTheme",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cclient%5C%5CClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cclient%5C%5Ckeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5Chooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ToastContainer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cgrid-pattern.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CClientAnimatePresence.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CSocketContext.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cclient%5C%5CClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cclient%5C%5Ckeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5Chooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ToastContainer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cgrid-pattern.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CClientAnimatePresence.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CSocketContext.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js */ \"(ssr)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js */ \"(ssr)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js */ \"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js */ \"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js */ \"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js */ \"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-toastify/dist/index.mjs */ \"(ssr)/./node_modules/react-toastify/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientAnimatePresence.tsx */ \"(ssr)/./src/components/ClientAnimatePresence.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navbar.tsx */ \"(ssr)/./src/components/Navbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.tsx */ \"(ssr)/./src/context/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/SocketContext.tsx */ \"(ssr)/./src/context/SocketContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/ThemeContext.tsx */ \"(ssr)/./src/context/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cclient%5C%5CClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cclient%5C%5Ckeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5Chooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Cclient-boundary%5C%5CuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ToastContainer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cgrid-pattern.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CClientAnimatePresence.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CSocketContext.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQcm9qZWN0JTVDJTVDcmVhbGNvZGUlNUMlNUNjbGllbnQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXNGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxQcm9qZWN0XFxcXHJlYWxjb2RlXFxcXGNsaWVudFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-intersection-observer */ \"(ssr)/./node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FiArrowRight_FiCode_FiGithub_FiGlobe_FiLock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FiArrowRight,FiCode,FiGithub,FiGlobe,FiLock,FiUsers!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Animation variants\nconst fadeIn = {\n    hidden: {\n        opacity: 0,\n        y: 20\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6,\n            ease: \"easeOut\"\n        }\n    }\n};\nconst staggerContainer = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.2\n        }\n    }\n};\nconst FeatureCard = ({ icon, title, description })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        className: \"card p-6 flex flex-col items-center text-center\",\n        initial: \"hidden\",\n        whileInView: \"visible\",\n        viewport: {\n            once: true\n        },\n        variants: fadeIn,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-4xl mb-4 text-blue-500\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-xl font-bold mb-2\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 dark:text-gray-300\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n// Simple code block component\nconst CodeBlock = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        className: \"bg-zinc-900 rounded-xl p-6 text-gray-300 font-mono text-sm overflow-hidden\",\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.8\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center text-gray-500 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-3 h-3 rounded-full bg-red-500 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-3 h-3 rounded-full bg-yellow-500 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-3 h-3 rounded-full bg-green-500 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2\",\n                        children: \"collaborative-code.js\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-purple-400\",\n                children: \"// Real-time collaborative code editor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-blue-400\",\n                children: \"// Start coding with your team...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\nfunction HomePage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useScroll)();\n    const heroRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const { ref: inViewRef, inView: isHeroInView } = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_6__.useInView)({\n        threshold: 0.1\n    });\n    // Combine refs\n    const setRefs = (el)=>{\n        // For react-intersection-observer\n        inViewRef(el);\n        // For heroRef\n        if (heroRef) {\n            // @ts-ignore\n            heroRef.current = el;\n        }\n    };\n    // Parallax effect\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_7__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        -300\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.section, {\n                ref: setRefs,\n                className: \"min-h-screen flex flex-col items-center justify-center relative overflow-hidden py-20 px-4\",\n                initial: \"hidden\",\n                animate: isHeroInView ? \"visible\" : \"hidden\",\n                variants: staggerContainer,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"absolute inset-0 -z-10\",\n                        style: {\n                            y\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-blue-900/20 to-purple-900/20 dark:from-blue-900/30 dark:to-purple-900/30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-grid-pattern opacity-10\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                        className: \"text-4xl md:text-6xl lg:text-7xl font-bold text-center mb-6 bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent\",\n                        variants: fadeIn,\n                        children: \"Realtime Code Collaboration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                        className: \"text-xl md:text-2xl text-center max-w-3xl mb-10 text-gray-700 dark:text-gray-300\",\n                        variants: fadeIn,\n                        children: \"Code together in real-time with your team. Share, edit, and collaborate on code from anywhere in the world.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"flex flex-col sm:flex-row gap-4 mb-16\",\n                        variants: fadeIn,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                onClick: ()=>router.push('/dashboard'),\n                                className: \"btn px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full text-lg font-medium flex items-center justify-center gap-2 group\",\n                                whileHover: {\n                                    scale: 1.05,\n                                    boxShadow: \"0 10px 25px -5px rgba(59, 130, 246, 0.5)\"\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: [\n                                    \"Get Started\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiCode_FiGithub_FiGlobe_FiLock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiArrowRight, {\n                                        className: \"group-hover:translate-x-1 transition-transform\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                onClick: ()=>router.push('/login'),\n                                className: \"btn px-8 py-3 bg-transparent border-2 border-blue-500 text-blue-500 dark:text-blue-400 rounded-full text-lg font-medium\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: \"Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"w-full max-w-4xl\",\n                        variants: fadeIn,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {}, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-gray-50 dark:bg-zinc-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"text-center mb-16\",\n                            initial: \"hidden\",\n                            whileInView: \"visible\",\n                            viewport: {\n                                once: true,\n                                margin: \"-100px\"\n                            },\n                            variants: fadeIn,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    children: \"Powerful Features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\",\n                                    children: \"Everything you need for seamless code collaboration\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiCode_FiGithub_FiGlobe_FiLock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiCode, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    title: \"Real-time Editing\",\n                                    description: \"See changes as they happen with real-time synchronization between all collaborators.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiCode_FiGithub_FiGlobe_FiLock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiUsers, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    title: \"Team Collaboration\",\n                                    description: \"Invite team members to join your coding sessions with a simple link.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiCode_FiGithub_FiGlobe_FiLock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiLock, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    title: \"Secure Connection\",\n                                    description: \"Your code is protected with end-to-end encryption and secure authentication.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiCode_FiGithub_FiGlobe_FiLock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiGlobe, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    title: \"Cross-Platform\",\n                                    description: \"Work from any device with a browser - desktop, tablet, or mobile.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.section, {\n                className: \"py-20 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white\",\n                initial: \"hidden\",\n                whileInView: \"visible\",\n                viewport: {\n                    once: true\n                },\n                variants: staggerContainer,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-5xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h2, {\n                            className: \"text-3xl md:text-5xl font-bold mb-6\",\n                            variants: fadeIn,\n                            children: \"Ready to start collaborating?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                            className: \"text-xl mb-10 text-blue-100\",\n                            variants: fadeIn,\n                            children: \"Join thousands of developers who are already using RealCode for their team projects.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            variants: fadeIn,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                onClick: ()=>router.push('/signup'),\n                                className: \"btn px-10 py-4 bg-white text-blue-600 rounded-full text-xl font-medium\",\n                                whileHover: {\n                                    scale: 1.05,\n                                    boxShadow: \"0 10px 25px -5px rgba(255, 255, 255, 0.3)\"\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: \"Sign Up for Free\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"py-12 px-4 bg-zinc-900 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiCode_FiGithub_FiGlobe_FiLock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiCode, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" RealCode\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Realtime collaborative coding platform for teams.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/features\",\n                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                    children: \"Features\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/pricing\",\n                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                    children: \"Pricing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/docs\",\n                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                    children: \"Documentation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/about\",\n                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                    children: \"About Us\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/blog\",\n                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                    children: \"Blog\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/careers\",\n                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                    children: \"Careers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Connect\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/contact\",\n                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                    children: \"Contact Us\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"https://twitter.com\",\n                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                    children: \"Twitter\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"https://github.com\",\n                                                    className: \"text-gray-400 hover:text-white transition-colors flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiCode_FiGithub_FiGlobe_FiLock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiGithub, {\n                                                            className: \"mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 130\n                                                        }, this),\n                                                        \" GitHub\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto mt-12 pt-8 border-t border-zinc-800 text-center text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"\\xa9 \",\n                                new Date().getFullYear(),\n                                \" RealCode. All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClientAnimatePresence.tsx":
/*!**************************************************!*\
  !*** ./src/components/ClientAnimatePresence.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientAnimatePresence)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ClientAnimatePresence({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.AnimatePresence, {\n        mode: \"wait\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\ClientAnimatePresence.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DbGllbnRBbmltYXRlUHJlc2VuY2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRWdEO0FBRWpDLFNBQVNDLHNCQUFzQixFQUFFQyxRQUFRLEVBQWlDO0lBQ3ZGLHFCQUFPLDhEQUFDRiwwREFBZUE7UUFBQ0csTUFBSztrQkFBUUQ7Ozs7OztBQUN2QyIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RcXHJlYWxjb2RlXFxjbGllbnRcXHNyY1xcY29tcG9uZW50c1xcQ2xpZW50QW5pbWF0ZVByZXNlbmNlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCB7IEFuaW1hdGVQcmVzZW5jZSB9IGZyb20gXCJmcmFtZXItbW90aW9uXCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDbGllbnRBbmltYXRlUHJlc2VuY2UoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xyXG4gIHJldHVybiA8QW5pbWF0ZVByZXNlbmNlIG1vZGU9XCJ3YWl0XCI+e2NoaWxkcmVufTwvQW5pbWF0ZVByZXNlbmNlPjtcclxufVxyXG4iXSwibmFtZXMiOlsiQW5pbWF0ZVByZXNlbmNlIiwiQ2xpZW50QW5pbWF0ZVByZXNlbmNlIiwiY2hpbGRyZW4iLCJtb2RlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClientAnimatePresence.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/context/ThemeContext */ \"(ssr)/./src/context/ThemeContext.tsx\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_FiCode_FiMonitor_FiMoon_FiSun_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FiCode,FiMonitor,FiMoon,FiSun!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst Navbar = ()=>{\n    const { user, logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { theme, setTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handleLogout = async ()=>{\n        logout();\n        router.push(\"/login\");\n    };\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.nav, {\n        className: \"w-full bg-zinc-900 p-4 flex justify-between items-center text-white sticky top-0 z-50 glass\",\n        initial: {\n            y: -100,\n            opacity: 0\n        },\n        animate: {\n            y: 0,\n            opacity: 1\n        },\n        transition: {\n            type: \"spring\",\n            stiffness: 100,\n            damping: 15\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                className: \"flex items-center space-x-2\",\n                whileHover: {\n                    scale: 1.05\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCode_FiMonitor_FiMoon_FiSun_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiCode, {\n                        className: \"text-2xl text-blue-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/\",\n                        className: \"text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent\",\n                        children: \"RealCode\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:flex items-center space-x-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 mr-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                whileHover: {\n                                    scale: 1.1\n                                },\n                                whileTap: {\n                                    scale: 0.9\n                                },\n                                onClick: ()=>setTheme(\"light\"),\n                                className: `p-2 rounded-full ${theme === \"light\" ? \"bg-yellow-400 text-yellow-900\" : \"text-gray-400\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCode_FiMonitor_FiMoon_FiSun_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiSun, {}, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                whileHover: {\n                                    scale: 1.1\n                                },\n                                whileTap: {\n                                    scale: 0.9\n                                },\n                                onClick: ()=>setTheme(\"dark\"),\n                                className: `p-2 rounded-full ${theme === \"dark\" ? \"bg-indigo-600 text-white\" : \"text-gray-400\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCode_FiMonitor_FiMoon_FiSun_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiMoon, {}, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                whileHover: {\n                                    scale: 1.1\n                                },\n                                whileTap: {\n                                    scale: 0.9\n                                },\n                                onClick: ()=>setTheme(\"system\"),\n                                className: `p-2 rounded-full ${theme === \"system\" ? \"bg-green-500 text-white\" : \"text-gray-400\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCode_FiMonitor_FiMoon_FiSun_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiMonitor, {}, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, undefined),\n                    user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                                className: \"text-sm bg-zinc-800 px-3 py-1 rounded-full\",\n                                initial: {\n                                    opacity: 0,\n                                    x: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                children: [\n                                    \"Hello, \",\n                                    user.email\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                onClick: handleLogout,\n                                className: \"bg-gradient-to-r from-red-500 to-pink-600 px-4 py-2 rounded-md font-medium\",\n                                whileHover: {\n                                    scale: 1.05,\n                                    boxShadow: \"0 10px 25px -5px rgba(239, 68, 68, 0.4)\"\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: \"Logout\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/login\",\n                                    className: \"px-4 py-2 rounded-md border border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white transition-colors\",\n                                    children: \"Login\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/signup\",\n                                    className: \"px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-colors\",\n                                    children: \"Signup\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                className: \"md:hidden text-white\",\n                onClick: toggleMenu,\n                whileTap: {\n                    scale: 0.9\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: isMenuOpen ? \"M6 18L18 6M6 6l12 12\" : \"M4 6h16M4 12h16M4 18h16\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                className: \"absolute top-16 left-0 right-0 bg-zinc-900 p-4 md:hidden z-50 glass\",\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                exit: {\n                    opacity: 0,\n                    y: -20\n                },\n                transition: {\n                    duration: 0.2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-4 py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.1\n                                    },\n                                    whileTap: {\n                                        scale: 0.9\n                                    },\n                                    onClick: ()=>setTheme(\"light\"),\n                                    className: `p-2 rounded-full ${theme === \"light\" ? \"bg-yellow-400 text-yellow-900\" : \"text-gray-400\"}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCode_FiMonitor_FiMoon_FiSun_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiSun, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.1\n                                    },\n                                    whileTap: {\n                                        scale: 0.9\n                                    },\n                                    onClick: ()=>setTheme(\"dark\"),\n                                    className: `p-2 rounded-full ${theme === \"dark\" ? \"bg-indigo-600 text-white\" : \"text-gray-400\"}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCode_FiMonitor_FiMoon_FiSun_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiMoon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.1\n                                    },\n                                    whileTap: {\n                                        scale: 0.9\n                                    },\n                                    onClick: ()=>setTheme(\"system\"),\n                                    className: `p-2 rounded-full ${theme === \"system\" ? \"bg-green-500 text-white\" : \"text-gray-400\"}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCode_FiMonitor_FiMoon_FiSun_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiMonitor, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, undefined),\n                        user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-2 border-t border-zinc-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            \"Hello, \",\n                                            user.email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                    onClick: handleLogout,\n                                    className: \"w-full bg-gradient-to-r from-red-500 to-pink-600 py-2 rounded-md font-medium\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/login\",\n                                    className: \"w-full text-center py-2 rounded-md border border-blue-500 text-blue-500\",\n                                    children: \"Login\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/signup\",\n                                    className: \"w-full text-center py-2 rounded-md bg-blue-600 text-white\",\n                                    children: \"Signup\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/./node_modules/@clerk/clerk-react/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    loading: true,\n    logout: async ()=>{},\n    signInWithGoogle: async ()=>({}),\n    signInWithEmail: async ()=>({}),\n    signUpWithEmail: async ()=>({})\n});\nconst AuthProvider = ({ children })=>{\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { isLoaded: clerkLoaded, user: clerkUser, isSignedIn } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    const { signOut, setActive } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__.useClerk)();\n    const { signIn, isLoaded: signInLoaded } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__.useSignIn)();\n    const { signUp, isLoaded: signUpLoaded } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__.useSignUp)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (clerkLoaded) {\n                if (isSignedIn && clerkUser) {\n                    const primaryEmail = clerkUser.primaryEmailAddress?.emailAddress;\n                    if (primaryEmail) {\n                        setUser({\n                            email: primaryEmail,\n                            name: clerkUser.fullName || clerkUser.username || primaryEmail.split('@')[0],\n                            photoURL: clerkUser.imageUrl\n                        });\n                    }\n                } else {\n                    setUser(null);\n                }\n                setLoading(false);\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        clerkLoaded,\n        clerkUser,\n        isSignedIn\n    ]);\n    const signInWithGoogle = async ()=>{\n        if (!signInLoaded) return {\n            success: false,\n            error: \"Auth not loaded\"\n        };\n        try {\n            await signIn.authenticateWithRedirect({\n                strategy: \"oauth_google\",\n                redirectUrl: window.location.origin + \"/sso-callback\",\n                redirectUrlComplete: window.location.origin + \"/\"\n            });\n        } catch (error) {\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n    };\n    const signInWithEmail = async (email, password)=>{\n        if (!signInLoaded) return {\n            success: false,\n            error: \"Auth not loaded\"\n        };\n        try {\n            const result = await signIn.create({\n                identifier: email,\n                password\n            });\n            if (result.status === \"complete\") {\n                await setActive({\n                    session: result.createdSessionId\n                });\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: \"Sign-in failed\",\n                    nextStep: result.status\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n    };\n    const signUpWithEmail = async (email, password, name, verificationCode)=>{\n        if (!signUpLoaded) return {\n            success: false,\n            error: \"Auth not loaded\"\n        };\n        try {\n            if (verificationCode) {\n                const result = await signUp.attemptEmailAddressVerification({\n                    code: verificationCode\n                });\n                if (result.status === \"complete\") {\n                    await setActive({\n                        session: result.createdSessionId\n                    });\n                    return {\n                        success: true\n                    };\n                } else {\n                    return {\n                        success: false,\n                        error: \"Invalid or expired code\",\n                        nextStep: result.status\n                    };\n                }\n            }\n            const result = await signUp.create({\n                emailAddress: email,\n                password,\n                firstName: name ? name.split(' ')[0] : undefined,\n                lastName: name && name.split(' ').length > 1 ? name.split(' ').slice(1).join(' ') : undefined\n            });\n            if (result.status === \"complete\") {\n                await setActive({\n                    session: result.createdSessionId\n                });\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: \"Verification required\",\n                    nextStep: result.status\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await signOut();\n            setUser(null);\n        } catch (error) {}\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            loading: loading || !clerkLoaded,\n            logout,\n            signInWithGoogle,\n            signInWithEmail,\n            signUpWithEmail\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/SocketContext.tsx":
/*!***************************************!*\
  !*** ./src/context/SocketContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_socketService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/socketService */ \"(ssr)/./src/services/socketService.ts\");\n/* __next_internal_client_entry_do_not_use__ useSocket,SocketProvider auto */ \n\n\n\nconst SocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst useSocket = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SocketContext);\n    if (!context) {\n        throw new Error(\"useSocket must be used within SocketProvider\");\n    }\n    return context;\n};\n// Use an instance of SocketService\nconst socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getInstance();\nconst SocketProvider = ({ children })=>{\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(socketServiceInstance.isConnected());\n    const socket = socketServiceInstance.getSocket();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SocketProvider.useEffect\": ()=>{\n            // Handle connection status changes\n            const handleConnect = {\n                \"SocketProvider.useEffect.handleConnect\": ()=>{\n                    console.log(\"Socket connected\");\n                    setIsConnected(true);\n                }\n            }[\"SocketProvider.useEffect.handleConnect\"];\n            const handleDisconnect = {\n                \"SocketProvider.useEffect.handleDisconnect\": ()=>{\n                    console.log(\"Socket disconnected\");\n                    setIsConnected(false);\n                    // Try to reconnect after a short delay\n                    setTimeout({\n                        \"SocketProvider.useEffect.handleDisconnect\": ()=>{\n                            console.log(\"Attempting to reconnect...\");\n                            socketServiceInstance.connect();\n                        }\n                    }[\"SocketProvider.useEffect.handleDisconnect\"], 3000);\n                }\n            }[\"SocketProvider.useEffect.handleDisconnect\"];\n            if (socket) {\n                socket.on(\"connect\", handleConnect);\n                socket.on(\"disconnect\", handleDisconnect);\n            }\n            return ({\n                \"SocketProvider.useEffect\": ()=>{\n                    if (socket) {\n                        socket.off(\"connect\", handleConnect);\n                        socket.off(\"disconnect\", handleDisconnect);\n                    }\n                }\n            })[\"SocketProvider.useEffect\"];\n        }\n    }[\"SocketProvider.useEffect\"], [\n        socket\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocketContext.Provider, {\n        value: {\n            isConnected,\n            socket\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\context\\\\SocketContext.tsx\",\n        lineNumber: 62,\n        columnNumber: 9\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/SocketContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/ThemeContext.tsx":
/*!**************************************!*\
  !*** ./src/context/ThemeContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    theme: \"system\",\n    setTheme: ()=>null\n});\nconst ThemeProvider = ({ children })=>{\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"system\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Update theme\n    const updateTheme = (newTheme)=>{\n        const root = window.document.documentElement;\n        const isDark = newTheme === \"dark\" || newTheme === \"system\" && window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n        root.classList.remove(\"light\", \"dark\");\n        root.classList.add(isDark ? \"dark\" : \"light\");\n        // Store the preference\n        if (newTheme !== \"system\") {\n            localStorage.setItem(\"theme\", newTheme);\n        } else {\n            localStorage.removeItem(\"theme\");\n        }\n    };\n    // Set theme\n    const handleSetTheme = (newTheme)=>{\n        setTheme(newTheme);\n        updateTheme(newTheme);\n    };\n    // Initialize theme\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            setMounted(true);\n            const storedTheme = localStorage.getItem(\"theme\");\n            if (storedTheme) {\n                setTheme(storedTheme);\n                updateTheme(storedTheme);\n            } else {\n                updateTheme(\"system\");\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Handle system theme changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (theme === \"system\") {\n                const mediaQuery = window.matchMedia(\"(prefers-color-scheme: dark)\");\n                const handleChange = {\n                    \"ThemeProvider.useEffect.handleChange\": ()=>{\n                        updateTheme(\"system\");\n                    }\n                }[\"ThemeProvider.useEffect.handleChange\"];\n                mediaQuery.addEventListener(\"change\", handleChange);\n                return ({\n                    \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener(\"change\", handleChange)\n                })[\"ThemeProvider.useEffect\"];\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            setTheme: handleSetTheme\n        },\n        children: mounted && children\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\context\\\\ThemeContext.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\nconst useTheme = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/services/socketService.ts":
/*!***************************************!*\
  !*** ./src/services/socketService.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n// client/src/services/socketService.ts\n\n\n// Fix: Ensure SOCKET_URL is defined before use (move it above all uses)\nconst SOCKET_URL = \"http://localhost:5001\" || 0;\n// Create a singleton socket instance\nclass SocketService {\n    constructor(){\n        this.socket = null;\n        this.listeners = new Map();\n        this.connected = false;\n        this.initSocket();\n    }\n    static getInstance() {\n        if (!SocketService.instance) {\n            SocketService.instance = new SocketService();\n        }\n        return SocketService.instance;\n    }\n    initSocket() {\n        try {\n            console.log('Initializing socket connection to', SOCKET_URL);\n            // Remove listeners from old socket if it exists\n            if (this.socket) {\n                this.socket.removeAllListeners();\n                this.socket.disconnect();\n            }\n            // Use polling first for better compatibility, then upgrade to websocket\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                reconnectionAttempts: 10,\n                reconnectionDelay: 1000,\n                timeout: 10000,\n                transports: [\n                    'polling',\n                    'websocket'\n                ],\n                autoConnect: true,\n                forceNew: true,\n                upgrade: true\n            });\n            console.log('Socket instance created successfully');\n            this.setupEventListeners();\n        } catch (error) {\n            console.error('Error initializing socket:', error);\n        }\n    }\n    setupEventListeners() {\n        if (!this.socket) return;\n        // Clear any existing listeners to prevent duplicates\n        this.socket.removeAllListeners();\n        // Connection events\n        this.socket.on('connect', ()=>{\n            console.log('Socket connected successfully:', this.socket?.id);\n            this.connected = true;\n            this.emitEvent('connect', null);\n        });\n        this.socket.on('disconnect', (reason)=>{\n            console.log(`Socket disconnected. Reason: ${reason}`);\n            this.connected = false;\n            this.emitEvent('disconnect', reason);\n            if (reason !== 'io client disconnect') {\n                console.log('Reconnection attempt will start in 2 seconds...');\n                setTimeout(()=>{\n                    console.log('Attempting to reconnect...');\n                    this.connect();\n                }, 2000);\n            }\n        });\n        // Transport and reconnection events\n        if (this.socket.io) {\n            this.socket.io.on('error', (error)=>{\n                console.error('Transport error:', error);\n            });\n            this.socket.io.on('reconnect_attempt', (attempt)=>{\n                console.log(`Reconnection attempt ${attempt}`);\n            });\n            this.socket.io.on('reconnect', (attemptNumber)=>{\n                this.connected = true;\n                this.emitEvent('connect', null);\n            });\n            this.socket.io.on('reconnect_error', (error)=>{\n                console.error('Reconnection error:', error);\n            });\n            this.socket.io.on('reconnect_failed', ()=>{\n                this.emitEvent('error', 'Failed to reconnect after multiple attempts');\n                // Try a different approach after all reconnection attempts fail\n                setTimeout(()=>{\n                    this.initSocket();\n                }, 3000);\n            });\n        }\n        this.socket.on('connect_error', (error)=>{\n            console.error('Socket connection error:', error?.message || 'Unknown error');\n            this.connected = false;\n            // Handle specific error types\n            if (error?.message?.includes('xhr poll error')) {\n                console.log('XHR polling error detected - will try alternative connection method');\n                // Set a flag to track this specific error\n                if (false) {}\n                // Emit the error event\n                this.emitEvent('error', 'XHR polling error - trying alternative connection');\n                // Try the alternative transport after a short delay\n                setTimeout(()=>{\n                    this.tryAlternativeTransport();\n                }, 1000);\n            } else if (error?.message?.includes('websocket') || error?.message?.includes('WebSocket') || typeof error === 'object' && 'type' in error && error.type === 'TransportError') {\n                console.log('WebSocket error detected - will try polling transport');\n                // Set a flag to track this specific error\n                if (false) {}\n                // Try polling immediately\n                setTimeout(()=>{\n                    try {\n                        console.log('Switching to polling transport...');\n                        this.socket?.disconnect();\n                        this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                            reconnectionAttempts: 5,\n                            reconnectionDelay: 1000,\n                            timeout: 10000,\n                            transports: [\n                                'polling'\n                            ],\n                            autoConnect: true,\n                            forceNew: true,\n                            upgrade: false\n                        });\n                        this.setupEventListeners();\n                    } catch (e) {\n                        console.error('Error switching to polling transport:', e);\n                    }\n                }, 1000);\n                this.emitEvent('error', 'WebSocket error - trying polling transport');\n            } else {\n                // For other errors, just emit the error event\n                this.emitEvent('error', error?.message || 'Unknown error');\n                // Try to reconnect after a delay\n                setTimeout(()=>{\n                    this.connect();\n                }, 3000);\n            }\n        });\n        // Application-specific events\n        this.socket.on('code-update', (code)=>{\n            this.emitEvent('code-update', code);\n        });\n        this.socket.on('user-typing', (data)=>{\n            this.emitEvent('user-typing', data);\n        });\n        this.socket.on('user-joined', (users)=>{\n            this.emitEvent('user-joined', users);\n        });\n        this.socket.on('user-left', (users)=>{\n            this.emitEvent('user-left', users);\n        });\n        this.socket.on('highlight-line', (data)=>{\n            this.emitEvent('highlight-line', data);\n        });\n        this.socket.on('cursor-move', ({ userId, position })=>{\n            console.log(`Cursor move received from user ${userId}:`, position);\n            this.listeners.get('cursor-move')?.forEach((callback)=>callback({\n                    userId,\n                    position\n                }));\n        });\n        this.socket.on('room-users-updated', (data)=>{\n            this.emitEvent('room-users-updated', data);\n        });\n        // Handle request for initial code from new users\n        this.socket.on('get-initial-code', (data)=>{\n            this.emitEvent('get-initial-code', data);\n        });\n        // Handle receiving initial code\n        this.socket.on('initial-code-received', (data)=>{\n            this.emitEvent('initial-code-received', data);\n        });\n        // Handle teacher selection events\n        this.socket.on('teacher-selection', (data)=>{\n            this.emitEvent('teacher-selection', data);\n        });\n        this.socket.on('clear-teacher-selection', (data)=>{\n            this.emitEvent('clear-teacher-selection', data);\n        });\n    }\n    // Add event listener\n    on(event, callback) {\n        if (!this.listeners.has(event)) {\n            this.listeners.set(event, []);\n        }\n        this.listeners.get(event)?.push(callback);\n    }\n    // Remove event listener\n    off(event, callback) {\n        if (!this.listeners.has(event)) return;\n        const callbacks = this.listeners.get(event) || [];\n        const index = callbacks.indexOf(callback);\n        if (index !== -1) {\n            callbacks.splice(index, 1);\n        }\n    }\n    // Emit event to listeners\n    emitEvent(event, data) {\n        if (!this.listeners.has(event)) return;\n        const callbacks = this.listeners.get(event) || [];\n        callbacks.forEach((callback)=>{\n            if (typeof callback === 'function') {\n                try {\n                    callback(data);\n                } catch (error) {\n                    console.error(`Error in ${event} listener:`, error);\n                }\n            }\n        });\n    }\n    // Check if socket is connected\n    isConnected() {\n        return this.connected && !!this.socket?.connected;\n    }\n    // Connect to socket server with fallback mechanisms\n    connect() {\n        if (!this.socket) {\n            this.initSocket();\n        } else if (!this.socket.connected) {\n            console.log('Socket exists but not connected, attempting to reconnect...');\n            // Try to reconnect with existing socket\n            try {\n                this.socket.connect();\n            } catch (error) {\n                console.error('Error reconnecting with existing socket:', error);\n                // If reconnection fails, create a new socket\n                this.initSocket();\n            }\n        }\n        // Set a timeout to check if connection was successful\n        setTimeout(()=>{\n            if (!this.isConnected()) {\n                this.tryAlternativeTransport();\n            }\n        }, 5000);\n    }\n    // Try alternative transport method if WebSocket fails\n    tryAlternativeTransport() {\n        try {\n            // Disconnect existing socket if any\n            if (this.socket) {\n                this.socket.disconnect();\n            }\n            // Create new socket with both transports but prioritize polling\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                reconnectionAttempts: 5,\n                reconnectionDelay: 1000,\n                timeout: 10000,\n                transports: [\n                    'polling',\n                    'websocket'\n                ],\n                autoConnect: true,\n                forceNew: true,\n                upgrade: true\n            });\n            this.setupEventListeners();\n        } catch (error) {\n            console.error('Error setting up alternative transport:', error);\n        }\n    }\n    // Disconnect from socket server\n    disconnect() {\n        if (this.socket) {\n            this.socket.disconnect();\n        }\n    }\n    // Create a new room\n    createRoom(username, roomId) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket || !this.isConnected()) {\n                return reject(new Error('Socket not connected'));\n            }\n            // Generate a unique userId if not already stored\n            let userId =  false ? 0 : null;\n            if (!userId) {\n                userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;\n                if (false) {}\n            }\n            this.socket.emit('create-room', {\n                username,\n                roomId,\n                userId\n            }, (response)=>{\n                if (response.error) {\n                    reject(new Error(response.error));\n                } else {\n                    // If the server validated and possibly changed the username, update it locally\n                    if (response.username && response.username !== username) {\n                        if (false) {}\n                    }\n                    resolve({\n                        roomId: response.roomId,\n                        username: response.username || username\n                    });\n                }\n            });\n        });\n    }\n    // Join an existing room with fallback to HTTP if socket fails\n    joinRoom(roomId, username) {\n        return new Promise(async (resolve, reject)=>{\n            // Generate a unique userId if not already stored\n            let userId =  false ? 0 : null;\n            if (!userId) {\n                userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;\n                if (false) {}\n            }\n            // Check if we have a socket connection\n            if (!this.socket || !this.isConnected()) {\n                // Try to connect\n                this.connect();\n                // Wait a bit to see if connection succeeds\n                await new Promise((r)=>setTimeout(r, 2000));\n                // If still not connected, use HTTP fallback\n                if (!this.isConnected()) {\n                    return this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                }\n            }\n            // If we reach here, we have a socket connection, so use it\n            try {\n                if (!this.socket) {\n                    throw new Error('Socket is null');\n                }\n                // Emit join-room with userId, username, and roomId\n                this.socket.emit('join-room', {\n                    roomId,\n                    username,\n                    userId\n                }, (response)=>{\n                    if (response.error) {\n                        // If socket join fails, try HTTP fallback\n                        this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                    } else if (response.success) {\n                        // If the server validated and possibly changed the username, update it locally\n                        if (response.username && response.username !== username) {\n                            if (false) {}\n                        }\n                        // Mark the current user in the users list\n                        const usersWithCurrentFlag = (response.users || []).map((user)=>({\n                                ...user,\n                                userId: user.userId || '',\n                                isCurrentUser: (user.userId || '') === userId\n                            }));\n                        resolve({\n                            users: usersWithCurrentFlag || [],\n                            username: response.username || username,\n                            role: response.role\n                        });\n                    } else {\n                        // If socket join fails, try HTTP fallback\n                        this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                    }\n                });\n                // Set a timeout in case the callback never fires\n                setTimeout(()=>{\n                    // If we haven't resolved or rejected yet, try HTTP fallback\n                    this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                }, 5000);\n            } catch (error) {\n                // If socket join throws an exception, try HTTP fallback\n                this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n            }\n        });\n    }\n    // HTTP fallback for joining a room when socket fails\n    joinRoomViaHttp(roomId, username, userId, resolve, reject) {\n        // Use axios to make a direct HTTP request to the server\n        const apiUrl = `${SOCKET_URL}/api/join-room`;\n        axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(apiUrl, {\n            roomId,\n            username,\n            userId\n        }).then((response)=>{\n            if (response.data.error) {\n                reject(new Error(response.data.error));\n            } else {\n                // If the server validated and possibly changed the username, update it locally\n                if (response.data.username && response.data.username !== username) {\n                    if (false) {}\n                }\n                // Create a default user list with at least the current user\n                const users = response.data.users || [\n                    {\n                        userId,\n                        username: response.data.username || username,\n                        socketId: 'http-fallback'\n                    }\n                ];\n                // Fix: When mapping users, always provide a fallback for userId (empty string if undefined)\n                const usersWithCurrentFlag = users.map((user)=>({\n                        ...user,\n                        userId: user.userId || '',\n                        isCurrentUser: (user.userId || '') === userId\n                    }));\n                resolve({\n                    users: usersWithCurrentFlag,\n                    username: response.data.username || username,\n                    role: response.data.role\n                });\n                // Try to reconnect socket after successful HTTP fallback\n                setTimeout(()=>this.connect(), 1000);\n            }\n        }).catch((error)=>{\n            // If HTTP fallback also fails, create a minimal response with just the current user\n            const fallbackUser = {\n                userId,\n                username,\n                isCurrentUser: true\n            };\n            // Resolve with just the current user to allow the UI to function\n            resolve({\n                users: [\n                    fallbackUser\n                ],\n                username,\n                role: 'student' // Default role for fallback\n            });\n            // Try to reconnect socket after error\n            setTimeout(()=>this.connect(), 2000);\n        });\n    }\n    // Send code changes to the server with HTTP fallback\n    sendCodeChange(roomId, code) {\n        // If socket is connected, use it\n        if (this.socket && this.isConnected()) {\n            try {\n                // Use volatile for code changes to prevent queueing\n                // This helps prevent outdated updates from being sent\n                this.socket.volatile.emit('code-change', {\n                    roomId,\n                    code\n                });\n                return true;\n            } catch (error) {\n            // Fall through to HTTP fallback\n            }\n        } else {\n            console.warn('Socket not connected, falling back to HTTP for code change.');\n        }\n        // HTTP fallback for code changes\n        this.sendCodeChangeViaHttp(roomId, code);\n        return true;\n    }\n    // Send initial code to a requesting user\n    sendInitialCode(roomId, code, requestingUserId) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot send initial code');\n            return false;\n        }\n        try {\n            this.socket.emit('send-initial-code', {\n                roomId,\n                code,\n                requestingUserId\n            });\n            console.log(`Sent initial code to user ${requestingUserId} in room ${roomId}`);\n            return true;\n        } catch (error) {\n            console.error('Error sending initial code:', error);\n            return false;\n        }\n    }\n    // Send teacher text selection to students\n    sendTeacherSelection(roomId, selection) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot send teacher selection');\n            return false;\n        }\n        try {\n            this.socket.emit('teacher-selection', {\n                roomId,\n                selection\n            });\n            console.log(`Sent teacher selection to room ${roomId}:`, selection);\n            return true;\n        } catch (error) {\n            console.error('Error sending teacher selection:', error);\n            return false;\n        }\n    }\n    // Clear teacher text selection\n    clearTeacherSelection(roomId) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot clear teacher selection');\n            return false;\n        }\n        try {\n            this.socket.emit('clear-teacher-selection', {\n                roomId\n            });\n            console.log(`Cleared teacher selection in room ${roomId}`);\n            return true;\n        } catch (error) {\n            console.error('Error clearing teacher selection:', error);\n            return false;\n        }\n    }\n    // HTTP fallback for sending code changes\n    sendCodeChangeViaHttp(roomId, code) {\n        // Only send HTTP fallback for significant changes to reduce traffic\n        // Store the last sent code to avoid sending duplicates\n        const lastSentCode =  false ? 0 : null;\n        if (lastSentCode === code) {\n            return; // Don't send duplicate code\n        }\n        // Use axios to make a direct HTTP request to the server\n        const apiUrl = `${SOCKET_URL}/api/code-change`;\n        const userId =  false ? 0 : '';\n        axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(apiUrl, {\n            roomId,\n            code,\n            userId\n        }).then((response)=>{\n            // Store the sent code to avoid duplicates\n            if (false) {}\n        }).catch((error)=>{\n            console.error('Error sending code change via HTTP:', error);\n        });\n        // Try to reconnect socket\n        if (!this.isConnected()) {\n            setTimeout(()=>this.connect(), 1000);\n        }\n    }\n    // Send typing notification\n    sendTyping(roomId, username) {\n        if (!this.socket || !this.isConnected()) {\n            return;\n        }\n        // Use the exact username provided by the user without any modifications\n        // This ensures we use exactly what the user entered on the dashboard\n        const validUsername = username;\n        // Get userId from localStorage\n        const userId =  false ? 0 : `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;\n        try {\n            this.socket.emit('typing', {\n                roomId,\n                username: validUsername,\n                userId\n            });\n        } catch (error) {}\n    }\n    // Emit a highlight-line event to the server\n    sendHighlightLine(roomId, startLine, endLine, comment) {\n        if (!this.socket || !this.isConnected()) return;\n        try {\n            this.socket.emit('highlight-line', {\n                roomId,\n                startLine,\n                endLine,\n                comment\n            });\n        } catch (error) {\n        // Optionally handle error\n        }\n    }\n    // Leave a room\n    leaveRoom(roomId) {\n        if (!this.socket || !this.isConnected()) return;\n        try {\n            this.socket.emit('leave-room', roomId);\n        } catch (error) {}\n    }\n    // Add a public method to listen for the next connect event\n    onConnect(callback) {\n        if (this.isConnected()) {\n            callback();\n        } else if (this.socket) {\n            this.socket.once('connect', callback);\n        } else {\n            // If socket is not initialized, initialize and then listen\n            this.initSocket();\n            // Wait for the socket to be created, then attach the listener\n            setTimeout(()=>{\n                this.socket?.once('connect', callback);\n            }, 100);\n        }\n    }\n    getSocket() {\n        return this.socket;\n    }\n    // Emit cursor movement\n    sendCursorMove(roomId, userId, position) {\n        if (this.socket && this.connected) {\n            this.socket.emit(\"cursor-move\", {\n                roomId,\n                userId,\n                position\n            });\n        }\n    }\n    // Add a method to validate if a room exists on the server\n    async validateRoom(roomId) {\n        if (!this.socket || !this.isConnected()) {\n            throw new Error(\"Socket not connected\");\n        }\n        return new Promise((resolve, reject)=>{\n            if (!this.socket) {\n                throw new Error(\"Socket is not initialized\");\n            }\n            this.socket.emit(\"validate-room\", {\n                roomId\n            }, (response)=>{\n                if (response) {\n                    resolve(response);\n                } else {\n                    reject(new Error(\"Failed to validate room\"));\n                }\n            });\n        });\n    }\n}\n// Export the class itself instead of calling getInstance during export\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SocketService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/socketService.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@clerk","vendor-chunks/next","vendor-chunks/react-icons","vendor-chunks/framer-motion","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/motion-dom","vendor-chunks/ws","vendor-chunks/engine.io-client","vendor-chunks/swr","vendor-chunks/socket.io-client","vendor-chunks/react-toastify","vendor-chunks/socket.io-parser","vendor-chunks/follow-redirects","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/debug","vendor-chunks/tslib","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/motion-utils","vendor-chunks/engine.io-parser","vendor-chunks/cookie","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/use-sync-external-store","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/@socket.io","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/dequal","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/es-set-tostringtag","vendor-chunks/lower-case","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/snakecase-keys","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/react-intersection-observer"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CProject%5Crealcode%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProject%5Crealcode%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();