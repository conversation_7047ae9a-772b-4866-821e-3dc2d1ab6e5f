"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_qsharp_qsharp_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/qsharp/qsharp.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/qsharp/qsharp.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/qsharp/qsharp.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  keywords: [\n    \"namespace\",\n    \"open\",\n    \"import\",\n    \"export\",\n    \"as\",\n    \"operation\",\n    \"function\",\n    \"body\",\n    \"adjoint\",\n    \"newtype\",\n    \"struct\",\n    \"controlled\",\n    \"if\",\n    \"elif\",\n    \"else\",\n    \"repeat\",\n    \"until\",\n    \"fixup\",\n    \"for\",\n    \"in\",\n    \"while\",\n    \"return\",\n    \"fail\",\n    \"within\",\n    \"apply\",\n    \"Adjoint\",\n    \"Controlled\",\n    \"Adj\",\n    \"Ctl\",\n    \"is\",\n    \"self\",\n    \"auto\",\n    \"distribute\",\n    \"invert\",\n    \"intrinsic\",\n    \"let\",\n    \"set\",\n    \"w/\",\n    \"new\",\n    \"not\",\n    \"and\",\n    \"or\",\n    \"use\",\n    \"borrow\",\n    \"using\",\n    \"borrowing\",\n    \"mutable\",\n    \"internal\"\n  ],\n  typeKeywords: [\n    \"Unit\",\n    \"Int\",\n    \"BigInt\",\n    \"Double\",\n    \"Bool\",\n    \"String\",\n    \"Qubit\",\n    \"Result\",\n    \"Pauli\",\n    \"Range\"\n  ],\n  invalidKeywords: [\n    \"abstract\",\n    \"base\",\n    \"bool\",\n    \"break\",\n    \"byte\",\n    \"case\",\n    \"catch\",\n    \"char\",\n    \"checked\",\n    \"class\",\n    \"const\",\n    \"continue\",\n    \"decimal\",\n    \"default\",\n    \"delegate\",\n    \"do\",\n    \"double\",\n    \"enum\",\n    \"event\",\n    \"explicit\",\n    \"extern\",\n    \"finally\",\n    \"fixed\",\n    \"float\",\n    \"foreach\",\n    \"goto\",\n    \"implicit\",\n    \"int\",\n    \"interface\",\n    \"lock\",\n    \"long\",\n    \"null\",\n    \"object\",\n    \"operator\",\n    \"out\",\n    \"override\",\n    \"params\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"readonly\",\n    \"ref\",\n    \"sbyte\",\n    \"sealed\",\n    \"short\",\n    \"sizeof\",\n    \"stackalloc\",\n    \"static\",\n    \"string\",\n    \"switch\",\n    \"this\",\n    \"throw\",\n    \"try\",\n    \"typeof\",\n    \"unit\",\n    \"ulong\",\n    \"unchecked\",\n    \"unsafe\",\n    \"ushort\",\n    \"virtual\",\n    \"void\",\n    \"volatile\"\n  ],\n  constants: [\"true\", \"false\", \"PauliI\", \"PauliX\", \"PauliY\", \"PauliZ\", \"One\", \"Zero\"],\n  builtin: [\n    \"X\",\n    \"Y\",\n    \"Z\",\n    \"H\",\n    \"HY\",\n    \"S\",\n    \"T\",\n    \"SWAP\",\n    \"CNOT\",\n    \"CCNOT\",\n    \"MultiX\",\n    \"R\",\n    \"RFrac\",\n    \"Rx\",\n    \"Ry\",\n    \"Rz\",\n    \"R1\",\n    \"R1Frac\",\n    \"Exp\",\n    \"ExpFrac\",\n    \"Measure\",\n    \"M\",\n    \"MultiM\",\n    \"Message\",\n    \"Length\",\n    \"Assert\",\n    \"AssertProb\",\n    \"AssertEqual\"\n  ],\n  operators: [\n    \"and=\",\n    \"<-\",\n    \"->\",\n    \"*\",\n    \"*=\",\n    \"@\",\n    \"!\",\n    \"^\",\n    \"^=\",\n    \":\",\n    \"::\",\n    \".\",\n    \"..\",\n    \"==\",\n    \"...\",\n    \"=\",\n    \"=>\",\n    \">\",\n    \">=\",\n    \"<\",\n    \"<=\",\n    \"-\",\n    \"-=\",\n    \"!=\",\n    \"or=\",\n    \"%\",\n    \"%=\",\n    \"|\",\n    \"+\",\n    \"+=\",\n    \"?\",\n    \"/\",\n    \"/=\",\n    \"&&&\",\n    \"&&&=\",\n    \"^^^\",\n    \"^^^=\",\n    \">>>\",\n    \">>>=\",\n    \"<<<\",\n    \"<<<=\",\n    \"|||\",\n    \"|||=\",\n    \"~~~\",\n    \"_\",\n    \"w/\",\n    \"w/=\"\n  ],\n  namespaceFollows: [\"namespace\", \"open\"],\n  importsFollows: [\"import\"],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%@._]+/,\n  escapes: /\\\\[\\s\\S]/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [\n        /[a-zA-Z_$][\\w$]*/,\n        {\n          cases: {\n            \"@namespaceFollows\": {\n              token: \"keyword.$0\",\n              next: \"@namespace\"\n            },\n            \"@importsFollows\": {\n              token: \"keyword.$0\",\n              next: \"@imports\"\n            },\n            \"@typeKeywords\": \"type\",\n            \"@keywords\": \"keyword\",\n            \"@constants\": \"constant\",\n            \"@builtin\": \"keyword\",\n            \"@invalidKeywords\": \"invalid\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, { cases: { \"@operators\": \"operator\", \"@default\": \"\" } }],\n      // numbers\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      //[/\"([^\"\\\\]|\\\\.)*$/, 'string.invalid' ],  // non-terminated string\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    namespace: [\n      { include: \"@whitespace\" },\n      [/[A-Za-z]\\w*/, \"namespace\"],\n      [/[\\.]/, \"delimiter\"],\n      [\"\", \"\", \"@pop\"]\n    ],\n    imports: [\n      { include: \"@whitespace\" },\n      [/[A-Za-z]\\w*(?=\\.)/, \"namespace\"],\n      [/[A-Za-z]\\w*/, \"identifier\"],\n      [/\\*/, \"wildcard\"],\n      [/[\\.,]/, \"delimiter\"],\n      [\"\", \"\", \"@pop\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/(\\/\\/).*/, \"comment\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/qsharp/qsharp.js\n"));

/***/ })

}]);