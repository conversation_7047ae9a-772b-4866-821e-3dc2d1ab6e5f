// server/sockets/room.js

// Simple in-memory data store for rooms
const rooms = {};

// Initialize a room with default code
export function getRoom(roomId) {
  if (!rooms[roomId]) {
    rooms[roomId] = {
      users: [],
      code: "// Start coding...",
      teacherId: null, // Store the teacher's user ID for persistent role assignment
      editPermissions: {}, // Store edit permissions: { socketId: canEdit }
      studentList: [], // Store detailed student information
      createdAt: new Date().toISOString(),
      lastActivity: new Date().toISOString()
    };
    console.log(`Room ${roomId} created with enhanced permission system`);
  }
  return rooms[roomId];
}

// Log the current state of all rooms (for debugging)
function logRoomsState() {
  console.log('Current rooms state:',
    Object.keys(rooms).map(roomId => ({
      roomId,
      userCount: rooms[roomId].users.length,
      codeLength: rooms[roomId].code.length,
      teacherId: rooms[roomId].teacherId
    }))
  );
}

// Emit the updated list of users in the room with edit permissions
function emitRoomUsersUpdated(io, roomId) {
  const room = rooms[roomId];
  if (room) {
    const users = room.users.map((user) => ({
      username: user.username,
      role: user.role,
      socketId: user.socketId,
      userId: user.userId,
      canEdit: room.editPermissions[user.socketId] || false
    }));
    io.to(roomId).emit("room-users-updated", { users, count: users.length });
  }
}

// Set edit permission for a user
function setEditPermission(roomId, socketId, canEdit) {
  const room = rooms[roomId];
  if (room) {
    room.editPermissions[socketId] = canEdit;
    console.log(`Edit permission for ${socketId} in room ${roomId} set to: ${canEdit}`);
  }
}

// Get edit permission for a user
function getEditPermission(roomId, socketId) {
  const room = rooms[roomId];
  if (room && room.editPermissions.hasOwnProperty(socketId)) {
    return room.editPermissions[socketId];
  }
  // Default: students can't edit, teachers can edit
  return false;
}

// Remove edit permission when user disconnects
export function removeEditPermission(roomId, socketId) {
  const room = rooms[roomId];
  if (room && room.editPermissions.hasOwnProperty(socketId)) {
    delete room.editPermissions[socketId];
    console.log(`Edit permission removed for ${socketId} in room ${roomId}`);
  }
}

// Update student list with permission info
function updateStudentList(roomId) {
  const room = rooms[roomId];
  if (!room) return;

  room.studentList = room.users
    .filter(user => user.role === 'student')
    .map(user => ({
      socketId: user.socketId,
      username: user.username,
      userId: user.userId,
      email: user.email || null,
      canEdit: room.editPermissions[user.socketId] || false,
      joinedAt: user.joinedAt || new Date().toISOString(),
      lastActivity: user.lastActivity || new Date().toISOString()
    }));

  console.log(`Updated student list for room ${roomId}:`, room.studentList.length, 'students');
}

// Grant edit permission to a student
function grantEditPermission(roomId, targetSocketId) {
  setEditPermission(roomId, targetSocketId, true);
  updateStudentList(roomId);
  console.log(`Granted edit permission to ${targetSocketId} in room ${roomId}`);
}

// Revoke edit permission from a student
function revokeEditPermission(roomId, targetSocketId) {
  setEditPermission(roomId, targetSocketId, false);
  updateStudentList(roomId);
  console.log(`Revoked edit permission from ${targetSocketId} in room ${roomId}`);
}

// Get student list for teacher
function getStudentList(roomId) {
  const room = rooms[roomId];
  if (!room) return [];

  updateStudentList(roomId);
  return room.studentList;
}

// Check if user is teacher
function isTeacher(roomId, userId, socketId) {
  const room = rooms[roomId];
  if (!room) return false;

  // Check by userId first (persistent), then by being the first user
  if (room.teacherId === userId) return true;

  // If no teacher set and this is the first user, make them teacher
  if (!room.teacherId && room.users.length === 1) {
    room.teacherId = userId;
    return true;
  }

  return false;
}

// Export permission functions for use in other modules
export { setEditPermission, getEditPermission, grantEditPermission, revokeEditPermission, getStudentList, isTeacher, updateStudentList };

export const registerRoomHandlers = (io, socket) => {
  // Create a new room
  socket.on("create-room", ({ username, roomId: customRoomId, userId }, callback) => {
    try {
      // Use exactly the username provided by the user without any modifications
      // This ensures we use exactly what the user entered on the dashboard
      const validUsername = username;

      // Validate userId
      const validUserId = userId || `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      // Always use the provided room ID (which should be randomly generated on the client)
      const roomId = customRoomId || `${Math.random().toString(36).substring(2, 11)}`;
      console.log(`${validUsername} (${validUserId}) creating room: ${roomId}`);

      // Join the socket.io room
      socket.join(roomId);

      // Initialize room and add user
      const room = getRoom(roomId);

      // Set the teacher ID if not already set (room creator becomes the permanent teacher)
      if (!room.teacherId) {
        room.teacherId = validUserId;
        console.log(`Set teacherId to ${validUserId} for room ${roomId}`);
      }

      // Check if user already exists in the room
      const existingUserIndex = room.users.findIndex((u) => u.userId === validUserId);

      let role;
      if (existingUserIndex === -1) {
        // For create-room, the user is always the teacher (room creator)
        role = "teacher";

        // Add user to the room with the assigned role
        room.users.push({
          socketId: socket.id,
          username: validUsername,
          userId: validUserId,
          role, // Add the role to the user object
        });
      } else {
        // Update existing user's socket ID and ensure they have teacher role
        room.users[existingUserIndex].socketId = socket.id;
        room.users[existingUserIndex].role = "teacher"; // Ensure creator is teacher
        role = "teacher";
      }

      // Store user info on the socket for easy access
      socket.username = validUsername;
      socket.userId = validUserId;
      socket.currentRoomId = roomId;
      socket.role = role; // Store the role on the socket

      console.log(`Set role to ${role} for ${validUsername} (${validUserId}) in room ${roomId}`);
      // Send success response with room ID, username, and role
      callback({ roomId, username: validUsername, role, users: room.users });
      console.log(`Room ${roomId} created by ${validUsername} (${validUserId})`);
      logRoomsState();
    } catch (error) {
      console.error(`Error creating room:`, error);
      callback({ error: `Failed to create room: ${error.message}` });
    }
  })

  // Add a new handler to validate if a room exists
  socket.on("validate-room", ({ roomId }, callback) => {
    if (!roomId) {
      return callback({ exists: false });
    }

    const roomExists = !!rooms[roomId];
    callback({ exists: roomExists });
  });

  // Update the join-room handler to validate room existence
  socket.on("join-room", ({ roomId, username, userId }, callback) => {
    if (!roomId) {
      return callback({ error: "Room ID is required" });
    }

    try {
      const validUsername = username;
      const validUserId = userId || `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      console.log(`${validUsername} (${validUserId}) attempting to join room: ${roomId}`);

      // Check if the room exists
      if (!rooms[roomId]) {
        console.error(`Room ${roomId} does not exist`);
        return callback({ error: "Room does not exist" });
      }

      // Proceed with joining the room
      const room = getRoom(roomId);
      socket.join(roomId);

      const existingUserIndex = room.users.findIndex((u) => u.userId === validUserId);
      let userRole;

      // Determine role based on whether this user is the teacher
      const isTeacher = room.teacherId === validUserId;

      if (existingUserIndex === -1) {
        // New user joining - assign role based on teacher ID
        userRole = isTeacher ? "teacher" : "student";
        room.users.push({
          socketId: socket.id,
          username: validUsername,
          userId: validUserId,
          role: userRole,
        });
        console.log(`New user ${validUsername} (${validUserId}) joined as ${userRole} in room ${roomId}`);
      } else {
        // Existing user reconnecting - assign role based on teacher ID (not stored role)
        userRole = isTeacher ? "teacher" : "student";
        room.users[existingUserIndex].socketId = socket.id;
        room.users[existingUserIndex].role = userRole; // Update role to ensure consistency
        console.log(`Existing user ${validUsername} (${validUserId}) reconnected as ${userRole} in room ${roomId}`);
      }

      socket.username = validUsername;
      socket.userId = validUserId;
      socket.currentRoomId = roomId;
      socket.role = userRole;

      console.log(`${validUsername} (${validUserId}) joined room: ${roomId}`);
      callback({ success: true, users: room.users, role: userRole, username: validUsername });

      // Emit the updated list of users
      emitRoomUsersUpdated(io, roomId);

      // If this is a new user (not a reconnection) and there are other users in the room,
      // request initial code from existing users and sync teacher state
      if (existingUserIndex === -1 && room.users.length > 1) {
        console.log(`Requesting initial code for new user ${validUsername} in room ${roomId}`);
        // Emit to all other users in the room to request initial code
        socket.to(roomId).emit('get-initial-code', {
          requestingUserId: validUserId,
          requestingUsername: validUsername
        });

        // Also emit user-joined event with new user's socket ID to trigger teacher sync
        console.log(`Emitting user-joined event for teacher sync to room ${roomId}`);
        socket.to(roomId).emit('user-joined', {
          newUserSocketId: socket.id,
          newUserName: validUsername,
          newUserId: validUserId,
          allUsers: room.users
        });
      }

      // Send initial edit permission to the new user
      const canEdit = getEditPermission(roomId, socket.id);
      socket.emit('edit-permission', { canEdit });
      console.log(`Sent initial edit permission to ${validUsername}: ${canEdit}`);

      // If this is a student joining, update the student list for teachers
      if (userRole === 'student') {
        const studentList = getStudentList(roomId);
        // Emit updated student list to all teachers in the room
        room.users.filter(u => u.role === 'teacher').forEach(teacher => {
          io.to(teacher.socketId).emit('update-student-list', { students: studentList });
        });
        console.log(`Updated student list sent to teachers in room ${roomId}`);
      }
    } catch (error) {
      console.error("Error in join-room handler:", error);
      callback({ error: `Failed to join room: ${error.message}` });
    }
  })

  // Handle setting edit permissions (teacher only)
  socket.on("set-edit-permission", ({ roomId, targetSocketId, canEdit }) => {
    if (!roomId || !targetSocketId || typeof canEdit !== 'boolean') {
      return console.error(`Invalid set-edit-permission payload:`, { roomId, targetSocketId, canEdit });
    }

    try {
      const room = getRoom(roomId);
      let user = room.users.find(u => u.socketId === socket.id);
      if (!user && socket.userId) {
        user = room.users.find(u => u.userId === socket.userId);
      }

      // Only teachers can set edit permissions
      if (!user || user.role !== 'teacher') {
        console.log(`Non-teacher user ${user?.username || 'unknown'} attempted to set edit permission`);
        return;
      }

      // Find the target user
      const targetUser = room.users.find(u => u.socketId === targetSocketId);
      if (!targetUser) {
        console.log(`Target user with socket ${targetSocketId} not found in room ${roomId}`);
        return;
      }

      // Set the permission
      setEditPermission(roomId, targetSocketId, canEdit);

      // Emit permission change to the target user
      io.to(targetSocketId).emit('edit-permission', { canEdit });

      // Emit updated user list to all users in the room
      emitRoomUsersUpdated(io, roomId);

      console.log(`Teacher ${user.username} set edit permission for ${targetUser.username} to: ${canEdit}`);
    } catch (error) {
      console.error(`Error handling set-edit-permission for room ${roomId}:`, error);
    }
  });

  // Handle grant edit permission (new simplified event)
  socket.on("grant-edit-permission", ({ roomId, targetSocketId }) => {
    if (!roomId || !targetSocketId) {
      return console.error(`Invalid grant-edit-permission payload:`, { roomId, targetSocketId });
    }

    try {
      const room = getRoom(roomId);
      let user = room.users.find(u => u.socketId === socket.id);
      if (!user && socket.userId) {
        user = room.users.find(u => u.userId === socket.userId);
      }

      // Only teachers can grant permissions
      if (!user || user.role !== 'teacher') {
        console.log(`Non-teacher user ${user?.username || 'unknown'} attempted to grant edit permission`);
        return;
      }

      // Grant permission
      grantEditPermission(roomId, targetSocketId);

      // Emit permission update to target student
      io.to(targetSocketId).emit('permission-updated', { canEdit: true });

      // Emit updated student list to teacher
      const studentList = getStudentList(roomId);
      io.to(roomId).emit('update-student-list', { students: studentList });

      console.log(`Teacher ${user.username} granted edit permission to ${targetSocketId}`);
    } catch (error) {
      console.error(`Error handling grant-edit-permission for room ${roomId}:`, error);
    }
  });

  // Handle revoke edit permission
  socket.on("revoke-edit-permission", ({ roomId, targetSocketId }) => {
    if (!roomId || !targetSocketId) {
      return console.error(`Invalid revoke-edit-permission payload:`, { roomId, targetSocketId });
    }

    try {
      const room = getRoom(roomId);
      let user = room.users.find(u => u.socketId === socket.id);
      if (!user && socket.userId) {
        user = room.users.find(u => u.userId === socket.userId);
      }

      // Only teachers can revoke permissions
      if (!user || user.role !== 'teacher') {
        console.log(`Non-teacher user ${user?.username || 'unknown'} attempted to revoke edit permission`);
        return;
      }

      // Revoke permission
      revokeEditPermission(roomId, targetSocketId);

      // Emit permission update to target student
      io.to(targetSocketId).emit('permission-updated', { canEdit: false });

      // Emit updated student list to teacher
      const studentList = getStudentList(roomId);
      io.to(roomId).emit('update-student-list', { students: studentList });

      console.log(`Teacher ${user.username} revoked edit permission from ${targetSocketId}`);
    } catch (error) {
      console.error(`Error handling revoke-edit-permission for room ${roomId}:`, error);
    }
  });

  // Handle code changes
  socket.on("code-change", ({ roomId, code }) => {
    if (!roomId || code === undefined) {
      return console.error(`Invalid roomId or code:`, { roomId, code });
    }

    try {
      // Get the room
      const room = getRoom(roomId);

      // Store the latest code
      room.code = code;

      // Broadcast the code update to all users in the room
      io.in(roomId).emit("code-update", code);
    } catch (error) {
      console.error(`Error handling code-change for room ${roomId}:`, error);
    }
  });

  // Handle initial code response from existing users
  socket.on("send-initial-code", ({ roomId, code, requestingUserId }) => {
    if (!roomId || code === undefined || !requestingUserId) {
      return console.error(`Invalid send-initial-code payload:`, { roomId, code, requestingUserId });
    }

    try {
      console.log(`Sending initial code to user ${requestingUserId} in room ${roomId}, code length: ${code.length}`);

      // Find the requesting user's socket and send them the code
      const room = getRoom(roomId);
      const requestingUser = room.users.find(user => user.userId === requestingUserId);

      if (requestingUser) {
        // Send the initial code only to the requesting user
        io.to(requestingUser.socketId).emit("initial-code-received", { code });
        console.log(`Initial code sent to ${requestingUser.username} (${requestingUserId})`);

        // Update the room's stored code if this is more recent
        room.code = code;
      } else {
        console.log(`Requesting user ${requestingUserId} not found in room ${roomId}`);
      }
    } catch (error) {
      console.error(`Error handling send-initial-code for room ${roomId}:`, error);
    }
  });



  // Handle user disconnect
  socket.on("disconnect", () => {
    const roomId = socket.currentRoomId;
    if (roomId) {
      const room = getRoom(roomId);
      room.users = room.users.filter((user) => user.socketId !== socket.id);

      // Emit updated user list
      emitRoomUsersUpdated(roomId);
    }
  });

  // Handle real-time annotation highlights
  socket.on("highlight-line", ({ roomId, startLine, endLine, comment }) => {
    if (!roomId || typeof startLine !== 'number' || typeof endLine !== 'number') {
      return console.error(`Invalid highlight-line payload:`, { roomId, startLine, endLine, comment });
    }
    try {
      // Broadcast to all other users in the room
      socket.to(roomId).emit("highlight-line", { startLine, endLine, comment });
      console.log(`Broadcasted highlight-line to room ${roomId}: lines ${startLine}-${endLine}${comment ? ' with comment' : ''}`);
    } catch (error) {
      console.error(`Error handling highlight-line for room ${roomId}:`, error);
    }
  });

  // Handle teacher text selection highlighting
  socket.on("teacher-selection", ({ roomId, selection }) => {
    if (!roomId || !selection) {
      return console.error(`Invalid teacher-selection payload:`, { roomId, selection });
    }

    try {
      // Get the room to verify the user is a teacher
      const room = getRoom(roomId);
      // Try to find user by socketId, fallback to userId if not found
      let user = room.users.find(u => u.socketId === socket.id);
      if (!user && socket.userId) {
        user = room.users.find(u => u.userId === socket.userId);
      }
      if (!user || user.role !== 'teacher') {
        console.log(`Non-teacher user ${user?.username || 'unknown'} attempted to send teacher selection`);
        return;
      }
      // Broadcast teacher selection to all students in the room
      socket.to(roomId).emit("teacher-selection", {
        selection,
        teacherName: user.username,
        teacherId: user.userId
      });
      console.log(`Teacher ${user.username} highlighted selection in room ${roomId}:`, selection);
    } catch (error) {
      console.error(`Error handling teacher-selection for room ${roomId}:`, error);
    }
  });

  // Handle clearing teacher selection
  socket.on("clear-teacher-selection", ({ roomId }) => {
    if (!roomId) {
      return console.error(`Invalid clear-teacher-selection payload:`, { roomId });
    }
    try {
      const room = getRoom(roomId);
      let user = room.users.find(u => u.socketId === socket.id);
      if (!user && socket.userId) {
        user = room.users.find(u => u.userId === socket.userId);
      }
      if (!user || user.role !== 'teacher') {
        console.log(`Non-teacher user ${user?.username || 'unknown'} attempted to clear teacher selection`);
        return;
      }
      socket.to(roomId).emit("clear-teacher-selection", {
        teacherName: user.username,
        teacherId: user.userId
      });
      console.log(`Teacher ${user.username} cleared selection in room ${roomId}`);
    } catch (error) {
      console.error(`Error handling clear-teacher-selection for room ${roomId}:`, error);
    }
  });

  // Handle teacher cursor position updates
  socket.on("teacher-cursor-position", ({ roomId, position }) => {
    if (!roomId || !position) {
      return console.error(`Invalid teacher-cursor-position payload:`, { roomId, position });
    }
    try {
      const room = getRoom(roomId);
      let user = room.users.find(u => u.socketId === socket.id);
      if (!user && socket.userId) {
        user = room.users.find(u => u.userId === socket.userId);
      }
      if (!user || user.role !== 'teacher') {
        console.log(`Non-teacher user ${user?.username || 'unknown'} attempted to send teacher cursor position`);
        return;
      }
      socket.to(roomId).emit("teacher-cursor-position", {
        position,
        teacherName: user.username,
        teacherId: user.userId
      });
    } catch (error) {
      console.error(`Error handling teacher-cursor-position for room ${roomId}:`, error);
    }
  });

  // Handle teacher text selection highlighting (enhanced version)
  socket.on("teacher-text-highlight", ({ roomId, selection }) => {
    if (!roomId || !selection) {
      return console.error(`Invalid teacher-text-highlight payload:`, { roomId, selection });
    }
    try {
      const room = getRoom(roomId);
      let user = room.users.find(u => u.socketId === socket.id);
      if (!user && socket.userId) {
        user = room.users.find(u => u.userId === socket.userId);
      }
      if (!user || user.role !== 'teacher') {
        console.log(`Non-teacher user ${user?.username || 'unknown'} attempted to send teacher text highlight`);
        return;
      }
      socket.to(roomId).emit("teacher-text-highlight", {
        selection,
        teacherName: user.username,
        teacherId: user.userId
      });
      console.log(`Teacher ${user.username} highlighted text in room ${roomId}:`, selection);
    } catch (error) {
      console.error(`Error handling teacher-text-highlight for room ${roomId}:`, error);
    }
  });

  // Handle clearing teacher text highlight
  socket.on("clear-teacher-text-highlight", ({ roomId }) => {
    if (!roomId) {
      return console.error(`Invalid clear-teacher-text-highlight payload:`, { roomId });
    }
    try {
      const room = getRoom(roomId);
      let user = room.users.find(u => u.socketId === socket.id);
      if (!user && socket.userId) {
        user = room.users.find(u => u.userId === socket.userId);
      }
      if (!user || user.role !== 'teacher') {
        console.log(`Non-teacher user ${user?.username || 'unknown'} attempted to clear teacher text highlight`);
        return;
      }
      socket.to(roomId).emit("clear-teacher-text-highlight", {
        teacherName: user.username,
        teacherId: user.userId
      });
      console.log(`Teacher ${user.username} cleared text highlight in room ${roomId}`);
    } catch (error) {
      console.error(`Error handling clear-teacher-text-highlight for room ${roomId}:`, error);
    }
  });

  // Handle syncing current code to a specific user
  socket.on("sync-code", ({ roomId, code, targetSocketId }) => {
    if (!roomId || code === undefined || !targetSocketId) {
      return console.error(`Invalid sync-code payload:`, { roomId, code, targetSocketId });
    }
    try {
      console.log(`Syncing code to user ${targetSocketId} in room ${roomId}, code length: ${code.length}`);
      // Send code directly to the target socket
      io.to(targetSocketId).emit("sync-code", { code });
    } catch (error) {
      console.error(`Error handling sync-code for room ${roomId}:`, error);
    }
  });

  // Handle syncing current teacher selection to a specific user
  socket.on("sync-teacher-selection", ({ roomId, selection, targetSocketId }) => {
    if (!roomId || !selection || !targetSocketId) {
      return console.error(`Invalid sync-teacher-selection payload:`, { roomId, selection, targetSocketId });
    }
    try {
      const room = getRoom(roomId);
      let user = room.users.find(u => u.socketId === socket.id);
      if (!user && socket.userId) {
        user = room.users.find(u => u.userId === socket.userId);
      }
      if (!user || user.role !== 'teacher') {
        console.log(`Non-teacher user ${user?.username || 'unknown'} attempted to sync teacher selection`);
        return;
      }
      console.log(`Syncing teacher selection to user ${targetSocketId} in room ${roomId}`);
      // Send selection directly to the target socket
      io.to(targetSocketId).emit("sync-teacher-selection", {
        selection,
        teacherName: user.username,
        teacherId: user.userId
      });
    } catch (error) {
      console.error(`Error handling sync-teacher-selection for room ${roomId}:`, error);
    }
  });

  // Handle syncing current teacher cursor to a specific user
  socket.on("sync-teacher-cursor", ({ roomId, position, targetSocketId }) => {
    if (!roomId || !position || !targetSocketId) {
      return console.error(`Invalid sync-teacher-cursor payload:`, { roomId, position, targetSocketId });
    }
    try {
      const room = getRoom(roomId);
      let user = room.users.find(u => u.socketId === socket.id);
      if (!user && socket.userId) {
        user = room.users.find(u => u.userId === socket.userId);
      }
      if (!user || user.role !== 'teacher') {
        console.log(`Non-teacher user ${user?.username || 'unknown'} attempted to sync teacher cursor`);
        return;
      }
      console.log(`Syncing teacher cursor to user ${targetSocketId} in room ${roomId}`);
      // Send cursor position directly to the target socket
      io.to(targetSocketId).emit("sync-teacher-cursor", {
        position,
        teacherName: user.username,
        teacherId: user.userId
      });
    } catch (error) {
      console.error(`Error handling sync-teacher-cursor for room ${roomId}:`, error);
    }
  });

  // Handle typing notifications
  socket.on("typing", ({ roomId, username, userId }) => {
    if (!roomId) return;

    try {
      // Get the room
      const room = rooms[roomId];
      if (!room) return;

      // Use exactly the username provided by the user without any modifications
      // This ensures we use exactly what the user entered on the dashboard
      let typingUser = { username: username, userId: userId || null };

      // First try to find the user by userId
      if (userId) {
        const user = room.users.find(u => u.userId === userId);
        if (user) {
          typingUser = { username: user.username, userId: user.userId };
          console.log(`Found user by userId: ${typingUser.username} (${typingUser.userId})`);
        }
      }
      // Then try by socket.userId
      else if (socket.userId) {
        const user = room.users.find(u => u.userId === socket.userId);
        if (user) {
          typingUser = { username: user.username, userId: user.userId };
          console.log(`Found user by socket.userId: ${typingUser.username} (${typingUser.userId})`);
        }
      }
      // Then try by socketId
      else {
        const user = room.users.find(u => u.socketId === socket.id);
        if (user) {
          typingUser = { username: user.username, userId: user.userId };
          console.log(`Found user by socketId: ${typingUser.username} (${typingUser.userId})`);
        }
      }

      console.log(`${typingUser.username} is typing in room ${roomId}`);

      // Broadcast typing notification to all other clients in the room
      socket.to(roomId).emit("user-typing", typingUser);
    } catch (error) {
      console.error(`Error handling typing notification:`, error);
    }
  })

  // Handle cursor movement
  socket.on("cursor-move", ({ roomId, userId, position }) => {
    if (!roomId || !userId || !position) return;

    // Broadcast the cursor position to other users in the room
    socket.to(roomId).emit("cursor-move", { userId, position });
    console.log(`Cursor move from user ${userId} in room ${roomId}:`, position);
  });

  // Handle user leaving a room
  socket.on("leave-room", (roomId) => {
    if (!roomId) return;

    try {
      // Get the room
      const room = rooms[roomId];
      if (!room) return;

      // Find the user by userId if available, otherwise by socketId
      let userIndex = -1;
      if (socket.userId) {
        userIndex = room.users.findIndex(u => u.userId === socket.userId);
      }

      // Fallback to socketId if userId not found
      if (userIndex === -1) {
        userIndex = room.users.findIndex(u => u.socketId === socket.id);
      }

      if (userIndex === -1) return;

      const user = room.users[userIndex];
      console.log(`${user.username} (${user.userId}) leaving room ${roomId}`);

      // Remove the user from the room
      room.users.splice(userIndex, 1);

      // Leave the socket.io room
      socket.leave(roomId);

      // Clear room info from socket
      if (socket.currentRoomId === roomId) {
        socket.currentRoomId = null;
      }

      // Notify remaining users
      io.to(roomId).emit("user-left", room.users);

      // Emit the updated list of users
      emitRoomUsersUpdated(io, roomId);

      // Clean up empty rooms
      if (room.users.length === 0) {
        console.log(`Room ${roomId} is now empty, cleaning up`);
        delete rooms[roomId];
      }

      logRoomsState();
    } catch (error) {
      console.error(`Error leaving room:`, error);
    }
  })

  // Handle disconnections
  socket.on("disconnect", () => {
    const userId = socket.userId;
    const username = socket.username;
    console.log(`User disconnected: ${username || 'Unknown'} (${userId || socket.id})`);

    try {
      // Find all rooms the user was in
      Object.keys(rooms).forEach(roomId => {
        const room = rooms[roomId];

        // Find user by userId if available, otherwise by socketId
        let userIndex = -1;
        if (userId) {
          userIndex = room.users.findIndex(u => u.userId === userId);
        }

        // Fallback to socketId if userId not found
        if (userIndex === -1) {
          userIndex = room.users.findIndex(u => u.socketId === socket.id);
        }

        if (userIndex !== -1) {
          const user = room.users[userIndex];
          console.log(`${user.username} (${user.userId}) removed from room ${roomId} due to disconnect`);

          // Remove the user from the room
          room.users.splice(userIndex, 1);

          // Remove edit permission for this user
          removeEditPermission(roomId, socket.id);

          // Notify remaining users
          io.to(roomId).emit("user-left", room.users);

          // Emit the updated list of users
          emitRoomUsersUpdated(io, roomId);

          // Clean up empty rooms
          if (room.users.length === 0) {
            console.log(`Room ${roomId} is now empty, cleaning up`);
            delete rooms[roomId];
          }
        }
      });



      logRoomsState();
    } catch (error) {
      console.error("Error in disconnect handler:", error);
    }
  })
}

