import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import "./grid-pattern.css";
import { Clerk<PERSON>rovider } from "@clerk/nextjs";
import { AuthProvider } from "@/context/AuthContext"
import { SocketProvider } from "@/context/SocketContext";
import Navbar from "@/components/Navbar"
import "react-toastify/dist/ReactToastify.css"
import { ToastContainer } from "react-toastify"
import ClientAnimatePresence from "@/components/ClientAnimatePresence";
import { ThemeProvider } from "@/context/ThemeContext"

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "RealCode | Realtime Code Collaboration",
  description: "Realtime collaborative coding platform",
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className={`${geistSans.variable} ${geistMono.variable}`}>
      <body>
        <ClerkProvider>
          <ThemeProvider>
            <AuthProvider>
              <SocketProvider>
                <Navbar />
                <ClientAnimatePresence>
                  <main key="main-content">{children}</main>
                </ClientAnimatePresence>
                <ToastContainer
                  position="bottom-right"
                  autoClose={3000}
                  hideProgressBar={false}
                  newestOnTop
                  closeOnClick
                  rtl={false}
                  pauseOnFocusLoss
                  draggable
                  pauseOnHover
                  theme="colored"
                />
              </SocketProvider>
            </AuthProvider>
          </ThemeProvider>
        </ClerkProvider>
      </body>
    </html>
  )
}
