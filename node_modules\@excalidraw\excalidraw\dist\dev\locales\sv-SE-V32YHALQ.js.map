{"version": 3, "sources": ["../../../locales/sv-SE.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Klistra in\",\n    \"pasteAsPlaintext\": \"Klistra som oformaterad text\",\n    \"pasteCharts\": \"Klistra in diagram\",\n    \"selectAll\": \"Markera alla\",\n    \"multiSelect\": \"<PERSON>ägg till element till markering\",\n    \"moveCanvas\": \"Flytta canvas\",\n    \"cut\": \"<PERSON>lipp ut\",\n    \"copy\": \"<PERSON><PERSON>ra\",\n    \"copyAsPng\": \"Kopiera till urklipp som PNG\",\n    \"copyAsSvg\": \"Kopiera till urklipp som SVG\",\n    \"copyText\": \"Kopiera till urklipp som text\",\n    \"copySource\": \"Kopiera källa till urklipp\",\n    \"convertToCode\": \"Konvertera till kod\",\n    \"bringForward\": \"Flytta framåt\",\n    \"sendToBack\": \"Flytta underst\",\n    \"bringToFront\": \"Flytta främst\",\n    \"sendBackward\": \"Ski<PERSON>a bakåt\",\n    \"delete\": \"Ta bort\",\n    \"copyStyles\": \"Kopiera stil\",\n    \"pasteStyles\": \"Klistra in stil\",\n    \"stroke\": \"Linje\",\n    \"background\": \"Bakgrund\",\n    \"fill\": \"Fyllnad\",\n    \"strokeWidth\": \"Linjebredd\",\n    \"strokeStyle\": \"Linjestil\",\n    \"strokeStyle_solid\": \"Solid\",\n    \"strokeStyle_dashed\": \"Streckad\",\n    \"strokeStyle_dotted\": \"Punktad\",\n    \"sloppiness\": \"Slarvighet\",\n    \"opacity\": \"Genomskinlighet\",\n    \"textAlign\": \"Textjustering\",\n    \"edges\": \"Kanter\",\n    \"sharp\": \"Skarp\",\n    \"round\": \"Rund\",\n    \"arrowheads\": \"Pilhuvuden\",\n    \"arrowhead_none\": \"Inga\",\n    \"arrowhead_arrow\": \"Pil\",\n    \"arrowhead_bar\": \"Stolpe\",\n    \"arrowhead_circle\": \"Cirkel\",\n    \"arrowhead_circle_outline\": \"Cirkel (kontur)\",\n    \"arrowhead_triangle\": \"Triangel\",\n    \"arrowhead_triangle_outline\": \"Triangel (kontur)\",\n    \"arrowhead_diamond\": \"Diamant\",\n    \"arrowhead_diamond_outline\": \"Diamant (kontur)\",\n    \"fontSize\": \"Teckenstorlek\",\n    \"fontFamily\": \"Teckensnitt\",\n    \"addWatermark\": \"Lägg till \\\"Skapad med Excalidraw\\\"\",\n    \"handDrawn\": \"Handritad\",\n    \"normal\": \"Normal\",\n    \"code\": \"Kod\",\n    \"small\": \"Liten\",\n    \"medium\": \"Medium\",\n    \"large\": \"Stor\",\n    \"veryLarge\": \"Mycket stor\",\n    \"solid\": \"Solid\",\n    \"hachure\": \"Skraffering\",\n    \"zigzag\": \"Sicksack\",\n    \"crossHatch\": \"Skraffera med kors\",\n    \"thin\": \"Tunn\",\n    \"bold\": \"Fet\",\n    \"left\": \"Vänster\",\n    \"center\": \"Centrera\",\n    \"right\": \"Höger\",\n    \"extraBold\": \"Extra fet\",\n    \"architect\": \"Arkitekt\",\n    \"artist\": \"Artist\",\n    \"cartoonist\": \"Serietecknare\",\n    \"fileTitle\": \"Filnamn\",\n    \"colorPicker\": \"Färgväljare\",\n    \"canvasColors\": \"Används på canvas\",\n    \"canvasBackground\": \"Canvas-bakgrund\",\n    \"drawingCanvas\": \"Ritar canvas\",\n    \"layers\": \"Lager\",\n    \"actions\": \"Åtgärder\",\n    \"language\": \"Språk\",\n    \"liveCollaboration\": \"Samarbeta live...\",\n    \"duplicateSelection\": \"Duplicera\",\n    \"untitled\": \"Namnlös\",\n    \"name\": \"Namn\",\n    \"yourName\": \"Ditt namn\",\n    \"madeWithExcalidraw\": \"Skapad med Excalidraw\",\n    \"group\": \"Gruppera markering\",\n    \"ungroup\": \"Avgruppera markering\",\n    \"collaborators\": \"Medarbetare\",\n    \"showGrid\": \"Visa rutnät\",\n    \"addToLibrary\": \"Lägg till i biblioteket\",\n    \"removeFromLibrary\": \"Ta bort från bibliotek\",\n    \"libraryLoadingMessage\": \"Laddar bibliotek…\",\n    \"libraries\": \"Bläddra i bibliotek\",\n    \"loadingScene\": \"Laddar skiss…\",\n    \"align\": \"Justera\",\n    \"alignTop\": \"Justera överkant\",\n    \"alignBottom\": \"Justera underkant\",\n    \"alignLeft\": \"Justera vänster\",\n    \"alignRight\": \"Justera höger\",\n    \"centerVertically\": \"Centrera vertikalt\",\n    \"centerHorizontally\": \"Centrera horisontellt\",\n    \"distributeHorizontally\": \"Fördela horisontellt\",\n    \"distributeVertically\": \"Fördela vertikalt\",\n    \"flipHorizontal\": \"Vänd horisontellt\",\n    \"flipVertical\": \"Vänd vertikalt\",\n    \"viewMode\": \"Visningsläge\",\n    \"share\": \"Dela\",\n    \"showStroke\": \"Visa färgväljare för linjefärg\",\n    \"showBackground\": \"Visa färgväljare för bakgrundsfärg\",\n    \"toggleTheme\": \"Växla tema\",\n    \"personalLib\": \"Personligt bibliotek\",\n    \"excalidrawLib\": \"Excalidraw bibliotek\",\n    \"decreaseFontSize\": \"Minska fontstorleken\",\n    \"increaseFontSize\": \"Öka fontstorleken\",\n    \"unbindText\": \"Koppla bort text\",\n    \"bindText\": \"Bind texten till behållaren\",\n    \"createContainerFromText\": \"Radbryt text i en avgränsad yta\",\n    \"link\": {\n      \"edit\": \"Redigera länk\",\n      \"editEmbed\": \"Redigera länk & bädda in\",\n      \"create\": \"Skapa länk\",\n      \"createEmbed\": \"Skapa länk & bädda in\",\n      \"label\": \"Länk\",\n      \"labelEmbed\": \"Länka & bädda in\",\n      \"empty\": \"Ingen länk är angiven\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Redigera linje\",\n      \"exit\": \"Avsluta linjeredigerare\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Lås\",\n      \"unlock\": \"Lås upp\",\n      \"lockAll\": \"Lås alla\",\n      \"unlockAll\": \"Lås upp alla\"\n    },\n    \"statusPublished\": \"Publicerad\",\n    \"sidebarLock\": \"Håll sidofältet öppet\",\n    \"selectAllElementsInFrame\": \"Markera alla element i rutan\",\n    \"removeAllElementsFromFrame\": \"Ta bort alla element från rutan\",\n    \"eyeDropper\": \"Välj färg från canvas\",\n    \"textToDiagram\": \"Text till diagram\",\n    \"prompt\": \"Fråga\"\n  },\n  \"library\": {\n    \"noItems\": \"Inga objekt tillagda ännu...\",\n    \"hint_emptyLibrary\": \"Välj ett objekt på canvasen för att lägga till det här, eller installera ett bibliotek från det publika arkivet, nedan.\",\n    \"hint_emptyPrivateLibrary\": \"Välj ett objekt på canvasen för att lägga till det här.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Återställ canvasen\",\n    \"exportJSON\": \"Exportera till fil\",\n    \"exportImage\": \"Exportera bild...\",\n    \"export\": \"Spara till...\",\n    \"copyToClipboard\": \"Kopiera till urklipp\",\n    \"save\": \"Spara till aktuell fil\",\n    \"saveAs\": \"Spara som\",\n    \"load\": \"Öppna\",\n    \"getShareableLink\": \"Hämta delbar länk\",\n    \"close\": \"Stäng\",\n    \"selectLanguage\": \"Välj språk\",\n    \"scrollBackToContent\": \"Bläddra tillbaka till innehållet\",\n    \"zoomIn\": \"Zooma in\",\n    \"zoomOut\": \"Zooma ut\",\n    \"resetZoom\": \"Återställ zoom\",\n    \"menu\": \"Meny\",\n    \"done\": \"Klart\",\n    \"edit\": \"Redigera\",\n    \"undo\": \"Ångra\",\n    \"redo\": \"Gör om\",\n    \"resetLibrary\": \"Återställ bibliotek\",\n    \"createNewRoom\": \"Skapa ett nytt rum\",\n    \"fullScreen\": \"Helskärm\",\n    \"darkMode\": \"Mörkt läge\",\n    \"lightMode\": \"Ljust läge\",\n    \"zenMode\": \"Zen-läge\",\n    \"objectsSnapMode\": \"Fäst mot objekt\",\n    \"exitZenMode\": \"Gå ur zen-läge\",\n    \"cancel\": \"Avbryt\",\n    \"clear\": \"Rensa\",\n    \"remove\": \"Ta bort\",\n    \"embed\": \"Växla inbäddning\",\n    \"publishLibrary\": \"Publicera\",\n    \"submit\": \"Skicka\",\n    \"confirm\": \"Bekräfta\",\n    \"embeddableInteractionButton\": \"Klicka för att interagera\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Detta rensar hela canvasen. Är du säker?\",\n    \"couldNotCreateShareableLink\": \"Kunde inte skapa delbar länk.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Kunde inte skapa delbar länk: skissen är för stor\",\n    \"couldNotLoadInvalidFile\": \"Kunde inte ladda ogiltig fil\",\n    \"importBackendFailed\": \"Importering från backend misslyckades.\",\n    \"cannotExportEmptyCanvas\": \"Kan inte exportera tom canvas.\",\n    \"couldNotCopyToClipboard\": \"Kunde inte kopiera till urklipp.\",\n    \"decryptFailed\": \"Kunde inte avkryptera data.\",\n    \"uploadedSecurly\": \"Uppladdning har säkrats med kryptering från ände till ände. vilket innebär att Excalidraw server och tredje part inte kan läsa innehållet.\",\n    \"loadSceneOverridePrompt\": \"Laddning av extern skiss kommer att ersätta ditt befintliga innehåll. Vill du fortsätta?\",\n    \"collabStopOverridePrompt\": \"Att stoppa sessionen kommer att skriva över din föregående, lokalt lagrade skiss. Är du säker?\\n\\n(Om du vill behålla din lokala skiss, stäng bara webbläsarfliken istället.)\",\n    \"errorAddingToLibrary\": \"Kunde inte lägga till objekt i biblioteket\",\n    \"errorRemovingFromLibrary\": \"Kunde inte ta bort objekt från biblioteket\",\n    \"confirmAddLibrary\": \"Detta kommer att lägga till {{numShapes}} form(er) till ditt bibliotek. Är du säker?\",\n    \"imageDoesNotContainScene\": \"Den här bilden verkar inte innehålla någon skissdata. Har du aktiverat inbäddning av skiss under export?\",\n    \"cannotRestoreFromImage\": \"Skiss kunde inte återställas från denna bildfil\",\n    \"invalidSceneUrl\": \"Det gick inte att importera skiss från den angivna webbadressen. Antingen har den fel format, eller så innehåller den ingen giltig Excalidraw JSON data.\",\n    \"resetLibrary\": \"Detta kommer att rensa ditt bibliotek. Är du säker?\",\n    \"removeItemsFromsLibrary\": \"Ta bort {{count}} objekt från biblioteket?\",\n    \"invalidEncryptionKey\": \"Krypteringsnyckeln måste vara 22 tecken. Livesamarbetet är inaktiverat.\",\n    \"collabOfflineWarning\": \"Ingen internetanslutning tillgänglig.\\nDina ändringar kommer inte att sparas!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Filtypen stöds inte.\",\n    \"imageInsertError\": \"Kunde inte infoga bild. Försök igen senare...\",\n    \"fileTooBig\": \"Filen är för stor. Maximal tillåten storlek är {{maxSize}}.\",\n    \"svgImageInsertError\": \"Kunde inte infoga SVG-bild. SVG-koden ser ogiltig ut.\",\n    \"failedToFetchImage\": \"Kunde inte hämta bilden.\",\n    \"invalidSVGString\": \"Ogiltig SVG.\",\n    \"cannotResolveCollabServer\": \"Det gick inte att ansluta till samarbets-servern. Ladda om sidan och försök igen.\",\n    \"importLibraryError\": \"Kunde inte ladda bibliotek\",\n    \"collabSaveFailed\": \"Det gick inte att spara i backend-databasen. Om problemen kvarstår bör du spara filen lokalt för att se till att du inte förlorar ditt arbete.\",\n    \"collabSaveFailed_sizeExceeded\": \"Det gick inte att spara till backend-databasen, whiteboarden verkar vara för stor. Du bör spara filen lokalt för att du inte ska förlora ditt arbete.\",\n    \"imageToolNotSupported\": \"Bilder är inaktiverade.\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Det ser ut som om du använder Brave-webbläsaren med <bold>Aggressivt Blockera fingeravtryck</bold> inställningen aktiverad.\",\n      \"line2\": \"Detta kan resultera i trasiga <bold>Textelement</bold> i dina ritningar.\",\n      \"line3\": \"Vi rekommenderar starkt att du inaktiverar den här inställningen. Du kan följa <link>dessa steg</link> för att inaktivera den.\",\n      \"line4\": \"Om inaktivering av denna inställning inte åtgärdar visningen av textelement, öppna ett <issueLink>ärende</issueLink> på vår GitHub, eller skriv till oss på <discordLink>Discord</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"Inbäddbara element kan inte läggas till i biblioteket.\",\n      \"iframe\": \"IFrame-element kan inte läggas till i biblioteket.\",\n      \"image\": \"Stöd för att lägga till bilder till biblioteket kommer snart!\"\n    },\n    \"asyncPasteFailedOnRead\": \"Kunde inte klistra in (kunde inte läsa från urklipp).\",\n    \"asyncPasteFailedOnParse\": \"Kunde inte klistra in.\",\n    \"copyToSystemClipboardFailed\": \"Kunde inte kopiera till urklipp.\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Markering\",\n    \"image\": \"Infoga bild\",\n    \"rectangle\": \"Rektangel\",\n    \"diamond\": \"Diamant\",\n    \"ellipse\": \"Ellips\",\n    \"arrow\": \"Pil\",\n    \"line\": \"Linje\",\n    \"freedraw\": \"Rita\",\n    \"text\": \"Text\",\n    \"library\": \"Bibliotek\",\n    \"lock\": \"Håll valt verktyg aktivt efter ritande\",\n    \"penMode\": \"Pennläge - förhindra touch\",\n    \"link\": \"Lägg till / Uppdatera länk för en vald form\",\n    \"eraser\": \"Radergummi\",\n    \"frame\": \"Rutverktyg\",\n    \"magicframe\": \"Trådram till kod\",\n    \"embeddable\": \"Bädda in (web)\",\n    \"laser\": \"Laserpekare\",\n    \"hand\": \"Hand (panoreringsverktyg)\",\n    \"extraTools\": \"Fler verktyg\",\n    \"mermaidToExcalidraw\": \"Mermaid till Excalidraw\",\n    \"magicSettings\": \"AI-inställningar\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Canvas-åtgärder\",\n    \"selectedShapeActions\": \"Valda formåtgärder\",\n    \"shapes\": \"Former\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"För att flytta whiteboarden, håll mushjulet eller mellanslagstangenten medan du drar eller använd handverktyget\",\n    \"linearElement\": \"Klicka för att starta flera punkter, dra för en linje\",\n    \"freeDraw\": \"Klicka och dra, släpp när du är klar\",\n    \"text\": \"Tips: du kan också lägga till text genom att dubbelklicka var som helst med markeringsverktyget\",\n    \"embeddable\": \"Klicka-dra för att skapa en webbplats-inbäddning\",\n    \"text_selected\": \"Dubbelklicka eller tryck ENTER för att redigera text\",\n    \"text_editing\": \"Tryck Escape eller CtrlOrCmd + ENTER för att slutföra redigeringen\",\n    \"linearElementMulti\": \"Klicka på sista punkten eller tryck Escape eller Enter för att avsluta\",\n    \"lockAngle\": \"Du kan begränsa vinkeln genom att hålla SKIFT\",\n    \"resize\": \"Du kan behålla proportioner genom att hålla SHIFT medan du ändrar storlek,\\nhåller du ALT ändras storlek relativt mitten\",\n    \"resizeImage\": \"Du kan ändra storlek fritt genom att hålla SHIFT,\\nhåll ALT för att ändra storlek från mitten\",\n    \"rotate\": \"Du kan begränsa vinklar genom att hålla SHIFT medan du roterar\",\n    \"lineEditor_info\": \"Håll Ctrl/Cmd och dubbelklicka eller tryck på Ctrl/Cmd + Enter för att redigera punkter\",\n    \"lineEditor_pointSelected\": \"Tryck på Ta bort för att ta bort punkt(er), Ctrl + D eller Cmd + D för att duplicera, eller dra för att flytta\",\n    \"lineEditor_nothingSelected\": \"Välj en punkt att redigera (håll SHIFT för att välja flera),\\neller håll ned Alt och klicka för att lägga till nya punkter\",\n    \"placeImage\": \"Klicka för att placera bilden, eller klicka och dra för att ställa in dess storlek manuellt\",\n    \"publishLibrary\": \"Publicera ditt eget bibliotek\",\n    \"bindTextToElement\": \"Tryck på Enter för att lägga till text\",\n    \"deepBoxSelect\": \"Håll Ctrl eller Cmd för att djupvälja, och för att förhindra att dra\",\n    \"eraserRevert\": \"Håll Alt för att återställa de element som är markerade för borttagning\",\n    \"firefox_clipboard_write\": \"Denna funktion kan sannolikt aktiveras genom att ställa in \\\"dom.events.asyncClipboard.clipboardItem\\\" flaggan till \\\"true\\\". För att ändra webbläsarens flaggor i Firefox, besök \\\"about:config\\\" sidan.\",\n    \"disableSnapping\": \"Håll Ctrl eller Cmd för att inaktivera fästning\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Kan inte visa förhandsgranskning\",\n    \"canvasTooBig\": \"Canvasen kan vara för stor.\",\n    \"canvasTooBigTip\": \"Tips: prova att flytta de mest avlägsna elementen lite närmare varandra.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Ett fel uppstod. Försök <button>med att läsa in sidan på nytt.</button>\",\n    \"clearCanvasMessage\": \"Om omladdning inte fungerar, försök <button>rensa canvasen.</button>\",\n    \"clearCanvasCaveat\": \" Detta kommer att leda till förlust av arbete \",\n    \"trackedToSentry\": \"Felet med identifieraren {{eventId}} spårades på vårt system.\",\n    \"openIssueMessage\": \"Vi var mycket försiktiga med att inte inkludera din skissinformation om felet. Om din skiss inte är privat, vänligen överväga att följa upp på vår <button>buggspårare.</button> Vänligen inkludera information nedan genom att kopiera och klistra in i GitHub-problemet.\",\n    \"sceneContent\": \"Skissinnehåll:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Du kan bjuda in personer till din nuvarande skiss för att samarbeta med dig.\",\n    \"desc_privacy\": \"Oroa dig inte, sessionen använder kryptering från ände till ände, så vad du än ritar kommer att förbli privat. Inte ens vår server kommer att kunna se vad du skissar.\",\n    \"button_startSession\": \"Starta sessionen\",\n    \"button_stopSession\": \"Stoppa session\",\n    \"desc_inProgressIntro\": \"Nu pågår en live-samarbetssession.\",\n    \"desc_shareLink\": \"Dela denna länk med någon du vill samarbeta med:\",\n    \"desc_exitSession\": \"Att avbryta sessionen kommer att koppla bort dig från rummet, men du kommer att kunna fortsätta arbeta med skissen, lokalt. Observera att detta inte påverkar andra människor, och de kommer fortfarande att kunna samarbeta på deras version.\",\n    \"shareTitle\": \"Delta i en live-samarbetssession på Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Fel\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Spara till disk\",\n    \"disk_details\": \"Exportera skissdata till en fil som du kan importera från senare.\",\n    \"disk_button\": \"Spara till fil\",\n    \"link_title\": \"Delbar länk\",\n    \"link_details\": \"Exportera som en skrivskyddad länk.\",\n    \"link_button\": \"Exportera till länk\",\n    \"excalidrawplus_description\": \"Spara skissen till din Excalidraw+ arbetsyta.\",\n    \"excalidrawplus_button\": \"Exportera\",\n    \"excalidrawplus_exportError\": \"Det gick inte att exportera till Excalidraw+ just nu...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Läs vår blogg\",\n    \"click\": \"klicka\",\n    \"deepSelect\": \"Djupval\",\n    \"deepBoxSelect\": \"Djupval inom boxen, och förhindra att dra\",\n    \"curvedArrow\": \"Böjd pil\",\n    \"curvedLine\": \"Böjd linje\",\n    \"documentation\": \"Dokumentation\",\n    \"doubleClick\": \"dubbelklicka\",\n    \"drag\": \"dra\",\n    \"editor\": \"Redigerare\",\n    \"editLineArrowPoints\": \"Redigera linje-/pilpunkter\",\n    \"editText\": \"Redigera text / lägg till etikett\",\n    \"github\": \"Hittat ett problem? Rapportera\",\n    \"howto\": \"Följ våra guider\",\n    \"or\": \"eller\",\n    \"preventBinding\": \"Förhindra pilbindning\",\n    \"tools\": \"Verktyg\",\n    \"shortcuts\": \"Tangentbordsgenvägar\",\n    \"textFinish\": \"Slutför redigering (text)\",\n    \"textNewLine\": \"Lägg till ny rad (text)\",\n    \"title\": \"Hjälp\",\n    \"view\": \"Visa\",\n    \"zoomToFit\": \"Zooma för att rymma alla element\",\n    \"zoomToSelection\": \"Zooma till markering\",\n    \"toggleElementLock\": \"Lås/Lås upp valda\",\n    \"movePageUpDown\": \"Flytta sida upp/ner\",\n    \"movePageLeftRight\": \"Flytta sida vänster/höger\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Rensa canvas\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Publicera bibliotek\",\n    \"itemName\": \"Objektnamn\",\n    \"authorName\": \"Upphovsmannens namn\",\n    \"githubUsername\": \"GitHub-användarnamn\",\n    \"twitterUsername\": \"Twitter-användarnamn\",\n    \"libraryName\": \"Biblioteksnamn\",\n    \"libraryDesc\": \"Biblioteksbeskrivning\",\n    \"website\": \"Webbplats\",\n    \"placeholder\": {\n      \"authorName\": \"Ditt namn eller användarnamn\",\n      \"libraryName\": \"Namn på ditt bibliotek\",\n      \"libraryDesc\": \"Beskrivning av ditt bibliotek för att hjälpa människor att förstå dess användning\",\n      \"githubHandle\": \"Github-användarnamn (valfritt), så att du kan redigera biblioteket när du har skickat in det för granskning\",\n      \"twitterHandle\": \"Twitter-användarnamn (valfritt), så vi vet vem att kreditera när du marknadsför på Twitter\",\n      \"website\": \"Länk till din personliga webbplats eller någon annan (valfritt)\"\n    },\n    \"errors\": {\n      \"required\": \"Obligatoriskt\",\n      \"website\": \"Ange en giltig URL\"\n    },\n    \"noteDescription\": \"Skicka ditt bibliotek för att inkluderas i <link>det offentliga bibliotekets arkiv</link>för andra människor att använda i sina skisser.\",\n    \"noteGuidelines\": \"Biblioteket måste godkännas manuellt först. Vänligen läs <link>riktlinjerna</link> innan du skickar in. Du behöver ett GitHub-konto för att kommunicera och göra ändringar om så önskas, men det krävs inte.\",\n    \"noteLicense\": \"Genom att skicka in godkänner du att biblioteket kommer att publiceras under <link>MIT-licens, </link>vilket kort sagt betyder att vem som helst kan använda det utan restriktioner.\",\n    \"noteItems\": \"Varje objekt måste ha sitt eget namn så att det är filtrerbart. Följande objekt kommer att inkluderas:\",\n    \"atleastOneLibItem\": \"Välj minst ett biblioteksobjekt för att komma igång\",\n    \"republishWarning\": \"Obs: några av de markerade objekten är redan markerade som publicerade/skickade. Du bör endast skicka objekt igen när du uppdaterar ett befintligt bibliotek eller inlämning.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Bibliotek inskickat\",\n    \"content\": \"Tack {{authorName}}. Ditt bibliotek har skickats för granskning. Du kan följa status<link>här</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Återställ bibliotek\",\n    \"removeItemsFromLib\": \"Ta bort markerade objekt från biblioteket\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Exportera bild\",\n    \"label\": {\n      \"withBackground\": \"Bakgrund\",\n      \"onlySelected\": \"Endast markerade\",\n      \"darkMode\": \"Mörkt läge\",\n      \"embedScene\": \"Bädda in skiss\",\n      \"scale\": \"Skala\",\n      \"padding\": \"Utfyllnad\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"Skissdata kommer att sparas i den exporterade PNG/SVG-filen så att skissen kan återställas från den.\\nKommer att öka exporterad filstorlek.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Exportera till PNG\",\n      \"exportToSvg\": \"Exportera till SVG\",\n      \"copyPngToClipboard\": \"Kopiera PNG till urklipp\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Kopiera till urklipp\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Dina skisser är krypterade från ände till ände så Excalidraws servrar kommer aldrig att se dem.\",\n    \"link\": \"Blogginlägg om kryptering från ände till ände i Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Vinkel\",\n    \"element\": \"Element\",\n    \"elements\": \"Element\",\n    \"height\": \"Höjd\",\n    \"scene\": \"Skiss\",\n    \"selected\": \"Valda\",\n    \"storage\": \"Lagring\",\n    \"title\": \"Statistik för nördar\",\n    \"total\": \"Totalt\",\n    \"version\": \"Version\",\n    \"versionCopy\": \"Klicka för att kopiera\",\n    \"versionNotAvailable\": \"Versionen är inte tillgänglig\",\n    \"width\": \"Bredd\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Tillagd i biblioteket\",\n    \"copyStyles\": \"Kopierade stilar.\",\n    \"copyToClipboard\": \"Kopierad till urklipp.\",\n    \"copyToClipboardAsPng\": \"Kopierade {{exportSelection}} till urklipp som PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Fil sparad.\",\n    \"fileSavedToFilename\": \"Sparad till {filename}\",\n    \"canvas\": \"canvas\",\n    \"selection\": \"markering\",\n    \"pasteAsSingleElement\": \"Använd {{shortcut}} för att klistra in som ett enda element,\\neller klistra in i en befintlig textredigerare\",\n    \"unableToEmbed\": \"Att bädda in denna webbadress är för närvarande inte tillåtet. Skapa en problemrapport på GitHub för att begära att webbadressen vitlistas.\",\n    \"unrecognizedLinkFormat\": \"Länken du bäddade in matchar inte det förväntade formatet. Försök klistra in 'embed'-strängen som tillhandahålls av källwebbplatsen\"\n  },\n  \"colors\": {\n    \"transparent\": \"Genomskinlig\",\n    \"black\": \"Svart\",\n    \"white\": \"Vit\",\n    \"red\": \"Röd\",\n    \"pink\": \"Rosa\",\n    \"grape\": \"Lila\",\n    \"violet\": \"Violett\",\n    \"gray\": \"Grå\",\n    \"blue\": \"Blå\",\n    \"cyan\": \"Turkos\",\n    \"teal\": \"Blågrön\",\n    \"green\": \"Grön\",\n    \"yellow\": \"Gul\",\n    \"orange\": \"Orange\",\n    \"bronze\": \"Brons\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"All data sparas lokalt i din webbläsare.\",\n      \"center_heading_plus\": \"Ville du gå till Excalidraw+ istället?\",\n      \"menuHint\": \"Exportera, inställningar, språk, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Exportera, inställningar och mer...\",\n      \"center_heading\": \"Förenklade. Diagram.\",\n      \"toolbarHint\": \"Välj ett verktyg & börja rita!\",\n      \"helpHint\": \"Genvägar & hjälp\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Mest frekvent använda anpassade färger\",\n    \"colors\": \"Färger\",\n    \"shades\": \"Nyanser\",\n    \"hexCode\": \"Hex-kod\",\n    \"noShades\": \"Inga nyanser tillgängliga för denna färg\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Exportera som bild\",\n        \"button\": \"Exportera som bild\",\n        \"description\": \"Exportera scendata som en bild från vilken du kan importera senare.\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Spara till disk\",\n        \"button\": \"Spara till disk\",\n        \"description\": \"Exportera scendata till en fil från vilken du kan importera senare.\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Exportera till Excalidraw+\",\n        \"description\": \"Spara skissen till din Excalidraw+ arbetsyta.\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Läs in från fil\",\n        \"button\": \"Läs in från fil\",\n        \"description\": \"Laddar från en fil kommer <bold>ersätta ditt befintliga innehåll</bold>.<br></br>Du kan säkerhetskopiera din ritning först med hjälp av ett av alternativen nedan.\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Läs in från länk\",\n        \"button\": \"Ersätt mitt innehåll\",\n        \"description\": \"Inläsning av en extern ritning kommer <bold>ersätta ditt befintliga innehåll</bold>.<br></br>Du kan säkerhetskopiera din ritning först genom att använda ett av alternativen nedan.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"Mermaid till Excalidraw\",\n    \"button\": \"Infoga\",\n    \"description\": \"För närvarande stöds endast <flowchartLink>Flödesdiagram</flowchartLink>,<sequenceLink> Sekvensdiagram </sequenceLink> och <classLink>Klassdiagram</classLink>. De andra typerna kommer att återges som bild i Excalidraw.\",\n    \"syntax\": \"Mermaid-syntax\",\n    \"preview\": \"Förhandsgranska\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}