"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_pla_pla_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/pla/pla.js":
/*!***********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/pla/pla.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/pla/pla.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"[\", \"]\"],\n    [\"<\", \">\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"<\", close: \">\" },\n    { open: \"(\", close: \")\" }\n  ],\n  surroundingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"<\", close: \">\" },\n    { open: \"(\", close: \")\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".pla\",\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \".i\",\n    \".o\",\n    \".mv\",\n    \".ilb\",\n    \".ob\",\n    \".label\",\n    \".type\",\n    \".phase\",\n    \".pair\",\n    \".symbolic\",\n    \".symbolic-output\",\n    \".kiss\",\n    \".p\",\n    \".e\",\n    \".end\"\n  ],\n  // regular expressions\n  comment: /#.*$/,\n  identifier: /[a-zA-Z]+[a-zA-Z0-9_\\-]*/,\n  plaContent: /[01\\-~\\|]+/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // comments and whitespace\n      { include: \"@whitespace\" },\n      [/@comment/, \"comment\"],\n      // keyword\n      [\n        /\\.([a-zA-Z_\\-]+)/,\n        {\n          cases: {\n            \"@eos\": { token: \"keyword.$1\" },\n            \"@keywords\": {\n              cases: {\n                \".type\": { token: \"keyword.$1\", next: \"@type\" },\n                \"@default\": { token: \"keyword.$1\", next: \"@keywordArg\" }\n              }\n            },\n            \"@default\": { token: \"keyword.$1\" }\n          }\n        }\n      ],\n      // identifiers\n      [/@identifier/, \"identifier\"],\n      // PLA row\n      [/@plaContent/, \"string\"]\n    ],\n    whitespace: [[/[ \\t\\r\\n]+/, \"\"]],\n    type: [{ include: \"@whitespace\" }, [/\\w+/, { token: \"type\", next: \"@pop\" }]],\n    keywordArg: [\n      // whitespace\n      [\n        /[ \\t\\r\\n]+/,\n        {\n          cases: {\n            \"@eos\": { token: \"\", next: \"@pop\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // comments\n      [/@comment/, \"comment\", \"@pop\"],\n      // brackets\n      [\n        /[<>()\\[\\]]/,\n        {\n          cases: {\n            \"@eos\": { token: \"@brackets\", next: \"@pop\" },\n            \"@default\": \"@brackets\"\n          }\n        }\n      ],\n      // numbers\n      [\n        /\\-?\\d+/,\n        {\n          cases: {\n            \"@eos\": { token: \"number\", next: \"@pop\" },\n            \"@default\": \"number\"\n          }\n        }\n      ],\n      // identifiers\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@eos\": { token: \"identifier\", next: \"@pop\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // delimiter\n      [\n        /[;=]/,\n        {\n          cases: {\n            \"@eos\": { token: \"delimiter\", next: \"@pop\" },\n            \"@default\": \"delimiter\"\n          }\n        }\n      ]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/pla/pla.js\n"));

/***/ })

}]);