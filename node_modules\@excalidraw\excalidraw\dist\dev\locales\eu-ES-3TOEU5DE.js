import "../chunk-XDFCUUT6.js";

// locales/eu-ES.json
var labels = {
  paste: "Itsatsi",
  pasteAsPlaintext: "Itsatsi testu arrunt gisa",
  pasteCharts: "Itsatsi grafikoak",
  selectAll: "Hautatu dena",
  multiSelect: "Gehitu elementua hautapenera",
  moveCanvas: "Mugitu oihala",
  cut: "<PERSON><PERSON><PERSON>",
  copy: "Kopia<PERSON>",
  copyAsPng: "Kopiatu arbelera PNG gisa",
  copyAsSvg: "Kopiatu arbelera SVG gisa",
  copyText: "Kopiatu arbelera testu gisa",
  copySource: "Kopiatu iturria arbelean",
  convertToCode: "Bihurtu kodea",
  bringForward: "<PERSON><PERSON>ri aurrerago",
  sendToBack: "Eraman atzera",
  bringToFront: "<PERSON>karri aurrera",
  sendBackward: "Eraman atzerago",
  delete: "Ezabatu",
  copyStyles: "<PERSON>pia<PERSON> estiloak",
  pasteStyles: "Itsatsi estiloak",
  stroke: "<PERSON><PERSON>",
  background: "<PERSON><PERSON><PERSON> planoa",
  fill: "Bet<PERSON>",
  strokeWidth: "<PERSON>rar<PERSON> zabalera",
  strokeStyle: "Marraren estiloa",
  strokeStyle_solid: "Solidoa",
  strokeStyle_dashed: "Marratua",
  strokeStyle_dotted: "Puntukatua",
  sloppiness: "Marraren trazoa",
  opacity: "Opakotasuna",
  textAlign: "Testuaren lerrokapena",
  edges: "Ertzak",
  sharp: "Ertz bizia",
  round: "Borobildua",
  arrowheads: "Gezi-puntak",
  arrowhead_none: "Bat ere ez",
  arrowhead_arrow: "Gezia",
  arrowhead_bar: "Barra",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "Hirukia",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "Letra-tamaina",
  fontFamily: "Letra-tipoa",
  addWatermark: 'Gehitu "Excalidraw bidez egina"',
  handDrawn: "Eskuz marraztua",
  normal: "Normala",
  code: "Kodea",
  small: "Txikia",
  medium: "Ertaina",
  large: "Handia",
  veryLarge: "Oso handia",
  solid: "Solidoa",
  hachure: "Itzalduna",
  zigzag: "Sigi-saga",
  crossHatch: "Marraduna",
  thin: "Mehea",
  bold: "Lodia",
  left: "Ezkerrean",
  center: "Erdian",
  right: "Eskuinean",
  extraBold: "Oso lodia",
  architect: "Arkitektoa",
  artist: "Artista",
  cartoonist: "Marrazkilaria",
  fileTitle: "Fitxategi izena",
  colorPicker: "Kolore-hautatzailea",
  canvasColors: "Oihalean erabilita",
  canvasBackground: "Oihalaren atzeko planoa",
  drawingCanvas: "Marrazteko oihala",
  layers: "Geruzak",
  actions: "Ekintzak",
  language: "Hizkuntza",
  liveCollaboration: "Zuzeneko elkarlana...",
  duplicateSelection: "Bikoiztu",
  untitled: "Izengabea",
  name: "Izena",
  yourName: "Zure izena",
  madeWithExcalidraw: "Excalidraw bidez egina",
  group: "Hautapena taldea bihurtu",
  ungroup: "Desegin hautapenaren taldea",
  collaborators: "Kolaboratzaileak",
  showGrid: "Erakutsi sareta",
  addToLibrary: "Gehitu liburutegira",
  removeFromLibrary: "Kendu liburutegitik",
  libraryLoadingMessage: "Liburutegia kargatzen\u2026",
  libraries: "Arakatu liburutegiak",
  loadingScene: "Eszena kargatzen\u2026",
  align: "Lerrokatu",
  alignTop: "Lerrokatu goian",
  alignBottom: "Lerrokatu behean",
  alignLeft: "Lerrokatu ezkerrean",
  alignRight: "Lerrokatu eskuinean",
  centerVertically: "Erdiratu bertikalki",
  centerHorizontally: "Erdiratu horizontalki",
  distributeHorizontally: "Banandu horizontalki",
  distributeVertically: "Banandu bertikalki",
  flipHorizontal: "Irauli horizontalki",
  flipVertical: "Irauli bertikalki",
  viewMode: "Ikuspegia",
  share: "Partekatu",
  showStroke: "Erakutsi marraren kolore-hautatzailea",
  showBackground: "Erakutsi atzeko planoaren kolore-hautatzailea",
  toggleTheme: "Aldatu gaia",
  personalLib: "Liburutegi pertsonala",
  excalidrawLib: "Excalidraw liburutegia",
  decreaseFontSize: "Txikitu letra tamaina",
  increaseFontSize: "Handitu letra tamaina",
  unbindText: "Askatu testua",
  bindText: "Lotu testua edukiontziari",
  createContainerFromText: "Bilatu testua edukiontzi batean",
  link: {
    edit: "Editatu esteka",
    editEmbed: "Editatu esteka eta kapsulatu",
    create: "Sortu esteka",
    createEmbed: "Sortu esteka eta kapsulatu",
    label: "Esteka",
    labelEmbed: "Esteka eta kapsula",
    empty: "Ez da estekarik ezarri"
  },
  lineEditor: {
    edit: "Editatu lerroa",
    exit: "Irten lerro-editoretik"
  },
  elementLock: {
    lock: "Blokeatu",
    unlock: "Desblokeatu",
    lockAll: "Blokeatu guztiak",
    unlockAll: "Desblokeatu guztiak"
  },
  statusPublished: "Argitaratua",
  sidebarLock: "Mantendu alboko barra irekita",
  selectAllElementsInFrame: "Hautatu markoko elementu guztiak",
  removeAllElementsFromFrame: "Kendu markoko elementu guztiak",
  eyeDropper: "Aukeratu kolorea oihaletik",
  textToDiagram: "Testutik diagramara",
  prompt: ""
};
var library = {
  noItems: "Oraindik ez da elementurik gehitu...",
  hint_emptyLibrary: "Hautatu oihaleko elementu bat hemen gehitzeko, edo instalatu liburutegi bat beheko biltegi publikotik.",
  hint_emptyPrivateLibrary: "Hautatu oihaleko elementu bat hemen gehitzeko."
};
var buttons = {
  clearReset: "Garbitu oihala",
  exportJSON: "Esportatu fitxategira",
  exportImage: "Esportatu irudia...",
  export: "Gorde hemen...",
  copyToClipboard: "Kopiatu arbelera",
  save: "Gorde uneko fitxategian",
  saveAs: "Gorde honela",
  load: "Ireki",
  getShareableLink: "Lortu partekatzeko esteka",
  close: "Itxi",
  selectLanguage: "Hautatu hizkuntza",
  scrollBackToContent: "Joan atzera edukira",
  zoomIn: "Handiagotu",
  zoomOut: "Txikiagotu",
  resetZoom: "Leheneratu zooma",
  menu: "Menua",
  done: "Egina",
  edit: "Editatu",
  undo: "Desegin",
  redo: "Berregin",
  resetLibrary: "Leheneratu liburutegia",
  createNewRoom: "Sortu gela berria",
  fullScreen: "Pantaila osoa",
  darkMode: "Modu iluna",
  lightMode: "Modu argia",
  zenMode: "Zen modua",
  objectsSnapMode: "Atxiki objektuei",
  exitZenMode: "Irten Zen modutik",
  cancel: "Utzi",
  clear: "Garbitu",
  remove: "Kendu",
  embed: "Aldatu kapsulatzea",
  publishLibrary: "Argitaratu",
  submit: "Bidali",
  confirm: "Bai",
  embeddableInteractionButton: "Egin klik elkar eragiteko"
};
var alerts = {
  clearReset: "Honek oihal osoa garbituko du. Ziur zaude?",
  couldNotCreateShareableLink: "Ezin izan da partekatzeko estekarik sortu.",
  couldNotCreateShareableLinkTooBig: "Ezin izan da partekatzeko estekarik sortu: eszena handiegia da",
  couldNotLoadInvalidFile: "Ezin izan da kargatu, fitxategiak ez du balio",
  importBackendFailed: "Inportazioak huts egin du.",
  cannotExportEmptyCanvas: "Ezin izan da oihal hutsa esportatu.",
  couldNotCopyToClipboard: "Ezin izan da arbelean kopiatu.",
  decryptFailed: "Ezin izan da deszifratu.",
  uploadedSecurly: "Kargatzea muturretik muturrerako zifratze bidez ziurtatu da, hau da, Excalidraw zerbitzariak eta hirugarrenek ezin dutela edukia irakurri.",
  loadSceneOverridePrompt: "Kanpoko marrazkia kargatzeak lehendik duzun edukia ordezkatuko du. Jarraitu nahi duzu?",
  collabStopOverridePrompt: "Saioa gelditzeak lokalean gordetako zure aurreko marrazkia gainidatziko du. Ziur zaude?\n\n(Zure marrazki lokala mantendu nahi baduzu, itxi arakatzailearen fitxa.)",
  errorAddingToLibrary: "Ezin izan da elementua liburutegian gehitu",
  errorRemovingFromLibrary: "Ezin izan da elementua liburutegitik kendu",
  confirmAddLibrary: "Honek {{numShapes}} forma gehituko ditu zure liburutegian. Ziur zaude?",
  imageDoesNotContainScene: "Irudi honek ez dirudi eszena daturik duenik. Eszena kapsulatzea gaitu al duzu esportazioan?",
  cannotRestoreFromImage: "Ezin izan da eszena leheneratu irudi fitxategi honetatik",
  invalidSceneUrl: "Ezin izan da eszena inportatu emandako URLtik. Gaizki eratuta dago edo ez du baliozko Excalidraw JSON daturik.",
  resetLibrary: "Honek zure liburutegia garbituko du. Ziur zaude?",
  removeItemsFromsLibrary: "Liburutegitik {{count}} elementu ezabatu?",
  invalidEncryptionKey: "Enkriptazio-gakoak 22 karaktere izan behar ditu. Zuzeneko lankidetza desgaituta dago.",
  collabOfflineWarning: "Ez dago Interneteko konexiorik.\nZure aldaketak ez dira gordeko!"
};
var errors = {
  unsupportedFileType: "Onartu gabeko fitxategi mota.",
  imageInsertError: "Ezin izan da irudia txertatu. Saiatu berriro geroago...",
  fileTooBig: "Fitxategia handiegia da. Onartutako gehienezko tamaina {{maxSize}} da.",
  svgImageInsertError: "Ezin izan da SVG irudia txertatu. SVG markak baliogabea dirudi.",
  failedToFetchImage: "Ezin izan da irudia eskuratu.",
  invalidSVGString: "SVG baliogabea.",
  cannotResolveCollabServer: "Ezin izan da elkarlaneko zerbitzarira konektatu. Mesedez, berriro kargatu orria eta saiatu berriro.",
  importLibraryError: "Ezin izan da liburutegia kargatu",
  collabSaveFailed: "Ezin izan da backend datu-basean gorde. Arazoak jarraitzen badu, zure fitxategia lokalean gorde beharko zenuke zure lana ez duzula galtzen ziurtatzeko.",
  collabSaveFailed_sizeExceeded: "Ezin izan da backend datu-basean gorde, ohiala handiegia dela dirudi. Fitxategia lokalean gorde beharko zenuke zure lana galtzen ez duzula ziurtatzeko.",
  imageToolNotSupported: "Irudiak desgaituta daude.",
  brave_measure_text_error: {
    line1: "Brave arakatzailea erabiltzen ari zarela dirudi <bold>Blokeatu hatz-markak erasokorki</bold> ezarpena gaituta.",
    line2: "Honek zure marrazkietako <bold>Testu-elementuak</bold> hautsi ditzake.",
    line3: "Ezarpen hau desgaitzea gomendatzen dugu. <link>urrats hauek</link> jarrai ditzakezu hori nola egin jakiteko.",
    line4: "Ezarpen hau desgaituz gero, testu-elementuen bistaratzea konpontzen ez bada, ireki <issueLink>arazo</issueLink> gure GitHub-en edo idatzi iezaguzu <discordLink>Discord</discordLink> helbidera"
  },
  libraryElementTypeError: {
    embeddable: "Kapsulatutako elementuak ezin dira liburutegira gehitu.",
    iframe: "IFrame elementuak ezin dira liburutegira gehitu.",
    image: "Laster egongo da irudiak liburutegian gehitzeko laguntza!"
  },
  asyncPasteFailedOnRead: "Ezin izan da itsatsi (ezin izan da sistemaren arbeletik irakurri).",
  asyncPasteFailedOnParse: "Ezin izan da itsatsi.",
  copyToSystemClipboardFailed: "Ezin izan da arbelean kopiatu."
};
var toolBar = {
  selection: "Hautapena",
  image: "Txertatu irudia",
  rectangle: "Laukizuzena",
  diamond: "Diamantea",
  ellipse: "Elipsea",
  arrow: "Gezia",
  line: "Lerroa",
  freedraw: "Marraztu",
  text: "Testua",
  library: "Liburutegia",
  lock: "Mantendu aktibo hautatutako tresna marraztu ondoren",
  penMode: "Luma modua - ukipena saihestu",
  link: "Gehitu / Eguneratu esteka hautatutako forma baterako",
  eraser: "Borragoma",
  frame: "Marko tresna",
  magicframe: "Wireframe kodetzeko",
  embeddable: "Web kapsulatzea",
  laser: "Laser punteroa",
  hand: "Eskua (panoratze tresna)",
  extraTools: "Tresna gehiago",
  mermaidToExcalidraw: "",
  magicSettings: "AI ezarpenak"
};
var headings = {
  canvasActions: "Canvas ekintzak",
  selectedShapeActions: "Hautatutako formaren ekintzak",
  shapes: "Formak"
};
var hints = {
  canvasPanning: "Oihala mugitzeko, eutsi saguaren gurpila edo zuriune-barra arrastatzean, edo erabili esku tresna",
  linearElement: "Egin klik hainbat puntu hasteko, arrastatu lerro bakarrerako",
  freeDraw: "Egin klik eta arrastatu, askatu amaitutakoan",
  text: "Aholkua: testua gehitu dezakezu edozein lekutan klik bikoitza eginez hautapen tresnarekin",
  embeddable: "Egin klik eta arrastatu webgunea kapsulatzeko",
  text_selected: "Egin klik bikoitza edo sakatu SARTU testua editatzeko",
  text_editing: "Sakatu Esc edo Ctrl+SARTU editatzen amaitzeko",
  linearElementMulti: "Egin klik azken puntuan edo sakatu Esc edo Sartu amaitzeko",
  lockAngle: "SHIFT sakatuta angelua mantendu dezakezu",
  resize: "Proportzioak mantendu ditzakezu SHIFT sakatuta tamaina aldatzen duzun bitartean.\nsakatu ALT erditik tamaina aldatzeko",
  resizeImage: "Tamaina libreki alda dezakezu SHIFT sakatuta,\nsakatu ALT erditik tamaina aldatzeko",
  rotate: "Angeluak mantendu ditzakezu SHIFT sakatuta biratzen duzun bitartean",
  lineEditor_info: "Eutsi sakatuta Ctrl edo Cmd eta egin klik bikoitza edo sakatu Ctrl edo Cmd + Sartu puntuak editatzeko",
  lineEditor_pointSelected: "Sakatu Ezabatu puntuak kentzeko,\nKtrl+D bikoizteko, edo arrastatu mugitzeko",
  lineEditor_nothingSelected: "Hautatu editatzeko puntu bat (SHIFT sakatuta anitz hautatzeko),\nedo eduki Alt sakatuta eta egin klik puntu berriak gehitzeko",
  placeImage: "Egin klik irudia kokatzeko, edo egin klik eta arrastatu bere tamaina eskuz ezartzeko",
  publishLibrary: "Argitaratu zure liburutegia",
  bindTextToElement: "Sakatu Sartu testua gehitzeko",
  deepBoxSelect: "Eutsi Ctrl edo Cmd sakatuta aukeraketa sakona egiteko eta arrastatzea saihesteko",
  eraserRevert: "Eduki Alt sakatuta ezabatzeko markatutako elementuak leheneratzeko",
  firefox_clipboard_write: 'Ezaugarri hau "dom.events.asyncClipboard.clipboardItem" marka "true" gisa ezarrita gaitu daiteke. Firefox-en arakatzailearen banderak aldatzeko, bisitatu "about:config" orrialdera.',
  disableSnapping: "Eduki sakatuta Ctrl edo Cmd tekla atxikipena desgaitzeko"
};
var canvasError = {
  cannotShowPreview: "Ezin da oihala aurreikusi",
  canvasTooBig: "Agian oihala handiegia da.",
  canvasTooBigTip: "Aholkua: saiatu urrunen dauden elementuak pixka bat hurbiltzen."
};
var errorSplash = {
  headingMain: "Errore bat aurkitu da. Saiatu <button>orria birkargatzen.</button>",
  clearCanvasMessage: "Birkargatzea ez bada burutzen, saiatu <button>oihala garbitzen.</button>",
  clearCanvasCaveat: " Honen ondorioz lana galduko da ",
  trackedToSentry: "Identifikatzailearen errorea {{eventId}} gure sistemak behatu du.",
  openIssueMessage: "Oso kontuz ibili gara zure eszenaren informazioa errorean ez sartzeko. Zure eszena pribatua ez bada, kontuan hartu gure <button>erroreen jarraipena egitea.</button> Sartu beheko informazioa kopiatu eta itsatsi bidez GitHub issue-n.",
  sceneContent: "Eszenaren edukia:"
};
var roomDialog = {
  desc_intro: "Jendea zure uneko eszenara gonbida dezakezu zurekin elkarlanean aritzeko.",
  desc_privacy: "Ez kezkatu, saioak muturretik muturrerako enkriptatzea erabiltzen du, beraz, marrazten duzuna pribatua izango da. Gure zerbitzariak ere ezingo du ikusi zer egiten duzun.",
  button_startSession: "Hasi saioa",
  button_stopSession: "Itxi saioa",
  desc_inProgressIntro: "Zuzeneko lankidetza saioa abian da.",
  desc_shareLink: "Partekatu esteka hau elkarlanean aritu nahi duzun edonorekin:",
  desc_exitSession: "Saioa ixteak aretotik deskonektatuko zaitu, baina eszenarekin lanean jarraitu ahal izango duzu lokalean. Kontuan izan honek ez diela beste pertsonei eragingo, eta euren bertsioan elkarlanean aritu ahal izango dira.",
  shareTitle: "Sartu Excalidraw-en zuzeneko lankidetza-saio batean"
};
var errorDialog = {
  title: "Errorea"
};
var exportDialog = {
  disk_title: "Gorde diskoan",
  disk_details: "Esportatu eszenaren datuak geroago inportatu ahal izango duzun fitxategi batan.",
  disk_button: "Gorde fitxategian",
  link_title: "Partekatzeko esteka",
  link_details: "Esportatu irakurtzeko soilik moduko esteka.",
  link_button: "Esportatu esteka",
  excalidrawplus_description: "Gorde eszena zure Excalidraw+ laneko areara.",
  excalidrawplus_button: "Esportatu",
  excalidrawplus_exportError: "Une honetan ezin izan da esportatu Excalidraw+era..."
};
var helpDialog = {
  blog: "Irakurri gure bloga",
  click: "sakatu",
  deepSelect: "Hautapen sakona",
  deepBoxSelect: "Hautapen sakona egin laukizuzen bidez, eta saihestu arrastatzea",
  curvedArrow: "Gezi kurbatua",
  curvedLine: "Lerro kurbatua",
  documentation: "Dokumentazioa",
  doubleClick: "klik bikoitza",
  drag: "arrastatu",
  editor: "Editorea",
  editLineArrowPoints: "Editatu lerroak/gezi-puntuak",
  editText: "Editatu testua / gehitu etiketa",
  github: "Arazorik izan al duzu? Eman horren berri",
  howto: "Jarraitu gure gidak",
  or: "edo",
  preventBinding: "Saihestu gezien gainjartzea",
  tools: "Tresnak",
  shortcuts: "Laster-teklak",
  textFinish: "Bukatu edizioa (testu editorea)",
  textNewLine: "Gehitu lerro berri bat (testu editorea)",
  title: "Laguntza",
  view: "Bistaratu",
  zoomToFit: "Egin zoom elementu guztiak ikusteko",
  zoomToSelection: "Zooma hautapenera",
  toggleElementLock: "Blokeatu/desbloketatu hautapena",
  movePageUpDown: "Mugitu orria gora/behera",
  movePageLeftRight: "Mugitu orria ezker/eskuin"
};
var clearCanvasDialog = {
  title: "Garbitu oihala"
};
var publishDialog = {
  title: "Argitaratu liburutegia",
  itemName: "Elementuaren izena",
  authorName: "Egilearen izena",
  githubUsername: "GitHub-eko erabiltzaile-izena",
  twitterUsername: "Twitter-eko erabiltzaile-izena",
  libraryName: "Liburutegiaren izena",
  libraryDesc: "Liburutegiaren deskripzioa",
  website: "Webgunea",
  placeholder: {
    authorName: "Zure izena edo erabiltzaile-izena",
    libraryName: "Zure liburutegiaren izena",
    libraryDesc: "Zure liburutegiaren deskripzioa laguntzeko jendeari ulertzen haren erabilpena",
    githubHandle: "GitHub heldulekua (aukerakoa), liburutegia editatu ahal izateko berrikustera bidalitakoan",
    twitterHandle: "Twitter-eko erabiltzaile-izena (aukerakoa), badakigu nori kreditatu behar dugun Twitter bidez sustatzeko",
    website: "Estekatu zure webgunera edo nahi duzun tokira (aukerakoa)"
  },
  errors: {
    required: "Beharrezkoa",
    website: "Sartu baliozko URL bat"
  },
  noteDescription: "Bidali zure liburutegira sartu ahal izateko <link>zure liburutegiko biltegian</link>beste jendeak bere marrazkietan erabili ahal izateko.",
  noteGuidelines: "Liburutegia eskuz onartu behar da. Irakurri <link>gidalerroak</link> bidali aurretik. GitHub kontu bat edukitzea komeni da komunikatzeko eta aldaketak egin ahal izateko, baina ez da guztiz beharrezkoa.",
  noteLicense: "Bidaltzen baduzu, onartzen duzu liburutegia <link>MIT lizentziarekin argitaratuko dela, </link>zeinak, laburbilduz, esan nahi du edozeinek erabiltzen ahal duela murrizketarik gabe.",
  noteItems: "Liburutegiko elementu bakoitzak bere izena eduki behar du iragazi ahal izateko. Liburutegiko hurrengo elementuak barne daude:",
  atleastOneLibItem: "Hautatu gutxienez liburutegiko elementu bat gutxienez hasi ahal izateko",
  republishWarning: "Oharra: hautatutako elementu batzuk dagoeneko argitaratuta/bidalita bezala markatuta daude. Elementuak berriro bidali behar dituzu lehendik dagoen liburutegi edo bidalketa eguneratzen duzunean."
};
var publishSuccessDialog = {
  title: "Liburutegia bidali da",
  content: "Eskerrik asko {{authorName}}. Zure liburutegia bidali da berrikustera. Jarraitu dezakezu haren egoera<link>hemen</link>"
};
var confirmDialog = {
  resetLibrary: "Leheneratu liburutegia",
  removeItemsFromLib: "Kendu hautatutako elementuak liburutegitik"
};
var imageExportDialog = {
  header: "Esportatu irudia",
  label: {
    withBackground: "Atzeko planoa",
    onlySelected: "Hautapena soilik",
    darkMode: "Modu iluna",
    embedScene: "Txertatu eszena",
    scale: "Eskala",
    padding: "Betegarria"
  },
  tooltip: {
    embedScene: "Eszenaren datuak esportatutako PNG/SVG fitxategian gordeko dira, eszena bertatik berrezartzeko.\nEsportatutako fitxategien tamaina handituko da."
  },
  title: {
    exportToPng: "Esportatu PNG gisa",
    exportToSvg: "Esportatu SVG gisa",
    copyPngToClipboard: "Kopiatu PNG arbelera"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "Kopiatu arbelean"
  }
};
var encrypted = {
  tooltip: "Zure marrazkiak muturretik muturrera enkriptatu dira, beraz Excalidraw-ren zerbitzariek ezingo dituzte ikusi.",
  link: "Excalidraw-ren muturretik muturrerako enkriptatzearen gaineko mezua blogean"
};
var stats = {
  angle: "Angelua",
  element: "Elementua",
  elements: "Elementuak",
  height: "Altuera",
  scene: "Eszena",
  selected: "Hautatua",
  storage: "Biltegia",
  title: "Datuak",
  total: "Guztira",
  version: "Bertsioa",
  versionCopy: "Klikatu kopiatzeko",
  versionNotAvailable: "Bertsio ez eskuragarria",
  width: "Zabalera"
};
var toast = {
  addedToLibrary: "Liburutegira gehitu da",
  copyStyles: "Estiloak kopiatu dira.",
  copyToClipboard: "Arbelean kopiatu da.",
  copyToClipboardAsPng: "{{exportSelection}} kopiatu da arbelean PNG gisa\n({{exportColorScheme}})",
  fileSaved: "Fitxategia gorde da.",
  fileSavedToFilename: "{filename}-n gorde da",
  canvas: "oihala",
  selection: "hautapena",
  pasteAsSingleElement: "Erabili {{shortcut}} elementu bakar gisa itsasteko,\nedo itsatsi lehendik dagoen testu-editore batean",
  unableToEmbed: "Url hau txertatzea ez da une honetan onartzen. Sortu issue bat GitHub-en Urla zerrenda zurian sartzea eskatzeko",
  unrecognizedLinkFormat: "Kapsulatu duzun esteka ez dator bat espero den formatuarekin. Mesedez, saiatu iturburu-guneak emandako 'kapsulatu' katea itsasten"
};
var colors = {
  transparent: "Gardena",
  black: "Beltza",
  white: "Zuria",
  red: "Gorria",
  pink: "Arrosa",
  grape: "Mahats kolorea",
  violet: "Bioleta",
  gray: "Grisa",
  blue: "Urdina",
  cyan: "Ziana",
  teal: "Berde urdinxka",
  green: "Berdea",
  yellow: "Horia",
  orange: "Laranja",
  bronze: "Brontzea"
};
var welcomeScreen = {
  app: {
    center_heading: "Zure datu guztiak lokalean gordetzen dira zure nabigatzailean.",
    center_heading_plus: "Horren ordez Excalidraw+-era joan nahi al zenuen?",
    menuHint: "Esportatu, hobespenak, hizkuntzak..."
  },
  defaults: {
    menuHint: "Esportatu, hobespenak eta gehiago...",
    center_heading: "Diagramak. Egina. Sinplea.",
    toolbarHint: "Aukeratu tresna bat eta hasi marrazten!",
    helpHint: "Lasterbideak eta laguntza"
  }
};
var colorPicker = {
  mostUsedCustomColors: "Gehien erabilitako kolore pertsonalizatuak",
  colors: "Koloreak",
  shades: "\xD1abardurak",
  hexCode: "Hez kodea",
  noShades: "Kolore honetarako ez dago \xF1abardurarik eskuragarri"
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "Esportatu irudi gisa",
      button: "Esportatu irudi gisa",
      description: "Esportatu eszenaren datuak geroago inportatu ahal izango duzun irudi gisa."
    },
    saveToDisk: {
      title: "Gorde diskoan",
      button: "Gorde diskoan",
      description: "Esportatu eszenaren datuak geroago inportatu ahal izango duzun fitxategi batan."
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "Esportatu Excalidraw+ra",
      description: "Gorde eszena zure Excalidraw+ laneko areara."
    }
  },
  modal: {
    loadFromFile: {
      title: "Fitxategitik kargatu",
      button: "Fitxategitik kargatu",
      description: "Fitxategi batetik kargatzeak <bold>lehendik duzun edukia ordezkatuko du</bold>.<br></br>Lehenengo marrazkiaren babeskopia egin dezakezu beheko aukeretako bat erabiliz."
    },
    shareableLink: {
      title: "Estekatik kargatu",
      button: "Ordeztu nire edukia",
      description: "Kanpoko irudi bat kargatzeak <bold>lehendik duzun edukia ordezkatuko du</bold>.<br></br>. Zure marrazkiaren babeskopia egin dezakezu lehenik beheko aukeretako bat erabiliz."
    }
  }
};
var mermaid = {
  title: "",
  button: "Txertatu",
  description: "",
  syntax: "",
  preview: "Aurrebista"
};
var eu_ES_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  eu_ES_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=eu-ES-3TOEU5DE.js.map
