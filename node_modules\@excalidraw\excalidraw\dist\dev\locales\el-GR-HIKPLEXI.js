import "../chunk-XDFCUUT6.js";

// locales/el-GR.json
var labels = {
  paste: "\u0395\u03C0\u03B9\u03BA\u03CC\u03BB\u03BB\u03B7\u03C3\u03B7",
  pasteAsPlaintext: "\u0395\u03C0\u03B9\u03BA\u03CC\u03BB\u03BB\u03B7\u03C3\u03B7 \u03C9\u03C2 \u03B1\u03C0\u03BB\u03CC \u03BA\u03B5\u03AF\u03BC\u03B5\u03BD\u03BF",
  pasteCharts: "\u0395\u03C0\u03B9\u03BA\u03CC\u03BB\u03BB\u03B7\u03C3\u03B7 \u03B3\u03C1\u03B1\u03C6\u03B7\u03BC\u03AC\u03C4\u03C9\u03BD",
  selectAll: "\u0395\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE \u03CC\u03BB\u03C9\u03BD",
  multiSelect: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AD\u03C3\u03C4\u03B5 \u03C4\u03BF \u03C3\u03C4\u03BF\u03B9\u03C7\u03B5\u03AF\u03BF \u03C3\u03C4\u03B7\u03BD \u03B5\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE",
  moveCanvas: "\u039C\u03B5\u03C4\u03B1\u03BA\u03AF\u03BD\u03B7\u03C3\u03B7 \u03BA\u03B1\u03BC\u03B2\u03AC",
  cut: "\u0391\u03C0\u03BF\u03BA\u03BF\u03C0\u03AE",
  copy: "\u0391\u03BD\u03C4\u03B9\u03B3\u03C1\u03B1\u03C6\u03AE",
  copyAsPng: "\u0391\u03BD\u03C4\u03B9\u03B3\u03C1\u03B1\u03C6\u03AE \u03C3\u03C4\u03BF \u03C0\u03C1\u03CC\u03C7\u03B5\u03B9\u03C1\u03BF \u03C9\u03C2 PNG",
  copyAsSvg: "\u0391\u03BD\u03C4\u03B9\u03B3\u03C1\u03B1\u03C6\u03AE \u03C3\u03C4\u03BF \u03C0\u03C1\u03CC\u03C7\u03B5\u03B9\u03C1\u03BF \u03C9\u03C2 SVG",
  copyText: "\u0391\u03BD\u03C4\u03B9\u03B3\u03C1\u03B1\u03C6\u03AE \u03C3\u03C4\u03BF \u03C0\u03C1\u03CC\u03C7\u03B5\u03B9\u03C1\u03BF \u03C9\u03C2 \u03BA\u03B5\u03AF\u03BC\u03B5\u03BD\u03BF",
  copySource: "",
  convertToCode: "",
  bringForward: "\u03A3\u03C4\u03BF \u03C0\u03C1\u03BF\u03C3\u03BA\u03AE\u03BD\u03B9\u03BF",
  sendToBack: "\u0388\u03BD\u03B1 \u03B5\u03C0\u03AF\u03C0\u03B5\u03B4\u03BF \u03C0\u03AF\u03C3\u03C9",
  bringToFront: "\u0388\u03BD\u03B1 \u03B5\u03C0\u03AF\u03C0\u03B5\u03B4\u03BF \u03BC\u03C0\u03C1\u03BF\u03C3\u03C4\u03AC",
  sendBackward: "\u03A3\u03C4\u03BF \u03C0\u03B1\u03C1\u03B1\u03C3\u03BA\u03AE\u03BD\u03B9\u03BF",
  delete: "\u0394\u03B9\u03B1\u03B3\u03C1\u03B1\u03C6\u03AE",
  copyStyles: "\u0391\u03BD\u03C4\u03B9\u03B3\u03C1\u03B1\u03C6\u03AE \u03B5\u03BC\u03C6\u03AC\u03BD\u03B9\u03C3\u03B7\u03C2",
  pasteStyles: "\u0395\u03C0\u03B9\u03BA\u03CC\u03BB\u03BB\u03B7\u03C3\u03B7 \u03B5\u03BC\u03C6\u03AC\u03BD\u03B9\u03C3\u03B7\u03C2",
  stroke: "\u039C\u03BF\u03BB\u03C5\u03B2\u03B9\u03AC",
  background: "\u03A6\u03CC\u03BD\u03C4\u03BF",
  fill: "\u0393\u03AD\u03BC\u03B9\u03C3\u03BC\u03B1",
  strokeWidth: "\u03A0\u03AC\u03C7\u03BF\u03C2 \u03BC\u03BF\u03BB\u03C5\u03B2\u03B9\u03AC\u03C2",
  strokeStyle: "\u03A3\u03C4\u03C5\u03BB \u03C0\u03B5\u03C1\u03B9\u03B3\u03C1\u03AC\u03BC\u03BC\u03B1\u03C4\u03BF\u03C2",
  strokeStyle_solid: "\u03A3\u03C5\u03BC\u03C0\u03B1\u03B3\u03AE\u03C2",
  strokeStyle_dashed: "\u0394\u03B9\u03B1\u03BA\u03B5\u03BA\u03BF\u03BC\u03BC\u03AD\u03BD\u03B7 \u03BC\u03B5 \u03C0\u03B1\u03CD\u03BB\u03B5\u03C2",
  strokeStyle_dotted: "\u0394\u03B9\u03B1\u03BA\u03B5\u03BA\u03BF\u03BC\u03BC\u03AD\u03BD\u03B7 \u03BC\u03B5 \u03C4\u03B5\u03BB\u03B5\u03AF\u03B5\u03C2",
  sloppiness: "\u0391\u03BA\u03B1\u03C4\u03B1\u03C3\u03C4\u03B1\u03C3\u03AF\u03B1",
  opacity: "\u0394\u03B9\u03B1\u03C6\u03AC\u03BD\u03B5\u03B9\u03B1",
  textAlign: "\u03A3\u03C4\u03BF\u03AF\u03C7\u03B9\u03C3\u03B7 \u03BA\u03B5\u03B9\u03BC\u03AD\u03BD\u03BF\u03C5",
  edges: "\u0386\u03BA\u03C1\u03B5\u03C2",
  sharp: "\u039F\u03BE\u03CD",
  round: "\u03A3\u03C4\u03C1\u03BF\u03B3\u03B3\u03C5\u03BB\u03CC",
  arrowheads: "\u03A3\u03CD\u03BC\u03B2\u03BF\u03BB\u03B1 \u03B2\u03B5\u03BB\u03CE\u03BD",
  arrowhead_none: "\u039A\u03B1\u03BD\u03AD\u03BD\u03B1",
  arrowhead_arrow: "\u0392\u03AD\u03BB\u03BF\u03C2",
  arrowhead_bar: "\u039C\u03C0\u03AC\u03C1\u03B1",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "\u03A4\u03C1\u03AF\u03B3\u03C9\u03BD\u03BF",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "\u039C\u03AD\u03B3\u03B5\u03B8\u03BF\u03C2 \u03B3\u03C1\u03B1\u03BC\u03BC\u03B1\u03C4\u03BF\u03C3\u03B5\u03B9\u03C1\u03AC\u03C2",
  fontFamily: "\u0393\u03C1\u03B1\u03BC\u03BC\u03B1\u03C4\u03BF\u03C3\u03B5\u03B9\u03C1\u03AC",
  addWatermark: '\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 "\u03A6\u03C4\u03B9\u03B1\u03B3\u03BC\u03AD\u03BD\u03BF \u03BC\u03B5 Excalidraw"',
  handDrawn: "\u03A3\u03C7\u03B5\u03B4\u03B9\u03B1\u03C3\u03BC\u03AD\u03BD\u03BF \u03C3\u03C4\u03BF \u03C7\u03AD\u03C1\u03B9",
  normal: "\u039A\u03B1\u03BD\u03BF\u03BD\u03B9\u03BA\u03AE",
  code: "\u039A\u03CE\u03B4\u03B9\u03BA\u03B1\u03C2",
  small: "\u039C\u03B9\u03BA\u03C1\u03CC",
  medium: "\u039C\u03B5\u03C3\u03B1\u03AF\u03BF",
  large: "\u039C\u03B5\u03B3\u03AC\u03BB\u03BF",
  veryLarge: "\u03A0\u03BF\u03BB\u03CD \u03BC\u03B5\u03B3\u03AC\u03BB\u03BF",
  solid: "\u03A3\u03C5\u03BC\u03C0\u03B1\u03B3\u03AE\u03C2",
  hachure: "\u0395\u03BA\u03BA\u03CC\u03BB\u03B1\u03C8\u03B7",
  zigzag: "",
  crossHatch: "\u0394\u03B9\u03B1\u03C3\u03C4\u03B1\u03C5\u03C1\u03BF\u03CD\u03BC\u03B5\u03BD\u03B7 \u03B5\u03BA\u03BA\u03CC\u03BB\u03B1\u03C8\u03B7",
  thin: "\u039B\u03B5\u03C0\u03C4\u03AE",
  bold: "\u0388\u03BD\u03C4\u03BF\u03BD\u03B7",
  left: "\u0391\u03C1\u03B9\u03C3\u03C4\u03B5\u03C1\u03AC",
  center: "\u039A\u03AD\u03BD\u03C4\u03C1\u03BF",
  right: "\u0394\u03B5\u03BE\u03B9\u03AC",
  extraBold: "\u03A0\u03BF\u03BB\u03CD \u03AD\u03BD\u03C4\u03BF\u03BD\u03B7",
  architect: "\u0391\u03C1\u03C7\u03B9\u03C4\u03AD\u03BA\u03C4\u03BF\u03BD\u03B1\u03C2",
  artist: "\u039A\u03B1\u03BB\u03BB\u03B9\u03C4\u03AD\u03C7\u03BD\u03B7\u03C2",
  cartoonist: "\u03A3\u03BA\u03B9\u03C4\u03C3\u03BF\u03B3\u03C1\u03AC\u03C6\u03BF\u03C2",
  fileTitle: "\u038C\u03BD\u03BF\u03BC\u03B1 \u03B1\u03C1\u03C7\u03B5\u03AF\u03BF\u03C5",
  colorPicker: "\u0395\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE \u03A7\u03C1\u03CE\u03BC\u03B1\u03C4\u03BF\u03C2",
  canvasColors: "\u03A7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B5\u03AF\u03C4\u03B1\u03B9 \u03C3\u03C4\u03BF\u03BD \u03BA\u03B1\u03BC\u03B2\u03AC",
  canvasBackground: "\u03A6\u03CC\u03BD\u03C4\u03BF \u03BA\u03B1\u03BC\u03B2\u03AC",
  drawingCanvas: "\u03A3\u03C7\u03B5\u03B4\u03AF\u03B1\u03C3\u03B7 \u03BA\u03B1\u03BC\u03B2\u03AC",
  layers: "\u03A3\u03C4\u03C1\u03CE\u03BC\u03B1\u03C4\u03B1",
  actions: "\u0395\u03BD\u03AD\u03C1\u03B3\u03B5\u03B9\u03B5\u03C2",
  language: "\u0393\u03BB\u03CE\u03C3\u03C3\u03B1",
  liveCollaboration: "Live \u03C3\u03C5\u03BD\u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1...",
  duplicateSelection: "\u0394\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u03B1\u03BD\u03C4\u03B9\u03B3\u03C1\u03AC\u03C6\u03BF\u03C5",
  untitled: "\u03A7\u03C9\u03C1\u03AF\u03C2 \u03C4\u03AF\u03C4\u03BB\u03BF",
  name: "\u038C\u03BD\u03BF\u03BC\u03B1",
  yourName: "\u03A4\u03BF \u03CC\u03BD\u03BF\u03BC\u03AC \u03C3\u03BF\u03C5",
  madeWithExcalidraw: "\u03A6\u03C4\u03B9\u03B1\u03B3\u03BC\u03AD\u03BD\u03BF \u03BC\u03B5 Excalidraw",
  group: "\u0394\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u03BF\u03BC\u03AC\u03B4\u03B1\u03C2 \u03B1\u03C0\u03CC \u03B5\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE",
  ungroup: "\u039A\u03B1\u03C4\u03AC\u03C1\u03B3\u03B7\u03C3\u03B7 \u03BF\u03BC\u03AC\u03B4\u03B1\u03C2 \u03B1\u03C0\u03CC \u03B5\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE",
  collaborators: "\u03A3\u03C5\u03BD\u03B5\u03C1\u03B3\u03AC\u03C4\u03B5\u03C2",
  showGrid: "\u03A0\u03C1\u03BF\u03B2\u03BF\u03BB\u03AE \u03C0\u03BB\u03AD\u03B3\u03BC\u03B1\u03C4\u03BF\u03C2",
  addToLibrary: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03C3\u03C4\u03B7 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7",
  removeFromLibrary: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 \u03B1\u03C0\u03CC \u03C4\u03B7 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7",
  libraryLoadingMessage: "\u03A6\u03CC\u03C1\u03C4\u03C9\u03C3\u03B7 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7\u03C2\u2026",
  libraries: "\u0386\u03BB\u03BB\u03B5\u03C2 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B5\u03C2",
  loadingScene: "\u03A6\u03CC\u03C1\u03C4\u03C9\u03C3\u03B7 \u03C3\u03BA\u03B7\u03BD\u03AE\u03C2\u2026",
  align: "\u03A3\u03C4\u03BF\u03AF\u03C7\u03B9\u03C3\u03B7",
  alignTop: "\u03A3\u03C4\u03BF\u03AF\u03C7\u03B9\u03C3\u03B7 \u03C0\u03AC\u03BD\u03C9",
  alignBottom: "\u03A3\u03C4\u03BF\u03AF\u03C7\u03B9\u03C3\u03B7 \u03BA\u03AC\u03C4\u03C9",
  alignLeft: "\u03A3\u03C4\u03BF\u03AF\u03C7\u03B9\u03C3\u03B7 \u03B1\u03C1\u03B9\u03C3\u03C4\u03B5\u03C1\u03AC",
  alignRight: "\u03A3\u03C4\u03BF\u03AF\u03C7\u03B9\u03C3\u03B7 \u03B4\u03B5\u03BE\u03B9\u03AC",
  centerVertically: "\u039A\u03AD\u03BD\u03C4\u03C1\u03BF \u03BA\u03AC\u03B8\u03B5\u03C4\u03B1",
  centerHorizontally: "\u039A\u03AD\u03BD\u03C4\u03C1\u03BF \u03BF\u03C1\u03B9\u03B6\u03CC\u03BD\u03C4\u03B9\u03B1",
  distributeHorizontally: "\u039F\u03C1\u03B9\u03B6\u03CC\u03BD\u03C4\u03B9\u03B1 \u03BA\u03B1\u03C4\u03B1\u03BD\u03BF\u03BC\u03AE",
  distributeVertically: "\u039A\u03B1\u03C4\u03B1\u03BA\u03CC\u03C1\u03C5\u03C6\u03B7 \u03BA\u03B1\u03C4\u03B1\u03BD\u03BF\u03BC\u03AE",
  flipHorizontal: "\u039F\u03C1\u03B9\u03B6\u03CC\u03BD\u03C4\u03B9\u03B1 \u03B1\u03BD\u03B1\u03C3\u03C4\u03C1\u03BF\u03C6\u03AE",
  flipVertical: "\u039A\u03B1\u03C4\u03B1\u03BA\u03CC\u03C1\u03C5\u03C6\u03B7 \u03B1\u03BD\u03B1\u03C3\u03C4\u03C1\u03BF\u03C6\u03AE",
  viewMode: "\u039B\u03B5\u03B9\u03C4\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u03C0\u03C1\u03BF\u03B2\u03BF\u03BB\u03AE\u03C2",
  share: "\u039A\u03BF\u03B9\u03BD\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7",
  showStroke: "\u0395\u03BC\u03C6\u03AC\u03BD\u03B9\u03C3\u03B7 \u03B5\u03C0\u03B9\u03BB\u03BF\u03B3\u03AD\u03B1 \u03C7\u03C1\u03C9\u03BC\u03AC\u03C4\u03C9\u03BD \u03C0\u03B9\u03BD\u03B5\u03BB\u03B9\u03AC\u03C2",
  showBackground: "\u0395\u03BC\u03C6\u03AC\u03BD\u03B9\u03C3\u03B7 \u03B5\u03C0\u03B9\u03BB\u03BF\u03B3\u03AD\u03B1 \u03C7\u03C1\u03CE\u03BC\u03B1\u03C4\u03BF\u03C2 \u03C6\u03CC\u03BD\u03C4\u03BF\u03C5",
  toggleTheme: "\u0395\u03BD\u03B1\u03BB\u03BB\u03B1\u03B3\u03AE \u03B8\u03AD\u03BC\u03B1\u03C4\u03BF\u03C2",
  personalLib: "\u03A0\u03C1\u03BF\u03C3\u03C9\u03C0\u03B9\u03BA\u03AE \u0392\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7",
  excalidrawLib: "\u0392\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7 Excalidraw",
  decreaseFontSize: "\u039C\u03B5\u03AF\u03C9\u03C3\u03B7 \u03BC\u03B5\u03B3\u03AD\u03B8\u03BF\u03C5\u03C2 \u03B3\u03C1\u03B1\u03BC\u03BC\u03B1\u03C4\u03BF\u03C3\u03B5\u03B9\u03C1\u03AC\u03C2",
  increaseFontSize: "\u0391\u03CD\u03BE\u03B7\u03C3\u03B7 \u03BC\u03B5\u03B3\u03AD\u03B8\u03BF\u03C5\u03C2 \u03B3\u03C1\u03B1\u03BC\u03BC\u03B1\u03C4\u03BF\u03C3\u03B5\u03B9\u03C1\u03AC\u03C2",
  unbindText: "\u0391\u03C0\u03BF\u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7 \u03BA\u03B5\u03B9\u03BC\u03AD\u03BD\u03BF\u03C5",
  bindText: "\u0394\u03AD\u03C3\u03BC\u03B5\u03C5\u03C3\u03B7 \u03BA\u03B5\u03B9\u03BC\u03AD\u03BD\u03BF\u03C5 \u03C3\u03C4\u03BF \u03B4\u03BF\u03C7\u03B5\u03AF\u03BF",
  createContainerFromText: "",
  link: {
    edit: "\u0395\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1 \u03C3\u03C5\u03BD\u03B4\u03AD\u03C3\u03BC\u03BF\u03C5",
    editEmbed: "",
    create: "\u0394\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u03C3\u03C5\u03BD\u03B4\u03AD\u03C3\u03BC\u03BF\u03C5",
    createEmbed: "",
    label: "\u03A3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF\u03C2",
    labelEmbed: "",
    empty: ""
  },
  lineEditor: {
    edit: "\u0395\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1 \u03B3\u03C1\u03B1\u03BC\u03BC\u03AE\u03C2",
    exit: "\u0388\u03BE\u03BF\u03B4\u03BF\u03C2 \u03B5\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03C4\u03AE \u03BA\u03B5\u03B9\u03BC\u03AD\u03BD\u03BF\u03C5"
  },
  elementLock: {
    lock: "\u039A\u03BB\u03B5\u03AF\u03B4\u03C9\u03BC\u03B1",
    unlock: "\u039E\u03B5\u03BA\u03BB\u03B5\u03AF\u03B4\u03C9\u03BC\u03B1",
    lockAll: "\u039A\u03BB\u03B5\u03AF\u03B4\u03C9\u03BC\u03B1 \u03CC\u03BB\u03C9\u03BD",
    unlockAll: "\u039E\u03B5\u03BA\u03BB\u03B5\u03AF\u03B4\u03C9\u03BC\u03B1 \u03CC\u03BB\u03C9\u03BD"
  },
  statusPublished: "\u0394\u03B7\u03BC\u03BF\u03C3\u03B9\u03B5\u03C5\u03BC\u03AD\u03BD\u03BF",
  sidebarLock: "\u039A\u03C1\u03B1\u03C4\u03AE\u03C3\u03C4\u03B5 \u03C4\u03B7\u03BD \u03C0\u03BB\u03B1\u03CA\u03BD\u03AE \u03BC\u03C0\u03AC\u03C1\u03B1 \u03B1\u03BD\u03BF\u03B9\u03C7\u03C4\u03AE",
  selectAllElementsInFrame: "",
  removeAllElementsFromFrame: "",
  eyeDropper: "",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "\u0394\u03B5\u03BD \u03AD\u03C7\u03BF\u03C5\u03BD \u03C0\u03C1\u03BF\u03C3\u03C4\u03B5\u03B8\u03B5\u03AF \u03B1\u03BD\u03C4\u03B9\u03BA\u03B5\u03AF\u03BC\u03B5\u03BD\u03B1 \u03B1\u03BA\u03CC\u03BC\u03B7...",
  hint_emptyLibrary: "\u0395\u03C0\u03B9\u03BB\u03AD\u03BE\u03C4\u03B5 \u03AD\u03BD\u03B1 \u03C3\u03C4\u03BF\u03B9\u03C7\u03B5\u03AF\u03BF \u03C3\u03C4\u03BF\u03BD \u03BA\u03B1\u03BC\u03B2\u03AC \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03BF \u03C0\u03C1\u03BF\u03C3\u03B8\u03AD\u03C3\u03B5\u03C4\u03B5 \u03B5\u03B4\u03CE, \u03AE \u03B5\u03B3\u03BA\u03B1\u03C4\u03B1\u03C3\u03C4\u03AE\u03C3\u03C4\u03B5 \u03BC\u03B9\u03B1 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7 \u03B1\u03C0\u03CC \u03C4\u03BF \u03B4\u03B7\u03BC\u03CC\u03C3\u03B9\u03BF \u03B1\u03C0\u03BF\u03B8\u03B5\u03C4\u03AE\u03C1\u03B9\u03BF, \u03C0\u03B1\u03C1\u03B1\u03BA\u03AC\u03C4\u03C9.",
  hint_emptyPrivateLibrary: "\u0395\u03C0\u03B9\u03BB\u03AD\u03BE\u03C4\u03B5 \u03AD\u03BD\u03B1 \u03C3\u03C4\u03BF\u03B9\u03C7\u03B5\u03AF\u03BF \u03C3\u03C4\u03BF\u03BD \u03BA\u03B1\u03BC\u03B2\u03AC \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03BF \u03C0\u03C1\u03BF\u03C3\u03B8\u03AD\u03C3\u03B5\u03C4\u03B5 \u03B5\u03B4\u03CE."
};
var buttons = {
  clearReset: "\u0395\u03C0\u03B1\u03BD\u03B1\u03C6\u03BF\u03C1\u03AC \u03C4\u03BF\u03C5 \u03BA\u03B1\u03BC\u03B2\u03AC",
  exportJSON: "\u0395\u03BE\u03B1\u03B3\u03C9\u03B3\u03AE \u03C3\u03B5 \u03B1\u03C1\u03C7\u03B5\u03AF\u03BF",
  exportImage: "\u0395\u03BE\u03B1\u03B3\u03C9\u03B3\u03AE \u03B5\u03B9\u03BA\u03CC\u03BD\u03B1\u03C2...",
  export: "\u0391\u03C0\u03BF\u03B8\u03AE\u03BA\u03B5\u03C5\u03C3\u03B7 \u03C9\u03C2...",
  copyToClipboard: "\u0391\u03BD\u03C4\u03B9\u03B3\u03C1\u03B1\u03C6\u03AE \u03C3\u03C4\u03BF \u03C0\u03C1\u03CC\u03C7\u03B5\u03B9\u03C1\u03BF",
  save: "\u0391\u03C0\u03BF\u03B8\u03AE\u03BA\u03B5\u03C5\u03C3\u03B7 \u03C3\u03C4\u03BF \u03C4\u03C1\u03AD\u03C7\u03BF\u03BD \u03B1\u03C1\u03C7\u03B5\u03AF\u03BF",
  saveAs: "\u0391\u03C0\u03BF\u03B8\u03AE\u03BA\u03B5\u03C5\u03C3\u03B7 \u03C9\u03C2",
  load: "\u0386\u03BD\u03BF\u03B9\u03B3\u03BC\u03B1",
  getShareableLink: "\u0394\u03B7\u03BC\u03CC\u03C3\u03B9\u03BF\u03C2 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF\u03C2",
  close: "\u039A\u03BB\u03B5\u03AF\u03C3\u03B9\u03BC\u03BF",
  selectLanguage: "\u0395\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE \u03B3\u03BB\u03CE\u03C3\u03C3\u03B1\u03C2",
  scrollBackToContent: "\u039C\u03B5\u03C4\u03B1\u03BA\u03B9\u03BD\u03B7\u03B8\u03B5\u03AF\u03C4\u03B5 \u03C0\u03AF\u03C3\u03C9 \u03C3\u03C4\u03BF \u03C0\u03B5\u03C1\u03B9\u03B5\u03C7\u03CC\u03BC\u03B5\u03BD\u03BF",
  zoomIn: "\u039C\u03B5\u03B3\u03AD\u03B8\u03C5\u03BD\u03C3\u03B7",
  zoomOut: "\u03A3\u03BC\u03AF\u03BA\u03C1\u03C5\u03BD\u03C3\u03B7",
  resetZoom: "\u0395\u03C0\u03B1\u03BD\u03B1\u03C6\u03BF\u03C1\u03AC \u03BC\u03B5\u03B3\u03AD\u03B8\u03C5\u03BD\u03C3\u03B7\u03C2",
  menu: "\u039C\u03B5\u03BD\u03BF\u03CD",
  done: "\u03A4\u03AD\u03BB\u03BF\u03C2",
  edit: "\u0395\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1",
  undo: "\u0391\u03BD\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7",
  redo: "\u0395\u03C0\u03B1\u03BD\u03B1\u03C6\u03BF\u03C1\u03AC",
  resetLibrary: "\u039A\u03B1\u03B8\u03B1\u03C1\u03B9\u03C3\u03BC\u03CC\u03C2 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7\u03C2",
  createNewRoom: "\u0394\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u03BD\u03AD\u03BF\u03C5 \u03C7\u03CE\u03C1\u03BF\u03C5",
  fullScreen: "\u03A0\u03BB\u03AE\u03C1\u03B7\u03C2 \u03BF\u03B8\u03CC\u03BD\u03B7",
  darkMode: "\u03A3\u03BA\u03BF\u03C4\u03B5\u03B9\u03BD\u03AE \u03BB\u03B5\u03B9\u03C4\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1",
  lightMode: "\u03A6\u03C9\u03C4\u03B5\u03B9\u03BD\u03AE \u03BB\u03B5\u03B9\u03C4\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1",
  zenMode: "\u039B\u03B5\u03B9\u03C4\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 Z\u03B5\u03BD",
  objectsSnapMode: "",
  exitZenMode: "\u0388\u03BE\u03BF\u03B4\u03BF\u03C2 \u03B1\u03C0\u03CC \u03C4\u03B7\u03BD \u03BB\u03B5\u03B9\u03C4\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 Zen",
  cancel: "\u0391\u03BA\u03CD\u03C1\u03C9\u03C3\u03B7",
  clear: "\u039A\u03B1\u03B8\u03B1\u03C1\u03B9\u03C3\u03BC\u03CC\u03C2",
  remove: "\u039A\u03B1\u03C4\u03AC\u03C1\u03B3\u03B7\u03C3\u03B7",
  embed: "",
  publishLibrary: "\u0394\u03B7\u03BC\u03BF\u03C3\u03AF\u03B5\u03C5\u03C3\u03B7",
  submit: "\u03A5\u03C0\u03BF\u03B2\u03BF\u03BB\u03AE",
  confirm: "\u0395\u03C0\u03B9\u03B2\u03B5\u03B2\u03B1\u03AF\u03C9\u03C3\u03B7",
  embeddableInteractionButton: ""
};
var alerts = {
  clearReset: "\u0391\u03C5\u03C4\u03CC \u03B8\u03B1 \u03C3\u03B2\u03AE\u03C3\u03B5\u03B9 \u03BF\u03BB\u03CC\u03BA\u03BB\u03B7\u03C1\u03BF \u03C4\u03BF\u03BD \u03BA\u03B1\u03BC\u03B2\u03AC. \u0395\u03AF\u03C3\u03B1\u03B9 \u03C3\u03AF\u03B3\u03BF\u03C5\u03C1\u03BF\u03C2;",
  couldNotCreateShareableLink: "\u0394\u03B5\u03BD \u03AE\u03C4\u03B1\u03BD \u03B4\u03C5\u03BD\u03B1\u03C4\u03AE \u03B7 \u03B4\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u03C3\u03C5\u03BD\u03B4\u03AD\u03C3\u03BC\u03BF\u03C5 \u03BA\u03BF\u03B9\u03BD\u03AE\u03C2 \u03C7\u03C1\u03AE\u03C3\u03B7\u03C2.",
  couldNotCreateShareableLinkTooBig: "\u0394\u03B5\u03BD \u03AE\u03C4\u03B1\u03BD \u03B4\u03C5\u03BD\u03B1\u03C4\u03AE \u03B7 \u03B4\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u03BA\u03BF\u03B9\u03BD\u03CC\u03C7\u03C1\u03B7\u03C3\u03C4\u03BF\u03C5 \u03C3\u03C5\u03BD\u03B4\u03AD\u03C3\u03BC\u03BF\u03C5: \u03B7 \u03C3\u03BA\u03B7\u03BD\u03AE \u03B5\u03AF\u03BD\u03B1\u03B9 \u03C0\u03BF\u03BB\u03CD \u03BC\u03B5\u03B3\u03AC\u03BB\u03B7",
  couldNotLoadInvalidFile: "\u0394\u03B5\u03BD \u03BC\u03C0\u03CC\u03C1\u03B5\u03C3\u03B5 \u03BD\u03B1 \u03B1\u03BD\u03BF\u03AF\u03BE\u03B5\u03B9 \u03B5\u03C3\u03C6\u03B1\u03BB\u03BC\u03AD\u03BD\u03BF \u03B1\u03C1\u03C7\u03B5\u03AF\u03BF",
  importBackendFailed: "\u0397 \u03B5\u03B9\u03C3\u03B1\u03B3\u03C9\u03B3\u03AE \u03B1\u03C0\u03CC \u03C4\u03BF backend \u03B1\u03C0\u03AD\u03C4\u03C5\u03C7\u03B5.",
  cannotExportEmptyCanvas: "\u0394\u03B5\u03BD \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B4\u03C5\u03BD\u03B1\u03C4\u03AE \u03B7 \u03B5\u03BE\u03B1\u03B3\u03C9\u03B3\u03AE \u03BA\u03B5\u03BD\u03BF\u03CD \u03BA\u03B1\u03BC\u03B2\u03AC.",
  couldNotCopyToClipboard: "\u0391\u03B4\u03C5\u03BD\u03B1\u03BC\u03AF\u03B1 \u03B1\u03BD\u03C4\u03B9\u03B3\u03C1\u03B1\u03C6\u03AE\u03C2 \u03C3\u03C4\u03BF \u03C0\u03C1\u03CC\u03C7\u03B5\u03B9\u03C1\u03BF.",
  decryptFailed: "\u0394\u03B5\u03BD \u03AE\u03C4\u03B1\u03BD \u03B4\u03C5\u03BD\u03B1\u03C4\u03AE \u03B7 \u03B1\u03C0\u03BF\u03BA\u03C1\u03C5\u03C0\u03C4\u03BF\u03B3\u03C1\u03AC\u03C6\u03B7\u03C3\u03B7 \u03B4\u03B5\u03B4\u03BF\u03BC\u03AD\u03BD\u03C9\u03BD.",
  uploadedSecurly: "\u0397 \u03BC\u03B5\u03C4\u03B1\u03C6\u03CC\u03C1\u03C4\u03C9\u03C3\u03B7 \u03AD\u03C7\u03B5\u03B9 \u03B5\u03BE\u03B1\u03C3\u03C6\u03B1\u03BB\u03B9\u03C3\u03C4\u03B5\u03AF \u03BC\u03B5 \u03BA\u03C1\u03C5\u03C0\u03C4\u03BF\u03B3\u03C1\u03AC\u03C6\u03B7\u03C3\u03B7 \u03B1\u03C0\u03CC \u03AC\u03BA\u03C1\u03BF \u03C3\u03B5 \u03AC\u03BA\u03C1\u03BF, \u03C0\u03C1\u03AC\u03B3\u03BC\u03B1 \u03C0\u03BF\u03C5 \u03C3\u03B7\u03BC\u03B1\u03AF\u03BD\u03B5\u03B9 \u03CC\u03C4\u03B9 \u03BF \u03B4\u03B9\u03B1\u03BA\u03BF\u03BC\u03B9\u03C3\u03C4\u03AE\u03C2 Excalidraw \u03BA\u03B1\u03B9 \u03C4\u03C1\u03AF\u03C4\u03B1 \u03BC\u03AD\u03C1\u03B7 \u03B4\u03B5\u03BD \u03BC\u03C0\u03BF\u03C1\u03BF\u03CD\u03BD \u03BD\u03B1 \u03B4\u03B9\u03B1\u03B2\u03AC\u03C3\u03BF\u03C5\u03BD \u03C4\u03BF \u03C0\u03B5\u03C1\u03B9\u03B5\u03C7\u03CC\u03BC\u03B5\u03BD\u03BF.",
  loadSceneOverridePrompt: "\u0397 \u03C6\u03CC\u03C1\u03C4\u03C9\u03C3\u03B7 \u03B5\u03BE\u03C9\u03C4\u03B5\u03C1\u03B9\u03BA\u03BF\u03CD \u03C3\u03C7\u03B5\u03B4\u03AF\u03BF\u03C5 \u03B8\u03B1 \u03B1\u03BD\u03C4\u03B9\u03BA\u03B1\u03C4\u03B1\u03C3\u03C4\u03AE\u03C3\u03B5\u03B9 \u03C4\u03BF \u03C5\u03C0\u03AC\u03C1\u03C7\u03BF\u03BD \u03C0\u03B5\u03C1\u03B9\u03B5\u03C7\u03CC\u03BC\u03B5\u03BD\u03BF. \u0395\u03C0\u03B9\u03B8\u03C5\u03BC\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5;",
  collabStopOverridePrompt: "\u0397 \u03B4\u03B9\u03B1\u03BA\u03BF\u03C0\u03AE \u03C4\u03B7\u03C2 \u03C3\u03C5\u03BD\u03B5\u03B4\u03C1\u03AF\u03B1\u03C2 \u03B8\u03B1 \u03B1\u03BD\u03C4\u03B9\u03BA\u03B1\u03C4\u03B1\u03C3\u03C4\u03AE\u03C3\u03B5\u03B9 \u03C4\u03BF \u03C0\u03C1\u03BF\u03B7\u03B3\u03BF\u03CD\u03BC\u03B5\u03BD\u03BF, \u03C4\u03BF\u03C0\u03B9\u03BA\u03AC \u03B1\u03C0\u03BF\u03B8\u03B7\u03BA\u03B5\u03C5\u03BC\u03AD\u03BD\u03BF \u03C3\u03C7\u03AD\u03B4\u03B9\u03BF. \u0395\u03AF\u03C3\u03C4\u03B5 \u03C3\u03AF\u03B3\u03BF\u03C5\u03C1\u03BF\u03B9?\n\n(\u0391\u03BD \u03B8\u03AD\u03BB\u03B5\u03C4\u03B5 \u03BD\u03B1 \u03B4\u03B9\u03B1\u03C4\u03B7\u03C1\u03AE\u03C3\u03B5\u03C4\u03B5 \u03C4\u03BF \u03C4\u03BF\u03C0\u03B9\u03BA\u03CC \u03C3\u03B1\u03C2 \u03C3\u03C7\u03AD\u03B4\u03B9\u03BF, \u03B1\u03C0\u03BB\u03AC \u03BA\u03BB\u03B5\u03AF\u03C3\u03C4\u03B5 \u03C4\u03B7\u03BD \u03BA\u03B1\u03C1\u03C4\u03AD\u03BB\u03B1 \u03C4\u03BF\u03C5 \u03C0\u03C1\u03BF\u03B3\u03C1\u03AC\u03BC\u03BC\u03B1\u03C4\u03BF\u03C2 \u03C0\u03B5\u03C1\u03B9\u03AE\u03B3\u03B7\u03C3\u03B7\u03C2.)",
  errorAddingToLibrary: "\u0391\u03B4\u03C5\u03BD\u03B1\u03BC\u03AF\u03B1 \u03C0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7\u03C2 \u03B1\u03BD\u03C4\u03B9\u03BA\u03B5\u03B9\u03BC\u03AD\u03BD\u03BF\u03C5 \u03C3\u03C4\u03B7 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7",
  errorRemovingFromLibrary: "\u0391\u03B4\u03C5\u03BD\u03B1\u03BC\u03AF\u03B1 \u03B1\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7\u03C2 \u03B1\u03BD\u03C4\u03B9\u03BA\u03B5\u03B9\u03BC\u03AD\u03BD\u03BF\u03C5 \u03B1\u03C0\u03CC \u03C4\u03B7 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7",
  confirmAddLibrary: "\u0391\u03C5\u03C4\u03CC \u03B8\u03B1 \u03C0\u03C1\u03BF\u03C3\u03B8\u03AD\u03C3\u03B5\u03B9 {{numShapes}} \u03C3\u03C7\u03AE\u03BC\u03B1(\u03C4\u03B1) \u03C3\u03C4\u03B7 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7 \u03C3\u03B1\u03C2. \u0395\u03AF\u03C3\u03C4\u03B5 \u03C3\u03AF\u03B3\u03BF\u03C5\u03C1\u03BF\u03B9;",
  imageDoesNotContainScene: "\u0391\u03C5\u03C4\u03AE \u03B7 \u03B5\u03B9\u03BA\u03CC\u03BD\u03B1 \u03B4\u03B5\u03BD \u03C6\u03B1\u03AF\u03BD\u03B5\u03C4\u03B1\u03B9 \u03BD\u03B1 \u03C0\u03B5\u03C1\u03B9\u03AD\u03C7\u03B5\u03B9 \u03B4\u03B5\u03B4\u03BF\u03BC\u03AD\u03BD\u03B1 \u03C3\u03BA\u03B7\u03BD\u03AE\u03C2. \u0388\u03C7\u03B5\u03C4\u03B5 \u03B5\u03BD\u03B5\u03C1\u03B3\u03BF\u03C0\u03BF\u03B9\u03AE\u03C3\u03B5\u03B9 \u03C4\u03B7\u03BD \u03B5\u03BD\u03C3\u03C9\u03BC\u03AC\u03C4\u03C9\u03C3\u03B7 \u03C3\u03BA\u03B7\u03BD\u03AE\u03C2 \u03BA\u03B1\u03C4\u03AC \u03C4\u03B7\u03BD \u03B5\u03BE\u03B1\u03B3\u03C9\u03B3\u03AE;",
  cannotRestoreFromImage: "\u0397 \u03C3\u03BA\u03B7\u03BD\u03AE \u03B4\u03B5\u03BD \u03AE\u03C4\u03B1\u03BD \u03B4\u03C5\u03BD\u03B1\u03C4\u03CC \u03BD\u03B1 \u03B1\u03C0\u03BF\u03BA\u03B1\u03C4\u03B1\u03C3\u03C4\u03B1\u03B8\u03B5\u03AF \u03B1\u03C0\u03CC \u03B1\u03C5\u03C4\u03CC \u03C4\u03BF \u03B1\u03C1\u03C7\u03B5\u03AF\u03BF \u03B5\u03B9\u03BA\u03CC\u03BD\u03B1\u03C2",
  invalidSceneUrl: "\u0394\u03B5\u03BD \u03AE\u03C4\u03B1\u03BD \u03B4\u03C5\u03BD\u03B1\u03C4\u03AE \u03B7 \u03B5\u03B9\u03C3\u03B1\u03B3\u03C9\u03B3\u03AE \u03C3\u03BA\u03B7\u03BD\u03AE\u03C2 \u03B1\u03C0\u03CC \u03C4\u03BF URL \u03C0\u03BF\u03C5 \u03B4\u03CE\u03C3\u03B1\u03C4\u03B5. \u0395\u03AF\u03C4\u03B5 \u03AD\u03C7\u03B5\u03B9 \u03BB\u03AC\u03B8\u03BF\u03C2 \u03BC\u03BF\u03C1\u03C6\u03AE, \u03B5\u03AF\u03C4\u03B5 \u03B4\u03B5\u03BD \u03C0\u03B5\u03C1\u03B9\u03AD\u03C7\u03B5\u03B9 \u03AD\u03B3\u03BA\u03C5\u03C1\u03B1 \u03B4\u03B5\u03B4\u03BF\u03BC\u03AD\u03BD\u03B1 JSON Excalidraw.",
  resetLibrary: "\u0391\u03C5\u03C4\u03CC \u03B8\u03B1 \u03BA\u03B1\u03B8\u03B1\u03C1\u03AF\u03C3\u03B5\u03B9 \u03C4\u03B7 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7 \u03C3\u03B1\u03C2. \u0395\u03AF\u03C3\u03C4\u03B5 \u03C3\u03AF\u03B3\u03BF\u03C5\u03C1\u03BF\u03B9;",
  removeItemsFromsLibrary: "\u0394\u03B9\u03B1\u03B3\u03C1\u03B1\u03C6\u03AE {{count}} \u03B1\u03BD\u03C4\u03B9\u03BA\u03B5\u03B9\u03BC\u03AD\u03BD\u03BF\u03C5(\u03C9\u03BD) \u03B1\u03C0\u03CC \u03C4\u03B7 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7;",
  invalidEncryptionKey: "\u03A4\u03BF \u03BA\u03BB\u03B5\u03B9\u03B4\u03AF \u03BA\u03C1\u03C5\u03C0\u03C4\u03BF\u03B3\u03C1\u03AC\u03C6\u03B7\u03C3\u03B7\u03C2 \u03C0\u03C1\u03AD\u03C0\u03B5\u03B9 \u03BD\u03B1 \u03B5\u03AF\u03BD\u03B1\u03B9 22 \u03C7\u03B1\u03C1\u03B1\u03BA\u03C4\u03AE\u03C1\u03B5\u03C2. \u0397 \u03B6\u03C9\u03BD\u03C4\u03B1\u03BD\u03AE \u03C3\u03C5\u03BD\u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B1\u03C0\u03B5\u03BD\u03B5\u03C1\u03B3\u03BF\u03C0\u03BF\u03B9\u03B7\u03BC\u03AD\u03BD\u03B7.",
  collabOfflineWarning: "\u0394\u03B5\u03BD \u03C5\u03C0\u03AC\u03C1\u03C7\u03B5\u03B9 \u03B4\u03B9\u03B1\u03B8\u03AD\u03C3\u03B9\u03BC\u03B7 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7 \u03C3\u03C4\u03BF internet.\n\u039F\u03B9 \u03B1\u03BB\u03BB\u03B1\u03B3\u03AD\u03C2 \u03C3\u03B1\u03C2 \u03B4\u03B5\u03BD \u03B8\u03B1 \u03B1\u03C0\u03BF\u03B8\u03B7\u03BA\u03B5\u03C5\u03C4\u03BF\u03CD\u03BD!"
};
var errors = {
  unsupportedFileType: "\u039C\u03B7 \u03C5\u03C0\u03BF\u03C3\u03C4\u03B7\u03C1\u03B9\u03B6\u03CC\u03BC\u03B5\u03BD\u03BF\u03C2 \u03C4\u03CD\u03C0\u03BF\u03C2 \u03B1\u03C1\u03C7\u03B5\u03AF\u03BF\u03C5.",
  imageInsertError: "\u0391\u03B4\u03C5\u03BD\u03B1\u03BC\u03AF\u03B1 \u03B5\u03B9\u03C3\u03B1\u03B3\u03C9\u03B3\u03AE\u03C2 \u03B5\u03B9\u03BA\u03CC\u03BD\u03B1\u03C2. \u03A0\u03C1\u03BF\u03C3\u03C0\u03B1\u03B8\u03AE\u03C3\u03C4\u03B5 \u03BE\u03B1\u03BD\u03AC \u03B1\u03C1\u03B3\u03CC\u03C4\u03B5\u03C1\u03B1...",
  fileTooBig: "\u03A4\u03BF \u03B1\u03C1\u03C7\u03B5\u03AF\u03BF \u03B5\u03AF\u03BD\u03B1\u03B9 \u03C0\u03BF\u03BB\u03CD \u03BC\u03B5\u03B3\u03AC\u03BB\u03BF. \u03A4\u03BF \u03BC\u03AD\u03B3\u03B9\u03C3\u03C4\u03BF \u03B5\u03C0\u03B9\u03C4\u03C1\u03B5\u03C0\u03CC\u03BC\u03B5\u03BD\u03BF \u03BC\u03AD\u03B3\u03B5\u03B8\u03BF\u03C2 \u03B5\u03AF\u03BD\u03B1\u03B9 {{maxSize}}.",
  svgImageInsertError: "\u0391\u03B4\u03C5\u03BD\u03B1\u03BC\u03AF\u03B1 \u03B5\u03B9\u03C3\u03B1\u03B3\u03C9\u03B3\u03AE\u03C2 \u03B5\u03B9\u03BA\u03CC\u03BD\u03B1\u03C2 SVG. \u0397 \u03C3\u03AE\u03BC\u03B1\u03BD\u03C3\u03B7 \u03C4\u03B7\u03C2 SVG \u03B4\u03B5\u03BD \u03C6\u03B1\u03AF\u03BD\u03B5\u03C4\u03B1\u03B9 \u03AD\u03B3\u03BA\u03C5\u03C1\u03B7.",
  failedToFetchImage: "",
  invalidSVGString: "\u039C\u03B7 \u03AD\u03B3\u03BA\u03C5\u03C1\u03BF SVG.",
  cannotResolveCollabServer: "\u0391\u03B4\u03C5\u03BD\u03B1\u03BC\u03AF\u03B1 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7\u03C2 \u03BC\u03B5 \u03C4\u03BF\u03BD \u03B4\u03B9\u03B1\u03BA\u03BF\u03BC\u03B9\u03C3\u03C4\u03AE \u03C3\u03C5\u03BD\u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1\u03C2. \u03A0\u03B1\u03C1\u03B1\u03BA\u03B1\u03BB\u03CE \u03B1\u03BD\u03B1\u03BD\u03B5\u03CE\u03C3\u03C4\u03B5 \u03C4\u03B7 \u03C3\u03B5\u03BB\u03AF\u03B4\u03B1 \u03BA\u03B1\u03B9 \u03C0\u03C1\u03BF\u03C3\u03C0\u03B1\u03B8\u03AE\u03C3\u03C4\u03B5 \u03BE\u03B1\u03BD\u03AC.",
  importLibraryError: "\u0391\u03B4\u03C5\u03BD\u03B1\u03BC\u03AF\u03B1 \u03C6\u03CC\u03C1\u03C4\u03C9\u03C3\u03B7\u03C2 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7\u03C2",
  collabSaveFailed: "\u0397 \u03B1\u03C0\u03BF\u03B8\u03AE\u03BA\u03B5\u03C5\u03C3\u03B7 \u03C3\u03C4\u03B7 \u03B2\u03AC\u03C3\u03B7 \u03B4\u03B5\u03B4\u03BF\u03BC\u03AD\u03BD\u03C9\u03BD \u03B4\u03B5\u03BD \u03AE\u03C4\u03B1\u03BD \u03B4\u03C5\u03BD\u03B1\u03C4\u03AE. \u0391\u03BD \u03C4\u03BF \u03C0\u03C1\u03BF\u03B2\u03BB\u03AE\u03BC\u03B1\u03C4\u03B1 \u03C0\u03B1\u03C1\u03B1\u03BC\u03B5\u03AF\u03BD\u03B5\u03B9, \u03B8\u03B1 \u03C0\u03C1\u03AD\u03C0\u03B5\u03B9 \u03BD\u03B1 \u03B1\u03C0\u03BF\u03B8\u03B7\u03BA\u03B5\u03CD\u03C3\u03B5\u03C4\u03B5 \u03C4\u03BF \u03B1\u03C1\u03C7\u03B5\u03AF\u03BF \u03C3\u03B1\u03C2 \u03C4\u03BF\u03C0\u03B9\u03BA\u03AC \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03B2\u03B5\u03B2\u03B1\u03B9\u03C9\u03B8\u03B5\u03AF\u03C4\u03B5 \u03CC\u03C4\u03B9 \u03B4\u03B5\u03BD \u03C7\u03AC\u03BD\u03B5\u03C4\u03B5 \u03C4\u03B7\u03BD \u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1 \u03C3\u03B1\u03C2.",
  collabSaveFailed_sizeExceeded: "\u0397 \u03B1\u03C0\u03BF\u03B8\u03AE\u03BA\u03B5\u03C5\u03C3\u03B7 \u03C3\u03C4\u03B7 \u03B2\u03AC\u03C3\u03B7 \u03B4\u03B5\u03B4\u03BF\u03BC\u03AD\u03BD\u03C9\u03BD \u03B4\u03B5\u03BD \u03AE\u03C4\u03B1\u03BD \u03B4\u03C5\u03BD\u03B1\u03C4\u03AE, \u03BF \u03BA\u03B1\u03BC\u03B2\u03AC\u03C2 \u03C6\u03B1\u03AF\u03BD\u03B5\u03C4\u03B1\u03B9 \u03BD\u03B1 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03C0\u03BF\u03BB\u03CD \u03BC\u03B5\u03B3\u03AC\u03BB\u03BF\u03C2. \u0398\u03B1 \u03C0\u03C1\u03AD\u03C0\u03B5\u03B9 \u03BD\u03B1 \u03B1\u03C0\u03BF\u03B8\u03B7\u03BA\u03B5\u03CD\u03C3\u03B5\u03C4\u03B5 \u03C4\u03BF \u03B1\u03C1\u03C7\u03B5\u03AF\u03BF \u03C4\u03BF\u03C0\u03B9\u03BA\u03AC \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03B2\u03B5\u03B2\u03B1\u03B9\u03C9\u03B8\u03B5\u03AF\u03C4\u03B5 \u03CC\u03C4\u03B9 \u03B4\u03B5\u03BD \u03B8\u03B1 \u03C7\u03AC\u03C3\u03B5\u03C4\u03B5 \u03C4\u03B7\u03BD \u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1 \u03C3\u03B1\u03C2.",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "",
    line2: "",
    line3: "",
    line4: ""
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "",
    image: ""
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "\u0395\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE",
  image: "\u0395\u03B9\u03C3\u03B1\u03B3\u03C9\u03B3\u03AE \u03B5\u03B9\u03BA\u03CC\u03BD\u03B1\u03C2",
  rectangle: "\u039F\u03C1\u03B8\u03BF\u03B3\u03CE\u03BD\u03B9\u03BF",
  diamond: "\u03A1\u03CC\u03BC\u03B2\u03BF\u03C2",
  ellipse: "\u0388\u03BB\u03BB\u03B5\u03B9\u03C8\u03B7",
  arrow: "\u0392\u03AD\u03BB\u03BF\u03C2",
  line: "\u0393\u03C1\u03B1\u03BC\u03BC\u03AE",
  freedraw: "\u03A3\u03C7\u03B5\u03B4\u03AF\u03B1\u03C3\u03B7",
  text: "\u039A\u03B5\u03AF\u03BC\u03B5\u03BD\u03BF",
  library: "\u0392\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7",
  lock: "\u039A\u03C1\u03AC\u03C4\u03B7\u03C3\u03B5 \u03B5\u03C0\u03B9\u03BB\u03B5\u03B3\u03BC\u03AD\u03BD\u03BF \u03C4\u03BF \u03B5\u03C1\u03B3\u03B1\u03BB\u03B5\u03AF\u03BF \u03BC\u03B5\u03C4\u03AC \u03C4\u03BF \u03C3\u03C7\u03AD\u03B4\u03B9\u03BF",
  penMode: "\u039B\u03B5\u03B9\u03C4\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u03BC\u03BF\u03BB\u03C5\u03B2\u03B9\u03BF\u03CD - \u03B1\u03C0\u03BF\u03C4\u03C1\u03BF\u03C0\u03AE \u03B1\u03C6\u03AE\u03C2",
  link: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7/ \u0395\u03BD\u03B7\u03BC\u03AD\u03C1\u03C9\u03C3\u03B7 \u03C3\u03C5\u03BD\u03B4\u03AD\u03C3\u03BC\u03BF\u03C5 \u03B3\u03B9\u03B1 \u03AD\u03BD\u03B1 \u03B5\u03C0\u03B9\u03BB\u03B5\u03B3\u03BC\u03AD\u03BD\u03BF \u03C3\u03C7\u03AE\u03BC\u03B1",
  eraser: "\u0393\u03CC\u03BC\u03B1",
  frame: "",
  magicframe: "",
  embeddable: "",
  laser: "",
  hand: "",
  extraTools: "",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "\u0395\u03BD\u03AD\u03C1\u03B3\u03B5\u03B9\u03B5\u03C2 \u03BA\u03B1\u03BC\u03B2\u03AC",
  selectedShapeActions: "\u0395\u03C0\u03B9\u03BB\u03B5\u03B3\u03BC\u03AD\u03BD\u03B5\u03C2 \u03B5\u03BD\u03AD\u03C1\u03B3\u03B5\u03B9\u03B5\u03C2 \u03C3\u03C7\u03AE\u03BC\u03B1\u03C4\u03BF\u03C2",
  shapes: "\u03A3\u03C7\u03AE\u03BC\u03B1\u03C4\u03B1"
};
var hints = {
  canvasPanning: "",
  linearElement: "\u039A\u03AC\u03BD\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03BE\u03B5\u03BA\u03B9\u03BD\u03AE\u03C3\u03B5\u03B9\u03C2 \u03C0\u03BF\u03BB\u03BB\u03B1\u03C0\u03BB\u03AC \u03C3\u03B7\u03BC\u03B5\u03AF\u03B1, \u03C3\u03CD\u03C1\u03B5 \u03B3\u03B9\u03B1 \u03BC\u03B9\u03B1 \u03B3\u03C1\u03B1\u03BC\u03BC\u03AE",
  freeDraw: "\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03BA\u03B1\u03B9 \u03C3\u03CD\u03C1\u03C4\u03B5, \u03B1\u03C0\u03B5\u03BB\u03B5\u03C5\u03B8\u03B5\u03C1\u03CE\u03C3\u03B1\u03C4\u03B5 \u03CC\u03C4\u03B1\u03BD \u03AD\u03C7\u03B5\u03C4\u03B5 \u03C4\u03B5\u03BB\u03B5\u03B9\u03CE\u03C3\u03B5\u03B9",
  text: "Tip: \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03B5\u03C0\u03AF\u03C3\u03B7\u03C2 \u03BD\u03B1 \u03C0\u03C1\u03BF\u03C3\u03B8\u03AD\u03C3\u03C4\u03B5 \u03BA\u03B5\u03AF\u03BC\u03B5\u03BD\u03BF \u03BC\u03B5 \u03B4\u03B9\u03C0\u03BB\u03CC-\u03BA\u03BB\u03B9\u03BA \u03BF\u03C0\u03BF\u03C5\u03B4\u03AE\u03C0\u03BF\u03C4\u03B5 \u03BC\u03B5 \u03C4\u03BF \u03B5\u03C1\u03B3\u03B1\u03BB\u03B5\u03AF\u03BF \u03B5\u03C0\u03B9\u03BB\u03BF\u03B3\u03CE\u03BD",
  embeddable: "",
  text_selected: "\u039A\u03AC\u03BD\u03C4\u03B5 \u03B4\u03B9\u03C0\u03BB\u03CC \u03BA\u03BB\u03B9\u03BA \u03AE \u03C0\u03B1\u03C4\u03AE\u03C3\u03C4\u03B5 ENTER \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03B5\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03C4\u03B5\u03AF\u03C4\u03B5 \u03C4\u03BF \u03BA\u03B5\u03AF\u03BC\u03B5\u03BD\u03BF",
  text_editing: "\u03A0\u03B1\u03C4\u03AE\u03C3\u03C4\u03B5 Escape \u03AE CtrlOrCmd+ENTER \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03BF\u03BB\u03BF\u03BA\u03BB\u03B7\u03C1\u03CE\u03C3\u03B5\u03C4\u03B5 \u03C4\u03B7\u03BD \u03B5\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1",
  linearElementMulti: "\u039A\u03AC\u03BD\u03B5 \u03BA\u03BB\u03B9\u03BA \u03C3\u03C4\u03BF \u03C4\u03B5\u03BB\u03B5\u03C5\u03C4\u03B1\u03AF\u03BF \u03C3\u03B7\u03BC\u03B5\u03AF\u03BF \u03AE \u03C0\u03AC\u03C4\u03B7\u03C3\u03B5 Escape \u03AE Enter \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03B5\u03BB\u03B5\u03B9\u03CE\u03C3\u03B5\u03B9\u03C2",
  lockAngle: "\u039C\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03C0\u03B5\u03C1\u03B9\u03BF\u03C1\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C4\u03B7 \u03B3\u03C9\u03BD\u03AF\u03B1 \u03BA\u03C1\u03B1\u03C4\u03CE\u03BD\u03C4\u03B1\u03C2 \u03C0\u03B1\u03C4\u03B7\u03BC\u03AD\u03BD\u03BF \u03C4\u03BF SHIFT",
  resize: "\u039C\u03C0\u03BF\u03C1\u03B5\u03AF\u03C2 \u03BD\u03B1 \u03C0\u03B5\u03C1\u03B9\u03BF\u03C1\u03AF\u03C3\u03B5\u03B9\u03C2 \u03C4\u03B9\u03C2 \u03B1\u03BD\u03B1\u03BB\u03BF\u03B3\u03AF\u03B5\u03C2 \u03BA\u03C1\u03B1\u03C4\u03CE\u03BD\u03C4\u03B1\u03C2 \u03C4\u03BF SHIFT \u03B5\u03BD\u03CE \u03B1\u03BB\u03BB\u03AC\u03B6\u03B5\u03B9\u03C2 \u03BC\u03AD\u03B3\u03B5\u03B8\u03BF\u03C2,\n\u03BA\u03C1\u03AC\u03C4\u03B7\u03C3\u03B5 \u03C0\u03B1\u03C4\u03B7\u03BC\u03AD\u03BD\u03BF \u03C4\u03BF ALT \u03B3\u03B9\u03B1 \u03B1\u03BB\u03BB\u03B1\u03B3\u03AE \u03BC\u03B5\u03B3\u03AD\u03B8\u03BF\u03C5\u03C2 \u03B1\u03C0\u03CC \u03C4\u03BF \u03BA\u03AD\u03BD\u03C4\u03C1\u03BF",
  resizeImage: "\u039C\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03B1\u03BB\u03BB\u03AC\u03BE\u03B5\u03C4\u03B5 \u03C4\u03BF \u03BC\u03AD\u03B3\u03B5\u03B8\u03BF\u03C2 \u03B5\u03BB\u03B5\u03CD\u03B8\u03B5\u03C1\u03B1 \u03BA\u03C1\u03B1\u03C4\u03CE\u03BD\u03C4\u03B1\u03C2 \u03C0\u03B1\u03C4\u03B7\u03BC\u03AD\u03BD\u03BF \u03C4\u03BF SHIFT,\n\u03BA\u03C1\u03B1\u03C4\u03AE\u03C3\u03C4\u03B5 \u03C0\u03B1\u03C4\u03B7\u03BC\u03AD\u03BD\u03BF \u03C4\u03BF ALT \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03B1\u03BB\u03BB\u03AC\u03BE\u03B5\u03C4\u03B5 \u03C4\u03BF \u03BC\u03AD\u03B3\u03B5\u03B8\u03BF\u03C2 \u03B1\u03C0\u03CC \u03C4\u03BF \u03BA\u03AD\u03BD\u03C4\u03C1\u03BF",
  rotate: "\u039C\u03C0\u03BF\u03C1\u03B5\u03AF\u03C2 \u03BD\u03B1 \u03C0\u03B5\u03C1\u03B9\u03BF\u03C1\u03AF\u03C3\u03B5\u03B9\u03C2 \u03C4\u03B9\u03C2 \u03B3\u03C9\u03BD\u03AF\u03B5\u03C2 \u03BA\u03C1\u03B1\u03C4\u03CE\u03BD\u03C4\u03B1\u03C2 \u03C0\u03B1\u03C4\u03B7\u03BC\u03AD\u03BD\u03BF \u03C4\u03BF \u03C0\u03BB\u03AE\u03BA\u03C4\u03C1\u03BF SHIFT \u03BA\u03B1\u03C4\u03AC \u03C4\u03B7\u03BD \u03C0\u03B5\u03C1\u03B9\u03C3\u03C4\u03C1\u03BF\u03C6\u03AE",
  lineEditor_info: "\u039A\u03C1\u03B1\u03C4\u03AE\u03C3\u03C4\u03B5 \u03C0\u03B1\u03C4\u03B7\u03BC\u03AD\u03BD\u03BF Ctrl \u03AE Cmd \u03BA\u03B1\u03B9 \u03C0\u03B1\u03C4\u03AE\u03C3\u03C4\u03B5 \u03C4\u03BF \u03C0\u03BB\u03AE\u03BA\u03C4\u03C1\u03BF Ctrl \u03AE Cmd + Enter \u03B3\u03B9\u03B1 \u03B5\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1 \u03C3\u03B7\u03BC\u03B5\u03AF\u03C9\u03BD",
  lineEditor_pointSelected: "\u03A0\u03B1\u03C4\u03AE\u03C3\u03C4\u03B5 \u0394\u03B9\u03B1\u03B3\u03C1\u03B1\u03C6\u03AE \u03B3\u03B9\u03B1 \u03B1\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 \u03C3\u03B7\u03BC\u03B5\u03AF\u03BF\u03C5(\u03C9\u03BD),\nCtrlOrCmd+D \u03B3\u03B9\u03B1 \u03B1\u03BD\u03C4\u03B9\u03B3\u03C1\u03B1\u03C6\u03AE, \u03AE \u03C3\u03CD\u03C1\u03B5\u03C4\u03B5 \u03B3\u03B9\u03B1 \u03BC\u03B5\u03C4\u03B1\u03BA\u03AF\u03BD\u03B7\u03C3\u03B7",
  lineEditor_nothingSelected: "\u0395\u03C0\u03B9\u03BB\u03AD\u03BE\u03C4\u03B5 \u03AD\u03BD\u03B1 \u03C3\u03B7\u03BC\u03B5\u03AF\u03BF \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03B5\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03C4\u03B5\u03AF\u03C4\u03B5 (\u03BA\u03C1\u03B1\u03C4\u03AE\u03C3\u03C4\u03B5 \u03C0\u03B1\u03C4\u03B7\u03BC\u03AD\u03BD\u03BF \u03C4\u03BF SHIFT \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03B5\u03C0\u03B9\u03BB\u03AD\u03BE\u03B5\u03C4\u03B5 \u03C0\u03BF\u03BB\u03BB\u03B1\u03C0\u03BB\u03AC),\n\u03AE \u03BA\u03C1\u03B1\u03C4\u03AE\u03C3\u03C4\u03B5 \u03C0\u03B1\u03C4\u03B7\u03BC\u03AD\u03BD\u03BF \u03C4\u03BF Alt \u03BA\u03B1\u03B9 \u03BA\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C0\u03C1\u03BF\u03C3\u03B8\u03AD\u03C3\u03B5\u03C4\u03B5 \u03BD\u03AD\u03B1 \u03C3\u03B7\u03BC\u03B5\u03AF\u03B1",
  placeImage: "\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03BF\u03C0\u03BF\u03B8\u03B5\u03C4\u03AE\u03C3\u03B5\u03C4\u03B5 \u03C4\u03B7\u03BD \u03B5\u03B9\u03BA\u03CC\u03BD\u03B1 \u03AE \u03BA\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03BA\u03B1\u03B9 \u03C3\u03CD\u03C1\u03B5\u03C4\u03B5 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03BF\u03C1\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C4\u03BF \u03BC\u03AD\u03B3\u03B5\u03B8\u03CC\u03C2 \u03C4\u03B7\u03C2 \u03C7\u03B5\u03B9\u03C1\u03BF\u03BA\u03AF\u03BD\u03B7\u03C4\u03B1",
  publishLibrary: "\u0394\u03B7\u03BC\u03BF\u03C3\u03B9\u03B5\u03CD\u03C3\u03C4\u03B5 \u03C4\u03B7 \u03B4\u03B9\u03BA\u03AE \u03C3\u03B1\u03C2 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7",
  bindTextToElement: "\u03A0\u03B1\u03C4\u03AE\u03C3\u03C4\u03B5 Enter \u03B3\u03B9\u03B1 \u03C0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03BA\u03B5\u03B9\u03BC\u03AD\u03BD\u03BF\u03C5",
  deepBoxSelect: "\u039A\u03C1\u03B1\u03C4\u03AE\u03C3\u03C4\u03B5 \u03C0\u03B1\u03C4\u03B7\u03BC\u03AD\u03BD\u03BF \u03C4\u03BF CtrlOrCmd \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03B5\u03C0\u03B9\u03BB\u03AD\u03BE\u03B5\u03C4\u03B5 \u03B2\u03B1\u03B8\u03B9\u03AC, \u03BA\u03B1\u03B9 \u03BD\u03B1 \u03B1\u03C0\u03BF\u03C4\u03C1\u03AD\u03C8\u03B5\u03C4\u03B5 \u03C4\u03B7 \u03BC\u03B5\u03C4\u03B1\u03C6\u03BF\u03C1\u03AC",
  eraserRevert: "\u039A\u03C1\u03B1\u03C4\u03AE\u03C3\u03C4\u03B5 \u03C0\u03B1\u03C4\u03B7\u03BC\u03AD\u03BD\u03BF \u03C4\u03BF Alt \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03B5\u03C0\u03B1\u03BD\u03B1\u03C6\u03AD\u03C1\u03B5\u03C4\u03B5 \u03C4\u03B1 \u03C3\u03C4\u03BF\u03B9\u03C7\u03B5\u03AF\u03B1 \u03C0\u03BF\u03C5 \u03C3\u03B7\u03BC\u03B5\u03B9\u03CE\u03B8\u03B7\u03BA\u03B1\u03BD \u03B3\u03B9\u03B1 \u03B4\u03B9\u03B1\u03B3\u03C1\u03B1\u03C6\u03AE",
  firefox_clipboard_write: '\u0391\u03C5\u03C4\u03AE \u03B7 \u03B5\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF \u03C0\u03B9\u03B8\u03B1\u03BD\u03CE\u03C2 \u03BD\u03B1 \u03B5\u03BD\u03B5\u03C1\u03B3\u03BF\u03C0\u03BF\u03B9\u03B7\u03B8\u03B5\u03AF \u03B1\u03BB\u03BB\u03AC\u03B6\u03BF\u03BD\u03C4\u03B1\u03C2 \u03C4\u03B7\u03BD \u03C1\u03CD\u03B8\u03BC\u03B9\u03C3\u03B7 "dom.events.asyncClipboard.clipboardItem" \u03C3\u03B5 "true". \u0393\u03B9\u03B1 \u03BD\u03B1 \u03B1\u03BB\u03BB\u03AC\u03BE\u03B5\u03C4\u03B5 \u03C4\u03B9\u03C2 \u03C1\u03C5\u03B8\u03BC\u03AF\u03C3\u03B5\u03B9\u03C2 \u03C4\u03BF\u03C5 \u03C0\u03C1\u03BF\u03B3\u03C1\u03AC\u03BC\u03BC\u03B1\u03C4\u03BF\u03C2 \u03C0\u03B5\u03C1\u03B9\u03AE\u03B3\u03B7\u03C3\u03B7\u03C2 \u03C3\u03C4\u03BF Firefox, \u03B5\u03C0\u03B9\u03C3\u03BA\u03B5\u03C6\u03B8\u03B5\u03AF\u03C4\u03B5 \u03C4\u03B7 \u03C3\u03B5\u03BB\u03AF\u03B4\u03B1 "about:config".',
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "\u0391\u03B4\u03C5\u03BD\u03B1\u03BC\u03AF\u03B1 \u03B5\u03BC\u03C6\u03AC\u03BD\u03B9\u03C3\u03B7\u03C2 \u03C0\u03C1\u03BF\u03B5\u03C0\u03B9\u03C3\u03BA\u03CC\u03C0\u03B7\u03C3\u03B7\u03C2",
  canvasTooBig: "\u039F \u03BA\u03B1\u03BC\u03B2\u03AC\u03C2 \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF \u03BD\u03B1 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03BC\u03B5\u03B3\u03AC\u03BB\u03BF\u03C2.",
  canvasTooBigTip: "\u03A3\u03C5\u03BC\u03B2\u03BF\u03C5\u03BB\u03AE: \u03C0\u03C1\u03BF\u03C3\u03C0\u03B1\u03B8\u03AE\u03C3\u03C4\u03B5 \u03BD\u03B1 \u03BC\u03B5\u03C4\u03B1\u03BA\u03B9\u03BD\u03AE\u03C3\u03B5\u03C4\u03B5 \u03C4\u03B1 \u03C0\u03B9\u03BF \u03B1\u03C0\u03BF\u03BC\u03B1\u03BA\u03C1\u03C5\u03C3\u03BC\u03AD\u03BD\u03B1 \u03C3\u03C4\u03BF\u03B9\u03C7\u03B5\u03AF\u03B1 \u03BB\u03AF\u03B3\u03BF \u03C0\u03B9\u03BF \u03BA\u03BF\u03BD\u03C4\u03AC \u03BC\u03B1\u03B6\u03AF."
};
var errorSplash = {
  headingMain: "\u03A3\u03C5\u03BD\u03AD\u03B2\u03B7 \u03BA\u03AC\u03C0\u03BF\u03B9\u03BF \u03C3\u03C6\u03AC\u03BB\u03BC\u03B1. \u03A0\u03C1\u03BF\u03C3\u03C0\u03AC\u03B8\u03B7\u03C3\u03B5 <button>\u03C6\u03CC\u03C1\u03C4\u03C9\u03C3\u03B5 \u03BE\u03B1\u03BD\u03AC \u03C4\u03B7\u03BD \u03C3\u03B5\u03BB\u03AF\u03B4\u03B1.</button>",
  clearCanvasMessage: "\u0395\u03AC\u03BD \u03C4\u03BF \u03C0\u03B1\u03C1\u03B1\u03C0\u03AC\u03BD\u03C9 \u03B4\u03B5\u03BD \u03B4\u03BF\u03C5\u03BB\u03AD\u03C8\u03B5\u03B9, \u03C0\u03C1\u03BF\u03C3\u03C0\u03AC\u03B8\u03B7\u03C3\u03B5 <button>\u03BA\u03B1\u03B8\u03B1\u03C1\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BA\u03B1\u03BD\u03B2\u03AC.</button>",
  clearCanvasCaveat: " \u0391\u03C5\u03C4\u03CC \u03B8\u03B1 \u03C0\u03C1\u03BF\u03BA\u03B1\u03BB\u03AD\u03C3\u03B5\u03B9 \u03B1\u03C0\u03CE\u03BB\u03B5\u03B9\u03B1 \u03C4\u03B7\u03C2 \u03B4\u03BF\u03C5\u03BB\u03B5\u03B9\u03AC\u03C2 \u03C3\u03BF\u03C5 ",
  trackedToSentry: "\u03A4\u03BF \u03C3\u03C6\u03AC\u03BB\u03BC\u03B1 \u03BC\u03B5 \u03B1\u03BD\u03B1\u03B3\u03BD\u03C9\u03C1\u03B9\u03C3\u03C4\u03B9\u03BA\u03CC {{eventId}} \u03C0\u03B1\u03C1\u03B1\u03BA\u03BF\u03BB\u03BF\u03C5\u03B8\u03AE\u03B8\u03B7\u03BA\u03B5 \u03C3\u03C4\u03BF \u03C3\u03CD\u03C3\u03C4\u03B7\u03BC\u03AC \u03BC\u03B1\u03C2.",
  openIssueMessage: "\u0389\u03BC\u03B1\u03C3\u03C4\u03B1\u03BD \u03C0\u03BF\u03BB\u03CD \u03C0\u03C1\u03BF\u03C3\u03B5\u03BA\u03C4\u03B9\u03BA\u03BF\u03AF \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03BC\u03B7\u03BD \u03C3\u03C5\u03BC\u03C0\u03B5\u03C1\u03B9\u03BB\u03AC\u03B2\u03BF\u03C5\u03BC\u03B5 \u03C4\u03B9\u03C2 \u03C0\u03BB\u03B7\u03C1\u03BF\u03C6\u03BF\u03C1\u03AF\u03B5\u03C2 \u03C4\u03B7\u03C2 \u03C3\u03BA\u03B7\u03BD\u03AE\u03C2 \u03C3\u03BF\u03C5 \u03C3\u03C4\u03BF \u03C3\u03C6\u03AC\u03BB\u03BC\u03B1. \u0391\u03BD \u03B7 \u03C3\u03BA\u03B7\u03BD\u03AE \u03C3\u03BF\u03C5 \u03B4\u03B5\u03BD \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B9\u03B4\u03B9\u03C9\u03C4\u03B9\u03BA\u03AE, \u03C0\u03B1\u03C1\u03B1\u03BA\u03B1\u03BB\u03CE \u03C3\u03BA\u03AD\u03C8\u03BF\u03C5 \u03BD\u03B1 \u03B1\u03BA\u03BF\u03BB\u03BF\u03C5\u03B8\u03AE\u03C3\u03B5\u03B9\u03C2 \u03C4\u03BF \u03B4\u03B9\u03BA\u03CC \u03BC\u03B1\u03C2 <button>\u03B1\u03BD\u03B9\u03C7\u03BD\u03B5\u03C5\u03C4\u03AE\u03C2 \u03C3\u03C6\u03B1\u03BB\u03BC\u03AC\u03C4\u03C9\u03BD.</button> \u03A0\u03B1\u03C1\u03B1\u03BA\u03B1\u03BB\u03CE \u03BD\u03B1 \u03C3\u03C5\u03BC\u03C0\u03B5\u03C1\u03B9\u03BB\u03AC\u03B2\u03B5\u03C4\u03B5 \u03C4\u03B9\u03C2 \u03C0\u03B1\u03C1\u03B1\u03BA\u03AC\u03C4\u03C9 \u03C0\u03BB\u03B7\u03C1\u03BF\u03C6\u03BF\u03C1\u03AF\u03B5\u03C2, \u03B1\u03BD\u03C4\u03B9\u03B3\u03C1\u03AC\u03C6\u03BF\u03BD\u03C4\u03B1\u03C2 \u03BA\u03B1\u03B9 \u03B5\u03C0\u03B9\u03BA\u03BF\u03BB\u03BB\u03CE\u03BD\u03C4\u03B1\u03C2 \u03C4\u03BF \u03B6\u03AE\u03C4\u03B7\u03BC\u03B1 \u03C3\u03C4\u03BF GitHub.",
  sceneContent: "\u03A0\u03B5\u03C1\u03B9\u03B5\u03C7\u03CC\u03BC\u03B5\u03BD\u03BF \u03C3\u03BA\u03B7\u03BD\u03AE\u03C2:"
};
var roomDialog = {
  desc_intro: "\u039C\u03C0\u03BF\u03C1\u03B5\u03AF\u03C2 \u03BD\u03B1 \u03C0\u03C1\u03BF\u03C3\u03BA\u03B1\u03BB\u03AD\u03C3\u03B5\u03B9\u03C2 \u03AC\u03BB\u03BB\u03BF\u03C5\u03C2 \u03BD\u03B1 \u03B4\u03BF\u03C5\u03BB\u03AD\u03C8\u03BF\u03C5\u03BD \u03BC\u03B1\u03B6\u03AF \u03C3\u03BF\u03C5.",
  desc_privacy: "\u039C\u03B7\u03BD \u03B1\u03BD\u03B7\u03C3\u03C5\u03C7\u03B5\u03AF\u03C2, \u03B7 \u03C3\u03C5\u03BD\u03B5\u03B4\u03C1\u03AF\u03B1 \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03B5\u03AF \u03BA\u03C1\u03C5\u03C0\u03C4\u03BF\u03B3\u03C1\u03AC\u03C6\u03B7\u03C3\u03B7 \u03B1\u03C0\u03CC \u03C3\u03B7\u03BC\u03B5\u03AF\u03BF \u03C3\u03B5 \u03C3\u03B7\u03BC\u03B5\u03AF\u03BF, \u03AC\u03C1\u03B1 \u03BF\u03C4\u03B9\u03B4\u03AE\u03C0\u03BF\u03C4\u03B5 \u03BA\u03AC\u03BD\u03B5\u03B9\u03C2 \u03B8\u03B1 \u03C0\u03B1\u03C1\u03B1\u03BC\u03B5\u03AF\u03BD\u03B5\u03B9 \u03B1\u03BD\u03BF\u03B9\u03C7\u03C4\u03CC \u03BC\u03CC\u03BD\u03BF \u03C3\u03B5 \u03B5\u03C3\u03AD\u03BD\u03B1. \u039F\u03CD\u03C4\u03B5 \u03BF\u03B9 \u03BC\u03B7\u03C7\u03B1\u03BD\u03AD\u03C2 \u03BC\u03B1\u03C2 \u03BC\u03C0\u03BF\u03C1\u03BF\u03CD\u03BD \u03BD\u03B1 \u03B4\u03BF\u03C5\u03BD \u03C4\u03B9 \u03BA\u03AC\u03BD\u03B5\u03B9\u03C2.",
  button_startSession: "\u0388\u03BD\u03B1\u03C1\u03BE\u03B7 \u03A3\u03C5\u03BD\u03B5\u03B4\u03C1\u03AF\u03B1\u03C2",
  button_stopSession: "\u03A4\u03B5\u03C1\u03BC\u03B1\u03C4\u03B9\u03C3\u03BC\u03CC\u03C2 \u03A3\u03C5\u03BD\u03B5\u03B4\u03C1\u03AF\u03B1\u03C2",
  desc_inProgressIntro: "\u0397 \u03B6\u03C9\u03BD\u03C4\u03B1\u03BD\u03AE \u03C3\u03C5\u03BD\u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1 \u03BC\u03B5 \u03AC\u03BB\u03BB\u03BF\u03C5\u03C2 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03C3\u03B5 \u03B5\u03BD\u03B5\u03C1\u03B3\u03AE.",
  desc_shareLink: "\u039C\u03BF\u03B9\u03C1\u03B1\u03C3\u03C4\u03B5\u03AF\u03C4\u03B5 \u03C4\u03BF\u03BD \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF \u03BC\u03B5 \u03CC\u03C0\u03BF\u03B9\u03BF\u03BD \u03B8\u03AD\u03BB\u03B5\u03C4\u03B5 \u03BD\u03B1 \u03B4\u03BF\u03C5\u03BB\u03AD\u03C8\u03B5\u03C4\u03B5 \u03BC\u03B1\u03B6\u03AF:",
  desc_exitSession: "\u0397 \u03B4\u03B9\u03B1\u03BA\u03BF\u03C0\u03AE \u03B8\u03B1 \u03C3\u03B1\u03C2 \u03B1\u03C0\u03BF\u03C3\u03C5\u03BD\u03B4\u03AD\u03C3\u03B5\u03B9 \u03B1\u03C0\u03CC \u03C4\u03BF \u03B4\u03C9\u03BC\u03AC\u03C4\u03B9\u03BF, \u03B1\u03BB\u03BB\u03AC \u03B8\u03B1 \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03BD\u03B1 \u03B4\u03BF\u03C5\u03BB\u03B5\u03CD\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF\u03BD \u03C0\u03AF\u03BD\u03B1\u03BA\u03B1, \u03C4\u03BF\u03C0\u03B9\u03BA\u03AC. \u03A3\u03B7\u03BC\u03B5\u03B9\u03CE\u03C3\u03B1\u03C4\u03B5 \u03CC\u03C4\u03B9 \u03B1\u03C5\u03C4\u03CC \u03B4\u03B5\u03BD \u03B8\u03B1 \u03B5\u03C0\u03B7\u03C1\u03B5\u03AC\u03C3\u03B5\u03B9 \u03C4\u03BF\u03BD \u03C0\u03AF\u03BD\u03B1\u03BA\u03B1 \u03AC\u03BB\u03BB\u03C9\u03BD, \u03BA\u03B1\u03B9 \u03B8\u03B1 \u03BC\u03C0\u03BF\u03C1\u03BF\u03CD\u03BD \u03B1\u03BA\u03CC\u03BC\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03B9\u03C3\u03C6\u03AD\u03C1\u03BF\u03C5\u03BD \u03C3\u03C4\u03B7\u03BD \u03B4\u03B9\u03BA\u03AE \u03C4\u03BF\u03C5\u03C2 \u03AD\u03BA\u03B4\u03BF\u03C3\u03B7.",
  shareTitle: "\u03A3\u03C5\u03BC\u03BC\u03B5\u03C4\u03AC\u03C3\u03C7\u03B5\u03C4\u03B5 \u03C3\u03B5 \u03BC\u03B9\u03B1 \u03B6\u03C9\u03BD\u03C4\u03B1\u03BD\u03AE \u03C3\u03C5\u03BD\u03B5\u03B4\u03C1\u03AF\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1\u03C2 \u03B3\u03B9\u03B1 \u03C4\u03BF Excalidraw"
};
var errorDialog = {
  title: "\u03A3\u03C6\u03AC\u03BB\u03BC\u03B1"
};
var exportDialog = {
  disk_title: "\u0391\u03C0\u03BF\u03B8\u03AE\u03BA\u03B5\u03C5\u03C3\u03B7 \u03C3\u03C4\u03BF \u03B4\u03AF\u03C3\u03BA\u03BF",
  disk_details: "\u0395\u03BE\u03B1\u03B3\u03C9\u03B3\u03AE \u03B4\u03B5\u03B4\u03BF\u03BC\u03AD\u03BD\u03C9\u03BD \u03C3\u03BA\u03B7\u03BD\u03AE\u03C2 \u03C3\u03B5 \u03AD\u03BD\u03B1 \u03B1\u03C1\u03C7\u03B5\u03AF\u03BF \u03B1\u03C0\u03CC \u03C4\u03BF \u03BF\u03C0\u03BF\u03AF\u03BF \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03B5\u03B9\u03C3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03B1\u03C1\u03B3\u03CC\u03C4\u03B5\u03C1\u03B1.",
  disk_button: "\u0391\u03C0\u03BF\u03B8\u03AE\u03BA\u03B5\u03C5\u03C3\u03B7 \u03C3\u03B5 \u03B1\u03C1\u03C7\u03B5\u03AF\u03BF",
  link_title: "\u039A\u03BF\u03B9\u03BD\u03CC\u03C7\u03C1\u03B7\u03C3\u03C4\u03BF\u03C2 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF\u03C2",
  link_details: "\u0395\u03BE\u03B1\u03B3\u03C9\u03B3\u03AE \u03C9\u03C2 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF \u03BC\u03CC\u03BD\u03BF \u03B3\u03B9\u03B1 \u03B1\u03BD\u03AC\u03B3\u03BD\u03C9\u03C3\u03B7.",
  link_button: "\u0395\u03BE\u03B1\u03B3\u03C9\u03B3\u03AE \u03C3\u03B5 \u03A3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7",
  excalidrawplus_description: "\u0391\u03C0\u03BF\u03B8\u03B7\u03BA\u03B5\u03CD\u03C3\u03C4\u03B5 \u03C4\u03B7 \u03C3\u03BA\u03B7\u03BD\u03AE \u03C3\u03C4\u03BF \u03C7\u03CE\u03C1\u03BF \u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1\u03C2 \u03C3\u03B1\u03C2 Excalidraw+.",
  excalidrawplus_button: "\u0395\u03BE\u03B1\u03B3\u03C9\u03B3\u03AE",
  excalidrawplus_exportError: "\u0394\u03B5\u03BD \u03AE\u03C4\u03B1\u03BD \u03B4\u03C5\u03BD\u03B1\u03C4\u03AE \u03B7 \u03B5\u03BE\u03B1\u03B3\u03C9\u03B3\u03AE \u03C3\u03C4\u03BF Excalidraw+ \u03B1\u03C5\u03C4\u03AE \u03C4\u03B7 \u03C3\u03C4\u03B9\u03B3\u03BC\u03AE..."
};
var helpDialog = {
  blog: "\u0394\u03B9\u03B1\u03B2\u03AC\u03C3\u03C4\u03B5 \u03C4\u03BF Blog \u03BC\u03B1\u03C2",
  click: "\u03BA\u03BB\u03B9\u03BA",
  deepSelect: "\u0392\u03B1\u03B8\u03B9\u03AC \u03B5\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE",
  deepBoxSelect: "\u0392\u03B1\u03B8\u03B9\u03AC \u03B5\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE \u03BC\u03AD\u03C3\u03B1 \u03C3\u03C4\u03BF \u03C0\u03BB\u03B1\u03AF\u03C3\u03B9\u03BF \u03BA\u03B1\u03B9 \u03B1\u03C0\u03BF\u03C4\u03C1\u03BF\u03C0\u03AE \u03C3\u03C5\u03C1\u03C3\u03AF\u03BC\u03B1\u03C4\u03BF\u03C2",
  curvedArrow: "\u039A\u03C5\u03C1\u03C4\u03CC \u03B2\u03AD\u03BB\u03BF\u03C2",
  curvedLine: "\u039A\u03C5\u03C1\u03C4\u03AE \u03B3\u03C1\u03B1\u03BC\u03BC\u03AE",
  documentation: "\u0395\u03B3\u03C7\u03B5\u03B9\u03C1\u03AF\u03B4\u03B9\u03BF",
  doubleClick: "\u03B4\u03B9\u03C0\u03BB\u03CC \u03BA\u03BB\u03B9\u03BA",
  drag: "\u03C3\u03CD\u03C1\u03B5",
  editor: "\u0395\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03C4\u03AE\u03C2",
  editLineArrowPoints: "",
  editText: "",
  github: "\u0392\u03C1\u03AE\u03BA\u03B1\u03C4\u03B5 \u03C0\u03C1\u03CC\u03B2\u03BB\u03B7\u03BC\u03B1; \u03A5\u03C0\u03BF\u03B2\u03AC\u03BB\u03B5\u03C4\u03B5 \u03C4\u03BF",
  howto: "\u0391\u03BA\u03BF\u03BB\u03BF\u03C5\u03B8\u03AE\u03C3\u03C4\u03B5 \u03C4\u03BF\u03C5\u03C2 \u03BF\u03B4\u03B7\u03B3\u03BF\u03CD\u03C2 \u03BC\u03B1\u03C2",
  or: "\u03AE",
  preventBinding: "\u0391\u03C0\u03BF\u03C4\u03C1\u03BF\u03C0\u03AE \u03B4\u03AD\u03C3\u03BC\u03B5\u03C5\u03C3\u03B7\u03C2 \u03B2\u03AD\u03BB\u03C9\u03BD",
  tools: "\u0395\u03C1\u03B3\u03B1\u03BB\u03B5\u03AF\u03B1",
  shortcuts: "\u03A3\u03C5\u03BD\u03C4\u03BF\u03BC\u03B5\u03CD\u03C3\u03B5\u03B9\u03C2 \u03C0\u03BB\u03B7\u03BA\u03C4\u03C1\u03BF\u03BB\u03BF\u03B3\u03AF\u03BF\u03C5",
  textFinish: "\u039F\u03BB\u03BF\u03BA\u03BB\u03AE\u03C1\u03C9\u03C3\u03B7 \u03B5\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1\u03C2 (\u03B5\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03C4\u03AE\u03C2 \u03BA\u03B5\u03B9\u03BC\u03AD\u03BD\u03BF\u03C5)",
  textNewLine: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03BD\u03AD\u03B1\u03C2 \u03B3\u03C1\u03B1\u03BC\u03BC\u03AE\u03C2 (\u03B5\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03C4\u03AE\u03C2 \u03BA\u03B5\u03B9\u03BC\u03AD\u03BD\u03BF\u03C5)",
  title: "\u0392\u03BF\u03AE\u03B8\u03B5\u03B9\u03B1",
  view: "\u03A0\u03C1\u03BF\u03B2\u03BF\u03BB\u03AE",
  zoomToFit: "Zoom \u03CE\u03C3\u03C4\u03B5 \u03BD\u03B1 \u03C7\u03C9\u03C1\u03AD\u03C3\u03BF\u03C5\u03BD \u03CC\u03BB\u03B1 \u03C4\u03B1 \u03C3\u03C4\u03BF\u03B9\u03C7\u03B5\u03AF\u03B1",
  zoomToSelection: "\u0396\u03BF\u03C5\u03BC \u03C3\u03C4\u03B7\u03BD \u03B5\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE",
  toggleElementLock: "\u039A\u03BB\u03B5\u03AF\u03B4\u03C9\u03BC\u03B1/\u039E\u03B5\u03BA\u03BB\u03B5\u03AF\u03B4\u03C9\u03BC\u03B1 \u03B5\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE\u03C2",
  movePageUpDown: "\u039C\u03B5\u03C4\u03B1\u03BA\u03AF\u03BD\u03B7\u03C3\u03B7 \u03C3\u03B5\u03BB\u03AF\u03B4\u03B1\u03C2 \u03C0\u03AC\u03BD\u03C9/\u03BA\u03AC\u03C4\u03C9",
  movePageLeftRight: "\u039C\u03B5\u03C4\u03B1\u03BA\u03AF\u03BD\u03B7\u03C3\u03B7 \u03C3\u03B5\u03BB\u03AF\u03B4\u03B1\u03C2 \u03B1\u03C1\u03B9\u03C3\u03C4\u03B5\u03C1\u03AC/\u03B4\u03B5\u03BE\u03B9\u03AC"
};
var clearCanvasDialog = {
  title: "\u039A\u03B1\u03B8\u03B1\u03C1\u03B9\u03C3\u03BC\u03CC\u03C2 \u03BA\u03B1\u03BC\u03B2\u03AC"
};
var publishDialog = {
  title: "\u0394\u03B7\u03BC\u03BF\u03C3\u03AF\u03B5\u03C5\u03C3\u03B7 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7\u03C2",
  itemName: "\u038C\u03BD\u03BF\u03BC\u03B1 \u03B1\u03BD\u03C4\u03B9\u03BA\u03B5\u03B9\u03BC\u03AD\u03BD\u03BF\u03C5",
  authorName: "\u038C\u03BD\u03BF\u03BC\u03B1 \u03B4\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03BF\u03CD",
  githubUsername: "GitHub username",
  twitterUsername: "Twitter username",
  libraryName: "\u038C\u03BD\u03BF\u03BC\u03B1 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7\u03C2",
  libraryDesc: "\u03A0\u03B5\u03C1\u03B9\u03B3\u03C1\u03B1\u03C6\u03AE \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7\u03C2",
  website: "\u0399\u03C3\u03C4\u03BF\u03C3\u03B5\u03BB\u03AF\u03B4\u03B1",
  placeholder: {
    authorName: "\u038C\u03BD\u03BF\u03BC\u03B1 \u03AE \u03CC\u03BD\u03BF\u03BC\u03B1 \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7",
    libraryName: "\u038C\u03BD\u03BF\u03BC\u03B1 \u03C4\u03B7\u03C2 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7\u03C2 \u03C3\u03B1\u03C2",
    libraryDesc: "\u03A0\u03B5\u03C1\u03B9\u03B3\u03C1\u03B1\u03C6\u03AE \u03C4\u03B7\u03C2 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7\u03C2 \u03C3\u03B1\u03C2 \u03CE\u03C3\u03C4\u03B5 \u03BD\u03B1 \u03B2\u03BF\u03B7\u03B8\u03AE\u03C3\u03B5\u03B9 \u03C4\u03BF \u03BA\u03BF\u03B9\u03BD\u03CC \u03BD\u03B1 \u03BA\u03B1\u03C4\u03B1\u03BD\u03BF\u03AE\u03C3\u03B5\u03B9 \u03C4\u03B7 \u03C7\u03C1\u03AE\u03C3\u03B7 \u03C4\u03B7\u03C2",
    githubHandle: "\u038C\u03BD\u03BF\u03BC\u03B1 \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7 \u03C3\u03C4\u03BF GitHub (\u03C0\u03C1\u03BF\u03B1\u03B9\u03C1\u03B5\u03C4\u03B9\u03BA\u03CC), \u03CE\u03C3\u03C4\u03B5 \u03BD\u03B1 \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03B5\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03C4\u03B5\u03AF\u03C4\u03B5 \u03C4\u03B7 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7 \u03B1\u03C6\u03BF\u03CD \u03C5\u03C0\u03BF\u03B2\u03BB\u03B7\u03B8\u03B5\u03AF \u03B3\u03B9\u03B1 \u03B1\u03BE\u03B9\u03BF\u03BB\u03CC\u03B3\u03B7\u03C3\u03B7",
    twitterHandle: "\u038C\u03BD\u03BF\u03BC\u03B1 \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7 Twitter (\u03C0\u03C1\u03BF\u03B1\u03B9\u03C1\u03B5\u03C4\u03B9\u03BA\u03CC), \u03CE\u03C3\u03C4\u03B5 \u03BD\u03B1 \u03B3\u03BD\u03C9\u03C1\u03AF\u03B6\u03BF\u03C5\u03BC\u03B5 \u03C3\u03B5 \u03C0\u03BF\u03B9\u03BF\u03BD/\u03B7 \u03BD\u03B1 \u03B4\u03CE\u03C3\u03BF\u03C5\u03BC\u03B5 \u03B5\u03CD\u03C3\u03B7\u03BC\u03B1 \u03BA\u03B1\u03C4\u03AC \u03C4\u03B7\u03BD \u03C0\u03C1\u03BF\u03CE\u03B8\u03B7\u03C3\u03B7 \u03BC\u03AD\u03C3\u03C9 Twitter",
    website: "\u03A3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF\u03C2 \u03B3\u03B9\u03B1 \u03C4\u03B7\u03BD \u03C0\u03C1\u03BF\u03C3\u03C9\u03C0\u03B9\u03BA\u03AE \u03C3\u03B1\u03C2 \u03B9\u03C3\u03C4\u03BF\u03C3\u03B5\u03BB\u03AF\u03B4\u03B1 \u03AE \u03B1\u03BB\u03BB\u03BF\u03CD (\u03C0\u03C1\u03BF\u03B1\u03B9\u03C1\u03B5\u03C4\u03B9\u03BA\u03CC)"
  },
  errors: {
    required: "\u0391\u03C0\u03B1\u03B9\u03C4\u03B5\u03AF\u03C4\u03B1\u03B9",
    website: "\u0395\u03B9\u03C3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03BC\u03B9\u03B1 \u03AD\u03B3\u03BA\u03C5\u03C1\u03B7 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 URL"
  },
  noteDescription: "\u03A5\u03C0\u03BF\u03B2\u03AC\u03BB\u03B5\u03C4\u03B5 \u03C4\u03B7 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7 \u03C3\u03B1\u03C2 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BC\u03C0\u03B5\u03C1\u03B9\u03BB\u03B7\u03C6\u03B8\u03B5\u03AF \u03C3\u03C4\u03BF <link>\u03B4\u03B7\u03BC\u03CC\u03C3\u03B9\u03BF \u03B1\u03C0\u03BF\u03B8\u03B5\u03C4\u03AE\u03C1\u03B9\u03BF \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7\u03C2</link>\u03CE\u03C3\u03C4\u03B5 \u03BD\u03B1 \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03B7\u03B8\u03B5\u03AF \u03B1\u03C0\u03CC \u03AC\u03BB\u03BB\u03B1 \u03AC\u03C4\u03BF\u03BC\u03B1 \u03C3\u03C4\u03B1 \u03C3\u03C7\u03AD\u03B4\u03B9\u03AC \u03C4\u03BF\u03C5\u03C2.",
  noteGuidelines: "\u0397 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7 \u03C0\u03C1\u03AD\u03C0\u03B5\u03B9 \u03C0\u03C1\u03CE\u03C4\u03B1 \u03BD\u03B1 \u03B5\u03B3\u03BA\u03C1\u03B9\u03B8\u03B5\u03AF \u03C7\u03B5\u03B9\u03C1\u03BF\u03BA\u03AF\u03BD\u03B7\u03C4\u03B1. \u03A0\u03B1\u03C1\u03B1\u03BA\u03B1\u03BB\u03CE \u03B4\u03B9\u03B1\u03B2\u03AC\u03C3\u03C4\u03B5 \u03C4\u03BF\u03C5\u03C2 <link>\u03BF\u03B4\u03B7\u03B3\u03AF\u03B5\u03C2</link> \u03C0\u03C1\u03B9\u03BD \u03C4\u03B7\u03BD \u03C5\u03C0\u03BF\u03B2\u03BF\u03BB\u03AE. \u0398\u03B1 \u03C7\u03C1\u03B5\u03B9\u03B1\u03C3\u03C4\u03B5\u03AF\u03C4\u03B5 \u03AD\u03BD\u03B1\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC GitHub \u03B3\u03B9\u03B1 \u03C4\u03B7\u03BD \u03B5\u03C0\u03B9\u03BA\u03BF\u03B9\u03BD\u03C9\u03BD\u03AF\u03B1 \u03BA\u03B1\u03B9 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C0\u03C1\u03BF\u03B2\u03B5\u03AF\u03C4\u03B5 \u03C3\u03B5 \u03B1\u03BB\u03BB\u03B1\u03B3\u03AD\u03C2 \u03B5\u03C6' \u03CC\u03C3\u03BF\u03BD \u03C7\u03C1\u03B5\u03B9\u03B1\u03C3\u03C4\u03B5\u03AF, \u03B1\u03BB\u03BB\u03AC \u03B4\u03B5\u03BD \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B1\u03C5\u03C3\u03C4\u03B7\u03C1\u03AE \u03B1\u03C0\u03B1\u03AF\u03C4\u03B7\u03C3\u03B7.",
  noteLicense: "\u039C\u03B5 \u03C4\u03B7\u03BD \u03C5\u03C0\u03BF\u03B2\u03BF\u03BB\u03AE, \u03C3\u03C5\u03BC\u03C6\u03C9\u03BD\u03B5\u03AF\u03C4\u03B5 \u03CC\u03C4\u03B9 \u03B7 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7 \u03B8\u03B1 \u03B4\u03B7\u03BC\u03BF\u03C3\u03B9\u03B5\u03C5\u03B8\u03B5\u03AF \u03C5\u03C0\u03CC \u03C4\u03B7\u03BD <link>\u0386\u03B4\u03B5\u03B9\u03B1 MIT, </link>\u03C0\u03BF\u03C5 \u03B5\u03BD \u03C3\u03C5\u03BD\u03C4\u03BF\u03BC\u03AF\u03B1 \u03C3\u03B7\u03BC\u03B1\u03AF\u03BD\u03B5\u03B9 \u03CC\u03C4\u03B9 \u03BF \u03BA\u03B1\u03B8\u03AD\u03BD\u03B1\u03C2 \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF \u03BD\u03B1 \u03C4\u03B1 \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03AE\u03C3\u03B5\u03B9 \u03C7\u03C9\u03C1\u03AF\u03C2 \u03C0\u03B5\u03C1\u03B9\u03BF\u03C1\u03B9\u03C3\u03BC\u03BF\u03CD\u03C2.",
  noteItems: "\u039A\u03AC\u03B8\u03B5 \u03B1\u03BD\u03C4\u03B9\u03BA\u03B5\u03AF\u03BC\u03B5\u03BD\u03BF \u03C4\u03B7\u03C2 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7\u03C2 \u03C0\u03C1\u03AD\u03C0\u03B5\u03B9 \u03BD\u03B1 \u03AD\u03C7\u03B5\u03B9 \u03C4\u03BF \u03B4\u03B9\u03BA\u03CC \u03C4\u03BF\u03C5 \u03CC\u03BD\u03BF\u03BC\u03B1 \u03CE\u03C3\u03C4\u03B5 \u03BD\u03B1 \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF \u03BD\u03B1 \u03C6\u03B9\u03BB\u03C4\u03C1\u03B1\u03C1\u03B9\u03C3\u03C4\u03B5\u03AF. \u0398\u03B1 \u03C3\u03C5\u03BC\u03C0\u03B5\u03C1\u03B9\u03BB\u03B7\u03C6\u03B8\u03BF\u03CD\u03BD \u03C4\u03B1 \u03B1\u03BA\u03CC\u03BB\u03BF\u03C5\u03B8\u03B1 \u03B1\u03BD\u03C4\u03B9\u03BA\u03B5\u03AF\u03BC\u03B5\u03BD\u03B1 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7\u03C2:",
  atleastOneLibItem: "\u03A0\u03B1\u03C1\u03B1\u03BA\u03B1\u03BB\u03CE \u03B5\u03C0\u03B9\u03BB\u03AD\u03BE\u03C4\u03B5 \u03C4\u03BF\u03C5\u03BB\u03AC\u03C7\u03B9\u03C3\u03C4\u03BF\u03BD \u03AD\u03BD\u03B1 \u03B1\u03BD\u03C4\u03B9\u03BA\u03B5\u03AF\u03BC\u03B5\u03BD\u03BF \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7\u03C2 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03BE\u03B5\u03BA\u03B9\u03BD\u03AE\u03C3\u03B5\u03C4\u03B5",
  republishWarning: "\u03A3\u03B7\u03BC\u03B5\u03AF\u03C9\u03C3\u03B7: \u03BC\u03B5\u03C1\u03B9\u03BA\u03AC \u03B1\u03C0\u03CC \u03C4\u03B1 \u03B5\u03C0\u03B9\u03BB\u03B5\u03B3\u03BC\u03AD\u03BD\u03B1 \u03B1\u03BD\u03C4\u03B9\u03BA\u03AD\u03B9\u03BC\u03B5\u03BD\u03B1 \u03AD\u03C7\u03BF\u03C5\u03BD \u03AE\u03B4\u03B7 \u03B5\u03C0\u03B9\u03C3\u03B7\u03BC\u03B1\u03BD\u03B8\u03B5\u03AF \u03C9\u03C2 \u03B4\u03B7\u03BC\u03BF\u03C3\u03B9\u03B5\u03C5\u03BC\u03AD\u03BD\u03B1/\u03C5\u03C0\u03BF\u03B2\u03B5\u03B2\u03BB\u03B7\u03BC\u03AD\u03BD\u03B1. \u0398\u03B1 \u03C0\u03C1\u03AD\u03C0\u03B5\u03B9 \u03BD\u03B1 \u03C5\u03C0\u03BF\u03B2\u03AC\u03BB\u03B5\u03C4\u03B5 \u03B1\u03BD\u03C4\u03B9\u03BA\u03B5\u03AF\u03BC\u03B5\u03BD\u03B1 \u03B5\u03BA \u03BD\u03AD\u03BF\u03C5 \u03BC\u03CC\u03BD\u03BF \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03B5\u03BD\u03B7\u03BC\u03B5\u03C1\u03CE\u03C3\u03B5\u03C4\u03B5 \u03BC\u03AF\u03B1 \u03AE\u03B4\u03B7 \u03C5\u03C0\u03AC\u03C1\u03C7\u03BF\u03C5\u03C3\u03B1 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7 \u03AE \u03C5\u03C0\u03BF\u03B2\u03BF\u03BB\u03AE."
};
var publishSuccessDialog = {
  title: "\u0397 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7 \u03C5\u03C0\u03BF\u03B2\u03BB\u03AE\u03B8\u03B7\u03BA\u03B5",
  content: "\u0395\u03C5\u03C7\u03B1\u03C1\u03B9\u03C3\u03C4\u03BF\u03CD\u03BC\u03B5 {{authorName}}. \u0397 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7 \u03C3\u03B1\u03C2 \u03AD\u03C7\u03B5\u03B9 \u03C5\u03C0\u03BF\u03B2\u03BB\u03B7\u03B8\u03B5\u03AF \u03B3\u03B9\u03B1 \u03B1\u03BE\u03B9\u03BF\u03BB\u03CC\u03B3\u03B7\u03C3\u03B7. \u039C\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03C0\u03B1\u03C1\u03B1\u03BA\u03BF\u03BB\u03BF\u03C5\u03B8\u03B5\u03AF\u03C4\u03B5 \u03C4\u03B7 \u03B4\u03B9\u03B1\u03B4\u03B9\u03BA\u03B1\u03C3\u03AF\u03B1<link>\u03B5\u03B4\u03CE</link>"
};
var confirmDialog = {
  resetLibrary: "\u039A\u03B1\u03B8\u03B1\u03C1\u03B9\u03C3\u03BC\u03CC\u03C2 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7\u03C2",
  removeItemsFromLib: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 \u03B5\u03C0\u03B9\u03BB\u03B5\u03B3\u03BC\u03AD\u03BD\u03C9\u03BD \u03B1\u03BD\u03C4\u03B9\u03BA\u03B5\u03B9\u03BC\u03AD\u03BD\u03C9\u03BD \u03B1\u03C0\u03CC \u03C4\u03B7 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7"
};
var imageExportDialog = {
  header: "",
  label: {
    withBackground: "",
    onlySelected: "",
    darkMode: "",
    embedScene: "",
    scale: "",
    padding: ""
  },
  tooltip: {
    embedScene: ""
  },
  title: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  },
  button: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  }
};
var encrypted = {
  tooltip: "\u03A4\u03B1 \u03C3\u03C7\u03AD\u03B4\u03B9\u03AC \u03C3\u03BF\u03C5 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03BA\u03C1\u03C5\u03C0\u03C4\u03BF\u03B3\u03C1\u03B1\u03C6\u03B7\u03BC\u03AD\u03BD\u03B1 \u03B1\u03C0\u03CC \u03AC\u03BA\u03C1\u03BF \u03C3\u03B5 \u03AC\u03BA\u03C1\u03BF, \u03AD\u03C4\u03C3\u03B9 \u03B4\u03B5\u03BD \u03B8\u03B1 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03C0\u03BF\u03C4\u03AD \u03BF\u03C1\u03B1\u03C4\u03AC \u03BC\u03AD\u03C3\u03B1 \u03B1\u03C0\u03CC \u03C4\u03BF\u03C5\u03C2 \u03B4\u03B9\u03B1\u03BA\u03BF\u03BC\u03B9\u03C3\u03C4\u03AD\u03C2 \u03C4\u03BF\u03C5 Excalidraw.",
  link: "Blog post \u03C3\u03C4\u03B7\u03BD \u03BA\u03C1\u03C5\u03C0\u03C4\u03BF\u03B3\u03C1\u03AC\u03C6\u03B7\u03C3\u03B7 end-to-end \u03C3\u03C4\u03BF Excalidraw"
};
var stats = {
  angle: "\u0393\u03C9\u03BD\u03AF\u03B1",
  element: "\u03A3\u03C4\u03BF\u03B9\u03C7\u03B5\u03AF\u03BF",
  elements: "\u03A3\u03C4\u03BF\u03B9\u03C7\u03B5\u03AF\u03B1",
  height: "\u038E\u03C8\u03BF\u03C2",
  scene: "\u03A3\u03BA\u03B7\u03BD\u03AE",
  selected: "\u0395\u03C0\u03B9\u03BB\u03B5\u03B3\u03BC\u03AD\u03BD\u03B1",
  storage: "\u03A7\u03CE\u03C1\u03BF\u03C2",
  title: "\u03A3\u03C4\u03B1\u03C4\u03B9\u03C3\u03C4\u03B9\u03BA\u03AC \u03B3\u03B9\u03B1 \u03C3\u03C0\u03B1\u03C3\u03AF\u03BA\u03BB\u03B5\u03C2",
  total: "\u03A3\u03CD\u03BD\u03BF\u03BB\u03BF\xA0",
  version: "\u0388\u03BA\u03B4\u03BF\u03C3\u03B7",
  versionCopy: "\u039A\u03AC\u03BD\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03B1\u03BD\u03C4\u03B9\u03B3\u03C1\u03B1\u03C6\u03AE",
  versionNotAvailable: "\u0388\u03BA\u03B4\u03BF\u03C3\u03B7 \u03BC\u03B7 \u03B4\u03B9\u03B1\u03B8\u03AD\u03C3\u03B9\u03BC\u03B7",
  width: "\u03A0\u03BB\u03AC\u03C4\u03BF\u03C2"
};
var toast = {
  addedToLibrary: "\u03A0\u03C1\u03BF\u03C3\u03C4\u03AD\u03B8\u03B7\u03BA\u03B5 \u03C3\u03C4\u03B7 \u03B2\u03B9\u03B2\u03BB\u03B9\u03BF\u03B8\u03AE\u03BA\u03B7",
  copyStyles: "\u0391\u03BD\u03C4\u03B9\u03B3\u03C1\u03AC\u03C6\u03B7\u03BA\u03B1\u03BD \u03C3\u03C4\u03C5\u03BB.",
  copyToClipboard: "\u0391\u03BD\u03C4\u03B9\u03B3\u03C1\u03AC\u03C6\u03B7\u03BA\u03B5 \u03C3\u03C4\u03BF \u03C0\u03C1\u03CC\u03C7\u03B5\u03B9\u03C1\u03BF.",
  copyToClipboardAsPng: "\u0391\u03BD\u03C4\u03B9\u03B3\u03C1\u03AC\u03C6\u03B7\u03BA\u03B5 {{exportSelection}} \u03C3\u03C4\u03BF \u03C0\u03C1\u03CC\u03C7\u03B5\u03B9\u03C1\u03BF \u03C9\u03C2 PNG\n({{exportColorScheme}})",
  fileSaved: "\u03A4\u03BF \u03B1\u03C1\u03C7\u03B5\u03AF\u03BF \u03B1\u03C0\u03BF\u03B8\u03B7\u03BA\u03B5\u03CD\u03C4\u03B7\u03BA\u03B5.",
  fileSavedToFilename: "\u0391\u03C0\u03BF\u03B8\u03B7\u03BA\u03B5\u03CD\u03C4\u03B7\u03BA\u03B5 \u03C3\u03C4\u03BF {filename}",
  canvas: "\u03BA\u03B1\u03BC\u03B2\u03AC\u03C2",
  selection: "\u03B5\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE",
  pasteAsSingleElement: "\u03A7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B5 \u03C4\u03BF {{shortcut}} \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03B5\u03C0\u03B9\u03BA\u03BF\u03BB\u03BB\u03AE\u03C3\u03B5\u03B9\u03C2 \u03C9\u03C2 \u03AD\u03BD\u03B1 \u03BC\u03CC\u03BD\u03BF \u03C3\u03C4\u03BF\u03B9\u03C7\u03B5\u03AF\u03BF,\n\u03AE \u03BD\u03B1 \u03B5\u03C0\u03B9\u03BA\u03BF\u03BB\u03BB\u03AE\u03C3\u03B5\u03B9\u03C2 \u03C3\u03B5 \u03AD\u03BD\u03B1\u03BD \u03C5\u03C0\u03AC\u03C1\u03C7\u03BF\u03BD\u03C4\u03B1 \u03B5\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03C4\u03AE \u03BA\u03B5\u03B9\u03BC\u03AD\u03BD\u03BF\u03C5",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "\u0394\u03B9\u03B1\u03C6\u03B1\u03BD\u03AD\u03C2",
  black: "\u039C\u03B1\u03CD\u03C1\u03BF",
  white: "\u039B\u03B5\u03C5\u03BA\u03CC",
  red: "\u039A\u03CC\u03BA\u03BA\u03B9\u03BD\u03BF",
  pink: "\u03A1\u03BF\u03B6",
  grape: "\u03A3\u03C4\u03B1\u03C6\u03C5\u03BB\u03AF",
  violet: "\u0392\u03B9\u03BF\u03BB\u03B5\u03C4\u03AF",
  gray: "\u0393\u03BA\u03C1\u03B9",
  blue: "\u039C\u03C0\u03BB\u03B5",
  cyan: "\u039A\u03C5\u03B1\u03BD\u03CC",
  teal: "\u03A4\u03B9\u03C1\u03BA\u03BF\u03C5\u03AC\u03B6",
  green: "\u03A0\u03C1\u03AC\u03C3\u03B9\u03BD\u03BF",
  yellow: "\u039A\u03AF\u03C4\u03C1\u03B9\u03BD\u03BF",
  orange: "\u03A0\u03BF\u03C1\u03C4\u03BF\u03BA\u03B1\u03BB\u03AF",
  bronze: "\u03A7\u03B1\u03BB\u03BA\u03B9\u03BD\u03BF"
};
var welcomeScreen = {
  app: {
    center_heading: "\u038C\u03BB\u03B1 \u03C4\u03B1 \u03B4\u03B5\u03B4\u03BF\u03BC\u03AD\u03BD\u03B1 \u03C3\u03B1\u03C2 \u03B1\u03C0\u03BF\u03B8\u03B7\u03BA\u03B5\u03CD\u03BF\u03BD\u03C4\u03B1\u03B9 \u03C4\u03BF\u03C0\u03B9\u03BA\u03AC \u03C3\u03C4\u03BF \u03C0\u03C1\u03CC\u03B3\u03C1\u03B1\u03BC\u03BC\u03B1 \u03C0\u03B5\u03C1\u03B9\u03AE\u03B3\u03B7\u03C3\u03B7\u03C2.",
    center_heading_plus: "\u039C\u03AE\u03C0\u03C9\u03C2 \u03B8\u03AD\u03BB\u03B1\u03C4\u03B5 \u03BD\u03B1 \u03C0\u03AC\u03C4\u03B5 \u03C3\u03C4\u03BF Excalidraw+;",
    menuHint: "\u0395\u03BE\u03B1\u03B3\u03C9\u03B3\u03AE, \u03C0\u03C1\u03BF\u03C4\u03B9\u03BC\u03AE\u03C3\u03B5\u03B9\u03C2, \u03B3\u03BB\u03CE\u03C3\u03C3\u03B5\u03C2, ..."
  },
  defaults: {
    menuHint: "\u0395\u03BE\u03B1\u03B3\u03C9\u03B3\u03AE, \u03C0\u03C1\u03BF\u03C4\u03B9\u03BC\u03AE\u03C3\u03B5\u03B9\u03C2 \u03BA\u03B1\u03B9 \u03AC\u03BB\u03BB\u03B5\u03C2 \u03B5\u03C0\u03B9\u03BB\u03BF\u03B3\u03AD\u03C2...",
    center_heading: "\u0394\u03B9\u03B1\u03B3\u03C1\u03AC\u03BC\u03BC\u03B1\u03C4\u03B1. \u0395\u03CD\u03BA\u03BF\u03BB\u03B1. \u0393\u03C1\u03AE\u03B3\u03BF\u03C1\u03B1.",
    toolbarHint: "\u0395\u03C0\u03B9\u03BB\u03AD\u03BE\u03C4\u03B5 \u03AD\u03BD\u03B1 \u03B5\u03C1\u03B3\u03B1\u03BB\u03B5\u03AF\u03BF \u03BA\u03B1\u03B9 \u03BE\u03B5\u03BA\u03B9\u03BD\u03AE\u03C3\u03C4\u03B5 \u03BD\u03B1 \u03C3\u03C7\u03B5\u03B4\u03B9\u03AC\u03B6\u03B5\u03C4\u03B1\u03B9!",
    helpHint: "\u03A3\u03C5\u03BD\u03C4\u03BF\u03BC\u03B5\u03CD\u03C3\u03B5\u03B9\u03C2 \u03BA\u03B1\u03B9 \u03B2\u03BF\u03AE\u03B8\u03B5\u03B9\u03B1"
  }
};
var colorPicker = {
  mostUsedCustomColors: "\u03A0\u03B9\u03BF \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03BF\u03CD\u03BC\u03B5\u03BD\u03B1 \u03C7\u03C1\u03CE\u03BC\u03B1\u03C4\u03B1",
  colors: "\u03A7\u03C1\u03CE\u03BC\u03B1\u03C4\u03B1",
  shades: "\u0391\u03C0\u03BF\u03C7\u03C1\u03CE\u03C3\u03B5\u03B9\u03C2",
  hexCode: "\u039A\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 Hex",
  noShades: "\u0394\u03B5\u03BD \u03C5\u03C0\u03AC\u03C1\u03C7\u03BF\u03C5\u03BD \u03B4\u03B9\u03B1\u03B8\u03AD\u03C3\u03B9\u03BC\u03B5\u03C2 \u03B1\u03C0\u03BF\u03C7\u03C1\u03CE\u03C3\u03B5\u03B9\u03C2 \u03B3\u03B9\u03B1 \u03B1\u03C5\u03C4\u03CC \u03C4\u03BF \u03C7\u03C1\u03CE\u03BC\u03B1"
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "",
      button: "",
      description: ""
    },
    saveToDisk: {
      title: "",
      button: "",
      description: ""
    },
    excalidrawPlus: {
      title: "",
      button: "",
      description: ""
    }
  },
  modal: {
    loadFromFile: {
      title: "",
      button: "",
      description: ""
    },
    shareableLink: {
      title: "",
      button: "",
      description: ""
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var el_GR_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  el_GR_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=el-GR-HIKPLEXI.js.map
