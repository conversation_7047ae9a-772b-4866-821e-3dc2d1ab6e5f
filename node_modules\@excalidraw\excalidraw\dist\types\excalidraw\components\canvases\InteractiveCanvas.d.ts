import React from "react";
import type { DOMAttributes } from "react";
import type { Device, InteractiveCanvasAppState } from "../../types";
import type { RenderableElementsMap, RenderInteractiveSceneCallback } from "../../scene/types";
import type { NonDeletedExcalidrawElement, NonDeletedSceneElementsMap } from "../../element/types";
type InteractiveCanvasProps = {
    containerRef: React.RefObject<HTMLDivElement | null>;
    canvas: HTMLCanvasElement | null;
    elementsMap: RenderableElementsMap;
    visibleElements: readonly NonDeletedExcalidrawElement[];
    selectedElements: readonly NonDeletedExcalidrawElement[];
    allElementsMap: NonDeletedSceneElementsMap;
    sceneNonce: number | undefined;
    selectionNonce: number | undefined;
    scale: number;
    appState: InteractiveCanvasAppState;
    device: Device;
    renderInteractiveSceneCallback: (data: RenderInteractiveSceneCallback) => void;
    handleCanvasRef: (canvas: HTMLCanvasElement | null) => void;
    onContextMenu: Exclude<DOMAttributes<HTMLCanvasElement | HTMLDivElement>["onContextMenu"], undefined>;
    onPointerMove: Exclude<DOMAttributes<HTMLCanvasElement>["onPointerMove"], undefined>;
    onPointerUp: Exclude<DOMAttributes<HTMLCanvasElement>["onPointerUp"], undefined>;
    onPointerCancel: Exclude<DOMAttributes<HTMLCanvasElement>["onPointerCancel"], undefined>;
    onTouchMove: Exclude<DOMAttributes<HTMLCanvasElement>["onTouchMove"], undefined>;
    onPointerDown: Exclude<DOMAttributes<HTMLCanvasElement>["onPointerDown"], undefined>;
    onDoubleClick: Exclude<DOMAttributes<HTMLCanvasElement>["onDoubleClick"], undefined>;
};
declare const _default: React.MemoExoticComponent<(props: InteractiveCanvasProps) => import("react/jsx-runtime").JSX.Element>;
export default _default;
