"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/context/EditPermissionContext.tsx":
/*!***********************************************!*\
  !*** ./src/context/EditPermissionContext.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditPermissionProvider: () => (/* binding */ EditPermissionProvider),\n/* harmony export */   useEditPermission: () => (/* binding */ useEditPermission)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSocketService */ \"(app-pages-browser)/./src/hooks/useSocketService.ts\");\n/* __next_internal_client_entry_do_not_use__ EditPermissionProvider,useEditPermission auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst EditPermissionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction EditPermissionProvider(param) {\n    let { children } = param;\n    _s();\n    const [canEdit, setCanEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTeacher, setIsTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Use the socket service hook\n    const { socketService, isReady: socketReady, isConnected } = (0,_hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__.useSocketService)();\n    // Compute permission badge based on current state\n    const permissionBadge = isTeacher ? 'teacher' : canEdit ? 'edit-access' : 'view-only';\n    // Grant edit permission to a student\n    const grantEditPermission = (targetSocketId)=>{\n        if (!isTeacher) {\n            console.warn('Only teachers can grant edit permissions');\n            return;\n        }\n        if (!socketService || !socketReady) {\n            console.warn('Socket service not ready, cannot grant edit permission');\n            return;\n        }\n        const roomId =  true ? window.localStorage.getItem('currentRoomId') : 0;\n        if (!roomId) {\n            console.error('No room ID found');\n            return;\n        }\n        console.log(\"Granting edit permission to \".concat(targetSocketId));\n        try {\n            // FIX: Use the public method, not .emit\n            socketService.setEditPermission(roomId, targetSocketId, true);\n        } catch (error) {\n            console.error('Error granting edit permission:', error);\n        }\n    };\n    // Revoke edit permission from a student\n    const revokeEditPermission = (targetSocketId)=>{\n        if (!isTeacher) {\n            console.warn('Only teachers can revoke edit permissions');\n            return;\n        }\n        if (!socketService || !socketReady) {\n            console.warn('Socket service not ready, cannot revoke edit permission');\n            return;\n        }\n        const roomId =  true ? window.localStorage.getItem('currentRoomId') : 0;\n        if (!roomId) {\n            console.error('No room ID found');\n            return;\n        }\n        console.log(\"Revoking edit permission from \".concat(targetSocketId));\n        try {\n            // FIX: Use the public method, not .emit\n            socketService.setEditPermission(roomId, targetSocketId, false);\n        } catch (error) {\n            console.error('Error revoking edit permission:', error);\n        }\n    };\n    // Legacy method for backward compatibility\n    const setEditPermission = (targetSocketId, canEdit)=>{\n        if (canEdit) {\n            grantEditPermission(targetSocketId);\n        } else {\n            revokeEditPermission(targetSocketId);\n        }\n    };\n    const updateUserPermission = (socketId, canEdit)=>{\n        setUsers((prevUsers)=>prevUsers.map((user)=>user.socketId === socketId ? {\n                    ...user,\n                    canEdit\n                } : user));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPermissionProvider.useEffect\": ()=>{\n            // Only set up listeners when socket is ready\n            if (!socketReady || !socketService) {\n                console.log('Waiting for socket to be ready for edit permissions...');\n                return;\n            }\n            // Listen for edit permission changes\n            const handleEditPermission = {\n                \"EditPermissionProvider.useEffect.handleEditPermission\": (data)=>{\n                    console.log('Received edit permission update:', data);\n                    setCanEdit(data.canEdit);\n                }\n            }[\"EditPermissionProvider.useEffect.handleEditPermission\"];\n            // Listen for permission updates (new event)\n            const handlePermissionUpdated = {\n                \"EditPermissionProvider.useEffect.handlePermissionUpdated\": (data)=>{\n                    console.log('Received permission-updated event:', data);\n                    setCanEdit(data.canEdit);\n                }\n            }[\"EditPermissionProvider.useEffect.handlePermissionUpdated\"];\n            // Listen for student list updates (for teachers) - ROBUST VERSION\n            const handleUpdateStudentList = {\n                \"EditPermissionProvider.useEffect.handleUpdateStudentList\": (data)=>{\n                    console.log('🎯 [ROBUST] Received update-student-list event:', JSON.stringify(data, null, 2));\n                    // Robust validation\n                    if (!data) {\n                        console.warn('🚨 [ROBUST] update-student-list event received with null/undefined data');\n                        return;\n                    }\n                    if (!data.students) {\n                        console.warn('🚨 [ROBUST] update-student-list event missing students property:', data);\n                        return;\n                    }\n                    if (!Array.isArray(data.students)) {\n                        console.warn('🚨 [ROBUST] update-student-list students is not an array:', typeof data.students, data.students);\n                        return;\n                    }\n                    console.log(\"✅ [ROBUST] Valid students array received with \".concat(data.students.length, \" items\"));\n                    // The backend already filters to only students, so we trust the data\n                    // Just ensure each student has required properties\n                    const validStudents = data.students.filter({\n                        \"EditPermissionProvider.useEffect.handleUpdateStudentList.validStudents\": (student)=>{\n                            const isValid = student && typeof student === 'object' && student.socketId && student.username && student.userId;\n                            if (!isValid) {\n                                console.warn('🚨 [ROBUST] Invalid student object:', student);\n                            }\n                            return isValid;\n                        }\n                    }[\"EditPermissionProvider.useEffect.handleUpdateStudentList.validStudents\"]);\n                    console.log(\"✅ [ROBUST] Setting \".concat(validStudents.length, \" valid students to state\"));\n                    setStudents(validStudents);\n                }\n            }[\"EditPermissionProvider.useEffect.handleUpdateStudentList\"];\n            // Listen for room users updates (includes permission info)\n            const handleRoomUsersUpdated = {\n                \"EditPermissionProvider.useEffect.handleRoomUsersUpdated\": (data)=>{\n                    console.log('Room users updated with permissions:', data);\n                    setUsers(data.users);\n                }\n            }[\"EditPermissionProvider.useEffect.handleRoomUsersUpdated\"];\n            // Listen for user-joined events to trigger student list refresh for teachers\n            const handleUserJoined = {\n                \"EditPermissionProvider.useEffect.handleUserJoined\": (data)=>{\n                    console.log('User joined event received:', data);\n                    // If this is a teacher, request updated student list\n                    if (isTeacher) {\n                        const currentRoomId = window.location.pathname.split('/').pop();\n                        if (currentRoomId) {\n                            console.log('Teacher detected user joined, requesting updated student list');\n                            const sock = socketService.getSocket && socketService.getSocket();\n                            if (sock && typeof sock.emit === 'function') {\n                                // Use type assertion to bypass TS error for custom event\n                                sock.emit('request-student-list', {\n                                    roomId: currentRoomId\n                                });\n                            } else {\n                                console.warn('Socket instance not available for request-student-list emit');\n                            }\n                        }\n                    }\n                }\n            }[\"EditPermissionProvider.useEffect.handleUserJoined\"];\n            // Set up socket listeners with null checks\n            try {\n                socketService.on('edit-permission', handleEditPermission);\n                socketService.on('permission-updated', handlePermissionUpdated);\n                socketService.on('update-student-list', handleUpdateStudentList);\n                socketService.on('room-users-updated', handleRoomUsersUpdated);\n                socketService.on('user-joined', handleUserJoined);\n                console.log('Enhanced permission socket listeners set up successfully');\n            } catch (error) {\n                console.error('Error setting up socket listeners:', error);\n            }\n            // Cleanup listeners\n            return ({\n                \"EditPermissionProvider.useEffect\": ()=>{\n                    try {\n                        if (socketService) {\n                            socketService.off('edit-permission', handleEditPermission);\n                            socketService.off('permission-updated', handlePermissionUpdated);\n                            socketService.off('update-student-list', handleUpdateStudentList);\n                            socketService.off('room-users-updated', handleRoomUsersUpdated);\n                            socketService.off('user-joined', handleUserJoined);\n                            console.log('Enhanced permission socket listeners cleaned up');\n                        }\n                    } catch (error) {\n                        console.error('Error cleaning up socket listeners:', error);\n                    }\n                }\n            })[\"EditPermissionProvider.useEffect\"];\n        }\n    }[\"EditPermissionProvider.useEffect\"], [\n        socketReady\n    ]);\n    // In the effect that requests the student list, use the correct public method\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPermissionProvider.useEffect\": ()=>{\n            if (!socketReady || !socketService || !isTeacher) {\n                return;\n            }\n            // If this is a teacher, request the initial student list\n            const currentRoomId = window.location.pathname.split('/').pop();\n            if (currentRoomId) {\n                console.log('Teacher role detected, requesting initial student list for room:', currentRoomId);\n                // FIX: Use the underlying socket directly for custom events\n                const sock = socketService.getSocket && socketService.getSocket();\n                if (sock && typeof sock.emit === 'function') {\n                    sock.emit('request-student-list', {\n                        roomId: currentRoomId\n                    });\n                } else {\n                    console.warn('Socket instance not available for request-student-list emit');\n                }\n            }\n        }\n    }[\"EditPermissionProvider.useEffect\"], [\n        socketReady,\n        isTeacher,\n        socketService\n    ]);\n    // Log permission changes for debugging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPermissionProvider.useEffect\": ()=>{\n            console.log(\"Edit permission state: canEdit=\".concat(canEdit, \", isTeacher=\").concat(isTeacher, \", socketReady=\").concat(socketReady, \", isConnected=\").concat(isConnected));\n        }\n    }[\"EditPermissionProvider.useEffect\"], [\n        canEdit,\n        isTeacher,\n        socketReady,\n        isConnected\n    ]);\n    const value = {\n        canEdit,\n        isTeacher,\n        users,\n        students,\n        permissionBadge,\n        grantEditPermission,\n        revokeEditPermission,\n        setEditPermission,\n        updateUserPermission,\n        setUsers,\n        setStudents,\n        setCanEdit,\n        setIsTeacher\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditPermissionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\context\\\\EditPermissionContext.tsx\",\n        lineNumber: 295,\n        columnNumber: 5\n    }, this);\n}\n_s(EditPermissionProvider, \"udh6tS1itqpINKcvqOf60EZ6C1A=\", false, function() {\n    return [\n        _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__.useSocketService\n    ];\n});\n_c = EditPermissionProvider;\nfunction useEditPermission() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(EditPermissionContext);\n    if (context === undefined) {\n        throw new Error('useEditPermission must be used within an EditPermissionProvider');\n    }\n    return context;\n}\n_s1(useEditPermission, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"EditPermissionProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/context/EditPermissionContext.tsx\n"));

/***/ })

});