"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_csp_csp_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/csp/csp.js":
/*!***********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/csp/csp.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/csp/csp.ts\nvar conf = {\n  brackets: [],\n  autoClosingPairs: [],\n  surroundingPairs: []\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  // defaultToken: 'invalid',\n  keywords: [],\n  typeKeywords: [],\n  tokenPostfix: \".csp\",\n  operators: [],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  tokenizer: {\n    root: [\n      [/child-src/, \"string.quote\"],\n      [/connect-src/, \"string.quote\"],\n      [/default-src/, \"string.quote\"],\n      [/font-src/, \"string.quote\"],\n      [/frame-src/, \"string.quote\"],\n      [/img-src/, \"string.quote\"],\n      [/manifest-src/, \"string.quote\"],\n      [/media-src/, \"string.quote\"],\n      [/object-src/, \"string.quote\"],\n      [/script-src/, \"string.quote\"],\n      [/style-src/, \"string.quote\"],\n      [/worker-src/, \"string.quote\"],\n      [/base-uri/, \"string.quote\"],\n      [/plugin-types/, \"string.quote\"],\n      [/sandbox/, \"string.quote\"],\n      [/disown-opener/, \"string.quote\"],\n      [/form-action/, \"string.quote\"],\n      [/frame-ancestors/, \"string.quote\"],\n      [/report-uri/, \"string.quote\"],\n      [/report-to/, \"string.quote\"],\n      [/upgrade-insecure-requests/, \"string.quote\"],\n      [/block-all-mixed-content/, \"string.quote\"],\n      [/require-sri-for/, \"string.quote\"],\n      [/reflected-xss/, \"string.quote\"],\n      [/referrer/, \"string.quote\"],\n      [/policy-uri/, \"string.quote\"],\n      [/'self'/, \"string.quote\"],\n      [/'unsafe-inline'/, \"string.quote\"],\n      [/'unsafe-eval'/, \"string.quote\"],\n      [/'strict-dynamic'/, \"string.quote\"],\n      [/'unsafe-hashed-attributes'/, \"string.quote\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbW9uYWNvLWVkaXRvci9lc20vdnMvYmFzaWMtbGFuZ3VhZ2VzL2NzcC9jc3AuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUdBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsSUFBSSxjQUFjLEVBQUUsY0FBYyxFQUFFO0FBQ2hGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUlFIiwic291cmNlcyI6WyJDOlxcUHJvamVjdFxccmVhbGNvZGVcXG5vZGVfbW9kdWxlc1xcbW9uYWNvLWVkaXRvclxcZXNtXFx2c1xcYmFzaWMtbGFuZ3VhZ2VzXFxjc3BcXGNzcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiEtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4gKiBWZXJzaW9uOiAwLjUyLjIoNDA0NTQ1YmRlZDFkZjZmZmE0MWVhMGFmNGU4ZGRiMjE5MDE4YzZjMSlcbiAqIFJlbGVhc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZVxuICogaHR0cHM6Ly9naXRodWIuY29tL21pY3Jvc29mdC9tb25hY28tZWRpdG9yL2Jsb2IvbWFpbi9MSUNFTlNFLnR4dFxuICotLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cblxuLy8gc3JjL2Jhc2ljLWxhbmd1YWdlcy9jc3AvY3NwLnRzXG52YXIgY29uZiA9IHtcbiAgYnJhY2tldHM6IFtdLFxuICBhdXRvQ2xvc2luZ1BhaXJzOiBbXSxcbiAgc3Vycm91bmRpbmdQYWlyczogW11cbn07XG52YXIgbGFuZ3VhZ2UgPSB7XG4gIC8vIFNldCBkZWZhdWx0VG9rZW4gdG8gaW52YWxpZCB0byBzZWUgd2hhdCB5b3UgZG8gbm90IHRva2VuaXplIHlldFxuICAvLyBkZWZhdWx0VG9rZW46ICdpbnZhbGlkJyxcbiAga2V5d29yZHM6IFtdLFxuICB0eXBlS2V5d29yZHM6IFtdLFxuICB0b2tlblBvc3RmaXg6IFwiLmNzcFwiLFxuICBvcGVyYXRvcnM6IFtdLFxuICBzeW1ib2xzOiAvWz0+PCF+PzomfCtcXC0qXFwvXFxeJV0rLyxcbiAgZXNjYXBlczogL1xcXFwoPzpbYWJmbnJ0dlxcXFxcIiddfHhbMC05QS1GYS1mXXsxLDR9fHVbMC05QS1GYS1mXXs0fXxVWzAtOUEtRmEtZl17OH0pLyxcbiAgdG9rZW5pemVyOiB7XG4gICAgcm9vdDogW1xuICAgICAgWy9jaGlsZC1zcmMvLCBcInN0cmluZy5xdW90ZVwiXSxcbiAgICAgIFsvY29ubmVjdC1zcmMvLCBcInN0cmluZy5xdW90ZVwiXSxcbiAgICAgIFsvZGVmYXVsdC1zcmMvLCBcInN0cmluZy5xdW90ZVwiXSxcbiAgICAgIFsvZm9udC1zcmMvLCBcInN0cmluZy5xdW90ZVwiXSxcbiAgICAgIFsvZnJhbWUtc3JjLywgXCJzdHJpbmcucXVvdGVcIl0sXG4gICAgICBbL2ltZy1zcmMvLCBcInN0cmluZy5xdW90ZVwiXSxcbiAgICAgIFsvbWFuaWZlc3Qtc3JjLywgXCJzdHJpbmcucXVvdGVcIl0sXG4gICAgICBbL21lZGlhLXNyYy8sIFwic3RyaW5nLnF1b3RlXCJdLFxuICAgICAgWy9vYmplY3Qtc3JjLywgXCJzdHJpbmcucXVvdGVcIl0sXG4gICAgICBbL3NjcmlwdC1zcmMvLCBcInN0cmluZy5xdW90ZVwiXSxcbiAgICAgIFsvc3R5bGUtc3JjLywgXCJzdHJpbmcucXVvdGVcIl0sXG4gICAgICBbL3dvcmtlci1zcmMvLCBcInN0cmluZy5xdW90ZVwiXSxcbiAgICAgIFsvYmFzZS11cmkvLCBcInN0cmluZy5xdW90ZVwiXSxcbiAgICAgIFsvcGx1Z2luLXR5cGVzLywgXCJzdHJpbmcucXVvdGVcIl0sXG4gICAgICBbL3NhbmRib3gvLCBcInN0cmluZy5xdW90ZVwiXSxcbiAgICAgIFsvZGlzb3duLW9wZW5lci8sIFwic3RyaW5nLnF1b3RlXCJdLFxuICAgICAgWy9mb3JtLWFjdGlvbi8sIFwic3RyaW5nLnF1b3RlXCJdLFxuICAgICAgWy9mcmFtZS1hbmNlc3RvcnMvLCBcInN0cmluZy5xdW90ZVwiXSxcbiAgICAgIFsvcmVwb3J0LXVyaS8sIFwic3RyaW5nLnF1b3RlXCJdLFxuICAgICAgWy9yZXBvcnQtdG8vLCBcInN0cmluZy5xdW90ZVwiXSxcbiAgICAgIFsvdXBncmFkZS1pbnNlY3VyZS1yZXF1ZXN0cy8sIFwic3RyaW5nLnF1b3RlXCJdLFxuICAgICAgWy9ibG9jay1hbGwtbWl4ZWQtY29udGVudC8sIFwic3RyaW5nLnF1b3RlXCJdLFxuICAgICAgWy9yZXF1aXJlLXNyaS1mb3IvLCBcInN0cmluZy5xdW90ZVwiXSxcbiAgICAgIFsvcmVmbGVjdGVkLXhzcy8sIFwic3RyaW5nLnF1b3RlXCJdLFxuICAgICAgWy9yZWZlcnJlci8sIFwic3RyaW5nLnF1b3RlXCJdLFxuICAgICAgWy9wb2xpY3ktdXJpLywgXCJzdHJpbmcucXVvdGVcIl0sXG4gICAgICBbLydzZWxmJy8sIFwic3RyaW5nLnF1b3RlXCJdLFxuICAgICAgWy8ndW5zYWZlLWlubGluZScvLCBcInN0cmluZy5xdW90ZVwiXSxcbiAgICAgIFsvJ3Vuc2FmZS1ldmFsJy8sIFwic3RyaW5nLnF1b3RlXCJdLFxuICAgICAgWy8nc3RyaWN0LWR5bmFtaWMnLywgXCJzdHJpbmcucXVvdGVcIl0sXG4gICAgICBbLyd1bnNhZmUtaGFzaGVkLWF0dHJpYnV0ZXMnLywgXCJzdHJpbmcucXVvdGVcIl1cbiAgICBdXG4gIH1cbn07XG5leHBvcnQge1xuICBjb25mLFxuICBsYW5ndWFnZVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/csp/csp.js\n"));

/***/ })

}]);