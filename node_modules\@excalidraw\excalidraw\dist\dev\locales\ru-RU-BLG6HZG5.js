import "../chunk-XDFCUUT6.js";

// locales/ru-RU.json
var labels = {
  paste: "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044C",
  pasteAsPlaintext: "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u043A\u0430\u043A \u043E\u0431\u044B\u0447\u043D\u044B\u0439 \u0442\u0435\u043A\u0441\u0442",
  pasteCharts: "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u0434\u0438\u0430\u0433\u0440\u0430\u043C\u043C\u044B",
  selectAll: "\u0412\u044B\u0431\u0440\u0430\u0442\u044C \u0432\u0441\u0451",
  multiSelect: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u044D\u043B\u0435\u043C\u0435\u043D\u0442 \u0432 \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u044B\u0439 \u0444\u0440\u0430\u0433\u043C\u0435\u043D\u0442",
  moveCanvas: "\u041F\u0435\u0440\u0435\u043C\u0435\u0441\u0442\u0438\u0442\u044C \u0445\u043E\u043B\u0441\u0442",
  cut: "\u0412\u044B\u0440\u0435\u0437\u0430\u0442\u044C",
  copy: "\u041A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C",
  copyAsPng: "\u0421\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432 \u0431\u0443\u0444\u0435\u0440 \u043E\u0431\u043C\u0435\u043D\u0430 \u043A\u0430\u043A PNG",
  copyAsSvg: "\u0421\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432 \u0431\u0443\u0444\u0435\u0440 \u043E\u0431\u043C\u0435\u043D\u0430 \u043A\u0430\u043A SVG",
  copyText: "\u0421\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432 \u0431\u0443\u0444\u0435\u0440 \u043E\u0431\u043C\u0435\u043D\u0430 \u043A\u0430\u043A \u0442\u0435\u043A\u0441\u0442",
  copySource: "\u041A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0438\u0441\u0442\u043E\u0447\u043D\u0438\u043A \u0432 \u0431\u0443\u0444\u0435\u0440 \u043E\u0431\u043C\u0435\u043D\u0430",
  convertToCode: "\u041F\u0440\u0435\u043E\u0431\u0440\u0430\u0437\u043E\u0432\u0430\u0442\u044C \u0432 \u043A\u043E\u0434",
  bringForward: "\u041F\u0435\u0440\u0435\u043C\u0435\u0441\u0442\u0438\u0442\u044C \u0432\u043F\u0435\u0440\u0435\u0434",
  sendToBack: "\u041D\u0430 \u0437\u0430\u0434\u043D\u0438\u0439 \u043F\u043B\u0430\u043D",
  bringToFront: "\u041D\u0430 \u043F\u0435\u0440\u0435\u0434\u043D\u0438\u0439 \u043F\u043B\u0430\u043D",
  sendBackward: "\u041F\u0435\u0440\u0435\u043C\u0435\u0441\u0442\u0438\u0442\u044C \u043D\u0430\u0437\u0430\u0434",
  delete: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C",
  copyStyles: "\u0421\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0441\u0442\u0438\u043B\u0438",
  pasteStyles: "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u0441\u0442\u0438\u043B\u0438",
  stroke: "\u041E\u0431\u0432\u043E\u0434\u043A\u0430",
  background: "\u0424\u043E\u043D",
  fill: "\u0417\u0430\u043B\u0438\u0432\u043A\u0430",
  strokeWidth: "\u0422\u043E\u043B\u0449\u0438\u043D\u0430 \u0448\u0442\u0440\u0438\u0445\u0430",
  strokeStyle: "\u0421\u0442\u0438\u043B\u044C \u043E\u0431\u0432\u043E\u0434\u043A\u0438",
  strokeStyle_solid: "\u0421\u043F\u043B\u043E\u0448\u043D\u0430\u044F",
  strokeStyle_dashed: "\u041F\u0443\u043D\u043A\u0442\u0438\u0440\u043D\u0430\u044F",
  strokeStyle_dotted: "\u0422\u043E\u0447\u0435\u0447\u043D\u0430\u044F",
  sloppiness: "\u0421\u0442\u0438\u043B\u044C \u043E\u0431\u0432\u043E\u0434\u043A\u0438",
  opacity: "\u041D\u0435\u043F\u0440\u043E\u0437\u0440\u0430\u0447\u043D\u043E\u0441\u0442\u044C",
  textAlign: "\u0412\u044B\u0440\u0430\u0432\u043D\u0438\u0432\u0430\u043D\u0438\u0435 \u0442\u0435\u043A\u0441\u0442\u0430",
  edges: "\u041A\u0440\u0430\u044F",
  sharp: "\u041E\u0441\u0442\u0440\u044B\u0435",
  round: "\u0421\u043A\u0440\u0443\u0433\u043B\u0435\u043D\u043D\u044B\u0435",
  arrowheads: "\u0421\u0442\u0440\u0435\u043B\u043A\u0430",
  arrowhead_none: "\u041D\u0435\u0442",
  arrowhead_arrow: "C\u0442\u0440\u0435\u043B\u043A\u0430",
  arrowhead_bar: "\u0427\u0435\u0440\u0442\u0430",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "\u0422\u0440\u0435\u0443\u0433\u043E\u043B\u044C\u043D\u0438\u043A",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "\u0420\u0430\u0437\u043C\u0435\u0440 \u0448\u0440\u0438\u0444\u0442\u0430",
  fontFamily: "\u0421\u0435\u043C\u0435\u0439\u0441\u0442\u0432\u043E \u0448\u0440\u0438\u0444\u0442\u043E\u0432",
  addWatermark: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \xAB\u0421\u043E\u0437\u0434\u0430\u043D\u043E \u0432 Excalidraw\xBB",
  handDrawn: "\u041E\u0442 \u0440\u0443\u043A\u0438",
  normal: "\u041E\u0431\u044B\u0447\u043D\u044B\u0439",
  code: "\u041A\u043E\u0434",
  small: "\u041C\u0430\u043B\u044B\u0439",
  medium: "\u0421\u0440\u0435\u0434\u043D\u0438\u0439",
  large: "\u0411\u043E\u043B\u044C\u0448\u043E\u0439",
  veryLarge: "\u041E\u0447\u0435\u043D\u044C \u0431\u043E\u043B\u044C\u0448\u043E\u0439",
  solid: "\u041E\u0434\u043D\u043E\u0442\u043E\u043D\u043D\u0430\u044F",
  hachure: "\u0428\u0442\u0440\u0438\u0445\u043E\u0432\u0430\u043D\u043D\u0430\u044F",
  zigzag: "\u0417\u0438\u0433\u0437\u0430\u0433",
  crossHatch: "\u041F\u0435\u0440\u0435\u043A\u0440\u0435\u0441\u0442\u043D\u0430\u044F",
  thin: "\u0422\u043E\u043D\u043A\u0430\u044F",
  bold: "\u0416\u0438\u0440\u043D\u0430\u044F",
  left: "\u0421\u043B\u0435\u0432\u0430",
  center: "\u0426\u0435\u043D\u0442\u0440",
  right: "\u0421\u043F\u0440\u0430\u0432\u0430",
  extraBold: "\u041E\u0447\u0435\u043D\u044C \u0436\u0438\u0440\u043D\u0430\u044F",
  architect: "\u0410\u0440\u0445\u0438\u0442\u0435\u043A\u0442\u043E\u0440",
  artist: "\u0425\u0443\u0434\u043E\u0436\u043D\u0438\u043A",
  cartoonist: "\u041A\u0430\u0440\u0438\u043A\u0430\u0442\u0443\u0440\u0438\u0441\u0442",
  fileTitle: "\u0418\u043C\u044F \u0444\u0430\u0439\u043B\u0430",
  colorPicker: "\u0412\u044B\u0431\u043E\u0440 \u0446\u0432\u0435\u0442\u0430",
  canvasColors: "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442\u0441\u044F \u043D\u0430 \u0445\u043E\u043B\u0441\u0442\u0435",
  canvasBackground: "\u0424\u043E\u043D \u0445\u043E\u043B\u0441\u0442\u0430",
  drawingCanvas: "\u041F\u043E\u043B\u043E\u0442\u043D\u043E",
  layers: "\u0421\u043B\u043E\u0438",
  actions: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
  language: "\u042F\u0437\u044B\u043A",
  liveCollaboration: "\u041E\u043D\u043B\u0430\u0439\u043D \u0432\u0437\u0430\u0438\u043C\u043E\u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0435...",
  duplicateSelection: "\u0414\u0443\u0431\u043B\u0438\u043A\u0430\u0442",
  untitled: "\u0411\u0435\u0437\u044B\u043C\u044F\u043D\u043D\u044B\u0439",
  name: "\u0418\u043C\u044F",
  yourName: "\u0412\u0430\u0448\u0435 \u0438\u043C\u044F",
  madeWithExcalidraw: "\u0421\u0434\u0435\u043B\u0430\u043D\u043E \u0432 Excalidraw",
  group: "\u0421\u0433\u0440\u0443\u043F\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u0438\u0435",
  ungroup: "\u0420\u0430\u0437\u0434\u0435\u043B\u0438\u0442\u044C \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u0438\u0435",
  collaborators: "\u0423\u0447\u0430\u0441\u0442\u043D\u0438\u043A\u0438",
  showGrid: "\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u0441\u0435\u0442\u043A\u0443",
  addToLibrary: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0432 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0443",
  removeFromLibrary: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u0438\u0437 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438",
  libraryLoadingMessage: "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438\u2026",
  libraries: "\u041F\u0440\u043E\u0441\u043C\u043E\u0442\u0440\u0435\u0442\u044C \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438",
  loadingScene: "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u0441\u0446\u0435\u043D\u044B\u2026",
  align: "\u0412\u044B\u0440\u043E\u0432\u043D\u044F\u0442\u044C",
  alignTop: "\u0412\u044B\u0440\u043E\u0432\u043D\u044F\u0442\u044C \u043F\u043E \u0432\u0435\u0440\u0445\u043D\u0435\u043C\u0443 \u043A\u0440\u0430\u044E",
  alignBottom: "\u0412\u044B\u0440\u043E\u0432\u043D\u044F\u0442\u044C \u043F\u043E \u043D\u0438\u0436\u043D\u0435\u043C\u0443 \u043A\u0440\u0430\u044E",
  alignLeft: "\u0412\u044B\u0440\u043E\u0432\u043D\u044F\u0442\u044C \u043F\u043E \u043B\u0435\u0432\u043E\u043C\u0443 \u043A\u0440\u0430\u044E",
  alignRight: "\u0412\u044B\u0440\u043E\u0432\u043D\u044F\u0442\u044C \u043F\u043E \u043F\u0440\u0430\u0432\u043E\u043C\u0443 \u043A\u0440\u0430\u044E",
  centerVertically: "\u0426\u0435\u043D\u0442\u0440\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043F\u043E \u0432\u0435\u0440\u0442\u0438\u043A\u0430\u043B\u0438",
  centerHorizontally: "\u0426\u0435\u043D\u0442\u0440\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043F\u043E \u0433\u043E\u0440\u0438\u0437\u043E\u043D\u0442\u0430\u043B\u0438",
  distributeHorizontally: "\u0420\u0430\u0441\u043F\u0440\u0435\u0434\u0435\u043B\u0438\u0442\u044C \u043F\u043E \u0433\u043E\u0440\u0438\u0437\u043E\u043D\u0442\u0430\u043B\u0438",
  distributeVertically: "\u0420\u0430\u0441\u043F\u0440\u0435\u0434\u0435\u043B\u0438\u0442\u044C \u043F\u043E \u0432\u0435\u0440\u0442\u0438\u043A\u0430\u043B\u0438",
  flipHorizontal: "\u041F\u0435\u0440\u0435\u0432\u043E\u0440\u043E\u0442 \u043F\u043E \u0433\u043E\u0440\u0438\u0437\u043E\u043D\u0442\u0430\u043B\u0438",
  flipVertical: "\u041F\u0435\u0440\u0435\u0432\u043E\u0440\u043E\u0442 \u043F\u043E \u0432\u0435\u0440\u0442\u0438\u043A\u0430\u043B\u0438",
  viewMode: "\u0412\u0438\u0434",
  share: "\u041F\u043E\u0434\u0435\u043B\u0438\u0442\u044C\u0441\u044F",
  showStroke: "\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u0432\u044B\u0431\u043E\u0440 \u0446\u0432\u0435\u0442\u0430 \u043E\u0431\u0432\u043E\u0434\u043A\u0438",
  showBackground: "\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u0432\u044B\u0431\u043E\u0440 \u0446\u0432\u0435\u0442\u0430 \u0444\u043E\u043D\u0430",
  toggleTheme: "\u041F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u0442\u0435\u043C\u0443",
  personalLib: "\u041B\u0438\u0447\u043D\u0430\u044F \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430",
  excalidrawLib: "\u0411\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430 Excalidraw",
  decreaseFontSize: "\u0423\u043C\u0435\u043D\u044C\u0448\u0438\u0442\u044C \u0448\u0440\u0438\u0444\u0442",
  increaseFontSize: "\u0423\u0432\u0435\u043B\u0438\u0447\u0438\u0442\u044C \u0448\u0440\u0438\u0444\u0442",
  unbindText: "\u041E\u0442\u0432\u044F\u0437\u0430\u0442\u044C \u0442\u0435\u043A\u0441\u0442",
  bindText: "\u041F\u0440\u0438\u0432\u044F\u0437\u0430\u0442\u044C \u0442\u0435\u043A\u0441\u0442 \u043A \u043A\u043E\u043D\u0442\u0435\u0439\u043D\u0435\u0440\u0443",
  createContainerFromText: "\u041F\u043E\u043C\u0435\u0441\u0442\u0438\u0442\u044C \u0442\u0435\u043A\u0441\u0442 \u0432 \u043A\u043E\u043D\u0442\u0435\u0439\u043D\u0435\u0440",
  link: {
    edit: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0441\u0441\u044B\u043B\u043A\u0443",
    editEmbed: "",
    create: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0441\u0441\u044B\u043B\u043A\u0443",
    createEmbed: "",
    label: "\u0421\u0441\u044B\u043B\u043A\u0430",
    labelEmbed: "",
    empty: ""
  },
  lineEditor: {
    edit: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435 \u0441\u0442\u0440\u043E\u043A\u0438",
    exit: "\u0412\u044B\u0445\u043E\u0434 \u0438\u0437 \u0440\u0435\u0434\u0430\u043A\u0442\u043E\u0440\u0430 \u0441\u0442\u0440\u043E\u043A\u0438"
  },
  elementLock: {
    lock: "\u0411\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u0430\u0442\u044C",
    unlock: "\u0420\u0430\u0437\u0431\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u0430\u0442\u044C",
    lockAll: "\u0417\u0430\u0431\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432\u0441\u0435",
    unlockAll: "\u0420\u0430\u0437\u0431\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432\u0441\u0435"
  },
  statusPublished: "\u041E\u043F\u0443\u0431\u043B\u0438\u043A\u043E\u0432\u0430\u043D\u043E",
  sidebarLock: "\u0414\u0435\u0440\u0436\u0430\u0442\u044C \u0431\u043E\u043A\u043E\u0432\u0443\u044E \u043F\u0430\u043D\u0435\u043B\u044C \u043E\u0442\u043A\u0440\u044B\u0442\u043E\u0439",
  selectAllElementsInFrame: "",
  removeAllElementsFromFrame: "",
  eyeDropper: "\u0412\u0437\u044F\u0442\u044C \u043E\u0431\u0440\u0430\u0437\u0435\u0446 \u0446\u0432\u0435\u0442\u0430 \u0441 \u0445\u043E\u043B\u0441\u0442\u0430",
  textToDiagram: "\u0422\u0435\u043A\u0441\u0442 \u0432 \u0434\u0438\u0430\u0433\u0440\u0430\u043C\u043C\u0443",
  prompt: ""
};
var library = {
  noItems: "\u041F\u043E\u043A\u0430 \u043D\u0438\u0447\u0435\u0433\u043E \u043D\u0435 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u043E...",
  hint_emptyLibrary: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043E\u0431\u044A\u0435\u043A\u0442 \u043D\u0430 \u0445\u043E\u043B\u0441\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0435\u0433\u043E \u0441\u044E\u0434\u0430, \u0438\u043B\u0438 \u0443\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0443 \u0438\u0437 \u043F\u0443\u0431\u043B\u0438\u0447\u043D\u043E\u0433\u043E \u0440\u0435\u043F\u043E\u0437\u0438\u0442\u043E\u0440\u0438\u044F \u043D\u0438\u0436\u0435.",
  hint_emptyPrivateLibrary: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043E\u0431\u044A\u0435\u043A\u0442 \u043D\u0430 \u0445\u043E\u043B\u0441\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0435\u0433\u043E \u0441\u044E\u0434\u0430."
};
var buttons = {
  clearReset: "\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C \u0445\u043E\u043B\u0441\u0442 \u0438 \u0441\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u0446\u0432\u0435\u0442 \u0444\u043E\u043D\u0430",
  exportJSON: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0432",
  exportImage: "\u042D\u043A\u0441\u043F\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435...",
  export: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u043A\u0430\u043A...",
  copyToClipboard: "\u0421\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432 \u0431\u0443\u0444\u0435\u0440 \u043E\u0431\u043C\u0435\u043D\u0430",
  save: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0432 \u0442\u0435\u043A\u0443\u0449\u0438\u0439 \u0444\u0430\u0439\u043B",
  saveAs: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u043A\u0430\u043A",
  load: "\u041E\u0442\u043A\u0440\u044B\u0442\u044C",
  getShareableLink: "\u041F\u043E\u043B\u0443\u0447\u0438\u0442\u044C \u0434\u043E\u0441\u0442\u0443\u043F \u043F\u043E \u0441\u0441\u044B\u043B\u043A\u0435",
  close: "\u0417\u0430\u043A\u0440\u044B\u0442\u044C",
  selectLanguage: "\u0412\u044B\u0431\u0440\u0430\u0442\u044C \u044F\u0437\u044B\u043A",
  scrollBackToContent: "\u0412\u0435\u0440\u043D\u0443\u0442\u044C\u0441\u044F \u043A \u0441\u043E\u0434\u0435\u0440\u0436\u0438\u043C\u043E\u043C\u0443",
  zoomIn: "\u0423\u0432\u0435\u043B\u0438\u0447\u0438\u0442\u044C",
  zoomOut: "\u0423\u043C\u0435\u043D\u044C\u0448\u0438\u0442\u044C",
  resetZoom: "\u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u043C\u0430\u0441\u0448\u0442\u0430\u0431",
  menu: "\u041C\u0435\u043D\u044E",
  done: "\u0413\u043E\u0442\u043E\u0432\u043E",
  edit: "\u0418\u0437\u043C\u0435\u043D\u0438\u0442\u044C",
  undo: "\u0428\u0430\u0433 \u043D\u0430\u0437\u0430\u0434",
  redo: "\u0428\u0430\u0433 \u0432\u043F\u0435\u0440\u0435\u0434",
  resetLibrary: "\u0421\u0431\u0440\u043E\u0441 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438",
  createNewRoom: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043D\u043E\u0432\u0443\u044E \u043A\u043E\u043C\u043D\u0430\u0442\u0443",
  fullScreen: "\u041F\u043E\u043B\u043D\u044B\u0439 \u044D\u043A\u0440\u0430\u043D",
  darkMode: "\u0422\u0435\u043C\u043D\u0430\u044F \u0442\u0435\u043C\u0430",
  lightMode: "\u0421\u0432\u0435\u0442\u043B\u0430\u044F \u0442\u0435\u043C\u0430",
  zenMode: "\u0420\u0435\u0436\u0438\u043C \u0414\u0437\u0435\u043D",
  objectsSnapMode: "\u041F\u0440\u0438\u0432\u044F\u0437\u043A\u0430 \u043A \u043E\u0431\u044A\u0435\u043A\u0442\u0430\u043C",
  exitZenMode: "\u0412\u044B\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u043A\u043E\u043D\u0446\u0435\u043D\u0442\u0440\u0430\u0446\u0438\u0438 \u0432\u043D\u0438\u043C\u0430\u043D\u0438\u044F",
  cancel: "\u041E\u0442\u043C\u0435\u043D\u0438\u0442\u044C",
  clear: "\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",
  remove: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C",
  embed: "",
  publishLibrary: "\u041E\u043F\u0443\u0431\u043B\u0438\u043A\u043E\u0432\u0430\u0442\u044C",
  submit: "\u041E\u0442\u043F\u0440\u0430\u0432\u0438\u0442\u044C",
  confirm: "\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0434\u0438\u0442\u044C",
  embeddableInteractionButton: ""
};
var alerts = {
  clearReset: "\u042D\u0442\u043E \u043E\u0447\u0438\u0441\u0442\u0438\u0442 \u0432\u0435\u0441\u044C \u0445\u043E\u043B\u0441\u0442. \u0412\u044B \u0443\u0432\u0435\u0440\u0435\u043D\u044B?",
  couldNotCreateShareableLink: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043E\u0437\u0434\u0430\u0442\u044C \u043E\u0431\u0449\u0435\u0434\u043E\u0441\u0442\u0443\u043F\u043D\u0443\u044E \u0441\u0441\u044B\u043B\u043A\u0443.",
  couldNotCreateShareableLinkTooBig: "\u041D\u0435\u043B\u044C\u0437\u044F \u0441\u043E\u0437\u0434\u0430\u0442\u044C \u0441\u0441\u044B\u043B\u043A\u0443, \u0447\u0442\u043E\u0431\u044B \u043F\u043E\u0434\u0435\u043B\u0438\u0442\u044C\u0441\u044F. \u0421\u0446\u0435\u043D\u0430 \u0441\u043B\u0438\u0448\u043A\u043E\u043C \u0431\u043E\u043B\u044C\u0448\u0430\u044F",
  couldNotLoadInvalidFile: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u043D\u0435\u0434\u043E\u043F\u0443\u0441\u0442\u0438\u043C\u044B\u0439 \u0444\u0430\u0439\u043B",
  importBackendFailed: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0438\u043C\u043F\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0438\u0437 \u0431\u044D\u043A\u044D\u043D\u0434\u0430.",
  cannotExportEmptyCanvas: "\u041D\u0435 \u043C\u043E\u0436\u0435\u0442 \u044D\u043A\u0441\u043F\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043F\u0443\u0441\u0442\u043E\u0439 \u0445\u043E\u043B\u0441\u0442.",
  couldNotCopyToClipboard: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432 \u0431\u0443\u0444\u0435\u0440 \u043E\u0431\u043C\u0435\u043D\u0430.",
  decryptFailed: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0440\u0430\u0441\u0448\u0438\u0444\u0440\u043E\u0432\u0430\u0442\u044C \u0434\u0430\u043D\u043D\u044B\u0435.",
  uploadedSecurly: "\u0417\u0430\u0433\u0440\u0443\u0436\u0430\u0435\u043C\u044B\u0435 \u0434\u0430\u043D\u043D\u044B\u0435 \u0437\u0430\u0449\u0438\u0449\u0435\u043D\u0430 \u0441\u043A\u0432\u043E\u0437\u043D\u044B\u043C \u0448\u0438\u0444\u0440\u043E\u0432\u0430\u043D\u0438\u0435\u043C, \u0447\u0442\u043E \u043E\u0437\u043D\u0430\u0447\u0430\u0435\u0442, \u0447\u0442\u043E \u0441\u0435\u0440\u0432\u0435\u0440 Excalidraw \u0438 \u0442\u0440\u0435\u0442\u044C\u0438 \u0441\u0442\u043E\u0440\u043E\u043D\u044B \u043D\u0435 \u043C\u043E\u0433\u0443\u0442 \u043F\u0440\u043E\u0447\u0438\u0442\u0430\u0442\u044C \u0441\u043E\u0434\u0435\u0440\u0436\u0438\u043C\u043E\u0435.",
  loadSceneOverridePrompt: "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u0440\u0438\u0441\u0443\u043D\u043A\u0430 \u043F\u0440\u0438\u0432\u0435\u0434\u0451\u0442 \u043A \u0437\u0430\u043C\u0435\u043D\u0435 \u0438\u043C\u0435\u044E\u0449\u0435\u0433\u043E\u0441\u044F \u0441\u043E\u0434\u0435\u0440\u0436\u0438\u043C\u043E\u0433\u043E. \u0412\u044B \u0445\u043E\u0442\u0438\u0442\u0435 \u043F\u0440\u043E\u0434\u043E\u043B\u0436\u0438\u0442\u044C?",
  collabStopOverridePrompt: "\u041E\u0441\u0442\u0430\u043D\u043E\u0432\u043A\u0430 \u0441\u0435\u0441\u0441\u0438\u0438 \u043F\u0435\u0440\u0435\u0437\u0430\u043F\u0438\u0448\u0435\u0442 \u0432\u0430\u0448 \u043F\u0440\u0435\u0434\u044B\u0434\u0443\u0449\u0438\u0439, \u043B\u043E\u043A\u0430\u043B\u044C\u043D\u043E \u0441\u043E\u0445\u0440\u0430\u043D\u0451\u043D\u043D\u044B\u0439 \u0440\u0438\u0441\u0443\u043D\u043E\u043A. \u0412\u044B \u0443\u0432\u0435\u0440\u0435\u043D\u044B? \n\n(\u0415\u0441\u043B\u0438 \u0432\u044B \u0445\u043E\u0442\u0438\u0442\u0435 \u043E\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u0432\u0430\u0448 \u043B\u043E\u043A\u0430\u043B\u044C\u043D\u044B\u0439 \u0440\u0438\u0441\u0443\u043D\u043E\u043A, \u043F\u0440\u043E\u0441\u0442\u043E \u0437\u0430\u043A\u0440\u043E\u0439\u0442\u0435 \u0432\u043A\u043B\u0430\u0434\u043A\u0443 \u0431\u0440\u0430\u0443\u0437\u0435\u0440\u0430)",
  errorAddingToLibrary: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043E\u0431\u044A\u0435\u043A\u0442 \u0432 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0443",
  errorRemovingFromLibrary: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u043E\u0431\u044A\u0435\u043A\u0442 \u0438\u0437 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438",
  confirmAddLibrary: "\u0411\u0443\u0434\u0435\u0442 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u043E {{numShapes}} \u0444\u0438\u0433\u0443\u0440 \u0432 \u0432\u0430\u0448\u0443 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0443. \u041F\u0440\u043E\u0434\u043E\u043B\u0436\u0438\u0442\u044C?",
  imageDoesNotContainScene: "\u042D\u0442\u043E \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435 \u043D\u0435 \u0441\u043E\u0434\u0435\u0440\u0436\u0438\u0442 \u0434\u0430\u043D\u043D\u044B\u0445 \u0441\u0446\u0435\u043D\u044B. \u0412\u044B \u0432\u043A\u043B\u044E\u0447\u0438\u043B\u0438 \u0432\u0441\u0442\u0440\u0430\u0438\u0432\u0430\u043D\u0438\u0435 \u0441\u0446\u0435\u043D\u044B \u0432\u043E \u0432\u0440\u0435\u043C\u044F \u044D\u043A\u0441\u043F\u043E\u0440\u0442\u0430?",
  cannotRestoreFromImage: "\u0421\u0446\u0435\u043D\u0430 \u043D\u0435 \u043C\u043E\u0436\u0435\u0442 \u0431\u044B\u0442\u044C \u0432\u043E\u0441\u0441\u0442\u0430\u043D\u043E\u0432\u043B\u0435\u043D\u0430 \u0438\u0437 \u044D\u0442\u043E\u0433\u043E \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F",
  invalidSceneUrl: "\u041D\u0435\u0432\u043E\u0437\u043C\u043E\u0436\u043D\u043E \u0438\u043C\u043F\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0441\u0446\u0435\u043D\u0443 \u0441 \u043F\u0440\u0435\u0434\u043E\u0441\u0442\u0430\u0432\u043B\u0435\u043D\u043D\u043E\u0433\u043E URL. \u041D\u0435\u0432\u0435\u0440\u043D\u044B\u0439 \u0444\u043E\u0440\u043C\u0430\u0442, \u0438\u043B\u0438 \u043D\u0435 \u0441\u043E\u0434\u0435\u0440\u0436\u0438\u0442 \u0432\u0435\u0440\u043D\u044B\u0445 Excalidraw JSON \u0434\u0430\u043D\u043D\u044B\u0445.",
  resetLibrary: "\u042D\u0442\u043E \u043E\u0447\u0438\u0441\u0442\u0438\u0442 \u0432\u0430\u0448\u0443 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0443. \u0412\u044B \u0443\u0432\u0435\u0440\u0435\u043D\u044B?",
  removeItemsFromsLibrary: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C {{count}} \u043E\u0431\u044A\u0435\u043A\u0442(\u043E\u0432) \u0438\u0437 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438?",
  invalidEncryptionKey: "\u041A\u043B\u044E\u0447 \u0448\u0438\u0444\u0440\u043E\u0432\u0430\u043D\u0438\u044F \u0434\u043E\u043B\u0436\u0435\u043D \u0441\u043E\u0441\u0442\u043E\u044F\u0442\u044C \u0438\u0437 22 \u0441\u0438\u043C\u0432\u043E\u043B\u043E\u0432. \u041E\u0434\u043D\u043E\u0432\u0440\u0435\u043C\u0435\u043D\u043D\u043E\u0435 \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435 \u043E\u0442\u043A\u043B\u044E\u0447\u0435\u043D\u043E.",
  collabOfflineWarning: "\u041E\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u0435\u0442 \u0438\u043D\u0442\u0435\u0440\u043D\u0435\u0442-\u0441\u043E\u0435\u0434\u0438\u043D\u0435\u043D\u0438\u0435.\n\u0412\u0430\u0448\u0438 \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F \u043D\u0435 \u0431\u0443\u0434\u0443\u0442 \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u044B!"
};
var errors = {
  unsupportedFileType: "\u041D\u0435\u043F\u043E\u0434\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0435\u043C\u044B\u0439 \u0442\u0438\u043F \u0444\u0430\u0439\u043B\u0430.",
  imageInsertError: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0432\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435. \u041F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u043F\u043E\u0437\u0436\u0435...",
  fileTooBig: "\u041E\u0447\u0435\u043D\u044C \u0431\u043E\u043B\u044C\u0448\u043E\u0439 \u0444\u0430\u0439\u043B. \u041C\u0430\u043A\u0441\u0438\u043C\u0430\u043B\u044C\u043D\u043E \u0440\u0430\u0437\u0440\u0435\u0448\u0435\u043D\u043D\u044B\u0439 \u0440\u0430\u0437\u043C\u0435\u0440 {{maxSize}}.",
  svgImageInsertError: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0432\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435 SVG. \u0420\u0430\u0437\u043C\u0435\u0442\u043A\u0430 SVG \u0432\u044B\u0433\u043B\u044F\u0434\u0438\u0442 \u043D\u0435\u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0442\u0435\u043B\u044C\u043D\u043E\u0439.",
  failedToFetchImage: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u043F\u043E\u043B\u0443\u0447\u0438\u0442\u044C \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435.",
  invalidSVGString: "\u041D\u0435\u043A\u043E\u0440\u0440\u0435\u043A\u0442\u043D\u044B\u0439 SVG.",
  cannotResolveCollabServer: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u043F\u043E\u0434\u043A\u043B\u044E\u0447\u0438\u0442\u044C\u0441\u044F \u043A \u0441\u0435\u0440\u0432\u0435\u0440\u0443 \u0441\u043E\u0432\u043C\u0435\u0441\u0442\u043D\u043E\u0433\u043E \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F. \u041F\u0435\u0440\u0435\u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u0435 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0443 \u0438 \u043F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u0435 \u043F\u043E\u043F\u044B\u0442\u043A\u0443.",
  importLibraryError: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0443",
  collabSaveFailed: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0432 \u0431\u0430\u0437\u0443 \u0434\u0430\u043D\u043D\u044B\u0445. \u0415\u0441\u043B\u0438 \u043F\u0440\u043E\u0431\u043B\u0435\u043C\u0430 \u043F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u0441\u044F, \u043D\u0443\u0436\u043D\u043E \u0431\u0443\u0434\u0435\u0442 \u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0444\u0430\u0439\u043B \u043B\u043E\u043A\u0430\u043B\u044C\u043D\u043E, \u0447\u0442\u043E\u0431\u044B \u0431\u044B\u0442\u044C \u0443\u0432\u0435\u0440\u0435\u043D\u043D\u044B\u043C, \u0447\u0442\u043E \u0432\u044B \u043D\u0435 \u043F\u043E\u0442\u0435\u0440\u044F\u0435\u0442\u0435 \u0432\u0430\u0448\u0443 \u0440\u0430\u0431\u043E\u0442\u0443.",
  collabSaveFailed_sizeExceeded: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0432 \u0431\u0430\u0437\u0443 \u0434\u0430\u043D\u043D\u044B\u0445. \u041F\u043E\u0445\u043E\u0436\u0435, \u0447\u0442\u043E \u0445\u043E\u043B\u0441\u0442 \u0441\u043B\u0438\u0448\u043A\u043E\u043C \u0431\u043E\u043B\u044C\u0448\u043E\u0439. \u041D\u0443\u0436\u043D\u043E \u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0444\u0430\u0439\u043B \u043B\u043E\u043A\u0430\u043B\u044C\u043D\u043E, \u0447\u0442\u043E\u0431\u044B \u0431\u044B\u0442\u044C \u0443\u0432\u0435\u0440\u0435\u043D\u043D\u044B\u043C, \u0447\u0442\u043E \u0432\u044B \u043D\u0435 \u043F\u043E\u0442\u0435\u0440\u044F\u0435\u0442\u0435 \u0432\u0430\u0448\u0443 \u0440\u0430\u0431\u043E\u0442\u0443.",
  imageToolNotSupported: "\u0418\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F \u043E\u0442\u043A\u043B\u044E\u0447\u0435\u043D\u044B.",
  brave_measure_text_error: {
    line1: "\u041F\u043E\u0445\u043E\u0436\u0435, \u0432\u044B \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442\u0435 \u0431\u0440\u0430\u0443\u0437\u0435\u0440 Brave \u0441 \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u043D\u043E\u0439 \u043E\u043F\u0446\u0438\u0435\u0439 <bold>\u0410\u0433\u0440\u0435\u0441\u0441\u0438\u0432\u043D\u043E \u0431\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043E\u0442\u0441\u043B\u0435\u0436\u0438\u0432\u0430\u043D\u0438\u0435</bold>.",
    line2: "\u042D\u0442\u043E \u043C\u043E\u0436\u0435\u0442 \u043F\u0440\u0438\u0432\u0435\u0441\u0442\u0438 \u043A \u043F\u043E\u043B\u043E\u043C\u043A\u0435 <bold>\u0422\u0435\u043A\u0441\u0442\u043E\u0432\u044B\u0445 \u043E\u0431\u044A\u0435\u043A\u0442\u043E\u0432</bold> \u043D\u0430 \u0440\u0438\u0441\u0443\u043D\u043A\u0435.",
    line3: "\u041C\u044B \u043D\u0430\u0441\u0442\u043E\u044F\u0442\u0435\u043B\u044C\u043D\u043E \u0440\u0435\u043A\u043E\u043C\u0435\u043D\u0434\u0443\u0435\u043C \u043E\u0442\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u044D\u0442\u0443 \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0443. \u0414\u043B\u044F \u044D\u0442\u043E\u0433\u043E \u043D\u0443\u0436\u043D\u043E \u0432\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C <link>\u044D\u0442\u0438 \u0448\u0430\u0433\u0438</link>.",
    line4: "\u0415\u0441\u043B\u0438 \u043E\u0442\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0435 \u044D\u0442\u043E\u0439 \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u043D\u0435 \u0438\u0441\u043F\u0440\u0430\u0432\u0438\u0442 \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435 \u0442\u0435\u043A\u0441\u0442\u043E\u0432\u044B\u0445 \u043E\u0431\u044A\u0435\u043A\u0442\u043E\u0432, \u0441\u043E\u0437\u0434\u0430\u0439\u0442\u0435 <issueLink>issue</issueLink> \u043D\u0430 \u043D\u0430\u0448\u0435\u043C GitHub \u0438\u043B\u0438 \u043D\u0430\u043F\u0438\u0448\u0438\u0442\u0435 \u043D\u0430\u043C \u0432 <discordLink>Discord</discordLink>"
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "\u042D\u043B\u0435\u043C\u0435\u043D\u0442\u044B IFrame \u043D\u0435 \u043C\u043E\u0433\u0443\u0442 \u0431\u044B\u0442\u044C \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u044B \u0432 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0443.",
    image: ""
  },
  asyncPasteFailedOnRead: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0432\u0441\u0442\u0430\u0432\u0438\u0442\u044C (\u043D\u0435\u0432\u043E\u0437\u043C\u043E\u0436\u043D\u043E \u043F\u0440\u043E\u0447\u0438\u0442\u0430\u0442\u044C \u0438\u0437 \u0441\u0438\u0441\u0442\u0435\u043C\u043D\u043E\u0433\u043E \u0431\u0443\u0444\u0435\u0440\u0430 \u043E\u0431\u043C\u0435\u043D\u0430).",
  asyncPasteFailedOnParse: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0432\u0441\u0442\u0430\u0432\u0438\u0442\u044C.",
  copyToSystemClipboardFailed: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432 \u0431\u0443\u0444\u0435\u0440 \u043E\u0431\u043C\u0435\u043D\u0430."
};
var toolBar = {
  selection: "\u0412\u044B\u0434\u0435\u043B\u0435\u043D\u0438\u0435 \u043E\u0431\u043B\u0430\u0441\u0442\u0438",
  image: "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435",
  rectangle: "\u041F\u0440\u044F\u043C\u043E\u0443\u0433\u043E\u043B\u044C\u043D\u0438\u043A",
  diamond: "\u0420\u043E\u043C\u0431",
  ellipse: "\u042D\u043B\u043B\u0438\u043F\u0441",
  arrow: "C\u0442\u0440\u0435\u043B\u043A\u0430",
  line: "\u041B\u0438\u043D\u0438\u044F",
  freedraw: "\u0427\u0435\u0440\u0442\u0438\u0442\u044C",
  text: "\u0422\u0435\u043A\u0441\u0442",
  library: "\u0411\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430",
  lock: "\u0421\u043E\u0445\u0440\u0430\u043D\u044F\u0442\u044C \u0432\u044B\u0431\u0440\u0430\u043D\u043D\u044B\u0439 \u0438\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442 \u0430\u043A\u0442\u0438\u0432\u043D\u044B\u043C \u043F\u043E\u0441\u043B\u0435 \u0440\u0438\u0441\u043E\u0432\u0430\u043D\u0438\u044F",
  penMode: "\u0420\u0435\u0436\u0438\u043C \u043F\u0435\u0440\u0430 - \u043F\u0440\u0435\u0434\u043E\u0442\u0432\u0440\u0430\u0449\u0435\u043D\u0438\u0435 \u043A\u0430\u0441\u0430\u043D\u0438\u044F",
  link: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C/\u043E\u0431\u043D\u043E\u0432\u0438\u0442\u044C \u0441\u0441\u044B\u043B\u043A\u0443 \u0434\u043B\u044F \u0432\u044B\u0431\u0440\u0430\u043D\u043D\u043E\u0439 \u0444\u0438\u0433\u0443\u0440\u044B",
  eraser: "\u041B\u0430\u0441\u0442\u0438\u043A",
  frame: "",
  magicframe: "",
  embeddable: "",
  laser: "\u041B\u0430\u0437\u0435\u0440\u043D\u0430\u044F \u0443\u043A\u0430\u0437\u043A\u0430",
  hand: "\u0420\u0443\u043A\u0430 (\u043F\u0435\u0440\u0435\u043C\u0435\u0449\u0435\u043D\u0438\u0435 \u0445\u043E\u043B\u0441\u0442\u0430)",
  extraTools: "",
  mermaidToExcalidraw: "\u0418\u0437 Mermaid \u0432 Excalidraw",
  magicSettings: "\u041F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u044B AI"
};
var headings = {
  canvasActions: "\u041E\u043F\u0435\u0440\u0430\u0446\u0438\u0438 \u0445\u043E\u043B\u0441\u0442\u0430",
  selectedShapeActions: "\u041E\u043F\u0435\u0440\u0430\u0446\u0438\u0438 \u0432\u044B\u0431\u0440\u0430\u043D\u043D\u043E\u0439 \u0444\u0438\u0433\u0443\u0440\u044B",
  shapes: "\u0424\u0438\u0433\u0443\u0440\u044B"
};
var hints = {
  canvasPanning: '\u0427\u0442\u043E\u0431\u044B \u0434\u0432\u0438\u0433\u0430\u0442\u044C \u0445\u043E\u043B\u0441\u0442, \u0443\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0439\u0442\u0435 \u043A\u043E\u043B\u0435\u0441\u043E \u043C\u044B\u0448\u0438 \u0438\u043B\u0438 \u043F\u0440\u043E\u0431\u0435\u043B \u0432\u043E \u0432\u0440\u0435\u043C\u044F \u043F\u0435\u0440\u0435\u0442\u0430\u0441\u043A\u0438\u0432\u0430\u043D\u0438\u044F, \u0438\u043B\u0438 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0439\u0442\u0435 \u0438\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442 "\u0420\u0443\u043A\u0430"',
  linearElement: "\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u043D\u0430\u0447\u0430\u0442\u044C \u043D\u0435\u0441\u043A\u043E\u043B\u044C\u043A\u043E \u0442\u043E\u0447\u0435\u043A, \u043F\u0435\u0440\u0435\u0442\u0430\u0449\u0438\u0442\u0435 \u0434\u043B\u044F \u043E\u0434\u043D\u043E\u0439 \u043B\u0438\u043D\u0438\u0438",
  freeDraw: "\u041D\u0430\u0436\u043C\u0438\u0442\u0435 \u0438 \u043F\u0435\u0440\u0435\u0442\u0430\u0441\u043A\u0438\u0432\u0430\u0439\u0442\u0435, \u043E\u0442\u043F\u0443\u0441\u0442\u0438\u0442\u0435 \u043F\u043E \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043D\u0438\u0438",
  text: "\u0421\u043E\u0432\u0435\u0442: \u043F\u0440\u0438 \u0432\u044B\u0431\u0440\u0430\u043D\u043D\u043E\u043C \u0438\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442\u0435 \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u0438\u044F \u0434\u0432\u0430\u0436\u0434\u044B \u0449\u0451\u043B\u043A\u043D\u0438\u0442\u0435 \u0432 \u043B\u044E\u0431\u043E\u043C \u043C\u0435\u0441\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0442\u0435\u043A\u0441\u0442",
  embeddable: "",
  text_selected: "\u0414\u0432\u0430\u0436\u0434\u044B \u0449\u0435\u043B\u043A\u043D\u0438\u0442\u0435 \u043C\u044B\u0448\u044C\u044E \u0438\u043B\u0438 \u043D\u0430\u0436\u043C\u0438\u0442\u0435 ENTER, \u0447\u0442\u043E\u0431\u044B \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0442\u0435\u043A\u0441\u0442",
  text_editing: "\u041D\u0430\u0436\u043C\u0438\u0442\u0435 Escape \u043B\u0438\u0431\u043E Ctrl \u0438\u043B\u0438 Cmd + ENTER \u0434\u043B\u044F \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043D\u0438\u044F \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F",
  linearElementMulti: "\u041A\u043B\u0438\u043A\u043D\u0438\u0442\u0435 \u043D\u0430 \u043F\u043E\u0441\u043B\u0435\u0434\u043D\u0435\u0439 \u0442\u043E\u0447\u043A\u0435 \u0438\u043B\u0438 \u043D\u0430\u0436\u043C\u0438\u0442\u0435 Escape \u0438\u043B\u0438 Enter \u0447\u0442\u043E\u0431\u044B \u0437\u0430\u043A\u043E\u043D\u0447\u0438\u0442\u044C",
  lockAngle: "\u0412\u044B \u043C\u043E\u0436\u0435\u0442\u0435 \u043E\u0433\u0440\u0430\u043D\u0438\u0447\u0438\u0442\u044C \u0443\u0433\u043E\u043B \u0443\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u044F SHIFT",
  resize: "\u0412\u044B \u043C\u043E\u0436\u0435\u0442\u0435 \u043E\u0433\u0440\u0430\u043D\u0438\u0447\u0438\u0442\u044C \u043F\u0440\u043E\u043F\u043E\u0440\u0446\u0438\u0438, \u0443\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u044F SHIFT \u0432\u043E \u0432\u0440\u0435\u043C\u044F \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F \u0440\u0430\u0437\u043C\u0435\u0440\u043E\u0432,\n\u0443\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0439\u0442\u0435 ALT \u0447\u0442\u043E\u0431\u044B \u0438\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u0440\u0430\u0437\u043C\u0435\u0440 \u0438\u0437 \u0446\u0435\u043D\u0442\u0440\u0430",
  resizeImage: "\u0412\u044B \u043C\u043E\u0436\u0435\u0442\u0435 \u0441\u0432\u043E\u0431\u043E\u0434\u043D\u043E \u0438\u0437\u043C\u0435\u043D\u044F\u0442\u044C \u0440\u0430\u0437\u043C\u0435\u0440\u044B, \u0443\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u044F \u043A\u043D\u043E\u043F\u043A\u0443 SHIFT,\n\u0443\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0439\u0442\u0435 \u043A\u043D\u043E\u043F\u043A\u0443 ALT, \u0447\u0442\u043E\u0431\u044B \u0438\u0437\u043C\u0435\u043D\u044F\u0442\u044C \u0440\u0430\u0437\u043C\u0435\u0440 \u043E\u0442\u043D\u043E\u0441\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0446\u0435\u043D\u0442\u0440\u0430",
  rotate: "\u0412\u044B \u043C\u043E\u0436\u0435\u0442\u0435 \u043E\u0433\u0440\u0430\u043D\u0438\u0447\u0438\u0442\u044C \u0443\u0433\u043B\u044B, \u0443\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u044F SHIFT \u0432\u043E \u0432\u0440\u0435\u043C\u044F \u0432\u0440\u0430\u0449\u0435\u043D\u0438\u044F",
  lineEditor_info: "\u0423\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0439\u0442\u0435 CtrlOrCmd \u0438 \u0434\u0432\u0430\u0436\u0434\u044B \u043A\u043B\u0438\u043A\u043D\u0438\u0442\u0435 \u0438\u043B\u0438 \u043D\u0430\u0436\u043C\u0438\u0442\u0435 CtrlOrCmd + Enter \u0434\u043B\u044F \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F \u0442\u043E\u0447\u0435\u043A",
  lineEditor_pointSelected: "\u041D\u0430\u0436\u043C\u0438\u0442\u0435 Delete \u0434\u043B\u044F \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F \u0442\u043E\u0447\u043A\u0438 (\u0442\u043E\u0447\u0435\u043A),\nCtrl+D \u0438\u043B\u0438 Cmd+D \u0434\u043B\u044F \u0434\u0443\u0431\u043B\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F, \u043F\u0435\u0440\u0435\u0442\u0430\u0449\u0438\u0442\u0435 \u0434\u043B\u044F \u043F\u0435\u0440\u0435\u043C\u0435\u0449\u0435\u043D\u0438\u044F",
  lineEditor_nothingSelected: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0442\u043E\u0447\u043A\u0443 \u0434\u043B\u044F \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F (\u0443\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0439\u0442\u0435 SHIFT \u0432\u044B\u0431\u043E\u0440\u0430 \u043D\u0435\u0441\u043A\u043E\u043B\u044C\u043A\u0438\u0445 \u0442\u043E\u0447\u0435\u043A),\n\u0438\u043B\u0438 \u0443\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0439\u0442\u0435 Alt \u0438 \u043A\u043B\u0438\u043A\u043D\u0438\u0442\u0435 \u0434\u043B\u044F \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u0438\u044F \u043D\u043E\u0432\u044B\u0445 \u0442\u043E\u0447\u0435\u043A",
  placeImage: "\u0429\u0435\u043B\u043A\u043D\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0440\u0430\u0437\u043C\u0435\u0441\u0442\u0438\u0442\u044C \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435, \u0438\u043B\u0438 \u043D\u0430\u0436\u043C\u0438\u0442\u0435 \u0438 \u043F\u0435\u0440\u0435\u0442\u0430\u0449\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0443\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u044C \u0435\u0433\u043E \u0440\u0430\u0437\u043C\u0435\u0440 \u0432\u0440\u0443\u0447\u043D\u0443\u044E",
  publishLibrary: "\u041E\u043F\u0443\u0431\u043B\u0438\u043A\u043E\u0432\u0430\u0442\u044C \u0441\u0432\u043E\u044E \u0441\u043E\u0431\u0441\u0442\u0432\u0435\u043D\u043D\u0443\u044E \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0443",
  bindTextToElement: "\u041D\u0430\u0436\u043C\u0438\u0442\u0435 Enter \u0434\u043B\u044F \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u0438\u044F \u0442\u0435\u043A\u0441\u0442\u0430",
  deepBoxSelect: "\u0423\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0439\u0442\u0435 Ctrl \u0438\u043B\u0438 Cmd \u0434\u043B\u044F \u0433\u043B\u0443\u0431\u043E\u043A\u043E\u0433\u043E \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u0438\u044F, \u0447\u0442\u043E\u0431\u044B \u043F\u0440\u0435\u0434\u043E\u0442\u0432\u0440\u0430\u0442\u0438\u0442\u044C \u043F\u0435\u0440\u0435\u0442\u0430\u0441\u043A\u0438\u0432\u0430\u043D\u0438\u0435",
  eraserRevert: "\u0423\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0439\u0442\u0435 Alt, \u0447\u0442\u043E\u0431\u044B \u0432\u0435\u0440\u043D\u0443\u0442\u044C \u044D\u043B\u0435\u043C\u0435\u043D\u0442\u044B, \u043E\u0442\u043C\u0435\u0447\u0435\u043D\u043D\u044B\u0435 \u0434\u043B\u044F \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F",
  firefox_clipboard_write: '\u042D\u0442\u0430 \u0444\u0443\u043D\u043A\u0446\u0438\u044F \u043C\u043E\u0436\u0435\u0442 \u0431\u044B\u0442\u044C \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0430 \u043F\u0440\u0438 \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u0438 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F \u0444\u043B\u0430\u0433\u0430 "dom.events.asyncClipboard.clipboardItem" \u043D\u0430 "true". \u0427\u0442\u043E\u0431\u044B \u0438\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u0444\u043B\u0430\u0433\u0438 \u0431\u0440\u0430\u0443\u0437\u0435\u0440\u0430 \u0432 Firefox, \u043F\u043E\u0441\u0435\u0442\u0438\u0442\u0435 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0443 "about:config".',
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "\u041D\u0435 \u0443\u0434\u0430\u0435\u0442\u0441\u044F \u043E\u0442\u043E\u0431\u0440\u0430\u0437\u0438\u0442\u044C \u043F\u0440\u0435\u0434\u043F\u0440\u043E\u0441\u043C\u043E\u0442\u0440",
  canvasTooBig: "\u0421\u0446\u0435\u043D\u0430 \u0441\u043B\u0438\u0448\u043A\u043E\u043C \u0431\u043E\u043B\u044C\u0448\u0430\u044F.",
  canvasTooBigTip: "\u0421\u043E\u0432\u0435\u0442: \u043F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u0441\u0431\u043B\u0438\u0437\u0438\u0442\u044C \u044D\u043B\u0435\u043C\u0435\u043D\u0442\u044B \u0440\u0438\u0441\u0443\u043D\u043A\u0430."
};
var errorSplash = {
  headingMain: "\u0412\u043E\u0437\u043D\u0438\u043A\u043B\u0430 \u043E\u0448\u0438\u0431\u043A\u0430. \u041F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 <button>\u043F\u0435\u0440\u0435\u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0443.</button>",
  clearCanvasMessage: "\u0415\u0441\u043B\u0438 \u043F\u0435\u0440\u0435\u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u044B \u043D\u0435 \u043F\u043E\u043C\u043E\u0433\u043B\u0430, \u043F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 <button>\u043E\u0447\u0438\u0441\u0442\u0438\u0442\u044C \u0445\u043E\u043B\u0441\u0442.</button>",
  clearCanvasCaveat: " \u0422\u0435\u043A\u0443\u0449\u0430\u044F \u0440\u0430\u0431\u043E\u0442\u0430 \u0431\u0443\u0434\u0435\u0442 \u0443\u0442\u0435\u0440\u044F\u043D\u0430 ",
  trackedToSentry: "\u041E\u0448\u0438\u0431\u043A\u0430 \u0441 \u0438\u0434\u0435\u043D\u0442\u0438\u0444\u0438\u043A\u0430\u0442\u043E\u0440\u043E\u043C {{eventId}} \u043E\u0442\u0441\u043B\u0435\u0436\u0438\u0432\u0430\u0435\u0442\u0441\u044F \u0432 \u043D\u0430\u0448\u0435\u0439 \u0441\u0438\u0441\u0442\u0435\u043C\u0435.",
  openIssueMessage: "\u0414\u043B\u044F \u0431\u0435\u0437\u043E\u043F\u0430\u0441\u043D\u043E\u0441\u0442\u0438 \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043E \u0432\u0430\u0448\u0435\u0439 \u0441\u0446\u0435\u043D\u0435 \u043D\u0435 \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0430 \u0432 \u043E\u0448\u0438\u0431\u043A\u0443. \u0415\u0441\u043B\u0438 \u0432 \u0441\u0446\u0435\u043D\u0435 \u043D\u0435\u0442 \u043D\u0438\u0447\u0435\u0433\u043E \u043A\u043E\u043D\u0444\u0438\u0434\u0435\u043D\u0446\u0438\u0430\u043B\u044C\u043D\u043E\u0433\u043E, \u043F\u043E\u0436\u0430\u043B\u0443\u0439\u0441\u0442\u0430 \u0441\u043B\u0435\u0434\u0443\u0439\u0442\u0435 \u043D\u0430\u0448\u0438\u043C <button>\u0431\u0430\u0433 \u0442\u0440\u0435\u043A\u0435\u0440\u0435.</button> \u041F\u043E\u0436\u0430\u043B\u0443\u0439\u0441\u0442\u0430, \u043F\u0440\u0438\u043B\u043E\u0436\u0438\u0442\u0435 \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044E \u043D\u0438\u0436\u0435, \u0441\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0432 \u0438 \u0432\u0441\u0442\u0430\u0432\u0438\u0432 \u0435\u0451, \u0432 issue GitHub.",
  sceneContent: "\u0421\u043E\u0434\u0435\u0440\u0436\u0430\u043D\u0438\u0435 \u0441\u0446\u0435\u043D\u044B:"
};
var roomDialog = {
  desc_intro: "\u0412\u044B \u043C\u043E\u0436\u0435\u0442\u0435 \u043F\u0440\u0438\u0433\u043B\u0430\u0441\u0438\u0442\u044C \u043B\u044E\u0434\u0435\u0439 \u0432 \u0442\u0435\u043A\u0443\u0449\u0443\u044E \u0441\u0446\u0435\u043D\u0443 \u0434\u043B\u044F \u0441\u043E\u0432\u043C\u0435\u0441\u0442\u043D\u043E\u0439 \u0440\u0430\u0431\u043E\u0442\u044B.",
  desc_privacy: "\u041D\u0435 \u0431\u0435\u0441\u043F\u043E\u043A\u043E\u0439\u0442\u0435\u0441\u044C \u2014 \u0432\u043E \u0432\u0440\u0435\u043C\u044F \u0441\u0435\u0430\u043D\u0441\u0430 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442\u0441\u044F \u0441\u043A\u0432\u043E\u0437\u043D\u043E\u0435 \u0448\u0438\u0444\u0440\u043E\u0432\u0430\u043D\u0438\u0435. \u0412\u0441\u0451, \u0447\u0442\u043E \u0432\u044B \u043D\u0430\u0440\u0438\u0441\u0443\u0435\u0442\u0435, \u043E\u0441\u0442\u0430\u043D\u0435\u0442\u0441\u044F \u043A\u043E\u043D\u0444\u0438\u0434\u0435\u043D\u0446\u0438\u0430\u043B\u044C\u043D\u044B\u043C \u0438 \u043D\u0435 \u0431\u0443\u0434\u0435\u0442 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u043E \u0434\u0430\u0436\u0435 \u043D\u0430\u0448\u0435\u043C\u0443 \u0441\u0435\u0440\u0432\u0435\u0440\u0443.",
  button_startSession: "\u041D\u0430\u0447\u0430\u0442\u044C \u0441\u0435\u0430\u043D\u0441",
  button_stopSession: "\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u044C \u0441\u0435\u0430\u043D\u0441",
  desc_inProgressIntro: "\u0421\u0435\u0430\u043D\u0441 \u0441\u043E\u0432\u043C\u0435\u0441\u0442\u043D\u043E\u0439 \u0440\u0430\u0431\u043E\u0442\u044B \u0437\u0430\u043F\u0443\u0449\u0435\u043D.",
  desc_shareLink: "\u041F\u043E\u0434\u0435\u043B\u0438\u0442\u0435\u0441\u044C \u044D\u0442\u043E\u0439 \u0441\u0441\u044B\u043B\u043A\u043E\u0439 \u0441\u043E \u0432\u0441\u0435\u043C\u0438 \u0443\u0447\u0430\u0441\u0442\u043D\u0438\u043A\u0430\u043C\u0438:",
  desc_exitSession: "\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0432 \u0441\u0435\u0430\u043D\u0441, \u0432\u044B \u0432\u044B\u0439\u0434\u0435\u0442\u0435 \u0438\u0437 \u043A\u043E\u043C\u043D\u0430\u0442\u044B, \u043D\u043E \u0441\u043C\u043E\u0436\u0435\u0442\u0435 \u043F\u0440\u043E\u0434\u043E\u043B\u0436\u0438\u0442\u044C \u0440\u0430\u0431\u043E\u0442\u0430\u0442\u044C \u0441 \u0434\u043E\u043A\u0443\u043C\u0435\u043D\u0442\u043E\u043C \u043B\u043E\u043A\u0430\u043B\u044C\u043D\u043E. \u042D\u0442\u043E \u043D\u0435 \u043F\u043E\u0432\u043B\u0438\u044F\u0435\u0442 \u043D\u0430 \u0440\u0430\u0431\u043E\u0442\u0443 \u0434\u0440\u0443\u0433\u0438\u0445 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u0435\u0439 \u2014 \u043E\u043D\u0438 \u0441\u043C\u043E\u0433\u0443\u0442 \u043F\u0440\u043E\u0434\u043E\u043B\u0436\u0438\u0442\u044C \u0441\u043E\u0432\u043C\u0435\u0441\u0442\u043D\u0443\u044E \u0440\u0430\u0431\u043E\u0442\u0443 \u0441 \u0438\u0445 \u0432\u0435\u0440\u0441\u0438\u0435\u0439 \u0434\u043E\u043A\u0443\u043C\u0435\u043D\u0442\u0430.",
  shareTitle: "\u041F\u0440\u0438\u0441\u043E\u0435\u0434\u0438\u043D\u0438\u0442\u044C\u0441\u044F \u043A \u0430\u043A\u0442\u0438\u0432\u043D\u043E\u0439 \u0441\u043E\u0432\u043C\u0435\u0441\u0442\u043D\u043E\u0439 \u0441\u0435\u0441\u0441\u0438\u0438 \u043D\u0430 Excalidraw"
};
var errorDialog = {
  title: "\u041E\u0448\u0438\u0431\u043A\u0430"
};
var exportDialog = {
  disk_title: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u043D\u0430 \u0434\u0438\u0441\u043A",
  disk_details: "\u042D\u043A\u0441\u043F\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0434\u0430\u043D\u043D\u044B\u0435 \u0441\u0446\u0435\u043D\u044B \u0432 \u0444\u0430\u0439\u043B, \u0438\u0437 \u043A\u043E\u0442\u043E\u0440\u043E\u0433\u043E \u043C\u043E\u0436\u043D\u043E \u0438\u043C\u043F\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043F\u043E\u0437\u0436\u0435.",
  disk_button: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0432 \u0444\u0430\u0439\u043B",
  link_title: "\u041F\u043E\u0434\u0435\u043B\u0438\u0442\u0435\u0441\u044C \u0441\u0441\u044B\u043B\u043A\u043E\u0439",
  link_details: "\u042D\u043A\u0441\u043F\u043E\u0440\u0442 \u0441\u0441\u044B\u043B\u043A\u0438 \u0442\u043E\u043B\u044C\u043A\u043E \u0434\u043B\u044F \u0447\u0442\u0435\u043D\u0438\u044F.",
  link_button: "\u042D\u043A\u0441\u043F\u043E\u0440\u0442 \u0432 \u0441\u0441\u044B\u043B\u043A\u0443",
  excalidrawplus_description: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u0435 \u0441\u0446\u0435\u043D\u0443 \u0432 \u0432\u0430\u0448\u0435 \u0440\u0430\u0431\u043E\u0447\u0435\u0435 \u043F\u0440\u043E\u0441\u0442\u0440\u0430\u043D\u0441\u0442\u0432\u043E Excalidraw+.",
  excalidrawplus_button: "\u042D\u043A\u0441\u043F\u043E\u0440\u0442",
  excalidrawplus_exportError: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u044D\u043A\u0441\u043F\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432 Excalidraw+ \u043D\u0430 \u0434\u0430\u043D\u043D\u044B\u0439 \u043C\u043E\u043C\u0435\u043D\u0442..."
};
var helpDialog = {
  blog: "\u041F\u0440\u043E\u0447\u0438\u0442\u0430\u0439\u0442\u0435 \u043D\u0430\u0448 \u0431\u043B\u043E\u0433",
  click: "\u043D\u0430\u0436\u0430\u0442\u044C",
  deepSelect: "\u0413\u043B\u0443\u0431\u043E\u043A\u043E\u0435 \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u0438\u0435",
  deepBoxSelect: "\u0413\u043B\u0443\u0431\u043E\u043A\u043E\u0435 \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u0438\u0435 \u0440\u0430\u043C\u043A\u043E\u0439, \u0438 \u043F\u0440\u0435\u0434\u043E\u0442\u0432\u0440\u0430\u0449\u0435\u043D\u0438\u0435 \u043F\u0435\u0440\u0435\u0442\u0430\u0441\u043A\u0438\u0432\u0430\u043D\u0438\u044F",
  curvedArrow: "\u0418\u0437\u043E\u0433\u043D\u0443\u0442\u0430\u044F \u0441\u0442\u0440\u0435\u043B\u043A\u0430",
  curvedLine: "\u0418\u0437\u043E\u0433\u043D\u0443\u0442\u0430\u044F \u043B\u0438\u043D\u0438\u044F",
  documentation: "\u0414\u043E\u043A\u0443\u043C\u0435\u043D\u0442\u0430\u0446\u0438\u044F",
  doubleClick: "\u0434\u0432\u043E\u0439\u043D\u043E\u0439 \u043A\u043B\u0438\u043A",
  drag: "\u043F\u0435\u0440\u0435\u0442\u0430\u0449\u0438\u0442\u044C",
  editor: "\u0420\u0435\u0434\u0430\u043A\u0442\u043E\u0440",
  editLineArrowPoints: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043A\u043E\u043D\u0446\u044B \u043B\u0438\u043D\u0438\u0439/\u0441\u0442\u0440\u0435\u043B\u043E\u043A",
  editText: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0442\u0435\u043A\u0441\u0442 / \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043C\u0435\u0442\u043A\u0443",
  github: "\u041D\u0430\u0448\u043B\u0438 \u043F\u0440\u043E\u0431\u043B\u0435\u043C\u0443? \u041E\u0442\u043F\u0440\u0430\u0432\u044C\u0442\u0435",
  howto: "\u0421\u043B\u0435\u0434\u0443\u0439\u0442\u0435 \u043D\u0430\u0448\u0438\u043C \u0438\u043D\u0441\u0442\u0440\u0443\u043A\u0446\u0438\u044F\u043C",
  or: "\u0438\u043B\u0438",
  preventBinding: "\u041F\u0440\u0435\u0434\u043E\u0442\u0432\u0440\u0430\u0449\u0430\u0442\u044C \u043F\u0440\u0438\u0432\u044F\u0437\u043A\u0443 \u0441\u0442\u0440\u0435\u043B\u043E\u043A",
  tools: "\u0418\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442\u044B",
  shortcuts: "\u0413\u043E\u0440\u044F\u0447\u0438\u0435 \u043A\u043B\u0430\u0432\u0438\u0448\u0438",
  textFinish: "\u0417\u0430\u043A\u043E\u043D\u0447\u0438\u0442\u044C \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435 (\u0442\u0435\u043A\u0441\u0442\u043E\u0432\u044B\u0439 \u0440\u0435\u0434\u0430\u043A\u0442\u043E\u0440)",
  textNewLine: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043D\u043E\u0432\u0443\u044E \u0441\u0442\u0440\u043E\u043A\u0443 (\u0442\u0435\u043A\u0441\u0442\u043E\u0432\u044B\u0439 \u0440\u0435\u0434\u0430\u043A\u0442\u043E\u0440)",
  title: "\u041F\u043E\u043C\u043E\u0449\u044C",
  view: "\u041F\u0440\u043E\u0441\u043C\u043E\u0442\u0440",
  zoomToFit: "\u041E\u0442\u043C\u0430\u0441\u0442\u0448\u0442\u0430\u0431\u0438\u0440\u043E\u0432\u0430\u0442\u044C, \u0447\u0442\u043E\u0431\u044B \u043F\u043E\u043C\u0435\u0441\u0442\u0438\u043B\u0438\u0441\u044C \u0432\u0441\u0435 \u044D\u043B\u0435\u043C\u0435\u043D\u0442\u044B",
  zoomToSelection: "\u0423\u0432\u0435\u043B\u0438\u0447\u0438\u0442\u044C \u0434\u043E \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u043E\u0433\u043E",
  toggleElementLock: "\u0417\u0430\u0431\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u0430\u0442\u044C/\u0440\u0430\u0437\u0431\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u0438\u0435",
  movePageUpDown: "\u0421\u0434\u0432\u0438\u043D\u0443\u0442\u044C \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0443 \u0432\u0432\u0435\u0440\u0445/\u0432\u043D\u0438\u0437",
  movePageLeftRight: "\u0421\u0434\u0432\u0438\u043D\u0443\u0442\u044C \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0443 \u0432\u043F\u0440\u0430\u0432\u043E/\u0432\u043B\u0435\u0432\u043E"
};
var clearCanvasDialog = {
  title: "\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C \u0445\u043E\u043B\u0441\u0442"
};
var publishDialog = {
  title: "\u041E\u043F\u0443\u0431\u043B\u0438\u043A\u043E\u0432\u0430\u0442\u044C \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0443",
  itemName: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u043E\u0431\u044A\u0435\u043A\u0442\u0430",
  authorName: "\u0418\u043C\u044F \u0430\u0432\u0442\u043E\u0440\u0430",
  githubUsername: "\u0418\u043C\u044F \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044F GitHub",
  twitterUsername: "\u0418\u043C\u044F \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044F \u0432 Twitter",
  libraryName: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438",
  libraryDesc: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438",
  website: "\u0412\u0435\u0431-\u0441\u0430\u0439\u0442",
  placeholder: {
    authorName: "\u0412\u0430\u0448\u0435 \u0438\u043C\u044F \u0438\u043B\u0438 \u0438\u043C\u044F \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044F",
    libraryName: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0432\u0430\u0448\u0435\u0439 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438",
    libraryDesc: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u0432\u0430\u0448\u0435\u0439 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438, \u043A\u043E\u0442\u043E\u0440\u043E\u0435 \u043F\u043E\u043C\u043E\u0436\u0435\u0442 \u043B\u044E\u0434\u044F\u043C \u043F\u043E\u043D\u044F\u0442\u044C \u0435\u0451 \u043D\u0430\u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435",
    githubHandle: "\u0418\u043C\u044F \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044F GitHub (\u043D\u0435\u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u043E), \u0447\u0442\u043E\u0431\u044B \u0432\u044B \u0441\u043C\u043E\u0433\u043B\u0438 \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0443 \u043F\u043E\u0441\u043B\u0435 \u0435\u0451 \u043E\u0442\u043F\u0440\u0430\u0432\u043A\u0438 \u043D\u0430 \u043F\u0440\u043E\u0432\u0435\u0440\u043A\u0443",
    twitterHandle: "\u0418\u043C\u044F \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044F \u0432 Twitter (\u043D\u0435\u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u043E), \u0447\u0442\u043E\u0431\u044B \u043C\u044B \u0437\u043D\u0430\u043B\u0438, \u043A\u043E\u0433\u043E \u0443\u043F\u043E\u043C\u044F\u043D\u0443\u0442\u044C \u043F\u0440\u0438 \u043F\u0440\u043E\u0434\u0432\u0438\u0436\u0435\u043D\u0438\u0438 \u0432 Twitter",
    website: "\u0421\u0441\u044B\u043B\u043A\u0430 \u043D\u0430 \u0432\u0430\u0448 \u043B\u0438\u0447\u043D\u044B\u0439 \u0438\u043B\u0438 \u043A\u0430\u043A\u043E\u0439-\u0442\u043E \u0434\u0440\u0443\u0433\u043E\u0439 \u0441\u0430\u0439\u0442 (\u043D\u0435\u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u043E)"
  },
  errors: {
    required: "\u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u043E",
    website: "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0434\u043E\u043F\u0443\u0441\u0442\u0438\u043C\u044B\u0439 URL-\u0430\u0434\u0440\u0435\u0441"
  },
  noteDescription: "\u041E\u0442\u043F\u0440\u0430\u0432\u0438\u0442\u044C \u0432\u0430\u0448\u0443 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0443 \u0434\u043B\u044F \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u044F \u0432 <link>\u0445\u0440\u0430\u043D\u0438\u043B\u0438\u0449\u0435 \u043F\u0443\u0431\u043B\u0438\u0447\u043D\u044B\u0445 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A</link>, \u0447\u0442\u043E\u0431\u044B \u0434\u0440\u0443\u0433\u0438\u0435 \u043B\u044E\u0434\u0438 \u043C\u043E\u0433\u043B\u0438 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u043E\u0431\u044A\u0435\u043A\u0442\u044B \u0438\u0437 \u0432\u0430\u0448\u0435\u0439 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438 \u0432 \u0441\u0432\u043E\u0438\u0445 \u0440\u0438\u0441\u0443\u043D\u043A\u0430\u0445.",
  noteGuidelines: "\u0411\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430 \u0434\u043E\u043B\u0436\u043D\u0430 \u0431\u044B\u0442\u044C \u043F\u043E\u0434\u0442\u0432\u0435\u0440\u0436\u0434\u0435\u043D\u0430 \u0432\u0440\u0443\u0447\u043D\u0443\u044E. \u041F\u043E\u0436\u0430\u043B\u0443\u0439\u0441\u0442\u0430, \u043F\u0440\u043E\u0447\u0442\u0438\u0442\u0435 <link>\u0440\u0435\u043A\u043E\u043C\u0435\u043D\u0434\u0430\u0446\u0438\u0438</link> \u043F\u0435\u0440\u0435\u0434 \u043E\u0442\u043F\u0440\u0430\u0432\u043A\u043E\u0439. \u0412\u0430\u043C \u043F\u043E\u043D\u0430\u0434\u043E\u0431\u0438\u0442\u0441\u044F \u0443\u0447\u0435\u0442\u043D\u0430\u044F \u0437\u0430\u043F\u0438\u0441\u044C GitHub, \u0447\u0442\u043E\u0431\u044B \u043E\u0431\u0449\u0430\u0442\u044C\u0441\u044F \u0438 \u0432\u043D\u043E\u0441\u0438\u0442\u044C \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F \u043F\u0440\u0438 \u043D\u0435\u043E\u0431\u0445\u043E\u0434\u0438\u043C\u043E\u0441\u0442\u0438, \u043D\u043E \u044D\u0442\u043E \u043D\u0435 \u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u043E.",
  noteLicense: "\u0412\u044B\u043F\u043E\u043B\u043D\u044F\u044F \u043E\u0442\u043F\u0440\u0430\u0432\u043A\u0443, \u0432\u044B \u0441\u043E\u0433\u043B\u0430\u0448\u0430\u0435\u0442\u0435\u0441\u044C \u0441 \u0442\u0435\u043C, \u0447\u0442\u043E \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430 \u0431\u0443\u0434\u0435\u0442 \u043E\u043F\u0443\u0431\u043B\u0438\u043A\u043E\u0432\u0430\u043D\u0430 \u043F\u043E\u0434 <link>\u043B\u0438\u0446\u0435\u043D\u0437\u0438\u0435\u0439 MIT, </link>, \u0447\u0442\u043E, \u0432\u043A\u0440\u0430\u0442\u0446\u0435, \u043E\u0437\u043D\u0430\u0447\u0430\u0435\u0442, \u0447\u0442\u043E \u043A\u0430\u0436\u0434\u044B\u0439 \u043C\u043E\u0436\u0435\u0442 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u0435\u0451 \u0431\u0435\u0437 \u043E\u0433\u0440\u0430\u043D\u0438\u0447\u0435\u043D\u0438\u0439.",
  noteItems: "\u041A\u0430\u0436\u0434\u044B\u0439 \u043E\u0431\u044A\u0435\u043A\u0442 \u0432 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0435 \u0434\u043E\u043B\u0436\u0435\u043D \u0438\u043C\u0435\u0442\u044C \u0441\u0432\u043E\u0435 \u0441\u043E\u0431\u0441\u0442\u0432\u0435\u043D\u043D\u043E\u0435 \u0438\u043C\u044F, \u0447\u0442\u043E\u0431\u044B \u043F\u043E \u043D\u0435\u043C\u0443 \u043C\u043E\u0436\u043D\u043E \u0431\u044B\u043B\u043E \u0444\u0438\u043B\u044C\u0442\u0440\u043E\u0432\u0430\u0442\u044C. \u0421\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0435 \u043E\u0431\u044A\u0435\u043A\u0442\u044B \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438 \u0431\u0443\u0434\u0443\u0442 \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u044B:",
  atleastOneLibItem: "\u041F\u043E\u0436\u0430\u043B\u0443\u0439\u0441\u0442\u0430, \u0432\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0445\u043E\u0442\u044F \u0431\u044B \u043E\u0434\u0438\u043D \u043E\u0431\u044A\u0435\u043A\u0442 \u0432 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0435, \u0447\u0442\u043E\u0431\u044B \u043D\u0430\u0447\u0430\u0442\u044C",
  republishWarning: "\u041F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u0435: \u043D\u0435\u043A\u043E\u0442\u043E\u0440\u044B\u0435 \u0438\u0437 \u0432\u044B\u0431\u0440\u0430\u043D\u043D\u044B\u0445 \u044D\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u0432 \u043F\u043E\u043C\u0435\u0447\u0435\u043D\u044B \u043A\u0430\u043A \u0443\u0436\u0435 \u043E\u043F\u0443\u0431\u043B\u0438\u043A\u043E\u0432\u0430\u043D\u043D\u044B\u0435/\u043E\u0442\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u043D\u044B\u0435. \u0412\u044B \u0434\u043E\u043B\u0436\u043D\u044B \u043F\u043E\u0432\u0442\u043E\u0440\u043D\u043E \u043E\u0442\u043F\u0440\u0430\u0432\u0438\u0442\u044C \u044D\u043B\u0435\u043C\u0435\u043D\u0442\u044B \u0442\u043E\u043B\u044C\u043A\u043E \u043F\u0440\u0438 \u043E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u0438\u0438 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0435\u0439 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438 \u0438\u043B\u0438 \u0441\u0434\u0430\u0447\u0435 \u0440\u0430\u0431\u043E\u0442\u044B."
};
var publishSuccessDialog = {
  title: "\u0411\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430 \u043E\u0442\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0430",
  content: "\u0411\u043B\u0430\u0433\u043E\u0434\u0430\u0440\u0438\u043C \u0432\u0430\u0441, {{authorName}}. \u0412\u0430\u0448\u0430 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430 \u0431\u044B\u043B\u0430 \u043E\u0442\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0430 \u043D\u0430 \u043F\u0440\u043E\u0432\u0435\u0440\u043A\u0443. \u0412\u044B \u043C\u043E\u0436\u0435\u0442\u0435 \u043E\u0442\u0441\u043B\u0435\u0436\u0438\u0432\u0430\u0442\u044C \u0441\u0442\u0430\u0442\u0443\u0441<link>\u0437\u0434\u0435\u0441\u044C</link>"
};
var confirmDialog = {
  resetLibrary: "\u0421\u0431\u0440\u043E\u0441 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438",
  removeItemsFromLib: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u0432\u044B\u0431\u0440\u0430\u043D\u043D\u044B\u0435 \u043E\u0431\u044A\u0435\u043A\u0442\u044B \u0438\u0437 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0438"
};
var imageExportDialog = {
  header: "\u042D\u043A\u0441\u043F\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435",
  label: {
    withBackground: "\u0424\u043E\u043D",
    onlySelected: "\u0422\u043E\u043B\u044C\u043A\u043E \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u043E\u0435",
    darkMode: "\u0422\u0435\u043C\u043D\u0430\u044F \u0442\u0435\u043C\u0430",
    embedScene: "\u0412\u0441\u0442\u0440\u043E\u0438\u0442\u044C \u0441\u0446\u0435\u043D\u0443",
    scale: "\u041C\u0430\u0441\u0448\u0442\u0430\u0431",
    padding: "\u041E\u0442\u0441\u0442\u0443\u043F"
  },
  tooltip: {
    embedScene: "\u0421\u0446\u0435\u043D\u0430 \u0431\u0443\u0434\u0435\u0442 \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0430 \u0432 PNG/SVG \u0444\u0430\u0439\u043B \u0442\u0430\u043A, \u0447\u0442\u043E\u0431\u044B \u0432\u0441\u044E \u0441\u0446\u0435\u043D\u0443 \u043C\u043E\u0436\u043D\u043E \u0431\u0443\u0434\u0435\u0442 \u0432\u043E\u0441\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u044C \u0438\u0437 \u044D\u0442\u043E\u0433\u043E \u0444\u0430\u0439\u043B\u0430. \u042D\u0442\u043E \u0443\u0432\u0435\u043B\u0438\u0447\u0438\u0442 \u0440\u0430\u0437\u043C\u0435\u0440 \u0444\u0430\u0439\u043B\u0430."
  },
  title: {
    exportToPng: "\u042D\u043A\u0441\u043F\u043E\u0440\u0442 \u0432 PNG",
    exportToSvg: "\u042D\u043A\u0441\u043F\u043E\u0440\u0442 \u0432 SVG",
    copyPngToClipboard: "\u0421\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C PNG \u0432 \u0431\u0443\u0444\u0435\u0440 \u043E\u0431\u043C\u0435\u043D\u0430"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "\u0421\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432 \u0431\u0443\u0444\u0435\u0440 \u043E\u0431\u043C\u0435\u043D\u0430"
  }
};
var encrypted = {
  tooltip: "\u0412\u0430\u0448\u0438 \u0434\u0430\u043D\u043D\u044B\u0435 \u0437\u0430\u0449\u0438\u0449\u0435\u043D\u044B \u0441\u043A\u0432\u043E\u0437\u043D\u044B\u043C (End-to-end) \u0448\u0438\u0444\u0440\u043E\u0432\u0430\u043D\u0438\u0435\u043C. \u0421\u0435\u0440\u0432\u0435\u0440\u044B Excalidraw \u043D\u0438\u043A\u043E\u0433\u0434\u0430 \u043D\u0435 \u043F\u043E\u043B\u0443\u0447\u0430\u0442 \u0434\u043E\u0441\u0442\u0443\u043F \u043A \u043D\u0438\u043C.",
  link: "\u0417\u0430\u043F\u0438\u0441\u044C \u0431\u043B\u043E\u0433\u0430 \u043E \u0441\u043A\u0432\u043E\u0437\u043D\u043E\u043C \u0448\u0438\u0444\u0440\u043E\u0432\u0430\u043D\u0438\u0438 \u0432 Excalidraw"
};
var stats = {
  angle: "\u0423\u0433\u043E\u043B",
  element: "\u042D\u043B\u0435\u043C\u0435\u043D\u0442",
  elements: "\u042D\u043B\u0435\u043C\u0435\u043D\u0442\u044B",
  height: "\u0412\u044B\u0441\u043E\u0442\u0430",
  scene: "\u0421\u0446\u0435\u043D\u044B",
  selected: "\u0412\u044B\u0431\u0440\u0430\u043D",
  storage: "\u0425\u0440\u0430\u043D\u0438\u043B\u0438\u0449\u0435",
  title: "\u0421\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043A\u0430 \u0434\u043B\u044F \u0431\u043E\u0442\u0430\u043D\u0438\u043A\u043E\u0432",
  total: "\u0412\u0441\u0435\u0433\u043E",
  version: "\u0412\u0435\u0440\u0441\u0438\u044F",
  versionCopy: "\u041A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C",
  versionNotAvailable: "\u0412\u0435\u0440\u0441\u0438\u044F \u043D\u0435 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u0430",
  width: "\u0428\u0438\u0440\u0438\u043D\u0430"
};
var toast = {
  addedToLibrary: "\u0414\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u043E \u0432 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0443",
  copyStyles: "\u0421\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u043D\u044B \u0441\u0442\u0438\u043B\u0438.",
  copyToClipboard: "\u0421\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u043D\u043E \u0432 \u0431\u0443\u0444\u0435\u0440 \u043E\u0431\u043C\u0435\u043D\u0430.",
  copyToClipboardAsPng: "{{exportSelection}} \u0441\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u043D\u043E \u043A\u0430\u043A PNG ({{exportColorScheme}})",
  fileSaved: "\u0424\u0430\u0439\u043B \u0441\u043E\u0445\u0440\u0430\u043D\u0451\u043D.",
  fileSavedToFilename: "\u0421\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u043E \u0432 {filename}",
  canvas: "\u0445\u043E\u043B\u0441\u0442",
  selection: "\u0432\u044B\u0434\u0435\u043B\u0435\u043D\u0438\u0435",
  pasteAsSingleElement: "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0439\u0442\u0435 {{shortcut}}, \u0447\u0442\u043E\u0431\u044B \u0432\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u043E\u0434\u0438\u043D \u043E\u0431\u044A\u0435\u043A\u0442,\n\u0438\u043B\u0438 \u0432\u0441\u0442\u0430\u0432\u044C\u0442\u0435 \u0432 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0439 \u0442\u0435\u043A\u0441\u0442\u043E\u0432\u044B\u0439 \u0440\u0435\u0434\u0430\u043A\u0442\u043E\u0440",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "\u041F\u0440\u043E\u0437\u0440\u0430\u0447\u043D\u044B\u0439",
  black: "\u0427\u0451\u0440\u043D\u044B\u0439",
  white: "\u0411\u0435\u043B\u044B\u0439",
  red: "\u041A\u0440\u0430\u0441\u043D\u044B\u0439",
  pink: "\u0420\u043E\u0437\u043E\u0432\u044B\u0439",
  grape: "\u0412\u0438\u043D\u043E\u0433\u0440\u0430\u0434\u043D\u044B\u0439",
  violet: "\u0424\u0438\u043E\u043B\u0435\u0442\u043E\u0432\u044B\u0439",
  gray: "\u0421\u0435\u0440\u044B\u0439",
  blue: "\u0421\u0438\u043D\u0438\u0439",
  cyan: "\u0413\u043E\u043B\u0443\u0431\u043E\u0439",
  teal: "\u0411\u0438\u0440\u044E\u0437\u043E\u0432\u044B\u0439",
  green: "\u0417\u0435\u043B\u0451\u043D\u044B\u0439",
  yellow: "\u0416\u0451\u043B\u0442\u044B\u0439",
  orange: "\u041E\u0440\u0430\u043D\u0436\u0435\u0432\u044B\u0439",
  bronze: "\u0411\u0440\u043E\u043D\u0437\u043E\u0432\u044B\u0439"
};
var welcomeScreen = {
  app: {
    center_heading: "\u0412\u0441\u0435 \u0432\u0430\u0448\u0438 \u0434\u0430\u043D\u043D\u044B\u0435 \u0441\u043E\u0445\u0440\u0430\u043D\u044F\u044E\u0442\u0441\u044F \u043B\u043E\u043A\u0430\u043B\u044C\u043D\u043E \u0432 \u0432\u0430\u0448\u0435\u043C \u0431\u0440\u0430\u0443\u0437\u0435\u0440\u0435.",
    center_heading_plus: "\u0425\u043E\u0442\u0438\u0442\u0435 \u043F\u0435\u0440\u0435\u0439\u0442\u0438 \u043D\u0430 Excalidraw+?",
    menuHint: "\u042D\u043A\u0441\u043F\u043E\u0440\u0442, \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438, \u044F\u0437\u044B\u043A\u0438, ..."
  },
  defaults: {
    menuHint: "\u042D\u043A\u0441\u043F\u043E\u0440\u0442, \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0438 \u0434\u0440\u0443\u0433\u043E\u0435...",
    center_heading: "\u0414\u0438\u0430\u0433\u0440\u0430\u043C\u043C\u044B. \u041F\u0440\u043E\u0441\u0442\u043E.",
    toolbarHint: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0438\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442 \u0438 \u043D\u0430\u0447\u043D\u0438\u0442\u0435 \u0440\u0438\u0441\u043E\u0432\u0430\u0442\u044C!",
    helpHint: "\u0421\u043E\u0447\u0435\u0442\u0430\u043D\u0438\u044F \u043A\u043B\u0430\u0432\u0438\u0448 \u0438 \u043F\u043E\u043C\u043E\u0449\u044C"
  }
};
var colorPicker = {
  mostUsedCustomColors: "\u0427\u0430\u0441\u0442\u043E \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u043C\u044B\u0435 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u0438\u0435 \u0446\u0432\u0435\u0442\u0430",
  colors: "\u0426\u0432\u0435\u0442\u0430",
  shades: "\u041E\u0442\u0442\u0435\u043D\u043A\u0438",
  hexCode: "\u0428\u0435\u0441\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u0435\u0440\u0438\u0447\u043D\u044B\u0439 \u043A\u043E\u0434",
  noShades: "\u041D\u0435\u0442 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u044B\u0445 \u043E\u0442\u0442\u0435\u043D\u043A\u043E\u0432 \u0434\u043B\u044F \u044D\u0442\u043E\u0433\u043E \u0446\u0432\u0435\u0442\u0430"
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "\u042D\u043A\u0441\u043F\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043A\u0430\u043A \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435",
      button: "\u042D\u043A\u0441\u043F\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043A\u0430\u043A \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435",
      description: ""
    },
    saveToDisk: {
      title: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u043D\u0430 \u0434\u0438\u0441\u043A",
      button: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u043D\u0430 \u0434\u0438\u0441\u043A",
      description: ""
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "\u042D\u043A\u0441\u043F\u043E\u0440\u0442 \u0432 Excalidraw+",
      description: ""
    }
  },
  modal: {
    loadFromFile: {
      title: "\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0438\u0437 \u0444\u0430\u0439\u043B\u0430",
      button: "\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0438\u0437 \u0444\u0430\u0439\u043B\u0430",
      description: ""
    },
    shareableLink: {
      title: "\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u043F\u043E \u0441\u0441\u044B\u043B\u043A\u0435",
      button: "",
      description: ""
    }
  }
};
var mermaid = {
  title: "\u0418\u0437 Mermaid \u0432 Excalidraw",
  button: "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044C",
  description: "",
  syntax: "\u0421\u0438\u043D\u0442\u0430\u043A\u0441\u0438\u0441 Mermaid",
  preview: "\u041F\u0440\u0435\u0434\u043F\u0440\u043E\u0441\u043C\u043E\u0442\u0440"
};
var ru_RU_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  ru_RU_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=ru-RU-BLG6HZG5.js.map
