"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_objective-c_objective-c_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/objective-c/objective-c.js":
/*!***************************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/objective-c/objective-c.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/objective-c/objective-c.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".objective-c\",\n  keywords: [\n    \"#import\",\n    \"#include\",\n    \"#define\",\n    \"#else\",\n    \"#endif\",\n    \"#if\",\n    \"#ifdef\",\n    \"#ifndef\",\n    \"#ident\",\n    \"#undef\",\n    \"@class\",\n    \"@defs\",\n    \"@dynamic\",\n    \"@encode\",\n    \"@end\",\n    \"@implementation\",\n    \"@interface\",\n    \"@package\",\n    \"@private\",\n    \"@protected\",\n    \"@property\",\n    \"@protocol\",\n    \"@public\",\n    \"@selector\",\n    \"@synthesize\",\n    \"__declspec\",\n    \"assign\",\n    \"auto\",\n    \"BOOL\",\n    \"break\",\n    \"bycopy\",\n    \"byref\",\n    \"case\",\n    \"char\",\n    \"Class\",\n    \"const\",\n    \"copy\",\n    \"continue\",\n    \"default\",\n    \"do\",\n    \"double\",\n    \"else\",\n    \"enum\",\n    \"extern\",\n    \"FALSE\",\n    \"false\",\n    \"float\",\n    \"for\",\n    \"goto\",\n    \"if\",\n    \"in\",\n    \"int\",\n    \"id\",\n    \"inout\",\n    \"IMP\",\n    \"long\",\n    \"nil\",\n    \"nonatomic\",\n    \"NULL\",\n    \"oneway\",\n    \"out\",\n    \"private\",\n    \"public\",\n    \"protected\",\n    \"readwrite\",\n    \"readonly\",\n    \"register\",\n    \"return\",\n    \"SEL\",\n    \"self\",\n    \"short\",\n    \"signed\",\n    \"sizeof\",\n    \"static\",\n    \"struct\",\n    \"super\",\n    \"switch\",\n    \"typedef\",\n    \"TRUE\",\n    \"true\",\n    \"union\",\n    \"unsigned\",\n    \"volatile\",\n    \"void\",\n    \"while\"\n  ],\n  decpart: /\\d(_?\\d)*/,\n  decimal: /0|@decpart/,\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[,:;]/, \"delimiter\"],\n      [/[{}\\[\\]()<>]/, \"@brackets\"],\n      [\n        /[a-zA-Z@#]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~,]|and\\\\b|or\\\\b|not\\\\b]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [\".\", \"comment\"]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*(_?[0-9a-fA-F])*/, \"number.hex\"],\n      [\n        /@decimal((\\.@decpart)?([eE][\\-+]?@decpart)?)[fF]*/,\n        {\n          cases: {\n            \"(\\\\d)*\": \"number\",\n            $0: \"number.float\"\n          }\n        }\n      ]\n    ],\n    // Recognize strings, including those broken across lines with \\ (but not without)\n    strings: [\n      [/'$/, \"string.escape\", \"@popall\"],\n      [/'/, \"string.escape\", \"@stringBody\"],\n      [/\"$/, \"string.escape\", \"@popall\"],\n      [/\"/, \"string.escape\", \"@dblStringBody\"]\n    ],\n    stringBody: [\n      [/[^\\\\']+$/, \"string\", \"@popall\"],\n      [/[^\\\\']+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    dblStringBody: [\n      [/[^\\\\\"]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/objective-c/objective-c.js\n"));

/***/ })

}]);