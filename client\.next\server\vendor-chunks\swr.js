"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/swr";
exports.ids = ["vendor-chunks/swr"];
exports.modules = {

/***/ "(ssr)/./node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ noop),\n/* harmony export */   B: () => (/* binding */ isPromiseLike),\n/* harmony export */   I: () => (/* binding */ IS_REACT_LEGACY),\n/* harmony export */   O: () => (/* binding */ OBJECT),\n/* harmony export */   S: () => (/* binding */ SWRConfigContext),\n/* harmony export */   U: () => (/* binding */ UNDEFINED),\n/* harmony export */   a: () => (/* binding */ isFunction),\n/* harmony export */   b: () => (/* binding */ SWRGlobalState),\n/* harmony export */   c: () => (/* binding */ cache),\n/* harmony export */   d: () => (/* binding */ defaultConfig),\n/* harmony export */   e: () => (/* binding */ isUndefined),\n/* harmony export */   f: () => (/* binding */ mergeConfigs),\n/* harmony export */   g: () => (/* binding */ SWRConfig),\n/* harmony export */   h: () => (/* binding */ initCache),\n/* harmony export */   i: () => (/* binding */ isWindowDefined),\n/* harmony export */   j: () => (/* binding */ mutate),\n/* harmony export */   k: () => (/* binding */ compare),\n/* harmony export */   l: () => (/* binding */ stableHash),\n/* harmony export */   m: () => (/* binding */ mergeObjects),\n/* harmony export */   n: () => (/* binding */ internalMutate),\n/* harmony export */   o: () => (/* binding */ getTimestamp),\n/* harmony export */   p: () => (/* binding */ preset),\n/* harmony export */   q: () => (/* binding */ defaultConfigOptions),\n/* harmony export */   r: () => (/* binding */ IS_SERVER),\n/* harmony export */   s: () => (/* binding */ serialize),\n/* harmony export */   t: () => (/* binding */ rAF),\n/* harmony export */   u: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   v: () => (/* binding */ slowConnection),\n/* harmony export */   w: () => (/* binding */ isDocumentDefined),\n/* harmony export */   x: () => (/* binding */ isLegacyDeno),\n/* harmony export */   y: () => (/* binding */ hasRequestAnimationFrame),\n/* harmony export */   z: () => (/* binding */ createCacheHelper)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _events_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./events.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var dequal_lite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dequal/lite */ \"(ssr)/./node_modules/dequal/lite/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ A,B,I,O,S,U,a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z auto */ \n\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = 'undefined';\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = \"undefined\" != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst isLegacyDeno = isWindowDefined && 'Deno' in window;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window['requestAnimationFrame'] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== 'hidden';\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener('visibilitychange', callback);\n    }\n    onWindowEvent('focus', callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener('visibilitychange', callback);\n        }\n        offWindowEvent('focus', callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent('online', onOnline);\n    onWindowEvent('offline', onOffline);\n    return ()=>{\n        offWindowEvent('online', onOnline);\n        offWindowEvent('offline', onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\nconst IS_REACT_LEGACY = !react__WEBPACK_IMPORTED_MODULE_0__.useId;\nconst IS_SERVER = !isWindowDefined || isLegacyDeno;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window['requestAnimationFrame'](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? react__WEBPACK_IMPORTED_MODULE_0__.useEffect : react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== 'undefined' && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    'slow-2g',\n    '2g'\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst isObjectType = (value, type)=>OBJECT.prototype.toString.call(value) === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const isDate = isObjectType(arg, 'Date');\n    const isRegex = isObjectType(arg, 'RegExp');\n    const isPlainObject = isObjectType(arg, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === 'boolean' ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === 'function' ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (!/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const startRevalidate = ()=>{\n            const revalidators = EVENT_REVALIDATORS[key];\n            const revalidate = isFunction(options.revalidate) ? options.revalidate(get().data, _k) : options.revalidate !== false;\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](_events_mjs__WEBPACK_IMPORTED_MODULE_2__.MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (error) throw error;\n                return data;\n            } else if (error && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data: committedData,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!error) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    const populateCachedData = populateCache(data, committedData);\n                    set({\n                        data: populateCachedData,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                } else {\n                    // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                    set({\n                        data,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                }\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        Promise.resolve(startRevalidate()).then(()=>{\n            // The mutation and revalidation are ended, we can clear it since the data is\n            // not an optimistic value anymore.\n            set({\n                _c: UNDEFINED\n            });\n        });\n        // Throw error or return data\n        if (error) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return data;\n    }\n}\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = Object.create(null);\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = Object.create(null);\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    Object.create(null),\n                    Object.create(null),\n                    Object.create(null),\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, _events_mjs__WEBPACK_IMPORTED_MODULE_2__.FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, _events_mjs__WEBPACK_IMPORTED_MODULE_2__.RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = dequal_lite__WEBPACK_IMPORTED_MODULE_1__.dequal;\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, preset);\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1, fallback: f1 } = a;\n        const { use: u2, fallback: f2 } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\nconst SWRConfigContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});\nconst SWRConfig = (props)=>{\n    const { value } = props;\n    const parentConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"SWRConfig.useMemo[config]\": ()=>isFunctionalConfig ? value(parentConfig) : value\n    }[\"SWRConfig.useMemo[config]\"], [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"SWRConfig.useMemo[extendedConfig]\": ()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config)\n    }[\"SWRConfig.useMemo[extendedConfig]\"], [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect({\n        \"SWRConfig.useIsomorphicLayoutEffect\": ()=>{\n            if (cacheContext) {\n                cacheContext[2] && cacheContext[2]();\n                return cacheContext[3];\n            }\n        }\n    }[\"SWRConfig.useIsomorphicLayoutEffect\"], []);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/_internal/constants.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/swr/dist/_internal/constants.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INFINITE_PREFIX: () => (/* binding */ INFINITE_PREFIX)\n/* harmony export */ });\nconst INFINITE_PREFIX = '$inf$';\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3dyL2Rpc3QvX2ludGVybmFsL2NvbnN0YW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUUyQiIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RcXHJlYWxjb2RlXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcc3dyXFxkaXN0XFxfaW50ZXJuYWxcXGNvbnN0YW50cy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgSU5GSU5JVEVfUFJFRklYID0gJyRpbmYkJztcblxuZXhwb3J0IHsgSU5GSU5JVEVfUFJFRklYIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/_internal/constants.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/_internal/events.mjs":
/*!****************************************************!*\
  !*** ./node_modules/swr/dist/_internal/events.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ERROR_REVALIDATE_EVENT: () => (/* binding */ ERROR_REVALIDATE_EVENT),\n/* harmony export */   FOCUS_EVENT: () => (/* binding */ FOCUS_EVENT),\n/* harmony export */   MUTATE_EVENT: () => (/* binding */ MUTATE_EVENT),\n/* harmony export */   RECONNECT_EVENT: () => (/* binding */ RECONNECT_EVENT)\n/* harmony export */ });\nconst FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3dyL2Rpc3QvX2ludGVybmFsL2V2ZW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUU4RSIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RcXHJlYWxjb2RlXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcc3dyXFxkaXN0XFxfaW50ZXJuYWxcXGV2ZW50cy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgRk9DVVNfRVZFTlQgPSAwO1xuY29uc3QgUkVDT05ORUNUX0VWRU5UID0gMTtcbmNvbnN0IE1VVEFURV9FVkVOVCA9IDI7XG5jb25zdCBFUlJPUl9SRVZBTElEQVRFX0VWRU5UID0gMztcblxuZXhwb3J0IHsgRVJST1JfUkVWQUxJREFURV9FVkVOVCwgRk9DVVNfRVZFTlQsIE1VVEFURV9FVkVOVCwgUkVDT05ORUNUX0VWRU5UIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/_internal/events.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/_internal/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/swr/dist/_internal/index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INFINITE_PREFIX: () => (/* reexport safe */ _constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX),\n/* harmony export */   IS_REACT_LEGACY: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.I),\n/* harmony export */   IS_SERVER: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.r),\n/* harmony export */   OBJECT: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.O),\n/* harmony export */   SWRConfig: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.g),\n/* harmony export */   SWRGlobalState: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   UNDEFINED: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.U),\n/* harmony export */   cache: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   compare: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.k),\n/* harmony export */   createCacheHelper: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.z),\n/* harmony export */   defaultConfig: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   defaultConfigOptions: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.q),\n/* harmony export */   getTimestamp: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.o),\n/* harmony export */   hasRequestAnimationFrame: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.y),\n/* harmony export */   initCache: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   internalMutate: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   isDocumentDefined: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.w),\n/* harmony export */   isFunction: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.a),\n/* harmony export */   isLegacyDeno: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.x),\n/* harmony export */   isPromiseLike: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.B),\n/* harmony export */   isUndefined: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.e),\n/* harmony export */   isWindowDefined: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.i),\n/* harmony export */   mergeConfigs: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   mergeObjects: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   mutate: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   noop: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.A),\n/* harmony export */   normalize: () => (/* binding */ normalize),\n/* harmony export */   preload: () => (/* binding */ preload),\n/* harmony export */   preset: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   rAF: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.t),\n/* harmony export */   revalidateEvents: () => (/* reexport module object */ _events_mjs__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   serialize: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   slowConnection: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.v),\n/* harmony export */   stableHash: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.l),\n/* harmony export */   subscribeCallback: () => (/* binding */ subscribeCallback),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.u),\n/* harmony export */   useSWRConfig: () => (/* binding */ useSWRConfig),\n/* harmony export */   withArgs: () => (/* binding */ withArgs),\n/* harmony export */   withMiddleware: () => (/* binding */ withMiddleware)\n/* harmony export */ });\n/* harmony import */ var _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config-context-client-v7VOFo66.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs\");\n/* harmony import */ var _events_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./events.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var _constants_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/constants.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\n\n\n\n// @ts-expect-error\nconst enableDevtools = _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.i && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = react__WEBPACK_IMPORTED_MODULE_3__;\n    }\n};\n\nconst normalize = (args)=>{\n    return (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\n\nconst useSWRConfig = ()=>{\n    return (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.m)(_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.d, (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.S));\n};\n\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(key_);\n    const [, , , PRELOAD] = _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.b.get(_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.c);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(key_);\n            const [, , , PRELOAD] = _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.b.get(_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.c);\n            if (key.startsWith(_constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX)) {\n                // we want the infinite fetcher to be called.\n                // handling of the PRELOAD cache happens there.\n                return fetcher_(...args);\n            }\n            const req = PRELOAD[key];\n            if ((0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.e)(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\n\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.f)(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\n\nsetupDevTools();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/_internal/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/index/index.mjs":
/*!***********************************************!*\
  !*** ./node_modules/swr/dist/index/index.mjs ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SWRConfig: () => (/* binding */ SWRConfig),\n/* harmony export */   \"default\": () => (/* binding */ useSWR),\n/* harmony export */   mutate: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.j),\n/* harmony export */   preload: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.preload),\n/* harmony export */   unstable_serialize: () => (/* binding */ unstable_serialize),\n/* harmony export */   useSWRConfig: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.useSWRConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/index.js\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/index.mjs\");\n\n\n\n\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst isObjectType = (value, type)=>OBJECT.prototype.toString.call(value) === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const isDate = isObjectType(arg, 'Date');\n    const isRegex = isObjectType(arg, 'RegExp');\n    const isPlainObject = isObjectType(arg, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst unstable_serialize = (key)=>serialize(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = react__WEBPACK_IMPORTED_MODULE_0__.use || // This extra generic is to avoid TypeScript mixing up the generic and JSX sytax\n// and emitting an error.\n// We assume that this is only for the `use(thenable)` case, not `use(context)`.\n// https://github.com/facebook/react/blob/aed00dacfb79d17c53218404c52b1c7aa59c4a89/packages/react-server/src/ReactFizzThenable.js#L45\n((thenable)=>{\n    switch(thenable.status){\n        case 'pending':\n            throw thenable;\n        case 'fulfilled':\n            return thenable.value;\n        case 'rejected':\n            throw thenable.reason;\n        default:\n            thenable.status = 'pending';\n            thenable.then((v)=>{\n                thenable.status = 'fulfilled';\n                thenable.value = v;\n            }, (e)=>{\n                thenable.status = 'rejected';\n                thenable.reason = e;\n            });\n            throw thenable;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache, compare, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.b.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.s)(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Refs to keep the key and config.\n    const keyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(key);\n    const fetcherRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fetcher);\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.z)(cache, key);\n    const stateDependencies = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({}).current;\n    // Resolve the fallback data from either the inline option, or the global provider.\n    // If it's a promise, we simply let React suspend and resolve it for us.\n    const fallback = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(fallbackData) ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(config.fallback) ? _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U : config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            return revalidateIfStale !== false;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.m)(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData) ? fallback && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.B)(fallback) ? use(fallback) : fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(data);\n    const returnedData = keepPreviousData ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData) ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(laggyDataRef.current) ? data : laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.I) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.o)()\n                ];\n            }\n            // Wait until the ongoing request is done. Deduplication is also\n            // considered here.\n            ;\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.a)(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (!getConfig().revalidateOnFocus || !getConfig().revalidateOnReconnect || isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](_internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    const boundMutate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.n)(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U, WITH_DEDUPE);\n        let nextFocusRevalidatedAt = 0;\n        if (getConfig().revalidateOnFocus) {\n            const initNow = Date.now();\n            nextFocusRevalidatedAt = initNow + getConfig().focusThrottleInterval;\n        }\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        const onRevalidate = (type, opts = {})=>{\n            if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.subscribeCallback)(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) || _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.r) {\n                // Revalidate immediately.\n                softRevalidate();\n            } else {\n                // Delay the revalidate if we have data to return so we won't block\n                // rendering.\n                (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.t)(softRevalidate);\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.a)(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any fallback data. This causes hydration errors. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.I && _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.r) {\n            throw new Error('Fallback data is required when using Suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(returnedData)) {\n                promise.status = 'fulfilled';\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    const swrResponse = {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n    return swrResponse;\n};\nconst SWRConfig = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.O.defineProperty(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.g, 'defaultValue', {\n    value: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.d\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error, isLoading } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (isLoading) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.withArgs)(useSWRHandler);\n\n// useSWR\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/index/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/infinite/index.mjs":
/*!**************************************************!*\
  !*** ./node_modules/swr/dist/infinite/index.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSWRInfinite),\n/* harmony export */   infinite: () => (/* binding */ infinite),\n/* harmony export */   unstable_serialize: () => (/* binding */ unstable_serialize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _index_index_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../index/index.mjs */ \"(ssr)/./node_modules/swr/dist/index/index.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs\");\n/* harmony import */ var _internal_constants_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_internal/constants.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/constants.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/index.mjs\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/index.js\");\n\n\n\n\n\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst isObjectType = (value, type)=>OBJECT.prototype.toString.call(value) === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const isDate = isObjectType(arg, 'Date');\n    const isRegex = isObjectType(arg, 'RegExp');\n    const isPlainObject = isObjectType(arg, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst getFirstPageKey = (getKey)=>{\n    return serialize(getKey ? getKey(0, null) : null)[0];\n};\nconst unstable_serialize = (getKey)=>{\n    return _internal_constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX + getFirstPageKey(getKey);\n};\n\n// We have to several type castings here because `useSWRInfinite` is a special\n// hook where `key` and return type are not like the normal `useSWR` types.\nconst EMPTY_PROMISE = Promise.resolve();\nconst infinite = (useSWRNext)=>(getKey, fn, config)=>{\n        const didMountRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n        const { cache: cache$1, initialSize = 1, revalidateAll = false, persistSize = false, revalidateFirstPage = true, revalidateOnMount = false, parallel = false } = config;\n        const [, , , PRELOAD] = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.b.get(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.c);\n        // The serialized key of the first page. This key will be used to store\n        // metadata of this SWR infinite hook.\n        let infiniteKey;\n        try {\n            infiniteKey = getFirstPageKey(getKey);\n            if (infiniteKey) infiniteKey = _internal_constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX + infiniteKey;\n        } catch (err) {\n        // Not ready yet.\n        }\n        const [get, set, subscribeCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, infiniteKey);\n        const getSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n            const size = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(get()._l) ? initialSize : get()._l;\n            return size;\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            cache$1,\n            infiniteKey,\n            initialSize\n        ]);\n        (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback)=>{\n            if (infiniteKey) return subscribeCache(infiniteKey, ()=>{\n                callback();\n            });\n            return ()=>{};\n        }, // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            cache$1,\n            infiniteKey\n        ]), getSnapshot, getSnapshot);\n        const resolvePageSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n            const cachedPageSize = get()._l;\n            return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(cachedPageSize) ? initialSize : cachedPageSize;\n        // `cache` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            initialSize\n        ]);\n        // keep the last page size to restore it with the persistSize option\n        const lastPageSizeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(resolvePageSize());\n        // When the page key changes, we reset the page size if it's not persisted\n        (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.u)(()=>{\n            if (!didMountRef.current) {\n                didMountRef.current = true;\n                return;\n            }\n            if (infiniteKey) {\n                // If the key has been changed, we keep the current page size if persistSize is enabled\n                // Otherwise, we reset the page size to cached pageSize\n                set({\n                    _l: persistSize ? lastPageSizeRef.current : resolvePageSize()\n                });\n            }\n        // `initialSize` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            cache$1\n        ]);\n        // Needs to check didMountRef during mounting, not in the fetcher\n        const shouldRevalidateOnMount = revalidateOnMount && !didMountRef.current;\n        // Actual SWR hook to load all pages in one fetcher.\n        const swr = useSWRNext(infiniteKey, async (key)=>{\n            // get the revalidate context\n            const forceRevalidateAll = get()._i;\n            const shouldRevalidatePage = get()._r;\n            set({\n                _r: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.U\n            });\n            // return an array of page data\n            const data = [];\n            const pageSize = resolvePageSize();\n            const [getCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, key);\n            const cacheData = getCache().data;\n            const revalidators = [];\n            let previousPageData = null;\n            for(let i = 0; i < pageSize; ++i){\n                const [pageKey, pageArg] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.s)(getKey(i, parallel ? null : previousPageData));\n                if (!pageKey) {\n                    break;\n                }\n                const [getSWRCache, setSWRCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, pageKey);\n                // Get the cached page data.\n                let pageData = getSWRCache().data;\n                // should fetch (or revalidate) if:\n                // - `revalidateAll` is enabled\n                // - `mutate()` called\n                // - the cache is missing\n                // - it's the first page and it's not the initial render\n                // - `revalidateOnMount` is enabled and it's on mount\n                // - cache for that page has changed\n                const shouldFetchPage = revalidateAll || forceRevalidateAll || (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(pageData) || revalidateFirstPage && !i && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(cacheData) || shouldRevalidateOnMount || cacheData && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(cacheData[i]) && !config.compare(cacheData[i], pageData);\n                if (fn && (typeof shouldRevalidatePage === 'function' ? shouldRevalidatePage(pageData, pageArg) : shouldFetchPage)) {\n                    const revalidate = async ()=>{\n                        const hasPreloadedRequest = pageKey in PRELOAD;\n                        if (!hasPreloadedRequest) {\n                            pageData = await fn(pageArg);\n                        } else {\n                            const req = PRELOAD[pageKey];\n                            // delete the preload cache key before resolving it\n                            // in case there's an error\n                            delete PRELOAD[pageKey];\n                            // get the page data from the preload cache\n                            pageData = await req;\n                        }\n                        setSWRCache({\n                            data: pageData,\n                            _k: pageArg\n                        });\n                        data[i] = pageData;\n                    };\n                    if (parallel) {\n                        revalidators.push(revalidate);\n                    } else {\n                        await revalidate();\n                    }\n                } else {\n                    data[i] = pageData;\n                }\n                if (!parallel) {\n                    previousPageData = pageData;\n                }\n            }\n            // flush all revalidateions in parallel\n            if (parallel) {\n                await Promise.all(revalidators.map((r)=>r()));\n            }\n            // once we executed the data fetching based on the context, clear the context\n            set({\n                _i: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.U\n            });\n            // return the data\n            return data;\n        }, config);\n        const mutate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(// eslint-disable-next-line func-names\n        function(data, opts) {\n            // When passing as a boolean, it's explicitly used to disable/enable\n            // revalidation.\n            const options = typeof opts === 'boolean' ? {\n                revalidate: opts\n            } : opts || {};\n            // Default to true.\n            const shouldRevalidate = options.revalidate !== false;\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            if (shouldRevalidate) {\n                if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(data)) {\n                    // We only revalidate the pages that are changed\n                    set({\n                        _i: false,\n                        _r: options.revalidate\n                    });\n                } else {\n                    // Calling `mutate()`, we revalidate all pages\n                    set({\n                        _i: true,\n                        _r: options.revalidate\n                    });\n                }\n            }\n            return arguments.length ? swr.mutate(data, {\n                ...options,\n                revalidate: shouldRevalidate\n            }) : swr.mutate();\n        }, // swr.mutate is always the same reference\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache$1\n        ]);\n        // Extend the SWR API\n        const setSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((arg)=>{\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            const [, changeSize] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, infiniteKey);\n            let size;\n            if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.a)(arg)) {\n                size = arg(resolvePageSize());\n            } else if (typeof arg == 'number') {\n                size = arg;\n            }\n            if (typeof size != 'number') return EMPTY_PROMISE;\n            changeSize({\n                _l: size\n            });\n            lastPageSizeRef.current = size;\n            // Calculate the page data after the size change.\n            const data = [];\n            const [getInfiniteCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, infiniteKey);\n            let previousPageData = null;\n            for(let i = 0; i < size; ++i){\n                const [pageKey] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.s)(getKey(i, previousPageData));\n                const [getCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, pageKey);\n                // Get the cached page data.\n                const pageData = pageKey ? getCache().data : _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.U;\n                // Call `mutate` with infinte cache data if we can't get it from the page cache.\n                if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(pageData)) {\n                    return mutate(getInfiniteCache().data);\n                }\n                data.push(pageData);\n                previousPageData = pageData;\n            }\n            return mutate(data);\n        }, // exclude getKey from the dependencies, which isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache$1,\n            mutate,\n            resolvePageSize\n        ]);\n        // Use getter functions to avoid unnecessary re-renders caused by triggering\n        // all the getters of the returned swr object.\n        return {\n            size: resolvePageSize(),\n            setSize,\n            mutate,\n            get data () {\n                return swr.data;\n            },\n            get error () {\n                return swr.error;\n            },\n            get isValidating () {\n                return swr.isValidating;\n            },\n            get isLoading () {\n                return swr.isLoading;\n            }\n        };\n    };\nconst useSWRInfinite = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.withMiddleware)(_index_index_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"], infinite);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/infinite/index.mjs\n");

/***/ })

};
;