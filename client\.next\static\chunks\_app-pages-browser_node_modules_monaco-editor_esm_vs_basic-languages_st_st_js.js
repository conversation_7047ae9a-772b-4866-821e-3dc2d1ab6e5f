"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_st_st_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/st/st.js":
/*!*********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/st/st.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/st/st.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"(*\", \"*)\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"var\", \"end_var\"],\n    [\"var_input\", \"end_var\"],\n    [\"var_output\", \"end_var\"],\n    [\"var_in_out\", \"end_var\"],\n    [\"var_temp\", \"end_var\"],\n    [\"var_global\", \"end_var\"],\n    [\"var_access\", \"end_var\"],\n    [\"var_external\", \"end_var\"],\n    [\"type\", \"end_type\"],\n    [\"struct\", \"end_struct\"],\n    [\"program\", \"end_program\"],\n    [\"function\", \"end_function\"],\n    [\"function_block\", \"end_function_block\"],\n    [\"action\", \"end_action\"],\n    [\"step\", \"end_step\"],\n    [\"initial_step\", \"end_step\"],\n    [\"transaction\", \"end_transaction\"],\n    [\"configuration\", \"end_configuration\"],\n    [\"tcp\", \"end_tcp\"],\n    [\"recource\", \"end_recource\"],\n    [\"channel\", \"end_channel\"],\n    [\"library\", \"end_library\"],\n    [\"folder\", \"end_folder\"],\n    [\"binaries\", \"end_binaries\"],\n    [\"includes\", \"end_includes\"],\n    [\"sources\", \"end_sources\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"/*\", close: \"*/\" },\n    { open: \"'\", close: \"'\", notIn: [\"string_sq\"] },\n    { open: '\"', close: '\"', notIn: [\"string_dq\"] },\n    { open: \"var_input\", close: \"end_var\" },\n    { open: \"var_output\", close: \"end_var\" },\n    { open: \"var_in_out\", close: \"end_var\" },\n    { open: \"var_temp\", close: \"end_var\" },\n    { open: \"var_global\", close: \"end_var\" },\n    { open: \"var_access\", close: \"end_var\" },\n    { open: \"var_external\", close: \"end_var\" },\n    { open: \"type\", close: \"end_type\" },\n    { open: \"struct\", close: \"end_struct\" },\n    { open: \"program\", close: \"end_program\" },\n    { open: \"function\", close: \"end_function\" },\n    { open: \"function_block\", close: \"end_function_block\" },\n    { open: \"action\", close: \"end_action\" },\n    { open: \"step\", close: \"end_step\" },\n    { open: \"initial_step\", close: \"end_step\" },\n    { open: \"transaction\", close: \"end_transaction\" },\n    { open: \"configuration\", close: \"end_configuration\" },\n    { open: \"tcp\", close: \"end_tcp\" },\n    { open: \"recource\", close: \"end_recource\" },\n    { open: \"channel\", close: \"end_channel\" },\n    { open: \"library\", close: \"end_library\" },\n    { open: \"folder\", close: \"end_folder\" },\n    { open: \"binaries\", close: \"end_binaries\" },\n    { open: \"includes\", close: \"end_includes\" },\n    { open: \"sources\", close: \"end_sources\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"var\", close: \"end_var\" },\n    { open: \"var_input\", close: \"end_var\" },\n    { open: \"var_output\", close: \"end_var\" },\n    { open: \"var_in_out\", close: \"end_var\" },\n    { open: \"var_temp\", close: \"end_var\" },\n    { open: \"var_global\", close: \"end_var\" },\n    { open: \"var_access\", close: \"end_var\" },\n    { open: \"var_external\", close: \"end_var\" },\n    { open: \"type\", close: \"end_type\" },\n    { open: \"struct\", close: \"end_struct\" },\n    { open: \"program\", close: \"end_program\" },\n    { open: \"function\", close: \"end_function\" },\n    { open: \"function_block\", close: \"end_function_block\" },\n    { open: \"action\", close: \"end_action\" },\n    { open: \"step\", close: \"end_step\" },\n    { open: \"initial_step\", close: \"end_step\" },\n    { open: \"transaction\", close: \"end_transaction\" },\n    { open: \"configuration\", close: \"end_configuration\" },\n    { open: \"tcp\", close: \"end_tcp\" },\n    { open: \"recource\", close: \"end_recource\" },\n    { open: \"channel\", close: \"end_channel\" },\n    { open: \"library\", close: \"end_library\" },\n    { open: \"folder\", close: \"end_folder\" },\n    { open: \"binaries\", close: \"end_binaries\" },\n    { open: \"includes\", close: \"end_includes\" },\n    { open: \"sources\", close: \"end_sources\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#pragma\\\\s+region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#pragma\\\\s+endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".st\",\n  ignoreCase: true,\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  keywords: [\n    \"if\",\n    \"end_if\",\n    \"elsif\",\n    \"else\",\n    \"case\",\n    \"of\",\n    \"to\",\n    \"__try\",\n    \"__catch\",\n    \"__finally\",\n    \"do\",\n    \"with\",\n    \"by\",\n    \"while\",\n    \"repeat\",\n    \"end_while\",\n    \"end_repeat\",\n    \"end_case\",\n    \"for\",\n    \"end_for\",\n    \"task\",\n    \"retain\",\n    \"non_retain\",\n    \"constant\",\n    \"with\",\n    \"at\",\n    \"exit\",\n    \"return\",\n    \"interval\",\n    \"priority\",\n    \"address\",\n    \"port\",\n    \"on_channel\",\n    \"then\",\n    \"iec\",\n    \"file\",\n    \"uses\",\n    \"version\",\n    \"packagetype\",\n    \"displayname\",\n    \"copyright\",\n    \"summary\",\n    \"vendor\",\n    \"common_source\",\n    \"from\",\n    \"extends\",\n    \"implements\"\n  ],\n  constant: [\"false\", \"true\", \"null\"],\n  defineKeywords: [\n    \"var\",\n    \"var_input\",\n    \"var_output\",\n    \"var_in_out\",\n    \"var_temp\",\n    \"var_global\",\n    \"var_access\",\n    \"var_external\",\n    \"end_var\",\n    \"type\",\n    \"end_type\",\n    \"struct\",\n    \"end_struct\",\n    \"program\",\n    \"end_program\",\n    \"function\",\n    \"end_function\",\n    \"function_block\",\n    \"end_function_block\",\n    \"interface\",\n    \"end_interface\",\n    \"method\",\n    \"end_method\",\n    \"property\",\n    \"end_property\",\n    \"namespace\",\n    \"end_namespace\",\n    \"configuration\",\n    \"end_configuration\",\n    \"tcp\",\n    \"end_tcp\",\n    \"resource\",\n    \"end_resource\",\n    \"channel\",\n    \"end_channel\",\n    \"library\",\n    \"end_library\",\n    \"folder\",\n    \"end_folder\",\n    \"binaries\",\n    \"end_binaries\",\n    \"includes\",\n    \"end_includes\",\n    \"sources\",\n    \"end_sources\",\n    \"action\",\n    \"end_action\",\n    \"step\",\n    \"initial_step\",\n    \"end_step\",\n    \"transaction\",\n    \"end_transaction\"\n  ],\n  typeKeywords: [\n    \"int\",\n    \"sint\",\n    \"dint\",\n    \"lint\",\n    \"usint\",\n    \"uint\",\n    \"udint\",\n    \"ulint\",\n    \"real\",\n    \"lreal\",\n    \"time\",\n    \"date\",\n    \"time_of_day\",\n    \"date_and_time\",\n    \"string\",\n    \"bool\",\n    \"byte\",\n    \"word\",\n    \"dword\",\n    \"array\",\n    \"pointer\",\n    \"lword\"\n  ],\n  operators: [\n    \"=\",\n    \">\",\n    \"<\",\n    \":\",\n    \":=\",\n    \"<=\",\n    \">=\",\n    \"<>\",\n    \"&\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"**\",\n    \"MOD\",\n    \"^\",\n    \"or\",\n    \"and\",\n    \"not\",\n    \"xor\",\n    \"abs\",\n    \"acos\",\n    \"asin\",\n    \"atan\",\n    \"cos\",\n    \"exp\",\n    \"expt\",\n    \"ln\",\n    \"log\",\n    \"sin\",\n    \"sqrt\",\n    \"tan\",\n    \"sel\",\n    \"max\",\n    \"min\",\n    \"limit\",\n    \"mux\",\n    \"shl\",\n    \"shr\",\n    \"rol\",\n    \"ror\",\n    \"indexof\",\n    \"sizeof\",\n    \"adr\",\n    \"adrinst\",\n    \"bitadr\",\n    \"is_valid\",\n    \"ref\",\n    \"ref_to\"\n  ],\n  builtinVariables: [],\n  builtinFunctions: [\n    \"sr\",\n    \"rs\",\n    \"tp\",\n    \"ton\",\n    \"tof\",\n    \"eq\",\n    \"ge\",\n    \"le\",\n    \"lt\",\n    \"ne\",\n    \"round\",\n    \"trunc\",\n    \"ctd\",\n    \"\\u0441tu\",\n    \"ctud\",\n    \"r_trig\",\n    \"f_trig\",\n    \"move\",\n    \"concat\",\n    \"delete\",\n    \"find\",\n    \"insert\",\n    \"left\",\n    \"len\",\n    \"replace\",\n    \"right\",\n    \"rtc\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  // C# style strings\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/(\\.\\.)/, \"delimiter\"],\n      [/\\b(16#[0-9A-Fa-f\\_]*)+\\b/, \"number.hex\"],\n      [/\\b(2#[01\\_]+)+\\b/, \"number.binary\"],\n      [/\\b(8#[0-9\\_]*)+\\b/, \"number.octal\"],\n      [/\\b\\d*\\.\\d+([eE][\\-+]?\\d+)?\\b/, \"number.float\"],\n      [/\\b(L?REAL)#[0-9\\_\\.e]+\\b/, \"number.float\"],\n      [/\\b(BYTE|(?:D|L)?WORD|U?(?:S|D|L)?INT)#[0-9\\_]+\\b/, \"number\"],\n      [/\\d+/, \"number\"],\n      [/\\b(T|DT|TOD)#[0-9:-_shmyd]+\\b/, \"tag\"],\n      [/\\%(I|Q|M)(X|B|W|D|L)[0-9\\.]+/, \"tag\"],\n      [/\\%(I|Q|M)[0-9\\.]*/, \"tag\"],\n      [/\\b[A-Za-z]{1,6}#[0-9]+\\b/, \"tag\"],\n      [/\\b(TO_|CTU_|CTD_|CTUD_|MUX_|SEL_)[A_Za-z]+\\b/, \"predefined\"],\n      [/\\b[A_Za-z]+(_TO_)[A_Za-z]+\\b/, \"predefined\"],\n      [/[;]/, \"delimiter\"],\n      [/[.]/, { token: \"delimiter\", next: \"@params\" }],\n      // identifiers and keywords\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@operators\": \"operators\",\n            \"@keywords\": \"keyword\",\n            \"@typeKeywords\": \"type\",\n            \"@defineKeywords\": \"variable\",\n            \"@constant\": \"constant\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string_dq\" }],\n      [/'/, { token: \"string.quote\", bracket: \"@open\", next: \"@string_sq\" }],\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    params: [\n      [/\\b[A-Za-z0-9_]+\\b(?=\\()/, { token: \"identifier\", next: \"@pop\" }],\n      [/\\b[A-Za-z0-9_]+\\b/, \"variable.name\", \"@pop\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [\"\\\\*/\", \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    comment2: [\n      [/[^\\(*]+/, \"comment\"],\n      [/\\(\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [\"\\\\*\\\\)\", \"comment\", \"@pop\"],\n      [/[\\(*]/, \"comment\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\/.*$/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\(\\*/, \"comment\", \"@comment2\"]\n    ],\n    string_dq: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    string_sq: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/st/st.js\n"));

/***/ })

}]);