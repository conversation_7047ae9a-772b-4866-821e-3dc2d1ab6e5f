// socket.d.ts

import { Server, Socket } from "socket.io"

// Extend the Socket types for custom events
declare module "socket.io" {
  interface ClientToServerEvents {
    "code-change": (data: { roomId: string; code: string }) => void
    "create-room": (data: { username: string }, callback: (res: { roomId: string }) => void) => void
    "join-room": (data: { roomId: string; username: string }, callback: (res: { error?: string }) => void) => void
    "typing": (data: { roomId: string; username: string }) => void
    "leave-room": (roomId: string) => void
    "highlight-line": (data: { roomId: string; startLine: number; endLine: number; comment?: string }) => void;
  }

  interface ServerToClientEvents {
    "code-update": (code: string) => void
    "user-typing": (data: { username: string }) => void
    "highlight-line": (data: { startLine: number; endLine: number; comment?: string }) => void;
  }
}
