"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_scss_scss_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/scss/scss.js":
/*!*************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/scss/scss.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/scss/scss.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|([@$#!.:]?[\\w-?]+%?)|[@#!.]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"],\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".scss\",\n  ws: \"[ \t\\n\\r\\f]*\",\n  // whitespaces (referenced in several rules)\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [{ include: \"@selector\" }],\n    selector: [\n      { include: \"@comments\" },\n      { include: \"@import\" },\n      { include: \"@variabledeclaration\" },\n      { include: \"@warndebug\" },\n      // sass: log statements\n      [\"[@](include)\", { token: \"keyword\", next: \"@includedeclaration\" }],\n      // sass: include statement\n      [\n        \"[@](keyframes|-webkit-keyframes|-moz-keyframes|-o-keyframes)\",\n        { token: \"keyword\", next: \"@keyframedeclaration\" }\n      ],\n      [\"[@](page|content|font-face|-moz-document)\", { token: \"keyword\" }],\n      // sass: placeholder for includes\n      [\"[@](charset|namespace)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\"[@](function)\", { token: \"keyword\", next: \"@functiondeclaration\" }],\n      [\"[@](mixin)\", { token: \"keyword\", next: \"@mixindeclaration\" }],\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"meta\", next: \"@urldeclaration\" }],\n      { include: \"@controlstatement\" },\n      // sass control statements\n      { include: \"@selectorname\" },\n      [\"[&\\\\*]\", \"tag\"],\n      // selector symbols\n      [\"[>\\\\+,]\", \"delimiter\"],\n      // selector operators\n      [\"\\\\[\", { token: \"delimiter.bracket\", next: \"@selectorattribute\" }],\n      [\"{\", { token: \"delimiter.curly\", next: \"@selectorbody\" }]\n    ],\n    selectorbody: [\n      [\"[*_]?@identifier@ws:(?=(\\\\s|\\\\d|[^{;}]*[;}]))\", \"attribute.name\", \"@rulevalue\"],\n      // rule definition: to distinguish from a nested selector check for whitespace, number or a semicolon\n      { include: \"@selector\" },\n      // sass: nested selectors\n      [\"[@](extend)\", { token: \"keyword\", next: \"@extendbody\" }],\n      // sass: extend other selectors\n      [\"[@](return)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    selectorname: [\n      [\"#{\", { token: \"meta\", next: \"@variableinterpolation\" }],\n      // sass: interpolation\n      [\"(\\\\.|#(?=[^{])|%|(@identifier)|:)+\", \"tag\"]\n      // selector (.foo, div, ...)\n    ],\n    selectorattribute: [{ include: \"@term\" }, [\"]\", { token: \"delimiter.bracket\", next: \"@pop\" }]],\n    term: [\n      { include: \"@comments\" },\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"meta\", next: \"@urldeclaration\" }],\n      { include: \"@functioninvocation\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@variablereference\" },\n      [\"(and\\\\b|or\\\\b|not\\\\b)\", \"operator\"],\n      { include: \"@name\" },\n      [\"([<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~,])\", \"operator\"],\n      [\",\", \"delimiter\"],\n      [\"!default\", \"literal\"],\n      [\"\\\\(\", { token: \"delimiter.parenthesis\", next: \"@parenthizedterm\" }]\n    ],\n    rulevalue: [\n      { include: \"@term\" },\n      [\"!important\", \"literal\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@nestedproperty\" }],\n      // sass: nested properties\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    nestedproperty: [\n      [\"[*_]?@identifier@ws:\", \"attribute.name\", \"@rulevalue\"],\n      { include: \"@comments\" },\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    warndebug: [[\"[@](warn|debug)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    import: [[\"[@](import)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    variabledeclaration: [\n      // sass variables\n      [\"\\\\$@identifier@ws:\", \"variable.decl\", \"@declarationbody\"]\n    ],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    parenthizedterm: [\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    declarationbody: [\n      { include: \"@term\" },\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    extendbody: [\n      { include: \"@selectorname\" },\n      [\"!optional\", \"literal\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    variablereference: [\n      // sass variable reference\n      [\"\\\\$@identifier\", \"variable.ref\"],\n      [\"\\\\.\\\\.\\\\.\", \"operator\"],\n      // var args in reference\n      [\"#{\", { token: \"meta\", next: \"@variableinterpolation\" }]\n      // sass var resolve\n    ],\n    variableinterpolation: [\n      { include: \"@variablereference\" },\n      [\"}\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [\".\", \"comment\"]\n    ],\n    name: [[\"@identifier\", \"attribute.value\"]],\n    numbers: [\n      [\"(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"number.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"number\",\n        \"@pop\"\n      ]\n    ],\n    functiondeclaration: [\n      [\"@identifier@ws\\\\(\", { token: \"meta\", next: \"@parameterdeclaration\" }],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@functionbody\" }]\n    ],\n    mixindeclaration: [\n      // mixin with parameters\n      [\"@identifier@ws\\\\(\", { token: \"meta\", next: \"@parameterdeclaration\" }],\n      // mixin without parameters\n      [\"@identifier\", \"meta\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    parameterdeclaration: [\n      [\"\\\\$@identifier@ws:\", \"variable.decl\"],\n      [\"\\\\.\\\\.\\\\.\", \"operator\"],\n      // var args in declaration\n      [\",\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    includedeclaration: [\n      { include: \"@functioninvocation\" },\n      [\"@identifier\", \"meta\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }],\n      // missing semicolon\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    keyframedeclaration: [\n      [\"@identifier\", \"meta\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@keyframebody\" }]\n    ],\n    keyframebody: [\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.curly\", next: \"@selectorbody\" }],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    controlstatement: [\n      [\n        \"[@](if|else|for|while|each|media)\",\n        { token: \"keyword.flow\", next: \"@controlstatementdeclaration\" }\n      ]\n    ],\n    controlstatementdeclaration: [\n      [\"(in|from|through|if|to)\\\\b\", { token: \"keyword.flow\" }],\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    functionbody: [\n      [\"[@](return)\", { token: \"keyword\" }],\n      { include: \"@variabledeclaration\" },\n      { include: \"@term\" },\n      { include: \"@controlstatement\" },\n      [\";\", \"delimiter\"],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    functioninvocation: [[\"@identifier\\\\(\", { token: \"meta\", next: \"@functionarguments\" }]],\n    functionarguments: [\n      [\"\\\\$@identifier@ws:\", \"attribute.name\"],\n      [\"[,]\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    strings: [\n      ['~?\"', { token: \"string.delimiter\", next: \"@stringenddoublequote\" }],\n      [\"~?'\", { token: \"string.delimiter\", next: \"@stringendquote\" }]\n    ],\n    stringenddoublequote: [\n      [\"\\\\\\\\.\", \"string\"],\n      ['\"', { token: \"string.delimiter\", next: \"@pop\" }],\n      [\".\", \"string\"]\n    ],\n    stringendquote: [\n      [\"\\\\\\\\.\", \"string\"],\n      [\"'\", { token: \"string.delimiter\", next: \"@pop\" }],\n      [\".\", \"string\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/scss/scss.js\n"));

/***/ })

}]);