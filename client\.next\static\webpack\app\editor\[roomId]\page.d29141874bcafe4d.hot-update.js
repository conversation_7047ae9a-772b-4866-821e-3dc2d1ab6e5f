"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var monaco_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! monaco-editor */ \"(app-pages-browser)/../node_modules/monaco-editor/esm/vs/editor/editor.main.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlignLeft,FiCode,FiCopy,FiDownload,FiHelpCircle,FiLoader,FiMoon,FiPlay,FiSearch,FiSun,FiUpload,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_socketService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/socketService */ \"(app-pages-browser)/./src/services/socketService.ts\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/throttle */ \"(app-pages-browser)/./node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/SocketContext */ \"(app-pages-browser)/./src/context/SocketContext.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_formatCode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/formatCode */ \"(app-pages-browser)/./src/utils/formatCode.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/ThemeContext */ \"(app-pages-browser)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CodeEditor(param) {\n    let { roomId, username: initialUsername } = param;\n    _s();\n    const { isConnected } = (0,_context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket)();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"// Start coding...\");\n    // Use a ref to always track the latest code value\n    const latestCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"// Start coding...\");\n    // Get username from localStorage if available, otherwise use initialUsername\n    const storedUsername =  true ? localStorage.getItem(\"username\") : 0;\n    console.log(\"Initial username: \".concat(initialUsername, \", Stored username: \").concat(storedUsername));\n    // Track the username internally to handle server validation\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedUsername || initialUsername);\n    const [typingUser, setTypingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeUsers, setActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUserList, setShowUserList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"javascript\");\n    const [copySuccess, setCopySuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [teacherSelectionDecorations, setTeacherSelectionDecorations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Code execution states\n    const [isExecuting, setIsExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionOutput, setExecutionOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [executionError, setExecutionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOutput, setShowOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Piston API runtimes\n    const [runtimes, setRuntimes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [runtimesLoaded, setRuntimesLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    // Theme and minimap state (ensure these are always defined at the top)\n    const { theme, setTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme)();\n    const [showShortcuts, setShowShortcuts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [minimapEnabled, setMinimapEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Language-specific code snippets\n    const SNIPPETS = {\n        javascript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            }\n        ],\n        typescript: [\n            {\n                label: \"Function\",\n                value: \"function myFunction(): void {\\n  // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (let i = 0; i < 10; i++) {\\n  // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n  // code\\n}\"\n            },\n            {\n                label: \"Console Log\",\n                value: \"console.log('Hello, World!');\"\n            },\n            {\n                label: \"Type Declaration\",\n                value: \"type MyType = {\\n  key: string;\\n};\"\n            }\n        ],\n        python3: [\n            {\n                label: \"Function\",\n                value: \"def my_function():\\n    # code\\n    pass\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in range(10):\\n    # code\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition:\\n    # code\"\n            },\n            {\n                label: \"Print\",\n                value: \"print('Hello, World!')\"\n            }\n        ],\n        java: [\n            {\n                label: \"Main Method\",\n                value: \"public static void main(String[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"System.out.println(\\\"Hello, World!\\\");\"\n            }\n        ],\n        csharp: [\n            {\n                label: \"Main Method\",\n                value: \"static void Main(string[] args) {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"Console.WriteLine(\\\"Hello, World!\\\");\"\n            }\n        ],\n        c: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"printf(\\\"Hello, World!\\\\n\\\");\"\n            }\n        ],\n        cpp: [\n            {\n                label: \"Main Function\",\n                value: \"int main() {\\n    // code\\n    return 0;\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for (int i = 0; i < 10; i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if (condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"std::cout << \\\"Hello, World!\\\" << std::endl;\"\n            }\n        ],\n        go: [\n            {\n                label: \"Main Function\",\n                value: \"func main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i := 0; i < 10; i++ {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"fmt.Println(\\\"Hello, World!\\\")\"\n            }\n        ],\n        ruby: [\n            {\n                label: \"Method\",\n                value: \"def my_method\\n  # code\\nend\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..9\\n  # code\\nend\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition\\n  # code\\nend\"\n            },\n            {\n                label: \"Print\",\n                value: \"puts 'Hello, World!'\"\n            }\n        ],\n        rust: [\n            {\n                label: \"Main Function\",\n                value: \"fn main() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for i in 0..10 {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if condition {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"println!(\\\"Hello, World!\\\");\"\n            }\n        ],\n        php: [\n            {\n                label: \"Function\",\n                value: \"function myFunction() {\\n    // code\\n}\"\n            },\n            {\n                label: \"For Loop\",\n                value: \"for ($i = 0; $i < 10; $i++) {\\n    // code\\n}\"\n            },\n            {\n                label: \"If Statement\",\n                value: \"if ($condition) {\\n    // code\\n}\"\n            },\n            {\n                label: \"Print\",\n                value: \"echo 'Hello, World!';\"\n            }\n        ]\n    };\n    // Declare the `socketService` instance at the top of the file\n    const socketService = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n    // Leave room handler\n    const handleLeaveRoom = ()=>{\n        socketService.leaveRoom(roomId);\n        router.push(\"/dashboard\");\n    };\n    // Handle editor mounting\n    const handleEditorDidMount = (editor)=>{\n        editorRef.current = editor;\n        setIsLoading(false);\n        // Auto-focus the editor after mounting\n        setTimeout(()=>{\n            if (editor) {\n                editor.focus();\n                // Add keyboard shortcut (Ctrl+Enter) to run code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.CtrlCmd | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.Enter, ()=>{\n                    executeCode();\n                });\n                // Add keyboard shortcut (Shift+Alt+F) to format code\n                editor.addCommand(monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Shift | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyMod.Alt | monaco_editor__WEBPACK_IMPORTED_MODULE_3__.KeyCode.KeyF, ()=>{\n                    formatCurrentCode();\n                });\n            }\n        }, 500) // Small delay to ensure the editor is fully ready\n        ;\n    };\n    // Track if the change is from a remote update\n    const isRemoteUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Create a throttled version of the code change handler\n    // This prevents sending too many updates when typing quickly\n    // but ensures updates are sent at regular intervals\n    const throttledCodeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(lodash_throttle__WEBPACK_IMPORTED_MODULE_5___default()({\n        \"CodeEditor.useRef\": (code)=>{\n            if (roomId && isConnected) {\n                console.log(\"Sending throttled code update to room \".concat(roomId, \", length: \").concat(code.length));\n                socketService.sendCodeChange(roomId, code);\n            }\n        }\n    }[\"CodeEditor.useRef\"], 50) // Use throttle with a short interval for more responsive updates\n    ).current;\n    // Create a debounced version of the typing notification\n    const debouncedTypingNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_6___default()({\n        \"CodeEditor.useCallback[debouncedTypingNotification]\": ()=>{\n            if (roomId && isConnected) {\n                // Always send the current username with typing notifications\n                console.log(\"Sending typing notification with username: \".concat(username));\n                socketService.sendTyping(roomId, username);\n            }\n        }\n    }[\"CodeEditor.useCallback[debouncedTypingNotification]\"], 1000), [\n        roomId,\n        isConnected,\n        username\n    ]);\n    // Handle editor change\n    const handleEditorChange = (value)=>{\n        if (typeof value !== \"string\") return;\n        // Make sure loading is off during code changes\n        setIsLoading(false);\n        // If this change is from a remote update, just update the state and return\n        if (isRemoteUpdate.current) {\n            console.log(\"Handling remote update, not emitting\");\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            isRemoteUpdate.current = false;\n            return;\n        }\n        // Only process if the value actually changed\n        if (value !== latestCodeRef.current) {\n            // Update local state immediately\n            setCode(value);\n            latestCodeRef.current = value; // Update the ref\n            // Send typing notification (debounced)\n            debouncedTypingNotification();\n            // Send code update (throttled)\n            throttledCodeChange(value);\n            // If not connected, try to reconnect\n            if (!isConnected) {\n                console.log(\"Socket not connected, attempting to reconnect\");\n                socketService.connect();\n            }\n        }\n    };\n    // Copy code to clipboard\n    const copyCodeToClipboard = ()=>{\n        if (navigator.clipboard && code) {\n            navigator.clipboard.writeText(code).then(()=>{\n                setCopySuccess(true);\n                setTimeout(()=>setCopySuccess(false), 2000);\n            }).catch((err)=>{\n                console.error('Failed to copy code: ', err);\n            });\n        }\n    };\n    // Toggle user list\n    const toggleUserList = ()=>{\n        setShowUserList((prev)=>!prev);\n    };\n    // Change language\n    const changeLanguage = (lang)=>{\n        setLanguage(lang);\n        // Log available runtimes for this language\n        if (runtimesLoaded && runtimes.length > 0) {\n            const availableRuntimes = runtimes.filter((runtime)=>runtime.language === lang || runtime.aliases && runtime.aliases.includes(lang));\n            if (availableRuntimes.length > 0) {\n                console.log(\"Available runtimes for \".concat(lang, \":\"), availableRuntimes);\n            } else {\n                console.log(\"No runtimes available for \".concat(lang));\n            }\n        }\n    };\n    // Format code\n    const formatCurrentCode = ()=>{\n        if (!code || !editorRef.current) return;\n        try {\n            // Format the code based on the current language\n            const formattedCode = (0,_utils_formatCode__WEBPACK_IMPORTED_MODULE_8__.formatCode)(code, language);\n            // Only update if the code actually changed\n            if (formattedCode !== code) {\n                // Update the editor\n                const model = editorRef.current.getModel();\n                if (model) {\n                    // Store current cursor position/selection\n                    const currentPosition = editorRef.current.getPosition();\n                    const currentSelection = editorRef.current.getSelection();\n                    // Update the editor content\n                    editorRef.current.executeEdits('format', [\n                        {\n                            range: model.getFullModelRange(),\n                            text: formattedCode,\n                            forceMoveMarkers: true\n                        }\n                    ]);\n                    // Restore cursor position if possible\n                    if (currentPosition) {\n                        editorRef.current.setPosition(currentPosition);\n                    }\n                    if (currentSelection) {\n                        editorRef.current.setSelection(currentSelection);\n                    }\n                    // Update state and ref\n                    setCode(formattedCode);\n                    latestCodeRef.current = formattedCode;\n                    // Send the formatted code to other users\n                    if (roomId && isConnected) {\n                        console.log(\"Sending formatted code to room \".concat(roomId, \", length: \").concat(formattedCode.length));\n                        socketService.sendCodeChange(roomId, formattedCode);\n                    }\n                    // Show a success message\n                    console.log('Code formatted successfully');\n                }\n            } else {\n                console.log('Code is already formatted');\n            }\n        } catch (error) {\n            console.error('Error formatting code:', error);\n        }\n    };\n    // Clear execution output\n    const clearOutput = ()=>{\n        setExecutionOutput(null);\n        setExecutionError(null);\n    };\n    // Fetch and store available runtimes from Piston API\n    const fetchRuntimes = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get('https://emkc.org/api/v2/piston/runtimes');\n            console.log('Piston API status:', response.status);\n            console.log('Available runtimes:', response.data);\n            // Store the runtimes in state\n            setRuntimes(response.data);\n            setRuntimesLoaded(true);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error fetching Piston API runtimes:', error);\n            return {\n                success: false,\n                error: axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) ? \"\".concat(error.message, \" - \").concat(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) || 'Unknown', \" \").concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText) || '') : String(error)\n            };\n        }\n    };\n    // Check if the Piston API is available\n    const checkPistonAPI = async ()=>{\n        // If we haven't loaded runtimes yet, fetch them\n        if (!runtimesLoaded) {\n            return await fetchRuntimes();\n        }\n        // If we already have runtimes, just return success\n        if (runtimes.length > 0) {\n            return {\n                success: true,\n                data: runtimes\n            };\n        }\n        // If we've tried to load runtimes but have none, try again\n        return await fetchRuntimes();\n    };\n    // Test the Piston API with a hardcoded sample payload\n    const testPistonAPI = async ()=>{\n        setIsExecuting(true);\n        setExecutionError(null);\n        setExecutionOutput(null);\n        setShowOutput(true);\n        try {\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Find JavaScript runtime\n            const jsRuntimes = apiStatus.data.filter((runtime)=>runtime.language === \"javascript\" || runtime.aliases && runtime.aliases.includes(\"javascript\"));\n            if (jsRuntimes.length === 0) {\n                setExecutionError('No JavaScript runtime found. Please try a different language.');\n                return;\n            }\n            const jsRuntime = jsRuntimes[0];\n            console.log(\"Using JavaScript runtime: \".concat(jsRuntime.language, \" \").concat(jsRuntime.version));\n            // Sample JavaScript code that should work\n            const samplePayload = {\n                language: jsRuntime.language,\n                version: jsRuntime.version,\n                files: [\n                    {\n                        name: \"main.js\",\n                        content: \"console.log('Hello, World!');\"\n                    }\n                ],\n                stdin: \"\",\n                args: []\n            };\n            console.log('Testing Piston API with sample payload:', JSON.stringify(samplePayload, null, 2));\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', samplePayload);\n            console.log('Sample execution response:', response.data);\n            if (response.data.run) {\n                setExecutionOutput(\"API Test Successful!\\n\\n\" + \"Output: \" + response.data.run.stdout + \"\\n\\n\" + \"The API is working correctly. You can now run your own code.\");\n            } else {\n                setExecutionError('API test failed. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error testing Piston API:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                setExecutionError(\"API Test Failed: \".concat(error.response.status, \" \").concat(error.response.statusText, \"\\n\\n\") + JSON.stringify(error.response.data, null, 2));\n            } else {\n                setExecutionError(error instanceof Error ? \"API Test Error: \".concat(error.message) : 'An unknown error occurred while testing the API.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // Execute code using Piston API\n    const executeCode = async ()=>{\n        if (!code || isExecuting) return;\n        // Map Monaco editor language to Piston API language\n        const languageMap = {\n            javascript: \"javascript\",\n            typescript: \"typescript\",\n            python: \"python3\",\n            python3: \"python3\",\n            java: \"java\",\n            csharp: \"csharp\",\n            c: \"c\",\n            cpp: \"cpp\",\n            go: \"go\",\n            ruby: \"ruby\",\n            rust: \"rust\",\n            php: \"php\"\n        };\n        // Get the mapped language\n        const pistonLanguage = languageMap[language] || language;\n        // Check if we have runtimes loaded\n        if (!runtimesLoaded || runtimes.length === 0) {\n            setExecutionError('Runtimes not loaded yet. Please try again in a moment.');\n            setShowOutput(true);\n            return;\n        }\n        // Find available runtimes for the selected language\n        const availableRuntimes = runtimes.filter((runtime)=>runtime.language === pistonLanguage || runtime.aliases && runtime.aliases.includes(pistonLanguage));\n        // Check if the language is supported\n        if (availableRuntimes.length === 0) {\n            // Get a list of supported languages from the runtimes\n            const supportedLanguages = [\n                ...new Set(runtimes.flatMap((runtime)=>[\n                        runtime.language,\n                        ...runtime.aliases || []\n                    ]))\n            ].sort();\n            setExecutionError(\"The language '\".concat(language, \"' (mapped to '\").concat(pistonLanguage, \"') is not supported by the Piston API.\\n\\n\") + \"Supported languages: \".concat(supportedLanguages.join(', ')));\n            setShowOutput(true);\n            return;\n        }\n        // Get the latest version of the runtime\n        const selectedRuntime = availableRuntimes[0];\n        try {\n            setIsExecuting(true);\n            setExecutionError(null);\n            setExecutionOutput(null);\n            setShowOutput(true);\n            // First check if the API is available\n            const apiStatus = await checkPistonAPI();\n            if (!apiStatus.success) {\n                setExecutionError(\"API Check Failed: \".concat(apiStatus.error, \"\\nThe Piston API might be down or unreachable.\"));\n                return;\n            }\n            // Determine file extension based on language\n            let fileExtension = '';\n            if (selectedRuntime.language === 'python3' || selectedRuntime.language === 'python') fileExtension = '.py';\n            else if (selectedRuntime.language === 'javascript') fileExtension = '.js';\n            else if (selectedRuntime.language === 'typescript') fileExtension = '.ts';\n            else if (selectedRuntime.language === 'java') fileExtension = '.java';\n            else if (selectedRuntime.language === 'csharp') fileExtension = '.cs';\n            else if (selectedRuntime.language === 'c') fileExtension = '.c';\n            else if (selectedRuntime.language === 'cpp') fileExtension = '.cpp';\n            else if (selectedRuntime.language === 'go') fileExtension = '.go';\n            else if (selectedRuntime.language === 'rust') fileExtension = '.rs';\n            else if (selectedRuntime.language === 'ruby') fileExtension = '.rb';\n            else if (selectedRuntime.language === 'php') fileExtension = '.php';\n            else fileExtension = \".\".concat(selectedRuntime.language);\n            console.log(\"Selected runtime: \".concat(selectedRuntime.language, \" \").concat(selectedRuntime.version));\n            // Prepare the payload according to Piston API documentation\n            const payload = {\n                language: selectedRuntime.language,\n                version: selectedRuntime.version,\n                files: [\n                    {\n                        name: \"main\".concat(fileExtension),\n                        content: code\n                    }\n                ],\n                stdin: '',\n                args: [],\n                compile_timeout: 10000,\n                run_timeout: 5000\n            };\n            // Log the payload for debugging\n            console.log(\"Executing \".concat(pistonLanguage, \" code with payload:\"), JSON.stringify(payload, null, 2));\n            // Make the API request\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post('https://emkc.org/api/v2/piston/execute', payload);\n            console.log('Execution response:', response.data);\n            const result = response.data;\n            if (result.run) {\n                // Format the output\n                let output = '';\n                let hasOutput = false;\n                // Add compile output if available (for compiled languages)\n                if (result.compile && result.compile.stderr) {\n                    output += \"Compilation output:\\n\".concat(result.compile.stderr, \"\\n\\n\");\n                    hasOutput = true;\n                }\n                // Add standard output\n                if (result.run.stdout) {\n                    output += result.run.stdout;\n                    hasOutput = true;\n                }\n                // Add error output\n                if (result.run.stderr) {\n                    if (hasOutput) output += '\\n';\n                    output += \"Error output:\\n\".concat(result.run.stderr);\n                    hasOutput = true;\n                }\n                // Add exit code if non-zero\n                if (result.run.code !== 0) {\n                    if (hasOutput) output += '\\n';\n                    output += \"\\nProcess exited with code \".concat(result.run.code);\n                    hasOutput = true;\n                }\n                if (!hasOutput) {\n                    output = 'Program executed successfully with no output.';\n                }\n                setExecutionOutput(output);\n            } else {\n                setExecutionError('Failed to execute code. No run data returned.');\n            }\n        } catch (error) {\n            console.error('Error executing code:', error);\n            // Handle Axios errors with more detailed information\n            if (axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].isAxiosError(error) && error.response) {\n                const statusCode = error.response.status;\n                const responseData = error.response.data;\n                console.error('API Error Details:', {\n                    status: statusCode,\n                    data: responseData\n                });\n                // Format a more helpful error message\n                if (statusCode === 400) {\n                    setExecutionError(\"API Error (400 Bad Request): \".concat(JSON.stringify(responseData), \"\\n\\n\") + 'This usually means the API request format is incorrect. ' + 'Please check the console for more details.');\n                } else if (statusCode === 429) {\n                    setExecutionError('Rate limit exceeded. Please try again later.');\n                } else {\n                    setExecutionError(\"API Error (\".concat(statusCode, \"): \").concat(JSON.stringify(responseData), \"\\n\\n\") + 'Please check the console for more details.');\n                }\n            } else {\n                // Handle non-Axios errors\n                setExecutionError(error instanceof Error ? \"Error: \".concat(error.message) : 'An unknown error occurred while executing the code.');\n            }\n        } finally{\n            setIsExecuting(false);\n        }\n    };\n    // This function will be called when we receive a code update from another user\n    const handleCodeUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleCodeUpdate]\": (incomingCode)=>{\n            console.log(\"Remote code update received, length:\", incomingCode.length);\n            // Make sure loading is off during code updates\n            setIsLoading(false);\n            // Only update if the code is different from our latest code\n            if (incomingCode !== latestCodeRef.current) {\n                try {\n                    // Set the flag to indicate this is a remote update\n                    isRemoteUpdate.current = true;\n                    // Update the editor if it's mounted\n                    if (editorRef.current) {\n                        console.log(\"Updating editor with remote code\");\n                        // Use the editor's model to update the value to preserve cursor position\n                        const model = editorRef.current.getModel();\n                        if (model) {\n                            // Store current cursor position/selection\n                            const currentPosition = editorRef.current.getPosition();\n                            const currentSelection = editorRef.current.getSelection();\n                            // Use executeEdits with better handling of cursor position\n                            editorRef.current.executeEdits('remote', [\n                                {\n                                    range: model.getFullModelRange(),\n                                    text: incomingCode,\n                                    forceMoveMarkers: true\n                                }\n                            ]);\n                            // Restore cursor position if possible\n                            if (currentPosition) {\n                                editorRef.current.setPosition(currentPosition);\n                            }\n                            if (currentSelection) {\n                                editorRef.current.setSelection(currentSelection);\n                            }\n                            // Also update our state and ref\n                            setCode(incomingCode);\n                            latestCodeRef.current = incomingCode;\n                        }\n                    } else {\n                        // If editor isn't mounted yet, just update the state and ref\n                        console.log(\"Editor not mounted, updating state only\");\n                        setCode(incomingCode);\n                        latestCodeRef.current = incomingCode;\n                        isRemoteUpdate.current = false;\n                    }\n                } catch (error) {\n                    console.error(\"Error updating editor with remote code:\", error);\n                    isRemoteUpdate.current = false;\n                }\n            } else {\n                console.log(\"Remote code matches current code, ignoring\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleCodeUpdate]\"], []);\n    // State to track cursor positions\n    const [cursorPositions, setCursorPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Emit cursor movement\n    const handleMouseMove = (e)=>{\n        const position = {\n            x: e.clientX,\n            y: e.clientY\n        };\n        _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().sendCursorMove(roomId, username, position);\n    };\n    // Listen for cursor movement\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            const handleCursorMove = {\n                \"CodeEditor.useEffect.handleCursorMove\": (param)=>{\n                    let { userId, position } = param;\n                    setCursorPositions({\n                        \"CodeEditor.useEffect.handleCursorMove\": (prev)=>({\n                                ...prev,\n                                [userId]: position\n                            })\n                    }[\"CodeEditor.useEffect.handleCursorMove\"]);\n                }\n            }[\"CodeEditor.useEffect.handleCursorMove\"];\n            _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().on(\"cursor-move\", handleCursorMove);\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance().off(\"cursor-move\", handleCursorMove);\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId\n    ]);\n    // Render cursors\n    const renderCursors = ()=>{\n        return Object.entries(cursorPositions).map((param)=>{\n            let [userId, position] = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    left: position.x,\n                    top: position.y,\n                    backgroundColor: \"red\",\n                    width: 10,\n                    height: 10,\n                    borderRadius: \"50%\",\n                    pointerEvents: \"none\"\n                }\n            }, userId, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 720,\n                columnNumber: 7\n            }, this);\n        });\n    };\n    // Fetch runtimes when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            fetchRuntimes();\n        }\n    }[\"CodeEditor.useEffect\"], []);\n    // Handle request for initial code from new users\n    const handleGetInitialCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleGetInitialCode]\": (data)=>{\n            console.log(\"Received request for initial code from \".concat(data.requestingUsername, \" (\").concat(data.requestingUserId, \")\"));\n            // Only respond if we have code and we're connected\n            if (roomId && latestCodeRef.current && latestCodeRef.current.trim() !== \"// Start coding...\") {\n                const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                console.log(\"Sending initial code to \".concat(data.requestingUsername, \", length: \").concat(latestCodeRef.current.length));\n                socketServiceInstance.sendInitialCode(roomId, latestCodeRef.current, data.requestingUserId);\n            } else {\n                console.log(\"Not sending initial code - no meaningful code to share or not in room\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleGetInitialCode]\"], [\n        roomId\n    ]);\n    // Handle receiving initial code as a new user\n    const handleInitialCodeReceived = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CodeEditor.useCallback[handleInitialCodeReceived]\": (data)=>{\n            console.log(\"Received initial code, length: \".concat(data.code.length));\n            // Only apply initial code if we don't have meaningful code yet\n            // This prevents overwriting code that the user might have already started typing\n            if (latestCodeRef.current === \"// Start coding...\" || latestCodeRef.current.trim() === \"\") {\n                console.log(\"Applying received initial code\");\n                // Set the flag to indicate this is a remote update\n                isRemoteUpdate.current = true;\n                // Update the code state and ref\n                setCode(data.code);\n                latestCodeRef.current = data.code;\n                // Update the editor if it's mounted\n                if (editorRef.current) {\n                    editorRef.current.setValue(data.code);\n                }\n            } else {\n                console.log(\"Not applying initial code - user already has meaningful code\");\n            }\n        }\n    }[\"CodeEditor.useCallback[handleInitialCodeReceived]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CodeEditor.useEffect\": ()=>{\n            // Handle user typing notifications\n            const handleUserTyping = {\n                \"CodeEditor.useEffect.handleUserTyping\": (param)=>{\n                    let { username, userId } = param;\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    // Don't show typing indicator for current user\n                    if (userId && userId === currentUserId) {\n                        return;\n                    }\n                    // Use the exact username without any modifications\n                    // This ensures we display exactly what the user entered on the dashboard\n                    setTypingUser(username);\n                    console.log(\"User typing: \".concat(username));\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserTyping\": ()=>setTypingUser(null)\n                    }[\"CodeEditor.useEffect.handleUserTyping\"], 2000);\n                }\n            }[\"CodeEditor.useEffect.handleUserTyping\"];\n            // Handle user list updates from both user-joined/user-left and room-users-updated events\n            const handleUserListUpdate = {\n                \"CodeEditor.useEffect.handleUserListUpdate\": (data)=>{\n                    console.log('User list update received:', data);\n                    let users = [];\n                    // Handle different event formats\n                    if (Array.isArray(data)) {\n                        // This is from user-joined or user-left events\n                        users = data;\n                    } else if (data && Array.isArray(data.users)) {\n                        // This is from room-users-updated event\n                        users = data.users.map({\n                            \"CodeEditor.useEffect.handleUserListUpdate\": (user)=>{\n                                if (typeof user === 'string') {\n                                    // If it's just a username string, convert to user object\n                                    return {\n                                        username: user\n                                    };\n                                }\n                                return user;\n                            }\n                        }[\"CodeEditor.useEffect.handleUserListUpdate\"]);\n                    }\n                    console.log('Processed users:', users);\n                    console.log('Current username state:', username);\n                    console.log('Stored username:', localStorage.getItem('username'));\n                    // Get current userId from localStorage\n                    const currentUserId = localStorage.getItem('userId');\n                    console.log('Current userId from localStorage:', currentUserId);\n                    // Filter out any invalid users and map to display names\n                    const usernames = users.filter({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>user && user.username\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]).map({\n                        \"CodeEditor.useEffect.handleUserListUpdate.usernames\": (user)=>{\n                            const displayName = user.username;\n                            const isCurrentUser = user.userId === currentUserId;\n                            return isCurrentUser ? \"\".concat(displayName, \" (you)\") : displayName;\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate.usernames\"]);\n                    console.log('Setting active users:', usernames);\n                    // Only update if we have users to display\n                    if (usernames.length > 0) {\n                        setActiveUsers(usernames);\n                    } else if (activeUsers.length === 0) {\n                        // If we don't have users from the server but we're connected, add ourselves\n                        const storedUsername = localStorage.getItem('username');\n                        if (storedUsername) {\n                            setActiveUsers([\n                                \"\".concat(storedUsername, \" (you)\")\n                            ]);\n                        }\n                    }\n                    // Log the current state of the active users list\n                    setTimeout({\n                        \"CodeEditor.useEffect.handleUserListUpdate\": ()=>{\n                            console.log('Active users state after update:', activeUsers);\n                        }\n                    }[\"CodeEditor.useEffect.handleUserListUpdate\"], 100);\n                }\n            }[\"CodeEditor.useEffect.handleUserListUpdate\"];\n            // Register event listeners\n            const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n            socketServiceInstance.on('code-update', handleCodeUpdate);\n            socketServiceInstance.on('user-typing', handleUserTyping);\n            socketServiceInstance.on('user-joined', handleUserListUpdate);\n            socketServiceInstance.on('user-left', handleUserListUpdate);\n            socketServiceInstance.on('room-users-updated', handleUserListUpdate);\n            socketServiceInstance.on('get-initial-code', handleGetInitialCode);\n            socketServiceInstance.on('initial-code-received', handleInitialCodeReceived);\n            // Join the room when component mounts\n            if (roomId) {\n                console.log(\"Joining room:\", roomId);\n                // Only show loading on initial join, not for code updates\n                if (!editorRef.current) {\n                    setIsLoading(true);\n                }\n                const joinRoom = {\n                    \"CodeEditor.useEffect.joinRoom\": async ()=>{\n                        try {\n                            if (socketServiceInstance.isConnected()) {\n                                console.log(\"Socket connected, joining room\");\n                                const { username: validatedUsername, users } = await socketServiceInstance.joinRoom(roomId, username);\n                                console.log(\"Successfully joined room:\", roomId);\n                                console.log(\"Users in room:\", users);\n                                if (validatedUsername !== username) {\n                                    console.log(\"Server validated username from \".concat(username, \" to \").concat(validatedUsername));\n                                    setUsername(validatedUsername);\n                                    localStorage.setItem('username', validatedUsername);\n                                }\n                                if (users && Array.isArray(users)) {\n                                    console.log('Setting active users from join response');\n                                    handleUserListUpdate(users);\n                                }\n                                setIsLoading(false);\n                            } else {\n                                console.log(\"Socket not connected, trying to connect...\");\n                                await new Promise({\n                                    \"CodeEditor.useEffect.joinRoom\": (resolve)=>socketServiceInstance.onConnect({\n                                            \"CodeEditor.useEffect.joinRoom\": ()=>resolve()\n                                        }[\"CodeEditor.useEffect.joinRoom\"])\n                                }[\"CodeEditor.useEffect.joinRoom\"]);\n                                // After connect, try join again\n                                await joinRoom();\n                                return;\n                            }\n                        } catch (error) {\n                            console.error(\"Error joining room:\", error);\n                            setIsLoading(false);\n                        }\n                    }\n                }[\"CodeEditor.useEffect.joinRoom\"];\n                // Start the join process\n                joinRoom();\n                // Set a timeout to hide the loading screen even if the join fails\n                setTimeout({\n                    \"CodeEditor.useEffect\": ()=>{\n                        setIsLoading(false);\n                    }\n                }[\"CodeEditor.useEffect\"], 3000);\n            }\n            // Clean up event listeners when component unmounts\n            return ({\n                \"CodeEditor.useEffect\": ()=>{\n                    // Use an instance of SocketService\n                    const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n                    socketServiceInstance.off('code-update', handleCodeUpdate);\n                    socketServiceInstance.off('user-typing', handleUserTyping);\n                    socketServiceInstance.off('user-joined', handleUserListUpdate);\n                    socketServiceInstance.off('user-left', handleUserListUpdate);\n                    socketServiceInstance.off('room-users-updated', handleUserListUpdate);\n                    socketServiceInstance.off('get-initial-code', handleGetInitialCode);\n                    socketServiceInstance.off('initial-code-received', handleInitialCodeReceived);\n                    // Leave the room when component unmounts\n                    if (roomId) {\n                        socketServiceInstance.leaveRoom(roomId);\n                    }\n                }\n            })[\"CodeEditor.useEffect\"];\n        }\n    }[\"CodeEditor.useEffect\"], [\n        roomId,\n        initialUsername,\n        username,\n        handleCodeUpdate,\n        handleGetInitialCode,\n        handleInitialCodeReceived\n    ]);\n    // Download code as file\n    const handleDownload = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"code-\".concat(roomId || \"session\", \".txt\");\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    // Upload code from file\n    const handleUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            var _event_target;\n            if (typeof ((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result) === \"string\") {\n                setCode(event.target.result);\n                latestCodeRef.current = event.target.result;\n            }\n        };\n        reader.readAsText(file);\n    };\n    // Show Monaco's find/replace dialog\n    const handleFindReplace = ()=>{\n        if (editorRef.current) {\n            const action = editorRef.current.getAction(\"actions.find\");\n            if (action) action.run();\n        }\n    };\n    // Insert code snippet\n    const insertSnippet = (snippet)=>{\n        if (editorRef.current) {\n            const model = editorRef.current.getModel();\n            const position = editorRef.current.getPosition();\n            if (model && position) {\n                editorRef.current.executeEdits(\"snippet\", [\n                    {\n                        range: new monaco_editor__WEBPACK_IMPORTED_MODULE_3__.Range(position.lineNumber, position.column, position.lineNumber, position.column),\n                        text: snippet,\n                        forceMoveMarkers: true\n                    }\n                ]);\n                editorRef.current.focus();\n            }\n        }\n    };\n    // Use the instance of SocketService for the connect method\n    const handleReconnect = ()=>{\n        const socketServiceInstance = _services_socketService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getInstance();\n        socketServiceInstance.connect();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full h-[80vh] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-800 shadow-xl flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"bg-gray-100 dark:bg-zinc-800 px-4 py-2 flex flex-wrap items-center justify-between border-b border-gray-200 dark:border-gray-700 gap-2 min-h-[48px]\",\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    style: {\n                        zIndex: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-3 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 min-w-[90px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1006,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-400 truncate\",\n                                            children: isConnected ? 'Connected' : 'Disconnected'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded ml-1 transition-colors\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1011,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1005,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: language,\n                                    onChange: (e)=>changeLanguage(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"javascript\",\n                                            children: \"JavaScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1026,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"typescript\",\n                                            children: \"TypeScript\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1027,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"python3\",\n                                            children: \"Python\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1028,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"java\",\n                                            children: \"Java\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1029,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"csharp\",\n                                            children: \"C#\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1030,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"c\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1031,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"cpp\",\n                                            children: \"C++\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"go\",\n                                            children: \"Go\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ruby\",\n                                            children: \"Ruby\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1034,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rust\",\n                                            children: \"Rust\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1035,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"php\",\n                                            children: \"PHP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1036,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: formatCurrentCode,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm bg-blue-600 hover:bg-blue-700 text-white transition-colors whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiAlignLeft, {\n                                            size: 14\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1046,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Format\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1047,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1040,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: executeCode,\n                                    disabled: isExecuting,\n                                    className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm \".concat(isExecuting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700', \" text-white transition-colors whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: isExecuting ? 1 : 1.05\n                                    },\n                                    whileTap: {\n                                        scale: isExecuting ? 1 : 0.95\n                                    },\n                                    children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                                className: \"animate-spin\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1060,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Running...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1061,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiPlay, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1065,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Run Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1066,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1051,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Room:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1073,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-white dark:bg-zinc-700 px-2 py-1 rounded font-mono text-xs truncate max-w-[120px]\",\n                                            children: roomId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1074,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                            onClick: copyCodeToClipboard,\n                                            className: \"text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            title: \"Copy code to clipboard\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCopy, {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1082,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1075,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1072,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1003,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap items-center gap-2 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowOutput((prev)=>!prev),\n                                    className: \"flex items-center space-x-1 text-sm \".concat(showOutput ? 'text-blue-500 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300', \" hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\"),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiCode, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1095,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Output\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1096,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1089,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: toggleUserList,\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUsers, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                activeUsers.length,\n                                                \" online\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1107,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"flex items-center space-x-1 text-sm text-yellow-600 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSun, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1118,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiMoon, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1118,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setMinimapEnabled((v)=>!v),\n                                    className: \"flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Toggle Minimap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Minimap\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1129,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleFindReplace,\n                                    className: \"flex items-center space-x-1 text-sm text-green-600 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Find/Replace\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiSearch, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1140,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleDownload,\n                                    className: \"flex items-center space-x-1 text-sm text-indigo-600 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Download Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiDownload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1151,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"flex items-center space-x-1 text-sm text-pink-600 dark:text-pink-300 bg-pink-100 dark:bg-pink-900/30 hover:bg-pink-200 dark:hover:bg-pink-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Upload Code\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiUpload, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1162,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \".js,.ts,.py,.java,.c,.cpp,.go,.rb,.rs,.php,.txt\",\n                                    style: {\n                                        display: \"none\"\n                                    },\n                                    onChange: handleUpload\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    onChange: (e)=>e.target.value && insertSnippet(e.target.value),\n                                    className: \"bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[100px]\",\n                                    defaultValue: \"\",\n                                    title: \"Insert Snippet\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Snippets\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1179,\n                                            columnNumber: 15\n                                        }, this),\n                                        (SNIPPETS[language] || SNIPPETS['javascript']).map((snippet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: snippet.value,\n                                                children: snippet.label\n                                            }, snippet.label, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1181,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>setShowShortcuts(true),\n                                    className: \"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors px-2 py-1 rounded-md whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    title: \"Keyboard Shortcuts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiHelpCircle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1193,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: handleLeaveRoom,\n                                    className: \"flex items-center space-x-1 text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors px-2 py-1 rounded-md ml-2 whitespace-nowrap\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Leave Room\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1203,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1197,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1087,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 996,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showUserList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute right-0 top-12 bg-white dark:bg-zinc-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 w-48 overflow-hidden\",\n                        initial: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            height: 'auto'\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 border-b border-gray-200 dark:border-gray-700 font-medium text-sm text-gray-600 dark:text-gray-300\",\n                                children: \"Active Users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1218,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"max-h-48 overflow-y-auto\",\n                                children: activeUsers.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.li, {\n                                        className: \"px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-zinc-700 flex items-center space-x-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full bg-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1230,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: user\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1231,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1223,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1221,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1211,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1209,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    className: \"h-[calc(100%-40px)] \".concat(showOutput ? 'pb-[33%]' : ''),\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.4,\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                height: \"100%\",\n                                defaultLanguage: language,\n                                defaultValue: code,\n                                onChange: handleEditorChange,\n                                onMount: handleEditorDidMount,\n                                theme: theme === \"dark\" ? \"vs-dark\" : \"light\",\n                                options: {\n                                    minimap: {\n                                        enabled: minimapEnabled\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1249,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1248,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1246,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1240,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: typingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 left-4 text-sm text-gray-400 bg-gray-800/80 px-3 py-1 rounded-full backdrop-blur-sm\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            typingUser,\n                            \" is typing...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1265,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1263,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-0 left-0 right-0 bg-gray-900 text-white border-t border-gray-700 h-1/3 overflow-hidden flex flex-col\",\n                        initial: {\n                            height: 0\n                        },\n                        animate: {\n                            height: '33%'\n                        },\n                        exit: {\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 bg-gray-800 border-b border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1288,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearOutput,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1290,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: testPistonAPI,\n                                                disabled: isExecuting,\n                                                className: \"text-xs text-blue-400 hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"Test API\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1297,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowOutput(false),\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                lineNumber: 1304,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 1289,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1287,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-auto font-mono text-sm whitespace-pre-wrap\",\n                                children: isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlignLeft_FiCode_FiCopy_FiDownload_FiHelpCircle_FiLoader_FiMoon_FiPlay_FiSearch_FiSun_FiUpload_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_13__.FiLoader, {\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1315,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Executing code...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1316,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1314,\n                                    columnNumber: 19\n                                }, this) : executionError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400\",\n                                    children: executionError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1319,\n                                    columnNumber: 19\n                                }, this) : executionOutput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: executionOutput\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1321,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: 'Click \"Run Code\" to execute your code.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1323,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 1312,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1280,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1278,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: copySuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"absolute bottom-4 right-4 text-sm text-white bg-green-500 px-3 py-1 rounded-full shadow-lg\",\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: \"Copied to clipboard!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1333,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1331,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                    children: showShortcuts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/60\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 max-w-md w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-700 dark:hover:text-white\",\n                                    onClick: ()=>setShowShortcuts(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1355,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    children: \"Keyboard Shortcuts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1361,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Run Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1363,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+Enter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1363,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Format Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1364,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Shift+Alt+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1364,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Find/Replace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1365,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Ctrl+F\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1365,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Download Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1366,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+D\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1366,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Upload Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1367,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+U\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1367,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Minimap:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1368,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+M\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1368,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Toggle Theme:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1369,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+T\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1369,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                    children: \"Show Shortcuts:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                                    lineNumber: 1370,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" Alt+H\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                            lineNumber: 1370,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 1362,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 1354,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 1348,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n                    lineNumber: 1346,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n            lineNumber: 994,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 992,\n        columnNumber: 5\n    }, this);\n} // Keyboard shortcuts global handler\n // useEffect(() => {\n //   const handler = (e: KeyboardEvent) => {\n //     if (e.altKey && e.key.toLowerCase() === \"d\") {\n //       e.preventDefault();\n //       handleDownload();\n //     } else if (e.altKey && e.key.toLowerCase() === \"u\") {\n //       e.preventDefault();\n //       fileInputRef.current?.click();\n //     } else if (e.altKey && e.key.toLowerCase() === \"m\") {\n //       e.preventDefault();\n //       setMinimapEnabled((v: boolean) => !v);\n //     } else if (e.altKey && e.key.toLowerCase() === \"t\") {\n //       e.preventDefault();\n //       setTheme && setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n //     } else if (e.altKey && e.key.toLowerCase() === \"h\") {\n //       e.preventDefault();\n //       setShowShortcuts(true);\n //     }\n //   };\n //   window.addEventListener(\"keydown\", handler);\n //   return () => window.removeEventListener(\"keydown\", handler);\n // }, [theme, setTheme]);\n_s(CodeEditor, \"j93S6x7QXx83juLRdS1huZklQZk=\", false, function() {\n    return [\n        _context_SocketContext__WEBPACK_IMPORTED_MODULE_7__.useSocket,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _context_ThemeContext__WEBPACK_IMPORTED_MODULE_10__.useTheme\n    ];\n});\n_c = CodeEditor;\nvar _c;\n$RefreshReg$(_c, \"CodeEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CodeEditor.tsx\n"));

/***/ })

});