{"version": 3, "sources": ["../../../locales/zh-CN.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"粘贴\",\n    \"pasteAsPlaintext\": \"粘贴为纯文本\",\n    \"pasteCharts\": \"粘贴图表\",\n    \"selectAll\": \"全部选中\",\n    \"multiSelect\": \"添加元素到选区\",\n    \"moveCanvas\": \"移动画布\",\n    \"cut\": \"剪切\",\n    \"copy\": \"拷贝\",\n    \"copyAsPng\": \"复制为 PNG 到剪贴板\",\n    \"copyAsSvg\": \"复制为 SVG 到剪贴板\",\n    \"copyText\": \"复制文本到剪贴板\",\n    \"copySource\": \"复制源码到剪贴板\",\n    \"convertToCode\": \"转换成代码\",\n    \"bringForward\": \"上移一层\",\n    \"sendToBack\": \"置于底层\",\n    \"bringToFront\": \"置于顶层\",\n    \"sendBackward\": \"下移一层\",\n    \"delete\": \"删除\",\n    \"copyStyles\": \"拷贝样式\",\n    \"pasteStyles\": \"粘贴样式\",\n    \"stroke\": \"描边\",\n    \"background\": \"背景\",\n    \"fill\": \"填充\",\n    \"strokeWidth\": \"描边宽度\",\n    \"strokeStyle\": \"边框样式\",\n    \"strokeStyle_solid\": \"实线\",\n    \"strokeStyle_dashed\": \"虚线\",\n    \"strokeStyle_dotted\": \"点虚线\",\n    \"sloppiness\": \"线条风格\",\n    \"opacity\": \"透明度\",\n    \"textAlign\": \"文本对齐\",\n    \"edges\": \"边角\",\n    \"sharp\": \"尖锐\",\n    \"round\": \"圆润\",\n    \"arrowheads\": \"端点\",\n    \"arrowhead_none\": \"无\",\n    \"arrowhead_arrow\": \"箭头\",\n    \"arrowhead_bar\": \"条状\",\n    \"arrowhead_circle\": \"圆点\",\n    \"arrowhead_circle_outline\": \"圆点（空心）\",\n    \"arrowhead_triangle\": \"三角箭头\",\n    \"arrowhead_triangle_outline\": \"三角箭头（空心）\",\n    \"arrowhead_diamond\": \"菱形\",\n    \"arrowhead_diamond_outline\": \"菱形（空心）\",\n    \"fontSize\": \"字体大小\",\n    \"fontFamily\": \"字体\",\n    \"addWatermark\": \"添加 “使用 Excalidraw 创建” 水印\",\n    \"handDrawn\": \"手写\",\n    \"normal\": \"普通\",\n    \"code\": \"代码\",\n    \"small\": \"小\",\n    \"medium\": \"中\",\n    \"large\": \"大\",\n    \"veryLarge\": \"加大\",\n    \"solid\": \"实心\",\n    \"hachure\": \"线条\",\n    \"zigzag\": \"之字形折线\",\n    \"crossHatch\": \"交叉线条\",\n    \"thin\": \"细\",\n    \"bold\": \"粗\",\n    \"left\": \"左对齐\",\n    \"center\": \"居中\",\n    \"right\": \"右对齐\",\n    \"extraBold\": \"特粗\",\n    \"architect\": \"朴素\",\n    \"artist\": \"艺术\",\n    \"cartoonist\": \"漫画家\",\n    \"fileTitle\": \"文件名\",\n    \"colorPicker\": \"取色器\",\n    \"canvasColors\": \"画布上的\",\n    \"canvasBackground\": \"画布背景\",\n    \"drawingCanvas\": \"绘制 Canvas\",\n    \"layers\": \"图层\",\n    \"actions\": \"操作\",\n    \"language\": \"语言\",\n    \"liveCollaboration\": \"实时协作...\",\n    \"duplicateSelection\": \"复制\",\n    \"untitled\": \"无标题\",\n    \"name\": \"名字\",\n    \"yourName\": \"您的姓名\",\n    \"madeWithExcalidraw\": \"使用 Excalidraw 创建\",\n    \"group\": \"编组\",\n    \"ungroup\": \"解除编组\",\n    \"collaborators\": \"协作者\",\n    \"showGrid\": \"显示网格\",\n    \"addToLibrary\": \"添加到素材库中\",\n    \"removeFromLibrary\": \"从素材库中移除\",\n    \"libraryLoadingMessage\": \"正在加载素材库…\",\n    \"libraries\": \"浏览素材库\",\n    \"loadingScene\": \"正在加载绘图…\",\n    \"align\": \"对齐\",\n    \"alignTop\": \"顶部对齐\",\n    \"alignBottom\": \"底端对齐\",\n    \"alignLeft\": \"左对齐\",\n    \"alignRight\": \"右对齐\",\n    \"centerVertically\": \"垂直居中\",\n    \"centerHorizontally\": \"水平居中\",\n    \"distributeHorizontally\": \"水平等距分布\",\n    \"distributeVertically\": \"垂直等距分布\",\n    \"flipHorizontal\": \"水平翻转\",\n    \"flipVertical\": \"垂直翻转\",\n    \"viewMode\": \"查看模式\",\n    \"share\": \"分享\",\n    \"showStroke\": \"显示描边颜色选择器\",\n    \"showBackground\": \"显示背景颜色选择器\",\n    \"toggleTheme\": \"切换主题\",\n    \"personalLib\": \"个人素材库\",\n    \"excalidrawLib\": \"Excalidraw 素材库\",\n    \"decreaseFontSize\": \"缩小字体大小\",\n    \"increaseFontSize\": \"放大字体大小\",\n    \"unbindText\": \"取消文本绑定\",\n    \"bindText\": \"将文本绑定到容器\",\n    \"createContainerFromText\": \"将文本包围在容器中\",\n    \"link\": {\n      \"edit\": \"编辑链接\",\n      \"editEmbed\": \"编辑链接与嵌入\",\n      \"create\": \"新建链接\",\n      \"createEmbed\": \"创建链接与嵌入\",\n      \"label\": \"链接\",\n      \"labelEmbed\": \"链接与嵌入\",\n      \"empty\": \"未设定链接\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"编辑线条\",\n      \"exit\": \"退出线条编辑\"\n    },\n    \"elementLock\": {\n      \"lock\": \"锁定\",\n      \"unlock\": \"解除锁定\",\n      \"lockAll\": \"全部锁定\",\n      \"unlockAll\": \"全部解锁\"\n    },\n    \"statusPublished\": \"已发布\",\n    \"sidebarLock\": \"侧边栏常驻\",\n    \"selectAllElementsInFrame\": \"选择画框中的所有元素\",\n    \"removeAllElementsFromFrame\": \"分离出画框中的所有元素\",\n    \"eyeDropper\": \"从画布上取色\",\n    \"textToDiagram\": \"文字至图表\",\n    \"prompt\": \"Prompt\"\n  },\n  \"library\": {\n    \"noItems\": \"尚未添加任何项目……\",\n    \"hint_emptyLibrary\": \"选中画布上的项目添加到此处，或从下方的公共素材库中导入。\",\n    \"hint_emptyPrivateLibrary\": \"选中画布上的项目添加到此处。\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"重置画布\",\n    \"exportJSON\": \"导出为文件\",\n    \"exportImage\": \"导出图片...\",\n    \"export\": \"保存到...\",\n    \"copyToClipboard\": \"复制到剪贴板\",\n    \"save\": \"保存至当前文件\",\n    \"saveAs\": \"保存为\",\n    \"load\": \"打开\",\n    \"getShareableLink\": \"获取共享链接\",\n    \"close\": \"关闭\",\n    \"selectLanguage\": \"选择语言\",\n    \"scrollBackToContent\": \"滚动回到内容\",\n    \"zoomIn\": \"放大\",\n    \"zoomOut\": \"缩小\",\n    \"resetZoom\": \"重置缩放\",\n    \"menu\": \"菜单\",\n    \"done\": \"完成\",\n    \"edit\": \"编辑\",\n    \"undo\": \"撤销\",\n    \"redo\": \"重做\",\n    \"resetLibrary\": \"重置素材库\",\n    \"createNewRoom\": \"新建会议室\",\n    \"fullScreen\": \"全屏\",\n    \"darkMode\": \"深色模式\",\n    \"lightMode\": \"浅色模式\",\n    \"zenMode\": \"禅模式\",\n    \"objectsSnapMode\": \"吸附至对象\",\n    \"exitZenMode\": \"退出禅模式\",\n    \"cancel\": \"取消\",\n    \"clear\": \"清除\",\n    \"remove\": \"删除\",\n    \"embed\": \"切换嵌入\",\n    \"publishLibrary\": \"发布\",\n    \"submit\": \"提交\",\n    \"confirm\": \"确定\",\n    \"embeddableInteractionButton\": \"点击以开始交互\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"这将会清除整个画布。您是否要继续?\",\n    \"couldNotCreateShareableLink\": \"无法创建共享链接\",\n    \"couldNotCreateShareableLinkTooBig\": \"无法创建可共享链接：画布过大\",\n    \"couldNotLoadInvalidFile\": \"无法加载无效的文件\",\n    \"importBackendFailed\": \"从后端导入失败。\",\n    \"cannotExportEmptyCanvas\": \"无法导出空白画布。\",\n    \"couldNotCopyToClipboard\": \"无法复制到剪贴板。\",\n    \"decryptFailed\": \"无法解密数据。\",\n    \"uploadedSecurly\": \"上传已被端到端加密保护，这意味着 Excalidraw 的服务器和第三方都无法读取内容。\",\n    \"loadSceneOverridePrompt\": \"加载外部绘图将取代您现有的内容。您想要继续吗？\",\n    \"collabStopOverridePrompt\": \"停止会话将覆盖您先前本地存储的绘图。 您确定吗？\\n\\n(如果您想保持本地绘图，只需关闭浏览器选项卡。)\",\n    \"errorAddingToLibrary\": \"无法将项目添加到素材库中\",\n    \"errorRemovingFromLibrary\": \"无法从素材库中移除项目\",\n    \"confirmAddLibrary\": \"这将添加 {{numShapes}} 个形状到您的素材库中。您确定吗？\",\n    \"imageDoesNotContainScene\": \"此图像似乎不包含任何画布数据。您是否在导出时启用了画布嵌入功能？\",\n    \"cannotRestoreFromImage\": \"无法从此图像文件恢复画布\",\n    \"invalidSceneUrl\": \"无法从提供的 URL 导入场景。它或者格式不正确，或者不包含有效的 Excalidraw JSON 数据。\",\n    \"resetLibrary\": \"这将会清除你的素材库。你确定要这么做吗？\",\n    \"removeItemsFromsLibrary\": \"确定要从素材库中删除 {{count}} 个项目吗？\",\n    \"invalidEncryptionKey\": \"密钥必须包含22个字符。实时协作已被禁用。\",\n    \"collabOfflineWarning\": \"无网络连接。\\n您的改动将不会被保存！\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"不支持的文件格式。\",\n    \"imageInsertError\": \"无法插入图像。请稍后再试……\",\n    \"fileTooBig\": \"文件过大。最大允许的大小为 {{maxSize}}。\",\n    \"svgImageInsertError\": \"无法插入 SVG 图像。该 SVG 标记似乎是无效的。\",\n    \"failedToFetchImage\": \"无法获取图片。\",\n    \"invalidSVGString\": \"无效的 SVG。\",\n    \"cannotResolveCollabServer\": \"无法连接到实时协作服务器。请重新加载页面并重试。\",\n    \"importLibraryError\": \"无法加载素材库\",\n    \"collabSaveFailed\": \"无法保存到后端数据库。如果问题持续存在，您应该保存文件到本地，以确保您的工作不会丢失。\",\n    \"collabSaveFailed_sizeExceeded\": \"无法保存到后端数据库，画布似乎过大。您应该保存文件到本地，以确保您的工作不会丢失。\",\n    \"imageToolNotSupported\": \"图片已被禁用。\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"您似乎正在使用 Brave 浏览器并启用了<bold>积极阻止指纹识别</bold>的设置。\",\n      \"line2\": \"这可能会破坏绘图中的 <bold>文本元素</bold>。\",\n      \"line3\": \"我们强烈建议禁用此设置。您可以按照<link>这些步骤</link>来设置。\",\n      \"line4\": \"如果禁用此设置无法修复文本元素的显示，请在 GitHub 上提交一个 <issueLink>issue</issueLink> ，或者在 <discordLink>Discord</discordLink> 上反馈\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"嵌入的元素不能被添加到素材库。\",\n      \"iframe\": \"不能将 IFrame 元素添加到素材库中。\",\n      \"image\": \"我们不久将支持添加图片到素材库\"\n    },\n    \"asyncPasteFailedOnRead\": \"无法粘贴（无法读取系统剪贴板）。\",\n    \"asyncPasteFailedOnParse\": \"无法粘贴。\",\n    \"copyToSystemClipboardFailed\": \"无法复制到剪贴板。\"\n  },\n  \"toolBar\": {\n    \"selection\": \"选择\",\n    \"image\": \"插入图像\",\n    \"rectangle\": \"矩形\",\n    \"diamond\": \"菱形\",\n    \"ellipse\": \"椭圆\",\n    \"arrow\": \"箭头\",\n    \"line\": \"线条\",\n    \"freedraw\": \"自由书写\",\n    \"text\": \"文字\",\n    \"library\": \"素材库\",\n    \"lock\": \"绘制后保持所选的工具栏状态\",\n    \"penMode\": \"笔模式 – 避免误触\",\n    \"link\": \"为选中的形状添加/更新链接\",\n    \"eraser\": \"橡皮\",\n    \"frame\": \"画框工具\",\n    \"magicframe\": \"线框图至代码\",\n    \"embeddable\": \"嵌入网页\",\n    \"laser\": \"激光笔\",\n    \"hand\": \"抓手（平移工具）\",\n    \"extraTools\": \"更多工具\",\n    \"mermaidToExcalidraw\": \"Mermaid 至 Excalidraw\",\n    \"magicSettings\": \"AI 设置\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"画布动作\",\n    \"selectedShapeActions\": \"选定形状操作\",\n    \"shapes\": \"形状\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"要移动画布，请按住鼠标滚轮或空格键同时拖拽鼠标，或使用抓手工具。\",\n    \"linearElement\": \"点击创建多个点 拖动创建直线\",\n    \"freeDraw\": \"点击并拖动，完成时松开\",\n    \"text\": \"提示：您也可以使用选择工具双击任意位置来添加文字\",\n    \"embeddable\": \"点击并拖动以创建嵌入网页\",\n    \"text_selected\": \"双击或按回车键以编辑文本\",\n    \"text_editing\": \"按下 Escape 或 CtrlOrCmd+ENTER 完成编辑\",\n    \"linearElementMulti\": \"点击最后一个点或按下 Esc/Enter 来完成\",\n    \"lockAngle\": \"可以按住 Shift 来约束角度\",\n    \"resize\": \"您可以按住SHIFT来限制比例大小，\\n按住ALT来调整中心大小\",\n    \"resizeImage\": \"按住SHIFT可以自由缩放，\\n按住ALT可以从中间缩放\",\n    \"rotate\": \"旋转时可以按住 Shift 来约束角度\",\n    \"lineEditor_info\": \"按住 CtrlOrCmd 并双击或按 CtrlOrCmd + Enter 来编辑点\",\n    \"lineEditor_pointSelected\": \"按下 Delete 移除点，CtrlOrCmd+D 以复制，拖动以移动\",\n    \"lineEditor_nothingSelected\": \"选择要编辑的点 (按住 SHIFT 选择多个)，\\n或按住 Alt 并点击以添加新点\",\n    \"placeImage\": \"点击放置图像，或者点击并拖动以手动设置图像大小\",\n    \"publishLibrary\": \"发布您自己的素材库\",\n    \"bindTextToElement\": \"按下 Enter 以添加文本\",\n    \"deepBoxSelect\": \"按住 CtrlOrCmd 以深度选择，并避免拖拽\",\n    \"eraserRevert\": \"按住 Alt 以反选被标记删除的元素\",\n    \"firefox_clipboard_write\": \"将高级配置首选项“dom.events.asyncClipboard.clipboardItem”设置为“true”可以启用此功能。要更改 Firefox 的高级配置首选项，请前往“about:config”页面。\",\n    \"disableSnapping\": \"按住 Ctrl 或 Cmd 以禁用吸附\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"无法显示预览\",\n    \"canvasTooBig\": \"画布可能过大。\",\n    \"canvasTooBigTip\": \"提示：尝试将最远的元素移动到和其它元素更近一些。\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"遇到异常。请尝试<button>重新加载页面</button>。\",\n    \"clearCanvasMessage\": \"如果重新加载页面无效，请尝试<button>清除画布</button>。\",\n    \"clearCanvasCaveat\": \"这会造成当前工作丢失\",\n    \"trackedToSentry\": \"标识符为{{eventId}}的错误已在我们的系统中被记录\",\n    \"openIssueMessage\": \"我们非常谨慎地处理错误信息，您的画布内容不会被包含在错误报告中。如果您的画布内容不需要保持私密，请考虑在我们的 <button>bug 跟踪系统</button>上提供更多信息。请复制粘贴以下信息到 GitHub Issue 中。\",\n    \"sceneContent\": \"画布内容:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"你可以邀请其他人到目前的画面中与你协作。\",\n    \"desc_privacy\": \"别担心，该会话使用端到端加密，无论绘制什么都将保持私密，甚至连我们的服务器也无法查看。\",\n    \"button_startSession\": \"开始会话\",\n    \"button_stopSession\": \"结束会话\",\n    \"desc_inProgressIntro\": \"实时协作会话进行中。\",\n    \"desc_shareLink\": \"分享此链接给你要协作的用户\",\n    \"desc_exitSession\": \"停止会话将中断您与房间的连接，但您依然可以在本地继续使用画布。请注意，这不会影响到其他用户，他们仍可以在他们的版本上继续协作。\",\n    \"shareTitle\": \"加入 Excalidraw 实时协作会话\"\n  },\n  \"errorDialog\": {\n    \"title\": \"错误\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"保存到本地\",\n    \"disk_details\": \"将画布数据导出为文件，以便以后导入\",\n    \"disk_button\": \"保存为文件\",\n    \"link_title\": \"分享链接\",\n    \"link_details\": \"导出为只读链接。\",\n    \"link_button\": \"导出链接\",\n    \"excalidrawplus_description\": \"将画布保存到您的 Excalidraw+ 工作区。\",\n    \"excalidrawplus_button\": \"导出\",\n    \"excalidrawplus_exportError\": \"暂时无法导出到 Excalidraw+ ...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"浏览我们的博客\",\n    \"click\": \"单击\",\n    \"deepSelect\": \"深度选择\",\n    \"deepBoxSelect\": \"在方框内深度选择并避免拖拽\",\n    \"curvedArrow\": \"曲线箭头\",\n    \"curvedLine\": \"曲线\",\n    \"documentation\": \"文档\",\n    \"doubleClick\": \"双击\",\n    \"drag\": \"拖动\",\n    \"editor\": \"编辑器\",\n    \"editLineArrowPoints\": \"编辑线条或箭头的点\",\n    \"editText\": \"添加或编辑文本\",\n    \"github\": \"发现问题？提交反馈\",\n    \"howto\": \"帮助文档\",\n    \"or\": \"或\",\n    \"preventBinding\": \"禁用箭头吸附\",\n    \"tools\": \"工具\",\n    \"shortcuts\": \"快捷键列表\",\n    \"textFinish\": \"完成编辑 (文本编辑器)\",\n    \"textNewLine\": \"添加新行(文本编辑器)\",\n    \"title\": \"帮助\",\n    \"view\": \"视图\",\n    \"zoomToFit\": \"缩放以适应所有元素\",\n    \"zoomToSelection\": \"缩放到选区\",\n    \"toggleElementLock\": \"锁定/解锁\",\n    \"movePageUpDown\": \"上下移动页面\",\n    \"movePageLeftRight\": \"左右移动页面\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"清除画布\"\n  },\n  \"publishDialog\": {\n    \"title\": \"发布素材库\",\n    \"itemName\": \"项目名称\",\n    \"authorName\": \"作者名\",\n    \"githubUsername\": \"GitHub 用户名\",\n    \"twitterUsername\": \"Twitter 用户名\",\n    \"libraryName\": \"名称\",\n    \"libraryDesc\": \"简介\",\n    \"website\": \"网址\",\n    \"placeholder\": {\n      \"authorName\": \"您的名字或用户名\",\n      \"libraryName\": \"素材库名称\",\n      \"libraryDesc\": \"介绍您的素材库，让人们了解其用途\",\n      \"githubHandle\": \"GitHub 用户名（可选），填写后，您可以编辑已提交待审的素材库\",\n      \"twitterHandle\": \"Twitter 用户名（可选），填写后，当我们在Twitter发布推广信息时便可提及您\",\n      \"website\": \"您个人网站的或任意的链接（可选）\"\n    },\n    \"errors\": {\n      \"required\": \"必填\",\n      \"website\": \"输入一个有效的URL\"\n    },\n    \"noteDescription\": \"提交后，您的素材库将被包含在<link>公共素材库广场</link>以供其他人在绘图中使用。\",\n    \"noteGuidelines\": \"提交的素材库需先经人工审核。在提交之前，请先阅读<link>指南</link> 。后续沟通和对库的修改需要 GitHub 账号，但这不是必须的。\",\n    \"noteLicense\": \"提交即表明您已同意素材库将遵循 <link>MIT 许可证</link>，简而言之，任何人都可以不受限制地使用它们。\",\n    \"noteItems\": \"素材库中每个项目都有各自的名称以供筛选。以下项目将被包含：\",\n    \"atleastOneLibItem\": \"请选择至少一个素材库以开始\",\n    \"republishWarning\": \"注意：部分选中的项目已经发布或提交。请仅在更新已有或已提交的素材库时重复提交项目。\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"素材库已提交\",\n    \"content\": \"谢谢你 {{authorName}}。您的素材库已被提交审核。请点击<link>此处</link>跟进此次提交的状态\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"重置素材库\",\n    \"removeItemsFromLib\": \"从素材库中删除选中的项目\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"导出图片\",\n    \"label\": {\n      \"withBackground\": \"背景\",\n      \"onlySelected\": \"仅选中\",\n      \"darkMode\": \"深色模式\",\n      \"embedScene\": \"包含画布数据\",\n      \"scale\": \"缩放比例\",\n      \"padding\": \"内边距\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"画布数据将被保存到导出的 PNG/SVG 文件，以便恢复。\\n将会增加导出文件的大小。\"\n    },\n    \"title\": {\n      \"exportToPng\": \"导出为 PNG\",\n      \"exportToSvg\": \"导出为 SVG\",\n      \"copyPngToClipboard\": \"复制 PNG 到剪切板\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"复制到剪贴板\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"您的绘图采用端到端加密，其内容对于 Excalidraw 服务器是不可见的。\",\n    \"link\": \"Excalidraw 中关于端到端加密的博客\"\n  },\n  \"stats\": {\n    \"angle\": \"角度\",\n    \"element\": \"元素\",\n    \"elements\": \"元素\",\n    \"height\": \"高度\",\n    \"scene\": \"画布\",\n    \"selected\": \"选中\",\n    \"storage\": \"存储\",\n    \"title\": \"详细统计信息\",\n    \"total\": \"总计\",\n    \"version\": \"版本\",\n    \"versionCopy\": \"点击复制\",\n    \"versionNotAvailable\": \"版本不可用\",\n    \"width\": \"宽度\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"添加到素材库中\",\n    \"copyStyles\": \"样式已拷贝。\",\n    \"copyToClipboard\": \"已复制到剪切板。\",\n    \"copyToClipboardAsPng\": \"已将 {{exportSelection}} 作为 PNG 复制到剪贴板\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"文件已保存。\",\n    \"fileSavedToFilename\": \"保存到 {filename}\",\n    \"canvas\": \"画布\",\n    \"selection\": \"所选项\",\n    \"pasteAsSingleElement\": \"使用 {{shortcut}} 粘贴为单个元素，\\n或粘贴到现有的文本编辑器里\",\n    \"unableToEmbed\": \"目前不允许嵌入此网址。请在 GitHub 上提 issue 请求将此网址加入白名单\",\n    \"unrecognizedLinkFormat\": \"您嵌入的链接不符合格式要求。请尝试粘贴源网站提供的“嵌入 (embed)”字符串\"\n  },\n  \"colors\": {\n    \"transparent\": \"透明\",\n    \"black\": \"黑\",\n    \"white\": \"白\",\n    \"red\": \"红\",\n    \"pink\": \"粉红\",\n    \"grape\": \"紫红\",\n    \"violet\": \"蓝紫\",\n    \"gray\": \"灰\",\n    \"blue\": \"蓝\",\n    \"cyan\": \"青\",\n    \"teal\": \"蓝绿\",\n    \"green\": \"绿\",\n    \"yellow\": \"黄\",\n    \"orange\": \"橙\",\n    \"bronze\": \"古铜\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"您的所有数据都储存在浏览器本地。\",\n      \"center_heading_plus\": \"是否前往 Excalidraw+ ？\",\n      \"menuHint\": \"导出、首选项、语言……\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"导出、首选项……\",\n      \"center_heading\": \"图，化繁为简。\",\n      \"toolbarHint\": \"选择工具并开始绘图！\",\n      \"helpHint\": \"快捷键和帮助\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"常用自定义颜色\",\n    \"colors\": \"颜色\",\n    \"shades\": \"色调明暗\",\n    \"hexCode\": \"十六进制值\",\n    \"noShades\": \"此颜色没有可用的明暗变化\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"导出为图片\",\n        \"button\": \"导出为图片\",\n        \"description\": \"将画布数据导出为图片，以便以后导入。\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"保存到本地\",\n        \"button\": \"保存到本地\",\n        \"description\": \"将画布数据导出为文件，以便以后导入。\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"导出到 Excalidraw+\",\n        \"description\": \"将画布保存到您的 Excalidraw+ 工作区。\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"从文件加载\",\n        \"button\": \"从文件加载\",\n        \"description\": \"从文件加载将<bold>替换您现有的内容</bold>。<br></br>您可以先使用下列方式备份您的绘图。\"\n      },\n      \"shareableLink\": {\n        \"title\": \"从链接加载\",\n        \"button\": \"替换我的内容\",\n        \"description\": \"加载外部绘图将<bold>替换您现有的内容</bold>。<br></br>您可以先使用下列方式备份您的绘图。\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"Mermaid 至 Excalidraw\",\n    \"button\": \"插入\",\n    \"description\": \"目前仅支持<flowchartLink>流程图</flowchartLink>、<sequenceLink>序列图</sequenceLink>和<classLink>类图</classLink>。其他类型在 Excalidraw 中将以图像呈现。\",\n    \"syntax\": \"Mermaid 语法\",\n    \"preview\": \"预览\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}