/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_node_modules_monaco-editor_esm_vs_editor_common_services_sync_recursive_";
exports.ids = ["_ssr_node_modules_monaco-editor_esm_vs_editor_common_services_sync_recursive_"];
exports.modules = {

/***/ "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services sync recursive ^.*$":
/*!******************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/editor/common/services/ sync ^.*$ ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./editorBaseApi": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorBaseApi.js",
	"./editorBaseApi.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorBaseApi.js",
	"./editorSimpleWorker": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorSimpleWorker.js",
	"./editorSimpleWorker.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorSimpleWorker.js",
	"./editorWorker": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorWorker.js",
	"./editorWorker.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorWorker.js",
	"./editorWorkerHost": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorWorkerHost.js",
	"./editorWorkerHost.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/editorWorkerHost.js",
	"./findSectionHeaders": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/findSectionHeaders.js",
	"./findSectionHeaders.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/findSectionHeaders.js",
	"./getIconClasses": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/getIconClasses.js",
	"./getIconClasses.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/getIconClasses.js",
	"./languageFeatureDebounce": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatureDebounce.js",
	"./languageFeatureDebounce.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatureDebounce.js",
	"./languageFeatures": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatures.js",
	"./languageFeatures.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatures.js",
	"./languageFeaturesService": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageFeaturesService.js",
	"./languageFeaturesService.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageFeaturesService.js",
	"./languageService": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageService.js",
	"./languageService.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/languageService.js",
	"./languagesAssociations": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/languagesAssociations.js",
	"./languagesAssociations.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/languagesAssociations.js",
	"./languagesRegistry": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/languagesRegistry.js",
	"./languagesRegistry.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/languagesRegistry.js",
	"./markerDecorations": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorations.js",
	"./markerDecorations.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorations.js",
	"./markerDecorationsService": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorationsService.js",
	"./markerDecorationsService.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorationsService.js",
	"./model": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/model.js",
	"./model.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/model.js",
	"./modelService": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/modelService.js",
	"./modelService.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/modelService.js",
	"./resolverService": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/resolverService.js",
	"./resolverService.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/resolverService.js",
	"./semanticTokensDto": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensDto.js",
	"./semanticTokensDto.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensDto.js",
	"./semanticTokensProviderStyling": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensProviderStyling.js",
	"./semanticTokensProviderStyling.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensProviderStyling.js",
	"./semanticTokensStyling": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStyling.js",
	"./semanticTokensStyling.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStyling.js",
	"./semanticTokensStylingService": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStylingService.js",
	"./semanticTokensStylingService.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStylingService.js",
	"./textModelSync/textModelSync.impl": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.impl.js",
	"./textModelSync/textModelSync.impl.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.impl.js",
	"./textModelSync/textModelSync.protocol": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js",
	"./textModelSync/textModelSync.protocol.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js",
	"./textResourceConfiguration": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/textResourceConfiguration.js",
	"./textResourceConfiguration.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/textResourceConfiguration.js",
	"./treeSitterParserService": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/treeSitterParserService.js",
	"./treeSitterParserService.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/treeSitterParserService.js",
	"./treeViewsDnd": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDnd.js",
	"./treeViewsDnd.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDnd.js",
	"./treeViewsDndService": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDndService.js",
	"./treeViewsDndService.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDndService.js",
	"./unicodeTextModelHighlighter": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/unicodeTextModelHighlighter.js",
	"./unicodeTextModelHighlighter.js": "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services/unicodeTextModelHighlighter.js"
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = "(ssr)/../node_modules/monaco-editor/esm/vs/editor/common/services sync recursive ^.*$";

/***/ })

};
;