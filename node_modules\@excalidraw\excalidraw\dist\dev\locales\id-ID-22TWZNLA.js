import "../chunk-XDFCUUT6.js";

// locales/id-ID.json
var labels = {
  paste: "Tempel",
  pasteAsPlaintext: "Tempel sebagai teks biasa",
  pasteCharts: "Tempel diagram",
  selectAll: "<PERSON><PERSON><PERSON> semua",
  multiSelect: "Tambahkan elemen ke pilihan",
  moveCanvas: "Pindahkan kanvas",
  cut: "Potong",
  copy: "Salin",
  copyAsPng: "Salin ke papan klip sebagai PNG",
  copyAsSvg: "Salin ke papan klip sebagai SVG",
  copyText: "Salin ke papan klip sebagai teks",
  copySource: "",
  convertToCode: "",
  bringForward: "Bawa maju",
  sendToBack: "<PERSON><PERSON> ke belakang",
  bringToFront: "Bawa ke depan",
  sendBackward: "Kirim mundur",
  delete: "Hapus",
  copyStyles: "Salin gaya",
  pasteStyles: "Tempelkan gaya",
  stroke: "Guratan",
  background: "Latar",
  fill: "Isian",
  strokeWidth: "Lebar guratan",
  strokeStyle: "Gaya guratan",
  strokeStyle_solid: "Padat",
  strokeStyle_dashed: "Putus-putus",
  strokeStyle_dotted: "Titik-titik",
  sloppiness: "Kecerobohan",
  opacity: "Keburaman",
  textAlign: "Perataan teks",
  edges: "Tepi",
  sharp: "Tajam",
  round: "Bulat",
  arrowheads: "Mata panah",
  arrowhead_none: "Tidak ada",
  arrowhead_arrow: "Panah",
  arrowhead_bar: "Batang",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "Segitiga",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "Ukuran font",
  fontFamily: "Jenis font",
  addWatermark: 'Tambahkan "Dibuat dengan Excalidraw"',
  handDrawn: "Tulisan tangan",
  normal: "Normal",
  code: "Kode",
  small: "Kecil",
  medium: "Sedang",
  large: "Besar",
  veryLarge: "Sangat besar",
  solid: "Padat",
  hachure: "Garis-garis",
  zigzag: "Zigzag",
  crossHatch: "Asiran silang",
  thin: "Lembut",
  bold: "Tebal",
  left: "Kiri",
  center: "Tengah",
  right: "Kanan",
  extraBold: "Sangat tebal",
  architect: "Arsitek",
  artist: "Artis",
  cartoonist: "Kartunis",
  fileTitle: "Nama file",
  colorPicker: "Pilihan Warna",
  canvasColors: "Digunakan di kanvas",
  canvasBackground: "Latar Kanvas",
  drawingCanvas: "Kanvas",
  layers: "Lapisan",
  actions: "Aksi",
  language: "Bahasa",
  liveCollaboration: "Kolaborasi langsung...",
  duplicateSelection: "Duplikat",
  untitled: "Tanpa judul",
  name: "Nama",
  yourName: "Nama Anda",
  madeWithExcalidraw: "Dibuat dengan Excalidraw",
  group: "Kelompokan pilihan",
  ungroup: "Pisahkan pilihan",
  collaborators: "Kolaborator",
  showGrid: "Tampilkan grid",
  addToLibrary: "Tambahkan ke pustaka",
  removeFromLibrary: "Hapus dari pustaka",
  libraryLoadingMessage: "Memuat pustaka\u2026",
  libraries: "Telusur pustaka",
  loadingScene: "Memuat pemandangan\u2026",
  align: "Perataan",
  alignTop: "Rata atas",
  alignBottom: "Rata bawah",
  alignLeft: "Rata kiri",
  alignRight: "Rata kanan",
  centerVertically: "Pusatkan secara vertikal",
  centerHorizontally: "Pusatkan secara horizontal",
  distributeHorizontally: "Distribusikan horizontal",
  distributeVertically: "Distribusikan vertikal",
  flipHorizontal: "Balikkan horizontal",
  flipVertical: "Balikkan vertikal",
  viewMode: "Mode tampilan",
  share: "Bagikan",
  showStroke: "Tampilkan garis pengambil warna",
  showBackground: "Tampilkan latar pengambil warna",
  toggleTheme: "Ubah tema",
  personalLib: "Pustaka Pribadi",
  excalidrawLib: "Pustaka Excalidraw",
  decreaseFontSize: "Kecilkan ukuran font",
  increaseFontSize: "Besarkan ukuran font",
  unbindText: "Lepas teks",
  bindText: "Kunci teks ke kontainer",
  createContainerFromText: "Bungkus teks dalam kontainer",
  link: {
    edit: "Edit tautan",
    editEmbed: "",
    create: "Buat tautan",
    createEmbed: "",
    label: "Tautan",
    labelEmbed: "",
    empty: ""
  },
  lineEditor: {
    edit: "Edit tautan",
    exit: "Keluar editor garis"
  },
  elementLock: {
    lock: "Kunci",
    unlock: "Lepas",
    lockAll: "Kunci semua",
    unlockAll: "Lepas semua"
  },
  statusPublished: "Telah terbit",
  sidebarLock: "Biarkan sidebar tetap terbuka",
  selectAllElementsInFrame: "Pilih semua elemen di bingkai",
  removeAllElementsFromFrame: "Hapus semua elemen dari bingkai",
  eyeDropper: "Ambil warna dari kanvas",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "Belum ada item yang ditambahkan...",
  hint_emptyLibrary: "Pilih item pada kanvas untuk menambahkan nya di sini, atau pasang pustaka dari gudang di bawah ini.",
  hint_emptyPrivateLibrary: "Pilih item pada kanvas untuk menambahkan nya di sini."
};
var buttons = {
  clearReset: "Setel Ulang Kanvas",
  exportJSON: "Ekspor ke file",
  exportImage: "Ekspor gambar...",
  export: "Simpan ke...",
  copyToClipboard: "Salin ke Papan Klip",
  save: "Simpan ke file sekarang",
  saveAs: "Simpan sebagai",
  load: "Buka",
  getShareableLink: "Buat Tautan yang Bisa Dibagian",
  close: "Tutup",
  selectLanguage: "Pilih bahasa",
  scrollBackToContent: "Gulir kembali ke konten",
  zoomIn: "Besarkan",
  zoomOut: "Kecilkan",
  resetZoom: "Reset Pembesaran",
  menu: "Menu",
  done: "Selesai",
  edit: "Edit",
  undo: "Urungkan",
  redo: "Ulangi",
  resetLibrary: "Reset pustaka",
  createNewRoom: "Buat ruang baru",
  fullScreen: "Layar penuh",
  darkMode: "Mode gelap",
  lightMode: "Mode terang",
  zenMode: "Mode zen",
  objectsSnapMode: "",
  exitZenMode: "Keluar dari mode zen",
  cancel: "Batal",
  clear: "Hapus",
  remove: "Hapus",
  embed: "",
  publishLibrary: "Terbitkan",
  submit: "Kirimkan",
  confirm: "Konfirmasi",
  embeddableInteractionButton: ""
};
var alerts = {
  clearReset: "Ini akan menghapus semua yang ada dikanvas. Apakah kamu yakin ?",
  couldNotCreateShareableLink: "Tidak bisa membuat tautan yang bisa dibagikan",
  couldNotCreateShareableLinkTooBig: "Tidak dapat membuat tautan yang dapat dibagikan: pemandangan terlalu besar",
  couldNotLoadInvalidFile: "Tidak dapat memuat berkas yang tidak valid",
  importBackendFailed: "Gagal mengimpor dari backend",
  cannotExportEmptyCanvas: "Tidak bisa mengekspor kanvas kosong",
  couldNotCopyToClipboard: "Tidak bisa menyalin ke papan klip.",
  decryptFailed: "Tidak dapat mengdekripsi data.",
  uploadedSecurly: "Pengunggahan ini telah diamankan menggunakan enkripsi end-to-end, artinya server Excalidraw dan pihak ketiga tidak data membaca nya",
  loadSceneOverridePrompt: "Memuat gambar external akan mengganti konten Anda yang ada. Apakah Anda ingin melanjutkan?",
  collabStopOverridePrompt: "Menghentikan sesi akan menimpa gambar Anda yang tersimpan secara lokal. Anda yakin?\n\n(Jika Anda ingin menyimpan gambar lokal Anda, gantinya cukup tutup tab browser.)",
  errorAddingToLibrary: "Tidak dapat menambahkan item ke pustaka",
  errorRemovingFromLibrary: "Tidak dapat membuang item dari pustaka",
  confirmAddLibrary: "Ini akan menambahkan {{numShapes}} bentuk ke pustaka Anda. Anda yakin?",
  imageDoesNotContainScene: "Gambar ini sepertinya tidak terdapat data pemandangan. Sudahkah Anda mengaktifkan penyematan pemandangan ketika ekspor?",
  cannotRestoreFromImage: "Pemandangan tidak dapat dipulihkan dari file gambar ini",
  invalidSceneUrl: "Tidak dapat impor pemandangan dari URL. Kemungkinan URL itu rusak atau tidak berisi data JSON Excalidraw yang valid.",
  resetLibrary: "Ini akan menghapus pustaka Anda. Anda yakin?",
  removeItemsFromsLibrary: "Hapus {{count}} item dari pustaka?",
  invalidEncryptionKey: "Sandi enkripsi harus 22 karakter. Kolaborasi langsung dinonaktifkan.",
  collabOfflineWarning: "Tidak ada koneksi internet.\nPerubahan tidak akan disimpan!"
};
var errors = {
  unsupportedFileType: "Tipe file tidak didukung.",
  imageInsertError: "Tidak dapat menyisipkan gambar. Coba lagi nanti...",
  fileTooBig: "File terlalu besar. Ukuran maksimum yang dibolehkan {{maxSize}}.",
  svgImageInsertError: "Tidak dapat menyisipkan gambar SVG. Markup SVG sepertinya tidak valid.",
  failedToFetchImage: "",
  invalidSVGString: "SVG tidak valid.",
  cannotResolveCollabServer: "Tidak dapat terhubung ke server kolab. Muat ulang laman dan coba lagi.",
  importLibraryError: "Tidak dapat memuat pustaka",
  collabSaveFailed: "Tidak dapat menyimpan ke dalam basis data server. Jika masih berlanjut, Anda sebaiknya simpan berkas Anda secara lokal untuk memastikan pekerjaan Anda tidak hilang.",
  collabSaveFailed_sizeExceeded: "Tidak dapat menyimpan ke dalam basis data server, tampaknya ukuran kanvas terlalu besar. Anda sebaiknya simpan berkas Anda secara lokal untuk memastikan pekerjaan Anda tidak hilang.",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "Sepertinya Anda menggunkan peramban Brave dengan pengaturan <bold>Blokir Fingerprinting yang Agresif</bold> diaktifkan.",
    line2: "Ini dapat membuat <bold>Elemen Teks</bold> dalam gambar mu.",
    line3: "Kami sangat menyarankan mematikan pengaturan ini. Anda dapat mengikuti <link>langkah-langkah ini</link> untuk melakukannya.",
    line4: "Jika mematikan pengaturan ini tidak membenarkan tampilan elemen teks, mohon buka\n<issueLink>isu</issueLink> di GitHub kami, atau chat kami di <discordLink>Discord</discordLink>"
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "",
    image: ""
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "Pilihan",
  image: "Sisipkan gambar",
  rectangle: "Persegi",
  diamond: "Berlian",
  ellipse: "Elips",
  arrow: "Panah",
  line: "Garis",
  freedraw: "Gambar",
  text: "Teks",
  library: "Pustaka",
  lock: "Biarkan alat yang dipilih aktif setelah menggambar",
  penMode: "Mode pena - mencegah sentuhan",
  link: "Tambah/Perbarui tautan untuk bentuk yang dipilih",
  eraser: "Penghapus",
  frame: "Alat bingkai",
  magicframe: "",
  embeddable: "",
  laser: "",
  hand: "Tangan (alat panning)",
  extraTools: "Alat-alat lain",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "Opsi Kanvas",
  selectedShapeActions: "Opsi bentuk yang dipilih",
  shapes: "Bentuk"
};
var hints = {
  canvasPanning: "Untuk memindahkan kanvas, tekan roda mouse atau spacebar sambil menyeret, atau menggunakan alat tangan",
  linearElement: "Klik untuk memulai banyak poin, seret untuk satu baris",
  freeDraw: "Klik dan seret, lepaskan jika Anda selesai",
  text: "Tip: Anda juga dapat menambahkan teks dengan klik ganda di mana saja dengan alat pemilihan",
  embeddable: "",
  text_selected: "Klik ganda atau tekan ENTER untuk edit teks",
  text_editing: "Tekan Escape atau CtrlAtauCmd+ENTER untuk selesai mengedit",
  linearElementMulti: "Klik pada titik akhir atau tekan Escape atau Enter untuk menyelesaikan",
  lockAngle: "Anda dapat menjaga sudut dengan menahan SHIFT",
  resize: "Anda dapat menjaga proposi dengan menekan SHIFT sambil mengubah ukuran,\ntekan AlT untuk mengubah ukuran dari tengah",
  resizeImage: "Anda dapat mengubah secara bebas dengan menekan SHIFT,\nTekan ALT untuk mengubah dari tengah",
  rotate: "Anda dapat menjaga sudut dengan menahan SHIFT sambil memutar",
  lineEditor_info: "Tekan Ctrl/Cmd dan Dobel-klik atau tekan Ctrl/Cmd +Enter untuk mengedit poin",
  lineEditor_pointSelected: "Tekan Delete untuk menghapus titik, Ctrl/Cmd + D untuk menduplikasi, atau seret untuk memindahkan",
  lineEditor_nothingSelected: "Pilih titik untuk mengedit (tekan SHIFT untuk pilih banyak), atau tekan Alt dan klik untuk tambahkan titik baru",
  placeImage: "Klik untuk tempatkan gambar, atau klik dan jatuhkan untuk tetapkan ukuran secara manual",
  publishLibrary: "Terbitkan pustaka Anda",
  bindTextToElement: "Tekan enter untuk tambahkan teks",
  deepBoxSelect: "Tekan Ctrl atau Cmd untuk memilih yang di dalam, dan mencegah penggeseran",
  eraserRevert: "Tahan Alt untuk mengembalikan elemen yang ditandai untuk dihapus",
  firefox_clipboard_write: 'Fitur ini dapat diaktifkan melalui pengaturan flag "dom.events.asyncClipboard.clipboardItem" ke "true". Untuk mengganti flag di Firefox, pergi ke laman "about:config".',
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "Tidak dapat menampilkan pratinjau",
  canvasTooBig: "Kanvas mungkin terlalu besar.",
  canvasTooBigTip: "Tip: coba pindahkan elemen-terjauh lebih dekat bersama."
};
var errorSplash = {
  headingMain: "Mengalami sebuah kesalahan. Cobalah <button>muat ulang halaman.</button>",
  clearCanvasMessage: "Jika memuat ulang tidak bekerja, cobalah <button>bersihkan canvas.</button>",
  clearCanvasCaveat: " Ini akan menghasilkan hilangnya pekerjaan ",
  trackedToSentry: "Kesalahan dengan pengidentifikasi {{eventId}} dilacak di sistem kami.",
  openIssueMessage: "Kami sangat berhati-hati untuk tidak menyertakan informasi pemandangan Anda pada kesalahan. Jika pemandangan Anda tidak bersifat pribadi, mohon pertimbangkan menindak lanjut pada <button>pelacak bug.</button> Mohon sertakan informasi dibawah ini dengan menyalin dan menempelkan di Github issue.",
  sceneContent: "Pemandangan konten:"
};
var roomDialog = {
  desc_intro: "Anda dapat mengundang orang ke pemandangan Anda saat ini untuk berkolaborasi dengan Anda.",
  desc_privacy: "Jangan khawatir, sesi menggunakan enkripsi end-to-end, sehingga apa pun yang Anda gambar akan tetap bersifat pribadi. Bahkan server kami tidak dapat melihat apa yang Anda lakukan.",
  button_startSession: "Mulai sesi",
  button_stopSession: "Hentikan sesi",
  desc_inProgressIntro: "Sesi kolaborasi sedang berlangsung sekarang.",
  desc_shareLink: "Bagikan tautan ini dengan siapa pun yang Anda inginkan untuk kolaborasi bersama:",
  desc_exitSession: "Menghentikan sesi akan memutuskan hubungan Anda dari ruangan, tetapi Anda dapat melanjutkan bekerja dengan pemandangan Anda secara lokal. Perhatikan bahwa ini tidak memengaruhi orang lain, dan mereka masih dapat berkolaborasi pada versi mereka.",
  shareTitle: "Gabung sesi kolaborasi langsung di Excalidraw"
};
var errorDialog = {
  title: "Kesalahan"
};
var exportDialog = {
  disk_title: "Simpan ke disk",
  disk_details: "Ekspor data pemandangan ke file yang mana Anda dapat impor nanti.",
  disk_button: "Simpan ke file",
  link_title: "Tautan",
  link_details: "Ekspor sebagai tautan yang hanya dibaca.",
  link_button: "Ekspor ke tautan",
  excalidrawplus_description: "Simpan pemandangan ke ruang kerja Excalidraw+ Anda.",
  excalidrawplus_button: "Ekspor",
  excalidrawplus_exportError: "Tidak dapat ekspor ke Excalidraw+ saat ini..."
};
var helpDialog = {
  blog: "Baca blog kami",
  click: "klik",
  deepSelect: "Pilih dalam",
  deepBoxSelect: "Pilih dalam kotak, dan cegah penggeseran",
  curvedArrow: "Panah lengkung",
  curvedLine: "Garis lengkung",
  documentation: "Dokumentasi",
  doubleClick: "klik-ganda",
  drag: "seret",
  editor: "Editor",
  editLineArrowPoints: "Edit titik garis/panah",
  editText: "Edit teks / tambah label",
  github: "Menemukan masalah? Kirimkan",
  howto: "Ikuti panduan kami",
  or: "atau",
  preventBinding: "Cegah pengikatan panah",
  tools: "Alat",
  shortcuts: "Pintasan keyboard",
  textFinish: "Selesai mengedit (editor teks)",
  textNewLine: "Tambahkan garis baru (editor teks)",
  title: "Bantuan",
  view: "Tampilan",
  zoomToFit: "Perbesar agar sesuai dengan semua elemen",
  zoomToSelection: "Perbesar ke seleksi",
  toggleElementLock: "Kunci/lepas seleksi",
  movePageUpDown: "Pindah halaman keatas/kebawah",
  movePageLeftRight: "Pindah halaman kebawah/keatas"
};
var clearCanvasDialog = {
  title: "Hapus kanvas"
};
var publishDialog = {
  title: "Terbitkan pustaka",
  itemName: "Nama item",
  authorName: "Nama pembuat",
  githubUsername: "Nama pengguna github",
  twitterUsername: "Nama pengguna Twitter",
  libraryName: "Nama Pustaka",
  libraryDesc: "Deskripsi pustaka",
  website: "Situs Web",
  placeholder: {
    authorName: "Nama atau nama pengguna Anda",
    libraryName: "Nama dari pustaka Anda",
    libraryDesc: "Deskripsi pustaka Anda untuk membantu orang mengerti penggunaannya",
    githubHandle: "Akun GitHub (opsional), jadi Anda dapat mengubah pustaka ketika diserahkan untuk review",
    twitterHandle: "Nama pengguna Twitter (opsional), jadi kami tahu siapa dipuji ketika mempromosikannya melalui Twitter",
    website: "Hubungkan ke situs personal Anda atau lainnya (opsional)"
  },
  errors: {
    required: "Dibutuhkan",
    website: "Masukkan URL valid"
  },
  noteDescription: "Kirimkan pustaka Anda untuk disertakan di <link>repositori pustaka publik</link>untuk orang lain menggunakannya dalam gambar mereka.",
  noteGuidelines: "Pustaka butuh disetujui secara manual terlebih dahulu. Baca <link>pedoman</link> sebelum mengirim. Anda butuh akun GitHub untuk berkomunikasi dan membuat perubahan jika dibutuhkan, tetapi tidak wajib dibutukan.",
  noteLicense: "Dengan mengkirimkannya, Anda setuju pustaka akan diterbitkan dibawah <link>Lisensi MIT, </link>yang artinya siapa pun dapat menggunakannya tanpa batasan.",
  noteItems: "Setiap item pustaka harus memiliki nama, sehingga bisa disortir. Item pustaka di bawah ini akan dimasukan:",
  atleastOneLibItem: "Pilih setidaknya satu item pustaka untuk mulai",
  republishWarning: "Catatan: beberapa item yang dipilih telah ditandai sebagai sudah dipublikasikan/diserahkan. Anda hanya dapat menyerahkan kembali item-item ketika memperbarui pustaka atau pengumpulan."
};
var publishSuccessDialog = {
  title: "Pustaka telah dikirm",
  content: "Terima kasih {{authorName}}. pustaka Anda telah diserahkan untuk ditinjau ulang. Anda dapat cek statusnya<link>di sini</link>"
};
var confirmDialog = {
  resetLibrary: "Reset pustaka",
  removeItemsFromLib: "Hapus item yang dipilih dari pustaka"
};
var imageExportDialog = {
  header: "Ekspor gambar",
  label: {
    withBackground: "Latar",
    onlySelected: "Hanya yang dipilih",
    darkMode: "Mode gelap",
    embedScene: "Sematkan pemandangan",
    scale: "Skala",
    padding: "Lapisan"
  },
  tooltip: {
    embedScene: "Data pemandangan akan disimpan dalam file PNG/SVG yang diekspor sehingga pemandangan itu dapat dipulihkan darinya.\nAkan membesarkan ukuran file yang diekspor."
  },
  title: {
    exportToPng: "Ekspor ke PNG",
    exportToSvg: "Ekspor ke SVG",
    copyPngToClipboard: "Salin PNG ke papan klip"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "Salin ke papan klip"
  }
};
var encrypted = {
  tooltip: "Gambar anda terenkripsi end-to-end sehingga server Excalidraw tidak akan pernah dapat melihatnya.",
  link: "Pos blog tentang enkripsi ujung ke ujung di Excalidraw"
};
var stats = {
  angle: "Sudut",
  element: "Elemen",
  elements: "Elemen",
  height: "Tinggi",
  scene: "Pemandangan",
  selected: "Terpilih",
  storage: "Penyimpanan",
  title: "Statistik untuk nerd",
  total: "Total",
  version: "Versi",
  versionCopy: "Klik untuk salin",
  versionNotAvailable: "Versi tidak tersedia",
  width: "Lebar"
};
var toast = {
  addedToLibrary: "Tambahkan ke pustaka",
  copyStyles: "Gaya tersalin.",
  copyToClipboard: "Tersalin ke papan klip.",
  copyToClipboardAsPng: "Tersalin {{exportSelection}} ke clipboard sebagai PNG\n({{exportColorScheme}})",
  fileSaved: "File tersimpan.",
  fileSavedToFilename: "Disimpan ke {filename}",
  canvas: "kanvas",
  selection: "pilihan",
  pasteAsSingleElement: "Gunakan {{shortcut}} untuk menempelkan sebagai satu elemen,\natau tempelkan ke teks editor yang ada",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "Transparan",
  black: "Hitam",
  white: "Putih",
  red: "Merah",
  pink: "Pink",
  grape: "Ungu",
  violet: "Violet",
  gray: "Abu-abu",
  blue: "Biru",
  cyan: "Cyan",
  teal: "Teal",
  green: "Hijau",
  yellow: "Kuning",
  orange: "Jingga",
  bronze: "Tembaga"
};
var welcomeScreen = {
  app: {
    center_heading: "Semua data Anda disimpan secara lokal di peramban Anda.",
    center_heading_plus: "Apa Anda ingin berpindah ke Excalidraw+?",
    menuHint: "Ekspor, preferensi, bahasa, ..."
  },
  defaults: {
    menuHint: "Ekspor, preferensi, dan selebihnya...",
    center_heading: "Diagram. Menjadi. Mudah.",
    toolbarHint: "Pilih alat & mulai menggambar!",
    helpHint: "Pintasan & bantuan"
  }
};
var colorPicker = {
  mostUsedCustomColors: "Warna yang sering dipakai",
  colors: "Warna",
  shades: "Nuansa",
  hexCode: "Kode hexa",
  noShades: "Tidak ada nuansa untuk warna ini"
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "Ekspor sebagai gambar",
      button: "Ekspor sebagai gambar",
      description: "Ekspor data pemandangan sebagai gambar yang dapat anda impor nanti."
    },
    saveToDisk: {
      title: "Simpan ke disk",
      button: "Simpan ke disk",
      description: "Ekspor data pemandangan ke file yang dapat Anda dapat impor nanti."
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "Ekspor ke Excalidraw+",
      description: "Simpan pemandangan ke ruang kerja Excalidraw+ Anda."
    }
  },
  modal: {
    loadFromFile: {
      title: "Muat dari file",
      button: "Muat dari file",
      description: "Memuat dari file yang akan <bold>menggantikan konten Anda sekarang</bold>.<br></br>Anda dapat mencadangkan gambar anda dulu menggunakan opsi-opsi ini."
    },
    shareableLink: {
      title: "Muat dari link",
      button: "Ganti konten saya",
      description: "Memuat dari file yang akan <bold>menggantikan konten Anda sekarang</bold>.<br></br>Anda dapat mencadangkan gambar anda dulu menggunakan opsi-opsi ini."
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var id_ID_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  id_ID_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=id-ID-22TWZNLA.js.map
