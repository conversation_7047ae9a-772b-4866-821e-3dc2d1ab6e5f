import"../chunk-SRAX5OIU.js";var o={paste:"Pegar",pasteAsPlaintext:"Pegar coma texto sen formato",pasteCharts:"Pegar gr\xE1ficos",selectAll:"Seleccionar todo",multiSelect:"Engadir elemento \xE1 selecci\xF3n",moveCanvas:"Mover o lenzo",cut:"Cortar",copy:"Copiar",copyAsPng:"Copiar no portapapeis como PNG",copyAsSvg:"Copiar no portapapeis como SVG",copyText:"Copia no portapapeis como texto",copySource:"",convertToCode:"",bringForward:"Traer cara adiante",sendToBack:"Enviar cara atr\xE1s",bringToFront:"Traer \xE1 fronte",sendBackward:"Enviar ao fondo",delete:"Borrar",copyStyles:"Copiar estilo",pasteStyles:"Pegar estilo",stroke:"Trazo",background:"Fondo",fill:"Recheo",strokeWidth:"Largo do trazo",strokeStyle:"Estilo do trazo",strokeStyle_solid:"S\xF3lido",strokeStyle_dashed:"Li\xF1a de trazos",strokeStyle_dotted:"Li\xF1a de puntos",sloppiness:"Estilo de trazo",opacity:"Opacidade",textAlign:"Ali\xF1ar texto",edges:"Bordos",sharp:"Agudo",round:"Redondo",arrowheads:"Puntas de frecha",arrowhead_none:"Ningunha",arrowhead_arrow:"Frecha",arrowhead_bar:"Barra",arrowhead_circle:"",arrowhead_circle_outline:"",arrowhead_triangle:"Tri\xE1ngulo",arrowhead_triangle_outline:"",arrowhead_diamond:"",arrowhead_diamond_outline:"",fontSize:"Tama\xF1o da fonte",fontFamily:"Tipo de fonte",addWatermark:'Engadir "Feito con Excalidraw"',handDrawn:"Debuxado a man",normal:"Normal",code:"C\xF3digo",small:"Pequeno",medium:"Mediano",large:"Grande",veryLarge:"Moi grande",solid:"S\xF3lido",hachure:"Folleto",zigzag:"Zigzag",crossHatch:"Raiado transversal",thin:"Estreito",bold:"Groso",left:"Esquerda",center:"Centrado",right:"Dereita",extraBold:"Moi groso",architect:"Arquitecto",artist:"Artista",cartoonist:"Caricatura",fileTitle:"Nome do arquivo",colorPicker:"Selector de cor",canvasColors:"Usado en lenzo",canvasBackground:"Fondo do lenzo",drawingCanvas:"Lenzo de debuxo",layers:"Capas",actions:"Acci\xF3ns",language:"Idioma",liveCollaboration:"Colaboraci\xF3n en directo...",duplicateSelection:"Duplicar",untitled:"Sen t\xEDtulo",name:"Nome",yourName:"O teu nome",madeWithExcalidraw:"Feito con Excalidraw",group:"Agrupar selecci\xF3n",ungroup:"Desagrupar selecci\xF3n",collaborators:"Colaboradores",showGrid:"Mostrar cuadr\xEDcula",addToLibrary:"Engadir \xE1 biblioteca",removeFromLibrary:"Eliminar da biblioteca",libraryLoadingMessage:"Cargando biblioteca\u2026",libraries:"Explorar bibliotecas",loadingScene:"Cargando escena\u2026",align:"Ali\xF1amento",alignTop:"Ali\xF1amento superior",alignBottom:"Ali\xF1amento inferior",alignLeft:"Ali\xF1ar a esquerda",alignRight:"Ali\xF1ar a dereita",centerVertically:"Centrar verticalmente",centerHorizontally:"Centrar horizontalmente",distributeHorizontally:"Distribu\xEDr horizontalmente",distributeVertically:"Distribu\xEDr verticalmente",flipHorizontal:"Virar horizontalmente",flipVertical:"Virar verticalmente",viewMode:"Modo de visualizaci\xF3n",share:"Compartir",showStroke:"Mostrar selector de cores do trazo",showBackground:"Mostrar selector de cores do fondo",toggleTheme:"Alternar tema",personalLib:"Biblioteca Persoal",excalidrawLib:"Biblioteca Excalidraw",decreaseFontSize:"Diminu\xEDr tama\xF1o da fonte",increaseFontSize:"Aumentar o tama\xF1o da fonte",unbindText:"Desvincular texto",bindText:"Ligar o texto ao contedor",createContainerFromText:"Envolver o texto nun contedor",link:{edit:"Editar ligaz\xF3n",editEmbed:"",create:"Crear ligaz\xF3n",createEmbed:"",label:"Ligaz\xF3n",labelEmbed:"",empty:""},lineEditor:{edit:"Editar li\xF1a",exit:"Sa\xEDr do editor de li\xF1as"},elementLock:{lock:"Bloquear",unlock:"Desbloquear",lockAll:"Bloquear todo",unlockAll:"Desbloquear todo"},statusPublished:"Publicado",sidebarLock:"Manter a barra lateral aberta",selectAllElementsInFrame:"",removeAllElementsFromFrame:"",eyeDropper:"",textToDiagram:"",prompt:""},r={noItems:"A\xEDnda non hai elementos engadidos...",hint_emptyLibrary:"Seleccione un elemento no lenzo para engadilo aqu\xED, ou instale unha biblioteca dende o repositorio p\xFAblico, como se detalla a continuaci\xF3n.",hint_emptyPrivateLibrary:"Seleccione un elemento do lenzo para engadilo aqu\xED."},i={clearReset:"Limpar o lenzo",exportJSON:"Exportar a arquivo",exportImage:"Exportar imaxe...",export:"Gardar en...",copyToClipboard:"Copiar ao portapapeis",save:"Gardar no ficheiro actual",saveAs:"Gardar como",load:"Abrir",getShareableLink:"Obter unha ligaz\xF3n que se poida compartir",close:"Pechar",selectLanguage:"Seleccionar idioma",scrollBackToContent:"Volver ao contido",zoomIn:"Ampliar",zoomOut:"Reducir",resetZoom:"Reiniciar zoom",menu:"Men\xFA",done:"Feito",edit:"Editar",undo:"Desfacer",redo:"Refacer",resetLibrary:"Reiniciar biblioteca",createNewRoom:"Crear nova sala",fullScreen:"Pantalla completa",darkMode:"Modo escuro",lightMode:"Modo claro",zenMode:"Modo zen",objectsSnapMode:"",exitZenMode:"Sa\xEDr do modo zen",cancel:"Cancelar",clear:"Limpar",remove:"Eliminar",embed:"",publishLibrary:"Publicar",submit:"Enviar",confirm:"Confirmar",embeddableInteractionButton:"Faga clic para interactuar"},n={clearReset:"Isto limpar\xE1 todo o lenzo. Est\xE1s seguro?",couldNotCreateShareableLink:"Non se puido crear unha ligaz\xF3n para compartir.",couldNotCreateShareableLinkTooBig:"Non se puido crear a ligaz\xF3n para compartir: a escena \xE9 demasiado grande",couldNotLoadInvalidFile:"Non se puido cargar o ficheiro non v\xE1lido",importBackendFailed:"A importaci\xF3n dende o backend fallou.",cannotExportEmptyCanvas:"Non se pode exportar un lenzo baleiro.",couldNotCopyToClipboard:"Non se puido copiar ao portapapeis.",decryptFailed:"Non se poideron descifrar os datos.",uploadedSecurly:"A carga foi asegurada con cifrado de extremo a extremo, o que significa que o servidor de Excalidraw e terceiros non poden ler o contido.",loadSceneOverridePrompt:"A carga dun debuxo externo substituir\xE1 o contido existente. Desexa continuar?",collabStopOverridePrompt:`Deter a sesi\xF3n, sobrescribir\xE1 o seu debuxo local previamente almacenado. Est\xE1 seguro?

(Se quere manter o seu debuxo local, simplemente peche a lapela do navegador.)`,errorAddingToLibrary:"Non se puido engadir o elemento \xE1 biblioteca",errorRemovingFromLibrary:"Non se puido eliminar o elemento da biblioteca",confirmAddLibrary:"Isto engadir\xE1 {{numShapes}} forma(s) a t\xFAa biblioteca. Est\xE1s seguro?",imageDoesNotContainScene:"Esta imaxe non parece conter ning\xFAn dato da escena. Activou a inserci\xF3n de escenas durante a exportaci\xF3n?",cannotRestoreFromImage:"Non se puido restaurar a escena dende este arquivo de imaxe",invalidSceneUrl:"Non se puido importar a escena dende a URL proporcionada. Ou ben est\xE1 malformada ou non cont\xE9n un JSON con informaci\xF3n v\xE1lida para Excalidraw.",resetLibrary:"Isto limpar\xE1 a s\xFAa biblioteca. Est\xE1 seguro?",removeItemsFromsLibrary:"Eliminar {{count}} elemento(s) da biblioteca?",invalidEncryptionKey:"A clave de cifrado debe ter 22 caracteres. A colaboraci\xF3n en directo est\xE1 desactivada.",collabOfflineWarning:`Non hai conexi\xF3n a Internet dispo\xF1ible.
Os teus cambios non ser\xE1n gardados!`},t={unsupportedFileType:"Tipo de ficheiro non soportado.",imageInsertError:"Non se puido inserir a imaxe. Probe de novo m\xE1is tarde...",fileTooBig:"O ficheiro \xE9 demasiado grande. O tama\xF1o m\xE1ximo permitido \xE9 {{maxSize}}.",svgImageInsertError:"Non se puido inserir como imaxe SVG. O marcado SVG semella inv\xE1lido.",failedToFetchImage:"",invalidSVGString:"SVG inv\xE1lido.",cannotResolveCollabServer:"Non se puido conectar ao servidor de colaboraci\xF3n. Por favor recargue a p\xE1xina e probe de novo.",importLibraryError:"Non se puido cargar a biblioteca",collabSaveFailed:"Non se puido gardar na base de datos. Se o problema persiste, deber\xEDas gardar o teu arquivo de maneira local para asegurarte de non perdelo teu traballo.",collabSaveFailed_sizeExceeded:"Non se puido gardar na base de datos, o lenzo semella demasiado grande. Deber\xEDas gardar o teu arquivo de maneira local para asegurarte de non perdelo teu traballo.",imageToolNotSupported:"",brave_measure_text_error:{line1:"",line2:"",line3:"",line4:""},libraryElementTypeError:{embeddable:"",iframe:"",image:""},asyncPasteFailedOnRead:"",asyncPasteFailedOnParse:"",copyToSystemClipboardFailed:""},s={selection:"Selecci\xF3n",image:"Inserir imaxe",rectangle:"Rect\xE1ngulo",diamond:"Diamante",ellipse:"Elipse",arrow:"Frecha",line:"Li\xF1a",freedraw:"Debuxar",text:"Texto",library:"Biblioteca",lock:"Manter a ferramenta seleccionada activa despois de debuxar",penMode:"Modo lapis - evitar o contacto",link:"Engadir/ Actualizar ligaz\xF3n para a forma seleccionada",eraser:"Goma de borrar",frame:"",magicframe:"",embeddable:"Inserir na web",laser:"Punteiro l\xE1ser",hand:"Man (ferramenta de desprazamento)",extraTools:"M\xE1is ferramentas",mermaidToExcalidraw:"",magicSettings:""},l={canvasActions:"Acci\xF3ns do lenzo",selectedShapeActions:"Acci\xF3ns da forma seleccionada",shapes:"Formas"},d={canvasPanning:"Para mover o lenzo, mante\xF1a pulsada a roda do rato ou a barra de espazo mentres arrastra, ou utilice a ferramenta da man",linearElement:"Faga clic para iniciar varios puntos, arrastre para unha sola li\xF1a",freeDraw:"Fai clic e arrastra, solta cando acabes",text:"Consello: tam\xE9n podes engadir texto facendo dobre-clic en calquera lugar coa ferramenta de selecci\xF3n",embeddable:"Faga clic e arrastre para crear un sitio web embebido",text_selected:"Dobre-clic ou prema ENTER para editar o texto",text_editing:"Prema Escape ou CtrlOrCmd+ENTER para finalizar a edici\xF3n",linearElementMulti:"Faga clic no \xFAltimo punto ou prema Escape ou Enter para rematar",lockAngle:"Pode reducir o \xE1ngulo mantendo SHIFT",resize:`Pode reducir as proporci\xF3ns mantendo SHIFT mentres axusta o tama\xF1o,
mante\xF1a ALT para axustalo dende o centro`,resizeImage:`Pode axustar o tama\xF1o libremente mantendo SHIFT,
mante\xF1a ALT para axustalo dende o centro`,rotate:"Podes reducir os \xE1ngulos mantendo SHIFT mentres os rotas",lineEditor_info:"Mante\xF1a pulsado CtrlOrCmd e faga dobre clic ou prema CtrlOrCmd + Enter para editar puntos",lineEditor_pointSelected:`Prema Suprimir para eliminar o(s) punto(s)
CtrlOrCmd+D para duplicalos, ou arrastre para movelos`,lineEditor_nothingSelected:`Seleccione un punto para editar (mante\xF1a pulsado SHIFT para selecci\xF3n m\xFAltiple),
ou mante\xF1a pulsado Alt e faga clic para engadir novos puntos`,placeImage:"Faga clic para colocar a imaxe, ou faga clic e arrastre para establecer o seu tama\xF1o manualmente",publishLibrary:"Publica a t\xFAa propia biblioteca",bindTextToElement:"Prema a tecla enter para engadir texto",deepBoxSelect:"Mante\xF1a pulsado CtrlOrCmd para seleccionar en profundidade e evitar o arrastre",eraserRevert:"Mante\xF1a pulsado Alt para reverter os elementos marcados para a s\xFAa eliminaci\xF3n",firefox_clipboard_write:'Esta funci\xF3n p\xF3dese activar establecendo a opci\xF3n "dom.events.asyncClipboard.clipboardItem" a "true". Para cambiar as opci\xF3ns do navegador en Firefox, visita a p\xE1xina "about:config".',disableSnapping:""},c={cannotShowPreview:"Non se pode mostrar a vista previa",canvasTooBig:"Pode que o lenzo sexa demasiado grande.",canvasTooBigTip:"Consello: Probe a acercar un pouco os elementos m\xE1is afastados."},u={headingMain:"Atopouse un erro. Probe <button>recargando a p\xE1xina.</button>",clearCanvasMessage:"Se recargar non funcionou, probe <button>limpando o lenzo.</button>",clearCanvasCaveat:" Isto resultar\xE1 nunha perda do seu traballo ",trackedToSentry:"O erro con identificador {{eventId}} foi rastrexado no noso sistema.",openIssueMessage:"Fomos moi cautelosos de non inclu\xEDr a informaci\xF3n da s\xFAa escena no erro. Se a s\xFAa escena non \xE9 privada, por favor, considere o seguimento do noso <button>rastrexador de erros.</button> Por favor incl\xFAa a seguinte informaci\xF3n copi\xE1ndoa e peg\xE1ndoa na issue de Github.",sceneContent:"Contido da escena:"},p={desc_intro:"Podes invitar xente a colaborar contigo na t\xFAa escena actual.",desc_privacy:"Non te preocupes, a sesi\xF3n usa cifrado de punto a punto, polo que calquera cousa que debuxes mantense privada. Nin tan sequera o noso servidor ser\xE1 capaz de ver o que fas.",button_startSession:"Comezar sesi\xF3n",button_stopSession:"Rematar sesi\xF3n",desc_inProgressIntro:"A sesi\xF3n de colaboraci\xF3n en directo est\xE1 agora en progreso.",desc_shareLink:"Comparte esta ligaz\xF3n con calquera que queiras colaborar:",desc_exitSession:"Deter a sesi\xF3n desconectarao da sala, pero poder\xE1 seguir traballando coa escena de maneira local. Te\xF1a en conta que isto non afectar\xE1 a outras persoas, que poder\xE1n seguir colaborando na s\xFAa versi\xF3n.",shareTitle:"\xDAnase a unha sesi\xF3n de colaboraci\xF3n en directo en Excalidraw"},m={title:"Erro"},b={disk_title:"Gardar no disco",disk_details:"Exporte os datos da escena a un ficheiro que poder\xE1s importar m\xE1is tarde.",disk_button:"Gardar nun ficheiro",link_title:"Ligaz\xF3n para compartir",link_details:"Exportar como unha ligaz\xF3n de s\xF3 lectura.",link_button:"Exportar a unha ligaz\xF3n",excalidrawplus_description:"Garde a escena no seu espazo de traballo en Excalidraw+.",excalidrawplus_button:"Exportar",excalidrawplus_exportError:"Non se puido exportar a Excalidraw+ neste momento..."},g={blog:"Le o noso blog",click:"clic",deepSelect:"Selecci\xF3n en profundidade",deepBoxSelect:"Selecci\xF3n en profundidade dentro da caixa, evitando o arrastre",curvedArrow:"Frecha curva",curvedLine:"Li\xF1a curva",documentation:"Documentaci\xF3n",doubleClick:"dobre-clic",drag:"arrastrar",editor:"Editor",editLineArrowPoints:"",editText:"",github:"Encontrou un problema? Env\xEDeo",howto:"Sigue as nosas normas",or:"ou",preventBinding:"Evitar a uni\xF3n de frechas",tools:"Ferramentas",shortcuts:"Atallos de teclado",textFinish:"Rematar de editar (editor de texto)",textNewLine:"Engadir unha nova li\xF1a (editor de texto)",title:"Axuda",view:"Vista",zoomToFit:"Zoom que se axuste a todos os elementos",zoomToSelection:"Zoom \xE1 selecci\xF3n",toggleElementLock:"Bloquear/desbloquear selecci\xF3n",movePageUpDown:"Mover p\xE1xina cara enriba/abaixo",movePageLeftRight:"Mover p\xE1xina cara a esquerda/dereita"},x={title:"Limpar lenzo"},v={title:"Publicar biblioteca",itemName:"Nome do elemento",authorName:"Nome do autor",githubUsername:"Nome de usuario en Github",twitterUsername:"Nome de usuario en Twitter",libraryName:"Nome da biblioteca",libraryDesc:"Descrici\xF3n da biblioteca",website:"P\xE1xina web",placeholder:{authorName:"O seu nome ou nome de usuario",libraryName:"Nome da s\xFAa biblioteca",libraryDesc:"Descrici\xF3n da s\xFAa biblioteca para axudar a xente a entender o seu uso",githubHandle:"Nome de usuario de GitHub (opcional), as\xED poder\xE1s editar a biblioteca unha vez enviada para a s\xFAa revisi\xF3n",twitterHandle:"Nome de usuario en Twitter(opcional), as\xED sabemos a quen darlle cr\xE9dito cando se lle de promoci\xF3n a trav\xE9s de Twitter",website:"Ligaz\xF3n ao teu sitio web persoal ou a outro sitio (opcional)"},errors:{required:"Obrigatorio",website:"Introduza unha URL v\xE1lida"},noteDescription:"Env\xEDe a s\xFAa biblioteca para que sexa inclu\xEDda no <link>repositorio p\xFAblico de bibliotecas</link>para que outra xente a poida usar nos seus debuxos.",noteGuidelines:"A biblioteca necesita ser aprobada manualmente primeiro. Por favor, lea as <link>normas</link> antes de ser enviado. Necesitar\xE1s unha conta de GitHub para comunicarte ou facer cambios se se solicitan, pero non \xE9 estritamente necesario.",noteLicense:"Ao enviar, est\xE1s de acordo con que a biblioteca sexa publicada baixo a <link>Licenza MIT, </link>o cal significa que, en resumo, calquera pode usalo sen restrici\xF3ns.",noteItems:"Cada elemento da biblioteca debe ter o seu nome propio para que se poida filtrar. Os seguintes elementos da biblioteca ser\xE1n inclu\xEDdos:",atleastOneLibItem:"Por favor seleccione polo menos un elemento da biblioteca para comezar",republishWarning:"Nota: alg\xFAns dos elementos seleccionados est\xE1n marcados como xa publicados/enviados. S\xF3 deber\xEDas reenviar elementos cando se actualice unha biblioteca ou env\xEDo."},h={title:"Biblioteca enviada",content:"Grazas {{authorName}}. A s\xFAa biblioteca foi enviada para ser revisada. Pode seguir o estado<link>aqu\xED</link>"},E={resetLibrary:"Restablecer biblioteca",removeItemsFromLib:"Eliminar os elementos seleccionados da biblioteca"},f={header:"Exportar imaxe",label:{withBackground:"Fondo",onlySelected:"",darkMode:"Modo escuro",embedScene:"",scale:"",padding:""},tooltip:{embedScene:""},title:{exportToPng:"Exportar a PNG",exportToSvg:"Exportar a SVG",copyPngToClipboard:"Copiar PNG ao portapapeis"},button:{exportToPng:"PNG",exportToSvg:"SVG",copyPngToClipboard:"Copiar ao portapapeis"}},S={tooltip:"Os teus debuxos est\xE1n cifrados de punto a punto, polo que os servidores de Excalidraw nunca os ver\xE1n.",link:"Entrada do blog acerca do cifrado de punto a punto en Excalidraw"},C={angle:"\xC1ngulo",element:"Elemento",elements:"Elementos",height:"Alto",scene:"Escena",selected:"Seleccionado",storage:"Almacenamento",title:"Estad\xEDsticas para nerds",total:"Total",version:"Versi\xF3n",versionCopy:"Faga clic para copiar",versionNotAvailable:"Versi\xF3n non dispo\xF1ible",width:"Ancho"},y={addedToLibrary:"Engadido \xE1 biblioteca",copyStyles:"Estilos copiados.",copyToClipboard:"Copiado ao portapapeis.",copyToClipboardAsPng:`Copiar {{exportSelection}} ao portapapeis como PNG
({{exportColorScheme}})`,fileSaved:"Ficheiro gardado.",fileSavedToFilename:"Gardado en {filename}",canvas:"lenzo",selection:"selecci\xF3n",pasteAsSingleElement:`Usa {{shortcut}} para pegar como un \xFAnico elemento
ou pega nun editor de texto existente`,unableToEmbed:"",unrecognizedLinkFormat:""},z={transparent:"Transparente",black:"Negro",white:"Branco",red:"Vermello",pink:"Rosa",grape:"Uva",violet:"Violeta",gray:"Gris",blue:"Azul",cyan:"",teal:"",green:"Verde",yellow:"Marelo",orange:"Laranxa",bronze:"Bronce"},T={app:{center_heading:"Toda a informaci\xF3n \xE9 gardada de maneira local no seu navegador.",center_heading_plus:"Queres ir a Excalidraw+ no seu lugar?",menuHint:"Exportar, preferencias, idiomas, ..."},defaults:{menuHint:"Exportar, preferencias, e m\xE1is...",center_heading:"Diagramas. Feito. Sinxelo.",toolbarHint:"Escolle unha ferramenta & Comeza a debuxar!",helpHint:"Atallos & axuda"}},w={mostUsedCustomColors:"",colors:"Cores",shades:"",hexCode:"",noShades:""},P={action:{exportToImage:{title:"Exportar como imaxe",button:"Exportar como imaxe",description:""},saveToDisk:{title:"Gardar no disco",button:"Gardar no disco",description:""},excalidrawPlus:{title:"Excalidraw+",button:"Exportar a Excalidraw+",description:""}},modal:{loadFromFile:{title:"Cargar dende arquivo",button:"Cargar dende arquivo",description:""},shareableLink:{title:"Cargar dende un enlace",button:"Substitu\xEDr o meu contido",description:""}}},A={title:"",button:"",description:"",syntax:"",preview:""},N={labels:o,library:r,buttons:i,alerts:n,errors:t,toolBar:s,headings:l,hints:d,canvasError:c,errorSplash:u,roomDialog:p,errorDialog:m,exportDialog:b,helpDialog:g,clearCanvasDialog:x,publishDialog:v,publishSuccessDialog:h,confirmDialog:E,imageExportDialog:f,encrypted:S,stats:C,toast:y,colors:z,welcomeScreen:T,colorPicker:w,overwriteConfirm:P,mermaid:A};export{n as alerts,i as buttons,c as canvasError,x as clearCanvasDialog,w as colorPicker,z as colors,E as confirmDialog,N as default,S as encrypted,m as errorDialog,u as errorSplash,t as errors,b as exportDialog,l as headings,g as helpDialog,d as hints,f as imageExportDialog,o as labels,r as library,A as mermaid,P as overwriteConfirm,v as publishDialog,h as publishSuccessDialog,p as roomDialog,C as stats,y as toast,s as toolBar,T as welcomeScreen};
