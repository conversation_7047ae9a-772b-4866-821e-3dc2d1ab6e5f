"use client"

import React, { use<PERSON><PERSON>back, useEffect, useState, useRef } from "react"
import Editor from "@monaco-editor/react"
import * as monaco from "monaco-editor"
import { motion, AnimatePresence } from "framer-motion"
import { FiUsers, FiCopy, FiCode, FiPlay, FiLoader, FiAlignLeft, FiDownload, FiUpload, FiSearch, FiSun, FiMoon, FiHelpCircle } from "react-icons/fi"
import SocketService from "../services/socketService"
import { throttle, debounce } from "lodash"
import { useSocket } from "../context/SocketContext"
import axios from "axios"
import { formatCode } from "../utils/formatCode"
import { useRouter } from "next/navigation"
import { useTheme } from "../context/ThemeContext"
import { useEditPermission } from "../context/EditPermissionContext"
//import { editor as MonacoEditor, IStandaloneCodeEditor, IEditorMouseEvent, ICursorSelectionChangedEvent, IContextMenuEvent } from 'monaco-editor';

interface CodeEditorProps {
  roomId: string
  username: string
}

export default function CodeEditor({ roomId, username: initialUsername }: CodeEditorProps) {
  const { isConnected } = useSocket()
  const { canEdit, isTeacher, setIsTeacher } = useEditPermission()
  const editorRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null)
  const [code, setCode] = useState<string>("// Start coding...")
  // Use a ref to always track the latest code value
  const latestCodeRef = useRef<string>("// Start coding...")

  // Get username from localStorage if available, otherwise use initialUsername
  const storedUsername = typeof window !== 'undefined' ? localStorage.getItem("username") : null
  console.log(`Initial username: ${initialUsername}, Stored username: ${storedUsername}`)

  // Track the username internally to handle server validation
  const [username, setUsername] = useState(storedUsername || initialUsername)
  const [typingUser, setTypingUser] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeUsers, setActiveUsers] = useState<string[]>([])
  const [showUserList, setShowUserList] = useState(false)
  const [language, setLanguage] = useState("javascript")
  const [copySuccess, setCopySuccess] = useState(false)
  // Get user role from localStorage if available, otherwise null
  const storedUserRole = typeof window !== 'undefined' ? localStorage.getItem("userRole") : null
  const [userRole, setUserRole] = useState<string | null>(storedUserRole)

  // Update isTeacher in context when userRole changes
  useEffect(() => {
    if (userRole === 'teacher') {
      setIsTeacher(true);
    }
  }, [userRole, setIsTeacher]);

  // Code execution states
  const [isExecuting, setIsExecuting] = useState(false)
  const [executionOutput, setExecutionOutput] = useState<string | null>(null)
  const [executionError, setExecutionError] = useState<string | null>(null)
  const [showOutput, setShowOutput] = useState(false)

  // Piston API runtimes
  const [runtimes, setRuntimes] = useState<Array<{
    language: string;
    version: string;
    aliases: string[];
    runtime?: string;
  }>>([]);
  const [runtimesLoaded, setRuntimesLoaded] = useState(false);

  const router = useRouter();

  // Theme and minimap state (ensure these are always defined at the top)
  const { theme, setTheme } = useTheme();
  const [showShortcuts, setShowShortcuts] = useState(false);
  const [minimapEnabled, setMinimapEnabled] = useState(true);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  // Language-specific code snippets
  const SNIPPETS: Record<string, { label: string; value: string }[]> = {
    javascript: [
      { label: "Function", value: "function myFunction() {\n  // code\n}" },
      { label: "For Loop", value: "for (let i = 0; i < 10; i++) {\n  // code\n}" },
      { label: "If Statement", value: "if (condition) {\n  // code\n}" },
      { label: "Console Log", value: "console.log('Hello, World!');" },
    ],
    typescript: [
      { label: "Function", value: "function myFunction(): void {\n  // code\n}" },
      { label: "For Loop", value: "for (let i = 0; i < 10; i++) {\n  // code\n}" },
      { label: "If Statement", value: "if (condition) {\n  // code\n}" },
      { label: "Console Log", value: "console.log('Hello, World!');" },
      { label: "Type Declaration", value: "type MyType = {\n  key: string;\n};" },
    ],
    python3: [
      { label: "Function", value: "def my_function():\n    # code\n    pass" },
      { label: "For Loop", value: "for i in range(10):\n    # code" },
      { label: "If Statement", value: "if condition:\n    # code" },
      { label: "Print", value: "print('Hello, World!')" },
    ],
    java: [
      { label: "Main Method", value: "public static void main(String[] args) {\n    // code\n}" },
      { label: "For Loop", value: "for (int i = 0; i < 10; i++) {\n    // code\n}" },
      { label: "If Statement", value: "if (condition) {\n    // code\n}" },
      { label: "Print", value: "System.out.println(\"Hello, World!\");" },
    ],
    csharp: [
      { label: "Main Method", value: "static void Main(string[] args) {\n    // code\n}" },
      { label: "For Loop", value: "for (int i = 0; i < 10; i++) {\n    // code\n}" },
      { label: "If Statement", value: "if (condition) {\n    // code\n}" },
      { label: "Print", value: "Console.WriteLine(\"Hello, World!\");" },
    ],
    c: [
      { label: "Main Function", value: "int main() {\n    // code\n    return 0;\n}" },
      { label: "For Loop", value: "for (int i = 0; i < 10; i++) {\n    // code\n}" },
      { label: "If Statement", value: "if (condition) {\n    // code\n}" },
      { label: "Print", value: "printf(\"Hello, World!\\n\");" },
    ],
    cpp: [
      { label: "Main Function", value: "int main() {\n    // code\n    return 0;\n}" },
      { label: "For Loop", value: "for (int i = 0; i < 10; i++) {\n    // code\n}" },
      { label: "If Statement", value: "if (condition) {\n    // code\n}" },
      { label: "Print", value: "std::cout << \"Hello, World!\" << std::endl;" },
    ],
    go: [
      { label: "Main Function", value: "func main() {\n    // code\n}" },
      { label: "For Loop", value: "for i := 0; i < 10; i++ {\n    // code\n}" },
      { label: "If Statement", value: "if condition {\n    // code\n}" },
      { label: "Print", value: "fmt.Println(\"Hello, World!\")" },
    ],
    ruby: [
      { label: "Method", value: "def my_method\n  # code\nend" },
      { label: "For Loop", value: "for i in 0..9\n  # code\nend" },
      { label: "If Statement", value: "if condition\n  # code\nend" },
      { label: "Print", value: "puts 'Hello, World!'" },
    ],
    rust: [
      { label: "Main Function", value: "fn main() {\n    // code\n}" },
      { label: "For Loop", value: "for i in 0..10 {\n    // code\n}" },
      { label: "If Statement", value: "if condition {\n    // code\n}" },
      { label: "Print", value: "println!(\"Hello, World!\");" },
    ],
    php: [
      { label: "Function", value: "function myFunction() {\n    // code\n}" },
      { label: "For Loop", value: "for ($i = 0; $i < 10; $i++) {\n    // code\n}" },
      { label: "If Statement", value: "if ($condition) {\n    // code\n}" },
      { label: "Print", value: "echo 'Hello, World!';" },
    ],
  };

  // Declare the `socketService` instance at the top of the file
  const socketService = SocketService.getInstance();

  // Leave room handler
  const handleLeaveRoom = () => {
    socketService.leaveRoom(roomId);
    router.push("/dashboard");
  };

  // --- Teacher Selection Highlighting and Cursor Sharing ---

  // Add custom CSS for teacher selection and cursor if not already present
  useEffect(() => {
    if (typeof window !== 'undefined' && !document.getElementById('teacher-highlighting-css')) {
      const style = document.createElement('style');
      style.id = 'teacher-highlighting-css';
      style.innerHTML = `
        .teacher-selection-highlight {
          background: rgba(255, 230, 80, 0.5) !important;
          border-bottom: 2px solid orange !important;
          border-radius: 2px;
        }
        .teacher-cursor-highlight {
          border-left: 2px solid orange !important;
          margin-left: -1px;
          animation: teacher-cursor-blink 1s steps(2, start) infinite;
        }
        @keyframes teacher-cursor-blink {
          0%, 100% { opacity: 1; }
          50% { opacity: 0; }
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  // Store decoration ids for cleanup
  const [teacherSelectionDecorations, setTeacherSelectionDecorations] = useState<string[]>([]);
  const [teacherCursorDecorations, setTeacherCursorDecorations] = useState<string[]>([]);
  // Add state for teacher cursor position (for custom blinking cursor overlay)
  const [teacherCursorPosition, setTeacherCursorPosition] = useState<{
    lineNumber: number;
    column: number;
    teacherName: string;
  } | null>(null);

  // State to track current teacher selection and cursor for syncing to new users
  const [currentTeacherSelection, setCurrentTeacherSelection] = useState<any>(null)
  const [currentTeacherCursor, setCurrentTeacherCursor] = useState<{ lineNumber: number; column: number } | null>(null)

  // Update editor read-only state based on permissions
  useEffect(() => {
    if (editorRef.current) {
      const editor = editorRef.current;
      const shouldBeReadOnly = !canEdit && userRole !== 'teacher';

      console.log(`🔒 Updating editor read-only state: ${shouldBeReadOnly} (canEdit: ${canEdit}, userRole: ${userRole})`);

      editor.updateOptions({
        readOnly: shouldBeReadOnly,
        // Disable additional features when read-only
        contextmenu: !shouldBeReadOnly,
        quickSuggestions: !shouldBeReadOnly,
        parameterHints: { enabled: !shouldBeReadOnly },
        suggestOnTriggerCharacters: !shouldBeReadOnly,
        acceptSuggestionOnEnter: shouldBeReadOnly ? 'off' : 'on',
        tabCompletion: shouldBeReadOnly ? 'off' : 'on',
        wordBasedSuggestions: shouldBeReadOnly ? 'off' : 'allDocuments',
        // Visual indicators for read-only mode
        renderLineHighlight: shouldBeReadOnly ? 'none' : 'line',
        cursorStyle: shouldBeReadOnly ? 'line-thin' : 'line'
      });

      // Add visual styling for read-only mode
      const editorElement = editor.getDomNode();
      if (editorElement) {
        if (shouldBeReadOnly) {
          editorElement.style.opacity = '0.7';
          editorElement.style.pointerEvents = 'none';
          editorElement.title = '👁️ View-only mode - You need edit permission to modify the code';
        } else {
          editorElement.style.opacity = '1';
          editorElement.style.pointerEvents = 'auto';
          editorElement.title = '✏️ Edit mode - You can modify the code';
        }
      }
    }
  }, [canEdit, userRole]);

  // Handle editor mounting
  const handleEditorDidMount = (editor: monaco.editor.IStandaloneCodeEditor) => {
    editorRef.current = editor
    setIsLoading(false)

    // Auto-focus the editor after mounting (only if user can edit)
    setTimeout(() => {
      if (editor && (canEdit || userRole === 'teacher')) {
        editor.focus()

        // Add keyboard shortcut (Ctrl+Enter) to run code
        editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
          executeCode()
        })

        // Add keyboard shortcut (Shift+Alt+F) to format code
        editor.addCommand(monaco.KeyMod.Shift | monaco.KeyMod.Alt | monaco.KeyCode.KeyF, () => {
          formatCurrentCode()
        })

        // Add teacher cursor and selection listeners
        if (userRole === 'teacher') {
          // Track cursor position changes - emit 'teacher-cursor' event
          editor.onDidChangeCursorPosition((e) => {
            const position = e.position;
            const socketServiceInstance = SocketService.getInstance();

            if (roomId) {
              console.log('🎯 Teacher cursor position changed:', {
                lineNumber: position.lineNumber,
                column: position.column
              });

              // Update current teacher cursor state for syncing to new users
              setCurrentTeacherCursor({
                lineNumber: position.lineNumber,
                column: position.column
              });

              // Send cursor position to students using teacher-cursor-position event
              socketServiceInstance.sendTeacherCursorPosition(roomId, {
                lineNumber: position.lineNumber,
                column: position.column
              });
            }
          });

          // Track selection changes - emit 'teacher-selection' event
          editor.onDidChangeCursorSelection((e) => {
            console.log('🎯 Teacher cursor selection changed:', {
              selection: e.selection,
              isEmpty: e.selection.isEmpty(),
              userRole: userRole,
              roomId: roomId
            });

            const selection = e.selection;
            const socketServiceInstance = SocketService.getInstance();

            if (!selection.isEmpty() && roomId) {
              // Send selection range to students using teacher-selection event
              console.log('📤 Teacher sending selection range to students:', {
                startLineNumber: selection.startLineNumber,
                startColumn: selection.startColumn,
                endLineNumber: selection.endLineNumber,
                endColumn: selection.endColumn
              });

              const selectionData = {
                startLineNumber: selection.startLineNumber,
                startColumn: selection.startColumn,
                endLineNumber: selection.endLineNumber,
                endColumn: selection.endColumn
              };

              // Update current teacher selection state for syncing to new users
              setCurrentTeacherSelection(selectionData);

              // Use both teacher-selection (for range highlighting) and teacher-text-highlight (for compatibility)
              socketServiceInstance.sendTeacherSelection(roomId, selectionData);
              socketServiceInstance.sendTeacherTextHighlight(roomId, selectionData);
            } else if (roomId) {
              // Clear selection when teacher deselects
              console.log('🧹 Teacher clearing selection');
              setCurrentTeacherSelection(null);
              socketServiceInstance.clearTeacherSelection(roomId);
              socketServiceInstance.clearTeacherTextHighlight(roomId);
            }
          });
        }
      }
    }, 500) // Small delay to ensure the editor is fully ready
  }

  // Track if the change is from a remote update
  const isRemoteUpdate = useRef(false);

  // Create a throttled version of the code change handler
  // This prevents sending too many updates when typing quickly
  // but ensures updates are sent at regular intervals
  const throttledCodeChange = useRef(
    throttle((code: string) => {
      if (roomId && isConnected) {
        console.log(`Sending throttled code update to room ${roomId}, length: ${code.length}`);
        socketService.sendCodeChange(roomId, code);
      }
    }, 50) // Use throttle with a short interval for more responsive updates
  ).current;

  // Create a debounced version of the typing notification
  const debouncedTypingNotification = useCallback(
    debounce(() => {
      if (roomId && isConnected) {
        // Always send the current username with typing notifications
        console.log(`Sending typing notification with username: ${username}`);
        socketService.sendTyping(roomId, username);
      }
    }, 1000),
    [roomId, isConnected, username]
  );

  // Handle editor change
  const handleEditorChange = (value?: string) => {
    if (typeof value !== "string") return;

    // Make sure loading is off during code changes
    setIsLoading(false);

    // If this change is from a remote update, just update the state and return
    if (isRemoteUpdate.current) {
      console.log("Handling remote update, not emitting");
      setCode(value);
      latestCodeRef.current = value; // Update the ref
      isRemoteUpdate.current = false;
      return;
    }

    // Check if user has edit permission (teachers always have permission)
    if (!canEdit && userRole !== 'teacher') {
      console.log("🔒 User doesn't have edit permission, ignoring change");
      return;
    }

    // Only process if the value actually changed
    if (value !== latestCodeRef.current) {
      // Update local state immediately
      setCode(value);
      latestCodeRef.current = value; // Update the ref

      // Send typing notification (debounced)
      debouncedTypingNotification();

      // Send code update (throttled)
      throttledCodeChange(value);

      // If not connected, try to reconnect
      if (!isConnected) {
        console.log("Socket not connected, attempting to reconnect");
        socketService.connect();
      }
    }
  }

  // Copy code to clipboard
  const copyCodeToClipboard = () => {
    if (navigator.clipboard && code) {
      navigator.clipboard.writeText(code)
        .then(() => {
          setCopySuccess(true)
          setTimeout(() => setCopySuccess(false), 2000)
        })
        .catch(err => {
          console.error('Failed to copy code: ', err)
        })
    }
  }

  // Toggle user list
  const toggleUserList = () => {
    setShowUserList(prev => !prev)
  }

  // Change language
  const changeLanguage = (lang: string) => {
    setLanguage(lang)

    // Log available runtimes for this language
    if (runtimesLoaded && runtimes.length > 0) {
      const availableRuntimes = runtimes.filter(runtime =>
        runtime.language === lang ||
        (runtime.aliases && runtime.aliases.includes(lang))
      )

      if (availableRuntimes.length > 0) {
        console.log(`Available runtimes for ${lang}:`, availableRuntimes);
      } else {
        console.log(`No runtimes available for ${lang}`);
      }
    }
  }

  // Format code
  const formatCurrentCode = () => {
    if (!code || !editorRef.current) return;

    try {
      // Format the code based on the current language
      const formattedCode = formatCode(code, language);

      // Only update if the code actually changed
      if (formattedCode !== code) {
        // Update the editor
        const model = editorRef.current.getModel();
        if (model) {
          // Store current cursor position/selection
          const currentPosition = editorRef.current.getPosition();
          const currentSelection = editorRef.current.getSelection();

          // Update the editor content
          editorRef.current.executeEdits('format', [
            {
              range: model.getFullModelRange(),
              text: formattedCode,
              forceMoveMarkers: true
            }
          ]);

          // Restore cursor position if possible
          if (currentPosition) {
            editorRef.current.setPosition(currentPosition);
          }
          if (currentSelection) {
            editorRef.current.setSelection(currentSelection);
          }

          // Update state and ref
          setCode(formattedCode);
          latestCodeRef.current = formattedCode;

          // Send the formatted code to other users
          if (roomId && isConnected) {
            console.log(`Sending formatted code to room ${roomId}, length: ${formattedCode.length}`);
            socketService.sendCodeChange(roomId, formattedCode);
          }

          // Show a success message
          console.log('Code formatted successfully');
        }
      } else {
        console.log('Code is already formatted');
      }
    } catch (error) {
      console.error('Error formatting code:', error);
    }
  }

  // Clear execution output
  const clearOutput = () => {
    setExecutionOutput(null)
    setExecutionError(null)
  }

  // Fetch and store available runtimes from Piston API
  const fetchRuntimes = async () => {
    try {
      const response = await axios.get('https://emkc.org/api/v2/piston/runtimes');
      console.log('Piston API status:', response.status);
      console.log('Available runtimes:', response.data);

      // Store the runtimes in state
      setRuntimes(response.data);
      setRuntimesLoaded(true);

      return { success: true, data: response.data };
    } catch (error) {
      console.error('Error fetching Piston API runtimes:', error);
      return {
        success: false,
        error: axios.isAxiosError(error)
          ? `${error.message} - ${error.response?.status || 'Unknown'} ${error.response?.statusText || ''}`
          : String(error)
      };
    }
  };

  // Check if the Piston API is available
  const checkPistonAPI = async () => {
    // If we haven't loaded runtimes yet, fetch them
    if (!runtimesLoaded) {
      return await fetchRuntimes();
    }

    // If we already have runtimes, just return success
    if (runtimes.length > 0) {
      return { success: true, data: runtimes };
    }

    // If we've tried to load runtimes but have none, try again
    return await fetchRuntimes();
  };

  // Test the Piston API with a hardcoded sample payload
  const testPistonAPI = async () => {
    setIsExecuting(true)
    setExecutionError(null)
    setExecutionOutput(null)
    setShowOutput(true)

    try {
      // First check if the API is available
      const apiStatus = await checkPistonAPI();
      if (!apiStatus.success) {
        setExecutionError(`API Check Failed: ${apiStatus.error}\nThe Piston API might be down or unreachable.`);
        return;
      }

      // Find JavaScript runtime
      const jsRuntimes = apiStatus.data.filter((runtime: any) =>
        runtime.language === "javascript" ||
        (runtime.aliases && runtime.aliases.includes("javascript"))
      );

      if (jsRuntimes.length === 0) {
        setExecutionError('No JavaScript runtime found. Please try a different language.');
        return;
      }

      const jsRuntime = jsRuntimes[0];
      console.log(`Using JavaScript runtime: ${jsRuntime.language} ${jsRuntime.version}`);

      // Sample JavaScript code that should work
      const samplePayload = {
        language: jsRuntime.language,
        version: jsRuntime.version,
        files: [{
          name: "main.js",
          content: "console.log('Hello, World!');"
        }],
        stdin: "",
        args: []
      };

      console.log('Testing Piston API with sample payload:', JSON.stringify(samplePayload, null, 2));

      const response = await axios.post('https://emkc.org/api/v2/piston/execute', samplePayload);

      console.log('Sample execution response:', response.data);

      if (response.data.run) {
        setExecutionOutput(
          "API Test Successful!\n\n" +
          "Output: " + response.data.run.stdout + "\n\n" +
          "The API is working correctly. You can now run your own code."
        );
      } else {
        setExecutionError('API test failed. No run data returned.');
      }
    } catch (error) {
      console.error('Error testing Piston API:', error);

      if (axios.isAxiosError(error) && error.response) {
        setExecutionError(
          `API Test Failed: ${error.response.status} ${error.response.statusText}\n\n` +
          JSON.stringify(error.response.data, null, 2)
        );
      } else {
        setExecutionError(
          error instanceof Error
            ? `API Test Error: ${error.message}`
            : 'An unknown error occurred while testing the API.'
        );
      }
    } finally {
      setIsExecuting(false);
    }
  }

  // Execute code using Piston API
  const executeCode = async () => {
    if (!code || isExecuting) return

    // Map Monaco editor language to Piston API language
    const languageMap: Record<string, string> = {
      javascript: "javascript",
      typescript: "typescript",
      python: "python3",
      python3: "python3",
      java: "java",
      csharp: "csharp",
      c: "c",
      cpp: "cpp",
      go: "go",
      ruby: "ruby",
      rust: "rust",
      php: "php"
    }

    // Get the mapped language
    const pistonLanguage = languageMap[language] || language

    // Check if we have runtimes loaded
    if (!runtimesLoaded || runtimes.length === 0) {
      setExecutionError('Runtimes not loaded yet. Please try again in a moment.')
      setShowOutput(true)
      return
    }

    // Find available runtimes for the selected language
    const availableRuntimes = runtimes.filter(runtime =>
      runtime.language === pistonLanguage ||
      (runtime.aliases && runtime.aliases.includes(pistonLanguage))
    )

    // Check if the language is supported
    if (availableRuntimes.length === 0) {
      // Get a list of supported languages from the runtimes
      const supportedLanguages = [...new Set(runtimes.flatMap(runtime =>
        [runtime.language, ...(runtime.aliases || [])]
      ))].sort()

      setExecutionError(
        `The language '${language}' (mapped to '${pistonLanguage}') is not supported by the Piston API.\n\n` +
        `Supported languages: ${supportedLanguages.join(', ')}`
      )
      setShowOutput(true)
      return
    }

    // Get the latest version of the runtime
    const selectedRuntime = availableRuntimes[0]

    try {
      setIsExecuting(true)
      setExecutionError(null)
      setExecutionOutput(null)
      setShowOutput(true)

      // First check if the API is available
      const apiStatus = await checkPistonAPI();
      if (!apiStatus.success) {
        setExecutionError(`API Check Failed: ${apiStatus.error}\nThe Piston API might be down or unreachable.`);
        return;
      }

      // Determine file extension based on language
      let fileExtension = '';
      if (selectedRuntime.language === 'python3' || selectedRuntime.language === 'python') fileExtension = '.py';
      else if (selectedRuntime.language === 'javascript') fileExtension = '.js';
      else if (selectedRuntime.language === 'typescript') fileExtension = '.ts';
      else if (selectedRuntime.language === 'java') fileExtension = '.java';
      else if (selectedRuntime.language === 'csharp') fileExtension = '.cs';
      else if (selectedRuntime.language === 'c') fileExtension = '.c';
      else if (selectedRuntime.language === 'cpp') fileExtension = '.cpp';
      else if (selectedRuntime.language === 'go') fileExtension = '.go';
      else if (selectedRuntime.language === 'rust') fileExtension = '.rs';
      else if (selectedRuntime.language === 'ruby') fileExtension = '.rb';
      else if (selectedRuntime.language === 'php') fileExtension = '.php';
      else fileExtension = `.${selectedRuntime.language}`;

      console.log(`Selected runtime: ${selectedRuntime.language} ${selectedRuntime.version}`);

      // Prepare the payload according to Piston API documentation
      const payload = {
        language: selectedRuntime.language,
        version: selectedRuntime.version,
        files: [{
          name: `main${fileExtension}`,
          content: code
        }],
        stdin: '',
        args: [],
        compile_timeout: 10000,
        run_timeout: 5000
      };

      // Log the payload for debugging
      console.log(`Executing ${pistonLanguage} code with payload:`, JSON.stringify(payload, null, 2));

      // Make the API request
      const response = await axios.post('https://emkc.org/api/v2/piston/execute', payload);

      console.log('Execution response:', response.data);

      const result = response.data;

      if (result.run) {
        // Format the output
        let output = '';
        let hasOutput = false;

        // Add compile output if available (for compiled languages)
        if (result.compile && result.compile.stderr) {
          output += `Compilation output:\n${result.compile.stderr}\n\n`;
          hasOutput = true;
        }

        // Add standard output
        if (result.run.stdout) {
          output += result.run.stdout;
          hasOutput = true;
        }

        // Add error output
        if (result.run.stderr) {
          if (hasOutput) output += '\n';
          output += `Error output:\n${result.run.stderr}`;
          hasOutput = true;
        }

        // Add exit code if non-zero
        if (result.run.code !== 0) {
          if (hasOutput) output += '\n';
          output += `\nProcess exited with code ${result.run.code}`;
          hasOutput = true;
        }

        if (!hasOutput) {
          output = 'Program executed successfully with no output.';
        }

        setExecutionOutput(output);
      } else {
        setExecutionError('Failed to execute code. No run data returned.');
      }
    } catch (error) {
      console.error('Error executing code:', error)

      // Handle Axios errors with more detailed information
      if (axios.isAxiosError(error) && error.response) {
        const statusCode = error.response.status
        const responseData = error.response.data

        console.error('API Error Details:', {
          status: statusCode,
          data: responseData
        })

        // Format a more helpful error message
        if (statusCode === 400) {
          setExecutionError(
            `API Error (400 Bad Request): ${JSON.stringify(responseData)}\n\n` +
            'This usually means the API request format is incorrect. ' +
            'Please check the console for more details.'
          )
        } else if (statusCode === 429) {
          setExecutionError('Rate limit exceeded. Please try again later.')
        } else {
          setExecutionError(
            `API Error (${statusCode}): ${JSON.stringify(responseData)}\n\n` +
            'Please check the console for more details.'
          )
        }
      } else {
        // Handle non-Axios errors
        setExecutionError(
          error instanceof Error
            ? `Error: ${error.message}`
            : 'An unknown error occurred while executing the code.'
        )
      }
    } finally {
      setIsExecuting(false)
    }
  }

  // This function will be called when we receive a code update from another user
  const handleCodeUpdate = useCallback((incomingCode: string) => {
    console.log("Remote code update received, length:", incomingCode.length);

    // Make sure loading is off during code updates
    setIsLoading(false);

    // Only update if the code is different from our latest code
    if (incomingCode !== latestCodeRef.current) {
      try {
        // Set the flag to indicate this is a remote update
        isRemoteUpdate.current = true;

        // Update the editor if it's mounted
        if (editorRef.current) {
          console.log("Updating editor with remote code");
          // Use the editor's model to update the value to preserve cursor position
          const model = editorRef.current.getModel();
          if (model) {
            // Store current cursor position/selection
            const currentPosition = editorRef.current.getPosition();
            const currentSelection = editorRef.current.getSelection();

            // Use executeEdits with better handling of cursor position
            editorRef.current.executeEdits('remote', [
              {
                range: model.getFullModelRange(),
                text: incomingCode,
                forceMoveMarkers: true
              }
            ]);

            // Restore cursor position if possible
            if (currentPosition) {
              editorRef.current.setPosition(currentPosition);
            }
            if (currentSelection) {
              editorRef.current.setSelection(currentSelection);
            }

            // Also update our state and ref
            setCode(incomingCode);
            latestCodeRef.current = incomingCode;
          }
        } else {
          // If editor isn't mounted yet, just update the state and ref
          console.log("Editor not mounted, updating state only");
          setCode(incomingCode);
          latestCodeRef.current = incomingCode;
          isRemoteUpdate.current = false;
        }
      } catch (error) {
        console.error("Error updating editor with remote code:", error);
        isRemoteUpdate.current = false;
      }
    } else {
      console.log("Remote code matches current code, ignoring");
    }
  }, [])

  // State to track cursor positions
  const [cursorPositions, setCursorPositions] = useState<Record<string, { x: number; y: number }>>({});

  // Emit cursor movement
  const handleMouseMove = (e: React.MouseEvent) => {
    const position = { x: e.clientX, y: e.clientY };
    SocketService.getInstance().sendCursorMove(roomId, username, position);
  };

  // Listen for cursor movement
  useEffect(() => {
    const handleCursorMove = ({ userId, position }: { userId: string; position: { x: number; y: number } }) => {
      setCursorPositions((prev) => ({ ...prev, [userId]: position }));
    };

    SocketService.getInstance().on("cursor-move", handleCursorMove);

    return () => {
      SocketService.getInstance().off("cursor-move", handleCursorMove);
    };
  }, [roomId]);

  // Render cursors
  const renderCursors = () => {
    return Object.entries(cursorPositions).map(([userId, position]) => (
      <div
        key={userId}
        style={{
          position: "absolute",
          left: position.x,
          top: position.y,
          backgroundColor: "red",
          width: 10,
          height: 10,
          borderRadius: "50%",
          pointerEvents: "none",
        }}
      />
    ));
  };



  // Fetch runtimes when component mounts
  useEffect(() => {
    fetchRuntimes();
  }, []);

  // Handle request for initial code from new users
  const handleGetInitialCode = useCallback((data: { requestingUserId: string; requestingUsername: string }) => {
    console.log(`Received request for initial code from ${data.requestingUsername} (${data.requestingUserId})`);

    // Only respond if we have code and we're connected
    if (roomId && latestCodeRef.current && latestCodeRef.current.trim() !== "// Start coding...") {
      const socketServiceInstance = SocketService.getInstance();
      console.log(`Sending initial code to ${data.requestingUsername}, length: ${latestCodeRef.current.length}`);
      socketServiceInstance.sendInitialCode(roomId, latestCodeRef.current, data.requestingUserId);
    } else {
      console.log(`Not sending initial code - no meaningful code to share or not in room`);
    }
  }, [roomId]);

  // Handle receiving initial code as a new user
  const handleInitialCodeReceived = useCallback((data: { code: string }) => {
    console.log(`Received initial code, length: ${data.code.length}`);

    // Only apply initial code if we don't have meaningful code yet
    // This prevents overwriting code that the user might have already started typing
    if (latestCodeRef.current === "// Start coding..." || latestCodeRef.current.trim() === "") {
      console.log("Applying received initial code");

      // Set the flag to indicate this is a remote update
      isRemoteUpdate.current = true;

      // Update the code state and ref
      setCode(data.code);
      latestCodeRef.current = data.code;

      // Update the editor if it's mounted
      if (editorRef.current) {
        editorRef.current.setValue(data.code);
      }
    } else {
      console.log("Not applying initial code - user already has meaningful code");
    }
  }, []);

  // Handle teacher selection highlighting
  const handleTeacherSelection = useCallback((data: { selection: any; teacherName: string; teacherId: string }) => {
    console.log(`Received teacher selection from ${data.teacherName}:`, data.selection);

    // Extra debug: check if CSS class is present
    if (typeof window !== 'undefined') {
      const styleExists = !!document.querySelector('style#teacher-highlighting-css') || !!Array.from(document.styleSheets).find(sheet => {
        try {
          return Array.from(sheet.cssRules).some(rule => (rule as CSSStyleRule).selectorText?.includes('.teacher-selection-highlight'));
        } catch { return false; }
      });
      if (!styleExists) {
        console.warn('⚠️ teacher-selection-highlight CSS missing, injecting fallback');
        const style = document.createElement('style');
        style.id = 'teacher-highlighting-css-fallback';
        style.innerHTML = `
          .teacher-selection-highlight {
            background: rgba(255, 230, 80, 0.7) !important;
            border-bottom: 2px solid orange !important;
            border-radius: 2px;
          }
        `;
        document.head.appendChild(style);
      }
    }

    if (!editorRef.current || userRole === 'teacher') {
      console.log('Skipping teacher highlight: editor not available or user is teacher');
      return; // Don't show teacher highlights to the teacher themselves
    }

    try {
      const editor = editorRef.current;
      const model = editor.getModel();

      if (!model || !data.selection) {
        console.log('No model or selection');
        return;
      }

      // Convert selection to Monaco range
      const range = new monaco.Range(
        data.selection.startLineNumber,
        data.selection.startColumn,
        data.selection.endLineNumber,
        data.selection.endColumn
      );
      console.log('Applying teacher selection highlight with range:', range);

      // Clear previous teacher selection decorations and apply new ones with yellow/orange styling
      const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [
        {
          range: range,
          options: {
            className: 'teacher-selection-highlight', // Yellow background with orange border
            hoverMessage: {
              value: `🎯 Teacher ${data.teacherName} selected this text\n\nRange: Line ${data.selection.startLineNumber}-${data.selection.endLineNumber}`
            },
            stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,
            overviewRuler: {
              color: 'rgba(255, 152, 0, 0.8)', // Orange in overview ruler
              position: monaco.editor.OverviewRulerLane.Right
            }
          }
        }
      ]);
      console.log('New teacher selection decoration IDs:', newDecorations);
      setTeacherSelectionDecorations(newDecorations);

      // Debug: Check DOM for highlight
      setTimeout(() => {
        const elements = document.querySelectorAll('.teacher-selection-highlight');
        console.log('DOM .teacher-selection-highlight count:', elements.length);
        elements.forEach((el, i) => {
          console.log(`Highlight ${i}:`, el, el.className, el.getAttribute('style'));
        });
      }, 200);
    } catch (error) {
      console.error('Error applying teacher selection:', error);
    }
  }, [userRole, teacherSelectionDecorations]);

  // Handle clearing teacher selection
  const handleClearTeacherSelection = useCallback((data: { teacherName: string; teacherId: string }) => {
    console.log(`Teacher ${data.teacherName} cleared selection`);

    if (!editorRef.current || userRole === 'teacher') {
      return; // Don't clear for the teacher themselves
    }

    try {
      const editor = editorRef.current;
      // Clear all teacher selection decorations
      const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, []);
      setTeacherSelectionDecorations(newDecorations);
    } catch (error) {
      console.error('Error clearing teacher selection:', error);
    }
  }, [userRole, teacherSelectionDecorations]);

  // Handle teacher cursor position updates with blinking orange cursor decoration
  const handleTeacherCursorPosition = useCallback((data: { position: { lineNumber: number; column: number }; teacherName: string; teacherId: string }) => {
    console.log(`🎯 Teacher ${data.teacherName} cursor at line ${data.position.lineNumber}, column ${data.position.column}`);

    if (!editorRef.current || userRole === 'teacher') {
      return; // Don't show cursor to the teacher themselves
    }

    try {
      const editor = editorRef.current;
      const model = editor.getModel();

      if (!model) {
        console.log('⏭️ Skipping teacher cursor: model not available');
        return;
      }

      // Validate cursor position bounds
      const lineCount = model.getLineCount();
      if (data.position.lineNumber > lineCount || data.position.lineNumber <= 0) {
        console.log('⏭️ Skipping teacher cursor: position out of bounds');
        return;
      }

      // Create a range for the cursor position (single character width)
      const cursorRange = new monaco.Range(
        data.position.lineNumber,
        data.position.column,
        data.position.lineNumber,
        data.position.column + 1
      );

      // Clear previous teacher cursor decorations and apply new one
      const newCursorDecorations = editor.deltaDecorations(teacherCursorDecorations, [
        {
          range: cursorRange,
          options: {
            className: 'teacher-cursor', // Blinking orange cursor
            hoverMessage: {
              value: `🎯 Teacher ${data.teacherName} cursor position\n\nLine ${data.position.lineNumber}, Column ${data.position.column}`
            },
            stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,
            overviewRuler: {
              color: 'rgba(255, 152, 0, 0.8)', // Orange in overview ruler
              position: monaco.editor.OverviewRulerLane.Left
            }
          }
        }
      ]);

      setTeacherCursorDecorations(newCursorDecorations);
      // Set the teacher cursor position for overlay rendering
      setTeacherCursorPosition({
        lineNumber: data.position.lineNumber,
        column: data.position.column,
        teacherName: data.teacherName
      });
    } catch (error) {
      console.error('❌ Error handling teacher cursor position:', error);
    }
  }, [userRole, teacherCursorDecorations]);

  // Handle teacher text highlight with enhanced error handling and multiple CSS class fallbacks
  const handleTeacherTextHighlight = useCallback((data: { selection: any; teacherName: string; teacherId: string }) => {
    console.log(`🎨 Teacher ${data.teacherName} highlighted text:`, data.selection);
    console.log(`🔍 Debug info:`, {
      editorAvailable: !!editorRef.current,
      userRole: userRole,
      roomId: roomId,
      currentDecorations: teacherSelectionDecorations.length,
      monacoAvailable: typeof monaco !== 'undefined'
    });

    if (!editorRef.current || userRole === 'teacher') {
      console.log('⏭️ Skipping teacher text highlight: editor not available or user is teacher');
      return; // Don't show highlights to the teacher themselves
    }

    try {
      const editor = editorRef.current;
      const model = editor.getModel();

      if (!model) {
        console.error('❌ Monaco Editor model not available');
        return;
      }

      if (!data.selection) {
        console.error('❌ Selection data is missing');
        return;
      }

      // Validate selection data with more robust checking
      const { startLineNumber, startColumn, endLineNumber, endColumn } = data.selection;

      if (
        typeof startLineNumber !== 'number' || startLineNumber <= 0 ||
        typeof startColumn !== 'number' || startColumn <= 0 ||
        typeof endLineNumber !== 'number' || endLineNumber <= 0 ||
        typeof endColumn !== 'number' || endColumn <= 0
      ) {
        console.error('❌ Invalid selection data:', data.selection);
        return;
      }

      // Ensure the selection is within the model bounds
      const lineCount = model.getLineCount();
      if (startLineNumber > lineCount || endLineNumber > lineCount) {
        console.warn('⚠️ Selection extends beyond model bounds, adjusting...');
        const adjustedEndLine = Math.min(endLineNumber, lineCount);
        const adjustedEndColumn = adjustedEndLine === lineCount ?
          model.getLineMaxColumn(adjustedEndLine) : endColumn;

        console.log(`Adjusted selection: (${startLineNumber}, ${startColumn}, ${adjustedEndLine}, ${adjustedEndColumn})`);
      }

      console.log(`✅ Creating Monaco Range: (${startLineNumber}, ${startColumn}, ${endLineNumber}, ${endColumn})`);

      // Convert selection to Monaco range
      const range = new monaco.Range(
        startLineNumber,
        startColumn,
        endLineNumber,
        endColumn
      );

      console.log('✅ Monaco Range created successfully:', range);

      // Apply decoration with multiple CSS class options and inline styles for better compatibility
      console.log('🎨 Applying teacher text highlight decoration...');
      const decorationOptions = {
        range: range,
        options: {
          className: 'teacher-highlight teacher-text-highlight', // Multiple classes for fallback
          hoverMessage: {
            value: `🎯 Teacher ${data.teacherName} highlighted this text\n\nClick to focus on this selection`
          },
          stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,
          // Add inline styles as fallback
          inlineClassName: 'teacher-highlight-inline',
          // Force inline styles for maximum compatibility
          overviewRuler: {
            color: 'rgba(59, 130, 246, 0.8)',
            position: monaco.editor.OverviewRulerLane.Right
          },
          // Add background color directly
          backgroundColor: 'rgba(59, 130, 246, 0.25)',
          // Add border styling
          border: '2px solid rgba(59, 130, 246, 0.7)'
        }
      };

      // Clear previous decorations and apply new ones
      const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [decorationOptions]);

      console.log('✅ Teacher text highlight decorations applied:', newDecorations);
      console.log('🔍 Decoration details:', decorationOptions);

      setTeacherSelectionDecorations(newDecorations);

      // Debug: Check if decorations are actually in the DOM
      setTimeout(() => {
        const decorationElements = document.querySelectorAll('.teacher-text-highlight, .teacher-highlight');
        console.log('🔍 Found decoration elements in DOM:', decorationElements.length);
        decorationElements.forEach((el, index) => {
          console.log(`🔍 Decoration ${index + 1}:`, {
            element: el,
            className: el.className,
            style: el.getAttribute('style'),
            computedStyle: window.getComputedStyle(el).background
          });
        });

        // Also check Monaco's internal decorations
        const allDecorations = editor.getModel()?.getAllDecorations();
        console.log('🔍 All Monaco decorations:', allDecorations?.length);
        const teacherDecorations = allDecorations?.filter(d =>
          d.options.className?.includes('teacher-highlight') ||
          d.options.className?.includes('teacher-text-highlight')
        );
        console.log('🔍 Teacher decorations in model:', teacherDecorations);
      }, 200);

      // Force a layout update to ensure the decoration is visible
      setTimeout(() => {
        editor.layout();
        console.log('🔄 Editor layout refreshed');
      }, 100);

    } catch (error) {
      console.error('❌ Error applying teacher text highlight:', error);
      console.error('🔍 Error details:', {
        editorAvailable: !!editorRef.current,
        modelAvailable: !!editorRef.current?.getModel(),
        selectionData: data.selection,
        userRole: userRole,
        monacoAvailable: typeof monaco !== 'undefined',
        currentDecorations: teacherSelectionDecorations
      });
    }
  }, [userRole, teacherSelectionDecorations]);

  // Handle clearing teacher text highlight with enhanced logging
  const handleClearTeacherTextHighlight = useCallback((data: { teacherName: string; teacherId: string }) => {
    console.log(`🧹 Teacher ${data.teacherName} cleared text highlight`);
    console.log(`🔍 Clear debug info:`, {
      editorAvailable: !!editorRef.current,
      userRole: userRole,
      roomId: roomId,
      currentDecorations: teacherSelectionDecorations.length,
      monacoAvailable: typeof monaco !== 'undefined'
    });

    if (!editorRef.current || userRole === 'teacher') {
      console.log('⏭️ Skipping clear teacher text highlight: editor not available or user is teacher');
      return; // Don't clear for the teacher themselves
    }

    try {
      const editor = editorRef.current;
      const model = editor.getModel();

      if (!model) {
        console.error('❌ Monaco Editor model not available for clearing highlights');
        return;
      }

      console.log('🧹 Clearing teacher text highlight decorations...');
      console.log(`🔍 Clearing ${teacherSelectionDecorations.length} existing decorations`);

      // Clear all teacher text highlight decorations
      const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, []);
      console.log('✅ Teacher text highlight decorations cleared:', newDecorations);
      setTeacherSelectionDecorations(newDecorations);

      // Force a layout update to ensure the decorations are removed
      setTimeout(() => {
        editor.layout();
        console.log('🔄 Editor layout refreshed after clearing highlights');
      }, 50);

    } catch (error) {
      console.error('❌ Error clearing teacher text highlight:', error);
      console.error('🔍 Error details:', {
        editorAvailable: !!editorRef.current,
        modelAvailable: !!editorRef.current?.getModel(),
        userRole: userRole,
        currentDecorations: teacherSelectionDecorations,
        monacoAvailable: typeof monaco !== 'undefined'
      });
    }
  }, [userRole, teacherSelectionDecorations]);

  // Handle sync events for newly joined users
  const handleSyncCode = useCallback((data: { code: string }) => {
    console.log('🔄 Received sync-code event:', data);

    if (!editorRef.current) {
      console.log('⏭️ Skipping sync-code: editor not available');
      return;
    }

    try {
      // Set the editor value directly
      editorRef.current.setValue(data.code);
      setCode(data.code);
      latestCodeRef.current = data.code;
      console.log('✅ Code synced successfully');
    } catch (error) {
      console.error('❌ Error syncing code:', error);
    }
  }, []);

  const handleSyncTeacherSelection = useCallback((data: { selection: any; teacherName: string; teacherId: string }) => {
    console.log('🔄 Received sync-teacher-selection event:', data);

    if (!editorRef.current || userRole === 'teacher') {
      return; // Don't show highlights to the teacher themselves
    }

    try {
      const editor = editorRef.current;
      const model = editor.getModel();

      if (!model || !data.selection) {
        return;
      }

      // Convert selection to Monaco range
      const range = new monaco.Range(
        data.selection.startLineNumber,
        data.selection.startColumn,
        data.selection.endLineNumber,
        data.selection.endColumn
      );

      // Apply teacher selection decoration
      const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, [
        {
          range: range,
          options: {
            className: 'teacher-selection-highlight',
            hoverMessage: {
              value: `🎯 Teacher ${data.teacherName} selected this text (synced)\n\nRange: Line ${data.selection.startLineNumber}-${data.selection.endLineNumber}`
            },
            stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,
            overviewRuler: {
              color: 'rgba(255, 152, 0, 0.8)',
              position: monaco.editor.OverviewRulerLane.Right
            }
          }
        }
      ]);

      setTeacherSelectionDecorations(newDecorations);
      console.log('✅ Teacher selection synced successfully');
    } catch (error) {
      console.error('❌ Error syncing teacher selection:', error);
    }
  }, [userRole, teacherSelectionDecorations]);

  const handleSyncTeacherCursor = useCallback((data: { position: { lineNumber: number; column: number }; teacherName: string; teacherId: string }) => {
    console.log('🔄 Received sync-teacher-cursor event:', data);

    if (!editorRef.current || userRole === 'teacher') {
      return; // Don't show cursor to the teacher themselves
    }

    try {
      const editor = editorRef.current;
      const model = editor.getModel();

      if (!model) {
        return;
      }

      // Create a range for the cursor position
      const cursorRange = new monaco.Range(
        data.position.lineNumber,
        data.position.column,
        data.position.lineNumber,
        data.position.column + 1
      );

      // Apply teacher cursor decoration
      const newCursorDecorations = editor.deltaDecorations(teacherCursorDecorations, [
        {
          range: cursorRange,
          options: {
            className: 'teacher-cursor',
            hoverMessage: {
              value: `🎯 Teacher ${data.teacherName} cursor position (synced)\n\nLine ${data.position.lineNumber}, Column ${data.position.column}`
            },
            stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,
            overviewRuler: {
              color: 'rgba(255, 152, 0, 0.8)',
              position: monaco.editor.OverviewRulerLane.Left
            }
          }
        }
      ]);

      setTeacherCursorDecorations(newCursorDecorations);

      // Update teacher cursor position state
      setTeacherCursorPosition({
        lineNumber: data.position.lineNumber,
        column: data.position.column,
        teacherName: data.teacherName
      });

      console.log('✅ Teacher cursor synced successfully');
    } catch (error) {
      console.error('❌ Error syncing teacher cursor:', error);
    }
  }, [userRole, teacherCursorDecorations]);

  useEffect(() => {
    // Handle user typing notifications
    const handleUserTyping = ({ username, userId }: { username: string, userId: string | null }) => {
      // Get current userId from localStorage
      const currentUserId = localStorage.getItem('userId');

      // Don't show typing indicator for current user
      if (userId && userId === currentUserId) {
        return;
      }

      // Use the exact username without any modifications
      // This ensures we display exactly what the user entered on the dashboard
      setTypingUser(username);
      console.log(`User typing: ${username}`);
      setTimeout(() => setTypingUser(null), 2000);
    };

    // Handle user list updates from both user-joined/user-left and room-users-updated events
    const handleUserListUpdate = (data: any) => {
      console.log('User list update received:', data);

      let users: Array<{ socketId?: string, username: string, userId?: string, role?: string }> = [];

      // Handle different event formats
      if (Array.isArray(data)) {
        // This is from user-joined or user-left events
        users = data;
      } else if (data && Array.isArray(data.users)) {
        // This is from room-users-updated event
        users = data.users.map((user: any) => {
          if (typeof user === 'string') {
            // If it's just a username string, convert to user object
            return { username: user };
          }
          return user;
        });
      } else if (data && data.newUserSocketId && userRole === 'teacher') {
        // This is the enhanced user-joined event for teacher sync
        console.log('🎯 New user joined, syncing teacher state:', data);

        const socketServiceInstance = SocketService.getInstance();

        // Sync current code
        if (code && code !== "// Start coding...") {
          console.log('📤 Syncing current code to new user');
          socketServiceInstance.syncCodeToUser(roomId, code, data.newUserSocketId);
        }

        // Sync current teacher selection if exists
        if (currentTeacherSelection) {
          console.log('📤 Syncing current teacher selection to new user');
          socketServiceInstance.syncTeacherSelectionToUser(roomId, currentTeacherSelection, data.newUserSocketId);
        }

        // Sync current teacher cursor if exists
        if (currentTeacherCursor) {
          console.log('📤 Syncing current teacher cursor to new user');
          socketServiceInstance.syncTeacherCursorToUser(roomId, currentTeacherCursor, data.newUserSocketId);
        }

        // Continue with normal user list processing
        if (data.allUsers) {
          users = data.allUsers;
        }
      }

      console.log('Processed users:', users);
      console.log('Current username state:', username);
      console.log('Stored username:', localStorage.getItem('username'));

      // Get current userId from localStorage
      const currentUserId = localStorage.getItem('userId');
      console.log('Current userId from localStorage:', currentUserId);

      // Filter out any invalid users and map to display names
      const usernames = users
        .filter(user => user && user.username)
        .map(user => {
          const displayName = user.username;
          const isCurrentUser = user.userId === currentUserId;
          return isCurrentUser ? `${displayName} (you)` : displayName;
        });

      console.log('Setting active users:', usernames);

      // Only update if we have users to display
      if (usernames.length > 0) {
        setActiveUsers(usernames);
      } else if (activeUsers.length === 0) {
        // If we don't have users from the server but we're connected, add ourselves
        const storedUsername = localStorage.getItem('username');
        if (storedUsername) {
          setActiveUsers([`${storedUsername} (you)`]);
        }
      }

      // Log the current state of the active users list
      setTimeout(() => {
        console.log('Active users state after update:', activeUsers);
      }, 100);
    };



    // Register event listeners
    const socketServiceInstance = SocketService.getInstance();

    socketServiceInstance.on('code-update', handleCodeUpdate);
    socketServiceInstance.on('user-typing', handleUserTyping);
    socketServiceInstance.on('user-joined', handleUserListUpdate);
    socketServiceInstance.on('user-left', handleUserListUpdate);
    socketServiceInstance.on('room-users-updated', handleUserListUpdate);
    socketServiceInstance.on('get-initial-code', handleGetInitialCode);
    socketServiceInstance.on('initial-code-received', handleInitialCodeReceived);
    socketServiceInstance.on('teacher-selection', handleTeacherSelection);
    socketServiceInstance.on('clear-teacher-selection', handleClearTeacherSelection);
    socketServiceInstance.on('teacher-cursor-position', handleTeacherCursorPosition);
    socketServiceInstance.on('teacher-text-highlight', handleTeacherTextHighlight);
    socketServiceInstance.on('clear-teacher-text-highlight', handleClearTeacherTextHighlight);
    socketServiceInstance.on('sync-code', handleSyncCode);
    socketServiceInstance.on('sync-teacher-selection', handleSyncTeacherSelection);
    socketServiceInstance.on('sync-teacher-cursor', handleSyncTeacherCursor);

    // Join the room when component mounts
    if (roomId) {
      console.log("Joining room:", roomId);
      // Only show loading on initial join, not for code updates
      if (!editorRef.current) {
        setIsLoading(true);
      }

      const joinRoom = async () => {
        try {
          if (socketServiceInstance.isConnected()) {
            console.log("Socket connected, joining room");
            const { username: validatedUsername, users, role } = await socketServiceInstance.joinRoom(roomId, username);
            console.log("Successfully joined room:", roomId);
            console.log("Users in room:", users);
            console.log("User role:", role);

            if (validatedUsername !== username) {
              console.log(`Server validated username from ${username} to ${validatedUsername}`);
              setUsername(validatedUsername);
              localStorage.setItem('username', validatedUsername);
            }

            // Set user role and persist to localStorage
            if (role) {
              setUserRole(role);
              localStorage.setItem('userRole', role);
              console.log(`User role set to: ${role} and persisted to localStorage`);
            }

            if (users && Array.isArray(users)) {
              console.log('Setting active users from join response');
              handleUserListUpdate(users);
            }
            setIsLoading(false);
          } else {
            console.log("Socket not connected, trying to connect...");
            await new Promise<void>(resolve => socketServiceInstance.onConnect(() => resolve()))
            // After connect, try join again
            await joinRoom();
            return;
          }
        } catch (error) {
          console.error("Error joining room:", error);
          setIsLoading(false);
        }
      };
      // Start the join process
      joinRoom();

      // Set a timeout to hide the loading screen even if the join fails
      setTimeout(() => {
        setIsLoading(false);
      }, 3000);
    }

    // Clean up event listeners when component unmounts
    return () => {
      // Use an instance of SocketService
      const socketServiceInstance = SocketService.getInstance();

      socketServiceInstance.off('code-update', handleCodeUpdate);
      socketServiceInstance.off('user-typing', handleUserTyping);
      socketServiceInstance.off('user-joined', handleUserListUpdate);
      socketServiceInstance.off('user-left', handleUserListUpdate);
      socketServiceInstance.off('room-users-updated', handleUserListUpdate);
      socketServiceInstance.off('get-initial-code', handleGetInitialCode);
      socketServiceInstance.off('initial-code-received', handleInitialCodeReceived);
      socketServiceInstance.off('teacher-selection', handleTeacherSelection);
      socketServiceInstance.off('clear-teacher-selection', handleClearTeacherSelection);
      socketServiceInstance.off('teacher-cursor-position', handleTeacherCursorPosition);
      socketServiceInstance.off('teacher-text-highlight', handleTeacherTextHighlight);
      socketServiceInstance.off('clear-teacher-text-highlight', handleClearTeacherTextHighlight);
      socketServiceInstance.off('sync-code', handleSyncCode);
      socketServiceInstance.off('sync-teacher-selection', handleSyncTeacherSelection);
      socketServiceInstance.off('sync-teacher-cursor', handleSyncTeacherCursor);

      // Leave the room when component unmounts
      if (roomId) {
        socketServiceInstance.leaveRoom(roomId);
      }
    };
  }, [roomId, initialUsername, username, handleCodeUpdate, handleGetInitialCode, handleInitialCodeReceived, handleTeacherSelection, handleClearTeacherSelection, handleTeacherCursorPosition, handleTeacherTextHighlight, handleClearTeacherTextHighlight, handleSyncCode, handleSyncTeacherSelection, handleSyncTeacherCursor]);

  // Download code as file
  const handleDownload = () => {
    const blob = new Blob([code], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `code-${roomId || "session"}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Upload code from file
  const handleUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = (event) => {
      if (typeof event.target?.result === "string") {
        setCode(event.target.result);
        latestCodeRef.current = event.target.result;
      }
    };
    reader.readAsText(file);
  };

  // Show Monaco's find/replace dialog
  const handleFindReplace = () => {
    if (editorRef.current) {
      const action = editorRef.current.getAction("actions.find");
      if (action) action.run();
    }
  };

  // Insert code snippet
  const insertSnippet = (snippet: string) => {
    if (editorRef.current) {
      const model = editorRef.current.getModel();
      const position = editorRef.current.getPosition();
      if (model && position) {
        editorRef.current.executeEdits("snippet", [{
          range: new monaco.Range(position.lineNumber, position.column, position.lineNumber, position.column),
          text: snippet,
          forceMoveMarkers: true
        }]);
        editorRef.current.focus();
      }
    }
  };

  // Use the instance of SocketService for the connect method
  const handleReconnect = () => {
    const socketServiceInstance = SocketService.getInstance();
    socketServiceInstance.connect();
  };

  // Manual test function for debugging teacher text highlighting
  const testManualHighlight = useCallback(() => {
    console.log('🧪 Manual test highlight triggered');

    if (!editorRef.current) {
      console.error('❌ Editor not available for manual test');
      return;
    }

    try {
      const editor = editorRef.current;
      const model = editor.getModel();

      if (!model) {
        console.error('❌ Monaco Editor model not available for manual test');
        return;
      }

      console.log('✅ Manual test: Editor and model available');
      console.log('✅ Manual test: Model line count:', model.getLineCount());
      console.log('✅ Manual test: Model content preview:', model.getValue().substring(0, 100));

      // Create a test range
      const range = new monaco.Range(1, 1, 1, 20);
      console.log('✅ Manual test: Range created:', range);

      // Test multiple decoration approaches with yellow/orange theme
      const decorationOptions = [
        {
          range: range,
          options: {
            className: 'teacher-selection-highlight teacher-text-highlight teacher-highlight',
            hoverMessage: { value: 'Manual test selection highlight - Yellow background with orange border!' },
            stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,
            overviewRuler: {
              color: 'rgba(255, 152, 0, 0.8)', // Orange in overview ruler
              position: monaco.editor.OverviewRulerLane.Right
            }
          }
        },
        {
          range: new monaco.Range(2, 1, 2, 15),
          options: {
            className: 'teacher-cursor',
            hoverMessage: { value: 'Manual test cursor - Blinking orange cursor!' },
            stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges
          }
        },
        {
          range: new monaco.Range(3, 1, 3, 25),
          options: {
            className: 'teacher-selection-highlight',
            hoverMessage: { value: 'Manual test selection - Line 3!' },
            stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,
            overviewRuler: {
              color: 'rgba(255, 152, 0, 0.8)', // Orange in overview ruler
              position: monaco.editor.OverviewRulerLane.Left
            }
          }
        }
      ];

      // Apply decorations
      const newDecorations = editor.deltaDecorations(teacherSelectionDecorations, decorationOptions);

      console.log('✅ Manual test: Decorations applied:', newDecorations);
      setTeacherSelectionDecorations(newDecorations);

      // Debug: Check DOM after decoration
      setTimeout(() => {
        const decorationElements = document.querySelectorAll('.teacher-text-highlight, .teacher-highlight');
        console.log('🔍 Manual test: Found decoration elements:', decorationElements.length);

        // Force add a visible element for testing with yellow/orange theme
        const testDiv = document.createElement('div');
        testDiv.style.cssText = `
          position: fixed;
          top: 10px;
          right: 10px;
          background: rgba(255, 152, 0, 0.9);
          color: white;
          padding: 12px;
          border-radius: 6px;
          border: 2px solid rgba(255, 235, 59, 0.8);
          box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
          z-index: 9999;
          font-family: monospace;
          font-weight: bold;
          animation: pulse 2s ease-in-out infinite;
        `;
        testDiv.innerHTML = `
          🎯 Manual Test Results<br>
          📊 ${newDecorations.length} decorations applied<br>
          🎨 Yellow/Orange theme active
        `;
        document.body.appendChild(testDiv);

        setTimeout(() => {
          if (document.body.contains(testDiv)) {
            document.body.removeChild(testDiv);
          }
        }, 4000);
      }, 100);

    } catch (error) {
      console.error('❌ Manual test error:', error);
    }
  }, [teacherSelectionDecorations]);

  // Add manual test to window for debugging
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).testManualHighlight = testManualHighlight;
      console.log('🔧 Manual test function added to window.testManualHighlight()');
    }
  }, [testManualHighlight]);

  return (
    <div style={{ position: "relative" }}>
      {/* Existing editor container */}
      <div className="relative w-full h-[80vh] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-800 shadow-xl flex flex-col">
        {/* Editor toolbar */}
        <motion.div
          className="bg-gray-100 dark:bg-zinc-800 px-4 py-2 flex flex-wrap items-center justify-between border-b border-gray-200 dark:border-gray-700 gap-2 min-h-[48px]"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          style={{ zIndex: 2 }}
        >
          <div className="flex flex-wrap items-center gap-3 min-w-0">
            {/* Connection status indicator */}
            <div className="flex items-center space-x-1 min-w-[90px]">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-xs text-gray-600 dark:text-gray-400 truncate">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
              {!isConnected && (
                <button
                  onClick={handleReconnect}
                  className="text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded ml-1 transition-colors"
                >
                  Reconnect
                </button>
              )}
            </div>

            {/* Language selector */}
            <select
              value={language}
              onChange={(e) => changeLanguage(e.target.value)}
              className="bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]"
            >
              <option value="javascript">JavaScript</option>
              <option value="typescript">TypeScript</option>
              <option value="python3">Python</option>
              <option value="java">Java</option>
              <option value="csharp">C#</option>
              <option value="c">C</option>
              <option value="cpp">C++</option>
              <option value="go">Go</option>
              <option value="ruby">Ruby</option>
              <option value="rust">Rust</option>
              <option value="php">PHP</option>
            </select>

            {/* Format Code button */}
            <motion.button
              onClick={formatCurrentCode}
              className="flex items-center space-x-1 px-3 py-1 rounded-md text-sm bg-blue-600 hover:bg-blue-700 text-white transition-colors whitespace-nowrap"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FiAlignLeft size={14} />
              <span>Format</span>
            </motion.button>

            {/* Run Code button */}
            <motion.button
              onClick={executeCode}
              disabled={isExecuting}
              className={`flex items-center space-x-1 px-3 py-1 rounded-md text-sm ${isExecuting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'} text-white transition-colors whitespace-nowrap`}
              whileHover={{ scale: isExecuting ? 1 : 1.05 }}
              whileTap={{ scale: isExecuting ? 1 : 0.95 }}
            >
              {isExecuting ? (
                <>
                  <FiLoader className="animate-spin" size={14} />
                  <span>Running...</span>
                </>
              ) : (
                <>
                  <FiPlay size={14} />
                  <span>Run Code</span>
                </>
              )}
            </motion.button>

            {/* Room ID display */}
            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300 min-w-0">
              <span>Room:</span>
              <code className="bg-white dark:bg-zinc-700 px-2 py-1 rounded font-mono text-xs truncate max-w-[120px]">{roomId}</code>
              <motion.button
                onClick={copyCodeToClipboard}
                className="text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                title="Copy code to clipboard"
              >
                <FiCopy size={14} />
              </motion.button>
            </div>
          </div>

          <div className="flex flex-wrap items-center gap-2 min-w-0">
            {/* Output toggle */}
            <motion.button
              onClick={() => setShowOutput(prev => !prev)}
              className={`flex items-center space-x-1 text-sm ${showOutput ? 'text-blue-500 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300'} hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FiCode size={16} />
              <span>Output</span>
            </motion.button>

            {/* User list toggle */}
            <motion.button
              onClick={toggleUserList}
              className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 transition-colors px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FiUsers size={16} />
              <span>{activeUsers.length} online</span>
            </motion.button>

            {/* Role indicator */}
            {userRole && (
              <motion.div
                className={`flex items-center space-x-1 text-xs font-medium px-2 py-1 rounded-full whitespace-nowrap ${
                  userRole === 'teacher'
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                }`}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.5 }}
              >
                <span>{userRole === 'teacher' ? '👨‍🏫' : '👨‍🎓'}</span>
                <span>{userRole === 'teacher' ? 'Teacher' : 'Student'}</span>
              </motion.div>
            )}

            {/* Theme switcher */}
            <motion.button
              onClick={() => setTheme && setTheme(theme === "dark" ? "light" : "dark")}
              className="flex items-center space-x-1 text-sm text-yellow-600 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              title="Toggle Theme"
            >
              {theme === "dark" ? <FiSun /> : <FiMoon />}
            </motion.button>

            {/* Minimap toggle */}
            <motion.button
              onClick={() => setMinimapEnabled((v: boolean) => !v)}
              className="flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              title="Toggle Minimap"
            >
              <span>Minimap</span>
            </motion.button>

            {/* Find/Replace */}
            <motion.button
              onClick={handleFindReplace}
              className="flex items-center space-x-1 text-sm text-green-600 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              title="Find/Replace"
            >
              <FiSearch />
            </motion.button>

            {/* Download code */}
            <motion.button
              onClick={handleDownload}
              className="flex items-center space-x-1 text-sm text-indigo-600 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/30 hover:bg-indigo-200 dark:hover:bg-indigo-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              title="Download Code"
            >
              <FiDownload />
            </motion.button>

            {/* Upload code */}
            <motion.button
              onClick={() => fileInputRef.current?.click()}
              className="flex items-center space-x-1 text-sm text-pink-600 dark:text-pink-300 bg-pink-100 dark:bg-pink-900/30 hover:bg-pink-200 dark:hover:bg-pink-800/50 transition-colors px-2 py-1 rounded-md whitespace-nowrap"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              title="Upload Code"
            >
              <FiUpload />
            </motion.button>
            <input
              ref={fileInputRef}
              type="file"
              accept=".js,.ts,.py,.java,.c,.cpp,.go,.rb,.rs,.php,.txt"
              style={{ display: "none" }}
              onChange={handleUpload}
            />

            {/* Snippet dropdown */}
            <select
              onChange={e => e.target.value && insertSnippet(e.target.value)}
              className="bg-white dark:bg-zinc-700 text-gray-800 dark:text-white text-sm rounded-md border border-gray-300 dark:border-gray-600 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[100px]"
              defaultValue=""
              title="Insert Snippet"
            >
              <option value="" disabled>Snippets</option>
              {(SNIPPETS[language] || SNIPPETS['javascript']).map(snippet => (
                <option key={snippet.label} value={snippet.value}>{snippet.label}</option>
              ))}
            </select>

            {/* Shortcuts help */}
            <motion.button
              onClick={() => setShowShortcuts(true)}
              className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors px-2 py-1 rounded-md whitespace-nowrap"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              title="Keyboard Shortcuts"
            >
              <FiHelpCircle />
            </motion.button>

            {/* Leave Room button */}
            <motion.button
              onClick={handleLeaveRoom}
              className="flex items-center space-x-1 text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors px-2 py-1 rounded-md ml-2 whitespace-nowrap"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span>Leave Room</span>
            </motion.button>
          </div>
        </motion.div>

        {/* User list dropdown */}
        <AnimatePresence>
          {showUserList && (
            <motion.div
              className="absolute right-0 top-12 bg-white dark:bg-zinc-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 w-48 overflow-hidden"
              initial={{ opacity: 0, y: -10, height: 0 }}
              animate={{ opacity: 1, y: 0, height: 'auto' }}
              exit={{ opacity: 0, y: -10, height: 0 }}
              transition={{ duration: 0.2 }}
            >
              <div className="p-2 border-b border-gray-200 dark:border-gray-700 font-medium text-sm text-gray-600 dark:text-gray-300">
                Active Users
              </div>
              <ul className="max-h-48 overflow-y-auto">
                {activeUsers.map((user, index) => (
                  <motion.li
                    key={index}
                    className="px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-zinc-700 flex items-center space-x-2"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                    <span>{user}</span>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Code editor */}
        <motion.div
          className={`h-[calc(100%-40px)] ${showOutput ? 'pb-[33%]' : ''}`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          <div className="relative w-full h-full flex flex-col">
            {/* Editor toolbar and other components */}
            <div className="flex-1 relative">
              <Editor
                height="100%"
                defaultLanguage={language}
                defaultValue={code}
                onChange={handleEditorChange}
                onMount={handleEditorDidMount}
                theme={theme === "dark" ? "vs-dark" : "light"}
                options={{ minimap: { enabled: minimapEnabled } }}
              />

              {/* Teacher cursor indicator */}
              {teacherCursorPosition && editorRef.current && (
                <TeacherCursor
                  editor={editorRef.current}
                  position={teacherCursorPosition}
                />
              )}
                       </div>
          </div>
        </motion.div>

        {/* Typing indicator */}
        <AnimatePresence>
          {typingUser && (
            <motion.div
              className="absolute bottom-4 left-4 text-sm text-gray-400 bg-gray-800/80 px-3 py-1 rounded-full backdrop-blur-sm"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.2 }}
            >
              {typingUser} is typing...
            </motion.div>
          )}
        </AnimatePresence>

        {/* Output panel */}
        <AnimatePresence>
          {showOutput && (
            <motion.div
              className="absolute bottom-0 left-0 right-0 bg-gray-900 text-white border-t border-gray-700 h-1/3 overflow-hidden flex flex-col"
              initial={{ height: 0 }}
              animate={{ height: '33%' }}
              exit={{ height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex justify-between items-center p-2 bg-gray-800 border-b border-gray-700">
                <div className="font-medium">Output</div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={clearOutput}
                    disabled={isExecuting}
                    className="text-xs text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Clear
                  </button>
                  <button
                    onClick={testPistonAPI}
                    disabled={isExecuting}
                    className="text-xs text-blue-400 hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Test API
                  </button>
                  <button
                    onClick={() => setShowOutput(false)}
                    className="text-gray-400 hover:text-white"
                  >
                    ×
                  </button>
                </div>
              </div>
              <div className="flex-1 p-4 overflow-auto font-mono text-sm whitespace-pre-wrap">
                {isExecuting ? (
                  <div className="flex items-center space-x-2 text-gray-400">
                    <FiLoader className="animate-spin" />
                    <span>Executing code...</span>
                  </div>
                ) : executionError ? (
                  <div className="text-red-400">{executionError}</div>
                ) : executionOutput ? (
                  <div>{executionOutput}</div>
                ) : (
                  <div className="text-gray-400">Click "Run Code" to execute your code.</div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Copy success notification */}
        <AnimatePresence>
          {copySuccess && (
            <motion.div
              className="absolute bottom-4 right-4 text-sm text-white bg-green-500 px-3 py-1 rounded-full shadow-lg"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.2 }}
            >
              Copied to clipboard!
            </motion.div>
          )}
        </AnimatePresence>

        {/* Keyboard Shortcuts Modal */}
        <AnimatePresence>
          {showShortcuts && (
            <motion.div
              className="fixed inset-0 z-50 flex items-center justify-center bg-black/60"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 max-w-md w-full relative">
                <button
                  className="absolute top-2 right-2 text-gray-400 hover:text-gray-700 dark:hover:text-white"
                  onClick={() => setShowShortcuts(false)}
                >
                  ×
                </button>
                <h2 className="text-xl font-bold mb-4">Keyboard Shortcuts</h2>
                <ul className="space-y-2 text-sm">
                  <li><b>Run Code:</b> Ctrl+Enter</li>
                  <li><b>Format Code:</b> Shift+Alt+F</li>
                  <li><b>Find/Replace:</b> Ctrl+F</li>
                  <li><b>Download Code:</b> Alt+D</li>
                  <li><b>Upload Code:</b> Alt+U</li>
                  <li><b>Toggle Minimap:</b> Alt+M</li>
                  <li><b>Toggle Theme:</b> Alt+T</li>
                  <li><b>Show Shortcuts:</b> Alt+H</li>
                </ul>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

// Teacher cursor component
interface TeacherCursorProps {
  editor: monaco.editor.IStandaloneCodeEditor;
  position: { lineNumber: number; column: number; teacherName: string };
}

const TeacherCursor: React.FC<TeacherCursorProps> = ({ editor, position }) => {
  const [cursorStyle, setCursorStyle] = useState<React.CSSProperties>({});

  useEffect(() => {
    const updateCursorPosition = () => {
      try {
        // Get the pixel position of the cursor
        const pixelPosition = editor.getScrolledVisiblePosition({
          lineNumber: position.lineNumber,
          column: position.column
        });

        if (pixelPosition) {
          // Get the editor container position
          const editorContainer = editor.getDomNode();
          if (editorContainer) {
            const containerRect = editorContainer.getBoundingClientRect();

            setCursorStyle({
              position: 'absolute',
              left: `${pixelPosition.left}px`,
              top: `${pixelPosition.top}px`,
              zIndex: 1000,
              pointerEvents: 'none'
            });
          }
        }
      } catch (error) {
        console.error('Error updating teacher cursor position:', error);
      }
    };

    // Update position immediately
    updateCursorPosition();

    // Update position when editor scrolls or layout changes
    const scrollDisposable = editor.onDidScrollChange(updateCursorPosition);
    const layoutDisposable = editor.onDidLayoutChange(updateCursorPosition);

    return () => {
      scrollDisposable.dispose();
      layoutDisposable.dispose();
    };
  }, [editor, position]);

  return (
    <div style={cursorStyle}>
      <div className="teacher-cursor">
        <div className="teacher-cursor-label">
          {position.teacherName}
        </div>
      </div>
    </div>
  );
};

// Keyboard shortcuts global handler
// useEffect(() => {
//   const handler = (e: KeyboardEvent) => {
//     if (e.altKey && e.key.toLowerCase() === "d") {
//       e.preventDefault();
//       handleDownload();
//     } else if (e.altKey && e.key.toLowerCase() === "u") {
//       e.preventDefault();
//       fileInputRef.current?.click();
//     } else if (e.altKey && e.key.toLowerCase() === "m") {
//       e.preventDefault();
//       setMinimapEnabled((v: boolean) => !v);
//     } else if (e.altKey && e.key.toLowerCase() === "t") {
//       e.preventDefault();
//       setTheme && setTheme(theme === "dark" ? "light" : "dark");
//     } else if (e.altKey && e.key.toLowerCase() === "h") {
//       e.preventDefault();
//       setShowShortcuts(true);
//     }
//   };
//   window.addEventListener("keydown", handler);
//   return () => window.removeEventListener("keydown", handler);
// }, [theme, setTheme]);