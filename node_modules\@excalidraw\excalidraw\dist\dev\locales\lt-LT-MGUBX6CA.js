import "../chunk-XDFCUUT6.js";

// locales/lt-LT.json
var labels = {
  paste: "\u012Eklijuoti",
  pasteAsPlaintext: "\u012Eklijuoti kaip paprast\u0105 tekst\u0105",
  pasteCharts: "\u012Eklijuoti diagramas",
  selectAll: "Pa\u017Eym\u0117ti visk\u0105",
  multiSelect: "Prid\u0117kite element\u0105 prie pasirinkt\u0173",
  moveCanvas: "Judinti drob\u0119",
  cut: "I\u0161kirpti",
  copy: "Kopijuoti",
  copyAsPng: "Kopijuoti \u012F i\u0161karpin\u0119 kaip PNG",
  copyAsSvg: "Kopijuoti \u012F i\u0161karpin\u0119 kaip SVG",
  copyText: "Kopijuoti \u012F i\u0161karpin\u0119 kaip tekst\u0105",
  copySource: "",
  convertToCode: "",
  bringForward: "Kelti priekio link",
  sendToBack: "Nustumti \u012F u\u017Enugar\u012F",
  bringToFront: "I\u0161kelti \u012F priek\u012F",
  sendBackward: "Nustumti link u\u017Enugario",
  delete: "I\u0161trinti",
  copyStyles: "Kopijuoti stilius",
  pasteStyles: "\u012Eklijuoti stilius",
  stroke: "Linija",
  background: "Fonas",
  fill: "U\u017Epildymas",
  strokeWidth: "Linijos storis",
  strokeStyle: "Linijos stilius",
  strokeStyle_solid: "I\u0161tisin\u0117",
  strokeStyle_dashed: "Br\u016Bk\u0161niuota",
  strokeStyle_dotted: "Ta\u0161kuota",
  sloppiness: "Netvarkingumas",
  opacity: "Nepermatomumas",
  textAlign: "Teksto lygiavimas",
  edges: "Kra\u0161tai",
  sharp: "A\u0161trus",
  round: "U\u017Eapvalintas",
  arrowheads: "Rodykl\u0117s vir\u0161\u016Bn\u0117s",
  arrowhead_none: "Jokios",
  arrowhead_arrow: "Rodykl\u0117",
  arrowhead_bar: "Bruk\u0161nys",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "Trikampis",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "\u0160rifto dydis",
  fontFamily: "\u0160riftas",
  addWatermark: "Sukurta su Excalidraw",
  handDrawn: "Ranka ra\u0161ytas",
  normal: "Normalus",
  code: "Kodas",
  small: "Ma\u017Eas",
  medium: "Vidutinis",
  large: "Didelis",
  veryLarge: "Labai didelis",
  solid: "",
  hachure: "",
  zigzag: "",
  crossHatch: "",
  thin: "Plonas",
  bold: "Pastorintas",
  left: "Kair\u0117je",
  center: "Centre",
  right: "De\u0161in\u0117je",
  extraBold: "Labiau pastorintas",
  architect: "Architektas",
  artist: "Menininkas",
  cartoonist: "Karikat\u016Bristas",
  fileTitle: "Failo pavadinimas",
  colorPicker: "Spalvos parinkiklis",
  canvasColors: "",
  canvasBackground: "Drob\u0117s fonas",
  drawingCanvas: "",
  layers: "Sluoksniai",
  actions: "Veiksmai",
  language: "Kalba",
  liveCollaboration: "Bendradarbiavimas gyvai...",
  duplicateSelection: "",
  untitled: "",
  name: "",
  yourName: "J\u016Bs\u0173 vardas",
  madeWithExcalidraw: "Sukurta su Excalidraw",
  group: "Grupuoti pasirinkim\u0105",
  ungroup: "I\u0161grupuoti pasirinkim\u0105",
  collaborators: "Bendradarbiautojai",
  showGrid: "Rodyti tinklel\u012F",
  addToLibrary: "Prid\u0117ti \u012F bibliotek\u0105",
  removeFromLibrary: "Pa\u0161alinti i\u0161 bibliotekos",
  libraryLoadingMessage: "",
  libraries: "Nar\u0161yti bibliotekas",
  loadingScene: "",
  align: "Lygiuoti",
  alignTop: "Lygiuoti vir\u0161uje",
  alignBottom: "Lygiuoti apa\u010Dioje",
  alignLeft: "Lygiuoti kair\u0117je",
  alignRight: "Lygiuoti de\u0161in\u0117je",
  centerVertically: "Centruoti vertikaliai",
  centerHorizontally: "Centruoti horizontaliai",
  distributeHorizontally: "",
  distributeVertically: "",
  flipHorizontal: "Apversti horizontaliai",
  flipVertical: "Apversti vertikaliai",
  viewMode: "",
  share: "Dalintis",
  showStroke: "",
  showBackground: "",
  toggleTheme: "",
  personalLib: "Asmenin\u0117 biblioteka",
  excalidrawLib: "Exaclidraw biblioteka",
  decreaseFontSize: "",
  increaseFontSize: "",
  unbindText: "",
  bindText: "",
  createContainerFromText: "",
  link: {
    edit: "Redeguoti nuorod\u0105",
    editEmbed: "",
    create: "Sukurti nuorod\u0105",
    createEmbed: "",
    label: "Nuoroda",
    labelEmbed: "",
    empty: ""
  },
  lineEditor: {
    edit: "",
    exit: ""
  },
  elementLock: {
    lock: "U\u017Erakinti",
    unlock: "Atrakinti",
    lockAll: "",
    unlockAll: ""
  },
  statusPublished: "",
  sidebarLock: "",
  selectAllElementsInFrame: "",
  removeAllElementsFromFrame: "",
  eyeDropper: "",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "",
  hint_emptyLibrary: "",
  hint_emptyPrivateLibrary: ""
};
var buttons = {
  clearReset: "",
  exportJSON: "Eksportuoti \u012F fail\u0105",
  exportImage: "",
  export: "",
  copyToClipboard: "Kopijuoti \u012F i\u0161karpin\u0119",
  save: "",
  saveAs: "I\u0161saugoti kaip",
  load: "",
  getShareableLink: "Gauti nuorod\u0105 dalinimuisi",
  close: "U\u017Edaryti",
  selectLanguage: "Pasirinkite kalb\u0105",
  scrollBackToContent: "",
  zoomIn: "Priartinti",
  zoomOut: "Nutolinti",
  resetZoom: "",
  menu: "Meniu",
  done: "",
  edit: "Redaguoti",
  undo: "Anuliuoti",
  redo: "",
  resetLibrary: "Atstatyti bibliotek\u0105",
  createNewRoom: "Sukurti nauj\u0105 kambar\u012F",
  fullScreen: "Visas ekranas",
  darkMode: "Tamsus re\u017Eimas",
  lightMode: "\u0160viesus re\u017Eimas",
  zenMode: "\u201EZen\u201C re\u017Eimas",
  objectsSnapMode: "",
  exitZenMode: "I\u0161eiti i\u0161 \u201EZen\u201C re\u017Eimo",
  cancel: "At\u0161aukti",
  clear: "I\u0161valyti",
  remove: "Pa\u0161alinti",
  embed: "",
  publishLibrary: "Paskelbti",
  submit: "Pateikti",
  confirm: "Patvirtinti",
  embeddableInteractionButton: ""
};
var alerts = {
  clearReset: "",
  couldNotCreateShareableLink: "",
  couldNotCreateShareableLinkTooBig: "",
  couldNotLoadInvalidFile: "",
  importBackendFailed: "",
  cannotExportEmptyCanvas: "",
  couldNotCopyToClipboard: "",
  decryptFailed: "",
  uploadedSecurly: "",
  loadSceneOverridePrompt: "",
  collabStopOverridePrompt: "Sesijos nutraukimas perra\u0161ys ankstesn\u012F, lokaliai i\u0161saugot\u0105 pie\u0161in\u012F. Ar tikrai to nori?\n\n(Jei nori i\u0161laikyti lokal\u0173 pie\u0161in\u012F, tiesiog u\u017Edaryk nar\u0161ykl\u0117s skirtuk\u0105.)",
  errorAddingToLibrary: "Nepavyko \u012Ftraukti elemento \u012F bibliotek\u0105",
  errorRemovingFromLibrary: "Nepavyko pa\u0161alinti elemento i\u0161 bibliotekos",
  confirmAddLibrary: "Tai \u012Ftrauks {{numShapes}} fig\u016Br\u0105/-as \u012F tavo bibliotek\u0105. Ar tikrai to nori?",
  imageDoesNotContainScene: "Pana\u0161u, jog \u0161is paveiksliukas neturi scenos duomen\u0173. Ar yra \u012Fjuntas scenos \u012Ftraukimas ekportavimo metu?",
  cannotRestoreFromImage: "Nepavyko atstatyti scenos i\u0161 \u0161io nuotraukos failo",
  invalidSceneUrl: "Nepavyko suimportuoti scenos i\u0161 pateiktos nuorodos (URL). Ji arba blogai suformatuota, arba savyje neturi teising\u0173 Excalidraw JSON duomen\u0173.",
  resetLibrary: "Tai i\u0161valys tavo bibliotek\u0105. Ar tikrai to nori?",
  removeItemsFromsLibrary: "I\u0161trinti {{count}} element\u0105/-us i\u0161 bibliotekos?",
  invalidEncryptionKey: "\u0160ifravimo raktas turi b\u016Bti i\u0161 22 simboli\u0173. Redagavimas gyvai yra i\u0161jungtas.",
  collabOfflineWarning: ""
};
var errors = {
  unsupportedFileType: "Nepalaikomas failo tipas.",
  imageInsertError: "Nepyko \u012Fkelti paveiksliuko. Pabandyk v\u0117liau...",
  fileTooBig: "Per didelis failas. Did\u017Eiausias leid\u017Eiamas dydis yra {{maxSize}}.",
  svgImageInsertError: "Nepavyko \u012Ftraukti SVG paveiksliuko. Pana\u0161u, jog SVG yra nevalidus.",
  failedToFetchImage: "",
  invalidSVGString: "Nevalidus SVG.",
  cannotResolveCollabServer: "Nepavyko prisijungti prie serverio bendradarbiavimui. Perkrauk puslap\u012F ir pabandyk prisijungti dar kart\u0105.",
  importLibraryError: "Nepavyko \u012Fkelti bibliotekos",
  collabSaveFailed: "",
  collabSaveFailed_sizeExceeded: "",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "",
    line2: "",
    line3: "",
    line4: ""
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "",
    image: ""
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "\u017Dym\u0117jimas",
  image: "\u012Ekelti paveiksliuk\u0105",
  rectangle: "Sta\u010Diakampis",
  diamond: "Deimantas",
  ellipse: "Elips\u0117",
  arrow: "Rodykl\u0117",
  line: "Linija",
  freedraw: "Pie\u0161ti",
  text: "Tekstas",
  library: "Biblioteka",
  lock: "Baigus pie\u0161ti, i\u0161laikyti pasirinkt\u0105 \u012Frank\u012F",
  penMode: "Ra\u0161yklio re\u017Eimas - neleisti prisilietim\u0173",
  link: "Prid\u0117ti / Atnaujinti pasirinktos fig\u016Bros nuorod\u0105",
  eraser: "Trintukas",
  frame: "",
  magicframe: "",
  embeddable: "",
  laser: "",
  hand: "",
  extraTools: "",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "Veiksmai su drobe",
  selectedShapeActions: "Veiksmai su pasirinkta fig\u016Bra",
  shapes: "Fig\u016Bros"
};
var hints = {
  canvasPanning: "",
  linearElement: "Paspaudimai sukurs papildomus ta\u0161kus, nepertraukiamas tempimas sukurs linij\u0105",
  freeDraw: "Spausk ir tempk, paleisk kai nor\u0117si pabaigti",
  text: "U\u017Euomina: tekst\u0105 taip pat galima prid\u0117ti bet kur su dvigubu pel\u0117s paspaudimu, kol parinkas \u017Eym\u0117jimo \u012Frankis",
  embeddable: "",
  text_selected: "",
  text_editing: "",
  linearElementMulti: "",
  lockAngle: "",
  resize: "",
  resizeImage: "",
  rotate: "",
  lineEditor_info: "",
  lineEditor_pointSelected: "",
  lineEditor_nothingSelected: "",
  placeImage: "",
  publishLibrary: "",
  bindTextToElement: "",
  deepBoxSelect: "",
  eraserRevert: "",
  firefox_clipboard_write: "",
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "",
  canvasTooBig: "",
  canvasTooBigTip: ""
};
var errorSplash = {
  headingMain: "",
  clearCanvasMessage: "",
  clearCanvasCaveat: "",
  trackedToSentry: "",
  openIssueMessage: "",
  sceneContent: ""
};
var roomDialog = {
  desc_intro: "",
  desc_privacy: "",
  button_startSession: "Prad\u0117ti seans\u0105",
  button_stopSession: "Sustabdyti seans\u0105",
  desc_inProgressIntro: "",
  desc_shareLink: "",
  desc_exitSession: "",
  shareTitle: ""
};
var errorDialog = {
  title: "Klaida"
};
var exportDialog = {
  disk_title: "\u012Era\u0161yti \u012F disk\u0105",
  disk_details: "",
  disk_button: "\u012Era\u0161yti \u012F fail\u0105",
  link_title: "Nuoroda dalinimuisi",
  link_details: "",
  link_button: "",
  excalidrawplus_description: "",
  excalidrawplus_button: "Eksportuoti",
  excalidrawplus_exportError: ""
};
var helpDialog = {
  blog: "",
  click: "paspaudimas",
  deepSelect: "",
  deepBoxSelect: "",
  curvedArrow: "Banguota rodykl\u0117",
  curvedLine: "Banguota linija",
  documentation: "Dokumentacija",
  doubleClick: "dvigubas paspaudimas",
  drag: "vilkti",
  editor: "Redaktorius",
  editLineArrowPoints: "",
  editText: "",
  github: "Radai klaid\u0105? Pateik",
  howto: "Vadovaukis m\u016Bs\u0173 gidu",
  or: "arba",
  preventBinding: "",
  tools: "\u012Erankiai",
  shortcuts: "Spartieji klavi\u0161ai",
  textFinish: "Baigti redagavim\u0105 (teksto redaktoriuje)",
  textNewLine: "Prid\u0117ti nauj\u0105 eilut\u0119 (tekto redaktoriuje)",
  title: "Pagalba",
  view: "",
  zoomToFit: "",
  zoomToSelection: "Priartinti iki pa\u017Eym\u0117tos vietos",
  toggleElementLock: "",
  movePageUpDown: "Pajudinti puslap\u012F auk\u0161tyn/\u017Eemyn",
  movePageLeftRight: "Pajudinti puslap\u012F kair\u0117n/de\u0161in\u0117n"
};
var clearCanvasDialog = {
  title: "I\u0161valyti drob\u0119"
};
var publishDialog = {
  title: "Pavie\u0161inti bibliotek\u0105",
  itemName: "Elemento pavadinimas",
  authorName: "Autoriaus vardas",
  githubUsername: "Github spalyvardis",
  twitterUsername: "Twitter slapyvardis",
  libraryName: "Bibliotekos pavadinimas",
  libraryDesc: "Bibliotekos apra\u0161as",
  website: "Tinklalapis",
  placeholder: {
    authorName: "Tavo vardas arba spalyvardis",
    libraryName: "Tavo bibliotekos pavadinimas",
    libraryDesc: "Tavo bibliotekos apra\u0161as, pad\u0117ti \u017Emon\u0117ms geriau suprasti jos paskirt\u012F",
    githubHandle: "",
    twitterHandle: "",
    website: ""
  },
  errors: {
    required: "Privalomas",
    website: "\u012Eveskite teising\u0105 nuorod\u0105 (URL)"
  },
  noteDescription: "Pateik savo bibliotek\u0105, jog ji gal\u0117t\u0173 b\u016Bti \u012Ftraukta \u012F <link></link>jog kiti \u017Emon\u0117s gal\u0117t\u0173 tai naudoti savo pie\u0161iniuose.",
  noteGuidelines: "Vis\u0173 pirma, biblioteka turi b\u016Bti rankiniu b\u016Bdu patvirtinta. Pra\u0161ome paskaityti <link>gair\u0117s</link>",
  noteLicense: "<link>MIT licencija, </link>",
  noteItems: "",
  atleastOneLibItem: "",
  republishWarning: ""
};
var publishSuccessDialog = {
  title: "Biblioteka pateikta",
  content: "A\u010Di\u016B {{authorName}}. Tavo biblioteka buvo pateikta per\u017Ei\u016Brai. Gali sekti b\u016Bsen\u0105<link>\u010Dia</link>"
};
var confirmDialog = {
  resetLibrary: "Atstatyti bibliotek\u0105",
  removeItemsFromLib: "Pa\u0161alinti pasirinktus elementus i\u0161 bibliotekos"
};
var imageExportDialog = {
  header: "",
  label: {
    withBackground: "",
    onlySelected: "",
    darkMode: "",
    embedScene: "",
    scale: "",
    padding: ""
  },
  tooltip: {
    embedScene: ""
  },
  title: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  },
  button: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  }
};
var encrypted = {
  tooltip: "",
  link: ""
};
var stats = {
  angle: "",
  element: "Elementas",
  elements: "Elementai",
  height: "Auk\u0161tis",
  scene: "Scena",
  selected: "Pasirinkta",
  storage: "Saugykla",
  title: "Informacija moksliukams",
  total: "",
  version: "",
  versionCopy: "",
  versionNotAvailable: "",
  width: "Plotis"
};
var toast = {
  addedToLibrary: "Prid\u0117ta \u012F bibliotek\u0105",
  copyStyles: "",
  copyToClipboard: "Nukopijuota \u012F i\u0161karpin\u0119.",
  copyToClipboardAsPng: "",
  fileSaved: "Failas i\u0161saugotas.",
  fileSavedToFilename: "I\u0161saugota \u012F {filename}",
  canvas: "drob\u0117",
  selection: "",
  pasteAsSingleElement: "",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "Permatoma",
  black: "",
  white: "",
  red: "",
  pink: "",
  grape: "",
  violet: "",
  gray: "",
  blue: "",
  cyan: "",
  teal: "",
  green: "",
  yellow: "",
  orange: "",
  bronze: ""
};
var welcomeScreen = {
  app: {
    center_heading: "",
    center_heading_plus: "",
    menuHint: ""
  },
  defaults: {
    menuHint: "",
    center_heading: "",
    toolbarHint: "",
    helpHint: ""
  }
};
var colorPicker = {
  mostUsedCustomColors: "",
  colors: "",
  shades: "",
  hexCode: "",
  noShades: ""
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "",
      button: "",
      description: ""
    },
    saveToDisk: {
      title: "",
      button: "",
      description: ""
    },
    excalidrawPlus: {
      title: "",
      button: "",
      description: ""
    }
  },
  modal: {
    loadFromFile: {
      title: "",
      button: "",
      description: ""
    },
    shareableLink: {
      title: "",
      button: "",
      description: ""
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var lt_LT_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  lt_LT_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=lt-LT-MGUBX6CA.js.map
