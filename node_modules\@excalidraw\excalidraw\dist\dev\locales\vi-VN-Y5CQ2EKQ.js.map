{"version": 3, "sources": ["../../../locales/vi-VN.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"<PERSON><PERSON>\",\n    \"pasteAsPlaintext\": \"<PERSON><PERSON> kiểu văn bản thuần\",\n    \"pasteCharts\": \"<PERSON><PERSON> biểu đồ\",\n    \"selectAll\": \"<PERSON>ọn tất cả\",\n    \"multiSelect\": \"Thêm mới vào Select\",\n    \"moveCanvas\": \"Di chuyển canvas\",\n    \"cut\": \"Cắt\",\n    \"copy\": \"Sao chép\",\n    \"copyAsPng\": \"Sao chép vào bộ nhớ tạm dưới dạng PNG\",\n    \"copyAsSvg\": \"Sao chép vào bộ nhớ tạm dưới dạng SVG\",\n    \"copyText\": \"Sao chép vào bộ nhớ tạm dưới dạng chữ\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Đưa ra trước\",\n    \"sendToBack\": \"Hạ xuống dưới\",\n    \"bringToFront\": \"Đ<PERSON>a ra đầu tiên\",\n    \"sendBackward\": \"Hạ xuống cuối\",\n    \"delete\": \"Xóa\",\n    \"copyStyles\": \"Sao chép định dạng\",\n    \"pasteStyles\": \"Dán định dạng\",\n    \"stroke\": \"Nét\",\n    \"background\": \"Nền\",\n    \"fill\": \"Fill\",\n    \"strokeWidth\": \"Độ dày nét\",\n    \"strokeStyle\": \"Kiểu nét\",\n    \"strokeStyle_solid\": \"Khối\",\n    \"strokeStyle_dashed\": \"Gạch ngang\",\n    \"strokeStyle_dotted\": \"Nhiều chấm\",\n    \"sloppiness\": \"Hoa văn nét\",\n    \"opacity\": \"Độ trong suốt\",\n    \"textAlign\": \"Căn chỉnh văn bản\",\n    \"edges\": \"Cạnh\",\n    \"sharp\": \"Nhọn\",\n    \"round\": \"Tròn\",\n    \"arrowheads\": \"Đầu mũi tên\",\n    \"arrowhead_none\": \"Không\",\n    \"arrowhead_arrow\": \"Mũi tên\",\n    \"arrowhead_bar\": \"Thanh\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Tam giác\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Cỡ chữ\",\n    \"fontFamily\": \"Phông chữ\",\n    \"addWatermark\": \"Làm với Excalidraw\\\"\",\n    \"handDrawn\": \"Vẽ tay\",\n    \"normal\": \"Bình thường\",\n    \"code\": \"Mã\",\n    \"small\": \"Nhỏ\",\n    \"medium\": \"Vừa\",\n    \"large\": \"Lớn\",\n    \"veryLarge\": \"Rất lớn\",\n    \"solid\": \"Đặc\",\n    \"hachure\": \"Nét gạch gạch\",\n    \"zigzag\": \"Zigzag\",\n    \"crossHatch\": \"Nét gạch chéo\",\n    \"thin\": \"Mỏng\",\n    \"bold\": \"In đậm\",\n    \"left\": \"Trái\",\n    \"center\": \"Giữa\",\n    \"right\": \"Phải\",\n    \"extraBold\": \"Nét siêu đậm\",\n    \"architect\": \"Kiến trúc sư\",\n    \"artist\": \"Nghệ sỹ\",\n    \"cartoonist\": \"Hoạt hình\",\n    \"fileTitle\": \"Tên tập tin\",\n    \"colorPicker\": \"Chọn màu\",\n    \"canvasColors\": \"Đã dùng trên canvas\",\n    \"canvasBackground\": \"Nền canvas\",\n    \"drawingCanvas\": \"Canvas vẽ\",\n    \"layers\": \"Lớp\",\n    \"actions\": \"Chức năng\",\n    \"language\": \"Ngôn ngữ\",\n    \"liveCollaboration\": \"Hợp tác trực tiếp...\",\n    \"duplicateSelection\": \"Tạo bản sao\",\n    \"untitled\": \"Không có tiêu đề\",\n    \"name\": \"Tên\",\n    \"yourName\": \"Tên của bạn\",\n    \"madeWithExcalidraw\": \"Làm với Excalidraw\",\n    \"group\": \"Gộp nhóm lại lựa chọn\",\n    \"ungroup\": \"Tách nhóm lựa chọn\",\n    \"collaborators\": \"Cộng tác viên\",\n    \"showGrid\": \"Hiển thị lưới\",\n    \"addToLibrary\": \"Thêm vào thư viện\",\n    \"removeFromLibrary\": \"Xóa khỏi thư viện\",\n    \"libraryLoadingMessage\": \"Đang tải thư viện…\",\n    \"libraries\": \"Xem thư viện\",\n    \"loadingScene\": \"Đang tải về…\",\n    \"align\": \"Căn chỉnh\",\n    \"alignTop\": \"Căn trên\",\n    \"alignBottom\": \"Căn dưới\",\n    \"alignLeft\": \"Canh trái\",\n    \"alignRight\": \"Canh phải\",\n    \"centerVertically\": \"Giữa theo chiều dọc\",\n    \"centerHorizontally\": \"Giữa theo chiều ngang\",\n    \"distributeHorizontally\": \"Phân bố theo chiều ngang\",\n    \"distributeVertically\": \"Phân bố theo chiều dọc\",\n    \"flipHorizontal\": \"Lật ngang\",\n    \"flipVertical\": \"Lật dọc\",\n    \"viewMode\": \"Chế độ xem\",\n    \"share\": \"Chia sẻ\",\n    \"showStroke\": \"Hiển thị chọn màu\",\n    \"showBackground\": \"Hiện thị chọn màu nền\",\n    \"toggleTheme\": \"\",\n    \"personalLib\": \"Thư viện cá nhân\",\n    \"excalidrawLib\": \"Thư viện Excalidraw\",\n    \"decreaseFontSize\": \"Giảm cỡ chữ\",\n    \"increaseFontSize\": \"Tăng cỡ chữ\",\n    \"unbindText\": \"\",\n    \"bindText\": \"\",\n    \"createContainerFromText\": \"\",\n    \"link\": {\n      \"edit\": \"Sửa liên kết\",\n      \"editEmbed\": \"\",\n      \"create\": \"Tạo liên kết\",\n      \"createEmbed\": \"\",\n      \"label\": \"Liên kết\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Điều chỉnh nét\",\n      \"exit\": \"Thoát chỉnh nét\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Khoá\",\n      \"unlock\": \"Mở khoá\",\n      \"lockAll\": \"Khóa tất cả\",\n      \"unlockAll\": \"Mở khóa tất cả\"\n    },\n    \"statusPublished\": \"Đã đăng tải\",\n    \"sidebarLock\": \"Giữ thanh bên luôn mở\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Chưa có món nào...\",\n    \"hint_emptyLibrary\": \"Chọn một món trên canvas để thêm nó vào đây, hoặc cài đặt thư viện từ kho lưu trữ công cộng, ở bên dưới.\",\n    \"hint_emptyPrivateLibrary\": \"Chọn một món trên canvas để thêm nó vào đây.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Reset canvas\",\n    \"exportJSON\": \"Xuất ra tập tin\",\n    \"exportImage\": \"Xuất file ảnh...\",\n    \"export\": \"Lưu vào...\",\n    \"copyToClipboard\": \"Sao chép vào bộ nhớ tạm\",\n    \"save\": \"Lưu vào tập tin hiện tại\",\n    \"saveAs\": \"Lưu thành\",\n    \"load\": \"Mở\",\n    \"getShareableLink\": \"Tạo liên kết để chia sẻ\",\n    \"close\": \"Đóng\",\n    \"selectLanguage\": \"Chọn ngôn ngữ\",\n    \"scrollBackToContent\": \"Cuộn về nội dung chính\",\n    \"zoomIn\": \"Phóng to\",\n    \"zoomOut\": \"Thu nhỏ\",\n    \"resetZoom\": \"Đặt lại thu phóng\",\n    \"menu\": \"Bảng chọn\",\n    \"done\": \"Xong\",\n    \"edit\": \"Chỉnh sửa\",\n    \"undo\": \"Hoàn tác\",\n    \"redo\": \"Làm lại\",\n    \"resetLibrary\": \"\",\n    \"createNewRoom\": \"Tạo phòng mới\",\n    \"fullScreen\": \"Toàn màn hình\",\n    \"darkMode\": \"Chế độ tối\",\n    \"lightMode\": \"Chế độ sáng\",\n    \"zenMode\": \"Chế độ zen\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Thoát chể độ zen\",\n    \"cancel\": \"Hủy\",\n    \"clear\": \"Làm sạch\",\n    \"remove\": \"Xóa\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Đăng tải\",\n    \"submit\": \"Gửi\",\n    \"confirm\": \"Xác nhận\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Điều này sẽ dọn hết canvas. Bạn có chắc không?\",\n    \"couldNotCreateShareableLink\": \"Không thể tạo đường dẫn chia sẻ.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Không thể tạo đường dẫn chia sẻ: bản vẽ quá lớn\",\n    \"couldNotLoadInvalidFile\": \"Không thể load tập tin không hợp lệ\",\n    \"importBackendFailed\": \"\",\n    \"cannotExportEmptyCanvas\": \"Không thể xuất canvas trống.\",\n    \"couldNotCopyToClipboard\": \"\",\n    \"decryptFailed\": \"Không thể giải mã dữ liệu.\",\n    \"uploadedSecurly\": \"\",\n    \"loadSceneOverridePrompt\": \"\",\n    \"collabStopOverridePrompt\": \"Dừng phiên sẽ ghi đè lên bản vẽ được lưu trữ cục bộ trước đó của bạn. Bạn có chắc không?\\n\\n(Nếu bạn muốn giữ bản vẽ cục bộ của mình, chỉ cần đóng tab trình duyệt.)\",\n    \"errorAddingToLibrary\": \"Không thể thêm món vào thư viện\",\n    \"errorRemovingFromLibrary\": \"Không thể xoá món khỏi thư viện\",\n    \"confirmAddLibrary\": \"Hình {{numShapes}} sẽ được thêm vào thư viện. Bạn chắc chứ?\",\n    \"imageDoesNotContainScene\": \"Hình ảnh này dường như không chứa bất kỳ dữ liệu cảnh nào. Bạn đã bật tính năng nhúng cảnh khi xuất chưa?\",\n    \"cannotRestoreFromImage\": \"\",\n    \"invalidSceneUrl\": \"\",\n    \"resetLibrary\": \"\",\n    \"removeItemsFromsLibrary\": \"Xoá {{count}} món từ thư viện?\",\n    \"invalidEncryptionKey\": \"Khóa mã hóa phải có 22 ký tự. Hợp tác trực tiếp bị vô hiệu hóa.\",\n    \"collabOfflineWarning\": \"Không có kết nối internet.\\nThay đổi của bạn sẽ không được lưu!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Loại tập tin không được hỗ trợ.\",\n    \"imageInsertError\": \"Không thể thêm ảnh. Hãy thử lại sau...\",\n    \"fileTooBig\": \"Tệp tin quá lớn. Dung lượng tối đa cho phép là {{maxSize}}.\",\n    \"svgImageInsertError\": \"Không thể thêm ảnh SVG. Mã SVG có vẻ sai.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"SVG không hợp lệ.\",\n    \"cannotResolveCollabServer\": \"Không thể kết nối với máy chủ hợp tác. Hãy tải lại trang và thử lại.\",\n    \"importLibraryError\": \"Không thể tải thư viện\",\n    \"collabSaveFailed\": \"Không thể lưu vào cơ sở dữ liệu. Nếu vấn đề tiếp tục xảy ra, bạn nên lưu tệp vào máy để đảm bảo bạn không bị mất công việc.\",\n    \"collabSaveFailed_sizeExceeded\": \"Không thể lưu vào cơ sở dữ liệu, canvas có vẻ quá lớn. Bạn nên lưu tệp cục bộ để đảm bảo bạn không bị mất công việc.\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Lựa chọn\",\n    \"image\": \"Chèn ảnh\",\n    \"rectangle\": \"Hình chữ nhật\",\n    \"diamond\": \"Kim cương\",\n    \"ellipse\": \"Hình elíp\",\n    \"arrow\": \"Mũi tên\",\n    \"line\": \"Đường kẻ\",\n    \"freedraw\": \"Vẽ\",\n    \"text\": \"Văn bản\",\n    \"library\": \"Thư viện\",\n    \"lock\": \"Giữ dụng cũ hiện tại sau khi vẽ\",\n    \"penMode\": \"Chế độ bút vẽ - ngăn ngừa chạm nhầm\",\n    \"link\": \"Thêm/ Chỉnh sửa liên kết cho hình được chọn\",\n    \"eraser\": \"Xóa\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"Tay kéo\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Hành động canvas\",\n    \"selectedShapeActions\": \"Các hành động cho hình dạng đã chọn\",\n    \"shapes\": \"Các hình khối\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Để di chuyển canvas, giữ con lăn chuột hoặc phím cách trong khi kéo, hoặc sử dụng công cụ cầm tay\",\n    \"linearElement\": \"Ấn để bắt đầu nhiểm điểm vẽ, kéo để vẽ một đường thẳng\",\n    \"freeDraw\": \"Ấn bà kéo, thả khi bạn xong\",\n    \"text\": \"Mẹo: bạn có thể thêm văn bản tại bất cứ đâu bằng cách ấn hai lần bằng tool lựa chọn\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"Ấn 2 lần hoặc nhấn ENTER để chỉnh văn bản\",\n    \"text_editing\": \"Nhấn Escape hoặc Ctrl/Cmd+ENTER để hoàn thành chỉnh sửa\",\n    \"linearElementMulti\": \"Nhấn vào điểm cuối hoặc nhấn Escape hoặc Enter để kết thúc\",\n    \"lockAngle\": \"Bạn có thể chỉnh lại góc bằng cách giữ phím SHIFT\",\n    \"resize\": \"Bạn có thể chỉnh tỷ lệ bằng cách giữ SHIFT khi chỉnh kích cỡ,\\ngiữ ALT để chỉnh kích cỡ từ trung tâm\",\n    \"resizeImage\": \"\",\n    \"rotate\": \"\",\n    \"lineEditor_info\": \"\",\n    \"lineEditor_pointSelected\": \"\",\n    \"lineEditor_nothingSelected\": \"\",\n    \"placeImage\": \"\",\n    \"publishLibrary\": \"\",\n    \"bindTextToElement\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"eraserRevert\": \"\",\n    \"firefox_clipboard_write\": \"Tính năng này có thể được bật bằng cách đặt cờ \\\"dom.events.asyncClipboard.clipboardItem\\\" thành \\\"true\\\". Để thay đổi cờ trình duyệt trong Firefox, hãy truy cập trang \\\"about:config\\\".\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Không thể xem trước\",\n    \"canvasTooBig\": \"Canvas này có thể hơi lớn.\",\n    \"canvasTooBigTip\": \"Mẹo: hãy thử di chuyển các elements nhất lại gần nhau hơn một chút.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"\",\n    \"clearCanvasMessage\": \"Nếu không tải lại được, hãy thử <button>dọn canvas.</button>\",\n    \"clearCanvasCaveat\": \" Điều này sẽ dẫn đến mất dữ liệu bạn đã làm \",\n    \"trackedToSentry\": \"\",\n    \"openIssueMessage\": \"\",\n    \"sceneContent\": \"\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"\",\n    \"desc_privacy\": \"\",\n    \"button_startSession\": \"\",\n    \"button_stopSession\": \"\",\n    \"desc_inProgressIntro\": \"\",\n    \"desc_shareLink\": \"\",\n    \"desc_exitSession\": \"\",\n    \"shareTitle\": \"\"\n  },\n  \"errorDialog\": {\n    \"title\": \"\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"\",\n    \"disk_details\": \"\",\n    \"disk_button\": \"\",\n    \"link_title\": \"\",\n    \"link_details\": \"\",\n    \"link_button\": \"\",\n    \"excalidrawplus_description\": \"\",\n    \"excalidrawplus_button\": \"\",\n    \"excalidrawplus_exportError\": \"\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"\",\n    \"click\": \"\",\n    \"deepSelect\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"curvedArrow\": \"\",\n    \"curvedLine\": \"\",\n    \"documentation\": \"\",\n    \"doubleClick\": \"\",\n    \"drag\": \"\",\n    \"editor\": \"\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"\",\n    \"howto\": \"\",\n    \"or\": \"\",\n    \"preventBinding\": \"\",\n    \"tools\": \"\",\n    \"shortcuts\": \"\",\n    \"textFinish\": \"\",\n    \"textNewLine\": \"\",\n    \"title\": \"\",\n    \"view\": \"\",\n    \"zoomToFit\": \"\",\n    \"zoomToSelection\": \"\",\n    \"toggleElementLock\": \"\",\n    \"movePageUpDown\": \"\",\n    \"movePageLeftRight\": \"\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Dọn canvas\"\n  },\n  \"publishDialog\": {\n    \"title\": \"\",\n    \"itemName\": \"Tên món\",\n    \"authorName\": \"\",\n    \"githubUsername\": \"\",\n    \"twitterUsername\": \"\",\n    \"libraryName\": \"\",\n    \"libraryDesc\": \"\",\n    \"website\": \"\",\n    \"placeholder\": {\n      \"authorName\": \"\",\n      \"libraryName\": \"\",\n      \"libraryDesc\": \"\",\n      \"githubHandle\": \"\",\n      \"twitterHandle\": \"\",\n      \"website\": \"\"\n    },\n    \"errors\": {\n      \"required\": \"\",\n      \"website\": \"\"\n    },\n    \"noteDescription\": \"\",\n    \"noteGuidelines\": \"\",\n    \"noteLicense\": \"\",\n    \"noteItems\": \"Từng món trong thư viện phải có tên riêng để có thể lọc. Các món thư viện sau đây sẽ thêm:\",\n    \"atleastOneLibItem\": \"Vui lòng chọn ít nhất một món thư viện để bắt đầu\",\n    \"republishWarning\": \"Lưu ý: một số món đã chọn được đánh dấu là đã xuất bản/đã gửi. Bạn chỉ nên gửi lại các món khi cập nhật thư viện hiện có hoặc gửi.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"\",\n    \"content\": \"\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"\",\n    \"removeItemsFromLib\": \"Xóa món đã chọn khỏi thư viện\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"\",\n    \"link\": \"\"\n  },\n  \"stats\": {\n    \"angle\": \"\",\n    \"element\": \"\",\n    \"elements\": \"\",\n    \"height\": \"\",\n    \"scene\": \"\",\n    \"selected\": \"\",\n    \"storage\": \"\",\n    \"title\": \"\",\n    \"total\": \"\",\n    \"version\": \"\",\n    \"versionCopy\": \"\",\n    \"versionNotAvailable\": \"\",\n    \"width\": \"\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"\",\n    \"copyStyles\": \"\",\n    \"copyToClipboard\": \"\",\n    \"copyToClipboardAsPng\": \"\",\n    \"fileSaved\": \"\",\n    \"fileSavedToFilename\": \"\",\n    \"canvas\": \"canvas\",\n    \"selection\": \"\",\n    \"pasteAsSingleElement\": \"\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"\",\n    \"black\": \"\",\n    \"white\": \"\",\n    \"red\": \"\",\n    \"pink\": \"\",\n    \"grape\": \"\",\n    \"violet\": \"\",\n    \"gray\": \"\",\n    \"blue\": \"\",\n    \"cyan\": \"\",\n    \"teal\": \"\",\n    \"green\": \"\",\n    \"yellow\": \"\",\n    \"orange\": \"\",\n    \"bronze\": \"\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"\",\n      \"center_heading_plus\": \"\",\n      \"menuHint\": \"\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"\",\n      \"center_heading\": \"\",\n      \"toolbarHint\": \"\",\n      \"helpHint\": \"\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"\",\n    \"shades\": \"\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}