{"version": 3, "sources": ["../../../locales/lv-LV.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n    \"pasteAsPlaintext\": \"<PERSON><PERSON><PERSON><PERSON>ēt kā vienkāršu tekstu\",\n    \"pasteCharts\": \"Ielīmēt grafikus\",\n    \"selectAll\": \"Atlasīt visu\",\n    \"multiSelect\": \"Pievienot elementu atlasei\",\n    \"moveCanvas\": \"<PERSON><PERSON>r<PERSON><PERSON>t tāfeli\",\n    \"cut\": \"Izgriezt\",\n    \"copy\": \"Kopēt\",\n    \"copyAsPng\": \"Kopēt starpliktuvē kā PNG\",\n    \"copyAsSvg\": \"Kop<PERSON>t starpliktuvē kā SVG\",\n    \"copyText\": \"Kop<PERSON>t starpliktuvē kā tekstu\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Pārvietot vienu slāni augstāk\",\n    \"sendToBack\": \"Pārvietot uz zemāko slāni\",\n    \"bringToFront\": \"<PERSON><PERSON>rvie<PERSON>t uz virsējo slāni\",\n    \"sendBackward\": \"<PERSON><PERSON>r<PERSON><PERSON>t par vienu slāni zemāk\",\n    \"delete\": \"Dzēst\",\n    \"copyStyles\": \"Kop<PERSON>t stilus\",\n    \"pasteStyles\": \"Ielīmēt stilus\",\n    \"stroke\": \"Svītras krāsa\",\n    \"background\": \"Fona krāsa\",\n    \"fill\": \"Aizpildījums\",\n    \"strokeWidth\": \"Svītras platums\",\n    \"strokeStyle\": \"Svītras stils\",\n    \"strokeStyle_solid\": \"Vienlaidu\",\n    \"strokeStyle_dashed\": \"Raustīta līnija\",\n    \"strokeStyle_dotted\": \"Punktota līnija\",\n    \"sloppiness\": \"Precizitāte\",\n    \"opacity\": \"Necaurspīdīgums\",\n    \"textAlign\": \"Teksta līdzināšana\",\n    \"edges\": \"Malas\",\n    \"sharp\": \"Asas\",\n    \"round\": \"Apaļas\",\n    \"arrowheads\": \"Bultas\",\n    \"arrowhead_none\": \"Nekādas\",\n    \"arrowhead_arrow\": \"Bulta\",\n    \"arrowhead_bar\": \"Svītra\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Trijstūris\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Teksta lielums\",\n    \"fontFamily\": \"Fontu saime\",\n    \"addWatermark\": \"Pievienot \\\"Radīts ar Excalidraw\\\"\",\n    \"handDrawn\": \"Rokraksts\",\n    \"normal\": \"Parasts\",\n    \"code\": \"Kods\",\n    \"small\": \"Mazs\",\n    \"medium\": \"Vidējs\",\n    \"large\": \"Liels\",\n    \"veryLarge\": \"Ļoti liels\",\n    \"solid\": \"Pilns\",\n    \"hachure\": \"Svītrots\",\n    \"zigzag\": \"Zigzaglīnija\",\n    \"crossHatch\": \"Šķērssvītrots\",\n    \"thin\": \"Šaurs\",\n    \"bold\": \"Trekns\",\n    \"left\": \"Pa kreisi\",\n    \"center\": \"Vidū\",\n    \"right\": \"Pa labi\",\n    \"extraBold\": \"Īpaši trekns\",\n    \"architect\": \"Arhitekts\",\n    \"artist\": \"Mākslinieks\",\n    \"cartoonist\": \"Karikatūrists\",\n    \"fileTitle\": \"Datnes nosaukums\",\n    \"colorPicker\": \"Krāsu atlasītājs\",\n    \"canvasColors\": \"Izmantots tāfelei\",\n    \"canvasBackground\": \"Ainas fons\",\n    \"drawingCanvas\": \"Tāfele\",\n    \"layers\": \"Slāņi\",\n    \"actions\": \"Darbības\",\n    \"language\": \"Valoda\",\n    \"liveCollaboration\": \"Sadarbība tiešsaistē...\",\n    \"duplicateSelection\": \"Izveidot kopiju\",\n    \"untitled\": \"Bez nosaukuma\",\n    \"name\": \"Vārds\",\n    \"yourName\": \"Jūsu vārds\",\n    \"madeWithExcalidraw\": \"Radīts ar Excalidraw\",\n    \"group\": \"Grupēt atlasīto\",\n    \"ungroup\": \"Atgrupēt atlasīto\",\n    \"collaborators\": \"Dalībnieki\",\n    \"showGrid\": \"Rādīt režģi\",\n    \"addToLibrary\": \"Pievienot bibliotēkai\",\n    \"removeFromLibrary\": \"Izņemt no bibliotēkas\",\n    \"libraryLoadingMessage\": \"Ielādē bibliotēku…\",\n    \"libraries\": \"Apskatīt bibliotēkas\",\n    \"loadingScene\": \"Ielādē ainu…\",\n    \"align\": \"Līdzināt\",\n    \"alignTop\": \"Līdzināt augšpusē\",\n    \"alignBottom\": \"Līdzināt lejā\",\n    \"alignLeft\": \"Līdzināt pa kreisi\",\n    \"alignRight\": \"Līdzināt pa labi\",\n    \"centerVertically\": \"Centrēt vertikāli\",\n    \"centerHorizontally\": \"Centrēt horizontāli\",\n    \"distributeHorizontally\": \"Izdalīt horizontāli\",\n    \"distributeVertically\": \"Izdalīt vertikāli\",\n    \"flipHorizontal\": \"Apmest horizontāli\",\n    \"flipVertical\": \"Apmest vertikāli\",\n    \"viewMode\": \"Skata režīms\",\n    \"share\": \"Kopīgot\",\n    \"showStroke\": \"Rādīt svītras krāsas atlasītāju\",\n    \"showBackground\": \"Rādīt fona krāsas atlasītāju\",\n    \"toggleTheme\": \"Pārslēgt krāsu tēmu\",\n    \"personalLib\": \"Personīgā bibliotēka\",\n    \"excalidrawLib\": \"Excalidraw bibliotēka\",\n    \"decreaseFontSize\": \"Samazināt fonta izmēru\",\n    \"increaseFontSize\": \"Palielināt fonta izmēru\",\n    \"unbindText\": \"Atdalīt tekstu\",\n    \"bindText\": \"Piesaistīt tekstu figūrai\",\n    \"createContainerFromText\": \"Ietilpināt tekstu figurā\",\n    \"link\": {\n      \"edit\": \"Rediģēt saiti\",\n      \"editEmbed\": \"\",\n      \"create\": \"Izveidot saiti\",\n      \"createEmbed\": \"\",\n      \"label\": \"Saite\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Rediģēt līniju\",\n      \"exit\": \"Aizvērt līnijas redaktoru\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Fiksēt\",\n      \"unlock\": \"Atbrīvot\",\n      \"lockAll\": \"Fiksēt visu\",\n      \"unlockAll\": \"Atbrīvot visu\"\n    },\n    \"statusPublished\": \"Publicēts\",\n    \"sidebarLock\": \"Paturēt atvērtu sānjoslu\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Neviena vienība vēl nav pievienota...\",\n    \"hint_emptyLibrary\": \"Atlasiet objektu tāfelē, lai to šeit pievienotu, vai pievienojiet publisku bibliotēku zemāk.\",\n    \"hint_emptyPrivateLibrary\": \"Atlasiet objektu tāfelē, lai to šeit pievienotu.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Atiestatīt tāfeli\",\n    \"exportJSON\": \"Eksportēt kā failu\",\n    \"exportImage\": \"Eksportēt attēlu...\",\n    \"export\": \"Saglabāt uz...\",\n    \"copyToClipboard\": \"Kopēt starpliktuvē\",\n    \"save\": \"Saglabāt pašreizējo datni\",\n    \"saveAs\": \"Saglabāt kā\",\n    \"load\": \"Atvērt\",\n    \"getShareableLink\": \"Iegūt kopīgošanas saiti\",\n    \"close\": \"Aizvērt\",\n    \"selectLanguage\": \"Izvēlieties valodu\",\n    \"scrollBackToContent\": \"Atgriezties pie satura\",\n    \"zoomIn\": \"Tuvināt\",\n    \"zoomOut\": \"Tālināt\",\n    \"resetZoom\": \"Atiestatīt tuvinājumu\",\n    \"menu\": \"Izvēlne\",\n    \"done\": \"Gatavs\",\n    \"edit\": \"Rediģēt\",\n    \"undo\": \"Atsaukt\",\n    \"redo\": \"Atcelt atsaukšanu\",\n    \"resetLibrary\": \"Atiestatīt bibliotēku\",\n    \"createNewRoom\": \"Izveidot jaunu telpu\",\n    \"fullScreen\": \"Pilnekrāna režīms\",\n    \"darkMode\": \"Tumšais režīms\",\n    \"lightMode\": \"Gaišais režīms\",\n    \"zenMode\": \"Zen režīms\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Pamest Zen režīmu\",\n    \"cancel\": \"Atcelt\",\n    \"clear\": \"Notīrīt\",\n    \"remove\": \"Noņemt\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Publicēt\",\n    \"submit\": \"Iesniegt\",\n    \"confirm\": \"Apstiprināt\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Šī funkcija notīrīs visu tāfeli. Vai turpināt?\",\n    \"couldNotCreateShareableLink\": \"Nevarēja izveidot kopīgojamo saiti.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Nevarēja izveidot kopīgojamo saiti – aina ir par lielu\",\n    \"couldNotLoadInvalidFile\": \"Nevarēja ielādēt nederīgu datni\",\n    \"importBackendFailed\": \"Ielāde no krātuves neizdevās.\",\n    \"cannotExportEmptyCanvas\": \"Nevar eksportēt tukšu tāfeli.\",\n    \"couldNotCopyToClipboard\": \"Nevarēja nokopēt starpliktuvē.\",\n    \"decryptFailed\": \"Nevarēja atšifrēt datus.\",\n    \"uploadedSecurly\": \"Augšuplāde nodrošināta ar šifrēšanu no gala līdz galam, kas nozīmē, ka Excalidraw serveri un trešās puses nevar lasīt saturu.\",\n    \"loadSceneOverridePrompt\": \"Ārēja satura ielāde aizstās jūsu pašreizējo saturu. Vai vēlaties turpināt?\",\n    \"collabStopOverridePrompt\": \"Sesijas pārtraukšana pārrakstīs jūsu iepriekšējo zīmējumu, kas saglabāts jūsu pārlūkā. Vai turpināt?\\n\\n(Ja vēlaties paturēt zīmējumu, kas saglabāts jūsu pārlūkā, vienkārši aizveriet pārlūka cilni.)\",\n    \"errorAddingToLibrary\": \"Nevarēja pievienot vienumu bibliotēkai\",\n    \"errorRemovingFromLibrary\": \"Nevarēja izņemt vienumu no bibliotēkas\",\n    \"confirmAddLibrary\": \"Šī funkcija pievienos {{numShapes}} formu(-as) jūsu bibliotēkai. Vai turpināt?\",\n    \"imageDoesNotContainScene\": \"Šķiet, ka attēls nesatur ainas datus. Vai iespējojāt ainas iegulšanu, kad eksportējāt?\",\n    \"cannotRestoreFromImage\": \"Ainu nevarēja atgūt no attēla datnes\",\n    \"invalidSceneUrl\": \"Nevarēja importēt ainu no norādītā URL. Vai nu tas ir nederīgs, vai nesatur derīgus Excalidraw JSON datus.\",\n    \"resetLibrary\": \"Šī funkcija iztukšos bibliotēku. Vai turpināt?\",\n    \"removeItemsFromsLibrary\": \"Vai izņemt {{count}} vienumu(s) no bibliotēkas?\",\n    \"invalidEncryptionKey\": \"Šifrēšanas atslēgai jābūt 22 simbolus garai. Tiešsaistes sadarbība ir izslēgta.\",\n    \"collabOfflineWarning\": \"Nav pieejams interneta pieslēgums.\\nJūsu izmaiņas netiks saglabātas!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Neatbalstīts datnes veids.\",\n    \"imageInsertError\": \"Nevarēja ievietot attēlu. Mēģiniet vēlāk...\",\n    \"fileTooBig\": \"Datne ir par lielu. Lielākais atļautais izmērs ir {{maxSize}}.\",\n    \"svgImageInsertError\": \"Nevarēja ievietot SVG attēlu. Šķiet, ka SVG marķējums nav derīgs.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"Nederīgs SVG.\",\n    \"cannotResolveCollabServer\": \"Nevarēja savienoties ar sadarbošanās serveri. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz.\",\n    \"importLibraryError\": \"Nevarēja ielādēt bibliotēku\",\n    \"collabSaveFailed\": \"Darbs nav saglabāts datubāzē. Ja problēma turpinās, saglabājiet datni lokālajā krātuvē, lai nodrošinātos pret darba pazaudēšanu.\",\n    \"collabSaveFailed_sizeExceeded\": \"Darbs nav saglabāts datubāzē, šķiet, ka tāfele ir pārāk liela. Saglabājiet datni lokālajā krātuvē, lai nodrošinātos pret darba pazaudēšanu.\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Atlase\",\n    \"image\": \"Ievietot attēlu\",\n    \"rectangle\": \"Taisnstūris\",\n    \"diamond\": \"Rombs\",\n    \"ellipse\": \"Elipse\",\n    \"arrow\": \"Bulta\",\n    \"line\": \"Līnija\",\n    \"freedraw\": \"Zīmēt\",\n    \"text\": \"Teksts\",\n    \"library\": \"Bibliotēka\",\n    \"lock\": \"Paturēt izvēlēto rīku pēc darbības\",\n    \"penMode\": \"Pildspalvas režīms – novērst pieskaršanos\",\n    \"link\": \"Pievienot/rediģēt atlasītās figūras saiti\",\n    \"eraser\": \"Dzēšgumija\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"Roka (panoramēšanas rīks)\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Tāfeles darbības\",\n    \"selectedShapeActions\": \"Izvēlētās formas darbības\",\n    \"shapes\": \"Formas\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Lai bīdītu tāfeli, turiet nospiestu ritināšanas vai atstarpes taustiņu, vai izmanto rokas rīku\",\n    \"linearElement\": \"Klikšķiniet, lai sāktu zīmēt vairākus punktus; velciet, lai zīmētu līniju\",\n    \"freeDraw\": \"Spiediet un velciet; atlaidiet, kad pabeidzat\",\n    \"text\": \"Ieteikums: lai pievienotu tekstu, varat arī jebkur dubultklikšķināt ar atlases rīku\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"Dubultklikšķiniet vai spiediet ievades taustiņu, lai rediģētu tekstu\",\n    \"text_editing\": \"Spiediet iziešanas taustiņu vai CtrlOrCmd+ENTER, lai beigtu rediģēt\",\n    \"linearElementMulti\": \"Klikšķiniet uz pēdējā punkta vai spiediet izejas vai ievades taustiņu, lai pabeigtu\",\n    \"lockAngle\": \"Varat ierobežot leņķi, turot nospiestu SHIFT\",\n    \"resize\": \"Kad maināt izmēru, varat ierobežot proporcijas, turot nospiestu SHIFT,\\nvai arī ALT, lai mainītu izmēru ap centru\",\n    \"resizeImage\": \"Varat brīvi mainīt izmēru, turot nospiestu SHIFT;\\nturiet nospiestu ALT, lai mainītu izmēru ap centru\",\n    \"rotate\": \"Rotējot varat ierobežot leņķi, turot nospiestu SHIFT\",\n    \"lineEditor_info\": \"Turiet CtrlOrCmd un dubultklikšķiniet, vai spiediet CtrlOrCmd + Enter, lai rediģētu punktus\",\n    \"lineEditor_pointSelected\": \"Spiediet dzēšanas taustiņu, lai noņemtu punktus, – CtrlOrCmd+D, lai to kopētu, vai velciet, lai pārvietotu\",\n    \"lineEditor_nothingSelected\": \"Atlasiet punktu, lai labotu (turiet nospiestu SHIFT, lai atlasītu vairākus),\\nvai turiet Alt un clikšķiniet, lai pievienotu jaunus punktus\",\n    \"placeImage\": \"Klikšķiniet, lai novietotu attēlu, vai spiediet un velciet, lai iestatītu tā izmēru\",\n    \"publishLibrary\": \"Publicēt savu bibliotēku\",\n    \"bindTextToElement\": \"Spiediet ievades taustiņu, lai pievienotu tekstu\",\n    \"deepBoxSelect\": \"Turient nospiestu Ctrl vai Cmd, lai atlasītu dziļumā un lai nepieļautu objektu pavilkšanu\",\n    \"eraserRevert\": \"Turiet Alt, lai noņemtu elementus no dzēsšanas atlases\",\n    \"firefox_clipboard_write\": \"Šis iestatījums var tikt ieslēgts ar \\\"dom.events.asyncClipboard.clipboardItem\\\" marķieri pārslēgtu uz \\\"true\\\". Lai mainītu pārlūka marķierus Firefox, apmeklē \\\"about:config\\\" lapu.\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Nevar rādīt priekšskatījumu\",\n    \"canvasTooBig\": \"Iespējams, tāfele ir par lielu.\",\n    \"canvasTooBigTip\": \"Ieteikums: mēģiniet satuvināt pašus tālākos elementus.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Notikusi kļūda. Mēģiniet <button>pārlādēt lapu.</button>\",\n    \"clearCanvasMessage\": \"Ja pārlādēšana nestrādā, mēģiniet <button>notīrot tāfeli.</button>\",\n    \"clearCanvasCaveat\": \" Tas novedīs pie darba zaudēšanas \",\n    \"trackedToSentry\": \"Kļūda ar kodu {{eventId}} tika noteikta mūsu sistēmā.\",\n    \"openIssueMessage\": \"Mēs uzmanījāmies, lai neiekļautu jūsu ainas informāciju šajā kļūdā. Ja jūsu aina nav privāta, lūdzu ziņojiet par šo kļūdu mūsu <button>kļūdu uzskaitē.</button> Lūdzu, miniet sekojošo informāciju to kopējot un ielīmējot jūsu ziņojumā platformā GitHub.\",\n    \"sceneContent\": \"Ainas saturs:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Varat ielūgt cilvēkus pašreizējajā ainā, lai sadarbotos ar tiem.\",\n    \"desc_privacy\": \"Neuztraucieties, sesija izmanto šifrēšanu no gala līdz galam, tātad jūsu zīmējums paliks privāts. Pat mūsu serveri nevarēs redzēt, ar ko esat nācis klajā.\",\n    \"button_startSession\": \"Sākt sesiju\",\n    \"button_stopSession\": \"Beigt sesiju\",\n    \"desc_inProgressIntro\": \"Notiek tiešsaistes sadarbības sesija.\",\n    \"desc_shareLink\": \"Dalieties ar šo saiti ar jebkuru, ar ko vēlaties sadarboties:\",\n    \"desc_exitSession\": \"Sesijas beigšana jūs atvienos no sadarbošanās, bet jūs vēl joprojām varēsiet strādāt ar ainu savā datorā. Ievērojiet, ka šis neietekmēs citus dalībniekus, un viņi vēl joprojām varēs sadarboties savā ainas versijā.\",\n    \"shareTitle\": \"Pievienoties tiešsaistes sadarbībai programmā Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Kļūda\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Saglabāt diskā\",\n    \"disk_details\": \"Eksportēt ainas datus datnē, ko vēlāk varēsiet importēt.\",\n    \"disk_button\": \"Saglabāt datnē\",\n    \"link_title\": \"Kopīgošanas saite\",\n    \"link_details\": \"Eksportēt kā tikai lasāmu saiti.\",\n    \"link_button\": \"Eksportēt kā saiti\",\n    \"excalidrawplus_description\": \"Saglabāt ainu savā Excalidraw+ darbvietā.\",\n    \"excalidrawplus_button\": \"Eksportēt\",\n    \"excalidrawplus_exportError\": \"Pašreiz nevarēja eksportēt uz Excalidraw+...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Lasīt mūsu blogu\",\n    \"click\": \"klikšķis\",\n    \"deepSelect\": \"Atlasīt dziļumā\",\n    \"deepBoxSelect\": \"Atlasīt dziļumā kastes ietvaros, un nepieļaut pavilkšanu\",\n    \"curvedArrow\": \"Liekta bulta\",\n    \"curvedLine\": \"Liekta līnija\",\n    \"documentation\": \"Dokumentācija\",\n    \"doubleClick\": \"dubultklikšķis\",\n    \"drag\": \"vilkt\",\n    \"editor\": \"Redaktors\",\n    \"editLineArrowPoints\": \"Rediģēt līniju/bultu punktus\",\n    \"editText\": \"Rediģēt tekstu/pievienot birku\",\n    \"github\": \"Sastapāt kļūdu? Ziņot\",\n    \"howto\": \"Sekojiet mūsu instrukcijām\",\n    \"or\": \"vai\",\n    \"preventBinding\": \"Novērst bultu piesaistīšanos\",\n    \"tools\": \"Rīki\",\n    \"shortcuts\": \"Tastatūras saīsnes\",\n    \"textFinish\": \"Pabeigt rediģēšanu (teksta redaktorā)\",\n    \"textNewLine\": \"Nākamā rindiņa (teksta redaktorā)\",\n    \"title\": \"Palīdzība\",\n    \"view\": \"Skatīt\",\n    \"zoomToFit\": \"Iestatīt mērogu, kas iekļauj visus elementus\",\n    \"zoomToSelection\": \"Iestatīt mērogu, lai rādītu atlasi\",\n    \"toggleElementLock\": \"Fiksēt/atbrīvot atlasīto\",\n    \"movePageUpDown\": \"Pārvietot lapu augšup/lejup\",\n    \"movePageLeftRight\": \"Pārvietot lapu pa labi/kreisi\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Notīrīt tāfeli\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Publicēt bibliotēku\",\n    \"itemName\": \"Vienuma nosaukums\",\n    \"authorName\": \"Autora vārds\",\n    \"githubUsername\": \"GitHub lietotājvārds\",\n    \"twitterUsername\": \"Twitter lietotājvārds\",\n    \"libraryName\": \"Bibliotēkas nosaukums\",\n    \"libraryDesc\": \"Bibliotēkas apraksts\",\n    \"website\": \"Mājaslapa\",\n    \"placeholder\": {\n      \"authorName\": \"Jūsu vārds vai lietotājvārds\",\n      \"libraryName\": \"Jūsu bibliotēkas nosaukums\",\n      \"libraryDesc\": \"Bibliotēkas apraksts, kas palīdzēs citiem saprast tās pielietojumu\",\n      \"githubHandle\": \"GitHub lietotājvārds (neobligāts), lai jūs varētu rediģēt bibliotēku pēc tās iesniegšanas izskatīšanai\",\n      \"twitterHandle\": \"Twitter lietotājvārds (neobligāts), lai mēs varētu jūs pieminēt kā autoru, kad reklamēsim bibliotēku platformā Twitter\",\n      \"website\": \"Saikne uz jūsu personīgo mājaslapu vai kādu citu lapu (neobligāta)\"\n    },\n    \"errors\": {\n      \"required\": \"Obligāts\",\n      \"website\": \"Ievadiet derīgu URL\"\n    },\n    \"noteDescription\": \"Iesniegt savu bibliotēku iekļaušanai <link>publiskajā bibliotēku datubāzē</link>, lai citi to varētu izmantot savos zīmējumos.\",\n    \"noteGuidelines\": \"Šai bibliotēkai vispirms jātiek manuāli apstiprinātai. Lūdzu, izlasiet <link>norādījumus</link> pirms iesniegšanas. Jums vajadzēs GitHub kontu, lai sazinātos un veiktu izmaiņas, ja tādas būs pieprasītas, bet tas nav absolūti nepieciešams.\",\n    \"noteLicense\": \"Iesniedzot bibliotēku, jūs piekrītat tās publicēšanai saskaņā ar <link>MIT Licenci, </link>kas īsumā nozīmē, ka jebkurš to varēs izmantot bez ierobežojumiem.\",\n    \"noteItems\": \"Katram bibliotēkas vienumam jābūt savam nosaukumam, lai to varētu atrast filtrējot. Tiks iekļauti sekojošie bibliotēkas vienumi:\",\n    \"atleastOneLibItem\": \"Lūdzu, atlasiet vismaz vienu bibliotēkas vienumu, lai sāktu darbu\",\n    \"republishWarning\": \"Ievēro: daži no atzīmētajiem objektiem jau atzīmēti kā publicēti vai iesniegti publicēšanai. Tos vajadzētu atkārtoti iesniegt tikai tad, ja vēlies labot esošo bibliotēku.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Bibliotēka iesniegta\",\n    \"content\": \"Paldies, {{authorName}}! Jūsu bibliotēka iesniegta izskatīšanai. Jūs varat izsekot iesnieguma statusam<link>šeit</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Atiestatīt bibliotēku\",\n    \"removeItemsFromLib\": \"Noņemt atlasītos vienumus no bibliotēkas\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Jūsu zīmējumi ir šifrēti no gala līdz galam; līdz ar to Excalidraw serveri tos nekad neredzēs.\",\n    \"link\": \"Ieraksts par šifrēšanu no gala līdz galam Excalidraw blogā\"\n  },\n  \"stats\": {\n    \"angle\": \"Leņķis\",\n    \"element\": \"Elements\",\n    \"elements\": \"Elementi\",\n    \"height\": \"Augstums\",\n    \"scene\": \"Aina\",\n    \"selected\": \"Atlasīti\",\n    \"storage\": \"Krātuve\",\n    \"title\": \"Statistika entuziastiem\",\n    \"total\": \"Kopā\",\n    \"version\": \"Versija\",\n    \"versionCopy\": \"Klikšķiniet, lai nokopētu\",\n    \"versionNotAvailable\": \"Versija nav pieejama\",\n    \"width\": \"Platums\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Pievienots bibliotēkai\",\n    \"copyStyles\": \"Nokopēja stilus.\",\n    \"copyToClipboard\": \"Nokopēja starpliktuvē.\",\n    \"copyToClipboardAsPng\": \"Nokopēja {{exportSelection}} starpliktuvē kā PNG ({{exportColorScheme}})\",\n    \"fileSaved\": \"Datne saglabāta.\",\n    \"fileSavedToFilename\": \"Saglabāts kā {filename}\",\n    \"canvas\": \"tāfeli\",\n    \"selection\": \"atlasi\",\n    \"pasteAsSingleElement\": \"Izmantojiet {{shortcut}}, lai ielīmētu kā jaunu elementu, vai ielīmētu esošā teksta lauciņā\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Caurspīdīgs\",\n    \"black\": \"\",\n    \"white\": \"\",\n    \"red\": \"\",\n    \"pink\": \"\",\n    \"grape\": \"\",\n    \"violet\": \"\",\n    \"gray\": \"\",\n    \"blue\": \"\",\n    \"cyan\": \"\",\n    \"teal\": \"\",\n    \"green\": \"\",\n    \"yellow\": \"\",\n    \"orange\": \"\",\n    \"bronze\": \"\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Visi jūsu dati tiek glabāti uz vietas jūsu pārlūkā.\",\n      \"center_heading_plus\": \"Vai tā vietā vēlies doties uz Excalidraw+?\",\n      \"menuHint\": \"Eksportēšana, iestatījumi, valodas...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Eksportēšana, iestatījumi un vēl...\",\n      \"center_heading\": \"Diagrammas. Izveidotas. Vienkārši.\",\n      \"toolbarHint\": \"Izvēlies rīku un sāc zīmēt!\",\n      \"helpHint\": \"Īsceļi un palīdzība\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"\",\n    \"shades\": \"\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}