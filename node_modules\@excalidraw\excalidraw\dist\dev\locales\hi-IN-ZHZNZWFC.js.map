{"version": 3, "sources": ["../../../locales/hi-IN.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"चिपकाएँ\",\n    \"pasteAsPlaintext\": \"सादे पाठ के रूप में चिपकाएं\",\n    \"pasteCharts\": \"चार्ट चिपकाएँ\",\n    \"selectAll\": \"सभी चुनें\",\n    \"multiSelect\": \"आकार को चयन में जोड़ें\",\n    \"moveCanvas\": \"कैनवास को स्थानांतरित करें\",\n    \"cut\": \"काटें\",\n    \"copy\": \"प्रतिलिपि\",\n    \"copyAsPng\": \"क्लिपबोर्ड पर कॉपी करें ,पीएनजी के रूप में\",\n    \"copyAsSvg\": \"क्लिपबोर्ड पर कॉपी करें,एसवीजी के रूप में\",\n    \"copyText\": \"लेखन के रूप में पटल पर कॉपी करें\",\n    \"copySource\": \"स्त्रोत को प्रति-फलक पे प्रतिलिपित करे.\",\n    \"convertToCode\": \"सांकेतिक लिपि में परिवर्तित करे\",\n    \"bringForward\": \"सामने लाएं\",\n    \"sendToBack\": \"पीछे भेजें\",\n    \"bringToFront\": \"सामने लाएँ\",\n    \"sendBackward\": \"पीचे भीजे\",\n    \"delete\": \"मिटाए\",\n    \"copyStyles\": \"कॉपी स्टाइल\",\n    \"pasteStyles\": \"स्टाइल पेस्ट करें\",\n    \"stroke\": \"रेखा\",\n    \"background\": \"पृष्ठभूमि\",\n    \"fill\": \"भरें\",\n    \"strokeWidth\": \"रेखा की चौड़ाई\",\n    \"strokeStyle\": \"स्ट्रोक का आकार\",\n    \"strokeStyle_solid\": \"ठोस\",\n    \"strokeStyle_dashed\": \"डैश\",\n    \"strokeStyle_dotted\": \"बिंदीदार\",\n    \"sloppiness\": \"बेढ़ंगापन\",\n    \"opacity\": \"अपारदर्शिता\",\n    \"textAlign\": \"टेक्स्ट संरेखन\",\n    \"edges\": \"किनारा\",\n    \"sharp\": \"नुकीला\",\n    \"round\": \"गोल\",\n    \"arrowheads\": \"तीर शीर्ष\",\n    \"arrowhead_none\": \"कोई भी नहीं\",\n    \"arrowhead_arrow\": \"तीर\",\n    \"arrowhead_bar\": \"बार\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"त्रिकोण\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"फ़ॉन्ट का आकार\",\n    \"fontFamily\": \"फ़ॉन्ट का परिवार\",\n    \"addWatermark\": \"ऐड \\\"मेड विथ एक्सकैलिडराव\\\"\",\n    \"handDrawn\": \"हाथ से बनाया हुआ\",\n    \"normal\": \"साधारण\",\n    \"code\": \"कोड\",\n    \"small\": \"छोटा\",\n    \"medium\": \"मध्यम\",\n    \"large\": \"बड़ा\",\n    \"veryLarge\": \"बहुत बड़ा\",\n    \"solid\": \"दृढ़\",\n    \"hachure\": \"हैशूर\",\n    \"zigzag\": \"तेढ़ी मेढ़ी\",\n    \"crossHatch\": \"क्रॉस हैच\",\n    \"thin\": \"पतला\",\n    \"bold\": \"मोटा\",\n    \"left\": \"बाएं\",\n    \"center\": \"मध्य\",\n    \"right\": \"दाएँ\",\n    \"extraBold\": \"बहुत मोटा\",\n    \"architect\": \"वास्तुकार\",\n    \"artist\": \"कलाकार\",\n    \"cartoonist\": \"व्यंग्य चित्रकार\",\n    \"fileTitle\": \"फ़ाइल का नाम\",\n    \"colorPicker\": \"रंग चयन\",\n    \"canvasColors\": \"कॅनवास पर प्रयोगित\",\n    \"canvasBackground\": \"कैनवास बैकग्राउंड\",\n    \"drawingCanvas\": \"कैनवास बना रहे हैं\",\n    \"layers\": \"परतें\",\n    \"actions\": \"कार्रवाई\",\n    \"language\": \"भाषा\",\n    \"liveCollaboration\": \"जीवंत सहयोग...\",\n    \"duplicateSelection\": \"डुप्लिकेट\",\n    \"untitled\": \"अशीर्षित\",\n    \"name\": \"नाम\",\n    \"yourName\": \"आपका नाम\",\n    \"madeWithExcalidraw\": \"मेड विथ एक्सकैलिडराव\",\n    \"group\": \"समूह चयन\",\n    \"ungroup\": \"समूह चयन असमूहीकृत करें\",\n    \"collaborators\": \"सहयोगी\",\n    \"showGrid\": \"ग्रिड दिखाएं\",\n    \"addToLibrary\": \"लाइब्रेरी से जोड़ें\",\n    \"removeFromLibrary\": \"लाइब्रेरी से निकालें\",\n    \"libraryLoadingMessage\": \"लाइब्रेरी खुल रही है\",\n    \"libraries\": \"लाइब्रेरी ब्राउज़ करें\",\n    \"loadingScene\": \"दृश्य खुल रहा है\",\n    \"align\": \"संरेखित करें\",\n    \"alignTop\": \"ऊपर संरेखित करें\",\n    \"alignBottom\": \"नीचे संरेखित करें\",\n    \"alignLeft\": \"बायें संरेखित करें\",\n    \"alignRight\": \"दायें संरेखित करें\",\n    \"centerVertically\": \"लंबवत केन्द्रित\",\n    \"centerHorizontally\": \"क्षैतिज केन्द्रित\",\n    \"distributeHorizontally\": \"क्षैतिज रूप से वितरित करें\",\n    \"distributeVertically\": \"खड़ी रूप से वितरित करें\",\n    \"flipHorizontal\": \"दायें बायें पलटे\",\n    \"flipVertical\": \"ऊपर नीचे पलटे\",\n    \"viewMode\": \"अलग अलग देखें\",\n    \"share\": \"शेयर करें\",\n    \"showStroke\": \"\",\n    \"showBackground\": \"पृष्ठभूमि रंग वरक़ दिखाये\",\n    \"toggleTheme\": \"\",\n    \"personalLib\": \"वैयक्तिक समूहकोष\",\n    \"excalidrawLib\": \"एक्सकेलीड्रॉ समूहकोष\",\n    \"decreaseFontSize\": \"आकार घटाइऐ\",\n    \"increaseFontSize\": \"फ़ॉन्ट आकार बढ़ाएँ\",\n    \"unbindText\": \"लिपि को बंधमुक्त करें\",\n    \"bindText\": \"लेखन को कोश से जोड़े\",\n    \"createContainerFromText\": \"मूलपाठ कंटेनर में मोड के दिखाए\",\n    \"link\": {\n      \"edit\": \"कड़ी संपादित करे\",\n      \"editEmbed\": \"\",\n      \"create\": \"\",\n      \"createEmbed\": \"\",\n      \"label\": \"\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"रेखा संपादित करे\",\n      \"exit\": \"रेखा संपादक के बाहर\"\n    },\n    \"elementLock\": {\n      \"lock\": \"ताले में रखें\",\n      \"unlock\": \"ताले से बाहर\",\n      \"lockAll\": \"सब ताले के अंदर रखे\",\n      \"unlockAll\": \"सब ताले के बाहर निकाले\"\n    },\n    \"statusPublished\": \"प्रकाशित\",\n    \"sidebarLock\": \"साइडबार खुला रखे.\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"चित्रफलक से रंग चुने\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"अभी तक कोई आइटम जोडा नहीं गया.\",\n    \"hint_emptyLibrary\": \"यहाँ जोड़ने के लिए पटल से एक वस्तु चुने, अथवा जन कोष से एक संग्रह नीचे स्थापित करें.\",\n    \"hint_emptyPrivateLibrary\": \"यहाँ जोड़ने के लिए पटल से एक वस्तु चुने.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"कैनवास रीसेट करें\",\n    \"exportJSON\": \"\",\n    \"exportImage\": \"प्रतिमा निर्यात करे...\",\n    \"export\": \"यंहा सुरक्षित करे...\",\n    \"copyToClipboard\": \"क्लिपबोर्ड पर प्रतिलिपि बनाएँ\",\n    \"save\": \"\",\n    \"saveAs\": \"सेव करे इस तरह\",\n    \"load\": \"खोलें\",\n    \"getShareableLink\": \"साझा करने योग्य लिंक प्राप्त करें\",\n    \"close\": \"बंद करें\",\n    \"selectLanguage\": \"भाषा चुनें\",\n    \"scrollBackToContent\": \"सामग्री पर वापस स्क्रॉल करें\",\n    \"zoomIn\": \"बड़ा करें\",\n    \"zoomOut\": \"छोटा करें\",\n    \"resetZoom\": \"ज़ूम रीसेट करें\",\n    \"menu\": \"मेन्यू\",\n    \"done\": \"समाप्त\",\n    \"edit\": \"संशोधन करें\",\n    \"undo\": \"पूर्ववत् करें\",\n    \"redo\": \"फिर से करें\",\n    \"resetLibrary\": \"\",\n    \"createNewRoom\": \"एक नया कमरा बनाएं\",\n    \"fullScreen\": \"पूरी स्क्रीन\",\n    \"darkMode\": \"डार्क मोड\",\n    \"lightMode\": \"लाइट मोड\",\n    \"zenMode\": \"ज़ेन मोड\",\n    \"objectsSnapMode\": \"वस्तुओं से पकड़े\",\n    \"exitZenMode\": \"जेन मोड से बाहर निकलें\",\n    \"cancel\": \"\",\n    \"clear\": \"साफ़ करे\",\n    \"remove\": \"हटाएं\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"प्रकाशित करें\",\n    \"submit\": \"प्रस्तुत करे\",\n    \"confirm\": \"पुष्टि करें\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"इससे पूरा कैनवास साफ हो जाएगा। क्या आपको यकीन है?\",\n    \"couldNotCreateShareableLink\": \"साझा करने योग्य लिंक नहीं बनाया जा सका।\",\n    \"couldNotCreateShareableLinkTooBig\": \"लिंक शेयर नहीं कर सकता: दृश्य बहुत बड़ा\",\n    \"couldNotLoadInvalidFile\": \"अमान्य फ़ाइल लोड नहीं की जा सकी\",\n    \"importBackendFailed\": \"बैकएंड से आयात करना विफल रहा।\",\n    \"cannotExportEmptyCanvas\": \"खाली कैनवास निर्यात नहीं कर सकता।\",\n    \"couldNotCopyToClipboard\": \"क्लिपबोर्ड पर कॉपी नहीं किया जा सका\",\n    \"decryptFailed\": \"डेटा को डिक्रिप्ट नहीं किया जा सका।\",\n    \"uploadedSecurly\": \"अपलोड को एंड-टू-एंड एन्क्रिप्शन के साथ सुरक्षित किया गया है, जिसका मतलब है कि एक्सक्लूसिव सर्वर और थर्ड पार्टी कंटेंट नहीं पढ़ सकते हैं।\",\n    \"loadSceneOverridePrompt\": \"लोड हो रहा है बाहरी ड्राइंग आपके मौजूदा सामग्री को बदल देगा। क्या आप जारी रखना चाहते हैं?\",\n    \"collabStopOverridePrompt\": \"चालू सत्र समाप्ति से आपका संग्रहित पूर्व स्थानीय अधिलेखन नष्ट होकर पुनः अधिलेखित होगा, क्या आपको यक़ीन हैं? ( यदी आपको पूर्व स्थापित अधिलेखन सुरक्षित चाहिये तो बस ब्राउज़र टैब बंद करे)\",\n    \"errorAddingToLibrary\": \"संग्रह में जोडा न जा सका\",\n    \"errorRemovingFromLibrary\": \"संग्रह से हटाया नहीं जा सका\",\n    \"confirmAddLibrary\": \"लाइब्रेरी जोड़ें पुष्‍टि करें आकार संख्या\",\n    \"imageDoesNotContainScene\": \"ऐसा लगता है कि इस छवि में कोई दृश्य डेटा नहीं है। क्या आपने निर्यात के दौरान दृश्य एम्बेडिंग अनुमतित की है?\",\n    \"cannotRestoreFromImage\": \"छवि फ़ाइल बहाल दृश्य नहीं है\",\n    \"invalidSceneUrl\": \"दिये गये युआरेल से दृश्य आयात नहीं किया जा सका. यह या तो अनुचित है, या इसमें उचित Excalidraw JSON डेटा नहीं है।\",\n    \"resetLibrary\": \"यह पूरा संग्रह रिक्त करेगा. क्या आपको यक़ीन हैं?\",\n    \"removeItemsFromsLibrary\": \"{{count}} वस्तु(यें) संग्रह से हटायें?\",\n    \"invalidEncryptionKey\": \"कूटलेखन कुंजी 22 अक्षरों की होनी चाहिये, इसलिये जीवंत सहयोग अक्षम हैं\",\n    \"collabOfflineWarning\": \"कोई इंटरनेट कनेक्शन उपलब्ध नहीं है।\\nआपके बदलाव सहेजे नहीं जाएंगे!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"असमर्थित फाइल प्रकार\",\n    \"imageInsertError\": \"छवि सम्मिलित नहीं की जा सकी. पुनः प्रयत्न करे...\",\n    \"fileTooBig\": \"फ़ाइल ज़रूरत से ज़्यादा बड़ी हैं. अधिकतम अनुमित परिमाण {{maxSize}} हैं\",\n    \"svgImageInsertError\": \"एसवीजी छवि सम्मिलित नहीं कर सके, एसवीजी रचना अनुचित हैं\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"अनुचित SVG\",\n    \"cannotResolveCollabServer\": \"कॉलेब सर्वर से कनेक्शन नहीं हो पा रहा. कृपया पृष्ठ को पुनः लाने का प्रयास करे.\",\n    \"importLibraryError\": \"संग्रह प्रतिष्ठापित नहीं किया जा सका\",\n    \"collabSaveFailed\": \"किसी कारण वश अंदरूनी डेटाबेस में सहेजा नहीं जा सका। यदि समस्या बनी रहती है, तो किये काम को खोने न देने के लिये अपनी फ़ाइल को स्थानीय रूप से सहेजे।\",\n    \"collabSaveFailed_sizeExceeded\": \"लगता है कि पृष्ठ तल काफ़ी बड़ा है, इस्कारण अंदरूनी डेटाबेस में सहेजा नहीं जा सका। किये काम को खोने न देने के लिये अपनी फ़ाइल को स्थानीय रूप से सहेजे।\",\n    \"imageToolNotSupported\": \"प्रतिमायें अक्षम की गयी हैं\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"लगता है कि आप Brave ब्राउज़र का उपयोग कर रहे और साथ में <bold>आक्रामक उँगलियो के छाप </bold> का चयन किया हुवा है\",\n      \"line2\": \"यह आपके चित्रों के <bold>पाठ तत्वों</bold>को खंडित कर सकता हैं\",\n      \"line3\": \"हमें आपसे ठोस आग्रह है की आप सेट्टिंग में इस विकल्प का चयन ना करे.<link> इस अनुक्रम </link> का पालन करके इसका पता लगा सकते हैं\",\n      \"line4\": \"यदि इस सेटिंग्स को अक्षम करने पर भी पृष्ठ ठीक नहीं दिखता हो तो, हमारे GitHub पर एक <issueLink>मुद्दा प्रस्तुत</issueLink> करे, या हमें <discordLink>डिस्कोर्ड</discordLink> पर लिखित सम्पर्क करें\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"आयफ़्रेम तत्व समूहकोष में जोडा नहीं जा सका.\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"चिपकाया नहीं जा सका (सिस्टम क्लिपबोर्ड से पढ़ा नहीं जा सका).\",\n    \"asyncPasteFailedOnParse\": \"चिपकाया नहीं जा सका.\",\n    \"copyToSystemClipboardFailed\": \"क्लिपबोर्ड पर प्रतिलिपि नहीं बनाई जा सकी.\"\n  },\n  \"toolBar\": {\n    \"selection\": \"चयन\",\n    \"image\": \"छवि सम्मिलित करें\",\n    \"rectangle\": \"आयात\",\n    \"diamond\": \"तिर्यग्वर्ग\",\n    \"ellipse\": \"दीर्घवृत्त\",\n    \"arrow\": \"तीर\",\n    \"line\": \"रेखा\",\n    \"freedraw\": \"चित्रांतित करे\",\n    \"text\": \"पाठ\",\n    \"library\": \"लाइब्रेरी\",\n    \"lock\": \"ड्राइंग के बाद चयनित टूल को सक्रिय रखें\",\n    \"penMode\": \"पेन का मोड - स्पर्श टाले\",\n    \"link\": \"\",\n    \"eraser\": \"रबड़\",\n    \"frame\": \"\",\n    \"magicframe\": \"तारिक ढाँचें को सांकेतिक लिपि में\",\n    \"embeddable\": \"\",\n    \"laser\": \"लेसर टॉर्च\",\n    \"hand\": \"हाथ ( खिसकाने का औज़ार)\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"मर्मेड से एक्सकाली में\",\n    \"magicSettings\": \"कृतिम बुद्धिमत्ता सेटिंग्स\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"कैनवास क्रिया\",\n    \"selectedShapeActions\": \"चयनित आकृति क्रियाएं\",\n    \"shapes\": \"आकृतियाँ\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"कैनवास को सरकाने के लिए, ड्रैग करते समय माउस व्हील को पकड़े रखे या स्पेसबार को दबाए रखे, अथवा हाथ वाले औज़ार का उपयोग करें\",\n    \"linearElement\": \"कई बिंदुओं को शुरू करने के लिए क्लिक करें, सिंगल लाइन के लिए खींचें\",\n    \"freeDraw\": \"क्लिक करें और खींचें। समाप्त करने के लिए, छोड़ो\",\n    \"text\": \"आप चयन टूल से कहीं भी डबल-क्लिक करके टेक्स्ट जोड़ सकते हैं\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"\",\n    \"text_editing\": \"\",\n    \"linearElementMulti\": \"अंतिम बिंदु पर क्लिक करें या समाप्त होने के लिए एस्केप या एंटर दबाएं\",\n    \"lockAngle\": \"आप घूर्णन करते समय SHIFT पकड़कर कोणों को मोड़ सकते हैं\",\n    \"resize\": \"आकार बदलते समय आप SHIFT को पकड़ कर अनुपात में कमी कर सकते हैं,\\nकेंद्र से आकार बदलने के लिए ALT दबाए रखें\",\n    \"resizeImage\": \"\",\n    \"rotate\": \"आप घूर्णन करते समय SHIFT पकड़कर कोणों को विवश कर सकते हैं\",\n    \"lineEditor_info\": \"बिंदुओं को सम्पादित करने के लिए CtrlOrCmd को दबायें रखते हुये डबल क्लिक करे, अथवा CtrlOrCmd + Enter साथ दबाये\",\n    \"lineEditor_pointSelected\": \"\",\n    \"lineEditor_nothingSelected\": \"\",\n    \"placeImage\": \"\",\n    \"publishLibrary\": \"\",\n    \"bindTextToElement\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"eraserRevert\": \"मिटाने के लिए चुने हुए चीजों को ना चुनने के लिए Alt साथ में दबाए\",\n    \"firefox_clipboard_write\": \"\\\"dom.events.asyncClipboard.clipboardItem\\\" फ़्लैग को \\\"true\\\" पर सेट करके इस सुविधा को संभवतः सक्षम किया जा सकता है। Firefox में ब्राउज़र फ़्लैग बदलने के लिए, \\\"about:config\\\" पृष्ठ पर जाएँ।\",\n    \"disableSnapping\": \"स्नैपिंग को निष्क्रिय करने के लिए CtrlOrCmd दबाए रखें\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"पूर्वावलोकन नहीं दिखा सकते हैं\",\n    \"canvasTooBig\": \"कैनवास बहुत बड़ा\",\n    \"canvasTooBigTip\": \"कैनवास बहुत बड़ा टिप\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"एक त्रुटि का सामना करना पड़ा। प्रयत्न <button>इस पृष्ठ को पुनः लोड करें</button>\",\n    \"clearCanvasMessage\": \"यदि पुनः लोड करना काम नहीं करता है, तो प्रयास करें <button>कैनवास साफ करना।</button>\",\n    \"clearCanvasCaveat\": \" इससे काम का नुकसान होगा \",\n    \"trackedToSentry\": \"पहचानकर्ता के साथ त्रुटि {{eventId}} हमारे सिस्टम पर नज़र रखी गई थी।\",\n    \"openIssueMessage\": \"हम बहुत सतर्क थे कि त्रुटि पर आपकी दृश्य जानकारी शामिल न करें। यदि आपका दृश्य निजी नहीं है, तो कृपया हमारे बारे में विचार करें <button>बग ट्रैकर</button> कृपया GitHub मुद्दे को कॉपी और पेस्ट करके नीचे दी गई जानकारी शामिल करें।\",\n    \"sceneContent\": \"दृश्य सामग्री:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"आप अपने वर्तमान दृश्य के लोगों को अपने साथ सहयोग करने के लिए आमंत्रित कर सकते हैं।\",\n    \"desc_privacy\": \"चिंता न करें, सत्र अंत-से-अंत एन्क्रिप्शन का उपयोग करता है, इसलिए आप जो भी ड्रा करेंगे वह निजी रहेगा। यहां तक कि हमारा सर्वर भी नहीं देख पाएगा कि आप क्या कर रहे हैं।\",\n    \"button_startSession\": \"सत्र प्रारंभ करें\",\n    \"button_stopSession\": \"सत्र रुकें\",\n    \"desc_inProgressIntro\": \"लाइव सहयोग सत्र अब जारी है।\",\n    \"desc_shareLink\": \"इस लिंक को आप जिस किसी के साथ भी सहयोग करना चाहते हैं, उसके साथ साझा करें\",\n    \"desc_exitSession\": \"सत्र रोकना आपको रूम से बाहर कर देगा, लेकिन आप स्थानीय स्तर पर दृश्य के साथ काम करना जारी रख पाएंगे। ध्यान दें कि यह अन्य लोगों को प्रभावित नहीं करेगा, और वे अभी भी अपने संस्करण पर सहयोग करने में सक्षम होंगे।\",\n    \"shareTitle\": \"\"\n  },\n  \"errorDialog\": {\n    \"title\": \"गलती\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"\",\n    \"disk_details\": \"\",\n    \"disk_button\": \"\",\n    \"link_title\": \"\",\n    \"link_details\": \"\",\n    \"link_button\": \"\",\n    \"excalidrawplus_description\": \"\",\n    \"excalidrawplus_button\": \"\",\n    \"excalidrawplus_exportError\": \"\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"हमारा ब्लॉग पढे\",\n    \"click\": \"क्लिक करें\",\n    \"deepSelect\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"curvedArrow\": \"वक्र तीर\",\n    \"curvedLine\": \"वक्र रेखा\",\n    \"documentation\": \"\",\n    \"doubleClick\": \"\",\n    \"drag\": \"खींचें\",\n    \"editor\": \"संपादक\",\n    \"editLineArrowPoints\": \"रेखा/तीर बिंदु सम्पादित करे\",\n    \"editText\": \"पाठ्य सम्पादित करे/ लेबल जोड़े\",\n    \"github\": \"मुद्दा मिला? प्रस्तुत करें\",\n    \"howto\": \"हमारे गाइड का पालन करें\",\n    \"or\": \"या\",\n    \"preventBinding\": \"तीर बंधन रोकें\",\n    \"tools\": \"औज़ार\",\n    \"shortcuts\": \"कीबोर्ड के शॉर्टकट्स\",\n    \"textFinish\": \"\",\n    \"textNewLine\": \"\",\n    \"title\": \"मदद\",\n    \"view\": \"दृश्य\",\n    \"zoomToFit\": \"सभी तत्वों को फिट करने के लिए ज़ूम करें\",\n    \"zoomToSelection\": \"चयन तक ज़ूम करे\",\n    \"toggleElementLock\": \"ताले के अंदर/बाहर चुनाव\",\n    \"movePageUpDown\": \"पृष्ठ ऊपर/नीचे करे\",\n    \"movePageLeftRight\": \"पृष्ठ बायी/दायी तरफ करे\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"\"\n  },\n  \"publishDialog\": {\n    \"title\": \"\",\n    \"itemName\": \"\",\n    \"authorName\": \"\",\n    \"githubUsername\": \"\",\n    \"twitterUsername\": \"\",\n    \"libraryName\": \"\",\n    \"libraryDesc\": \"\",\n    \"website\": \"\",\n    \"placeholder\": {\n      \"authorName\": \"\",\n      \"libraryName\": \"\",\n      \"libraryDesc\": \"\",\n      \"githubHandle\": \"\",\n      \"twitterHandle\": \"\",\n      \"website\": \"\"\n    },\n    \"errors\": {\n      \"required\": \"\",\n      \"website\": \"मान्य URL प्रविष्ट करें\"\n    },\n    \"noteDescription\": \"संग्रह सम्मिलित करने हेतु प्रस्तुत करें <link>सार्वजनिक संग्रहालय</link>अन्य वक्तियों को उनके चित्रकारी में उपयोग के लिये\",\n    \"noteGuidelines\": \"संग्रह को पहले स्वीकृति आवश्यक कृपया यह पढ़ें <link>दिशा-निर्देश</link>\",\n    \"noteLicense\": \"जमा करके, आप सहमत हैं कि संग्रहण को <link>MIT लाइसेंस</link> के तहत प्रकाशित किया जाएगा, जिसका संक्षिप्त अर्थ है कि कोई भी बिना किसी प्रतिबंध के उनका उपयोग कर सकता है।\",\n    \"noteItems\": \"\",\n    \"atleastOneLibItem\": \"\",\n    \"republishWarning\": \"टिप्पणी: कुछ चुने हुवे आइटम पहले ही प्रकाशित/प्रस्तुत किए जा चुके हैं। किसी प्रकाशित संग्रह को अद्यतन करते समय या पहले से प्रस्तुत आइटम को पुन्हा प्रस्तुत करते समय, आप बस उसे केवल अद्यतन करें ।\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"\",\n    \"content\": \"{{authorName}} धन्यवाद. आपका संग्रहण समीक्षा के लिए दर्ज हो चुका है. समीक्षा स्थिति <link>यहाँ</link>जान सकते हैं.\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"\",\n    \"removeItemsFromLib\": \"\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"आपके चित्र अंत-से-अंत एन्क्रिप्टेड हैं, इसलिए एक्सक्लूसिव्रॉव के सर्वर उन्हें कभी नहीं देखेंगे।\",\n    \"link\": \"\"\n  },\n  \"stats\": {\n    \"angle\": \"कोण\",\n    \"element\": \"एलिमेंट\",\n    \"elements\": \"एलिमेंट\",\n    \"height\": \"ऊंचाई\",\n    \"scene\": \"दृश्य\",\n    \"selected\": \"चयनित\",\n    \"storage\": \"संग्रह\",\n    \"title\": \"बेवकूफ के लिए आँकड़े\",\n    \"total\": \"कुल\",\n    \"version\": \"संस्करण\",\n    \"versionCopy\": \"काॅपी करने के लिए क्लिक करें\",\n    \"versionNotAvailable\": \"संस्करण उपलब्ध नहीं है\",\n    \"width\": \"चौड़ाई\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"\",\n    \"copyStyles\": \"काॅपी कीए स्टाइल\",\n    \"copyToClipboard\": \"क्लिपबोर्ड में कॉपी कीए\",\n    \"copyToClipboardAsPng\": \"\",\n    \"fileSaved\": \"\",\n    \"fileSavedToFilename\": \"\",\n    \"canvas\": \"\",\n    \"selection\": \"\",\n    \"pasteAsSingleElement\": \"एक अवयव के रूप में चिपकाने के लिए {{shortcut}} का उपयोग करें,\\nया किसी मौजूदा पाठ संपादक में चिपकायें\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"\",\n    \"black\": \"काला\",\n    \"white\": \"सफ़ेद\",\n    \"red\": \"लाल\",\n    \"pink\": \"गुलाबी\",\n    \"grape\": \"अंगूरी\",\n    \"violet\": \"जामुनी\",\n    \"gray\": \"गहरा\",\n    \"blue\": \"नीला\",\n    \"cyan\": \"आसमानी\",\n    \"teal\": \"हरा-नीला\",\n    \"green\": \"हरा\",\n    \"yellow\": \"पीला\",\n    \"orange\": \"नारंगी\",\n    \"bronze\": \"कांस्य\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"आपका सर्व डेटा ब्राउज़र के भीतर स्थानिक जगह पे सुरक्षित किया गया.\",\n      \"center_heading_plus\": \"बजाय आपको Excalidraw+ पर जाना है?\",\n      \"menuHint\": \"निर्यात, पसंद, भाषायें, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"निर्यात, पसंद, और भी...\",\n      \"center_heading\": \"चित्रांकन। बनाया गया। सरल।\",\n      \"toolbarHint\": \"एक औजार चुने और चित्रकारी प्रारंभ करे!\",\n      \"helpHint\": \"शॉर्ट्कट और सहाय्य\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"अधिकांश उपयोगित रंग\",\n    \"colors\": \"रंग\",\n    \"shades\": \"छाया\",\n    \"hexCode\": \"हेक्स कोड\",\n    \"noShades\": \"इस रंग की कोई छाया उपलब्ध नहीं हैं\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"छवि स्वरूप में निर्यात करे\",\n        \"button\": \"छवि स्वरूप निर्यात करे\",\n        \"description\": \"दृष्य डेटा छवि स्वरूप में निर्यात करे, उस स्वरूप से आप उसे पुनः आयात कर सकते हो\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"डिस्क में सम्हाले\",\n        \"button\": \"डिस्क में सम्हाले\",\n        \"description\": \"दृष्य डेटा बाहरी फ़ाइल में निर्यात करे, जहाँसे आप उसे पुनः आयात कर सकते हो\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"एक्षकालीड्रॉ+\",\n        \"button\": \"एक्षकालीड्रॉ+ में निर्यात करे\",\n        \"description\": \"दृष्य को आपके एक्षकालीड्रॉ+ के कर्यस्थल में सम्हाले\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"फ़ाइल से लोड करें:\",\n        \"button\": \"फ़ाइल से लोड करें:\",\n        \"description\": \"फ़ाइल से लोड करने पर <bold>यह आपके कार्य की जगह लेलेगा </bold><br></br>आपकी ड्रॉइंग निम्न दर्शित विकल्पो में से एक चुनके और उपयोग करके सम्हाल सकते हैं\"\n      },\n      \"shareableLink\": {\n        \"title\": \"लिंक से लोड करें:\",\n        \"button\": \"इस जगह प्रतिस्थापित करे\",\n        \"description\": \"बाहर का चित्र लोड करने पर <bold>यह आपके कार्य की जगह लेलेगा </bold><br></br>आप आपकी ड्रॉइंग पहले निम्न दर्शित विकल्पो में से एक चुनके और उपयोग करके सम्हाल सकते हों.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"मर्मेड से एक्सकाली में\",\n    \"button\": \"अंदर डाले\",\n    \"description\": \"\",\n    \"syntax\": \"मर्मेड संरचना नियम\",\n    \"preview\": \"पूर्वावलोकन\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}