{"version": 3, "sources": ["../../../locales/sl-SI.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Prilepi\",\n    \"pasteAsPlaintext\": \"Prilepi kot navadno besedilo\",\n    \"pasteCharts\": \"Prilepi grafikone\",\n    \"selectAll\": \"Izberi vse\",\n    \"multiSelect\": \"Dodaj element v izbor\",\n    \"moveCanvas\": \"Premakni platno\",\n    \"cut\": \"<PERSON><PERSON><PERSON>ž<PERSON>\",\n    \"copy\": \"<PERSON><PERSON>raj\",\n    \"copyAsPng\": \"Kopiraj v odložišče kot PNG\",\n    \"copyAsSvg\": \"Kopiraj v odložišče kot SVG\",\n    \"copyText\": \"Kopiraj v odložišče kot besedilo\",\n    \"copySource\": \"Kopiraj vir v odložišče\",\n    \"convertToCode\": \"Pretvori v kodo\",\n    \"bringForward\": \"Postavi naprej\",\n    \"sendToBack\": \"Pomakni v ozadje\",\n    \"bringToFront\": \"Pomakni v ospredje\",\n    \"sendBackward\": \"Pošlji nazaj\",\n    \"delete\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n    \"copyStyles\": \"<PERSON><PERSON><PERSON> slog\",\n    \"pasteStyles\": \"Prilepi slog\",\n    \"stroke\": \"Poteza\",\n    \"background\": \"Ozadje\",\n    \"fill\": \"Polnilo\",\n    \"strokeWidth\": \"Debelina poteze\",\n    \"strokeStyle\": \"Slog poteze\",\n    \"strokeStyle_solid\": \"Polna\",\n    \"strokeStyle_dashed\": \"Črtkana\",\n    \"strokeStyle_dotted\": \"Pikasta\",\n    \"sloppiness\": \"Površnost\",\n    \"opacity\": \"Prekrivnost\",\n    \"textAlign\": \"Poravnava besedila\",\n    \"edges\": \"Robovi\",\n    \"sharp\": \"Ostri\",\n    \"round\": \"Okrogli\",\n    \"arrowheads\": \"Puščice\",\n    \"arrowhead_none\": \"Brez\",\n    \"arrowhead_arrow\": \"Puščica\",\n    \"arrowhead_bar\": \"Palica\",\n    \"arrowhead_circle\": \"Krog\",\n    \"arrowhead_circle_outline\": \"Krog (oris)\",\n    \"arrowhead_triangle\": \"Trikotnik\",\n    \"arrowhead_triangle_outline\": \"Trikotnik (oris)\",\n    \"arrowhead_diamond\": \"Diamant\",\n    \"arrowhead_diamond_outline\": \"Diamant (oris)\",\n    \"fontSize\": \"Velikost pisave\",\n    \"fontFamily\": \"Družina pisave\",\n    \"addWatermark\": \"Dodaj \\\"Izdelano z Excalidraw\\\"\",\n    \"handDrawn\": \"Ročno narisano\",\n    \"normal\": \"Običajno\",\n    \"code\": \"Koda\",\n    \"small\": \"Majhna\",\n    \"medium\": \"Srednja\",\n    \"large\": \"Velika\",\n    \"veryLarge\": \"Zelo velika\",\n    \"solid\": \"Polno\",\n    \"hachure\": \"Šrafura\",\n    \"zigzag\": \"Cikcak\",\n    \"crossHatch\": \"Križno\",\n    \"thin\": \"Tanko\",\n    \"bold\": \"Krepko\",\n    \"left\": \"Levo\",\n    \"center\": \"Sredina\",\n    \"right\": \"Desno\",\n    \"extraBold\": \"Ekstra krepko\",\n    \"architect\": \"Arhitekt\",\n    \"artist\": \"Umetnik\",\n    \"cartoonist\": \"Risar\",\n    \"fileTitle\": \"Ime datoteke\",\n    \"colorPicker\": \"Izbor barve\",\n    \"canvasColors\": \"Uporabljeno na platnu\",\n    \"canvasBackground\": \"Ozadje platna\",\n    \"drawingCanvas\": \"Platno za risanje\",\n    \"layers\": \"Plasti\",\n    \"actions\": \"Dejanja\",\n    \"language\": \"Jezik\",\n    \"liveCollaboration\": \"Sodelovanje v živo...\",\n    \"duplicateSelection\": \"Podvoji\",\n    \"untitled\": \"Neimenovana\",\n    \"name\": \"Ime\",\n    \"yourName\": \"Vaše ime\",\n    \"madeWithExcalidraw\": \"Izdelano z Excalidraw\",\n    \"group\": \"Združi izbor\",\n    \"ungroup\": \"Razdruži izbor\",\n    \"collaborators\": \"Sodelavci\",\n    \"showGrid\": \"Prikaži mrežo\",\n    \"addToLibrary\": \"Dodaj v knjižnico\",\n    \"removeFromLibrary\": \"Odstrani iz knjižnice\",\n    \"libraryLoadingMessage\": \"Nalaganje knjižnice ...\",\n    \"libraries\": \"Brskaj po knjižnicah\",\n    \"loadingScene\": \"Nalaganje scene...\",\n    \"align\": \"Poravnava\",\n    \"alignTop\": \"Poravnaj na vrh\",\n    \"alignBottom\": \"Poravnaj na dno\",\n    \"alignLeft\": \"Poravnaj levo\",\n    \"alignRight\": \"Poravnaj desno\",\n    \"centerVertically\": \"Navpično na sredini\",\n    \"centerHorizontally\": \"Vodoravno na sredini\",\n    \"distributeHorizontally\": \"Porazdeli vodoravno\",\n    \"distributeVertically\": \"Porazdeli navpično\",\n    \"flipHorizontal\": \"Obrni vodoravno\",\n    \"flipVertical\": \"Obrni navpično\",\n    \"viewMode\": \"Način ogleda\",\n    \"share\": \"Deli\",\n    \"showStroke\": \"Prikaži izbirnik barv poteze\",\n    \"showBackground\": \"Prikaži izbirnik barv ozadja\",\n    \"toggleTheme\": \"Obrni temo\",\n    \"personalLib\": \"Osebna knjižnica\",\n    \"excalidrawLib\": \"Knjižnica Excalidraw\",\n    \"decreaseFontSize\": \"Zmanjšaj velikost pisave\",\n    \"increaseFontSize\": \"Povečaj velikost pisave\",\n    \"unbindText\": \"Veži besedilo\",\n    \"bindText\": \"Veži besedilo na element\",\n    \"createContainerFromText\": \"Zavij besedilo v vsebnik\",\n    \"link\": {\n      \"edit\": \"Uredi povezavo\",\n      \"editEmbed\": \"Uredi povezavo in vdelaj\",\n      \"create\": \"Ustvari povezavo\",\n      \"createEmbed\": \"Ustvari povezavo in vdelaj\",\n      \"label\": \"Povezava\",\n      \"labelEmbed\": \"Povezava in vdelovanje\",\n      \"empty\": \"Povezava ni nastavljena\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Uredi črto\",\n      \"exit\": \"Zapri urejanje črte\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Zakleni\",\n      \"unlock\": \"Odkleni\",\n      \"lockAll\": \"Zakleni vse\",\n      \"unlockAll\": \"Odkleni vse\"\n    },\n    \"statusPublished\": \"Objavljeno\",\n    \"sidebarLock\": \"Obdrži stransko vrstico odprto\",\n    \"selectAllElementsInFrame\": \"Izberi vse elemente v okvirju\",\n    \"removeAllElementsFromFrame\": \"Izbriši vse elemente v okvirju\",\n    \"eyeDropper\": \"Izberi barvo s platna\",\n    \"textToDiagram\": \"Besedilo v diagram\",\n    \"prompt\": \"Poziv\"\n  },\n  \"library\": {\n    \"noItems\": \"Dodan še ni noben element...\",\n    \"hint_emptyLibrary\": \"Izberite element na platnu, da ga dodate sem, ali namestite knjižnico iz javnega skladišča spodaj.\",\n    \"hint_emptyPrivateLibrary\": \"Izberite element na platnu, da ga dodate sem.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Ponastavi platno\",\n    \"exportJSON\": \"Izvozi v datoteko\",\n    \"exportImage\": \"Izvozi sliko...\",\n    \"export\": \"Shrani v...\",\n    \"copyToClipboard\": \"Kopiraj v odložišče\",\n    \"save\": \"Shrani v trenutno datoteko\",\n    \"saveAs\": \"Shrani kot\",\n    \"load\": \"Odpri\",\n    \"getShareableLink\": \"Pridobi povezavo za deljenje\",\n    \"close\": \"Zapri\",\n    \"selectLanguage\": \"Izberi jezik\",\n    \"scrollBackToContent\": \"Pomakni se nazaj na vsebino\",\n    \"zoomIn\": \"Povečaj\",\n    \"zoomOut\": \"Pomanjšaj\",\n    \"resetZoom\": \"Ponastavi povečavo\",\n    \"menu\": \"Meni\",\n    \"done\": \"Končano\",\n    \"edit\": \"Uredi\",\n    \"undo\": \"Razveljavi\",\n    \"redo\": \"Ponovi\",\n    \"resetLibrary\": \"Ponastavi knjižnico\",\n    \"createNewRoom\": \"Ustvari novo sobo\",\n    \"fullScreen\": \"Celozaslonski način\",\n    \"darkMode\": \"Temni način\",\n    \"lightMode\": \"Svetli način\",\n    \"zenMode\": \"Način Zen\",\n    \"objectsSnapMode\": \"Pripenjanje na predmete\",\n    \"exitZenMode\": \"Zapri način Zen\",\n    \"cancel\": \"Prekliči\",\n    \"clear\": \"Počisti\",\n    \"remove\": \"Odstrani\",\n    \"embed\": \"Preklopi vdelavo\",\n    \"publishLibrary\": \"Objavi\",\n    \"submit\": \"Pošlji\",\n    \"confirm\": \"Potrdi\",\n    \"embeddableInteractionButton\": \"Kliknite za interakcijo\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"To bo počistilo celotno platno. Ali ste prepričani?\",\n    \"couldNotCreateShareableLink\": \"Povezave za deljenje ni bilo mogoče ustvariti.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Povezave za deljenje ni bilo mogoče ustvariti: scena je prevelika\",\n    \"couldNotLoadInvalidFile\": \"Neveljavne datoteke ni bilo mogoče naložiti\",\n    \"importBackendFailed\": \"Uvoz iz zaledja ni uspel.\",\n    \"cannotExportEmptyCanvas\": \"Izvoz prazne scene ni mogoč.\",\n    \"couldNotCopyToClipboard\": \"Kopiranje v odložišče ni uspelo.\",\n    \"decryptFailed\": \"Dešifriranje podatkov ni uspelo.\",\n    \"uploadedSecurly\": \"Nalaganje je bilo zaščiteno s šifriranjem od konca do konca, kar pomeni, da strežnik Excalidraw in tretje osebe ne morejo brati vsebine.\",\n    \"loadSceneOverridePrompt\": \"Nalaganje zunanje risbe bo nadomestilo vašo obstoječo vsebino. Ali želite nadaljevati?\",\n    \"collabStopOverridePrompt\": \"Ustavitev seje bo prepisala vašo prejšnjo, lokalno shranjeno risbo. Ali ste prepričani?\\n\\n(Če želite obdržati lokalno risbo, preprosto zaprite zavihek brskalnika.)\",\n    \"errorAddingToLibrary\": \"Elementa ni bilo mogoče dodati v knjižnico\",\n    \"errorRemovingFromLibrary\": \"Elementa ni bilo mogoče izbrisati iz knjižnice\",\n    \"confirmAddLibrary\": \"S tem boste v vašo knjižnico dodali oblike ({{numShapes}}). Ali ste prepričani?\",\n    \"imageDoesNotContainScene\": \"Zdi se, da ta slika ne vsebuje podatkov o sceni. Ali ste med izvozom omogočili vdelavo scene?\",\n    \"cannotRestoreFromImage\": \"Scene ni bilo mogoče obnoviti iz te slikovne datoteke\",\n    \"invalidSceneUrl\": \"S priloženega URL-ja ni bilo mogoče uvoziti scene. Je napačno oblikovana ali pa ne vsebuje veljavnih podatkov Excalidraw JSON.\",\n    \"resetLibrary\": \"To bo počistilo vašo knjižnico. Ali ste prepričani?\",\n    \"removeItemsFromsLibrary\": \"Izbriši elemente ({{count}}) iz knjižnice?\",\n    \"invalidEncryptionKey\": \"Ključ za šifriranje mora vsebovati 22 znakov. Sodelovanje v živo je onemogočeno.\",\n    \"collabOfflineWarning\": \"Internetna povezava ni na voljo.\\nVaše spremembe ne bodo shranjene!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Nepodprt tip datoteke.\",\n    \"imageInsertError\": \"Vstavljanje slike ni bilo uspešno. Poskusite ponovno kasneje...\",\n    \"fileTooBig\": \"Datoteka je prevelika. Največja dovoljena velikost je {{maxSize}}.\",\n    \"svgImageInsertError\": \"Vstavljanje slike SVG ni uspelo. Oznake SVG so videti neveljavne.\",\n    \"failedToFetchImage\": \"Pridobivanje slike ni uspelo.\",\n    \"invalidSVGString\": \"Neveljaven SVG.\",\n    \"cannotResolveCollabServer\": \"Povezave s strežnikom za sodelovanje ni bilo mogoče vzpostaviti. Ponovno naložite stran in poskusite znova.\",\n    \"importLibraryError\": \"Nalaganje knjižnice ni uspelo\",\n    \"collabSaveFailed\": \"Ni bilo mogoče shraniti v zaledno bazo podatkov. Če se težave nadaljujejo, shranite datoteko lokalno, da ne boste izgubili svojega dela.\",\n    \"collabSaveFailed_sizeExceeded\": \"Ni bilo mogoče shraniti v zaledno bazo podatkov, zdi se, da je platno preveliko. Datoteko shranite lokalno, da ne izgubite svojega dela.\",\n    \"imageToolNotSupported\": \"Slike so onemogočene.\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Videti je, da uporabljate brskalnik Brave z omogočeno nastavitvijo <bold>Agresivno blokiranje prstnih odtisov</bold>.\",\n      \"line2\": \"To bi lahko povzročilo motnje v obnašanju <bold>besedilnih elementov</bold> v vaših risbah.\",\n      \"line3\": \"Močno priporočamo, da onemogočite to nastavitev. Sledite <link>tem korakom</link>, kako to storiti.\",\n      \"line4\": \"Če onemogočanje te nastavitve ne popravi prikaza besedilnih elementov, odprite <issueLink>vprašanje</issueLink> na našem GitHubu ali nam pišite na <discordLink>Discord</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"Vdelani elementi ne morejo biti dodani v knjižnico.\",\n      \"iframe\": \"Elementov iFrame ni mogoče dodati v knjižnico.\",\n      \"image\": \"Podpora za dodajanje slik v knjižnico prihaja kmalu!\"\n    },\n    \"asyncPasteFailedOnRead\": \"Ni bilo mogoče prilepiti (ni bilo mogoče brati iz sistemskega odložišča).\",\n    \"asyncPasteFailedOnParse\": \"Ni bilo mogoče prilepiti.\",\n    \"copyToSystemClipboardFailed\": \"Ni bilo mogoče kopirati v odložišče.\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Izbor\",\n    \"image\": \"Vstavi sliko\",\n    \"rectangle\": \"Pravokotnik\",\n    \"diamond\": \"Diamant\",\n    \"ellipse\": \"Elipsa\",\n    \"arrow\": \"Puščica\",\n    \"line\": \"Črta\",\n    \"freedraw\": \"Risanje\",\n    \"text\": \"Besedilo\",\n    \"library\": \"Knjižnica\",\n    \"lock\": \"Ohrani izbrano orodje aktivno po risanju\",\n    \"penMode\": \"Način peresa - prepreči dotik\",\n    \"link\": \"Dodaj/posodobi povezavo za izbrano obliko\",\n    \"eraser\": \"Radirka\",\n    \"frame\": \"Okvir\",\n    \"magicframe\": \"Žični okvir v kodo\",\n    \"embeddable\": \"Spletna vdelava\",\n    \"laser\": \"Laserski kazalec\",\n    \"hand\": \"Roka (orodje za premikanje)\",\n    \"extraTools\": \"Več orodij\",\n    \"mermaidToExcalidraw\": \"Mermaid v Excalidraw\",\n    \"magicSettings\": \"Nastavitve AI\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Dejanja za platno\",\n    \"selectedShapeActions\": \"Dejanja za izbrane oblike\",\n    \"shapes\": \"Oblike\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Za premikanje platna med vlečenjem držite kolesce miške ali preslednico ali uporabite orodje roka\",\n    \"linearElement\": \"Kliknite za začetek več točk, povlecite za posamezno črto\",\n    \"freeDraw\": \"Kliknite in povlecite, spustite, ko končate\",\n    \"text\": \"Namig: besedilo lahko dodate tudi z dvoklikom kjer koli z orodjem za izbiro\",\n    \"embeddable\": \"Kliknite in povlecite, da ustvarite spletno vdelavo\",\n    \"text_selected\": \"Dvokliknite ali pritisnite tipko Enter, da uredite besedilo\",\n    \"text_editing\": \"Pritisnite tipko Escape ali CtrlOrCmd+Enter za zaključek urejanja\",\n    \"linearElementMulti\": \"Kliknite zadnjo točko ali pritisnite Escape ali Enter, da končate\",\n    \"lockAngle\": \"Kot lahko omejite tako, da držite tipko Shift\",\n    \"resize\": \"Razmerja lahko omejite tako, da držite tipko Shift med spreminjanjem velikosti. Držite tipko Alt, da spremenite velikost od središča\",\n    \"resizeImage\": \"Velikost lahko prosto spreminjate tako, da držite tipko Shift. Držite tipko Alt, da spremenite velikost od središča\",\n    \"rotate\": \"Kote lahko omejite tako, da med vrtenjem držite tipko Shift\",\n    \"lineEditor_info\": \"Držite CtrlOrCmd in dvokliknite ali pritisnite CtrlOrCmd + Enter za urejanje točk\",\n    \"lineEditor_pointSelected\": \"Pritisnite tipko Delete, da odstranite točko(e), CtrlOrCmd+D za podvojitev ali povlecite za premikanje\",\n    \"lineEditor_nothingSelected\": \"Izberite točko za urejanje (pridržite tipko Shift za izbiro več točk), ali držite tipko Alt in kliknite za dodajanje novih točk\",\n    \"placeImage\": \"Kliknite, da postavite sliko, ali kliknite in povlecite, da ročno nastavite njeno velikost\",\n    \"publishLibrary\": \"Objavi svojo knjižnico\",\n    \"bindTextToElement\": \"Pritisnite tipko Enter za dodajanje besedila\",\n    \"deepBoxSelect\": \"Držite tipko CtrlOrCmd za globoko izbiro in preprečitev vlečenja\",\n    \"eraserRevert\": \"Pridržite tipko Alt, da razveljavite elemente, označene za brisanje\",\n    \"firefox_clipboard_write\": \"To funkcijo lahko verjetno omogočite z nastavitvijo zastavice \\\"dom.events.asyncClipboard.clipboardItem\\\" na \\\"true\\\". Če želite spremeniti zastavice brskalnika v Firefoxu, obiščite stran \\\"about:config\\\".\",\n    \"disableSnapping\": \"Držite CtrlOrCmd, da onemogočite pripenjanje\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Predogleda ni bilo mogoče prikazati\",\n    \"canvasTooBig\": \"Morda je platno preveliko.\",\n    \"canvasTooBigTip\": \"Nasvet: poskusite premakniti najbolj oddaljene elemente nekoliko bližje skupaj.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Prišlo je do napake. Poskusite <button>ponovno naložiti stran.</button>\",\n    \"clearCanvasMessage\": \"Če ponovno nalaganje ne deluje, poskusite <button>počistiti platno.</button>\",\n    \"clearCanvasCaveat\": \" To bo povzročilo izgubo dela \",\n    \"trackedToSentry\": \"Napaka z identifikatorjem {{eventId}} smo zabeležili v naš sistem.\",\n    \"openIssueMessage\": \"Zelo smo bili previdni, da v podatke o napaki nismo vključili vaših podatkov o sceni. Če vaša scena ni zasebna, vas prosimo, da napišete več podrobnosti na našem <button>sledilniku hroščev.</button> Prosimo, vključite spodnje informacije tako, da jih kopirate in prilepite v GitHub vprašanje.\",\n    \"sceneContent\": \"Vsebina scene:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Na vašo trenutno sceno lahko povabite osebe, ki bodo sodelovale z vami.\",\n    \"desc_privacy\": \"Brez skrbi. Seja uporablja šifriranje od konca do konca, tako da bo vse, kar narišete, ostalo zasebno. Niti naš strežnik ne bo mogel videti, kaj si izmislite.\",\n    \"button_startSession\": \"Začni sejo\",\n    \"button_stopSession\": \"Ustavi sejo\",\n    \"desc_inProgressIntro\": \"Seja sodelovanja v živo je v teku.\",\n    \"desc_shareLink\": \"Delite to povezavo z vsemi, s katerimi želite sodelovati:\",\n    \"desc_exitSession\": \"Ustavitev seje vas bo odklopila od sobe, vendar boste lahko lokalno nadaljevali delo s sceno. To pa ne bo vplivalo na druge osebe. Druge osebe bodo še vedno lahko sodelovale v svoji različici.\",\n    \"shareTitle\": \"Pridruži se seji sodelovanja v živo na Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Napaka\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Shrani na disk\",\n    \"disk_details\": \"Izvozite podatke scene v datoteko, iz katere jo lahko pozneje uvozite.\",\n    \"disk_button\": \"Shrani v datoteko\",\n    \"link_title\": \"Povezava za deljenje\",\n    \"link_details\": \"Izvoz kot povezava samo za branje.\",\n    \"link_button\": \"Izvoz kot povezava\",\n    \"excalidrawplus_description\": \"Shrani sceno v svoj delovni prostor Excalidraw+.\",\n    \"excalidrawplus_button\": \"Izvoz\",\n    \"excalidrawplus_exportError\": \"Trenutno ni bilo mogoče izvoziti v Excalidraw+...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Preberite naš blog\",\n    \"click\": \"klik\",\n    \"deepSelect\": \"Globoka izbira\",\n    \"deepBoxSelect\": \"Globoka izbira znotraj polja in preprečitev vlečenja\",\n    \"curvedArrow\": \"Ukrivljena puščica\",\n    \"curvedLine\": \"Ukrivljena črta\",\n    \"documentation\": \"Dokumentacija\",\n    \"doubleClick\": \"dvojni klik\",\n    \"drag\": \"vleci\",\n    \"editor\": \"Urejevalnik\",\n    \"editLineArrowPoints\": \"Uredi črto/točke puščice\",\n    \"editText\": \"Uredi besedilo / dodaj oznako\",\n    \"github\": \"Ste našli težavo? Pošljite\",\n    \"howto\": \"Sledite našim vodičem\",\n    \"or\": \"ali\",\n    \"preventBinding\": \"Prepreči vezanje puščice\",\n    \"tools\": \"Orodja\",\n    \"shortcuts\": \"Bližnjice na tipkovnici\",\n    \"textFinish\": \"Zaključi urejanje (urejevalnik besedila)\",\n    \"textNewLine\": \"Dodaj novo vrstico (urejevalnik besedila)\",\n    \"title\": \"Pomoč\",\n    \"view\": \"Pogled\",\n    \"zoomToFit\": \"Približaj na vse elemente\",\n    \"zoomToSelection\": \"Približaj na izbor\",\n    \"toggleElementLock\": \"Zakleni/odkleni izbor\",\n    \"movePageUpDown\": \"Premakni stran gor/dol\",\n    \"movePageLeftRight\": \"Premakni stran levo/desno\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Počisti platno\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Objavi knjižnico\",\n    \"itemName\": \"Ime elementa\",\n    \"authorName\": \"Ime avtorja\",\n    \"githubUsername\": \"GitHub uporabniško ime\",\n    \"twitterUsername\": \"Twitter uporabniško ime\",\n    \"libraryName\": \"Ime knjižnice\",\n    \"libraryDesc\": \"Opis knijžnice\",\n    \"website\": \"Spletna stran\",\n    \"placeholder\": {\n      \"authorName\": \"Vaše ime ali uporabniško ime\",\n      \"libraryName\": \"Ime vaše knjižnice\",\n      \"libraryDesc\": \"Opis vaše knjižnice, da bodo ljudje lažje razumeli njeno uporabo\",\n      \"githubHandle\": \"GitHub uporabniško ime (neobvezno), tako da lahko urejate knjižnico potem, ko jo pošljete v pregled\",\n      \"twitterHandle\": \"Twitter uporabniško ime (neobvezno), tako da vemo, koga omeniti pri promociji prek Twitterja\",\n      \"website\": \"Povezava na vašo osebno spletno stran ali drugam (neobvezno)\"\n    },\n    \"errors\": {\n      \"required\": \"Obvezno\",\n      \"website\": \"Vnesite veljaven URL\"\n    },\n    \"noteDescription\": \"Predložite svojo knjižnico, da bo vključena v <link>javno skladišče knjižnic,</link>da jih drugi lahko uporabljajo v svojih risbah.\",\n    \"noteGuidelines\": \"Knjižnica mora biti najprej ročno odobrena. Prosimo vas, da pred oddajanjem preberete naše <link>smernice.</link>Za komunikacijo in spreminjanje po potrebi boste potrebovali račun GitHub, vendar to ni obvezno.\",\n    \"noteLicense\": \"Z oddajo se strinjate, da bo knjižnica objavljena pod <link>licenco MIT, </link>kar na kratko pomeni, da jo lahko kdorkoli uporablja brez omejitev.\",\n    \"noteItems\": \"Vsak element knjižnice mora imeti svoje ime, tako da ga je mogoče filtrirati. Vključeni bodo naslednji elementi knjižnice:\",\n    \"atleastOneLibItem\": \"Za začetek izberite vsaj en element knjižnice\",\n    \"republishWarning\": \"Opomba: nekateri izbrani predmeti so označeni kot že objavljeni/oddani. Elemente lahko znova oddate samo, ko posodabljate obstoječo knjižnico ali oddajo.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Knjižnica oddana\",\n    \"content\": \"{{authorName}}, hvala. Vaša knjižnica je bila poslana v pregled. Stanje lahko spremljate<link>tukaj</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Ponastavi knjižnico\",\n    \"removeItemsFromLib\": \"Odstran izbrane elemente iz knjižnice\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Izvozi sliko\",\n    \"label\": {\n      \"withBackground\": \"Ozadje\",\n      \"onlySelected\": \"Samo izbor\",\n      \"darkMode\": \"Temni način\",\n      \"embedScene\": \"Vdelaj sceno\",\n      \"scale\": \"Povečava\",\n      \"padding\": \"Odmik\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"Podatki o sceni bodo shranjeni v izvoženo datoteko PNG/SVG, tako da bo sceno mogoče obnoviti iz nje.\\nTo bo povečalo velikost izvožene datoteke.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Izvozi v PNG\",\n      \"exportToSvg\": \"Izvozi v SVG\",\n      \"copyPngToClipboard\": \"Kopiraj PNG v odložišče\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Kopiraj v odložišče\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Vaše risbe so šifrirane od konca do konca, tako da jih strežniki Excalidraw nikoli ne bodo videli.\",\n    \"link\": \"Blog objava o šifriranju od konca do konca v Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Kot\",\n    \"element\": \"Element\",\n    \"elements\": \"Elementi\",\n    \"height\": \"Višina\",\n    \"scene\": \"Scena\",\n    \"selected\": \"Izbrano\",\n    \"storage\": \"Shramba\",\n    \"title\": \"Statistika za napredne uporabnike\",\n    \"total\": \"Skupaj\",\n    \"version\": \"Različica\",\n    \"versionCopy\": \"Kliknite za kopiranje\",\n    \"versionNotAvailable\": \"Različica ni na voljo\",\n    \"width\": \"Širina\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Dodano v knjižnico\",\n    \"copyStyles\": \"Slogi kopirani.\",\n    \"copyToClipboard\": \"Kopirano v odložišče.\",\n    \"copyToClipboardAsPng\": \"Kopirano v odložišče kot PNG ({{exportSelection}}, {{exportColorScheme}})\",\n    \"fileSaved\": \"Datoteka shranjena.\",\n    \"fileSavedToFilename\": \"Shranjeno v {filename}\",\n    \"canvas\": \"platno\",\n    \"selection\": \"izbor\",\n    \"pasteAsSingleElement\": \"Uporabite {{shortcut}}, da prilepite kot en element,\\n ali prilepite v obstoječ urejevalnik besedil\",\n    \"unableToEmbed\": \"Vdelava tega URL-ja trenutno ni dovoljena. Ustvarite vprašanje na GitHub-u in prosite za vmestitev URL-ja na seznam dovoljenih\",\n    \"unrecognizedLinkFormat\": \"Povezava, ki ste jo vdelali, se ne ujema s pričakovano obliko. Poskusite prilepiti niz za vdelavo, ki ste ga prejeli na izvorni strani\"\n  },\n  \"colors\": {\n    \"transparent\": \"Prosojno\",\n    \"black\": \"Črna\",\n    \"white\": \"Bela\",\n    \"red\": \"Rdeča\",\n    \"pink\": \"Roza\",\n    \"grape\": \"Grozdje\",\n    \"violet\": \"Vijolična\",\n    \"gray\": \"Siva\",\n    \"blue\": \"Modra\",\n    \"cyan\": \"Cijan\",\n    \"teal\": \"Turkizna\",\n    \"green\": \"Zelena\",\n    \"yellow\": \"Rumena\",\n    \"orange\": \"Oranžna\",\n    \"bronze\": \"Bronasta\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Vsi vaši podatki so shranjeni lokalno v vašem brskalniku.\",\n      \"center_heading_plus\": \"Ste namesto tega želeli odpreti Excalidraw+?\",\n      \"menuHint\": \"Izvoz, nastavitve, jeziki, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Izvoz, nastavitve in več ...\",\n      \"center_heading\": \"Diagrami. Enostavno.\",\n      \"toolbarHint\": \"Izberi orodje in začni z risanjem!\",\n      \"helpHint\": \"Bližnjice in pomoč\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Najpogosteje uporabljene barve po meri\",\n    \"colors\": \"Barve\",\n    \"shades\": \"Odtenki\",\n    \"hexCode\": \"Hex koda\",\n    \"noShades\": \"Odtenki za to barvo niso na voljo\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Izvozi kot sliko\",\n        \"button\": \"Izvozi kot sliko\",\n        \"description\": \"Izvozite podatke scene kot sliko, iz katere jo lahko pozneje uvozite.\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Shrani na disk\",\n        \"button\": \"Shrani na disk\",\n        \"description\": \"Izvozite podatke scene v datoteko, iz katere jo lahko pozneje uvozite.\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Izvozi v Excalidraw+\",\n        \"description\": \"Shrani sceno v svoj delovni prostor Excalidraw+.\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Naloži iz datoteke\",\n        \"button\": \"Naloži iz datoteke\",\n        \"description\": \"Nalaganje iz datoteke bo <bold>prepisalo vašo obstoječo vsebino</bold>.<br></br>Svojo risbo lahko najprej varnostno kopirate z eno od spodnjih možnosti.\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Naloži iz povezave\",\n        \"button\": \"Zamenjaj mojo vsebino\",\n        \"description\": \"Nalaganje zunanje risbe bo <bold>prepisalo vašo obstoječo vsebino</bold>.<br></br>Svojo risbo lahko najprej varnostno kopirate z eno od spodnjih možnosti.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"Mermaid v Excalidraw\",\n    \"button\": \"Vstavi\",\n    \"description\": \"Trenutno so podprti samo <flowchartLink>diagrami poteka</flowchartLink>, <sequenceLink>diagrami zaporedij</sequenceLink> in <classLink>Razredni diagrami</classLink>. Druge vrste bodo upodobljene kot slike v Excalidraw.\",\n    \"syntax\": \"Sintaksa Mermaid\",\n    \"preview\": \"Predogled\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}