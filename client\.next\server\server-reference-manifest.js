self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"7f59b66d8a290199ea29179bca6dae3b4b4a16b454\": {\n      \"workers\": {\n        \"app/editor/[roomId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f59b66d8a290199ea29179bca6dae3b4b4a16b454%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227fcbf338e0eabed52b3de56621b23e86b71bbccc4a%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fe3a6bbad02aa5d1ffcff38f7b7802f4ca03b4854%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f59b66d8a290199ea29179bca6dae3b4b4a16b454%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227fcbf338e0eabed52b3de56621b23e86b71bbccc4a%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fe3a6bbad02aa5d1ffcff38f7b7802f4ca03b4854%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/editor/[roomId]/page\": \"rsc\",\n        \"app/page\": \"rsc\"\n      }\n    },\n    \"7fcbf338e0eabed52b3de56621b23e86b71bbccc4a\": {\n      \"workers\": {\n        \"app/editor/[roomId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f59b66d8a290199ea29179bca6dae3b4b4a16b454%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227fcbf338e0eabed52b3de56621b23e86b71bbccc4a%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fe3a6bbad02aa5d1ffcff38f7b7802f4ca03b4854%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f59b66d8a290199ea29179bca6dae3b4b4a16b454%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227fcbf338e0eabed52b3de56621b23e86b71bbccc4a%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fe3a6bbad02aa5d1ffcff38f7b7802f4ca03b4854%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/editor/[roomId]/page\": \"rsc\",\n        \"app/page\": \"rsc\"\n      }\n    },\n    \"7fe3a6bbad02aa5d1ffcff38f7b7802f4ca03b4854\": {\n      \"workers\": {\n        \"app/editor/[roomId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f59b66d8a290199ea29179bca6dae3b4b4a16b454%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227fcbf338e0eabed52b3de56621b23e86b71bbccc4a%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fe3a6bbad02aa5d1ffcff38f7b7802f4ca03b4854%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f59b66d8a290199ea29179bca6dae3b4b4a16b454%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227fcbf338e0eabed52b3de56621b23e86b71bbccc4a%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fe3a6bbad02aa5d1ffcff38f7b7802f4ca03b4854%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/editor/[roomId]/page\": \"rsc\",\n        \"app/page\": \"rsc\"\n      }\n    },\n    \"7f9ed16b4fff2e48eeac48cc53af5424b5b0291d44\": {\n      \"workers\": {\n        \"app/editor/[roomId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%7B%22id%22%3A%227f9ed16b4fff2e48eeac48cc53af5424b5b0291d44%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CProject%5C%5Crealcode%5C%5Cclient%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%7B%22id%22%3A%227f9ed16b4fff2e48eeac48cc53af5424b5b0291d44%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/editor/[roomId]/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"