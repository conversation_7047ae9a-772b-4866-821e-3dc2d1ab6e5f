"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/TeacherControlPanel.tsx":
/*!************************************************!*\
  !*** ./src/components/TeacherControlPanel.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherControlPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit3,FiEye,FiLock,FiUnlock,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/EditPermissionContext */ \"(app-pages-browser)/./src/context/EditPermissionContext.tsx\");\n/* harmony import */ var _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSocketService */ \"(app-pages-browser)/./src/hooks/useSocketService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TeacherControlPanel(param) {\n    let { className = '' } = param;\n    _s();\n    const { isTeacher, students, grantEditPermission, revokeEditPermission } = (0,_context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__.useEditPermission)();\n    const { isReady, isConnected } = (0,_hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__.useSocketService)();\n    const [pending, setPending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Debug logging for student list updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherControlPanel.useEffect\": ()=>{\n            console.log('🎯 [TEACHER_PANEL] State update:', {\n                isTeacher,\n                studentCount: students.length,\n                isReady,\n                isConnected,\n                students: students.map({\n                    \"TeacherControlPanel.useEffect\": (s)=>({\n                            username: s.username,\n                            socketId: s.socketId,\n                            canEdit: s.canEdit\n                        })\n                }[\"TeacherControlPanel.useEffect\"])\n            });\n        }\n    }[\"TeacherControlPanel.useEffect\"], [\n        students,\n        isTeacher,\n        isReady,\n        isConnected\n    ]);\n    // Only show panel for teachers\n    if (!isTeacher) {\n        console.log('🎯 [TEACHER_PANEL] Not showing panel (not teacher)');\n        return null;\n    }\n    const handleToggle = (student)=>{\n        setPending(student.socketId);\n        if (student.canEdit) {\n            revokeEditPermission(student.socketId);\n        } else {\n            grantEditPermission(student.socketId);\n        }\n        setTimeout(()=>setPending(null), 1000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-full max-w-md bg-white border rounded-lg shadow-lg p-4 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiUsers, {\n                        className: \"text-blue-500 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-bold\",\n                        children: \"Student Access Panel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            !isReady || !isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-yellow-600 text-sm mb-2\",\n                children: \"Waiting for connection...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    students.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-400 flex flex-col items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiUsers, {\n                                className: \"w-8 h-8 mb-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"No students in the room yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this),\n                    students.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between bg-gray-50 rounded px-3 py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: student.username\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this),\n                                        student.canEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded bg-green-100 text-green-700 text-xs ml-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiEdit3, {\n                                                    className: \"w-3 h-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" Edit\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded bg-gray-100 text-gray-600 text-xs ml-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiEye, {\n                                                    className: \"w-3 h-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" View\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center px-3 py-1 rounded text-xs font-medium transition-all \".concat(student.canEdit ? 'bg-green-100 text-green-700 hover:bg-green-200' : 'bg-gray-100 text-gray-600 hover:bg-gray-200', \" \").concat(pending === student.socketId || !isReady || !isConnected ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                                    onClick: ()=>handleToggle(student),\n                                    disabled: pending === student.socketId || !isReady || !isConnected,\n                                    title: student.canEdit ? 'Revoke edit access' : 'Grant edit access',\n                                    children: student.canEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiUnlock, {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 36\n                                            }, this),\n                                            \" Revoke\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiLock, {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 88\n                                            }, this),\n                                            \" Grant\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, student.socketId, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_s(TeacherControlPanel, \"oCMkkMEC9UU4yBXi0XK0+5hcNko=\", false, function() {\n    return [\n        _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__.useEditPermission,\n        _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__.useSocketService\n    ];\n});\n_c = TeacherControlPanel;\nvar _c;\n$RefreshReg$(_c, \"TeacherControlPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TeacherControlPanel.tsx\n"));

/***/ })

});