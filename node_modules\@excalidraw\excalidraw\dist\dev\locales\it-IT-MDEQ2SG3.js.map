{"version": 3, "sources": ["../../../locales/it-IT.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Incolla\",\n    \"pasteAsPlaintext\": \"Incolla come testo normale\",\n    \"pasteCharts\": \"Incolla grafici\",\n    \"selectAll\": \"Seleziona tutto\",\n    \"multiSelect\": \"Aggiungi elemento alla selezione\",\n    \"moveCanvas\": \"Sposta tela\",\n    \"cut\": \"Taglia\",\n    \"copy\": \"Copia\",\n    \"copyAsPng\": \"Copia negli appunti come PNG\",\n    \"copyAsSvg\": \"Copia negli appunti come SVG\",\n    \"copyText\": \"Copia negli appunti come testo\",\n    \"copySource\": \"Copia sorgente negli appunti\",\n    \"convertToCode\": \"Converti in codice\",\n    \"bringForward\": \"Porta avanti\",\n    \"sendToBack\": \"Manda in fondo\",\n    \"bringToFront\": \"Porta in cima\",\n    \"sendBackward\": \"Manda dietro\",\n    \"delete\": \"Elimina\",\n    \"copyStyles\": \"Copia stili\",\n    \"pasteStyles\": \"Incolla stili\",\n    \"stroke\": \"Tratto\",\n    \"background\": \"Sfondo\",\n    \"fill\": \"Riempimento\",\n    \"strokeWidth\": \"Spessore del tratto\",\n    \"strokeStyle\": \"Stile del tratto\",\n    \"strokeStyle_solid\": \"Pieno\",\n    \"strokeStyle_dashed\": \"Tratteggiato\",\n    \"strokeStyle_dotted\": \"Punteggiato\",\n    \"sloppiness\": \"Imprecisione\",\n    \"opacity\": \"Opacità\",\n    \"textAlign\": \"Allineamento del testo\",\n    \"edges\": \"Bordi\",\n    \"sharp\": \"Acuto\",\n    \"round\": \"Rotondo\",\n    \"arrowheads\": \"Punta della freccia\",\n    \"arrowhead_none\": \"Nessuno\",\n    \"arrowhead_arrow\": \"Freccia\",\n    \"arrowhead_bar\": \"Barra\",\n    \"arrowhead_circle\": \"Cerchio\",\n    \"arrowhead_circle_outline\": \"Cerchio (contorno)\",\n    \"arrowhead_triangle\": \"Triangolo\",\n    \"arrowhead_triangle_outline\": \"Triangolo (contorno)\",\n    \"arrowhead_diamond\": \"Diamante\",\n    \"arrowhead_diamond_outline\": \"Diamante (contorno)\",\n    \"fontSize\": \"Dimensione carattere\",\n    \"fontFamily\": \"Carattere\",\n    \"addWatermark\": \"Aggiungi \\\"Creato con Excalidraw\\\"\",\n    \"handDrawn\": \"A mano libera\",\n    \"normal\": \"Normale\",\n    \"code\": \"Codice\",\n    \"small\": \"Piccolo\",\n    \"medium\": \"Medio\",\n    \"large\": \"Grande\",\n    \"veryLarge\": \"Molto grande\",\n    \"solid\": \"Pieno\",\n    \"hachure\": \"Tratteggio obliquo\",\n    \"zigzag\": \"Zig zag\",\n    \"crossHatch\": \"Tratteggio incrociato\",\n    \"thin\": \"Sottile\",\n    \"bold\": \"Grassetto\",\n    \"left\": \"Sinistra\",\n    \"center\": \"Centro\",\n    \"right\": \"Destra\",\n    \"extraBold\": \"Extra Grassetto\",\n    \"architect\": \"Architetto\",\n    \"artist\": \"Artista\",\n    \"cartoonist\": \"Fumettista\",\n    \"fileTitle\": \"Nome del file\",\n    \"colorPicker\": \"Selettore colore\",\n    \"canvasColors\": \"Usato su tela\",\n    \"canvasBackground\": \"Sfondo tela\",\n    \"drawingCanvas\": \"Area di disegno\",\n    \"layers\": \"Livelli\",\n    \"actions\": \"Azioni\",\n    \"language\": \"Lingua\",\n    \"liveCollaboration\": \"Collaborazione dal vivo...\",\n    \"duplicateSelection\": \"Duplica\",\n    \"untitled\": \"Senza titolo\",\n    \"name\": \"Nome\",\n    \"yourName\": \"Il vostro nome\",\n    \"madeWithExcalidraw\": \"Creato con Excalidraw\",\n    \"group\": \"Crea gruppo da selezione\",\n    \"ungroup\": \"Dividi gruppo da selezione\",\n    \"collaborators\": \"Collaboratori\",\n    \"showGrid\": \"Visualizza griglia\",\n    \"addToLibrary\": \"Aggiungi alla libreria\",\n    \"removeFromLibrary\": \"Rimuovi dalla libreria\",\n    \"libraryLoadingMessage\": \"Caricamento libreria…\",\n    \"libraries\": \"Sfoglia librerie\",\n    \"loadingScene\": \"Caricamento della scena…\",\n    \"align\": \"Allinea\",\n    \"alignTop\": \"Allinea in alto\",\n    \"alignBottom\": \"Allinea in basso\",\n    \"alignLeft\": \"Allinea a sinistra\",\n    \"alignRight\": \"Allinea a destra\",\n    \"centerVertically\": \"Centra Verticalmente\",\n    \"centerHorizontally\": \"Centra orizzontalmente\",\n    \"distributeHorizontally\": \"Distribuisci orizzontalmente\",\n    \"distributeVertically\": \"Distribuisci verticalmente\",\n    \"flipHorizontal\": \"Capovolgi orizzontalmente\",\n    \"flipVertical\": \"Capovolgi verticalmente\",\n    \"viewMode\": \"Modalità visualizzazione\",\n    \"share\": \"Condividi\",\n    \"showStroke\": \"Mostra selettore colore del tratto\",\n    \"showBackground\": \"Mostra selettore colore di sfondo\",\n    \"toggleTheme\": \"Cambia tema\",\n    \"personalLib\": \"Libreria Personale\",\n    \"excalidrawLib\": \"Libreria di Excalidraw\",\n    \"decreaseFontSize\": \"Riduci dimensione dei caratteri\",\n    \"increaseFontSize\": \"Aumenta la dimensione dei caratteri\",\n    \"unbindText\": \"Scollega testo\",\n    \"bindText\": \"Associa il testo al container\",\n    \"createContainerFromText\": \"Avvolgi il testo in un container\",\n    \"link\": {\n      \"edit\": \"Modifica link\",\n      \"editEmbed\": \"Modifica collegamento e incorpora\",\n      \"create\": \"Crea link\",\n      \"createEmbed\": \"Crea collegamento e incorpora\",\n      \"label\": \"Link\",\n      \"labelEmbed\": \"Collega & incorpora\",\n      \"empty\": \"Nessun collegamento impostato\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Modifica linea\",\n      \"exit\": \"Esci dall'editor di linea\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Blocca\",\n      \"unlock\": \"Sblocca\",\n      \"lockAll\": \"Blocca tutto\",\n      \"unlockAll\": \"Sblocca tutto\"\n    },\n    \"statusPublished\": \"Pubblicato\",\n    \"sidebarLock\": \"Mantieni aperta la barra laterale\",\n    \"selectAllElementsInFrame\": \"Seleziona tutti gli elementi nel riquadro\",\n    \"removeAllElementsFromFrame\": \"Rimuovi tutti gli elementi dal riquadro\",\n    \"eyeDropper\": \"Scegli il colore della tela\",\n    \"textToDiagram\": \"Testo a diagramma\",\n    \"prompt\": \"Prompt\"\n  },\n  \"library\": {\n    \"noItems\": \"Nessun elemento ancora aggiunto...\",\n    \"hint_emptyLibrary\": \"Seleziona un elemento sulla tela per aggiungerlo qui, o installa una libreria dal repository pubblico qui sotto.\",\n    \"hint_emptyPrivateLibrary\": \"Seleziona un elemento sulla tela per aggiungerlo qui.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Svuota la tela\",\n    \"exportJSON\": \"Esporta su file\",\n    \"exportImage\": \"Esporta immagine...\",\n    \"export\": \"Salva in...\",\n    \"copyToClipboard\": \"Copia negli appunti\",\n    \"save\": \"Salva sul file corrente\",\n    \"saveAs\": \"Salva con nome\",\n    \"load\": \"Apri\",\n    \"getShareableLink\": \"Ottieni link condivisibile\",\n    \"close\": \"Chiudi\",\n    \"selectLanguage\": \"Seleziona lingua\",\n    \"scrollBackToContent\": \"Scorri indietro fino al contenuto\",\n    \"zoomIn\": \"Aumenta ingrandimento\",\n    \"zoomOut\": \"Riduci ingrandimento\",\n    \"resetZoom\": \"Ripristina ingrandimento\",\n    \"menu\": \"Menù\",\n    \"done\": \"Fatto\",\n    \"edit\": \"Modifica\",\n    \"undo\": \"Annulla\",\n    \"redo\": \"Ripeti\",\n    \"resetLibrary\": \"Ripristina libreria\",\n    \"createNewRoom\": \"Crea nuova stanza\",\n    \"fullScreen\": \"Schermo intero\",\n    \"darkMode\": \"Tema scuro\",\n    \"lightMode\": \"Tema chiaro\",\n    \"zenMode\": \"Modalità Zen\",\n    \"objectsSnapMode\": \"Aggancia agli oggetti\",\n    \"exitZenMode\": \"Uscire dalla modalità zen\",\n    \"cancel\": \"Annulla\",\n    \"clear\": \"Cancella\",\n    \"remove\": \"Rimuovi\",\n    \"embed\": \"Attiva/disattiva incorporamento\",\n    \"publishLibrary\": \"Pubblica\",\n    \"submit\": \"Invia\",\n    \"confirm\": \"Conferma\",\n    \"embeddableInteractionButton\": \"Clicca per interagire\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Questa azione cancellerà l'intera tela. Sei sicuro?\",\n    \"couldNotCreateShareableLink\": \"Non riesco a creare un link condivisibile.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Impossibile creare il link condivisibile: la scena è troppo grande\",\n    \"couldNotLoadInvalidFile\": \"Impossibile caricare un file no valido\",\n    \"importBackendFailed\": \"Importazione dal server fallita.\",\n    \"cannotExportEmptyCanvas\": \"Non è possibile esportare una tela vuota.\",\n    \"couldNotCopyToClipboard\": \"Impossibile copiare negli appunti.\",\n    \"decryptFailed\": \"Impossibile decriptare i dati.\",\n    \"uploadedSecurly\": \"L'upload è stato protetto con la crittografia end-to-end, il che significa che il server Excalidraw e terze parti non possono leggere il contenuto.\",\n    \"loadSceneOverridePrompt\": \"Se carichi questo disegno esterno, sostituirà quello che hai. Vuoi continuare?\",\n    \"collabStopOverridePrompt\": \"Interrompere la sessione sovrascriverà il precedente disegno memorizzato localmente. Sei sicuro?\\n\\n(Se vuoi mantenere il tuo disegno locale, chiudi semplicemente la scheda del browser.)\",\n    \"errorAddingToLibrary\": \"Impossibile aggiungere l'elemento alla libreria\",\n    \"errorRemovingFromLibrary\": \"Impossibile rimuovere l'elemento dalla libreria\",\n    \"confirmAddLibrary\": \"Questo aggiungerà {{numShapes}} forma(e) alla tua libreria. Sei sicuro?\",\n    \"imageDoesNotContainScene\": \"Questa immagine pare non contenere alcuna scena. Avevi incluso la scena durante l'esportazione?\",\n    \"cannotRestoreFromImage\": \"Impossibile ripristinare la scena da questo file immagine\",\n    \"invalidSceneUrl\": \"Impossibile importare la scena dall'URL fornito. Potrebbe essere malformato o non contenere dati JSON Excalidraw validi.\",\n    \"resetLibrary\": \"Questa azione cancellerà l'intera libreria. Sei sicuro?\",\n    \"removeItemsFromsLibrary\": \"Eliminare {{count}} elementi dalla libreria?\",\n    \"invalidEncryptionKey\": \"La chiave di cifratura deve essere composta da 22 caratteri. La collaborazione live è disabilitata.\",\n    \"collabOfflineWarning\": \"Nessuna connessione internet disponibile.\\nLe tue modifiche non verranno salvate!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Tipo di file non supportato.\",\n    \"imageInsertError\": \"Non è stato possibile inserire l'immagine. Riprova più tardi...\",\n    \"fileTooBig\": \"Il file è troppo grande. La dimensione massima consentita è {{maxSize}}.\",\n    \"svgImageInsertError\": \"Impossibile inserire l'immagine SVG. Il markup SVG non sembra corretto.\",\n    \"failedToFetchImage\": \"Impossibile recuperare l'immagine.\",\n    \"invalidSVGString\": \"SVG non valido.\",\n    \"cannotResolveCollabServer\": \"Impossibile connettersi al server di collab. Ricarica la pagina e riprova.\",\n    \"importLibraryError\": \"Impossibile caricare la libreria\",\n    \"collabSaveFailed\": \"Impossibile salvare nel database di backend. Se i problemi persistono, dovresti salvare il tuo file localmente per assicurarti di non perdere il tuo lavoro.\",\n    \"collabSaveFailed_sizeExceeded\": \"Impossibile salvare nel database di backend, la tela sembra essere troppo grande. Dovresti salvare il file localmente per assicurarti di non perdere il tuo lavoro.\",\n    \"imageToolNotSupported\": \"Le immagini sono disabilitate.\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Sembra che tu stia utilizzando il browser Brave con l'impostazione <bold>Blocco aggressivo delle impronte digitali</bold> abilitata.\",\n      \"line2\": \"Ciò potrebbe causare la rottura degli <bold>Elementi di testo</bold> nei tuoi disegni.\",\n      \"line3\": \"Consigliamo vivamente di disabilitare questa impostazione. Puoi seguire <link>questi passaggi</link> su come farlo.\",\n      \"line4\": \"Se la disattivazione di questa impostazione non risolve la visualizzazione degli elementi di testo, apri un <issueLink>problema</issueLink> sul nostro GitHub o scrivici su <discordLink>Discord</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"Gli elementi incorporabili non possono essere aggiunti alla libreria.\",\n      \"iframe\": \"Gli elementi IFrame non possono essere aggiunti alla libreria.\",\n      \"image\": \"Il supporto per l'aggiunta d'immagini alla libreria verrà aggiunto a breve!\"\n    },\n    \"asyncPasteFailedOnRead\": \"Impossibile incollare (non è possibile leggere dagli appunti di sistema).\",\n    \"asyncPasteFailedOnParse\": \"Impossibile incollare.\",\n    \"copyToSystemClipboardFailed\": \"Impossibile copiare negli appunti.\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Selezione\",\n    \"image\": \"Inserisci immagine\",\n    \"rectangle\": \"Rettangolo\",\n    \"diamond\": \"Rombo\",\n    \"ellipse\": \"Ellisse\",\n    \"arrow\": \"Freccia\",\n    \"line\": \"Linea\",\n    \"freedraw\": \"Disegno\",\n    \"text\": \"Testo\",\n    \"library\": \"Libreria\",\n    \"lock\": \"Mantieni lo strumento selezionato attivo dopo aver disegnato\",\n    \"penMode\": \"Modalità penna - previene il tocco\",\n    \"link\": \"Aggiungi/ aggiorna il link per una forma selezionata\",\n    \"eraser\": \"Gomma\",\n    \"frame\": \"Strumento riquadro\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"Incorporamento Web\",\n    \"laser\": \"Puntatore laser\",\n    \"hand\": \"Mano (strumento di panoramica)\",\n    \"extraTools\": \"Altri strumenti\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"Impostazioni di IA\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Azioni sulla Tela\",\n    \"selectedShapeActions\": \"Impostazioni della forma selezionata\",\n    \"shapes\": \"Forme\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Per spostare la tela, tieni premuta la rotellina del mouse o la barra spaziatrice mentre trascini oppure usa lo strumento mano\",\n    \"linearElement\": \"Clicca per iniziare una linea in più punti, trascina per singola linea\",\n    \"freeDraw\": \"Clicca e trascina, rilascia quando avrai finito\",\n    \"text\": \"Suggerimento: puoi anche aggiungere del testo facendo doppio clic ovunque con lo strumento di selezione\",\n    \"embeddable\": \"Fare click e trascina per creare un incorporamento web\",\n    \"text_selected\": \"Fai doppio click o premi INVIO per modificare il testo\",\n    \"text_editing\": \"Premi ESC o CtrlOCmd+INVIO per completare le modifiche\",\n    \"linearElementMulti\": \"Clicca sull'ultimo punto o premi Esc o Invio per finire\",\n    \"lockAngle\": \"Puoi limitare l'angolo tenendo premuto SHIFT\",\n    \"resize\": \"Per vincolare le proporzioni, tieni premuto MAIUSC durante il ridimensionamento;\\nper ridimensionare dal centro, tieni premuto ALT\",\n    \"resizeImage\": \"Puoi ridimensionare liberamente tenendo premuto SHIFT,\\ntieni premuto ALT per ridimensionare dal centro\",\n    \"rotate\": \"Puoi mantenere gli angoli tenendo premuto SHIFT durante la rotazione\",\n    \"lineEditor_info\": \"Tieni premuto Ctrl o Cmd e doppio clic oppure premi Ctrl o Cmd + Invio per modificare i punti\",\n    \"lineEditor_pointSelected\": \"Premi Elimina per rimuovere il punto(i),\\nCtrlOCmd+D per duplicare o trascinare per spostare\",\n    \"lineEditor_nothingSelected\": \"Seleziona un punto da modificare (tieni premuto MAIUSC per selezionare più punti),\\noppure tieni premuto Alt e fai clic per aggiungere nuovi punti\",\n    \"placeImage\": \"Fai click per posizionare l'immagine, o click e trascina per impostarne la dimensione manualmente\",\n    \"publishLibrary\": \"Pubblica la tua libreria\",\n    \"bindTextToElement\": \"Premi invio per aggiungere il testo\",\n    \"deepBoxSelect\": \"Tieni premuto CtrlOCmd per selezionare in profondità e per impedire il trascinamento\",\n    \"eraserRevert\": \"Tieni premuto Alt per ripristinare gli elementi contrassegnati per l'eliminazione\",\n    \"firefox_clipboard_write\": \"Questa funzione può essere abilitata impostando il flag \\\"dom.events.asyncClipboard.clipboardItem\\\" su \\\"true\\\". Per modificare i flag del browser in Firefox, visitare la pagina \\\"about:config\\\".\",\n    \"disableSnapping\": \"Tieni premuto Ctrl o Cmd per disabilitare lo snap\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Impossibile visualizzare l'anteprima\",\n    \"canvasTooBig\": \"La tela potrebbe essere troppo grande.\",\n    \"canvasTooBigTip\": \"Suggerimento: prova a spostare gli elementi più lontani più vicini tra loro.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Si è verificato un errore. Provare <button>ricaricando la pagina.</button>\",\n    \"clearCanvasMessage\": \"Se ricaricare non funziona, prova <button>pulire la tela.</button>\",\n    \"clearCanvasCaveat\": \" Questo risulterà nella perdita del lavoro \",\n    \"trackedToSentry\": \"L'errore con identificativo {{eventId}} è stato tracciato nel nostro sistema.\",\n    \"openIssueMessage\": \"Siamo stati molto cauti nel non includere informazioni della scena nell'errore. Se la tua scena non è privata, ti preghiamo di considerare la sua inclusione nel nostro <button>bug tracker.</button> Per favore includi le informazioni riportate qui sotto copiandole e incollandole nella issue di GitHub.\",\n    \"sceneContent\": \"Contenuto della scena:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Puoi invitare persone nella tua scena attuale per collaborare con te.\",\n    \"desc_privacy\": \"Non preoccuparti, la sessione utilizza la crittografia end-to-end, quindi qualsiasi cosa disegni rimarrà privata. Nemmeno il nostro server sarà in grado di vedere cosa hai creato.\",\n    \"button_startSession\": \"Avvia sessione\",\n    \"button_stopSession\": \"Termina sessione\",\n    \"desc_inProgressIntro\": \"La sessione di collaborazione è attualmente in corso.\",\n    \"desc_shareLink\": \"Condividi questo link con chiunque desideri collaborare:\",\n    \"desc_exitSession\": \"Interrompere la sessione scollegherà la tua stanza ma potrai continuare a lavorare con la scena, localmente. Tieni presente che questo non influirà sulle altre persone, e che saranno ancora in grado di collaborare alla loro versione.\",\n    \"shareTitle\": \"Partecipa a una sessione di collaborazione live su Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Errore\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Salva su disco\",\n    \"disk_details\": \"Esporta i dati della scena su file, dal quale potrai importare in seguito.\",\n    \"disk_button\": \"Salva su file\",\n    \"link_title\": \"Link condivisibile\",\n    \"link_details\": \"Esporta come link di sola lettura.\",\n    \"link_button\": \"Esporta come Link\",\n    \"excalidrawplus_description\": \"Salva la scena nel tuo spazio di lavoro Excalidraw+.\",\n    \"excalidrawplus_button\": \"Esporta\",\n    \"excalidrawplus_exportError\": \"Non è stato possibile esportare su Excalidraw+ al questo momento...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Leggi il nostro blog\",\n    \"click\": \"click\",\n    \"deepSelect\": \"Selezione profonda\",\n    \"deepBoxSelect\": \"Seleziona in profondità all'interno della casella e previene il trascinamento\",\n    \"curvedArrow\": \"Freccia curva\",\n    \"curvedLine\": \"Linea curva\",\n    \"documentation\": \"Documentazione\",\n    \"doubleClick\": \"doppio-click\",\n    \"drag\": \"trascina\",\n    \"editor\": \"Editor\",\n    \"editLineArrowPoints\": \"Modifica punti linea/freccia\",\n    \"editText\": \"Modifica testo / aggiungi etichetta\",\n    \"github\": \"Trovato un problema? Segnalalo\",\n    \"howto\": \"Segui le nostre guide\",\n    \"or\": \"oppure\",\n    \"preventBinding\": \"Impedisci legame della freccia\",\n    \"tools\": \"Stumenti\",\n    \"shortcuts\": \"Scorciatoie da tastiera\",\n    \"textFinish\": \"Completa la modifica (editor di testo)\",\n    \"textNewLine\": \"Aggiungi nuova riga (editor di testo)\",\n    \"title\": \"Guida\",\n    \"view\": \"Vista\",\n    \"zoomToFit\": \"Adatta zoom per mostrare tutti gli elementi\",\n    \"zoomToSelection\": \"Zoom alla selezione\",\n    \"toggleElementLock\": \"Blocca/sblocca selezione\",\n    \"movePageUpDown\": \"Sposta la pagina su/giù\",\n    \"movePageLeftRight\": \"Sposta la pagina a sinistra/destra\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Svuota la tela\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Pubblica la libreria\",\n    \"itemName\": \"Nome dell'elemento\",\n    \"authorName\": \"Nome dell'autore\",\n    \"githubUsername\": \"Nome utente di GitHub\",\n    \"twitterUsername\": \"Nome utente di Twitter\",\n    \"libraryName\": \"Nome della libreria\",\n    \"libraryDesc\": \"Descrizione della libreria\",\n    \"website\": \"Sito Web\",\n    \"placeholder\": {\n      \"authorName\": \"Il tuo nome o nome utente\",\n      \"libraryName\": \"Nome della tua libreria\",\n      \"libraryDesc\": \"Descrizione della tua libreria per aiutare le persone a comprenderne lo scopo\",\n      \"githubHandle\": \"Handle di GitHub (opzionale), così che tu possa modificare la libreria una volta inviata per la revisione\",\n      \"twitterHandle\": \"Nome utente di Twitter (opzionale), così che sappiamo chi accreditare promuovendo su Twitter\",\n      \"website\": \"Link al tuo sito web personale o altro (opzionale)\"\n    },\n    \"errors\": {\n      \"required\": \"Obbligatorio\",\n      \"website\": \"Inserisci un URL valido\"\n    },\n    \"noteDescription\": \"Invia la tua libreria da includere nella <link>repository della libreria pubblica</link>perché sia usata da altri nei loro disegni.\",\n    \"noteGuidelines\": \"La libreria dev'esser prima approvata manualmente. Sei pregato di leggere le <link>linee guida</link> prima di inviarla. Necessiterai di un profilo di GitHub per comunicare ed effettuare modifiche se richiesto, ma non è strettamente necessario.\",\n    \"noteLicense\": \"Inviando, acconsenti che la libreria sarà pubblicata sotto la <link>Licenza MIT, </link>che in breve significa che chiunque possa usarla senza restrizioni.\",\n    \"noteItems\": \"Ogni elemento della libreria deve avere il proprio nome, così che sia filtrabile. Gli elementi della seguente libreria saranno inclusi:\",\n    \"atleastOneLibItem\": \"Sei pregato di selezionare almeno un elemento della libreria per iniziare\",\n    \"republishWarning\": \"Nota: alcuni degli elementi selezionati sono contrassegnati come già pubblicati/presentati. È necessario reinviare gli elementi solo quando si aggiorna una libreria o una presentazione esistente.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Libreria inviata\",\n    \"content\": \"Grazie {{authorName}}. La tua libreria è stata inviata per la revisione. Puoi monitorarne lo stato<link>qui</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Ripristina la libreria\",\n    \"removeItemsFromLib\": \"Rimuovi gli elementi selezionati dalla libreria\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Esporta immagine\",\n    \"label\": {\n      \"withBackground\": \"Sfondo\",\n      \"onlySelected\": \"Solo selezionato\",\n      \"darkMode\": \"Tema scuro\",\n      \"embedScene\": \"Includi scena\",\n      \"scale\": \"Scala\",\n      \"padding\": \"Rientro\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"I dati della scena saranno salvati nel file PNG/SVG esportato in modo che la scena possa essere ripristinata da esso.\\nQuesto aumenterà la dimensione del file esportato.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Esporta come PNG\",\n      \"exportToSvg\": \"Esporta come SVG\",\n      \"copyPngToClipboard\": \"Copia PNG negli appunti\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Copia negli appunti\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"I tuoi disegni sono crittografati end-to-end in modo che i server di Excalidraw non li possano mai vedere.\",\n    \"link\": \"Articolo del blog sulla crittografia end-to-end di Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Angolo\",\n    \"element\": \"Elemento\",\n    \"elements\": \"Elementi\",\n    \"height\": \"Altezza\",\n    \"scene\": \"Scena\",\n    \"selected\": \"Selezionato\",\n    \"storage\": \"Memoria\",\n    \"title\": \"Statistiche per nerd\",\n    \"total\": \"Totale\",\n    \"version\": \"Versione\",\n    \"versionCopy\": \"Clicca per copiare\",\n    \"versionNotAvailable\": \"Versione non disponibile\",\n    \"width\": \"Larghezza\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Aggiunto alla libreria\",\n    \"copyStyles\": \"Stili copiati.\",\n    \"copyToClipboard\": \"Copiato negli appunti.\",\n    \"copyToClipboardAsPng\": \"{{exportSelection}} copiato negli appunti come PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"File salvato.\",\n    \"fileSavedToFilename\": \"Salvato in {filename}\",\n    \"canvas\": \"tela\",\n    \"selection\": \"selezione\",\n    \"pasteAsSingleElement\": \"Usa {{shortcut}} per incollare come un singolo elemento,\\no incollare in un editor di testo esistente\",\n    \"unableToEmbed\": \"Incorporare questo url non è permesso. Crea una issue su GitHub per richiedere che l'url sia autorizzato\",\n    \"unrecognizedLinkFormat\": \"Il link che hai incorporato non corrisponde al formato previsto. Prova a incollare la stringa 'embed' fornita dal sito di origine\"\n  },\n  \"colors\": {\n    \"transparent\": \"Trasparente\",\n    \"black\": \"Nero\",\n    \"white\": \"Bianco\",\n    \"red\": \"Rosso\",\n    \"pink\": \"Rosa\",\n    \"grape\": \"Uva\",\n    \"violet\": \"Viola\",\n    \"gray\": \"Grigio\",\n    \"blue\": \"Blu\",\n    \"cyan\": \"Ciano\",\n    \"teal\": \"Verde acqua\",\n    \"green\": \"Verde\",\n    \"yellow\": \"Giallo\",\n    \"orange\": \"Arancio\",\n    \"bronze\": \"Bronzo\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Tutti i tuoi dati sono salvati localmente nel browser.\",\n      \"center_heading_plus\": \"Volevi invece andare su Excalidraw+?\",\n      \"menuHint\": \"Esporta, preferenze, lingue, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Esporta, preferenze, e altro...\",\n      \"center_heading\": \"Diagrammi. Fatto. Semplice.\",\n      \"toolbarHint\": \"Scegli uno strumento & Inizia a disegnare!\",\n      \"helpHint\": \"Scorciatoie & aiuto\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Colori personalizzati più utilizzati\",\n    \"colors\": \"Colori\",\n    \"shades\": \"Sfumature\",\n    \"hexCode\": \"Codice esadecimale\",\n    \"noShades\": \"Nessuna sfumatura disponibile per questo colore\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Esporta come immagine\",\n        \"button\": \"Esporta come immagine\",\n        \"description\": \"Esporta i dati della scena come immagine, che potrai importare in seguito.\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Salva su disco\",\n        \"button\": \"Salva su disco\",\n        \"description\": \"Esporta i dati della scena su file, che potrai importare in seguito.\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Esporta su Excalidraw+\",\n        \"description\": \"Salva la scena sul tuo spazio di lavoro Excalidraw+.\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Carica da file\",\n        \"button\": \"Carica da file\",\n        \"description\": \"Il caricamento da file sostituirà <bold>il contenuto esistente</bold>.<br></br>Puoi salvare il tuo disegno prima usando una delle opzioni qui sotto.\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Carica da link\",\n        \"button\": \"Sostituisci il mio contenuto\",\n        \"description\": \"Il caricamento da file sostituirà <bold>il contenuto esistente</bold>.<br></br>Puoi salvare il tuo disegno prima usando una delle opzioni qui sotto.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"Inserisci\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"Anteprima\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}