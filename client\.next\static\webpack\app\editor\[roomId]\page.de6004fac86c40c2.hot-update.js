"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/services/socketService.ts":
/*!***************************************!*\
  !*** ./src/services/socketService.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n// client/src/services/socketService.ts\n\n\n// Fix: Ensure SOCKET_URL is defined before use (move it above all uses)\nconst SOCKET_URL = \"http://localhost:5001\" || 0;\n// Create a singleton socket instance\nclass SocketService {\n    static getInstance() {\n        if (!SocketService.instance) {\n            SocketService.instance = new SocketService();\n        }\n        return SocketService.instance;\n    }\n    initSocket() {\n        try {\n            console.log('Initializing socket connection to', SOCKET_URL);\n            // Remove listeners from old socket if it exists\n            if (this.socket) {\n                this.socket.removeAllListeners();\n                this.socket.disconnect();\n            }\n            // Use polling first for better compatibility, then upgrade to websocket\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                reconnectionAttempts: 10,\n                reconnectionDelay: 1000,\n                timeout: 10000,\n                transports: [\n                    'polling',\n                    'websocket'\n                ],\n                autoConnect: true,\n                forceNew: true,\n                upgrade: true\n            });\n            console.log('Socket instance created successfully');\n            this.setupEventListeners();\n        } catch (error) {\n            console.error('Error initializing socket:', error);\n        }\n    }\n    setupEventListeners() {\n        if (!this.socket) return;\n        // Clear any existing listeners to prevent duplicates\n        this.socket.removeAllListeners();\n        // Connection events\n        this.socket.on('connect', ()=>{\n            var _this_socket;\n            console.log('Socket connected successfully:', (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.id);\n            this.connected = true;\n            this.emitEvent('connect', null);\n        });\n        this.socket.on('disconnect', (reason)=>{\n            console.log(\"Socket disconnected. Reason: \".concat(reason));\n            this.connected = false;\n            this.emitEvent('disconnect', reason);\n            if (reason !== 'io client disconnect') {\n                console.log('Reconnection attempt will start in 2 seconds...');\n                setTimeout(()=>{\n                    console.log('Attempting to reconnect...');\n                    this.connect();\n                }, 2000);\n            }\n        });\n        // Transport and reconnection events\n        if (this.socket.io) {\n            this.socket.io.on('error', (error)=>{\n                console.error('Transport error:', error);\n            });\n            this.socket.io.on('reconnect_attempt', (attempt)=>{\n                console.log(\"Reconnection attempt \".concat(attempt));\n            });\n            this.socket.io.on('reconnect', (attemptNumber)=>{\n                this.connected = true;\n                this.emitEvent('connect', null);\n            });\n            this.socket.io.on('reconnect_error', (error)=>{\n                console.error('Reconnection error:', error);\n            });\n            this.socket.io.on('reconnect_failed', ()=>{\n                this.emitEvent('error', 'Failed to reconnect after multiple attempts');\n                // Try a different approach after all reconnection attempts fail\n                setTimeout(()=>{\n                    this.initSocket();\n                }, 3000);\n            });\n        }\n        this.socket.on('connect_error', (error)=>{\n            var _error_message, _error_message1, _error_message2;\n            console.error('Socket connection error:', (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error');\n            this.connected = false;\n            // Handle specific error types\n            if (error === null || error === void 0 ? void 0 : (_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('xhr poll error')) {\n                console.log('XHR polling error detected - will try alternative connection method');\n                // Set a flag to track this specific error\n                if (true) {\n                    window.localStorage.setItem('socket_xhr_error', 'true');\n                }\n                // Emit the error event\n                this.emitEvent('error', 'XHR polling error - trying alternative connection');\n                // Try the alternative transport after a short delay\n                setTimeout(()=>{\n                    this.tryAlternativeTransport();\n                }, 1000);\n            } else if ((error === null || error === void 0 ? void 0 : (_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('websocket')) || (error === null || error === void 0 ? void 0 : (_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('WebSocket')) || typeof error === 'object' && 'type' in error && error.type === 'TransportError') {\n                console.log('WebSocket error detected - will try polling transport');\n                // Set a flag to track this specific error\n                if (true) {\n                    window.localStorage.setItem('socket_websocket_error', 'true');\n                }\n                // Try polling immediately\n                setTimeout(()=>{\n                    try {\n                        var _this_socket;\n                        console.log('Switching to polling transport...');\n                        (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.disconnect();\n                        this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                            reconnectionAttempts: 5,\n                            reconnectionDelay: 1000,\n                            timeout: 10000,\n                            transports: [\n                                'polling'\n                            ],\n                            autoConnect: true,\n                            forceNew: true,\n                            upgrade: false\n                        });\n                        this.setupEventListeners();\n                    } catch (e) {\n                        console.error('Error switching to polling transport:', e);\n                    }\n                }, 1000);\n                this.emitEvent('error', 'WebSocket error - trying polling transport');\n            } else {\n                // For other errors, just emit the error event\n                this.emitEvent('error', (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error');\n                // Try to reconnect after a delay\n                setTimeout(()=>{\n                    this.connect();\n                }, 3000);\n            }\n        });\n        // Application-specific events\n        this.socket.on('code-update', (code)=>{\n            this.emitEvent('code-update', code);\n        });\n        this.socket.on('user-typing', (data)=>{\n            this.emitEvent('user-typing', data);\n        });\n        this.socket.on('user-joined', (users)=>{\n            this.emitEvent('user-joined', users);\n        });\n        this.socket.on('user-left', (users)=>{\n            this.emitEvent('user-left', users);\n        });\n        this.socket.on('highlight-line', (data)=>{\n            this.emitEvent('highlight-line', data);\n        });\n        this.socket.on('cursor-move', (param)=>{\n            let { userId, position } = param;\n            var _this_listeners_get;\n            console.log(\"Cursor move received from user \".concat(userId, \":\"), position);\n            (_this_listeners_get = this.listeners.get('cursor-move')) === null || _this_listeners_get === void 0 ? void 0 : _this_listeners_get.forEach((callback)=>callback({\n                    userId,\n                    position\n                }));\n        });\n        this.socket.on('room-users-updated', (data)=>{\n            this.emitEvent('room-users-updated', data);\n        });\n        // Handle request for initial code from new users\n        this.socket.on('get-initial-code', (data)=>{\n            this.emitEvent('get-initial-code', data);\n        });\n        // Handle receiving initial code\n        this.socket.on('initial-code-received', (data)=>{\n            this.emitEvent('initial-code-received', data);\n        });\n        // Handle teacher selection events\n        this.socket.on('teacher-selection', (data)=>{\n            this.emitEvent('teacher-selection', data);\n        });\n        this.socket.on('clear-teacher-selection', (data)=>{\n            this.emitEvent('clear-teacher-selection', data);\n        });\n    }\n    // Add event listener\n    on(event, callback) {\n        var _this_listeners_get;\n        if (!this.listeners.has(event)) {\n            this.listeners.set(event, []);\n        }\n        (_this_listeners_get = this.listeners.get(event)) === null || _this_listeners_get === void 0 ? void 0 : _this_listeners_get.push(callback);\n    }\n    // Remove event listener\n    off(event, callback) {\n        if (!this.listeners.has(event)) return;\n        const callbacks = this.listeners.get(event) || [];\n        const index = callbacks.indexOf(callback);\n        if (index !== -1) {\n            callbacks.splice(index, 1);\n        }\n    }\n    // Emit event to listeners\n    emitEvent(event, data) {\n        if (!this.listeners.has(event)) return;\n        const callbacks = this.listeners.get(event) || [];\n        callbacks.forEach((callback)=>{\n            if (typeof callback === 'function') {\n                try {\n                    callback(data);\n                } catch (error) {\n                    console.error(\"Error in \".concat(event, \" listener:\"), error);\n                }\n            }\n        });\n    }\n    // Check if socket is connected\n    isConnected() {\n        var _this_socket;\n        return this.connected && !!((_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.connected);\n    }\n    // Connect to socket server with fallback mechanisms\n    connect() {\n        if (!this.socket) {\n            this.initSocket();\n        } else if (!this.socket.connected) {\n            console.log('Socket exists but not connected, attempting to reconnect...');\n            // Try to reconnect with existing socket\n            try {\n                this.socket.connect();\n            } catch (error) {\n                console.error('Error reconnecting with existing socket:', error);\n                // If reconnection fails, create a new socket\n                this.initSocket();\n            }\n        }\n        // Set a timeout to check if connection was successful\n        setTimeout(()=>{\n            if (!this.isConnected()) {\n                this.tryAlternativeTransport();\n            }\n        }, 5000);\n    }\n    // Try alternative transport method if WebSocket fails\n    tryAlternativeTransport() {\n        try {\n            // Disconnect existing socket if any\n            if (this.socket) {\n                this.socket.disconnect();\n            }\n            // Create new socket with both transports but prioritize polling\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                reconnectionAttempts: 5,\n                reconnectionDelay: 1000,\n                timeout: 10000,\n                transports: [\n                    'polling',\n                    'websocket'\n                ],\n                autoConnect: true,\n                forceNew: true,\n                upgrade: true\n            });\n            this.setupEventListeners();\n        } catch (error) {\n            console.error('Error setting up alternative transport:', error);\n        }\n    }\n    // Disconnect from socket server\n    disconnect() {\n        if (this.socket) {\n            this.socket.disconnect();\n        }\n    }\n    // Create a new room\n    createRoom(username, roomId) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket || !this.isConnected()) {\n                return reject(new Error('Socket not connected'));\n            }\n            // Generate a unique userId if not already stored\n            let userId =  true ? window.localStorage.getItem('userId') : 0;\n            if (!userId) {\n                userId = \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9));\n                if (true) {\n                    window.localStorage.setItem('userId', userId);\n                }\n            }\n            this.socket.emit('create-room', {\n                username,\n                roomId,\n                userId\n            }, (response)=>{\n                if (response.error) {\n                    reject(new Error(response.error));\n                } else {\n                    // If the server validated and possibly changed the username, update it locally\n                    if (response.username && response.username !== username) {\n                        if (true) {\n                            window.localStorage.setItem('username', response.username);\n                        }\n                    }\n                    resolve({\n                        roomId: response.roomId,\n                        username: response.username || username\n                    });\n                }\n            });\n        });\n    }\n    // Join an existing room with fallback to HTTP if socket fails\n    joinRoom(roomId, username) {\n        return new Promise(async (resolve, reject)=>{\n            // Generate a unique userId if not already stored\n            let userId =  true ? window.localStorage.getItem('userId') : 0;\n            if (!userId) {\n                userId = \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9));\n                if (true) {\n                    window.localStorage.setItem('userId', userId);\n                }\n            }\n            // Check if we have a socket connection\n            if (!this.socket || !this.isConnected()) {\n                // Try to connect\n                this.connect();\n                // Wait a bit to see if connection succeeds\n                await new Promise((r)=>setTimeout(r, 2000));\n                // If still not connected, use HTTP fallback\n                if (!this.isConnected()) {\n                    return this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                }\n            }\n            // If we reach here, we have a socket connection, so use it\n            try {\n                if (!this.socket) {\n                    throw new Error('Socket is null');\n                }\n                // Emit join-room with userId, username, and roomId\n                this.socket.emit('join-room', {\n                    roomId,\n                    username,\n                    userId\n                }, (response)=>{\n                    if (response.error) {\n                        // If socket join fails, try HTTP fallback\n                        this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                    } else if (response.success) {\n                        // If the server validated and possibly changed the username, update it locally\n                        if (response.username && response.username !== username) {\n                            if (true) {\n                                window.localStorage.setItem('username', response.username);\n                            }\n                        }\n                        // Mark the current user in the users list\n                        const usersWithCurrentFlag = (response.users || []).map((user)=>({\n                                ...user,\n                                userId: user.userId || '',\n                                isCurrentUser: (user.userId || '') === userId\n                            }));\n                        resolve({\n                            users: usersWithCurrentFlag || [],\n                            username: response.username || username,\n                            role: response.role\n                        });\n                    } else {\n                        // If socket join fails, try HTTP fallback\n                        this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                    }\n                });\n                // Set a timeout in case the callback never fires\n                setTimeout(()=>{\n                    // If we haven't resolved or rejected yet, try HTTP fallback\n                    this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                }, 5000);\n            } catch (error) {\n                // If socket join throws an exception, try HTTP fallback\n                this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n            }\n        });\n    }\n    // HTTP fallback for joining a room when socket fails\n    joinRoomViaHttp(roomId, username, userId, resolve, reject) {\n        // Use axios to make a direct HTTP request to the server\n        const apiUrl = \"\".concat(SOCKET_URL, \"/api/join-room\");\n        axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(apiUrl, {\n            roomId,\n            username,\n            userId\n        }).then((response)=>{\n            if (response.data.error) {\n                reject(new Error(response.data.error));\n            } else {\n                // If the server validated and possibly changed the username, update it locally\n                if (response.data.username && response.data.username !== username) {\n                    if (true) {\n                        window.localStorage.setItem('username', response.data.username);\n                    }\n                }\n                // Create a default user list with at least the current user\n                const users = response.data.users || [\n                    {\n                        userId,\n                        username: response.data.username || username,\n                        socketId: 'http-fallback'\n                    }\n                ];\n                // Fix: When mapping users, always provide a fallback for userId (empty string if undefined)\n                const usersWithCurrentFlag = users.map((user)=>({\n                        ...user,\n                        userId: user.userId || '',\n                        isCurrentUser: (user.userId || '') === userId\n                    }));\n                resolve({\n                    users: usersWithCurrentFlag,\n                    username: response.data.username || username\n                });\n                // Try to reconnect socket after successful HTTP fallback\n                setTimeout(()=>this.connect(), 1000);\n            }\n        }).catch((error)=>{\n            // If HTTP fallback also fails, create a minimal response with just the current user\n            const fallbackUser = {\n                userId,\n                username,\n                isCurrentUser: true\n            };\n            // Resolve with just the current user to allow the UI to function\n            resolve({\n                users: [\n                    fallbackUser\n                ],\n                username\n            });\n            // Try to reconnect socket after error\n            setTimeout(()=>this.connect(), 2000);\n        });\n    }\n    // Send code changes to the server with HTTP fallback\n    sendCodeChange(roomId, code) {\n        // If socket is connected, use it\n        if (this.socket && this.isConnected()) {\n            try {\n                // Use volatile for code changes to prevent queueing\n                // This helps prevent outdated updates from being sent\n                this.socket.volatile.emit('code-change', {\n                    roomId,\n                    code\n                });\n                return true;\n            } catch (error) {\n            // Fall through to HTTP fallback\n            }\n        } else {\n            console.warn('Socket not connected, falling back to HTTP for code change.');\n        }\n        // HTTP fallback for code changes\n        this.sendCodeChangeViaHttp(roomId, code);\n        return true;\n    }\n    // Send initial code to a requesting user\n    sendInitialCode(roomId, code, requestingUserId) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot send initial code');\n            return false;\n        }\n        try {\n            this.socket.emit('send-initial-code', {\n                roomId,\n                code,\n                requestingUserId\n            });\n            console.log(\"Sent initial code to user \".concat(requestingUserId, \" in room \").concat(roomId));\n            return true;\n        } catch (error) {\n            console.error('Error sending initial code:', error);\n            return false;\n        }\n    }\n    // Send teacher text selection to students\n    sendTeacherSelection(roomId, selection) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot send teacher selection');\n            return false;\n        }\n        try {\n            this.socket.emit('teacher-selection', {\n                roomId,\n                selection\n            });\n            console.log(\"Sent teacher selection to room \".concat(roomId, \":\"), selection);\n            return true;\n        } catch (error) {\n            console.error('Error sending teacher selection:', error);\n            return false;\n        }\n    }\n    // Clear teacher text selection\n    clearTeacherSelection(roomId) {\n        if (!this.socket || !this.isConnected()) {\n            console.warn('Socket not connected, cannot clear teacher selection');\n            return false;\n        }\n        try {\n            this.socket.emit('clear-teacher-selection', {\n                roomId\n            });\n            console.log(\"Cleared teacher selection in room \".concat(roomId));\n            return true;\n        } catch (error) {\n            console.error('Error clearing teacher selection:', error);\n            return false;\n        }\n    }\n    // HTTP fallback for sending code changes\n    sendCodeChangeViaHttp(roomId, code) {\n        // Only send HTTP fallback for significant changes to reduce traffic\n        // Store the last sent code to avoid sending duplicates\n        const lastSentCode =  true ? window.localStorage.getItem(\"last_http_code_\".concat(roomId)) : 0;\n        if (lastSentCode === code) {\n            return; // Don't send duplicate code\n        }\n        // Use axios to make a direct HTTP request to the server\n        const apiUrl = \"\".concat(SOCKET_URL, \"/api/code-change\");\n        const userId =  true ? window.localStorage.getItem('userId') || '' : 0;\n        axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(apiUrl, {\n            roomId,\n            code,\n            userId\n        }).then((response)=>{\n            // Store the sent code to avoid duplicates\n            if (true) {\n                window.localStorage.setItem(\"last_http_code_\".concat(roomId), code);\n            }\n        }).catch((error)=>{\n            console.error('Error sending code change via HTTP:', error);\n        });\n        // Try to reconnect socket\n        if (!this.isConnected()) {\n            setTimeout(()=>this.connect(), 1000);\n        }\n    }\n    // Send typing notification\n    sendTyping(roomId, username) {\n        if (!this.socket || !this.isConnected()) {\n            return;\n        }\n        // Use the exact username provided by the user without any modifications\n        // This ensures we use exactly what the user entered on the dashboard\n        const validUsername = username;\n        // Get userId from localStorage\n        const userId =  true ? window.localStorage.getItem('userId') || \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9)) : 0;\n        try {\n            this.socket.emit('typing', {\n                roomId,\n                username: validUsername,\n                userId\n            });\n        } catch (error) {}\n    }\n    // Emit a highlight-line event to the server\n    sendHighlightLine(roomId, startLine, endLine, comment) {\n        if (!this.socket || !this.isConnected()) return;\n        try {\n            this.socket.emit('highlight-line', {\n                roomId,\n                startLine,\n                endLine,\n                comment\n            });\n        } catch (error) {\n        // Optionally handle error\n        }\n    }\n    // Leave a room\n    leaveRoom(roomId) {\n        if (!this.socket || !this.isConnected()) return;\n        try {\n            this.socket.emit('leave-room', roomId);\n        } catch (error) {}\n    }\n    // Add a public method to listen for the next connect event\n    onConnect(callback) {\n        if (this.isConnected()) {\n            callback();\n        } else if (this.socket) {\n            this.socket.once('connect', callback);\n        } else {\n            // If socket is not initialized, initialize and then listen\n            this.initSocket();\n            // Wait for the socket to be created, then attach the listener\n            setTimeout(()=>{\n                var _this_socket;\n                (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.once('connect', callback);\n            }, 100);\n        }\n    }\n    getSocket() {\n        return this.socket;\n    }\n    // Emit cursor movement\n    sendCursorMove(roomId, userId, position) {\n        if (this.socket && this.connected) {\n            this.socket.emit(\"cursor-move\", {\n                roomId,\n                userId,\n                position\n            });\n        }\n    }\n    // Add a method to validate if a room exists on the server\n    async validateRoom(roomId) {\n        if (!this.socket || !this.isConnected()) {\n            throw new Error(\"Socket not connected\");\n        }\n        return new Promise((resolve, reject)=>{\n            if (!this.socket) {\n                throw new Error(\"Socket is not initialized\");\n            }\n            this.socket.emit(\"validate-room\", {\n                roomId\n            }, (response)=>{\n                if (response) {\n                    resolve(response);\n                } else {\n                    reject(new Error(\"Failed to validate room\"));\n                }\n            });\n        });\n    }\n    constructor(){\n        this.socket = null;\n        this.listeners = new Map();\n        this.connected = false;\n        this.initSocket();\n    }\n}\n// Export the class itself instead of calling getInstance during export\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SocketService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/socketService.ts\n"));

/***/ })

});