import React from "react";
export declare const DropdownMenuContentPropsContext: React.Context<{
    onSelect?: ((event: Event) => void) | undefined;
}>;
export declare const getDropdownMenuItemClassName: (className?: string, selected?: boolean, hovered?: boolean) => string;
export declare const useHandleDropdownMenuItemClick: (origOnClick: React.MouseEventHandler<HTMLAnchorElement | HTMLButtonElement> | undefined, onSelect: ((event: Event) => void) | undefined) => (event: React.MouseEvent<HTMLAnchorElement | HTMLButtonElement, MouseEvent>) => void;
