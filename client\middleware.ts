// client/middleware.ts or /middleware.ts (depending on monorepo vs single app)
import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export function middleware(request: NextRequest) {
  const isAuth = request.cookies.get("__session")?.value
  const url = request.nextUrl.pathname

  const isProtected = url.startsWith("/editor")

  if (isProtected && !isAuth) {
    return NextResponse.redirect(new URL("/login", request.url))
  }

  return NextResponse.next()
}
