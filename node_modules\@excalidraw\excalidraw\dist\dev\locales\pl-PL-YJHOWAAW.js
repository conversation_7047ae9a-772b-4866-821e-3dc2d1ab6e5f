import "../chunk-XDFCUUT6.js";

// locales/pl-PL.json
var labels = {
  paste: "Wklej",
  pasteAsPlaintext: "Wklej jako zwyk\u0142y tekst",
  pasteCharts: "Wklej wykresy",
  selectAll: "<PERSON>az<PERSON>z wszystko",
  multiSelect: "Dodaj element do zaznaczenia",
  moveCanvas: "Przesu\u0144 obszar roboczy",
  cut: "Wytnij",
  copy: "Kopiuj",
  copyAsPng: "Skopiuj do schowka jako plik PNG",
  copyAsSvg: "Skopiuj do schowka jako plik SVG",
  copyText: "Skopiuj do schowka jako tekst",
  copySource: "Skopiuj \u017Ar\xF3d\u0142o do schowka",
  convertToCode: "Skonwertuj do kodu",
  bringForward: "Przenie\u015B wy\u017Cej",
  sendToBack: "Przenie\u015B na sp\xF3d",
  bringToFront: "Przenie\u015B na wierzch",
  sendBackward: "Przenie\u015B ni\u017Cej",
  delete: "Usu\u0144",
  copyStyles: "Kopiuj style",
  pasteStyles: "Wklej style",
  stroke: "Kolor obramowania",
  background: "Kolor wype\u0142nienia",
  fill: "Wype\u0142nienie",
  strokeWidth: "Grubo\u015B\u0107 obramowania",
  strokeStyle: "Styl obrysu",
  strokeStyle_solid: "Pe\u0142ny",
  strokeStyle_dashed: "Kreskowany",
  strokeStyle_dotted: "Kropkowany",
  sloppiness: "Styl kreski",
  opacity: "Prze\u017Aroczysto\u015B\u0107",
  textAlign: "Wyr\xF3wnanie tekstu",
  edges: "Kraw\u0119dzie",
  sharp: "Ostry",
  round: "Zaokr\u0105glij",
  arrowheads: "Groty",
  arrowhead_none: "Brak",
  arrowhead_arrow: "Strza\u0142ka",
  arrowhead_bar: "Kreska",
  arrowhead_circle: "Okr\u0105g",
  arrowhead_circle_outline: "Okr\u0105g (obrys)",
  arrowhead_triangle: "Tr\xF3jk\u0105t",
  arrowhead_triangle_outline: "Tr\xF3jk\u0105t (obrys)",
  arrowhead_diamond: "Romb",
  arrowhead_diamond_outline: "Romb (obrys)",
  fontSize: "Rozmiar tekstu",
  fontFamily: "Kr\xF3j pisma",
  addWatermark: 'Dodaj "Zrobione w Excalidraw"',
  handDrawn: "Odr\u0119czny",
  normal: "Normalny",
  code: "Kod",
  small: "Ma\u0142y",
  medium: "\u015Aredni",
  large: "Du\u017Cy",
  veryLarge: "Bardzo du\u017Cy",
  solid: "Pe\u0142ne",
  hachure: "Linie",
  zigzag: "Zygzak",
  crossHatch: "Zakre\u015Blone",
  thin: "Cienkie",
  bold: "Pogrubione",
  left: "Do lewej",
  center: "Do \u015Brodka",
  right: "Do prawej",
  extraBold: "Ekstra pogrubione",
  architect: "Dok\u0142adny",
  artist: "Artystyczny",
  cartoonist: "Rysunkowy",
  fileTitle: "Nazwa pliku",
  colorPicker: "Paleta kolor\xF3w",
  canvasColors: "U\u017Cywane na p\u0142\xF3tnie",
  canvasBackground: "Kolor dokumentu",
  drawingCanvas: "Obszar roboczy",
  layers: "Warstwy",
  actions: "Akcje",
  language: "J\u0119zyk",
  liveCollaboration: "Wsp\xF3\u0142praca w czasie rzeczywistym...",
  duplicateSelection: "Powiel",
  untitled: "Bez tytu\u0142u",
  name: "Nazwa",
  yourName: "Twoje imi\u0119",
  madeWithExcalidraw: "Zrobione w Excalidraw",
  group: "Zgrupuj wybrane",
  ungroup: "Rozgrupuj wybrane",
  collaborators: "Wsp\xF3\u0142tw\xF3rcy",
  showGrid: "Poka\u017C siatk\u0119",
  addToLibrary: "Dodaj do biblioteki",
  removeFromLibrary: "Usu\u0144 z biblioteki",
  libraryLoadingMessage: "Wczytywanie biblioteki\u2026",
  libraries: "Przegl\u0105daj biblioteki",
  loadingScene: "Wczytywanie sceny\u2026",
  align: "Wyr\xF3wnaj",
  alignTop: "Wyr\xF3wnaj do g\xF3ry",
  alignBottom: "Wyr\xF3wnaj do do\u0142u",
  alignLeft: "Wyr\xF3wnaj do lewej",
  alignRight: "Wyr\xF3wnaj do prawej",
  centerVertically: "Wy\u015Brodkuj w pionie",
  centerHorizontally: "Wy\u015Brodkuj w poziomie",
  distributeHorizontally: "Roz\u0142\xF3\u017C poziomo",
  distributeVertically: "Roz\u0142\xF3\u017C pionowo",
  flipHorizontal: "Odwr\xF3\u0107 w poziomie",
  flipVertical: "Odwr\xF3\u0107 w pionie",
  viewMode: "Tryb widoku",
  share: "Udost\u0119pnij",
  showStroke: "Poka\u017C pr\xF3bnik kolor\xF3w obrysu",
  showBackground: "Poka\u017C pr\xF3bnik koloru t\u0142a",
  toggleTheme: "Prze\u0142\u0105cz motyw",
  personalLib: "Biblioteka prywatna",
  excalidrawLib: "Biblioteka Excalidraw",
  decreaseFontSize: "Zmniejsz rozmiar czcionki",
  increaseFontSize: "Zwi\u0119ksz rozmiar czcionki",
  unbindText: "Od\u0142\u0105cz tekst od kontenera",
  bindText: "Po\u0142\u0105cz tekst z kontenerem",
  createContainerFromText: "Zawijaj tekst w kontenerze",
  link: {
    edit: "Edytuj \u0142\u0105cze",
    editEmbed: "Edytuj i osad\u017A link",
    create: "Utw\xF3rz \u0142\u0105cze",
    createEmbed: "Stw\xF3rz i osad\u017A\xA0link",
    label: "\u0141\u0105cze",
    labelEmbed: "Podlinkuj i osad\u017A",
    empty: "Nie ustawiono linku"
  },
  lineEditor: {
    edit: "Edytuj lini\u0119",
    exit: "Wyjd\u017A z edytora linii"
  },
  elementLock: {
    lock: "Zablokuj",
    unlock: "Odblokuj",
    lockAll: "Zablokuj wszystko",
    unlockAll: "Odblokuj wszystko"
  },
  statusPublished: "Opublikowano",
  sidebarLock: "Panel boczny zawsze otwarty",
  selectAllElementsInFrame: "Zaznacz wszystkie elementy w ramce",
  removeAllElementsFromFrame: "Usu\u0144 wszystkie elementy z ramki",
  eyeDropper: "Wybierz kolor z p\u0142\xF3tna",
  textToDiagram: "Tekst do diagramu",
  prompt: ""
};
var library = {
  noItems: "Nie dodano jeszcze \u017Cadnych element\xF3w...",
  hint_emptyLibrary: "Wybierz element na p\u0142\xF3tnie, aby go tutaj doda\u0107, lub zainstaluj bibliotek\u0119 z poni\u017Cszego publicznego repozytorium.",
  hint_emptyPrivateLibrary: "Wybierz element, aby doda\u0107 go tutaj."
};
var buttons = {
  clearReset: "Wyczy\u015B\u0107 dokument i zresetuj kolor dokumentu",
  exportJSON: "Eksportuj do pliku",
  exportImage: "Eksportuj obraz...",
  export: "Zapisz jako...",
  copyToClipboard: "Skopiuj do schowka",
  save: "Zapisz do bie\u017C\u0105cego pliku",
  saveAs: "Zapisz jako",
  load: "Otw\xF3rz",
  getShareableLink: "Udost\u0119pnij",
  close: "Zamknij",
  selectLanguage: "Wybierz j\u0119zyk",
  scrollBackToContent: "Wr\xF3\u0107 do obszaru roboczego",
  zoomIn: "Powi\u0119ksz",
  zoomOut: "Pomniejsz",
  resetZoom: "Zresetuj powi\u0119kszenie",
  menu: "Menu",
  done: "Gotowe",
  edit: "Edytuj",
  undo: "Cofnij",
  redo: "Przywr\xF3\u0107",
  resetLibrary: "Resetuj bibliotek\u0119",
  createNewRoom: "Utw\xF3rz nowy pok\xF3j",
  fullScreen: "Pe\u0142ny ekran",
  darkMode: "Ciemny motyw",
  lightMode: "Jasny motyw",
  zenMode: "Tryb Zen",
  objectsSnapMode: "Przyci\u0105ganie do obiekt\xF3w",
  exitZenMode: "Wyjd\u017A z trybu Zen",
  cancel: "Anuluj",
  clear: "Wyczy\u015B\u0107",
  remove: "Usu\u0144",
  embed: "Prze\u0142\u0105cz osadzenie",
  publishLibrary: "Opublikuj",
  submit: "Prze\u015Blij",
  confirm: "Zatwierd\u017A",
  embeddableInteractionButton: "Kliknij, aby wej\u015B\u0107 w interakcj\u0119"
};
var alerts = {
  clearReset: "To spowoduje usuni\u0119cie wszystkiego z dokumentu. Czy chcesz kontynuowa\u0107?",
  couldNotCreateShareableLink: "Wyst\u0105pi\u0142 b\u0142\u0105d przy generowaniu linka do udost\u0119pniania.",
  couldNotCreateShareableLinkTooBig: "Nie mo\u017Cna utworzy\u0107 linku do udost\u0119pnienia: scena jest za du\u017Ca",
  couldNotLoadInvalidFile: "Nie uda\u0142o si\u0119 otworzy\u0107 pliku. Wybrany plik jest nieprawid\u0142owy.",
  importBackendFailed: "Wyst\u0105pi\u0142 b\u0142\u0105d podczas importowania pliku.",
  cannotExportEmptyCanvas: "Najpierw musisz co\u015B narysowa\u0107, aby zapisa\u0107 dokument.",
  couldNotCopyToClipboard: "Nie uda\u0142o si\u0119 skopiowa\u0107 do schowka.",
  decryptFailed: "Nie uda\u0142o si\u0119 odszyfrowa\u0107 danych.",
  uploadedSecurly: "By zapewni\u0107 Ci prywatno\u015B\u0107, udost\u0119pnianie projektu jest zabezpieczone szyfrowaniem end-to-end, co oznacza, \u017Ce poza tob\u0105 i osob\u0105 z kt\xF3r\u0105 podzielisz si\u0119 linkiem, nikt nie ma dost\u0119pu do tego co udost\u0119pniasz.",
  loadSceneOverridePrompt: "Wczytanie zewn\u0119trznego rysunku zast\u0105pi istniej\u0105c\u0105 zawarto\u015B\u0107. Czy chcesz kontynuowa\u0107?",
  collabStopOverridePrompt: "Zatrzymanie sesji nadpisze poprzedni, zapisany lokalnie rysunek. Czy jeste\u015B pewien?\n\n(Je\u015Bli chcesz zachowa\u0107 sw\xF3j lokalny rysunek, po prostu zamknij zak\u0142adk\u0119 przegl\u0105darki.)",
  errorAddingToLibrary: "Nie uda\u0142o si\u0119 doda\u0107 elementu do biblioteki",
  errorRemovingFromLibrary: "Nie uda\u0142o si\u0119 usun\u0105\u0107 elementu z biblioteki",
  confirmAddLibrary: "To doda {{numShapes}} kszta\u0142t\xF3w do twojej biblioteki. Jeste\u015B pewien?",
  imageDoesNotContainScene: "Ten obraz nie zawiera \u017Cadnych informacji o scenie. Czy w\u0142\u0105czy\u0142e\u015B osadzanie sceny podczas eksportu?",
  cannotRestoreFromImage: "Scena nie mog\u0142a zosta\u0107 przywr\xF3cona z pliku obrazu",
  invalidSceneUrl: "Nie uda\u0142o si\u0119 zaimportowa\u0107 sceny z podanego adresu URL. Jest ona wadliwa lub nie zawiera poprawnych danych Excalidraw w formacie JSON.",
  resetLibrary: "To wyczy\u015Bci twoj\u0105 bibliotek\u0119. Jeste\u015B pewien?",
  removeItemsFromsLibrary: "Usun\u0105\u0107 {{count}} element(\xF3w) z biblioteki?",
  invalidEncryptionKey: "Klucz szyfrowania musi sk\u0142ada\u0107 si\u0119 z 22 znak\xF3w. Wsp\xF3\u0142praca na \u017Cywo jest wy\u0142\u0105czona.",
  collabOfflineWarning: "Brak po\u0142\u0105czenia z Internetem.\nTwoje zmiany nie zostan\u0105 zapisane!"
};
var errors = {
  unsupportedFileType: "Nieobs\u0142ugiwany typ pliku.",
  imageInsertError: "Nie uda\u0142o si\u0119 wstawi\u0107 obrazu. Spr\xF3buj ponownie p\xF3\u017Aniej...",
  fileTooBig: "Plik jest zbyt du\u017Cy. Maksymalny dozwolony rozmiar to {{maxSize}}.",
  svgImageInsertError: "Nie uda\u0142o si\u0119 wstawi\u0107 obrazu SVG. Znacznik SVG wygl\u0105da na nieprawid\u0142owy.",
  failedToFetchImage: "Nie uda\u0142o si\u0119\xA0za\u0142adowa\u0107 obrazu.",
  invalidSVGString: "Nieprawid\u0142owy SVG.",
  cannotResolveCollabServer: "Nie mo\u017Cna po\u0142\u0105czy\u0107 si\u0119 z serwerem wsp\xF3\u0142pracy w czasie rzeczywistym. Prosz\u0119 od\u015Bwie\u017Cy\u0107 stron\u0119 i spr\xF3bowa\u0107 ponownie.",
  importLibraryError: "Wyst\u0105pi\u0142 b\u0142\u0105d w trakcie \u0142adowania biblioteki",
  collabSaveFailed: "Nie uda\u0142o si\u0119 zapisa\u0107 w bazie danych. Je\u015Bli problemy nie ust\u0105pi\u0105, zapisz plik lokalnie, aby nie utraci\u0107 swojej pracy.",
  collabSaveFailed_sizeExceeded: "Nie uda\u0142o si\u0119 zapisa\u0107 w bazie danych \u2014 dokument jest za du\u017Cy. Zapisz plik lokalnie, aby nie utraci\u0107 swojej pracy.",
  imageToolNotSupported: "Dodawanie obraz\xF3w jest wy\u0142\u0105czone.",
  brave_measure_text_error: {
    line1: "Wygl\u0105da na to, \u017Ce u\u017Cywasz przegl\u0105darki Brave z w\u0142\u0105czonym ustawieniem <bold>Agressively Block Fingerprinting</bold>.",
    line2: "Mo\u017Ce to doprowadzi\u0107 do z\u0142amania <bold>element\xF3w tekstu</bold> na rysunkach.",
    line3: "Zdecydowanie zalecamy wy\u0142\u0105czenie tego ustawienia. Mo\u017Cesz wykona\u0107 <link>te kroki</link>, aby to zrobi\u0107.",
    line4: "Je\u015Bli wy\u0142\u0105czenie tego ustawienia nie naprawia wy\u015Bwietlania element\xF3w tekstowych, zg\u0142o\u015B <issueLink>problem</issueLink> na naszym GitHubie lub napisz do nas na <discordLink>Discordzie</discordLink>"
  },
  libraryElementTypeError: {
    embeddable: "Elementy osadzone nie mog\u0105 zosta\u0107 dodane do biblioteki.",
    iframe: "Elementy IFrame nie mog\u0105 zosta\u0107 dodane do biblioteki.",
    image: "Dodawania obraz\xF3w do biblioteki nadejdzie wkr\xF3tce!"
  },
  asyncPasteFailedOnRead: "Nie uda\u0142o si\u0119 wklei\u0107 (nie uda\u0142o si\u0119 odczyta\u0107 ze schowka systemowego).",
  asyncPasteFailedOnParse: "Nie uda\u0142o si\u0119 wklei\u0107.",
  copyToSystemClipboardFailed: "Nie uda\u0142o si\u0119\xA0skopiowa\u0107 do schowka."
};
var toolBar = {
  selection: "Zaznaczenie",
  image: "Wstaw obraz",
  rectangle: "Prostok\u0105t",
  diamond: "Romb",
  ellipse: "Elipsa",
  arrow: "Strza\u0142ka",
  line: "Linia",
  freedraw: "Rysuj",
  text: "Tekst",
  library: "Biblioteka",
  lock: "Zablokuj wybrane narz\u0119dzie",
  penMode: "Tryb pi\xF3ra \u2014 zapobiegaj dotkni\u0119ciom",
  link: "Dodaj/aktualizuj link dla wybranego kszta\u0142tu",
  eraser: "Gumka",
  frame: "Ramka",
  magicframe: "Wireframe do kodu",
  embeddable: "Osadzenie z internetu",
  laser: "Wska\u017Anik laserowy",
  hand: "R\u0119ka (narz\u0119dzie do przesuwania)",
  extraTools: "Wi\u0119cej narz\u0119dzi",
  mermaidToExcalidraw: "Konwertuj diagram Mermaid do Excalidraw",
  magicSettings: "Ustawienia AI"
};
var headings = {
  canvasActions: "Narz\u0119dzia",
  selectedShapeActions: "Wybrane narz\u0119dzie",
  shapes: "Kszta\u0142ty"
};
var hints = {
  canvasPanning: "Aby przesun\u0105\u0107 p\u0142\xF3tno, przytrzymaj k\xF3\u0142ko myszy lub spacj\u0119 podczas przeci\u0105gania, albo u\u017Cyj narz\u0119dzia r\u0119ki",
  linearElement: "Naci\u015Bnij, aby zrobi\u0107 punkt, przeci\u0105gnij, aby narysowa\u0107 lini\u0119",
  freeDraw: "Naci\u015Bnij i przeci\u0105gnij by rysowa\u0107, pu\u015B\u0107 kiedy sko\u0144czysz",
  text: "Wskaz\xF3wka: mo\u017Cesz r\xF3wnie\u017C doda\u0107 tekst klikaj\u0105c dwukrotnie gdziekolwiek za pomoc\u0105 narz\u0119dzia zaznaczania",
  embeddable: "Kliknij i przeci\u0105gnij, aby stworzy\u0107\xA0osadzenie strony",
  text_selected: "Kliknij dwukrotnie lub naci\u015Bnij ENTER, aby edytowa\u0107 tekst",
  text_editing: "Naci\u015Bnij Escape lub Ctrl (Cmd w macOS) + ENTER, aby zako\u0144czy\u0107 edycj\u0119",
  linearElementMulti: "Aby zako\u0144czy\u0107 krzyw\u0105, ponownie kliknij w ostatni punkt, b\u0105d\u017A naci\u015Bnij Esc albo Enter",
  lockAngle: "Mo\u017Cesz ograniczy\u0107 k\u0105t trzymaj\u0105c SHIFT",
  resize: "Mo\u017Cesz zachowa\u0107 proporcj\u0119 trzymaj\u0105\u0107 wcisni\u0119ty SHIFT, przytrzymaj ALT by zmieni\u0107 rozmiar wzgl\u0119dem \u015Brodka",
  resizeImage: "Mo\u017Cesz zmieni\u0107 rozmiar swobodnie trzymaj\u0105c SHIFT,\nprzytrzymaj ALT, aby przeskalowa\u0107 wzgl\u0119dem \u015Brodka obiektu",
  rotate: "Mo\u017Cesz obraca\u0107\xA0element w r\xF3wnych odst\u0119pach trzymaj\u0105c wci\u015Bni\u0119ty SHIFT",
  lineEditor_info: "Przytrzymaj CtrlOrCmd i kliknij dwukrotnie lub naci\u015Bnij CtrlOrCmd + Enter, aby edytowa\u0107 punkty",
  lineEditor_pointSelected: "Naci\u015Bnij przycisk Delete, aby usun\u0105\u0107 punkt. Ctrl/Cmd+D, aby go zduplikowa\u0107. Przeci\u0105gnij, aby go przenie\u015B\u0107",
  lineEditor_nothingSelected: "Wybierz punkt do edycji (przytrzymaj SHIFT, aby wybra\u0107 wiele),\nlub przytrzymaj Alt i kliknij, aby doda\u0107 nowe punkty",
  placeImage: "Kliknij, aby umie\u015Bci\u0107 obraz, lub kliknij i przeci\u0105gnij, aby ustawi\u0107 jego rozmiar r\u0119cznie",
  publishLibrary: "Opublikuj w\u0142asn\u0105 bibliotek\u0119",
  bindTextToElement: "Wci\u015Bnij enter, aby doda\u0107 tekst",
  deepBoxSelect: "Przytrzymaj CtrlOrCmd, aby wybra\u0107 w obr\u0119bie grupy i unikn\u0105\u0107 przeci\u0105gania",
  eraserRevert: "Przytrzymaj Alt, aby przywr\xF3ci\u0107 elementy oznaczone do usuni\u0119cia",
  firefox_clipboard_write: 'Ta funkcja mo\u017Ce by\u0107 w\u0142\u0105czona poprzez ustawienie flagi "dom.events.asyncClipboard.clipboardItem" na "true". Aby zmieni\u0107 flagi przegl\u0105darki w Firefox, odwied\u017A stron\u0119 "about:config".',
  disableSnapping: "Przytrzymaj Ctrl lub Cmd, aby wy\u0142\u0105czy\u0107\xA0przyci\u0105ganie"
};
var canvasError = {
  cannotShowPreview: "Nie mo\u017Cna wy\u015Bwietli\u0107 podgl\u0105du",
  canvasTooBig: "Obszar roboczy mo\u017Ce by\u0107 za du\u017Cy.",
  canvasTooBigTip: "Wskaz\xF3wka: spr\xF3buj nieco zbli\u017Cy\u0107 najdalej wysuni\u0119te elementy."
};
var errorSplash = {
  headingMain: "Wyst\u0105pi\u0142 b\u0142\u0105d. Spr\xF3buj <button>od\u015Bwie\u017Cy\u0107 stron\u0119.</button>",
  clearCanvasMessage: "Je\u015Bli od\u015Bwie\u017Cenie strony nie zadzia\u0142a\u0142o, spr\xF3buj <button>usun\u0105\u0107 wszystko z dokumentu.</button>",
  clearCanvasCaveat: " Pami\u0119taj tylko, \u017Ce spowoduje to utrat\u0119 ca\u0142ej twojej pracy ",
  trackedToSentry: "B\u0142\u0105d o identyfikatorze {{eventId}} zosta\u0142 zaraportowany w naszym systemie.",
  openIssueMessage: "Szanujemy twoj\u0105 prywatno\u015B\u0107 i raport nie zawiera\u0142 \u017Cadnych danych dotycz\u0105cych tego nad czym pracowa\u0142e\u015B, natomiast je\u017Celi jeste\u015B w stanie podzieli\u0107 si\u0119 tym nad czym pracowa\u0142e\u015B, prosimy o dodatkowy raport poprzez <button>nasze narz\u0119dzie do raportowania b\u0142\u0119d\xF3w.</button> Prosimy o do\u0142\u0105czenie poni\u017Cszej informacji poprzez skopiowanie jej i umieszczenie jej w zg\u0142oszeniu na portalu GitHub.",
  sceneContent: "Zawarto\u015B\u0107 dokumentu:"
};
var roomDialog = {
  desc_intro: "B\u0119dziesz w stanie pracowa\u0107\xA0wraz z osobami kt\xF3re zaprosisz do wsp\xF3\u0142pracy.",
  desc_privacy: "By zapewni\u0107 Ci prywatno\u015B\u0107, sesja wsp\xF3\u0142pracy na \u017Cywo jest zabezpieczona szyfrowaniem end-to-end, co oznacza, \u017Ce poza tob\u0105 i osobami z kt\xF3rymi podzielisz si\u0119 linkiem, nikt nie ma dost\u0119pu do tego co b\u0119dziecie tworzy\u0107.",
  button_startSession: "Rozpocznij sesj\u0119",
  button_stopSession: "Zako\u0144cz sesj\u0119",
  desc_inProgressIntro: "Sesja wsp\xF3\u0142pracy na \u017Cywo w\u0142a\u015Bnie si\u0119 rozpocz\u0119\u0142a.",
  desc_shareLink: "Udost\u0119pnij ten link osobom, z kt\xF3rymi chcesz wsp\xF3\u0142pracowa\u0107:",
  desc_exitSession: "Zako\u0144czenie sesji spowoduje od\u0142\u0105czenie ciebie od pokoju, ale nadal b\u0119dziesz m\xF3g\u0142 lokalnie kontynuowa\u0107 prac\u0119. Zauwa\u017C, \u017Ce osoby z kt\xF3rymi wsp\xF3\u0142pracowa\u0142e\u015B nadal b\u0119d\u0105 mog\u0142y wsp\xF3\u0142pracowa\u0107.",
  shareTitle: "Do\u0142\u0105cz do sesji wsp\xF3\u0142pracy na \u017Cywo w Excalidraw"
};
var errorDialog = {
  title: "Wyst\u0105pi\u0142 b\u0142\u0105d"
};
var exportDialog = {
  disk_title: "Zapisz na dysku",
  disk_details: "Eksportuj dane sceny do pliku, z kt\xF3rego mo\u017Cesz importowa\u0107 p\xF3\u017Aniej.",
  disk_button: "Zapisz do pliku",
  link_title: "Link do udost\u0119pnienia",
  link_details: "Eksportuj jako link tylko do odczytu.",
  link_button: "Wygeneruj link",
  excalidrawplus_description: "Zapisz scen\u0119 do swojego obszaru roboczego Excalidraw+.",
  excalidrawplus_button: "Eksportuj",
  excalidrawplus_exportError: "W tej chwili nie mo\u017Cna wyeksportowa\u0107 do Excalidraw+..."
};
var helpDialog = {
  blog: "Przeczytaj na naszym blogu",
  click: "klikni\u0119cie",
  deepSelect: "Wyb\xF3r w obr\u0119bie grupy",
  deepBoxSelect: "Wyb\xF3r w obr\u0119bie grupy i unikanie przeci\u0105gania",
  curvedArrow: "Zakrzywiona strza\u0142ka",
  curvedLine: "Zakrzywiona linia",
  documentation: "Dokumentacja",
  doubleClick: "podw\xF3jne klikni\u0119cie",
  drag: "przeci\u0105gnij",
  editor: "Edytor",
  editLineArrowPoints: "Edytuj punkty linii/strza\u0142ki",
  editText: "Edytuj tekst/dodaj etykiet\u0119",
  github: "Znalaz\u0142e\u015B problem? Prze\u015Blij",
  howto: "Skorzystaj z instrukcji",
  or: "lub",
  preventBinding: "Zapobiegaj wi\u0105zaniu strza\u0142ek",
  tools: "Narz\u0119dzia",
  shortcuts: "Skr\xF3ty klawiszowe",
  textFinish: "Zako\u0144cz edycj\u0119 (edytor tekstu)",
  textNewLine: "Dodaj nowy wiersz (edytor tekstu)",
  title: "Pomoc",
  view: "Widok",
  zoomToFit: "Powi\u0119ksz, aby wy\u015Bwietli\u0107 wszystkie elementy",
  zoomToSelection: "Przybli\u017C do zaznaczenia",
  toggleElementLock: "Zablokuj/odblokuj zaznaczenie",
  movePageUpDown: "Przesu\u0144 stron\u0119 w g\xF3r\u0119/w d\xF3\u0142",
  movePageLeftRight: "Przenie\u015B stron\u0119 w lewo/prawo"
};
var clearCanvasDialog = {
  title: "Wyczy\u015B\u0107 p\u0142\xF3tno"
};
var publishDialog = {
  title: "Opublikuj bibliotek\u0119",
  itemName: "Nazwa elementu",
  authorName: "Nazwa autora",
  githubUsername: "Nazwa u\u017Cytkownika na GitHubie",
  twitterUsername: "Nazwa u\u017Cytkownika Twitter",
  libraryName: "Nazwa biblioteki",
  libraryDesc: "Opis biblioteki",
  website: "Strona internetowa",
  placeholder: {
    authorName: "Twoje imi\u0119 lub nazwa u\u017Cytkownika",
    libraryName: "Nazwa twojej biblioteki",
    libraryDesc: "Opis twojej biblioteki, aby pom\xF3c innym zrozumie\u0107 jej dzia\u0142anie",
    githubHandle: "Uchwyt GitHub (opcjonalny), dzi\u0119ki czemu mo\u017Cesz edytowa\u0107 bibliotek\u0119 po przes\u0142aniu do sprawdzenia",
    twitterHandle: "Nazwa u\u017Cytkownika w serwisie Twitter (opcjonalna), aby wiedzie\u0107 kogo oznaczy\u0107 przy promowaniu na Twitterze",
    website: "Link do Twojej osobistej strony internetowej lub gdzie indziej (opcjonalnie)"
  },
  errors: {
    required: "Wymagane",
    website: "Wprowad\u017A prawid\u0142owy adres URL"
  },
  noteDescription: "<link></link>dla innych os\xF3b do wykorzystania w swoich rysunkach.",
  noteGuidelines: "Biblioteka musi by\u0107 najpierw zatwierdzona r\u0119cznie. Przeczytaj <link>wytyczne</link>",
  noteLicense: "Wysy\u0142aj\u0105c zgadzasz si\u0119, \u017Ce biblioteka zostanie opublikowana pod <link>Licencja MIT, </link>w skr\xF3cie, ka\u017Cdy mo\u017Ce z nich korzysta\u0107 bez ogranicze\u0144.",
  noteItems: "Ka\u017Cdy element biblioteki musi mie\u0107 w\u0142asn\u0105 nazw\u0119, aby by\u0142 filtrowalny. Uwzgl\u0119dnione zostan\u0105 nast\u0119puj\u0105ce elementy biblioteki:",
  atleastOneLibItem: "Prosz\u0119 wybra\u0107 co najmniej jeden element biblioteki, by rozpocz\u0105\u0107",
  republishWarning: "Uwaga: niekt\xF3re z wybranych element\xF3w s\u0105 oznaczone jako ju\u017C opublikowane/wys\u0142ane. Powiniene\u015B ponownie przes\u0142a\u0107 elementy tylko wtedy, gdy aktualizujesz istniej\u0105c\u0105 bibliotek\u0119 lub zg\u0142oszenie."
};
var publishSuccessDialog = {
  title: "Biblioteka zosta\u0142a przes\u0142ana",
  content: "Dzi\u0119kujemy {{authorName}}. Twoja biblioteka zosta\u0142a przes\u0142ana do sprawdzenia. Mo\u017Cesz \u015Bledzi\u0107 jej stan<link>tutaj</link>"
};
var confirmDialog = {
  resetLibrary: "Zresetuj Bibliotek\u0119",
  removeItemsFromLib: "Usu\u0144 wybrane elementy z biblioteki"
};
var imageExportDialog = {
  header: "Eksportuj obraz",
  label: {
    withBackground: "T\u0142o",
    onlySelected: "Tylko wybrane",
    darkMode: "Tryb ciemny",
    embedScene: "Osad\u017A scen\u0119",
    scale: "Skala",
    padding: "Dope\u0142nienie"
  },
  tooltip: {
    embedScene: "Dane sceny zostan\u0105 zapisane w eksportowanym pliku PNG/SVG tak, aby scena mog\u0142a zosta\u0107 z niego przywr\xF3cona.\nZwi\u0119kszy to rozmiar eksportowanego pliku."
  },
  title: {
    exportToPng: "Zapisz jako PNG",
    exportToSvg: "Zapisz jako SVG",
    copyPngToClipboard: "Skopiuj do schowka jako PNG"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "Skopiuj do schowka"
  }
};
var encrypted = {
  tooltip: "Twoje rysunki s\u0105 zabezpieczone szyfrowaniem end-to-end, tak wi\u0119c nawet w Excalidraw nie jeste\u015Bmy w stanie zobaczy\u0107 tego co tworzysz.",
  link: "Wpis na blogu dotycz\u0105cy szyfrowania end-to-end w Excalidraw"
};
var stats = {
  angle: "K\u0105t",
  element: "Element",
  elements: "Elementy",
  height: "Wysoko\u015B\u0107",
  scene: "Scena",
  selected: "Zaznaczenie",
  storage: "Pami\u0119\u0107",
  title: "Statystyki dla nerd\xF3w",
  total: "\u0141\u0105cznie",
  version: "Wersja",
  versionCopy: "Kliknij, aby skopiowa\u0107",
  versionNotAvailable: "Wersja niedost\u0119pna",
  width: "Szeroko\u015B\u0107"
};
var toast = {
  addedToLibrary: "Dodano do biblioteki",
  copyStyles: "Skopiowano style.",
  copyToClipboard: "Skopiowano do schowka.",
  copyToClipboardAsPng: "Skopiowano {{exportSelection}} do schowka jako PNG\n({{exportColorScheme}})",
  fileSaved: "Zapisano plik.",
  fileSavedToFilename: "Zapisano jako {filename}",
  canvas: "p\u0142\xF3tno",
  selection: "zaznaczenie",
  pasteAsSingleElement: "U\u017Cyj {{shortcut}}, aby wklei\u0107 jako pojedynczy element,\nlub wklej do istniej\u0105cego edytora tekstu",
  unableToEmbed: "Osadzenie tego linku jest obecnie niedozwolone. Zg\u0142o\u015B propozycj\u0119 na portalu GitHub, aby doda\u0107\xA0go do listy dozwolonych wyj\u0105tk\xF3w",
  unrecognizedLinkFormat: 'Osadzony link ma niew\u0142a\u015Bciwy format. Spr\xF3buj wklei\u0107 ca\u0142\u0105\xA0zawarto\u015B\u0107 pola "embed" z oryginalnej strony.'
};
var colors = {
  transparent: "Przezroczysty",
  black: "Czarny",
  white: "Bia\u0142y",
  red: "Czerwony",
  pink: "R\xF3\u017Cowy",
  grape: "Winogronowy",
  violet: "Fioletowy",
  gray: "Szary",
  blue: "Niebieski",
  cyan: "Cyjanowy",
  teal: "Turkusowy",
  green: "Zielony",
  yellow: "\u017B\xF3\u0142ty",
  orange: "Pomara\u0144czowy",
  bronze: "Br\u0105zowy"
};
var welcomeScreen = {
  app: {
    center_heading: "Wszystkie dane s\u0105 zapisywane lokalnie w przegl\u0105darce.",
    center_heading_plus: "Czy zamiast tego chcesz przej\u015B\u0107 do Excalidraw+?",
    menuHint: "Eksportuj, preferencje, j\u0119zyki..."
  },
  defaults: {
    menuHint: "Eksportuj, preferencje i wi\u0119cej...",
    center_heading: "Schematy uproszczone.",
    toolbarHint: "Wybierz narz\u0119dzie i zacznij rysowa\u0107!",
    helpHint: "Skr\xF3ty klawiaturowe i pomoc"
  }
};
var colorPicker = {
  mostUsedCustomColors: "Najcz\u0119\u015Bciej u\u017Cywane kolory",
  colors: "Kolory",
  shades: "Odcienie",
  hexCode: "Kod HEX",
  noShades: "Brak dost\u0119pnych odcieni dla tego koloru"
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "Eksportuj jako obraz",
      button: "Eksportuj jako obraz",
      description: "Eksportuj zawarto\u015B\u0107 sceny jako obraz z mo\u017Cliwo\u015Bci\u0105\xA0importowania."
    },
    saveToDisk: {
      title: "Zapisz na dysku",
      button: "Zapisz na dysku",
      description: "Eksportuj zawarto\u015B\u0107 sceny jako plik z mo\u017Cliwo\u015Bci\u0105\xA0importowania."
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "Eksportuj do Excalidraw+",
      description: "Zapisz scen\u0119 do\xA0swojego obszaru roboczego Excalidraw+."
    }
  },
  modal: {
    loadFromFile: {
      title: "Wczytaj z pliku",
      button: "Wczytaj z pliku",
      description: "Wczytanie z pliku <bold>nadpisze istniej\u0105c\u0105 zawarto\u015B\u0107</bold>.<br></br>Mo\u017Cesz najpierw utworzy\u0107 kopi\u0119 zapasow\u0105 swojego rysunku, u\u017Cywaj\u0105c jednej z poni\u017Cszych opcji."
    },
    shareableLink: {
      title: "Wczytaj z linku",
      button: "Nadpisz moj\u0105\xA0zawarto\u015B\u0107",
      description: "Wczytanie zewn\u0119trznego pliku <bold>nadpisze istniej\u0105c\u0105 zawarto\u015B\u0107</bold>.<br></br>Mo\u017Cesz najpierw utworzy\u0107 kopi\u0119 zapasow\u0105 swojego rysunku, u\u017Cywaj\u0105c jednej z poni\u017Cszych opcji."
    }
  }
};
var mermaid = {
  title: "Konwertuj diagram Mermaid do Excalidraw",
  button: "Wstaw",
  description: "Obecnie wspierane s\u0105 jedynie <flowchartLink>proste grafy</flowchartLink>, <sequenceLink>sekwencje</sequenceLink> i <classLink>diagramy klas</classLink>. Pozosta\u0142e typy b\u0119d\u0105\xA0wy\u015Bwietlane jako obrazy w Excalidraw.",
  syntax: "Sk\u0142adnia diagram\xF3w Mermaid",
  preview: "Podgl\u0105d"
};
var pl_PL_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  pl_PL_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=pl-PL-YJHOWAAW.js.map
