"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_coffee_coffee_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/coffee/coffee.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/coffee/coffee.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/coffee/coffee.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#%\\^\\&\\*\\(\\)\\=\\$\\-\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    blockComment: [\"###\", \"###\"],\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  ignoreCase: true,\n  tokenPostfix: \".coffee\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  regEx: /\\/(?!\\/\\/)(?:[^\\/\\\\]|\\\\.)*\\/[igm]*/,\n  keywords: [\n    \"and\",\n    \"or\",\n    \"is\",\n    \"isnt\",\n    \"not\",\n    \"on\",\n    \"yes\",\n    \"@\",\n    \"no\",\n    \"off\",\n    \"true\",\n    \"false\",\n    \"null\",\n    \"this\",\n    \"new\",\n    \"delete\",\n    \"typeof\",\n    \"in\",\n    \"instanceof\",\n    \"return\",\n    \"throw\",\n    \"break\",\n    \"continue\",\n    \"debugger\",\n    \"if\",\n    \"else\",\n    \"switch\",\n    \"for\",\n    \"while\",\n    \"do\",\n    \"try\",\n    \"catch\",\n    \"finally\",\n    \"class\",\n    \"extends\",\n    \"super\",\n    \"undefined\",\n    \"then\",\n    \"unless\",\n    \"until\",\n    \"loop\",\n    \"of\",\n    \"by\",\n    \"when\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?&%|+\\-*\\/\\^\\.,\\:]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"'$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [/\\@[a-zA-Z_]\\w*/, \"variable.predefined\"],\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            this: \"variable.predefined\",\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // whitespace\n      [/[ \\t\\r\\n]+/, \"\"],\n      // Comments\n      [/###/, \"comment\", \"@comment\"],\n      [/#.*$/, \"comment\"],\n      // regular expressions\n      [\"///\", { token: \"regexp\", next: \"@hereregexp\" }],\n      [/^(\\s*)(@regEx)/, [\"\", \"regexp\"]],\n      [/(\\()(\\s*)(@regEx)/, [\"@brackets\", \"\", \"regexp\"]],\n      [/(\\,)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\=)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\:)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\[)(\\s*)(@regEx)/, [\"@brackets\", \"\", \"regexp\"]],\n      [/(\\!)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\&)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\|)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\?)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\{)(\\s*)(@regEx)/, [\"@brackets\", \"\", \"regexp\"]],\n      [/(\\;)(\\s*)(@regEx)/, [\"\", \"\", \"regexp\"]],\n      // delimiters\n      [\n        /}/,\n        {\n          cases: {\n            \"$S2==interpolatedstring\": {\n              token: \"string\",\n              next: \"@pop\"\n            },\n            \"@default\": \"@brackets\"\n          }\n        }\n      ],\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d+[eE]([\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d+\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/0[0-7]+(?!\\d)/, \"number.octal\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[,.]/, \"delimiter\"],\n      // strings:\n      [/\"\"\"/, \"string\", '@herestring.\"\"\"'],\n      [/'''/, \"string\", \"@herestring.'''\"],\n      [\n        /\"/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: '@string.\"' }\n          }\n        }\n      ],\n      [\n        /'/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: \"@string.'\" }\n          }\n        }\n      ]\n    ],\n    string: [\n      [/[^\"'\\#\\\\]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\./, \"string.escape.invalid\"],\n      [/\\./, \"string.escape.invalid\"],\n      [\n        /#{/,\n        {\n          cases: {\n            '$S2==\"': {\n              token: \"string\",\n              next: \"root.interpolatedstring\"\n            },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/#/, \"string\"]\n    ],\n    herestring: [\n      [\n        /(\"\"\"|''')/,\n        {\n          cases: {\n            \"$1==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/[^#\\\\'\"]+/, \"string\"],\n      [/['\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\./, \"string.escape.invalid\"],\n      [/#{/, { token: \"string.quote\", next: \"root.interpolatedstring\" }],\n      [/#/, \"string\"]\n    ],\n    comment: [\n      [/[^#]+/, \"comment\"],\n      [/###/, \"comment\", \"@pop\"],\n      [/#/, \"comment\"]\n    ],\n    hereregexp: [\n      [/[^\\\\\\/#]+/, \"regexp\"],\n      [/\\\\./, \"regexp\"],\n      [/#.*$/, \"comment\"],\n      [\"///[igm]*\", { token: \"regexp\", next: \"@pop\" }],\n      [/\\//, \"regexp\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/coffee/coffee.js\n"));

/***/ })

}]);