"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_ruby_ruby_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/ruby/ruby.js":
/*!*************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/ruby/ruby.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/ruby/ruby.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"=begin\", \"=end\"]\n  },\n  brackets: [\n    [\"(\", \")\"],\n    [\"{\", \"}\"],\n    [\"[\", \"]\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  indentationRules: {\n    increaseIndentPattern: new RegExp(\n      `^\\\\s*((begin|class|(private|protected)\\\\s+def|def|else|elsif|ensure|for|if|module|rescue|unless|until|when|while|case)|([^#]*\\\\sdo\\\\b)|([^#]*=\\\\s*(case|if|unless)))\\\\b([^#\\\\{;]|(\"|'|/).*\\\\4)*(#.*)?$`\n    ),\n    decreaseIndentPattern: new RegExp(\n      \"^\\\\s*([}\\\\]]([,)]?\\\\s*(#|$)|\\\\.[a-zA-Z_]\\\\w*\\\\b)|(end|rescue|ensure|else|elsif|when)\\\\b)\"\n    )\n  }\n};\nvar language = {\n  tokenPostfix: \".ruby\",\n  keywords: [\n    \"__LINE__\",\n    \"__ENCODING__\",\n    \"__FILE__\",\n    \"BEGIN\",\n    \"END\",\n    \"alias\",\n    \"and\",\n    \"begin\",\n    \"break\",\n    \"case\",\n    \"class\",\n    \"def\",\n    \"defined?\",\n    \"do\",\n    \"else\",\n    \"elsif\",\n    \"end\",\n    \"ensure\",\n    \"for\",\n    \"false\",\n    \"if\",\n    \"in\",\n    \"module\",\n    \"next\",\n    \"nil\",\n    \"not\",\n    \"or\",\n    \"redo\",\n    \"rescue\",\n    \"retry\",\n    \"return\",\n    \"self\",\n    \"super\",\n    \"then\",\n    \"true\",\n    \"undef\",\n    \"unless\",\n    \"until\",\n    \"when\",\n    \"while\",\n    \"yield\"\n  ],\n  keywordops: [\"::\", \"..\", \"...\", \"?\", \":\", \"=>\"],\n  builtins: [\n    \"require\",\n    \"public\",\n    \"private\",\n    \"include\",\n    \"extend\",\n    \"attr_reader\",\n    \"protected\",\n    \"private_class_method\",\n    \"protected_class_method\",\n    \"new\"\n  ],\n  // these are closed by 'end' (if, while and until are handled separately)\n  declarations: [\n    \"module\",\n    \"class\",\n    \"def\",\n    \"case\",\n    \"do\",\n    \"begin\",\n    \"for\",\n    \"if\",\n    \"while\",\n    \"until\",\n    \"unless\"\n  ],\n  linedecls: [\"def\", \"case\", \"do\", \"begin\", \"for\", \"if\", \"while\", \"until\", \"unless\"],\n  operators: [\n    \"^\",\n    \"&\",\n    \"|\",\n    \"<=>\",\n    \"==\",\n    \"===\",\n    \"!~\",\n    \"=~\",\n    \">\",\n    \">=\",\n    \"<\",\n    \"<=\",\n    \"<<\",\n    \">>\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"**\",\n    \"~\",\n    \"+@\",\n    \"-@\",\n    \"[]\",\n    \"[]=\",\n    \"`\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"**=\",\n    \"/=\",\n    \"^=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\",\n    \"&=\",\n    \"&&=\",\n    \"||=\",\n    \"|=\"\n  ],\n  brackets: [\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" }\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%\\.]+/,\n  // escape sequences\n  escape: /(?:[abefnrstv\\\\\"'\\n\\r]|[0-7]{1,3}|x[0-9A-Fa-f]{1,2}|u[0-9A-Fa-f]{4})/,\n  escapes: /\\\\(?:C\\-(@escape|.)|c(@escape|.)|@escape)/,\n  decpart: /\\d(_?\\d)*/,\n  decimal: /0|@decpart/,\n  delim: /[^a-zA-Z0-9\\s\\n\\r]/,\n  heredelim: /(?:\\w+|'[^']*'|\"[^\"]*\"|`[^`]*`)/,\n  regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n  regexpesc: /\\\\(?:[AzZbBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})?/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    // Main entry.\n    // root.<decl> where decl is the current opening declaration (like 'class')\n    root: [\n      // identifiers and keywords\n      // most complexity here is due to matching 'end' correctly with declarations.\n      // We distinguish a declaration that comes first on a line, versus declarations further on a line (which are most likey modifiers)\n      [\n        /^(\\s*)([a-z_]\\w*[!?=]?)/,\n        [\n          \"white\",\n          {\n            cases: {\n              \"for|until|while\": {\n                token: \"keyword.$2\",\n                next: \"@dodecl.$2\"\n              },\n              \"@declarations\": {\n                token: \"keyword.$2\",\n                next: \"@root.$2\"\n              },\n              end: { token: \"keyword.$S2\", next: \"@pop\" },\n              \"@keywords\": \"keyword\",\n              \"@builtins\": \"predefined\",\n              \"@default\": \"identifier\"\n            }\n          }\n        ]\n      ],\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            \"if|unless|while|until\": {\n              token: \"keyword.$0x\",\n              next: \"@modifier.$0x\"\n            },\n            for: { token: \"keyword.$2\", next: \"@dodecl.$2\" },\n            \"@linedecls\": { token: \"keyword.$0\", next: \"@root.$0\" },\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[A-Z][\\w]*[!?=]?/, \"constructor.identifier\"],\n      // constant\n      [/\\$[\\w]*/, \"global.constant\"],\n      // global\n      [/@[\\w]*/, \"namespace.instance.identifier\"],\n      // instance\n      [/@@@[\\w]*/, \"namespace.class.identifier\"],\n      // class\n      // here document\n      [/<<[-~](@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      [/[ \\t\\r\\n]+<<(@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      [/^<<(@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      // whitespace\n      { include: \"@whitespace\" },\n      // strings\n      [/\"/, { token: \"string.d.delim\", next: '@dstring.d.\"' }],\n      [/'/, { token: \"string.sq.delim\", next: \"@sstring.sq\" }],\n      // % literals. For efficiency, rematch in the 'pstring' state\n      [/%([rsqxwW]|Q?)/, { token: \"@rematch\", next: \"pstring\" }],\n      // commands and symbols\n      [/`/, { token: \"string.x.delim\", next: \"@dstring.x.`\" }],\n      [/:(\\w|[$@])\\w*[!?=]?/, \"string.s\"],\n      [/:\"/, { token: \"string.s.delim\", next: '@dstring.s.\"' }],\n      [/:'/, { token: \"string.s.delim\", next: \"@sstring.s\" }],\n      // regular expressions. Lookahead for a (not escaped) closing forwardslash on the same line\n      [/\\/(?=(\\\\\\/|[^\\/\\n])+\\/)/, { token: \"regexp.delim\", next: \"@regexp\" }],\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@keywordops\": \"keyword\",\n            \"@operators\": \"operator\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/[;,]/, \"delimiter\"],\n      // numbers\n      [/0[xX][0-9a-fA-F](_?[0-9a-fA-F])*/, \"number.hex\"],\n      [/0[_oO][0-7](_?[0-7])*/, \"number.octal\"],\n      [/0[bB][01](_?[01])*/, \"number.binary\"],\n      [/0[dD]@decpart/, \"number\"],\n      [\n        /@decimal((\\.@decpart)?([eE][\\-+]?@decpart)?)/,\n        {\n          cases: {\n            $1: \"number.float\",\n            \"@default\": \"number\"\n          }\n        }\n      ]\n    ],\n    // used to not treat a 'do' as a block opener if it occurs on the same\n    // line as a 'do' statement: 'while|until|for'\n    // dodecl.<decl> where decl is the declarations started, like 'while'\n    dodecl: [\n      [/^/, { token: \"\", switchTo: \"@root.$S2\" }],\n      // get out of do-skipping mode on a new line\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            // end on same line\n            do: { token: \"keyword\", switchTo: \"@root.$S2\" },\n            // do on same line: not an open bracket here\n            \"@linedecls\": {\n              token: \"@rematch\",\n              switchTo: \"@root.$S2\"\n            },\n            // other declaration on same line: rematch\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@root\" }\n    ],\n    // used to prevent potential modifiers ('if|until|while|unless') to match\n    // with 'end' keywords.\n    // modifier.<decl>x where decl is the declaration starter, like 'if'\n    modifier: [\n      [/^/, \"\", \"@pop\"],\n      // it was a modifier: get out of modifier mode on a new line\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            // end on same line\n            \"then|else|elsif|do\": {\n              token: \"keyword\",\n              switchTo: \"@root.$S2\"\n            },\n            // real declaration and not a modifier\n            \"@linedecls\": {\n              token: \"@rematch\",\n              switchTo: \"@root.$S2\"\n            },\n            // other declaration => not a modifier\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@root\" }\n    ],\n    // single quote strings (also used for symbols)\n    // sstring.<kind>  where kind is 'sq' (single quote) or 's' (symbol)\n    sstring: [\n      [/[^\\\\']+/, \"string.$S2\"],\n      [/\\\\\\\\|\\\\'|\\\\$/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.invalid\"],\n      [/'/, { token: \"string.$S2.delim\", next: \"@pop\" }]\n    ],\n    // double quoted \"string\".\n    // dstring.<kind>.<delim> where kind is 'd' (double quoted), 'x' (command), or 's' (symbol)\n    // and delim is the ending delimiter (\" or `)\n    dstring: [\n      [/[^\\\\`\"#]+/, \"string.$S2\"],\n      [/#/, \"string.$S2.escape\", \"@interpolated\"],\n      [/\\\\$/, \"string.$S2.escape\"],\n      [/@escapes/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.escape.invalid\"],\n      [\n        /[`\"]/,\n        {\n          cases: {\n            \"$#==$S3\": { token: \"string.$S2.delim\", next: \"@pop\" },\n            \"@default\": \"string.$S2\"\n          }\n        }\n      ]\n    ],\n    // literal documents\n    // heredoc.<close> where close is the closing delimiter\n    heredoc: [\n      [\n        /^(\\s*)(@heredelim)$/,\n        {\n          cases: {\n            \"$2==$S2\": [\"string.heredoc\", { token: \"string.heredoc.delimiter\", next: \"@pop\" }],\n            \"@default\": [\"string.heredoc\", \"string.heredoc\"]\n          }\n        }\n      ],\n      [/.*/, \"string.heredoc\"]\n    ],\n    // interpolated sequence\n    interpolated: [\n      [/\\$\\w*/, \"global.constant\", \"@pop\"],\n      [/@\\w*/, \"namespace.class.identifier\", \"@pop\"],\n      [/@@@\\w*/, \"namespace.instance.identifier\", \"@pop\"],\n      [\n        /[{]/,\n        {\n          token: \"string.escape.curly\",\n          switchTo: \"@interpolated_compound\"\n        }\n      ],\n      [\"\", \"\", \"@pop\"]\n      // just a # is interpreted as a #\n    ],\n    // any code\n    interpolated_compound: [\n      [/[}]/, { token: \"string.escape.curly\", next: \"@pop\" }],\n      { include: \"@root\" }\n    ],\n    // %r quoted regexp\n    // pregexp.<open>.<close> where open/close are the open/close delimiter\n    pregexp: [\n      { include: \"@whitespace\" },\n      // turns out that you can quote using regex control characters, aargh!\n      // for example; %r|kgjgaj| is ok (even though | is used for alternation)\n      // so, we need to match those first\n      [\n        /[^\\(\\{\\[\\\\]/,\n        {\n          cases: {\n            \"$#==$S3\": { token: \"regexp.delim\", next: \"@pop\" },\n            \"$#==$S2\": { token: \"regexp.delim\", next: \"@push\" },\n            // nested delimiters are allowed..\n            \"~[)}\\\\]]\": \"@brackets.regexp.escape.control\",\n            \"~@regexpctl\": \"regexp.escape.control\",\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexcontrol\" }\n    ],\n    // We match regular expression quite precisely\n    regexp: [\n      { include: \"@regexcontrol\" },\n      [/[^\\\\\\/]/, \"regexp\"],\n      [\"/[ixmp]*\", { token: \"regexp.delim\" }, \"@pop\"]\n    ],\n    regexcontrol: [\n      [\n        /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n        [\n          \"@brackets.regexp.escape.control\",\n          \"regexp.escape.control\",\n          \"@brackets.regexp.escape.control\"\n        ]\n      ],\n      [\n        /(\\[)(\\^?)/,\n        [\"@brackets.regexp.escape.control\", { token: \"regexp.escape.control\", next: \"@regexrange\" }]\n      ],\n      [/(\\()(\\?[:=!])/, [\"@brackets.regexp.escape.control\", \"regexp.escape.control\"]],\n      [/\\(\\?#/, { token: \"regexp.escape.control\", next: \"@regexpcomment\" }],\n      [/[()]/, \"@brackets.regexp.escape.control\"],\n      [/@regexpctl/, \"regexp.escape.control\"],\n      [/\\\\$/, \"regexp.escape\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/\\\\\\./, \"regexp.invalid\"],\n      [/#/, \"regexp.escape\", \"@interpolated\"]\n    ],\n    regexrange: [\n      [/-/, \"regexp.escape.control\"],\n      [/\\^/, \"regexp.invalid\"],\n      [/\\\\$/, \"regexp.escape\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/[^\\]]/, \"regexp\"],\n      [/\\]/, \"@brackets.regexp.escape.control\", \"@pop\"]\n    ],\n    regexpcomment: [\n      [/[^)]+/, \"comment\"],\n      [/\\)/, { token: \"regexp.escape.control\", next: \"@pop\" }]\n    ],\n    // % quoted strings\n    // A bit repetitive since we need to often special case the kind of ending delimiter\n    pstring: [\n      [/%([qws])\\(/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.(.)\" }],\n      [/%([qws])\\[/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.[.]\" }],\n      [/%([qws])\\{/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.{.}\" }],\n      [/%([qws])</, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.<.>\" }],\n      [/%([qws])(@delim)/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.$2.$2\" }],\n      [/%r\\(/, { token: \"regexp.delim\", switchTo: \"@pregexp.(.)\" }],\n      [/%r\\[/, { token: \"regexp.delim\", switchTo: \"@pregexp.[.]\" }],\n      [/%r\\{/, { token: \"regexp.delim\", switchTo: \"@pregexp.{.}\" }],\n      [/%r</, { token: \"regexp.delim\", switchTo: \"@pregexp.<.>\" }],\n      [/%r(@delim)/, { token: \"regexp.delim\", switchTo: \"@pregexp.$1.$1\" }],\n      [/%(x|W|Q?)\\(/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.(.)\" }],\n      [/%(x|W|Q?)\\[/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.[.]\" }],\n      [/%(x|W|Q?)\\{/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.{.}\" }],\n      [/%(x|W|Q?)</, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.<.>\" }],\n      [/%(x|W|Q?)(@delim)/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.$2.$2\" }],\n      [/%([rqwsxW]|Q?)./, { token: \"invalid\", next: \"@pop\" }],\n      // recover\n      [/./, { token: \"invalid\", next: \"@pop\" }]\n      // recover\n    ],\n    // non-expanded quoted string.\n    // qstring.<kind>.<open>.<close>\n    //  kind = q|w|s  (single quote, array, symbol)\n    //  open = open delimiter\n    //  close = close delimiter\n    qstring: [\n      [/\\\\$/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.escape\"],\n      [\n        /./,\n        {\n          cases: {\n            \"$#==$S4\": { token: \"string.$S2.delim\", next: \"@pop\" },\n            \"$#==$S3\": { token: \"string.$S2.delim\", next: \"@push\" },\n            // nested delimiters are allowed..\n            \"@default\": \"string.$S2\"\n          }\n        }\n      ]\n    ],\n    // expanded quoted string.\n    // qqstring.<kind>.<open>.<close>\n    //  kind = Q|W|x  (double quote, array, command)\n    //  open = open delimiter\n    //  close = close delimiter\n    qqstring: [[/#/, \"string.$S2.escape\", \"@interpolated\"], { include: \"@qstring\" }],\n    // whitespace & comments\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/^\\s*=begin\\b/, \"comment\", \"@comment\"],\n      [/#.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^=]+/, \"comment\"],\n      [/^\\s*=begin\\b/, \"comment.invalid\"],\n      // nested comment\n      [/^\\s*=end\\b.*/, \"comment\", \"@pop\"],\n      [/[=]/, \"comment\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbW9uYWNvLWVkaXRvci9lc20vdnMvYmFzaWMtbGFuZ3VhZ2VzL3J1YnkvcnVieS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7O0FBR0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsT0FBTyxLQUFLO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsTUFBTSxRQUFRLFlBQVksR0FBRztBQUM3QixNQUFNLHVCQUF1QjtBQUM3QixNQUFNLHVCQUF1QjtBQUM3QixNQUFNLHVCQUF1QjtBQUM3QixNQUFNO0FBQ047QUFDQTtBQUNBLE1BQU0sUUFBUSxZQUFZLEdBQUc7QUFDN0IsTUFBTSx1QkFBdUI7QUFDN0IsTUFBTSx1QkFBdUI7QUFDN0IsTUFBTSx1QkFBdUI7QUFDN0IsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLHNMQUFzTDtBQUN0TDtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSx1REFBdUQ7QUFDN0QsTUFBTSxRQUFRLFlBQVksNkJBQTZCO0FBQ3ZELE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QyxJQUFJLGNBQWMsSUFBSSxjQUFjLEVBQUU7QUFDOUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQiwwRUFBMEUsRUFBRSxjQUFjLEVBQUU7QUFDNUY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2YscUJBQXFCLG9DQUFvQztBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLG1CQUFtQix5Q0FBeUM7QUFDNUQsNEJBQTRCLHVDQUF1QztBQUNuRSxtQkFBbUIsb0NBQW9DO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyx3REFBd0Q7QUFDekYsdUNBQXVDLHdEQUF3RDtBQUMvRiw4QkFBOEIsd0RBQXdEO0FBQ3RGO0FBQ0EsUUFBUSx3QkFBd0I7QUFDaEM7QUFDQSxjQUFjLCtDQUErQztBQUM3RCxjQUFjLCtDQUErQztBQUM3RDtBQUNBLDJCQUEyQixvQ0FBb0M7QUFDL0Q7QUFDQSxjQUFjLCtDQUErQztBQUM3RDtBQUNBLGVBQWUsK0NBQStDO0FBQzlELGVBQWUsNkNBQTZDO0FBQzVEO0FBQ0Esb0NBQW9DLHdDQUF3QztBQUM1RTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsa0NBQWtDO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsb0NBQW9DO0FBQ3ZEO0FBQ0Esa0JBQWtCLHlDQUF5QztBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLG9DQUFvQztBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMseUNBQXlDO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIseUNBQXlDO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxpREFBaUQ7QUFDN0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSxNQUFNLDRDQUE0QztBQUM1RCxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLHdCQUF3QjtBQUNoQztBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQSx5QkFBeUIscUNBQXFDO0FBQzlELHlCQUF5QixzQ0FBc0M7QUFDL0Q7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQSxRQUFRLDBCQUEwQjtBQUNsQztBQUNBLHFCQUFxQix1QkFBdUI7QUFDNUM7QUFDQTtBQUNBO0FBQ0EsWUFBWSxrQkFBa0I7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE4QyxxREFBcUQ7QUFDbkc7QUFDQTtBQUNBLGtCQUFrQix3REFBd0Q7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsOENBQThDO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHVEQUF1RDtBQUM5RSx1QkFBdUIsdURBQXVEO0FBQzlFLGtCQUFrQixLQUFLLGtEQUFrRCxFQUFFLEdBQUc7QUFDOUUsc0JBQXNCLHVEQUF1RDtBQUM3RSw2QkFBNkIseURBQXlEO0FBQ3RGLGlCQUFpQixpREFBaUQ7QUFDbEUsaUJBQWlCLGlEQUFpRDtBQUNsRSxZQUFZLEtBQUssNENBQTRDLEVBQUUsR0FBRztBQUNsRSxnQkFBZ0IsaURBQWlEO0FBQ2pFLHVCQUF1QixtREFBbUQ7QUFDMUUsd0JBQXdCLHdEQUF3RDtBQUNoRix3QkFBd0Isd0RBQXdEO0FBQ2hGLG1CQUFtQixLQUFLLG1EQUFtRCxFQUFFLEdBQUc7QUFDaEYsdUJBQXVCLHdEQUF3RDtBQUMvRSw4QkFBOEIsMERBQTBEO0FBQ3hGLDRCQUE0QixnQ0FBZ0M7QUFDNUQ7QUFDQSxjQUFjLGdDQUFnQztBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHlDQUF5QztBQUNsRSx5QkFBeUIsMENBQTBDO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4REFBOEQscUJBQXFCO0FBQ25GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUlFIiwic291cmNlcyI6WyJDOlxcUHJvamVjdFxccmVhbGNvZGVcXG5vZGVfbW9kdWxlc1xcbW9uYWNvLWVkaXRvclxcZXNtXFx2c1xcYmFzaWMtbGFuZ3VhZ2VzXFxydWJ5XFxydWJ5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi4gQWxsIHJpZ2h0cyByZXNlcnZlZC5cbiAqIFZlcnNpb246IDAuNTIuMig0MDQ1NDViZGVkMWRmNmZmYTQxZWEwYWY0ZThkZGIyMTkwMThjNmMxKVxuICogUmVsZWFzZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlXG4gKiBodHRwczovL2dpdGh1Yi5jb20vbWljcm9zb2Z0L21vbmFjby1lZGl0b3IvYmxvYi9tYWluL0xJQ0VOU0UudHh0XG4gKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuXG4vLyBzcmMvYmFzaWMtbGFuZ3VhZ2VzL3J1YnkvcnVieS50c1xudmFyIGNvbmYgPSB7XG4gIGNvbW1lbnRzOiB7XG4gICAgbGluZUNvbW1lbnQ6IFwiI1wiLFxuICAgIGJsb2NrQ29tbWVudDogW1wiPWJlZ2luXCIsIFwiPWVuZFwiXVxuICB9LFxuICBicmFja2V0czogW1xuICAgIFtcIihcIiwgXCIpXCJdLFxuICAgIFtcIntcIiwgXCJ9XCJdLFxuICAgIFtcIltcIiwgXCJdXCJdXG4gIF0sXG4gIGF1dG9DbG9zaW5nUGFpcnM6IFtcbiAgICB7IG9wZW46IFwie1wiLCBjbG9zZTogXCJ9XCIgfSxcbiAgICB7IG9wZW46IFwiW1wiLCBjbG9zZTogXCJdXCIgfSxcbiAgICB7IG9wZW46IFwiKFwiLCBjbG9zZTogXCIpXCIgfSxcbiAgICB7IG9wZW46ICdcIicsIGNsb3NlOiAnXCInIH0sXG4gICAgeyBvcGVuOiBcIidcIiwgY2xvc2U6IFwiJ1wiIH1cbiAgXSxcbiAgc3Vycm91bmRpbmdQYWlyczogW1xuICAgIHsgb3BlbjogXCJ7XCIsIGNsb3NlOiBcIn1cIiB9LFxuICAgIHsgb3BlbjogXCJbXCIsIGNsb3NlOiBcIl1cIiB9LFxuICAgIHsgb3BlbjogXCIoXCIsIGNsb3NlOiBcIilcIiB9LFxuICAgIHsgb3BlbjogJ1wiJywgY2xvc2U6ICdcIicgfSxcbiAgICB7IG9wZW46IFwiJ1wiLCBjbG9zZTogXCInXCIgfVxuICBdLFxuICBpbmRlbnRhdGlvblJ1bGVzOiB7XG4gICAgaW5jcmVhc2VJbmRlbnRQYXR0ZXJuOiBuZXcgUmVnRXhwKFxuICAgICAgYF5cXFxccyooKGJlZ2lufGNsYXNzfChwcml2YXRlfHByb3RlY3RlZClcXFxccytkZWZ8ZGVmfGVsc2V8ZWxzaWZ8ZW5zdXJlfGZvcnxpZnxtb2R1bGV8cmVzY3VlfHVubGVzc3x1bnRpbHx3aGVufHdoaWxlfGNhc2UpfChbXiNdKlxcXFxzZG9cXFxcYil8KFteI10qPVxcXFxzKihjYXNlfGlmfHVubGVzcykpKVxcXFxiKFteI1xcXFx7O118KFwifCd8LykuKlxcXFw0KSooIy4qKT8kYFxuICAgICksXG4gICAgZGVjcmVhc2VJbmRlbnRQYXR0ZXJuOiBuZXcgUmVnRXhwKFxuICAgICAgXCJeXFxcXHMqKFt9XFxcXF1dKFssKV0/XFxcXHMqKCN8JCl8XFxcXC5bYS16QS1aX11cXFxcdypcXFxcYil8KGVuZHxyZXNjdWV8ZW5zdXJlfGVsc2V8ZWxzaWZ8d2hlbilcXFxcYilcIlxuICAgIClcbiAgfVxufTtcbnZhciBsYW5ndWFnZSA9IHtcbiAgdG9rZW5Qb3N0Zml4OiBcIi5ydWJ5XCIsXG4gIGtleXdvcmRzOiBbXG4gICAgXCJfX0xJTkVfX1wiLFxuICAgIFwiX19FTkNPRElOR19fXCIsXG4gICAgXCJfX0ZJTEVfX1wiLFxuICAgIFwiQkVHSU5cIixcbiAgICBcIkVORFwiLFxuICAgIFwiYWxpYXNcIixcbiAgICBcImFuZFwiLFxuICAgIFwiYmVnaW5cIixcbiAgICBcImJyZWFrXCIsXG4gICAgXCJjYXNlXCIsXG4gICAgXCJjbGFzc1wiLFxuICAgIFwiZGVmXCIsXG4gICAgXCJkZWZpbmVkP1wiLFxuICAgIFwiZG9cIixcbiAgICBcImVsc2VcIixcbiAgICBcImVsc2lmXCIsXG4gICAgXCJlbmRcIixcbiAgICBcImVuc3VyZVwiLFxuICAgIFwiZm9yXCIsXG4gICAgXCJmYWxzZVwiLFxuICAgIFwiaWZcIixcbiAgICBcImluXCIsXG4gICAgXCJtb2R1bGVcIixcbiAgICBcIm5leHRcIixcbiAgICBcIm5pbFwiLFxuICAgIFwibm90XCIsXG4gICAgXCJvclwiLFxuICAgIFwicmVkb1wiLFxuICAgIFwicmVzY3VlXCIsXG4gICAgXCJyZXRyeVwiLFxuICAgIFwicmV0dXJuXCIsXG4gICAgXCJzZWxmXCIsXG4gICAgXCJzdXBlclwiLFxuICAgIFwidGhlblwiLFxuICAgIFwidHJ1ZVwiLFxuICAgIFwidW5kZWZcIixcbiAgICBcInVubGVzc1wiLFxuICAgIFwidW50aWxcIixcbiAgICBcIndoZW5cIixcbiAgICBcIndoaWxlXCIsXG4gICAgXCJ5aWVsZFwiXG4gIF0sXG4gIGtleXdvcmRvcHM6IFtcIjo6XCIsIFwiLi5cIiwgXCIuLi5cIiwgXCI/XCIsIFwiOlwiLCBcIj0+XCJdLFxuICBidWlsdGluczogW1xuICAgIFwicmVxdWlyZVwiLFxuICAgIFwicHVibGljXCIsXG4gICAgXCJwcml2YXRlXCIsXG4gICAgXCJpbmNsdWRlXCIsXG4gICAgXCJleHRlbmRcIixcbiAgICBcImF0dHJfcmVhZGVyXCIsXG4gICAgXCJwcm90ZWN0ZWRcIixcbiAgICBcInByaXZhdGVfY2xhc3NfbWV0aG9kXCIsXG4gICAgXCJwcm90ZWN0ZWRfY2xhc3NfbWV0aG9kXCIsXG4gICAgXCJuZXdcIlxuICBdLFxuICAvLyB0aGVzZSBhcmUgY2xvc2VkIGJ5ICdlbmQnIChpZiwgd2hpbGUgYW5kIHVudGlsIGFyZSBoYW5kbGVkIHNlcGFyYXRlbHkpXG4gIGRlY2xhcmF0aW9uczogW1xuICAgIFwibW9kdWxlXCIsXG4gICAgXCJjbGFzc1wiLFxuICAgIFwiZGVmXCIsXG4gICAgXCJjYXNlXCIsXG4gICAgXCJkb1wiLFxuICAgIFwiYmVnaW5cIixcbiAgICBcImZvclwiLFxuICAgIFwiaWZcIixcbiAgICBcIndoaWxlXCIsXG4gICAgXCJ1bnRpbFwiLFxuICAgIFwidW5sZXNzXCJcbiAgXSxcbiAgbGluZWRlY2xzOiBbXCJkZWZcIiwgXCJjYXNlXCIsIFwiZG9cIiwgXCJiZWdpblwiLCBcImZvclwiLCBcImlmXCIsIFwid2hpbGVcIiwgXCJ1bnRpbFwiLCBcInVubGVzc1wiXSxcbiAgb3BlcmF0b3JzOiBbXG4gICAgXCJeXCIsXG4gICAgXCImXCIsXG4gICAgXCJ8XCIsXG4gICAgXCI8PT5cIixcbiAgICBcIj09XCIsXG4gICAgXCI9PT1cIixcbiAgICBcIiF+XCIsXG4gICAgXCI9flwiLFxuICAgIFwiPlwiLFxuICAgIFwiPj1cIixcbiAgICBcIjxcIixcbiAgICBcIjw9XCIsXG4gICAgXCI8PFwiLFxuICAgIFwiPj5cIixcbiAgICBcIitcIixcbiAgICBcIi1cIixcbiAgICBcIipcIixcbiAgICBcIi9cIixcbiAgICBcIiVcIixcbiAgICBcIioqXCIsXG4gICAgXCJ+XCIsXG4gICAgXCIrQFwiLFxuICAgIFwiLUBcIixcbiAgICBcIltdXCIsXG4gICAgXCJbXT1cIixcbiAgICBcImBcIixcbiAgICBcIis9XCIsXG4gICAgXCItPVwiLFxuICAgIFwiKj1cIixcbiAgICBcIioqPVwiLFxuICAgIFwiLz1cIixcbiAgICBcIl49XCIsXG4gICAgXCIlPVwiLFxuICAgIFwiPDw9XCIsXG4gICAgXCI+Pj1cIixcbiAgICBcIiY9XCIsXG4gICAgXCImJj1cIixcbiAgICBcInx8PVwiLFxuICAgIFwifD1cIlxuICBdLFxuICBicmFja2V0czogW1xuICAgIHsgb3BlbjogXCIoXCIsIGNsb3NlOiBcIilcIiwgdG9rZW46IFwiZGVsaW1pdGVyLnBhcmVudGhlc2lzXCIgfSxcbiAgICB7IG9wZW46IFwie1wiLCBjbG9zZTogXCJ9XCIsIHRva2VuOiBcImRlbGltaXRlci5jdXJseVwiIH0sXG4gICAgeyBvcGVuOiBcIltcIiwgY2xvc2U6IFwiXVwiLCB0b2tlbjogXCJkZWxpbWl0ZXIuc3F1YXJlXCIgfVxuICBdLFxuICAvLyB3ZSBpbmNsdWRlIHRoZXNlIGNvbW1vbiByZWd1bGFyIGV4cHJlc3Npb25zXG4gIHN5bWJvbHM6IC9bPT48IX4/OiZ8K1xcLSpcXC9cXF4lXFwuXSsvLFxuICAvLyBlc2NhcGUgc2VxdWVuY2VzXG4gIGVzY2FwZTogLyg/OlthYmVmbnJzdHZcXFxcXCInXFxuXFxyXXxbMC03XXsxLDN9fHhbMC05QS1GYS1mXXsxLDJ9fHVbMC05QS1GYS1mXXs0fSkvLFxuICBlc2NhcGVzOiAvXFxcXCg/OkNcXC0oQGVzY2FwZXwuKXxjKEBlc2NhcGV8Lil8QGVzY2FwZSkvLFxuICBkZWNwYXJ0OiAvXFxkKF8/XFxkKSovLFxuICBkZWNpbWFsOiAvMHxAZGVjcGFydC8sXG4gIGRlbGltOiAvW15hLXpBLVowLTlcXHNcXG5cXHJdLyxcbiAgaGVyZWRlbGltOiAvKD86XFx3K3wnW14nXSonfFwiW15cIl0qXCJ8YFteYF0qYCkvLFxuICByZWdleHBjdGw6IC9bKCl7fVxcW1xcXVxcJFxcXnxcXC0qKz9cXC5dLyxcbiAgcmVnZXhwZXNjOiAvXFxcXCg/OltBelpiQmREZm5yc3R2d1duMFxcXFxcXC9dfEByZWdleHBjdGx8Y1tBLVpdfHhbMC05YS1mQS1GXXsyfXx1WzAtOWEtZkEtRl17NH0pPy8sXG4gIC8vIFRoZSBtYWluIHRva2VuaXplciBmb3Igb3VyIGxhbmd1YWdlc1xuICB0b2tlbml6ZXI6IHtcbiAgICAvLyBNYWluIGVudHJ5LlxuICAgIC8vIHJvb3QuPGRlY2w+IHdoZXJlIGRlY2wgaXMgdGhlIGN1cnJlbnQgb3BlbmluZyBkZWNsYXJhdGlvbiAobGlrZSAnY2xhc3MnKVxuICAgIHJvb3Q6IFtcbiAgICAgIC8vIGlkZW50aWZpZXJzIGFuZCBrZXl3b3Jkc1xuICAgICAgLy8gbW9zdCBjb21wbGV4aXR5IGhlcmUgaXMgZHVlIHRvIG1hdGNoaW5nICdlbmQnIGNvcnJlY3RseSB3aXRoIGRlY2xhcmF0aW9ucy5cbiAgICAgIC8vIFdlIGRpc3Rpbmd1aXNoIGEgZGVjbGFyYXRpb24gdGhhdCBjb21lcyBmaXJzdCBvbiBhIGxpbmUsIHZlcnN1cyBkZWNsYXJhdGlvbnMgZnVydGhlciBvbiBhIGxpbmUgKHdoaWNoIGFyZSBtb3N0IGxpa2V5IG1vZGlmaWVycylcbiAgICAgIFtcbiAgICAgICAgL14oXFxzKikoW2Etel9dXFx3KlshPz1dPykvLFxuICAgICAgICBbXG4gICAgICAgICAgXCJ3aGl0ZVwiLFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIGNhc2VzOiB7XG4gICAgICAgICAgICAgIFwiZm9yfHVudGlsfHdoaWxlXCI6IHtcbiAgICAgICAgICAgICAgICB0b2tlbjogXCJrZXl3b3JkLiQyXCIsXG4gICAgICAgICAgICAgICAgbmV4dDogXCJAZG9kZWNsLiQyXCJcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgXCJAZGVjbGFyYXRpb25zXCI6IHtcbiAgICAgICAgICAgICAgICB0b2tlbjogXCJrZXl3b3JkLiQyXCIsXG4gICAgICAgICAgICAgICAgbmV4dDogXCJAcm9vdC4kMlwiXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIGVuZDogeyB0b2tlbjogXCJrZXl3b3JkLiRTMlwiLCBuZXh0OiBcIkBwb3BcIiB9LFxuICAgICAgICAgICAgICBcIkBrZXl3b3Jkc1wiOiBcImtleXdvcmRcIixcbiAgICAgICAgICAgICAgXCJAYnVpbHRpbnNcIjogXCJwcmVkZWZpbmVkXCIsXG4gICAgICAgICAgICAgIFwiQGRlZmF1bHRcIjogXCJpZGVudGlmaWVyXCJcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIF1cbiAgICAgIF0sXG4gICAgICBbXG4gICAgICAgIC9bYS16X11cXHcqWyE/PV0/LyxcbiAgICAgICAge1xuICAgICAgICAgIGNhc2VzOiB7XG4gICAgICAgICAgICBcImlmfHVubGVzc3x3aGlsZXx1bnRpbFwiOiB7XG4gICAgICAgICAgICAgIHRva2VuOiBcImtleXdvcmQuJDB4XCIsXG4gICAgICAgICAgICAgIG5leHQ6IFwiQG1vZGlmaWVyLiQweFwiXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZm9yOiB7IHRva2VuOiBcImtleXdvcmQuJDJcIiwgbmV4dDogXCJAZG9kZWNsLiQyXCIgfSxcbiAgICAgICAgICAgIFwiQGxpbmVkZWNsc1wiOiB7IHRva2VuOiBcImtleXdvcmQuJDBcIiwgbmV4dDogXCJAcm9vdC4kMFwiIH0sXG4gICAgICAgICAgICBlbmQ6IHsgdG9rZW46IFwia2V5d29yZC4kUzJcIiwgbmV4dDogXCJAcG9wXCIgfSxcbiAgICAgICAgICAgIFwiQGtleXdvcmRzXCI6IFwia2V5d29yZFwiLFxuICAgICAgICAgICAgXCJAYnVpbHRpbnNcIjogXCJwcmVkZWZpbmVkXCIsXG4gICAgICAgICAgICBcIkBkZWZhdWx0XCI6IFwiaWRlbnRpZmllclwiXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgWy9bQS1aXVtcXHddKlshPz1dPy8sIFwiY29uc3RydWN0b3IuaWRlbnRpZmllclwiXSxcbiAgICAgIC8vIGNvbnN0YW50XG4gICAgICBbL1xcJFtcXHddKi8sIFwiZ2xvYmFsLmNvbnN0YW50XCJdLFxuICAgICAgLy8gZ2xvYmFsXG4gICAgICBbL0BbXFx3XSovLCBcIm5hbWVzcGFjZS5pbnN0YW5jZS5pZGVudGlmaWVyXCJdLFxuICAgICAgLy8gaW5zdGFuY2VcbiAgICAgIFsvQEBAW1xcd10qLywgXCJuYW1lc3BhY2UuY2xhc3MuaWRlbnRpZmllclwiXSxcbiAgICAgIC8vIGNsYXNzXG4gICAgICAvLyBoZXJlIGRvY3VtZW50XG4gICAgICBbLzw8Wy1+XShAaGVyZWRlbGltKS4qLywgeyB0b2tlbjogXCJzdHJpbmcuaGVyZWRvYy5kZWxpbWl0ZXJcIiwgbmV4dDogXCJAaGVyZWRvYy4kMVwiIH1dLFxuICAgICAgWy9bIFxcdFxcclxcbl0rPDwoQGhlcmVkZWxpbSkuKi8sIHsgdG9rZW46IFwic3RyaW5nLmhlcmVkb2MuZGVsaW1pdGVyXCIsIG5leHQ6IFwiQGhlcmVkb2MuJDFcIiB9XSxcbiAgICAgIFsvXjw8KEBoZXJlZGVsaW0pLiovLCB7IHRva2VuOiBcInN0cmluZy5oZXJlZG9jLmRlbGltaXRlclwiLCBuZXh0OiBcIkBoZXJlZG9jLiQxXCIgfV0sXG4gICAgICAvLyB3aGl0ZXNwYWNlXG4gICAgICB7IGluY2x1ZGU6IFwiQHdoaXRlc3BhY2VcIiB9LFxuICAgICAgLy8gc3RyaW5nc1xuICAgICAgWy9cIi8sIHsgdG9rZW46IFwic3RyaW5nLmQuZGVsaW1cIiwgbmV4dDogJ0Bkc3RyaW5nLmQuXCInIH1dLFxuICAgICAgWy8nLywgeyB0b2tlbjogXCJzdHJpbmcuc3EuZGVsaW1cIiwgbmV4dDogXCJAc3N0cmluZy5zcVwiIH1dLFxuICAgICAgLy8gJSBsaXRlcmFscy4gRm9yIGVmZmljaWVuY3ksIHJlbWF0Y2ggaW4gdGhlICdwc3RyaW5nJyBzdGF0ZVxuICAgICAgWy8lKFtyc3F4d1ddfFE/KS8sIHsgdG9rZW46IFwiQHJlbWF0Y2hcIiwgbmV4dDogXCJwc3RyaW5nXCIgfV0sXG4gICAgICAvLyBjb21tYW5kcyBhbmQgc3ltYm9sc1xuICAgICAgWy9gLywgeyB0b2tlbjogXCJzdHJpbmcueC5kZWxpbVwiLCBuZXh0OiBcIkBkc3RyaW5nLnguYFwiIH1dLFxuICAgICAgWy86KFxcd3xbJEBdKVxcdypbIT89XT8vLCBcInN0cmluZy5zXCJdLFxuICAgICAgWy86XCIvLCB7IHRva2VuOiBcInN0cmluZy5zLmRlbGltXCIsIG5leHQ6ICdAZHN0cmluZy5zLlwiJyB9XSxcbiAgICAgIFsvOicvLCB7IHRva2VuOiBcInN0cmluZy5zLmRlbGltXCIsIG5leHQ6IFwiQHNzdHJpbmcuc1wiIH1dLFxuICAgICAgLy8gcmVndWxhciBleHByZXNzaW9ucy4gTG9va2FoZWFkIGZvciBhIChub3QgZXNjYXBlZCkgY2xvc2luZyBmb3J3YXJkc2xhc2ggb24gdGhlIHNhbWUgbGluZVxuICAgICAgWy9cXC8oPz0oXFxcXFxcL3xbXlxcL1xcbl0pK1xcLykvLCB7IHRva2VuOiBcInJlZ2V4cC5kZWxpbVwiLCBuZXh0OiBcIkByZWdleHBcIiB9XSxcbiAgICAgIC8vIGRlbGltaXRlcnMgYW5kIG9wZXJhdG9yc1xuICAgICAgWy9be30oKVxcW1xcXV0vLCBcIkBicmFja2V0c1wiXSxcbiAgICAgIFtcbiAgICAgICAgL0BzeW1ib2xzLyxcbiAgICAgICAge1xuICAgICAgICAgIGNhc2VzOiB7XG4gICAgICAgICAgICBcIkBrZXl3b3Jkb3BzXCI6IFwia2V5d29yZFwiLFxuICAgICAgICAgICAgXCJAb3BlcmF0b3JzXCI6IFwib3BlcmF0b3JcIixcbiAgICAgICAgICAgIFwiQGRlZmF1bHRcIjogXCJcIlxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIFsvWzssXS8sIFwiZGVsaW1pdGVyXCJdLFxuICAgICAgLy8gbnVtYmVyc1xuICAgICAgWy8wW3hYXVswLTlhLWZBLUZdKF8/WzAtOWEtZkEtRl0pKi8sIFwibnVtYmVyLmhleFwiXSxcbiAgICAgIFsvMFtfb09dWzAtN10oXz9bMC03XSkqLywgXCJudW1iZXIub2N0YWxcIl0sXG4gICAgICBbLzBbYkJdWzAxXShfP1swMV0pKi8sIFwibnVtYmVyLmJpbmFyeVwiXSxcbiAgICAgIFsvMFtkRF1AZGVjcGFydC8sIFwibnVtYmVyXCJdLFxuICAgICAgW1xuICAgICAgICAvQGRlY2ltYWwoKFxcLkBkZWNwYXJ0KT8oW2VFXVtcXC0rXT9AZGVjcGFydCk/KS8sXG4gICAgICAgIHtcbiAgICAgICAgICBjYXNlczoge1xuICAgICAgICAgICAgJDE6IFwibnVtYmVyLmZsb2F0XCIsXG4gICAgICAgICAgICBcIkBkZWZhdWx0XCI6IFwibnVtYmVyXCJcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIF1cbiAgICBdLFxuICAgIC8vIHVzZWQgdG8gbm90IHRyZWF0IGEgJ2RvJyBhcyBhIGJsb2NrIG9wZW5lciBpZiBpdCBvY2N1cnMgb24gdGhlIHNhbWVcbiAgICAvLyBsaW5lIGFzIGEgJ2RvJyBzdGF0ZW1lbnQ6ICd3aGlsZXx1bnRpbHxmb3InXG4gICAgLy8gZG9kZWNsLjxkZWNsPiB3aGVyZSBkZWNsIGlzIHRoZSBkZWNsYXJhdGlvbnMgc3RhcnRlZCwgbGlrZSAnd2hpbGUnXG4gICAgZG9kZWNsOiBbXG4gICAgICBbL14vLCB7IHRva2VuOiBcIlwiLCBzd2l0Y2hUbzogXCJAcm9vdC4kUzJcIiB9XSxcbiAgICAgIC8vIGdldCBvdXQgb2YgZG8tc2tpcHBpbmcgbW9kZSBvbiBhIG5ldyBsaW5lXG4gICAgICBbXG4gICAgICAgIC9bYS16X11cXHcqWyE/PV0/LyxcbiAgICAgICAge1xuICAgICAgICAgIGNhc2VzOiB7XG4gICAgICAgICAgICBlbmQ6IHsgdG9rZW46IFwia2V5d29yZC4kUzJcIiwgbmV4dDogXCJAcG9wXCIgfSxcbiAgICAgICAgICAgIC8vIGVuZCBvbiBzYW1lIGxpbmVcbiAgICAgICAgICAgIGRvOiB7IHRva2VuOiBcImtleXdvcmRcIiwgc3dpdGNoVG86IFwiQHJvb3QuJFMyXCIgfSxcbiAgICAgICAgICAgIC8vIGRvIG9uIHNhbWUgbGluZTogbm90IGFuIG9wZW4gYnJhY2tldCBoZXJlXG4gICAgICAgICAgICBcIkBsaW5lZGVjbHNcIjoge1xuICAgICAgICAgICAgICB0b2tlbjogXCJAcmVtYXRjaFwiLFxuICAgICAgICAgICAgICBzd2l0Y2hUbzogXCJAcm9vdC4kUzJcIlxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIC8vIG90aGVyIGRlY2xhcmF0aW9uIG9uIHNhbWUgbGluZTogcmVtYXRjaFxuICAgICAgICAgICAgXCJAa2V5d29yZHNcIjogXCJrZXl3b3JkXCIsXG4gICAgICAgICAgICBcIkBidWlsdGluc1wiOiBcInByZWRlZmluZWRcIixcbiAgICAgICAgICAgIFwiQGRlZmF1bHRcIjogXCJpZGVudGlmaWVyXCJcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIF0sXG4gICAgICB7IGluY2x1ZGU6IFwiQHJvb3RcIiB9XG4gICAgXSxcbiAgICAvLyB1c2VkIHRvIHByZXZlbnQgcG90ZW50aWFsIG1vZGlmaWVycyAoJ2lmfHVudGlsfHdoaWxlfHVubGVzcycpIHRvIG1hdGNoXG4gICAgLy8gd2l0aCAnZW5kJyBrZXl3b3Jkcy5cbiAgICAvLyBtb2RpZmllci48ZGVjbD54IHdoZXJlIGRlY2wgaXMgdGhlIGRlY2xhcmF0aW9uIHN0YXJ0ZXIsIGxpa2UgJ2lmJ1xuICAgIG1vZGlmaWVyOiBbXG4gICAgICBbL14vLCBcIlwiLCBcIkBwb3BcIl0sXG4gICAgICAvLyBpdCB3YXMgYSBtb2RpZmllcjogZ2V0IG91dCBvZiBtb2RpZmllciBtb2RlIG9uIGEgbmV3IGxpbmVcbiAgICAgIFtcbiAgICAgICAgL1thLXpfXVxcdypbIT89XT8vLFxuICAgICAgICB7XG4gICAgICAgICAgY2FzZXM6IHtcbiAgICAgICAgICAgIGVuZDogeyB0b2tlbjogXCJrZXl3b3JkLiRTMlwiLCBuZXh0OiBcIkBwb3BcIiB9LFxuICAgICAgICAgICAgLy8gZW5kIG9uIHNhbWUgbGluZVxuICAgICAgICAgICAgXCJ0aGVufGVsc2V8ZWxzaWZ8ZG9cIjoge1xuICAgICAgICAgICAgICB0b2tlbjogXCJrZXl3b3JkXCIsXG4gICAgICAgICAgICAgIHN3aXRjaFRvOiBcIkByb290LiRTMlwiXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgLy8gcmVhbCBkZWNsYXJhdGlvbiBhbmQgbm90IGEgbW9kaWZpZXJcbiAgICAgICAgICAgIFwiQGxpbmVkZWNsc1wiOiB7XG4gICAgICAgICAgICAgIHRva2VuOiBcIkByZW1hdGNoXCIsXG4gICAgICAgICAgICAgIHN3aXRjaFRvOiBcIkByb290LiRTMlwiXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgLy8gb3RoZXIgZGVjbGFyYXRpb24gPT4gbm90IGEgbW9kaWZpZXJcbiAgICAgICAgICAgIFwiQGtleXdvcmRzXCI6IFwia2V5d29yZFwiLFxuICAgICAgICAgICAgXCJAYnVpbHRpbnNcIjogXCJwcmVkZWZpbmVkXCIsXG4gICAgICAgICAgICBcIkBkZWZhdWx0XCI6IFwiaWRlbnRpZmllclwiXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgeyBpbmNsdWRlOiBcIkByb290XCIgfVxuICAgIF0sXG4gICAgLy8gc2luZ2xlIHF1b3RlIHN0cmluZ3MgKGFsc28gdXNlZCBmb3Igc3ltYm9scylcbiAgICAvLyBzc3RyaW5nLjxraW5kPiAgd2hlcmUga2luZCBpcyAnc3EnIChzaW5nbGUgcXVvdGUpIG9yICdzJyAoc3ltYm9sKVxuICAgIHNzdHJpbmc6IFtcbiAgICAgIFsvW15cXFxcJ10rLywgXCJzdHJpbmcuJFMyXCJdLFxuICAgICAgWy9cXFxcXFxcXHxcXFxcJ3xcXFxcJC8sIFwic3RyaW5nLiRTMi5lc2NhcGVcIl0sXG4gICAgICBbL1xcXFwuLywgXCJzdHJpbmcuJFMyLmludmFsaWRcIl0sXG4gICAgICBbLycvLCB7IHRva2VuOiBcInN0cmluZy4kUzIuZGVsaW1cIiwgbmV4dDogXCJAcG9wXCIgfV1cbiAgICBdLFxuICAgIC8vIGRvdWJsZSBxdW90ZWQgXCJzdHJpbmdcIi5cbiAgICAvLyBkc3RyaW5nLjxraW5kPi48ZGVsaW0+IHdoZXJlIGtpbmQgaXMgJ2QnIChkb3VibGUgcXVvdGVkKSwgJ3gnIChjb21tYW5kKSwgb3IgJ3MnIChzeW1ib2wpXG4gICAgLy8gYW5kIGRlbGltIGlzIHRoZSBlbmRpbmcgZGVsaW1pdGVyIChcIiBvciBgKVxuICAgIGRzdHJpbmc6IFtcbiAgICAgIFsvW15cXFxcYFwiI10rLywgXCJzdHJpbmcuJFMyXCJdLFxuICAgICAgWy8jLywgXCJzdHJpbmcuJFMyLmVzY2FwZVwiLCBcIkBpbnRlcnBvbGF0ZWRcIl0sXG4gICAgICBbL1xcXFwkLywgXCJzdHJpbmcuJFMyLmVzY2FwZVwiXSxcbiAgICAgIFsvQGVzY2FwZXMvLCBcInN0cmluZy4kUzIuZXNjYXBlXCJdLFxuICAgICAgWy9cXFxcLi8sIFwic3RyaW5nLiRTMi5lc2NhcGUuaW52YWxpZFwiXSxcbiAgICAgIFtcbiAgICAgICAgL1tgXCJdLyxcbiAgICAgICAge1xuICAgICAgICAgIGNhc2VzOiB7XG4gICAgICAgICAgICBcIiQjPT0kUzNcIjogeyB0b2tlbjogXCJzdHJpbmcuJFMyLmRlbGltXCIsIG5leHQ6IFwiQHBvcFwiIH0sXG4gICAgICAgICAgICBcIkBkZWZhdWx0XCI6IFwic3RyaW5nLiRTMlwiXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdXG4gICAgXSxcbiAgICAvLyBsaXRlcmFsIGRvY3VtZW50c1xuICAgIC8vIGhlcmVkb2MuPGNsb3NlPiB3aGVyZSBjbG9zZSBpcyB0aGUgY2xvc2luZyBkZWxpbWl0ZXJcbiAgICBoZXJlZG9jOiBbXG4gICAgICBbXG4gICAgICAgIC9eKFxccyopKEBoZXJlZGVsaW0pJC8sXG4gICAgICAgIHtcbiAgICAgICAgICBjYXNlczoge1xuICAgICAgICAgICAgXCIkMj09JFMyXCI6IFtcInN0cmluZy5oZXJlZG9jXCIsIHsgdG9rZW46IFwic3RyaW5nLmhlcmVkb2MuZGVsaW1pdGVyXCIsIG5leHQ6IFwiQHBvcFwiIH1dLFxuICAgICAgICAgICAgXCJAZGVmYXVsdFwiOiBbXCJzdHJpbmcuaGVyZWRvY1wiLCBcInN0cmluZy5oZXJlZG9jXCJdXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgWy8uKi8sIFwic3RyaW5nLmhlcmVkb2NcIl1cbiAgICBdLFxuICAgIC8vIGludGVycG9sYXRlZCBzZXF1ZW5jZVxuICAgIGludGVycG9sYXRlZDogW1xuICAgICAgWy9cXCRcXHcqLywgXCJnbG9iYWwuY29uc3RhbnRcIiwgXCJAcG9wXCJdLFxuICAgICAgWy9AXFx3Ki8sIFwibmFtZXNwYWNlLmNsYXNzLmlkZW50aWZpZXJcIiwgXCJAcG9wXCJdLFxuICAgICAgWy9AQEBcXHcqLywgXCJuYW1lc3BhY2UuaW5zdGFuY2UuaWRlbnRpZmllclwiLCBcIkBwb3BcIl0sXG4gICAgICBbXG4gICAgICAgIC9be10vLFxuICAgICAgICB7XG4gICAgICAgICAgdG9rZW46IFwic3RyaW5nLmVzY2FwZS5jdXJseVwiLFxuICAgICAgICAgIHN3aXRjaFRvOiBcIkBpbnRlcnBvbGF0ZWRfY29tcG91bmRcIlxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgW1wiXCIsIFwiXCIsIFwiQHBvcFwiXVxuICAgICAgLy8ganVzdCBhICMgaXMgaW50ZXJwcmV0ZWQgYXMgYSAjXG4gICAgXSxcbiAgICAvLyBhbnkgY29kZVxuICAgIGludGVycG9sYXRlZF9jb21wb3VuZDogW1xuICAgICAgWy9bfV0vLCB7IHRva2VuOiBcInN0cmluZy5lc2NhcGUuY3VybHlcIiwgbmV4dDogXCJAcG9wXCIgfV0sXG4gICAgICB7IGluY2x1ZGU6IFwiQHJvb3RcIiB9XG4gICAgXSxcbiAgICAvLyAlciBxdW90ZWQgcmVnZXhwXG4gICAgLy8gcHJlZ2V4cC48b3Blbj4uPGNsb3NlPiB3aGVyZSBvcGVuL2Nsb3NlIGFyZSB0aGUgb3Blbi9jbG9zZSBkZWxpbWl0ZXJcbiAgICBwcmVnZXhwOiBbXG4gICAgICB7IGluY2x1ZGU6IFwiQHdoaXRlc3BhY2VcIiB9LFxuICAgICAgLy8gdHVybnMgb3V0IHRoYXQgeW91IGNhbiBxdW90ZSB1c2luZyByZWdleCBjb250cm9sIGNoYXJhY3RlcnMsIGFhcmdoIVxuICAgICAgLy8gZm9yIGV4YW1wbGU7ICVyfGtnamdhanwgaXMgb2sgKGV2ZW4gdGhvdWdoIHwgaXMgdXNlZCBmb3IgYWx0ZXJuYXRpb24pXG4gICAgICAvLyBzbywgd2UgbmVlZCB0byBtYXRjaCB0aG9zZSBmaXJzdFxuICAgICAgW1xuICAgICAgICAvW15cXChcXHtcXFtcXFxcXS8sXG4gICAgICAgIHtcbiAgICAgICAgICBjYXNlczoge1xuICAgICAgICAgICAgXCIkIz09JFMzXCI6IHsgdG9rZW46IFwicmVnZXhwLmRlbGltXCIsIG5leHQ6IFwiQHBvcFwiIH0sXG4gICAgICAgICAgICBcIiQjPT0kUzJcIjogeyB0b2tlbjogXCJyZWdleHAuZGVsaW1cIiwgbmV4dDogXCJAcHVzaFwiIH0sXG4gICAgICAgICAgICAvLyBuZXN0ZWQgZGVsaW1pdGVycyBhcmUgYWxsb3dlZC4uXG4gICAgICAgICAgICBcIn5bKX1cXFxcXV1cIjogXCJAYnJhY2tldHMucmVnZXhwLmVzY2FwZS5jb250cm9sXCIsXG4gICAgICAgICAgICBcIn5AcmVnZXhwY3RsXCI6IFwicmVnZXhwLmVzY2FwZS5jb250cm9sXCIsXG4gICAgICAgICAgICBcIkBkZWZhdWx0XCI6IFwicmVnZXhwXCJcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIF0sXG4gICAgICB7IGluY2x1ZGU6IFwiQHJlZ2V4Y29udHJvbFwiIH1cbiAgICBdLFxuICAgIC8vIFdlIG1hdGNoIHJlZ3VsYXIgZXhwcmVzc2lvbiBxdWl0ZSBwcmVjaXNlbHlcbiAgICByZWdleHA6IFtcbiAgICAgIHsgaW5jbHVkZTogXCJAcmVnZXhjb250cm9sXCIgfSxcbiAgICAgIFsvW15cXFxcXFwvXS8sIFwicmVnZXhwXCJdLFxuICAgICAgW1wiL1tpeG1wXSpcIiwgeyB0b2tlbjogXCJyZWdleHAuZGVsaW1cIiB9LCBcIkBwb3BcIl1cbiAgICBdLFxuICAgIHJlZ2V4Y29udHJvbDogW1xuICAgICAgW1xuICAgICAgICAvKFxceykoXFxkKyg/OixcXGQqKT8pKFxcfSkvLFxuICAgICAgICBbXG4gICAgICAgICAgXCJAYnJhY2tldHMucmVnZXhwLmVzY2FwZS5jb250cm9sXCIsXG4gICAgICAgICAgXCJyZWdleHAuZXNjYXBlLmNvbnRyb2xcIixcbiAgICAgICAgICBcIkBicmFja2V0cy5yZWdleHAuZXNjYXBlLmNvbnRyb2xcIlxuICAgICAgICBdXG4gICAgICBdLFxuICAgICAgW1xuICAgICAgICAvKFxcWykoXFxePykvLFxuICAgICAgICBbXCJAYnJhY2tldHMucmVnZXhwLmVzY2FwZS5jb250cm9sXCIsIHsgdG9rZW46IFwicmVnZXhwLmVzY2FwZS5jb250cm9sXCIsIG5leHQ6IFwiQHJlZ2V4cmFuZ2VcIiB9XVxuICAgICAgXSxcbiAgICAgIFsvKFxcKCkoXFw/Wzo9IV0pLywgW1wiQGJyYWNrZXRzLnJlZ2V4cC5lc2NhcGUuY29udHJvbFwiLCBcInJlZ2V4cC5lc2NhcGUuY29udHJvbFwiXV0sXG4gICAgICBbL1xcKFxcPyMvLCB7IHRva2VuOiBcInJlZ2V4cC5lc2NhcGUuY29udHJvbFwiLCBuZXh0OiBcIkByZWdleHBjb21tZW50XCIgfV0sXG4gICAgICBbL1soKV0vLCBcIkBicmFja2V0cy5yZWdleHAuZXNjYXBlLmNvbnRyb2xcIl0sXG4gICAgICBbL0ByZWdleHBjdGwvLCBcInJlZ2V4cC5lc2NhcGUuY29udHJvbFwiXSxcbiAgICAgIFsvXFxcXCQvLCBcInJlZ2V4cC5lc2NhcGVcIl0sXG4gICAgICBbL0ByZWdleHBlc2MvLCBcInJlZ2V4cC5lc2NhcGVcIl0sXG4gICAgICBbL1xcXFxcXC4vLCBcInJlZ2V4cC5pbnZhbGlkXCJdLFxuICAgICAgWy8jLywgXCJyZWdleHAuZXNjYXBlXCIsIFwiQGludGVycG9sYXRlZFwiXVxuICAgIF0sXG4gICAgcmVnZXhyYW5nZTogW1xuICAgICAgWy8tLywgXCJyZWdleHAuZXNjYXBlLmNvbnRyb2xcIl0sXG4gICAgICBbL1xcXi8sIFwicmVnZXhwLmludmFsaWRcIl0sXG4gICAgICBbL1xcXFwkLywgXCJyZWdleHAuZXNjYXBlXCJdLFxuICAgICAgWy9AcmVnZXhwZXNjLywgXCJyZWdleHAuZXNjYXBlXCJdLFxuICAgICAgWy9bXlxcXV0vLCBcInJlZ2V4cFwiXSxcbiAgICAgIFsvXFxdLywgXCJAYnJhY2tldHMucmVnZXhwLmVzY2FwZS5jb250cm9sXCIsIFwiQHBvcFwiXVxuICAgIF0sXG4gICAgcmVnZXhwY29tbWVudDogW1xuICAgICAgWy9bXildKy8sIFwiY29tbWVudFwiXSxcbiAgICAgIFsvXFwpLywgeyB0b2tlbjogXCJyZWdleHAuZXNjYXBlLmNvbnRyb2xcIiwgbmV4dDogXCJAcG9wXCIgfV1cbiAgICBdLFxuICAgIC8vICUgcXVvdGVkIHN0cmluZ3NcbiAgICAvLyBBIGJpdCByZXBldGl0aXZlIHNpbmNlIHdlIG5lZWQgdG8gb2Z0ZW4gc3BlY2lhbCBjYXNlIHRoZSBraW5kIG9mIGVuZGluZyBkZWxpbWl0ZXJcbiAgICBwc3RyaW5nOiBbXG4gICAgICBbLyUoW3F3c10pXFwoLywgeyB0b2tlbjogXCJzdHJpbmcuJDEuZGVsaW1cIiwgc3dpdGNoVG86IFwiQHFzdHJpbmcuJDEuKC4pXCIgfV0sXG4gICAgICBbLyUoW3F3c10pXFxbLywgeyB0b2tlbjogXCJzdHJpbmcuJDEuZGVsaW1cIiwgc3dpdGNoVG86IFwiQHFzdHJpbmcuJDEuWy5dXCIgfV0sXG4gICAgICBbLyUoW3F3c10pXFx7LywgeyB0b2tlbjogXCJzdHJpbmcuJDEuZGVsaW1cIiwgc3dpdGNoVG86IFwiQHFzdHJpbmcuJDEuey59XCIgfV0sXG4gICAgICBbLyUoW3F3c10pPC8sIHsgdG9rZW46IFwic3RyaW5nLiQxLmRlbGltXCIsIHN3aXRjaFRvOiBcIkBxc3RyaW5nLiQxLjwuPlwiIH1dLFxuICAgICAgWy8lKFtxd3NdKShAZGVsaW0pLywgeyB0b2tlbjogXCJzdHJpbmcuJDEuZGVsaW1cIiwgc3dpdGNoVG86IFwiQHFzdHJpbmcuJDEuJDIuJDJcIiB9XSxcbiAgICAgIFsvJXJcXCgvLCB7IHRva2VuOiBcInJlZ2V4cC5kZWxpbVwiLCBzd2l0Y2hUbzogXCJAcHJlZ2V4cC4oLilcIiB9XSxcbiAgICAgIFsvJXJcXFsvLCB7IHRva2VuOiBcInJlZ2V4cC5kZWxpbVwiLCBzd2l0Y2hUbzogXCJAcHJlZ2V4cC5bLl1cIiB9XSxcbiAgICAgIFsvJXJcXHsvLCB7IHRva2VuOiBcInJlZ2V4cC5kZWxpbVwiLCBzd2l0Y2hUbzogXCJAcHJlZ2V4cC57Ln1cIiB9XSxcbiAgICAgIFsvJXI8LywgeyB0b2tlbjogXCJyZWdleHAuZGVsaW1cIiwgc3dpdGNoVG86IFwiQHByZWdleHAuPC4+XCIgfV0sXG4gICAgICBbLyVyKEBkZWxpbSkvLCB7IHRva2VuOiBcInJlZ2V4cC5kZWxpbVwiLCBzd2l0Y2hUbzogXCJAcHJlZ2V4cC4kMS4kMVwiIH1dLFxuICAgICAgWy8lKHh8V3xRPylcXCgvLCB7IHRva2VuOiBcInN0cmluZy4kMS5kZWxpbVwiLCBzd2l0Y2hUbzogXCJAcXFzdHJpbmcuJDEuKC4pXCIgfV0sXG4gICAgICBbLyUoeHxXfFE/KVxcWy8sIHsgdG9rZW46IFwic3RyaW5nLiQxLmRlbGltXCIsIHN3aXRjaFRvOiBcIkBxcXN0cmluZy4kMS5bLl1cIiB9XSxcbiAgICAgIFsvJSh4fFd8UT8pXFx7LywgeyB0b2tlbjogXCJzdHJpbmcuJDEuZGVsaW1cIiwgc3dpdGNoVG86IFwiQHFxc3RyaW5nLiQxLnsufVwiIH1dLFxuICAgICAgWy8lKHh8V3xRPyk8LywgeyB0b2tlbjogXCJzdHJpbmcuJDEuZGVsaW1cIiwgc3dpdGNoVG86IFwiQHFxc3RyaW5nLiQxLjwuPlwiIH1dLFxuICAgICAgWy8lKHh8V3xRPykoQGRlbGltKS8sIHsgdG9rZW46IFwic3RyaW5nLiQxLmRlbGltXCIsIHN3aXRjaFRvOiBcIkBxcXN0cmluZy4kMS4kMi4kMlwiIH1dLFxuICAgICAgWy8lKFtycXdzeFddfFE/KS4vLCB7IHRva2VuOiBcImludmFsaWRcIiwgbmV4dDogXCJAcG9wXCIgfV0sXG4gICAgICAvLyByZWNvdmVyXG4gICAgICBbLy4vLCB7IHRva2VuOiBcImludmFsaWRcIiwgbmV4dDogXCJAcG9wXCIgfV1cbiAgICAgIC8vIHJlY292ZXJcbiAgICBdLFxuICAgIC8vIG5vbi1leHBhbmRlZCBxdW90ZWQgc3RyaW5nLlxuICAgIC8vIHFzdHJpbmcuPGtpbmQ+LjxvcGVuPi48Y2xvc2U+XG4gICAgLy8gIGtpbmQgPSBxfHd8cyAgKHNpbmdsZSBxdW90ZSwgYXJyYXksIHN5bWJvbClcbiAgICAvLyAgb3BlbiA9IG9wZW4gZGVsaW1pdGVyXG4gICAgLy8gIGNsb3NlID0gY2xvc2UgZGVsaW1pdGVyXG4gICAgcXN0cmluZzogW1xuICAgICAgWy9cXFxcJC8sIFwic3RyaW5nLiRTMi5lc2NhcGVcIl0sXG4gICAgICBbL1xcXFwuLywgXCJzdHJpbmcuJFMyLmVzY2FwZVwiXSxcbiAgICAgIFtcbiAgICAgICAgLy4vLFxuICAgICAgICB7XG4gICAgICAgICAgY2FzZXM6IHtcbiAgICAgICAgICAgIFwiJCM9PSRTNFwiOiB7IHRva2VuOiBcInN0cmluZy4kUzIuZGVsaW1cIiwgbmV4dDogXCJAcG9wXCIgfSxcbiAgICAgICAgICAgIFwiJCM9PSRTM1wiOiB7IHRva2VuOiBcInN0cmluZy4kUzIuZGVsaW1cIiwgbmV4dDogXCJAcHVzaFwiIH0sXG4gICAgICAgICAgICAvLyBuZXN0ZWQgZGVsaW1pdGVycyBhcmUgYWxsb3dlZC4uXG4gICAgICAgICAgICBcIkBkZWZhdWx0XCI6IFwic3RyaW5nLiRTMlwiXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdXG4gICAgXSxcbiAgICAvLyBleHBhbmRlZCBxdW90ZWQgc3RyaW5nLlxuICAgIC8vIHFxc3RyaW5nLjxraW5kPi48b3Blbj4uPGNsb3NlPlxuICAgIC8vICBraW5kID0gUXxXfHggIChkb3VibGUgcXVvdGUsIGFycmF5LCBjb21tYW5kKVxuICAgIC8vICBvcGVuID0gb3BlbiBkZWxpbWl0ZXJcbiAgICAvLyAgY2xvc2UgPSBjbG9zZSBkZWxpbWl0ZXJcbiAgICBxcXN0cmluZzogW1svIy8sIFwic3RyaW5nLiRTMi5lc2NhcGVcIiwgXCJAaW50ZXJwb2xhdGVkXCJdLCB7IGluY2x1ZGU6IFwiQHFzdHJpbmdcIiB9XSxcbiAgICAvLyB3aGl0ZXNwYWNlICYgY29tbWVudHNcbiAgICB3aGl0ZXNwYWNlOiBbXG4gICAgICBbL1sgXFx0XFxyXFxuXSsvLCBcIlwiXSxcbiAgICAgIFsvXlxccyo9YmVnaW5cXGIvLCBcImNvbW1lbnRcIiwgXCJAY29tbWVudFwiXSxcbiAgICAgIFsvIy4qJC8sIFwiY29tbWVudFwiXVxuICAgIF0sXG4gICAgY29tbWVudDogW1xuICAgICAgWy9bXj1dKy8sIFwiY29tbWVudFwiXSxcbiAgICAgIFsvXlxccyo9YmVnaW5cXGIvLCBcImNvbW1lbnQuaW52YWxpZFwiXSxcbiAgICAgIC8vIG5lc3RlZCBjb21tZW50XG4gICAgICBbL15cXHMqPWVuZFxcYi4qLywgXCJjb21tZW50XCIsIFwiQHBvcFwiXSxcbiAgICAgIFsvWz1dLywgXCJjb21tZW50XCJdXG4gICAgXVxuICB9XG59O1xuZXhwb3J0IHtcbiAgY29uZixcbiAgbGFuZ3VhZ2Vcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/ruby/ruby.js\n"));

/***/ })

}]);