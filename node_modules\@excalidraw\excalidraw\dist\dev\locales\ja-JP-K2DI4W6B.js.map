{"version": 3, "sources": ["../../../locales/ja-JP.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"貼り付け\",\n    \"pasteAsPlaintext\": \"書式なしテキストとして貼り付け\",\n    \"pasteCharts\": \"チャートの貼り付け\",\n    \"selectAll\": \"すべて選択\",\n    \"multiSelect\": \"複数選択\",\n    \"moveCanvas\": \"キャンバスを移動\",\n    \"cut\": \"切り取り\",\n    \"copy\": \"コピー\",\n    \"copyAsPng\": \"PNGとしてクリップボードへコピー\",\n    \"copyAsSvg\": \"SVGとしてクリップボードへコピー\",\n    \"copyText\": \"テキストとしてクリップボードにコピー\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"前面に移動\",\n    \"sendToBack\": \"最背面に移動\",\n    \"bringToFront\": \"最前面に移動\",\n    \"sendBackward\": \"背面に移動\",\n    \"delete\": \"削除\",\n    \"copyStyles\": \"スタイルのコピー\",\n    \"pasteStyles\": \"スタイルの貼り付け\",\n    \"stroke\": \"線\",\n    \"background\": \"背景\",\n    \"fill\": \"塗りつぶし\",\n    \"strokeWidth\": \"線の太さ\",\n    \"strokeStyle\": \"線の種類\",\n    \"strokeStyle_solid\": \"実線\",\n    \"strokeStyle_dashed\": \"破線\",\n    \"strokeStyle_dotted\": \"点線\",\n    \"sloppiness\": \"ばらつき加減\",\n    \"opacity\": \"透明度\",\n    \"textAlign\": \"文字の配置\",\n    \"edges\": \"角\",\n    \"sharp\": \"四角\",\n    \"round\": \"丸\",\n    \"arrowheads\": \"線の終点\",\n    \"arrowhead_none\": \"なし\",\n    \"arrowhead_arrow\": \"矢印\",\n    \"arrowhead_bar\": \"バー\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"三角\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"フォントの大きさ\",\n    \"fontFamily\": \"フォントの種類\",\n    \"addWatermark\": \"\\\"Made with Excalidraw\\\"と表示\",\n    \"handDrawn\": \"手描き風\",\n    \"normal\": \"普通\",\n    \"code\": \"コード\",\n    \"small\": \"小\",\n    \"medium\": \"中\",\n    \"large\": \"大\",\n    \"veryLarge\": \"特大\",\n    \"solid\": \"ベタ塗り\",\n    \"hachure\": \"斜線\",\n    \"zigzag\": \"ジグザグ\",\n    \"crossHatch\": \"網掛け\",\n    \"thin\": \"細\",\n    \"bold\": \"太字\",\n    \"left\": \"左寄せ\",\n    \"center\": \"中央寄せ\",\n    \"right\": \"右寄せ\",\n    \"extraBold\": \"極太\",\n    \"architect\": \"正確\",\n    \"artist\": \"アート\",\n    \"cartoonist\": \"漫画風\",\n    \"fileTitle\": \"ファイル名\",\n    \"colorPicker\": \"色選択\",\n    \"canvasColors\": \"キャンバス上で使用\",\n    \"canvasBackground\": \"キャンバスの背景\",\n    \"drawingCanvas\": \"キャンバスの描画\",\n    \"layers\": \"レイヤー\",\n    \"actions\": \"操作\",\n    \"language\": \"言語\",\n    \"liveCollaboration\": \"共同編集...\",\n    \"duplicateSelection\": \"複製\",\n    \"untitled\": \"無題\",\n    \"name\": \"名前\",\n    \"yourName\": \"あなたの名前\",\n    \"madeWithExcalidraw\": \"Excalidrawで作成\",\n    \"group\": \"グループ化\",\n    \"ungroup\": \"グループ化を解除\",\n    \"collaborators\": \"共同編集者\",\n    \"showGrid\": \"グリッドを表示\",\n    \"addToLibrary\": \"ライブラリに追加\",\n    \"removeFromLibrary\": \"ライブラリから削除\",\n    \"libraryLoadingMessage\": \"ライブラリを読み込み中…\",\n    \"libraries\": \"ライブラリを参照する\",\n    \"loadingScene\": \"シーンを読み込み中…\",\n    \"align\": \"配置\",\n    \"alignTop\": \"上揃え\",\n    \"alignBottom\": \"下揃え\",\n    \"alignLeft\": \"左揃え\",\n    \"alignRight\": \"右揃え\",\n    \"centerVertically\": \"縦方向に中央揃え\",\n    \"centerHorizontally\": \"横方向に中央揃え\",\n    \"distributeHorizontally\": \"水平方向に分散配置\",\n    \"distributeVertically\": \"垂直方向に分散配置\",\n    \"flipHorizontal\": \"水平方向に反転\",\n    \"flipVertical\": \"垂直方向に反転\",\n    \"viewMode\": \"閲覧モード\",\n    \"share\": \"共有\",\n    \"showStroke\": \"ストロークカラーピッカーを表示\",\n    \"showBackground\": \"背景色ピッカーを表示\",\n    \"toggleTheme\": \"テーマの切り替え\",\n    \"personalLib\": \"個人ライブラリ\",\n    \"excalidrawLib\": \"Excalidrawライブラリ\",\n    \"decreaseFontSize\": \"フォントサイズを縮小\",\n    \"increaseFontSize\": \"フォントサイズを拡大\",\n    \"unbindText\": \"テキストのバインド解除\",\n    \"bindText\": \"テキストをコンテナにバインド\",\n    \"createContainerFromText\": \"コンテナ内でテキストを折り返す\",\n    \"link\": {\n      \"edit\": \"リンクを編集\",\n      \"editEmbed\": \"リンクの編集と埋め込み\",\n      \"create\": \"リンクを作成\",\n      \"createEmbed\": \"リンクの作成と埋め込み\",\n      \"label\": \"リンク\",\n      \"labelEmbed\": \"リンクと埋め込み\",\n      \"empty\": \"リンクが設定されていません\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"行を編集\",\n      \"exit\": \"行エディタを終了\"\n    },\n    \"elementLock\": {\n      \"lock\": \"ロック\",\n      \"unlock\": \"ロック解除\",\n      \"lockAll\": \"すべてロック\",\n      \"unlockAll\": \"すべてのロックを解除\"\n    },\n    \"statusPublished\": \"公開済み\",\n    \"sidebarLock\": \"サイドバーを開いたままにする\",\n    \"selectAllElementsInFrame\": \"フレーム内のすべての要素を選択\",\n    \"removeAllElementsFromFrame\": \"フレーム内のすべての要素を削除\",\n    \"eyeDropper\": \"キャンバスから色を選択\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"まだアイテムが追加されていません…\",\n    \"hint_emptyLibrary\": \"キャンバス上のアイテムを選択してここに追加するか、以下の公開リポジトリからライブラリをインストールしてください。\",\n    \"hint_emptyPrivateLibrary\": \"キャンバス上のアイテムを選択すると、ここに追加されます。\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"キャンバスのリセット\",\n    \"exportJSON\": \"ファイルへエクスポート\",\n    \"exportImage\": \"画像のエクスポート...\",\n    \"export\": \"名前を付けて保存...\",\n    \"copyToClipboard\": \"クリップボードにコピー\",\n    \"save\": \"現在のファイルに保存\",\n    \"saveAs\": \"名前を付けて保存\",\n    \"load\": \"開く\",\n    \"getShareableLink\": \"共有URLの取得\",\n    \"close\": \"閉じる\",\n    \"selectLanguage\": \"言語の選択\",\n    \"scrollBackToContent\": \"コンテンツまでスクロールで戻る\",\n    \"zoomIn\": \"拡大\",\n    \"zoomOut\": \"縮小\",\n    \"resetZoom\": \"拡大/縮小をリセット\",\n    \"menu\": \"メニュー\",\n    \"done\": \"完了\",\n    \"edit\": \"編集\",\n    \"undo\": \"元に戻す\",\n    \"redo\": \"やり直し\",\n    \"resetLibrary\": \"ライブラリをリセット\",\n    \"createNewRoom\": \"新しい部屋を作成する\",\n    \"fullScreen\": \"フルスクリーン\",\n    \"darkMode\": \"ダークモード\",\n    \"lightMode\": \"ライトモード\",\n    \"zenMode\": \"Zenモード\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"集中モードをやめる\",\n    \"cancel\": \"キャンセル\",\n    \"clear\": \"消去\",\n    \"remove\": \"削除\",\n    \"embed\": \"埋め込みの切り替え\",\n    \"publishLibrary\": \"公開\",\n    \"submit\": \"送信\",\n    \"confirm\": \"確認\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"この操作によってキャンバス全体が消えます。よろしいですか？\",\n    \"couldNotCreateShareableLink\": \"共有URLを作成できませんでした。\",\n    \"couldNotCreateShareableLinkTooBig\": \"共有可能なリンクを作成できませんでした: シーンが大きすぎます\",\n    \"couldNotLoadInvalidFile\": \"無効なファイルを読み込めませんでした。\",\n    \"importBackendFailed\": \"サーバーからの読み込みに失敗しました。\",\n    \"cannotExportEmptyCanvas\": \"空のキャンバスはエクスポートできません。\",\n    \"couldNotCopyToClipboard\": \"クリップボードにコピーできませんでした。\",\n    \"decryptFailed\": \"データを復号できませんでした。\",\n    \"uploadedSecurly\": \"データのアップロードはエンドツーエンド暗号化によって保護されています。Excalidrawサーバーと第三者はデータの内容を見ることができません。\",\n    \"loadSceneOverridePrompt\": \"外部図面を読み込むと、既存のコンテンツが置き換わります。続行しますか？\",\n    \"collabStopOverridePrompt\": \"セッションを停止すると、ローカルに保存されている図が上書きされます。 本当によろしいですか？\\n\\n(ローカルの図を保持したい場合は、セッションを停止せずにブラウザタブを閉じてください。)\",\n    \"errorAddingToLibrary\": \"アイテムをライブラリに追加できませんでした\",\n    \"errorRemovingFromLibrary\": \"ライブラリからアイテムを削除できませんでした\",\n    \"confirmAddLibrary\": \"{{numShapes}} 個の図形をライブラリに追加します。よろしいですか？\",\n    \"imageDoesNotContainScene\": \"この画像にはシーンデータが含まれていないようです。エクスポート時にシーンの埋め込みを有効にしましたか？\",\n    \"cannotRestoreFromImage\": \"このイメージファイルからシーンを復元できませんでした\",\n    \"invalidSceneUrl\": \"指定された URL からシーンをインポートできませんでした。不正な形式であるか、有効な Excalidraw JSON データが含まれていません。\",\n    \"resetLibrary\": \"ライブラリを消去します。本当によろしいですか？\",\n    \"removeItemsFromsLibrary\": \"{{count}} 個のアイテムをライブラリから削除しますか？\",\n    \"invalidEncryptionKey\": \"暗号化キーは22文字でなければなりません。ライブコラボレーションは無効化されています。\",\n    \"collabOfflineWarning\": \"インターネットに接続されていません。\\n変更は保存されません！\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"サポートされていないファイル形式です。\",\n    \"imageInsertError\": \"画像を挿入できませんでした。後でもう一度お試しください...\",\n    \"fileTooBig\": \"ファイルが大きすぎます。許可される最大サイズは {{maxSize}} です。\",\n    \"svgImageInsertError\": \"SVGイメージを挿入できませんでした。SVGマークアップは無効に見えます。\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"無効なSVGです。\",\n    \"cannotResolveCollabServer\": \"コラボレーションサーバに接続できませんでした。ページを再読み込みして、もう一度お試しください。\",\n    \"importLibraryError\": \"ライブラリを読み込めませんでした。\",\n    \"collabSaveFailed\": \"バックエンドデータベースに保存できませんでした。問題が解決しない場合は、作業を失わないようにローカルにファイルを保存してください。\",\n    \"collabSaveFailed_sizeExceeded\": \"キャンバスが大きすぎるため、バックエンドデータベースに保存できませんでした。問題が解決しない場合は、作業を失わないようにローカルにファイルを保存してください。\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"<bold>Aggressly Block Fingerprinting</bold> の設定が有効なBraveブラウザを使用しているようです。\",\n      \"line2\": \"これにより、図面の <bold>テキスト要素</bold> が壊れる可能性があります。\",\n      \"line3\": \"この設定を無効にすることを強く推奨します。 <link>設定手順</link> をこちらから確認できます。\",\n      \"line4\": \"この設定を無効にすると、テキスト要素の表示が修正されません。 GitHub で <issueLink>Issue</issueLink> を開くか、 <discordLink>Discord</discordLink> にご記入ください\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"選択\",\n    \"image\": \"画像を挿入\",\n    \"rectangle\": \"矩形\",\n    \"diamond\": \"ひし形\",\n    \"ellipse\": \"楕円\",\n    \"arrow\": \"矢印\",\n    \"line\": \"直線\",\n    \"freedraw\": \"描画\",\n    \"text\": \"テキスト\",\n    \"library\": \"ライブラリ\",\n    \"lock\": \"描画後も使用中のツールを選択したままにする\",\n    \"penMode\": \"ペンモード - タッチ防止\",\n    \"link\": \"選択した図形のリンクを追加/更新\",\n    \"eraser\": \"消しゴム\",\n    \"frame\": \"フレームツール\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"Web埋め込み\",\n    \"laser\": \"\",\n    \"hand\": \"手 (パンニングツール)\",\n    \"extraTools\": \"その他のツール\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"キャンバス操作\",\n    \"selectedShapeActions\": \"選択された図形に対する操作\",\n    \"shapes\": \"図形\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"キャンバスを移動するには、マウスホイールまたはスペースバーを押しながらドラッグするか、手ツールを使用します\",\n    \"linearElement\": \"クリックすると複数の頂点からなる曲線を開始、ドラッグすると直線\",\n    \"freeDraw\": \"クリックしてドラッグします。離すと終了します\",\n    \"text\": \"ヒント: 選択ツールを使用して任意の場所をダブルクリックしてテキストを追加することもできます\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"テキストを編集するには、ダブルクリックまたはEnterキーを押します\",\n    \"text_editing\": \"Esc キーまたは CtrlOrCmd+ENTER キーを押して編集を終了します\",\n    \"linearElementMulti\": \"最後のポイントをクリックするか、エスケープまたはEnterを押して終了します\",\n    \"lockAngle\": \"SHIFTを押したままにすると、角度を制限することができます\",\n    \"resize\": \"サイズを変更中にSHIFTを押すと縦横比を固定できます。Altを押すと中央からサイズを変更できます\",\n    \"resizeImage\": \"SHIFTを長押しすると自由にサイズを変更できます。\\n中央からサイズを変更するにはALTを長押しします\",\n    \"rotate\": \"回転中にSHIFT キーを押すと角度を制限することができます\",\n    \"lineEditor_info\": \"CtrlOrCmd を押したままダブルクリックするか、CtrlOrCmd + Enter を押して点を編集します\",\n    \"lineEditor_pointSelected\": \"Deleteキーを押すと点を削除、CtrlOrCmd+Dで複製、マウスドラッグで移動\",\n    \"lineEditor_nothingSelected\": \"編集する点を選択（SHIFTを押したままで複数選択）、\\nAltキーを押しながらクリックすると新しい点を追加\",\n    \"placeImage\": \"クリックして画像を配置するか、クリックしてドラッグしてサイズを手動で設定します\",\n    \"publishLibrary\": \"自分のライブラリを公開\",\n    \"bindTextToElement\": \"Enterを押してテキストを追加\",\n    \"deepBoxSelect\": \"CtrlOrCmd を押し続けることでドラッグを抑止し、深い選択を行います\",\n    \"eraserRevert\": \"Alt を押し続けることで削除マークされた要素を元に戻す\",\n    \"firefox_clipboard_write\": \"この機能は、\\\"dom.events.asyncClipboard.clipboardItem\\\" フラグを \\\"true\\\" に設定することで有効になる可能性があります。Firefox でブラウザーの設定を変更するには、\\\"about:config\\\" ページを参照してください。\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"プレビューを表示できません\",\n    \"canvasTooBig\": \"キャンバスが大きすぎます。\",\n    \"canvasTooBigTip\": \"ヒント: 最も遠い要素をもう少し近づけてみてください。\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"エラーが発生しました。もう一度やり直してください。 <button>ページを再読み込みする。</button>\",\n    \"clearCanvasMessage\": \"再読み込みがうまくいかない場合は、 <button>キャンバスを消去しています</button>\",\n    \"clearCanvasCaveat\": \" これにより作業が失われます \",\n    \"trackedToSentry\": \"識別子のエラー {{eventId}} が我々のシステムで追跡されました。\",\n    \"openIssueMessage\": \"エラーに関するシーン情報を含めないように非常に慎重に設定しました。もしあなたのシーンがプライベートでない場合は、私たちのフォローアップを検討してください。 <button>バグ報告</button> GitHub のIssueに以下の情報をコピーして貼り付けてください。\",\n    \"sceneContent\": \"シーンの内容:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"他の人を編集中のあなたの画面に招待して共同編集することができます。\",\n    \"desc_privacy\": \"このセッションはエンドツーエンド暗号化されており、描画内容は保護されています。運営サーバーからも内容は見えません。\",\n    \"button_startSession\": \"セッションを開始する\",\n    \"button_stopSession\": \"セッションを終了する\",\n    \"desc_inProgressIntro\": \"共同編集セッションが有効になっています。\",\n    \"desc_shareLink\": \"下記URLを共同編集したい人に共有してください：\",\n    \"desc_exitSession\": \"セッションを終了するとあなたはルームから切断されますが、ローカルで作業を続けることができます。セッションを終了しても他のメンバには影響はなく、引き続き共同作業を行うことができます。\",\n    \"shareTitle\": \"Excalidrawの共同編集セッションに参加する\"\n  },\n  \"errorDialog\": {\n    \"title\": \"エラー\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"ディスクに保存\",\n    \"disk_details\": \"シーンデータを後からインポートできるファイルにエクスポートします。\",\n    \"disk_button\": \"ファイルへ保存\",\n    \"link_title\": \"共有可能なリンク\",\n    \"link_details\": \"読み取り専用リンクとしてエクスポート\",\n    \"link_button\": \"リンクとしてエクスポート\",\n    \"excalidrawplus_description\": \"Excalidraw+ ワークスペースにシーンを保存します。\",\n    \"excalidrawplus_button\": \"エクスポート\",\n    \"excalidrawplus_exportError\": \"Excalidraw+ にエクスポートできませんでした...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"公式ブログを読む\",\n    \"click\": \"クリック\",\n    \"deepSelect\": \"深い選択\",\n    \"deepBoxSelect\": \"ボックス内の深い選択、およびドラッグの抑止\",\n    \"curvedArrow\": \"カーブした矢印\",\n    \"curvedLine\": \"曲線\",\n    \"documentation\": \"ドキュメント\",\n    \"doubleClick\": \"ダブルクリック\",\n    \"drag\": \"ドラッグ\",\n    \"editor\": \"エディタ\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"テキストの編集 / ラベルの追加\",\n    \"github\": \"不具合報告はこちら\",\n    \"howto\": \"ヘルプ・マニュアル\",\n    \"or\": \"または\",\n    \"preventBinding\": \"矢印を結合しない\",\n    \"tools\": \"ツール\",\n    \"shortcuts\": \"キーボードショートカット\",\n    \"textFinish\": \"編集を終了 (テキストエディタ)\",\n    \"textNewLine\": \"新しい行を追加 (テキスト)\",\n    \"title\": \"ヘルプ\",\n    \"view\": \"表示\",\n    \"zoomToFit\": \"すべての要素が収まるようにズーム\",\n    \"zoomToSelection\": \"選択要素にズーム\",\n    \"toggleElementLock\": \"選択したアイテムをロック/ロック解除\",\n    \"movePageUpDown\": \"ページを上下に移動\",\n    \"movePageLeftRight\": \"ページを左右に移動\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"キャンバスを消去\"\n  },\n  \"publishDialog\": {\n    \"title\": \"ライブラリを公開\",\n    \"itemName\": \"アイテム名\",\n    \"authorName\": \"作成者名\",\n    \"githubUsername\": \"GitHub ユーザ名\",\n    \"twitterUsername\": \"Twitter ユーザ名\",\n    \"libraryName\": \"ライブラリ名\",\n    \"libraryDesc\": \"ライブラリの説明\",\n    \"website\": \"Webサイト\",\n    \"placeholder\": {\n      \"authorName\": \"お名前またはユーザー名\",\n      \"libraryName\": \"あなたのライブラリ名\",\n      \"libraryDesc\": \"ライブラリの使い方を理解するための説明\",\n      \"githubHandle\": \"GitHubハンドル(任意)。一度レビューのために送信されると、ライブラリを編集できます\",\n      \"twitterHandle\": \"Twitterのユーザー名 (任意)。Twitterでプロモーションする際にクレジットする人を知っておくためのものです\",\n      \"website\": \"個人のウェブサイトまたは他のサイトへのリンク (任意)\"\n    },\n    \"errors\": {\n      \"required\": \"必須項目\",\n      \"website\": \"有効な URL を入力してください\"\n    },\n    \"noteDescription\": \"以下に含めるライブラリを提出してください <link>公開ライブラリのリポジトリ</link>他の人が作図に使えるようにするためです\",\n    \"noteGuidelines\": \"最初にライブラリを手動で承認する必要があります。次をお読みください <link>ガイドライン</link> 送信する前に、GitHubアカウントが必要になりますが、必須ではありません。\",\n    \"noteLicense\": \"提出することにより、ライブラリが次の下で公開されることに同意します： <link>MIT ライセンス </link>つまり誰でも制限なく使えるということです\",\n    \"noteItems\": \"各ライブラリ項目は、フィルタリングのために独自の名前を持つ必要があります。以下のライブラリアイテムが含まれます:\",\n    \"atleastOneLibItem\": \"開始するには少なくとも1つのライブラリ項目を選択してください\",\n    \"republishWarning\": \"注意: 選択された項目の中には、すでに公開/投稿済みと表示されているものがあります。既存のライブラリや投稿を更新する場合のみ、アイテムを再投稿してください。\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"ライブラリを送信しました\",\n    \"content\": \"{{authorName}} さん、ありがとうございます。あなたのライブラリはレビューのために提出されました。状況を追跡できます。<link>こちら</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"ライブラリをリセット\",\n    \"removeItemsFromLib\": \"選択したアイテムをライブラリから削除\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"画像をエクスポート\",\n    \"label\": {\n      \"withBackground\": \"背景\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"ダークモード\",\n      \"embedScene\": \"\",\n      \"scale\": \"スケール\",\n      \"padding\": \"余白\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"PNG にエクスポート\",\n      \"exportToSvg\": \"SVG にエクスポート\",\n      \"copyPngToClipboard\": \"クリップボードにPNGをコピー\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"クリップボードにコピー\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"描画内容はエンドツーエンド暗号化が施されており、Excalidrawサーバーが内容を見ることはできません。\",\n    \"link\": \"Excalidrawのエンドツーエンド暗号化に関するブログ記事\"\n  },\n  \"stats\": {\n    \"angle\": \"角度\",\n    \"element\": \"要素\",\n    \"elements\": \"要素\",\n    \"height\": \"高さ\",\n    \"scene\": \"シーン\",\n    \"selected\": \"選択済み\",\n    \"storage\": \"ストレージ\",\n    \"title\": \"詳細統計情報\",\n    \"total\": \"合計\",\n    \"version\": \"バージョン\",\n    \"versionCopy\": \"クリックしてコピー\",\n    \"versionNotAvailable\": \"利用できないバージョン\",\n    \"width\": \"幅\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"ライブラリに追加しました\",\n    \"copyStyles\": \"スタイルをコピーしました。\",\n    \"copyToClipboard\": \"クリップボードにコピー\",\n    \"copyToClipboardAsPng\": \"{{exportSelection}} を PNG 形式でクリップボードにコピーしました\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"ファイルを保存しました\",\n    \"fileSavedToFilename\": \"{filename} に保存しました\",\n    \"canvas\": \"キャンバス\",\n    \"selection\": \"選択\",\n    \"pasteAsSingleElement\": \"{{shortcut}} を使用して単一の要素として貼り付けるか、\\n既存のテキストエディタに貼り付け\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"透明\",\n    \"black\": \"黒\",\n    \"white\": \"白\",\n    \"red\": \"赤\",\n    \"pink\": \"ピンク\",\n    \"grape\": \"グレープ\",\n    \"violet\": \"バイオレット\",\n    \"gray\": \"灰色\",\n    \"blue\": \"青\",\n    \"cyan\": \"シアン\",\n    \"teal\": \"ティール\",\n    \"green\": \"緑\",\n    \"yellow\": \"黄\",\n    \"orange\": \"オレンジ\",\n    \"bronze\": \"ブロンズ\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"すべてのデータはブラウザにローカル保存されます。\",\n      \"center_heading_plus\": \"代わりにExcalidraw+を開きますか？\",\n      \"menuHint\": \"エクスポート、設定、言語...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"エクスポート、設定、その他...\",\n      \"center_heading\": \"ダイアグラムを簡単に。\",\n      \"toolbarHint\": \"ツールを選んで描き始めよう！\",\n      \"helpHint\": \"ショートカットとヘルプ\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"最も使用されているカスタム色\",\n    \"colors\": \"色\",\n    \"shades\": \"影\",\n    \"hexCode\": \"Hexコード\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"画像としてエクスポート\",\n        \"button\": \"画像としてエクスポート\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"ディスクに保存\",\n        \"button\": \"ディスクに保存\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Excalidraw+にエクスポート\",\n        \"description\": \"Excalidraw+ ワークスペースにシーンを保存します。\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"ファイルからロード\",\n        \"button\": \"ファイルからロード\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"リンクからロード\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}