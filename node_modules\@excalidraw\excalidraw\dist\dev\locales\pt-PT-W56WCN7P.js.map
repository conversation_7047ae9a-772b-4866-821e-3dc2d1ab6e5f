{"version": 3, "sources": ["../../../locales/pt-PT.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Colar\",\n    \"pasteAsPlaintext\": \"Colar como texto simples\",\n    \"pasteCharts\": \"Colar gráficos\",\n    \"selectAll\": \"Selecionar tudo\",\n    \"multiSelect\": \"Adicionar elemento à seleção\",\n    \"moveCanvas\": \"Mover tela\",\n    \"cut\": \"Cortar\",\n    \"copy\": \"Copiar\",\n    \"copyAsPng\": \"Copiar para a área de transferência como PNG\",\n    \"copyAsSvg\": \"Copiar para a área de transferência como SVG\",\n    \"copyText\": \"Copiar para Área de Transferência como texto\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Trazer para o primeiro plano\",\n    \"sendToBack\": \"Enviar para o plano de fundo\",\n    \"bringToFront\": \"Trazer para o primeiro plano\",\n    \"sendBackward\": \"Enviar para trás\",\n    \"delete\": \"Apagar\",\n    \"copyStyles\": \"Copiar os estilos\",\n    \"pasteStyles\": \"Colar os estilos\",\n    \"stroke\": \"Contornos\",\n    \"background\": \"Fundo\",\n    \"fill\": \"Preenchimento\",\n    \"strokeWidth\": \"Espessura do traço\",\n    \"strokeStyle\": \"Estilo de traço\",\n    \"strokeStyle_solid\": \"Sólido\",\n    \"strokeStyle_dashed\": \"Tracejado\",\n    \"strokeStyle_dotted\": \"Pontilhado\",\n    \"sloppiness\": \"Desleixo\",\n    \"opacity\": \"Opacidade\",\n    \"textAlign\": \"Alinhamento do texto\",\n    \"edges\": \"Arestas\",\n    \"sharp\": \"Aguçado\",\n    \"round\": \"Redondo\",\n    \"arrowheads\": \"Pontas\",\n    \"arrowhead_none\": \"Nenhuma\",\n    \"arrowhead_arrow\": \"Seta\",\n    \"arrowhead_bar\": \"Barra\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Triângulo\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Tamanho da fonte\",\n    \"fontFamily\": \"Família da fontes\",\n    \"addWatermark\": \"Adicionar \\\"Feito com Excalidraw\\\"\",\n    \"handDrawn\": \"Manuscrito\",\n    \"normal\": \"Normal\",\n    \"code\": \"Código\",\n    \"small\": \"Pequeno\",\n    \"medium\": \"Médio\",\n    \"large\": \"Grande\",\n    \"veryLarge\": \"Muito grande\",\n    \"solid\": \"Sólido\",\n    \"hachure\": \"Eclosão\",\n    \"zigzag\": \"ziguezague\",\n    \"crossHatch\": \"Sombreado\",\n    \"thin\": \"Fino\",\n    \"bold\": \"Espesso\",\n    \"left\": \"Esquerda\",\n    \"center\": \"Centralizar\",\n    \"right\": \"Direita\",\n    \"extraBold\": \"Muito espesso\",\n    \"architect\": \"Arquitecto\",\n    \"artist\": \"Artista\",\n    \"cartoonist\": \"Caricaturista\",\n    \"fileTitle\": \"Nome do ficheiro\",\n    \"colorPicker\": \"Seletor de cores\",\n    \"canvasColors\": \"Usado na tela\",\n    \"canvasBackground\": \"Fundo da área de desenho\",\n    \"drawingCanvas\": \"Área de desenho\",\n    \"layers\": \"Camadas\",\n    \"actions\": \"Ações\",\n    \"language\": \"Idioma\",\n    \"liveCollaboration\": \"Colaboração ao vivo...\",\n    \"duplicateSelection\": \"Duplicar\",\n    \"untitled\": \"Sem título\",\n    \"name\": \"Nome\",\n    \"yourName\": \"O seu nome\",\n    \"madeWithExcalidraw\": \"Feito com Excalidraw\",\n    \"group\": \"Agrupar seleção\",\n    \"ungroup\": \"Desagrupar seleção\",\n    \"collaborators\": \"Colaboradores\",\n    \"showGrid\": \"Mostrar grelha\",\n    \"addToLibrary\": \"Adicionar à biblioteca\",\n    \"removeFromLibrary\": \"Remover da biblioteca\",\n    \"libraryLoadingMessage\": \"A carregar a biblioteca…\",\n    \"libraries\": \"Procurar bibliotecas\",\n    \"loadingScene\": \"A carregar a cena…\",\n    \"align\": \"Alinhamento\",\n    \"alignTop\": \"Alinhar ao topo\",\n    \"alignBottom\": \"Alinhar ao fundo\",\n    \"alignLeft\": \"Alinhar à esquerda\",\n    \"alignRight\": \"Alinhar à direita\",\n    \"centerVertically\": \"Centrar verticalmente\",\n    \"centerHorizontally\": \"Centrar horizontalmente\",\n    \"distributeHorizontally\": \"Distribuir horizontalmente\",\n    \"distributeVertically\": \"Distribuir verticalmente\",\n    \"flipHorizontal\": \"Inverter horizontalmente\",\n    \"flipVertical\": \"Inverter verticalmente\",\n    \"viewMode\": \"Modo de visualização\",\n    \"share\": \"Partilhar\",\n    \"showStroke\": \"Mostrar seletor de cores do traço\",\n    \"showBackground\": \"Mostrar seletor de cores do fundo\",\n    \"toggleTheme\": \"Alternar tema\",\n    \"personalLib\": \"Biblioteca pessoal\",\n    \"excalidrawLib\": \"Biblioteca do Excalidraw\",\n    \"decreaseFontSize\": \"Reduzir o tamanho do tipo de letra\",\n    \"increaseFontSize\": \"Aumentar o tamanho do tipo de letra\",\n    \"unbindText\": \"Desvincular texto\",\n    \"bindText\": \"Ligar texto ao recipiente\",\n    \"createContainerFromText\": \"Envolver texto num recipiente\",\n    \"link\": {\n      \"edit\": \"Editar ligação\",\n      \"editEmbed\": \"\",\n      \"create\": \"Criar ligação\",\n      \"createEmbed\": \"\",\n      \"label\": \"Ligação\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Editar linha\",\n      \"exit\": \"Sair do editor de linha\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Bloquear\",\n      \"unlock\": \"Desbloquear\",\n      \"lockAll\": \"Bloquear todos\",\n      \"unlockAll\": \"Desbloquear todos\"\n    },\n    \"statusPublished\": \"Publicado\",\n    \"sidebarLock\": \"Manter a barra lateral aberta\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Ainda não foram adicionados nenhuns itens...\",\n    \"hint_emptyLibrary\": \"Seleccione um item na tela para adicioná-lo aqui, ou então instale uma biblioteca do repositório público abaixo.\",\n    \"hint_emptyPrivateLibrary\": \"Seleccione um item na tela para adicioná-lo aqui.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Limpar a área de desenho e redefinir a cor de fundo\",\n    \"exportJSON\": \"Exportar para ficheiro\",\n    \"exportImage\": \"Exportar imagem...\",\n    \"export\": \"Guardar para...\",\n    \"copyToClipboard\": \"Copiar para o clipboard\",\n    \"save\": \"Guardar no ficheiro atual\",\n    \"saveAs\": \"Guardar como\",\n    \"load\": \"Abrir\",\n    \"getShareableLink\": \"Obter um link de partilha\",\n    \"close\": \"Fechar\",\n    \"selectLanguage\": \"Selecionar idioma\",\n    \"scrollBackToContent\": \"Voltar ao conteúdo\",\n    \"zoomIn\": \"Aumentar zoom\",\n    \"zoomOut\": \"Diminuir zoom\",\n    \"resetZoom\": \"Redefinir zoom\",\n    \"menu\": \"Menu\",\n    \"done\": \"Concluído\",\n    \"edit\": \"Editar\",\n    \"undo\": \"Desfazer\",\n    \"redo\": \"Refazer\",\n    \"resetLibrary\": \"Repor a biblioteca\",\n    \"createNewRoom\": \"Criar nova sala\",\n    \"fullScreen\": \"Ecrã inteiro\",\n    \"darkMode\": \"Modo escuro\",\n    \"lightMode\": \"Modo claro\",\n    \"zenMode\": \"Modo zen\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Sair do modo zen\",\n    \"cancel\": \"Cancelar\",\n    \"clear\": \"Limpar\",\n    \"remove\": \"Remover\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Publicar\",\n    \"submit\": \"Enviar\",\n    \"confirm\": \"Confirmar\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Isto irá limpar toda a área de desenho. Tem a certeza?\",\n    \"couldNotCreateShareableLink\": \"Não foi possível criar um link partilhável.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Não foi possível criar um link partilhável: a cena é muito grande\",\n    \"couldNotLoadInvalidFile\": \"Não foi possível carregar o ficheiro inválido\",\n    \"importBackendFailed\": \"A importação do servidor falhou.\",\n    \"cannotExportEmptyCanvas\": \"Não é possível exportar uma área de desenho vazia.\",\n    \"couldNotCopyToClipboard\": \"Não foi possível copiar para a área de transferência.\",\n    \"decryptFailed\": \"Não foi possível desencriptar os dados.\",\n    \"uploadedSecurly\": \"O upload foi protegido com criptografia de ponta a ponta, o que significa que o servidor do Excalidraw e terceiros não podem ler o conteúdo.\",\n    \"loadSceneOverridePrompt\": \"Se carregar um desenho externo substituirá o conteúdo existente. Quer continuar?\",\n    \"collabStopOverridePrompt\": \"Ao interromper a sessão irá substituir o último desenho guardado. Tem a certeza?\\n\\n(Caso queira manter o último desenho, simplesmente feche a janela do navegador.)\",\n    \"errorAddingToLibrary\": \"Não foi possível adicionar o item à biblioteca\",\n    \"errorRemovingFromLibrary\": \"Não foi possível remover o item da biblioteca\",\n    \"confirmAddLibrary\": \"Isso adicionará {{numShapes}} forma(s) à sua biblioteca. Tem a certeza?\",\n    \"imageDoesNotContainScene\": \"Esta imagem parece não conter dados de cenas. Ativou a incorporação da cena durante a exportação?\",\n    \"cannotRestoreFromImage\": \"Não foi possível restaurar a cena deste ficheiro de imagem\",\n    \"invalidSceneUrl\": \"Não foi possível importar a cena a partir do URL fornecido. Ou está mal formado ou não contém dados JSON do Excalidraw válidos.\",\n    \"resetLibrary\": \"Isto irá limpar a sua biblioteca. Tem a certeza?\",\n    \"removeItemsFromsLibrary\": \"Apagar {{count}} item(ns) da biblioteca?\",\n    \"invalidEncryptionKey\": \"Chave de encriptação deve ter 22 caracteres. A colaboração ao vivo está desativada.\",\n    \"collabOfflineWarning\": \"Sem ligação à internet disponível.\\nAs suas alterações não serão salvas!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Tipo de ficheiro não suportado.\",\n    \"imageInsertError\": \"Não foi possível inserir a imagem, tente novamente mais tarde...\",\n    \"fileTooBig\": \"O ficheiro é muito grande. O tamanho máximo permitido é {{maxSize}}.\",\n    \"svgImageInsertError\": \"Não foi possível inserir a imagem SVG. A marcação SVG parece inválida.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"SVG inválido.\",\n    \"cannotResolveCollabServer\": \"Não foi possível fazer a ligação ao servidor colaborativo. Por favor, volte a carregar a página e tente novamente.\",\n    \"importLibraryError\": \"Não foi possível carregar a biblioteca\",\n    \"collabSaveFailed\": \"Não foi possível guardar na base de dados de backend. Se os problemas persistirem, guarde o ficheiro localmente para garantir que não perde o seu trabalho.\",\n    \"collabSaveFailed_sizeExceeded\": \"Não foi possível guardar na base de dados de backend, o ecrã parece estar muito grande. Deve guardar o ficheiro localmente para garantir que não perde o seu trabalho.\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Seleção\",\n    \"image\": \"Inserir imagem\",\n    \"rectangle\": \"Retângulo\",\n    \"diamond\": \"Losango\",\n    \"ellipse\": \"Elipse\",\n    \"arrow\": \"Flecha\",\n    \"line\": \"Linha\",\n    \"freedraw\": \"Desenhar\",\n    \"text\": \"Texto\",\n    \"library\": \"Biblioteca\",\n    \"lock\": \"Manter a ferramenta selecionada ativa após desenhar\",\n    \"penMode\": \"Modo caneta - impedir toque\",\n    \"link\": \"Acrescentar/ Adicionar ligação para uma forma seleccionada\",\n    \"eraser\": \"Borracha\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"Mão (ferramenta de movimento da tela)\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Ações da área de desenho\",\n    \"selectedShapeActions\": \"Ações das formas selecionadas\",\n    \"shapes\": \"Formas\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Para mover a tela, carregue na roda do rato ou na barra de espaço enquanto arrasta, ou use a ferramenta da mão\",\n    \"linearElement\": \"Clique para iniciar vários pontos, arraste para uma única linha\",\n    \"freeDraw\": \"Clique e arraste, large quando terminar\",\n    \"text\": \"Dica: também pode adicionar texto clicando duas vezes em qualquer lugar com a ferramenta de seleção\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"Clique duas vezes ou pressione a tecla Enter para editar o texto\",\n    \"text_editing\": \"Pressione a tecla Escape ou CtrlOrCmd+ENTER para terminar a edição\",\n    \"linearElementMulti\": \"Clique no último ponto ou pressione Escape ou Enter para terminar\",\n    \"lockAngle\": \"Pode restringir o ângulo mantendo premida a tecla SHIFT\",\n    \"resize\": \"Pode restringir as proporções mantendo a tecla SHIFT premida enquanto redimensiona,\\nmantenha a tecla ALT premida para redimensionar a partir do centro\",\n    \"resizeImage\": \"Pode redimensionar livremente mantendo pressionada a tecla SHIFT,\\nmantenha pressionada a tecla ALT para redimensionar do centro\",\n    \"rotate\": \"Pode restringir os ângulos mantendo a tecla SHIFT premida enquanto roda\",\n    \"lineEditor_info\": \"Pressione CtrlOrCmd e faça um duplo-clique ou pressione CtrlOrCmd + Enter para editar pontos\",\n    \"lineEditor_pointSelected\": \"Carregue na tecla Delete para remover o(s) ponto(s), CtrlOuCmd+D para duplicar, ou arraste para mover\",\n    \"lineEditor_nothingSelected\": \"Seleccione um ponto para editar (carregue em SHIFT para seleccionar vários),\\nou carregue em Alt e clique para acrescentar novos pontos\",\n    \"placeImage\": \"Clique para colocar a imagem ou clique e arraste para definir o seu tamanho manualmente\",\n    \"publishLibrary\": \"Publique a sua própria biblioteca\",\n    \"bindTextToElement\": \"Carregue Enter para acrescentar texto\",\n    \"deepBoxSelect\": \"Mantenha a tecla CtrlOrCmd carregada para selecção profunda, impedindo o arrastamento\",\n    \"eraserRevert\": \"Carregue também em Alt para reverter os elementos marcados para serem apagados\",\n    \"firefox_clipboard_write\": \"Esta função pode provavelmente ser ativada definindo a opção \\\"dom.events.asyncClipboard.clipboardItem\\\" como \\\"true\\\". Para alterar os sinalizadores do navegador no Firefox, visite a página \\\"about:config\\\".\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Não é possível mostrar uma pré-visualização\",\n    \"canvasTooBig\": \"A área de desenho pode ser muito grande.\",\n    \"canvasTooBigTip\": \"Dica: tente aproximar um pouco os elementos mais distantes.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Foi encontrado um erro. Tente <button>recarregar a página.</button>\",\n    \"clearCanvasMessage\": \"Se a recarga não funcionar, tente <button>a limpar a área de desenho.</button>\",\n    \"clearCanvasCaveat\": \" Isso resultará em perda de trabalho \",\n    \"trackedToSentry\": \"O erro com o identificador {{eventId}} foi rastreado no nosso sistema.\",\n    \"openIssueMessage\": \"Fomos muito cautelosos para não incluir suas informações de cena no erro. Se sua cena não for privada, por favor, considere seguir nosso <button>rastreador de bugs.</button> Por favor, inclua informações abaixo, copiando e colando no relatório de erros no GitHub.\",\n    \"sceneContent\": \"Conteúdo da cena:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Pode convidar pessoas para colaborarem na sua cena atual.\",\n    \"desc_privacy\": \"Não se preocupe, a sessão usa criptografia de ponta-a-ponta, por isso o que desenhar permanecerá privado. Nem mesmo o nosso servidor poderá ver o que cria.\",\n    \"button_startSession\": \"Iniciar sessão\",\n    \"button_stopSession\": \"Parar sessão\",\n    \"desc_inProgressIntro\": \"A sessão de colaboração ao vivo está agora em andamento.\",\n    \"desc_shareLink\": \"Partilhe este link com qualquer pessoa com quem queira colaborar:\",\n    \"desc_exitSession\": \"Interrompendo a sessão irá desconectar-se da sala, mas poderá continuar a trabalhar com a cena localmente. Note que isso não afetará outras pessoas e elas ainda poderão colaborar nas versões deles.\",\n    \"shareTitle\": \"Participe numa sessão de colaboração ao vivo no Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Erro\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Guardar no disco\",\n    \"disk_details\": \"Exportar os dados da cena para um ficheiro do qual poderá importar mais tarde.\",\n    \"disk_button\": \"Guardar num ficheiro\",\n    \"link_title\": \"Link partilhável\",\n    \"link_details\": \"Exportar como um link de apenas leitura.\",\n    \"link_button\": \"Exportar para link\",\n    \"excalidrawplus_description\": \"Guardar a cena no seu espaço de trabalho Excalidraw+\",\n    \"excalidrawplus_button\": \"Exportar\",\n    \"excalidrawplus_exportError\": \"Não foi possível exportar para o Excalidraw+ neste momento...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Leia o nosso blogue\",\n    \"click\": \"clicar\",\n    \"deepSelect\": \"Selecção profunda\",\n    \"deepBoxSelect\": \"Selecção profunda dentro da caixa, impedindo que seja arrastada\",\n    \"curvedArrow\": \"Seta curva\",\n    \"curvedLine\": \"Linha curva\",\n    \"documentation\": \"Documentação\",\n    \"doubleClick\": \"clique duplo\",\n    \"drag\": \"arrastar\",\n    \"editor\": \"Editor\",\n    \"editLineArrowPoints\": \"Editar pontos de linha/seta\",\n    \"editText\": \"Editar texto / adicionar etiqueta\",\n    \"github\": \"Encontrou algum problema? Informe-nos\",\n    \"howto\": \"Siga os nossos guias\",\n    \"or\": \"ou\",\n    \"preventBinding\": \"Prevenir fixação de seta\",\n    \"tools\": \"Ferramentas\",\n    \"shortcuts\": \"Atalhos de teclado\",\n    \"textFinish\": \"Finalizar edição (editor texto)\",\n    \"textNewLine\": \"Adicionar nova linha (editor de texto)\",\n    \"title\": \"Ajuda\",\n    \"view\": \"Visualizar\",\n    \"zoomToFit\": \"Ajustar para todos os elementos caberem\",\n    \"zoomToSelection\": \"Ampliar a seleção\",\n    \"toggleElementLock\": \"Trancar/destrancar selecção\",\n    \"movePageUpDown\": \"Mover página para cima / baixo\",\n    \"movePageLeftRight\": \"Mover página para esquerda / direita\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Apagar tela\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Publicar biblioteca\",\n    \"itemName\": \"Nome do item\",\n    \"authorName\": \"Nome do autor\",\n    \"githubUsername\": \"Nome de utilizador do GitHub\",\n    \"twitterUsername\": \"Nome de utilizador no Twitter\",\n    \"libraryName\": \"Nome da biblioteca\",\n    \"libraryDesc\": \"Descrição da biblioteca\",\n    \"website\": \"Página web\",\n    \"placeholder\": {\n      \"authorName\": \"Introduza o seu nome ou nome de utilizador\",\n      \"libraryName\": \"Nome da sua biblioteca\",\n      \"libraryDesc\": \"Descrição da sua biblioteca para ajudar as pessoas a entender a utilização dela\",\n      \"githubHandle\": \"Identificador do GitHub (opcional), para que possa editar a biblioteca depois desta ser enviada para revisão\",\n      \"twitterHandle\": \"Nome do Twitter (opcional), para que saibamos quem merece os créditos na promoção via Twitter\",\n      \"website\": \"Ligação para a sua página pessoal ou qualquer outra (opcional)\"\n    },\n    \"errors\": {\n      \"required\": \"Obrigatório\",\n      \"website\": \"Introduza um URL válido\"\n    },\n    \"noteDescription\": \"Envie a sua biblioteca para ser incluída no <link>repositório de bibliotecas públicas</link>para outras pessoas a poderem usar nos seus próprios desenhos.\",\n    \"noteGuidelines\": \"A biblioteca precisa ser aprovada manualmente primeiro. Por favor, leia <link>orientações</link> antes de enviar. Vai precisar de uma conta no GitHub para comunicar e fazer alterações se solicitado, mas não é estritamente necessária.\",\n    \"noteLicense\": \"Ao enviar, concorda que a biblioteca será publicada sob a <link>Licença MIT, </link>o que significa, de forma resumida, que qualquer pessoa pode utilizá-la sem restrições.\",\n    \"noteItems\": \"Cada item da biblioteca deve ter o seu próprio nome para que este seja pesquisável com filtros. Os seguintes itens da biblioteca serão incluídos:\",\n    \"atleastOneLibItem\": \"Por favor, seleccione pelo menos um item da biblioteca para começar\",\n    \"republishWarning\": \"Nota: alguns dos itens seleccionados estão marcados como já publicados/enviados. Só deve reenviar itens ao actualizar uma biblioteca existente ou submissão.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Biblioteca enviada\",\n    \"content\": \"Obrigado {{authorName}}. A sua biblioteca foi enviada para análise. Pode acompanhar o status<link>aqui</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Repor a biblioteca\",\n    \"removeItemsFromLib\": \"Remover os itens seleccionados da biblioteca\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Exportar imagem\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"Cena embutida\",\n      \"scale\": \"\",\n      \"padding\": \"Espaçamento\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Exportar em PNG\",\n      \"exportToSvg\": \"Exportar em SVG\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Os seus desenhos são encriptados de ponta-a-ponta, por isso os servidores do Excalidraw nunca os verão.\",\n    \"link\": \"Publicação de blogue na encriptação ponta-a-ponta no Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Ângulo\",\n    \"element\": \"Elemento\",\n    \"elements\": \"Elementos\",\n    \"height\": \"Altura\",\n    \"scene\": \"Cena\",\n    \"selected\": \"Selecionado\",\n    \"storage\": \"Armazenamento\",\n    \"title\": \"Estatísticas para nerds\",\n    \"total\": \"Total\",\n    \"version\": \"Versão\",\n    \"versionCopy\": \"Clique para copiar\",\n    \"versionNotAvailable\": \"Versão não disponível\",\n    \"width\": \"Largura\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Acrescentado à biblioteca\",\n    \"copyStyles\": \"Estilos copiados.\",\n    \"copyToClipboard\": \"Copiado para a área de transferência.\",\n    \"copyToClipboardAsPng\": \"{{exportSelection}} copiado para a área de transferência como PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Ficheiro guardado.\",\n    \"fileSavedToFilename\": \"Guardado como {filename}\",\n    \"canvas\": \"área de desenho\",\n    \"selection\": \"seleção\",\n    \"pasteAsSingleElement\": \"Usar {{shortcut}} para colar como um único elemento,\\nou colar num editor de texto existente\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Transparente\",\n    \"black\": \"Preto\",\n    \"white\": \"Branco\",\n    \"red\": \"Vermelho\",\n    \"pink\": \"Rosa\",\n    \"grape\": \"Uva\",\n    \"violet\": \"Violeta\",\n    \"gray\": \"Cinza\",\n    \"blue\": \"Azul\",\n    \"cyan\": \"\",\n    \"teal\": \"\",\n    \"green\": \"Verde\",\n    \"yellow\": \"Amarelo\",\n    \"orange\": \"Laranja\",\n    \"bronze\": \"Bronze\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Todos os dados são guardados no seu navegador local.\",\n      \"center_heading_plus\": \"Queria antes ir para o Excalidraw+?\",\n      \"menuHint\": \"Exportar, preferências, idiomas...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Exportar, preferências e outros...\",\n      \"center_heading\": \"Diagramas. Feito. Simples.\",\n      \"toolbarHint\": \"Escolha uma ferramenta e comece a desenhar!\",\n      \"helpHint\": \"Atalhos e ajuda\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"Cores\",\n    \"shades\": \"Tons\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Guardar no disco\",\n        \"button\": \"Guardar no disco\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Carregar a partir de ficheiro\",\n        \"button\": \"Carregar a partir de ficheiro\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}