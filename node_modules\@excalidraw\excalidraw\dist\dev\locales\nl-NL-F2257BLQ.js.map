{"version": 3, "sources": ["../../../locales/nl-NL.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Plakken\",\n    \"pasteAsPlaintext\": \"Plakken als platte tekst\",\n    \"pasteCharts\": \"Plak grafieken\",\n    \"selectAll\": \"Alles selecteren\",\n    \"multiSelect\": \"Voeg element toe aan selectie\",\n    \"moveCanvas\": \"Canvas verplaatsen\",\n    \"cut\": \"Knip\",\n    \"copy\": \"Kopiëren\",\n    \"copyAsPng\": \"Kopieer als PNG\",\n    \"copyAsSvg\": \"Kopieer naar klembord als SVG\",\n    \"copyText\": \"Kopieer naar klembord als tekst\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Breng naar voren\",\n    \"sendToBack\": \"Stuur naar achtergrond\",\n    \"bringToFront\": \"Breng naar voorgrond\",\n    \"sendBackward\": \"Breng naar achter\",\n    \"delete\": \"Verwijderen\",\n    \"copyStyles\": \"<PERSON><PERSON><PERSON> opmaak\",\n    \"pasteStyles\": \"<PERSON>lak opmaak\",\n    \"stroke\": \"Lijn\",\n    \"background\": \"Acht<PERSON>grond\",\n    \"fill\": \"Invulling\",\n    \"strokeWidth\": \"Lijnbreedte\",\n    \"strokeStyle\": \"Lijnstijl\",\n    \"strokeStyle_solid\": \"Ononderbroken\",\n    \"strokeStyle_dashed\": \"Gestreept\",\n    \"strokeStyle_dotted\": \"Gestippeld\",\n    \"sloppiness\": \"Slordigheid\",\n    \"opacity\": \"Doorzichtigheid\",\n    \"textAlign\": \"Uitlijning\",\n    \"edges\": \"Randen\",\n    \"sharp\": \"Hoekig\",\n    \"round\": \"Rond\",\n    \"arrowheads\": \"Pijlpunten\",\n    \"arrowhead_none\": \"Geen\",\n    \"arrowhead_arrow\": \"Pijl\",\n    \"arrowhead_bar\": \"Balk\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Driehoek\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Tekstgrootte\",\n    \"fontFamily\": \"Lettertype\",\n    \"addWatermark\": \"Voeg \\\"Gemaakt met Excalidraw\\\" toe\",\n    \"handDrawn\": \"Handgetekend\",\n    \"normal\": \"Normaal\",\n    \"code\": \"Code\",\n    \"small\": \"Klein\",\n    \"medium\": \"Medium\",\n    \"large\": \"Groot\",\n    \"veryLarge\": \"Zeer groot\",\n    \"solid\": \"Ingekleurd\",\n    \"hachure\": \"Arcering\",\n    \"zigzag\": \"\",\n    \"crossHatch\": \"Tweemaal gearceerd\",\n    \"thin\": \"Dun\",\n    \"bold\": \"Vet\",\n    \"left\": \"Links\",\n    \"center\": \"Midden\",\n    \"right\": \"Rechts\",\n    \"extraBold\": \"Zwaar\",\n    \"architect\": \"Architect\",\n    \"artist\": \"Artiest\",\n    \"cartoonist\": \"Cartoonist\",\n    \"fileTitle\": \"Bestandsnaam\",\n    \"colorPicker\": \"Kleurenkiezer\",\n    \"canvasColors\": \"Gebruikt op canvas\",\n    \"canvasBackground\": \"Canvas achtergrond\",\n    \"drawingCanvas\": \"Canvas\",\n    \"layers\": \"Lagen\",\n    \"actions\": \"Acties\",\n    \"language\": \"Taal\",\n    \"liveCollaboration\": \"Live Samenwerking...\",\n    \"duplicateSelection\": \"Dupliceer\",\n    \"untitled\": \"Naamloos\",\n    \"name\": \"Naam\",\n    \"yourName\": \"Jouw naam\",\n    \"madeWithExcalidraw\": \"Gemaakt met Excalidraw\",\n    \"group\": \"Groeperen\",\n    \"ungroup\": \"Groep opheffen\",\n    \"collaborators\": \"Deelnemers\",\n    \"showGrid\": \"Raster weergeven\",\n    \"addToLibrary\": \"Voeg toe aan bibliotheek\",\n    \"removeFromLibrary\": \"Verwijder uit bibliotheek\",\n    \"libraryLoadingMessage\": \"Bibliotheek laden…\",\n    \"libraries\": \"Blader door bibliotheken\",\n    \"loadingScene\": \"Scène laden…\",\n    \"align\": \"Uitlijnen\",\n    \"alignTop\": \"Boven uitlijnen\",\n    \"alignBottom\": \"Onder uitlijnen\",\n    \"alignLeft\": \"Links uitlijnen\",\n    \"alignRight\": \"Rechts uitlijnen\",\n    \"centerVertically\": \"Verticaal Centreren\",\n    \"centerHorizontally\": \"Horizontaal Centreren\",\n    \"distributeHorizontally\": \"Horizontaal verspreiden\",\n    \"distributeVertically\": \"Verticaal distribueren\",\n    \"flipHorizontal\": \"Horizontaal spiegelen\",\n    \"flipVertical\": \"Verticaal spiegelen\",\n    \"viewMode\": \"Weergavemodus\",\n    \"share\": \"Deel\",\n    \"showStroke\": \"Toon lijn kleur kiezer\",\n    \"showBackground\": \"Toon achtergrondkleur kiezer\",\n    \"toggleTheme\": \"Thema aan/uit\",\n    \"personalLib\": \"Persoonlijke bibliotheek\",\n    \"excalidrawLib\": \"Excalidraw bibliotheek\",\n    \"decreaseFontSize\": \"Letters verkleinen\",\n    \"increaseFontSize\": \"Letters vergroten\",\n    \"unbindText\": \"Ontkoppel tekst\",\n    \"bindText\": \"Koppel tekst aan de container\",\n    \"createContainerFromText\": \"\",\n    \"link\": {\n      \"edit\": \"Wijzig link\",\n      \"editEmbed\": \"Link bewerken & insluiten\",\n      \"create\": \"Maak link\",\n      \"createEmbed\": \"Link maken en insluiten\",\n      \"label\": \"Link\",\n      \"labelEmbed\": \"Link toevoegen & insluiten\",\n      \"empty\": \"Er is geen link ingesteld\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Bewerk regel\",\n      \"exit\": \"Verlaat regel-editor\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Vergrendel\",\n      \"unlock\": \"Ontgrendel\",\n      \"lockAll\": \"Vergrendel alles\",\n      \"unlockAll\": \"Ontgrendel alles\"\n    },\n    \"statusPublished\": \"Gepubliceerd\",\n    \"sidebarLock\": \"Zijbalk open houden\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Nog geen items toegevoegd...\",\n    \"hint_emptyLibrary\": \"Selecteer een item op het canvas om het hier toe te voegen of installeer een bibliotheek uit de openbare repository, hieronder.\",\n    \"hint_emptyPrivateLibrary\": \"Selecteer een item op het canvas om het hier toe te voegen.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Canvas opnieuw instellen\",\n    \"exportJSON\": \"Exporteren naar bestand\",\n    \"exportImage\": \"Exporteer afbeelding...\",\n    \"export\": \"Sla op...\",\n    \"copyToClipboard\": \"Kopieer\",\n    \"save\": \"Opslaan naar huidige bestand\",\n    \"saveAs\": \"Opslaan als\",\n    \"load\": \"Open\",\n    \"getShareableLink\": \"Maak een deelbare link\",\n    \"close\": \"Sluiten\",\n    \"selectLanguage\": \"Taal selecteren\",\n    \"scrollBackToContent\": \"Scroll terug naar inhoud\",\n    \"zoomIn\": \"Inzoomen\",\n    \"zoomOut\": \"Uitzoomen\",\n    \"resetZoom\": \"Zoom terugzetten\",\n    \"menu\": \"Menu\",\n    \"done\": \"Klaar\",\n    \"edit\": \"Bewerken\",\n    \"undo\": \"Ongedaan maken\",\n    \"redo\": \"Herstel ongedaan maken\",\n    \"resetLibrary\": \"Bibliotheek Resetten\",\n    \"createNewRoom\": \"Creëer live-samenwerkingssessie\",\n    \"fullScreen\": \"Volledig scherm\",\n    \"darkMode\": \"Donkere modus\",\n    \"lightMode\": \"Lichte modus\",\n    \"zenMode\": \"Zen modus\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"Verlaat zen modus\",\n    \"cancel\": \"Annuleren\",\n    \"clear\": \"Wissen\",\n    \"remove\": \"Verwijderen\",\n    \"embed\": \"Insluiten in-/uitschakelen\",\n    \"publishLibrary\": \"Publiceren\",\n    \"submit\": \"Versturen\",\n    \"confirm\": \"Bevestigen\",\n    \"embeddableInteractionButton\": \"Klik voor interactie\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Dit zal het hele canvas verwijderen. Weet je het zeker?\",\n    \"couldNotCreateShareableLink\": \"Kon geen deelbare link aanmaken.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Kan geen deelbare link aanmaken: de scène is te groot\",\n    \"couldNotLoadInvalidFile\": \"Kan ongeldig bestand niet laden\",\n    \"importBackendFailed\": \"Importeren vanuit backend mislukt.\",\n    \"cannotExportEmptyCanvas\": \"Kan geen leeg canvas exporteren.\",\n    \"couldNotCopyToClipboard\": \"Kon niet naar klembord kopiëren.\",\n    \"decryptFailed\": \"Kan gegevens niet decoderen.\",\n    \"uploadedSecurly\": \"De upload is beveiligd met end-to-end encryptie, wat betekent dat de Excalidraw server en derden de inhoud niet kunnen lezen.\",\n    \"loadSceneOverridePrompt\": \"Het inladen van een externe tekening zal je bestaande inhoud vervangen. Wil je verdergaan?\",\n    \"collabStopOverridePrompt\": \"Wanneer de sessie wordt gestopt, overschrijft u de eerdere, lokaal opgeslagen tekening. Weet je het zeker?\\n\\n(Als je de lokale tekening wilt behouden, sluit je in plaats daarvan het browsertabblad)\",\n    \"errorAddingToLibrary\": \"Kan item niet toevoegen aan de bibliotheek\",\n    \"errorRemovingFromLibrary\": \"Kan item niet uit de bibliotheek verwijderen\",\n    \"confirmAddLibrary\": \"Dit zal {{numShapes}} vorm(en) toevoegen aan je bibliotheek. Weet je het zeker?\",\n    \"imageDoesNotContainScene\": \"Deze afbeelding lijkt geen scène gegevens te bevatten. Heb je scène embedding tijdens het exporteren ingeschakeld?\",\n    \"cannotRestoreFromImage\": \"Scène kan niet worden hersteld vanuit dit afbeeldingsbestand\",\n    \"invalidSceneUrl\": \"Kan scène niet importeren vanuit de opgegeven URL. Het is onjuist of bevat geen geldige Excalidraw JSON-gegevens.\",\n    \"resetLibrary\": \"Dit zal je bibliotheek wissen. Weet je het zeker?\",\n    \"removeItemsFromsLibrary\": \"Verwijder {{count}} item(s) uit bibliotheek?\",\n    \"invalidEncryptionKey\": \"Encryptiesleutel moet 22 tekens zijn. Live samenwerking is uitgeschakeld.\",\n    \"collabOfflineWarning\": \"Geen internetverbinding beschikbaar.\\nJe wijzigingen worden niet opgeslagen!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Niet-ondersteund bestandstype.\",\n    \"imageInsertError\": \"Afbeelding invoegen mislukt. Probeer het later opnieuw...\",\n    \"fileTooBig\": \"Bestand is te groot. Maximale grootte is {{maxSize}}.\",\n    \"svgImageInsertError\": \"Kon geen SVG-afbeelding invoegen. De SVG-opmaak ziet er niet geldig uit.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"Ongeldige SVG.\",\n    \"cannotResolveCollabServer\": \"Kan geen verbinding maken met de collab server. Herlaad de pagina en probeer het opnieuw.\",\n    \"importLibraryError\": \"Kon bibliotheek niet laden\",\n    \"collabSaveFailed\": \"Kan niet opslaan in de backend database. Als de problemen blijven bestaan, moet u het bestand lokaal opslaan om ervoor te zorgen dat u uw werk niet verliest.\",\n    \"collabSaveFailed_sizeExceeded\": \"Kan de backend database niet opslaan, het canvas lijkt te groot te zijn. U moet het bestand lokaal opslaan om ervoor te zorgen dat u uw werk niet verliest.\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"Ingesloten elementen kunnen niet worden toegevoegd aan de bibliotheek.\",\n      \"iframe\": \"\",\n      \"image\": \"Ondersteuning voor het toevoegen van afbeeldingen aan de bibliotheek komt binnenkort!\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Selectie\",\n    \"image\": \"Voeg afbeelding in\",\n    \"rectangle\": \"Rechthoek\",\n    \"diamond\": \"Ruit\",\n    \"ellipse\": \"Ovaal\",\n    \"arrow\": \"Pijl\",\n    \"line\": \"Lijn\",\n    \"freedraw\": \"Teken\",\n    \"text\": \"Tekst\",\n    \"library\": \"Bibliotheek\",\n    \"lock\": \"Geselecteerde tool actief houden na tekenen\",\n    \"penMode\": \"Pen modus - Blokkeer aanraken\",\n    \"link\": \"Link toevoegen / bijwerken voor een geselecteerde vorm\",\n    \"eraser\": \"Gum\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"Web insluiten\",\n    \"laser\": \"\",\n    \"hand\": \"\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Canvasacties\",\n    \"selectedShapeActions\": \"Acties van geselecteerde vorm\",\n    \"shapes\": \"Vormen\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Om de canvas te verplaatsen, houd muiswiel of spatiebalk ingedrukt tijdens slepen, of gebruik het handgereedschap\",\n    \"linearElement\": \"Klik om meerdere punten te starten, sleep voor één lijn\",\n    \"freeDraw\": \"Klik en sleep, laat los als je klaar bent\",\n    \"text\": \"Tip: je kunt tekst toevoegen door ergens dubbel te klikken met de selectietool\",\n    \"embeddable\": \"Klink-sleep om een website-insluiting te maken\",\n    \"text_selected\": \"Dubbelklik of druk op ENTER om tekst te bewerken\",\n    \"text_editing\": \"Druk op Escape of CtrlOrCmd+ENTER om het bewerken te voltooien\",\n    \"linearElementMulti\": \"Klik op het laatste punt of druk op Escape of Enter om te stoppen\",\n    \"lockAngle\": \"Je kunt de hoek beperken door SHIFT ingedrukt te houden\",\n    \"resize\": \"Houd tijdens het vergroten SHIFT ingedrukt om verhoudingen te behouden,\\ngebruik ALT om vanuit het midden te vergroten/verkleinen\",\n    \"resizeImage\": \"\",\n    \"rotate\": \"Je kan hoeken beperken door SHIFT ingedrukt te houden wanneer je draait\",\n    \"lineEditor_info\": \"Houd CtrlOrCmd en Dubbelklik of druk op CtrlOrCmd + Enter om punten te bewerken\",\n    \"lineEditor_pointSelected\": \"\",\n    \"lineEditor_nothingSelected\": \"\",\n    \"placeImage\": \"\",\n    \"publishLibrary\": \"Publiceer je eigen bibliotheek\",\n    \"bindTextToElement\": \"Druk op enter om tekst toe te voegen\",\n    \"deepBoxSelect\": \"\",\n    \"eraserRevert\": \"\",\n    \"firefox_clipboard_write\": \"\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Kan voorbeeld niet tonen\",\n    \"canvasTooBig\": \"Het canvas is mogelijk te groot.\",\n    \"canvasTooBigTip\": \"Tip: beweeg de verste elementen iets dichter bij elkaar.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Fout opgetreden. Probeer <button>de pagina opnieuw laden.</button>\",\n    \"clearCanvasMessage\": \"Als herladen niet werkt, probeer <button>het canvas te wissen.</button>\",\n    \"clearCanvasCaveat\": \" Dit zal leiden tot verlies van je werk \",\n    \"trackedToSentry\": \"De fout met ID {{eventId}} was gevolgd op ons systeem.\",\n    \"openIssueMessage\": \"We waren voorzichtig om je scène-informatie niet in de fout toe te voegen. Als je scène niet privé is, overweeg dan alstublieft het opvolgen op onze <button>bugtracker.</button> Kopieer de informatie hieronder naar de GitHub issue.\",\n    \"sceneContent\": \"Scène-inhoud:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Je kunt mensen uitnodigen om met je samen te werken.\",\n    \"desc_privacy\": \"Geen zorgen, de sessie gebruikt end-to-end encryptie, dus wat je tekent blijft privé. Zelfs onze server zal niet kunnen zien wat je tekent.\",\n    \"button_startSession\": \"Start sessie\",\n    \"button_stopSession\": \"Stop sessie\",\n    \"desc_inProgressIntro\": \"De live-samenwerkingssessie is nu gestart.\",\n    \"desc_shareLink\": \"Deel deze link met iedereen waarmee je wil samenwerken:\",\n    \"desc_exitSession\": \"Het stoppen van de sessie zal je loskoppelen van de kamer, maar je kunt lokaal doorwerken met de scène.\\nPas op: dit heeft geen invloed op andere mensen en dat zij nog steeds in staat zullen zijn om samen te werken aan hun versie.\",\n    \"shareTitle\": \"Neem deel aan een live samenwerkingssessie op Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Fout\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Opslaan op schijf\",\n    \"disk_details\": \"De scènegegevens exporteren naar een bestand waaruit u later kunt importeren.\",\n    \"disk_button\": \"Opslaan naar bestand\",\n    \"link_title\": \"Deelbare link\",\n    \"link_details\": \"Exporteren als een alleen-lezen link.\",\n    \"link_button\": \"Exporteer naar link\",\n    \"excalidrawplus_description\": \"Sla de scène op in je Excalidraw+ werkruimte.\",\n    \"excalidrawplus_button\": \"Exporteer\",\n    \"excalidrawplus_exportError\": \"Kan op dit moment niet exporteren naar Excalidraw+...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Lees onze blog\",\n    \"click\": \"klik\",\n    \"deepSelect\": \"Deep selecteer\",\n    \"deepBoxSelect\": \"\",\n    \"curvedArrow\": \"Gebogen pijl\",\n    \"curvedLine\": \"Kromme lijn\",\n    \"documentation\": \"Documentatie\",\n    \"doubleClick\": \"dubbelklikken\",\n    \"drag\": \"slepen\",\n    \"editor\": \"Editor\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"Probleem gevonden? Verzenden\",\n    \"howto\": \"Volg onze handleidingen\",\n    \"or\": \"of\",\n    \"preventBinding\": \"Pijlbinding voorkomen\",\n    \"tools\": \"Tools\",\n    \"shortcuts\": \"Sneltoetsen\",\n    \"textFinish\": \"Voltooi het bewerken (teksteditor)\",\n    \"textNewLine\": \"Nieuwe regel toevoegen (teksteditor)\",\n    \"title\": \"Help\",\n    \"view\": \"Weergave\",\n    \"zoomToFit\": \"Zoom in op alle elementen\",\n    \"zoomToSelection\": \"Inzoomen op selectie\",\n    \"toggleElementLock\": \"\",\n    \"movePageUpDown\": \"Pagina omhoog/omlaag\",\n    \"movePageLeftRight\": \"Verplaats pagina links/rechts\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Wis canvas\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Publiceer bibliotheek\",\n    \"itemName\": \"Itemnaam\",\n    \"authorName\": \"Naam auteur\",\n    \"githubUsername\": \"GitHub gebruikersnaam\",\n    \"twitterUsername\": \"Twitter gebruikersnaam\",\n    \"libraryName\": \"Naam bibliotheek\",\n    \"libraryDesc\": \"Beschrijving van de bibliotheek\",\n    \"website\": \"Website\",\n    \"placeholder\": {\n      \"authorName\": \"Je naam of gebruikersnaam\",\n      \"libraryName\": \"Naam van je bibliotheek\",\n      \"libraryDesc\": \"Beschrijving van je bibliotheek om mensen te helpen het gebruik ervan te begrijpen\",\n      \"githubHandle\": \"\",\n      \"twitterHandle\": \"\",\n      \"website\": \"Link naar je persoonlijke website of elders (optioneel)\"\n    },\n    \"errors\": {\n      \"required\": \"Vereist\",\n      \"website\": \"Vul een geldige URL in\"\n    },\n    \"noteDescription\": \"<link>openbare repository</link>\",\n    \"noteGuidelines\": \"<link>richtlijnen</link>\",\n    \"noteLicense\": \"<link>MIT-licentie, </link>\",\n    \"noteItems\": \"\",\n    \"atleastOneLibItem\": \"\",\n    \"republishWarning\": \"\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Bibliotheek ingediend\",\n    \"content\": \"<link>Hier</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Reset bibliotheek\",\n    \"removeItemsFromLib\": \"Verwijder geselecteerde items uit bibliotheek\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Je tekeningen zijn beveiligd met end-to-end encryptie, dus Excalidraw's servers zullen nooit zien wat je tekent.\",\n    \"link\": \"Blog post over end-to-end versleuteling in Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Hoek\",\n    \"element\": \"Element\",\n    \"elements\": \"Elementen\",\n    \"height\": \"Hoogte\",\n    \"scene\": \"Scene\",\n    \"selected\": \"Geselecteerd\",\n    \"storage\": \"Opslag\",\n    \"title\": \"Statistieken voor nerds\",\n    \"total\": \"Totaal\",\n    \"version\": \"Versie\",\n    \"versionCopy\": \"Klik om te kopiëren\",\n    \"versionNotAvailable\": \"Versie niet beschikbaar\",\n    \"width\": \"Breedte\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Toegevoegd aan bibliotheek\",\n    \"copyStyles\": \"Stijlen gekopieerd.\",\n    \"copyToClipboard\": \"Gekopieerd naar het klembord.\",\n    \"copyToClipboardAsPng\": \"{{exportSelection}} naar klembord gekopieerd als PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Bestand opgeslagen.\",\n    \"fileSavedToFilename\": \"Opgeslagen als {filename}\",\n    \"canvas\": \"canvas\",\n    \"selection\": \"selectie\",\n    \"pasteAsSingleElement\": \"Gebruik {{shortcut}} om te plakken als een enkel element,\\nof plak in een bestaande teksteditor\",\n    \"unableToEmbed\": \"Het insluiten van deze url is momenteel niet toegestaan. Zet een probleem op GitHub om de URL op de whitelist te zetten\",\n    \"unrecognizedLinkFormat\": \"De link die u hebt ingesloten komt niet overeen met het verwachte formaat. Probeer de 'embed' string van de bronsite te plakken\"\n  },\n  \"colors\": {\n    \"transparent\": \"Transparant\",\n    \"black\": \"\",\n    \"white\": \"\",\n    \"red\": \"\",\n    \"pink\": \"\",\n    \"grape\": \"\",\n    \"violet\": \"\",\n    \"gray\": \"\",\n    \"blue\": \"\",\n    \"cyan\": \"\",\n    \"teal\": \"\",\n    \"green\": \"\",\n    \"yellow\": \"\",\n    \"orange\": \"\",\n    \"bronze\": \"\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"\",\n      \"center_heading_plus\": \"\",\n      \"menuHint\": \"\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Exporteren, voorkeuren en meer...\",\n      \"center_heading\": \"Diagrammen. Eenvoudig. Gemaakt.\",\n      \"toolbarHint\": \"Kies een tool & begin met tekenen!\",\n      \"helpHint\": \"Snelkoppelingen en hulp\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"\",\n    \"shades\": \"\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}