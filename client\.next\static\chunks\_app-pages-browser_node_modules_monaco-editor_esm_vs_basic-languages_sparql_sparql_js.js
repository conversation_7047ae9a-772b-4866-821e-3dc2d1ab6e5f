"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_sparql_sparql_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/sparql/sparql.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/sparql/sparql.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/sparql/sparql.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"'\", close: \"'\", notIn: [\"string\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".rq\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.angle\", open: \"<\", close: \">\" }\n  ],\n  keywords: [\n    \"add\",\n    \"as\",\n    \"asc\",\n    \"ask\",\n    \"base\",\n    \"by\",\n    \"clear\",\n    \"construct\",\n    \"copy\",\n    \"create\",\n    \"data\",\n    \"delete\",\n    \"desc\",\n    \"describe\",\n    \"distinct\",\n    \"drop\",\n    \"false\",\n    \"filter\",\n    \"from\",\n    \"graph\",\n    \"group\",\n    \"having\",\n    \"in\",\n    \"insert\",\n    \"limit\",\n    \"load\",\n    \"minus\",\n    \"move\",\n    \"named\",\n    \"not\",\n    \"offset\",\n    \"optional\",\n    \"order\",\n    \"prefix\",\n    \"reduced\",\n    \"select\",\n    \"service\",\n    \"silent\",\n    \"to\",\n    \"true\",\n    \"undef\",\n    \"union\",\n    \"using\",\n    \"values\",\n    \"where\",\n    \"with\"\n  ],\n  builtinFunctions: [\n    \"a\",\n    \"abs\",\n    \"avg\",\n    \"bind\",\n    \"bnode\",\n    \"bound\",\n    \"ceil\",\n    \"coalesce\",\n    \"concat\",\n    \"contains\",\n    \"count\",\n    \"datatype\",\n    \"day\",\n    \"encode_for_uri\",\n    \"exists\",\n    \"floor\",\n    \"group_concat\",\n    \"hours\",\n    \"if\",\n    \"iri\",\n    \"isblank\",\n    \"isiri\",\n    \"isliteral\",\n    \"isnumeric\",\n    \"isuri\",\n    \"lang\",\n    \"langmatches\",\n    \"lcase\",\n    \"max\",\n    \"md5\",\n    \"min\",\n    \"minutes\",\n    \"month\",\n    \"now\",\n    \"rand\",\n    \"regex\",\n    \"replace\",\n    \"round\",\n    \"sameterm\",\n    \"sample\",\n    \"seconds\",\n    \"sha1\",\n    \"sha256\",\n    \"sha384\",\n    \"sha512\",\n    \"str\",\n    \"strafter\",\n    \"strbefore\",\n    \"strdt\",\n    \"strends\",\n    \"strlang\",\n    \"strlen\",\n    \"strstarts\",\n    \"struuid\",\n    \"substr\",\n    \"sum\",\n    \"timezone\",\n    \"tz\",\n    \"ucase\",\n    \"uri\",\n    \"uuid\",\n    \"year\"\n  ],\n  // describe tokens\n  ignoreCase: true,\n  tokenizer: {\n    root: [\n      // resource indicators\n      [/<[^\\s\\u00a0>]*>?/, \"tag\"],\n      // strings\n      { include: \"@strings\" },\n      // line comment\n      [/#.*/, \"comment\"],\n      // special chars with special meaning\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[;,.]/, \"delimiter\"],\n      // (prefixed) name\n      [/[_\\w\\d]+:(\\.(?=[\\w_\\-\\\\%])|[:\\w_-]|\\\\[-\\\\_~.!$&'()*+,;=/?#@%]|%[a-f\\d][a-f\\d])*/, \"tag\"],\n      [/:(\\.(?=[\\w_\\-\\\\%])|[:\\w_-]|\\\\[-\\\\_~.!$&'()*+,;=/?#@%]|%[a-f\\d][a-f\\d])+/, \"tag\"],\n      // identifiers, builtinFunctions and keywords\n      [\n        /[$?]?[_\\w\\d]+/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword\" },\n            \"@builtinFunctions\": { token: \"predefined.sql\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // operators\n      [/\\^\\^/, \"operator.sql\"],\n      [/\\^[*+\\-<>=&|^\\/!?]*/, \"operator.sql\"],\n      [/[*+\\-<>=&|\\/!?]/, \"operator.sql\"],\n      // symbol\n      [/@[a-z\\d\\-]*/, \"metatag.html\"],\n      // whitespaces\n      [/\\s+/, \"white\"]\n    ],\n    strings: [\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-terminated single-quoted string\n      [/'$/, \"string.sql\", \"@pop\"],\n      [/'/, \"string.sql\", \"@stringBody\"],\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-terminated single-quoted string\n      [/\"$/, \"string.sql\", \"@pop\"],\n      [/\"/, \"string.sql\", \"@dblStringBody\"]\n    ],\n    // single-quoted strings\n    stringBody: [\n      [/[^\\\\']+/, \"string.sql\"],\n      [/\\\\./, \"string.escape\"],\n      [/'/, \"string.sql\", \"@pop\"]\n    ],\n    // double-quoted strings\n    dblStringBody: [\n      [/[^\\\\\"]+/, \"string.sql\"],\n      [/\\\\./, \"string.escape\"],\n      [/\"/, \"string.sql\", \"@pop\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/sparql/sparql.js\n"));

/***/ })

}]);