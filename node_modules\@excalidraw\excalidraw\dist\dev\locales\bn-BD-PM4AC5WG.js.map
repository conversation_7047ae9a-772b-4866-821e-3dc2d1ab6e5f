{"version": 3, "sources": ["../../../locales/bn-BD.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"পেস্ট করুন\",\n    \"pasteAsPlaintext\": \"প্লেইনটেক্সট হিসাবে পেস্ট করুন\",\n    \"pasteCharts\": \"চার্ট পেস্ট করুন\",\n    \"selectAll\": \"সবটা সিলেক্ট করুন\",\n    \"multiSelect\": \"একাধিক সিলেক্ট করুন\",\n    \"moveCanvas\": \"ক্যানভাস সরান\",\n    \"cut\": \"কাট করুন\",\n    \"copy\": \"কপি করুন\",\n    \"copyAsPng\": \"পীএনজী ছবির মতন কপি করুন\",\n    \"copyAsSvg\": \"এসভীজী ছবির মতন কপি করুন\",\n    \"copyText\": \"লিখিত তথ্যের মতন কপি করুন\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"অধিকতর সামনে আনুন\",\n    \"sendToBack\": \"অধিকতর পিছনে নিয়ে যান\",\n    \"bringToFront\": \"সবার সামনে আনুন\",\n    \"sendBackward\": \"সবার পিছনে নিয়ে যান\",\n    \"delete\": \"মুছা\",\n    \"copyStyles\": \"ডিজাইন কপি করুন\",\n    \"pasteStyles\": \"ডিজাইন পেস্ট করুন\",\n    \"stroke\": \"রেখাংশ\",\n    \"background\": \"পটভূমি\",\n    \"fill\": \"রং\",\n    \"strokeWidth\": \"রেখাংশের বেধ\",\n    \"strokeStyle\": \"রেখাংশের ডিজাইন\",\n    \"strokeStyle_solid\": \"পুরু\",\n    \"strokeStyle_dashed\": \"পাতলা\",\n    \"strokeStyle_dotted\": \"বিন্দুবিন্দু\",\n    \"sloppiness\": \"ভ্রান্তি\",\n    \"opacity\": \"দৃশ্যমানতা\",\n    \"textAlign\": \"লেখ অনুভূমি\",\n    \"edges\": \"কোণ\",\n    \"sharp\": \"তীক্ষ্ণ\",\n    \"round\": \"গোল\",\n    \"arrowheads\": \"তীরের শীর্ষভাগ\",\n    \"arrowhead_none\": \"কিছু না\",\n    \"arrowhead_arrow\": \"তীর\",\n    \"arrowhead_bar\": \"রেখাংশ\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"ত্রিভূজ\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"লেখনীর মাত্রা\",\n    \"fontFamily\": \"লেখনীর হরফ\",\n    \"addWatermark\": \"এক্সক্যালিড্র দ্বারা প্রস্তুত\",\n    \"handDrawn\": \"হাতে আঁকা\",\n    \"normal\": \"স্বাভাবিক\",\n    \"code\": \"কোড\",\n    \"small\": \"ছোট\",\n    \"medium\": \"মাঝারি\",\n    \"large\": \"বড়\",\n    \"veryLarge\": \"অনেক বড়\",\n    \"solid\": \"দৃঢ়\",\n    \"hachure\": \"ভ্রুলেখা\",\n    \"zigzag\": \"আঁকাবাঁকা\",\n    \"crossHatch\": \"ক্রস হ্যাচ\",\n    \"thin\": \"পাতলা\",\n    \"bold\": \"পুরু\",\n    \"left\": \"বাম\",\n    \"center\": \"কেন্দ্র\",\n    \"right\": \"ডান\",\n    \"extraBold\": \"অতি পুরু\",\n    \"architect\": \"স্থপতি\",\n    \"artist\": \"শিল্পী\",\n    \"cartoonist\": \"চিত্রকার\",\n    \"fileTitle\": \"ফাইলের নাম\",\n    \"colorPicker\": \"রং পছন্দ করুন\",\n    \"canvasColors\": \"ক্যানভাসের রং\",\n    \"canvasBackground\": \"ক্যানভাসের পটভূমি\",\n    \"drawingCanvas\": \"ব্যবহৃত ক্যানভাস\",\n    \"layers\": \"মাত্রা\",\n    \"actions\": \"ক্রিয়া\",\n    \"language\": \"ভাষা\",\n    \"liveCollaboration\": \"সরাসরি পারস্পরিক সহযোগিতা...\",\n    \"duplicateSelection\": \"সদৃশ সিলেক্ট\",\n    \"untitled\": \"অনামী\",\n    \"name\": \"নাম\",\n    \"yourName\": \"আপনার নাম\",\n    \"madeWithExcalidraw\": \"এক্সক্যালিড্র দ্বারা তৈরি\",\n    \"group\": \"দল গঠন করুন\",\n    \"ungroup\": \"দল বিভেদ করুন\",\n    \"collaborators\": \"সহযোগী\",\n    \"showGrid\": \"গ্রিড দেখান\",\n    \"addToLibrary\": \"সংগ্রহে যোগ করুন\",\n    \"removeFromLibrary\": \"সংগ্রহ থেকে বের করুন\",\n    \"libraryLoadingMessage\": \"সংগ্রহ তৈরি হচ্ছে\",\n    \"libraries\": \"সংগ্রহ দেখুন\",\n    \"loadingScene\": \"দৃশ্য তৈরি হচ্ছে\",\n    \"align\": \"পংক্তিবিন্যাস\",\n    \"alignTop\": \"উপর পংক্তি\",\n    \"alignBottom\": \"নিম্ন পংক্তি\",\n    \"alignLeft\": \"বাম পংক্তি\",\n    \"alignRight\": \"ডান পংক্তি\",\n    \"centerVertically\": \"উলম্ব কেন্দ্রিত\",\n    \"centerHorizontally\": \"অনুভূমিক কেন্দ্রিত\",\n    \"distributeHorizontally\": \"অনুভূমিকভাবে বিতরণ করুন\",\n    \"distributeVertically\": \"উল্লম্বভাবে বিতরণ করুন\",\n    \"flipHorizontal\": \"অনুভূমিক আবর্তন\",\n    \"flipVertical\": \"উলম্ব আবর্তন\",\n    \"viewMode\": \"দৃশ্য\",\n    \"share\": \"ভাগ করুন\",\n    \"showStroke\": \"\",\n    \"showBackground\": \"\",\n    \"toggleTheme\": \"\",\n    \"personalLib\": \"\",\n    \"excalidrawLib\": \"\",\n    \"decreaseFontSize\": \"লেখনীর মাত্রা কমান\",\n    \"increaseFontSize\": \"লেখনীর মাত্রা বাড়ান\",\n    \"unbindText\": \"\",\n    \"bindText\": \"\",\n    \"createContainerFromText\": \"\",\n    \"link\": {\n      \"edit\": \"লিঙ্ক সংশোধন\",\n      \"editEmbed\": \"\",\n      \"create\": \"লিঙ্ক তৈরী\",\n      \"createEmbed\": \"\",\n      \"label\": \"লিঙ্ক নামকরণ\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"\",\n      \"exit\": \"\"\n    },\n    \"elementLock\": {\n      \"lock\": \"আবদ্ধ করুন\",\n      \"unlock\": \"বিচ্ছিন্ন করুন\",\n      \"lockAll\": \"সব আবদ্ধ করুন\",\n      \"unlockAll\": \"সব বিচ্ছিন্ন করুন\"\n    },\n    \"statusPublished\": \"প্রকাশিত\",\n    \"sidebarLock\": \"লক\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"সংগ্রহে কিছু যোগ করা হয়নি\",\n    \"hint_emptyLibrary\": \"এখানে যোগ করার জন্য ক্যানভাসে একটি বস্তু নির্বাচন করুন, অথবা নীচে, প্রকাশ্য সংগ্রহশালা থেকে একটি সংগ্রহ ইনস্টল করুন৷\",\n    \"hint_emptyPrivateLibrary\": \"এখানে যোগ করার জন্য ক্যানভাসে একটি বস্তু নির্বাচন করুন\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"ক্যানভাস সাফ করুন\",\n    \"exportJSON\": \"জেসন নিবদ্ধ করুন\",\n    \"exportImage\": \"\",\n    \"export\": \"\",\n    \"copyToClipboard\": \"ক্লিপবোর্ডে কপি করুন\",\n    \"save\": \"জমা করুন\",\n    \"saveAs\": \"অন্যভাবে জমা করুন\",\n    \"load\": \"\",\n    \"getShareableLink\": \"ভাগযোগ্য লিঙ্ক পান\",\n    \"close\": \"বন্ধ করুন\",\n    \"selectLanguage\": \"ভাষা চিহ্নিত করুন\",\n    \"scrollBackToContent\": \"বিষয়বস্তুতে ফেরত যান\",\n    \"zoomIn\": \"বড় করুন\",\n    \"zoomOut\": \"ছোট করুন\",\n    \"resetZoom\": \"স্বাভাবিক করুন\",\n    \"menu\": \"তালিকা\",\n    \"done\": \"সম্পন্ন\",\n    \"edit\": \"সংশোধন করুন\",\n    \"undo\": \"ফেরত যান\",\n    \"redo\": \"পুনরায় করুন\",\n    \"resetLibrary\": \"সংগ্রহ সাফ করুন\",\n    \"createNewRoom\": \"নতুন রুম বানান\",\n    \"fullScreen\": \"পূর্ণস্ক্রীন\",\n    \"darkMode\": \"ডার্ক মোড\",\n    \"lightMode\": \"লাইট মোড\",\n    \"zenMode\": \"জেন মোড\",\n    \"objectsSnapMode\": \"\",\n    \"exitZenMode\": \"জেন মোড বন্ধ করুন\",\n    \"cancel\": \"বাতিল\",\n    \"clear\": \"সাফ\",\n    \"remove\": \"বিয়োগ\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"সংগ্রহ প্রকাশ করুন\",\n    \"submit\": \"জমা করুন\",\n    \"confirm\": \"নিশ্চিত করুন\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"এটি পুরো ক্যানভাস সাফ করবে। আপনি কি নিশ্চিত?\",\n    \"couldNotCreateShareableLink\": \"ভাগ করা যায় এমন লিঙ্ক তৈরি করা যায়নি।\",\n    \"couldNotCreateShareableLinkTooBig\": \"ভাগ করা যায় এমন লিঙ্ক তৈরি করা যায়নি: দৃশ্যটি খুব বড়\",\n    \"couldNotLoadInvalidFile\": \"অবৈধ ফাইল লোড করা যায়নি\",\n    \"importBackendFailed\": \"ব্যাকেন্ড থেকে আপলোড ব্যর্থ হয়েছে।\",\n    \"cannotExportEmptyCanvas\": \"খালি ক্যানভাস নিবদ্ধ করা যাবে না।\",\n    \"couldNotCopyToClipboard\": \"ক্লিপবোর্ডে কপি করা যায়নি।\",\n    \"decryptFailed\": \"তথ্য ডিক্রিপ্ট করা যায়নি।\",\n    \"uploadedSecurly\": \"আপলোডটি এন্ড-টু-এন্ড এনক্রিপশনের মাধ্যমে সুরক্ষিত করা হয়েছে, যার অর্থ হল এক্সক্যালিড্র সার্ভার এবং তৃতীয় পক্ষের দ্বারা পড়তে পারা সম্ভব নয়।\",\n    \"loadSceneOverridePrompt\": \"বাহ্যিক অঙ্কন লোড করা আপনার বিদ্যমান দৃশ্য প্রতিস্থাপন করবে। আপনি কি অবিরত করতে চান?\",\n    \"collabStopOverridePrompt\": \"অধিবেশন বন্ধ করা আপনার পূর্ববর্তী, স্থানীয়ভাবে সঞ্চিত অঙ্কন ওভাররাইট করবে। আপনি কি নিশ্চিত?\\n\\n(যদি আপনি আপনার স্থানীয় অঙ্কন রাখতে চান, তাহলে শুধু ব্রাউজার ট্যাবটি বন্ধ করুন।)\",\n    \"errorAddingToLibrary\": \"বস্তুটি সংগ্রহে যোগ করা যায়নি\",\n    \"errorRemovingFromLibrary\": \"বস্তুটি সংগ্রহ থেকে বিয়োগ করা যায়নি\",\n    \"confirmAddLibrary\": \"এটি আপনার সংগ্রহে {{numShapes}} আকার(গুলি) যোগ করবে। আপনি কি নিশ্চিত?\",\n    \"imageDoesNotContainScene\": \"এই ছবিতে কোনো দৃশ্যের তথ্য আছে বলে মনে হয় না৷ আপনি কি নিবদ্ধ করার সময় দৃশ্য এমবেডিং করতে সক্ষম?\",\n    \"cannotRestoreFromImage\": \"এই ফাইল থেকে দৃশ্য পুনরুদ্ধার করা যায়নি\",\n    \"invalidSceneUrl\": \"সরবরাহ করা লিঙ্ক থেকে দৃশ্য লোড করা যায়নি৷ এটি হয় বিকৃত, অথবা বৈধ এক্সক্যালিড্র জেসন তথ্য নেই৷\",\n    \"resetLibrary\": \"এটি আপনার সংগ্রহ পরিষ্কার করবে। আপনি কি নিশ্চিত?\",\n    \"removeItemsFromsLibrary\": \"সংগ্রহ থেকে {{count}} বস্তু বিয়োগ করা হবে। আপনি কি নিশ্চিত?\",\n    \"invalidEncryptionKey\": \"অবৈধ এনক্রীপশন কী।\",\n    \"collabOfflineWarning\": \"\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"অসমর্থিত ফাইল।\",\n    \"imageInsertError\": \"ছবি সন্নিবেশ করা যায়নি। পরে আবার চেষ্টা করুন...\",\n    \"fileTooBig\": \"ফাইলটি খুব বড়। সর্বাধিক অনুমোদিত আকার হল {{maxSize}}৷\",\n    \"svgImageInsertError\": \"এসভীজী ছবি সন্নিবেশ করা যায়নি। এসভীজী মার্কআপটি অবৈধ মনে হচ্ছে৷\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"এসভীজী মার্কআপটি অবৈধ মনে হচ্ছে৷\",\n    \"cannotResolveCollabServer\": \"কোল্যাব সার্ভারের সাথে সংযোগ করা যায়নি। পৃষ্ঠাটি পুনরায় লোড করে আবার চেষ্টা করুন।\",\n    \"importLibraryError\": \"সংগ্রহ লোড করা যায়নি\",\n    \"collabSaveFailed\": \"\",\n    \"collabSaveFailed_sizeExceeded\": \"\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"বাছাই\",\n    \"image\": \"চিত্র সন্নিবেশ\",\n    \"rectangle\": \"আয়তক্ষেত্র\",\n    \"diamond\": \"রুহিতন\",\n    \"ellipse\": \"উপবৃত্ত\",\n    \"arrow\": \"তীর\",\n    \"line\": \"রেখা\",\n    \"freedraw\": \"কলম\",\n    \"text\": \"লেখা\",\n    \"library\": \"সংগ্রহ\",\n    \"lock\": \"আঁকার পরে নির্বাচিত টুল সক্রিয় রাখুন\",\n    \"penMode\": \"\",\n    \"link\": \"একটি নির্বাচিত আকৃতির জন্য লিঙ্ক যোগ বা আপডেট করুন\",\n    \"eraser\": \"ঝাড়ন\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"\",\n    \"hand\": \"\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"ক্যানভাস কার্যকলাপ\",\n    \"selectedShapeActions\": \"বাছাই করা আকার(গুলি)র কার্যকলাপ\",\n    \"shapes\": \"আকার(গুলি)\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"\",\n    \"linearElement\": \"একাধিক বিন্দু শুরু করতে ক্লিক করুন, একক লাইনের জন্য টেনে আনুন\",\n    \"freeDraw\": \"ক্লিক করুন এবং টেনে আনুন, আপনার কাজ শেষ হলে ছেড়ে দিন\",\n    \"text\": \"বিশেষ্য: আপনি নির্বাচন টুলের সাথে যে কোনো জায়গায় ডাবল-ক্লিক করে পাঠ্য যোগ করতে পারেন\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"লেখা সম্পাদনা করতে ডাবল-ক্লিক করুন বা এন্টার টিপুন\",\n    \"text_editing\": \"লেখা সম্পাদনা শেষ করতে এসকেপ বা কন্ট্রোল/কম্যান্ড যোগে এন্টার টিপুন\",\n    \"linearElementMulti\": \"শেষ বিন্দুতে ক্লিক করুন অথবা শেষ করতে এসকেপ বা এন্টার টিপুন\",\n    \"lockAngle\": \"ঘোরানোর সময় আপনি শিফ্ট ধরে রেখে কোণ সীমাবদ্ধ করতে পারেন\",\n    \"resize\": \"আপনি আকার পরিবর্তন করার সময় শিফ্ট ধরে রেখে অনুপাতকে সীমাবদ্ধ করতে পারেন,\\nকেন্দ্র থেকে আকার পরিবর্তন করতে অল্ট ধরে রাখুন\",\n    \"resizeImage\": \"আপনি শিফ্ট ধরে রেখে অবাধে আকার পরিবর্তন করতে পারেন, কেন্দ্র থেকে আকার পরিবর্তন করতে অল্ট ধরুন\",\n    \"rotate\": \"আপনি ঘোরানোর সময় শিফ্ট ধরে রেখে কোণগুলিকে সীমাবদ্ধ করতে পারেন\",\n    \"lineEditor_info\": \"\",\n    \"lineEditor_pointSelected\": \"বিন্দু(গুলি) মুছতে ডিলিট টিপুন, কন্ট্রোল/কম্যান্ড যোগে ডি টিপুন নকল করতে অথবা সরানোর জন্য টানুন\",\n    \"lineEditor_nothingSelected\": \"সম্পাদনা করার জন্য একটি বিন্দু নির্বাচন করুন (একাধিক নির্বাচন করতে শিফ্ট ধরে রাখুন),\\nঅথবা অল্ট ধরে রাখুন এবং নতুন বিন্দু যোগ করতে ক্লিক করুন\",\n    \"placeImage\": \"ছবিটি স্থাপন করতে ক্লিক করুন, অথবা নিজে আকার সেট করতে ক্লিক করুন এবং টেনে আনুন\",\n    \"publishLibrary\": \"আপনার নিজস্ব সংগ্রহ প্রকাশ করুন\",\n    \"bindTextToElement\": \"লেখা যোগ করতে এন্টার টিপুন\",\n    \"deepBoxSelect\": \"\",\n    \"eraserRevert\": \"মুছে ফেলার জন্য চিহ্নিত উপাদানগুলিকে ফিরিয়ে আনতে অল্ট ধরে রাখুন\",\n    \"firefox_clipboard_write\": \"\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"প্রিভিউ দেখাতে অপারগ\",\n    \"canvasTooBig\": \"ক্যানভাস অনেক বড়।\",\n    \"canvasTooBigTip\": \"বিশেষ্য: দূরতম উপাদানগুলোকে একটু কাছাকাছি নিয়ে যাওয়ার চেষ্টা করুন।\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"একটি ত্রুটির সম্মুখীন হয়েছে৷ চেষ্টা করুন <button>পৃষ্ঠাটি পুনরায় লোড করার।</button>\",\n    \"clearCanvasMessage\": \"যদি পুনরায় লোড করা কাজ না করে, চেষ্টা করুন <button>ক্যানভাস পরিষ্কার করার।</button>\",\n    \"clearCanvasCaveat\": \" এর ফলে কাজের ক্ষতি হবে \",\n    \"trackedToSentry\": \"ত্রুটি {{eventId}} আমাদের সিস্টেমে ট্র্যাক করা হয়েছিল।\",\n    \"openIssueMessage\": \"আমরা ত্রুটিতে আপনার দৃশ্যের তথ্য অন্তর্ভুক্ত না করার জন্য খুব সতর্ক ছিলাম। আপনার দৃশ্য ব্যক্তিগত না হলে, আমাদের অনুসরণ করার কথা বিবেচনা করুন <button>ত্রুটি ইতিবৃত্ত।</button> অনুগ্রহ করে GitHub ইস্যুতে অনুলিপি এবং পেস্ট করে নীচের তথ্য অন্তর্ভুক্ত করুন।\",\n    \"sceneContent\": \"দৃশ্য বিষয়বস্তু:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"আপনি আপনার সাথে সহযোগিতা করার জন্য আপনার বর্তমান দৃশ্যে লোকেদের আমন্ত্রণ জানাতে পারেন৷\",\n    \"desc_privacy\": \"চিন্তা করবেন না, সেশনটি এন্ড-টু-এন্ড এনক্রিপশন ব্যবহার করে, তাই আপনি যা আঁকবেন তা গোপন থাকবে। এমনকি আমাদের সার্ভার আপনি যা নিয়ে এসেছেন তা দেখতে সক্ষম হবে না।\",\n    \"button_startSession\": \"সেশন শুরু করুন\",\n    \"button_stopSession\": \"সেশন বন্ধ করুন\",\n    \"desc_inProgressIntro\": \"লাইভ-সহযোগীতার সেশন এখন চলছে।\",\n    \"desc_shareLink\": \"আপনি যার সাথে সহযোগিতা করতে চান তাদের সাথে এই লিঙ্কটি ভাগ করুন: \",\n    \"desc_exitSession\": \"অধিবেশন বন্ধ করা আপনাকে রুম থেকে সংযোগ বিচ্ছিন্ন করবে, কিন্তু আপনি স্থানীয়ভাবে দৃশ্যের সাথে কাজ চালিয়ে যেতে সক্ষম হবেন। মনে রাখবেন যে এটি অন্য লোকেদের প্রভাবিত করবে না এবং তারা এখনও তাদের সংস্করণে সহযোগিতা করতে সক্ষম হবে।\",\n    \"shareTitle\": \"এক্সক্যালিড্র লাইভ সহযোগিতা সেশনে যোগ দিন\"\n  },\n  \"errorDialog\": {\n    \"title\": \"ত্রুটি\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"\",\n    \"disk_details\": \"\",\n    \"disk_button\": \"\",\n    \"link_title\": \"\",\n    \"link_details\": \"\",\n    \"link_button\": \"\",\n    \"excalidrawplus_description\": \"\",\n    \"excalidrawplus_button\": \"নিবদ্ধ\",\n    \"excalidrawplus_exportError\": \"\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"\",\n    \"click\": \"ক্লিক\",\n    \"deepSelect\": \"\",\n    \"deepBoxSelect\": \"\",\n    \"curvedArrow\": \"\",\n    \"curvedLine\": \"\",\n    \"documentation\": \"\",\n    \"doubleClick\": \"\",\n    \"drag\": \"\",\n    \"editor\": \"\",\n    \"editLineArrowPoints\": \"\",\n    \"editText\": \"\",\n    \"github\": \"\",\n    \"howto\": \"\",\n    \"or\": \"অথবা\",\n    \"preventBinding\": \"\",\n    \"tools\": \"\",\n    \"shortcuts\": \"\",\n    \"textFinish\": \"\",\n    \"textNewLine\": \"\",\n    \"title\": \"\",\n    \"view\": \"\",\n    \"zoomToFit\": \"\",\n    \"zoomToSelection\": \"\",\n    \"toggleElementLock\": \"\",\n    \"movePageUpDown\": \"\",\n    \"movePageLeftRight\": \"\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"\"\n  },\n  \"publishDialog\": {\n    \"title\": \"\",\n    \"itemName\": \"\",\n    \"authorName\": \"\",\n    \"githubUsername\": \"\",\n    \"twitterUsername\": \"\",\n    \"libraryName\": \"\",\n    \"libraryDesc\": \"\",\n    \"website\": \"\",\n    \"placeholder\": {\n      \"authorName\": \"\",\n      \"libraryName\": \"\",\n      \"libraryDesc\": \"\",\n      \"githubHandle\": \"\",\n      \"twitterHandle\": \"\",\n      \"website\": \"\"\n    },\n    \"errors\": {\n      \"required\": \"\",\n      \"website\": \"\"\n    },\n    \"noteDescription\": \"\",\n    \"noteGuidelines\": \"\",\n    \"noteLicense\": \"\",\n    \"noteItems\": \"\",\n    \"atleastOneLibItem\": \"\",\n    \"republishWarning\": \"\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"\",\n    \"content\": \"\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"\",\n    \"removeItemsFromLib\": \"\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"\",\n    \"label\": {\n      \"withBackground\": \"\",\n      \"onlySelected\": \"\",\n      \"darkMode\": \"\",\n      \"embedScene\": \"\",\n      \"scale\": \"\",\n      \"padding\": \"\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"\"\n    },\n    \"title\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"\",\n      \"exportToSvg\": \"\",\n      \"copyPngToClipboard\": \"\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"\",\n    \"link\": \"\"\n  },\n  \"stats\": {\n    \"angle\": \"কোণ\",\n    \"element\": \"\",\n    \"elements\": \"\",\n    \"height\": \"\",\n    \"scene\": \"\",\n    \"selected\": \"\",\n    \"storage\": \"\",\n    \"title\": \"\",\n    \"total\": \"\",\n    \"version\": \"\",\n    \"versionCopy\": \"\",\n    \"versionNotAvailable\": \"\",\n    \"width\": \"প্রস্থ\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"সংগ্রহশালায় যুক্ত হয়েছে\",\n    \"copyStyles\": \"\",\n    \"copyToClipboard\": \"ক্লিপবোর্ডে কপি করা হয়েছে।\",\n    \"copyToClipboardAsPng\": \"\",\n    \"fileSaved\": \"\",\n    \"fileSavedToFilename\": \"\",\n    \"canvas\": \"\",\n    \"selection\": \"বাছাই\",\n    \"pasteAsSingleElement\": \"\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"\",\n    \"black\": \"\",\n    \"white\": \"\",\n    \"red\": \"\",\n    \"pink\": \"\",\n    \"grape\": \"\",\n    \"violet\": \"\",\n    \"gray\": \"\",\n    \"blue\": \"\",\n    \"cyan\": \"\",\n    \"teal\": \"\",\n    \"green\": \"\",\n    \"yellow\": \"\",\n    \"orange\": \"\",\n    \"bronze\": \"\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"\",\n      \"center_heading_plus\": \"\",\n      \"menuHint\": \"\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"\",\n      \"center_heading\": \"\",\n      \"toolbarHint\": \"\",\n      \"helpHint\": \"\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"\",\n    \"colors\": \"\",\n    \"shades\": \"\",\n    \"hexCode\": \"\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}