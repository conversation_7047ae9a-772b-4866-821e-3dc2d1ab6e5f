"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_swift_swift_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/swift/swift.js":
/*!***************************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/swift/swift.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/swift/swift.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".swift\",\n  // TODO(owensd): Support the full range of unicode valid identifiers.\n  identifier: /[a-zA-Z_][\\w$]*/,\n  attributes: [\n    \"@GKInspectable\",\n    \"@IBAction\",\n    \"@IBDesignable\",\n    \"@IBInspectable\",\n    \"@IBOutlet\",\n    \"@IBSegueAction\",\n    \"@NSApplicationMain\",\n    \"@NSCopying\",\n    \"@NSManaged\",\n    \"@Sendable\",\n    \"@UIApplicationMain\",\n    \"@autoclosure\",\n    \"@actorIndependent\",\n    \"@asyncHandler\",\n    \"@available\",\n    \"@convention\",\n    \"@derivative\",\n    \"@differentiable\",\n    \"@discardableResult\",\n    \"@dynamicCallable\",\n    \"@dynamicMemberLookup\",\n    \"@escaping\",\n    \"@frozen\",\n    \"@globalActor\",\n    \"@inlinable\",\n    \"@inline\",\n    \"@main\",\n    \"@noDerivative\",\n    \"@nonobjc\",\n    \"@noreturn\",\n    \"@objc\",\n    \"@objcMembers\",\n    \"@preconcurrency\",\n    \"@propertyWrapper\",\n    \"@requires_stored_property_inits\",\n    \"@resultBuilder\",\n    \"@testable\",\n    \"@unchecked\",\n    \"@unknown\",\n    \"@usableFromInline\",\n    \"@warn_unqualified_access\"\n  ],\n  accessmodifiers: [\"open\", \"public\", \"internal\", \"fileprivate\", \"private\"],\n  keywords: [\n    \"#available\",\n    \"#colorLiteral\",\n    \"#column\",\n    \"#dsohandle\",\n    \"#else\",\n    \"#elseif\",\n    \"#endif\",\n    \"#error\",\n    \"#file\",\n    \"#fileID\",\n    \"#fileLiteral\",\n    \"#filePath\",\n    \"#function\",\n    \"#if\",\n    \"#imageLiteral\",\n    \"#keyPath\",\n    \"#line\",\n    \"#selector\",\n    \"#sourceLocation\",\n    \"#warning\",\n    \"Any\",\n    \"Protocol\",\n    \"Self\",\n    \"Type\",\n    \"actor\",\n    \"as\",\n    \"assignment\",\n    \"associatedtype\",\n    \"associativity\",\n    \"async\",\n    \"await\",\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"class\",\n    \"continue\",\n    \"convenience\",\n    \"default\",\n    \"defer\",\n    \"deinit\",\n    \"didSet\",\n    \"do\",\n    \"dynamic\",\n    \"dynamicType\",\n    \"else\",\n    \"enum\",\n    \"extension\",\n    \"fallthrough\",\n    \"false\",\n    \"fileprivate\",\n    \"final\",\n    \"for\",\n    \"func\",\n    \"get\",\n    \"guard\",\n    \"higherThan\",\n    \"if\",\n    \"import\",\n    \"in\",\n    \"indirect\",\n    \"infix\",\n    \"init\",\n    \"inout\",\n    \"internal\",\n    \"is\",\n    \"isolated\",\n    \"lazy\",\n    \"left\",\n    \"let\",\n    \"lowerThan\",\n    \"mutating\",\n    \"nil\",\n    \"none\",\n    \"nonisolated\",\n    \"nonmutating\",\n    \"open\",\n    \"operator\",\n    \"optional\",\n    \"override\",\n    \"postfix\",\n    \"precedence\",\n    \"precedencegroup\",\n    \"prefix\",\n    \"private\",\n    \"protocol\",\n    \"public\",\n    \"repeat\",\n    \"required\",\n    \"rethrows\",\n    \"return\",\n    \"right\",\n    \"safe\",\n    \"self\",\n    \"set\",\n    \"some\",\n    \"static\",\n    \"struct\",\n    \"subscript\",\n    \"super\",\n    \"switch\",\n    \"throw\",\n    \"throws\",\n    \"true\",\n    \"try\",\n    \"typealias\",\n    \"unowned\",\n    \"unsafe\",\n    \"var\",\n    \"weak\",\n    \"where\",\n    \"while\",\n    \"willSet\",\n    \"__consuming\",\n    \"__owned\"\n  ],\n  symbols: /[=(){}\\[\\].,:;@#\\_&\\-<>`?!+*\\\\\\/]/,\n  // Moved . to operatorstart so it can be a delimiter\n  operatorstart: /[\\/=\\-+!*%<>&|^~?\\u00A1-\\u00A7\\u00A9\\u00AB\\u00AC\\u00AE\\u00B0-\\u00B1\\u00B6\\u00BB\\u00BF\\u00D7\\u00F7\\u2016-\\u2017\\u2020-\\u2027\\u2030-\\u203E\\u2041-\\u2053\\u2055-\\u205E\\u2190-\\u23FF\\u2500-\\u2775\\u2794-\\u2BFF\\u2E00-\\u2E7F\\u3001-\\u3003\\u3008-\\u3030]/,\n  operatorend: /[\\u0300-\\u036F\\u1DC0-\\u1DFF\\u20D0-\\u20FF\\uFE00-\\uFE0F\\uFE20-\\uFE2F\\uE0100-\\uE01EF]/,\n  operators: /(@operatorstart)((@operatorstart)|(@operatorend))*/,\n  // TODO(owensd): These are borrowed from C#; need to validate correctness for Swift.\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      { include: \"@attribute\" },\n      { include: \"@literal\" },\n      { include: \"@keyword\" },\n      { include: \"@invokedmethod\" },\n      { include: \"@symbol\" }\n    ],\n    whitespace: [\n      [/\\s+/, \"white\"],\n      [/\"\"\"/, \"string.quote\", \"@endDblDocString\"]\n    ],\n    endDblDocString: [\n      [/[^\"]+/, \"string\"],\n      [/\\\\\"/, \"string\"],\n      [/\"\"\"/, \"string.quote\", \"@popall\"],\n      [/\"/, \"string\"]\n    ],\n    symbol: [\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/[.]/, \"delimiter\"],\n      [/@operators/, \"operator\"],\n      [/@symbols/, \"operator\"]\n    ],\n    comment: [\n      [/\\/\\/\\/.*$/, \"comment.doc\"],\n      [/\\/\\*\\*/, \"comment.doc\", \"@commentdocbody\"],\n      [/\\/\\/.*$/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@commentbody\"]\n    ],\n    commentdocbody: [\n      [/\\/\\*/, \"comment\", \"@commentbody\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/\\:[a-zA-Z]+\\:/, \"comment.doc.param\"],\n      [/./, \"comment.doc\"]\n    ],\n    commentbody: [\n      [/\\/\\*/, \"comment\", \"@commentbody\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/./, \"comment\"]\n    ],\n    attribute: [\n      [\n        /@@@identifier/,\n        {\n          cases: {\n            \"@attributes\": \"keyword.control\",\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ],\n    literal: [\n      [/\"/, { token: \"string.quote\", next: \"@stringlit\" }],\n      [/0[b]([01]_?)+/, \"number.binary\"],\n      [/0[o]([0-7]_?)+/, \"number.octal\"],\n      [/0[x]([0-9a-fA-F]_?)+([pP][\\-+](\\d_?)+)?/, \"number.hex\"],\n      [/(\\d_?)*\\.(\\d_?)+([eE][\\-+]?(\\d_?)+)?/, \"number.float\"],\n      [/(\\d_?)+/, \"number\"]\n    ],\n    stringlit: [\n      [/\\\\\\(/, { token: \"operator\", next: \"@interpolatedexpression\" }],\n      [/@escapes/, \"string\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }],\n      [/./, \"string\"]\n    ],\n    interpolatedexpression: [\n      [/\\(/, { token: \"operator\", next: \"@interpolatedexpression\" }],\n      [/\\)/, { token: \"operator\", next: \"@pop\" }],\n      { include: \"@literal\" },\n      { include: \"@keyword\" },\n      { include: \"@symbol\" }\n    ],\n    keyword: [\n      [/`/, { token: \"operator\", next: \"@escapedkeyword\" }],\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"[A-Z][a-zA-Z0-9$]*\": \"type.identifier\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    escapedkeyword: [\n      [/`/, { token: \"operator\", next: \"@pop\" }],\n      [/./, \"identifier\"]\n    ],\n    invokedmethod: [\n      [\n        /([.])(@identifier)/,\n        {\n          cases: {\n            $2: [\"delimeter\", \"type.identifier\"],\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ]\n  }\n};\n\n/*!---------------------------------------------------------------------------------------------\n *  Copyright (C) David Owens II, owensd.io. All rights reserved.\n *--------------------------------------------------------------------------------------------*/\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/swift/swift.js\n"));

/***/ })

}]);