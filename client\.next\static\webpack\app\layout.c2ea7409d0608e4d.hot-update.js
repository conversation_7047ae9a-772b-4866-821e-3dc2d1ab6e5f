"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1fa2695f5e52\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUHJvamVjdFxccmVhbGNvZGVcXGNsaWVudFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMWZhMjY5NWY1ZTUyXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/socketService.ts":
/*!***************************************!*\
  !*** ./src/services/socketService.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n// client/src/services/socketService.ts\n\n\n// Fix: Ensure SOCKET_URL is defined before use (move it above all uses)\nconst SOCKET_URL = \"http://localhost:5001\" || 0;\n// Create a singleton socket instance\nclass SocketService {\n    static getInstance() {\n        if (!SocketService.instance) {\n            SocketService.instance = new SocketService();\n        }\n        return SocketService.instance;\n    }\n    initSocket() {\n        try {\n            console.log('Initializing socket connection to', SOCKET_URL);\n            // Use polling first for better compatibility, then upgrade to websocket\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                reconnectionAttempts: 10,\n                reconnectionDelay: 1000,\n                timeout: 10000,\n                transports: [\n                    'polling',\n                    'websocket'\n                ],\n                autoConnect: true,\n                forceNew: true,\n                upgrade: true\n            });\n            console.log('Socket instance created successfully');\n            this.setupEventListeners();\n        } catch (error) {\n            console.error('Error initializing socket:', error);\n        }\n    }\n    setupEventListeners() {\n        if (!this.socket) return;\n        // Clear any existing listeners to prevent duplicates\n        this.socket.removeAllListeners();\n        // Connection events\n        this.socket.on('connect', ()=>{\n            var _this_socket;\n            console.log('Socket connected successfully:', (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.id);\n            this.connected = true;\n            this.emitEvent('connect', null);\n        });\n        this.socket.on('disconnect', (reason)=>{\n            console.log(\"Socket disconnected. Reason: \".concat(reason));\n            this.connected = false;\n            this.emitEvent('disconnect', reason);\n            if (reason !== 'io client disconnect') {\n                console.log('Reconnection attempt will start in 2 seconds...');\n                setTimeout(()=>{\n                    console.log('Attempting to reconnect...');\n                    this.connect();\n                }, 2000);\n            }\n        });\n        this.socket.on('error', (error)=>{\n            console.error('Socket error:', (error === null || error === void 0 ? void 0 : error.message) || error || 'Unknown error');\n            this.connected = false;\n            // Handle websocket errors specifically\n            if (error === 'websocket error' || typeof error === 'string' && error.includes('websocket')) {\n                console.log('WebSocket error detected via error event - switching to polling');\n                setTimeout(()=>{\n                    try {\n                        var _this_socket;\n                        (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.disconnect();\n                        this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                            reconnectionAttempts: 5,\n                            reconnectionDelay: 1000,\n                            timeout: 15000,\n                            transports: [\n                                'polling'\n                            ],\n                            autoConnect: true,\n                            forceNew: true,\n                            upgrade: false\n                        });\n                        this.setupEventListeners();\n                    } catch (e) {\n                        console.error('Error switching to polling after error event:', e);\n                    }\n                }, 500);\n            }\n        });\n        this.socket.on('connect_error', (error)=>{\n            var _error_message, _error_message1, _error_message2;\n            console.error('Socket connection error:', (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error');\n            this.connected = false;\n            // Handle specific error types\n            if (error === null || error === void 0 ? void 0 : (_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('xhr poll error')) {\n                console.log('XHR polling error detected - will try alternative connection method');\n                // Set a flag to track this specific error\n                if (true) {\n                    window.localStorage.setItem('socket_xhr_error', 'true');\n                }\n                // Emit the error event\n                this.emitEvent('error', 'XHR polling error - trying alternative connection');\n                // Try the alternative transport after a short delay\n                setTimeout(()=>{\n                    this.tryAlternativeTransport();\n                }, 1000);\n            } else if ((error === null || error === void 0 ? void 0 : (_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('websocket')) || (error === null || error === void 0 ? void 0 : (_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes('WebSocket')) || (error === null || error === void 0 ? void 0 : error.type) === 'TransportError') {\n                console.log('WebSocket error detected - will try polling transport');\n                // Set a flag to track this specific error\n                if (true) {\n                    window.localStorage.setItem('socket_websocket_error', 'true');\n                }\n                // Try polling immediately\n                setTimeout(()=>{\n                    try {\n                        var _this_socket;\n                        console.log('Switching to polling transport...');\n                        (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.disconnect();\n                        this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                            reconnectionAttempts: 5,\n                            reconnectionDelay: 1000,\n                            timeout: 10000,\n                            transports: [\n                                'polling'\n                            ],\n                            autoConnect: true,\n                            forceNew: true,\n                            upgrade: false\n                        });\n                        this.setupEventListeners();\n                    } catch (e) {\n                        console.error('Error switching to polling transport:', e);\n                    }\n                }, 1000);\n                this.emitEvent('error', 'WebSocket error - trying polling transport');\n            } else {\n                // For other errors, just emit the error event\n                this.emitEvent('error', (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error');\n                // Try to reconnect after a delay\n                setTimeout(()=>{\n                    this.connect();\n                }, 3000);\n            }\n        });\n        // Transport and reconnection events\n        if (this.socket.io) {\n            this.socket.io.on('error', (error)=>{\n                console.error('Transport error:', error);\n            });\n            this.socket.io.on('reconnect_attempt', (attempt)=>{\n                console.log(\"Reconnection attempt \".concat(attempt));\n            });\n            this.socket.io.on('reconnect', (attemptNumber)=>{\n                this.connected = true;\n                this.emitEvent('connect', null);\n            });\n            this.socket.io.on('reconnect_error', (error)=>{\n                console.error('Reconnection error:', error);\n            });\n            this.socket.io.on('reconnect_failed', ()=>{\n                this.emitEvent('error', 'Failed to reconnect after multiple attempts');\n                // Try a different approach after all reconnection attempts fail\n                setTimeout(()=>{\n                    this.initSocket();\n                }, 3000);\n            });\n        }\n        // Application-specific events\n        this.socket.on('code-update', (code)=>{\n            this.emitEvent('code-update', code);\n        });\n        this.socket.on('user-typing', (data)=>{\n            this.emitEvent('user-typing', data);\n        });\n        this.socket.on('user-joined', (users)=>{\n            this.emitEvent('user-joined', users);\n        });\n        this.socket.on('user-left', (users)=>{\n            this.emitEvent('user-left', users);\n        });\n        this.socket.on('highlight-line', (data)=>{\n            this.emitEvent('highlight-line', data);\n        });\n        this.socket.on('cursor-move', (param)=>{\n            let { userId, position } = param;\n            var _this_listeners_get;\n            console.log(\"Cursor move received from user \".concat(userId, \":\"), position);\n            (_this_listeners_get = this.listeners.get('cursor-move')) === null || _this_listeners_get === void 0 ? void 0 : _this_listeners_get.forEach((callback)=>callback({\n                    userId,\n                    position\n                }));\n        });\n        this.socket.on('room-users-updated', (data)=>{\n            this.emitEvent('room-users-updated', data);\n        });\n    }\n    // Add event listener\n    on(event, callback) {\n        var _this_listeners_get;\n        if (!this.listeners.has(event)) {\n            this.listeners.set(event, []);\n        }\n        (_this_listeners_get = this.listeners.get(event)) === null || _this_listeners_get === void 0 ? void 0 : _this_listeners_get.push(callback);\n    }\n    // Remove event listener\n    off(event, callback) {\n        if (!this.listeners.has(event)) return;\n        const callbacks = this.listeners.get(event) || [];\n        const index = callbacks.indexOf(callback);\n        if (index !== -1) {\n            callbacks.splice(index, 1);\n        }\n    }\n    // Emit event to listeners\n    emitEvent(event, data) {\n        if (!this.listeners.has(event)) return;\n        const callbacks = this.listeners.get(event) || [];\n        callbacks.forEach((callback)=>{\n            try {\n                callback(data);\n            } catch (error) {\n                console.error(\"Error in \".concat(event, \" listener:\"), error);\n            }\n        });\n    }\n    // Check if socket is connected\n    isConnected() {\n        var _this_socket;\n        return this.connected && !!((_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.connected);\n    }\n    // Connect to socket server with fallback mechanisms\n    connect() {\n        if (!this.socket) {\n            this.initSocket();\n        } else if (!this.socket.connected) {\n            console.log('Socket exists but not connected, attempting to reconnect...');\n            // Try to reconnect with existing socket\n            try {\n                this.socket.connect();\n            } catch (error) {\n                console.error('Error reconnecting with existing socket:', error);\n                // If reconnection fails, create a new socket\n                this.initSocket();\n            }\n        }\n        // Set a timeout to check if connection was successful\n        setTimeout(()=>{\n            if (!this.isConnected()) {\n                this.tryAlternativeTransport();\n            }\n        }, 5000);\n    }\n    // Try alternative transport method if WebSocket fails\n    tryAlternativeTransport() {\n        try {\n            // Disconnect existing socket if any\n            if (this.socket) {\n                this.socket.disconnect();\n            }\n            // Create new socket with both transports but prioritize polling\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(SOCKET_URL, {\n                reconnectionAttempts: 5,\n                reconnectionDelay: 1000,\n                timeout: 10000,\n                transports: [\n                    'polling',\n                    'websocket'\n                ],\n                autoConnect: true,\n                forceNew: true,\n                upgrade: true\n            });\n            this.setupEventListeners();\n        } catch (error) {\n            console.error('Error setting up alternative transport:', error);\n        }\n    }\n    // Disconnect from socket server\n    disconnect() {\n        if (this.socket) {\n            this.socket.disconnect();\n        }\n    }\n    // Create a new room\n    createRoom(username, roomId) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket || !this.isConnected()) {\n                return reject(new Error('Socket not connected'));\n            }\n            // Generate a unique userId if not already stored\n            let userId =  true ? window.localStorage.getItem('userId') : 0;\n            if (!userId) {\n                userId = \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9));\n                if (true) {\n                    window.localStorage.setItem('userId', userId);\n                }\n            }\n            this.socket.emit('create-room', {\n                username,\n                roomId,\n                userId\n            }, (response)=>{\n                if (response.error) {\n                    reject(new Error(response.error));\n                } else {\n                    // If the server validated and possibly changed the username, update it locally\n                    if (response.username && response.username !== username) {\n                        if (true) {\n                            window.localStorage.setItem('username', response.username);\n                        }\n                    }\n                    resolve({\n                        roomId: response.roomId,\n                        username: response.username || username\n                    });\n                }\n            });\n        });\n    }\n    // Join an existing room with fallback to HTTP if socket fails\n    joinRoom(roomId, username) {\n        return new Promise(async (resolve, reject)=>{\n            // Generate a unique userId if not already stored\n            let userId =  true ? window.localStorage.getItem('userId') : 0;\n            if (!userId) {\n                userId = \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9));\n                if (true) {\n                    window.localStorage.setItem('userId', userId);\n                }\n            }\n            // Check if we have a socket connection\n            if (!this.socket || !this.isConnected()) {\n                // Try to connect\n                this.connect();\n                // Wait a bit to see if connection succeeds\n                await new Promise((r)=>setTimeout(r, 2000));\n                // If still not connected, use HTTP fallback\n                if (!this.isConnected()) {\n                    return this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                }\n            }\n            // If we reach here, we have a socket connection, so use it\n            try {\n                if (!this.socket) {\n                    throw new Error('Socket is null');\n                }\n                // Emit join-room with userId, username, and roomId\n                this.socket.emit('join-room', {\n                    roomId,\n                    username,\n                    userId\n                }, (response)=>{\n                    if (response.error) {\n                        // If socket join fails, try HTTP fallback\n                        this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                    } else if (response.success) {\n                        // If the server validated and possibly changed the username, update it locally\n                        if (response.username && response.username !== username) {\n                            if (true) {\n                                window.localStorage.setItem('username', response.username);\n                            }\n                        }\n                        // Mark the current user in the users list\n                        const usersWithCurrentFlag = (response.users || []).map((user)=>({\n                                ...user,\n                                userId: user.userId || '',\n                                isCurrentUser: (user.userId || '') === userId\n                            }));\n                        resolve({\n                            users: usersWithCurrentFlag || [],\n                            username: response.username || username\n                        });\n                    } else {\n                        // If socket join fails, try HTTP fallback\n                        this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                    }\n                });\n                // Set a timeout in case the callback never fires\n                setTimeout(()=>{\n                    // If we haven't resolved or rejected yet, try HTTP fallback\n                    this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n                }, 5000);\n            } catch (error) {\n                // If socket join throws an exception, try HTTP fallback\n                this.joinRoomViaHttp(roomId, username, userId, resolve, reject);\n            }\n        });\n    }\n    // HTTP fallback for joining a room when socket fails\n    joinRoomViaHttp(roomId, username, userId, resolve, reject) {\n        // Use axios to make a direct HTTP request to the server\n        const apiUrl = \"\".concat(SOCKET_URL, \"/api/join-room\");\n        axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(apiUrl, {\n            roomId,\n            username,\n            userId\n        }).then((response)=>{\n            if (response.data.error) {\n                reject(new Error(response.data.error));\n            } else {\n                // If the server validated and possibly changed the username, update it locally\n                if (response.data.username && response.data.username !== username) {\n                    if (true) {\n                        window.localStorage.setItem('username', response.data.username);\n                    }\n                }\n                // Create a default user list with at least the current user\n                const users = response.data.users || [\n                    {\n                        userId,\n                        username: response.data.username || username,\n                        socketId: 'http-fallback'\n                    }\n                ];\n                // Fix: When mapping users, always provide a fallback for userId (empty string if undefined)\n                const usersWithCurrentFlag = users.map((user)=>({\n                        ...user,\n                        userId: user.userId || '',\n                        isCurrentUser: (user.userId || '') === userId\n                    }));\n                resolve({\n                    users: usersWithCurrentFlag,\n                    username: response.data.username || username\n                });\n                // Try to reconnect socket after successful HTTP fallback\n                setTimeout(()=>this.connect(), 1000);\n            }\n        }).catch((error)=>{\n            // If HTTP fallback also fails, create a minimal response with just the current user\n            const fallbackUser = {\n                userId,\n                username,\n                isCurrentUser: true\n            };\n            // Resolve with just the current user to allow the UI to function\n            resolve({\n                users: [\n                    fallbackUser\n                ],\n                username\n            });\n            // Try to reconnect socket after error\n            setTimeout(()=>this.connect(), 2000);\n        });\n    }\n    // Send code changes to the server with HTTP fallback\n    sendCodeChange(roomId, code) {\n        // If socket is connected, use it\n        if (this.socket && this.isConnected()) {\n            try {\n                // Use volatile for code changes to prevent queueing\n                // This helps prevent outdated updates from being sent\n                this.socket.volatile.emit('code-change', {\n                    roomId,\n                    code\n                });\n                return true;\n            } catch (error) {\n            // Fall through to HTTP fallback\n            }\n        } else {}\n        // HTTP fallback for code changes\n        this.sendCodeChangeViaHttp(roomId, code);\n        return true;\n    }\n    // HTTP fallback for sending code changes\n    sendCodeChangeViaHttp(roomId, code) {\n        // Only send HTTP fallback for significant changes to reduce traffic\n        // Store the last sent code to avoid sending duplicates\n        const lastSentCode =  true ? window.localStorage.getItem(\"last_http_code_\".concat(roomId)) : 0;\n        if (lastSentCode === code) {\n            return; // Don't send duplicate code\n        }\n        // Use axios to make a direct HTTP request to the server\n        const apiUrl = \"\".concat(SOCKET_URL, \"/api/code-change\");\n        const userId =  true ? window.localStorage.getItem('userId') || '' : 0;\n        axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(apiUrl, {\n            roomId,\n            code,\n            userId\n        }).then((response)=>{\n            // Store the sent code to avoid duplicates\n            if (true) {\n                window.localStorage.setItem(\"last_http_code_\".concat(roomId), code);\n            }\n        }).catch((error)=>{\n            console.error('Error sending code change via HTTP:', error);\n        });\n        // Try to reconnect socket\n        if (!this.isConnected()) {\n            setTimeout(()=>this.connect(), 1000);\n        }\n    }\n    // Send typing notification\n    sendTyping(roomId, username) {\n        if (!this.socket || !this.isConnected()) {\n            return;\n        }\n        // Use the exact username provided by the user without any modifications\n        // This ensures we use exactly what the user entered on the dashboard\n        const validUsername = username;\n        // Get userId from localStorage\n        const userId =  true ? window.localStorage.getItem('userId') || \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9)) : 0;\n        try {\n            this.socket.emit('typing', {\n                roomId,\n                username: validUsername,\n                userId\n            });\n        } catch (error) {}\n    }\n    // Emit a highlight-line event to the server\n    sendHighlightLine(roomId, startLine, endLine, comment) {\n        if (!this.socket || !this.isConnected()) return;\n        try {\n            this.socket.emit('highlight-line', {\n                roomId,\n                startLine,\n                endLine,\n                comment\n            });\n        } catch (error) {\n        // Optionally handle error\n        }\n    }\n    // Leave a room\n    leaveRoom(roomId) {\n        if (!this.socket || !this.isConnected()) return;\n        try {\n            this.socket.emit('leave-room', roomId);\n        } catch (error) {}\n    }\n    // Add a public method to listen for the next connect event\n    onConnect(callback) {\n        if (this.isConnected()) {\n            callback();\n        } else if (this.socket) {\n            this.socket.once('connect', callback);\n        } else {\n            // If socket is not initialized, initialize and then listen\n            this.initSocket();\n            // Wait for the socket to be created, then attach the listener\n            setTimeout(()=>{\n                var _this_socket;\n                (_this_socket = this.socket) === null || _this_socket === void 0 ? void 0 : _this_socket.once('connect', callback);\n            }, 100);\n        }\n    }\n    getSocket() {\n        return this.socket;\n    }\n    // Emit cursor movement\n    sendCursorMove(roomId, userId, position) {\n        if (this.socket && this.connected) {\n            this.socket.emit(\"cursor-move\", {\n                roomId,\n                userId,\n                position\n            });\n        }\n    }\n    // Add a method to validate if a room exists on the server\n    async validateRoom(roomId) {\n        if (!this.socket || !this.isConnected()) {\n            throw new Error(\"Socket not connected\");\n        }\n        return new Promise((resolve, reject)=>{\n            if (!this.socket) {\n                throw new Error(\"Socket is not initialized\");\n            }\n            this.socket.emit(\"validate-room\", {\n                roomId\n            }, (response)=>{\n                if (response) {\n                    resolve(response);\n                } else {\n                    reject(new Error(\"Failed to validate room\"));\n                }\n            });\n        });\n    }\n    constructor(){\n        this.socket = null;\n        this.listeners = new Map();\n        this.connected = false;\n        this.initSocket();\n    }\n}\n// Export the class itself instead of calling getInstance during export\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SocketService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/socketService.ts\n"));

/***/ })

});