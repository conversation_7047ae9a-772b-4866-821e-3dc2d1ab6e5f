"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_m3_m3_js"],{

/***/ "(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/m3/m3.js":
/*!*********************************************************************!*\
  !*** ../node_modules/monaco-editor/esm/vs/basic-languages/m3/m3.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: () => (/* binding */ conf),\n/* harmony export */   language: () => (/* binding */ language)\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/m3/m3.ts\nvar conf = {\n  comments: {\n    blockComment: [\"(*\", \"*)\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"(*\", close: \"*)\" },\n    { open: \"<*\", close: \"*>\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".m3\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  keywords: [\n    \"AND\",\n    \"ANY\",\n    \"ARRAY\",\n    \"AS\",\n    \"BEGIN\",\n    \"BITS\",\n    \"BRANDED\",\n    \"BY\",\n    \"CASE\",\n    \"CONST\",\n    \"DIV\",\n    \"DO\",\n    \"ELSE\",\n    \"ELSIF\",\n    \"END\",\n    \"EVAL\",\n    \"EXCEPT\",\n    \"EXCEPTION\",\n    \"EXIT\",\n    \"EXPORTS\",\n    \"FINALLY\",\n    \"FOR\",\n    \"FROM\",\n    \"GENERIC\",\n    \"IF\",\n    \"IMPORT\",\n    \"IN\",\n    \"INTERFACE\",\n    \"LOCK\",\n    \"LOOP\",\n    \"METHODS\",\n    \"MOD\",\n    \"MODULE\",\n    \"NOT\",\n    \"OBJECT\",\n    \"OF\",\n    \"OR\",\n    \"OVERRIDES\",\n    \"PROCEDURE\",\n    \"RAISE\",\n    \"RAISES\",\n    \"READONLY\",\n    \"RECORD\",\n    \"REF\",\n    \"REPEAT\",\n    \"RETURN\",\n    \"REVEAL\",\n    \"SET\",\n    \"THEN\",\n    \"TO\",\n    \"TRY\",\n    \"TYPE\",\n    \"TYPECASE\",\n    \"UNSAFE\",\n    \"UNTIL\",\n    \"UNTRACED\",\n    \"VALUE\",\n    \"VAR\",\n    \"WHILE\",\n    \"WITH\"\n  ],\n  reservedConstNames: [\n    \"ABS\",\n    \"ADR\",\n    \"ADRSIZE\",\n    \"BITSIZE\",\n    \"BYTESIZE\",\n    \"CEILING\",\n    \"DEC\",\n    \"DISPOSE\",\n    \"FALSE\",\n    \"FIRST\",\n    \"FLOAT\",\n    \"FLOOR\",\n    \"INC\",\n    \"ISTYPE\",\n    \"LAST\",\n    \"LOOPHOLE\",\n    \"MAX\",\n    \"MIN\",\n    \"NARROW\",\n    \"NEW\",\n    \"NIL\",\n    \"NUMBER\",\n    \"ORD\",\n    \"ROUND\",\n    \"SUBARRAY\",\n    \"TRUE\",\n    \"TRUNC\",\n    \"TYPECODE\",\n    \"VAL\"\n  ],\n  reservedTypeNames: [\n    \"ADDRESS\",\n    \"ANY\",\n    \"BOOLEAN\",\n    \"CARDINAL\",\n    \"CHAR\",\n    \"EXTENDED\",\n    \"INTEGER\",\n    \"LONGCARD\",\n    \"LONGINT\",\n    \"LONGREAL\",\n    \"MUTEX\",\n    \"NULL\",\n    \"REAL\",\n    \"REFANY\",\n    \"ROOT\",\n    \"TEXT\"\n  ],\n  operators: [\"+\", \"-\", \"*\", \"/\", \"&\", \"^\", \".\"],\n  relations: [\"=\", \"#\", \"<\", \"<=\", \">\", \">=\", \"<:\", \":\"],\n  delimiters: [\"|\", \"..\", \"=>\", \",\", \";\", \":=\"],\n  symbols: /[>=<#.,:;+\\-*/&^]+/,\n  escapes: /\\\\(?:[\\\\fnrt\"']|[0-7]{3})/,\n  tokenizer: {\n    root: [\n      // Identifiers and keywords\n      [/_\\w*/, \"invalid\"],\n      [\n        /[a-zA-Z][a-zA-Z0-9_]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@reservedConstNames\": { token: \"constant.reserved.$0\" },\n            \"@reservedTypeNames\": { token: \"type.reserved.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // Whitespace\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      // Integer- and real literals\n      [/[0-9]+\\.[0-9]+(?:[DdEeXx][\\+\\-]?[0-9]+)?/, \"number.float\"],\n      [/[0-9]+(?:\\_[0-9a-fA-F]+)?L?/, \"number\"],\n      // Operators, relations, and delimiters\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"operators\",\n            \"@relations\": \"operators\",\n            \"@delimiters\": \"delimiter\",\n            \"@default\": \"invalid\"\n          }\n        }\n      ],\n      // Character literals\n      [/'[^\\\\']'/, \"string.char\"],\n      [/(')(@escapes)(')/, [\"string.char\", \"string.escape\", \"string.char\"]],\n      [/'/, \"invalid\"],\n      // Text literals\n      [/\"([^\"\\\\]|\\\\.)*$/, \"invalid\"],\n      [/\"/, \"string.text\", \"@text\"]\n    ],\n    text: [\n      [/[^\\\\\"]+/, \"string.text\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"invalid\"],\n      [/\"/, \"string.text\", \"@pop\"]\n    ],\n    comment: [\n      [/\\(\\*/, \"comment\", \"@push\"],\n      [/\\*\\)/, \"comment\", \"@pop\"],\n      [/./, \"comment\"]\n    ],\n    pragma: [\n      [/<\\*/, \"keyword.pragma\", \"@push\"],\n      [/\\*>/, \"keyword.pragma\", \"@pop\"],\n      [/./, \"keyword.pragma\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\(\\*/, \"comment\", \"@comment\"],\n      [/<\\*/, \"keyword.pragma\", \"@pragma\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/monaco-editor/esm/vs/basic-languages/m3/m3.js\n"));

/***/ })

}]);